# BOPMaps Zooming Functionality

This document provides a comprehensive analysis of the zooming functionality in the BOPMaps application, detailing how zooming is implemented, the restrictions in place, and the differences between standard and 2.5D (OSM) zooming modes.

## Zoom Level Configuration

### Basic Zoom Parameters

The app implements zooming with the following key parameters defined in `lib/widgets/map/core/map_constants.dart`:

- **Minimum Zoom**: 2.0 - Allows viewing large areas of the world map
- **Maximum Zoom**: 19.0 - Provides detailed street level viewing
- **Default Zoom**: 17.0 - Initial zoom level when opening the map
- **Default Initial Latitude/Longitude**: 37.7749, -122.4194 (San Francisco)

### Zoom Detail Thresholds

The app defines specific zoom thresholds that determine the level of detail displayed:

- **Low Detail Threshold**: 14.0
- **Medium Detail Threshold**: 16.0
- **High Detail Threshold**: 17.0

These thresholds correspond to detail levels:
- Level 1 (Low): Used for zoom levels below 14.0
- Level 2 (Medium): Used for zoom levels between 14.0 and 16.0
- Level 3 (High): Used for zoom levels above 17.0

## Zooming Implementation

### Core Zooming Functions

The main zooming functionality is implemented in the `FlutterMapWidgetState` class through several methods:

1. **`zoomIn()`**: Increases zoom level by 1.0 up to the maximum allowed zoom (19.0)
```dart
void _zoomIn() {
  // Capture current zoom before starting zoom operation
  final currentZoom = _mapController.camera.zoom;
  final targetZoom = math.min(currentZoom + 1.0, MapConstants.maxZoom);
  
  // Only proceed if we're not already at max zoom
  if (currentZoom >= MapConstants.maxZoom) return;
  
  // Set zooming state
  setState(() {
    _isZooming = true;
    _lastZoomLevel = currentZoom;
  });
  
  // Perform the zoom operation
  _stateManager.zoomIn();
  
  // Set a timer to end the zooming state after animation completes
  _zoomDebounceTimer?.cancel();
  _zoomDebounceTimer = Timer(_zoomDebounceDelay, () {
    if (mounted) {
      // Get the actual final zoom (might be different from targetZoom)
      final actualZoom = _mapController.camera.zoom;
      
      setState(() {
        _isZooming = false;
        _lastZoomLevel = actualZoom;
      });
      
      // Force the state manager to recognize the final zoom
      _stateManager.updateZoomLevel(actualZoom);
    }
  });
}
```

2. **`zoomOut()`**: Decreases zoom level by 1.0 down to the minimum allowed zoom (2.0)
```dart
void _zoomOut() {
  // Capture current zoom before starting zoom operation
  final currentZoom = _mapController.camera.zoom;
  final targetZoom = math.max(currentZoom - 1.0, MapConstants.minZoom);
  
  // Only proceed if we're not already at min zoom
  if (currentZoom <= MapConstants.minZoom) return;
  
  // Set zooming state
  setState(() {
    _isZooming = true;
    _lastZoomLevel = currentZoom;
  });
  
  // Perform the zoom operation
  _stateManager.zoomOut();
  
  // Set a timer to end the zooming state after animation completes
  _zoomDebounceTimer?.cancel();
  _zoomDebounceTimer = Timer(_zoomDebounceDelay, () {
    if (mounted) {
      // Get the actual final zoom (might be different from targetZoom)
      final actualZoom = _mapController.camera.zoom;
      
      setState(() {
        _isZooming = false;
        _lastZoomLevel = actualZoom;
      });
      
      // Force the state manager to recognize the final zoom
      _stateManager.updateZoomLevel(actualZoom);
    }
  });
}
```

3. **`animateToLocation()`**: Animate to a specific location with an optional zoom level

```dart
void animateToLocation(double lat, double lng, {double? zoom}) {
  if (!mounted) return;
  
  _stateManager.moveTo(
    position: LatLng(lat, lng),
    zoom: zoom,
    animate: true,
  );
}
```

### Zoom Animation Management

The app implements a sophisticated debounce mechanism to handle smooth zooming:

- **Zoom Debounce Delay**: 150ms - prevents rapid fluctuations during zooming
- **Animation Duration**: 300ms - controls the speed of zoom animations
- **State Tracking**: Uses `_isZooming` flag to track zooming state

### Zoom Event Handling

The map subscribes to zoom change events from the underlying `flutter_map` controller:

```dart
_mapController.mapEventStream.listen((event) {
  // Check for rapid events that might indicate performance issues
  _detectRapidEvents();
  
  // Get current zoom from camera
  final currentZoom = _mapController.camera.zoom;
  
  // Different handling based on event type for smoother transitions
  if (event is MapEventMoveStart) {
    // On move start, capture the current zoom as starting point
    _lastZoomLevel = currentZoom;
  } else if (event is MapEventMove && (_lastZoomLevel - currentZoom).abs() > 0.01) {
    // During move, if zoom changed significantly, update zooming state
    setState(() {
      _isZooming = true;
    });
    
    // Reset the debounce timer when zoom changes during movement
    _zoomDebounceTimer?.cancel();
    _zoomDebounceTimer = Timer(_zoomDebounceDelay, () {
      if (mounted) {
        setState(() {
          _isZooming = false;
          _lastZoomLevel = currentZoom;
        });
        // Force update state manager with final zoom level
        _stateManager.updateZoomLevel(currentZoom);
      }
    });
  } else if (event is MapEventMoveEnd && _isZooming) {
    // On move end, update our stored zoom level and stabilize
    
    // Cancel any existing timer
    _zoomDebounceTimer?.cancel();
    
    // Immediately use the final zoom for better responsiveness
    _stateManager.updateZoomLevel(currentZoom);
    
    // Short delay before ending zooming state to allow layers to settle
    _zoomDebounceTimer = Timer(Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() {
          _isZooming = false;
          _lastZoomLevel = currentZoom;
        });
      }
    });
  }
});
```

## Zoom Restrictions and Safeguards

### OSM Rate Limiting Protection

The app implements safeguards against OpenStreetMap rate limiting when viewing large areas:

```dart
// Check if the current zoom level and visible area is safe for OSM without hitting rate limits
bool _isOSMSafe() {
  final double area = _calculateMapArea();
  final double zoom = _currentZoom;
  
  // OpenStreetMap thresholds:
  // - At zoom < 10, area > 200km² starts to get risky
  // - At zoom < 8, area > 500km² is risky
  // - At zoom < 6, area > 1000km² will likely hit limits
  if (zoom < 6 && area > 1000) return false;
  if (zoom < 8 && area > 500) return false;
  if (zoom < 10 && area > 200) return false;
  if (zoom < 12 && area > 100) return false;
  
  return true;
}
```

### Area Calculation

The app calculates the visible map area to determine when to apply restrictions:

```dart
// Calculate approximate map area in square kilometers based on visible bounds
double _calculateMapArea() {
  if (_mapKey.currentState == null) return 0.0;
  
  final bounds = _mapKey.currentState!.visibleBounds;
  
  // Calculate width in kilometers using the Haversine formula (simplified for small distances)
  final double latCenter = (bounds.northEast.latitude + bounds.southWest.latitude) / 2;
  final double lngDiff = (bounds.northEast.longitude - bounds.southWest.longitude).abs();
  final double latDiff = (bounds.northEast.latitude - bounds.southWest.latitude).abs();
  
  // Approximate conversion to kilometers (depends on latitude)
  // 1 degree longitude at equator is about 111.32 km, adjusting for latitude
  final double widthKm = lngDiff * 111.32 * math.cos(latCenter * math.pi / 180);
  // 1 degree latitude is about 110.57 km
  final double heightKm = latDiff * 110.57;
  
  return widthKm * heightKm;
}
```

### Tile Density Calculation

The app calculates tile density to optimize performance:

```dart
// Calculate how many tiles per square km are being loaded (tile density)
double _calculateTileDensity() {
  if (_mapKey.currentState == null) return 0.0;
  
  final zoom = _currentZoom;
  final area = _calculateMapArea();
  
  if (area <= 0) return 0.0;
  
  // Approximate number of tiles at this zoom level for the visible area
  // At zoom level z, each tile covers approximately 2^(16-z) km² at the equator
  final double tileCoverageKm2 = math.pow(2, 16 - zoom).toDouble();
  
  // Number of tiles needed to cover the current view
  final double numberOfTiles = area / tileCoverageKm2;
  
  // Tiles per square km
  return numberOfTiles / area;
}
```

### Fallback Mechanisms

When zoom levels or map area exceed safe thresholds, the app uses fallback tile sources:

1. **Primary Tile Source**: `https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png`
2. **Fallback Level 1**: `https://tile.openstreetmap.org/{z}/{x}/{y}.png`
3. **Fallback Level 2**: `https://a.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png`
4. **Fallback Level 3**: `https://stamen-tiles.a.ssl.fastly.net/toner/{z}/{x}/{y}.png`

## 2.5D vs Standard Map View

### Key Differences

#### 1. Rendering Approach

- **Standard View**: Flat 2D tile rendering with markers
- **2.5D View**: 
  - Applies perspective transformation matrix
  - Renders buildings with height
  - Applies tilt angle (default: 0.35 radians, about 20°)
  - Uses shadow effects for depth

#### 2. Tilt Implementation

The 2.5D view applies a tilt transformation:

```dart
Transform(
  alignment: Alignment.center,
  transform: MapTransformer.build25DMatrix(_tiltAnimation.value),
  child: MapTransformer.buildTiltedContainer(
    tiltValue: _tiltAnimation.value,
    constraints: constraints,
    child: Stack(
      // Map layers
    ),
  ),
)
```

#### 3. Building Rendering in 2.5D

Buildings in 2.5D view are rendered with:
- Base color: Color(0xFF292929)
- Top color: Color(0xFF3D3D3D)
- Side color: Color(0xFF333333)
- Height scale: 0.85 (default)
- Shadow opacity: 0.3

#### 4. Detail Level Adaptation

In 2.5D mode, building detail level adapts based on zoom level:

```dart
// Determine the appropriate detail level for the current state
final effectiveDetailLevel = _isZooming
    ? math.max(1, _stateManager.buildingDetailLevel - 1) // Reduce detail during zoom
    : _stateManager.buildingDetailLevel;
```

#### 5. Zoom-Enhanced Tilt

The 2.5D view enhances tilt effect with zoom:

```dart
// Calculate a tilt factor that enhances with zoom level
double _calculateZoomEnhancedTilt() {
  // Only apply zoom enhancement if 3D rendering is enabled
  if (_renderParams['render3D'] != true) return 0.0;
  
  // Base tilt from widget property
  final double baseTilt = widget.tiltFactor;
  
  // Enhance tilt based on zoom level for more dramatic 3D effect at high zooms
  if (widget.zoomLevel <= 14) return baseTilt;
  if (widget.zoomLevel >= 20) return baseTilt * 1.5; // 50% increase at maximum zoom
  
  // Linear interpolation between zoom 14 and 20
  final double zoomFactor = (widget.zoomLevel - 14) / 6;
  return baseTilt * (1.0 + zoomFactor * 0.5);
}
```

#### 6. Performance Optimization

The 2.5D view implements:
- Building simplification at lower zoom levels (below 14.0)
- Simplification tolerance: 0.00005
- Maximum buildings per tile: 100 (at low zoom)
- Emergency performance mode when detecting performance issues

### Toggling Between Views

Users can toggle between standard and 2.5D views via:

1. **Map Controls**:
```dart
MapControlButton(
  icon: Icons.layers,
  tooltip: 'Toggle 3D View',
  isActive: _isControlsActive,
  onPressed: () {
    if (_mapKey.currentState != null) {
      _mapKey.currentState!.toggleTilt();
    }
    setState(() {
      _isControlsActive = true;
    });
  },
)
```

2. **Settings Menu** (for 3D Buildings toggling):
```dart
SwitchListTile(
  title: const Text('3D Buildings'),
  subtitle: const Text('3D buildings on the map'),
  value: mapSettings.use3DBuildings,
  onChanged: (value) {
    mapSettings.toggle3DBuildings();
  },
)
```

## Caching Strategy

The app implements a multi-level caching system to optimize performance:

### Memory Cache
- Fast in-memory caching of recently accessed data
- Includes tiles, buildings, roads, and other map elements
- Auto-eviction of oldest items when the cache reaches its size limit

### Disk Cache
- Persistent storage of map data on the device
- Organized by region IDs and tile coordinates
- Allows offline usage of previously downloaded areas

### Zoom-Based Caching
- Different detail levels are cached based on zoom
- Low zoom levels use simplified geometries
- High zoom levels store full detail

## Emergency Performance Mode

The app implements an emergency performance mode to prevent freezes:

```dart
void _activateEmergencyMode() {
  final now = DateTime.now();
  // Don't reactivate too frequently
  if (now.difference(_lastEmergencyModeActivation) < const Duration(seconds: 10)) {
    return;
  }
  
  setState(() {
    _emergencyPerformanceMode = true;
    _lastEmergencyModeActivation = now;
  });
  
  // Cancel any previous timer
  _emergencyModeTimer?.cancel();
  
  // Schedule timer to exit emergency mode after delay
  _emergencyModeTimer = Timer(_emergencyModeDuration, () {
    if (mounted) {
      setState(() {
        _emergencyPerformanceMode = false;
      });
    }
  });
}
```

## Conclusion

The BOPMaps application implements a sophisticated zooming system with thoughtful performance optimizations, fallback mechanisms, and user experience enhancements. The 2.5D mode provides a visually appealing perspective view while maintaining performance through adaptive detail levels and caching strategies.

The zoom implementation balances between providing a rich visual experience and protecting against API rate limiting, ensuring a smooth user experience across different devices and network conditions.
