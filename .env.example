# BOPMaps Environment Variables
# Copy this file to .env and fill in your actual credentials

# API Configuration
API_BASE_URL=http://localhost:8000/api

# Spotify Configuration
# You'll need to replace these with your actual Spotify Developer credentials
# Get them from https://developer.spotify.com/dashboard/applications
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
# This must match what's configured in your Spotify Developer Dashboard
# and must match the scheme you've registered in your Flutter app
SPOTIFY_REDIRECT_URI=bopmaps://callback

# Mapbox Configuration
MAPBOX_ACCESS_TOKEN=your_mapbox_token_here 