import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:geolocator/geolocator.dart';
import 'package:bop_maps/screens/map/managers/smart_location_fetch_manager.dart';
import 'package:bop_maps/config/constants.dart';

void main() {
  group('SmartLocationFetchManager', () {
    late SmartLocationFetchManager manager;
    late List<String> fetchLog;
    late List<String> errorLog;

    setUp(() {
      fetchLog = [];
      errorLog = [];
      
      manager = SmartLocationFetchManager(
        onPinsFetched: (pins) {
          fetchLog.add('Fetched ${pins.length} pins');
        },
        onError: (error) {
          errorLog.add(error);
        },
        onFetchStarted: () {
          fetchLog.add('Fetch started');
        },
        onFetchCompleted: () {
          fetchLog.add('Fetch completed');
        },
      );
    });

    tearDown(() {
      manager.dispose();
    });

    test('should trigger initial load', () async {
      final position = Position(
        latitude: 37.7749,
        longitude: -122.4194,
        timestamp: DateTime.now(),
        accuracy: 10.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );

      expect(manager.shouldFetchNow(position), isTrue);
    });

    test('should not trigger fetch if moved less than 10 meters', () async {
      final position1 = Position(
        latitude: 37.7749,
        longitude: -122.4194,
        timestamp: DateTime.now(),
        accuracy: 10.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );

      // Simulate initial fetch
      manager.reset();
      await manager.handleLocationUpdate(position1);
      
      // Move less than 10 meters (approximately 0.00009 degrees)
      final position2 = Position(
        latitude: 37.7749 + 0.00005, // ~5.5 meters north
        longitude: -122.4194,
        timestamp: DateTime.now(),
        accuracy: 10.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );

      expect(manager.shouldFetchNow(position2), isFalse);
    });

    test('should trigger fetch if moved more than 10 meters', () async {
      final position1 = Position(
        latitude: 37.7749,
        longitude: -122.4194,
        timestamp: DateTime.now(),
        accuracy: 10.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );

      // Simulate initial fetch by setting internal state
      manager.reset();
      
      // Move more than 10 meters (approximately 0.0001 degrees)
      final position2 = Position(
        latitude: 37.7749 + 0.0001, // ~11 meters north
        longitude: -122.4194,
        timestamp: DateTime.now(),
        accuracy: 10.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );

      expect(manager.shouldFetchNow(position2), isTrue);
    });

    test('should handle filter changes', () {
      manager.setFilter(PinFilterType.fresh);
      expect(manager.getDebugInfo()['currentFilter'], contains('fresh'));
      
      manager.setFilter(PinFilterType.friends);
      expect(manager.getDebugInfo()['currentFilter'], contains('friends'));
    });

    test('should handle app lifecycle changes', () {
      manager.onAppLifecycleChanged(AppLifecycleState.paused);
      manager.onAppLifecycleChanged(AppLifecycleState.resumed);
      
      // Should force refresh on next location update after resume
      final position = Position(
        latitude: 37.7749,
        longitude: -122.4194,
        timestamp: DateTime.now(),
        accuracy: 10.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );
      
      expect(manager.shouldFetchNow(position), isTrue);
    });

    test('should handle location permission changes', () {
      manager.onLocationPermissionChanged(false);
      manager.onLocationPermissionChanged(true);
      
      // Should force refresh after permission granted
      final position = Position(
        latitude: 37.7749,
        longitude: -122.4194,
        timestamp: DateTime.now(),
        accuracy: 10.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );
      
      expect(manager.shouldFetchNow(position), isTrue);
    });

    test('should provide debug information', () {
      final debugInfo = manager.getDebugInfo();
      
      expect(debugInfo, containsPair('isInitialLoad', isA<bool>()));
      expect(debugInfo, containsPair('isFetchInProgress', isA<bool>()));
      expect(debugInfo, containsPair('currentFilter', isA<String>()));
    });
  });
}
