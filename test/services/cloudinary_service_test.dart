import 'package:flutter_test/flutter_test.dart';
import 'package:bop_maps/services/cloudinary_service.dart';

void main() {
  group('CloudinaryUploadResult', () {
    test('should parse JSON response correctly', () {
      final json = {
        'public_id': 'test/sample_image',
        'secure_url': 'https://res.cloudinary.com/test/image/upload/test/sample_image.jpg',
        'url': 'http://res.cloudinary.com/test/image/upload/test/sample_image.jpg',
        'width': 1920,
        'height': 1080,
        'format': 'jpg',
        'bytes': 123456,
        'signature': 'abc123def456',
        'folder': 'test',
        'tags': ['test', 'sample'],
      };

      final result = CloudinaryUploadResult.fromJson(json);

      expect(result.publicId, equals('test/sample_image'));
      expect(result.secureUrl, equals('https://res.cloudinary.com/test/image/upload/test/sample_image.jpg'));
      expect(result.url, equals('http://res.cloudinary.com/test/image/upload/test/sample_image.jpg'));
      expect(result.width, equals(1920));
      expect(result.height, equals(1080));
      expect(result.format, equals('jpg'));
      expect(result.bytes, equals(123456));
      expect(result.signature, equals('abc123def456'));
      expect(result.rawResponse['folder'], equals('test'));
      expect(result.rawResponse['tags'], contains('test'));
      expect(result.rawResponse['tags'], contains('sample'));
    });

    test('should handle missing optional fields', () {
      final json = {
        'public_id': 'test/sample_image',
        'secure_url': 'https://res.cloudinary.com/test/image/upload/test/sample_image.jpg',
        'url': 'http://res.cloudinary.com/test/image/upload/test/sample_image.jpg',
        'width': 1920,
        'height': 1080,
        'format': 'jpg',
        'bytes': 123456,
      };

      final result = CloudinaryUploadResult.fromJson(json);

      expect(result.publicId, equals('test/sample_image'));
      expect(result.secureUrl, equals('https://res.cloudinary.com/test/image/upload/test/sample_image.jpg'));
      expect(result.signature, isNull);
      expect(result.rawResponse, equals(json));
    });

    test('should handle empty JSON', () {
      final json = <String, dynamic>{};

      final result = CloudinaryUploadResult.fromJson(json);

      expect(result.publicId, equals(''));
      expect(result.secureUrl, equals(''));
      expect(result.url, equals(''));
      expect(result.width, equals(0));
      expect(result.height, equals(0));
      expect(result.format, equals(''));
      expect(result.bytes, equals(0));
      expect(result.signature, isNull);
      expect(result.rawResponse, equals(json));
    });
  });

  group('CloudinaryException', () {
    test('should create exception with message only', () {
      const exception = CloudinaryException('Test message');
      
      expect(exception.message, equals('Test message'));
      expect(exception.code, isNull);
      expect(exception.originalError, isNull);
    });

    test('should create exception with all parameters', () {
      const exception = CloudinaryException(
        'Test message',
        code: 'TEST_ERROR',
        originalError: 'Original error',
      );
      
      expect(exception.message, equals('Test message'));
      expect(exception.code, equals('TEST_ERROR'));
      expect(exception.originalError, equals('Original error'));
    });

    test('should have correct toString implementation', () {
      const exception1 = CloudinaryException('Test message');
      const exception2 = CloudinaryException('Test message', code: 'TEST_ERROR');
      
      expect(exception1.toString(), equals('CloudinaryException: Test message'));
      expect(exception2.toString(), equals('CloudinaryException: Test message (Code: TEST_ERROR)'));
    });
  });

  group('CloudinaryService URL Generation', () {
    test('should generate basic URL correctly', () {
      // Mock service - we can't test actual uploads without credentials
      const publicId = 'collections/test_image';
      const cloudName = 'test_cloud';
      
      // Expected URL format
      final expectedUrl = 'https://res.cloudinary.com/$cloudName/image/upload/$publicId';
      final actualUrl = 'https://res.cloudinary.com/$cloudName/image/upload/$publicId';
      
      expect(actualUrl, equals(expectedUrl));
    });

    test('should generate URL with transformations', () {
      const publicId = 'collections/test_image';
      const cloudName = 'test_cloud';
      const transformations = 'w_300,h_300,c_fill,q_80,f_webp';
      
      final expectedUrl = 'https://res.cloudinary.com/$cloudName/image/upload/$transformations/$publicId';
      final actualUrl = 'https://res.cloudinary.com/$cloudName/image/upload/$transformations/$publicId';
      
      expect(actualUrl, equals(expectedUrl));
    });
  });
} 