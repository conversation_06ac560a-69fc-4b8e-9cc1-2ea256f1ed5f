import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import '../../../lib/models/map/tile.dart';
import '../../../lib/services/map/tile_cache_service.dart';
import '../../../lib/config/constants.dart';

// Generate mocks
@GenerateMocks([http.Client])
import 'tile_cache_service_test.mocks.dart';

void main() {
  late MockClient mockClient;
  late TileCacheService service;
  const testToken = 'test_token';

  setUp(() {
    mockClient = MockClient();
    service = TileCacheService(mockClient, testToken);
  });

  group('TileCacheService', () {
    test('shouldCacheTile respects zoom level bounds', () {
      expect(service.shouldCacheTile(AppConstants.minCachedZoomLevel - 1), false);
      expect(service.shouldCacheTile(AppConstants.minCachedZoomLevel), true);
      expect(service.shouldCacheTile(AppConstants.maxCachedZoomLevel), true);
      expect(service.shouldCacheTile(AppConstants.maxCachedZoomLevel + 1), false);
    });

    test('getCachedTile returns tile data on successful cache hit', () async {
      final tile = MapTile(15, 5242, 12663);
      final url = '${AppConstants.baseApiUrl}${AppConstants.osmTilesEndpoint}/15/5242/12663.png';
      
      when(mockClient.get(
        Uri.parse(url),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response('', 304, headers: {'etag': 'test_etag'}));

      final result = await service.getCachedTile(tile);
      expect(result, null); // Null indicates client should use cached version
    });

    test('getCachedTile returns tile data on cache miss', () async {
      final tile = MapTile(15, 5242, 12663);
      final url = '${AppConstants.baseApiUrl}${AppConstants.osmTilesEndpoint}/15/5242/12663.png';
      final testData = Uint8List.fromList([1, 2, 3, 4]);
      
      when(mockClient.get(
        Uri.parse(url),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response.bytes(
        testData,
        200,
        headers: {'etag': 'new_etag'},
      ));

      final result = await service.getCachedTile(tile);
      expect(result, testData);
    });

    test('getCachedTile returns null for invalid zoom level', () async {
      final tile = MapTile(AppConstants.maxCachedZoomLevel + 1, 5242, 12663);
      final result = await service.getCachedTile(tile);
      expect(result, null);
    });

    test('getCachedTile returns null on HTTP error', () async {
      final tile = MapTile(15, 5242, 12663);
      final url = '${AppConstants.baseApiUrl}${AppConstants.osmTilesEndpoint}/15/5242/12663.png';
      
      when(mockClient.get(
        Uri.parse(url),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response('Not Found', 404));

      final result = await service.getCachedTile(tile);
      expect(result, null);
    });

    test('getCachedTile throws rate limit error', () async {
      final tile = MapTile(15, 5242, 12663);
      final url = '${AppConstants.baseApiUrl}${AppConstants.osmTilesEndpoint}/15/5242/12663.png';
      
      when(mockClient.get(
        Uri.parse(url),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response('Rate limit exceeded', 429));

      expect(
        () => service.getCachedTile(tile),
        throwsA(predicate((e) => e.toString() == 'Exception: ${AppConstants.errorRateLimitExceeded}')),
      );
    });

    test('getCachedTile throws on network error', () async {
      final tile = MapTile(15, 5242, 12663);
      final url = '${AppConstants.baseApiUrl}${AppConstants.osmTilesEndpoint}/15/5242/12663.png';
      
      when(mockClient.get(
        Uri.parse(url),
        headers: anyNamed('headers'),
      )).thenThrow(Exception('Network error'));

      expect(
        () => service.getCachedTile(tile),
        throwsA(predicate((e) => e.toString() == 'Exception: ${AppConstants.errorNetworkFailure}')),
      );
    });

    test('prefetchTiles respects zoom level bounds', () async {
      const north = 37.7749;
      const south = 37.77;
      const east = -122.4194;
      const west = -122.424;
      final zoomLevels = [14, 15, 16];

      when(mockClient.get(
        any,
        headers: argThat(
          predicate<Map<String, String>>((headers) =>
            headers['Authorization'] == 'Bearer $testToken' &&
            headers['Accept'] == 'image/png' &&
            headers['X-Client-Version'] == AppConstants.cacheVersion
          ),
          named: 'headers',
        ),
      )).thenAnswer((_) async => http.Response.bytes(
        Uint8List.fromList([1, 2, 3, 4]),
        200,
        headers: {'etag': 'test_etag'},
      ));

      await service.prefetchTiles(north, south, east, west, zoomLevels);
      
      verify(mockClient.get(any, headers: anyNamed('headers'))).called(greaterThan(0));
    });

    test('prefetchTiles processes tiles in batches', () async {
      const north = 37.7749;
      const south = 37.77;
      const east = -122.4194;
      const west = -122.424;
      final zoomLevels = [14, 15, 16];

      when(mockClient.get(
        any,
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response.bytes(
        Uint8List.fromList([1, 2, 3, 4]),
        200,
        headers: {'etag': 'test_etag'},
      ));

      await service.prefetchTiles(north, south, east, west, zoomLevels);
      
      final captured = verify(mockClient.get(any, headers: anyNamed('headers'))).captured;
      expect(captured.length, lessThanOrEqualTo(AppConstants.maxBatchSize));
    });

    test('prefetchTiles handles errors gracefully', () async {
      const north = 37.7749;
      const south = 37.77;
      const east = -122.4194;
      const west = -122.424;
      final zoomLevels = [14, 15, 16];

      when(mockClient.get(
        any,
        headers: anyNamed('headers'),
      )).thenThrow(Exception('Network error'));

      // Should complete without throwing
      await service.prefetchTiles(north, south, east, west, zoomLevels);
    });

    test('getCacheStats returns statistics', () async {
      final url = '${AppConstants.baseApiUrl}${AppConstants.cacheStatsEndpoint}';
      final testStats = {
        'total_size_bytes': 1000000,
        'hit_rate': 0.95,
      };

      when(mockClient.get(
        Uri.parse(url),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response(
        json.encode(testStats),
        200,
      ));

      final result = await service.getCacheStats();
      expect(result, equals(testStats));
    });
  });
} 