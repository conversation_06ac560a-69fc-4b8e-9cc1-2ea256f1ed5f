import 'dart:math' as math;
import 'package:flutter_test/flutter_test.dart';

// Large-scale test for accumulative pin rendering system
class LargeScaleProgressiveRenderingTest {
  static Set<String> renderedPinIds = <String>{};
  static Map<String, List<Map<String, dynamic>>> layerFeatures = {};
  static List<Map<String, dynamic>> pendingPins = [];
  static bool isPinFetchInProgress = false;
  static String currentRenderingContext = 'incrementalUpdate';
  static bool? pendingClusterDisplay;
  static bool isProgressiveRenderingEnabled = true;
  static int progressiveUpdateBatchSize = 12;
  static int totalBatchesProcessed = 0;
  static DateTime? renderingStartTime;
  static List<String> processingLog = [];
  
  // NYC coordinate bounds
  static const double nycLatMin = 40.7;
  static const double nycLatMax = 40.8;
  static const double nycLngMin = -74.0;
  static const double nycLngMax = -73.9;
  
  // NYC neighborhoods for realistic distribution
  static const List<Map<String, dynamic>> nycNeighborhoods = [
    {'name': 'Manhattan', 'lat': 40.7831, 'lng': -73.9712, 'weight': 0.3},
    {'name': 'Brooklyn', 'lat': 40.6782, 'lng': -73.9442, 'weight': 0.25},
    {'name': 'Queens', 'lat': 40.7282, 'lng': -73.7949, 'weight': 0.2},
    {'name': 'Bronx', 'lat': 40.8448, 'lng': -73.8648, 'weight': 0.15},
    {'name': 'Staten Island', 'lat': 40.5795, 'lng': -74.1502, 'weight': 0.1},
  ];
  
  // Pin rarity levels
  static const List<String> rarityLevels = ['common', 'uncommon', 'rare', 'epic', 'legendary'];
  
  // Generate realistic NYC test pins
  static List<Map<String, dynamic>> generateNYCTestPins(int count) {
    final random = math.Random(42); // Fixed seed for reproducible tests
    final pins = <Map<String, dynamic>>[];
    
    for (int i = 0; i < count; i++) {
      // Choose neighborhood based on weight
      final neighborhood = _selectWeightedNeighborhood(random);
      
      // Generate coordinates near the neighborhood center
      final lat = neighborhood['lat'] + (random.nextDouble() - 0.5) * 0.02; // ~1km radius
      final lng = neighborhood['lng'] + (random.nextDouble() - 0.5) * 0.02;
      
      // Ensure coordinates are within NYC bounds
      final clampedLat = lat.clamp(nycLatMin, nycLatMax);
      final clampedLng = lng.clamp(nycLngMin, nycLngMax);
      
      final rarity = rarityLevels[random.nextInt(rarityLevels.length)];
      
      pins.add({
        'id': 'nyc_pin_${i.toString().padLeft(3, '0')}',
        'latitude': clampedLat,
        'longitude': clampedLng,
        'title': '${neighborhood['name']} Pin ${i + 1}',
        'description': 'Test pin in ${neighborhood['name']}',
        'rarity': rarity,
        'neighborhood': neighborhood['name'],
        'created_at': DateTime.now().subtract(Duration(days: random.nextInt(30))).toIso8601String(),
        'user_id': 'test_user_${random.nextInt(100)}',
        'likes': random.nextInt(50),
        'views': random.nextInt(200),
        'isPlaying': false,
        'skinImageUrl': rarity == 'legendary' ? 'https://example.com/skin_$i.png' : null,
      });
    }
    
    processingLog.add('Generated ${pins.length} NYC test pins');
    return pins;
  }
  
  static Map<String, dynamic> _selectWeightedNeighborhood(math.Random random) {
    final randomValue = random.nextDouble();
    double cumulativeWeight = 0.0;
    
    for (final neighborhood in nycNeighborhoods) {
      cumulativeWeight += neighborhood['weight'];
      if (randomValue <= cumulativeWeight) {
        return neighborhood;
      }
    }
    
    return nycNeighborhoods.last; // Fallback
  }
  
  // Simulate the exact progressive rendering logic from snapchat_style_map_screen.dart
  static Future<void> simulateProgressiveRendering(
    List<Map<String, dynamic>> allPins,
    String renderingContext,
  ) async {
    currentRenderingContext = renderingContext;
    renderingStartTime = DateTime.now();
    totalBatchesProcessed = 0;
    processingLog.clear();
    
    processingLog.add('Starting progressive rendering: ${allPins.length} pins, context: $renderingContext');
    
    // Only clear rendered pins for specific contexts that require fresh rendering
    if (renderingContext == 'initialLoad' ||
        renderingContext == 'mapStyleChange' ||
        renderingContext == 'filterChange') {
      renderedPinIds.clear();
      layerFeatures.clear();
      processingLog.add('Cleared rendered pins for fresh $renderingContext rendering');
    }
    
    // Filter out already rendered pins for accumulative rendering
    List<Map<String, dynamic>> pinsToRender;
    if (renderingContext != 'initialLoad' &&
        renderingContext != 'mapStyleChange' &&
        renderingContext != 'filterChange') {
      pinsToRender = allPins.where((pin) {
        final pinId = pin['id']?.toString();
        return pinId != null && !renderedPinIds.contains(pinId);
      }).toList();
      processingLog.add('Filtered to ${pinsToRender.length} unrendered pins for accumulative rendering');
    } else {
      pinsToRender = allPins;
      processingLog.add('Using all ${pinsToRender.length} pins for fresh rendering');
    }
    
    // Set up progressive rendering
    pendingPins.clear();
    pendingPins.addAll(pinsToRender);
    isPinFetchInProgress = true;
    
    // Process pins in batches
    while (pendingPins.isNotEmpty) {
      await _processPinBatch();
      totalBatchesProcessed++;
      
      // Simulate processing delay
      await Future.delayed(Duration(milliseconds: 10));
    }
    
    // Complete progressive rendering
    await _onProgressiveRenderingComplete();
  }
  
  static Future<void> _processPinBatch() async {
    if (pendingPins.isEmpty) return;
    
    final batchSize = math.min(progressiveUpdateBatchSize, pendingPins.length);
    final batch = pendingPins.take(batchSize).toList();
    pendingPins.removeRange(0, batchSize);
    
    // Filter out already rendered pins
    final newPins = batch.where((pin) {
      final pinId = pin['id']?.toString();
      return pinId != null && !renderedPinIds.contains(pinId);
    }).toList();
    
    if (newPins.isEmpty) {
      processingLog.add('Batch ${totalBatchesProcessed + 1}: Skipped - all ${batch.length} pins already rendered');
      return;
    }
    
    processingLog.add('Batch ${totalBatchesProcessed + 1}: Processing ${newPins.length} new pins (${batch.length - newPins.length} already rendered, ${pendingPins.length} remaining)');
    
    // Add to individual layer
    await _addPinBatchToIndividualLayer(newPins);
    
    // Add to cluster layer
    await _addPinBatchToClusters(newPins);
    
    // Mark pins as rendered
    for (final pin in newPins) {
      final pinId = pin['id']?.toString();
      if (pinId != null) {
        renderedPinIds.add(pinId);
      }
    }
    
    processingLog.add('Batch ${totalBatchesProcessed + 1}: Added ${newPins.length} pins (total rendered: ${renderedPinIds.length})');
  }
  
  static Future<void> _addPinBatchToIndividualLayer(List<Map<String, dynamic>> batch) async {
    final features = <Map<String, dynamic>>[];
    
    for (final pin in batch) {
      final pinId = pin['id']?.toString();
      if (pinId != null) {
        features.add({
          'type': 'Feature',
          'geometry': {
            'type': 'Point',
            'coordinates': [pin['longitude'], pin['latitude']],
          },
          'properties': {
            'id': pinId,
            'title': pin['title'] ?? 'Pin',
            'rarity': pin['rarity'] ?? 'common',
            'neighborhood': pin['neighborhood'],
            'originalData': pin,
          },
        });
      }
    }
    
    _addToLayerFeatures('individual-pins-source', features);
  }
  
  static Future<void> _addPinBatchToClusters(List<Map<String, dynamic>> batch) async {
    // Get all rendered pins for clustering
    final allRenderedPins = <Map<String, dynamic>>[];
    
    // Add previously rendered pins
    for (final pinId in renderedPinIds) {
      // Simulate getting pin from cache
      allRenderedPins.add({'id': pinId, 'latitude': 40.75, 'longitude': -73.98});
    }
    
    // Add new batch
    allRenderedPins.addAll(batch);
    
    // Simulate cluster creation (simplified clustering algorithm)
    final clusters = _createSimplifiedClusters(allRenderedPins);
    
    // Determine if we should force full update
    final forceFullUpdate = currentRenderingContext == 'initialLoad' ||
        currentRenderingContext == 'mapStyleChange' ||
        currentRenderingContext == 'filterChange';
    
    if (forceFullUpdate) {
      _clearLayerFeatures('cluster-pins-source');
      _addToLayerFeatures('cluster-pins-source', clusters);
    } else {
      _addToLayerFeatures('cluster-pins-source', clusters);
    }
  }
  
  static List<Map<String, dynamic>> _createSimplifiedClusters(List<Map<String, dynamic>> pins) {
    final clusters = <Map<String, dynamic>>[];
    const clusterRadius = 0.01; // ~1km in degrees
    const minClusterSize = 3;
    
    final processed = <bool>[];
    for (int i = 0; i < pins.length; i++) {
      processed.add(false);
    }
    
    for (int i = 0; i < pins.length; i++) {
      if (processed[i]) continue;
      
      final pin = pins[i];
      final lat = pin['latitude'] as double?;
      final lng = pin['longitude'] as double?;

      if (lat == null || lng == null) continue;
      
      // Find nearby pins
      final nearbyPins = <Map<String, dynamic>>[pin];
      final nearbyIndices = <int>[i];
      
      for (int j = i + 1; j < pins.length; j++) {
        if (processed[j]) continue;
        
        final otherPin = pins[j];
        final otherLat = otherPin['latitude'] as double?;
        final otherLng = otherPin['longitude'] as double?;

        if (otherLat == null || otherLng == null) continue;
        
        final distance = math.sqrt(
          math.pow(lat - otherLat, 2) + math.pow(lng - otherLng, 2)
        );
        
        if (distance <= clusterRadius) {
          nearbyPins.add(otherPin);
          nearbyIndices.add(j);
        }
      }
      
      // Mark as processed
      for (final index in nearbyIndices) {
        processed[index] = true;
      }
      
      // Create cluster or individual pin
      if (nearbyPins.length >= minClusterSize) {
        clusters.add({
          'id': 'cluster_${clusters.length}',
          'isCluster': true,
          'count': nearbyPins.length,
          'latitude': lat,
          'longitude': lng,
          'pins': nearbyPins,
        });
      } else {
        for (final individualPin in nearbyPins) {
          clusters.add({
            ...individualPin,
            'isCluster': false,
          });
        }
      }
    }
    
    return clusters;
  }
  
  static Future<void> _onProgressiveRenderingComplete() async {
    final renderingDuration = renderingStartTime != null 
        ? DateTime.now().difference(renderingStartTime!).inMilliseconds
        : 0;
    
    processingLog.add('Progressive rendering complete: ${renderedPinIds.length} pins in ${totalBatchesProcessed} batches (${renderingDuration}ms)');
    
    // Reset state
    isPinFetchInProgress = false;
    pendingPins.clear();
    
    // Handle deferred layer switching
    if (pendingClusterDisplay != null) {
      processingLog.add('Executing deferred layer switch: ${pendingClusterDisplay! ? 'clusters' : 'individual'}');
      pendingClusterDisplay = null;
    }
  }
  
  // Helper methods for layer management
  static void _addToLayerFeatures(String sourceId, List<Map<String, dynamic>> features) {
    if (!layerFeatures.containsKey(sourceId)) {
      layerFeatures[sourceId] = [];
    }
    layerFeatures[sourceId]!.addAll(features);
  }
  
  static void _clearLayerFeatures(String sourceId) {
    layerFeatures[sourceId]?.clear();
  }
  
  static List<Map<String, dynamic>> getCurrentLayerFeatures(String sourceId) {
    return layerFeatures[sourceId] ?? [];
  }
  
  // Test timeout scenario
  static Future<void> simulateTimeoutScenario(List<Map<String, dynamic>> pins) async {
    processingLog.add('Simulating timeout scenario with ${pins.length} pins');
    
    pendingPins.clear();
    pendingPins.addAll(pins);
    isPinFetchInProgress = true;
    
    // Simulate getting stuck (don't process batches)
    await Future.delayed(Duration(milliseconds: 100));
    
    // Force timeout
    processingLog.add('Forcing timeout - progressive rendering stuck');
    await _onProgressiveRenderingComplete();
  }
  
  // Reset all state
  static void reset() {
    renderedPinIds.clear();
    layerFeatures.clear();
    pendingPins.clear();
    isPinFetchInProgress = false;
    currentRenderingContext = 'incrementalUpdate';
    pendingClusterDisplay = null;
    totalBatchesProcessed = 0;
    renderingStartTime = null;
    processingLog.clear();
  }
  
  // Get comprehensive test results
  static Map<String, dynamic> getTestResults() {
    final individualFeatures = getCurrentLayerFeatures('individual-pins-source');
    final clusterFeatures = getCurrentLayerFeatures('cluster-pins-source');
    
    return {
      'renderedPinCount': renderedPinIds.length,
      'individualLayerFeatures': individualFeatures.length,
      'clusterLayerFeatures': clusterFeatures.length,
      'totalBatchesProcessed': totalBatchesProcessed,
      'renderingDuration': renderingStartTime != null 
          ? DateTime.now().difference(renderingStartTime!).inMilliseconds
          : 0,
      'processingLog': List.from(processingLog),
      'isPinFetchInProgress': isPinFetchInProgress,
      'pendingPinsCount': pendingPins.length,
      'layerSources': layerFeatures.keys.toList(),
    };
  }
}

void main() {
  group('Large-Scale Progressive Rendering Tests', () {
    setUp(() {
      LargeScaleProgressiveRenderingTest.reset();
    });

    test('should handle 150 NYC pins with progressive rendering', () async {
      print('\n🏙️ Testing 150 NYC pins with progressive rendering...');

      // Generate 150 realistic NYC pins
      final pins = LargeScaleProgressiveRenderingTest.generateNYCTestPins(150);
      expect(pins.length, equals(150));

      // Verify NYC coordinate bounds
      for (final pin in pins) {
        final lat = pin['latitude'] as double;
        final lng = pin['longitude'] as double;
        expect(lat, greaterThanOrEqualTo(LargeScaleProgressiveRenderingTest.nycLatMin));
        expect(lat, lessThanOrEqualTo(LargeScaleProgressiveRenderingTest.nycLatMax));
        expect(lng, greaterThanOrEqualTo(LargeScaleProgressiveRenderingTest.nycLngMin));
        expect(lng, lessThanOrEqualTo(LargeScaleProgressiveRenderingTest.nycLngMax));
      }

      // Test initial load progressive rendering
      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(pins, 'initialLoad');

      final results = LargeScaleProgressiveRenderingTest.getTestResults();

      // Verify all pins were rendered
      expect(results['renderedPinCount'], equals(150));
      expect(results['individualLayerFeatures'], equals(150));
      expect(results['clusterLayerFeatures'], greaterThan(0));
      expect(results['totalBatchesProcessed'], greaterThan(10)); // Should be ~13 batches
      expect(results['isPinFetchInProgress'], isFalse);
      expect(results['pendingPinsCount'], equals(0));

      // Print results
      print('✅ Rendered ${results['renderedPinCount']} pins in ${results['totalBatchesProcessed']} batches');
      print('📊 Individual layer: ${results['individualLayerFeatures']} features');
      print('🔗 Cluster layer: ${results['clusterLayerFeatures']} features');
      print('⏱️ Duration: ${results['renderingDuration']}ms');

      // Print processing log
      final log = results['processingLog'] as List<dynamic>;
      for (final entry in log) {
        print('📝 $entry');
      }
    });

    test('should handle 200 NYC pins with incremental updates', () async {
      print('\n🏙️ Testing 200 NYC pins with incremental updates...');

      // Generate initial 100 pins
      final initialPins = LargeScaleProgressiveRenderingTest.generateNYCTestPins(100);
      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(initialPins, 'initialLoad');

      final initialResults = LargeScaleProgressiveRenderingTest.getTestResults();
      expect(initialResults['renderedPinCount'], equals(100));

      // Generate additional 100 pins for incremental update
      LargeScaleProgressiveRenderingTest.reset();
      final allPins = LargeScaleProgressiveRenderingTest.generateNYCTestPins(200);

      // Simulate that first 100 pins are already rendered
      for (int i = 0; i < 100; i++) {
        final pinId = allPins[i]['id']?.toString();
        if (pinId != null) {
          LargeScaleProgressiveRenderingTest.renderedPinIds.add(pinId);
        }
      }

      // Add initial pins to layers
      await LargeScaleProgressiveRenderingTest._addPinBatchToIndividualLayer(initialPins);

      // Test incremental update with all 200 pins
      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(allPins, 'incrementalUpdate');

      final finalResults = LargeScaleProgressiveRenderingTest.getTestResults();

      // Verify accumulative behavior - should have all 200 pins
      expect(finalResults['renderedPinCount'], equals(200));
      expect(finalResults['individualLayerFeatures'], greaterThanOrEqualTo(200));

      print('✅ Incremental update: ${finalResults['renderedPinCount']} total pins');
      print('📊 Individual layer: ${finalResults['individualLayerFeatures']} features');
      print('🔗 Cluster layer: ${finalResults['clusterLayerFeatures']} features');

      // Verify no duplicate pins in rendered set
      final uniqueIds = LargeScaleProgressiveRenderingTest.renderedPinIds.toSet();
      expect(uniqueIds.length, equals(LargeScaleProgressiveRenderingTest.renderedPinIds.length));
    });

    test('should handle map style change with 175 pins', () async {
      print('\n🎨 Testing map style change with 175 pins...');

      final pins = LargeScaleProgressiveRenderingTest.generateNYCTestPins(175);

      // Initial load
      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(pins, 'initialLoad');
      expect(LargeScaleProgressiveRenderingTest.renderedPinIds.length, equals(175));

      // Simulate map style change - should clear and re-render all pins
      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(pins, 'mapStyleChange');

      final results = LargeScaleProgressiveRenderingTest.getTestResults();

      // Verify all pins were re-rendered
      expect(results['renderedPinCount'], equals(175));
      expect(results['individualLayerFeatures'], equals(175));

      print('✅ Map style change: ${results['renderedPinCount']} pins re-rendered');
      print('📊 Batches processed: ${results['totalBatchesProcessed']}');
    });

    test('should handle filter change with 160 pins', () async {
      print('\n🔍 Testing filter change with 160 pins...');

      final pins = LargeScaleProgressiveRenderingTest.generateNYCTestPins(160);

      // Initial load
      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(pins, 'initialLoad');
      expect(LargeScaleProgressiveRenderingTest.renderedPinIds.length, equals(160));

      // Simulate filter change - should clear and re-render filtered pins
      final filteredPins = pins.where((pin) => pin['rarity'] != 'common').toList();
      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(filteredPins, 'filterChange');

      final results = LargeScaleProgressiveRenderingTest.getTestResults();

      // Verify filtered pins were rendered
      expect(results['renderedPinCount'], equals(filteredPins.length));
      expect(results['renderedPinCount'], lessThan(160)); // Should be fewer than original

      print('✅ Filter change: ${results['renderedPinCount']} filtered pins rendered');
      print('📊 Original: 160, Filtered: ${filteredPins.length}');
    });

    test('should handle timeout scenario gracefully', () async {
      print('\n⏰ Testing timeout scenario...');

      final pins = LargeScaleProgressiveRenderingTest.generateNYCTestPins(50);

      // Simulate timeout scenario
      await LargeScaleProgressiveRenderingTest.simulateTimeoutScenario(pins);

      final results = LargeScaleProgressiveRenderingTest.getTestResults();

      // Verify state was properly reset after timeout
      expect(results['isPinFetchInProgress'], isFalse);
      expect(results['pendingPinsCount'], equals(0));

      print('✅ Timeout handled gracefully');
      print('📊 State reset: fetchInProgress=${results['isPinFetchInProgress']}, pending=${results['pendingPinsCount']}');
    });

    test('should maintain performance with large pin counts', () async {
      print('\n⚡ Testing performance with 200 pins...');

      final pins = LargeScaleProgressiveRenderingTest.generateNYCTestPins(200);

      final stopwatch = Stopwatch()..start();
      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(pins, 'initialLoad');
      stopwatch.stop();

      final results = LargeScaleProgressiveRenderingTest.getTestResults();
      final totalTime = stopwatch.elapsedMilliseconds;

      // Performance expectations
      expect(totalTime, lessThan(5000)); // Should complete within 5 seconds
      expect(results['totalBatchesProcessed'], lessThan(25)); // Reasonable batch count

      final pinsPerSecond = (200 / (totalTime / 1000)).round();
      print('✅ Performance: ${totalTime}ms for 200 pins (${pinsPerSecond} pins/sec)');
      print('📊 Batches: ${results['totalBatchesProcessed']}, Avg batch size: ${(200 / results['totalBatchesProcessed']).round()}');
    });

    test('should validate NYC neighborhood distribution', () async {
      print('\n🗽 Testing NYC neighborhood distribution...');

      final pins = LargeScaleProgressiveRenderingTest.generateNYCTestPins(200);

      // Count pins by neighborhood
      final neighborhoodCounts = <String, int>{};
      for (final pin in pins) {
        final neighborhood = pin['neighborhood'] as String;
        neighborhoodCounts[neighborhood] = (neighborhoodCounts[neighborhood] ?? 0) + 1;
      }

      // Verify all neighborhoods are represented
      expect(neighborhoodCounts.keys.length, equals(5));
      expect(neighborhoodCounts.containsKey('Manhattan'), isTrue);
      expect(neighborhoodCounts.containsKey('Brooklyn'), isTrue);
      expect(neighborhoodCounts.containsKey('Queens'), isTrue);
      expect(neighborhoodCounts.containsKey('Bronx'), isTrue);
      expect(neighborhoodCounts.containsKey('Staten Island'), isTrue);

      // Verify Manhattan has the most pins (highest weight)
      expect(neighborhoodCounts['Manhattan'] ?? 0, greaterThan(neighborhoodCounts['Staten Island'] ?? 0));

      print('✅ Neighborhood distribution:');
      for (final entry in neighborhoodCounts.entries) {
        final percentage = ((entry.value / 200) * 100).round();
        print('   ${entry.key}: ${entry.value} pins (${percentage}%)');
      }
    });

    test('should detect and prevent duplicate pins', () async {
      print('\n🔍 Testing duplicate pin detection...');

      final pins = LargeScaleProgressiveRenderingTest.generateNYCTestPins(100);

      // Add some duplicate pins
      final duplicatePins = List<Map<String, dynamic>>.from(pins);
      duplicatePins.addAll(pins.take(10)); // Add first 10 pins again

      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(duplicatePins, 'initialLoad');

      final results = LargeScaleProgressiveRenderingTest.getTestResults();

      // Should only render unique pins
      expect(results['renderedPinCount'], equals(100)); // Not 110

      // Verify no duplicate IDs in rendered set
      final renderedIds = LargeScaleProgressiveRenderingTest.renderedPinIds.toList();
      final uniqueIds = renderedIds.toSet();
      expect(uniqueIds.length, equals(renderedIds.length));

      print('✅ Duplicate prevention: ${duplicatePins.length} input pins → ${results['renderedPinCount']} unique pins');
    });

    test('should handle edge cases gracefully', () async {
      print('\n🧪 Testing edge cases...');

      // Test empty pin list
      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering([], 'initialLoad');
      var results = LargeScaleProgressiveRenderingTest.getTestResults();
      expect(results['renderedPinCount'], equals(0));
      expect(results['totalBatchesProcessed'], equals(0));

      LargeScaleProgressiveRenderingTest.reset();

      // Test single pin
      final singlePin = LargeScaleProgressiveRenderingTest.generateNYCTestPins(1);
      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(singlePin, 'initialLoad');
      results = LargeScaleProgressiveRenderingTest.getTestResults();
      expect(results['renderedPinCount'], equals(1));
      expect(results['totalBatchesProcessed'], equals(1));

      LargeScaleProgressiveRenderingTest.reset();

      // Test pins with invalid coordinates
      final invalidPins = [
        {'id': 'invalid1', 'latitude': null, 'longitude': -73.98, 'title': 'Invalid Pin 1'},
        {'id': 'invalid2', 'latitude': 40.75, 'longitude': null, 'title': 'Invalid Pin 2'},
        {'id': 'valid1', 'latitude': 40.75, 'longitude': -73.98, 'title': 'Valid Pin'},
      ];

      await LargeScaleProgressiveRenderingTest.simulateProgressiveRendering(invalidPins, 'initialLoad');
      results = LargeScaleProgressiveRenderingTest.getTestResults();

      // Should handle invalid coordinates gracefully
      expect(results['renderedPinCount'], greaterThan(0));

      print('✅ Edge cases handled: empty list, single pin, invalid coordinates');
    });
  });
}
