import 'package:flutter_test/flutter_test.dart';
import 'package:bop_maps/utils/music_fallback_utils.dart';

void main() {
  group('Music Fallback Utils Tests', () {
    test('NoExactMatchException should be properly defined', () {
      // Test that NoExactMatchException can be created and has correct properties
      const exception = NoExactMatchException('Test message');

      expect(exception.message, equals('Test message'));
      expect(exception.toString(), equals('NoExactMatchException: Test message'));
    });

    test('NoExactMatchException should be throwable and catchable', () {
      expect(
        () => throw const NoExactMatchException('Test error'),
        throwsA(isA<NoExactMatchException>()),
      );

      try {
        throw const NoExactMatchException('Test error');
      } catch (e) {
        expect(e, isA<NoExactMatchException>());
        expect((e as NoExactMatchException).message, equals('Test error'));
      }
    });
  });
}
