// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in bop_maps/test/pin_layer_manager_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:math' as _i3;
import 'dart:typed_data' as _i8;
import 'dart:ui' as _i7;

import 'package:flutter/material.dart' as _i6;
import 'package:maplibre_gl/maplibre_gl.dart' as _i4;
import 'package:maplibre_gl_platform_interface/maplibre_gl_platform_interface.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeArgumentCallbacks_0<T> extends _i1.SmartFake
    implements _i2.ArgumentCallbacks<T> {
  _FakeArgumentCallbacks_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSymbol_1 extends _i1.SmartFake implements _i2.Symbol {
  _FakeSymbol_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLatLng_2 extends _i1.SmartFake implements _i2.LatLng {
  _FakeLatLng_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLine_3 extends _i1.SmartFake implements _i2.Line {
  _FakeLine_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCircle_4 extends _i1.SmartFake implements _i2.Circle {
  _FakeCircle_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFill_5 extends _i1.SmartFake implements _i2.Fill {
  _FakeFill_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLatLngBounds_6 extends _i1.SmartFake implements _i2.LatLngBounds {
  _FakeLatLngBounds_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePoint_7<T extends num> extends _i1.SmartFake
    implements _i3.Point<T> {
  _FakePoint_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [MaplibreMapController].
///
/// See the documentation for Mockito's code generation for more information.
class MockMaplibreMapController extends _i1.Mock
    implements _i4.MaplibreMapController {
  MockMaplibreMapController() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ArgumentCallbacks<_i2.Symbol> get onSymbolTapped => (super.noSuchMethod(
        Invocation.getter(#onSymbolTapped),
        returnValue: _FakeArgumentCallbacks_0<_i2.Symbol>(
          this,
          Invocation.getter(#onSymbolTapped),
        ),
      ) as _i2.ArgumentCallbacks<_i2.Symbol>);

  @override
  _i2.ArgumentCallbacks<_i2.Circle> get onCircleTapped => (super.noSuchMethod(
        Invocation.getter(#onCircleTapped),
        returnValue: _FakeArgumentCallbacks_0<_i2.Circle>(
          this,
          Invocation.getter(#onCircleTapped),
        ),
      ) as _i2.ArgumentCallbacks<_i2.Circle>);

  @override
  _i2.ArgumentCallbacks<_i2.Fill> get onFillTapped => (super.noSuchMethod(
        Invocation.getter(#onFillTapped),
        returnValue: _FakeArgumentCallbacks_0<_i2.Fill>(
          this,
          Invocation.getter(#onFillTapped),
        ),
      ) as _i2.ArgumentCallbacks<_i2.Fill>);

  @override
  List<_i4.OnFeatureInteractionCallback> get onFeatureTapped =>
      (super.noSuchMethod(
        Invocation.getter(#onFeatureTapped),
        returnValue: <_i4.OnFeatureInteractionCallback>[],
      ) as List<_i4.OnFeatureInteractionCallback>);

  @override
  List<_i4.OnFeatureDragnCallback> get onFeatureDrag => (super.noSuchMethod(
        Invocation.getter(#onFeatureDrag),
        returnValue: <_i4.OnFeatureDragnCallback>[],
      ) as List<_i4.OnFeatureDragnCallback>);

  @override
  _i2.ArgumentCallbacks<_i2.Symbol> get onInfoWindowTapped =>
      (super.noSuchMethod(
        Invocation.getter(#onInfoWindowTapped),
        returnValue: _FakeArgumentCallbacks_0<_i2.Symbol>(
          this,
          Invocation.getter(#onInfoWindowTapped),
        ),
      ) as _i2.ArgumentCallbacks<_i2.Symbol>);

  @override
  _i2.ArgumentCallbacks<_i2.Line> get onLineTapped => (super.noSuchMethod(
        Invocation.getter(#onLineTapped),
        returnValue: _FakeArgumentCallbacks_0<_i2.Line>(
          this,
          Invocation.getter(#onLineTapped),
        ),
      ) as _i2.ArgumentCallbacks<_i2.Line>);

  @override
  Set<_i2.Symbol> get symbols => (super.noSuchMethod(
        Invocation.getter(#symbols),
        returnValue: <_i2.Symbol>{},
      ) as Set<_i2.Symbol>);

  @override
  Set<_i2.Line> get lines => (super.noSuchMethod(
        Invocation.getter(#lines),
        returnValue: <_i2.Line>{},
      ) as Set<_i2.Line>);

  @override
  Set<_i2.Circle> get circles => (super.noSuchMethod(
        Invocation.getter(#circles),
        returnValue: <_i2.Circle>{},
      ) as Set<_i2.Circle>);

  @override
  Set<_i2.Fill> get fills => (super.noSuchMethod(
        Invocation.getter(#fills),
        returnValue: <_i2.Fill>{},
      ) as Set<_i2.Fill>);

  @override
  bool get isCameraMoving => (super.noSuchMethod(
        Invocation.getter(#isCameraMoving),
        returnValue: false,
      ) as bool);

  @override
  set fillManager(_i4.FillManager? _fillManager) => super.noSuchMethod(
        Invocation.setter(
          #fillManager,
          _fillManager,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set lineManager(_i4.LineManager? _lineManager) => super.noSuchMethod(
        Invocation.setter(
          #lineManager,
          _lineManager,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set circleManager(_i4.CircleManager? _circleManager) => super.noSuchMethod(
        Invocation.setter(
          #circleManager,
          _circleManager,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set symbolManager(_i4.SymbolManager? _symbolManager) => super.noSuchMethod(
        Invocation.setter(
          #symbolManager,
          _symbolManager,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  void resizeWebMap() => super.noSuchMethod(
        Invocation.method(
          #resizeWebMap,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void forceResizeWebMap() => super.noSuchMethod(
        Invocation.method(
          #forceResizeWebMap,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<bool?> animateCamera(
    _i2.CameraUpdate? cameraUpdate, {
    Duration? duration,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #animateCamera,
          [cameraUpdate],
          {#duration: duration},
        ),
        returnValue: _i5.Future<bool?>.value(),
      ) as _i5.Future<bool?>);

  @override
  _i5.Future<bool?> moveCamera(_i2.CameraUpdate? cameraUpdate) =>
      (super.noSuchMethod(
        Invocation.method(
          #moveCamera,
          [cameraUpdate],
        ),
        returnValue: _i5.Future<bool?>.value(),
      ) as _i5.Future<bool?>);

  @override
  _i5.Future<void> addGeoJsonSource(
    String? sourceId,
    Map<String, dynamic>? geojson, {
    String? promoteId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addGeoJsonSource,
          [
            sourceId,
            geojson,
          ],
          {#promoteId: promoteId},
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setGeoJsonSource(
    String? sourceId,
    Map<String, dynamic>? geojson,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setGeoJsonSource,
          [
            sourceId,
            geojson,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setGeoJsonFeature(
    String? sourceId,
    Map<String, dynamic>? geojsonFeature,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setGeoJsonFeature,
          [
            sourceId,
            geojsonFeature,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addSymbolLayer(
    String? sourceId,
    String? layerId,
    _i4.SymbolLayerProperties? properties, {
    String? belowLayerId,
    String? sourceLayer,
    double? minzoom,
    double? maxzoom,
    dynamic filter,
    bool? enableInteraction = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addSymbolLayer,
          [
            sourceId,
            layerId,
            properties,
          ],
          {
            #belowLayerId: belowLayerId,
            #sourceLayer: sourceLayer,
            #minzoom: minzoom,
            #maxzoom: maxzoom,
            #filter: filter,
            #enableInteraction: enableInteraction,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addLineLayer(
    String? sourceId,
    String? layerId,
    _i4.LineLayerProperties? properties, {
    String? belowLayerId,
    String? sourceLayer,
    double? minzoom,
    double? maxzoom,
    dynamic filter,
    bool? enableInteraction = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addLineLayer,
          [
            sourceId,
            layerId,
            properties,
          ],
          {
            #belowLayerId: belowLayerId,
            #sourceLayer: sourceLayer,
            #minzoom: minzoom,
            #maxzoom: maxzoom,
            #filter: filter,
            #enableInteraction: enableInteraction,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setLayerProperties(
    String? layerId,
    _i4.LayerProperties? properties,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setLayerProperties,
          [
            layerId,
            properties,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addFillLayer(
    String? sourceId,
    String? layerId,
    _i4.FillLayerProperties? properties, {
    String? belowLayerId,
    String? sourceLayer,
    double? minzoom,
    double? maxzoom,
    dynamic filter,
    bool? enableInteraction = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addFillLayer,
          [
            sourceId,
            layerId,
            properties,
          ],
          {
            #belowLayerId: belowLayerId,
            #sourceLayer: sourceLayer,
            #minzoom: minzoom,
            #maxzoom: maxzoom,
            #filter: filter,
            #enableInteraction: enableInteraction,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addFillExtrusionLayer(
    String? sourceId,
    String? layerId,
    _i4.FillExtrusionLayerProperties? properties, {
    String? belowLayerId,
    String? sourceLayer,
    double? minzoom,
    double? maxzoom,
    dynamic filter,
    bool? enableInteraction = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addFillExtrusionLayer,
          [
            sourceId,
            layerId,
            properties,
          ],
          {
            #belowLayerId: belowLayerId,
            #sourceLayer: sourceLayer,
            #minzoom: minzoom,
            #maxzoom: maxzoom,
            #filter: filter,
            #enableInteraction: enableInteraction,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addCircleLayer(
    String? sourceId,
    String? layerId,
    _i4.CircleLayerProperties? properties, {
    String? belowLayerId,
    String? sourceLayer,
    double? minzoom,
    double? maxzoom,
    dynamic filter,
    bool? enableInteraction = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addCircleLayer,
          [
            sourceId,
            layerId,
            properties,
          ],
          {
            #belowLayerId: belowLayerId,
            #sourceLayer: sourceLayer,
            #minzoom: minzoom,
            #maxzoom: maxzoom,
            #filter: filter,
            #enableInteraction: enableInteraction,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addRasterLayer(
    String? sourceId,
    String? layerId,
    _i4.RasterLayerProperties? properties, {
    String? belowLayerId,
    String? sourceLayer,
    double? minzoom,
    double? maxzoom,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addRasterLayer,
          [
            sourceId,
            layerId,
            properties,
          ],
          {
            #belowLayerId: belowLayerId,
            #sourceLayer: sourceLayer,
            #minzoom: minzoom,
            #maxzoom: maxzoom,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addHillshadeLayer(
    String? sourceId,
    String? layerId,
    _i4.HillshadeLayerProperties? properties, {
    String? belowLayerId,
    String? sourceLayer,
    double? minzoom,
    double? maxzoom,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addHillshadeLayer,
          [
            sourceId,
            layerId,
            properties,
          ],
          {
            #belowLayerId: belowLayerId,
            #sourceLayer: sourceLayer,
            #minzoom: minzoom,
            #maxzoom: maxzoom,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addHeatmapLayer(
    String? sourceId,
    String? layerId,
    _i4.HeatmapLayerProperties? properties, {
    String? belowLayerId,
    String? sourceLayer,
    double? minzoom,
    double? maxzoom,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addHeatmapLayer,
          [
            sourceId,
            layerId,
            properties,
          ],
          {
            #belowLayerId: belowLayerId,
            #sourceLayer: sourceLayer,
            #minzoom: minzoom,
            #maxzoom: maxzoom,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> updateMyLocationTrackingMode(
          _i2.MyLocationTrackingMode? myLocationTrackingMode) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateMyLocationTrackingMode,
          [myLocationTrackingMode],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> matchMapLanguageWithDeviceDefault() => (super.noSuchMethod(
        Invocation.method(
          #matchMapLanguageWithDeviceDefault,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> updateContentInsets(
    _i6.EdgeInsets? insets, [
    bool? animated = false,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateContentInsets,
          [
            insets,
            animated,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setMapLanguage(String? language) => (super.noSuchMethod(
        Invocation.method(
          #setMapLanguage,
          [language],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setTelemetryEnabled(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setTelemetryEnabled,
          [enabled],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool> getTelemetryEnabled() => (super.noSuchMethod(
        Invocation.method(
          #getTelemetryEnabled,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<_i2.Symbol> addSymbol(
    _i2.SymbolOptions? options, [
    Map<dynamic, dynamic>? data,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #addSymbol,
          [
            options,
            data,
          ],
        ),
        returnValue: _i5.Future<_i2.Symbol>.value(_FakeSymbol_1(
          this,
          Invocation.method(
            #addSymbol,
            [
              options,
              data,
            ],
          ),
        )),
      ) as _i5.Future<_i2.Symbol>);

  @override
  _i5.Future<List<_i2.Symbol>> addSymbols(
    List<_i2.SymbolOptions>? options, [
    List<Map<dynamic, dynamic>>? data,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #addSymbols,
          [
            options,
            data,
          ],
        ),
        returnValue: _i5.Future<List<_i2.Symbol>>.value(<_i2.Symbol>[]),
      ) as _i5.Future<List<_i2.Symbol>>);

  @override
  _i5.Future<void> updateSymbol(
    _i2.Symbol? symbol,
    _i2.SymbolOptions? changes,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateSymbol,
          [
            symbol,
            changes,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i2.LatLng> getSymbolLatLng(_i2.Symbol? symbol) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSymbolLatLng,
          [symbol],
        ),
        returnValue: _i5.Future<_i2.LatLng>.value(_FakeLatLng_2(
          this,
          Invocation.method(
            #getSymbolLatLng,
            [symbol],
          ),
        )),
      ) as _i5.Future<_i2.LatLng>);

  @override
  _i5.Future<void> removeSymbol(_i2.Symbol? symbol) => (super.noSuchMethod(
        Invocation.method(
          #removeSymbol,
          [symbol],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeSymbols(Iterable<_i2.Symbol>? symbols) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeSymbols,
          [symbols],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> clearSymbols() => (super.noSuchMethod(
        Invocation.method(
          #clearSymbols,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i2.Line> addLine(
    _i2.LineOptions? options, [
    Map<dynamic, dynamic>? data,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #addLine,
          [
            options,
            data,
          ],
        ),
        returnValue: _i5.Future<_i2.Line>.value(_FakeLine_3(
          this,
          Invocation.method(
            #addLine,
            [
              options,
              data,
            ],
          ),
        )),
      ) as _i5.Future<_i2.Line>);

  @override
  _i5.Future<List<_i2.Line>> addLines(
    List<_i2.LineOptions>? options, [
    List<Map<dynamic, dynamic>>? data,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #addLines,
          [
            options,
            data,
          ],
        ),
        returnValue: _i5.Future<List<_i2.Line>>.value(<_i2.Line>[]),
      ) as _i5.Future<List<_i2.Line>>);

  @override
  _i5.Future<void> updateLine(
    _i2.Line? line,
    _i2.LineOptions? changes,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateLine,
          [
            line,
            changes,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i2.LatLng>> getLineLatLngs(_i2.Line? line) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLineLatLngs,
          [line],
        ),
        returnValue: _i5.Future<List<_i2.LatLng>>.value(<_i2.LatLng>[]),
      ) as _i5.Future<List<_i2.LatLng>>);

  @override
  _i5.Future<void> removeLine(_i2.Line? line) => (super.noSuchMethod(
        Invocation.method(
          #removeLine,
          [line],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeLines(Iterable<_i2.Line>? lines) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeLines,
          [lines],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> clearLines() => (super.noSuchMethod(
        Invocation.method(
          #clearLines,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i2.Circle> addCircle(
    _i2.CircleOptions? options, [
    Map<dynamic, dynamic>? data,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #addCircle,
          [
            options,
            data,
          ],
        ),
        returnValue: _i5.Future<_i2.Circle>.value(_FakeCircle_4(
          this,
          Invocation.method(
            #addCircle,
            [
              options,
              data,
            ],
          ),
        )),
      ) as _i5.Future<_i2.Circle>);

  @override
  _i5.Future<List<_i2.Circle>> addCircles(
    List<_i2.CircleOptions>? options, [
    List<Map<dynamic, dynamic>>? data,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #addCircles,
          [
            options,
            data,
          ],
        ),
        returnValue: _i5.Future<List<_i2.Circle>>.value(<_i2.Circle>[]),
      ) as _i5.Future<List<_i2.Circle>>);

  @override
  _i5.Future<void> updateCircle(
    _i2.Circle? circle,
    _i2.CircleOptions? changes,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCircle,
          [
            circle,
            changes,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i2.LatLng> getCircleLatLng(_i2.Circle? circle) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCircleLatLng,
          [circle],
        ),
        returnValue: _i5.Future<_i2.LatLng>.value(_FakeLatLng_2(
          this,
          Invocation.method(
            #getCircleLatLng,
            [circle],
          ),
        )),
      ) as _i5.Future<_i2.LatLng>);

  @override
  _i5.Future<void> removeCircle(_i2.Circle? circle) => (super.noSuchMethod(
        Invocation.method(
          #removeCircle,
          [circle],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeCircles(Iterable<_i2.Circle>? circles) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeCircles,
          [circles],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> clearCircles() => (super.noSuchMethod(
        Invocation.method(
          #clearCircles,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i2.Fill> addFill(
    _i2.FillOptions? options, [
    Map<dynamic, dynamic>? data,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #addFill,
          [
            options,
            data,
          ],
        ),
        returnValue: _i5.Future<_i2.Fill>.value(_FakeFill_5(
          this,
          Invocation.method(
            #addFill,
            [
              options,
              data,
            ],
          ),
        )),
      ) as _i5.Future<_i2.Fill>);

  @override
  _i5.Future<List<_i2.Fill>> addFills(
    List<_i2.FillOptions>? options, [
    List<Map<dynamic, dynamic>>? data,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #addFills,
          [
            options,
            data,
          ],
        ),
        returnValue: _i5.Future<List<_i2.Fill>>.value(<_i2.Fill>[]),
      ) as _i5.Future<List<_i2.Fill>>);

  @override
  _i5.Future<void> updateFill(
    _i2.Fill? fill,
    _i2.FillOptions? changes,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateFill,
          [
            fill,
            changes,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> clearFills() => (super.noSuchMethod(
        Invocation.method(
          #clearFills,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeFill(_i2.Fill? fill) => (super.noSuchMethod(
        Invocation.method(
          #removeFill,
          [fill],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeFills(Iterable<_i2.Fill>? fills) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeFills,
          [fills],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<dynamic>> queryRenderedFeatures(
    _i3.Point<double>? point,
    List<String>? layerIds,
    List<Object>? filter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #queryRenderedFeatures,
          [
            point,
            layerIds,
            filter,
          ],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<List<dynamic>> queryRenderedFeaturesInRect(
    _i7.Rect? rect,
    List<String>? layerIds,
    String? filter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #queryRenderedFeaturesInRect,
          [
            rect,
            layerIds,
            filter,
          ],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<List<dynamic>> querySourceFeatures(
    String? sourceId,
    String? sourceLayerId,
    List<Object>? filter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #querySourceFeatures,
          [
            sourceId,
            sourceLayerId,
            filter,
          ],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<dynamic> invalidateAmbientCache() => (super.noSuchMethod(
        Invocation.method(
          #invalidateAmbientCache,
          [],
        ),
        returnValue: _i5.Future<dynamic>.value(),
      ) as _i5.Future<dynamic>);

  @override
  _i5.Future<dynamic> clearAmbientCache() => (super.noSuchMethod(
        Invocation.method(
          #clearAmbientCache,
          [],
        ),
        returnValue: _i5.Future<dynamic>.value(),
      ) as _i5.Future<dynamic>);

  @override
  _i5.Future<_i2.LatLng?> requestMyLocationLatLng() => (super.noSuchMethod(
        Invocation.method(
          #requestMyLocationLatLng,
          [],
        ),
        returnValue: _i5.Future<_i2.LatLng?>.value(),
      ) as _i5.Future<_i2.LatLng?>);

  @override
  _i5.Future<_i2.LatLngBounds> getVisibleRegion() => (super.noSuchMethod(
        Invocation.method(
          #getVisibleRegion,
          [],
        ),
        returnValue: _i5.Future<_i2.LatLngBounds>.value(_FakeLatLngBounds_6(
          this,
          Invocation.method(
            #getVisibleRegion,
            [],
          ),
        )),
      ) as _i5.Future<_i2.LatLngBounds>);

  @override
  _i5.Future<void> addImage(
    String? name,
    _i8.Uint8List? bytes, [
    bool? sdf = false,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #addImage,
          [
            name,
            bytes,
            sdf,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setSymbolIconAllowOverlap(bool? enable) =>
      (super.noSuchMethod(
        Invocation.method(
          #setSymbolIconAllowOverlap,
          [enable],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setSymbolIconIgnorePlacement(bool? enable) =>
      (super.noSuchMethod(
        Invocation.method(
          #setSymbolIconIgnorePlacement,
          [enable],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setSymbolTextAllowOverlap(bool? enable) =>
      (super.noSuchMethod(
        Invocation.method(
          #setSymbolTextAllowOverlap,
          [enable],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setSymbolTextIgnorePlacement(bool? enable) =>
      (super.noSuchMethod(
        Invocation.method(
          #setSymbolTextIgnorePlacement,
          [enable],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addImageSource(
    String? imageSourceId,
    _i8.Uint8List? bytes,
    _i2.LatLngQuad? coordinates,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #addImageSource,
          [
            imageSourceId,
            bytes,
            coordinates,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> updateImageSource(
    String? imageSourceId,
    _i8.Uint8List? bytes,
    _i2.LatLngQuad? coordinates,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateImageSource,
          [
            imageSourceId,
            bytes,
            coordinates,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeImageSource(String? imageSourceId) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeImageSource,
          [imageSourceId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeSource(String? sourceId) => (super.noSuchMethod(
        Invocation.method(
          #removeSource,
          [sourceId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addImageLayer(
    String? layerId,
    String? imageSourceId, {
    double? minzoom,
    double? maxzoom,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addImageLayer,
          [
            layerId,
            imageSourceId,
          ],
          {
            #minzoom: minzoom,
            #maxzoom: maxzoom,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addImageLayerBelow(
    String? layerId,
    String? sourceId,
    String? imageSourceId, {
    double? minzoom,
    double? maxzoom,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addImageLayerBelow,
          [
            layerId,
            sourceId,
            imageSourceId,
          ],
          {
            #minzoom: minzoom,
            #maxzoom: maxzoom,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> addLayerBelow(
    String? layerId,
    String? sourceId,
    String? imageSourceId, {
    double? minzoom,
    double? maxzoom,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addLayerBelow,
          [
            layerId,
            sourceId,
            imageSourceId,
          ],
          {
            #minzoom: minzoom,
            #maxzoom: maxzoom,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeLayer(String? layerId) => (super.noSuchMethod(
        Invocation.method(
          #removeLayer,
          [layerId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setFilter(
    String? layerId,
    dynamic filter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setFilter,
          [
            layerId,
            filter,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<dynamic> getFilter(String? layerId) => (super.noSuchMethod(
        Invocation.method(
          #getFilter,
          [layerId],
        ),
        returnValue: _i5.Future<dynamic>.value(),
      ) as _i5.Future<dynamic>);

  @override
  _i5.Future<_i3.Point<num>> toScreenLocation(_i2.LatLng? latLng) =>
      (super.noSuchMethod(
        Invocation.method(
          #toScreenLocation,
          [latLng],
        ),
        returnValue: _i5.Future<_i3.Point<num>>.value(_FakePoint_7<num>(
          this,
          Invocation.method(
            #toScreenLocation,
            [latLng],
          ),
        )),
      ) as _i5.Future<_i3.Point<num>>);

  @override
  _i5.Future<List<_i3.Point<num>>> toScreenLocationBatch(
          Iterable<_i2.LatLng>? latLngs) =>
      (super.noSuchMethod(
        Invocation.method(
          #toScreenLocationBatch,
          [latLngs],
        ),
        returnValue: _i5.Future<List<_i3.Point<num>>>.value(<_i3.Point<num>>[]),
      ) as _i5.Future<List<_i3.Point<num>>>);

  @override
  _i5.Future<_i2.LatLng> toLatLng(_i3.Point<num>? screenLocation) =>
      (super.noSuchMethod(
        Invocation.method(
          #toLatLng,
          [screenLocation],
        ),
        returnValue: _i5.Future<_i2.LatLng>.value(_FakeLatLng_2(
          this,
          Invocation.method(
            #toLatLng,
            [screenLocation],
          ),
        )),
      ) as _i5.Future<_i2.LatLng>);

  @override
  _i5.Future<double> getMetersPerPixelAtLatitude(double? latitude) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMetersPerPixelAtLatitude,
          [latitude],
        ),
        returnValue: _i5.Future<double>.value(0.0),
      ) as _i5.Future<double>);

  @override
  _i5.Future<void> addSource(
    String? sourceid,
    _i2.SourceProperties? properties,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #addSource,
          [
            sourceid,
            properties,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<dynamic> setCameraBounds({
    required double? west,
    required double? north,
    required double? south,
    required double? east,
    required int? padding,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setCameraBounds,
          [],
          {
            #west: west,
            #north: north,
            #south: south,
            #east: east,
            #padding: padding,
          },
        ),
        returnValue: _i5.Future<dynamic>.value(),
      ) as _i5.Future<dynamic>);

  @override
  _i5.Future<void> addLayer(
    String? sourceId,
    String? layerId,
    _i4.LayerProperties? properties, {
    String? belowLayerId,
    bool? enableInteraction = true,
    String? sourceLayer,
    double? minzoom,
    double? maxzoom,
    dynamic filter,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addLayer,
          [
            sourceId,
            layerId,
            properties,
          ],
          {
            #belowLayerId: belowLayerId,
            #enableInteraction: enableInteraction,
            #sourceLayer: sourceLayer,
            #minzoom: minzoom,
            #maxzoom: maxzoom,
            #filter: filter,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setLayerVisibility(
    String? layerId,
    bool? visible,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setLayerVisibility,
          [
            layerId,
            visible,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<dynamic>> getLayerIds() => (super.noSuchMethod(
        Invocation.method(
          #getLayerIds,
          [],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<List<String>> getSourceIds() => (super.noSuchMethod(
        Invocation.method(
          #getSourceIds,
          [],
        ),
        returnValue: _i5.Future<List<String>>.value(<String>[]),
      ) as _i5.Future<List<String>>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(_i7.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i7.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
