import 'package:flutter_test/flutter_test.dart';

// Mock implementation to test accumulative pin rendering logic
class AccumulativePinRenderingTest {
  static Set<String> renderedPinIds = <String>{};
  static Map<String, List<Map<String, dynamic>>> layerFeatures = {};
  
  // Mock the rendering context enum
  static String currentRenderingContext = 'incrementalUpdate';
  
  // Mock method to simulate _addToLayerFeatures
  static void addToLayerFeatures(String sourceId, List<Map<String, dynamic>> features) {
    if (!layerFeatures.containsKey(sourceId)) {
      layerFeatures[sourceId] = [];
    }
    layerFeatures[sourceId]!.addAll(features);
  }
  
  // Mock method to simulate _clearLayerFeatures
  static void clearLayerFeatures([String? sourceId]) {
    if (sourceId != null) {
      layerFeatures[sourceId]?.clear();
    } else {
      layerFeatures.clear();
    }
  }
  
  // Mock method to simulate getting current layer features
  static List<Map<String, dynamic>> getCurrentLayerFeatures(String sourceId) {
    return layerFeatures[sourceId] ?? [];
  }
  
  // Simulate the fixed progressive rendering logic
  static List<Map<String, dynamic>> simulateProgressiveRendering(
    List<Map<String, dynamic>> pinData,
    String renderingContext,
  ) {
    currentRenderingContext = renderingContext;
    
    // Only clear rendered pins for specific contexts that require fresh rendering
    if (renderingContext == 'initialLoad' ||
        renderingContext == 'mapStyleChange' ||
        renderingContext == 'filterChange') {
      renderedPinIds.clear();
      print('Cleared rendered pins for fresh $renderingContext rendering');
    }

    // For accumulative rendering, only add unrendered pins
    List<Map<String, dynamic>> pinsToRender;
    if (renderingContext != 'initialLoad' &&
        renderingContext != 'mapStyleChange' &&
        renderingContext != 'filterChange') {
      // Filter out already rendered pins to maintain accumulative behavior
      pinsToRender = pinData.where((pin) {
        final pinId = pin['id']?.toString();
        return pinId != null && !renderedPinIds.contains(pinId);
      }).toList();
      print('Added ${pinsToRender.length} unrendered pins for accumulative progressive rendering');
    } else {
      pinsToRender = pinData;
      print('Added ${pinsToRender.length} pins for fresh progressive rendering');
    }
    
    return pinsToRender;
  }
  
  // Simulate adding a batch to individual layer
  static void simulateAddPinBatchToIndividualLayer(List<Map<String, dynamic>> batch) {
    // Filter out already processed pins
    final newFeatures = <Map<String, dynamic>>[];
    final processedPinIds = <String>{};
    
    for (final pin in batch) {
      final pinId = pin['id']?.toString();
      if (pinId != null && !processedPinIds.contains(pinId)) {
        processedPinIds.add(pinId);
        newFeatures.add({
          'type': 'Feature',
          'geometry': {
            'type': 'Point',
            'coordinates': [pin['longitude'], pin['latitude']],
          },
          'properties': {
            'id': pinId,
            'title': pin['title'] ?? 'Pin',
          },
        });
      }
    }
    
    // Add to memory tracking
    addToLayerFeatures('individual-pins-source', newFeatures);
    
    // Mark pins as rendered
    for (final pin in batch) {
      final pinId = pin['id']?.toString();
      if (pinId != null) {
        renderedPinIds.add(pinId);
      }
    }
    
    print('Added ${newFeatures.length} pins to individual layer (total: ${getCurrentLayerFeatures('individual-pins-source').length})');
  }
  
  // Simulate adding a batch to cluster layer
  static void simulateAddPinBatchToClusters(List<Map<String, dynamic>> batch, bool forceFullUpdate) {
    // Get all pins rendered so far plus new batch
    final allRenderedPins = <Map<String, dynamic>>[];
    
    // Add previously rendered pins (simulated)
    for (final pinId in renderedPinIds) {
      allRenderedPins.add({'id': pinId, 'latitude': 0.0, 'longitude': 0.0});
    }
    
    // Add new batch
    allRenderedPins.addAll(batch);
    
    // Simulate cluster creation (simplified)
    final clusters = <Map<String, dynamic>>[];
    for (int i = 0; i < allRenderedPins.length; i += 3) {
      if (i + 2 < allRenderedPins.length) {
        // Create a cluster
        clusters.add({
          'id': 'cluster_$i',
          'isCluster': true,
          'count': 3,
          'latitude': 0.0,
          'longitude': 0.0,
        });
      } else {
        // Add remaining pins as individuals
        for (int j = i; j < allRenderedPins.length; j++) {
          clusters.add({
            ...allRenderedPins[j],
            'isCluster': false,
          });
        }
      }
    }
    
    // Update cluster layer features
    if (forceFullUpdate) {
      clearLayerFeatures('cluster-pins-source');
      addToLayerFeatures('cluster-pins-source', clusters);
      print('Full cluster layer update with ${clusters.length} features');
    } else {
      addToLayerFeatures('cluster-pins-source', clusters);
      print('Incremental cluster layer update: ${clusters.length} new features');
    }
  }
  
  // Reset state for testing
  static void reset() {
    renderedPinIds.clear();
    layerFeatures.clear();
    currentRenderingContext = 'incrementalUpdate';
  }
}

void main() {
  group('Accumulative Pin Rendering Tests', () {
    setUp(() {
      AccumulativePinRenderingTest.reset();
    });

    test('should accumulate pins across multiple batches for incremental updates', () {
      // First batch
      final batch1 = [
        {'id': 'pin1', 'latitude': 1.0, 'longitude': 1.0, 'title': 'Pin 1'},
        {'id': 'pin2', 'latitude': 2.0, 'longitude': 2.0, 'title': 'Pin 2'},
      ];
      
      var pinsToRender = AccumulativePinRenderingTest.simulateProgressiveRendering(
        batch1, 'incrementalUpdate');
      expect(pinsToRender.length, equals(2));
      
      AccumulativePinRenderingTest.simulateAddPinBatchToIndividualLayer(batch1);
      expect(AccumulativePinRenderingTest.renderedPinIds.length, equals(2));
      expect(AccumulativePinRenderingTest.getCurrentLayerFeatures('individual-pins-source').length, equals(2));
      
      // Second batch - should accumulate, not replace
      final batch2 = [
        {'id': 'pin3', 'latitude': 3.0, 'longitude': 3.0, 'title': 'Pin 3'},
        {'id': 'pin4', 'latitude': 4.0, 'longitude': 4.0, 'title': 'Pin 4'},
      ];
      
      pinsToRender = AccumulativePinRenderingTest.simulateProgressiveRendering(
        batch2, 'incrementalUpdate');
      expect(pinsToRender.length, equals(2)); // Only new pins
      
      AccumulativePinRenderingTest.simulateAddPinBatchToIndividualLayer(batch2);
      expect(AccumulativePinRenderingTest.renderedPinIds.length, equals(4));
      expect(AccumulativePinRenderingTest.getCurrentLayerFeatures('individual-pins-source').length, equals(4));
    });

    test('should clear pins for fresh rendering contexts', () {
      // Add initial pins
      final initialBatch = [
        {'id': 'pin1', 'latitude': 1.0, 'longitude': 1.0, 'title': 'Pin 1'},
        {'id': 'pin2', 'latitude': 2.0, 'longitude': 2.0, 'title': 'Pin 2'},
      ];
      
      AccumulativePinRenderingTest.simulateAddPinBatchToIndividualLayer(initialBatch);
      expect(AccumulativePinRenderingTest.renderedPinIds.length, equals(2));
      
      // Simulate map style change - should clear rendered pins
      final newBatch = [
        {'id': 'pin3', 'latitude': 3.0, 'longitude': 3.0, 'title': 'Pin 3'},
      ];
      
      var pinsToRender = AccumulativePinRenderingTest.simulateProgressiveRendering(
        newBatch, 'mapStyleChange');
      expect(AccumulativePinRenderingTest.renderedPinIds.length, equals(0)); // Should be cleared
      expect(pinsToRender.length, equals(1)); // All pins should be rendered
    });

    test('should not clear pins for incremental updates', () {
      // Add initial pins
      final initialBatch = [
        {'id': 'pin1', 'latitude': 1.0, 'longitude': 1.0, 'title': 'Pin 1'},
        {'id': 'pin2', 'latitude': 2.0, 'longitude': 2.0, 'title': 'Pin 2'},
      ];
      
      AccumulativePinRenderingTest.simulateAddPinBatchToIndividualLayer(initialBatch);
      expect(AccumulativePinRenderingTest.renderedPinIds.length, equals(2));
      
      // Simulate incremental update - should NOT clear rendered pins
      final newBatch = [
        {'id': 'pin3', 'latitude': 3.0, 'longitude': 3.0, 'title': 'Pin 3'},
      ];
      
      var pinsToRender = AccumulativePinRenderingTest.simulateProgressiveRendering(
        newBatch, 'incrementalUpdate');
      expect(AccumulativePinRenderingTest.renderedPinIds.length, equals(2)); // Should NOT be cleared
      expect(pinsToRender.length, equals(1)); // Only new pins should be rendered
    });

    test('should handle cluster layer updates with proper forceFullUpdate flag', () {
      final batch = [
        {'id': 'pin1', 'latitude': 1.0, 'longitude': 1.0, 'title': 'Pin 1'},
        {'id': 'pin2', 'latitude': 2.0, 'longitude': 2.0, 'title': 'Pin 2'},
      ];

      AccumulativePinRenderingTest.simulateAddPinBatchToIndividualLayer(batch);

      // Test incremental cluster update (should not force full update)
      AccumulativePinRenderingTest.currentRenderingContext = 'incrementalUpdate';
      AccumulativePinRenderingTest.simulateAddPinBatchToClusters(batch, false);

      // Test fresh rendering context (should force full update)
      AccumulativePinRenderingTest.currentRenderingContext = 'mapStyleChange';
      AccumulativePinRenderingTest.simulateAddPinBatchToClusters(batch, true);

      // Verify cluster features were added
      expect(AccumulativePinRenderingTest.getCurrentLayerFeatures('cluster-pins-source').isNotEmpty, isTrue);
    });

    test('should properly reset progressive rendering state', () {
      // Simulate progressive rendering in progress
      AccumulativePinRenderingTest.renderedPinIds.addAll(['pin1', 'pin2']);

      // Verify state before reset
      expect(AccumulativePinRenderingTest.renderedPinIds.length, equals(2));

      // Reset state
      AccumulativePinRenderingTest.reset();

      // Verify state after reset
      expect(AccumulativePinRenderingTest.renderedPinIds.length, equals(0));
      expect(AccumulativePinRenderingTest.layerFeatures.isEmpty, isTrue);
      expect(AccumulativePinRenderingTest.currentRenderingContext, equals('incrementalUpdate'));
    });
  });
}
