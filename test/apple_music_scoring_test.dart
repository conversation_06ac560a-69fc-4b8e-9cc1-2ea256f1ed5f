import 'package:flutter_test/flutter_test.dart';
import 'package:bop_maps/models/music_track.dart';

void main() {
  group('Apple Music Scoring Tests', () {
    test('Duration scoring should heavily penalize short tracks', () {
      // Create a target track (full song)
      final targetTrack = MusicTrack(
        id: 'target',
        title: 'Animals',
        artist: '<PERSON>',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 180000, // 3 minutes
        uri: '',
      );

      // Create a short track (likely a preview/clip)
      final shortTrack = MusicTrack(
        id: 'short',
        title: 'Animals (Mixed)',
        artist: '<PERSON>',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 37000, // 37 seconds
        uri: '',
        explicit: true,
      );

      // Create a full-length track
      final fullTrack = MusicTrack(
        id: 'full',
        title: 'Animals',
        artist: '<PERSON>',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 175000, // 2:55 (close to target)
        uri: '',
        explicit: false,
      );

      final shortScore = targetTrack.calculateMatchScore(shortTrack);
      final fullScore = targetTrack.calculateMatchScore(fullTrack);

      print('Short track score: ${shortScore.toStringAsFixed(1)}');
      print('Full track score: ${fullScore.toStringAsFixed(1)}');

      // Full track should score much higher than short track
      expect(fullScore, greaterThan(shortScore + 100));
      
      // Short track should get heavy penalty for being under 1 minute
      expect(shortScore, lessThan(800)); // Should be penalized heavily
    });

    test('Explicit prioritization should only work for exact matches', () {
      // Create target track
      final targetTrack = MusicTrack(
        id: 'target',
        title: 'Animals',
        artist: 'Martin Garrix',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 180000,
        uri: '',
      );

      // Exact match - explicit
      final exactExplicit = MusicTrack(
        id: 'exact_explicit',
        title: 'Animals',
        artist: 'Martin Garrix',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 175000,
        uri: '',
        explicit: true,
      );

      // Exact match - clean
      final exactClean = MusicTrack(
        id: 'exact_clean',
        title: 'Animals',
        artist: 'Martin Garrix',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 175000,
        uri: '',
        explicit: false,
      );

      // Different track - explicit (should not be prioritized)
      final differentExplicit = MusicTrack(
        id: 'different_explicit',
        title: 'Animals (Remix)',
        artist: 'Martin Garrix',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 175000,
        uri: '',
        explicit: true,
      );

      final exactExplicitScore = targetTrack.calculateMatchScore(exactExplicit);
      final exactCleanScore = targetTrack.calculateMatchScore(exactClean);
      final differentExplicitScore = targetTrack.calculateMatchScore(differentExplicit);

      print('Exact explicit score: ${exactExplicitScore.toStringAsFixed(1)}');
      print('Exact clean score: ${exactCleanScore.toStringAsFixed(1)}');
      print('Different explicit score: ${differentExplicitScore.toStringAsFixed(1)}');

      // Both exact matches should score very high
      expect(exactExplicitScore, greaterThan(900));
      expect(exactCleanScore, greaterThan(900));
      
      // Different track should score lower even if explicit
      expect(differentExplicitScore, lessThan(exactCleanScore));
      
      // Exact matches should be very close in score (explicit bonus is small)
      expect((exactExplicitScore - exactCleanScore).abs(), lessThan(50));
    });

    test('Normalized title matching should work correctly', () {
      final track1 = MusicTrack(
        id: '1',
        title: 'Animals',
        artist: 'Martin Garrix',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 180000,
        uri: '',
      );

      final track2 = MusicTrack(
        id: '2',
        title: 'Animals (Mixed)',
        artist: 'Martin Garrix',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 180000,
        uri: '',
      );

      print('Track1 normalized title: "${track1.normalizedTitle}"');
      print('Track2 normalized title: "${track2.normalizedTitle}"');

      // Normalized titles should NOT be the same now (Mixed should be preserved)
      expect(track1.normalizedTitle, isNot(equals(track2.normalizedTitle)));

      // Artists should be the same
      expect(track1.normalizedArtist, equals(track2.normalizedArtist));

      // Track1 should be "animals", Track2 should be "animals mixed"
      expect(track1.normalizedTitle, equals('animals'));
      expect(track2.normalizedTitle, equals('animals mixed'));
    });
  });
}
