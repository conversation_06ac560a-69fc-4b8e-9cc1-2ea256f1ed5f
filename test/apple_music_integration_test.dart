import 'package:flutter_test/flutter_test.dart';
import '../lib/services/music/shuffle_service.dart';

void main() {
  group('Apple Music Integration Tests', () {
    
    test('ShuffleService should handle Apple Music tracks correctly', () {
      // Create test Apple Music tracks
      final appleMusicTracks = [
        {
          'id': 'apple_1',
          'title': 'Apple Song 1',
          'artist': 'Apple Artist 1',
          'album': 'Apple Album 1',
          'platform': 'apple_music',
          'imageUrl': 'https://example.com/art1.jpg',
          'track_url': 'apple:song:1',
          'duration': '3:30',
          'popularity': 80,
        },
        {
          'id': 'apple_2',
          'title': 'Apple Song 2',
          'artist': 'Apple Artist 2',
          'album': 'Apple Album 2',
          'platform': 'apple_music',
          'imageUrl': 'https://example.com/art2.jpg',
          'track_url': 'apple:song:2',
          'duration': '4:15',
          'popularity': 75,
        },
        {
          'id': 'apple_3',
          'title': 'Apple Song 3',
          'artist': 'Apple Artist 3',
          'album': 'Apple Album 3',
          'platform': 'apple_music',
          'imageUrl': 'https://example.com/art3.jpg',
          'track_url': 'apple:song:3',
          'duration': '3:45',
          'popularity': 90,
        },
      ];

      // Test that we can identify Apple Music tracks
      expect(appleMusicTracks.every((track) => track['platform'] == 'apple_music'), isTrue);
    });

    test('Shuffle modes should be available', () {
      // Test that all shuffle modes are available
      expect(ShuffleMode.values.length, equals(3));
      expect(ShuffleMode.values.contains(ShuffleMode.random), isTrue);
      expect(ShuffleMode.values.contains(ShuffleMode.weighted), isTrue);
      expect(ShuffleMode.values.contains(ShuffleMode.artistSpaced), isTrue);
    });

    test('ShuffleResult should contain proper information', () {
      const result = ShuffleResult(
        success: true,
        message: 'Test message',
        tracksQueued: 5,
        platform: 'apple',
      );

      expect(result.success, isTrue);
      expect(result.message, equals('Test message'));
      expect(result.tracksQueued, equals(5));
      expect(result.platform, equals('apple'));
    });
  });
}
