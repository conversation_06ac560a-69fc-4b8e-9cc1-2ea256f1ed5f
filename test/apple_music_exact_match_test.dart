import 'package:flutter_test/flutter_test.dart';
import 'package:bop_maps/models/music_track.dart';

void main() {
  group('Apple Music Exact Match Logic Tests', () {
    test('Animals vs Animals (Mixed) should not be exact matches', () {
      final originalTrack = MusicTrack(
        id: 'original',
        title: 'Animals',
        artist: '<PERSON>',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 180000, // 3 minutes
        uri: '',
      );

      final mixedTrack = MusicTrack(
        id: 'mixed',
        title: 'Animals (Mixed)',
        artist: '<PERSON>',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 37000, // 37 seconds
        uri: '',
        explicit: true,
      );

      // Test normalization
      print('Original normalized: "${originalTrack.normalizedTitle}"');
      print('Mixed normalized: "${mixedTrack.normalizedTitle}"');
      
      // They should NOT be exact matches
      expect(originalTrack.normalizedTitle, isNot(equals(mixedTrack.normalizedTitle)));
      expect(originalTrack.normalizedArtist, equals(mixedTrack.normalizedArtist));
      
      // Test scoring - original should score much higher due to duration match
      final score = originalTrack.calculateMatchScore(mixedTrack);
      print('Score when comparing original to mixed: ${score.toStringAsFixed(1)}');
      
      // Score should be lower due to title mismatch and duration penalty
      expect(score, lessThan(900)); // Should not be considered an excellent match
    });

    test('Exact match detection should work for identical titles', () {
      final track1 = MusicTrack(
        id: '1',
        title: 'Animals',
        artist: 'Martin Garrix',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 180000,
        uri: '',
      );

      final track2 = MusicTrack(
        id: '2',
        title: 'Animals',
        artist: 'Martin Garrix',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 175000, // Slightly different duration
        uri: '',
        explicit: true,
      );

      // These should be exact matches (same normalized title and artist)
      expect(track1.normalizedTitle, equals(track2.normalizedTitle));
      expect(track1.normalizedArtist, equals(track2.normalizedArtist));
      
      // Score should be very high
      final score = track1.calculateMatchScore(track2);
      print('Score for exact title/artist match: ${score.toStringAsFixed(1)}');
      expect(score, greaterThan(950)); // Should be excellent match
    });

    test('Duration penalty should work correctly', () {
      final fullTrack = MusicTrack(
        id: 'full',
        title: 'Animals',
        artist: 'Martin Garrix',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 180000, // 3 minutes
        uri: '',
      );

      final shortTrack = MusicTrack(
        id: 'short',
        title: 'Animals',
        artist: 'Martin Garrix',
        albumArt: '',
        url: '',
        service: 'apple_music',
        serviceType: 'apple',
        durationMs: 37000, // 37 seconds - should get heavy penalty
        uri: '',
      );

      final score = fullTrack.calculateMatchScore(shortTrack);
      print('Score with duration penalty: ${score.toStringAsFixed(1)}');
      
      // Should get heavy penalty for being under 1 minute
      expect(score, lessThan(750)); // Heavy penalty should bring it below 750
    });
  });
}
