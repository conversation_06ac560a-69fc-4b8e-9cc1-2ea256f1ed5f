import 'dart:math' as math;
import 'package:flutter_test/flutter_test.dart';
import 'package:latlong2/latlong.dart';

// Mock implementation of the progressive clustering logic for testing
class ProgressiveClusteringTest {
  static List<Map<String, dynamic>> createProgressiveClusters(
    List<Map<String, dynamic>> allPins,
    List<Map<String, dynamic>> newPins,
    List<String> renderedPinIds,
    List<Map<String, dynamic>> existingClusters,
  ) {
    if (allPins.isEmpty) return [];
    
    // If this is the first batch, use normal clustering
    if (renderedPinIds.length <= newPins.length) {
      return _createOptimizedClusters(allPins);
    }
    
    // For subsequent batches, merge new pins with existing clusters
    final existingPins = <Map<String, dynamic>>[];
    
    // Separate existing clusters and individual pins
    for (final pinId in renderedPinIds) {
      if (pinId.startsWith('cluster_')) {
        // This was a cluster, find it in current clusters
        final existingCluster = existingClusters.firstWhere(
          (c) => c['id'] == pinId,
          orElse: () => <String, dynamic>{},
        );
        if (existingCluster.isNotEmpty) {
          // Keep existing cluster
          continue;
        }
      } else {
        // This was an individual pin
        final pin = allPins.firstWhere(
          (p) => p['id'].toString() == pinId,
          orElse: () => <String, dynamic>{},
        );
        if (pin.isNotEmpty) {
          existingPins.add(pin);
        }
      }
    }
    
    // Create clusters for new pins only
    final newPinsClusters = _createOptimizedClusters(newPins);
    
    // Merge new clusters with existing ones
    final mergedClusters = <Map<String, dynamic>>[];
    
    // Add existing clusters
    mergedClusters.addAll(existingClusters);
    
    // Process new clusters and merge with existing ones if possible
    for (final newCluster in newPinsClusters) {
      if (newCluster['isCluster'] == true) {
        // Check if this new cluster can be merged with any existing cluster
        bool merged = false;
        for (int i = 0; i < mergedClusters.length; i++) {
          final existingCluster = mergedClusters[i];
          if (existingCluster['isCluster'] == true) {
            final distance = _calculateGeographicDistance(
              LatLng(existingCluster['latitude'], existingCluster['longitude']),
              LatLng(newCluster['latitude'], newCluster['longitude']),
            );
            
            if (distance < 0.0005) { // Same clustering threshold
              // Merge clusters
              final existingPins = List<Map<String, dynamic>>.from(existingCluster['pins']);
              final newPins = List<Map<String, dynamic>>.from(newCluster['pins']);
              existingPins.addAll(newPins);
              
              final mergedCenter = _calculateClusterCenter(existingPins);
              mergedClusters[i] = {
                'id': existingCluster['id'], // Keep existing ID for stability
                'isCluster': true,
                'count': existingPins.length,
                'pins': existingPins,
                'latitude': mergedCenter.latitude,
                'longitude': mergedCenter.longitude,
                'center': mergedCenter,
              };
              merged = true;
              break;
            }
          }
        }
        
        if (!merged) {
          // Add as new cluster with stable ID
          mergedClusters.add({
            'id': 'cluster_${mergedClusters.where((c) => c['isCluster'] == true).length}_${DateTime.now().millisecondsSinceEpoch}',
            'isCluster': true,
            'count': newCluster['count'],
            'pins': newCluster['pins'],
            'latitude': newCluster['latitude'],
            'longitude': newCluster['longitude'],
            'center': newCluster['center'],
          });
        }
      } else {
        // Individual pin - check if it can be added to existing clusters
        final newPin = newCluster['pin'] as Map<String, dynamic>;
        bool addedToCluster = false;
        
        for (int i = 0; i < mergedClusters.length; i++) {
          final existingCluster = mergedClusters[i];
          if (existingCluster['isCluster'] == true) {
            final distance = _calculateGeographicDistance(
              LatLng(existingCluster['latitude'], existingCluster['longitude']),
              LatLng(newPin['latitude'], newPin['longitude']),
            );
            
            if (distance < 0.0005) {
              // Add to existing cluster
              final existingPins = List<Map<String, dynamic>>.from(existingCluster['pins']);
              existingPins.add(newPin);
              
              final mergedCenter = _calculateClusterCenter(existingPins);
              mergedClusters[i] = {
                'id': existingCluster['id'], // Keep existing ID
                'isCluster': true,
                'count': existingPins.length,
                'pins': existingPins,
                'latitude': mergedCenter.latitude,
                'longitude': mergedCenter.longitude,
                'center': mergedCenter,
              };
              addedToCluster = true;
              break;
            }
          }
        }
        
        if (!addedToCluster) {
          // Add as individual pin
          mergedClusters.add(newCluster);
        }
      }
    }
    
    // Add existing individual pins that weren't in clusters
    for (final existingPin in existingPins) {
      bool alreadyInCluster = false;
      for (final cluster in mergedClusters) {
        if (cluster['isCluster'] == true) {
          final clusterPins = cluster['pins'] as List<Map<String, dynamic>>;
          if (clusterPins.any((p) => p['id'] == existingPin['id'])) {
            alreadyInCluster = true;
            break;
          }
        }
      }
      
      if (!alreadyInCluster) {
        mergedClusters.add({
          'id': existingPin['id'],
          'isCluster': false,
          'pin': existingPin,
          'latitude': existingPin['latitude'],
          'longitude': existingPin['longitude'],
        });
      }
    }
    
    return mergedClusters;
  }
  
  // Mock clustering method
  static List<Map<String, dynamic>> _createOptimizedClusters(List<Map<String, dynamic>> pinsData) {
    if (pinsData.isEmpty) return [];
    
    // If only one pin, return it as an individual pin
    if (pinsData.length == 1) {
      final pin = pinsData[0];
      final lat = pin['latitude'] as double?;
      final lng = pin['longitude'] as double?;
      if (lat == null || lng == null) {
        return [];
      }
      return [{
        'id': pin['id'],
        'isCluster': false,
        'pin': pin,
        'latitude': lat,
        'longitude': lng,
      }];
    }
    
    final clusters = <Map<String, dynamic>>[];
    final remainingPins = List<Map<String, dynamic>>.from(pinsData);
    
    while (remainingPins.isNotEmpty) {
      final currentPin = remainingPins.removeAt(0);
      
      // Validate coordinates for current pin
      final currentLat = currentPin['latitude'] as double?;
      final currentLng = currentPin['longitude'] as double?;
      if (currentLat == null || currentLng == null) {
        continue;
      }
      
      final currentLatLng = LatLng(currentLat, currentLng);
      List<Map<String, dynamic>> clusterCandidates = [currentPin];
      
      // Find nearby pins for clustering
      remainingPins.removeWhere((pin) {
        final pinLat = pin['latitude'] as double?;
        final pinLng = pin['longitude'] as double?;
        if (pinLat == null || pinLng == null) {
          return false;
        }
        
        final pinLatLng = LatLng(pinLat, pinLng);
        final distance = _calculateGeographicDistance(currentLatLng, pinLatLng);
        if (distance < 0.0005) { // ~50 meters
          clusterCandidates.add(pin);
          return true;
        }
        return false;
      });
      
      if (clusterCandidates.length > 1) {
        // Create cluster
        final clusterCenter = _calculateClusterCenter(clusterCandidates);
        clusters.add({
          'id': 'cluster_${clusters.length}_${DateTime.now().millisecondsSinceEpoch}',
          'isCluster': true,
          'count': clusterCandidates.length,
          'pins': clusterCandidates,
          'latitude': clusterCenter.latitude,
          'longitude': clusterCenter.longitude,
          'center': clusterCenter,
        });
      } else {
        // Add as individual pin
        clusters.add({
          'id': currentPin['id'],
          'isCluster': false,
          'pin': currentPin,
          'latitude': currentLat,
          'longitude': currentLng,
        });
      }
    }
    
    return clusters;
  }
  
  // Mock distance calculation
  static double _calculateGeographicDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // Earth's radius in meters
    
    final double dLat = (point2.latitude - point1.latitude) * (math.pi / 180);
    final double dLng = (point2.longitude - point1.longitude) * (math.pi / 180);
    
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(point1.latitude * (math.pi / 180)) * math.cos(point2.latitude * (math.pi / 180)) *
        math.sin(dLng / 2) * math.sin(dLng / 2);
    
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }
  
  // Mock cluster center calculation
  static LatLng _calculateClusterCenter(List<Map<String, dynamic>> pins) {
    double totalLat = 0;
    double totalLng = 0;
    
    for (final pin in pins) {
      totalLat += pin['latitude'] as double;
      totalLng += pin['longitude'] as double;
    }
    
    return LatLng(totalLat / pins.length, totalLng / pins.length);
  }
}

void main() {
  group('Progressive Clustering Tests', () {
    test('should maintain cluster stability during progressive rendering', () {
      // Create test pins
      final pins = [
        {'id': '1', 'latitude': 40.7128, 'longitude': -74.0060}, // NYC
        {'id': '2', 'latitude': 40.7129, 'longitude': -74.0061}, // Very close to NYC
        {'id': '3', 'latitude': 40.7130, 'longitude': -74.0062}, // Very close to NYC
        {'id': '4', 'latitude': 34.0522, 'longitude': -118.2437}, // LA
        {'id': '5', 'latitude': 34.0523, 'longitude': -118.2438}, // Very close to LA
        {'id': '6', 'latitude': 41.8781, 'longitude': -87.6298}, // Chicago
      ];
      
      // First batch: pins 1, 2, 3 (should form a cluster)
      final firstBatch = pins.take(3).toList();
      final firstRenderedIds = ['1', '2', '3'];
      final firstClusters = ProgressiveClusteringTest.createProgressiveClusters(
        firstBatch,
        firstBatch,
        [],
        [],
      );
      
      // Verify first batch creates a cluster
      expect(firstClusters.length, 1);
      expect(firstClusters[0]['isCluster'], true);
      expect(firstClusters[0]['count'], 3);
      
      final firstClusterId = firstClusters[0]['id'] as String;
      
      // Second batch: pins 4, 5 (should form another cluster)
      final secondBatch = pins.skip(3).take(2).toList();
      final secondRenderedIds = [...firstRenderedIds, '4', '5'];
      final secondClusters = ProgressiveClusteringTest.createProgressiveClusters(
        pins.take(5).toList(),
        secondBatch,
        secondRenderedIds,
        firstClusters,
      );
      
      // Verify second batch maintains existing cluster and creates new one
      expect(secondClusters.length, 2);
      
      // Find the original cluster (should have same ID)
      final originalCluster = secondClusters.firstWhere(
        (c) => c['id'] == firstClusterId,
        orElse: () => <String, dynamic>{},
      );
      expect(originalCluster.isNotEmpty, true);
      expect(originalCluster['count'], 3); // Should still have 3 pins
      
      // Find the new cluster
      final newCluster = secondClusters.firstWhere(
        (c) => c['id'] != firstClusterId,
        orElse: () => <String, dynamic>{},
      );
      expect(newCluster.isNotEmpty, true);
      expect(newCluster['count'], 2);
      
      // Third batch: pin 6 (should be individual)
      final thirdBatch = pins.skip(5).take(1).toList();
      final thirdRenderedIds = [...secondRenderedIds, '6'];
      final thirdClusters = ProgressiveClusteringTest.createProgressiveClusters(
        pins,
        thirdBatch,
        thirdRenderedIds,
        secondClusters,
      );
      
      // Verify third batch maintains existing clusters and adds individual pin
      expect(thirdClusters.length, 3);
      
      // Original cluster should still exist with same ID
      final stillOriginalCluster = thirdClusters.firstWhere(
        (c) => c['id'] == firstClusterId,
        orElse: () => <String, dynamic>{},
      );
      expect(stillOriginalCluster.isNotEmpty, true);
      expect(stillOriginalCluster['count'], 3);
      
      // Individual pin should be present
      final individualPin = thirdClusters.firstWhere(
        (c) => c['id'] == '6',
        orElse: () => <String, dynamic>{},
      );
      expect(individualPin.isNotEmpty, true);
      expect(individualPin['isCluster'], false);
    });
    
    test('should merge new pins into existing clusters when close', () {
      // Create test pins
      final pins = [
        {'id': '1', 'latitude': 40.7128, 'longitude': -74.0060}, // NYC
        {'id': '2', 'latitude': 40.7129, 'longitude': -74.0061}, // Very close to NYC
        {'id': '3', 'latitude': 40.7130, 'longitude': -74.0062}, // Very close to NYC
        {'id': '4', 'latitude': 40.7131, 'longitude': -74.0063}, // Very close to NYC (new)
      ];
      
      // First batch: pins 1, 2, 3 (should form a cluster)
      final firstBatch = pins.take(3).toList();
      final firstRenderedIds = ['1', '2', '3'];
      final firstClusters = ProgressiveClusteringTest.createProgressiveClusters(
        firstBatch,
        firstBatch,
        [],
        [],
      );
      
      expect(firstClusters.length, 1);
      expect(firstClusters[0]['isCluster'], true);
      expect(firstClusters[0]['count'], 3);
      
      final firstClusterId = firstClusters[0]['id'] as String;
      
      // Second batch: pin 4 (should merge into existing cluster)
      final secondBatch = pins.skip(3).take(1).toList();
      final secondRenderedIds = [...firstRenderedIds, '4'];
      final secondClusters = ProgressiveClusteringTest.createProgressiveClusters(
        pins,
        secondBatch,
        secondRenderedIds,
        firstClusters,
      );
      
      // Should still have only 1 cluster, but with 4 pins
      expect(secondClusters.length, 1);
      expect(secondClusters[0]['id'], firstClusterId); // Same ID
      expect(secondClusters[0]['count'], 4); // Now has 4 pins
    });
  });
} 