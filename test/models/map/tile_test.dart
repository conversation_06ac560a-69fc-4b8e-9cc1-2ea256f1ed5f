import 'package:flutter_test/flutter_test.dart';
import '../../../lib/models/map/tile.dart';
import 'dart:math' show pi;

void main() {
  group('MapTile', () {
    test('constructor creates valid tile', () {
      final tile = MapTile(15, 16384, 10895);
      expect(tile.z, equals(15));
      expect(tile.x, equals(16384));
      expect(tile.y, equals(10895));
    });

    test('equality works correctly', () {
      final tile1 = MapTile(15, 16384, 10895);
      final tile2 = MapTile(15, 16384, 10895);
      final tile3 = MapTile(15, 16384, 10896);

      expect(tile1, equals(tile2));
      expect(tile1.hashCode, equals(tile2.hashCode));
      expect(tile1, isNot(equals(tile3)));
    });

    test('toString produces correct format', () {
      final tile = MapTile(15, 16384, 10895);
      expect(tile.toString(), equals('MapTile(z: 15, x: 16384, y: 10895)'));
    });

    test('fromLatLng converts San Francisco coordinates correctly', () {
      const lat = 37.7749;
      const lng = -122.4194;
      const zoom = 15;
      
      final tile = MapTile.fromLatLng(lat, lng, zoom);
      expect(tile.x, 5242);
      expect(tile.y, 12663);
      expect(tile.z, zoom);
    });

    test('getBounds returns correct bounds for San Francisco tile', () {
      final tile = MapTile(15, 5242, 12663);
      final bounds = tile.getBounds();
      
      expect(bounds['north'], closeTo(37.79676, 0.00001));
      expect(bounds['south'], closeTo(37.77204, 0.00001));
      expect(bounds['east'], closeTo(-122.41211, 0.00001));
      expect(bounds['west'], closeTo(-122.42584, 0.00001));
    });

    test('tilesInBounds returns correct number of tiles', () {
      const north = 37.7749;
      const south = 37.77;
      const east = -122.4194;
      const west = -122.424;
      const zoom = 14;
      
      final tiles = MapTile.tilesInBounds(north, south, east, west, zoom);
      expect(tiles.length, 4); // 2x2 grid of tiles at zoom 14
      
      // Verify first and last tiles
      expect(tiles.first.z, zoom);
      expect(tiles.last.z, zoom);
      
      // Verify x coordinates are within expected range
      for (final tile in tiles) {
        expect(tile.x >= 2620 && tile.x <= 2621, isTrue);
        expect(tile.y >= 6332 && tile.y <= 6333, isTrue);
      }
    });

    test('tilesInBounds handles zoom level 15', () {
      const north = 37.7749;
      const south = 37.77;
      const east = -122.4194;
      const west = -122.424;
      const zoom = 15;
      
      final tiles = MapTile.tilesInBounds(north, south, east, west, zoom);
      expect(tiles.length, 2); // 1x2 grid of tiles at zoom 15
      
      // Verify tile coordinates
      for (final tile in tiles) {
        expect(tile.x, 5241);
        expect(tile.y >= 12665 && tile.y <= 12666, isTrue);
        expect(tile.z, zoom);
      }
    });

    test('tilesInBounds handles zoom level 16', () {
      const north = 37.7749;
      const south = 37.77;
      const east = -122.4194;
      const west = -122.424;
      const zoom = 16;
      
      final tiles = MapTile.tilesInBounds(north, south, east, west, zoom);
      expect(tiles.length, 4); // 2x2 grid of tiles at zoom 16
      
      // Verify tile coordinates
      for (final tile in tiles) {
        expect(tile.x >= 10481 && tile.x <= 10482, isTrue);
        expect(tile.y >= 25331 && tile.y <= 25332, isTrue);
        expect(tile.z, zoom);
      }
    });

    test('toString returns correct format', () {
      final tile = MapTile(15, 5242, 12663);
      expect(tile.toString(), 'MapTile(z: 15, x: 5242, y: 12663)');
    });

    group('fromLatLng', () {
      test('converts extreme coordinates correctly', () {
        // Test North Pole
        var tile = MapTile.fromLatLng(85.0511, 0, 0);
        expect(tile.z, equals(0));
        expect(tile.x, equals(0));
        expect(tile.y, equals(0));

        // Test South Pole
        tile = MapTile.fromLatLng(-85.0511, 0, 0);
        expect(tile.z, equals(0));
        expect(tile.x, equals(0));
        expect(tile.y, equals(0));
      });
    });

    group('getBounds', () {
      test('returns correct bounds for San Francisco tile', () {
        final tile = MapTile(15, 5242, 12665);
        final bounds = tile.getBounds();

        // Test with reasonable precision (6 decimal places)
        expect(bounds['north']!, closeTo(37.79676, 0.000001));
        expect(bounds['south']!, closeTo(37.79312, 0.000001));
        expect(bounds['east']!, closeTo(-122.41211, 0.000001));
        expect(bounds['west']!, closeTo(-122.41577, 0.000001));
      });
    });

    group('tilesInBounds', () {
      test('returns correct number of tiles for small area', () {
        // Small area in San Francisco
        const north = 37.7749;
        const south = 37.7700;
        const east = -122.4194;
        const west = -122.4240;
        const zoom = 15;

        final tiles = MapTile.tilesInBounds(north, south, east, west, zoom);
        
        // The area should be covered by a small number of tiles at zoom 15
        expect(tiles.length, greaterThan(0));
        expect(tiles.length, lessThan(10));
        
        // Verify all tiles are within zoom level
        for (final tile in tiles) {
          expect(tile.z, equals(zoom));
        }
      });

      test('returns more tiles at higher zoom levels', () {
        // Same area, different zoom levels
        const north = 37.7749;
        const south = 37.7700;
        const east = -122.4194;
        const west = -122.4240;

        final tilesZoom14 = MapTile.tilesInBounds(north, south, east, west, 14);
        final tilesZoom15 = MapTile.tilesInBounds(north, south, east, west, 15);
        final tilesZoom16 = MapTile.tilesInBounds(north, south, east, west, 16);

        // Each zoom level should have approximately 4x more tiles
        expect(tilesZoom15.length, greaterThan(tilesZoom14.length));
        expect(tilesZoom16.length, greaterThanOrEqualTo(tilesZoom15.length * 4));
      });
    });
  });
} 