import 'package:flutter_test/flutter_test.dart';
import 'package:bop_maps/screens/map/managers/enhanced_gesture_manager.dart';

void main() {
  group('Gesture Integration Tests', () {
    late EnhancedGestureManager gestureManager;

    setUp(() {
      gestureManager = EnhancedGestureManager();
    });

    tearDown(() {
      gestureManager.dispose();
    });

    test('Pinch gesture should prevent tap detection', () {
      // Initially should allow taps
      expect(gestureManager.canTapImmediately(), isTrue);
      expect(gestureManager.shouldAllowTap, isTrue);

      // Simulate pinch gesture start (2 touch points)
      gestureManager.updateTouchPoints(2);

      // Should automatically start pinch gesture and block taps
      expect(gestureManager.currentGesture, equals(GestureState.pinching));
      expect(gestureManager.shouldAllowTap, isFalse);
      expect(gestureManager.canTapImmediately(), isFalse);

      // End pinch gesture
      gestureManager.updateTouchPoints(0);

      // Should return to none state
      expect(gestureManager.currentGesture, equals(GestureState.none));

      // Should allow taps again after debounce period
      expect(gestureManager.shouldAllowTap, isTrue);
    });

    test('Pan gesture should allow immediate taps after completion', () {
      // Start pan gesture
      gestureManager.startGesture(GestureType.pan);
      expect(gestureManager.currentGesture, equals(GestureState.panning));
      expect(gestureManager.shouldAllowTap, isFalse);

      // End pan gesture
      gestureManager.endGesture(GestureType.pan);
      expect(gestureManager.currentGesture, equals(GestureState.none));

      // Should allow immediate taps after pan (no debounce)
      expect(gestureManager.canTapImmediately(), isTrue);
      expect(gestureManager.shouldAllowTap, isTrue);
    });

    test('Pinch gesture should have debounce period after completion', () {
      // Start and end pinch gesture
      gestureManager.startGesture(GestureType.pinch);
      gestureManager.endGesture(GestureType.pinch);

      expect(gestureManager.currentGesture, equals(GestureState.none));

      // Should have debounce period for pinch gestures
      expect(gestureManager.canTapImmediately(), isFalse);
    });

    test('Touch point tracking should trigger automatic pinch detection', () {
      // Start with no touch points
      expect(gestureManager.currentGesture, equals(GestureState.none));

      // Single touch point should not trigger pinch
      gestureManager.updateTouchPoints(1);
      expect(gestureManager.currentGesture, equals(GestureState.none));

      // Two touch points should automatically trigger pinch
      gestureManager.updateTouchPoints(2);
      expect(gestureManager.currentGesture, equals(GestureState.pinching));

      // Reducing to one touch point should end pinch
      gestureManager.updateTouchPoints(1);
      expect(gestureManager.currentGesture, equals(GestureState.none));

      // Should allow immediate taps after reducing touch points
      expect(gestureManager.canTapImmediately(), isTrue);
    });

    test('Gesture priority system should work correctly', () {
      // Start with pan gesture
      gestureManager.startGesture(GestureType.pan);
      expect(gestureManager.currentGesture, equals(GestureState.panning));

      // Pinch should override pan (higher priority)
      gestureManager.startGesture(GestureType.pinch);
      expect(gestureManager.currentGesture, equals(GestureState.pinching));

      // Tap should not override pinch (lower priority)
      gestureManager.startGesture(GestureType.tap);
      expect(gestureManager.currentGesture, equals(GestureState.pinching));
    });
  });
}
