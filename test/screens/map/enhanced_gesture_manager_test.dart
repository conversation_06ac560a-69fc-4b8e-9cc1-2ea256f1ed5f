import 'package:flutter_test/flutter_test.dart';
import 'package:bop_maps/screens/map/managers/enhanced_gesture_manager.dart';

void main() {
  group('EnhancedGestureManager', () {
    late EnhancedGestureManager gestureManager;

    setUp(() {
      gestureManager = EnhancedGestureManager();
    });

    tearDown(() {
      gestureManager.dispose();
    });

    test('should allow tap when no other gestures are active', () {
      expect(gestureManager.shouldAllowTap, isTrue);
      expect(gestureManager.canTapImmediately(), isTrue);
    });

    test('should block tap during pinch gesture', () {
      // Simulate pinch gesture start
      gestureManager.updateTouchPoints(2);
      gestureManager.startGesture(GestureType.pinch);

      expect(gestureManager.shouldAllowTap, isFalse);
      expect(gestureManager.canTapImmediately(), isFalse);
      expect(gestureManager.currentGesture, equals(GestureState.pinching));
    });

    test('should block tap during pan gesture', () {
      gestureManager.startGesture(GestureType.pan);

      expect(gestureManager.shouldAllowTap, isFalse);
      expect(gestureManager.canTapImmediately(), isFalse);
      expect(gestureManager.currentGesture, equals(GestureState.panning));
    });

    test('should allow tap after pinch gesture ends', () async {
      // Start pinch gesture
      gestureManager.updateTouchPoints(2);
      gestureManager.startGesture(GestureType.pinch);
      expect(gestureManager.shouldAllowTap, isFalse);

      // End pinch gesture
      gestureManager.updateTouchPoints(0);
      gestureManager.endGesture(GestureType.pinch);

      // Should allow tap immediately after gesture ends
      expect(gestureManager.currentGesture, equals(GestureState.none));
      
      // Wait for debounce period
      await Future.delayed(const Duration(milliseconds: 250));
      expect(gestureManager.shouldAllowTap, isTrue);
    });

    test('should auto-detect pinch gesture from touch points', () {
      // Simulate two fingers touching
      gestureManager.updateTouchPoints(2);
      
      expect(gestureManager.currentGesture, equals(GestureState.pinching));
      expect(gestureManager.shouldAllowTap, isFalse);
    });

    test('should auto-end pinch gesture when touch points reduce', () {
      // Start with pinch
      gestureManager.updateTouchPoints(2);
      expect(gestureManager.currentGesture, equals(GestureState.pinching));

      // Reduce to single touch
      gestureManager.updateTouchPoints(1);
      expect(gestureManager.currentGesture, equals(GestureState.none));
    });

    test('should respect gesture priority (pinch > pan > tap)', () {
      // Start with pan
      gestureManager.startGesture(GestureType.pan);
      expect(gestureManager.currentGesture, equals(GestureState.panning));

      // Try to start pinch (higher priority)
      gestureManager.startGesture(GestureType.pinch);
      expect(gestureManager.currentGesture, equals(GestureState.pinching));

      // Try to start tap (lower priority) - should be blocked
      gestureManager.startGesture(GestureType.tap);
      expect(gestureManager.currentGesture, equals(GestureState.pinching));
    });

    test('should handle multiple touch points correctly', () {
      expect(gestureManager.shouldAllowTap, isTrue);

      // Single touch - should allow tap
      gestureManager.updateTouchPoints(1);
      expect(gestureManager.shouldAllowTap, isTrue);

      // Multiple touches - should block tap
      gestureManager.updateTouchPoints(2);
      expect(gestureManager.shouldAllowTap, isFalse);

      // Back to single touch
      gestureManager.updateTouchPoints(1);
      expect(gestureManager.shouldAllowTap, isTrue);
    });

    test('should process tap attempt correctly', () {
      // Should allow when no other gestures
      expect(gestureManager.processTapAttempt('test-pin'), isTrue);

      // Should block during pinch
      gestureManager.startGesture(GestureType.pinch);
      expect(gestureManager.processTapAttempt('test-pin'), isFalse);
    });

    test('should force end all gestures', () {
      // Start multiple gesture states
      gestureManager.updateTouchPoints(2);
      gestureManager.startGesture(GestureType.pinch);
      
      expect(gestureManager.currentGesture, equals(GestureState.pinching));
      expect(gestureManager.shouldAllowTap, isFalse);

      // Force end all
      gestureManager.forceEndAllGestures();
      
      expect(gestureManager.currentGesture, equals(GestureState.none));
      expect(gestureManager.shouldAllowTap, isTrue);
    });

    test('should handle gesture timeout', () async {
      gestureManager.startGesture(GestureType.pinch);
      expect(gestureManager.currentGesture, equals(GestureState.pinching));

      // Wait for timeout (500ms + buffer)
      await Future.delayed(const Duration(milliseconds: 600));
      
      expect(gestureManager.currentGesture, equals(GestureState.none));
    });
  });
}
