buildscript {
    ext.kotlin_version = '2.1.0'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
    }
}

// Fix for namespace issues with Flutter plugins
subprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            project.android {
                if (namespace == null) {
                    namespace = project.group ?: "com.example.${project.name}"
                }
                
                // Ensure consistent Java versions
                compileOptions {
                    sourceCompatibility = JavaVersion.VERSION_11
                    targetCompatibility = JavaVersion.VERSION_11
                }
                
                // Fix Kotlin JVM target
                if (project.hasProperty('kotlin')) {
                    kotlinOptions {
                        jvmTarget = JavaVersion.VERSION_11.toString()
                    }
                }
                
                project.tasks.whenTaskAdded { task ->
                    if (task.name.contains('processDebugManifest') || task.name.contains('processReleaseManifest')) {
                        task.doFirst {
                            File manifestFile = file("${projectDir}/src/main/AndroidManifest.xml")
                            if (manifestFile.exists()) {
                                String manifestContent = manifestFile.text
                                if (manifestContent.contains('package=')) {
                                    manifestContent = manifestContent.replaceAll(/package="[^"]*"/, "")
                                    manifestFile.write(manifestContent)
                                    println "Removed 'package' attribute from ${manifestFile}"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
} 