# Indoor Segmentation Model Setup

This directory contains the TensorFlow Lite model for indoor scene segmentation (floor/wall detection).

## Model Details

- **Base Model**: DeepLab with MobileNetV2 backbone
- **Input Size**: 513x513x3 (RGB)
- **Output**: 513x513x27 (27 class segmentation)
- **Key Classes**:
  - Class 1: Wall (includes windows, doors, fences)
  - Class 4: Floor (includes roads, ground, fields)
  - Class 5: Trees/Plants
  - Class 7: Stairs
  - Class 8: Furniture

## Setup Instructions

### Option 1: Generate Model (Recommended)

1. Ensure you have Python 3.7+ and TensorFlow installed:
   ```bash
   pip install tensorflow numpy
   ```

2. Run the conversion script from the project root:
   ```bash
   python scripts/convert_indoor_model.py
   ```

3. This will create `indoor_segmentation.tflite` in this directory.

### Option 2: Use Pre-trained Model

1. Download the original Indoor-segmentation model from:
   https://github.com/hellochick/Indoor-segmentation

2. Follow their instructions to get the pre-trained weights

3. Convert to TFLite format using the provided script

## Model Performance

- **Size**: ~15-20 MB (optimized for mobile)
- **Inference Time**: ~200-300ms on modern phones
- **Accuracy**: Optimized for indoor environments

## Testing

Run the ML service tests:
```bash
flutter test test/test_ml_service.dart
```

## Integration

The model is integrated with ARKit for enhanced floor/wall detection:
- ARKit provides geometric plane detection
- ML provides semantic understanding
- Combined approach gives robust results

## Troubleshooting

If the model fails to load:
1. Ensure the file `indoor_segmentation.tflite` exists in this directory
2. Check that the file is included in `pubspec.yaml` assets
3. Verify the model file is not corrupted (should be ~15-20MB)

## References

- Original model: https://github.com/hellochick/Indoor-segmentation
- TensorFlow Lite: https://www.tensorflow.org/lite
- Flutter TFLite plugin: https://pub.dev/packages/tflite_flutter 