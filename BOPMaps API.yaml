openapi: 3.0.3
info:
  title: BOPMaps API
  version: 1.0.0
  description: API documentation for BOPMaps - a musical geocaching app
paths:
  /api/token/verify/:
    post:
      operationId: token_verify_create
      description: |-
        Takes a token and indicates if it is valid.  This view provides no
        information about a token's fitness for a particular use.
      tags:
      - token
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenVerifyRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenVerifyRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenVerifyRequest'
        required: true
      responses:
        '200':
          description: No response body
  /api/users/:
    get:
      operationId: users_list
      description: API viewset for user management.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - users
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: users_create
      description: API viewset for user management.
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
        required: true
      security:
      - jwtAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRegistration'
          description: ''
  /api/users/me/:
    get:
      operationId: users_me_retrieve
      description: Get the current user's profile
      tags:
      - users
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/users/update_fcm_token/:
    post:
      operationId: users_update_fcm_token_create
      description: Update the user's FCM token for push notifications
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/users/update_location/:
    post:
      operationId: users_update_location_create
      description: Update the current user's location
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/users/update_profile/:
    patch:
      operationId: users_update_profile_partial_update
      description: Update the current user's profile
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: users_update_profile_update
      description: Update the current user's profile
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/users/{id}/:
    get:
      operationId: users_retrieve
      description: API viewset for user management.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this User.
        required: true
      tags:
      - users
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    patch:
      operationId: users_partial_update
      description: API viewset for user management.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this User.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    put:
      operationId: users_update
      description: API viewset for user management.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this User.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    delete:
      operationId: users_destroy
      description: API viewset for user management.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this User.
        required: true
      tags:
      - users
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/users/auth/token/:
    post:
      operationId: users_auth_token_create
      description: Custom token obtain pair view with extra data
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenObtainPairRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenObtainPairRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenObtainPairRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenObtainPair'
          description: ''
  /api/users/auth/token/refresh/:
    post:
      operationId: users_auth_token_refresh_create
      description: |-
        Takes a refresh type JSON web token and returns an access type JSON web
        token if the refresh token is valid.
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenRefresh'
          description: ''
  /api/users/auth/register/:
    post:
      operationId: users_auth_register_create
      description: API view for user registration
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
        required: true
      security:
      - jwtAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRegistration'
          description: ''
  /api/users/auth/logout:
    post:
      operationId: users_auth_logout_create
      description: API view to logout a user by invalidating their refresh token
      tags:
      - users
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/users/auth/logout/:
    post:
      operationId: users_auth_logout_create_2
      description: API view to logout a user by invalidating their refresh token
      tags:
      - users
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/users/auth/password-reset/:
    post:
      operationId: users_auth_password_reset_create
      description: API view to request a password reset
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetRequestRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordResetRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordResetRequestRequest'
        required: true
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordResetRequest'
          description: ''
  /api/users/auth/password-reset/confirm/:
    post:
      operationId: users_auth_password_reset_confirm_create
      description: API view to confirm a password reset
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetConfirmRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordResetConfirmRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordResetConfirmRequest'
        required: true
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordResetConfirm'
          description: ''
  /api/pins/interactions/:
    get:
      operationId: pins_interactions_list
      description: |-
        API viewset for Pin Interactions
        Limited to create, list, and retrieve operations
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPinInteractionList'
          description: ''
    post:
      operationId: pins_interactions_create
      description: |-
        API viewset for Pin Interactions
        Limited to create, list, and retrieve operations
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PinInteractionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PinInteractionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PinInteractionRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PinInteraction'
          description: ''
  /api/pins/interactions/{id}/:
    get:
      operationId: pins_interactions_retrieve
      description: |-
        API viewset for Pin Interactions
        Limited to create, list, and retrieve operations
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin interaction.
        required: true
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PinInteraction'
          description: ''
  /api/pins/collections/:
    get:
      operationId: pins_collections_list
      description: API viewset for Collection CRUD operations
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedCollectionList'
          description: ''
    post:
      operationId: pins_collections_create
      description: API viewset for Collection CRUD operations
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'
          description: ''
  /api/pins/collections/my_collections/:
    get:
      operationId: pins_collections_my_collections_retrieve
      description: Get only the current user's collections
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'
          description: ''
  /api/pins/collections/{id}/:
    get:
      operationId: pins_collections_retrieve
      description: |-
        Custom retrieve to check collection visibility before returning detail.
        Returns 404 if the collection is private and not owned by the requesting user.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this collection.
        required: true
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollectionDetail'
          description: ''
    patch:
      operationId: pins_collections_partial_update
      description: API viewset for Collection CRUD operations
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this collection.
        required: true
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCollectionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCollectionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCollectionRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'
          description: ''
    put:
      operationId: pins_collections_update
      description: API viewset for Collection CRUD operations
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this collection.
        required: true
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'
          description: ''
    delete:
      operationId: pins_collections_destroy
      description: API viewset for Collection CRUD operations
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this collection.
        required: true
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/pins/collections/{id}/add_pin/:
    post:
      operationId: pins_collections_add_pin_create
      description: Add a pin to a collection
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this collection.
        required: true
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'
          description: ''
  /api/pins/collections/{id}/remove_pin/:
    post:
      operationId: pins_collections_remove_pin_create
      description: Remove a pin from a collection
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this collection.
        required: true
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CollectionRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'
          description: ''
  /api/pins/:
    get:
      operationId: pins_list
      description: API viewset for Pin CRUD operations
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPinList'
          description: ''
    post:
      operationId: pins_create
      description: API viewset for Pin CRUD operations
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PinRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PinRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PinRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pin'
          description: ''
  /api/pins/list_map/:
    get:
      operationId: pins_list_map_retrieve
      description: Get pins for map display, optimized for performance with clustering
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PinGeo'
          description: ''
  /api/pins/nearby/:
    get:
      operationId: pins_nearby_retrieve
      description: Get pins near a specific location
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PinGeo'
          description: ''
  /api/pins/trending/:
    get:
      operationId: pins_trending_retrieve
      description: Get trending pins based on interaction count
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pin'
          description: ''
  /api/pins/{id}/:
    get:
      operationId: pins_retrieve
      description: |-
        Custom retrieve to check pin visibility before returning detail.
        Returns 404 if the pin is private and not owned by the requesting user.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin.
        required: true
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pin'
          description: ''
    patch:
      operationId: pins_partial_update
      description: API viewset for Pin CRUD operations
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin.
        required: true
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPinRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPinRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPinRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pin'
          description: ''
    put:
      operationId: pins_update
      description: API viewset for Pin CRUD operations
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin.
        required: true
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PinRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PinRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PinRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pin'
          description: ''
    delete:
      operationId: pins_destroy
      description: API viewset for Pin CRUD operations
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin.
        required: true
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/pins/{id}/collect/:
    post:
      operationId: pins_collect_create
      description: Record a collect interaction with a pin
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin.
        required: true
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PinRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PinRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PinRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pin'
          description: ''
  /api/pins/{id}/like/:
    post:
      operationId: pins_like_create
      description: Record a like interaction with a pin
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin.
        required: true
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PinRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PinRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PinRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pin'
          description: ''
  /api/pins/{id}/map_details/:
    get:
      operationId: pins_map_details_retrieve
      description: Get detailed pin information for map display with aura visualization
        settings
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin.
        required: true
      tags:
      - pins
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pin'
          description: ''
  /api/pins/{id}/share/:
    post:
      operationId: pins_share_create
      description: Record a share interaction with a pin
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin.
        required: true
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PinRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PinRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PinRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pin'
          description: ''
  /api/pins/{id}/view/:
    post:
      operationId: pins_view_create
      description: Record a view interaction with a pin
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin.
        required: true
      tags:
      - pins
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PinRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PinRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PinRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pin'
          description: ''
  /api/friends/requests/:
    get:
      operationId: friends_requests_list
      description: API viewset for FriendRequest management.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - friends
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedFriendRequestList'
          description: ''
    post:
      operationId: friends_requests_create
      description: API viewset for FriendRequest management.
      tags:
      - friends
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FriendRequest'
          description: ''
  /api/friends/requests/received/:
    get:
      operationId: friends_requests_received_retrieve
      description: Get friend requests received by the current user
      tags:
      - friends
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FriendRequest'
          description: ''
  /api/friends/requests/sent/:
    get:
      operationId: friends_requests_sent_retrieve
      description: Get friend requests sent by the current user
      tags:
      - friends
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FriendRequest'
          description: ''
  /api/friends/requests/{id}/:
    get:
      operationId: friends_requests_retrieve
      description: API viewset for FriendRequest management.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - friends
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FriendRequest'
          description: ''
    delete:
      operationId: friends_requests_destroy
      description: API viewset for FriendRequest management.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - friends
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/friends/requests/{id}/accept/:
    post:
      operationId: friends_requests_accept_create
      description: Accept a friend request
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - friends
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FriendRequest'
          description: ''
  /api/friends/requests/{id}/cancel/:
    post:
      operationId: friends_requests_cancel_create
      description: Cancel a friend request you've sent
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - friends
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FriendRequest'
          description: ''
  /api/friends/requests/{id}/reject/:
    post:
      operationId: friends_requests_reject_create
      description: Reject a friend request
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - friends
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FriendRequestRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FriendRequest'
          description: ''
  /api/friends/:
    get:
      operationId: friends_list
      description: API viewset for Friend management.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - friends
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedFriendList'
          description: ''
    post:
      operationId: friends_create
      description: API viewset for Friend management.
      tags:
      - friends
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FriendRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FriendRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FriendRequest'
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Friend'
          description: ''
  /api/friends/all_friends/:
    get:
      operationId: friends_all_friends_retrieve
      description: Get a list of all the current user's friends
      tags:
      - friends
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Friend'
          description: ''
  /api/friends/{id}/:
    get:
      operationId: friends_retrieve
      description: API viewset for Friend management.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - friends
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Friend'
          description: ''
    patch:
      operationId: friends_partial_update
      description: API viewset for Friend management.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - friends
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Friend'
          description: ''
    put:
      operationId: friends_update
      description: API viewset for Friend management.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - friends
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FriendRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FriendRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FriendRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Friend'
          description: ''
    delete:
      operationId: friends_destroy
      description: API viewset for Friend management.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - friends
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/friends/{id}/unfriend/:
    post:
      operationId: friends_unfriend_create
      description: Remove a friendship
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - friends
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FriendRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FriendRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FriendRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Friend'
          description: ''
  /api/music/auth/spotify/:
    get:
      operationId: music_auth_spotify_retrieve
      description: Start Spotify OAuth flow for mobile apps
      tags:
      - music
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/music/auth/spotify/mobile/:
    get:
      operationId: music_auth_spotify_mobile_retrieve
      description: Start Spotify OAuth flow for mobile apps
      tags:
      - music
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/music/auth/apple/token/:
    post:
      operationId: music_auth_apple_token_create
      description: Handle Apple Music authentication from mobile app
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/auth/callback/:
    post:
      operationId: music_auth_callback_create
      description: |-
        Handle OAuth callback from mobile app
        Accepts a JSON payload with the authorization code and exchanges it for tokens.
        This is used with the fixed redirect URI workflow.
      tags:
      - music
      security:
      - jwtAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/music/services/connected_services/:
    get:
      operationId: music_services_connected_services_retrieve
      description: Get all connected music services for the user
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MusicService'
          description: ''
  /api/music/services/disconnect/{service_type}/:
    delete:
      operationId: music_services_disconnect_destroy
      description: Disconnect a music service
      parameters:
      - in: path
        name: service_type
        schema:
          type: string
        required: true
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/music/spotify/playlists/:
    get:
      operationId: music_spotify_playlists_retrieve
      description: Get user's Spotify playlists
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/spotify/recently_played/:
    get:
      operationId: music_spotify_recently_played_retrieve
      description: Get user's recently played tracks on Spotify
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/spotify/saved_tracks/:
    get:
      operationId: music_spotify_saved_tracks_retrieve
      description: Get user's saved/liked tracks on Spotify
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/spotify/search/:
    get:
      operationId: music_spotify_search_retrieve
      description: Search for tracks on Spotify
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/spotify/{id}/playlist/{playlist_id}/:
    get:
      operationId: music_spotify_playlist_retrieve
      description: Get a specific Spotify playlist
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: playlist_id
        schema:
          type: string
        required: true
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/spotify/{id}/playlist/{playlist_id}/tracks/:
    get:
      operationId: music_spotify_playlist_tracks_retrieve
      description: Get tracks from a Spotify playlist
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: playlist_id
        schema:
          type: string
        required: true
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/spotify/{id}/track/{track_id}/:
    get:
      operationId: music_spotify_track_retrieve
      description: Get details for a specific Spotify track
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: track_id
        schema:
          type: string
        required: true
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/apple/playlists/:
    get:
      operationId: music_apple_playlists_retrieve
      description: Get user's Apple Music playlists
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/apple/recently_played/:
    get:
      operationId: music_apple_recently_played_retrieve
      description: Get user's recently played tracks from Apple Music
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/apple/saved_tracks/:
    get:
      operationId: music_apple_saved_tracks_retrieve
      description: Get user's saved/liked tracks from Apple Music
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/apple/search/:
    get:
      operationId: music_apple_search_retrieve
      description: Search for tracks on Apple Music
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/apple/{id}/playlist/{playlist_id}/:
    get:
      operationId: music_apple_playlist_retrieve
      description: Get a specific Apple Music playlist
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: playlist_id
        schema:
          type: string
        required: true
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/apple/{id}/playlist/{playlist_id}/tracks/:
    get:
      operationId: music_apple_playlist_tracks_retrieve
      description: Get tracks from an Apple Music playlist
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: playlist_id
        schema:
          type: string
        required: true
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/apple/{id}/track/{track_id}/:
    get:
      operationId: music_apple_track_retrieve
      description: Get details for a specific Apple Music track
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      - in: path
        name: track_id
        schema:
          type: string
        required: true
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/music/tracks/playlist/{service}/{playlist_id}/:
    get:
      operationId: music_tracks_playlist_retrieve
      description: Get tracks from a playlist
      parameters:
      - in: path
        name: playlist_id
        schema:
          type: string
        required: true
      - in: path
        name: service
        schema:
          type: string
        required: true
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MusicTrack'
          description: ''
  /api/music/tracks/playlists/:
    get:
      operationId: music_tracks_playlists_retrieve
      description: Get user's playlists
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MusicTrack'
          description: ''
  /api/music/tracks/recently_played/:
    get:
      operationId: music_tracks_recently_played_retrieve
      description: Get user's recently played tracks
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MusicTrack'
          description: ''
  /api/music/tracks/saved_tracks/:
    get:
      operationId: music_tracks_saved_tracks_retrieve
      description: Get user's saved/liked tracks
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MusicTrack'
          description: ''
  /api/music/tracks/search/:
    get:
      operationId: music_tracks_search_retrieve
      description: Search for music tracks
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MusicTrack'
          description: ''
  /api/music/tracks/track/{service}/{track_id}/:
    get:
      operationId: music_tracks_track_retrieve
      description: Get details for a specific track
      parameters:
      - in: path
        name: service
        schema:
          type: string
        required: true
      - in: path
        name: track_id
        schema:
          type: string
        required: true
      tags:
      - music
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MusicTrack'
          description: ''
  /api/gamification/achievements/:
    get:
      operationId: gamification_achievements_list
      description: API viewset for Achievement - Read only for users
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAchievementList'
          description: ''
  /api/gamification/achievements/completed/:
    get:
      operationId: gamification_achievements_completed_retrieve
      description: Get all achievements completed by the current user
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Achievement'
          description: ''
  /api/gamification/achievements/in_progress/:
    get:
      operationId: gamification_achievements_in_progress_retrieve
      description: Get achievements that the user has started but not completed
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Achievement'
          description: ''
  /api/gamification/achievements/{id}/:
    get:
      operationId: gamification_achievements_retrieve
      description: API viewset for Achievement - Read only for users
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this achievement.
        required: true
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Achievement'
          description: ''
  /api/gamification/skins/:
    get:
      operationId: gamification_skins_list
      description: API viewset for PinSkin - Read only for regular users
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPinSkinList'
          description: ''
  /api/gamification/skins/owned/:
    get:
      operationId: gamification_skins_owned_retrieve
      description: Alias for 'unlocked' to match test expectations
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PinSkin'
          description: ''
  /api/gamification/skins/unlocked/:
    get:
      operationId: gamification_skins_unlocked_retrieve
      description: Get only the skins that the current user has unlocked
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PinSkin'
          description: ''
  /api/gamification/skins/{id}/:
    get:
      operationId: gamification_skins_retrieve
      description: API viewset for PinSkin - Read only for regular users
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin skin.
        required: true
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PinSkin'
          description: ''
  /api/gamification/skins/{id}/equip/:
    post:
      operationId: gamification_skins_equip_create
      description: Equip a skin for the current user
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this pin skin.
        required: true
      tags:
      - gamification
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PinSkinRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PinSkinRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PinSkinRequest'
        required: true
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PinSkin'
          description: ''
  /api/gamification/user-achievements/:
    get:
      operationId: gamification_user_achievements_list
      description: API viewset for UserAchievement - Users can update progress
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserAchievementList'
          description: ''
    post:
      operationId: gamification_user_achievements_create
      description: API viewset for UserAchievement - Users can update progress
      tags:
      - gamification
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserAchievementRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserAchievementRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserAchievementRequest'
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAchievement'
          description: ''
  /api/gamification/user-achievements/{id}/:
    get:
      operationId: gamification_user_achievements_retrieve
      description: API viewset for UserAchievement - Users can update progress
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAchievement'
          description: ''
    patch:
      operationId: gamification_user_achievements_partial_update
      description: API viewset for UserAchievement - Users can update progress
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - gamification
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserAchievementRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserAchievementRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserAchievementRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAchievement'
          description: ''
    put:
      operationId: gamification_user_achievements_update
      description: API viewset for UserAchievement - Users can update progress
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - gamification
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserAchievementRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserAchievementRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserAchievementRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAchievement'
          description: ''
    delete:
      operationId: gamification_user_achievements_destroy
      description: API viewset for UserAchievement - Users can update progress
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - gamification
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/gamification/user-achievements/{id}/update_progress/:
    post:
      operationId: gamification_user_achievements_update_progress_create
      description: Update progress for an achievement
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - gamification
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserAchievementRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserAchievementRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserAchievementRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAchievement'
          description: ''
  /api/geo/trending/:
    get:
      operationId: geo_trending_list
      description: API viewset for trending areas (read-only)
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTrendingAreaList'
          description: ''
  /api/geo/trending/map_visualization/:
    get:
      operationId: geo_trending_map_visualization_retrieve
      description: Get trending areas with visualization parameters for heatmap
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrendingArea'
          description: ''
  /api/geo/trending/{id}/:
    get:
      operationId: geo_trending_retrieve
      description: API viewset for trending areas (read-only)
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this trending area.
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrendingArea'
          description: ''
  /api/geo/locations/:
    get:
      operationId: geo_locations_list
      description: API viewset for user location history (read-only)
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserLocationList'
          description: ''
  /api/geo/locations/{id}/:
    get:
      operationId: geo_locations_retrieve
      description: API viewset for user location history (read-only)
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserLocation'
          description: ''
  /api/geo/buildings/:
    get:
      operationId: geo_buildings_list
      description: API viewset for building data
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedBuildingList'
          description: ''
  /api/geo/buildings/{id}/:
    get:
      operationId: geo_buildings_retrieve
      description: API viewset for building data
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Building'
          description: ''
  /api/geo/roads/:
    get:
      operationId: geo_roads_list
      description: Override list method to add caching
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedRoadList'
          description: ''
  /api/geo/roads/{id}/:
    get:
      operationId: geo_roads_retrieve
      description: API viewset for road data
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this road.
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Road'
          description: ''
  /api/geo/parks/:
    get:
      operationId: geo_parks_list
      description: Override list method to add caching
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedParkList'
          description: ''
  /api/geo/parks/{id}/:
    get:
      operationId: geo_parks_retrieve
      description: API viewset for park data
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this park.
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Park'
          description: ''
  /api/geo/settings/:
    get:
      operationId: geo_settings_list
      description: API viewset for user map settings
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserMapSettingsList'
          description: ''
    post:
      operationId: geo_settings_create
      description: API viewset for user map settings
      tags:
      - geo
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserMapSettingsRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserMapSettingsRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserMapSettingsRequest'
      security:
      - jwtAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserMapSettings'
          description: ''
  /api/geo/settings/current/:
    get:
      operationId: geo_settings_current_retrieve
      description: Get settings for the current user, creating default settings if
        none exist
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserMapSettings'
          description: ''
  /api/geo/settings/{id}/:
    get:
      operationId: geo_settings_retrieve
      description: Get settings for the current user, creating default settings if
        none exist
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserMapSettings'
          description: ''
    patch:
      operationId: geo_settings_partial_update
      description: API viewset for user map settings
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - geo
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserMapSettingsRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserMapSettingsRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserMapSettingsRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserMapSettings'
          description: ''
    put:
      operationId: geo_settings_update
      description: API viewset for user map settings
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - geo
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserMapSettingsRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserMapSettingsRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserMapSettingsRequest'
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserMapSettings'
          description: ''
    delete:
      operationId: geo_settings_destroy
      description: API viewset for user map settings
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '204':
          description: No response body
  /api/geo/regions/:
    get:
      operationId: geo_regions_list
      description: API for cached region management
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedCachedRegionList'
          description: ''
  /api/geo/regions/{id}/:
    get:
      operationId: geo_regions_retrieve
      description: API for cached region management
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this cached region.
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CachedRegion'
          description: ''
  /api/geo/regions/{id}/download/:
    get:
      operationId: geo_regions_download_retrieve
      description: Download a region bundle file
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this cached region.
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CachedRegion'
          description: ''
  /api/geo/tiles/osm/{z}/{x}/{y}.png:
    get:
      operationId: geo_tiles_osm_.png_retrieve
      description: Get a map tile, either from cache or from OSM
      parameters:
      - in: path
        name: x
        schema:
          type: integer
        required: true
      - in: path
        name: y
        schema:
          type: integer
        required: true
      - in: path
        name: z
        schema:
          type: integer
        required: true
      tags:
      - geo
      responses:
        '200':
          description: No response body
  /api/geo/regions/bundle/:
    get:
      operationId: geo_regions_bundle_retrieve
      description: Check status of a region bundle task
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: geo_regions_bundle_create
      description: Create a new region bundle
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
  /api/geo/regions/bundle/{task_id}/:
    get:
      operationId: geo_regions_bundle_retrieve_2
      description: Check status of a region bundle task
      parameters:
      - in: path
        name: task_id
        schema:
          type: string
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: geo_regions_bundle_create_2
      description: Create a new region bundle
      parameters:
      - in: path
        name: task_id
        schema:
          type: string
        required: true
      tags:
      - geo
      security:
      - jwtAuth: []
      responses:
        '200':
          description: No response body
components:
  schemas:
    Achievement:
      type: object
      description: Serializer for the Achievement model
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 100
        description:
          type: string
        icon:
          type: string
          format: uri
        criteria:
          type: object
          additionalProperties: {}
          description: JSON criteria for achievement completion
        reward_skin:
          type: integer
          nullable: true
        reward_skin_details:
          allOf:
          - $ref: '#/components/schemas/PinSkin'
          readOnly: true
        is_completed:
          type: string
          readOnly: true
        progress:
          type: string
          readOnly: true
      required:
      - criteria
      - description
      - icon
      - id
      - is_completed
      - name
      - progress
      - reward_skin_details
    AchievementRequest:
      type: object
      description: Serializer for the Achievement model
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
        description:
          type: string
          minLength: 1
        icon:
          type: string
          format: binary
        criteria:
          type: object
          additionalProperties: {}
          description: JSON criteria for achievement completion
        reward_skin:
          type: integer
          nullable: true
      required:
      - criteria
      - description
      - icon
      - name
    Building:
      type: object
      description: Serializer for Building model with GeoJSON support
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureEnum'
        id:
          type: integer
          readOnly: true
        geometry:
          oneOf:
          - type: object
            properties:
              type:
                type: string
                enum:
                - Point
              coordinates:
                type: array
                items:
                  type: number
                  format: float
                example:
                - 12.9721
                - 77.5933
                minItems: 2
                maxItems: 3
          - type: object
            properties:
              type:
                type: string
                enum:
                - LineString
              coordinates:
                type: array
                items:
                  type: array
                  items:
                    type: number
                    format: float
                  example:
                  - 12.9721
                  - 77.5933
                  minItems: 2
                  maxItems: 3
                example:
                - - 22.4707
                  - 70.0577
                - - 12.9721
                  - 77.5933
                minItems: 2
          - type: object
            properties:
              type:
                type: string
                enum:
                - Polygon
              coordinates:
                type: array
                items:
                  type: array
                  items:
                    type: array
                    items:
                      type: number
                      format: float
                    example:
                    - 12.9721
                    - 77.5933
                    minItems: 2
                    maxItems: 3
                  example:
                  - - 22.4707
                    - 70.0577
                  - - 12.9721
                    - 77.5933
                  minItems: 4
                example:
                - - - 0.0
                    - 0.0
                  - - 0.0
                    - 50.0
                  - - 50.0
                    - 50.0
                  - - 50.0
                    - 0.0
                  - - 0.0
                    - 0.0
        properties:
          type: object
          properties:
            osm_id:
              type: integer
              maximum: 9223372036854775807
              minimum: -9223372036854775808
              format: int64
            name:
              type: string
              nullable: true
              maxLength: 255
            height_meters:
              type: number
              format: double
              readOnly: true
            level_count:
              type: integer
              readOnly: true
            building_type:
              type: string
              nullable: true
              maxLength: 50
            last_updated:
              type: string
              format: date-time
              readOnly: true
    BuildingList:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureCollectionEnum'
        features:
          type: array
          items:
            $ref: '#/components/schemas/Building'
    CachedRegion:
      type: object
      description: Serializer for CachedRegion model
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 255
        north:
          type: number
          format: double
        south:
          type: number
          format: double
        east:
          type: number
          format: double
        west:
          type: number
          format: double
        min_zoom:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        max_zoom:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        created_at:
          type: string
          format: date-time
          readOnly: true
        last_accessed:
          type: string
          format: date-time
          readOnly: true
        access_count:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        size_mb:
          type: string
          readOnly: true
        bundle_url:
          type: string
          readOnly: true
      required:
      - bundle_url
      - created_at
      - east
      - id
      - last_accessed
      - max_zoom
      - min_zoom
      - name
      - north
      - size_mb
      - south
      - west
    Collection:
      type: object
      description: Serializer for Collection model
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 100
        description:
          type: string
          nullable: true
        is_public:
          type: boolean
        primary_color:
          type: string
          nullable: true
          maxLength: 20
        cover_image_urls:
          type: array
          items:
            type: string
            format: uri
            maxLength: 200
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
        owner:
          type: integer
          readOnly: true
        owner_name:
          type: string
          readOnly: true
        item_count:
          type: integer
          readOnly: true
        last_updated:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - item_count
      - last_updated
      - name
      - owner
      - owner_name
      - updated_at
    CollectionDetail:
      type: object
      description: Detailed Collection serializer with pins
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 100
        description:
          type: string
          nullable: true
        is_public:
          type: boolean
        primary_color:
          type: string
          nullable: true
          maxLength: 20
        cover_image_urls:
          type: array
          items:
            type: string
            format: uri
            maxLength: 200
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
        owner:
          type: integer
          readOnly: true
        owner_name:
          type: string
          readOnly: true
        item_count:
          type: integer
          readOnly: true
        last_updated:
          type: string
          format: date-time
          readOnly: true
        pins:
          type: string
          readOnly: true
      required:
      - created_at
      - id
      - item_count
      - last_updated
      - name
      - owner
      - owner_name
      - pins
      - updated_at
    CollectionRequest:
      type: object
      description: Serializer for Collection model
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
        description:
          type: string
          nullable: true
        is_public:
          type: boolean
        primary_color:
          type: string
          nullable: true
          maxLength: 20
        cover_image_urls:
          type: array
          items:
            type: string
            format: uri
            minLength: 1
            maxLength: 200
      required:
      - name
    Friend:
      type: object
      description: Serializer for Friend model (for accepted friendships)
      properties:
        id:
          type: integer
          readOnly: true
        friend:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - friend
      - id
      - updated_at
    FriendRequest:
      type: object
      description: Serializer for Friend model that includes all fields
      properties:
        id:
          type: integer
          readOnly: true
        requester:
          allOf:
          - $ref: '#/components/schemas/UserMini'
          readOnly: true
        recipient:
          allOf:
          - $ref: '#/components/schemas/UserMini'
          readOnly: true
        status:
          $ref: '#/components/schemas/StatusEnum'
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - recipient
      - requester
      - updated_at
    FriendRequestRequest:
      type: object
      description: Serializer for Friend model that includes all fields
      properties:
        recipient_id:
          type: integer
          writeOnly: true
        status:
          $ref: '#/components/schemas/StatusEnum'
      required:
      - recipient_id
    GisFeatureCollectionEnum:
      type: string
      enum:
      - FeatureCollection
    GisFeatureEnum:
      type: string
      enum:
      - Feature
    InteractionTypeEnum:
      enum:
      - view
      - collect
      - like
      - share
      type: string
      description: |-
        * `view` - Viewed
        * `collect` - Collected
        * `like` - Liked
        * `share` - Shared
    MusicService:
      type: object
      description: Serializer for music service information
      properties:
        service_type:
          type: string
        connected_at:
          type: string
          format: date-time
        is_active:
          type: boolean
      required:
      - connected_at
      - is_active
      - service_type
    MusicTrack:
      type: object
      description: Serializer for music track data
      properties:
        id:
          type: string
        title:
          type: string
        artist:
          type: string
        album:
          type: string
          nullable: true
        album_art:
          type: string
          format: uri
          nullable: true
        url:
          type: string
          format: uri
        service:
          type: string
        preview_url:
          type: string
          format: uri
          nullable: true
      required:
      - artist
      - id
      - service
      - title
      - url
    PaginatedAchievementList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Achievement'
    PaginatedBuildingList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          $ref: '#/components/schemas/BuildingList'
    PaginatedCachedRegionList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/CachedRegion'
    PaginatedCollectionList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Collection'
    PaginatedFriendList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Friend'
    PaginatedFriendRequestList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/FriendRequest'
    PaginatedParkList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          $ref: '#/components/schemas/ParkList'
    PaginatedPinInteractionList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PinInteraction'
    PaginatedPinList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Pin'
    PaginatedPinSkinList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PinSkin'
    PaginatedRoadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          $ref: '#/components/schemas/RoadList'
    PaginatedTrendingAreaList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          $ref: '#/components/schemas/TrendingAreaList'
    PaginatedUserAchievementList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/UserAchievement'
    PaginatedUserList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/User'
    PaginatedUserLocationList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          $ref: '#/components/schemas/UserLocationList'
    PaginatedUserMapSettingsList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/UserMapSettings'
    Park:
      type: object
      description: Serializer for Park model with GeoJSON support
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureEnum'
        id:
          type: integer
          readOnly: true
        geometry:
          oneOf:
          - type: object
            properties:
              type:
                type: string
                enum:
                - Point
              coordinates:
                type: array
                items:
                  type: number
                  format: float
                example:
                - 12.9721
                - 77.5933
                minItems: 2
                maxItems: 3
          - type: object
            properties:
              type:
                type: string
                enum:
                - LineString
              coordinates:
                type: array
                items:
                  type: array
                  items:
                    type: number
                    format: float
                  example:
                  - 12.9721
                  - 77.5933
                  minItems: 2
                  maxItems: 3
                example:
                - - 22.4707
                  - 70.0577
                - - 12.9721
                  - 77.5933
                minItems: 2
          - type: object
            properties:
              type:
                type: string
                enum:
                - Polygon
              coordinates:
                type: array
                items:
                  type: array
                  items:
                    type: array
                    items:
                      type: number
                      format: float
                    example:
                    - 12.9721
                    - 77.5933
                    minItems: 2
                    maxItems: 3
                  example:
                  - - 22.4707
                    - 70.0577
                  - - 12.9721
                    - 77.5933
                  minItems: 4
                example:
                - - - 0.0
                    - 0.0
                  - - 0.0
                    - 50.0
                  - - 50.0
                    - 50.0
                  - - 50.0
                    - 0.0
                  - - 0.0
                    - 0.0
        properties:
          type: object
          properties:
            osm_id:
              type: integer
              maximum: 9223372036854775807
              minimum: -9223372036854775808
              format: int64
            name:
              type: string
              nullable: true
              maxLength: 255
            park_type:
              type: string
              maxLength: 50
            last_updated:
              type: string
              format: date-time
              readOnly: true
    ParkList:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureCollectionEnum'
        features:
          type: array
          items:
            $ref: '#/components/schemas/Park'
    PasswordResetConfirm:
      type: object
      description: Serializer for password reset confirmation
      properties:
        token:
          type: string
        password:
          type: string
      required:
      - password
      - token
    PasswordResetConfirmRequest:
      type: object
      description: Serializer for password reset confirmation
      properties:
        token:
          type: string
          minLength: 1
        password:
          type: string
          minLength: 1
      required:
      - password
      - token
    PasswordResetRequest:
      type: object
      description: Serializer for password reset request
      properties:
        email:
          type: string
          format: email
      required:
      - email
    PasswordResetRequestRequest:
      type: object
      description: Serializer for password reset request
      properties:
        email:
          type: string
          format: email
          minLength: 1
      required:
      - email
    PatchedCollectionRequest:
      type: object
      description: Serializer for Collection model
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
        description:
          type: string
          nullable: true
        is_public:
          type: boolean
        primary_color:
          type: string
          nullable: true
          maxLength: 20
        cover_image_urls:
          type: array
          items:
            type: string
            format: uri
            minLength: 1
            maxLength: 200
    PatchedPinRequest:
      type: object
      description: Serializer for Pin model
      properties:
        location:
          type: string
        title:
          type: string
          minLength: 1
          maxLength: 100
        description:
          type: string
          nullable: true
        track_title:
          type: string
          minLength: 1
          maxLength: 255
        track_artist:
          type: string
          minLength: 1
          maxLength: 255
        album:
          type: string
          nullable: true
          maxLength: 255
        track_url:
          type: string
          format: uri
          minLength: 1
          maxLength: 200
        service:
          $ref: '#/components/schemas/ServiceEnum'
        skin:
          type: integer
        rarity:
          $ref: '#/components/schemas/RarityEnum'
        aura_radius:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        is_private:
          type: boolean
        expiration_date:
          type: string
          format: date-time
          nullable: true
    PatchedUserAchievementRequest:
      type: object
      description: Serializer for the UserAchievement model
      properties:
        progress:
          type: object
          additionalProperties: {}
          description: Current progress towards achievement
    PatchedUserMapSettingsRequest:
      type: object
      description: Serializer for UserMapSettings model
      properties:
        show_feature_info:
          type: boolean
        use_3d_buildings:
          type: boolean
        default_latitude:
          type: number
          format: double
        default_longitude:
          type: number
          format: double
        default_zoom:
          type: number
          format: double
        max_cache_size_mb:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        theme:
          type: string
          minLength: 1
          maxLength: 20
    PatchedUserRequest:
      type: object
      description: Serializer for the User model
      properties:
        username:
          type: string
          minLength: 1
          pattern: ^[\w.@+-]+$
          maxLength: 150
        email:
          type: string
          format: email
          minLength: 1
          maxLength: 254
        profile_pic:
          type: string
          format: binary
          nullable: true
        bio:
          type: string
          nullable: true
        location:
          type: string
          nullable: true
        spotify_connected:
          type: boolean
        apple_music_connected:
          type: boolean
        soundcloud_connected:
          type: boolean
    PatchedUserUpdateRequest:
      type: object
      description: Serializer for updating user profile
      properties:
        username:
          type: string
          minLength: 1
          pattern: ^[\w.@+-]+$
          maxLength: 150
        profile_pic:
          type: string
          format: binary
          nullable: true
        bio:
          type: string
          nullable: true
        location:
          type: string
          nullable: true
        current_password:
          type: string
          writeOnly: true
          minLength: 1
        new_password:
          type: string
          writeOnly: true
          minLength: 1
    Pin:
      type: object
      description: Serializer for Pin model
      properties:
        id:
          type: integer
          readOnly: true
        owner:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        location:
          type: string
        title:
          type: string
          maxLength: 100
        description:
          type: string
          nullable: true
        track_title:
          type: string
          maxLength: 255
        track_artist:
          type: string
          maxLength: 255
        album:
          type: string
          nullable: true
          maxLength: 255
        track_url:
          type: string
          format: uri
          maxLength: 200
        service:
          $ref: '#/components/schemas/ServiceEnum'
        skin:
          type: integer
        skin_details:
          allOf:
          - $ref: '#/components/schemas/PinSkin'
          readOnly: true
        rarity:
          $ref: '#/components/schemas/RarityEnum'
        aura_radius:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        is_private:
          type: boolean
        expiration_date:
          type: string
          format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
        interaction_count:
          type: string
          readOnly: true
        distance:
          type: string
          readOnly: true
        has_expired:
          type: string
          readOnly: true
      required:
      - created_at
      - distance
      - has_expired
      - id
      - interaction_count
      - location
      - owner
      - service
      - skin_details
      - title
      - track_artist
      - track_title
      - track_url
      - updated_at
    PinGeo:
      type: object
      description: GeoJSON serializer for Pin model to display on map
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureEnum'
        id:
          type: integer
          readOnly: true
        geometry:
          type: object
          properties:
            type:
              type: string
              enum:
              - Point
            coordinates:
              type: array
              items:
                type: number
                format: float
              example:
              - 12.9721
              - 77.5933
              minItems: 2
              maxItems: 3
        properties:
          type: object
          properties:
            owner_name:
              type: string
              readOnly: true
            title:
              type: string
              maxLength: 100
            track_title:
              type: string
              maxLength: 255
            track_artist:
              type: string
              maxLength: 255
            service:
              enum:
              - spotify
              - apple
              - soundcloud
              type: string
              description: |-
                * `spotify` - Spotify
                * `apple` - Apple Music
                * `soundcloud` - SoundCloud
            rarity:
              enum:
              - common
              - uncommon
              - rare
              - epic
              - legendary
              type: string
              description: |-
                * `common` - Common
                * `uncommon` - Uncommon
                * `rare` - Rare
                * `epic` - Epic
                * `legendary` - Legendary
            like_count:
              type: string
              readOnly: true
            collect_count:
              type: string
              readOnly: true
            created_at:
              type: string
              format: date-time
              readOnly: true
            distance:
              type: string
              readOnly: true
            has_expired:
              type: string
              readOnly: true
            aura_radius:
              type: integer
              maximum: 2147483647
              minimum: -2147483648
    PinInteraction:
      type: object
      description: Serializer for PinInteraction model
      properties:
        id:
          type: integer
          readOnly: true
        user:
          type: integer
          readOnly: true
        pin:
          type: integer
        interaction_type:
          $ref: '#/components/schemas/InteractionTypeEnum'
        created_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - interaction_type
      - pin
      - user
    PinInteractionRequest:
      type: object
      description: Serializer for PinInteraction model
      properties:
        pin:
          type: integer
        interaction_type:
          $ref: '#/components/schemas/InteractionTypeEnum'
      required:
      - interaction_type
      - pin
    PinRequest:
      type: object
      description: Serializer for Pin model
      properties:
        location:
          type: string
        title:
          type: string
          minLength: 1
          maxLength: 100
        description:
          type: string
          nullable: true
        track_title:
          type: string
          minLength: 1
          maxLength: 255
        track_artist:
          type: string
          minLength: 1
          maxLength: 255
        album:
          type: string
          nullable: true
          maxLength: 255
        track_url:
          type: string
          format: uri
          minLength: 1
          maxLength: 200
        service:
          $ref: '#/components/schemas/ServiceEnum'
        skin:
          type: integer
        rarity:
          $ref: '#/components/schemas/RarityEnum'
        aura_radius:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        is_private:
          type: boolean
        expiration_date:
          type: string
          format: date-time
          nullable: true
      required:
      - location
      - service
      - title
      - track_artist
      - track_title
      - track_url
    PinSkin:
      type: object
      description: Serializer for the PinSkin model
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 50
        image:
          type: string
          format: uri
        description:
          type: string
          nullable: true
        is_premium:
          type: boolean
        created_at:
          type: string
          format: date-time
          readOnly: true
        is_owned:
          type: string
          readOnly: true
      required:
      - created_at
      - id
      - image
      - is_owned
      - name
    PinSkinRequest:
      type: object
      description: Serializer for the PinSkin model
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 50
        image:
          type: string
          format: binary
        description:
          type: string
          nullable: true
        is_premium:
          type: boolean
      required:
      - image
      - name
    RarityEnum:
      enum:
      - common
      - uncommon
      - rare
      - epic
      - legendary
      type: string
      description: |-
        * `common` - Common
        * `uncommon` - Uncommon
        * `rare` - Rare
        * `epic` - Epic
        * `legendary` - Legendary
    Road:
      type: object
      description: Serializer for Road model with GeoJSON support
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureEnum'
        id:
          type: integer
          readOnly: true
        geometry:
          type: object
          properties:
            type:
              type: string
              enum:
              - LineString
            coordinates:
              type: array
              items:
                type: array
                items:
                  type: number
                  format: float
                example:
                - 12.9721
                - 77.5933
                minItems: 2
                maxItems: 3
              example:
              - - 22.4707
                - 70.0577
              - - 12.9721
                - 77.5933
              minItems: 2
        properties:
          type: object
          properties:
            osm_id:
              type: integer
              maximum: 9223372036854775807
              minimum: -9223372036854775808
              format: int64
            name:
              type: string
              nullable: true
              maxLength: 255
            road_type:
              type: string
              maxLength: 50
            width_meters:
              type: number
              format: double
              readOnly: true
            lane_count:
              type: integer
              readOnly: true
            last_updated:
              type: string
              format: date-time
              readOnly: true
    RoadList:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureCollectionEnum'
        features:
          type: array
          items:
            $ref: '#/components/schemas/Road'
    ServiceEnum:
      enum:
      - spotify
      - apple
      - soundcloud
      type: string
      description: |-
        * `spotify` - Spotify
        * `apple` - Apple Music
        * `soundcloud` - SoundCloud
    StatusEnum:
      enum:
      - pending
      - accepted
      - rejected
      type: string
      description: |-
        * `pending` - Pending
        * `accepted` - Accepted
        * `rejected` - Rejected
    TokenObtainPair:
      type: object
      properties:
        access:
          type: string
          readOnly: true
        refresh:
          type: string
          readOnly: true
      required:
      - access
      - refresh
    TokenObtainPairRequest:
      type: object
      properties:
        username:
          type: string
          writeOnly: true
          minLength: 1
        password:
          type: string
          writeOnly: true
          minLength: 1
      required:
      - password
      - username
    TokenRefresh:
      type: object
      properties:
        access:
          type: string
          readOnly: true
        refresh:
          type: string
      required:
      - access
      - refresh
    TokenRefreshRequest:
      type: object
      properties:
        refresh:
          type: string
          minLength: 1
      required:
      - refresh
    TokenVerifyRequest:
      type: object
      properties:
        token:
          type: string
          writeOnly: true
          minLength: 1
      required:
      - token
    TrendingArea:
      type: object
      description: GeoJSON serializer for TrendingArea model
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureEnum'
        id:
          type: integer
          readOnly: true
        geometry:
          type: object
          properties:
            type:
              type: string
              enum:
              - Point
            coordinates:
              type: array
              items:
                type: number
                format: float
              example:
              - 12.9721
              - 77.5933
              minItems: 2
              maxItems: 3
        properties:
          type: object
          properties:
            name:
              type: string
              maxLength: 100
            radius:
              type: integer
              maximum: 2147483647
              minimum: -2147483648
            pin_count:
              type: integer
              maximum: 2147483647
              minimum: -2147483648
            top_genres:
              type: object
              additionalProperties: {}
            last_updated:
              type: string
              format: date-time
              readOnly: true
    TrendingAreaList:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureCollectionEnum'
        features:
          type: array
          items:
            $ref: '#/components/schemas/TrendingArea'
    User:
      type: object
      description: Serializer for the User model
      properties:
        id:
          type: integer
          readOnly: true
        username:
          type: string
          pattern: ^[\w.@+-]+$
          maxLength: 150
        email:
          type: string
          format: email
          maxLength: 254
        profile_pic:
          type: string
          format: uri
          nullable: true
        bio:
          type: string
          nullable: true
        location:
          type: string
          nullable: true
        last_active:
          type: string
          format: date-time
          readOnly: true
        spotify_connected:
          type: boolean
        apple_music_connected:
          type: boolean
        soundcloud_connected:
          type: boolean
      required:
      - email
      - id
      - last_active
      - username
    UserAchievement:
      type: object
      description: Serializer for the UserAchievement model
      properties:
        id:
          type: integer
          readOnly: true
        user:
          type: integer
          readOnly: true
        achievement:
          allOf:
          - $ref: '#/components/schemas/Achievement'
          readOnly: true
        completed_at:
          type: string
          format: date-time
          readOnly: true
        progress:
          type: object
          additionalProperties: {}
          description: Current progress towards achievement
        created_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - achievement
      - completed_at
      - created_at
      - id
      - user
    UserAchievementRequest:
      type: object
      description: Serializer for the UserAchievement model
      properties:
        progress:
          type: object
          additionalProperties: {}
          description: Current progress towards achievement
    UserLocation:
      type: object
      description: GeoJSON serializer for UserLocation model
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureEnum'
        id:
          type: integer
          readOnly: true
        geometry:
          type: object
          properties:
            type:
              type: string
              enum:
              - Point
            coordinates:
              type: array
              items:
                type: number
                format: float
              example:
              - 12.9721
              - 77.5933
              minItems: 2
              maxItems: 3
        properties:
          type: object
          properties:
            user:
              type: integer
            timestamp:
              type: string
              format: date-time
              readOnly: true
    UserLocationList:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/GisFeatureCollectionEnum'
        features:
          type: array
          items:
            $ref: '#/components/schemas/UserLocation'
    UserMapSettings:
      type: object
      description: Serializer for UserMapSettings model
      properties:
        id:
          type: integer
          readOnly: true
        user:
          type: integer
          readOnly: true
        username:
          type: string
          readOnly: true
        show_feature_info:
          type: boolean
        use_3d_buildings:
          type: boolean
        default_latitude:
          type: number
          format: double
        default_longitude:
          type: number
          format: double
        default_zoom:
          type: number
          format: double
        max_cache_size_mb:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        theme:
          type: string
          maxLength: 20
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - id
      - updated_at
      - user
      - username
    UserMapSettingsRequest:
      type: object
      description: Serializer for UserMapSettings model
      properties:
        show_feature_info:
          type: boolean
        use_3d_buildings:
          type: boolean
        default_latitude:
          type: number
          format: double
        default_longitude:
          type: number
          format: double
        default_zoom:
          type: number
          format: double
        max_cache_size_mb:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        theme:
          type: string
          minLength: 1
          maxLength: 20
    UserMini:
      type: object
      description: Minimal serializer for User model, used for nested relationships
      properties:
        id:
          type: integer
          readOnly: true
        username:
          type: string
          pattern: ^[\w.@+-]+$
          maxLength: 150
        profile_pic:
          type: string
          format: uri
          nullable: true
      required:
      - id
      - username
    UserMiniRequest:
      type: object
      description: Minimal serializer for User model, used for nested relationships
      properties:
        username:
          type: string
          minLength: 1
          pattern: ^[\w.@+-]+$
          maxLength: 150
        profile_pic:
          type: string
          format: binary
          nullable: true
      required:
      - username
    UserRegistration:
      type: object
      description: Serializer for registering new users
      properties:
        username:
          type: string
          pattern: ^[\w.@+-]+$
          maxLength: 150
        email:
          type: string
          format: email
          maxLength: 254
        profile_pic:
          type: string
          format: uri
          nullable: true
        bio:
          type: string
          nullable: true
      required:
      - email
      - username
    UserRegistrationRequest:
      type: object
      description: Serializer for registering new users
      properties:
        username:
          type: string
          minLength: 1
          pattern: ^[\w.@+-]+$
          maxLength: 150
        email:
          type: string
          format: email
          minLength: 1
          maxLength: 254
        password:
          type: string
          writeOnly: true
          minLength: 1
        password_confirm:
          type: string
          writeOnly: true
          minLength: 1
        profile_pic:
          type: string
          format: binary
          nullable: true
        bio:
          type: string
          nullable: true
      required:
      - email
      - password
      - password_confirm
      - username
    UserRequest:
      type: object
      description: Serializer for the User model
      properties:
        username:
          type: string
          minLength: 1
          pattern: ^[\w.@+-]+$
          maxLength: 150
        email:
          type: string
          format: email
          minLength: 1
          maxLength: 254
        profile_pic:
          type: string
          format: binary
          nullable: true
        bio:
          type: string
          nullable: true
        location:
          type: string
          nullable: true
        spotify_connected:
          type: boolean
        apple_music_connected:
          type: boolean
        soundcloud_connected:
          type: boolean
      required:
      - email
      - username
    UserUpdate:
      type: object
      description: Serializer for updating user profile
      properties:
        username:
          type: string
          pattern: ^[\w.@+-]+$
          maxLength: 150
        email:
          type: string
          format: email
          readOnly: true
        profile_pic:
          type: string
          format: uri
          nullable: true
        bio:
          type: string
          nullable: true
        location:
          type: string
          nullable: true
      required:
      - email
      - username
    UserUpdateRequest:
      type: object
      description: Serializer for updating user profile
      properties:
        username:
          type: string
          minLength: 1
          pattern: ^[\w.@+-]+$
          maxLength: 150
        profile_pic:
          type: string
          format: binary
          nullable: true
        bio:
          type: string
          nullable: true
        location:
          type: string
          nullable: true
        current_password:
          type: string
          writeOnly: true
          minLength: 1
        new_password:
          type: string
          writeOnly: true
          minLength: 1
      required:
      - username
  securitySchemes:
    jwtAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
