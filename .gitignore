# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
/ios/build/
.kiro

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Firebase config files
google-services.json
GoogleService-Info.plist
firebase_options.dart

# Mapbox
mapbox-credentials.json

# iOS & Android  related
local.properties
Key.properties
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/Pods/
**/ios/**/Podfile.lock
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/.vagrant/

# App signing files
*.jks
*.keystore
**/android/key.properties

# Coverage
coverage/
*.lcov

# Temporary files
*.tmp
*.temp 

# Plugin related
plugins/
sentry.properties