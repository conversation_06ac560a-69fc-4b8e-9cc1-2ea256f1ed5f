import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;

/// Error handling utility for onboarding music selection
class OnboardingErrorHandler {
  static const int MAX_RETRIES = 3;
  static const Duration INITIAL_RETRY_DELAY = Duration(seconds: 1);
  static const double BACKOFF_MULTIPLIER = 2.0;

  /// Execute a function with retry logic and exponential backoff
  static Future<T> withRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = MAX_RETRIES,
    Duration initialDelay = INITIAL_RETRY_DELAY,
    double backoffMultiplier = BACKOFF_MULTIPLIER,
    String? operationName,
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;

    while (attempt <= maxRetries) {
      try {
        final result = await operation();
        if (attempt > 0) {
          print('✅ [ErrorHandler] ${operationName ?? 'Operation'} succeeded on attempt ${attempt + 1}');
        }
        return result;
      } catch (e) {
        attempt++;
        
        if (attempt > maxRetries) {
          print('❌ [<PERSON><PERSON>r<PERSON>and<PERSON>] ${operationName ?? 'Operation'} failed after $maxRetries retries: $e');
          rethrow;
        }

        print('⚠️ [ErrorHandler] ${operationName ?? 'Operation'} failed on attempt $attempt, retrying in ${currentDelay.inSeconds}s: $e');
        
        await Future.delayed(currentDelay);
        currentDelay = Duration(
          milliseconds: (currentDelay.inMilliseconds * backoffMultiplier).round(),
        );
      }
    }

    throw Exception('Unexpected error in retry logic');
  }

  /// Handle errors gracefully with user-friendly messages
  static String getUserFriendlyMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Network connection issue. Please check your internet connection and try again.';
    }
    
    if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again.';
    }
    
    if (errorString.contains('unauthorized') || errorString.contains('401')) {
      return 'Authentication issue. Please try signing in again.';
    }
    
    if (errorString.contains('rate limit') || errorString.contains('429')) {
      return 'Too many requests. Please wait a moment and try again.';
    }
    
    if (errorString.contains('not found') || errorString.contains('404')) {
      return 'Content not found. Please try different search terms.';
    }
    
    if (errorString.contains('server') || errorString.contains('500')) {
      return 'Server issue. Please try again in a few moments.';
    }

    return 'Something went wrong. Please try again.';
  }

  /// Show error snackbar with retry option
  static void showErrorSnackbar(
    BuildContext context,
    dynamic error, {
    VoidCallback? onRetry,
    String? customMessage,
  }) {
    final message = customMessage ?? getUserFriendlyMessage(error);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        action: onRetry != null
            ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  /// Show loading dialog with cancellation option
  static void showLoadingDialog(
    BuildContext context, {
    String message = 'Loading...',
    VoidCallback? onCancel,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
        actions: onCancel != null
            ? [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    onCancel();
                  },
                  child: const Text('Cancel'),
                ),
              ]
            : null,
      ),
    );
  }

  /// Dismiss loading dialog
  static void dismissLoadingDialog(BuildContext context) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }

  /// Execute operation with loading dialog
  static Future<T> withLoadingDialog<T>(
    BuildContext context,
    Future<T> Function() operation, {
    String loadingMessage = 'Loading...',
    String? operationName,
  }) async {
    showLoadingDialog(context, message: loadingMessage);
    
    try {
      final result = await operation();
      dismissLoadingDialog(context);
      return result;
    } catch (e) {
      dismissLoadingDialog(context);
      showErrorSnackbar(context, e);
      rethrow;
    }
  }

  /// Check if error is recoverable
  static bool isRecoverableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Network errors are usually recoverable
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return true;
    }
    
    // Rate limiting is recoverable with delay
    if (errorString.contains('rate limit') || errorString.contains('429')) {
      return true;
    }
    
    // Server errors might be temporary
    if (errorString.contains('server') || errorString.contains('500')) {
      return true;
    }
    
    return false;
  }

  /// Get appropriate retry delay based on error type
  static Duration getRetryDelay(dynamic error, int attemptNumber) {
    final errorString = error.toString().toLowerCase();
    
    // Rate limiting errors need longer delays
    if (errorString.contains('rate limit') || errorString.contains('429')) {
      return Duration(seconds: math.pow(2, attemptNumber + 2).toInt()); // 4s, 8s, 16s
    }
    
    // Network errors can retry quickly
    if (errorString.contains('network') || errorString.contains('connection')) {
      return Duration(seconds: math.pow(2, attemptNumber).toInt()); // 1s, 2s, 4s
    }
    
    // Default exponential backoff
    return Duration(seconds: math.pow(2, attemptNumber + 1).toInt()); // 2s, 4s, 8s
  }

  /// Log error with context
  static void logError(
    dynamic error, {
    String? context,
    Map<String, dynamic>? additionalInfo,
  }) {
    final timestamp = DateTime.now().toIso8601String();
    final contextStr = context != null ? ' [$context]' : '';
    
    print('❌ [ErrorHandler]$contextStr [$timestamp] $error');
    
    if (additionalInfo != null) {
      print('📋 [ErrorHandler] Additional info: $additionalInfo');
    }
  }
}

/// Mixin to add error handling capabilities to widgets
mixin OnboardingErrorHandlerMixin<T extends StatefulWidget> on State<T> {
  /// Execute operation with error handling
  Future<R> handleOperation<R>(
    Future<R> Function() operation, {
    String? operationName,
    bool showLoading = false,
    String loadingMessage = 'Loading...',
    VoidCallback? onError,
  }) async {
    try {
      if (showLoading) {
        return await OnboardingErrorHandler.withLoadingDialog(
          context,
          operation,
          loadingMessage: loadingMessage,
          operationName: operationName,
        );
      } else {
        return await operation();
      }
    } catch (e) {
      OnboardingErrorHandler.logError(e, context: operationName);
      
      if (mounted) {
        OnboardingErrorHandler.showErrorSnackbar(
          context,
          e,
          onRetry: OnboardingErrorHandler.isRecoverableError(e) 
              ? () => handleOperation(operation, operationName: operationName)
              : null,
        );
      }
      
      onError?.call();
      rethrow;
    }
  }

  /// Show error message
  void showError(dynamic error, {VoidCallback? onRetry}) {
    if (mounted) {
      OnboardingErrorHandler.showErrorSnackbar(context, error, onRetry: onRetry);
    }
  }

  /// Show loading
  void showLoading({String message = 'Loading...'}) {
    OnboardingErrorHandler.showLoadingDialog(context, message: message);
  }

  /// Hide loading
  void hideLoading() {
    OnboardingErrorHandler.dismissLoadingDialog(context);
  }
}