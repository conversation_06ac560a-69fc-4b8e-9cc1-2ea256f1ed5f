import '../models/artist_with_genre.dart';
import '../models/music_track.dart';

/// Base class for cycling loader state management
abstract class CyclingLoaderState<T> {
  final List<T> items;
  final bool isLoading;
  final bool hasMore;
  final String? error;
  final int currentIndex;
  final Map<String, int> batchCounts;

  const CyclingLoaderState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    this.error,
    required this.currentIndex,
    required this.batchCounts,
  });

  /// Create initial state
  CyclingLoaderState<T> initial();

  /// Create loading state
  CyclingLoaderState<T> loading();

  /// Create success state with new items
  CyclingLoaderState<T> success({
    required List<T> newItems,
    required int newIndex,
    required Map<String, int> newBatchCounts,
    required bool hasMore,
  });

  /// Create error state
  CyclingLoaderState<T> withError(String errorMessage);

  /// Create state with updated items (for modifications like selection)
  CyclingLoaderState<T> withItems(List<T> newItems);
}

/// State for artist selection with genre cycling
class ArtistSelectionState extends CyclingLoaderState<ArtistWithGenre> {
  final Set<String> selectedArtistKeys; // Using artist|genre keys
  final Map<String, List<ArtistWithGenre>> similarArtistsMap; // Similar artists by parent artist name

  const ArtistSelectionState({
    required super.items,
    required super.isLoading,
    required super.hasMore,
    super.error,
    required super.currentIndex,
    required super.batchCounts,
    required this.selectedArtistKeys,
    this.similarArtistsMap = const {},
  });

  @override
  ArtistSelectionState initial() {
    return const ArtistSelectionState(
      items: [],
      isLoading: false,
      hasMore: true,
      currentIndex: 0,
      batchCounts: {},
      selectedArtistKeys: {},
      similarArtistsMap: {},
    );
  }

  @override
  ArtistSelectionState loading() {
    return ArtistSelectionState(
      items: items,
      isLoading: true,
      hasMore: hasMore,
      error: null,
      currentIndex: currentIndex,
      batchCounts: batchCounts,
      selectedArtistKeys: selectedArtistKeys,
      similarArtistsMap: similarArtistsMap,
    );
  }

  @override
  ArtistSelectionState success({
    required List<ArtistWithGenre> newItems,
    required int newIndex,
    required Map<String, int> newBatchCounts,
    required bool hasMore,
  }) {
    return ArtistSelectionState(
      items: [...items, ...newItems],
      isLoading: false,
      hasMore: hasMore,
      error: null,
      currentIndex: newIndex,
      batchCounts: newBatchCounts,
      selectedArtistKeys: selectedArtistKeys,
      similarArtistsMap: similarArtistsMap,
    );
  }

  @override
  ArtistSelectionState withError(String errorMessage) {
    return ArtistSelectionState(
      items: items,
      isLoading: false,
      hasMore: hasMore,
      error: errorMessage,
      currentIndex: currentIndex,
      batchCounts: batchCounts,
      selectedArtistKeys: selectedArtistKeys,
      similarArtistsMap: similarArtistsMap,
    );
  }

  @override
  ArtistSelectionState withItems(List<ArtistWithGenre> newItems) {
    return ArtistSelectionState(
      items: newItems,
      isLoading: isLoading,
      hasMore: hasMore,
      error: error,
      currentIndex: currentIndex,
      batchCounts: batchCounts,
      selectedArtistKeys: selectedArtistKeys,
      similarArtistsMap: similarArtistsMap,
    );
  }

  /// Update selection state
  ArtistSelectionState withSelection(Set<String> newSelectedKeys) {
    return ArtistSelectionState(
      items: items,
      isLoading: isLoading,
      hasMore: hasMore,
      error: error,
      currentIndex: currentIndex,
      batchCounts: batchCounts,
      selectedArtistKeys: newSelectedKeys,
      similarArtistsMap: similarArtistsMap,
    );
  }

  /// Update similar artists map
  ArtistSelectionState withSimilarArtists(Map<String, List<ArtistWithGenre>> newSimilarArtistsMap) {
    return ArtistSelectionState(
      items: items,
      isLoading: isLoading,
      hasMore: hasMore,
      error: error,
      currentIndex: currentIndex,
      batchCounts: batchCounts,
      selectedArtistKeys: selectedArtistKeys,
      similarArtistsMap: newSimilarArtistsMap,
    );
  }

  /// Get all artists including similar artists in proper order
  List<ArtistWithGenre> get allArtistsWithSimilar {
    final List<ArtistWithGenre> result = [];
    
    for (final artist in items) {
      // Add the main artist
      result.add(artist);
      
      // If this artist is selected, add its similar artists right after it
      if (isArtistSelected(artist)) {
        final similarArtists = similarArtistsMap[artist.name] ?? [];
        result.addAll(similarArtists);
      }
    }
    
    return result;
  }

  /// Helper methods
  List<ArtistWithGenre> get selectedArtists {
    return items.where((artist) => 
      selectedArtistKeys.contains(artist.uniqueKey)
    ).toList();
  }

  bool isArtistSelected(ArtistWithGenre artist) {
    return selectedArtistKeys.contains(artist.uniqueKey);
  }

  int get selectedCount => selectedArtistKeys.length;

  List<String> get selectedArtistNames {
    return SelectionKeyManager.getSelectedArtistNames(selectedArtistKeys);
  }
}

/// State for song selection with artist cycling
class SongSelectionState extends CyclingLoaderState<MusicTrack> {
  final MusicTrack? selectedTrack;

  const SongSelectionState({
    required super.items,
    required super.isLoading,
    required super.hasMore,
    super.error,
    required super.currentIndex,
    required super.batchCounts,
    this.selectedTrack,
  });

  @override
  SongSelectionState initial() {
    return const SongSelectionState(
      items: [],
      isLoading: false,
      hasMore: true,
      currentIndex: 0,
      batchCounts: {},
      selectedTrack: null,
    );
  }

  @override
  SongSelectionState loading() {
    return SongSelectionState(
      items: items,
      isLoading: true,
      hasMore: hasMore,
      error: null,
      currentIndex: currentIndex,
      batchCounts: batchCounts,
      selectedTrack: selectedTrack,
    );
  }

  @override
  SongSelectionState success({
    required List<MusicTrack> newItems,
    required int newIndex,
    required Map<String, int> newBatchCounts,
    required bool hasMore,
  }) {
    return SongSelectionState(
      items: [...items, ...newItems],
      isLoading: false,
      hasMore: hasMore,
      error: null,
      currentIndex: newIndex,
      batchCounts: newBatchCounts,
      selectedTrack: selectedTrack,
    );
  }

  @override
  SongSelectionState withError(String errorMessage) {
    return SongSelectionState(
      items: items,
      isLoading: false,
      hasMore: hasMore,
      error: errorMessage,
      currentIndex: currentIndex,
      batchCounts: batchCounts,
      selectedTrack: selectedTrack,
    );
  }

  @override
  SongSelectionState withItems(List<MusicTrack> newItems) {
    return SongSelectionState(
      items: newItems,
      isLoading: isLoading,
      hasMore: hasMore,
      error: error,
      currentIndex: currentIndex,
      batchCounts: batchCounts,
      selectedTrack: selectedTrack,
    );
  }

  /// Update selected track
  SongSelectionState withSelectedTrack(MusicTrack? track) {
    return SongSelectionState(
      items: items,
      isLoading: isLoading,
      hasMore: hasMore,
      error: error,
      currentIndex: currentIndex,
      batchCounts: batchCounts,
      selectedTrack: track,
    );
  }

  bool get hasSelection => selectedTrack != null;
}