import 'dart:math' as math;

/// Utility class for vector math operations used in AI recommendations
class VectorMathUtil {
  /// Calculate the cosine similarity between two vectors
  static double cosineSimilarity(List<double> a, List<double> b) {
    if (a.length != b.length) {
      throw ArgumentError('Vectors must have the same dimension');
    }
    
    double dotProduct = 0.0;
    double normA = 0.0;
    double normB = 0.0;
    
    for (int i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    normA = math.sqrt(normA);
    normB = math.sqrt(normB);
    
    if (normA == 0.0 || normB == 0.0) {
      return 0.0;
    }
    
    return dotProduct / (normA * normB);
  }
  
  /// Calculate the Euclidean distance between two vectors
  static double euclideanDistance(List<double> a, List<double> b) {
    if (a.length != b.length) {
      throw ArgumentError('Vectors must have the same dimension');
    }
    
    double sum = 0.0;
    
    for (int i = 0; i < a.length; i++) {
      final diff = a[i] - b[i];
      sum += diff * diff;
    }
    
    return math.sqrt(sum);
  }
  
  /// Calculate the Manhattan distance between two vectors
  static double manhattanDistance(List<double> a, List<double> b) {
    if (a.length != b.length) {
      throw ArgumentError('Vectors must have the same dimension');
    }
    
    double sum = 0.0;
    
    for (int i = 0; i < a.length; i++) {
      sum += (a[i] - b[i]).abs();
    }
    
    return sum;
  }
  
  /// Normalize a vector to have unit length (L2 norm)
  static List<double> normalize(List<double> vector) {
    double norm = 0.0;
    
    for (int i = 0; i < vector.length; i++) {
      norm += vector[i] * vector[i];
    }
    
    norm = math.sqrt(norm);
    
    if (norm == 0.0) {
      return List.filled(vector.length, 0.0);
    }
    
    return vector.map((v) => v / norm).toList();
  }
  
  /// Calculate the dot product of two vectors
  static double dotProduct(List<double> a, List<double> b) {
    if (a.length != b.length) {
      throw ArgumentError('Vectors must have the same dimension');
    }
    
    double sum = 0.0;
    
    for (int i = 0; i < a.length; i++) {
      sum += a[i] * b[i];
    }
    
    return sum;
  }
  
  /// Add two vectors
  static List<double> add(List<double> a, List<double> b) {
    if (a.length != b.length) {
      throw ArgumentError('Vectors must have the same dimension');
    }
    
    final result = List<double>.filled(a.length, 0.0);
    
    for (int i = 0; i < a.length; i++) {
      result[i] = a[i] + b[i];
    }
    
    return result;
  }
  
  /// Subtract vector b from vector a
  static List<double> subtract(List<double> a, List<double> b) {
    if (a.length != b.length) {
      throw ArgumentError('Vectors must have the same dimension');
    }
    
    final result = List<double>.filled(a.length, 0.0);
    
    for (int i = 0; i < a.length; i++) {
      result[i] = a[i] - b[i];
    }
    
    return result;
  }
  
  /// Multiply a vector by a scalar
  static List<double> scalarMultiply(List<double> vector, double scalar) {
    return vector.map((v) => v * scalar).toList();
  }
  
  /// Calculate the weighted average of multiple vectors
  static List<double> weightedAverage(List<List<double>> vectors, List<double> weights) {
    if (vectors.isEmpty) {
      throw ArgumentError('Empty vector list');
    }
    
    if (vectors.length != weights.length) {
      throw ArgumentError('Number of vectors must match number of weights');
    }
    
    final dimension = vectors.first.length;
    
    // Ensure all vectors have the same dimension
    for (final vector in vectors) {
      if (vector.length != dimension) {
        throw ArgumentError('All vectors must have the same dimension');
      }
    }
    
    // Calculate sum of weights
    double weightSum = 0.0;
    for (final weight in weights) {
      weightSum += weight;
    }
    
    if (weightSum == 0.0) {
      throw ArgumentError('Sum of weights must be non-zero');
    }
    
    // Initialize result vector with zeros
    final result = List<double>.filled(dimension, 0.0);
    
    // Calculate weighted sum
    for (int i = 0; i < vectors.length; i++) {
      final vector = vectors[i];
      final weight = weights[i] / weightSum; // Normalize weight
      
      for (int j = 0; j < dimension; j++) {
        result[j] += vector[j] * weight;
      }
    }
    
    return result;
  }
} 