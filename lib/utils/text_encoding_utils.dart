import 'dart:convert';

class TextEncodingUtils {
  /// Fix common emoji and UTF-8 encoding issues in text
  /// Specifically designed to handle corrupted emojis in collection names and descriptions
  static String fixEmojiEncoding(String text) {
    if (text.isEmpty) return text;
    
    // Common emoji encoding corruption patterns and their fixes
    String fixed = text
        // Fix music-related emojis
        .replaceAll(RegExp(r'Ã°ÂÂ»Ã°ÂÂ¹'), '💽🎧') // "edm Ã°ÂÂ»Ã°ÂÂ¹" -> "edm 💽🎧"
        .replaceAll(RegExp(r'Ã°ÂÂµÃ°ÂÂ¶'), '🎵🎶') // Musical notes
        .replaceAll(RegExp(r'Ã°ÂÂ¤Ã°ÂÂ¤'), '🎤🎤') // Microphones
        .replaceAll(RegExp(r'Ã°ÂÂ·'), '🎷') // Saxophone
        .replaceAll(RegExp(r'Ã°ÂÂ¸'), '🎸') // Guitar
        .replaceAll(RegExp(r'Ã°ÂÂ¹'), '🎹') // Piano
        .replaceAll(RegExp(r'Ã°ÂÂº'), '🎺') // Trumpet
        .replaceAll(RegExp(r'Ã°ÂÂ»'), '🎻') // Violin
        .replaceAll(RegExp(r'Ã°ÂÂ¼'), '🎼') // Musical score
        .replaceAll(RegExp(r'Ã°ÂÂ½'), '🎽') // Running shirt
        .replaceAll(RegExp(r'Ã°ÂÂ¾'), '🎾') // Tennis
        .replaceAll(RegExp(r'Ã°ÂÂ¿'), '🎿') // Ski
        
        // Fix face emojis
        .replaceAll(RegExp(r'Ã°ÂÂÂ'), '😀') // Grinning face
        .replaceAll(RegExp(r'Ã°ÂÂÂ'), '😁') // Beaming face
        .replaceAll(RegExp(r'Ã°ÂÂÂ'), '😂') // Face with tears of joy
        .replaceAll(RegExp(r'Ã°ÂÂÂ'), '😃') // Grinning face with big eyes
        .replaceAll(RegExp(r'Ã°ÂÂÂ'), '😄') // Grinning face with smiling eyes
        .replaceAll(RegExp(r'Ã°ÂÂÂ'), '😅') // Grinning face with sweat
        .replaceAll(RegExp(r'Ã°ÂÂÂ'), '😆') // Grinning squinting face
        .replaceAll(RegExp(r'Ã°ÂÂÂ'), '😇') // Smiling face with halo
        .replaceAll(RegExp(r'Ã°ÂÂÂ'), '😈') // Smiling face with horns
        .replaceAll(RegExp(r'Ã°ÂÂÂ'), '😉') // Winking face
        .replaceAll(RegExp(r'Ã°ÂÂÂÂ'), '😊') // Smiling face with smiling eyes
        
        // Fix heart emojis
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💕') // Two hearts
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💖') // Sparkling heart
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💗') // Growing heart
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💘') // Heart with arrow
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💙') // Blue heart
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💚') // Green heart
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💛') // Yellow heart
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💜') // Purple heart
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💝') // Heart with ribbon
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💞') // Revolving hearts
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💟') // Heart decoration
        
        // Fix common symbol emojis
        .replaceAll(RegExp(r'Ã¢Â¤Â¤'), '⭐') // Star
        .replaceAll(RegExp(r'Ã¢Â'), '⚡') // Lightning bolt
        .replaceAll(RegExp(r'Ã°ÂÂ'), '🔥') // Fire
        .replaceAll(RegExp(r'Ã°ÂÂ'), '💫') // Dizzy star
        .replaceAll(RegExp(r'Ã°ÂÂ'), '🌟') // Glowing star
        .replaceAll(RegExp(r'Ã°ÂÂ'), '✨') // Sparkles
        
        // Fix common text corruption patterns
        .replaceAll(RegExp(r'Ã¢â¬â¢'), "'") // Fix corrupted apostrophe
        .replaceAll(RegExp(r'Ã¢â¬'), '') // Remove specific corruption
        .replaceAll(RegExp(r'ÃâÂ°'), '') // Remove specific corruption
        .replaceAll(RegExp(r'Ã¢â'), '') // Remove specific corruption
        .replaceAll(RegExp(r'Ãâ'), '') // Remove specific corruption
        .replaceAll(RegExp(r'Ã©'), 'é') // Fix corrupted é
        .replaceAll(RegExp(r'Ã¨'), 'è') // Fix corrupted è
        .replaceAll(RegExp(r'Ã¡'), 'á') // Fix corrupted á
        .replaceAll(RegExp(r'Ã '), 'à') // Fix corrupted à
        .replaceAll(RegExp(r'Ã¼'), 'ü') // Fix corrupted ü
        .replaceAll(RegExp(r'Ã¶'), 'ö') // Fix corrupted ö
        .replaceAll(RegExp(r'Ã±'), 'ñ') // Fix corrupted ñ
        .replaceAll(RegExp(r'Ã§'), 'ç') // Fix corrupted ç
        
        // Remove control characters but keep all unicode including CJK characters
        .replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '')
        .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
        .trim();
    
    // If something went wrong with fixing, return original
    if (fixed.isEmpty && text.isNotEmpty) {
      return text;
    }
    
    return fixed;
  }
  
  /// Clean text specifically for collection names
  static String cleanCollectionName(String name) {
    if (name.isEmpty) return name;
    
    String cleaned = fixEmojiEncoding(name);
    
    // Additional cleaning for collection names
    cleaned = cleaned
        // Remove any remaining corruption artifacts
        .replaceAll(RegExp(r'[ÃÂ]{2,}'), '') // Remove repeated corruption patterns
        .replaceAll(RegExp(r'Â+'), '') // Remove repeated Â
        .replaceAll(RegExp(r'Ã+'), '') // Remove repeated Ã
        // Normalize spaces
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
    
    return cleaned.isEmpty ? name : cleaned;
  }
  
  /// Clean text specifically for collection descriptions
  static String cleanCollectionDescription(String description) {
    if (description.isEmpty) return description;
    
    String cleaned = fixEmojiEncoding(description);
    
    // Additional cleaning for descriptions (more lenient than names)
    cleaned = cleaned
        // Remove corruption artifacts but preserve line breaks
        .replaceAll(RegExp(r'[ÃÂ]{2,}'), '')
        .replaceAll(RegExp(r'Â+'), '')
        .replaceAll(RegExp(r'Ã+'), '')
        // Normalize spaces but preserve intentional line breaks
        .replaceAll(RegExp(r'[ \t]+'), ' ') // Normalize horizontal whitespace
        .replaceAll(RegExp(r'\n\s*\n\s*\n+'), '\n\n') // Normalize multiple line breaks
        .trim();
    
    return cleaned.isEmpty ? description : cleaned;
  }
  
  /// Validate if text contains corruption patterns
  static bool hasEncodingIssues(String text) {
    if (text.isEmpty) return false;
    
    // Check for common corruption patterns
    return text.contains(RegExp(r'Ã°ÂÂ')) ||
           text.contains(RegExp(r'Ã¢â')) ||
           text.contains(RegExp(r'ÃâÂ')) ||
           text.contains(RegExp(r'[ÃÂ]{3,}')) ||
           text.contains(RegExp(r'Â{2,}'));
  }
  
  /// Attempt to decode double-encoded UTF-8 text
  static String fixDoubleEncoding(String text) {
    if (text.isEmpty) return text;
    
    try {
      // Try to decode as Latin-1 first, then as UTF-8
      final bytes = latin1.encode(text);
      final decoded = utf8.decode(bytes);
      return decoded;
    } catch (e) {
      // If decoding fails, return the original text
      return text;
    }
  }
  
  /// Comprehensive text cleaning for collections
  static String cleanCollectionText(String text, {bool isDescription = false}) {
    if (text.isEmpty) return text;
    
    // First try to fix double encoding
    String cleaned = fixDoubleEncoding(text);
    
    // Then apply emoji and encoding fixes
    cleaned = fixEmojiEncoding(cleaned);
    
    // Apply specific cleaning based on text type
    if (isDescription) {
      cleaned = cleanCollectionDescription(cleaned);
    } else {
      cleaned = cleanCollectionName(cleaned);
    }
    
    return cleaned;
  }
} 