import 'dart:math' as math;
import 'package:vector_math/vector_math_64.dart';

class SphereGeometry {
  final List<Vector3> vertices;
  final List<Vector2> uvs;
  final List<int> indices;
  final List<Vector3> normals;
  
  SphereGeometry._(this.vertices, this.uvs, this.indices, this.normals);
  
  static SphereGeometry generate({
    double radius = 1.0,
    int latSegments = 32,
    int lonSegments = 32,
  }) {
    final vertices = <Vector3>[];
    final uvs = <Vector2>[];
    final indices = <int>[];
    final normals = <Vector3>[];
    
    for (int lat = 0; lat <= latSegments; lat++) {
      final theta = lat * math.pi / latSegments;
      final sinTheta = math.sin(theta);
      final cosTheta = math.cos(theta);
      
      for (int lon = 0; lon <= lonSegments; lon++) {
        final phi = lon * 2 * math.pi / lonSegments;
        final sinPhi = math.sin(phi);
        final cosPhi = math.cos(phi);
        
        final x = cosPhi * sinTheta;
        final y = cosTheta;
        final z = sinPhi * sinTheta;
        
        vertices.add(Vector3(x, y, z) * radius);
        uvs.add(Vector2(lon / lonSegments, lat / latSegments));
        normals.add(Vector3(x, y, z));
        
        if (lat < latSegments && lon < lonSegments) {
        final first = lat * (lonSegments + 1) + lon;
        final second = first + lonSegments + 1;
        
        indices.addAll([
            first, second, first + 1,
            second, second + 1, first + 1,
        ]);
        }
      }
    }
    
    return SphereGeometry._(vertices, uvs, indices, normals);
  }
} 