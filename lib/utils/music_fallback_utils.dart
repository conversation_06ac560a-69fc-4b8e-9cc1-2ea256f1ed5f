import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../models/music_track.dart';
import '../providers/youtube_provider.dart';

/// Exception thrown when Apple Music cannot find an exact match for a track
class NoExactMatchException implements Exception {
  final String message;

  const NoExactMatchException(this.message);

  @override
  String toString() => 'NoExactMatchException: $message';
}

/// Utility class for handling music service fallback scenarios
class MusicFallbackUtils {
  /// Show a snackbar when Apple Music can't find an exact match
  /// Provides user with option to try YouTube fallback
  static void showNoExactMatchSnackbar(
    BuildContext context,
    MusicTrack track, {
    VoidCallback? onDismiss,
    bool isFromQueue = false,
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.search_off, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Could not find exact match on Apple Music',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange.withOpacity(0.9),
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Try on YouTube',
          textColor: Colors.white,
          onPressed: () => _tryYouTubeFallback(context, track),
        ),
        onVisible: () {
          if (kDebugMode) {
            print('🔔 [MusicFallbackUtils] Showing no exact match snackbar for: "${track.title}" by "${track.artist}"');
          }
        },
        behavior: SnackBarBehavior.floating,
      ),
    ).closed.then((reason) {
      if (kDebugMode) {
        print('🔔 [MusicFallbackUtils] Snackbar closed with reason: $reason');
      }
      
      // If user dismissed without action and this is from queue, call onDismiss
      if (reason == SnackBarClosedReason.dismiss || 
          reason == SnackBarClosedReason.timeout) {
        onDismiss?.call();
      }
    });
  }

  /// Try YouTube fallback for the given track
  static Future<void> _tryYouTubeFallback(BuildContext context, MusicTrack track) async {
    if (!context.mounted) return;

    try {
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);

      if (!youtubeProvider.isInitialized) {
        await youtubeProvider.initialize();
      }

      if (kDebugMode) {
        print('🎬 [MusicFallbackUtils] Attempting YouTube playback for: ${track.title} by ${track.artist}');
      }

      // Show loading state
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Searching YouTube for "${track.title}"...',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.red.withOpacity(0.9),
        ),
      );

      final success = await youtubeProvider.playTrack(track);

      // Clear loading snackbar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      if (success) {
        if (kDebugMode) {
          print('✅ [MusicFallbackUtils] Successfully played track on YouTube: ${track.title}');
        }

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.play_circle, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Now playing: "${track.title}" (YouTube)',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red.withOpacity(0.9),
            duration: const Duration(seconds: 3),
          ),
        );
      } else {
        if (kDebugMode) {
          print('❌ [MusicFallbackUtils] YouTube fallback failed for: ${track.title}');
        }

        // Show failure message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Could not play "${track.title}" on any service',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [MusicFallbackUtils] YouTube fallback error: $e');
      }

      // Clear any loading snackbar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Error trying YouTube: ${e.toString()}',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  /// Show a snackbar for general Apple Music errors with YouTube fallback option
  static void showAppleMusicErrorSnackbar(
    BuildContext context,
    MusicTrack track,
    String errorMessage, {
    VoidCallback? onDismiss,
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                errorMessage,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Try YouTube',
          textColor: Colors.white,
          onPressed: () => _tryYouTubeFallback(context, track),
        ),
        behavior: SnackBarBehavior.floating,
      ),
    ).closed.then((reason) {
      if (reason == SnackBarClosedReason.dismiss || 
          reason == SnackBarClosedReason.timeout) {
        onDismiss?.call();
      }
    });
  }
}
