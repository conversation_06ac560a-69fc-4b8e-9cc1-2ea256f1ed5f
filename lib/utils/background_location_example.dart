import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/map_provider.dart';
import '../services/location/location_manager.dart';

/// Example usage of background location tracking
/// This demonstrates how to integrate background location with your app
class BackgroundLocationExample extends StatefulWidget {
  const BackgroundLocationExample({Key? key}) : super(key: key);

  @override
  State<BackgroundLocationExample> createState() => _BackgroundLocationExampleState();
}

class _BackgroundLocationExampleState extends State<BackgroundLocationExample> {
  final LocationManager _locationManager = LocationManager();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Background Location Example'),
      ),
      body: Consumer<MapProvider>(
        builder: (context, mapProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Location Status Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Location Status',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        Text('Status: ${_locationManager.locationStatus}'),
                        Text('Foreground Tracking: ${_locationManager.isTracking}'),
                        Text('Background Tracking: ${_locationManager.isBackgroundTracking}'),
                        if (_locationManager.currentPosition != null) ...[
                          const SizedBox(height: 8),
                          Text('Latitude: ${_locationManager.currentPosition!.latitude.toStringAsFixed(6)}'),
                          Text('Longitude: ${_locationManager.currentPosition!.longitude.toStringAsFixed(6)}'),
                          Text('Accuracy: ${_locationManager.currentPosition!.accuracy.toStringAsFixed(1)}m'),
                        ],
                        if (_locationManager.errorMessage != null) ...[
                          const SizedBox(height: 8),
                          Text(
                            'Error: ${_locationManager.errorMessage}',
                            style: TextStyle(color: Colors.red),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Control Buttons
                ElevatedButton(
                  onPressed: () async {
                    final success = await _locationManager.startTracking();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(success 
                          ? 'Foreground location tracking started'
                          : 'Failed to start foreground location tracking'
                        ),
                      ),
                    );
                    setState(() {});
                  },
                  child: const Text('Start Foreground Location'),
                ),
                
                const SizedBox(height: 8),
                
                ElevatedButton(
                  onPressed: () async {
                    final success = await mapProvider.startBackgroundLocationTracking();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(success 
                          ? 'Background location tracking started'
                          : 'Failed to start background location tracking'
                        ),
                      ),
                    );
                  },
                  child: const Text('Start Background Location'),
                ),
                
                const SizedBox(height: 8),
                
                ElevatedButton(
                  onPressed: () {
                    _locationManager.stopTracking();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Foreground location tracking stopped'),
                      ),
                    );
                    setState(() {});
                  },
                  child: const Text('Stop Foreground Location'),
                ),
                
                const SizedBox(height: 8),
                
                ElevatedButton(
                  onPressed: () async {
                    await mapProvider.stopBackgroundLocationTracking();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Background location tracking stopped'),
                      ),
                    );
                  },
                  child: const Text('Stop Background Location'),
                ),
                
                const SizedBox(height: 16),
                
                // Quick Toggle Button
                ElevatedButton.icon(
                  onPressed: () async {
                    final isCurrentlyTracking = await mapProvider.toggleBackgroundLocationTracking();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(isCurrentlyTracking 
                          ? 'Background location tracking enabled'
                          : 'Background location tracking disabled'
                        ),
                      ),
                    );
                  },
                  icon: Icon(mapProvider.isBackgroundLocationTracking 
                    ? Icons.location_off 
                    : Icons.location_on
                  ),
                  label: Text(mapProvider.isBackgroundLocationTracking 
                    ? 'Disable Background Tracking'
                    : 'Enable Background Tracking'
                  ),
                ),
                
                const Spacer(),
                
                // Info Card
                Card(
                  color: Colors.blue.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue),
                            const SizedBox(width: 8),
                            Text(
                              'Background Location Info',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• Background location allows the app to track your location even when minimized\n'
                          '• This helps discover music pins nearby automatically\n'
                          '• The app will show a persistent notification on Android\n'
                          '• Battery usage may increase when background tracking is enabled\n'
                          '• You can disable background tracking anytime in settings',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

/// Helper class for background location integration
class BackgroundLocationHelper {
  static final LocationManager _locationManager = LocationManager();
  
  /// Initialize background location with proper error handling
  static Future<bool> initializeBackgroundLocation() async {
    try {
      // First request basic location permission
      final status = await _locationManager.requestLocationPermission();
      if (status != LocationStatus.available) {
        debugPrint('❌ Location permission not available: $status');
        return false;
      }
      
      // Start background tracking
      final success = await _locationManager.startBackgroundTracking();
      if (success) {
        debugPrint('✅ Background location tracking initialized successfully');
      } else {
        debugPrint('❌ Failed to initialize background location tracking');
      }
      
      return success;
    } catch (e) {
      debugPrint('❌ Error initializing background location: $e');
      return false;
    }
  }
  
  /// Stop all location tracking
  static Future<void> cleanup() async {
    try {
      _locationManager.stopTracking();
      await _locationManager.stopBackgroundTracking();
      debugPrint('✅ Background location cleanup completed');
    } catch (e) {
      debugPrint('❌ Error during background location cleanup: $e');
    }
  }
  
  /// Check if background location is currently active
  static bool get isBackgroundLocationActive => _locationManager.isBackgroundTracking;
  
  /// Get current location status
  static LocationStatus get locationStatus => _locationManager.locationStatus;
} 