import 'dart:math';

/// Calculates the distance between two geographical points using the Haversine formula.
///
/// Returns the distance in meters.
double calculateDistance(
  double lat1, double lon1, double lat2, double lon2) {
  const p = 0.017453292519943295; // Pi / 180
  const R = 6371000.0; // Earth's radius in meters

  final a = 0.5 - cos((lat2 - lat1) * p) / 2 +
      cos(lat1 * p) * cos(lat2 * p) *
          (1 - cos((lon2 - lon1) * p)) / 2;
  return R * 2 * asin(sqrt(a));
} 