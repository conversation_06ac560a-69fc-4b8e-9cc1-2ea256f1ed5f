import 'package:flutter/foundation.dart';
import '../services/api/django_auth_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/spotify_provider.dart';
import '../screens/auth/services/auth_service.dart';

/// AuthManager provides a unified interface for authentication-related operations.
/// It demonstrates how to use both DjangoAuthService and SpotifyAuthService together.
class AuthManager {
  // Singleton pattern
  static final AuthManager _instance = AuthManager._internal();
  factory AuthManager() => _instance;
  AuthManager._internal();

  // Services
  final DjangoAuthService _djangoAuthService = DjangoAuthService();

  // Check if user is logged in to the app
  Future<bool> isUserLoggedIn() async {
    return await _djangoAuthService.isAuthenticated();
  }

  // Get user profile data
  Future<Map<String, dynamic>?> getUserProfile() async {
    return await _djangoAuthService.getUserProfile();
  }

  // Logout from the app (both Django and Spotify)
  Future<bool> logout() async {
    // DjangoAuthService.logout() now handles both Django and Spotify disconnection
    return await _djangoAuthService.logout();
  }

  // Test the Django backend connection
  Future<Map<String, dynamic>> testBackendConnection() async {
    if (kDebugMode) {
      print("Testing Django backend connection...");
    }
    
    try {
      Map<String, dynamic> result = await _djangoAuthService.testConnection();
      
      if (kDebugMode) {
        if (result['success']) {
          print("✅ Django connection test successful!");
          print("📊 Status: ${result['statusCode']}");
          print("⏱️ Response time: ${result['responseTime']} ms");
        } else {
          print("❌ Django connection test failed!");
          print("Error: ${result['error']}");
        }
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print("❌ Error testing Django connection: $e");
      }
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  static bool _isReauthenticating = false;
  static DateTime? _lastReauthAttempt;
  
  /// Handle 401 errors by triggering automatic re-authentication
  /// Returns true if re-auth was successful, false otherwise
  static Future<bool> handle401Error(BuildContext context, {String? source}) async {
    // Prevent multiple simultaneous re-auth attempts
    if (_isReauthenticating) {
      if (kDebugMode) {
        print('🔄 [AuthManager] Re-authentication already in progress, skipping duplicate attempt');
      }
      return false;
    }
    
    // Debounce: Don't allow re-auth more than once every 30 seconds
    final now = DateTime.now();
    if (_lastReauthAttempt != null && now.difference(_lastReauthAttempt!) < Duration(seconds: 30)) {
      if (kDebugMode) {
        print('🔄 [AuthManager] Re-authentication attempt too recent, skipping');
      }
      return false;
    }
    
    _lastReauthAttempt = now;
    _isReauthenticating = true;
    
    try {
      if (kDebugMode) {
        print('🔐 [AuthManager] 401 Unauthorized detected from: ${source ?? 'unknown'}');
        print('🔄 [AuthManager] Starting automatic re-authentication...');
      }
      
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // Check if user was previously authenticated with Spotify
      final wasSpotifyConnected = spotifyProvider.isConnected || 
                                  authProvider.currentUser?.connectedServices?['spotify'] == true;
      
      if (wasSpotifyConnected) {
        if (kDebugMode) {
          print('🎵 [AuthManager] Re-authenticating with Spotify using login screen logic');
        }
        
        // Use the EXACT same logic as login screen for Spotify authentication
        final success = await AuthService.handleSpotifySignIn(context);
        
        if (success) {
          if (kDebugMode) {
            print('✅ [AuthManager] Spotify re-authentication successful');
            print('✅ [AuthManager] All tokens and user data stored correctly');
          }
          
          // Show success message to user
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Re-authenticated successfully'),
                  ],
                ),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }
          
          return true;
        } else {
          if (kDebugMode) {
            print('❌ [AuthManager] Spotify re-authentication failed');
          }
        }
      } else {
        if (kDebugMode) {
          print('⚠️ [AuthManager] User was not previously connected to Spotify, cannot re-authenticate');
        }
      }
      
      // If we reach here, re-authentication failed
      if (context.mounted) {
        _showReauthFailedDialog(context);
      }
      
      return false;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AuthManager] Re-authentication error: $e');
      }
      
      if (context.mounted) {
        _showReauthFailedDialog(context);
      }
      
      return false;
    } finally {
      _isReauthenticating = false;
    }
  }
  
  /// Show dialog when re-authentication fails
  static void _showReauthFailedDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              SizedBox(width: 8),
              Text('Session Expired'),
            ],
          ),
          content: const Text(
            'Your session has expired. Please sign in again to continue using the app.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to login screen
                Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
              },
              child: const Text('Sign In Again'),
            ),
          ],
        );
      },
    );
  }
  
  /// Check if automatic re-authentication is currently in progress
  static bool get isReauthenticating => _isReauthenticating;
  
  /// Check if user can attempt re-authentication (not debounced)
  static bool get canAttemptReauth {
    if (_isReauthenticating) return false;
    
    final now = DateTime.now();
    if (_lastReauthAttempt != null && now.difference(_lastReauthAttempt!) < Duration(seconds: 30)) {
      return false;
    }
    
    return true;
  }
} 