import 'dart:math' as math;
import 'package:vector_math/vector_math_64.dart';

class EarthCalculations {
  static const double earthRadius = 6371.0; // km
  static const double earthCircumference = 2 * math.pi * earthRadius;
  
  /// Converts latitude and longitude to 3D position on unit sphere
  static Vector3 latLongToPoint(double lat, double lon) {
    final phi = (90 - lat) * math.pi / 180;
    final theta = (lon + 180) * math.pi / 180;
    
    final x = -math.sin(phi) * math.cos(theta);
    final y = math.cos(phi);
    final z = math.sin(phi) * math.sin(theta);
    
    return Vector3(x, y, z);
  }
  
  /// Converts 3D point on unit sphere to latitude and longitude
  static Vector2 pointToLatLong(Vector3 point) {
    final lat = 90 - math.acos(point.y) * 180 / math.pi;
    final lon = math.atan2(point.z, -point.x) * 180 / math.pi - 180;
    
    return Vector2(lat, lon);
  }
  
  /// Calculates the great circle distance between two points
  static double greatCircleDistance(double lat1, double lon1, double lat2, double lon2) {
    final phi1 = lat1 * math.pi / 180;
    final phi2 = lat2 * math.pi / 180;
    final deltaPhi = (lat2 - lat1) * math.pi / 180;
    final deltaLambda = (lon2 - lon1) * math.pi / 180;
    
    final a = math.sin(deltaPhi / 2) * math.sin(deltaPhi / 2) +
             math.cos(phi1) * math.cos(phi2) *
             math.sin(deltaLambda / 2) * math.sin(deltaLambda / 2);
             
    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    
    return earthRadius * c;
  }
  
  /// Calculates the initial bearing between two points
  static double initialBearing(double lat1, double lon1, double lat2, double lon2) {
    final phi1 = lat1 * math.pi / 180;
    final phi2 = lat2 * math.pi / 180;
    final deltaLambda = (lon2 - lon1) * math.pi / 180;
    
    final y = math.sin(deltaLambda) * math.cos(phi2);
    final x = math.cos(phi1) * math.sin(phi2) -
             math.sin(phi1) * math.cos(phi2) * math.cos(deltaLambda);
             
    return math.atan2(y, x) * 180 / math.pi;
  }
  
  /// Calculates the midpoint between two points
  static Vector2 midpoint(double lat1, double lon1, double lat2, double lon2) {
    final phi1 = lat1 * math.pi / 180;
    final phi2 = lat2 * math.pi / 180;
    final lambda1 = lon1 * math.pi / 180;
    final deltaLambda = (lon2 - lon1) * math.pi / 180;
    
    final Bx = math.cos(phi2) * math.cos(deltaLambda);
    final By = math.cos(phi2) * math.sin(deltaLambda);
    
    final phi3 = math.atan2(
      math.sin(phi1) + math.sin(phi2),
      math.sqrt((math.cos(phi1) + Bx) * (math.cos(phi1) + Bx) + By * By),
    );
    
    final lambda3 = lambda1 + math.atan2(By, math.cos(phi1) + Bx);
    
    return Vector2(
      phi3 * 180 / math.pi,
      lambda3 * 180 / math.pi,
    );
  }
  
  /// Calculates the destination point given a start point, bearing and distance
  static Vector2 destination(double lat, double lon, double bearing, double distance) {
    final delta = distance / earthRadius;
    final theta = bearing * math.pi / 180;
    final phi1 = lat * math.pi / 180;
    final lambda1 = lon * math.pi / 180;
    
    final phi2 = math.asin(
      math.sin(phi1) * math.cos(delta) +
      math.cos(phi1) * math.sin(delta) * math.cos(theta),
    );
    
    final lambda2 = lambda1 + math.atan2(
      math.sin(theta) * math.sin(delta) * math.cos(phi1),
      math.cos(delta) - math.sin(phi1) * math.sin(phi2),
    );
    
    return Vector2(
      phi2 * 180 / math.pi,
      lambda2 * 180 / math.pi,
    );
  }
} 