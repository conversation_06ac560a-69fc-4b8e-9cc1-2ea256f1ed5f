import 'package:flutter/material.dart';
import 'dart:async';

class InfiniteScrollController {
  final ScrollController _scrollController;
  final Future<void> Function() _onLoadMore;
  
  bool _isLoading = false;
  bool _hasMore = true;
  static const double LOAD_MORE_THRESHOLD = 0.8;
  
  Timer? _scrollDebounceTimer;
  static const Duration SCROLL_DEBOUNCE_DURATION = Duration(milliseconds: 200);

  InfiniteScrollController({
    required ScrollController scrollController,
    required Future<void> Function() onLoadMore,
  }) : _scrollController = scrollController,
       _onLoadMore = onLoadMore {
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    // Debounce scroll events to avoid excessive loading calls
    _scrollDebounceTimer?.cancel();
    _scrollDebounceTimer = Timer(SCROLL_DEBOUNCE_DURATION, () {
      if (_shouldLoadMore()) {
        _loadMore();
      }
    });
  }

  bool _shouldLoadMore() {
    if (!_scrollController.hasClients) return false;
    if (_isLoading || !_hasMore) return false;
    
    final scrollPosition = _scrollController.position.pixels;
    final maxScrollExtent = _scrollController.position.maxScrollExtent;
    
    // Load more when scrolled 80% of the way
    return scrollPosition >= maxScrollExtent * LOAD_MORE_THRESHOLD;
  }

  Future<void> _loadMore() async {
    if (_isLoading) return;
    
    print('🔄 [InfiniteScrollController] Loading more content...');
    _isLoading = true;
    
    try {
      await _onLoadMore();
    } catch (e) {
      print('❌ [InfiniteScrollController] Error loading more content: $e');
    } finally {
      _isLoading = false;
    }
  }

  /// Update loading state externally
  void setLoading(bool loading) {
    _isLoading = loading;
  }

  /// Update hasMore state externally
  void setHasMore(bool hasMore) {
    _hasMore = hasMore;
  }

  /// Get current loading state
  bool get isLoading => _isLoading;

  /// Get current hasMore state
  bool get hasMore => _hasMore;

  /// Force load more (useful for manual triggers)
  Future<void> forceLoadMore() async {
    if (!_isLoading) {
      await _loadMore();
    }
  }

  /// Reset the controller state
  void reset() {
    _isLoading = false;
    _hasMore = true;
    _scrollDebounceTimer?.cancel();
  }

  /// Dispose the controller
  void dispose() {
    _scrollDebounceTimer?.cancel();
    _scrollController.removeListener(_onScroll);
  }
}

/// Mixin to easily add infinite scroll functionality to widgets
mixin InfiniteScrollMixin<T extends StatefulWidget> on State<T> {
  late InfiniteScrollController _infiniteScrollController;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _infiniteScrollController = InfiniteScrollController(
      scrollController: _scrollController,
      onLoadMore: onLoadMore,
    );
  }

  @override
  void dispose() {
    _infiniteScrollController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// Override this method to implement loading logic
  Future<void> onLoadMore();

  /// Get the scroll controller for use in widgets
  ScrollController get scrollController => _scrollController;

  /// Get the infinite scroll controller for state management
  InfiniteScrollController get infiniteScrollController => _infiniteScrollController;

  /// Update loading state
  void setScrollLoading(bool loading) {
    _infiniteScrollController.setLoading(loading);
  }

  /// Update hasMore state
  void setScrollHasMore(bool hasMore) {
    _infiniteScrollController.setHasMore(hasMore);
  }

  /// Reset scroll state
  void resetScroll() {
    _infiniteScrollController.reset();
  }
}