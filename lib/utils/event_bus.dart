import 'dart:async';

/// A lightweight global event bus to publish/subscribe to simple app-wide events.
///
/// This avoids adding an external dependency while allowing loose coupling between
/// widgets that need to react to collection changes in real-time.
class EventBus {
  EventBus._internal();
  static final EventBus _instance = EventBus._internal();

  factory EventBus() => _instance;

  final _controller = StreamController<Object>.broadcast();

  /// Publish an event.
  void emit(Object event) => _controller.add(event);

  /// Global stream of all events.
  Stream<Object> get stream => _controller.stream;
}

/// Fired when a collection has been modified (e.g. a track added/removed).
class CollectionUpdatedEvent {
  CollectionUpdatedEvent(this.collectionId);
  final String collectionId;
} 