import 'package:flutter/material.dart';
import '../screens/profile/profile_screen.dart';
import '../screens/profile/settings_screen.dart';
import '../screens/friends/friends_screen.dart';
import '../screens/map/map_screen.dart';
import '../screens/gamification/challenges_screen.dart';
import '../screens/explore/explore_screen.dart';
import 'app_tab.dart';

/// A utility class that helps with complex navigation patterns
/// between bottom navigation tabs while preserving state
class NavigationHelper {
  // Static variable to track the current tab explicitly
  static AppTab _currentTab = AppTab.map;

  /// Get the explicitly tracked current tab
  static AppTab get currentTab => _currentTab;

  /// Set the current tab explicitly
  static void setCurrentTab(AppTab tab) {
    _currentTab = tab;
    debugPrint('NavigationHelper: Explicitly set current tab to ${tab.toString().split('.').last}');
  }
  /// Navigate to a specific screen by route name
  /// This is a more flexible approach than navigateToTab
  /// Returns a Future that completes when navigation completes (user returns)
  static Future<void> navigateToScreen(
    BuildContext context,
    String routeName,
    {bool maintainState = true}
  ) async {
    final navigator = Navigator.of(context);
    
    // Check if we're already on the target screen
    if (_isCurrentRoute(navigator, routeName)) return;
    
    // Handle special screens with custom transitions
    if (routeName == '/profile') {
      setCurrentTab(AppTab.profile);
      await navigator.push(
        PageRouteBuilder(
          transitionDuration: const Duration(milliseconds: 150),
          reverseTransitionDuration: const Duration(milliseconds: 150),
          pageBuilder: (context, animation, secondaryAnimation) =>
            const ProfileScreen(showBottomNav: true),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          maintainState: maintainState,
          settings: RouteSettings(name: routeName),
          fullscreenDialog: false,
          opaque: true,
        ),
      );
      return;
    }
    else if (routeName == '/settings') {
      await navigator.push(
        PageRouteBuilder(
          transitionDuration: const Duration(milliseconds: 150),
          reverseTransitionDuration: const Duration(milliseconds: 150),
          pageBuilder: (context, animation, secondaryAnimation) => 
            const SettingsScreen(showBottomNav: true),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          maintainState: maintainState,
          settings: RouteSettings(name: routeName),
          fullscreenDialog: false,
          opaque: true,
        ),
      );
      return;
    }
    else if (routeName == '/explore') {
      setCurrentTab(AppTab.explore);
      await navigator.push(
        PageRouteBuilder(
          transitionDuration: const Duration(milliseconds: 150),
          reverseTransitionDuration: const Duration(milliseconds: 150),
          pageBuilder: (context, animation, secondaryAnimation) =>
            const ExploreScreen(showBottomNav: true),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          maintainState: maintainState,
          settings: RouteSettings(name: routeName),
          fullscreenDialog: false,
          opaque: true,
        ),
      );
      return;
    }
    else if (routeName == '/friends') {
      setCurrentTab(AppTab.friends);
      await navigator.push(
        PageRouteBuilder(
          transitionDuration: const Duration(milliseconds: 150),
          reverseTransitionDuration: const Duration(milliseconds: 150),
          pageBuilder: (context, animation, secondaryAnimation) =>
            const FriendsScreen(showBottomNav: true),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          maintainState: maintainState,
          settings: RouteSettings(name: routeName),
          fullscreenDialog: false,
          opaque: true,
        ),
      );
      return;
    }
    else if (routeName == '/challenges') {
      setCurrentTab(AppTab.challenges);
      await navigator.push(
        PageRouteBuilder(
          transitionDuration: const Duration(milliseconds: 150),
          reverseTransitionDuration: const Duration(milliseconds: 150),
          pageBuilder: (context, animation, secondaryAnimation) =>
            const ChallengesScreen(showBottomNav: true),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          maintainState: maintainState,
          settings: RouteSettings(name: routeName),
          fullscreenDialog: false,
          opaque: true,
        ),
      );
      return;
    }
    else if (routeName == '/map') {
      // Set the current tab explicitly before navigation
      setCurrentTab(AppTab.map);
      // Just go back to the map (root)
      _popToMapOrRoot(navigator);
      return;
    }
    else {
      // For other routes, use standard navigation
      await navigator.pushNamed(routeName);
      return;
    }
  }

  /// Navigate to a tab via the bottom navigation bar
  /// This ensures consistent navigation regardless of current path
  /// Returns a Future that completes when navigation completes (user returns)
  static Future<void> navigateToTab(
    BuildContext context, 
    int tabIndex, 
    {bool maintainState = true}
  ) async {
    // Get the tab from the index
    final tab = _getTabFromIndex(tabIndex);
    final navigator = Navigator.of(context);
    
    // Handle based on tab
    switch (tab) {
      case AppTab.profile: // Profile
        // Check if we're already on the profile screen
        if (_isCurrentRoute(navigator, '/profile')) return;

        // Set the current tab explicitly before navigation
        setCurrentTab(AppTab.profile);

        // Otherwise go to profile screen with proper animation
        await navigator.push(
          PageRouteBuilder(
            transitionDuration: const Duration(milliseconds: 150),
            reverseTransitionDuration: const Duration(milliseconds: 150),
            pageBuilder: (context, animation, secondaryAnimation) =>
              const ProfileScreen(showBottomNav: true),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            maintainState: maintainState,
            settings: const RouteSettings(name: '/profile'),
            fullscreenDialog: false,
            opaque: true,
          ),
        );
        return;

      case AppTab.explore: // Explore
        // Check if we're already on the explore screen
        if (_isCurrentRoute(navigator, '/explore')) return;

        // Set the current tab explicitly before navigation
        setCurrentTab(AppTab.explore);

        // Otherwise navigate to explore with proper animation
        await navigator.push(
          PageRouteBuilder(
            transitionDuration: const Duration(milliseconds: 150),
            reverseTransitionDuration: const Duration(milliseconds: 150),
            pageBuilder: (context, animation, secondaryAnimation) =>
              const ExploreScreen(showBottomNav: true),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            maintainState: maintainState,
            settings: const RouteSettings(name: '/explore'),
            fullscreenDialog: false,
            opaque: true,
          ),
        );
        return;

      case AppTab.map: // Map (center button)
        // Set the current tab explicitly before navigation
        setCurrentTab(AppTab.map);
        // Just return to map screen (root) and complete immediately
        _popToMapOrRoot(navigator);
        return;

      case AppTab.challenges: // Challenges
        // Check if we're already on the challenges screen
        if (_isCurrentRoute(navigator, '/challenges')) return;

        // Set the current tab explicitly before navigation
        setCurrentTab(AppTab.challenges);

        // Otherwise navigate to challenges with proper animation
        await navigator.push(
          PageRouteBuilder(
            transitionDuration: const Duration(milliseconds: 150),
            reverseTransitionDuration: const Duration(milliseconds: 150),
            pageBuilder: (context, animation, secondaryAnimation) =>
              const ChallengesScreen(showBottomNav: true),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            maintainState: maintainState,
            settings: const RouteSettings(name: '/challenges'),
            fullscreenDialog: false,
            opaque: true,
          ),
        );
        return;
        
      case AppTab.friends: // Friends
        // Check if we're already on the friends screen
        if (_isCurrentRoute(navigator, '/friends')) return;

        // Set the current tab explicitly before navigation
        setCurrentTab(AppTab.friends);

        // Otherwise navigate to friends with proper animation
        await navigator.push(
          PageRouteBuilder(
            transitionDuration: const Duration(milliseconds: 150),
            reverseTransitionDuration: const Duration(milliseconds: 150),
            pageBuilder: (context, animation, secondaryAnimation) =>
              const FriendsScreen(showBottomNav: true),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            maintainState: maintainState,
            settings: const RouteSettings(name: '/friends'),
            fullscreenDialog: false,
            opaque: true,
          ),
        );
        return;
    }
  }
  
  /// Utility method to get tab from index, with safe fallback to map tab
  static AppTab _getTabFromIndex(int index) {
    if (index >= 0 && index < AppTab.values.length) {
      return AppTab.values[index];
    }
    return AppTab.map; // Default to map if invalid index
  }
  
  /// Pop to the map screen or root if map not found
  static void _popToMapOrRoot(NavigatorState navigator) {
    // Set the current tab to map since we're navigating there
    setCurrentTab(AppTab.map);

    // Pop all routes until we find the map or hit the root
    navigator.popUntil((route) {
      // If this is the map route, stop here
      if (route.settings.name == '/map') {
        return true;
      }

      // If this is the root route, check if it's the map
      if (route.isFirst) {
        // If the first route is not the map, replace it with map
        if (route.settings.name != '/map') {
          navigator.pushReplacement(
            MaterialPageRoute(
              builder: (context) => const MapScreen(),
              settings: const RouteSettings(name: '/map')
            ),
          );
        }
        return true;
      }

      // Keep popping
      return false;
    });
  }
  
  /// Check if the current/top route matches the given name
  static bool _isCurrentRoute(NavigatorState navigator, String routeName) {
    bool isCurrentRoute = false;

    // The first route in popUntil is the current route
    navigator.popUntil((route) {
      isCurrentRoute = route.settings.name == routeName;
      debugPrint('NavigationHelper: Checking route - name: ${route.settings.name}, looking for: $routeName, match: $isCurrentRoute');
      return true; // Don't actually pop anything
    });

    return isCurrentRoute;
  }

  /// More robust method to check if we're currently on the map screen
  /// This handles edge cases where route names might not be properly set
  static bool _isOnMapScreen(NavigatorState navigator) {
    bool isOnMap = false;
    String? currentRouteName;
    bool isFirstRoute = false;

    navigator.popUntil((route) {
      currentRouteName = route.settings.name;
      isFirstRoute = route.isFirst;

      // Check if route name is explicitly '/map'
      if (route.settings.name == '/map') {
        isOnMap = true;
        return true;
      }

      // Only consider it a map screen if it's the first/root route AND not explicitly another screen
      if (route.isFirst) {
        final routeName = route.settings.name;
        // Be more conservative - only assume map if route name is null or explicitly '/map'
        isOnMap = routeName == null || routeName == '/map';
        return true;
      }

      return true; // Don't actually pop anything
    });

    debugPrint('NavigationHelper: _isOnMapScreen - route: $currentRouteName, isFirst: $isFirstRoute, isOnMap: $isOnMap');
    return isOnMap;
  }

  /// Returns the appropriate tab index for the current route
  /// This helps keep the bottom navigation in sync with the actual screen
  static AppTab getCurrentTab(BuildContext context) {
    final navigator = Navigator.of(context);

    debugPrint('NavigationHelper: Getting current tab...');

    // Check current route with explicit checks first
    if (_isCurrentRoute(navigator, '/profile')) {
      debugPrint('NavigationHelper: Current tab is PROFILE');
      _currentTab = AppTab.profile; // Update tracked tab
      return AppTab.profile;
    } else if (_isCurrentRoute(navigator, '/explore')) {
      debugPrint('NavigationHelper: Current tab is EXPLORE');
      _currentTab = AppTab.explore; // Update tracked tab
      return AppTab.explore;
    } else if (_isCurrentRoute(navigator, '/friends')) {
      debugPrint('NavigationHelper: Current tab is FRIENDS');
      _currentTab = AppTab.friends; // Update tracked tab
      return AppTab.friends;
    } else if (_isCurrentRoute(navigator, '/challenges')) {
      debugPrint('NavigationHelper: Current tab is CHALLENGES');
      _currentTab = AppTab.challenges; // Update tracked tab
      return AppTab.challenges;
    } else if (_isCurrentRoute(navigator, '/map')) {
      debugPrint('NavigationHelper: Current tab is MAP (explicit)');
      _currentTab = AppTab.map; // Update tracked tab
      return AppTab.map;
    } else {
      // More careful fallback - check if we're actually on map screen
      if (_isOnMapScreen(navigator)) {
        debugPrint('NavigationHelper: Current tab is MAP (fallback detection)');
        _currentTab = AppTab.map; // Update tracked tab
        return AppTab.map;
      } else {
        // If we can't determine the route, use the explicitly tracked tab
        String? currentRouteName;
        navigator.popUntil((route) {
          currentRouteName = route.settings.name;
          return true;
        });
        debugPrint('NavigationHelper: Unknown route "$currentRouteName", using tracked tab: ${_currentTab.toString().split('.').last}');
        return _currentTab;
      }
    }
  }

  /// Navigate to a named route with optional arguments
  static void navigateTo({
    required BuildContext context,
    required String routeName,
    Map<String, dynamic>? arguments,
  }) {
    Navigator.of(context).pushNamed(
      routeName,
      arguments: arguments,
    );
  }

  /// Navigate to a specific tab route directly
  static void navigateToTabRoute(BuildContext context, int index) {
    // Get the corresponding routes for our tabs navigation
    final routes = [
      '/profile',
      '/explore',
      '/map',
      '/challenges',
      '/friends',
    ];
    
    if (index >= 0 && index < routes.length) {
      Navigator.of(context).pushNamedAndRemoveUntil(
        routes[index],
        (route) => false,
      );
    }
  }

  /// Push a replacement route
  static void replaceWith({
    required BuildContext context,
    required String routeName,
    Map<String, dynamic>? arguments,
  }) {
    Navigator.of(context).pushReplacementNamed(
      routeName,
      arguments: arguments,
    );
  }

  /// Pop to the root and then navigate to a specific route
  static void popToRootAndNavigateTo({
    required BuildContext context,
    required String routeName,
    Map<String, dynamic>? arguments,
  }) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }
} 