import 'package:flutter_open_chinese_convert/flutter_open_chinese_convert.dart';

/// Utility class for normalizing Chinese characters for better matching
class ChineseNormalizer {
  /// Normalize Chinese text by converting to Simplified Chinese for consistent comparison
  static Future<String> normalizeChineseText(String text) async {
    if (text.isEmpty) return text;
    
    try {
      // Convert Traditional to Simplified Chinese for consistent comparison
      final normalized = await ChineseConverter.convert(text, T2S());
      return normalized.toLowerCase().trim();
    } catch (e) {
      // If conversion fails, return original text
      return text.toLowerCase().trim();
    }
  }
  
  /// Check if text contains Chinese characters
  static bool containsChinese(String text) {
    if (text.isEmpty) return false;
    
    // Check for Chinese character ranges
    final chinesePattern = RegExp(r'[\u4e00-\u9fff\u3400-\u4dbf\u20000-\u2a6df\u2a700-\u2b73f\u2b740-\u2b81f\u2b820-\u2ceaf\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u3300-\u33ff]');
    return chinesePattern.hasMatch(text);
  }
  
  /// Compare two Chinese texts with normalization
  static Future<bool> compareChineseTexts(String text1, String text2) async {
    if (text1.isEmpty || text2.isEmpty) return text1 == text2;
    
    // If either text contains Chinese, normalize both
    if (containsChinese(text1) || containsChinese(text2)) {
      final normalized1 = await normalizeChineseText(text1);
      final normalized2 = await normalizeChineseText(text2);
      return normalized1 == normalized2;
    }
    
    // For non-Chinese text, do simple comparison
    return text1.toLowerCase().trim() == text2.toLowerCase().trim();
  }
} 