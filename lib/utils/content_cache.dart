import 'dart:collection';

/// Generic content cache with TTL and size limits
class ContentCache<T> {
  final int _maxSize;
  final Duration _ttl;
  final LinkedHashMap<String, _CacheEntry<T>> _cache = LinkedHashMap();
  final Map<String, DateTime> _accessTimes = {};

  ContentCache({
    int maxSize = 100,
    Duration ttl = const Duration(minutes: 30),
  }) : _maxSize = maxSize, _ttl = ttl;

  /// Get item from cache
  T? get(String key) {
    final entry = _cache[key];
    if (entry == null) return null;

    // Check if expired
    if (DateTime.now().difference(entry.timestamp) > _ttl) {
      _cache.remove(key);
      _accessTimes.remove(key);
      return null;
    }

    // Update access time for LRU
    _accessTimes[key] = DateTime.now();
    
    // Move to end (most recently used)
    _cache.remove(key);
    _cache[key] = entry;

    return entry.value;
  }

  /// Put item in cache
  void put(String key, T value) {
    // Remove if already exists
    _cache.remove(key);
    _accessTimes.remove(key);

    // Add new entry
    _cache[key] = _CacheEntry(value, DateTime.now());
    _accessTimes[key] = DateTime.now();

    // Evict if over size limit
    _evictIfNeeded();
  }

  /// Check if key exists and is not expired
  bool containsKey(String key) {
    final entry = _cache[key];
    if (entry == null) return false;

    // Check if expired
    if (DateTime.now().difference(entry.timestamp) > _ttl) {
      _cache.remove(key);
      _accessTimes.remove(key);
      return false;
    }

    return true;
  }

  /// Remove item from cache
  void remove(String key) {
    _cache.remove(key);
    _accessTimes.remove(key);
  }

  /// Clear all cache
  void clear() {
    _cache.clear();
    _accessTimes.clear();
  }

  /// Get cache statistics
  Map<String, dynamic> getStats() {
    final now = DateTime.now();
    int expiredCount = 0;
    
    for (final entry in _cache.values) {
      if (now.difference(entry.timestamp) > _ttl) {
        expiredCount++;
      }
    }

    return {
      'size': _cache.length,
      'maxSize': _maxSize,
      'expiredCount': expiredCount,
      'hitRate': _calculateHitRate(),
      'oldestEntry': _cache.isNotEmpty 
          ? _cache.values.first.timestamp.toIso8601String()
          : null,
      'newestEntry': _cache.isNotEmpty 
          ? _cache.values.last.timestamp.toIso8601String()
          : null,
    };
  }

  /// Evict oldest entries if over size limit
  void _evictIfNeeded() {
    while (_cache.length > _maxSize) {
      final oldestKey = _cache.keys.first;
      _cache.remove(oldestKey);
      _accessTimes.remove(oldestKey);
    }
  }

  /// Calculate hit rate (simplified)
  double _calculateHitRate() {
    // This is a simplified calculation
    // In a real implementation, you'd track hits and misses
    return _cache.isNotEmpty ? 0.8 : 0.0;
  }

  /// Clean up expired entries
  void cleanupExpired() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cache.entries) {
      if (now.difference(entry.value.timestamp) > _ttl) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
      _accessTimes.remove(key);
    }
  }

  /// Get cache size
  int get size => _cache.length;

  /// Check if cache is empty
  bool get isEmpty => _cache.isEmpty;

  /// Check if cache is at max capacity
  bool get isFull => _cache.length >= _maxSize;
}

/// Cache entry with timestamp
class _CacheEntry<T> {
  final T value;
  final DateTime timestamp;

  _CacheEntry(this.value, this.timestamp);
}

/// Specialized cache for music content
class MusicContentCache {
  static final ContentCache<List<Map<String, dynamic>>> _artistCache = 
      ContentCache(maxSize: 50, ttl: const Duration(hours: 1));
  
  static final ContentCache<List<Map<String, dynamic>>> _songCache = 
      ContentCache(maxSize: 100, ttl: const Duration(minutes: 30));
  
  static final ContentCache<List<String>> _genreCache = 
      ContentCache(maxSize: 20, ttl: const Duration(hours: 2));

  /// Artist cache methods
  static List<Map<String, dynamic>>? getArtists(String key) => _artistCache.get(key);
  static void putArtists(String key, List<Map<String, dynamic>> artists) => _artistCache.put(key, artists);
  static bool hasArtists(String key) => _artistCache.containsKey(key);

  /// Song cache methods
  static List<Map<String, dynamic>>? getSongs(String key) => _songCache.get(key);
  static void putSongs(String key, List<Map<String, dynamic>> songs) => _songCache.put(key, songs);
  static bool hasSongs(String key) => _songCache.containsKey(key);

  /// Genre cache methods
  static List<String>? getGenres(String key) => _genreCache.get(key);
  static void putGenres(String key, List<String> genres) => _genreCache.put(key, genres);
  static bool hasGenres(String key) => _genreCache.containsKey(key);

  /// Clear all caches
  static void clearAll() {
    _artistCache.clear();
    _songCache.clear();
    _genreCache.clear();
  }

  /// Get combined cache statistics
  static Map<String, dynamic> getStats() {
    return {
      'artists': _artistCache.getStats(),
      'songs': _songCache.getStats(),
      'genres': _genreCache.getStats(),
      'totalSize': _artistCache.size + _songCache.size + _genreCache.size,
    };
  }

  /// Cleanup expired entries in all caches
  static void cleanupExpired() {
    _artistCache.cleanupExpired();
    _songCache.cleanupExpired();
    _genreCache.cleanupExpired();
  }
}