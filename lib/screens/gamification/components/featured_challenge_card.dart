import 'package:flutter/material.dart';
import '../../../models/achievement.dart';

class FeaturedChallengeCard extends StatelessWidget {
  final Achievement achievement;
  final VoidCallback onTap;

  const FeaturedChallengeCard({
    Key? key,
    required this.achievement,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // Determine category color
    Color categoryColor = Colors.blue;
    if (achievement.category.toLowerCase().contains('artist')) {
      categoryColor = Colors.purple;
    } else if (achievement.category.toLowerCase().contains('genre')) {
      categoryColor = Colors.blue;
    } else if (achievement.category.toLowerCase().contains('location')) {
      categoryColor = Colors.green;
    } else if (achievement.category.toLowerCase().contains('social')) {
      categoryColor = Colors.red;
    }
    
    return Container(
      height: 180,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            categoryColor.withOpacity(0.15),
            categoryColor.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: categoryColor.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(24),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(24),
          child: Stack(
            children: [
              // Background pattern (subtle dots)
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24),
                  child: ShaderMask(
                    shaderCallback: (rect) {
                      return LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.white,
                          Colors.white.withOpacity(0.1),
                        ],
                      ).createShader(rect);
                    },
                    blendMode: BlendMode.dstIn,
                    child: ColoredBox(
                      color: Colors.white.withOpacity(0.05),
                      // Instead of depending on an external asset, we'll use a simpler solution
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            colors: [
                              categoryColor.withOpacity(0.05),
                              Colors.transparent,
                            ],
                            center: Alignment.topLeft,
                            radius: 1.0,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              
              // Content
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with featured badge
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Title - Wrap in Flexible to prevent overflow
                        Flexible(
                          child: Row(
                            mainAxisSize: MainAxisSize.min, // Make this row only as wide as needed
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.8),
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: categoryColor.withOpacity(0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  achievement.iconData,
                                  color: categoryColor,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 8), // Reduced width
                              Flexible(
                                child: Text(
                                  'Featured Challenge',
                                  style: TextStyle(
                                    fontSize: 16, // Reduced font size
                                    fontWeight: FontWeight.bold,
                                    color: colorScheme.onSurface,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(width: 4), // Add space between the two elements
                        
                        // Featured badge
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8, // Reduced padding
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.amber.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.amber.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.star_rounded,
                                color: Colors.amber[700],
                                size: 14, // Reduced size
                              ),
                              const SizedBox(width: 2), // Reduced spacing
                              Text(
                                'FEATURED',
                                style: TextStyle(
                                  fontSize: 9, // Reduced size
                                  fontWeight: FontWeight.bold,
                                  color: Colors.amber[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Challenge name
                    Text(
                      achievement.name,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                        fontSize: 16,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Challenge description
                    Text(
                      achievement.description,
                      style: TextStyle(
                        color: colorScheme.onSurface.withOpacity(0.6),
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const Spacer(),
                    
                    // Progress indicator
                    Row(
                      children: [
                        // XP reward
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.blue.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.bolt_rounded,
                                color: Colors.blue[700],
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '+${achievement.xpReward} XP',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(width: 12),
                        
                        // Progress percentage
                        Text(
                          '${achievement.progressPercentage.toInt()}%',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: categoryColor,
                            fontSize: 14,
                          ),
                        ),
                        
                        const SizedBox(width: 12),
                        
                        // Progress bar
                        Expanded(
                          child: Stack(
                            children: [
                              // Background
                              Container(
                                height: 6,
                                decoration: BoxDecoration(
                                  color: categoryColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(3),
                                ),
                              ),
                              // Progress
                              FractionallySizedBox(
                                widthFactor: achievement.progressPercentage / 100,
                                child: Container(
                                  height: 6,
                                  decoration: BoxDecoration(
                                    color: categoryColor,
                                    borderRadius: BorderRadius.circular(3),
                                    boxShadow: [
                                      BoxShadow(
                                        color: categoryColor.withOpacity(0.3),
                                        blurRadius: 4,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 