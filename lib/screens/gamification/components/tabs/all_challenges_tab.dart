import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../providers/gamification_provider.dart';
import '../../../../models/achievement.dart';
import '../../../../screens/gamification/category_detail_screen.dart';
import '../challenge_category_card.dart';
import '../featured_challenge_card.dart';
import '../challenge_progress_section.dart';
import '../stats_card.dart';
import './all_challenges_skeleton.dart';
import '../../../../widgets/gamification/components/achievement_details_sheet.dart';
import '../../../../widgets/gamification/components/user_rank_badge.dart';
import '../../../../widgets/gamification/components/categories/challenge_categories_section.dart';

class AllChallengesTab extends StatefulWidget {
  final GamificationProvider gamificationProvider;

  const AllChallengesTab({
    Key? key,
    required this.gamificationProvider,
  }) : super(key: key);

  @override
  State<AllChallengesTab> createState() => _AllChallengesTabState();
}

class _AllChallengesTabState extends State<AllChallengesTab> {
  final ScrollController _scrollController = ScrollController();
  
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final achievements = widget.gamificationProvider.achievements;
    final isLoading = widget.gamificationProvider.isLoading;
    final hasError = widget.gamificationProvider.error != null;
    
    return RefreshIndicator(
      onRefresh: () => widget.gamificationProvider.forceReload(),
      child: CustomScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          // Show skeleton loading for everything when loading
          if (isLoading)
            SliverToBoxAdapter(
              child: AllChallengesTabSkeleton(),
            )
          else ...[
            // User Rank Badge - only show when NOT loading
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: UserRankBadge(
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) => _buildRankDetailsModal(context),
                    );
                  },
                ),
              ),
            ),
            
            // Challenge Categories Section
            if (hasError)
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 32.0, horizontal: 24.0),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load challenges',
                          style: theme.textTheme.titleLarge,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Pull down to try again',
                          style: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              )
            else if (achievements.isEmpty)
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 32.0, horizontal: 24.0),
                  child: Center(
                    child: Column(
                      children: [
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.emoji_events_outlined,
                            size: 60,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                        const SizedBox(height: 24),
                        Text(
                          'No Challenges Available Yet',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Start using the app more to unlock challenges and earn rewards!',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )
            else
              SliverToBoxAdapter(
                child: ChallengeCategoriesSection(
                  gamificationProvider: widget.gamificationProvider,
                  onAchievementSelected: _showAchievementDetails,
                ),
              ),
            
            // Featured Challenge - only show if we have achievements
            if (!hasError && achievements.isNotEmpty)
              _buildFeaturedChallengeSection(),
            
            // New Challenges Section - only show if we have achievements
            if (!hasError && achievements.isNotEmpty)
              _buildNewChallengesSection(),
            
            // Bottom padding
            const SliverToBoxAdapter(
              child: SizedBox(height: 24),
            ),
          ],
        ],
      ),
    );
  }
  
  // Add the rank details modal
  Widget _buildRankDetailsModal(BuildContext context) {
    return Consumer<GamificationProvider>(
      builder: (context, provider, child) {
        final currentRank = provider.currentRank;
        final nextRank = provider.nextRank;
        final theme = Theme.of(context);
        
        return Container(
          margin: EdgeInsets.only(
            top: MediaQuery.of(context).padding.top + 20,
          ),
          decoration: BoxDecoration(
            color: theme.scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Title
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Your Rank',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              
              // Current rank badge
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: UserRankBadge(
                  size: 160,
                  showDetails: false,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Rank description
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Badge Details',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: currentRank.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      currentRank.badgeDescription,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Visual Effects',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: currentRank.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      currentRank.visualDescription,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Next rank preview
              if (nextRank != null)
                Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: nextRank.backgroundColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: nextRank.primaryColor.withOpacity(0.2),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: nextRank.backgroundColor.withOpacity(0.5),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              nextRank.emoji,
                              style: const TextStyle(fontSize: 30),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Next Rank: ${nextRank.name}',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: nextRank.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Reach level ${nextRank.requiredLevel} to unlock',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              
              const SizedBox(height: 24),
            ],
          ),
        );
      },
    );
  }
  
  // Featured Challenge Section
  Widget _buildFeaturedChallengeSection() {
    try {
      final featuredChallenge = widget.gamificationProvider.getFeaturedChallenge();
      
      return SliverToBoxAdapter(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
          child: FeaturedChallengeCard(
            achievement: featuredChallenge,
            onTap: () => _showAchievementDetails(featuredChallenge),
          ),
        ),
      );
    } catch (e) {
      debugPrint('Error showing featured challenge: $e');
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }
  }
  
  // New Challenges Section
  Widget _buildNewChallengesSection() {
    try {
      // Get the newest challenges (most recently added)
      final achievements = widget.gamificationProvider.achievements;
      if (achievements.isEmpty) {
        return const SliverToBoxAdapter(child: SizedBox.shrink());
      }
      
      // Create a safe copy and sort it by createdAt date
      final newestChallenges = List<Achievement>.from(achievements);
      newestChallenges.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      final recentChallenges = newestChallenges.take(3).toList();
      if (recentChallenges.isEmpty) {
        return const SliverToBoxAdapter(child: SizedBox.shrink());
      }
      
      return SliverToBoxAdapter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
              child: _buildSectionHeader(
                'Recently Added',
                Icons.new_releases_rounded,
                Colors.orange,
              ),
            ),
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              shrinkWrap: true,
              itemCount: recentChallenges.length,
              itemBuilder: (context, index) {
                final achievement = recentChallenges[index];
                return _buildRecentChallengeItem(achievement, index);
              },
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('Error building new challenges section: $e');
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }
  }
  
  // Recent Challenge Item
  Widget _buildRecentChallengeItem(Achievement achievement, int index) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: () => _showAchievementDetails(achievement),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Achievement Icon
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getCategoryColor(achievement).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    achievement.iconData,
                    color: _getCategoryColor(achievement),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                
                // Achievement Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 3,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.orange.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: Colors.orange.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.new_releases_outlined,
                                  color: Colors.orange[700],
                                  size: 10,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'NEW',
                                  style: TextStyle(
                                    fontSize: 9,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              achievement.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Text(
                        achievement.description,
                        style: TextStyle(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                          fontSize: 12,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      // Progress bar
                      Stack(
                        children: [
                          // Background
                          Container(
                            height: 4,
                            decoration: BoxDecoration(
                              color: _getCategoryColor(achievement).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          // Progress
                          FractionallySizedBox(
                            widthFactor: achievement.progressPercentage / 100,
                            child: Container(
                              height: 4,
                              decoration: BoxDecoration(
                                color: _getCategoryColor(achievement),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      // Progress text
                      Text(
                        '${achievement.progressPercentage.toInt()}% complete',
                        style: TextStyle(
                          color: theme.colorScheme.onSurface.withOpacity(0.5),
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  // Section Header
  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withOpacity(0.2),
            color.withOpacity(0.05),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.arrow_forward_ios_rounded,
              color: color,
              size: 10,
            ),
          ),
        ],
      ),
    );
  }
  
  // Helper: Get color based on achievement category
  Color _getCategoryColor(Achievement achievement) {
    if (achievement.category.toLowerCase().contains('artist')) {
      return Colors.purple;
    } else if (achievement.category.toLowerCase().contains('genre')) {
      return Colors.blue;
    } else if (achievement.category.toLowerCase().contains('location')) {
      return Colors.green;
    } else if (achievement.category.toLowerCase().contains('social')) {
      return Colors.red;
    }
    return Colors.orange;
  }
  
  // Show achievement details
  void _showAchievementDetails(Achievement achievement) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AchievementDetailsSheet(
        achievement: achievement,
      ),
    );
  }
} 