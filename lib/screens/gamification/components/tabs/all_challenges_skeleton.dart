import 'package:flutter/material.dart';

/// Perfect skeleton loading for AllChallengesTab
/// Mimics the exact structure with smooth shimmer animations
class AllChallengesTabSkeleton extends StatefulWidget {
  const AllChallengesTabSkeleton({Key? key}) : super(key: key);

  @override
  State<AllChallengesTabSkeleton> createState() => _AllChallengesTabSkeletonState();
}

class _AllChallengesTabSkeletonState extends State<AllChallengesTabSkeleton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    // Start the shimmer animation and repeat
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Skeleton User Rank Badge
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildSkeletonRankBadge(theme),
          ),
          
          // Skeleton Challenge Categories Section
          _buildSkeletonCategoriesSection(theme),
          
          // Skeleton Featured Challenge
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
            child: _buildSkeletonFeaturedChallenge(theme),
          ),
          
          // Skeleton Recently Added Section
          _buildSkeletonRecentlyAddedSection(theme),
          
          // Bottom padding
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildSkeletonRankBadge(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(0.1),
            theme.colorScheme.primary.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Skeleton avatar
          _buildShimmerContainer(
            width: 60,
            height: 60,
            borderRadius: 30,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
          
          const SizedBox(width: 16),
          
          // Skeleton rank info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerContainer(
                  width: double.infinity,
                  height: 20,
                  borderRadius: 10,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
                const SizedBox(height: 8),
                _buildShimmerContainer(
                  width: MediaQuery.of(context).size.width * 0.6,
                  height: 14,
                  borderRadius: 7,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
                const SizedBox(height: 12),
                // Progress bar skeleton
                _buildShimmerContainer(
                  width: double.infinity,
                  height: 6,
                  borderRadius: 3,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Skeleton level badge
          _buildShimmerContainer(
            width: 50,
            height: 30,
            borderRadius: 15,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonCategoriesSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header skeleton
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 4, 16, 12),
          child: _buildSkeletonSectionHeader(theme, Colors.indigo),
        ),
        
        // Horizontal category cards skeleton
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 4, // Artist, Genre, Location, Social
            itemBuilder: (context, index) => _buildSkeletonCategoryCard(theme, index),
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildSkeletonSectionHeader(ThemeData theme, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withOpacity(0.2),
            color.withOpacity(0.05),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Icon container skeleton
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              Icons.category_rounded,
              color: color.withOpacity(0.7),
              size: 16,
            ),
          ),
          const SizedBox(width: 8),
          // Title skeleton
          _buildShimmerContainer(
            width: 120,
            height: 15,
            borderRadius: 7.5,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
          const Spacer(),
          // Arrow skeleton
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.arrow_forward_ios_rounded,
              color: color.withOpacity(0.7),
              size: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonCategoryCard(ThemeData theme, int index) {
    final colors = [Colors.purple, Colors.blue, Colors.green, Colors.orange];
    final color = colors[index];
    
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withOpacity(0.15),
            color.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Category icon skeleton
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(14),
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                [Icons.mic, Icons.music_note, Icons.location_on, Icons.people][index],
                color: color.withOpacity(0.7),
                size: 22,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Category name skeleton
            _buildShimmerContainer(
              width: double.infinity,
              height: 15,
              borderRadius: 7.5,
              baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
            ),
            
            const SizedBox(height: 4),
            
            // Count skeleton
            _buildShimmerContainer(
              width: 80,
              height: 11,
              borderRadius: 5.5,
              baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
            ),
            
            const Spacer(),
            
            // Completion status skeleton
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildShimmerContainer(
                  width: 30,
                  height: 11,
                  borderRadius: 5.5,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
                _buildShimmerContainer(
                  width: 25,
                  height: 11,
                  borderRadius: 5.5,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
              ],
            ),
            
            const SizedBox(height: 6),
            
            // Progress bar skeleton
            _buildShimmerContainer(
              width: double.infinity,
              height: 4,
              borderRadius: 2,
              baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkeletonFeaturedChallenge(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(0.1),
            theme.colorScheme.primary.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Featured badge skeleton
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: theme.colorScheme.primary.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.star,
                      color: theme.colorScheme.primary.withOpacity(0.7),
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'FEATURED',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              // Icon skeleton
              Icon(
                Icons.emoji_events,
                color: theme.colorScheme.primary.withOpacity(0.5),
                size: 24,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Title skeleton
          _buildShimmerContainer(
            width: double.infinity,
            height: 20,
            borderRadius: 10,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
          
          const SizedBox(height: 8),
          
          // Description skeleton
          _buildShimmerContainer(
            width: MediaQuery.of(context).size.width * 0.8,
            height: 14,
            borderRadius: 7,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
          
          const SizedBox(height: 6),
          
          _buildShimmerContainer(
            width: MediaQuery.of(context).size.width * 0.6,
            height: 14,
            borderRadius: 7,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
          
          const SizedBox(height: 16),
          
          // Progress section skeleton
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildShimmerContainer(
                      width: 60,
                      height: 12,
                      borderRadius: 6,
                      baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                      highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                    ),
                    const SizedBox(height: 8),
                    _buildShimmerContainer(
                      width: double.infinity,
                      height: 6,
                      borderRadius: 3,
                      baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                      highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              _buildShimmerContainer(
                width: 40,
                height: 30,
                borderRadius: 15,
                baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonRecentlyAddedSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header skeleton
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
          child: _buildSkeletonSectionHeader(theme, Colors.orange),
        ),
        
        // Recent challenge items skeleton
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: List.generate(3, (index) => 
              Column(
                children: [
                  _buildSkeletonRecentChallengeItem(theme),
                  if (index < 2) const SizedBox(height: 12),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSkeletonRecentChallengeItem(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Achievement icon skeleton
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.emoji_events,
                color: Colors.orange.withOpacity(0.7),
                size: 24,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Achievement info skeleton
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // NEW badge skeleton
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: Colors.orange.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.new_releases_outlined,
                              color: Colors.orange[700]?.withOpacity(0.7),
                              size: 10,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'NEW',
                              style: TextStyle(
                                fontSize: 9,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange[700]?.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Title skeleton
                      Expanded(
                        child: _buildShimmerContainer(
                          width: double.infinity,
                          height: 16,
                          borderRadius: 8,
                          baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                          highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  // Description skeleton
                  _buildShimmerContainer(
                    width: double.infinity,
                    height: 12,
                    borderRadius: 6,
                    baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                    highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                  ),
                  const SizedBox(height: 4),
                  _buildShimmerContainer(
                    width: MediaQuery.of(context).size.width * 0.4,
                    height: 12,
                    borderRadius: 6,
                    baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                    highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                  ),
                  const SizedBox(height: 8),
                  // Progress bar skeleton
                  _buildShimmerContainer(
                    width: double.infinity,
                    height: 4,
                    borderRadius: 2,
                    baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                    highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                  ),
                  const SizedBox(height: 4),
                  // Progress text skeleton
                  _buildShimmerContainer(
                    width: 80,
                    height: 10,
                    borderRadius: 5,
                    baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                    highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerContainer({
    required double width,
    required double height,
    required double borderRadius,
    required Color baseColor,
    required Color highlightColor,
  }) {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
            gradient: LinearGradient(
              begin: Alignment(-1.0, -0.3),
              end: Alignment(1.0, 0.3),
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                (_shimmerAnimation.value - 0.3).clamp(0.0, 1.0),
                _shimmerAnimation.value.clamp(0.0, 1.0),
                (_shimmerAnimation.value + 0.3).clamp(0.0, 1.0),
              ],
            ),
          ),
        );
      },
    );
  }
} 