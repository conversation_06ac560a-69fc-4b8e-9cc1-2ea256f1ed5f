import 'package:flutter/material.dart';

class ChallengeProgressSection extends StatelessWidget {
  final int totalCount;
  final int completedCount;
  final int inProgressCount;
  final int lockedCount;

  const ChallengeProgressSection({
    Key? key,
    required this.totalCount,
    required this.completedCount,
    required this.inProgressCount,
    required this.lockedCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.insights_rounded,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Progress Overview',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      'Your journey to completing all challenges',
                      style: TextStyle(
                        fontSize: 12,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Progress Bars
          _buildProgressItem(
            context,
            label: 'Completed',
            count: completedCount,
            total: totalCount,
            color: Colors.green,
            icon: Icons.check_circle_rounded,
          ),
          
          const SizedBox(height: 16),
          
          _buildProgressItem(
            context,
            label: 'In Progress',
            count: inProgressCount,
            total: totalCount,
            color: Colors.orange,
            icon: Icons.pending_rounded,
          ),
          
          const SizedBox(height: 16),
          
          _buildProgressItem(
            context,
            label: 'Locked',
            count: lockedCount,
            total: totalCount,
            color: Colors.grey,
            icon: Icons.lock_rounded,
          ),
          
          const SizedBox(height: 20),
          
          // Total Progress
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Overall Progress',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                  fontSize: 14,
                ),
              ),
              Text(
                '${totalCount > 0 ? ((completedCount / totalCount) * 100).toInt() : 0}%',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Overall progress bar - Fixed implementation
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Container(
              height: 8,
              width: double.infinity, // Provide width constraint
              color: Colors.grey.withOpacity(0.1),
              child: Stack(
                children: [
                  // Calculate fixed widths instead of using FractionallySizedBox inside Row
                  Row(
                    children: [
                      // Completed portion
                      Container(
                        width: totalCount > 0 
                            ? (completedCount / totalCount) * MediaQuery.of(context).size.width - 40
                            : 0,
                        height: 8,
                        color: Colors.green,
                      ),
                      // In progress portion
                      Container(
                        width: totalCount > 0 
                            ? (inProgressCount / totalCount) * MediaQuery.of(context).size.width - 40
                            : 0,
                        height: 8,
                        color: Colors.orange,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildProgressItem(
    BuildContext context, {
    required String label,
    required int count,
    required int total,
    required Color color,
    required IconData icon,
  }) {
    final theme = Theme.of(context);
    final percentage = total > 0 ? (count / total * 100).toInt() : 0;
    
    return Row(
      children: [
        // Icon
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 16,
          ),
        ),
        
        const SizedBox(width: 12),
        
        // Label and count
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.onSurface,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    '$count/$total ($percentage%)',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: color,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Progress bar - use LayoutBuilder to get exact width constraints
              LayoutBuilder(
                builder: (context, constraints) {
                  return Stack(
                    children: [
                      // Background
                      Container(
                        height: 4,
                        width: constraints.maxWidth,
                        decoration: BoxDecoration(
                          color: color.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      // Progress
                      Container(
                        height: 4,
                        width: total > 0 ? (count / total) * constraints.maxWidth : 0,
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ],
                  );
                }
              ),
            ],
          ),
        ),
      ],
    );
  }
} 