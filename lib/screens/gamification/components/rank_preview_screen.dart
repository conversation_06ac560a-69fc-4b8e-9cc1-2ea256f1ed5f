import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../models/user_rank.dart';
import '../../../widgets/gamification/components/user_rank_badge.dart';

class RankPreviewScreen extends StatelessWidget {
  const RankPreviewScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final ranks = UserRank.allRanks;
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Rank Badges Preview'),
        backgroundColor: theme.scaffoldBackgroundColor,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'All Available Ranks',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Preview of all rank badges and their visual effects',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.textTheme.bodySmall?.color,
                ),
              ),
              const SizedBox(height: 24),
              LayoutBuilder(
                builder: (context, constraints) {
                  final crossAxisCount = constraints.maxWidth > 600 ? 3 : 2;
                  final itemWidth = (constraints.maxWidth - (crossAxisCount + 1) * 16) / crossAxisCount;
                  final itemHeight = itemWidth * 0.8;

                  return GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: crossAxisCount,
                      childAspectRatio: itemWidth / itemHeight,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                    ),
                    itemCount: ranks.length,
                    itemBuilder: (context, index) {
                      final rank = ranks[index];
                      return _RankPreviewCard(
                        rank: rank,
                        isSmallScreen: isSmallScreen,
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _RankPreviewCard extends StatelessWidget {
  final UserRank rank;
  final bool isSmallScreen;

  const _RankPreviewCard({
    Key? key,
    required this.rank,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
      decoration: BoxDecoration(
        color: rank.backgroundColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: rank.primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: isSmallScreen ? 60 : 80,
              ),
              child: MockRankBadge(rank: rank),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            rank.name,
            style: (isSmallScreen ? theme.textTheme.bodyMedium : theme.textTheme.titleSmall)?.copyWith(
              color: rank.primaryColor,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
          Text(
            'Level ${rank.requiredLevel}',
            style: (isSmallScreen ? theme.textTheme.bodySmall : theme.textTheme.bodyMedium)?.copyWith(
              color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }
}

/// A modified version of UserRankBadge that doesn't depend on Provider
class MockRankBadge extends StatefulWidget {
  final UserRank rank;

  const MockRankBadge({
    Key? key,
    required this.rank,
  }) : super(key: key);

  @override
  State<MockRankBadge> createState() => _MockRankBadgeState();
}

class _MockRankBadgeState extends State<MockRankBadge>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        if (widget.rank.hasProfileEffect)
          CustomPaint(
            size: const Size.square(80 * 1.2),
            painter: _RankEffectPainter(
              color: widget.rank.primaryColor,
              progress: _animationController.value,
              secondaryColor:
                  widget.rank.id == 'legend' ? Colors.amber : null,
            ),
          ),
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                widget.rank.primaryColor.withOpacity(0.8),
                widget.rank.backgroundColor,
              ],
              stops: const [0.1, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: widget.rank.primaryColor.withOpacity(0.4),
                blurRadius: 15,
                spreadRadius: 1,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 8,
                spreadRadius: -2,
                offset: const Offset(0, 3),
              ),
            ],
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1.0,
            ),
          ),
          child: Center(
            child: Text(
              widget.rank.emoji,
              style: TextStyle(
                fontSize: 40,
                shadows: [
                  Shadow(
                    color: widget.rank.primaryColor.withOpacity(0.7),
                    blurRadius: 10,
                  ),
                  Shadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _RankEffectPainter extends CustomPainter {
  final Color color;
  final double progress;
  final Color? secondaryColor;

  _RankEffectPainter({
    required this.color,
    required this.progress,
    this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Create gradient for special ranks (like Legend)
    final useGradient = secondaryColor != null;

    // Outer glow circles
    for (int i = 0; i < 3; i++) {
      final currentRadius = radius * (0.7 + (i * 0.15));
      final opacity = 0.2 - (i * 0.05);

      final paint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0 - (i * 0.5)
        ..color = color.withOpacity(opacity);

      canvas.drawCircle(center, currentRadius, paint);
    }

    // Animated arcs that rotate around the badge
    for (int i = 0; i < 4; i++) {
      final startAngle = (i * math.pi / 2) + (progress * 2 * math.pi);
      final sweepAngle = math.pi / 4;

      final arcPaint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3
        ..strokeCap = StrokeCap.round;

      if (useGradient) {
        final gradient = SweepGradient(
          center: Alignment.center,
          startAngle: startAngle,
          endAngle: startAngle + sweepAngle,
          colors: [
            color.withOpacity(0.7),
            secondaryColor!.withOpacity(0.7),
          ],
        );

        arcPaint.shader = gradient.createShader(
          Rect.fromCircle(center: center, radius: radius),
        );
      } else {
        arcPaint.color = color.withOpacity(0.5);
      }

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        false,
        arcPaint,
      );
    }

    // Animated particles for higher ranks
    if (secondaryColor != null) {
      final particleCount = 8;
      for (int i = 0; i < particleCount; i++) {
        final angle = (i * 2 * math.pi / particleCount) + (progress * 2 * math.pi);
        final distance = radius * 0.85;
        final x = center.dx + distance * math.cos(angle);
        final y = center.dy + distance * math.sin(angle);

        final pulseFactor = 0.5 + 0.5 * math.sin((progress * 10) + i);
        final particleSize = 2.0 + (pulseFactor * 2.0);

        final pulsePaint = Paint()
          ..color = secondaryColor!.withOpacity(0.5 + (0.5 * pulseFactor))
          ..style = PaintingStyle.fill;

        canvas.drawCircle(
          Offset(x, y),
          particleSize,
          pulsePaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(_RankEffectPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.secondaryColor != secondaryColor;
  }
} 