import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../../providers/gamification_provider.dart';
import '../../models/achievement.dart';
import '../../widgets/gamification/achievement_grid_item.dart';
import '../../widgets/gamification/achievement_card.dart';
import '../../widgets/gamification/components/achievement_details_sheet.dart';

class CategoryDetailScreen extends StatefulWidget {
  final String title;
  final IconData icon;
  final String categoryId;
  final Color color;

  const CategoryDetailScreen({
    Key? key,
    required this.title,
    required this.icon,
    required this.categoryId,
    required this.color,
  }) : super(key: key);

  @override
  State<CategoryDetailScreen> createState() => _CategoryDetailScreenState();
}

class _CategoryDetailScreenState extends State<CategoryDetailScreen> with TickerProviderStateMixin {
  late List<Achievement> filteredAchievements;
  late AnimationController _headerController;
  late AnimationController _fabController;
  String searchQuery = '';
  String filterType = 'All';
  bool _isSearchExpanded = false;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    filteredAchievements = [];
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _fabController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _headerController.forward();
      _fabController.forward();
      _loadAchievements();
    });
  }
  
  @override
  void dispose() {
    _headerController.dispose();
    _fabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showFab = _scrollController.offset > 100;
      if (showFab && _fabController.status != AnimationStatus.completed) {
        _fabController.forward();
      } else if (!showFab && _fabController.status != AnimationStatus.dismissed) {
        _fabController.reverse();
      }
    }
  }

  void _toggleSearch() {
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
      if (!_isSearchExpanded) {
        _searchController.clear();
        _handleSearch('');
      }
    });
  }
  
  void _handleSearch(String query) {
    setState(() {
      searchQuery = query.toLowerCase();
      _applyFilters();
    });
  }
  
  void _handleFilterChange(String? filter) {
    if (filter != null) {
      setState(() {
        filterType = filter;
        _applyFilters();
      });
    }
  }
  
  void _applyFilters() {
    final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
    List<Achievement> allAchievements;
    
    switch (widget.categoryId) {
      case 'artist':
        allAchievements = gamificationProvider.artistAchievements;
        break;
      case 'genre':
        allAchievements = gamificationProvider.genreAchievements;
        break;
      case 'location':
        allAchievements = gamificationProvider.locationAchievements;
        break;
      case 'social':
        allAchievements = gamificationProvider.socialAchievements;
        break;
      default:
        allAchievements = [];
    }

    filteredAchievements = allAchievements.where((achievement) {
      final matchesSearch = searchQuery.isEmpty ||
        achievement.name.toLowerCase().contains(searchQuery) ||
        achievement.description.toLowerCase().contains(searchQuery);
      
      bool matchesCategory = true;
      if (filterType != 'All') {
        if (filterType == 'Completed') {
          matchesCategory = achievement.isCompleted;
        } else if (filterType == 'In Progress') {
          matchesCategory = !achievement.isCompleted && achievement.progressPercentage > 0;
        } else if (filterType == 'Not Started') {
          matchesCategory = !achievement.isCompleted && achievement.progressPercentage == 0;
        }
      }
      
      return matchesSearch && matchesCategory;
    }).toList();
  }

  void _loadAchievements() {
    final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
    if (gamificationProvider.getCategoryProgressSummary(widget.categoryId)['total'] == 0) {
      gamificationProvider.loadCategoryAchievements(widget.categoryId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<GamificationProvider>(
      builder: (context, gamificationProvider, child) {
        // Get current achievements from provider and apply filters
        _applyFilters();
        
        final theme = Theme.of(context);
        final progressSummary = gamificationProvider.getCategoryProgressSummary(widget.categoryId);
        final totalCount = progressSummary['total'] as int;
        final completedCount = progressSummary['completed'] as int;
        final progressPercent = totalCount > 0 ? (completedCount / totalCount) : 0.0;
        final isLoading = gamificationProvider.isCategoryLoading(widget.categoryId);
        
        return Scaffold(
          backgroundColor: theme.scaffoldBackgroundColor,
          body: CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(
              parent: AlwaysScrollableScrollPhysics(),
            ),
            slivers: [
              // Modern App Bar with Hero Animation
              SliverAppBar(
                expandedHeight: 160,
                floating: false,
                pinned: true,
                elevation: 0,
                backgroundColor: widget.color,
                foregroundColor: Colors.white,
                leading: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                    iconSize: 20,
                  ),
                ),
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          widget.color,
                          widget.color.withOpacity(0.8),
                          widget.color.withOpacity(0.9),
                        ],
                      ),
                    ),
                    child: Stack(
                      children: [
                        // Animated background pattern
                        Positioned.fill(
                          child: CustomPaint(
                            painter: _BackgroundPatternPainter(widget.color),
                          ),
                        ),
                        // Content
                        Positioned(
                          left: 24,
                          right: 24,
                          bottom: 12,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              // Left side - Icon and Title
                              Expanded(
                                flex: 2,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Category Icon
                                    Container(
                                      width: 40,
                                      height: 40,
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(12),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withOpacity(0.2),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Icon(
                                        widget.icon,
                                        size: 22,
                                        color: Colors.white,
                                      ),
                                    ),
                                    
                                    const SizedBox(height: 8),
                                    
                                    // Title
                                    Text(
                                      widget.title,
                                      style: const TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                        height: 1.1,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    
                                    const SizedBox(height: 6),
                                    
                                    // Progress Bar
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          '${(progressPercent * 100).toInt()}% Complete',
                                          style: TextStyle(
                                            color: Colors.white.withOpacity(0.9),
                                            fontSize: 11,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        const SizedBox(height: 3),
                                        Container(
                                          height: 3,
                                          width: double.infinity,
                                          decoration: BoxDecoration(
                                            color: Colors.white.withOpacity(0.3),
                                            borderRadius: BorderRadius.circular(2),
                                          ),
                                          child: FractionallySizedBox(
                                            alignment: Alignment.centerLeft,
                                            widthFactor: progressPercent,
                                            child: Container(
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius: BorderRadius.circular(2),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              
                              const SizedBox(width: 12),
                              
                              // Right side - Stats
                              Column(
                                mainAxisAlignment: MainAxisAlignment.end,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      _buildMiniStatCard(completedCount.toString(), 'Done'),
                                      const SizedBox(width: 6),
                                      _buildMiniStatCard((totalCount - completedCount).toString(), 'Left'),
                                    ],
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'of $totalCount total',
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: Colors.white.withOpacity(0.7),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              // Filter Chips
              SliverToBoxAdapter(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    physics: const BouncingScrollPhysics(),
                    child: Row(
                      children: [
                        _buildModernFilterChip('All', Icons.grid_view),
                        const SizedBox(width: 12),
                        _buildModernFilterChip('Completed', Icons.check_circle),
                        const SizedBox(width: 12),
                        _buildModernFilterChip('In Progress', Icons.pending),
                        const SizedBox(width: 12),
                        _buildModernFilterChip('Not Started', Icons.radio_button_unchecked),
                      ],
                    ),
                  ),
                ),
              ),
              
              // Results Header
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                  child: Row(
                    children: [
                      Text(
                        '${filteredAchievements.length} Challenges',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      if (filteredAchievements.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: widget.color.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: widget.color.withOpacity(0.2)),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.filter_list,
                                size: 16,
                                color: widget.color,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                filterType,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: widget.color,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              
              // Achievements Grid or Empty State
              filteredAchievements.isEmpty
                  ? SliverFillRemaining(child: _buildModernEmptyState())
                  : SliverPadding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      sliver: SliverGrid(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: _getCrossAxisCount(context),
                          childAspectRatio: 0.75,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                        ),
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final achievement = filteredAchievements[index];
                            return _buildModernAchievementCard(achievement, index);
                          },
                          childCount: filteredAchievements.length,
                        ),
                      ),
                    ),
              
              // Bottom padding
              const SliverToBoxAdapter(
                child: SizedBox(height: 100),
              ),
            ],
          ),
          
          // Floating Action Button - Quick Stats
          floatingActionButton: ScaleTransition(
            scale: _fabController,
            child: FloatingActionButton.extended(
              onPressed: () => _showQuickStats(context),
              backgroundColor: widget.color,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.analytics),
              label: const Text('Stats'),
            ),
          ),
        );
      },
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    return 2;
  }

  Widget _buildMiniStatCard(String value, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 9,
              color: Colors.white.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernFilterChip(String label, IconData icon) {
    final isSelected = filterType == label;
    final theme = Theme.of(context);
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: FilterChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : widget.color,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : widget.color,
              ),
            ),
          ],
        ),
        selected: isSelected,
        onSelected: (selected) => _handleFilterChange(label),
        backgroundColor: Colors.transparent,
        selectedColor: widget.color,
        checkmarkColor: Colors.white,
        side: BorderSide(
          color: widget.color,
          width: 1.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildModernAchievementCard(dynamic achievement, int index) {
    final theme = Theme.of(context);
    final isCompleted = achievement.isCompleted;
    final progress = achievement.progressPercentage / 100;
    
    // Define gold colors for completed achievements
    final completedColors = [
      const Color(0xFFFFD700), // Gold
      const Color(0xFFFFA500), // Orange-gold
    ];
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isCompleted 
            ? [
                completedColors[0].withOpacity(0.15), // Gold background for completed
                completedColors[1].withOpacity(0.08),
              ]
            : [
                widget.color.withOpacity(0.08),
                widget.color.withOpacity(0.02),
              ],
        ),
        border: Border.all(
          color: isCompleted 
            ? completedColors[0].withOpacity(0.4) // Gold border for completed
            : widget.color.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: (isCompleted ? completedColors[0] : widget.color).withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(24),
        child: InkWell(
          onTap: () => _showAchievementDetails(achievement),
          borderRadius: BorderRadius.circular(24),
          splashColor: (isCompleted ? completedColors[0] : widget.color).withOpacity(0.1),
          highlightColor: (isCompleted ? completedColors[0] : widget.color).withOpacity(0.05),
          child: Stack(
            children: [
              // Background pattern
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24),
                  child: CustomPaint(
                    painter: _AchievementCardPatternPainter(
                      color: isCompleted ? completedColors[0] : widget.color,
                      opacity: 0.03,
                    ),
                  ),
                ),
              ),
              
              // Main content
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Top section with icon and status
                    Row(
                      children: [
                        // Achievement icon with glow effect
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: isCompleted 
                                ? completedColors // Gold gradient for completed
                                : [widget.color, widget.color.withOpacity(0.7)],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: (isCompleted ? completedColors[0] : widget.color).withOpacity(0.3),
                                blurRadius: 10,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: Icon(
                            isCompleted ? Icons.check_circle_rounded : achievement.iconData,
                            color: Colors.white,
                            size: 22,
                          ),
                        ),
                        
                        const Spacer(),
                        
                        // Status badge
                        if (isCompleted)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: completedColors, // Gold gradient for completed badge
                              ),
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: completedColors[0].withOpacity(0.3),
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: const Text(
                              'COMPLETED',
                              style: TextStyle(
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                letterSpacing: 0.3,
                              ),
                            ),
                          )
                        else if (progress > 0)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [widget.color, widget.color.withOpacity(0.8)],
                              ),
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: widget.color.withOpacity(0.3),
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Text(
                              '${(progress * 100).toInt()}%',
                              style: const TextStyle(
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Achievement name with shimmer effect
                    Container(
                      child: ShaderMask(
                        shaderCallback: (bounds) => LinearGradient(
                          colors: isCompleted 
                            ? completedColors // Gold text for completed
                            : [
                                theme.colorScheme.onSurface,
                                theme.colorScheme.onSurface.withOpacity(0.8),
                              ],
                        ).createShader(bounds),
                        child: Text(
                          achievement.name,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            height: 1.2,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Description with fade effect
                    Expanded(
                      child: Text(
                        achievement.description,
                        style: TextStyle(
                          fontSize: 13,
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                          height: 1.3,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Bottom section
                    if (!isCompleted) ...[
                      // Progress bar with glow
                      Container(
                        height: 6,
                        decoration: BoxDecoration(
                          color: widget.color.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: Stack(
                          children: [
                            Container(
                              width: double.infinity,
                              height: 6,
                              decoration: BoxDecoration(
                                color: widget.color.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(3),
                              ),
                            ),
                            FractionallySizedBox(
                              widthFactor: progress,
                              child: Container(
                                height: 6,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [widget.color, widget.color.withOpacity(0.8)],
                                  ),
                                  borderRadius: BorderRadius.circular(3),
                                  boxShadow: progress > 0 ? [
                                    BoxShadow(
                                      color: widget.color.withOpacity(0.5),
                                      blurRadius: 6,
                                      offset: const Offset(0, 0),
                                    ),
                                  ] : null,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // FIXED: Progress text with points (no more overflow)
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              progress > 0 ? 'In Progress' : 'Not Started',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: widget.color.withOpacity(0.8),
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 4), // Reduced spacing
                          Icon(
                            Icons.stars_rounded,
                            size: 10,
                            color: Colors.amber,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '${achievement.xpReward ?? 100}',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.amber,
                            ),
                          ),
                        ],
                      ),
                    ] else ...[
                      // FIXED: Completion info (no more overflow)
                      Row(
                        children: [
                          Icon(
                            Icons.celebration_rounded,
                            size: 16,
                            color: completedColors[0], // Gold celebration icon
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              'Challenge Completed!',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: completedColors[0], // Gold text
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 4), // Reduced spacing
                          Icon(
                            Icons.stars_rounded,
                            size: 12,
                            color: Colors.amber,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '${achievement.xpReward ?? 100} XP',
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                              color: Colors.amber,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              
              // Hover/tap feedback overlay
              Positioned.fill(
                child: Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(24),
                  child: InkWell(
                    onTap: () => _showEnhancedAchievementDetails(achievement),
                    borderRadius: BorderRadius.circular(24),
                    splashColor: (isCompleted ? completedColors[0] : widget.color).withOpacity(0.1),
                    highlightColor: (isCompleted ? completedColors[0] : widget.color).withOpacity(0.05),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ).animate(delay: (50 * index).ms)
      .fadeIn(duration: 400.ms, curve: Curves.easeOutQuad)
      .slideY(begin: 0.3, end: 0, duration: 400.ms, curve: Curves.easeOutBack)
      .scale(duration: 400.ms, curve: Curves.easeOutBack);
  }

  // Enhanced achievement details modal
  void _showEnhancedAchievementDetails(dynamic achievement) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) => _buildEnhancedAchievementModal(achievement),
    );
  }

  Widget _buildEnhancedAchievementModal(dynamic achievement) {
    final theme = Theme.of(context);
    final isCompleted = achievement.isCompleted;
    final progress = achievement.progressPercentage / 100;
    
    return Container(
      margin: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 40,
      ),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Drag handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header with animated icon
          Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Large animated icon
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: isCompleted 
                        ? [Colors.green, Colors.green.withOpacity(0.7)]
                        : [widget.color, widget.color.withOpacity(0.7)],
                    ),
                    borderRadius: BorderRadius.circular(32),
                    boxShadow: [
                      BoxShadow(
                        color: (isCompleted ? Colors.green : widget.color).withOpacity(0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Icon(
                    isCompleted ? Icons.check_circle_rounded : achievement.iconData,
                    color: Colors.white,
                    size: 50,
                  ),
                ).animate(delay: 200.ms)
                  .scale(duration: 600.ms, curve: Curves.elasticOut)
                  .shimmer(duration: 1000.ms, color: Colors.white.withOpacity(0.3)),
                
                const SizedBox(height: 20),
                
                // Achievement name
                Text(
                  achievement.name,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isCompleted ? Colors.green : widget.color,
                  ),
                  textAlign: TextAlign.center,
                ).animate(delay: 400.ms).fadeIn(duration: 600.ms),
                
                const SizedBox(height: 8),
                
                // Description
                Text(
                  achievement.description,
                  style: TextStyle(
                    fontSize: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ).animate(delay: 600.ms).fadeIn(duration: 600.ms),
              ],
            ),
          ),
          
          // Progress section
          if (!isCompleted) ...[
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 24),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: widget.color.withOpacity(0.05),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: widget.color.withOpacity(0.1)),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Progress',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: widget.color,
                        ),
                      ),
                      Text(
                        '${(progress * 100).toInt()}%',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: widget.color,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: widget.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Stack(
                      children: [
                        FractionallySizedBox(
                          widthFactor: progress,
                          child: Container(
                            height: 8,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [widget.color, widget.color.withOpacity(0.8)],
                              ),
                              borderRadius: BorderRadius.circular(4),
                              boxShadow: [
                                BoxShadow(
                                  color: widget.color.withOpacity(0.5),
                                  blurRadius: 8,
                                  offset: const Offset(0, 0),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ).animate(delay: 800.ms).slideX(begin: -0.5, duration: 600.ms),
          ],
          
          // Reward section
          Container(
            margin: const EdgeInsets.all(24),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.amber.withOpacity(0.1),
                  Colors.orange.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.amber.withOpacity(0.2)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Colors.amber, Colors.orange],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.amber.withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.stars_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Reward',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.amber[700],
                        ),
                      ),
                      Text(
                        '${achievement.xpReward ?? 100} Experience Points',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.amber,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ).animate(delay: 1000.ms).slideY(begin: 0.5, duration: 600.ms),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildModernEmptyState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: widget.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(
                searchQuery.isNotEmpty || filterType != 'All'
                    ? Icons.search_off_rounded
                    : Icons.emoji_events_outlined,
                size: 60,
                color: widget.color,
              ),
            ).animate()
              .scale(delay: 200.ms, duration: 600.ms)
              .fadeIn(),
            
            const SizedBox(height: 32),
            
            Text(
              searchQuery.isNotEmpty || filterType != 'All'
                  ? 'No Matching Challenges'
                  : 'No Challenges Yet',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ).animate(delay: 400.ms)
              .fadeIn(duration: 600.ms),
            
            const SizedBox(height: 16),
            
            Text(
              searchQuery.isNotEmpty || filterType != 'All'
                  ? 'Try adjusting your search terms or filters'
                  : 'New challenges will appear here as you progress',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ).animate(delay: 600.ms)
              .fadeIn(duration: 600.ms),
            
            const SizedBox(height: 32),
            
            if (searchQuery.isNotEmpty || filterType != 'All')
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _searchController.clear();
                    searchQuery = '';
                    filterType = 'All';
                    _isSearchExpanded = false;
                    _applyFilters();
                  });
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Clear Filters'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.color,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ).animate(delay: 800.ms)
                .fadeIn(duration: 600.ms)
                .slideY(begin: 0.2, end: 0),
          ],
        ),
      ),
    );
  }

  void _showAchievementDetails(dynamic achievement) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AchievementDetailsSheet(
        achievement: achievement,
      ),
    );
  }

  void _showQuickStats(BuildContext context) {
    final completedCount = filteredAchievements.where((a) => a.isCompleted).length;
    final inProgressCount = filteredAchievements.where((a) => !a.isCompleted && a.progressPercentage > 0).length;
    final notStartedCount = filteredAchievements.where((a) => !a.isCompleted && a.progressPercentage == 0).length;
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${widget.title} Statistics',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard('Completed', completedCount, Colors.green),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard('In Progress', inProgressCount, Colors.orange),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard('Not Started', notStartedCount, Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// Custom painter for achievement card background pattern
class _AchievementCardPatternPainter extends CustomPainter {
  final Color color;
  final double opacity;

  _AchievementCardPatternPainter({required this.color, required this.opacity});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(opacity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Draw subtle geometric pattern
    const spacing = 20.0;
    for (double i = 0; i < size.width + spacing; i += spacing) {
      for (double j = 0; j < size.height + spacing; j += spacing) {
        canvas.drawCircle(Offset(i, j), 2, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Background pattern painter for header
class _BackgroundPatternPainter extends CustomPainter {
  final Color color;
  
  _BackgroundPatternPainter(this.color);
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.fill;
    
    // Draw subtle geometric pattern
    for (int i = 0; i < 8; i++) {
      for (int j = 0; j < 8; j++) {
        final x = (size.width / 8) * i;
        final y = (size.height / 8) * j;
        
        if ((i + j) % 2 == 0) {
          canvas.drawCircle(
            Offset(x + 20, y + 20),
            4,
            paint,
          );
        }
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 