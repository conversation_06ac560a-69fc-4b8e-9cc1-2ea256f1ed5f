import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/gamification_provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/youtube_provider.dart';
import '../../widgets/navigation/bottom_navigation_bar.dart';
import '../../widgets/music/now_playing_bar.dart';
import '../../widgets/music/youtube_now_playing_bar.dart.dart';
import '../../utils/navigation_helper.dart';
import '../../widgets/gamification/achievement_card.dart';
import '../../widgets/gamification/achievement_grid_item.dart';
import '../../widgets/gamification/stats_overview.dart';
import '../../widgets/common/keep_alive_wrapper.dart';
import 'category_detail_screen.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/foundation.dart';
import 'components/tabs/all_challenges_tab.dart';
import 'components/tabs/all_challenges_skeleton.dart';
import '../../widgets/gamification/tabs/in_progress_challenges_tab.dart';
import '../../widgets/gamification/tabs/social_challenges_tab.dart';
import '../../widgets/gamification/components/achievement_details_sheet.dart';
import '../../widgets/gamification/components/user_rank_badge.dart';
import '../skins/skin_collection_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;
  
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}

class ChallengesScreen extends StatefulWidget {
  final bool showBottomNav;
  
  const ChallengesScreen({
    Key? key,
    this.showBottomNav = true,
  }) : super(key: key);

  @override
  State<ChallengesScreen> createState() => _ChallengesScreenState();
}

class _ChallengesScreenState extends State<ChallengesScreen> 
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin<ChallengesScreen> {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  double _scrollOffset = 0;
  static final _bucket = PageStorageBucket();

  // NEW: Cache control
  static const Duration _cacheDuration = Duration(minutes: 15);
  static const Duration _staleWhileRevalidateDuration = Duration(hours: 24);
  bool _isLoadingData = false;
  DateTime? _lastCacheUpdate;
  Map<String, dynamic> _cachedData = {};

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Initialize data immediately instead of waiting for post-frame
    _initializeWithCache();
    
    // Setup listeners
    _tabController.addListener(_handleTabChange);
    _scrollController.addListener(_handleScroll);
  }

  /// Initialize with proper caching strategy
  Future<void> _initializeWithCache() async {
    if (!mounted) return;
    
    try {
      setState(() => _isLoadingData = true);
      
      // 1. Get providers
      final gamificationProvider = context.read<GamificationProvider>();
      
      // 2. Load cached data first for immediate display
      await gamificationProvider.loadCachedProgress();
      
      // 3. Always fetch fresh data on initial load
      await gamificationProvider.initialize();
      
      // 4. Update cache with fresh data
      await _updateCache(gamificationProvider);
      
      // 5. Restore UI state
      await _loadFromCache();
      _restoreUIState();
      
    } catch (e) {
      debugPrint('Cache initialization error: $e');
      _showErrorSnackbar('Error loading challenges');
    } finally {
      if (mounted) {
        setState(() => _isLoadingData = false);
      }
    }
  }

  /// Load data from cache
  Future<void> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load cache metadata
      final lastUpdateStr = prefs.getString('challenges_cache_timestamp');
      if (lastUpdateStr != null) {
        _lastCacheUpdate = DateTime.parse(lastUpdateStr);
      }
      
      // Load cached data
      final cachedDataStr = prefs.getString('challenges_cache_data');
      if (cachedDataStr != null) {
        _cachedData = json.decode(cachedDataStr);
      }
      
      // Load UI state
      final uiState = prefs.getString('challenges_ui_state');
      if (uiState != null) {
        final state = json.decode(uiState);
        _restoreUIFromState(state);
      }
      
    } catch (e) {
      debugPrint('Error loading from cache: $e');
      _cachedData = {};
      _lastCacheUpdate = null;
    }
  }

  /// Check if cache needs refresh
  bool _shouldRefreshCache() {
    if (_lastCacheUpdate == null) return true;
    
    final now = DateTime.now();
    final age = now.difference(_lastCacheUpdate!);
    
    // Cache is considered stale after _cacheDuration
    return age > _cacheDuration;
  }

  /// Refresh data in background with error handling
  Future<void> _refreshDataInBackground() async {
    if (_isLoadingData) return;
    
    try {
      _isLoadingData = true;
      
      // Get fresh data
      final gamificationProvider = context.read<GamificationProvider>();
      await gamificationProvider.initialize();
      
      // Update cache
      await _updateCache(gamificationProvider);
      
      // Update UI if needed
      if (mounted) setState(() {});
      
    } catch (e) {
      debugPrint('Background refresh error: $e');
      // If refresh fails but we have valid cache, keep using it
      if (_cachedData.isNotEmpty && _lastCacheUpdate != null) {
        final cacheAge = DateTime.now().difference(_lastCacheUpdate!);
        if (cacheAge <= _staleWhileRevalidateDuration) {
          // Cache still valid, continue using it
          return;
        }
      }
      // Otherwise, show error
      _showErrorSnackbar('Could not refresh challenges data');
    } finally {
      _isLoadingData = false;
    }
  }

  /// Update cache with new data
  Future<void> _updateCache(GamificationProvider provider) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Prepare cache data
      final cacheData = {
        'achievements': provider.achievements.map((a) => a.toJson()).toList(),
        'categories': {
          'artist': provider.artistAchievements.map((a) => a.toJson()).toList(),
          'genre': provider.genreAchievements.map((a) => a.toJson()).toList(),
          'location': provider.locationAchievements.map((a) => a.toJson()).toList(),
          'social': provider.socialAchievements.map((a) => a.toJson()).toList(),
        },
        'stats': {
          'total_completed': provider.totalCompleted,
          'total_xp': provider.totalXpEarned,
        }
      };
      
      // Save cache
      await prefs.setString('challenges_cache_data', json.encode(cacheData));
      await prefs.setString('challenges_cache_timestamp', DateTime.now().toIso8601String());
      
      // Update memory cache
      _cachedData = cacheData;
      _lastCacheUpdate = DateTime.now();
      
    } catch (e) {
      debugPrint('Error updating cache: $e');
      throw Exception('Failed to update cache: $e');
    }
  }

  /// Save UI state separately from data cache
  Future<void> _saveUIState() async {
    if (!mounted) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final state = {
        'tab_index': _tabController.index,
        'scroll_position': _scrollController.hasClients ? _scrollController.position.pixels : 0.0,
        'last_viewed_achievement': null, // Add more UI state as needed
      };
      
      await prefs.setString('challenges_ui_state', json.encode(state));
      
    } catch (e) {
      debugPrint('Error saving UI state: $e');
    }
  }

  /// Restore UI state
  void _restoreUIState() {
    try {
      final state = _cachedData['ui_state'];
      if (state != null) {
        _restoreUIFromState(state);
      }
    } catch (e) {
      debugPrint('Error restoring UI state: $e');
    }
  }

  /// Restore UI from state data
  void _restoreUIFromState(Map<String, dynamic> state) {
    final tabIndex = state['tab_index'] as int? ?? 0;
    final scrollPosition = state['scroll_position'] as double? ?? 0.0;
    
    if (_tabController.index != tabIndex) {
      _tabController.index = tabIndex;
    }
    
    if (_scrollController.hasClients && scrollPosition > 0) {
      _scrollController.jumpTo(scrollPosition);
    }
  }

  /// Show error message
  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Force refresh data
  Future<void> _forceRefresh() async {
    if (_isLoadingData) return;
    
    try {
      _isLoadingData = true;
      if (!mounted) return;
      
      final gamificationProvider = context.read<GamificationProvider>();
      await gamificationProvider.forceReload();
      await _updateCache(gamificationProvider);
      
      if (mounted) setState(() {});
      
    } catch (e) {
      debugPrint('Force refresh error: $e');
      _showErrorSnackbar('Could not refresh data');
    } finally {
      _isLoadingData = false;
    }
  }

  void _handleTabChange() {
    if (!mounted || !_tabController.indexIsChanging) return;
    _saveCurrentState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      setState(() {});
    });
  }

  void _handleScroll() {
    if (!mounted) return;
    setState(() {
      _scrollOffset = _scrollController.offset;
    });
    _saveCurrentState();
  }

  void _saveCurrentState() {
    if (!mounted) return;
    final state = {
      'tab_index': _tabController.index,
      'scroll_position': _scrollController.hasClients ? _scrollController.position.pixels : 0.0,
    };
    _bucket.writeState(context, state, identifier: 'challenges_state');
  }

  @override
  void dispose() {
    _saveUIState();
    _tabController.removeListener(_handleTabChange);
    _scrollController.removeListener(_handleScroll);
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _handleNavigation(int index) {
    if (index == 3) return; // Already on challenges
    
    if (index == 2) { // Map tab
      NavigationHelper.navigateToScreen(context, '/map');
      return;
    }
    
    NavigationHelper.navigateToTab(context, index);
  }
  
  void _handleAddPin() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add pin feature coming soon')),
    );
  }

  Widget _buildTab(IconData icon, String text, double iconSize) {
    return Tab(
      height: 32,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: iconSize),
            const SizedBox(width: 4),
            Flexible(
              fit: FlexFit.loose,
              child: Text(
                text,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                style: const TextStyle(height: 1.1),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final youtubeProvider = Provider.of<YouTubeProvider>(context);
    final gamificationProvider = Provider.of<GamificationProvider>(context);
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;
    final hasActiveMusic = (spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null) ||
                          (youtubeProvider.hasActivePlayback && youtubeProvider.currentTrack != null);
    
    final theme = Theme.of(context);

    // Show loading indicator while initializing
    if (_isLoadingData) {
      return Scaffold(
        body: NestedScrollView(
          controller: _scrollController,
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              SliverAppBar(
                pinned: true,
                floating: true,
                snap: true,
                elevation: 0,
                scrolledUnderElevation: 2,
                backgroundColor: theme.scaffoldBackgroundColor,
                automaticallyImplyLeading: false,
                title: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.emoji_events_rounded,
                        color: theme.colorScheme.primary,
                        size: 22,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Challenges',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 22,
                        color: theme.colorScheme.onSurface,
                        letterSpacing: -0.5,
                      ),
                    ),
                  ],
                ),
                actions: [
                  Padding(
                    padding: const EdgeInsets.only(right: 16),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SkinCollectionScreen(),
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: screenWidth < 360 ? 10 : 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: (theme.brightness == Brightness.dark ? Colors.white : theme.colorScheme.surface)
                                .withOpacity(theme.brightness == Brightness.dark ? 0.1 : 0.8),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: (theme.brightness == Brightness.dark ? Colors.white : theme.colorScheme.onSurface)
                                  .withOpacity(theme.brightness == Brightness.dark ? 0.3 : 0.1),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(theme.brightness == Brightness.dark ? 0.1 : 0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.style,
                                size: screenWidth < 360 ? 16 : 18,
                                color: theme.brightness == Brightness.dark
                                    ? Colors.white.withOpacity(0.9)
                                    : theme.colorScheme.onSurface.withOpacity(0.8),
                              ),
                              SizedBox(width: screenWidth < 360 ? 4 : 6),
                              Text(
                                'Skins',
                                style: TextStyle(
                                  fontSize: screenWidth < 360 ? 12 : 13,
                                  fontWeight: FontWeight.w600,
                                  color: theme.brightness == Brightness.dark
                                      ? Colors.white.withOpacity(0.9)
                                      : theme.colorScheme.onSurface.withOpacity(0.8),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
                bottom: PreferredSize(
                  preferredSize: const Size.fromHeight(48),
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: TabBar(
                      controller: _tabController,
                      labelColor: theme.colorScheme.primary,
                      unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.5),
                      labelStyle: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: screenWidth < 360 ? 11 : 12,
                        letterSpacing: -0.3,
                      ),
                      unselectedLabelStyle: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: screenWidth < 360 ? 11 : 12,
                        letterSpacing: -0.3,
                      ),
                      indicator: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: theme.colorScheme.primary.withOpacity(0.1),
                      ),
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelPadding: EdgeInsets.symmetric(
                        horizontal: screenWidth < 360 ? 4 : 6,
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: screenWidth < 360 ? 4 : 8,
                      ),
                      dividerColor: Colors.transparent,
                      splashFactory: NoSplash.splashFactory,
                      overlayColor: MaterialStateProperty.resolveWith<Color?>(
                        (Set<MaterialState> states) {
                          return states.contains(MaterialState.focused) ? null : Colors.transparent;
                        },
                      ),
                      tabs: [
                        _buildTab(Icons.emoji_events, 'All', screenWidth < 360 ? 14 : 16),
                        _buildTab(Icons.trending_up, 'Pending', screenWidth < 360 ? 14 : 16),
                        _buildTab(Icons.group, 'Social', screenWidth < 360 ? 14 : 16),
                      ],
                    ),
                  ),
                ),
              ),
            ];
          },
          body: TabBarView(
            controller: _tabController,
            children: [
              const AllChallengesTabSkeleton(),
              const Center(child: AllChallengesTabSkeleton()),
              const Center(child: AllChallengesTabSkeleton()),
            ],
          ),
        ),
        bottomNavigationBar: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null)
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: const NowPlayingBar(),
              ),
            // YouTube removed - it has its own embedded player
            if (widget.showBottomNav)
              MusicPinBottomNavBar.auto(
                context: context,
                onTabSelected: _handleNavigation,
                onAddPinPressed: _handleAddPin,
              ),
          ],
        ),
      );
    }

    return PageStorage(
      bucket: _bucket,
      child: Scaffold(
        body: NestedScrollView(
          controller: _scrollController,
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              SliverAppBar(
                pinned: true,
                floating: true,
                snap: true,
                elevation: 0,
                scrolledUnderElevation: 2,
                backgroundColor: theme.scaffoldBackgroundColor,
                automaticallyImplyLeading: false,
                title: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.emoji_events_rounded,
                        color: theme.colorScheme.primary,
                        size: 22,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Challenges',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 22,
                        color: theme.colorScheme.onSurface,
                        letterSpacing: -0.5,
                      ),
                    ),
                  ],
                ),
                actions: [
                  Padding(
                    padding: const EdgeInsets.only(right: 16),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SkinCollectionScreen(),
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: screenWidth < 360 ? 10 : 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: (theme.brightness == Brightness.dark ? Colors.white : theme.colorScheme.surface)
                                .withOpacity(theme.brightness == Brightness.dark ? 0.1 : 0.8),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: (theme.brightness == Brightness.dark ? Colors.white : theme.colorScheme.onSurface)
                                  .withOpacity(theme.brightness == Brightness.dark ? 0.3 : 0.1),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(theme.brightness == Brightness.dark ? 0.1 : 0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.style,
                                size: screenWidth < 360 ? 16 : 18,
                                color: theme.brightness == Brightness.dark
                                    ? Colors.white.withOpacity(0.9)
                                    : theme.colorScheme.onSurface.withOpacity(0.8),
                              ),
                              SizedBox(width: screenWidth < 360 ? 4 : 6),
                              Text(
                                'Skins',
                                style: TextStyle(
                                  fontSize: screenWidth < 360 ? 12 : 13,
                                  fontWeight: FontWeight.w600,
                                  color: theme.brightness == Brightness.dark
                                      ? Colors.white.withOpacity(0.9)
                                      : theme.colorScheme.onSurface.withOpacity(0.8),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
                bottom: PreferredSize(
                  preferredSize: const Size.fromHeight(48),
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: TabBar(
                      controller: _tabController,
                      labelColor: theme.colorScheme.primary,
                      unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.5),
                      labelStyle: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: screenWidth < 360 ? 11 : 12,
                        letterSpacing: -0.3,
                      ),
                      unselectedLabelStyle: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: screenWidth < 360 ? 11 : 12,
                        letterSpacing: -0.3,
                      ),
                      indicator: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: theme.colorScheme.primary.withOpacity(0.1),
                      ),
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelPadding: EdgeInsets.symmetric(
                        horizontal: screenWidth < 360 ? 4 : 6,
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: screenWidth < 360 ? 4 : 8,
                      ),
                      dividerColor: Colors.transparent,
                      splashFactory: NoSplash.splashFactory,
                      overlayColor: MaterialStateProperty.resolveWith<Color?>(
                        (Set<MaterialState> states) {
                          return states.contains(MaterialState.focused) ? null : Colors.transparent;
                        },
                      ),
                      tabs: [
                        _buildTab(Icons.emoji_events, 'All', screenWidth < 360 ? 14 : 16),
                        _buildTab(Icons.trending_up, 'Pending', screenWidth < 360 ? 14 : 16),
                        _buildTab(Icons.group, 'Social', screenWidth < 360 ? 14 : 16),
                      ],
                    ),
                  ),
                ),
              ),
            ];
          },
          body: TabBarView(
            controller: _tabController,
            children: [
              KeepAliveWrapper(
                key: const PageStorageKey('all_challenges_tab'),
                child: AllChallengesTab(
                  gamificationProvider: gamificationProvider,
                ),
              ),
              KeepAliveWrapper(
                key: const PageStorageKey('in_progress_challenges_tab'),
                child: InProgressChallengesTab(
                  gamificationProvider: gamificationProvider,
                ),
              ),
              KeepAliveWrapper(
                key: const PageStorageKey('social_challenges_tab'),
                child: SocialChallengesTab(
                  gamificationProvider: gamificationProvider,
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null)
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: const NowPlayingBar(),
              ),
            // YouTube removed - it has its own embedded player
            if (widget.showBottomNav)
              MusicPinBottomNavBar.auto(
                context: context,
                onTabSelected: _handleNavigation,
                onAddPinPressed: _handleAddPin,
              ),
          ],
        ),
      ),
    );
  }
} 