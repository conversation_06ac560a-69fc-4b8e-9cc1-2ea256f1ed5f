import 'package:flutter/material.dart';
import '../../widgets/profile/bop_history_tab.dart';

class HistoryScreen extends StatelessWidget {
  const HistoryScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'History',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      ),
      body: const BOPHistoryTab(
        key: PageStorage<PERSON>ey('history_screen'),
      ),
    );
  }
} 