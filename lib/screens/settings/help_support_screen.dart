import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import '../../config/help_support.dart';
import 'get_support_screen.dart';

class HelpSupportScreen extends StatelessWidget {
  const HelpSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final dark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        iconTheme: IconThemeData(color: theme.colorScheme.onBackground),
        title: Text(
          'Help & Support',
          style: theme.textTheme.titleMedium?.copyWith(
            color: theme.colorScheme.onBackground,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onBackground),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Html(
                  data: HelpSupportContent.helpHtml.replaceAll('#1a73e8', dark ? '#64B5F6' : '#1a73e8').replaceAll('#333333', dark ? '#E0E0E0' : '#333333'),
                  style: {
                    'body': Style(
                      fontSize: FontSize(16.0),
                      lineHeight: LineHeight(1.6),
                      color: dark ? Colors.white : Colors.black87,
                    ),
                    'h2': Style(
                      fontSize: FontSize(20.0),
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                    'ul': Style(margin: Margins.only(left: 20, bottom: 12)),
                    'ol': Style(margin: Margins.only(left: 20, bottom: 12)),
                  },
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => const GetSupportScreen()),
                      );
                    },
                    icon: const Icon(Icons.chat_bubble_outline),
                    label: const Text('Chat with Us'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 