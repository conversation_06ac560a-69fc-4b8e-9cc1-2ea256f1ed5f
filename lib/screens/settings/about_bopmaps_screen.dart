import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import '../../config/about_bopmaps.dart';

class AboutBOPMapsScreen extends StatelessWidget {
  const AboutBOPMapsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        iconTheme: IconThemeData(color: theme.colorScheme.onBackground),
        title: Text(
          'About BOPMaps',
          style: theme.textTheme.titleMedium?.copyWith(
            color: theme.colorScheme.onBackground,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onBackground),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Html(
              data: AboutContent.aboutHtml.replaceAll('#1a73e8', isDark ? '#64B5F6' : '#1a73e8').replaceAll('#333333', isDark ? '#E0E0E0' : '#333333'),
              style: {
                'body': Style(
                  fontSize: FontSize(16.0),
                  lineHeight: LineHeight(1.6),
                  color: isDark ? Colors.white : Colors.black87,
                ),
                'h2': Style(
                  fontSize: FontSize(20.0),
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
                'ul': Style(margin: Margins.only(left: 20, bottom: 12)),
              },
            ),
          ),
        ),
      ),
    );
  }
} 