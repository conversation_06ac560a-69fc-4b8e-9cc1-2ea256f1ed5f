import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import '../../config/legal_documents.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        iconTheme: IconThemeData(color: theme.colorScheme.onBackground),
        title: Text(
          'Terms of Service',
          style: theme.textTheme.titleMedium?.copyWith(
            color: theme.colorScheme.onBackground,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onBackground),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16.0),
                child: Html(
                  data: AppDocuments.termsOfUseHtml.replaceAll(
                    '#1a73e8', 
                    isDarkMode ? '#64B5F6' : '#1a73e8'
                  ).replaceAll(
                    '#333333',
                    isDarkMode ? '#E0E0E0' : '#333333'
                  ),
                  style: {
                    "body": Style(
                      fontSize: FontSize(16.0),
                      lineHeight: LineHeight(1.6),
                      textAlign: TextAlign.left,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                    "h1": Style(
                      fontSize: FontSize(24.0),
                      fontWeight: FontWeight.bold,
                      textAlign: TextAlign.center,
                      color: theme.colorScheme.primary,
                    ),
                    "h2": Style(
                      fontSize: FontSize(20.0),
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                    "h3": Style(
                      fontSize: FontSize(18.0),
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  },
                ),
              ),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
} 