import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/spotify_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../config/theme.dart';
import 'package:flutter/foundation.dart';

class MusicSettingsScreen extends StatefulWidget {
  const MusicSettingsScreen({Key? key}) : super(key: key);

  @override
  _MusicSettingsScreenState createState() => _MusicSettingsScreenState();
}

class _MusicSettingsScreenState extends State<MusicSettingsScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

    if (spotifyProvider.isConnected) {
      // Load user profile if not already loaded
      if (spotifyProvider.userProfile == null) {
        await spotifyProvider.loadUserProfile();
      }

      // Load user stats if not already loaded
      if (spotifyProvider.userStats == null) {
        await spotifyProvider.loadUserStats();
      }
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Music Settings'),
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : Consumer<SpotifyProvider>(
              builder: (context, spotifyProvider, child) {
                if (!spotifyProvider.isConnected) {
                  return _buildConnectSection(spotifyProvider);
                }

                return _buildSettingsContent(spotifyProvider);
              },
            ),
    );
  }

  Widget _buildConnectSection(SpotifyProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.music_note, size: 80, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            'No music services connected',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Connect a music service to share songs and playlists',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: const Icon(Icons.music_note),
            label: const Text('Connect Spotify'),
            onPressed: () async {
              setState(() {
                _isLoading = true;
              });
              
              await provider.connect();
              
              // After connecting, load user data
              if (provider.isConnected) {
                await provider.loadUserProfile();
                await provider.loadUserStats();
              }
              
              setState(() {
                _isLoading = false;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsContent(SpotifyProvider provider) {
    final profile = provider.userProfile;
    final stats = provider.userStats;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile card
          _buildProfileCard(profile),
          
          const SizedBox(height: 24),
          
          // Stats section
          if (stats != null) _buildStatsSection(stats),
          
          const SizedBox(height: 24),
          
          // Settings options
          _buildSettingsOptions(provider),
          
          const SizedBox(height: 24),
          
          // Disconnect button
          _buildDisconnectButton(provider),
        ],
      ),
    );
  }

  Widget _buildProfileCard(Map<String, dynamic>? profile) {
    if (profile == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Text('Profile information not available'),
          ),
        ),
      );
    }

    final profileImage = profile['profile_image_url'];
    final displayName = profile['display_name'] ?? 'Spotify User';
    final email = profile['email'];
    final isPremium = profile['is_premium'] ?? false;
    final accountType = profile['product'] ?? 'free';

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile image
            if (profileImage != null)
              CircleAvatar(
                radius: 40,
                backgroundImage: NetworkImage(profileImage),
              )
            else
              const CircleAvatar(
                radius: 40,
                backgroundColor: Colors.green,
                child: Icon(Icons.person, size: 40, color: Colors.white),
              ),
            
            const SizedBox(height: 16),
            
            // Display name
            Text(
              displayName,
              style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            ),
            
            // Email
            if (email != null)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  email,
                  style: const TextStyle(color: Colors.grey),
                ),
              ),
            
            const SizedBox(height: 12),
            
            // Account type badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: isPremium ? Colors.green.shade800 : Colors.grey.shade800,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                isPremium ? 'Premium' : 'Free',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection(Map<String, dynamic> stats) {
    final topArtists = stats['top_artists'] as List<dynamic>? ?? [];
    final topTracks = stats['top_tracks'] as List<dynamic>? ?? [];
    final topGenres = stats['top_genres'] as List<dynamic>? ?? [];
    final playlistsCount = stats['playlists_count'] ?? 0;
    final savedTracksCount = stats['saved_tracks_count'] ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Your Music Stats',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        // Stats Grid
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.5,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildStatCard(
              title: 'Playlists',
              value: playlistsCount.toString(),
              icon: Icons.playlist_play,
              color: Colors.purple,
            ),
            _buildStatCard(
              title: 'Saved Tracks',
              value: savedTracksCount.toString(),
              icon: Icons.favorite,
              color: Colors.red,
            ),
            _buildStatCard(
              title: 'Top Artists',
              value: topArtists.length.toString(),
              icon: Icons.person,
              color: Colors.blue,
            ),
            _buildStatCard(
              title: 'Top Genres',
              value: topGenres.length.toString(),
              icon: Icons.category,
              color: Colors.orange,
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Top genres chips
        if (topGenres.isNotEmpty) 
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Your Top Genres',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: topGenres.map((genre) {
                  return Chip(
                    label: Text(genre.toString()),
                    backgroundColor: ThemeColors.primaryLight,
                    labelStyle: const TextStyle(color: Colors.white),
                  );
                }).toList(),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsOptions(SpotifyProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Music Settings',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        
        // Setting options list
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.refresh),
                title: const Text('Refresh Music Data'),
                onTap: () async {
                  setState(() {
                    _isLoading = true;
                  });
                  
                  await provider.loadUserProfile();
                  await provider.loadUserStats();
                  await provider.loadLikedSongs(refresh: true);
                  await provider.loadPlaylists(refresh: true);
                  
                  setState(() {
                    _isLoading = false;
                  });
                  
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Music data refreshed')),
                    );
                  }
                },
              ),
              const Divider(height: 1),
              
              // Auto-resume setting
              SwitchListTile(
                secondary: const Icon(Icons.play_circle_outline),
                title: const Text('Auto-resume playback'),
                subtitle: const Text('Automatically resume music playback when opening the app'),
                value: provider.autoResumePlayback,
                onChanged: (value) {
                  provider.toggleAutoResumePlayback();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        provider.autoResumePlayback 
                          ? 'Music will automatically resume when app is opened'
                          : 'Music will not auto-resume when app is opened'
                      ),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
              ),
              const Divider(height: 1),
              
              ListTile(
                leading: const Icon(Icons.favorite),
                title: const Text('Manage Saved Tracks'),
                onTap: () {
                  // Navigate to saved tracks screen
                  // Navigator.of(context).push(MaterialPageRoute(builder: (_) => SavedTracksScreen()));
                },
              ),
              const Divider(height: 1),
              
              // Only show the debug toggle in debug mode
              if (kDebugMode) 
                ...[
                  SwitchListTile(
                    secondary: const Icon(Icons.bug_report),
                    title: const Text('Prioritize Production Authentication'),
                    subtitle: const Text('Try real Spotify auth first, use debug tokens as fallback'),
                    value: provider.prioritizeProductionAuth,
                    onChanged: (value) {
                      provider.prioritizeProductionAuth = value;
                      
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            value 
                              ? 'App will try real authentication first'
                              : 'App will use debug tokens in debug mode'
                          ),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                  ),
                  
                  // Test authentication button
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.refresh),
                      label: const Text('Test Spotify Authentication'),
                      onPressed: () async {
                        setState(() {
                          _isLoading = true;
                        });
                        
                        // First disconnect to clear any existing tokens
                        await provider.disconnect();
                        
                        // Then try to connect with current settings
                        final success = await provider.connect();
                        
                        setState(() {
                          _isLoading = false;
                        });
                        
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                success 
                                  ? 'Authentication successful' 
                                  : 'Authentication failed'
                              ),
                              backgroundColor: success ? Colors.green : Colors.red,
                            ),
                          );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ThemeColors.primaryLight,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  
                  const Divider(height: 1),
                ],
              
              ListTile(
                leading: const Icon(Icons.playlist_play),
                title: const Text('Manage Playlists'),
                onTap: () {
                  // Navigate to playlists screen
                  // Navigator.of(context).push(MaterialPageRoute(builder: (_) => PlaylistsScreen()));
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDisconnectButton(SpotifyProvider provider) {
    return Center(
      child: ElevatedButton.icon(
        icon: const Icon(Icons.logout),
        label: const Text('Disconnect Spotify'),
        onPressed: () async {
          final confirmed = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Disconnect Spotify?'),
              content: const Text(
                'Are you sure you want to disconnect your Spotify account? '
                'You will need to reconnect it to use Spotify features.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('Disconnect'),
                ),
              ],
            ),
          );
          
          if (confirmed == true) {
            setState(() {
              _isLoading = true;
            });
            
            await provider.disconnect();
            
            setState(() {
              _isLoading = false;
            });
            
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Spotify disconnected')),
              );
            }
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    );
  }
} 