import 'package:flutter/material.dart';

class AuthButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? textColor;
  final Widget? icon;
  final double height;
  final double width;
  final double borderRadius;
  final EdgeInsetsGeometry contentPadding;
  final MainAxisAlignment mainAxisAlignment;
  final EdgeInsetsGeometry? iconPadding;

  const AuthButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.height = 48,
    this.width = double.infinity,
    this.borderRadius = 24,
    this.contentPadding = const EdgeInsets.symmetric(horizontal: 20),
    this.mainAxisAlignment = MainAxisAlignment.center,
    this.iconPadding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? theme.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          padding: EdgeInsets.zero,
          minimumSize: Size.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          elevation: 0,
        ),
        child: Container(
          height: height,
          padding: contentPadding,
          child: isLoading
              ? Center(
                  child: SizedBox(
                    height: 16,
                    width: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 1.5,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        textColor ?? Colors.white,
                      ),
                    ),
                  ),
                )
              : Row(
                  mainAxisAlignment: mainAxisAlignment,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (icon != null) ...[
                      SizedBox(
                        width: 18,
                        height: 18,
                        child: icon!,
                      ),
                      const SizedBox(width: 8),
                    ],
                    Flexible(
                      child: Text(
                        text,
                        style: TextStyle(
                          color: textColor ?? Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          height: 1.0,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }
} 