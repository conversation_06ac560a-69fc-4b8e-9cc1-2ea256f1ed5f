import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

class AnimatedBackground extends StatelessWidget {
  final AnimationController animationController;
  final bool isDarkMode;

  const AnimatedBackground({
    Key? key,
    required this.animationController,
    required this.isDarkMode,
  }) : super(key: key);

  List<Color> _getGradientColors(bool isDarkMode) {
    return isDarkMode ? [
      const Color(0xFF121212),
      const Color(0xFF1E1E1E),
      const Color(0xFF0A0A0A),
    ] : [
      const Color(0xFFF5F5F5),
      const Color(0xFFE0E0E0),
      const Color(0xFFD0D0D0),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animationController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: _getGradientColors(isDarkMode),
              stops: const [0.0, 0.6, 1.0],
            ),
          ),
          child: Stack(
            children: [
              // Subtle animated overlay
              Positioned.fill(
                child: Opacity(
                  opacity: 0.1 + (0.05 * math.sin(animationController.value * math.pi * 2)),
                  child: Image.network(
                    'https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80',
                    fit: BoxFit.cover,
                    color: (isDarkMode ? Colors.black : Colors.white).withOpacity(0.5),
                    colorBlendMode: BlendMode.darken,
                    errorBuilder: (context, error, stackTrace) {
                      return const SizedBox();
                    },
                  ),
                ),
              ),
              
              // Blurred overlay
              BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
                child: Container(
                  color: (isDarkMode ? Colors.black : Colors.white).withOpacity(0.4),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
} 