import 'package:flutter/material.dart';
import 'dart:math' as math;

class MusicVisualizer extends StatelessWidget {
  final AnimationController animationController;
  final bool isDarkMode;

  const MusicVisualizer({
    Key? key,
    required this.animationController,
    required this.isDarkMode,
  }) : super(key: key);

  List<Color> _getVisualizerColors(bool isDarkMode) {
    if (isDarkMode) {
      return [
        const Color(0xFFFF80AB).withOpacity(0.85), // Soft bubblegum pink
        const Color(0xFFBA68C8).withOpacity(0.85), // Light orchid purple
        const Color(0xFF80DEEA).withOpacity(0.85), // Soft aqua
        const Color(0xFFFF4081).withOpacity(0.85), // Vibrant neon pink
        const Color(0xFFA7BFFF).withOpacity(0.85), // Periwinkle blue
        const Color(0xFFF06292).withOpacity(0.85), // Muted rose-pink
        const Color(0xFFCE93D8).withOpacity(0.85), // Soft lavender
        const Color(0xFF00E5FF).withOpacity(0.85), // Neon cyan
        const Color(0xFFFF80AB).withOpacity(0.85), // Back to soft pink
      ];
    } else {
      return [
        const Color(0xFFE91E63).withOpacity(0.75), // Deeper pink
        const Color(0xFF9C27B0).withOpacity(0.75), // Deep purple
        const Color(0xFF2196F3).withOpacity(0.75), // Bright blue
        const Color(0xFFFF1744).withOpacity(0.75), // Strong red
        const Color(0xFF3F51B5).withOpacity(0.75), // Indigo
        const Color(0xFFE91E63).withOpacity(0.75), // Pink
        const Color(0xFF9C27B0).withOpacity(0.75), // Purple
        const Color(0xFF00BCD4).withOpacity(0.75), // Cyan
        const Color(0xFFE91E63).withOpacity(0.75), // Back to pink
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    final visualizerColors = _getVisualizerColors(isDarkMode);
    
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: SizedBox(
        height: 100,
        child: AnimatedBuilder(
          animation: animationController,
          builder: (context, child) {
            final primaryColorIndex = (animationController.value * (visualizerColors.length - 1) * 0.1).floor();
            final secondaryColorIndex = (primaryColorIndex + 2) % visualizerColors.length;
            final tertiaryColorIndex = (primaryColorIndex + 4) % visualizerColors.length;
            
            final primaryColorPercent = (animationController.value * (visualizerColors.length - 1) * 0.1) - primaryColorIndex;
            
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(
                48,
                (index) {
                  final double height = 5 + 
                    65 * math.sin(
                      (animationController.value * math.pi * 1.3) +
                      (index * 0.13) +
                      (math.sin(index * 0.3) * 0.4)
                    ).abs();
                  
                  final multiplier = 1.0 + ((index % 4) * 0.18);
                  
                  int colorGroup = (index ~/ 4) % 3;
                  Color baseColor;
                  
                  if (colorGroup == 0) {
                    final nextPrimaryIndex = (primaryColorIndex + 1) % visualizerColors.length;
                    baseColor = Color.lerp(
                      visualizerColors[primaryColorIndex],
                      visualizerColors[nextPrimaryIndex],
                      primaryColorPercent,
                    )!;
                  } else if (colorGroup == 1) {
                    final nextSecondaryIndex = (secondaryColorIndex + 1) % visualizerColors.length;
                    baseColor = Color.lerp(
                      visualizerColors[secondaryColorIndex],
                      visualizerColors[nextSecondaryIndex],
                      primaryColorPercent,
                    )!;
                  } else {
                    final nextTertiaryIndex = (tertiaryColorIndex + 1) % visualizerColors.length;
                    baseColor = Color.lerp(
                      visualizerColors[tertiaryColorIndex],
                      visualizerColors[nextTertiaryIndex],
                      primaryColorPercent,
                    )!;
                  }
                  
                  final finalOpacity = isDarkMode ? 
                    (0.5 + (height / 130) * 0.4) : 
                    (0.65 + (height / 130) * 0.3);
                  
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 350),
                    width: 2.6,
                    height: height * multiplier,
                    decoration: BoxDecoration(
                      color: baseColor.withOpacity(finalOpacity),
                      borderRadius: BorderRadius.circular(4),
                      boxShadow: [
                        BoxShadow(
                          color: baseColor.withOpacity(isDarkMode ? 0.4 : 0.5),
                          blurRadius: 8,
                          spreadRadius: 0.5,
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
} 