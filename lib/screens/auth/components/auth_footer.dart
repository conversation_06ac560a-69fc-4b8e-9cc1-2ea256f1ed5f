import 'package:flutter/material.dart';
import '../../../config/themes.dart';

class AuthFooter extends StatelessWidget {
  const AuthFooter({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isSmallScreen = MediaQuery.of(context).size.height < 600;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        children: [
          const SizedBox(height: 6),
          Container(
            height: 1,
            width: 40,
            margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
            decoration: BoxDecoration(
              color: (isDarkMode ? Colors.white : Colors.black).withOpacity(0.15),
              borderRadius: BorderRadius.circular(1),
            ),
          ),
          Text(
            'By continuing, you agree to our',
            style: TextStyle(
              color: (isDarkMode ? Colors.white : Colors.black).withOpacity(0.6),
              fontSize: isSmallScreen ? 11 : 12,
            ),
            textAlign: TextAlign.center,
          ),
          Wrap(
            alignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              TextButton(
                onPressed: () {
                  // Navigate to terms screen
                },
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  'Terms of Service',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: isSmallScreen ? 11 : 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Text(
                'and',
                style: TextStyle(
                  color: (isDarkMode ? Colors.white : Colors.black).withOpacity(0.6),
                  fontSize: isSmallScreen ? 11 : 12,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to privacy policy screen
                },
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  'Privacy Policy',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: isSmallScreen ? 11 : 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 