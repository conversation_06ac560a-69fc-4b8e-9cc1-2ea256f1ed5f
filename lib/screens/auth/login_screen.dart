import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../../providers/theme_provider.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../services/music/spotify_web_api_service.dart';
import '../../services/music/apple_music_auth_service.dart';
import 'components/animated_background.dart';
import 'components/music_visualizer.dart';
import 'services/auth_service.dart';
import 'components/auth_button.dart';
import 'components/auth_footer.dart';
import '../../services/api/api_client.dart'; // Added import for ApiClient
import '../../config/route_constants.dart'; // Added import for route constants

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with TickerProviderStateMixin {
  // Controllers
  late AnimationController _animationController;
  late AnimationController _pageAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _scaleAnimation;
  
  // State
  bool _isSpotifyLoading = false;
  bool _isAppleLoading = false;
  
  // Debounce timestamps for sign-in buttons
  DateTime? _lastSpotifySignIn;
  DateTime? _lastAppleSignIn;
  
  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 6000),
    )..repeat(reverse: false);
    
    _pageAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _pageAnimationController,
        curve: Curves.easeOut,
      ),
    );
    
    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _pageAnimationController,
        curve: Curves.easeOutCubic,
      ),
    );
    
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _pageAnimationController,
        curve: Curves.easeOutCubic,
      ),
    );
    
    _pageAnimationController.forward();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    _pageAnimationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Get the screen size to help with proper scaling
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.height < 600;
    
    return Scaffold(
      body: Stack(
        children: [
          // Animated Background
          AnimatedBackground(
            animationController: _animationController,
            isDarkMode: isDarkMode,
          ),
          
          // Subtle Star Field
          _buildStarField(isDarkMode),
          
          // Music Visualizer Effect
          MusicVisualizer(
            animationController: _animationController,
            isDarkMode: isDarkMode,
          ),
          
          // Content with Animation
          SafeArea(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: child,
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Spacer(flex: isSmallScreen ? 1 : 2),
                        
                        const SizedBox(height: 24),
                        
                        // Main content container
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24),
                            color: (isDarkMode ? Colors.black : Colors.white).withOpacity(0.15),
                            border: Border.all(
                              color: (isDarkMode ? Colors.white : Colors.black).withOpacity(0.08),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: (isDarkMode ? Colors.black : Colors.white).withOpacity(0.1),
                                blurRadius: 20,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(24),
                            child: BackdropFilter(
                              filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isSmallScreen ? 20 : 24,
                                  vertical: isSmallScreen ? 24 : 28
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: [
                                    // Title text with refined typography
                                    Center(
                                      child: RichText(
                                        textAlign: TextAlign.center,
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: 'Welcome to ',
                                              style: TextStyle(
                                                color: isDarkMode ? Colors.white.withOpacity(0.95) : Colors.black87,
                                                fontSize: 24,
                                                fontWeight: FontWeight.w500,
                                                letterSpacing: 0.2,
                                                height: 1.2,
                                              ),
                                            ),
                                            TextSpan(
                                              text: 'BOPMaps',
                                              style: TextStyle(
                                                color: isDarkMode ? Colors.white : Colors.black87,
                                                fontSize: 24,
                                                fontWeight: FontWeight.w500,
                                                letterSpacing: 0.2,
                                                height: 1.2,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    
                                    const SizedBox(height: 12),
                                    
                                    Center(
                                      child: Text(
                                        'Sign in with your music service',
                                        style: TextStyle(
                                          color: (isDarkMode ? Colors.white : Colors.black).withOpacity(0.65),
                                          fontSize: 15,
                                          fontWeight: FontWeight.w400,
                                          letterSpacing: 0.2,
                                          height: 1.3,
                                        ),
                                      ),
                                    ),
                                    
                                    SizedBox(height: isSmallScreen ? 28 : 32),
                                    
                                    // Login Buttons with enhanced shadows
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(22),
                                        boxShadow: [
                                          BoxShadow(
                                            color: const Color(0xFF1DB954).withOpacity(0.25),
                                            blurRadius: 12,
                                            offset: const Offset(0, 4),
                                            spreadRadius: -2,
                                          ),
                                        ],
                                      ),
                                      child: _buildSpotifyButton(),
                                    ),
                                    
                                    const SizedBox(height: 16),
                                    
                                    // Apple Music button with enhanced design
                                    Container(
                                      decoration: BoxDecoration(
                                        gradient: const LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: [
                                            Color(0xFF2C2C2E),
                                            Color(0xFF1C1C1E),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(22),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withOpacity(0.2),
                                            blurRadius: 12,
                                            offset: const Offset(0, 4),
                                            spreadRadius: -2,
                                          ),
                                        ],
                                      ),
                                      child: AuthButton(
                                        text: 'Continue with Apple Music',
                                        onPressed: _handleAppleMusicSignIn,
                                        isLoading: _isAppleLoading,
                                        backgroundColor: Colors.transparent,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        contentPadding: const EdgeInsets.only(left: 24, right: 20),
                                        icon: Image.asset(
                                          'assets/icons/apple_music.png',
                                          height: 18,
                                          width: 18,
                                        ),
                                      ),
                                    ),
                                    
                                    // Footer with refined spacing
                                    const SizedBox(height: 32),
                                    const AuthFooter(),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        
                        Spacer(flex: isSmallScreen ? 2 : 3),
                        
                        SizedBox(height: isSmallScreen ? 20 : 30),
                      ],
                    );
                  }
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSpotifyButton() {
    return AuthButton(
      text: 'Continue with Spotify',
      onPressed: _handleSpotifySignIn,
      isLoading: _isSpotifyLoading,
      backgroundColor: const Color(0xFF1DB954), // Spotify green
      mainAxisAlignment: MainAxisAlignment.start,
      contentPadding: const EdgeInsets.only(left: 0, right: 20),
      icon: Image.asset(
        'assets/icons/spotify.png',
        height: 18,
        width: 18,
      ),
    );
  }
  
  Future<void> _handleSpotifySignIn() async {
    // Debounce: Only allow once every 2 seconds
    final now = DateTime.now();
    if (_lastSpotifySignIn != null && now.difference(_lastSpotifySignIn!) < const Duration(seconds: 2)) {
      return;
    }
    _lastSpotifySignIn = now;
    
    if (_isSpotifyLoading) return;
    
    setState(() => _isSpotifyLoading = true);
    
    try {
      // Check if user is already authenticated with existing tokens
      final spotifyWebApi = SpotifyWebApiService();
      final isAlreadyAuthenticated = await spotifyWebApi.isAuthenticated();
      
      if (isAlreadyAuthenticated) {
        // User already has valid tokens, try to connect with existing authentication
        final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
        final connected = await spotifyProvider.connect();
        
        if (connected) {
          // Load user profile to complete the authentication state
          await spotifyProvider.loadUserProfile();
          
          // Get real access token and authenticate with backend
          try {
            final profile = spotifyProvider.userProfile;
            if (profile != null) {
              // Check if user exists in backend
              final userExists = await _checkSpotifyUserExists(profile['email']);
              
              if (userExists) {
                if (!mounted) return;
                Navigator.pushReplacementNamed(context, RouteConstants.map);
                return;
              } else {
                if (!mounted) return;
                Navigator.pushReplacementNamed(context, RouteConstants.onboardingSpotify);
                return;
              }
            }
          } catch (e) {
            // Still proceed to map since Spotify connection is successful
            if (!mounted) return;
            Navigator.pushReplacementNamed(context, RouteConstants.map);
            return;
          }
        }
      }
      
      // If we reach here, either no existing auth or connection failed - proceed with full auth flow
      final success = await AuthService.handleSpotifySignIn(context);
      
      if (!mounted) return;
      
      if (success) {
        Navigator.pushReplacementNamed(context, RouteConstants.map);
      } else {
        // Check if this is a new user (AuthService returned false for new user)
        // Get Spotify profile to navigate to onboarding
        try {
          final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
          
          // Try to load user profile if not already loaded
          if (spotifyProvider.userProfile == null) {
            await spotifyProvider.loadUserProfile();
            
            // Wait a bit more to ensure profile is properly stored
            await Future.delayed(const Duration(milliseconds: 500));
          }
          
          final profile = spotifyProvider.userProfile;
          
          if (profile != null) {
            Navigator.pushReplacementNamed(context, RouteConstants.onboardingSpotify);
            return;
          } else {
            // If we can't get profile, throw error
            throw Exception('Spotify authentication failed');
          }
        } catch (e) {
          // If we can't get profile, throw error
          throw Exception('Spotify authentication failed');
        }
      }
    } catch (e) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error connecting to Spotify: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: _handleSpotifySignIn,
          ),
        ),
      );
    } finally {
      if (mounted) {
        setState(() => _isSpotifyLoading = false);
      }
    }
  }

  // Helper method to check if Spotify user exists in backend
  Future<bool> _checkSpotifyUserExists(String? email) async {
    if (email == null || email.isEmpty) {
      return false;
    }
    
    try {
      final apiClient = ApiClient();
      final response = await apiClient.post(
        '/auth/check-email/',
        body: {'email': email},
        requiresAuth: false,
      );
      
      return response['exists'] == true;
    } catch (e) {
      return false;
    }
  }

  Future<void> _handleAppleMusicSignIn() async {
    if (_isAppleLoading) return;
    
    setState(() => _isAppleLoading = true);
    
    try {
      // Check if user is already authenticated with existing tokens
      final appleMusicAuth = AppleMusicAuthService();
      await appleMusicAuth.initialize();
      
      if (appleMusicAuth.isAuthenticated) {
        // User already has valid tokens, get profile data
        final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        final connected = await appleMusicProvider.connect();
        
        if (connected && mounted) {
          // Navigate to onboarding with Apple Music data
          Navigator.pushReplacementNamed(
            context,
            RouteConstants.onboarding,
            arguments: {
              'email': '', // Apple Music doesn't provide email
              'username': 'Apple Music User',
              'profile_pic': null,
              'source': 'apple_music',
            },
          );
          return;
        }
      }
      
      // If no existing auth or connection failed, proceed with full auth flow
      final success = await AuthService.handleAppleMusicSignIn(context);
      
      if (!mounted) return;
      
      if (success) {
        Navigator.pushNamed(
          context,
          RouteConstants.onboarding,
          arguments: {
            'email': '', // Apple Music doesn't provide email
            'username': 'Apple Music User',
            'profile_pic': null,
            'source': 'apple_music',
          },
        );
      } else {
        throw Exception('Apple Music authentication failed');
      }
    } catch (e) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error connecting to Apple Music: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: _handleAppleMusicSignIn,
          ),
        ),
      );
    } finally {
      if (mounted) {
        setState(() => _isAppleLoading = false);
      }
    }
  }

  Widget _buildStarField(bool isDarkMode) {
    return Positioned.fill(
      child: CustomPaint(
        painter: StarFieldPainter(
          animationController: _animationController,
          isDarkMode: isDarkMode,
        ),
      ),
    );
  }
}

class StarFieldPainter extends CustomPainter {
  final AnimationController animationController;
  final bool isDarkMode;
  
  StarFieldPainter({
    required this.animationController,
    required this.isDarkMode,
  }) : super(repaint: animationController);
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;
    
    // Create a subtle star field with different sizes and opacities
    final stars = _generateStars(size);
    
    for (final star in stars) {
      paint.color = isDarkMode 
        ? Colors.white.withOpacity(star.opacity * 0.6) 
        : Colors.black.withOpacity(star.opacity * 0.15);
      
      // Add subtle twinkling effect
      final twinkle = math.sin(animationController.value * 2 * math.pi * star.twinkleSpeed) * 0.3 + 0.7;
      paint.color = paint.color.withOpacity(paint.color.opacity * twinkle);
      
      // Draw star as a small circle or diamond shape
      if (star.size > 1.5) {
        _drawStar(canvas, paint, star.position, star.size);
      } else {
        canvas.drawCircle(star.position, star.size, paint);
      }
    }
  }
  
  void _drawStar(Canvas canvas, Paint paint, Offset position, double size) {
    final path = Path();
    const points = 4; // 4-pointed star (diamond-like)
    const angle = math.pi / points;
    
    for (int i = 0; i < points * 2; i++) {
      final currentAngle = i * angle;
      final radius = i.isEven ? size : size * 0.4;
      final x = position.dx + radius * math.cos(currentAngle);
      final y = position.dy + radius * math.sin(currentAngle);
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }
  
  List<_Star> _generateStars(Size size) {
    final stars = <_Star>[];
    final random = math.Random(42); // Fixed seed for consistent placement
    
    // Generate stars with different densities in different areas
    for (int i = 0; i < 80; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      
      // Vary size and opacity based on position
      final distanceFromCenter = ((x - size.width / 2).abs() + (y - size.height / 2).abs()) / (size.width + size.height);
      final baseOpacity = 0.2 + (1.0 - distanceFromCenter) * 0.6;
      
      stars.add(_Star(
        position: Offset(x, y),
        size: random.nextDouble() * 2.5 + 0.5, // 0.5 to 3.0
        opacity: (baseOpacity * (0.3 + random.nextDouble() * 0.7)).clamp(0.1, 1.0),
        twinkleSpeed: 0.5 + random.nextDouble() * 1.5, // Different twinkling speeds
      ));
    }
    
    return stars;
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class _Star {
  final Offset position;
  final double size;
  final double opacity;
  final double twinkleSpeed;
  
  _Star({
    required this.position,
    required this.size,
    required this.opacity,
    required this.twinkleSpeed,
  });
}