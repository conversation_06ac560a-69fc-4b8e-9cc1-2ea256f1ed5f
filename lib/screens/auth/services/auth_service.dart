import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../../providers/auth_provider.dart';
import '../../../providers/spotify_provider.dart';
import '../../../services/api/api_client.dart';
import '../../../config/constants.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../../utils/shared_preferences_storage.dart';
import '../../../services/music/apple_music_auth_service.dart';
import 'package:music_kit/music_kit.dart';

import '../../../services/music/spotify_web_api_service.dart';

class AuthService {
  static final ApiClient _apiClient = ApiClient();
  static final SharedPreferencesStorage _prefs = SharedPreferencesStorage.instance;

  // Key for storing the selected music service
  static const String _selectedServiceKey = 'selected_music_service';

  static Future<bool> handleSpotifySignIn(BuildContext context) async {
    print('DEBUG: handleSpotifySignIn called');
    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      if (kDebugMode) {
        print('🎵 Starting Spotify authentication with Web API');
      }

      print('DEBUG: About to call spotifyProvider.authenticate');
      // Step 1: Authenticate using Spotify Web API (PKCE flow)
      final authSuccess = await spotifyProvider.authenticate();
      print('DEBUG: spotifyProvider.authenticate returned: $authSuccess');

      if (!authSuccess) {
        if (kDebugMode) {
          print('❌ Spotify Web API authentication failed');
        }
        return false;
      }

      if (kDebugMode) {
        print('✅ Spotify Web API authentication successful');
      }

      // 🔧 ENHANCEMENT: Add delay to ensure token storage is complete
      print('DEBUG: Waiting 1000ms for token storage to complete');
      await Future.delayed(const Duration(milliseconds: 1000));

      // Wait for a short duration to ensure proper state transition
      print('DEBUG: Waiting additional 500ms for state transition');
      await Future.delayed(const Duration(milliseconds: 500));

      // Get user profile from Spotify with retry mechanism
      Map<String, dynamic>? userProfile;
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries && userProfile == null) {
        try {
          print('DEBUG: Attempting to load user profile, attempt ${retryCount + 1}');
          userProfile = await spotifyProvider.loadUserProfile().timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              if (kDebugMode) {
                print('⚠️ Profile loading attempt ${retryCount + 1} timed out');
              }
              return null;
            },
          );
          
          if (userProfile != null) {
            if (kDebugMode) {
              print('✅ Successfully loaded user profile on attempt ${retryCount + 1}');
            }
            break;
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Error loading profile on attempt ${retryCount + 1}: $e');
          }
        }
        
        retryCount++;
        if (retryCount < maxRetries) {
          print('DEBUG: Waiting 1s before next profile load attempt');
          await Future.delayed(const Duration(seconds: 1));
        }
      }

      if (userProfile == null) {
        print('DEBUG: Failed to load user profile after $maxRetries attempts');
        if (kDebugMode) {
          print('❌ Failed to load user profile after $maxRetries attempts');
        }
        return false;
      }

      // 🚀 ENHANCEMENT: Get the real access token from SpotifyWebApiService
      String? accessToken;
      try {
        if (kDebugMode) {
          print('🔑 Getting real access token from SpotifyWebApiService');
        }
        
        // Get the real access token from the web API service
        final webApiService = SpotifyWebApiService();
        accessToken = await webApiService.getAccessToken();
        
        if (accessToken == null || accessToken.isEmpty) {
          if (kDebugMode) {
            print('❌ Failed to get access token from SpotifyWebApiService');
          }
          return false;
        }
        
        if (kDebugMode) {
          print('✅ Successfully retrieved access token from SpotifyWebApiService');
          print('🔑 Token length: ${accessToken.length}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error getting access token from SpotifyWebApiService: $e');
        }
        return false;
      }

      // Send authentication data to backend
      try {
        if (kDebugMode) {
          print('📡 Sending authentication data to backend');
        }
        print('DEBUG: Sending authentication data to backend');
        
        final response = await _apiClient.post(
          AppConstants.spotifyAuthEndpoint,
          body: {
            'access_token': accessToken, // Now using the real access token
            'user_profile': {
              'spotify_id': userProfile['id'],
              'display_name': userProfile['display_name'],
              'email': userProfile['email'],
              'profile_pic': userProfile['images']?.isNotEmpty == true ? userProfile['images'][0]['url'] : null,
            }
          },
          requiresAuth: false,
        );
        print('DEBUG: Backend authentication response: $response');

        if (kDebugMode) {
          print('✅ Backend authentication response: $response');
        }

        // Check if we have the auth token and user data
        if (response['auth_token'] != null && response['user'] != null) {
          print('DEBUG: Logging in with backend token');
          // Use the backend-provided tokens for authentication
          final loginSuccess = await authProvider.login(
            email: response['user']['email'] ?? userProfile['email'],
            token: response['auth_token'],
            refreshToken: response['refresh_token'] ?? response['auth_token'], // Fallback to auth_token if no refresh token
            user: {
              'id': response['user']['id'],
              'username': response['user']['display_name'] ?? response['user']['name'],
              'email': response['user']['email'],
              'profile_pic': userProfile['images']?.isNotEmpty == true ? userProfile['images'][0]['url'] : null,
              'bio': response['user']['bio'] ?? 'Spotify music enthusiast 🎵',
              'is_verified': true,
              'favorite_genres': [],
              'spotify_connected': response['user']['spotify_connected'] ?? true,
              'created_at': DateTime.now().toIso8601String(),
            }
          );
          print('DEBUG: Login with backend token result: $loginSuccess');

          if (loginSuccess && authProvider.isAuthenticated) {
            if (kDebugMode) {
              print('✅ Authentication successful with backend token');
            }
            
            // Store selected service preference
            await _prefs.write(key: _selectedServiceKey, value: 'spotify');
            
            print('DEBUG: handleSpotifySignIn completed successfully');
            return true;
          }
        }

        // Check if backend indicates user doesn't exist (new user)
        if (response['user_exists'] == false || response['auth_token'] == null) {
          if (kDebugMode) {
            print('🆕 Backend indicates new user, should go to onboarding');
          }
          
          // Ensure the user profile is stored in SpotifyProvider for onboarding
          if (userProfile != null) {
            if (kDebugMode) {
              print('💾 Storing user profile in SpotifyProvider for onboarding');
            }
            // The profile is already loaded in the SpotifyProvider during the loadUserProfile() call
            // No additional action needed as it's already stored
          }
          
          // For new users, we don't want to authenticate locally
          // The login screen will handle navigation to onboarding
          return false;
        }

        if (kDebugMode) {
          print('⚠️ Backend authentication failed, falling back to local auth');
        }
        print('DEBUG: Backend authentication failed, falling back to local auth');

        // Fallback to local authentication if backend fails
        final fallbackSuccess = await authProvider.simulateLogin(
          userId: userProfile['id'] ?? 'spotify_user_${DateTime.now().millisecondsSinceEpoch}',
          name: userProfile['display_name'] ?? 'Spotify User',
          email: userProfile['email'] ?? '<EMAIL>',
          profilePic: userProfile['images']?.isNotEmpty == true ? userProfile['images'][0]['url'] : null,
          bio: 'Spotify music enthusiast 🎵',
          authToken: accessToken, // Use the real access token for local auth too
        );
        print('DEBUG: Fallback local login result: $fallbackSuccess');

        if (fallbackSuccess) {
          // Store selected service preference
          await _prefs.write(key: _selectedServiceKey, value: 'spotify');
        }

        print('DEBUG: handleSpotifySignIn completed with fallback');
        return fallbackSuccess;

      } catch (e) {
        if (kDebugMode) {
          print('❌ Backend authentication error: $e');
          print('⚠️ Falling back to local authentication');
        }
        print('DEBUG: Backend authentication error: $e, falling back to local authentication');

        // Fallback to local authentication if backend fails
        final fallbackResult = await authProvider.simulateLogin(
          userId: userProfile['id'] ?? 'spotify_user_${DateTime.now().millisecondsSinceEpoch}',
          name: userProfile['display_name'] ?? 'Spotify User',
          email: userProfile['email'] ?? '<EMAIL>',
          profilePic: userProfile['images']?.isNotEmpty == true ? userProfile['images'][0]['url'] : null,
          bio: 'Spotify music enthusiast 🎵',
          authToken: accessToken, // Use the real access token for local auth too
        );
        print('DEBUG: Fallback local login result: $fallbackResult');
        
        if (fallbackResult) {
          // Store selected service preference
          await _prefs.write(key: _selectedServiceKey, value: 'spotify');
        }
        
        print('DEBUG: handleSpotifySignIn completed with fallback (error)');
        return fallbackResult;
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Spotify authentication error: $e');
      }
      print('DEBUG: handleSpotifySignIn error: $e');
      return false;
    }
  }

  static Future<bool> handleAppleMusicSignIn(BuildContext context) async {
    print('DEBUG: handleAppleMusicSignIn called');
    
    try {
      if (kDebugMode) {
        print('🍎 Starting Apple Music authentication with MusicKit');
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      // Step 1: Initialize and get authorization from Apple Music
      final appleMusicAuth = AppleMusicAuthService();
      await appleMusicAuth.initialize();
      
      print('DEBUG: About to request Apple Music authorization');
      final success = await appleMusicAuth.requestAuthorization();
      print('DEBUG: Apple Music authorization returned: $success');
      
      if (!success) {
        if (kDebugMode) {
          print('❌ Apple Music authorization failed');
        }
        return false;
      }

      final userToken = appleMusicAuth.currentUserToken;
      if (userToken == null || userToken.isEmpty) {
        if (kDebugMode) {
          print('❌ No Apple Music user token available');
        }
        return false;
      }

      if (kDebugMode) {
        print('✅ Received Apple Music user token');
      }

      // Step 2: Get developer token
      final musicKit = MusicKit();
      final developerToken = await musicKit.requestDeveloperToken();
      
      if (developerToken.isEmpty) {
        if (kDebugMode) {
          print('❌ No Apple Music developer token available');
        }
        return false;
      }

      // Step 3: Create user profile data (email will be collected in onboarding)
      final userProfile = {
        'id': appleMusicAuth.currentUserId ?? 'apple_user_${DateTime.now().millisecondsSinceEpoch}',
        'display_name': 'Apple Music User',
        'email': '<EMAIL>', // Placeholder email, real email will be collected in onboarding
      };

      print('DEBUG: User profile created: $userProfile');

      // Step 5: Send authentication data to backend
      try {
        if (kDebugMode) {
          print('📡 Sending Apple Music authentication data to backend');
        }
        print('DEBUG: Sending Apple Music authentication data to backend');
        
        final response = await _apiClient.post(
          AppConstants.appleMusicAuthEndpoint,
          body: {
            // Use Apple's official parameter names from MusicKit documentation
            'userToken': userToken, // MusicKit user token (primary)
            'music_user_token': userToken, // Alternative parameter name for backward compatibility
            'developerToken': developerToken, // Apple Music developer JWT token
            'developer_token': developerToken, // Alternative parameter name
            'user_profile': {
              'id': userProfile['id'],
              'display_name': userProfile['display_name'],
              'email': userProfile['email'],
            }
          },
          requiresAuth: false,
        );
        print('DEBUG: Backend Apple Music authentication response: $response');

        if (kDebugMode) {
          print('✅ Backend Apple Music authentication response: $response');
        }

        // Check if we have the auth token and user data
        if (response['auth_token'] != null && response['user'] != null) {
          print('DEBUG: Logging in with backend token');
          // Use the backend-provided tokens for authentication
          final loginSuccess = await authProvider.login(
            email: response['user']['email'] ?? userProfile['email'],
            token: response['auth_token'],
            refreshToken: response['refresh_token'] ?? response['auth_token'], // Fallback to auth_token if no refresh token
            user: {
              'id': response['user']['id'],
              'username': response['user']['display_name'] ?? response['user']['name'],
              'email': response['user']['email'],
              'profile_pic': null, // Apple Music doesn't provide profile pics via API
              'bio': response['user']['bio'] ?? 'Apple Music enthusiast 🎵',
              'is_verified': true,
              'favorite_genres': [],
              'apple_music_connected': response['user']['apple_music_connected'] ?? true,
              'created_at': DateTime.now().toIso8601String(),
            }
          );
          print('DEBUG: Login with backend token result: $loginSuccess');

          if (loginSuccess && authProvider.isAuthenticated) {
            if (kDebugMode) {
              print('✅ Apple Music authentication successful with backend token');
            }
            
            // Store selected service preference
            await _prefs.write(key: _selectedServiceKey, value: 'apple_music');
            
            print('DEBUG: handleAppleMusicSignIn completed successfully');
            return true;
          }
        }

        if (kDebugMode) {
          print('⚠️ Backend Apple Music authentication failed, falling back to local auth');
        }
        print('DEBUG: Backend Apple Music authentication failed, falling back to local auth');

        // Fallback to local authentication if backend fails
        final fallbackResult = await authProvider.simulateLogin(
          userId: userProfile['id'] ?? 'apple_user_${DateTime.now().millisecondsSinceEpoch}',
          name: userProfile['display_name'] ?? 'Apple Music User',
          email: userProfile['email'] ?? '<EMAIL>', // Use placeholder email, real email collected in onboarding
          profilePic: null,
          bio: 'Apple Music enthusiast 🎵',
          authToken: userToken,
        );
        print('DEBUG: Fallback local login result: $fallbackResult');
        
        if (fallbackResult) {
          // Store selected service preference
          await _prefs.write(key: _selectedServiceKey, value: 'apple_music');
        }
        
        print('DEBUG: handleAppleMusicSignIn completed with fallback');
        return fallbackResult;

      } catch (e) {
        if (kDebugMode) {
          print('❌ Backend Apple Music authentication error: $e');
          print('⚠️ Falling back to local authentication');
          
          // Parse error response for better debugging
          if (e.toString().contains('400')) {
            print('💡 Hint: Invalid request parameters. Check Apple Music token format.');
            print('💡 Expected parameters: userToken, developerToken, user_profile');
          } else if (e.toString().contains('401')) {
            print('💡 Hint: Apple Music token validation failed. Check if MusicKit user token is valid.');
          } else if (e.toString().contains('403')) {
            print('💡 Hint: Apple Music authentication forbidden. Check developer token configuration.');
          } else if (e.toString().contains('422')) {
            print('💡 Hint: Validation error. Check if all required parameters are provided correctly.');
          }
        }
        print('DEBUG: Backend Apple Music authentication error: $e, falling back to local authentication');

        // Fallback to local authentication if backend fails
        final fallbackResult = await authProvider.simulateLogin(
          userId: userProfile['id'] ?? 'apple_user_${DateTime.now().millisecondsSinceEpoch}',
          name: userProfile['display_name'] ?? 'Apple Music User',
          email: userProfile['email'] ?? '<EMAIL>', // Use placeholder email, real email collected in onboarding
          profilePic: null,
          bio: 'Apple Music enthusiast 🎵',
          authToken: userToken,
        );
        print('DEBUG: Fallback local login result: $fallbackResult');
        
        if (fallbackResult) {
          // Store selected service preference
          await _prefs.write(key: _selectedServiceKey, value: 'apple_music');
          if (kDebugMode) {
            print('⚠️ Using local authentication due to backend error');
          }
        }
        
        print('DEBUG: handleAppleMusicSignIn completed with fallback (error)');
        return fallbackResult;
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Apple Music authentication error: $e');
      }
      print('DEBUG: handleAppleMusicSignIn error: $e');
      return false;
    }
  }

  static Future<bool> handleSoundCloudSignIn(BuildContext context) async {
    try {
      if (kDebugMode) {
        print('🎵 Starting SoundCloud authentication flow');
      }

      // For demonstration - replace with actual SoundCloud SDK integration
      await Future.delayed(const Duration(milliseconds: 500));
      
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final result = await authProvider.simulateLogin(
        userId: 'soundcloud_user_${DateTime.now().millisecondsSinceEpoch}',
        name: 'SoundCloud User',
        email: '<EMAIL>',
        bio: 'SoundCloud enthusiast 🎵',
        authToken: 'demo_soundcloud_token',
      );
      
      if (result) {
        // Store selected service preference
        await _prefs.write(key: _selectedServiceKey, value: 'soundcloud');
      }
      
      return result;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ SoundCloud authentication error: $e');
      }
      return false;
    }
  }

  static Future<bool> handleDemoMode(BuildContext context) async {
    try {
      if (kDebugMode) {
        print('🎵 Starting demo mode');
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final result = await authProvider.simulateLogin(
        userId: 'demo_user_${DateTime.now().millisecondsSinceEpoch}',
        name: 'Demo User',
        email: '<EMAIL>',
        bio: 'Demo user exploring BOPMaps',
        authToken: 'demo_token',
      );
      
      if (result) {
        // Store selected service preference as spotify for demo mode
        await _prefs.write(key: _selectedServiceKey, value: 'spotify');
      }
      
      return result;

    } catch (e) {
      if (kDebugMode) {
        print('❌ Demo mode error: $e');
      }
      return false;
    }
  }

  /// Get the saved selected music service
  static Future<String> getSavedSelectedService() async {
    try {
      final service = await _prefs.read(key: _selectedServiceKey);
      return service ?? 'spotify'; // Default to spotify
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error reading selected service: $e');
      }
      return 'spotify'; // Default to spotify
    }
  }

  /// Save the selected music service
  static Future<void> saveSelectedService(String service) async {
    try {
      await _prefs.write(key: _selectedServiceKey, value: service);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving selected service: $e');
      }
    }
  }
} 