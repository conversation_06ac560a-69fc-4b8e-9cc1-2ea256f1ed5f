import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/challenge_playlist_provider.dart';
import '../../providers/weekly_challenges_provider.dart';
import '../../services/api/weekly_challenges_api_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../services/music/shuffle_service.dart';
import 'components/challenge_header.dart';
import 'components/sort_filter_bar.dart';
import 'components/song_entry_card.dart';
import 'components/empty_state.dart';
import 'components/skeleton_loading.dart';
import '../explore/components/drop_song_screen.dart';

class ChallengePlaylistScreen extends StatefulWidget {
  final String challengeId;
  final String title;
  final String description;
  final Duration timeLeft;
  final int requiredParticipants;
  final List<Color> gradientColors;

  const ChallengePlaylistScreen({
    Key? key,
    required this.challengeId,
    required this.title,
    required this.description,
    required this.timeLeft,
    required this.requiredParticipants,
    required this.gradientColors,
  }) : super(key: key);

  @override
  State<ChallengePlaylistScreen> createState() => _ChallengePlaylistScreenState();
}

class _ChallengePlaylistScreenState extends State<ChallengePlaylistScreen> with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  bool _isHeaderVisible = true;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;
  late ChallengePlaylistProvider _playlistProvider;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    
    _fabAnimation = CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeInOut,
    );

    // Initialize provider immediately
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      _playlistProvider = Provider.of<ChallengePlaylistProvider>(context, listen: false);
      _loadEntries();
    });
  }

  Future<void> _loadEntries() async {
    if (!mounted) return;
    await _playlistProvider.loadEntries(widget.challengeId);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final isHeaderVisible = _scrollController.offset <= 100;
    if (isHeaderVisible != _isHeaderVisible) {
      setState(() {
        _isHeaderVisible = isHeaderVisible;
      });
      
      if (!isHeaderVisible) {
        _fabAnimationController.forward();
      } else {
        _fabAnimationController.reverse();
      }
    }
  }

  void _navigateToDropSong(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DropSongScreen(
          challengeId: widget.challengeId,
          challengeTitle: widget.title,
          description: widget.description,
          gradientColors: widget.gradientColors,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final apiService = context.read<ApiService>();
    final authService = context.read<AuthService>();
    
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: theme.scaffoldBackgroundColor,
        systemNavigationBarIconBrightness: theme.brightness == Brightness.dark 
            ? Brightness.light 
            : Brightness.dark,
      ),
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: ChangeNotifierProvider(
          create: (context) {
            final provider = ChallengePlaylistProvider(
              WeeklyChallengesApiService(apiService, authService),
            );
            // Load entries immediately when provider is created
            Future.microtask(() => provider.loadEntries(widget.challengeId));
            return provider;
          },
          child: Consumer<ChallengePlaylistProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading && provider.entries.isEmpty) {
                return ChallengesSkeleton(
                  gradientColors: widget.gradientColors,
                );
              }

              return RefreshIndicator(
                onRefresh: () => provider.loadEntries(widget.challengeId),
                color: widget.gradientColors.first,
                backgroundColor: theme.colorScheme.surface,
                displacement: 40,
                edgeOffset: 20,
                child: CustomScrollView(
                  controller: _scrollController,
                  physics: const AlwaysScrollableScrollPhysics(
                    parent: BouncingScrollPhysics(),
                  ),
                  slivers: [
                    // Challenge header
                    SliverToBoxAdapter(
                      child: ChallengeHeader(
                        title: widget.title,
                        description: widget.description,
                        timeLeft: widget.timeLeft,
                        totalParticipants: provider.entries.length,
                        requiredParticipants: widget.requiredParticipants,
                        gradientColors: widget.gradientColors,
                        onShuffle: () async {
                          // Provide immediate haptic feedback
                          HapticFeedback.mediumImpact();
                          
                          try {
                            // Get music providers
                            final spotifyProvider = context.read<SpotifyProvider>();
                            final appleMusicProvider = context.read<AppleMusicProvider>();
                            
                            // Check if any music service is connected
                            if (!spotifyProvider.isConnected && !appleMusicProvider.isConnected) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: const Text('Please connect Spotify or Apple Music to shuffle and play'),
                                  backgroundColor: widget.gradientColors.first,
                                  behavior: SnackBarBehavior.floating,
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                              return;
                            }
                            
                            // Show loading feedback
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Row(
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    const Text('Shuffling and starting playback...'),
                                  ],
                                ),
                                backgroundColor: widget.gradientColors.first,
                                behavior: SnackBarBehavior.floating,
                                duration: const Duration(seconds: 3),
                              ),
                            );
                            
                            // Actually shuffle and play the music
                            await provider.shuffleAndPlay(
                              spotifyProvider: spotifyProvider,
                              appleMusicProvider: appleMusicProvider,
                              mode: ShuffleMode.random, // Could be made configurable
                            );
                            
                            // Clear loading and show success
                            ScaffoldMessenger.of(context).clearSnackBars();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Row(
                                  children: [
                                    Icon(
                                      Icons.shuffle_rounded,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 12),
                                    Text('Successfully shuffled ${provider.entries.length} songs!'),
                                  ],
                                ),
                                backgroundColor: widget.gradientColors.first,
                                behavior: SnackBarBehavior.floating,
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          } catch (e) {
                            // Clear loading and show error
                            ScaffoldMessenger.of(context).clearSnackBars();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Row(
                                  children: [
                                    Icon(
                                      Icons.error_outline,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text('Failed to shuffle: ${e.toString()}'),
                                    ),
                                  ],
                                ),
                                backgroundColor: Colors.red.shade600,
                                behavior: SnackBarBehavior.floating,
                                duration: const Duration(seconds: 4),
                              ),
                            );
                            
                            // Fall back to just shuffling the UI list
                            provider.shuffleEntries();
                          }
                        },
                      ),
                    ),

                    // Sort and filter bar
                    SliverPersistentHeader(
                      pinned: true,
                      delegate: _SliverAppBarDelegate(
                        minHeight: 72,
                        maxHeight: 72,
                        child: SortFilterBar(gradientColors: widget.gradientColors),
                      ),
                    ),

                    // Content
                    if (provider.entries.isEmpty && !provider.isLoading)
                      SliverFillRemaining(
                        hasScrollBody: false,
                        child: ChallengeEmptyState(
                          showFriendsOnly: provider.showFriendsOnly,
                          description: widget.description,
                          gradientColors: widget.gradientColors,
                          onDropSong: () => _navigateToDropSong(context),
                        ),
                      )
                    else
                      SliverPadding(
                        padding: const EdgeInsets.only(bottom: 100),
                        sliver: SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final entry = provider.entries[index];
                              return SongEntryCard(
                                entry: entry,
                                gradientColors: widget.gradientColors,
                              );
                            },
                            childCount: provider.entries.length,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
        floatingActionButton: ScaleTransition(
          scale: _fabAnimation,
          child: FloatingActionButton.extended(
            onPressed: () => _navigateToDropSong(context),
            backgroundColor: widget.gradientColors.first,
            label: const Text('Drop a Song'),
            icon: const Icon(Icons.add_rounded),
          ),
        ),
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
} 