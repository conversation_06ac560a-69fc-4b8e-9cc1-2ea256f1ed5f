import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../models/music/song_entry.dart';
import '../../../providers/challenge_playlist_provider.dart';
import '../../../providers/spotify_provider.dart';
import '../../../models/music_track.dart';
import 'package:timeago/timeago.dart' as timeago;

class SongEntryCard extends StatelessWidget {
  final SongEntry entry;
  final List<Color> gradientColors;

  const SongEntryCard({
    Key? key,
    required this.entry,
    required this.gradientColors,
  }) : super(key: key);

  MusicTrack _createMusicTrack() {
    return MusicTrack(
      id: entry.songId,
      title: entry.title,
      artist: entry.artist,
      albumArt: entry.albumArt,
      uri: entry.spotifyUri,
      durationMs: 0,
      url: 'https://open.spotify.com/track/${entry.songId.split(':').last}',
      service: 'spotify',
      serviceType: 'spotify',
      genres: [],
      explicit: false,
      popularity: 0,
      album: '',
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final playlistProvider = context.watch<ChallengePlaylistProvider>();
    final spotifyProvider = context.watch<SpotifyProvider>();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.onSurface.withOpacity(0.08),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            spotifyProvider.playTrack(_createMusicTrack());
          },
          borderRadius: BorderRadius.circular(16),
          splashColor: gradientColors.first.withOpacity(0.1),
          highlightColor: gradientColors.first.withOpacity(0.05),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                // Main song row
                Row(
                  children: [
                    // Album art - smaller and more compact
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        entry.albumArt,
                        width: 48,
                        height: 48,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              color: theme.colorScheme.surfaceVariant,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.music_note_rounded,
                              size: 24,
                              color: theme.colorScheme.primary.withOpacity(0.5),
                            ),
                          );
                        },
                      ),
                    ),
                    
                    const SizedBox(width: 12),
                    
                    // Song info - expanded to take more space
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Song title
                          Text(
                            entry.title,
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              fontSize: 15,
                              height: 1.2,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          
                          const SizedBox(height: 2),
                          
                          // Artist name only
                          Text(
                            entry.artist,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                              fontSize: 13,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          
                          const SizedBox(height: 4),
                          
                          // Score and time
                          Row(
                            children: [
                              // Score indicator - more compact
                              if (entry.score != 0) ...[
                                Icon(
                                  entry.score > 0 
                                    ? Icons.trending_up_rounded 
                                    : Icons.trending_down_rounded,
                                  size: 14,
                                  color: entry.score > 0 ? Colors.green : Colors.red,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '${entry.score > 0 ? '+' : ''}${entry.score}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: entry.score > 0 ? Colors.green : Colors.red,
                                  ),
                                ),
                              ] else ...[
                                Icon(
                                  Icons.remove_rounded,
                                  size: 14,
                                  color: theme.colorScheme.onSurface.withOpacity(0.4),
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '0',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                                  ),
                                ),
                              ],
                              
                              const Spacer(),
                              
                              // Time ago
                              Text(
                                timeago.format(entry.droppedAt),
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                  fontSize: 11,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(width: 12),
                    
                    // Compact voting buttons
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _CompactVoteButton(
                          icon: Icons.keyboard_arrow_up_rounded,
                          count: entry.upvotes,
                          isSelected: entry.userVote == true,
                          onPressed: () {
                            HapticFeedback.lightImpact();
                            playlistProvider.vote(entry.id, true);
                          },
                          color: Colors.green,
                        ),
                        
                        const SizedBox(width: 8),
                        
                        _CompactVoteButton(
                          icon: Icons.keyboard_arrow_down_rounded,
                          count: entry.downvotes,
                          isSelected: entry.userVote == false,
                          onPressed: () {
                            HapticFeedback.lightImpact();
                            playlistProvider.vote(entry.id, false);
                          },
                          color: Colors.red,
                        ),
                      ],
                    ),
                  ],
                ),
                
                // Divider
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Divider(
                    height: 1,
                    color: theme.colorScheme.onSurface.withOpacity(0.08),
                  ),
                ),
                
                // User profile row
                Row(
                  children: [
                    // Profile picture
                    GestureDetector(
                      onTap: () {
                        // TODO: Navigate to user profile in the future
                        HapticFeedback.lightImpact();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: entry.isFromFriend 
                              ? gradientColors.first.withOpacity(0.3)
                              : theme.colorScheme.onSurface.withOpacity(0.1),
                            width: entry.isFromFriend ? 2 : 1,
                          ),
                        ),
                        child: CircleAvatar(
                          radius: 16,
                          backgroundImage: entry.dropperAvatarUrl != null
                              ? NetworkImage(entry.dropperAvatarUrl!)
                              : null,
                          backgroundColor: entry.dropperAvatarUrl == null
                              ? (entry.isFromFriend 
                                  ? gradientColors.first.withOpacity(0.1)
                                  : theme.colorScheme.surfaceVariant)
                              : null,
                          child: entry.dropperAvatarUrl == null
                              ? Icon(
                                  Icons.person_rounded,
                                  size: 18,
                                  color: entry.isFromFriend 
                                    ? gradientColors.first
                                    : theme.colorScheme.onSurfaceVariant,
                                )
                              : null,
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 12),
                    
                    // User info
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          // TODO: Navigate to user profile in the future
                          HapticFeedback.lightImpact();
                        },
                        child: Row(
                          children: [
                            Text(
                              'Dropped by ',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withOpacity(0.6),
                                fontSize: 13,
                              ),
                            ),
                            Text(
                              entry.dropperName,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: entry.isFromFriend 
                                  ? gradientColors.first 
                                  : theme.colorScheme.onSurface,
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            if (entry.isFromFriend) ...[
                              const SizedBox(width: 6),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: gradientColors.first.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: gradientColors.first.withOpacity(0.2),
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.people_rounded,
                                      size: 10,
                                      color: gradientColors.first,
                                    ),
                                    const SizedBox(width: 3),
                                    Text(
                                      'Friend',
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w600,
                                        color: gradientColors.first,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _CompactVoteButton extends StatelessWidget {
  final IconData icon;
  final int count;
  final bool isSelected;
  final VoidCallback onPressed;
  final Color color;

  const _CompactVoteButton({
    required this.icon,
    required this.count,
    required this.isSelected,
    required this.onPressed,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(12),
        splashColor: color.withOpacity(0.1),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: isSelected ? color.withOpacity(0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected 
                ? color.withOpacity(0.3) 
                : theme.colorScheme.onSurface.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 16,
                color: isSelected
                    ? color
                    : theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              if (count > 0) ...[
                const SizedBox(width: 3),
                Text(
                  count.toString(),
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected
                        ? color
                        : theme.colorScheme.onSurface.withOpacity(0.6),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
} 