import 'package:flutter/material.dart';
import '../../../providers/challenge_playlist_provider.dart';
import '../../../services/auth_service.dart';
import '../../../services/api_service.dart';
import 'package:provider/provider.dart';

class SortFilterBar extends StatefulWidget {
  final List<Color> gradientColors;
  
  const SortFilterBar({
    Key? key,
    required this.gradientColors,
  }) : super(key: key);

  @override
  State<SortFilterBar> createState() => _SortFilterBarState();
}

class _SortFilterBarState extends State<SortFilterBar> {
  bool _isSchoolVerified = false;
  String? _schoolName;

  @override
  void initState() {
    super.initState();
    _checkSchoolVerification();
  }

  Future<void> _checkSchoolVerification() async {
    try {
      final authService = AuthService(context.read<ApiService>());
      final schoolInfo = await authService.getQuickSchoolInfo();
      
      if (mounted) {
        setState(() {
          _isSchoolVerified = schoolInfo['is_verified'] ?? false;
          _schoolName = schoolInfo['school_name'];
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = context.watch<ChallengePlaylistProvider>();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // Sort dropdown - made more compact
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: theme.shadowColor.withOpacity(0.04),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Theme(
                data: Theme.of(context).copyWith(
                  dividerColor: Colors.transparent,
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<SortType>(
                    value: provider.sortType,
                    isDense: true,
                    isExpanded: true,
                    icon: Icon(
                      Icons.keyboard_arrow_down_rounded,
                      color: widget.gradientColors.first,
                      size: 20,
                    ),
                    items: [
                      DropdownMenuItem(
                        value: SortType.main,
                        child: Row(
                          children: [
                            Icon(
                              Icons.trending_up_rounded,
                              size: 18,
                              color: widget.gradientColors.first,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              'Top',
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                      ),
                      DropdownMenuItem(
                        value: SortType.recent,
                        child: Row(
                          children: [
                            Icon(
                              Icons.access_time_rounded,
                              size: 18,
                              color: widget.gradientColors.first,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              'Recent',
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        provider.setSortType(value);
                      }
                    },
                    dropdownColor: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(14),
                    elevation: 8,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Friends filter toggle - made smaller
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: provider.toggleFriendsOnly,
              borderRadius: BorderRadius.circular(14),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  gradient: provider.showFriendsOnly
                      ? LinearGradient(
                          colors: widget.gradientColors,
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : null,
                  color: provider.showFriendsOnly
                      ? null
                      : theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(14),
                  border: Border.all(
                    color: provider.showFriendsOnly
                        ? widget.gradientColors.first
                        : theme.colorScheme.onSurface.withOpacity(0.1),
                    width: 1,
                  ),
                  boxShadow: provider.showFriendsOnly
                      ? [
                          BoxShadow(
                            color: widget.gradientColors.first.withOpacity(0.3),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : [
                          BoxShadow(
                            color: theme.shadowColor.withOpacity(0.04),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.people_alt_rounded,
                      size: 18,
                      color: provider.showFriendsOnly
                          ? Colors.white
                          : widget.gradientColors.first,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Friends',
                      style: TextStyle(
                        color: provider.showFriendsOnly
                            ? Colors.white
                            : theme.colorScheme.onSurface,
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // School filter - only show if user is school verified
          if (_isSchoolVerified) ...[
            const SizedBox(width: 8),
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: provider.toggleSchoolOnly,
                borderRadius: BorderRadius.circular(14),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                  decoration: BoxDecoration(
                    gradient: provider.showSchoolOnly
                        ? LinearGradient(
                            colors: widget.gradientColors,
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : null,
                    color: provider.showSchoolOnly
                        ? null
                        : theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(14),
                    border: Border.all(
                      color: provider.showSchoolOnly
                          ? widget.gradientColors.first
                          : theme.colorScheme.onSurface.withOpacity(0.1),
                      width: 1,
                    ),
                    boxShadow: provider.showSchoolOnly
                        ? [
                            BoxShadow(
                              color: widget.gradientColors.first.withOpacity(0.3),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : [
                            BoxShadow(
                              color: theme.shadowColor.withOpacity(0.04),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.school_rounded,
                        size: 18,
                        color: provider.showSchoolOnly
                            ? Colors.white
                            : widget.gradientColors.first,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'School',
                        style: TextStyle(
                          color: provider.showSchoolOnly
                              ? Colors.white
                              : theme.colorScheme.onSurface,
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
} 