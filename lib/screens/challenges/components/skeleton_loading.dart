import 'package:flutter/material.dart';
import 'dart:ui';

/// Perfect skeleton loading for challenges screen
/// Mimics the exact structure with smooth shimmer animations
class ChallengesSkeleton extends StatefulWidget {
  final List<Color> gradientColors;
  
  const ChallengesSkeleton({
    Key? key,
    required this.gradientColors,
  }) : super(key: key);

  @override
  State<ChallengesSkeleton> createState() => _ChallengesSkeletonState();
}

class _ChallengesSkeletonState extends State<ChallengesSkeleton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    // Start the shimmer animation and repeat
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return CustomScrollView(
      physics: const NeverScrollableScrollPhysics(), // Disable scrolling during loading
      slivers: [
        // Skeleton Challenge Header
        SliverToBoxAdapter(
          child: _buildSkeletonHeader(context, theme),
        ),
        
        // Skeleton Sort Filter Bar
        SliverPersistentHeader(
          pinned: true,
          delegate: _SliverAppBarDelegate(
            minHeight: 72,
            maxHeight: 72,
            child: _buildSkeletonSortFilterBar(theme),
          ),
        ),
        
        // Skeleton Song Entry Cards
        SliverPadding(
          padding: const EdgeInsets.only(bottom: 100),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => _buildSkeletonSongCard(theme),
              childCount: 5, // Show 5 skeleton cards
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSkeletonHeader(BuildContext context, ThemeData theme) {
    final mediaQuery = MediaQuery.of(context);
    final topPadding = mediaQuery.padding.top;
    
    return Container(
      padding: EdgeInsets.only(
        top: topPadding + 12,
        left: 20,
        right: 20,
        bottom: 20,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: widget.gradientColors,
          stops: const [0.0, 1.0],
        ),
        borderRadius: const BorderRadius.vertical(
          bottom: Radius.circular(28),
        ),
        boxShadow: [
          BoxShadow(
            color: widget.gradientColors.first.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button and time left skeleton
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Back button skeleton
              ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.arrow_back_ios_rounded,
                      color: Colors.white.withOpacity(0.7),
                      size: 18,
                    ),
                  ),
                ),
              ),
              
              // Time left skeleton
              ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.timer_outlined,
                          color: Colors.white.withOpacity(0.7),
                          size: 14,
                        ),
                        const SizedBox(width: 6),
                        _buildShimmerContainer(
                          width: 60,
                          height: 13,
                          borderRadius: 6.5,
                          baseColor: Colors.white.withOpacity(0.3),
                          highlightColor: Colors.white.withOpacity(0.5),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Title skeleton
          _buildShimmerContainer(
            width: MediaQuery.of(context).size.width * 0.7,
            height: 24,
            borderRadius: 12,
            baseColor: Colors.white.withOpacity(0.2),
            highlightColor: Colors.white.withOpacity(0.4),
          ),
          
          const SizedBox(height: 8),
          
          // Description skeleton
          _buildShimmerContainer(
            width: MediaQuery.of(context).size.width * 0.9,
            height: 14,
            borderRadius: 7,
            baseColor: Colors.white.withOpacity(0.2),
            highlightColor: Colors.white.withOpacity(0.4),
          ),
          
          const SizedBox(height: 6),
          
          _buildShimmerContainer(
            width: MediaQuery.of(context).size.width * 0.6,
            height: 14,
            borderRadius: 7,
            baseColor: Colors.white.withOpacity(0.2),
            highlightColor: Colors.white.withOpacity(0.4),
          ),

          const SizedBox(height: 20),

          // Songs submitted section skeleton
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Songs count skeleton
                Row(
                  children: [
                    Icon(
                      Icons.music_note_rounded,
                      color: Colors.white.withOpacity(0.6),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    _buildShimmerContainer(
                      width: 130,
                      height: 16,
                      borderRadius: 8,
                      baseColor: Colors.white.withOpacity(0.2),
                      highlightColor: Colors.white.withOpacity(0.4),
                    ),
                  ],
                ),
                
                // Shuffle button skeleton
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.25),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.4),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.shuffle_rounded,
                    color: Colors.white.withOpacity(0.7),
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonSortFilterBar(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // Sort dropdown skeleton
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: theme.shadowColor.withOpacity(0.04),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.trending_up_rounded,
                    size: 18,
                    color: theme.colorScheme.onSurface.withOpacity(0.3),
                  ),
                  const SizedBox(width: 6),
                  _buildShimmerContainer(
                    width: 40,
                    height: 13,
                    borderRadius: 6.5,
                    baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                    highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.keyboard_arrow_down_rounded,
                    color: theme.colorScheme.onSurface.withOpacity(0.3),
                    size: 20,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Friends filter skeleton
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(14),
              border: Border.all(
                color: theme.colorScheme.onSurface.withOpacity(0.1),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.shadowColor.withOpacity(0.04),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.people_alt_rounded,
                  size: 18,
                  color: theme.colorScheme.onSurface.withOpacity(0.3),
                ),
                const SizedBox(width: 6),
                _buildShimmerContainer(
                  width: 45,
                  height: 14,
                  borderRadius: 7,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
              ],
            ),
          ),

          const SizedBox(width: 8),

          // School filter skeleton
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(14),
              border: Border.all(
                color: theme.colorScheme.onSurface.withOpacity(0.1),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.shadowColor.withOpacity(0.04),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.school_rounded,
                  size: 18,
                  color: theme.colorScheme.onSurface.withOpacity(0.3),
                ),
                const SizedBox(width: 6),
                _buildShimmerContainer(
                  width: 40,
                  height: 14,
                  borderRadius: 7,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonSongCard(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.onSurface.withOpacity(0.08),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            // Main song row
            Row(
              children: [
                // Album art skeleton
                _buildShimmerContainer(
                  width: 48,
                  height: 48,
                  borderRadius: 8,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
                
                const SizedBox(width: 12),
                
                // Song info skeleton
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Song title skeleton
                      _buildShimmerContainer(
                        width: double.infinity,
                        height: 15,
                        borderRadius: 7.5,
                        baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                        highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // Artist name skeleton
                      _buildShimmerContainer(
                        width: MediaQuery.of(context).size.width * 0.4,
                        height: 13,
                        borderRadius: 6.5,
                        baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                        highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                      ),
                      
                      const SizedBox(height: 6),
                      
                      // Score and time skeleton
                      Row(
                        children: [
                          _buildShimmerContainer(
                            width: 30,
                            height: 12,
                            borderRadius: 6,
                            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                          ),
                          const Spacer(),
                          _buildShimmerContainer(
                            width: 40,
                            height: 11,
                            borderRadius: 5.5,
                            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Voting buttons skeleton
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildSkeletonVoteButton(theme),
                    const SizedBox(width: 8),
                    _buildSkeletonVoteButton(theme),
                  ],
                ),
              ],
            ),
            
            // Divider
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Divider(
                height: 1,
                color: theme.colorScheme.onSurface.withOpacity(0.08),
              ),
            ),
            
            // User profile row skeleton
            Row(
              children: [
                // Profile picture skeleton
                _buildShimmerContainer(
                  width: 32,
                  height: 32,
                  borderRadius: 16,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
                
                const SizedBox(width: 12),
                
                // User info skeleton
                Expanded(
                  child: Row(
                    children: [
                      _buildShimmerContainer(
                        width: 70,
                        height: 13,
                        borderRadius: 6.5,
                        baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                        highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                      ),
                      const SizedBox(width: 6),
                      _buildShimmerContainer(
                        width: 80,
                        height: 13,
                        borderRadius: 6.5,
                        baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                        highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkeletonVoteButton(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.onSurface.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.keyboard_arrow_up_rounded,
            size: 16,
            color: theme.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(width: 3),
          _buildShimmerContainer(
            width: 15,
            height: 12,
            borderRadius: 6,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerContainer({
    required double width,
    required double height,
    required double borderRadius,
    required Color baseColor,
    required Color highlightColor,
  }) {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
            gradient: LinearGradient(
              begin: Alignment(-1.0, -0.3),
              end: Alignment(1.0, 0.3),
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                (_shimmerAnimation.value - 0.3).clamp(0.0, 1.0),
                _shimmerAnimation.value.clamp(0.0, 1.0),
                (_shimmerAnimation.value + 0.3).clamp(0.0, 1.0),
              ],
            ),
          ),
        );
      },
    );
  }
}

// Helper delegate for sticky header
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
} 