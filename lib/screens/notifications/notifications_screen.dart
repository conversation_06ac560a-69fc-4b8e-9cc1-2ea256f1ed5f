import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../config/themes.dart';
import '../../models/notification_model.dart';
import '../../providers/notification_provider.dart';
import '../../providers/auth_provider.dart';
import 'components/glassmorphic_notification_card.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  String _currentFilter = 'All';
  String _selectedCategory = 'all';
  final ScrollController _scrollController = ScrollController();
  
  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  
  // Selection mode
  bool _isSelectionMode = false;
  Set<String> _selectedNotifications = {};

  // Categories for filtering
  final List<NotificationCategoryItem> _categories = [
    NotificationCategoryItem(
      id: 'all',
      name: 'All',
      icon: Icons.notifications,
      color: Colors.blue,
    ),
    NotificationCategoryItem(
      id: 'map',
      name: 'Map & Pins',
      icon: Icons.location_on,
      color: Colors.red,
    ),
    NotificationCategoryItem(
      id: 'social',
      name: 'Friends',
      icon: Icons.people,
      color: Colors.green,
    ),
    NotificationCategoryItem(
      id: 'music',
      name: 'Music',
      icon: Icons.music_note,
      color: Colors.purple,
    ),
    NotificationCategoryItem(
      id: 'gamification',
      name: 'Challenges',
      icon: Icons.emoji_events,
      color: Colors.amber,
    ),
    NotificationCategoryItem(
      id: 'collection',
      name: 'Collections',
      icon: Icons.library_music,
      color: Colors.indigo,
    ),
    NotificationCategoryItem(
      id: 'customization',
      name: 'Skins',
      icon: Icons.palette,
      color: Colors.pink,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _scrollController.addListener(_onScroll);
    
    // Initialize notifications when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (authProvider.isAuthenticated && authProvider.currentUser?.id != null) {
        final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
        notificationProvider.initialize(authProvider.currentUser!.id.toString());
      }
    });
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _fadeController.forward();
    _slideController.forward();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      // Load more when user is 200 pixels from bottom
      final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
      
      if (notificationProvider.hasMoreData && !notificationProvider.isLoadingMore) {
        // Get current filter state
        NotificationCategory? category;
        if (_selectedCategory != 'all') {
          category = NotificationCategory.values.firstWhere(
            (c) => c.name == _selectedCategory,
            orElse: () => NotificationCategory.all,
          );
          if (category == NotificationCategory.all) category = null;
        }
        
        bool? isRead;
        switch (_currentFilter) {
          case 'Unread':
            isRead = false;
            break;
          case 'Read':
            isRead = true;
            break;
          default:
            isRead = null;
        }
        
        notificationProvider.loadMoreNotifications(
          category: category,
          isRead: isRead,
        );
      }
    }
  }

  List<NotificationModel> _getFilteredNotifications(List<NotificationModel> notifications) {
    var filtered = notifications;
    
    // Filter by category
    if (_selectedCategory != 'all') {
      final category = NotificationCategory.values.firstWhere(
        (c) => c.name == _selectedCategory,
        orElse: () => NotificationCategory.all,
      );
      if (category != NotificationCategory.all) {
        filtered = filtered.where((notification) => notification.category == category).toList();
      }
    }
    
    // Filter by read status
    switch (_currentFilter) {
      case 'Unread':
        filtered = filtered.where((n) => !n.isRead).toList();
        break;
      case 'Read':
        filtered = filtered.where((n) => n.isRead).toList();
        break;
      default:
        break;
    }
    
    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDark
                ? [
                    theme.scaffoldBackgroundColor,
                    theme.scaffoldBackgroundColor.withOpacity(0.95),
                  ]
                : [
                    theme.scaffoldBackgroundColor,
                    const Color(0xFFF8FAFC),
                  ],
          ),
        ),
        child: SafeArea(
          child: Consumer<NotificationProvider>(
            builder: (context, notificationProvider, child) {
              final notifications = notificationProvider.notifications;
              final unreadCount = notificationProvider.unreadCount;
              final isLoading = notificationProvider.isLoading;
              final error = notificationProvider.error;

              return RefreshIndicator(
                onRefresh: () async {
                  // Get current filter state for refresh
                  NotificationCategory? category;
                  if (_selectedCategory != 'all') {
                    category = NotificationCategory.values.firstWhere(
                      (c) => c.name == _selectedCategory,
                      orElse: () => NotificationCategory.all,
                    );
                    if (category == NotificationCategory.all) category = null;
                  }
                  
                  bool? isRead;
                  switch (_currentFilter) {
                    case 'Unread':
                      isRead = false;
                      break;
                    case 'Read':
                      isRead = true;
                      break;
                    default:
                      isRead = null;
                  }
                  
                  await notificationProvider.refreshNotifications(
                    category: category,
                    isRead: isRead,
                  );
                },
                child: CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                  // Enhanced App Bar
                  SliverAppBar(
                    pinned: true,
                    elevation: 0,
                    backgroundColor: theme.scaffoldBackgroundColor.withOpacity(0.95),
                    title: Text(
                      'Notifications',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 20 : 24,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    actions: [
                      if (_isSelectionMode) ...[
                        IconButton(
                          icon: Icon(
                            Icons.select_all,
                            color: theme.colorScheme.primary,
                          ),
                          onPressed: _selectAll,
                          tooltip: 'Select All',
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.delete_outline,
                            color: Colors.red,
                          ),
                          onPressed: () => _deleteSelected(notificationProvider),
                          tooltip: 'Delete Selected',
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.close,
                            color: theme.colorScheme.onSurface,
                          ),
                          onPressed: () => _exitSelectionMode(),
                          tooltip: 'Exit Selection',
                        ),
                      ] else ...[
                        if (unreadCount > 0) ...[
                          // Unread count badge
                          Container(
                            margin: const EdgeInsets.only(right: 8),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '$unreadCount',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: Icon(
                              Icons.done_all,
                              color: theme.colorScheme.primary,
                            ),
                            onPressed: () => _markAllAsRead(notificationProvider),
                            tooltip: 'Mark all as read',
                          ),
                        ],
                        IconButton(
                          icon: Icon(
                            Icons.select_all,
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                          onPressed: () => _enterSelectionMode(),
                          tooltip: 'Select notifications',
                        ),
                      ],
                    ],
                  ),
                  
                  // Category Filter Pills
                  SliverToBoxAdapter(
                    child: Container(
                      height: 60,
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          final category = _categories[index];
                          final isSelected = _selectedCategory == category.id;
                          final categoryCount = category.id == 'all' 
                              ? notifications.length
                              : notificationProvider.getNotificationsByCategory(
                                  NotificationCategory.values.firstWhere(
                                    (c) => c.name == category.id,
                                    orElse: () => NotificationCategory.all,
                                  ),
                                ).length;
                          
                          return GestureDetector(
                            onTap: () => _onCategoryChanged(category.id),
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              margin: const EdgeInsets.only(right: 12),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                gradient: isSelected
                                    ? LinearGradient(
                                        colors: [
                                          category.color,
                                          category.color.withOpacity(0.7),
                                        ],
                                      )
                                    : null,
                                color: isSelected 
                                    ? null 
                                    : theme.cardColor.withOpacity(0.7),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: isSelected 
                                      ? category.color
                                      : theme.dividerColor.withOpacity(0.3),
                                ),
                                boxShadow: isSelected ? [
                                  BoxShadow(
                                    color: category.color.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  )
                                ] : [],
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    category.icon,
                                    size: 16,
                                    color: isSelected 
                                        ? Colors.white
                                        : category.color,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    category.name,
                                    style: TextStyle(
                                      color: isSelected 
                                          ? Colors.white
                                          : theme.colorScheme.onSurface,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 12,
                                    ),
                                  ),
                                  if (categoryCount > 0) ...[
                                    const SizedBox(width: 6),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 6,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: isSelected 
                                            ? Colors.white.withOpacity(0.2)
                                            : category.color.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        '$categoryCount',
                                        style: TextStyle(
                                          color: isSelected 
                                              ? Colors.white
                                              : category.color,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  
                  // Filter Pills (Read/Unread) - Simplified
                  SliverToBoxAdapter(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Row(
                        children: [
                          _buildFilterChip('All', _currentFilter == 'All'),
                          const SizedBox(width: 8),
                          _buildFilterChip('Unread', _currentFilter == 'Unread'),
                          const SizedBox(width: 8),
                          _buildFilterChip('Read', _currentFilter == 'Read'),
                        ],
                      ),
                    ),
                  ),
                  
                  // Notifications List
                  if (isLoading)
                    SliverToBoxAdapter(
                      child: Container(
                        height: 200,
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                    )
                  else if (error != null)
                    SliverToBoxAdapter(
                      child: Container(
                        height: 200,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 48,
                                color: Colors.red,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                error,
                                style: TextStyle(color: Colors.red),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: () {
                                  // Get current filter state for retry
                                  NotificationCategory? category;
                                  if (_selectedCategory != 'all') {
                                    category = NotificationCategory.values.firstWhere(
                                      (c) => c.name == _selectedCategory,
                                      orElse: () => NotificationCategory.all,
                                    );
                                    if (category == NotificationCategory.all) category = null;
                                  }
                                  
                                  bool? isRead;
                                  switch (_currentFilter) {
                                    case 'Unread':
                                      isRead = false;
                                      break;
                                    case 'Read':
                                      isRead = true;
                                      break;
                                    default:
                                      isRead = null;
                                  }
                                  
                                  notificationProvider.refreshNotifications(
                                    category: category,
                                    isRead: isRead,
                                  );
                                },
                                child: Text('Retry'),
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                  else
                    _buildNotificationsList(theme, isDark, isSmallScreen, notificationProvider),
                ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationsList(ThemeData theme, bool isDark, bool isSmallScreen, NotificationProvider notificationProvider) {
    final filteredNotifications = _getFilteredNotifications(notificationProvider.notifications);
    
    if (filteredNotifications.isEmpty && !notificationProvider.isLoading) {
      return SliverToBoxAdapter(
        child: _buildEmptyState(theme),
      );
    }

    return SliverPadding(
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 12 : 16,
        vertical: 8,
      ),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            // Show loading indicator at the bottom when loading more
            if (index == filteredNotifications.length) {
              if (notificationProvider.isLoadingMore) {
                return Container(
                  padding: const EdgeInsets.all(20),
                  child: Center(
                    child: Column(
                      children: [
                        SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              theme.colorScheme.primary.withOpacity(0.7),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Loading more notifications...',
                          style: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              } else if (!notificationProvider.hasMoreData && filteredNotifications.isNotEmpty) {
                return Container(
                  padding: const EdgeInsets.all(20),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: theme.colorScheme.primary.withOpacity(0.5),
                          size: 20,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'All notifications loaded',
                          style: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }

            final notification = filteredNotifications[index];
            final isSelected = _selectedNotifications.contains(notification.id);
            
            return AnimatedContainer(
              duration: Duration(milliseconds: 300 + (index * 50)),
              curve: Curves.easeOutCubic,
              margin: const EdgeInsets.only(bottom: 12),
              child: GlassmorphicNotificationCard(
                notification: notification,
                isSelected: _isSelectionMode ? isSelected : null,
                onTap: () => _onNotificationTap(notification, notificationProvider),
                onLongPress: () => _onNotificationLongPress(notification),
                onSelectionChanged: (selected) => _onSelectionChanged(notification.id, selected),
                onDismiss: () => _dismissNotification(notification.id, notificationProvider),
                onMarkAsRead: () => _markAsRead(notification.id, notificationProvider),
                onMarkAsUnread: () => _markAsUnread(notification.id, notificationProvider),
              ),
            );
          },
          childCount: filteredNotifications.length + 1, // +1 for loading indicator
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      height: 400,
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: theme.cardColor.withOpacity(0.5),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: theme.dividerColor.withOpacity(0.2),
              ),
            ),
            child: Icon(
              _selectedCategory == 'all' 
                  ? Icons.notifications_off_outlined
                  : _categories.firstWhere((c) => c.id == _selectedCategory).icon,
              size: 48,
              color: theme.colorScheme.primary.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            _selectedCategory == 'all' 
                ? 'No notifications yet'
                : 'No ${_categories.firstWhere((c) => c.id == _selectedCategory).name.toLowerCase()} notifications',
            style: TextStyle(
              color: theme.colorScheme.onSurface.withOpacity(0.8),
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _selectedCategory == 'all'
                ? 'Stay tuned for updates about your music, friends, and challenges!'
                : 'We\'ll notify you when something interesting happens in this category.',
            style: TextStyle(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _onCategoryChanged(String categoryId) {
    if (_selectedCategory == categoryId) return;
    
    HapticFeedback.selectionClick();
    setState(() {
      _selectedCategory = categoryId;
    });
    
    // Reload notifications for new category
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
    
    NotificationCategory? category;
    if (categoryId != 'all') {
      category = NotificationCategory.values.firstWhere(
        (c) => c.name == categoryId,
        orElse: () => NotificationCategory.all,
      );
      if (category == NotificationCategory.all) category = null;
    }
    
    bool? isRead;
    switch (_currentFilter) {
      case 'Unread':
        isRead = false;
        break;
      case 'Read':
        isRead = true;
        break;
      default:
        isRead = null;
    }
    
    notificationProvider.loadNotifications(
      category: category,
      isRead: isRead,
      refresh: true,
    );
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    
    return GestureDetector(
      onTap: () {
        if (_currentFilter == label) return;
        
        setState(() {
          _currentFilter = label;
        });
        
        // Reload notifications for new filter
        final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
        
        NotificationCategory? category;
        if (_selectedCategory != 'all') {
          category = NotificationCategory.values.firstWhere(
            (c) => c.name == _selectedCategory,
            orElse: () => NotificationCategory.all,
          );
          if (category == NotificationCategory.all) category = null;
        }
        
        bool? isRead;
        switch (label) {
          case 'Unread':
            isRead = false;
            break;
          case 'Read':
            isRead = true;
            break;
          default:
            isRead = null;
        }
        
        notificationProvider.loadNotifications(
          category: category,
          isRead: isRead,
          refresh: true,
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? primaryColor.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? primaryColor : theme.dividerColor.withOpacity(0.3),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? primaryColor : theme.colorScheme.onSurface.withOpacity(0.7),
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  void _onNotificationTap(NotificationModel notification, NotificationProvider provider) {
    if (_isSelectionMode) {
      _onSelectionChanged(notification.id, !_selectedNotifications.contains(notification.id));
      return;
    }
    
    // Mark as read if unread
    if (!notification.isRead) {
      _markAsRead(notification.id, provider);
    }
    
    // Handle notification-specific actions
    _handleNotificationAction(notification);
  }

  void _onNotificationLongPress(NotificationModel notification) {
    if (!_isSelectionMode) {
      _enterSelectionMode();
    }
    _onSelectionChanged(notification.id, true);
    HapticFeedback.mediumImpact();
  }

  void _handleNotificationAction(NotificationModel notification) {
    // Navigate to appropriate screens based on notification type
    switch (notification.type) {
      case NotificationType.pinLike:
        // Navigate to pin details
        break;
      case NotificationType.friendNearby:
        // Navigate to map with friend's pin
        break;
      case NotificationType.aiRecommendation:
        // Open AI recommendations or play track
        break;
      case NotificationType.newRelease:
        // Open music player or artist page
        break;
      case NotificationType.challengeComplete:
        // Navigate to challenges screen
        break;
      case NotificationType.levelUp:
        // Show level up celebration
        break;
      case NotificationType.friendRequest:
        // Navigate to friend requests
        break;
      case NotificationType.musicChat:
        // Navigate to music chat
        break;
      case NotificationType.collectionUpdate:
        // Navigate to collection
        break;
      case NotificationType.skinUnlocked:
        // Navigate to skins
        break;
      default:
        break;
    }
  }

  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedNotifications.clear();
    });
  }

  void _onSelectionChanged(String notificationId, bool selected) {
    setState(() {
      if (selected) {
        _selectedNotifications.add(notificationId);
      } else {
        _selectedNotifications.remove(notificationId);
      }
    });
  }

  void _selectAll() {
    final provider = Provider.of<NotificationProvider>(context, listen: false);
    final filteredNotifications = _getFilteredNotifications(provider.notifications);
    setState(() {
      _selectedNotifications = filteredNotifications.map((n) => n.id).toSet();
    });
  }

  void _markAsRead(String notificationId, NotificationProvider provider) {
    provider.markAsRead(notificationId);
  }

  void _markAsUnread(String notificationId, NotificationProvider provider) {
    // This would require a new API method - for now, just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Mark as unread feature coming soon'),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _markAllAsRead(NotificationProvider provider) {
    final category = _selectedCategory == 'all' 
        ? null 
        : NotificationCategory.values.firstWhere(
            (c) => c.name == _selectedCategory,
            orElse: () => NotificationCategory.all,
          );
    
    provider.markAllAsRead(category: category);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('All notifications marked as read'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _dismissNotification(String notificationId, NotificationProvider provider) {
    provider.deleteNotifications([notificationId]);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Notification dismissed'),
        backgroundColor: Colors.grey.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _deleteSelected(NotificationProvider provider) {
    final count = _selectedNotifications.length;
    
    provider.deleteNotifications(_selectedNotifications.toList());
    
    setState(() {
      _selectedNotifications.clear();
      _isSelectionMode = false;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$count notification${count == 1 ? '' : 's'} deleted'),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    super.dispose();
  }
}

// Supporting classes and enums

class NotificationCategoryItem {
  final String id;
  final String name;
  final IconData icon;
  final Color color;

  NotificationCategoryItem({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
  });
} 