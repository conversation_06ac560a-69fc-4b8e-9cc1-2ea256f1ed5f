import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class NotificationActionButtons extends StatelessWidget {
  final bool isRead;
  final VoidCallback onMarkAsRead;
  final VoidCallback onMarkAsUnread;
  final VoidCallback onDelete;
  final Color? color;

  const NotificationActionButtons({
    Key? key,
    required this.isRead,
    required this.onMarkAsRead,
    required this.onMarkAsUnread,
    required this.onDelete,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = color ?? theme.colorScheme.primary;
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildActionButton(
          context: context,
          icon: isRead ? Icons.mark_as_unread : Icons.done,
          label: isRead ? 'Mark Unread' : 'Mark Read',
          color: Colors.blue,
          onTap: isRead ? onMarkAsUnread : onMarkAsRead,
        ),
        const SizedBox(width: 8),
        _buildActionButton(
          context: context,
          icon: Icons.delete_outline,
          label: 'Delete',
          color: Colors.red,
          onTap: onDelete,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withOpacity(0.3),
          ),
        ),
        child: Icon(
          icon,
          size: 16,
          color: color,
        ),
      ),
    );
  }
} 