import 'package:flutter/material.dart';

class NotificationCard extends StatelessWidget {
  final Map<String, dynamic> notification;
  final bool isSmallScreen;

  const NotificationCard({
    Key? key,
    required this.notification,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 10),
      decoration: BoxDecoration(
        color: notification['isRead'] 
            ? Theme.of(context).cardColor 
            : Theme.of(context).colorScheme.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 14),
        border: Border.all(
          color: Theme.of(context).dividerColor.withOpacity(0.05),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Handle notification tap
          },
          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 14),
          child: Padding(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 14),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildNotificationIcon(context),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        notification['title'],
                        style: TextStyle(
                          fontSize: isSmallScreen ? 14 : 15,
                          fontWeight: FontWeight.w600,
                          letterSpacing: -0.2,
                        ),
                      ),
                      if (notification['description'] != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          notification['description'],
                          style: TextStyle(
                            fontSize: isSmallScreen ? 12 : 13,
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                            letterSpacing: -0.1,
                          ),
                        ),
                      ],
                      const SizedBox(height: 4),
                      Text(
                        notification['time'],
                        style: TextStyle(
                          fontSize: isSmallScreen ? 11 : 12,
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                          letterSpacing: -0.1,
                        ),
                      ),
                    ],
                  ),
                ),
                if (!notification['isRead'])
                  Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.only(top: 4, left: 8),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon(BuildContext context) {
    final iconSize = isSmallScreen ? 40.0 : 44.0;
    
    return Container(
      width: iconSize,
      height: iconSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: _getNotificationColor(context).withOpacity(0.1),
      ),
      child: Icon(
        _getNotificationIcon(),
        color: _getNotificationColor(context),
        size: iconSize * 0.5,
      ),
    );
  }

  IconData _getNotificationIcon() {
    switch (notification['type']) {
      case 'pin_like':
        return Icons.favorite;
      case 'pin_comment':
        return Icons.comment;
      case 'new_follower':
        return Icons.person_add;
      case 'pin_nearby':
        return Icons.near_me;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(BuildContext context) {
    switch (notification['type']) {
      case 'pin_like':
        return Colors.red;
      case 'pin_comment':
        return Colors.blue;
      case 'new_follower':
        return Colors.green;
      case 'pin_nearby':
        return Theme.of(context).colorScheme.primary;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }
} 