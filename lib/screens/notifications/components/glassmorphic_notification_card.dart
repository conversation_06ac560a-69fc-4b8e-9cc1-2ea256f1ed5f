import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../models/notification_model.dart';

class GlassmorphicNotificationCard extends StatefulWidget {
  final NotificationModel notification;
  final bool? isSelected;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final Function(bool) onSelectionChanged;
  final VoidCallback onDismiss;
  final VoidCallback onMarkAsRead;
  final VoidCallback onMarkAsUnread;

  const GlassmorphicNotificationCard({
    Key? key,
    required this.notification,
    this.isSelected,
    required this.onTap,
    required this.onLongPress,
    required this.onSelectionChanged,
    required this.onDismiss,
    required this.onMarkAsRead,
    required this.onMarkAsUnread,
  }) : super(key: key);

  @override
  State<GlassmorphicNotificationCard> createState() => _GlassmorphicNotificationCardState();
}

class _GlassmorphicNotificationCardState extends State<GlassmorphicNotificationCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _glowController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    // Start glow animation for unread notifications
    if (!widget.notification.isRead) {
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(GlassmorphicNotificationCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update glow animation based on read status
    if (widget.notification.isRead != oldWidget.notification.isRead) {
      if (widget.notification.isRead) {
        _glowController.stop();
        _glowController.reset();
      } else {
        _glowController.repeat(reverse: true);
      }
    }
  }

  Color _getNotificationColor() {
    switch (widget.notification.type) {
      case NotificationType.pinLike:
      case NotificationType.pinTrending:
        return Colors.red;
      case NotificationType.friendNearby:
      case NotificationType.friendRequest:
      case NotificationType.musicChat:
        return Colors.green;
      case NotificationType.aiRecommendation:
      case NotificationType.newRelease:
        return Colors.purple;
      case NotificationType.challengeComplete:
      case NotificationType.levelUp:
        return Colors.amber;
      case NotificationType.collectionUpdate:
        return Colors.indigo;
      case NotificationType.skinUnlocked:
        return Colors.pink;
      default:
        return Colors.blue;
    }
  }

  IconData _getNotificationIcon() {
    switch (widget.notification.type) {
      case NotificationType.pinLike:
        return Icons.favorite;
      case NotificationType.pinTrending:
        return Icons.trending_up;
      case NotificationType.friendNearby:
        return Icons.location_on;
      case NotificationType.friendRequest:
        return Icons.person_add;
      case NotificationType.musicChat:
        return Icons.chat_bubble;
      case NotificationType.aiRecommendation:
        return Icons.auto_awesome;
      case NotificationType.newRelease:
        return Icons.new_releases;
      case NotificationType.challengeComplete:
        return Icons.emoji_events;
      case NotificationType.levelUp:
        return Icons.arrow_upward;
      case NotificationType.collectionUpdate:
        return Icons.library_music;
      case NotificationType.skinUnlocked:
        return Icons.palette;
      default:
        return Icons.notifications;
    }
  }

  Widget _buildPriorityIndicator() {
    final priority = widget.notification.priority;
    if (priority == NotificationPriority.low) return const SizedBox.shrink();
    
    final color = priority == NotificationPriority.urgent 
        ? Colors.red 
        : priority == NotificationPriority.high 
            ? Colors.orange 
            : Colors.blue;
    
    return Container(
      width: 4,
      height: 4,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildActionButton(
          icon: widget.notification.isRead ? Icons.mark_as_unread : Icons.done,
          color: Colors.blue,
          onTap: widget.notification.isRead ? widget.onMarkAsUnread : widget.onMarkAsRead,
        ),
        const SizedBox(width: 8),
        _buildActionButton(
          icon: Icons.delete_outline,
          color: Colors.red,
          onTap: widget.onDismiss,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withOpacity(0.3),
          ),
        ),
        child: Icon(
          icon,
          size: 16,
          color: color,
        ),
      ),
    );
  }

  Widget _buildCompactActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          size: 14,
          color: color,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final notificationColor = _getNotificationColor();
    final isSelected = widget.isSelected ?? false;

    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _glowAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Dismissible(
            key: Key(widget.notification.id),
            direction: DismissDirection.endToStart,
            onDismissed: (direction) {
              widget.onDismiss();
            },
            background: Container(
              alignment: Alignment.centerRight,
              padding: const EdgeInsets.only(right: 20),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                Icons.delete_outline,
                color: Colors.white,
                size: 24,
              ),
            ),
            child: GestureDetector(
              onTap: () {
                HapticFeedback.selectionClick();
                _scaleController.forward().then((_) {
                  _scaleController.reverse();
                });
                widget.onTap();
              },
              onLongPress: () {
                HapticFeedback.mediumImpact();
                widget.onLongPress();
              },
              child: Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: isDark
                      ? theme.cardColor.withOpacity(0.3)
                      : Colors.white.withOpacity(0.8),
                  border: Border.all(
                    color: isSelected
                        ? notificationColor
                        : (widget.notification.isRead
                            ? Colors.transparent
                            : notificationColor.withOpacity(0.3)),
                    width: isSelected ? 2 : (widget.notification.isRead ? 0 : 1),
                  ),
                  boxShadow: [
                    if (!widget.notification.isRead)
                      BoxShadow(
                        color: notificationColor.withOpacity(0.2 * _glowAnimation.value),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    BoxShadow(
                      color: Colors.black.withOpacity(isDark ? 0.3 : 0.1),
                      blurRadius: 15,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header Row
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Selection checkbox or notification icon/image
                        if (widget.isSelected != null) ...[
                          GestureDetector(
                            onTap: () => widget.onSelectionChanged(!isSelected),
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                color: isSelected ? notificationColor : Colors.transparent,
                                border: Border.all(
                                  color: notificationColor,
                                  width: 2,
                                ),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: isSelected
                                  ? const Icon(
                                      Icons.check,
                                      color: Colors.white,
                                      size: 12,
                                    )
                                  : null,
                            ),
                          ),
                          const SizedBox(width: 8),
                        ] else ...[
                          // Show image if available, otherwise show notification icon
                          if (widget.notification.imageUrl != null) ...[
                            Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: notificationColor.withOpacity(0.3),
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(7),
                                child: CachedNetworkImage(
                                  imageUrl: widget.notification.imageUrl!,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => Container(
                                    color: notificationColor.withOpacity(0.1),
                                    child: Icon(
                                      Icons.image,
                                      color: notificationColor.withOpacity(0.5),
                                      size: 16,
                                    ),
                                  ),
                                  errorWidget: (context, url, error) => Container(
                                    color: notificationColor.withOpacity(0.1),
                                    child: Icon(
                                      Icons.broken_image,
                                      color: notificationColor.withOpacity(0.5),
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ] else ...[
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: notificationColor.withOpacity(0.15),
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: notificationColor.withOpacity(0.3),
                                ),
                              ),
                              child: Icon(
                                _getNotificationIcon(),
                                color: notificationColor,
                                size: 18,
                              ),
                            ),
                          ],
                          const SizedBox(width: 10),
                        ],
                        
                        // Title and metadata
                        Expanded(
                          flex: 3,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Title row
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      widget.notification.title,
                                      style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  if (_buildPriorityIndicator() is! SizedBox) ...[
                                    const SizedBox(width: 4),
                                    _buildPriorityIndicator(),
                                  ],
                                  if (!widget.notification.isRead) ...[
                                    const SizedBox(width: 4),
                                    Container(
                                      width: 6,
                                      height: 6,
                                      decoration: BoxDecoration(
                                        color: notificationColor,
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                              const SizedBox(height: 4),
                              // Type and time row - wrapped to prevent overflow
                              Wrap(
                                spacing: 8,
                                runSpacing: 4,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: notificationColor.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Text(
                                      widget.notification.type.displayName,
                                      style: TextStyle(
                                        fontSize: 9,
                                        fontWeight: FontWeight.w600,
                                        color: notificationColor,
                                      ),
                                    ),
                                  ),
                                  Text(
                                    timeago.format(widget.notification.timestamp),
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        
                        // Action buttons (if not in selection mode)
                        if (widget.isSelected == null) ...[
                          const SizedBox(width: 4),
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _buildCompactActionButton(
                                icon: widget.notification.isRead ? Icons.mark_as_unread : Icons.done,
                                color: Colors.blue,
                                onTap: widget.notification.isRead ? widget.onMarkAsUnread : widget.onMarkAsRead,
                              ),
                              const SizedBox(height: 4),
                              _buildCompactActionButton(
                                icon: Icons.delete_outline,
                                color: Colors.red,
                                onTap: widget.onDismiss,
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Message
                    Text(
                      widget.notification.message,
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.onSurface.withOpacity(0.8),
                        height: 1.4,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    // Additional action data (if available)
                    if (widget.notification.actionData != null) ...[
                      const SizedBox(height: 12),
                      _buildActionDataWidget(),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionDataWidget() {
    final actionData = widget.notification.actionData!;
    final notificationColor = _getNotificationColor();

    // Build different action data widgets based on notification type
    switch (widget.notification.type) {
      case NotificationType.pinLike:
        final likes = actionData['likes'] as int? ?? 0;
        return _buildStatRow(
          icon: Icons.favorite,
          label: '$likes likes',
          color: notificationColor,
        );
      
      case NotificationType.challengeComplete:
        final xp = actionData['xp'] as int? ?? 0;
        return _buildStatRow(
          icon: Icons.stars,
          label: '+$xp XP earned',
          color: notificationColor,
        );
      
      case NotificationType.levelUp:
        final level = actionData['newLevel'] as int? ?? 0;
        return _buildStatRow(
          icon: Icons.arrow_upward,
          label: 'Level $level reached',
          color: notificationColor,
        );
      
      case NotificationType.collectionUpdate:
        final plays = actionData['plays'] as int? ?? 0;
        return _buildStatRow(
          icon: Icons.play_arrow,
          label: '$plays total plays',
          color: notificationColor,
        );
      
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildStatRow({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _glowController.dispose();
    super.dispose();
  }
} 