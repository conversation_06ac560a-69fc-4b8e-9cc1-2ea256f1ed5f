import 'package:flutter/material.dart';

class ChatMessage {
  final String message;
  final bool isAI;
  final DateTime timestamp;
  final Widget? specialContent; // Added for achievement and skin cards

  ChatMessage({
    required this.message,
    required this.isAI,
    required this.timestamp,
    this.specialContent, // Initialize specialContent
  });
}

enum OnboardingStep {
  welcome,
  auth,
  genres,
  genreComment,
  artists,
  songSelection,
  pinPlacement,
  celebration,
  completion,
}
