import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import '../../services/api/auth_service.dart';
import '../../services/api/api_client.dart';
import '../../services/verification/school_verification_service.dart';
import '../../providers/auth_provider.dart';
import '../../models/user.dart';
import '../../config/constants.dart';

/// Authentication service specifically for AI onboarding flow
/// Handles Google Sign-In and Email verification with proper backend endpoints
class AIOnboardingAuthService {
  final AuthService _authService = AuthService();
  final ApiClient _apiClient = ApiClient();
  final SchoolVerificationService _verificationService =
      SchoolVerificationService();

  // Get the singleton instance of GoogleSignIn
  final GoogleSignIn _googleSignIn = GoogleSignIn.instance;

  /// Handle Apple Sign-In during onboarding
  /// Uses the sign_in_with_apple package for Apple ID authentication
  Future<Map<String, dynamic>> handleAppleSignIn() async {
    try {
      if (kDebugMode) {
        print('🍎 [AIOnboarding] Starting Apple Sign-In');
      }

      // Check if Apple Sign-In is available on this platform
      final isAvailable = await SignInWithApple.isAvailable();
      if (!isAvailable) {
        if (kDebugMode) {
          print('❌ [AIOnboarding] Apple Sign-In not available on this platform');
        }
        return {
          'success': false,
          'message': 'Apple Sign-In is not available on this device',
        };
      }

      // Request Apple Sign-In credential
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      if (kDebugMode) {
        print('✅ [AIOnboarding] Apple Sign-In successful');
        print('🍎 [AIOnboarding] User ID: ${credential.userIdentifier}');
        print('📧 [AIOnboarding] Email: ${credential.email ?? 'Not provided'}');
        print('👤 [AIOnboarding] Name: ${credential.givenName ?? ''} ${credential.familyName ?? ''}');
      }

      // Prepare user data from Apple credential
      final fullName = [credential.givenName, credential.familyName]
          .where((name) => name != null && name.isNotEmpty)
          .join(' ');

      final displayName = fullName.isNotEmpty ? fullName : 'Apple User';
      final email = credential.email ?? '';

      // Send Apple authentication data to backend
      final response = await _apiClient.post(
        '/users/auth/apple/',
        body: {
          'credential': credential.identityToken,
          'authorization_code': credential.authorizationCode,
          'user_identifier': credential.userIdentifier,
          'email': email,
          'full_name': displayName,
          'given_name': credential.givenName ?? '',
          'family_name': credential.familyName ?? '',
        },
        requiresAuth: false,
      );

      if (response['error'] == true || response['success'] == false) {
        throw Exception(
            response['message'] ?? 'Failed to authenticate with server');
      }

      // Validate that we have the required data
      if (response['user'] == null || response['user']['id'] == null) {
        throw Exception('User data missing from Apple Sign-In response');
      }

      // Validate that we have the required auth data
      if (response['auth_token'] == null) {
        throw Exception('No authentication token received from Apple Sign-In');
      }

      if (kDebugMode) {
        print('✅ [AIOnboarding] Backend authentication successful');
        print('👤 [AIOnboarding] User ID: ${response['user']['id']}');
        print(
            '🔑 [AIOnboarding] Auth token received: ${response['auth_token'] != null}');
      }

      return {
        'success': true,
        'user': {
          'id': response['user']['id'],
          'email': response['user']['email'] ?? email,
          'name': response['user']['name'] ?? response['user']['username'] ?? displayName,
          'profile_pic': response['user']['profile_pic'],
          'apple_connected': true,
        },
        'auth_token': response['auth_token']?.toString() ?? '',
        'refresh_token': response['refresh_token']?.toString() ?? '',
        'is_new_user': response['is_new_user'] ?? false,
        'message': response['message'] ?? 'Apple Sign-In successful!',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Apple Sign-In error: $e');
      }

      // Handle specific Apple Sign-In errors
      String errorMessage = 'Apple Sign-In failed';
      if (e is SignInWithAppleAuthorizationException) {
        switch (e.code) {
          case AuthorizationErrorCode.canceled:
            errorMessage = 'Apple Sign-In was cancelled';
            break;
          case AuthorizationErrorCode.failed:
            errorMessage = 'Apple Sign-In failed';
            break;
          case AuthorizationErrorCode.invalidResponse:
            errorMessage = 'Invalid response from Apple';
            break;
          case AuthorizationErrorCode.notHandled:
            errorMessage = 'Apple Sign-In not handled';
            break;
          case AuthorizationErrorCode.notInteractive:
            errorMessage = 'Apple Sign-In not interactive';
            break;
          case AuthorizationErrorCode.unknown:
            errorMessage = 'Unknown Apple Sign-In error';
            break;
        }
      }

      return {
        'success': false,
        'message': '$errorMessage: ${e.toString()}',
      };
    }
  }

  /// Handle Google Sign-In during onboarding
  /// Uses the new /users/auth/google/ endpoint
  Future<Map<String, dynamic>> handleGoogleSignIn() async {
    try {
      if (kDebugMode) {
        print('🔗 [AIOnboarding] Starting Google Sign-In');
      }

      // Initialize Google Sign-In with required configuration
      await _googleSignIn.initialize(
        clientId: AppConstants.googleClientId,
      );

      // Sign out any existing user first
      await _googleSignIn.signOut();

      // Start the interactive sign-in process
      GoogleSignInAccount? account;
      if (_googleSignIn.supportsAuthenticate()) {
        // Use authenticate() on platforms that support it
        account = await _googleSignIn.authenticate();
      } else {
        // Try lightweight authentication first, then fallback to platform-specific sign-in
        account = await _googleSignIn.attemptLightweightAuthentication();
      }

      if (account == null) {
        if (kDebugMode) {
          print('❌ [AIOnboarding] Google Sign-In cancelled by user');
        }
        return {
          'success': false,
          'message': 'Google Sign-In was cancelled',
        };
      }

      // Get authentication details
      final GoogleSignInAuthentication googleAuth =
          await account.authentication;

      if (googleAuth.idToken == null) {
        if (kDebugMode) {
          print('❌ [AIOnboarding] Failed to get Google ID token');
        }
        return {
          'success': false,
          'message': 'Failed to get Google authentication tokens',
        };
      }

      if (kDebugMode) {
        print(
            '✅ [AIOnboarding] Google Sign-In successful for: ${account.email}');
        print(
            '📧 [AIOnboarding] User: ${account.displayName} (${account.email})');
      }

      // Send Google authentication data to backend using new endpoint
      final response = await _apiClient.post(
        '/users/auth/google/',
        body: {
          'credential': googleAuth.idToken,
        },
        requiresAuth: false,
      );

      if (response['error'] == true || response['success'] == false) {
        throw Exception(
            response['message'] ?? 'Failed to authenticate with server');
      }

      // Validate that we have the required data
      if (response['user'] == null || response['user']['id'] == null) {
        throw Exception('User data missing from Google Sign-In response');
      }

      // Validate that we have the required auth data
      if (response['auth_token'] == null) {
        throw Exception('No authentication token received from Google Sign-In');
      }

      if (kDebugMode) {
        print('✅ [AIOnboarding] Backend authentication successful');
        print('👤 [AIOnboarding] User ID: ${response['user']['id']}');
        print(
            '🔑 [AIOnboarding] Auth token received: ${response['auth_token'] != null}');
      }
      return {
        'success': true,
        'user': {
          'id': response['user']['id'],
          'email': response['user']['email'],
          'name': response['user']['name'] ?? response['user']['username'],
          'profile_pic': response['user']['profile_pic'],
          'google_connected': true,
        },
        'auth_token': response['auth_token']?.toString() ?? '',
        'refresh_token': response['refresh_token']?.toString() ?? '',
        'is_new_user': response['is_new_user'] ?? false,
        'message': response['message'] ?? 'Google Sign-In successful!',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Google Sign-In error: $e');
      }

      // Sign out on error to clear any partial state
      try {
        await _googleSignIn.signOut();
      } catch (signOutError) {
        if (kDebugMode) {
          print(
              '⚠️ [AIOnboarding] Error during cleanup sign-out: $signOutError');
        }
      }

      return {
        'success': false,
        'message': 'Google Sign-In failed: ${e.toString()}',
      };
    }
  }

  /// Step 1: Send verification code to email
  /// Uses the same endpoint as email_verification_step.dart
  Future<Map<String, dynamic>> sendEmailVerificationCode(String email) async {
    try {
      if (kDebugMode) {
        print('📧 [AIOnboarding] Sending verification code to: $email');
      }

      // Validate email format
      if (!_isValidEmail(email)) {
        return {
          'success': false,
          'message': 'Please enter a valid email address',
        };
      }

      // Use the same endpoint as email_verification_step.dart
      final response = await _apiClient.post(
        '/auth/send-verification-code/',
        body: {'email': email},
        requiresAuth: false,
      );

      if (response['error'] == true || response['success'] == false) {
        throw Exception(
            response['message'] ?? 'Failed to send verification code');
      }

      if (kDebugMode) {
        print('✅ [AIOnboarding] Verification code sent successfully');
      }

      return {
        'success': true,
        'message': response['message'] ?? 'Verification code sent to $email',
        'email': email,
        'expires_in': response['expires_in'] ?? 300, // Default 5 minutes
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Email verification send error: $e');
      }

      return {
        'success': false,
        'message': 'Failed to send verification code: ${e.toString()}',
      };
    }
  }

  /// Step 2: Verify the email code and complete authentication
  /// Uses the same endpoint as email_verification_step.dart
  Future<Map<String, dynamic>> verifyEmailCode({
    required String email,
    required String code,
    String? username,
  }) async {
    try {
      if (kDebugMode) {
        print('🔐 [AIOnboarding] Verifying code for email: $email');
      }

      // Validate code format (6 digits)
      if (code.length != 6 || !RegExp(r'^\d{6}$').hasMatch(code)) {
        return {
          'success': false,
          'message': 'Please enter a valid 6-digit code',
        };
      }

      // Use the same endpoint as email_verification_step.dart
      final response = await _apiClient.post(
        '/auth/verify-email-code/',
        body: {
          'email': email,
          'code': code,
          'username': username ?? _extractNameFromEmail(email),
        },
        requiresAuth: false,
      );

      if (response['error'] == true || response['success'] == false) {
        throw Exception(response['message'] ?? 'Failed to verify code');
      }

      if (kDebugMode) {
        print('✅ [AIOnboarding] Email verification successful');
      }

      // Check if user data is returned (existing user) or if we need to register a new user
      if (response['user'] != null) {
        // Existing user - return the user data

        // Validate that user data is complete
        if (response['user']['id'] == null) {
          throw Exception(
              'Email verification succeeded but user data is incomplete');
        }

        if (kDebugMode) {
          print(
              '👤 [AIOnboarding] Existing user found - User ID: ${response['user']['id']}');
          print(
              '🔑 [AIOnboarding] Auth token received: ${response['auth_token'] != null}');
        }

        return {
          'success': true,
          'user': {
            'id': response['user']['id'],
            'email': response['user']['email'] ?? email,
            'name': response['user']['name'] ??
                response['user']['username'] ??
                username,
            'profile_pic': response['user']['profile_pic'],
          },
          'auth_token': response['auth_token']?.toString() ?? '',
          'refresh_token': response['refresh_token']?.toString() ?? '',
          'is_new_user': false,
          'message': response['message'] ?? 'Email verified successfully!',
        };
      } else {
        // No user returned - need to register new user
        if (kDebugMode) {
          print('🆕 [AIOnboarding] Email verified, now registering new user');
        }

        return await _registerNewUser(
          email: email,
          username: username ?? _extractNameFromEmail(email),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Email verification error: $e');
      }

      return {
        'success': false,
        'message': 'Verification failed: ${e.toString()}',
      };
    }
  }

  /// Register a new user after email verification
  Future<Map<String, dynamic>> _registerNewUser({
    required String email,
    required String username,
  }) async {
    try {
      if (kDebugMode) {
        print('📝 [AIOnboarding] Registering new user: $email');
      }

      // First, try passwordless registration with email verification flag
      try {
        final response = await _apiClient.post(
          AppConstants
              .registerEndpoint, // Use the same endpoint as auth service
          body: {
            'username': username,
            'email': email,
            'password': '', // Empty password for email-verified accounts
            'email_verified': true, // Mark as already verified
            'registration_method': 'email_verification',
          },
          requiresAuth: false,
        );

        if (response['error'] != true && response['success'] != false) {
          if (kDebugMode) {
            print('✅ [AIOnboarding] Passwordless registration successful');
            print('👤 [AIOnboarding] New user ID: ${response['user']?['id']}');
            print(
                '🔑 [AIOnboarding] Auth token received: ${response['auth_token'] != null}');
          }

          // Validate that we have the required auth data
          if (response['auth_token'] == null) {
            throw Exception(
                'No authentication token received from registration');
          }

          return {
            'success': true,
            'user': {
              'id': response['user']['id'],
              'email': response['user']['email'] ?? email,
              'name': response['user']['name'] ??
                  response['user']['username'] ??
                  username,
              'profile_pic': response['user']['profile_pic'],
            },
            'auth_token': response['auth_token']?.toString() ?? '',
            'refresh_token': response['refresh_token']?.toString() ?? '',
            'is_new_user': response['is_new_user'],
            'message': 'Account created successfully!',
          };
        }
      } catch (passwordlessError) {
        if (kDebugMode) {
          print(
              '⚠️ [AIOnboarding] Passwordless registration failed, trying with generated password: $passwordlessError');
        }
      }

      // Fallback: Generate a temporary password for registration
      final tempPassword = _generateTemporaryPassword();

      final response = await _apiClient.post(
        AppConstants.registerEndpoint,
        body: {
          'username': username,
          'email': email,
          'password': tempPassword,
          'email_verified': true,
        },
        requiresAuth: false,
      );

      if (response['error'] == true || response['success'] == false) {
        throw Exception(response['message'] ?? 'Failed to register user');
      }

      // Validate that we have the required auth data
      if (response['auth_token'] == null) {
        throw Exception('No authentication token received from registration');
      }

      if (kDebugMode) {
        print(
            '✅ [AIOnboarding] User registration successful (with temp password)');
        print('👤 [AIOnboarding] New user ID: ${response['user']['id']}');
        print(
            '🔑 [AIOnboarding] Auth token received: ${response['auth_token'] != null}');
      }

      return {
        'success': true,
        'user': {
          'id': response['user']['id'],
          'email': response['user']['email'] ?? email,
          'name': response['user']['name'] ??
              response['user']['username'] ??
              username,
          'profile_pic': response['user']['profile_pic'],
        },
        'auth_token': response['auth_token']?.toString() ?? '',
        'refresh_token': response['refresh_token']?.toString() ?? '',
        'is_new_user': response['is_new_user'],
        'message': 'Account created successfully!',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] User registration error: $e');
      }

      return {
        'success': false,
        'message': 'Failed to create account: ${e.toString()}',
      };
    }
  }

  /// Generate a temporary password for registration fallback
  String _generateTemporaryPassword() {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#';
    final random = DateTime.now().millisecondsSinceEpoch;
    return 'temp_${random}_${chars[random % chars.length]}${chars[(random * 7) % chars.length]}${chars[(random * 13) % chars.length]}';
  }

  /// Check if email exists in the system
  /// Uses the same endpoint as email_check_step.dart
  Future<Map<String, dynamic>> checkEmailExists(String email) async {
    try {
      if (kDebugMode) {
        print('🔍 [AIOnboarding] Checking email existence: $email');
      }

      // Validate email format
      if (!_isValidEmail(email)) {
        return {
          'success': false,
          'message': 'Please enter a valid email address',
        };
      }

      // Use the same endpoint as email_check_step.dart
      final response = await _apiClient.post(
        '/auth/check-email/',
        body: {'email': email},
        requiresAuth: false,
      );

      final emailExists = response['exists'] == true;

      if (kDebugMode) {
        print('✅ [AIOnboarding] Email check complete - exists: $emailExists');
      }

      return {
        'success': true,
        'exists': emailExists,
        'user': response['user'],
        'message': emailExists
            ? 'Email found - please enter your password'
            : 'Email available for registration',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Email check error: $e');
      }

      return {
        'success': false,
        'message': 'Failed to check email: ${e.toString()}',
      };
    }
  }

  /// Complete the authentication process with AuthProvider
  /// This ensures the user is properly logged in and tokens are stored
  Future<bool> completeAuthentication({
    required AuthProvider authProvider,
    required Map<String, dynamic> userData,
    required String authToken,
    String? refreshToken,
  }) async {
    try {
      if (kDebugMode) {
        print(
            '✅ [AIOnboarding] Completing authentication for user: ${userData['email']}');
        print('🔑 [AIOnboarding] Auth token present: ${authToken.isNotEmpty}');
        print(
            '🔄 [AIOnboarding] Refresh token present: ${refreshToken?.isNotEmpty ?? false}');
      }

      // Validate required authentication data
      if (authToken.isEmpty) {
        throw Exception('Authentication token is missing or empty');
      }

      // Ensure we have valid tokens for authentication
      final validAuthToken = authToken.isNotEmpty
          ? authToken
          : 'temp_token_${DateTime.now().millisecondsSinceEpoch}';
      final validRefreshToken =
          (refreshToken?.isNotEmpty == true) ? refreshToken! : validAuthToken;

      if (kDebugMode) {
        print(
            '🔑 [AIOnboarding] Using auth token: ${validAuthToken.isNotEmpty}');
        print(
            '🔄 [AIOnboarding] Using refresh token: ${validRefreshToken.isNotEmpty}');
      }

      if (userData['email'] == null || userData['email'].toString().isEmpty) {
        throw Exception('User email is missing');
      }

      if (userData['id'] == null) {
        throw Exception('User ID is missing');
      }

      // Create User object from userData with proper null safety
      final user = User(
        id: int.parse(userData['id'].toString()),
        username: userData['name']?.toString() ??
            userData['username']?.toString() ??
            _extractNameFromEmail(userData['email'].toString()),
        email: userData['email'].toString(),
        profilePicUrl: userData['profile_pic']?.toString(),
        bio: userData['bio']?.toString() ?? 'New BOPMaps explorer 🎵',
        isVerified: userData['is_verified'] == true,
        favoriteGenres:
            (userData['favorite_genres'] as List<dynamic>?)?.cast<String>() ??
                const [],
        connectedServices:
            _parseConnectedServices(userData['connected_services']),
        createdAt: userData['created_at'] != null
            ? DateTime.parse(userData['created_at'].toString())
            : DateTime.now(),
      );

      // Initialize the auth provider with tokens and user data
      final success = await authProvider.login(
        token: validAuthToken,
        refreshToken: validRefreshToken,
        email: userData['email'].toString(),
        user: user.toJson(),
      );

      if (success && kDebugMode) {
        print('🎉 [AIOnboarding] Authentication completed successfully');
        print(
            '🎵 [AIOnboarding] User is now logged in and ready to explore BOPMaps!');
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Complete authentication error: $e');
        print('🔍 [AIOnboarding] UserData keys: ${userData.keys.toList()}');
        print('🔍 [AIOnboarding] AuthToken type: ${authToken.runtimeType}');
        print(
            '🔍 [AIOnboarding] RefreshToken type: ${refreshToken.runtimeType}');
      }
      return false;
    }
  }

  /// Sign out from Google and clear any stored authentication state
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      if (kDebugMode) {
        print('✅ [AIOnboarding] Google Sign-Out successful');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Google Sign-Out error: $e');
      }
    }
  }

  /// Disconnect from Google completely (revokes all permissions)
  Future<void> disconnect() async {
    try {
      await _googleSignIn.disconnect();
      if (kDebugMode) {
        print('✅ [AIOnboarding] Google disconnect successful');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Google disconnect error: $e');
      }
    }
  }

  /// Validate email format using the same pattern as other components
  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// Extract a readable name from email address for username generation
  String _extractNameFromEmail(String email) {
    final localPart = email.split('@')[0];
    final parts = localPart.split(RegExp(r'[._-]'));

    return parts
        .map((part) => part[0].toUpperCase() + part.substring(1).toLowerCase())
        .join(' ');
  }

  /// Helper to parse the connected_services field from the backend response
  Map<String, bool> _parseConnectedServices(dynamic services) {
    if (services == null) {
      return const {
        'spotify': false,
        'apple_music': false,
        'soundcloud': false,
      };
    }
    if (services is Map<String, bool>) {
      return services;
    }
    // Fallback for older backend responses or unexpected formats
    return const {
      'spotify': false,
      'apple_music': false,
      'soundcloud': false,
    };
  }

  /// Save user's music preferences (genres and artists) to the backend
  /// Uses the /users/music-preferences/ endpoint
  Future<Map<String, dynamic>> saveMusicPreferences({
    required List<String> topGenres,
    required List<String> topArtists,
    Map<String, List<String>> artistGenres = const {},
    Map<String, String> artistImageUrls = const {},
    Map<String, String> artistSpotifyIds = const {},
  }) async {
    try {
      if (kDebugMode) {
        print('🎵 [AIOnboarding] Saving music preferences...');
        print('🎵 [AIOnboarding] Top genres: ${topGenres.join(', ')}');
        print('🎵 [AIOnboarding] Top artists: ${topArtists.join(', ')}');
        if (artistGenres.isNotEmpty) {
          print('🎵 [AIOnboarding] Artist genres: $artistGenres');
        }
        if (artistImageUrls.isNotEmpty) {
          print('🎵 [AIOnboarding] Artist image URLs: $artistImageUrls');
        }
        if (artistSpotifyIds.isNotEmpty) {
          print('🎵 [AIOnboarding] Artist Spotify IDs: $artistSpotifyIds');
        }
      }

      // Validate input
      if (topGenres.isEmpty && topArtists.isEmpty) {
        return {
          'success': false,
          'message': 'At least one genre or artist must be provided',
        };
      }

      // Prepare request body
      final Map<String, dynamic> body = {};
      if (topGenres.isNotEmpty) body['top_genres'] = topGenres;
      if (topArtists.isNotEmpty) body['top_artists'] = topArtists;
      if (artistGenres.isNotEmpty) body['artist_genres'] = artistGenres;
      if (artistImageUrls.isNotEmpty) body['artist_image_urls'] = artistImageUrls;
      if (artistSpotifyIds.isNotEmpty) body['artist_spotify_ids'] = artistSpotifyIds;

      // Send request to music preferences endpoint
      final response = await _apiClient.patch(
        AppConstants.musicPreferencesEndpoint,
        body: body,
        requiresAuth: true,
      );

      if (response['error'] == true || response['success'] == false) {
        throw Exception(
            response['message'] ?? 'Failed to save music preferences');
      }

      if (kDebugMode) {
        print('✅ [AIOnboarding] Music preferences saved successfully');
        print('🎵 [AIOnboarding] Response: $response');
      }

      return {
        'success': true,
        'message': 'Music preferences saved successfully',
        'data': response,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AIOnboarding] Error saving music preferences: $e');
      }

      return {
        'success': false,
        'message': 'Failed to save music preferences: ${e.toString()}',
      };
    }
  }
}
