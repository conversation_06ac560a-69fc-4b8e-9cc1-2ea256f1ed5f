import 'dart:math';

/// AI Dialogue content for onboarding experience
class AIDialogueContent {
  static final _random = Random();
  
  /// Get a random message from a list
  static String getRandomMessage(List<DialogueOption> options) {
    if (options.isEmpty) return '';
    final option = options[_random.nextInt(options.length)];
    return option.text;
  }
  
  /// Get a specific message by slug
  static String? getMessageBySlug(List<DialogueOption> options, String slug) {
    try {
      return options.firstWhere((option) => option.slug == slug).text;
    } catch (e) {
      return null;
    }
  }
  
  /// Initial greeting messages
  static final List<DialogueOption> greetings = [
    const DialogueOption(slug: 'greeting_1', text: "Hey superstar! 🎶 Welcome to BOPMaps, your musical playground. Ready to drop some beats worldwide?"),
    const DialogueOption(slug: 'greeting_2', text: "Yo! Glad you're here! 🎉 I'm B<PERSON>, your AI buddy. Let's start your musical adventure!"),
    const DialogueOption(slug: 'greeting_3', text: "Hey music lover! 🌎 Let's turn your life soundtrack into pins on BOPMaps!"),
    const DialogueOption(slug: 'greeting_4', text: "Ahoy! 🚀 Welcome aboard BOPMaps—your map, your music, your rules! Ready to jam?"),
    const DialogueOption(slug: 'greeting_5', text: "Hey there, musical genius! 🎧 Ready to put your playlist on the map with BOPMaps?"),
    const DialogueOption(slug: 'greeting_6', text: "Welcome to the vibe tribe! 🌟 I'm BOP, your musical sidekick. Let's vibe together!"),
    const DialogueOption(slug: 'greeting_7', text: "Look who stepped into the party! 🎈 Welcome to BOPMaps, the place where music meets the world! Ready to explore?"),
    const DialogueOption(slug: 'greeting_8', text: "Hey, trailblazer! 🎸 You're about to pin your favorite songs around the globe. Let's rock this!"),
    const DialogueOption(slug: 'greeting_9', text: "Welcome aboard, musical adventurer! 🗺️ Ready to make some noise and map your jams?"),
  ];
  
  /// Auth introduction messages
  static final List<DialogueOption> authIntros = [
    const DialogueOption(slug: 'auth_intro_1', text: "Quick first step! How do you wanna sign in? Let's make this official! 📱"),
    const DialogueOption(slug: 'auth_intro_2', text: "Let's get you onboarded! Sign in your way, and let's start pinning! 🌟"),
    const DialogueOption(slug: 'auth_intro_3', text: "Let's set you up! Choose how you wanna connect with the BOPMaps fam! 🥳"),
    const DialogueOption(slug: 'auth_intro_4', text: "Ready to rock? Sign in so we can kickstart your musical journey! 🎸"),
    const DialogueOption(slug: 'auth_intro_5', text: "Let's get you signed in so we can jam! Pick your favorite method! 🎶"),
  ];
  
  /// Post-auth messages
  static final List<DialogueOption> postAuthMessages = [
    const DialogueOption(slug: 'post_auth_1', text: "You're officially in! 🎉 Let's dive into your musical vibes!"),
    const DialogueOption(slug: 'post_auth_2', text: "Boom! Account ready! Time to discover your music taste! 🚀"),
    const DialogueOption(slug: 'post_auth_3', text: "All set! 🎧 Let's see what tunes get you grooving!"),
    const DialogueOption(slug: 'post_auth_4', text: "Sweet! Now for the fun part—what makes your playlist pop? 🍿"),
    const DialogueOption(slug: 'post_auth_5', text: "Nice move! You're in! Now, let's find your musical match! 🎼"),
  ];
  
  /// Genre selection prompts
  static final List<DialogueOption> genrePrompts = [
    const DialogueOption(slug: 'genre_prompt_1', text: "What genres set your soul on fire? 🔥 Pick a few!"),
    const DialogueOption(slug: 'genre_prompt_2', text: "Share your musical vibe! 🎶 Which genres speak to you today?"),
    const DialogueOption(slug: 'genre_prompt_3', text: "Let's hear it! What sounds can't you live without? 🎧"),
    const DialogueOption(slug: 'genre_prompt_4', text: "Choose your genres—no wrong answers, only great music! 🌈"),
    const DialogueOption(slug: 'genre_prompt_5', text: "Show me your jams! What genres get your head bobbing? 🎤"),
  ];
  
  /// Genre-specific responses (5-10 per major genre)
  static final Map<String, List<DialogueOption>> genreResponses = {
    'hip-hop': [
      const DialogueOption(slug: 'hiphop_6', text: "Hip-hop enthusiast! Beats, rhymes, and life—let's pin it down! 🎤"),
      const DialogueOption(slug: 'hiphop_7', text: "You're keeping the culture alive! Hip-hop forever! 🔥"),
      const DialogueOption(slug: 'hiphop_8', text: "Hip-hop vibes incoming! Your playlist is legendary! 💯"),
      const DialogueOption(slug: 'hiphop_9', text: "A true hip-hop head! Can't wait to hear your picks! 🎧"),
      const DialogueOption(slug: 'hiphop_10', text: "Hip-hop runs deep! Ready to rep your favorites on the map! 🙌"),
      const DialogueOption(slug: 'hiphop_6', text: "Hip-hop fan—awesome choice! Always good energy. 🔥"),
      const DialogueOption(slug: 'hiphop_7', text: "Nice pick. Hip-hop has endless classics. 🎤"),
      const DialogueOption(slug: 'hiphop_8', text: "Hip-hop's all about storytelling—great taste! 📝"),
      const DialogueOption(slug: 'hiphop_9', text: "Hip-hop head spotted. Looking forward to your picks! 👀"),
      const DialogueOption(slug: 'hiphop_10', text: "Solid choice! Hip-hop is timeless. ⏰"),
    ],
    'pop': [
      const DialogueOption(slug: 'pop_1', text: "Great choice! Nothing beats a catchy pop track. 🎵"),
      const DialogueOption(slug: 'pop_2', text: "Love it—pop's all about the melodies you can't get out of your head. 🧠"),
      const DialogueOption(slug: 'pop_3', text: "Pop fan, nice! Everyone needs those feel-good tracks. 😊"),
      const DialogueOption(slug: 'pop_4', text: "Can't go wrong with pop. Those hooks stay with you. 🎣"),
      const DialogueOption(slug: 'pop_5', text: "Pop music's always a vibe. Solid pick! ✨"),
    ],

    'rock': [
      const DialogueOption(slug: 'rock_1', text: "Rock fan—excellent! Nothing beats that energy. 🤘"),
      const DialogueOption(slug: 'rock_2', text: "Nice! Rock's all about passion and great guitar riffs. 🎸"),
      const DialogueOption(slug: 'rock_3', text: "Classic choice! Rock music never disappoints. 🔥"),
      const DialogueOption(slug: 'rock_4', text: "Love the rock vibe. Let's turn it up! 📢"),
      const DialogueOption(slug: 'rock_5', text: "Rock music fan? Respect—it's timeless. ⚡"),
    ],

    'electronic': [
      const DialogueOption(slug: 'electronic_1', text: "Electronic music—nice! Those beats are addictive. 💻"),
      const DialogueOption(slug: 'electronic_2', text: "Cool pick. Electronic always sets the mood perfectly. 🎛️"),
      const DialogueOption(slug: 'electronic_3', text: "Electronic fan, awesome! Endless possibilities with those sounds. 🎧"),
      const DialogueOption(slug: 'electronic_4', text: "Love electronic tracks. Perfect for any vibe. 🌈"),
      const DialogueOption(slug: 'electronic_5', text: "Electronic music hits differently. Good taste! ⚡"),
    ],

    'r&b': [
      const DialogueOption(slug: 'rnb_1', text: "Smooth choice—R&B always sets the right mood. 🌙"),
      const DialogueOption(slug: 'rnb_2', text: "R&B vibes, love it. Great music for any moment. ✨"),
      const DialogueOption(slug: 'rnb_3', text: "Can't beat that R&B groove. Solid pick. 🎵"),
      const DialogueOption(slug: 'rnb_4', text: "R&B fan—excellent taste! Timeless genre. 💫"),
      const DialogueOption(slug: 'rnb_5', text: "R&B always hits just right. Let's get those vibes going. 🔥"),
    ],

    'indie': [
      const DialogueOption(slug: 'indie_1', text: "Nice choice—indie music always feels genuine. 🎸"),
      const DialogueOption(slug: 'indie_2', text: "Indie fan, great! Always something unique to discover. 🔍"),
      const DialogueOption(slug: 'indie_3', text: "Indie's all about finding those hidden gems. Solid choice! 💎"),
      const DialogueOption(slug: 'indie_4', text: "Love indie tracks—they keep things fresh and authentic. 🌱"),
      const DialogueOption(slug: 'indie_5', text: "Great pick! Indie music has endless creativity. 🎨"),
    ],
    'rap': [
      const DialogueOption(slug: 'rap_1', text: "Rap's a great pick—always fresh. 🎤"),
      const DialogueOption(slug: 'rap_2', text: "Rap lover, nice! Let's see those picks. 👀"),
      const DialogueOption(slug: 'rap_3', text: "Good taste! Rap brings the realness. 💯"),
      const DialogueOption(slug: 'rap_4', text: "Rap hits different. Excited for your playlist. 🔥"),
      const DialogueOption(slug: 'rap_5', text: "Rap fan, love it. Always bringing energy. ⚡"),
    ],

    'melodic rap': [
      const DialogueOption(slug: 'melodic_rap_1', text: "Melodic rap—nice choice. Best of both worlds. 🌊"),
      const DialogueOption(slug: 'melodic_rap_2', text: "Great taste! Melodic rap always hits the spot. 🎯"),
      const DialogueOption(slug: 'melodic_rap_3', text: "Melodic rap vibes, smooth choice. 🎵"),
      const DialogueOption(slug: 'melodic_rap_4', text: "Melodic rap fan—perfect for a chill mood. 😌"),
      const DialogueOption(slug: 'melodic_rap_5', text: "Good call. Melodic rap tracks are unforgettable. 🧠"),
    ],

    'edm': [
      const DialogueOption(slug: 'edm_1', text: "EDM fan—always ready for a good drop. 🎧"),
      const DialogueOption(slug: 'edm_2', text: "Great pick! EDM gets the energy flowing. ⚡"),
      const DialogueOption(slug: 'edm_3', text: "EDM vibes, perfect for any party. 🎉"),
      const DialogueOption(slug: 'edm_4', text: "Nice choice—EDM never disappoints. 💃"),
      const DialogueOption(slug: 'edm_5', text: "EDM enthusiast spotted. Let's get those beats rolling. 🔊"),
    ],

    'classical': [
      const DialogueOption(slug: 'classical_1', text: "Classical choice—timeless and elegant. 🎻"),
      const DialogueOption(slug: 'classical_2', text: "Nice! Classical music always sets the tone. 🎭"),
      const DialogueOption(slug: 'classical_3', text: "Classical music lover—great taste. 🎼"),
      const DialogueOption(slug: 'classical_4', text: "Classic choice! Excited to hear your selections. 🎹"),
      const DialogueOption(slug: 'classical_5', text: "Classical fan, fantastic! Music at its finest. 👑"),
    ],

    'broadway': [
      const DialogueOption(slug: 'broadway_1', text: "Broadway fan—love the drama and flair! 🎭"),
      const DialogueOption(slug: 'broadway_2', text: "Great pick! Broadway music always entertains. 🎪"),
      const DialogueOption(slug: 'broadway_3', text: "Broadway tunes—perfect for singing along. 🎤"),
      const DialogueOption(slug: 'broadway_4', text: "Nice! Broadway brings stories to life. 📚"),
      const DialogueOption(slug: 'broadway_5', text: "Broadway enthusiast—love your style. 🌟"),
    ],

    'atl hip-hop': [
      const DialogueOption(slug: 'atl_hiphop_1', text: "ATL hip-hop, great choice! Legendary tracks incoming. 🔥"),
      const DialogueOption(slug: 'atl_hiphop_2', text: "Atlanta's finest—solid taste in hip-hop. 🌟"),
      const DialogueOption(slug: 'atl_hiphop_3', text: "ATL hip-hop fan—can't go wrong. 💯"),
      const DialogueOption(slug: 'atl_hiphop_4', text: "Nice pick! ATL hip-hop has iconic vibes. 🎤"),
      const DialogueOption(slug: 'atl_hiphop_5', text: "ATL hip-hop enthusiast, excited for your playlist. 🙌"),
    ],
  };
  
  /// Multiple genre combination responses
  static final List<DialogueOption> multiGenreResponses = [
    const DialogueOption(slug: 'multi_genre_6', text: "You’re a musical explorer! 🌍 Love that eclectic mix!"),
    const DialogueOption(slug: 'multi_genre_7', text: "Diverse taste detected! Your map will be legendary! 🗺️"),
    const DialogueOption(slug: 'multi_genre_8', text: "Your playlist breaks boundaries! Excited to see your pins! 🌟"),
    const DialogueOption(slug: 'multi_genre_9', text: "Musical variety is your superpower! 🎶 Love it!"),
    const DialogueOption(slug: 'multi_genre_10', text: "Such range! Your pins will be iconic! 🎤"),
  ];
  
  /// Artist selection prompts
  static final List<DialogueOption> artistPrompts = [
    const DialogueOption(
      slug: 'artist_prompt_1',
      text: "Now for the fun part - who are your musical heroes? Pick some artists you vibe with!",
    ),
    const DialogueOption(
      slug: 'artist_prompt_2',
      text: "Time to name-drop! Which artists have earned a spot in your heart? ❤️",
    ),
    const DialogueOption(
      slug: 'artist_prompt_3',
      text: "Let's talk artists! Who's been on repeat lately?",
    ),
    const DialogueOption(
      slug: 'artist_prompt_4',
      text: "Artist selection time! Who makes the music that moves you?",
    ),
    const DialogueOption(
      slug: 'artist_prompt_5',
      text: "Show me your artists and I'll show you some magic! Who's on your playlist?",
    ),
  ];
  
  /// Song selection prompts
  static final List<DialogueOption> songPrompts = [
    const DialogueOption(
      slug: 'song_prompt_1',
      text: "Here's the moment of truth! Pick a song for your very first pin! Make it count! 📍",
    ),
    const DialogueOption(
      slug: 'song_prompt_2',
      text: "Time to make history! Choose the perfect track for your first musical mark on the world! 🌟",
    ),
    const DialogueOption(
      slug: 'song_prompt_3',
      text: "The big decision! Which song deserves the honor of being your first pin? 🎯",
    ),
    const DialogueOption(
      slug: 'song_prompt_4',
      text: "Your first pin awaits! Pick a banger that represents you! 🎵",
    ),
    const DialogueOption(
      slug: 'song_prompt_5',
      text: "This is it! Select the track that'll start your BOPMaps journey! 🚀",
    ),
  ];
  
  /// Post pin placement celebration
  static final List<DialogueOption> celebrations = [
    const DialogueOption(
      slug: 'celebration_1',
      text: "BOOM! 🎉 You just dropped your first pin! You're officially part of the BOPMaps family!",
    ),
    const DialogueOption(
      slug: 'celebration_2',
      text: "YES! 🎊 First pin down, infinite more to go! You're a natural!",
    ),
    const DialogueOption(
      slug: 'celebration_3',
      text: "Look at you go! 🌟 Your first musical mark on the world! I'm so proud!",
    ),
    const DialogueOption(
      slug: 'celebration_4',
      text: "Nailed it! 🎯 That pin looks amazing! The world just got a little more musical!",
    ),
    const DialogueOption(
      slug: 'celebration_5',
      text: "AMAZING! 🚀 You've officially started spreading the music! This is just the beginning!",
    ),
  ];
  
  /// Achievement messages
  static final List<DialogueOption> achievementMessages = [
    const DialogueOption(
      slug: 'achievement_1',
      text: "Check it out! You've completed your first challenge and earned your first pin skin! 🏆",
    ),
    const DialogueOption(
      slug: 'achievement_2',
      text: "Achievement unlocked! You've got your first skin - customize those pins! 🎨",
    ),
    const DialogueOption(
      slug: 'achievement_3',
      text: "Boom! First challenge complete and a shiny new skin to show for it! ✨",
    ),
  ];
  
  /// Final messages before map
  static final List<DialogueOption> finalMessages = [
    const DialogueOption(
      slug: 'final_1',
      text: "You're all set! Time to explore the musical world around you. Happy pinning! 🗺️",
    ),
    const DialogueOption(
      slug: 'final_2',
      text: "And that's a wrap! The world is your musical canvas now. Go make it sing! 🎵",
    ),
    const DialogueOption(
      slug: 'final_3',
      text: "You did it! Now go discover what others are listening to and keep dropping those beats! 🎧",
    ),
    const DialogueOption(
      slug: 'final_4',
      text: "Welcome to your new musical playground! Can't wait to see what you discover! 🎪",
    ),
    const DialogueOption(
      slug: 'final_5',
      text: "The stage is set! Time to explore, discover, and share your musical journey! 🚀",
    ),
  ];
  
  /// Email verification messages
  static final List<DialogueOption> emailVerificationMessages = [
    const DialogueOption(
      slug: 'email_verify_1',
      text: "I've sent a magic code to your email! Check your inbox and enter it here 📧",
    ),
    const DialogueOption(
      slug: 'email_verify_2',
      text: "Verification code incoming! It should arrive faster than a drum solo 🥁",
    ),
    const DialogueOption(
      slug: 'email_verify_3',
      text: "Code sent! Check your email (don't forget the spam folder, just in case!) 📮",
    ),
  ];
  
  /// Waiting/Loading messages
  static final List<DialogueOption> waitingMessages = [
    const DialogueOption(
      slug: 'waiting_1',
      text: "Tuning the instruments... 🎸",
    ),
    const DialogueOption(
      slug: 'waiting_2',
      text: "Setting up the stage... 🎪",
    ),
    const DialogueOption(
      slug: 'waiting_3',
      text: "Mixing the perfect beat... 🎛️",
    ),
    const DialogueOption(
      slug: 'waiting_4',
      text: "Getting the playlist ready... 🎵",
    ),
  ];
  
  /// Error messages (still friendly)
  static final List<DialogueOption> errorMessages = [
    const DialogueOption(
      slug: 'error_1',
      text: "Oops! Hit a wrong note there. Let's try that again! 🎹",
    ),
    const DialogueOption(
      slug: 'error_2',
      text: "Hmm, that didn't work. No worries, even the best DJs need a second take! 🎧",
    ),
    const DialogueOption(
      slug: 'error_3',
      text: "Technical difficulties! Don't worry, we'll get you back on track! 🛠️",
    ),
  ];
}

/// Individual dialogue option with slug for TTS
class DialogueOption {
  final String slug;
  final String text;
  
  const DialogueOption({
    required this.slug,
    required this.text,
  });
  
  Map<String, dynamic> toJson() => {
    'slug': slug,
    'text': text,
  };
} 