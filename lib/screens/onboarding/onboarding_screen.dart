import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import 'dart:io';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/user_provider.dart';
import '../../config/themes.dart';
import '../../utils/shared_preferences_storage.dart';
import '../../services/cloudinary_service.dart';
import '../../services/api/api_client.dart';
import 'widgets/onboarding_header.dart';
import 'widgets/email_check_step.dart';

import 'widgets/profile_setup_step.dart';
import 'widgets/email_verification_step.dart';
import 'widgets/appearance_setup_step.dart';
import 'widgets/onboarding_visualizer.dart';
import 'widgets/onboarding_progress.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({Key? key}) : super(key: key);

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  late AnimationController _animationController;
  late AnimationController _gradientController;
  late Animation<double> _fadeAnimation;
  
  int _currentStep = 0;
  int _totalSteps = 4; // Dynamic based on whether user exists
  
  // User data
  String _username = '';
  String _email = '';
  String? _profilePicturePath;
  bool _emailValidated = false;
  bool _emailVerified = false;
  bool _isExistingUser = false;
  Map<String, dynamic>? _existingUserData;
  Color _selectedThemeColor = AppTheme.primaryColor;
  bool _isDarkMode = false;
  
  // Keyboard visibility
  bool _isKeyboardVisible = false;
  
  // API client
  final ApiClient _apiClient = ApiClient();
  
  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _gradientController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
    _gradientController.repeat(reverse: true);
    
    // Initialize theme values from current provider state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
      setState(() {
        _selectedThemeColor = themeProvider.primaryColor;
        _isDarkMode = themeProvider.isDarkMode;
      });
      
      // Get navigation arguments if available (from login screen or music services)
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args != null && args is Map<String, dynamic>) {
        if (args['email'] != null) {
          setState(() {
            _email = args['email'];
          });
        }
        if (args['username'] != null) {
          setState(() {
            _username = args['username'];
          });
        }
        if (args['profile_pic'] != null) {
          setState(() {
            _profilePicturePath = args['profile_pic'];
          });
        }
      }
    });
  }
  
  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    _gradientController.dispose();
    super.dispose();
  }
  
  void _updateTotalSteps() {
    setState(() {
      if (_isExistingUser) {
        _totalSteps = 2; // Email Check + Email Verification (for login)
      } else {
        _totalSteps = 4; // Email Check + Profile Setup + Email Verification + Appearance
      }
    });
  }
  
  void _nextStep() {
    // Dismiss keyboard first for better UX
    FocusScope.of(context).unfocus();
    
    if (_currentStep < _totalSteps - 1) {
      setState(() => _currentStep++);
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      HapticFeedback.lightImpact();
    } else {
      if (_isExistingUser) {
        _attemptLogin();
      } else {
        _completeOnboarding();
      }
    }
  }
  
  void _previousStep() {
    // Dismiss keyboard first for better UX
    FocusScope.of(context).unfocus();
    
    if (_currentStep > 0) {
      setState(() => _currentStep--);
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      HapticFeedback.lightImpact();
    }
  }
  
  Future<void> _attemptLogin() async {
    HapticFeedback.mediumImpact();
    
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      // Show loading state
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  color: _selectedThemeColor,
                ),
                const SizedBox(height: 16),
                Text(
                  'Signing you in...',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
      
      // Attempt passwordless login with verified email
      final response = await _apiClient.post(
        '/auth/login/',
        body: {
          'email': _email,
          'email_verified': true,
        },
        requiresAuth: false,
      );
      
      if (response['auth_token'] != null && response['user'] != null) {
        // Login successful
        final loginSuccess = await authProvider.login(
          email: response['user']['email'],
          token: response['auth_token'],
          refreshToken: response['refresh_token'] ?? response['auth_token'],
          user: response['user'],
        );
        
        if (loginSuccess && mounted) {
          // Close loading dialog
          Navigator.of(context).pop();
          
          // Show completion animation
          await _animationController.reverse();
          
          if (mounted) {
            Navigator.of(context).pushReplacementNamed('/map');
          }
        }
      } else {
        // Close loading dialog
        if (mounted) Navigator.of(context).pop();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(child: Text('Login failed. Please try again.')),
                ],
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      // Close loading dialog if open
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
      
      if (kDebugMode) {
        print('❌ Login error: $e');
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(child: Text('Login failed. Please try again.')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }
  
  void _completeOnboarding() async {
    HapticFeedback.mediumImpact();
    
    try {
      // Get providers
      final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      // Show loading state
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  color: _selectedThemeColor,
                ),
                const SizedBox(height: 16),
                Text(
                  'Creating your account...',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
      
      // Apply theme changes
      await themeProvider.setPrimaryColor(_selectedThemeColor);
      await themeProvider.setThemeMode(_isDarkMode ? ThemeMode.dark : ThemeMode.light);
      
      // Register new user with backend
      try {
        if (kDebugMode) {
          print('🔄 Registering new user with backend');
        }
        
        // Create user account via backend (passwordless with email verification)
        final response = await _apiClient.post(
          '/auth/register/',
          body: {
            'username': _username,
            'email': _email,
            'email_verified': true, // User completed email verification
          },
          requiresAuth: false,
        );
        
        if (response['auth_token'] != null && response['user'] != null) {
          // Registration successful
          final loginSuccess = await authProvider.login(
            email: _email,
            token: response['auth_token'],
            refreshToken: response['refresh_token'] ?? response['auth_token'],
            user: response['user'],
          );
          
          if (loginSuccess) {
            // Upload profile picture if selected
            if (_profilePicturePath != null) {
              final userProvider = Provider.of<UserProvider>(context, listen: false);
              await userProvider.updateProfilePicture(_profilePicturePath!);
            }
            
            // Mark onboarding as completed
            final prefs = SharedPreferencesStorage.instance;
            await prefs.write(key: 'has_completed_onboarding_${response['user']['id']}', value: 'true');
            
            // Close loading dialog
            if (mounted) {
              Navigator.of(context).pop();
            }
            
            // Show completion animation
            await _animationController.reverse();
            
            if (mounted) {
              Navigator.of(context).pushReplacementNamed('/map');
            }
          } else {
            throw Exception('Failed to authenticate after registration');
          }
        } else {
          throw Exception('Registration failed: Invalid response');
        }
      } catch (e) {
        // Close loading dialog if open
        if (mounted && Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
        
        if (kDebugMode) {
          print('❌ Registration error: $e');
        }
        
        if (mounted) {
          String errorMessage = 'Failed to create account. Please try again.';
          
          // Parse specific error messages
          if (e.toString().contains('username') && e.toString().contains('exists')) {
            errorMessage = 'This username is already taken';
          } else if (e.toString().contains('email') && e.toString().contains('exists')) {
            errorMessage = 'An account with this email already exists';
          }
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(child: Text(errorMessage)),
                ],
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      // Close loading dialog if open
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
      
      if (kDebugMode) {
        print('❌ Error during onboarding completion: $e');
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(child: Text('An error occurred: ${e.toString()}')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }
  
  bool _canProceed() {
    switch (_currentStep) {
      case 0: // Email Check Step
        return _emailValidated && _email.isNotEmpty;
      case 1: 
        if (_isExistingUser) {
          // Email Verification Step (for existing users) - handled automatically
          return _emailVerified;
        } else {
          // Profile Setup (username + email + picture)
          final usernameValid = _username.length >= 2 && 
                               _username.length <= 30 && 
                               RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(_username);
          final emailValid = _email.isNotEmpty && 
                            RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(_email);
          return usernameValid && emailValid;
        }
      case 2: // Email Verification (only for new users)
        if (!_isExistingUser) {
          return _emailVerified;
        }
        return false;
      case 3: // Appearance Setup (only for new users)
        if (!_isExistingUser) {
          return true;
        }
        return false;
      default:
        return false;
    }
  }
  
  List<Widget> _buildSteps() {
    List<Widget> steps = [
      // Step 0: Email Check (always first)
      EmailCheckStep(
        email: _email,
        themeColor: _selectedThemeColor,
        onEmailChanged: (value) => setState(() => _email = value),
        onEmailValidated: (isValid) => setState(() => _emailValidated = isValid),
        onExistingUserDataChanged: (userData) {
          setState(() {
            _existingUserData = userData;
            _isExistingUser = userData != null;
          });
          _updateTotalSteps();
        },
      ),
    ];
    
    if (_isExistingUser && _existingUserData != null) {
      // Step 1: Email Verification (for existing user login)
      steps.add(
        EmailVerificationStep(
          email: _email,
          themeColor: _selectedThemeColor,
          onVerificationCompleted: () {
            setState(() => _emailVerified = true);
            // Auto-proceed to login after verification
            Future.delayed(const Duration(milliseconds: 500), () {
              _attemptLogin();
            });
          },
        ),
      );
    } else {
      // Steps for new users
      steps.addAll([
        // Step 1: Profile Setup
        ProfileSetupStep(
          username: _username,
          email: _email,
          profilePicturePath: _profilePicturePath,
          themeColor: _selectedThemeColor,
          onUsernameChanged: (value) => setState(() => _username = value),
          onEmailChanged: (value) => setState(() => _email = value),
          onProfilePictureChanged: (path) => setState(() => _profilePicturePath = path),
        ),
        // Step 2: Email Verification
        EmailVerificationStep(
          email: _email,
          themeColor: _selectedThemeColor,
          onVerificationCompleted: () => setState(() => _emailVerified = true),
        ),
        // Step 3: Appearance Setup
        AppearanceSetupStep(
          selectedThemeColor: _selectedThemeColor,
          isDarkMode: _isDarkMode,
          onThemeColorChanged: (color) {
            setState(() => _selectedThemeColor = color);
            // Apply color change immediately for preview
            final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
            themeProvider.setPrimaryColor(color);
          },
          onModeChanged: (isDark) {
            setState(() => _isDarkMode = isDark);
            // Apply theme mode change immediately for preview
            final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
            themeProvider.setThemeMode(isDark ? ThemeMode.dark : ThemeMode.light);
          },
        ),
      ]);
    }
    
    return steps;
  }
  
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    // Detect keyboard visibility
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;
    
    // Update keyboard visibility state
    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isKeyboardVisible = isKeyboardVisible;
          });
        }
      });
    }
    
    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0A0A0A) : const Color(0xFFFAFAFA),
      body: Stack(
        children: [
          // Animated gradient background
          AnimatedBuilder(
            animation: _gradientController,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      _selectedThemeColor.withOpacity(0.05),
                      _selectedThemeColor.withOpacity(0.02),
                      _selectedThemeColor.withOpacity(0.08),
                    ],
                    stops: [
                      0.0,
                      _gradientController.value,
                      1.0,
                    ],
                  ),
                ),
              );
            },
          ),
          
          // Visualizer at the very bottom - only show when keyboard is hidden
          if (!_isKeyboardVisible)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 80,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      (isDark ? const Color(0xFF0A0A0A) : const Color(0xFFFAFAFA)).withOpacity(0.3),
                    ],
                  ),
                ),
                child: OnboardingVisualizer(
                  themeColor: _selectedThemeColor,
                  isAnimating: true,
                ),
              ),
            ),
          
          // Main content
          SafeArea(
            child: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(), // Dismiss keyboard on tap outside
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    // Header with progress
                    OnboardingHeader(
                      currentStep: _currentStep,
                      totalSteps: _totalSteps,
                      onBack: _currentStep > 0 ? _previousStep : null,
                    ),
                    
                    // Content area - More space for forms
                    Expanded(
                      child: PageView(
                        controller: _pageController,
                        physics: const NeverScrollableScrollPhysics(),
                        children: _buildSteps(),
                      ),
                    ),
                    
                    // Bottom section with continue button only - hide when keyboard is visible
                    if (!_isKeyboardVisible)
                      Container(
                        padding: const EdgeInsets.fromLTRB(24, 16, 24, 32),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Continue button
                            SizedBox(
                              width: double.infinity,
                              height: 56,
                              child: ElevatedButton(
                                onPressed: _canProceed() ? _nextStep : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: _selectedThemeColor,
                                  foregroundColor: Colors.white,
                                  elevation: 0,
                                  shadowColor: _selectedThemeColor.withOpacity(0.3),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  disabledBackgroundColor: _selectedThemeColor.withOpacity(0.3),
                                ),
                                child: Text(
                                  _getButtonText(),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: 16),
                            
                            // Progress indicator
                            OnboardingProgress(
                              currentStep: _currentStep,
                              totalSteps: _totalSteps,
                              themeColor: _selectedThemeColor,
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  String _getButtonText() {
    if (_isExistingUser) {
      switch (_currentStep) {
        case 0:
          return 'Continue';
        case 1:
          return 'Verify Email'; // Email verification step
        default:
          return 'Continue';
      }
    } else {
      switch (_currentStep) {
        case 0:
          return 'Continue';
        case 1:
          return 'Continue';
        case 2:
          return 'Continue';
        case 3:
          return 'Complete Setup';
        default:
          return 'Continue';
      }
    }
  }
} 