import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/themes.dart';
import '../../../config/theme_constants.dart';
import 'dart:ui';

class AppearanceSetupStep extends StatefulWidget {
  final Color selectedThemeColor;
  final bool isDarkMode;
  final ValueChanged<Color> onThemeColorChanged;
  final ValueChanged<bool> onModeChanged;

  const AppearanceSetupStep({
    Key? key,
    required this.selectedThemeColor,
    required this.isDarkMode,
    required this.onThemeColorChanged,
    required this.onModeChanged,
  }) : super(key: key);

  @override
  State<AppearanceSetupStep> createState() => _AppearanceSetupStepState();
}

class _AppearanceSetupStepState extends State<AppearanceSetupStep> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Available theme colors - matching the settings screen exactly
  final List<Color> _themeColors = [
    AppTheme.primaryColor,     // Pink
    AppTheme.rockColor,        // Red
    AppTheme.hiphopColor,      // Green
    AppTheme.electronicColor,  // Blue
    AppTheme.jazzColor,        // Orange
    const Color(0xFF9C27B0),   // Purple
    const Color(0xFF607D8B),   // Blue Grey
    const Color(0xFF795548),   // Brown
    themeColorCyan,            // Cyan - Color(0xFF09C3DB)
    themeColorBlue,            // Rich Blue - Color(0xFF3B7DD8)
  ];
  
  final List<String> _colorNames = [
    'Pink',
    'Red',
    'Green',
    'Blue',
    'Orange',
    'Purple',
    'Grey',
    'Brown',
    'Cyan',
    'Rich Blue',
  ];

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _selectColor(Color color) {
    HapticFeedback.lightImpact();
    widget.onThemeColorChanged(color);
  }

  void _toggleMode(bool isDark) {
    HapticFeedback.lightImpact();
    widget.onModeChanged(isDark);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          child: Column(
            children: [
              const SizedBox(height: 20),
              
              // Title
              Text(
                'Customize your experience',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w700,
                  color: theme.colorScheme.onSurface,
                  letterSpacing: -0.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Subtitle
              Text(
                'Choose your theme color and appearance',
                style: TextStyle(
                  fontSize: 16,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              // Theme Color Section
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.1),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: widget.selectedThemeColor.withOpacity(0.1),
                      blurRadius: 15,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.palette_outlined,
                          color: widget.selectedThemeColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Theme Color',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 12,
                      runSpacing: 12,
                      children: _themeColors.asMap().entries.map((entry) {
                        final index = entry.key;
                        final color = entry.value;
                        final isSelected = color.value == widget.selectedThemeColor.value;
                        
                        return GestureDetector(
                          onTap: () => _selectColor(color),
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isSelected ? Colors.white : Colors.transparent,
                                width: 3,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: color.withOpacity(isSelected ? 0.4 : 0.2),
                                  blurRadius: isSelected ? 12 : 6,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: isSelected
                              ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 20,
                                )
                              : null,
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Appearance Mode Section
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.1),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: widget.selectedThemeColor.withOpacity(0.1),
                      blurRadius: 15,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          widget.isDarkMode ? Icons.dark_mode_outlined : Icons.light_mode_outlined,
                          color: widget.selectedThemeColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Appearance',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Column(
                      children: [
                        // Light mode option
                        _buildModeOption(
                          title: 'Light Mode',
                          subtitle: 'Clean and bright interface',
                          icon: Icons.light_mode_rounded,
                          isSelected: !widget.isDarkMode,
                          onTap: () => _toggleMode(false),
                          gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: [
                              Colors.white,
                              Colors.grey.shade50,
                            ],
                          ),
                          textColor: Colors.black87,
                        ),
                        
                        const SizedBox(height: 12),
                        
                        // Dark mode option
                        _buildModeOption(
                          title: 'Dark Mode',
                          subtitle: 'Easy on the eyes',
                          icon: Icons.dark_mode_rounded,
                          isSelected: widget.isDarkMode,
                          onTap: () => _toggleMode(true),
                          gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: [
                              const Color(0xFF1E1E1E),
                              const Color(0xFF121212),
                            ],
                          ),
                          textColor: Colors.white,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Info note
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: widget.selectedThemeColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: widget.selectedThemeColor.withOpacity(0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: widget.selectedThemeColor,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'You can change these settings anytime in the app',
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModeOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
    required Gradient gradient,
    required Color textColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected 
              ? widget.selectedThemeColor 
              : Colors.transparent,
            width: 2,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: widget.selectedThemeColor.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ] : [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isSelected 
                  ? widget.selectedThemeColor.withOpacity(0.2)
                  : textColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isSelected ? widget.selectedThemeColor : textColor.withOpacity(0.7),
                size: 20,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: textColor,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 13,
                      color: textColor.withOpacity(0.7),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
            
            // Selection indicator
            if (isSelected)
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: widget.selectedThemeColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 14,
                ),
              ),
          ],
        ),
      ),
    );
  }
} 