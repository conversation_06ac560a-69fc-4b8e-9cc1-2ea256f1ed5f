import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/themes.dart';
import 'dart:ui';

class ThemeSelectionStep extends StatefulWidget {
  final Color selectedColor;
  final ValueChanged<Color> onColorChanged;

  const ThemeSelectionStep({
    Key? key,
    required this.selectedColor,
    required this.onColorChanged,
  }) : super(key: key);

  @override
  State<ThemeSelectionStep> createState() => _ThemeSelectionStepState();
}

class _ThemeSelectionStepState extends State<ThemeSelectionStep> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Available theme colors
  final List<Color> _themeColors = [
    AppTheme.primaryColor,     // Pink
    AppTheme.popColor,         // Pink variant
    AppTheme.rockColor,        // Red
    AppTheme.hiphopColor,      // Green
    AppTheme.electronicColor,  // Blue
    AppTheme.jazzColor,        // Orange
    const Color(0xFF9C27B0),   // Purple
    const Color(0xFF00BCD4),   // Cyan
    const Color(0xFF795548),   // Brown
    const Color(0xFF607D8B),   // Blue Grey
    const Color(0xFFFF5722),   // Deep Orange
    const Color(0xFF3F51B5),   // Indigo
  ];
  
  final List<String> _colorNames = [
    'Pink',
    'Pop Pink',
    'Rock Red',
    'Hip Hop Green',
    'Electronic Blue',
    'Jazz Orange',
    'Purple',
    'Cyan',
    'Brown',
    'Blue Grey',
    'Deep Orange',
    'Indigo',
  ];

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _selectColor(Color color) {
    HapticFeedback.lightImpact();
    widget.onColorChanged(color);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          children: [
            const SizedBox(height: 40),
            
            // Icon with selected color
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: widget.selectedColor.withOpacity(0.1),
                shape: BoxShape.circle,
                border: Border.all(
                  color: widget.selectedColor.withOpacity(0.3),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: widget.selectedColor.withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Icon(
                Icons.palette_outlined,
                size: 36,
                color: widget.selectedColor,
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Title
            Text(
              'Choose your theme color',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.w700,
                color: theme.colorScheme.onSurface,
                letterSpacing: -0.5,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            // Subtitle
            Text(
              'This color will be used throughout\nthe app for accents and highlights',
              style: TextStyle(
                fontSize: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 48),
            
            // Color grid
            Expanded(
              child: GridView.builder(
                physics: const BouncingScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: _themeColors.length,
                itemBuilder: (context, index) {
                  final color = _themeColors[index];
                  final isSelected = color.value == widget.selectedColor.value;
                  
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    child: GestureDetector(
                      onTap: () => _selectColor(color),
                      child: Container(
                        decoration: BoxDecoration(
                          color: color.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected 
                              ? color 
                              : color.withOpacity(0.2),
                            width: isSelected ? 3 : 2,
                          ),
                          boxShadow: isSelected ? [
                            BoxShadow(
                              color: color.withOpacity(0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 6),
                            ),
                          ] : [
                            BoxShadow(
                              color: color.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Color circle
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: color,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: color.withOpacity(0.4),
                                    blurRadius: 8,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: isSelected
                                ? const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 20,
                                  )
                                : null,
                            ),
                            
                            const SizedBox(height: 8),
                            
                            // Color name
                            Text(
                              _colorNames[index],
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: theme.colorScheme.onSurface,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
} 