import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'dart:ui';
import 'dart:async';
import '../../../providers/user_provider.dart';

class ProfileSetupStepSpotify extends StatefulWidget {
  final String username;
  final String email;
  final String? profilePicturePath;
  final Color themeColor;
  final ValueChanged<String> onUsernameChanged;
  final ValueChanged<String> onEmailChanged;
  final ValueChanged<String?> onProfilePictureChanged;

  const ProfileSetupStepSpotify({
    Key? key,
    required this.username,
    required this.email,
    required this.profilePicturePath,
    required this.themeColor,
    required this.onUsernameChanged,
    required this.onEmailChanged,
    required this.onProfilePictureChanged,
  }) : super(key: key);

  @override
  State<ProfileSetupStepSpotify> createState() => _ProfileSetupStepSpotifyState();
}

class _ProfileSetupStepSpotifyState extends State<ProfileSetupStepSpotify> with SingleTickerProviderStateMixin {
  late TextEditingController _usernameController;
  late TextEditingController _emailController;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  final FocusNode _usernameFocusNode = FocusNode();
  final FocusNode _emailFocusNode = FocusNode();
  final ImagePicker _picker = ImagePicker();
  
  bool _isUsernameValid = false;
  bool _isCheckingUsername = false;
  String? _usernameErrorMessage;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      print('📧 [ProfileSetupStepSpotify] Initializing with email: ${widget.email}');
    }
    _usernameController = TextEditingController(text: widget.username);
    _emailController = TextEditingController(text: widget.email);
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
    
    // Add listeners for real-time validation
    _usernameController.addListener(_validateUsername);
    
    // Auto-focus after animation
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) _usernameFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _animationController.dispose();
    _usernameFocusNode.dispose();
    _emailFocusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(ProfileSetupStepSpotify oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update email controller if email changed
    if (oldWidget.email != widget.email) {
      if (kDebugMode) {
        print('📧 [ProfileSetupStepSpotify] Email updated from "${oldWidget.email}" to "${widget.email}"');
      }
      _emailController.text = widget.email;
    }
    
    // Update username controller if username changed
    if (oldWidget.username != widget.username) {
      if (kDebugMode) {
        print('👤 [ProfileSetupStepSpotify] Username updated from "${oldWidget.username}" to "${widget.username}"');
      }
      _usernameController.text = widget.username;
    }
  }

  void _validateUsername() {
    final value = _usernameController.text;
    
    // Cancel previous timer
    _debounceTimer?.cancel();
    
    setState(() {
      if (value.isEmpty) {
        _isUsernameValid = false;
        _usernameErrorMessage = null;
        _isCheckingUsername = false;
      } else if (value.length < 2) {
        _isUsernameValid = false;
        _usernameErrorMessage = 'Username must be at least 2 characters';
        _isCheckingUsername = false;
      } else if (value.length > 30) {
        _isUsernameValid = false;
        _usernameErrorMessage = 'Username must be 30 characters or less';
        _isCheckingUsername = false;
      } else if (!RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(value)) {
        _isUsernameValid = false;
        _usernameErrorMessage = 'Username can only contain letters, numbers, underscore, and hyphen';
        _isCheckingUsername = false;
      } else {
        // Valid format, check availability after debounce
        _isUsernameValid = true;
        _usernameErrorMessage = null;
        _isCheckingUsername = true;
        
        // Debounce the API call
        _debounceTimer = Timer(const Duration(milliseconds: 800), () {
          _checkUsernameAvailability(value);
        });
      }
    });
    
    widget.onUsernameChanged(value);
  }

  Future<void> _checkUsernameAvailability(String username) async {
    if (!mounted) return;
    
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      
      final isAvailable = await userProvider.checkUsernameAvailability(username);
      
      if (mounted) {
        setState(() {
          _isCheckingUsername = false;
          if (isAvailable) {
            // Username is available
            _isUsernameValid = true;
            _usernameErrorMessage = null;
          } else {
            // Username is taken
            _isUsernameValid = false;
            _usernameErrorMessage = 'This username is already taken';
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isCheckingUsername = false;
          // For errors, assume username is valid to not block user
          _isUsernameValid = true;
          _usernameErrorMessage = null;
        });
      }
      
      if (kDebugMode) {
        print('❌ Error checking username availability during onboarding: $e');
      }
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      HapticFeedback.lightImpact();
      
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );
      
      if (image != null) {
        widget.onProfilePictureChanged(image.path);
        HapticFeedback.mediumImpact();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage() {
    HapticFeedback.lightImpact();
    widget.onProfilePictureChanged(null);
  }

  bool _isNetworkImage(String? path) {
    if (path == null) return false;
    return path.startsWith('http://') || path.startsWith('https://');
  }

  Widget _buildProfileImage() {
    if (widget.profilePicturePath == null) {
      return Icon(
        Icons.camera_alt_outlined,
        size: 36,
        color: widget.themeColor,
      );
    }

    final isNetwork = _isNetworkImage(widget.profilePicturePath);
    
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(50),
          child: isNetwork
            ? Image.network(
                widget.profilePicturePath!,
                width: 100,
                height: 100,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 100,
                    height: 100,
                    color: widget.themeColor.withOpacity(0.1),
                    child: Icon(
                      Icons.person,
                      size: 40,
                      color: widget.themeColor,
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    width: 100,
                    height: 100,
                    color: widget.themeColor.withOpacity(0.1),
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                          : null,
                        color: widget.themeColor,
                        strokeWidth: 2,
                      ),
                    ),
                  );
                },
              )
            : Image.file(
                File(widget.profilePicturePath!),
                width: 100,
                height: 100,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 100,
                    height: 100,
                    color: widget.themeColor.withOpacity(0.1),
                    child: Icon(
                      Icons.person,
                      size: 40,
                      color: widget.themeColor,
                    ),
                  );
                },
              ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: _removeImage,
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 14,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Choose Photo Source',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildSourceOption(
                    icon: Icons.camera_alt_rounded,
                    label: 'Camera',
                    onTap: () {
                      Navigator.pop(context);
                      _pickImage(ImageSource.camera);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSourceOption(
                    icon: Icons.photo_library_rounded,
                    label: 'Gallery',
                    onTap: () {
                      Navigator.pop(context);
                      _pickImage(ImageSource.gallery);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Material(
      color: widget.themeColor.withOpacity(0.1),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: widget.themeColor,
              ),
              const SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    if (kDebugMode) {
      print('📧 [ProfileSetupStepSpotify] Building with email controller text: "${_emailController.text}"');
      print('📧 [ProfileSetupStepSpotify] Widget email: "${widget.email}"');
    }
    
    return ScaleTransition(
      scale: _scaleAnimation,
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          child: Column(
            children: [
              const SizedBox(height: 20),
              
              // Title
              Text(
                'Set up your profile',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w700,
                  color: theme.colorScheme.onSurface,
                  letterSpacing: -0.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Subtitle
              Text(
                'Choose a username and profile picture',
                style: TextStyle(
                  fontSize: 16,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              // Profile picture
              GestureDetector(
                onTap: () => _showImageSourceDialog(),
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.themeColor.withOpacity(0.1),
                    border: Border.all(
                      color: widget.themeColor.withOpacity(0.3),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: widget.themeColor.withOpacity(0.2),
                        blurRadius: 15,
                        offset: const Offset(0, 6),
                      ),
                    ],
                  ),
                  child: _buildProfileImage(),
                ),
              ),
              
              const SizedBox(height: 8),
              
              // Picture hint
              Text(
                widget.profilePicturePath != null && _isNetworkImage(widget.profilePicturePath)
                  ? 'From Spotify • Tap to change (optional)'
                  : 'Tap to add photo (optional)',
                style: TextStyle(
                  fontSize: 12,
                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Username input
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 4, bottom: 8),
                      child: Row(
                        children: [
                          Text(
                            'Username',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.onSurface.withOpacity(0.8),
                              letterSpacing: 0.2,
                            ),
                          ),
                          if (widget.username.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: widget.themeColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'from Spotify',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: widget.themeColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: theme.colorScheme.surface,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.04),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextField(
                        controller: _usernameController,
                        focusNode: _usernameFocusNode,
                        // Always editable for Spotify users
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface,
                          letterSpacing: 0.2,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Enter your username',
                          hintStyle: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface.withOpacity(0.4),
                            fontWeight: FontWeight.w400,
                          ),
                          prefixIcon: Container(
                            margin: const EdgeInsets.only(left: 12, right: 8),
                            child: Icon(
                              Icons.person_outline_rounded,
                              color: _usernameFocusNode.hasFocus 
                                ? widget.themeColor
                                : theme.colorScheme.onSurface.withOpacity(0.5),
                              size: 20,
                            ),
                          ),
                          suffixIcon: _usernameController.text.isNotEmpty
                            ? Container(
                                margin: const EdgeInsets.only(right: 12),
                                child: _isCheckingUsername
                                  ? SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(widget.themeColor),
                                      ),
                                    )
                                  : Icon(
                                      _isUsernameValid ? Icons.check_circle_rounded : Icons.error_rounded,
                                      color: _isUsernameValid ? Colors.green.shade600 : Colors.red.shade600,
                                      size: 20,
                                    ),
                              )
                            : null,
                          filled: true,
                          fillColor: theme.colorScheme.surface,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.outline.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.outline.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: widget.themeColor,
                              width: 2,
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Colors.red.shade400,
                              width: 1,
                            ),
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Colors.red.shade400,
                              width: 2,
                            ),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                          prefixIconConstraints: const BoxConstraints(
                            minWidth: 48,
                            minHeight: 48,
                          ),
                          suffixIconConstraints: const BoxConstraints(
                            minWidth: 48,
                            minHeight: 48,
                          ),
                        ),
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(30),
                          FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9_-]')),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              // Error/Success message
              const SizedBox(height: 12),
              Container(
                height: 20,
                child: _isCheckingUsername
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 1.5,
                            valueColor: AlwaysStoppedAnimation<Color>(widget.themeColor),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Checking availability...',
                          style: TextStyle(
                            fontSize: 12,
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    )
                  : _usernameErrorMessage != null
                    ? Text(
                        _usernameErrorMessage!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.red.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      )
                    : _isUsernameValid && _usernameController.text.isNotEmpty
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.check_circle,
                              size: 14,
                              color: Colors.green.shade600,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              'Username looks great!',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.green.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        )
                      : null,
              ),
              
              const SizedBox(height: 24),
              
              // Email input (read-only for Spotify users)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 4, bottom: 8),
                      child: Row(
                        children: [
                          Text(
                            'Email',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.onSurface.withOpacity(0.8),
                              letterSpacing: 0.2,
                            ),
                          ),
                          if (widget.email.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: widget.themeColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'from Spotify',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: widget.themeColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: theme.colorScheme.onSurface.withOpacity(0.03),
                        boxShadow: null,
                      ),
                      child: TextField(
                        controller: _emailController,
                        focusNode: _emailFocusNode,
                        readOnly: true, // Email is read-only for Spotify users
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface,
                          letterSpacing: 0.2,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Email from Spotify',
                          hintStyle: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface.withOpacity(0.4),
                            fontWeight: FontWeight.w400,
                          ),
                          prefixIcon: Container(
                            margin: const EdgeInsets.only(left: 12, right: 8),
                            child: Icon(
                              Icons.email_outlined,
                              color: theme.colorScheme.onSurface.withOpacity(0.3),
                              size: 20,
                            ),
                          ),
                          suffixIcon: Container(
                            margin: const EdgeInsets.only(right: 12),
                            child: Icon(
                              Icons.check_circle_rounded,
                              color: Colors.green.shade600,
                              size: 20,
                            ),
                          ),
                          filled: true,
                          fillColor: theme.colorScheme.surface,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.outline.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.outline.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: widget.themeColor,
                              width: 2,
                            ),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                          prefixIconConstraints: const BoxConstraints(
                            minWidth: 48,
                            minHeight: 48,
                          ),
                          suffixIconConstraints: const BoxConstraints(
                            minWidth: 48,
                            minHeight: 48,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Email success message
              const SizedBox(height: 12),
              Container(
                height: 20,
                child: widget.email.isNotEmpty
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 14,
                          color: Colors.green.shade600,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'Email verified from Spotify',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.green.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    )
                  : null,
              ),
            ],
          ),
        ),
      ),
    );
  }
} 