# AI Onboarding Screen Widget Organization

This directory contains the refactored components of the AI Onboarding Screen, organized into logical, reusable widgets.

## Directory Structure

```
widgets/
├── README.md                    # This file
├── animated_background.dart     # Animated gradient background
├── header_widget.dart          # App header with logo and progress indicator
├── welcome_screen.dart         # Initial welcome screen with Lottie animation
├── chat_interface.dart         # Complete chat interface with messages and typing
├── auth_options.dart           # Google and Email authentication buttons
├── genre_selector.dart         # Music genre selection with search
├── artist_selector.dart        # Artist selection with Spotify integration
├── song_selector.dart          # Song selection with album art display
└── input_area.dart             # Text input area for chat

models/
└── onboarding_models.dart      # ChatMessage class and OnboardingStep enum
```

## Widget Overview

### Core Components

#### `AnimatedBackground`
- Animated gradient background that responds to theme changes
- Uses sine wave animation for smooth color transitions
- **Props**: `backgroundController`, `isDarkMode`

#### `OnboardingHeader`
- Displays app logo and progress indicator
- Shows current step progress as a filled bar
- **Props**: `currentStep`

#### `WelcomeScreen`
- Initial onboarding screen with <PERSON>tie animation
- Contains welcome text and "Get Started" button
- **Props**: `onGetStarted`

### Chat System

#### `ChatInterface`
- Main chat container with fade/slide animations
- Manages message list and typing indicators
- **Props**: `fadeAnimation`, `slideAnimation`, `messages`, `scrollController`, `isAITyping`, `backgroundController`, `stepContent`

#### `MessageBubble`
- Individual chat message with AI/user styling
- Supports special content (achievement cards, etc.)
- **Props**: `message`

#### `TypingIndicator`
- Animated dots showing AI is typing
- **Props**: `backgroundController`

#### `InputArea`
- Text input field with send button
- Conditional visibility based on step
- **Props**: `textController`, `onSubmitted`, `shouldShow`

### Selection Components

#### `AuthOptions`
- Google and Email sign-in buttons
- Loading states during authentication
- **Props**: `isAuthenticating`, `onGoogleSignIn`, `onEmailSignIn`

#### `GenreSelector`
- Music genre chips with search functionality
- Animated transitions for search states
- Multiple rows of genre options
- **Props**: `selectedGenres`, `onGenresChanged`, `isProcessing`, `onDone`

#### `ArtistSelector`
- Horizontal scrolling artist cards
- Spotify search integration
- Artist image display with fallbacks
- **Props**: `availableArtists`, `filteredArtists`, `selectedArtists`, `searchController`, etc.

#### `SongSelector`
- Horizontal song cards with album art
- Track search with artist/song queries
- Selection indicators and loading states
- **Props**: `availableSongs`, `filteredSongs`, `selectedTrack`, `searchController`, etc.

## Models

#### `ChatMessage`
- Message data structure
- **Fields**: `message`, `isAI`, `timestamp`, `specialContent`

#### `OnboardingStep`
- Enum defining the onboarding flow
- **Values**: `welcome`, `auth`, `genres`, `genreComment`, `artists`, `songSelection`, `pinPlacement`, `celebration`, `completion`

## Usage

### Main Implementation
The refactored screen is implemented in `ai_onboarding_screen_refactored.dart`, which:
- Imports all widget components
- Manages state and business logic
- Coordinates between widgets
- Handles Spotify API calls
- Manages authentication flow

### Key Benefits of This Organization

1. **Separation of Concerns**: Each widget has a single responsibility
2. **Reusability**: Components can be used in other parts of the app
3. **Testability**: Individual widgets can be unit tested
4. **Maintainability**: Changes to one component don't affect others
5. **Readability**: Easier to understand and navigate code structure

### Integration

To use the refactored version, replace the import in your routing:

```dart
// OLD
import '../onboarding/ai_onboarding_screen.dart';

// NEW
import '../onboarding/ai_onboarding_screen_refactored.dart';
```

Then update the widget reference:
```dart
// OLD
AIOnboardingScreen()

// NEW  
AIOnboardingScreenRefactored()
```

## Dependencies

Each widget imports only the dependencies it needs:
- `flutter/material.dart` - Core Flutter widgets
- `flutter_animate` - Animation effects (genre selector)
- `lottie` - Lottie animations (welcome, chat)
- `../../../theme/app_colors.dart` - App color scheme
- `../models/onboarding_models.dart` - Local models