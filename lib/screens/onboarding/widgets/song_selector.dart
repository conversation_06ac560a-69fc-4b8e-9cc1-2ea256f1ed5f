import 'package:flutter/material.dart';
import '../../../theme/app_colors.dart';

class SongSelector extends StatefulWidget {
  final List<Map<String, dynamic>> availableSongs;
  final List<Map<String, dynamic>> filteredSongs;
  final Map<String, dynamic>? selectedTrack;
  final TextEditingController searchController;
  final bool isLoadingSongs;
  final bool isSearchingSongs;
  final Function(String) onSearchChanged;
  final Function(Map<String, dynamic>) onSongSelected;
  final VoidCallback onClearSearch;

  const SongSelector({
    Key? key,
    required this.availableSongs,
    required this.filteredSongs,
    required this.selectedTrack,
    required this.searchController,
    required this.isLoadingSongs,
    required this.isSearchingSongs,
    required this.onSearchChanged,
    required this.onSongSelected,
    required this.onClearSearch,
  }) : super(key: key);

  @override
  State<SongSelector> createState() => _SongSelectorState();
}

class _SongSelectorState extends State<SongSelector> {
  @override
  Widget build(BuildContext context) {
    // Use filtered songs if search is active, otherwise use all available songs
    final displaySongs = widget.filteredSongs.isNotEmpty
        ? widget.filteredSongs
        : widget.availableSongs;
    final isSearchActive = widget.searchController.text.isNotEmpty &&
        widget.searchController.text.length >= 3;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              const Icon(
                Icons.music_note,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Pick Your First Song',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).textTheme.headlineSmall?.color,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Search Bar for Songs
          TextField(
            controller: widget.searchController,
            decoration: InputDecoration(
              hintText: 'Search songs or artists...',
              prefixIcon: widget.isSearchingSongs
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.search),
              suffixIcon: widget.searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: widget.onClearSearch,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: widget.onSearchChanged,
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            isSearchActive
                ? 'Search results from Spotify:'
                : 'Choose a song inspired by your favorite artists to create your first musical pin!',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.color
                  ?.withOpacity(0.7),
            ),
          ),

          const SizedBox(height: 20),

          // Song List
          _buildSongList(displaySongs),
        ],
      ),
    );
  }

  Widget _buildSongList(List<Map<String, dynamic>> displaySongs) {
    if (widget.isLoadingSongs) {
      return Container(
        height: 180,
        alignment: Alignment.center,
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Finding perfect songs for you...'),
          ],
        ),
      );
    }

    if (displaySongs.isEmpty) {
      return Container(
        height: 180,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.music_off,
              size: 48,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No songs found',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try a different search term or check your connection.',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return SizedBox(
      height: 180, // Fixed height for horizontal list
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        scrollDirection: Axis.horizontal,
        itemCount: displaySongs.length,
        itemBuilder: (context, index) {
          final song = displaySongs[index];
          final isSelected = widget.selectedTrack?['id'] == song['id'];

          return Container(
            width: 150, // Fixed width for each card
            margin: const EdgeInsets.only(right: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? AppColors.primary : Colors.transparent,
                width: 2,
              ),
              color: Theme.of(context).cardColor.withOpacity(0.1),
            ),
            child: InkWell(
              onTap: () => widget.onSongSelected(song),
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Song Cover Image
                  if (song['album_cover'] != null)
                    Positioned.fill(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          song['album_cover'],
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            color: Theme.of(context).colorScheme.surfaceVariant,
                            child: Icon(
                              Icons.music_note,
                              size: 40,
                              color: Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.5),
                            ),
                          ),
                        ),
                      ),
                    ),

                  // Gradient overlay
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.8),
                          ],
                          stops: const [0.5, 1.0],
                        ),
                      ),
                    ),
                  ),

                  // Song Info
                  Positioned(
                    bottom: 8,
                    left: 8,
                    right: 8,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          song['name'] ?? 'Unknown Song',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          song['artist'] ?? 'Unknown Artist',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 10,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // Selection indicator
                  if (isSelected)
                    const Positioned(
                      top: 8,
                      right: 8,
                      child: Icon(
                        Icons.check_circle,
                        color: AppColors.primary,
                        size: 20,
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
