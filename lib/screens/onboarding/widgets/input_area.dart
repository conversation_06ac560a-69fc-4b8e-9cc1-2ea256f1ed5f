import 'package:flutter/material.dart';
import '../../../theme/app_colors.dart';

class InputArea extends StatelessWidget {
  final TextEditingController textController;
  final Function(String) onSubmitted;
  final bool shouldShow;

  const InputArea({
    Key? key,
    required this.textController,
    required this.onSubmitted,
    required this.shouldShow,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!shouldShow) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: textController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onSubmitted: onSubmitted,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () => onSubmitted(textController.text),
            icon: const Icon(Icons.send),
            style: IconButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
