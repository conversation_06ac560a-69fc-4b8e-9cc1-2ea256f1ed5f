import 'package:flutter/material.dart';
import '../../../theme/app_colors.dart';
import '../../../widgets/profile/components/artist_card.dart';

class ArtistSelector extends StatefulWidget {
  final List<Map<String, dynamic>> availableArtists;
  final List<Map<String, dynamic>> filteredArtists;
  final List<String> selectedArtists;
  final TextEditingController searchController;
  final bool isLoadingArtists;
  final bool isSearchingArtists;
  final Function(String) onSearchChanged;
  final Function(String) onArtistToggle;
  final VoidCallback onClearSearch;
  final VoidCallback onContinue;

  const ArtistSelector({
    Key? key,
    required this.availableArtists,
    required this.filteredArtists,
    required this.selectedArtists,
    required this.searchController,
    required this.isLoadingArtists,
    required this.isSearchingArtists,
    required this.onSearchChanged,
    required this.onArtistToggle,
    required this.onClearSearch,
    required this.onContinue,
  }) : super(key: key);

  @override
  State<ArtistSelector> createState() => _ArtistSelectorState();
}

class _ArtistSelectorState extends State<ArtistSelector> {
  @override
  Widget build(BuildContext context) {
    // Use filtered artists if search is active, otherwise use all available artists
    final displayArtists = widget.filteredArtists.isNotEmpty
        ? widget.filteredArtists
        : widget.availableArtists;
    final isSearchActive = widget.searchController.text.isNotEmpty &&
        widget.searchController.text.length >= 3;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              const Icon(
                Icons.person,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Choose Your Favorite Artists',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).textTheme.headlineSmall?.color,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Search Bar for Artists
          TextField(
            controller: widget.searchController,
            decoration: InputDecoration(
              hintText: 'Search artists...',
              prefixIcon: widget.isSearchingArtists
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.search),
              suffixIcon: widget.searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: widget.onClearSearch,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: widget.onSearchChanged,
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            isSearchActive
                ? 'Search results from Spotify:'
                : 'Artists from the genres you love:',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.color
                  ?.withOpacity(0.7),
            ),
          ),

          const SizedBox(height: 20),

          // Artist List (with constrained height)
          SizedBox(
            height: 180, // Fixed height for the artist list
            child: _buildArtistList(displayArtists),
          ),

          const SizedBox(height: 16),

          // Selected Artists Count
          if (widget.selectedArtists.isNotEmpty)
            Text(
              '${widget.selectedArtists.length} artists selected',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.7),
              ),
            ),

          const SizedBox(height: 16),

          // Continue Button
          if (widget.selectedArtists.isNotEmpty)
            ElevatedButton(
              onPressed: widget.onContinue,
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 48),
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Continue'),
            ),
        ],
      ),
    );
  }

  Widget _buildArtistList(List<Map<String, dynamic>> artists) {
    if (widget.isLoadingArtists) {
      return Container(
        height: 180,
        alignment: Alignment.center,
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Finding artists for your genres...'),
          ],
        ),
      );
    }

    if (artists.isEmpty) {
      final isSearchActive = widget.searchController.text.isNotEmpty &&
          widget.searchController.text.length >= 3;

      return Container(
        height: 180,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isSearchActive ? Icons.search_off : Icons.search_off,
              size: 48,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              isSearchActive ? 'No artists found' : 'No artists found',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isSearchActive
                  ? 'Try searching for another artist'
                  : 'Try selecting more genres or check your connection',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: artists.length,
      itemBuilder: (context, index) {
        final artist = artists[index];
        final isSelected = widget.selectedArtists.contains(artist['name']);

        return Container(
          width: 120,
          margin: const EdgeInsets.only(right: 12),
          child: GestureDetector(
            onTap: () => widget.onArtistToggle(artist['name']),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected ? AppColors.primary : Colors.transparent,
                  width: 2,
                ),
                color: Theme.of(context).cardColor.withOpacity(0.1),
              ),
              child: Column(
                children: [
                  // Artist Image
                  Expanded(
                    flex: 3,
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(10),
                        ),
                        color: Theme.of(context).colorScheme.surfaceVariant,
                      ),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(10),
                        ),
                        child: artist['image'] != null
                            ? Image.network(
                                artist['image'],
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    Icon(
                                  Icons.person,
                                  size: 40,
                                  color: Theme.of(context)
                                      .colorScheme
                                      .primary
                                      .withOpacity(0.5),
                                ),
                              )
                            : Icon(
                                Icons.person,
                                size: 40,
                                color: Theme.of(context)
                                    .colorScheme
                                    .primary
                                    .withOpacity(0.5),
                              ),
                      ),
                    ),
                  ),
                  // Artist Name
                  Expanded(
                    flex: 1,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      width: double.infinity,
                      child: Center(
                        child: Text(
                          artist['name'] ?? 'Unknown Artist',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color:
                                Theme.of(context).textTheme.bodyMedium?.color,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  // Selection indicator
                  if (isSelected)
                    Container(
                      padding: const EdgeInsets.all(4),
                      child: const Icon(
                        Icons.check_circle,
                        color: AppColors.primary,
                        size: 16,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
