import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui';
import 'dart:async';
import '../../../services/api/api_client.dart';

class EmailVerificationStep extends StatefulWidget {
  final String email;
  final Color themeColor;
  final VoidCallback? onVerificationCompleted;

  const EmailVerificationStep({
    Key? key,
    required this.email,
    required this.themeColor,
    this.onVerificationCompleted,
  }) : super(key: key);

  @override
  State<EmailVerificationStep> createState() => _EmailVerificationStepState();
}

class _EmailVerificationStepState extends State<EmailVerificationStep>
    with SingleTickerProviderStateMixin {
  late TextEditingController _codeController;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  final FocusNode _codeFocusNode = FocusNode();
  final ApiClient _apiClient = ApiClient();
  
  String? _codeError;
  bool _isLoading = false;
  bool _isResending = false;
  bool _codeSent = false;
  Timer? _resendTimer;
  int _resendCountdown = 0;
  
  @override
  void initState() {
    super.initState();
    _codeController = TextEditingController();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
    
    // Add listener for code validation
    _codeController.addListener(_validateCode);
    
    // Auto-send verification code when step loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sendVerificationCode();
    });
  }
  
  @override
  void dispose() {
    _codeController.dispose();
    _animationController.dispose();
    _codeFocusNode.dispose();
    _resendTimer?.cancel();
    super.dispose();
  }
  
  void _validateCode() {
    final code = _codeController.text;
    
    setState(() {
      if (code.isEmpty) {
        _codeError = null;
      } else if (code.length != 6) {
        _codeError = 'Verification code must be 6 digits';
      } else if (!RegExp(r'^[0-9]+$').hasMatch(code)) {
        _codeError = 'Code must contain only numbers';
      } else {
        _codeError = null;
      }
    });
    
    // Auto-verify when 6 digits are entered
    if (code.length == 6 && _codeError == null) {
      _verifyCode();
    }
  }
  
  Future<void> _sendVerificationCode() async {
    if (_isResending) return;
    
    setState(() {
      _isResending = true;
      _codeError = null;
    });
    
    try {
      final response = await _apiClient.post(
        '/auth/send-verification-code/',
        body: {
          'email': widget.email,
        },
        requiresAuth: false,
      );
      
      if (response['success'] == true) {
        setState(() {
          _codeSent = true;
          _isResending = false;
        });
        
        _startResendCountdown();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.email_outlined, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text('Verification code sent to ${widget.email}'),
                  ),
                ],
              ),
              backgroundColor: widget.themeColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
        
        // Auto-focus after sending
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) _codeFocusNode.requestFocus();
        });
      } else {
        throw Exception(response['message'] ?? 'Failed to send verification code');
      }
    } catch (e) {
      setState(() {
        _isResending = false;
        _codeError = 'Failed to send verification code. Please try again.';
      });
      
      if (kDebugMode) {
        print('❌ Error sending verification code: $e');
      }
    }
  }
  
  Future<void> _verifyCode() async {
    if (_isLoading) return;
    
    final code = _codeController.text;
    if (code.length != 6) return;
    
    setState(() {
      _isLoading = true;
      _codeError = null;
    });
    
    try {
      final response = await _apiClient.post(
        '/auth/verify-email-code/',
        body: {
          'email': widget.email,
          'code': code,
        },
        requiresAuth: false,
      );
      
      if (response['success'] == true) {
        setState(() {
          _isLoading = false;
        });
        
        // Show success feedback
        HapticFeedback.heavyImpact();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle_outline, color: Colors.white),
                  const SizedBox(width: 12),
                  Text('Email verified successfully!'),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
        
        // Notify parent that verification is complete
        widget.onVerificationCompleted?.call();
      } else {
        setState(() {
          _isLoading = false;
          _codeError = response['message'] ?? 'Invalid verification code';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _codeError = 'Verification failed. Please try again.';
      });
      
      if (kDebugMode) {
        print('❌ Error verifying code: $e');
      }
    }
  }
  
  void _startResendCountdown() {
    setState(() {
      _resendCountdown = 60; // 60 seconds countdown
    });
    
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _resendCountdown--;
      });
      
      if (_resendCountdown <= 0) {
        timer.cancel();
      }
    });
  }
  
  bool get canResend => _resendCountdown <= 0 && !_isResending;
  bool get isCodeValid => _codeController.text.length == 6 && _codeError == null;
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return ScaleTransition(
      scale: _scaleAnimation,
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          child: Column(
            children: [
              const SizedBox(height: 20),
              
              // Email icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: widget.themeColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: Icon(
                  Icons.email_outlined,
                  size: 40,
                  color: widget.themeColor,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Title
              Text(
                'Verify your email',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w700,
                  color: theme.colorScheme.onSurface,
                  letterSpacing: -0.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Subtitle
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: TextStyle(
                    fontSize: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                    height: 1.4,
                  ),
                  children: [
                    const TextSpan(text: 'We sent a verification code to\n'),
                    TextSpan(
                      text: widget.email,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: widget.themeColor,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Verification code field
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 4, bottom: 8),
                      child: Text(
                        'Verification Code',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface.withOpacity(0.8),
                          letterSpacing: 0.2,
                        ),
                      ),
                    ),
                    
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: (isDarkMode ? Colors.white : Colors.black).withOpacity(0.05),
                        border: Border.all(
                          color: _codeError != null
                              ? Colors.red
                              : _codeFocusNode.hasFocus
                                  ? widget.themeColor
                                  : (isDarkMode ? Colors.white : Colors.black).withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: TextField(
                        controller: _codeController,
                        focusNode: _codeFocusNode,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(6),
                        ],
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                          letterSpacing: 8,
                        ),
                        textAlign: TextAlign.center,
                        decoration: InputDecoration(
                          hintText: '000000',
                          hintStyle: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.onSurface.withOpacity(0.3),
                            letterSpacing: 8,
                          ),
                          prefixIcon: _isLoading
                              ? Padding(
                                  padding: const EdgeInsets.all(14),
                                  child: SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: widget.themeColor,
                                    ),
                                  ),
                                )
                              : Icon(
                                  Icons.verified_user_outlined,
                                  color: _codeFocusNode.hasFocus
                                      ? widget.themeColor
                                      : theme.colorScheme.onSurface.withOpacity(0.5),
                                  size: 20,
                                ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 16,
                          ),
                        ),
                      ),
                    ),
                    
                    if (_codeError != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 8, left: 4),
                        child: Text(
                          _codeError!,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Resend code section
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.2),
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'Didn\'t receive the code?',
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    if (_resendCountdown > 0)
                      Text(
                        'Resend in ${_resendCountdown}s',
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface.withOpacity(0.4),
                        ),
                      )
                    else
                      TextButton(
                        onPressed: canResend ? _sendVerificationCode : null,
                        child: Text(
                          _isResending ? 'Sending...' : 'Resend Code',
                          style: TextStyle(
                            color: widget.themeColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
} 