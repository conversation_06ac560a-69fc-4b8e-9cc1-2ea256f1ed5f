import 'package:flutter/material.dart';
import '../../../theme/app_colors.dart';
import '../models/onboarding_models.dart';

class OnboardingHeader extends StatelessWidget {
  final OnboardingStep currentStep;

  const OnboardingHeader({
    Key? key,
    required this.currentStep,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // BOP Logo/Text
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(
                    'assets/images/logo/BOPmaps.png',
                    height: 32,
                    width: 32,
                  ),
                ),
              ),
              const SizedBox(width: 8),
            ],
          ),
          const Spacer(),
          // Progress indicator
          ProgressIndicator(currentStep: currentStep),
        ],
      ),
    );
  }
}

class ProgressIndicator extends StatelessWidget {
  final OnboardingStep currentStep;

  const ProgressIndicator({
    Key? key,
    required this.currentStep,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final progress = _getProgressValue();
    return Container(
      width: 60,
      height: 6,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(3),
        color: Colors.grey.withOpacity(0.3),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progress,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            gradient: LinearGradient(
              colors: [
                AppColors.primary,
                AppColors.primary.withOpacity(0.7),
              ],
            ),
          ),
        ),
      ),
    );
  }

  double _getProgressValue() {
    switch (currentStep) {
      case OnboardingStep.welcome:
        return 0.1;
      case OnboardingStep.auth:
        return 0.2;
      case OnboardingStep.genres:
        return 0.4;
      case OnboardingStep.genreComment:
        return 0.5;
      case OnboardingStep.artists:
        return 0.6;
      case OnboardingStep.songSelection:
        return 0.8;
      case OnboardingStep.pinPlacement:
        return 0.9;
      case OnboardingStep.celebration:
      case OnboardingStep.completion:
        return 1.0;
    }
  }
}
