import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../theme/app_colors.dart';

class AnimatedBackground extends StatelessWidget {
  final AnimationController backgroundController;
  final bool isDarkMode;

  const AnimatedBackground({
    Key? key,
    required this.backgroundController,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: backgroundController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDarkMode
                  ? [
                      AppColors.backgroundDark,
                      AppColors.backgroundDark.withOpacity(0.8),
                      AppColors.primary.withOpacity(0.1),
                    ]
                  : [
                      AppColors.background,
                      AppColors.background.withOpacity(0.8),
                      AppColors.primary.withOpacity(0.05),
                    ],
              stops: [
                0.0,
                0.5 + 0.2 * math.sin(backgroundController.value * 2 * math.pi),
                1.0,
              ],
            ),
          ),
        );
      },
    );
  }
}
