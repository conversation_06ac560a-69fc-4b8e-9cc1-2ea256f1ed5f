import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import '../../../services/api/api_client.dart';

class EmailCheckStep extends StatefulWidget {
  final String email;
  final Color themeColor;
  final ValueChanged<String> onEmailChanged;
  final ValueChanged<bool> onEmailValidated;
  final VoidCallback? onExistingUserFound;
  final ValueChanged<Map<String, dynamic>?> onExistingUserDataChanged;

  const EmailCheckStep({
    Key? key,
    required this.email,
    required this.themeColor,
    required this.onEmailChanged,
    required this.onEmailValidated,
    this.onExistingUserFound,
    required this.onExistingUserDataChanged,
  }) : super(key: key);

  @override
  State<EmailCheckStep> createState() => _EmailCheckStepState();
}

class _EmailCheckStepState extends State<EmailCheckStep> with SingleTickerProviderStateMixin {
  late TextEditingController _emailController;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  final FocusNode _emailFocusNode = FocusNode();
  
  bool _isValidating = false;
  bool _isEmailValid = false;
  String? _emailError;
  Map<String, dynamic>? _existingUserData;
  
  final ApiClient _apiClient = ApiClient();

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController(text: widget.email);
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
    
    // Add listener for email validation
    _emailController.addListener(_validateEmail);
    
    // Auto-focus after animation if email is empty
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted && widget.email.isEmpty) {
        _emailFocusNode.requestFocus();
      } else if (widget.email.isNotEmpty) {
        // If email is pre-filled, validate it automatically
        _checkEmailExists();
      }
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _animationController.dispose();
    _emailFocusNode.dispose();
    super.dispose();
  }

  bool _isValidEmailFormat(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  void _validateEmail() {
    final value = _emailController.text.trim();
    
    setState(() {
      if (value.isEmpty) {
        _isEmailValid = false;
        _emailError = null;
        _existingUserData = null;
      } else if (!_isValidEmailFormat(value)) {
        _isEmailValid = false;
        _emailError = 'Please enter a valid email address';
        _existingUserData = null;
      } else {
        _emailError = null;
        // Don't set _isEmailValid to true here, wait for backend validation
      }
    });
    
    widget.onEmailChanged(value);
  }

  Future<void> _checkEmailExists() async {
    final email = _emailController.text.trim();
    
    if (email.isEmpty || !_isValidEmailFormat(email)) {
      return;
    }

    setState(() {
      _isValidating = true;
      _emailError = null;
    });

    try {
      final response = await _apiClient.post(
        '/auth/check-email/',
        body: {'email': email},
        requiresAuth: false,
      );

      if (!mounted) return;

      if (response['exists'] == true && response['user'] != null) {
        // User exists - allow proceeding to password login step
        setState(() {
          _existingUserData = response['user'];
          _isEmailValid = true; // Allow proceeding to next step
          _emailError = null;
        });
        
        widget.onEmailValidated(true);
        widget.onExistingUserDataChanged(response['user']);
      } else {
        // Email is available for registration
        setState(() {
          _isEmailValid = true;
          _emailError = null;
          _existingUserData = null;
        });
        
        widget.onEmailValidated(true);
        widget.onExistingUserDataChanged(null);
      }
    } catch (e) {
      if (!mounted) return;
      
      if (kDebugMode) {
        print('Error checking email: $e');
      }
      
      setState(() {
        _emailError = 'Unable to verify email. Please try again.';
        _isEmailValid = false;
      });
      
      widget.onEmailValidated(false);
    } finally {
      if (mounted) {
        setState(() => _isValidating = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return ScaleTransition(
      scale: _scaleAnimation,
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          child: Column(
            children: [
              const SizedBox(height: 40),
              
              // Icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: widget.themeColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: widget.themeColor.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.email_outlined,
                  size: 40,
                  color: widget.themeColor,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Title
              Text(
                'Verify your email',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w700,
                  color: theme.colorScheme.onSurface,
                  letterSpacing: -0.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Subtitle
              Text(
                widget.email.isNotEmpty 
                  ? 'We need to verify this email before continuing'
                  : 'Enter your email to get started',
                style: TextStyle(
                  fontSize: 16,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 40),
              
              // Email input
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 4, bottom: 8),
                      child: Row(
                        children: [
                          Text(
                            'Email Address',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.onSurface.withOpacity(0.8),
                              letterSpacing: 0.2,
                            ),
                          ),
                          if (widget.email.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: widget.themeColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'pre-filled',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: widget.themeColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: (isDarkMode ? Colors.white : Colors.black).withOpacity(0.05),
                        border: Border.all(
                          color: _emailError != null
                              ? Colors.red
                              : _emailFocusNode.hasFocus
                                  ? widget.themeColor
                                  : (isDarkMode ? Colors.white : Colors.black).withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: TextField(
                        controller: _emailController,
                        focusNode: _emailFocusNode,
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.done,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Enter your email address',
                          hintStyle: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface.withOpacity(0.4),
                          ),
                          prefixIcon: Icon(
                            Icons.email_outlined,
                            color: _emailFocusNode.hasFocus
                                ? widget.themeColor
                                : theme.colorScheme.onSurface.withOpacity(0.5),
                            size: 20,
                          ),
                          suffixIcon: _isValidating
                            ? Container(
                                margin: const EdgeInsets.only(right: 12),
                                child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(widget.themeColor),
                                  ),
                                ),
                              )
                            : _emailController.text.isNotEmpty
                              ? Container(
                                  margin: const EdgeInsets.only(right: 12),
                                  child: Icon(
                                    _isEmailValid ? Icons.check_circle : Icons.error,
                                    color: _isEmailValid ? Colors.green : Colors.red,
                                    size: 20,
                                  ),
                                )
                              : null,
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 16,
                          ),
                        ),
                        onSubmitted: (_) => _checkEmailExists(),
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Status message
                    Container(
                      height: 20,
                      child: _isValidating
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 12,
                                height: 12,
                                child: CircularProgressIndicator(
                                  strokeWidth: 1.5,
                                  valueColor: AlwaysStoppedAnimation<Color>(widget.themeColor),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Checking email...',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          )
                        : _emailError != null
                          ? Text(
                              _emailError!,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            )
                          : _isEmailValid && _existingUserData != null
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.person_outline,
                                    size: 14,
                                    color: Colors.blue.shade600,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    'Account found! Please enter your password.',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.blue.shade600,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              )
                            : _isEmailValid
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.check_circle,
                                      size: 14,
                                      color: Colors.green.shade600,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      'Email is available!',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.green.shade600,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                )
                              : null,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Check email button
              if (!_isValidating && _emailController.text.isNotEmpty && _emailError == null && !_isEmailValid)
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: _checkEmailExists,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.themeColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: const Text(
                      'Check Email',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              
              const SizedBox(height: 60),
            ],
          ),
        ),
      ),
    );
  }
} 