import 'package:flutter/material.dart';
import '../../../theme/app_colors.dart';

class AuthOptions extends StatelessWidget {
  final bool isAuthenticating;
  final VoidCallback onGoogleSignIn;
  final VoidCallback onEmailSignIn;

  const AuthOptions({
    Key? key,
    required this.isAuthenticating,
    required this.onGoogleSignIn,
    required this.onEmailSignIn,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Google Sign In
          ElevatedButton.icon(
            onPressed: isAuthenticating ? null : onGoogleSignIn,
            icon: isAuthenticating
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.g_mobiledata),
            label: Text(
                isAuthenticating ? 'Signing in...' : 'Continue with Google'),
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 48),
              backgroundColor: Colors.white,
              foregroundColor: Colors.black87,
            ),
          ),

          const SizedBox(height: 12),

          // Email Sign In
          ElevatedButton.icon(
            onPressed: isAuthenticating ? null : onEmailSignIn,
            icon: isAuthenticating
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.email),
            label: Text(
                isAuthenticating ? 'Signing in...' : 'Continue with Email'),
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 48),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
