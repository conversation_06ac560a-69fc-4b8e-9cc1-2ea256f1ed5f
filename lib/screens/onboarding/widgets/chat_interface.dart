import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'dart:math' as math;
import '../../../theme/app_colors.dart';
import '../models/onboarding_models.dart';

class ChatInterface extends StatelessWidget {
  final Animation<double> fadeAnimation;
  final Animation<Offset> slideAnimation;
  final List<ChatMessage> messages;
  final ScrollController scrollController;
  final bool isAITyping;
  final AnimationController backgroundController;
  final Widget stepContent;

  const ChatInterface({
    Key? key,
    required this.fadeAnimation,
    required this.slideAnimation,
    required this.messages,
    required this.scrollController,
    required this.isAITyping,
    required this.backgroundController,
    required this.stepContent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: fadeAnimation,
      child: SlideTransition(
        position: slideAnimation,
        child: Container(
          margin: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Chat Messages
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: messages.length + (isAITyping ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == messages.length && isAITyping) {
                      return TypingIndicator(
                        backgroundController: backgroundController,
                      );
                    }

                    final message = messages[index];
                    return MessageBubble(message: message);
                  },
                ),
              ),

              // Current Step Content
              stepContent,
            ],
          ),
        ),
      ),
    );
  }
}

class MessageBubble extends StatelessWidget {
  final ChatMessage message;

  const MessageBubble({
    Key? key,
    required this.message,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (message.isAI) ...[
            // AI Avatar
            Container(
              width: 40,
              height: 40,
              margin: const EdgeInsets.only(right: 12),
              child: Lottie.asset(
                'assets/anim/ai_search.json',
                fit: BoxFit.cover,
              ),
            ),

            // AI Message or Special Content
            Expanded(
              child: message.specialContent != null
                  ? message.specialContent! // Show special content if available
                  : Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(4),
                          topRight: Radius.circular(16),
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                      ),
                      child: Text(
                        message.message,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 16,
                        ),
                      ),
                    ),
            ),
          ] else ...[
            // User Message
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      constraints: BoxConstraints(
                        maxWidth: MediaQuery.of(context).size.width * 0.7,
                      ),
                      decoration: const BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(4),
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                      ),
                      child: Text(
                        message.message,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        softWrap: true,
                        overflow: TextOverflow.visible,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class TypingIndicator extends StatelessWidget {
  final AnimationController backgroundController;

  const TypingIndicator({
    Key? key,
    required this.backgroundController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            margin: const EdgeInsets.only(right: 12),
            child: Lottie.asset(
              'assets/anim/ai_search.json',
              fit: BoxFit.cover,
            ),
          ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(16),
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: TypingAnimation(backgroundController: backgroundController),
          ),
        ],
      ),
    );
  }
}

class TypingAnimation extends StatelessWidget {
  final AnimationController backgroundController;

  const TypingAnimation({
    Key? key,
    required this.backgroundController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: backgroundController,
          builder: (context, child) {
            final offset = math.sin(
                (backgroundController.value * 4 * math.pi) + (index * 0.5));
            return Transform.translate(
              offset: Offset(0, offset * 3),
              child: Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.primary.withOpacity(0.6),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}
