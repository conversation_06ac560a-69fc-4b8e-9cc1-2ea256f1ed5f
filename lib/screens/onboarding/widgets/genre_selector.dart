import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../theme/app_colors.dart';

class GenreSelector extends StatefulWidget {
  final List<String> selectedGenres;
  final Function(List<String>) onGenresChanged;
  final bool isProcessing;
  final VoidCallback onDone;

  const GenreSelector({
    Key? key,
    required this.selectedGenres,
    required this.onGenresChanged,
    required this.isProcessing,
    required this.onDone,
  }) : super(key: key);

  @override
  State<GenreSelector> createState() => _GenreSelectorState();
}

class _GenreSelectorState extends State<GenreSelector> {
  final TextEditingController _searchController = TextEditingController();
  List<String> _filteredGenres = [];

  // Use the canonical genres from SpotifyGenreService
  final List<String> _popularGenres = [
    'pop',
    'rock',
    'hip-hop',
    'electronic',
    'r-n-b',
    'indie',
    'jazz',
    'blues',
    'country',
    'folk',
    'reggae',
    'metal',
    'punk',
    'classical',
    'ambient',
    'house',
    'techno',
    'dubstep',
    'trance',
    'funk',
    'soul',
    'latin',
    'k-pop',
    'j-pop',
    'trap',
    'uk drill',
    'chicago drill',
    'lo-fi',
    'indie-pop',
    'alt-rock',
    'dance'
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onGenreToggle(String genre) {
    final updatedGenres = List<String>.from(widget.selectedGenres);
    if (updatedGenres.contains(genre)) {
      updatedGenres.remove(genre);
    } else {
      updatedGenres.add(genre);
    }
    widget.onGenresChanged(updatedGenres);
  }

  void _onSearchChanged(String value) {
    setState(() {
      if (value.isEmpty) {
        _filteredGenres.clear();
      } else {
        _filteredGenres = _popularGenres
            .where((genre) => genre.toLowerCase().contains(value.toLowerCase()))
            .toList();
      }
    });
  }

  void _clearSearch() {
    setState(() {
      _filteredGenres.clear();
      _searchController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    // Use filtered genres if search is active, otherwise use all genres
    final displayGenres =
        _filteredGenres.isNotEmpty ? _filteredGenres : _popularGenres;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search genres...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _filteredGenres.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearSearch,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: _onSearchChanged,
          ),

          const SizedBox(height: 16),

          // AnimatedSwitcher for smooth transition between no genres and genre chips
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 350),
            switchInCurve: Curves.easeOutBack,
            switchOutCurve: Curves.easeIn,
            transitionBuilder: (child, animation) => ScaleTransition(
              scale: animation,
              child: child,
            ),
            child:
                (_searchController.text.isNotEmpty && _filteredGenres.isEmpty)
                    ? _buildNoGenresFound()
                    : _buildGenreChips(displayGenres),
          ),

          const SizedBox(height: 16),

          // Done Button
          if (widget.selectedGenres.isNotEmpty)
            ElevatedButton(
              onPressed: widget.isProcessing ? null : widget.onDone,
              child: widget.isProcessing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('Done'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 48),
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNoGenresFound() {
    return Padding(
      key: const ValueKey('no_genres'),
      padding: const EdgeInsets.symmetric(vertical: 32),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.color
                  ?.withOpacity(0.3),
            ).animate(
              effects: [
                ScaleEffect(
                  begin: const Offset(0.5, 0.5),
                  end: const Offset(1, 1),
                  duration: 300.milliseconds,
                ),
                FadeEffect(
                  begin: 0,
                  end: 1,
                  duration: 300.milliseconds,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'No genres found',
              style: TextStyle(
                fontSize: 18,
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.5),
              ),
            ).animate(
              delay: 200.milliseconds,
              effects: [
                SlideEffect(
                  begin: const Offset(0, 0.5),
                  end: Offset.zero,
                  duration: 300.milliseconds,
                ),
                FadeEffect(
                  begin: 0,
                  end: 1,
                  duration: 300.milliseconds,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Try a different search term',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.3),
              ),
            ).animate(
              delay: 400.milliseconds,
              effects: [
                SlideEffect(
                  begin: const Offset(0, 0.5),
                  end: Offset.zero,
                  duration: 300.milliseconds,
                ),
                FadeEffect(
                  begin: 0,
                  end: 1,
                  duration: 300.milliseconds,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenreChips(List<String> displayGenres) {
    return SingleChildScrollView(
      key: const ValueKey('genre_chips'),
      scrollDirection: Axis.horizontal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // First row
          _buildGenreRow(displayGenres.take(10).toList()),
          // Second row
          _buildGenreRow(displayGenres.skip(10).take(10).toList()),
          // Third row
          _buildGenreRow(displayGenres.skip(20).toList()),
        ],
      ),
    );
  }

  Widget _buildGenreRow(List<String> genres) {
    if (genres.isEmpty) return const SizedBox.shrink();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      physics: const NeverScrollableScrollPhysics(),
      child: Row(
        children: genres.map((genre) {
          final isSelected = widget.selectedGenres.contains(genre);
          return Padding(
            padding: const EdgeInsets.only(right: 8, bottom: 8),
            child: FilterChip(
              label: Text(genre),
              selected: isSelected,
              onSelected: (_) => _onGenreToggle(genre),
              selectedColor: AppColors.primary.withOpacity(0.3),
              checkmarkColor: AppColors.primary,
            ),
          ).animate(
            effects: [
              ScaleEffect(
                begin: const Offset(0.8, 0.8),
                end: const Offset(1, 1),
                duration: 300.milliseconds,
              ),
              FadeEffect(
                begin: 0,
                end: 1,
                duration: 300.milliseconds,
              ),
            ],
          );
        }).toList(),
      ),
    );
  }
}
