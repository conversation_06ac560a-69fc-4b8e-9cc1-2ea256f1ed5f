import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:ui';
import '../../providers/friends_provider.dart';
import '../../models/friend.dart';
import '../../models/user.dart';

class UserSearchScreen extends StatefulWidget {
  const UserSearchScreen({Key? key}) : super(key: key);

  @override
  State<UserSearchScreen> createState() => _UserSearchScreenState();
}

class _UserSearchScreenState extends State<UserSearchScreen> with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _isSearching = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Local state to track request states for immediate UI updates
  final Map<String, bool> _localRequestStates = {};
  final Map<String, bool> _localPendingStates = {};

  @override
  void initState() {
    super.initState();
    // Animation setup
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut)
    );
    _animationController.forward();
    
    // Load suggested friends when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FriendsProvider>().loadSuggestedFriends();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _animationController.dispose();
    // Clear local state when leaving screen for fresh data next time
    _localRequestStates.clear();
    _localPendingStates.clear();
    super.dispose();
  }

  // Helper methods for checking request states with local overrides
  bool _hasPendingRequest(String userId, FriendsProvider provider) {
    // Check local state first (for immediate UI updates)
    if (_localPendingStates.containsKey(userId)) {
      return _localPendingStates[userId]!;
    }
    // Fall back to provider state
    return provider.hasPendingRequest(userId);
  }

  bool _hasSentRequest(String userId, FriendsProvider provider) {
    // Check local state first (for immediate UI updates)
    if (_localRequestStates.containsKey(userId)) {
      return _localRequestStates[userId]!;
    }
    // Fall back to provider state
    return provider.hasSentRequest(userId);
  }

  void _updateLocalRequestState(String userId, {bool? hasSent, bool? hasPending}) {
    setState(() {
      if (hasSent != null) {
        _localRequestStates[userId] = hasSent;
      }
      if (hasPending != null) {
        _localPendingStates[userId] = hasPending;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isSmallScreen = MediaQuery.of(context).size.width < 360;
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            size: 20,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            Navigator.pop(context);
          },
        ),
        title: Text(
          'Find Friends',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar with backdrop filter
          Center(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    height: 52,
                    width: MediaQuery.of(context).size.width * 0.92,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface.withOpacity(isDark ? 0.08 : 0.04),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.primary.withOpacity(isDark ? 0.15 : 0.1),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: theme.colorScheme.shadow.withOpacity(isDark ? 0.2 : 0.05),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _searchController,
                      focusNode: _searchFocusNode,
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.3,
                        letterSpacing: 0.15,
                        color: theme.colorScheme.onSurface,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Search by username or name',
                        hintStyle: TextStyle(
                          fontSize: 16,
                          height: 1.3,
                          letterSpacing: 0.15,
                          color: theme.colorScheme.primary.withOpacity(0.7),
                        ),
                        prefixIcon: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Icon(
                            Icons.search_rounded,
                            color: theme.colorScheme.primary,
                            size: 24,
                          ),
                        ),
                        prefixIconConstraints: const BoxConstraints(
                          minWidth: 56,
                          minHeight: 52,
                        ),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? Consumer<FriendsProvider>(
                                builder: (context, provider, child) {
                                  return provider.isSearching
                                      ? Padding(
                                          padding: const EdgeInsets.all(14),
                                          child: SizedBox(
                                            width: 24,
                                            height: 24,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2.5,
                                              color: theme.colorScheme.primary,
                                            ),
                                          ),
                                        )
                                      : IconButton(
                                          icon: Icon(
                                            Icons.close_rounded,
                                            color: theme.colorScheme.primary.withOpacity(0.7),
                                            size: 22,
                                          ),
                                          onPressed: () {
                                            _searchController.clear();
                                            setState(() {
                                              _isSearching = false;
                                            });
                                          },
                                        );
                                },
                              )
                            : null,
                        suffixIconConstraints: const BoxConstraints(
                          minWidth: 52,
                          minHeight: 52,
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        filled: true,
                        fillColor: Colors.transparent,
                      ),
                      onChanged: (value) {
                        setState(() {
                          _isSearching = value.trim().length >= 2;
                        });
                        if (_isSearching) {
                          context.read<FriendsProvider>().searchAllUsers(value.trim());
                        } else {
                          context.read<FriendsProvider>().clearUserSearch();
                        }
                      },
                      cursorColor: theme.colorScheme.primary,
                      cursorWidth: 2,
                      cursorHeight: 24,
                      cursorRadius: const Radius.circular(2),
                      textAlignVertical: TextAlignVertical.center,
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Search results or initial state
          Expanded(
            child: Consumer<FriendsProvider>(
              builder: (context, provider, child) {
                if (!_isSearching) {
                  return Column(
                    children: [
                      // Suggested friends section
                      if (provider.suggestedFriends.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                          child: Row(
                            children: [
                              Icon(
                                Icons.people_alt_outlined,
                                size: 20,
                                color: theme.colorScheme.primary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'People you may know',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: ListView.builder(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            itemCount: provider.suggestedFriends.length,
                            itemBuilder: (context, index) {
                              final user = provider.suggestedFriends[index];
                              // Use a unique key that includes the user ID and request state
                              final requestState = '${_hasSentRequest(user.id.toString(), provider)}_${_hasPendingRequest(user.id.toString(), provider)}';
                              return _buildUserTile(
                                context,
                                user,
                                isSmallScreen,
                                provider,
                                true,
                                key: ValueKey('suggested_${user.id}_$requestState'),
                              );
                            },
                          ),
                        ),
                      ] else ...[
                        Expanded(
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: _buildInitialState(theme, isSmallScreen),
                          ),
                        ),
                      ],
                      // Only show invite friends when not searching
                      if (!_searchFocusNode.hasFocus)
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: _buildInviteFriendsCard(context),
                        ),
                    ],
                  );
                }

                if (provider.isSearching) {
                  return Center(
                    child: CircularProgressIndicator(
                      color: theme.colorScheme.primary,
                      strokeWidth: 3,
                    ),
                  );
                }

                if (provider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline_rounded,
                          size: 48,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          provider.error!,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () {
                            HapticFeedback.mediumImpact();
                            provider.searchAllUsers(_searchController.text.trim());
                          },
                          icon: const Icon(Icons.refresh_rounded),
                          label: const Text('Retry'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.surface.withOpacity(isDark ? 0.24 : 0.87),
                            foregroundColor: theme.colorScheme.surface,
                            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final userResults = provider.userSearchResults;
                if (userResults.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off_rounded,
                          size: 48,
                          color: theme.colorScheme.onSurface.withOpacity(0.3),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No users found',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: theme.colorScheme.onSurface.withOpacity(0.8),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Try a different search term',
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.onSurface.withOpacity(0.5),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  itemCount: userResults.length,
                  itemBuilder: (context, index) {
                    final user = userResults[index];
                    final requestState = '${_hasSentRequest(user.id.toString(), provider)}_${_hasPendingRequest(user.id.toString(), provider)}';
                    return _buildUserTile(
                      context,
                      user,
                      isSmallScreen,
                      provider,
                      false,
                      key: ValueKey('search_${user.id}_$requestState'),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialState(ThemeData theme, bool isSmallScreen) {
    final isDark = theme.brightness == Brightness.dark;
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Decorative icon
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.group_add_rounded,
              size: 32,
              color: theme.colorScheme.primary,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Centered, concise message
          Text(
            'Type to search',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: isSmallScreen ? 14 : 15,
              color: theme.colorScheme.primary.withOpacity(0.8),
              fontWeight: FontWeight.w500,
              letterSpacing: 0.15,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserTile(
    BuildContext context,
    User user,
    bool isSmallScreen,
    FriendsProvider provider,
    bool isSuggested,
    {Key? key}
  ) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final bool isFriend = provider.isAlreadyFriend(user.id.toString());
    final bool hasPendingRequest = _hasPendingRequest(user.id.toString(), provider);
    
    // Get mutual friends count from user data if available
    final int mutualFriendsCount = user.mutualFriendsCount ?? 0;

    return Container(
      key: key,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withOpacity(isDark ? 0.05 : 0.03),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.onSurface.withOpacity(isDark ? 0.1 : 0.05),
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        leading: Hero(
          tag: 'user_avatar_${user.id}',
          child: Container(
            width: isSmallScreen ? 44 : 48,
            height: isSmallScreen ? 44 : 48,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: theme.colorScheme.onSurface.withOpacity(isDark ? 0.2 : 0.1),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(isSmallScreen ? 22 : 24),
              child: user.profilePictureUrl != null
                ? Image.network(
                    user.profilePictureUrl!,
                    width: isSmallScreen ? 44 : 48,
                    height: isSmallScreen ? 44 : 48,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Icon(
                      Icons.person,
                      size: isSmallScreen ? 22 : 24,
                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                    ),
                  )
                : Icon(
                    Icons.person,
                    size: isSmallScreen ? 22 : 24,
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
            ),
          ),
        ),
        title: Text(
          _getUserDisplayName(user),
          style: TextStyle(
            fontSize: isSmallScreen ? 14 : 15,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '@${user.username}',
              style: TextStyle(
                fontSize: isSmallScreen ? 12 : 13,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            if (mutualFriendsCount > 0) ...[
              const SizedBox(height: 4),
              Text(
                '$mutualFriendsCount mutual friends',
                style: TextStyle(
                  fontSize: isSmallScreen ? 11 : 12,
                  color: theme.colorScheme.primary.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
            if (isSuggested && mutualFriendsCount == 0) ...[
              const SizedBox(height: 4),
              Text(
                'Suggested for you',
                style: TextStyle(
                  fontSize: isSmallScreen ? 11 : 12,
                  color: theme.colorScheme.primary.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
        trailing: isFriend
          ? Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface.withOpacity(isDark ? 0.1 : 0.05),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_rounded,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Friends',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 12 : 13,
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            )
          : hasPendingRequest
            ? _hasSentRequest(user.id.toString(), provider)
              ? GestureDetector(
                  onTap: () async {
                    // Show confirmation dialog before cancelling
                    final shouldCancel = await showDialog<bool>(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Cancel Friend Request'),
                        content: Text('Cancel friend request to ${_getUserDisplayName(user)}?'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            child: const Text('Keep'),
                          ),
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(true),
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.red,
                            ),
                            child: const Text('Cancel Request'),
                          ),
                        ],
                      ),
                    );

                    if (shouldCancel == true) {
                      HapticFeedback.mediumImpact();
                      
                      // IMMEDIATELY update local state for instant UI feedback
                      _updateLocalRequestState(
                        user.id.toString(), 
                        hasSent: false, 
                        hasPending: false
                      );
                      
                      final requestId = provider.getSentRequestId(user.id.toString());
                      if (requestId != null) {
                        // Fire and forget - we're being super optimistic!
                        provider.cancelFriendRequest(requestId).then((success) {
                          if (success && context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Friend request to ${_getUserDisplayName(user)} cancelled'),
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                backgroundColor: Colors.orange.shade600,
                              ),
                            );
                          }
                          // Don't revert on failure - stay optimistic!
                        });
                      }
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(isDark ? 0.15 : 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.orange.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.cancel_outlined,
                          size: 16,
                          color: Colors.orange.shade700,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Pending',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 12 : 13,
                            fontWeight: FontWeight.w500,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface.withOpacity(isDark ? 0.1 : 0.05),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.hourglass_top_rounded,
                        size: 16,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Pending',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 12 : 13,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                )
            : ElevatedButton.icon(
                onPressed: () async {
                  HapticFeedback.mediumImpact();
                  
                  // IMMEDIATELY update local state for instant UI feedback
                  _updateLocalRequestState(
                    user.id.toString(), 
                    hasSent: true, 
                    hasPending: true
                  );
                  
                  // Fire and forget - we're being super optimistic!
                  provider.sendFriendRequest(user.id).then((success) {
                    if (success && context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Friend request sent to ${user.displayName ?? user.username}'),
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          backgroundColor: Colors.green.shade600,
                        ),
                      );
                    }
                    // Don't revert on failure - stay optimistic!
                  });
                },
                icon: Icon(
                  Icons.person_add_rounded,
                  size: isSmallScreen ? 14 : 16,
                ),
                label: Text(
                  'Add',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 12 : 13,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 12 : 16,
                    vertical: isSmallScreen ? 6 : 8,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
              ),
      ),
    );
  }

  Widget _buildInviteFriendsCard(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(0.8),
            theme.colorScheme.secondary.withOpacity(0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Icon section
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: theme.colorScheme.onPrimary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.people_alt_rounded,
                  color: theme.colorScheme.onPrimary,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Text section
              Expanded(
                child: Text(
                  'Share your BOP Map journey with friends and explore together!',
                  style: TextStyle(
                    color: theme.colorScheme.onPrimary.withOpacity(0.9),
                    fontSize: 13,
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Invite button
          GestureDetector(
            onTap: () async {
              HapticFeedback.mediumImpact();
              
              // Share app with friends using native iOS share sheet
              const String appName = 'BOP Maps';
              const String appDescription = '🎵 Discover music through location! Drop pins, share beats, and explore the world through sound.';
              const String appStoreLink = 'https://apps.apple.com/app/bop-maps/id123456789'; // Replace with actual App Store link
              const String playStoreLink = 'https://play.google.com/store/apps/details?id=com.bopmaps.app'; // Replace with actual Play Store link
              
              final String shareText = '''
Hey! 👋 Check out $appName!

$appDescription

🍎 iOS: $appStoreLink
🤖 Android: $playStoreLink

Let's explore music together! 🎶
''';

              try {
                await Share.share(
                  shareText,
                  subject: 'Join me on $appName! 🎵',
                );
              } catch (e) {
                print('Error sharing: $e');
                // Show fallback snackbar if sharing fails
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Unable to share at the moment'),
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      backgroundColor: Colors.red.shade600,
                    ),
                  );
                }
              }
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.share_rounded,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'INVITE FRIENDS',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getUserDisplayName(User user) {
    // Debug logging to see what data we're getting
    print('🔍 UserSearch: User data - id=${user.id}, username=${user.username}, displayName=${user.displayName}, firstName=${user.firstName}, lastName=${user.lastName}');
    
    if (user.displayName != null && user.displayName!.isNotEmpty) {
      return user.displayName!;
    } else if (user.firstName != null && user.lastName != null) {
      return '${user.firstName} ${user.lastName}';
    } else if (user.firstName != null && user.firstName!.isNotEmpty) {
      return user.firstName!;
    } else if (user.username.isNotEmpty) {
      return user.username;
    } else {
      return 'User ${user.id}';
    }
  }
} 