import 'package:bop_maps/models/customization_data.dart';
import 'package:bop_maps/models/music_track.dart';
import 'package:bop_maps/providers/apple_music_provider.dart';
import 'package:bop_maps/providers/spotify_provider.dart';
import 'package:bop_maps/providers/user_provider.dart';
import 'package:bop_maps/providers/youtube_provider.dart';
import 'package:bop_maps/screens/search/ai_search/ai_customization_bottom_sheet.dart';
import 'package:bop_maps/screens/search/ai_search/ai_search_provider.dart';
import 'package:bop_maps/screens/search/ai_search/genre_search_provider.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/all_tab.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/artist_based_tab.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/discover_tab.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/genre_based_tab.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/liked_songs_tab.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/mood_based_tab.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/recently_added_tab.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/recently_played_tab.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/top_tracks_tab.dart';
import 'package:bop_maps/services/music/spotify_service.dart';
import 'package:bop_maps/utils/chinese_normalizer.dart';
import 'package:bop_maps/widgets/music/apple_now_playing_bar.dart';
import 'package:bop_maps/widgets/music/now_playing_bar.dart';
import 'package:bop_maps/widgets/music/track_card.dart';
import 'package:bop_maps/widgets/music/youtube_now_playing_bar.dart.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:math' as math;
import '../../../providers/suggested_songs_provider.dart';

class AISearchView extends StatefulWidget {
  const AISearchView({super.key});

  @override
  State<AISearchView> createState() => _AISearchViewState();
}

class _AISearchViewState extends State<AISearchView>
    with TickerProviderStateMixin {
  // Controllers and services
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final SpotifyService _spotifyService = SpotifyService();
  Timer? _searchTimer;

  // Providers
  late GenreSearchProvider _genreSearchProvider;

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _colorController;

  List<MusicTrack> _searchResults = [];
  bool _isSearching = false;
  bool _hasSearched = false;
  String _currentQuery = '';

  // Pagination state
  final ScrollController _scrollController = ScrollController();

  // Scroll to top button
  bool _showScrollToTop = false;

  // Add debounced aggressive preloading
  Timer? _preloadTimer;
  Timer? _idlePreloadTimer;
  DateTime? _lastScrollTime;

  @override
  void initState() {
    super.initState();

    // Initialize GenreSearchProvider
    final aiProvider = Provider.of<AISearchProvider>(context, listen: false);
    _genreSearchProvider = GenreSearchProvider();

    _initializeAnimations();

    // Listen to text changes for UI updates
    _searchController.addListener(() {
      setState(() {});
    });

    // Listen to scroll for pagination
    _scrollController.addListener(_onScroll);
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _colorController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeController.forward();
    _slideController.forward();
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;
    final provider = Provider.of<AISearchProvider>(context, listen: false);

    final position = _scrollController.position;
    final pixels = position.pixels;
    final maxScrollExtent = position.maxScrollExtent;

    // Update last scroll time for idle detection
    _lastScrollTime = DateTime.now();

    // Early and aggressive pagination with multiple trigger points
    if (maxScrollExtent > 0) {
      final scrollPercentage = pixels / maxScrollExtent;

      // Check if we're in genre-based tab and use GenreSearchProvider
      if (provider.currentCategory == 'genreBased' &&
          _genreSearchProvider.selectedGenre != null) {
        // Handle pagination for GenreSearchProvider - use different thresholds and add logging
        print(
            '🔍 [Genre Scroll] Current position: ${pixels.toStringAsFixed(1)}/${maxScrollExtent.toStringAsFixed(1)} = ${(scrollPercentage * 100).toStringAsFixed(1)}%');
        print(
            '🔍 [Genre Scroll] hasMore: ${_genreSearchProvider.hasMore}, isLoadingMore: ${_genreSearchProvider.isLoadingMore}');

        if (scrollPercentage >= 0.7 &&
            !_genreSearchProvider.isLoadingMore &&
            _genreSearchProvider.hasMore) {
          print(
              '🔄 [Genre Pagination] Loading more at ${(scrollPercentage * 100).toStringAsFixed(1)}% scroll');
          _genreSearchProvider.loadMore(context);
        } else if (scrollPercentage >= 0.85 &&
            !_genreSearchProvider.isLoadingMore &&
            _genreSearchProvider.hasMore) {
          print(
              '🔄 [Genre Pagination] Backup trigger at ${(scrollPercentage * 100).toStringAsFixed(1)}% scroll');
          _genreSearchProvider.loadMore(context);
        } else if (scrollPercentage >= 0.95 &&
            !_genreSearchProvider.isLoadingMore &&
            _genreSearchProvider.hasMore) {
          print(
              '🔄 [Genre Pagination] Final trigger at ${(scrollPercentage * 100).toStringAsFixed(1)}% scroll');
          _genreSearchProvider.loadMore(context);
        }
      } else {
        // Handle pagination for AISearchProvider
        if (scrollPercentage >= 0.7 &&
            !provider.isLoadingMore &&
            provider.hasMoreContent) {
          print(
              '🔄 [AI Pagination] Loading more at ${(scrollPercentage * 100).toStringAsFixed(1)}% scroll');
          provider.loadMoreRecommendations(context);
        } else if (scrollPercentage >= 0.85 &&
            !provider.isLoadingMore &&
            provider.hasMoreContent) {
          print(
              '🔄 [AI Pagination] Backup trigger at ${(scrollPercentage * 100).toStringAsFixed(1)}% scroll');
          provider.loadMoreRecommendations(context);
        } else if (scrollPercentage >= 0.95 &&
            !provider.isLoadingMore &&
            provider.hasMoreContent) {
          print(
              '🔄 [AI Pagination] Final trigger at ${(scrollPercentage * 100).toStringAsFixed(1)}% scroll');
          provider.loadMoreRecommendations(context);
        }
      }

      // Aggressive preloading: if user is scrolling fast, load multiple pages ahead
      final scrollVelocity = position.userScrollDirection;
      if (scrollVelocity == ScrollDirection.forward &&
          scrollPercentage >= 0.5) {
        // User scrolling down past 50% - prepare for aggressive loading
        _scheduleAggressivePreload();
      } else {
        // Even for gentle scrolling, schedule idle preloading
        _scheduleIdlePreload();
      }
    }

    // Handle scroll to top button visibility (earlier trigger)
    final shouldShowButton = pixels > 300; // Reduced from 500 to 300
    if (shouldShowButton != _showScrollToTop) {
      setState(() {
        _showScrollToTop = shouldShowButton;
      });
    }
  }

  /// Schedule aggressive preloading for fast scrollers
  void _scheduleAggressivePreload() {
    final aiProvider = Provider.of<AISearchProvider>(context, listen: false);

    // Cancel any existing timer
    _preloadTimer?.cancel();

    // Update last scroll time
    _lastScrollTime = DateTime.now();

    // Decide which provider is active
    final bool inGenreTab = aiProvider.currentCategory == 'genreBased' &&
        _genreSearchProvider.selectedGenre != null;

    // Schedule preload after a short delay (user is actively scrolling)
    _preloadTimer = Timer(const Duration(milliseconds: 200), () {
      if (!mounted || _scrollController.hasClients == false) return;

      if (inGenreTab) {
        if (_genreSearchProvider.isLoadingMore || !_genreSearchProvider.hasMore) {
          return;
        }
      } else {
        if (aiProvider.isLoadingMore || !aiProvider.hasMoreContent) {
          return;
        }
      }

      final position = _scrollController.position;
      final scrollPercentage = position.pixels / position.maxScrollExtent;

      // If user is still scrolling and past 80%, preload aggressively
      if (scrollPercentage >= 0.8) {
        print('🚀 [Aggressive Preload] Loading extra content for fast scroller');
        _triggerLoadMoreForActiveTab();

        // Schedule another preload for very fast scrollers
        Future.delayed(const Duration(milliseconds: 500), () {
          if (!mounted || _scrollController.hasClients == false) return;

          if (inGenreTab) {
            if (_genreSearchProvider.isLoadingMore) return;
          } else {
            if (aiProvider.isLoadingMore) return;
          }

          final newPosition = _scrollController.position;
          final newScrollPercentage =
              newPosition.pixels / newPosition.maxScrollExtent;
          if (newScrollPercentage >= 0.9) {
            print('🚀 [Ultra Aggressive Preload] Loading even more content');
            _triggerLoadMoreForActiveTab();
          }
        });
      }
    });

    // Also schedule idle preloading
    _scheduleIdlePreload();
  }

  /// Schedule background preloading when user is idle
  void _scheduleIdlePreload() {
    final aiProvider = Provider.of<AISearchProvider>(context, listen: false);
    _idlePreloadTimer?.cancel();

    _idlePreloadTimer = Timer(const Duration(seconds: 2), () {
      if (!mounted) return;

      final bool inGenreTab = aiProvider.currentCategory == 'genreBased' &&
          _genreSearchProvider.selectedGenre != null;

      if (inGenreTab) {
        if (_genreSearchProvider.isLoadingMore) return;
      } else {
        if (aiProvider.isLoadingMore) return;
      }

      final now = DateTime.now();
      final timeSinceLastScroll = _lastScrollTime != null
          ? now.difference(_lastScrollTime!).inSeconds
          : 0;

      // Only preload if user has been idle for at least 1.5 seconds
      if (timeSinceLastScroll >= 1.5 && _scrollController.hasClients) {
        final position = _scrollController.position;
        final scrollPercentage = position.pixels / position.maxScrollExtent;

        // Preload if user is past 60% but hasn't triggered normal loading yet
        if (scrollPercentage >= 0.6 && scrollPercentage < 0.7) {
          print(
              '🌙 [Idle Preload] Loading content while user is idle (${scrollPercentage.toStringAsFixed(2)})');
          _triggerLoadMoreForActiveTab();
        }
        // Also preload if we're getting low on content (less than 10 items left to scroll)
        else {
          final int contentLength = inGenreTab
              ? _genreSearchProvider.currentTracks.length
              : aiProvider.currentRecommendations.length;

          if (contentLength > 0) {
            const itemsPerScreen = 6; // Approximate items visible
            final totalScreens = contentLength / itemsPerScreen;
            final currentScreen = scrollPercentage * totalScreens;
            final screensRemaining = totalScreens - currentScreen;

            if (screensRemaining < 1.5) {
              // Less than 2 screens of content left
              print(
                  '🌙 [Idle Preload] Loading content - low remaining ($screensRemaining screens)');
              _triggerLoadMoreForActiveTab();
            }
          }
        }
      }
    });
  }

  /// Decide which provider should load more based on the active tab
  void _triggerLoadMoreForActiveTab() {
    final aiProvider = Provider.of<AISearchProvider>(context, listen: false);
    if (aiProvider.currentCategory == 'genreBased' &&
        _genreSearchProvider.selectedGenre != null) {
      if (!_genreSearchProvider.isLoadingMore && _genreSearchProvider.hasMore) {
        _genreSearchProvider.loadMore(context);
      }
    } else {
      if (!aiProvider.isLoadingMore && aiProvider.hasMoreContent) {
        aiProvider.loadMoreRecommendations(context);
      }
    }
  }

  void _onSearchChanged(String query) {
    // Cancel any existing timer
    _searchTimer?.cancel();

    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _hasSearched = false;
        _currentQuery = '';
        _isSearching = false;
      });
      return;
    }

    // Only search if 2 or more characters (reduced from 3)
    if (query.trim().length >= 2) {
      // Debounce search by 300ms (reduced from 500ms for faster response)
      _searchTimer = Timer(const Duration(milliseconds: 300), () {
        _performSearch(query);
      });
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty || query.trim().length < 2) {
      setState(() {
        _searchResults = [];
        _hasSearched = false;
        _currentQuery = '';
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _currentQuery = query;
    });

    try {
      // Simple direct search using SpotifyService
      final results =
          await _spotifyService.searchTracks(query.trim(), limit: 25);

      if (mounted) {
        setState(() {
          _searchResults = results;
          _hasSearched = true;
          _isSearching = false;
        });
      }
    } catch (e) {
      print('Search error: $e');
      if (mounted) {
        setState(() {
          _searchResults = [];
          _hasSearched = true;
          _isSearching = false;
        });
      }
    }
  }

  void _onMoodChanged(int index) {
    final provider = Provider.of<AISearchProvider>(context, listen: false);
    HapticFeedback.selectionClick();
    provider.onMoodChanged(index, context);
    try {
      _colorController.reset();
      _colorController.forward();
    } catch (e) {
      print('⚠️ Animation controller error: $e');
    }
  }

  void _onCategoryChanged(String category) {
    Provider.of<AISearchProvider>(context, listen: false)
        .onCategoryChanged(category, context);
  }

  void _onGenreSelected(String? genre) {
    final aiProvider = Provider.of<AISearchProvider>(context, listen: false);

    if (genre != null) {
      // Use dedicated genre provider for genre-specific searches
      _genreSearchProvider.loadGenre(genre, context);
    } else {
      // Clear genre selection in GenreSearchProvider
      _genreSearchProvider.clearAll();
    }

    // Also update AISearchProvider for other categories
    aiProvider.onGenreSelected(genre, context);
  }

  void _playTrack(MusicTrack track) async {
    final spotifyProvider =
        Provider.of<SpotifyProvider>(context, listen: false);
    final appleMusicProvider =
        Provider.of<AppleMusicProvider>(context, listen: false);
    final youtubeProvider =
        Provider.of<YouTubeProvider>(context, listen: false);
    final aiProvider = Provider.of<AISearchProvider>(context, listen: false);

    HapticFeedback.lightImpact();

    // Try Spotify first if connected
    if (spotifyProvider.isConnected) {
      print(
          '🎵 Playing track through Spotify: ${track.title} by ${track.artist}');
      spotifyProvider.playTrack(track);
      return;
    }

    // If only Apple Music is connected, search for the track and play exact match with queue
    if (appleMusicProvider.isConnected) {
      print(
          '🍎 Apple Music user requesting track: ${track.title} by ${track.artist}');

      try {
        // Get tracks from current tab for queueing
        final tracksToQueue = await _getTracksFromCurrentTab(track);

        if (tracksToQueue.isNotEmpty) {
          // Use Apple Music queue manager to set up queue and play
          await _playWithAppleMusicQueue(track, tracksToQueue, appleMusicProvider);
          return;
        }

        // Fallback to single track playback if no queue available
        // Show precise loading state
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Finding exact match for "${track.title}" on Apple Music...',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            duration: const Duration(seconds: 4),
            backgroundColor: aiProvider.moods[aiProvider.selectedMoodIndex]
                ['color'] as Color,
          ),
        );

        // Search for the exact track on Apple Music using the provider's search method
        final appleMusicTrack = await appleMusicProvider.searchForTrack(
          track.artist,
          track.title,
        );

        // Clear the loading snackbar
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        if (appleMusicTrack != null) {
          // 🔍 ENHANCED: Use the new advanced matching utilities for precise cross-platform matching
          final matchScore = track.calculateMatchScore(appleMusicTrack);

          if (kDebugMode) {
            print('🔍 [AI Search] Cross-platform match analysis:');
            print('   Original (Spotify): ${track.matchDebugString}');
            print(
                '   Found (Apple Music): ${appleMusicTrack.matchDebugString}');
            print('   Match Score: ${matchScore.toStringAsFixed(1)}/1000');
          }

          // 🔧 FIX: Special case for international content with exact title matches
          final normalizedTargetTitle = track.normalizedTitle;
          final normalizedFoundTitle = appleMusicTrack.normalizedTitle;
          
          // If we have an exact title match, accept it regardless of artist differences (for international content)
          if (normalizedTargetTitle == normalizedFoundTitle) {
            print(
                '🎯 [AI Search] EXACT TITLE MATCH for international content - playing on Apple Music');

            // Play the track with exact title match
            appleMusicProvider.playTrack(appleMusicTrack);

            // Show success feedback for international match
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.check_circle,
                        color: Colors.white, size: 16),
                    const SizedBox(width: 8),
                    const Icon(Icons.language, color: Colors.white, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'International match! Playing "${appleMusicTrack.title}"',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                duration: const Duration(seconds: 2),
                backgroundColor: Colors.green.withOpacity(0.9),
              ),
            );
          }
          // 🔧 FIX: Check for Chinese character normalization match
          else if (ChineseNormalizer.containsChinese(track.title) || ChineseNormalizer.containsChinese(appleMusicTrack.title)) {
            try {
              final chineseMatch = await ChineseNormalizer.compareChineseTexts(track.title, appleMusicTrack.title);
              if (chineseMatch) {
                print(
                    '🎯 [AI Search] CHINESE NORMALIZED TITLE MATCH - playing on Apple Music');

                // Play the track with Chinese normalized match
                appleMusicProvider.playTrack(appleMusicTrack);

                // Show success feedback for Chinese match
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        const Icon(Icons.check_circle,
                            color: Colors.white, size: 16),
                        const SizedBox(width: 8),
                        const Icon(Icons.translate, color: Colors.white, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Chinese match! Playing "${appleMusicTrack.title}"',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    duration: const Duration(seconds: 2),
                    backgroundColor: Colors.green.withOpacity(0.9),
                  ),
                );
              } else {
                // Fallback to first result for Chinese content
                print(
                    '🔄 [AI Search] Chinese content - falling back to first result: "${appleMusicTrack.title}" by "${appleMusicTrack.artist}"');

                // Play the first result
                appleMusicProvider.playTrack(appleMusicTrack);

                // Show fallback feedback
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        const Icon(Icons.play_circle,
                            color: Colors.white, size: 16),
                        const SizedBox(width: 8),
                        const Icon(Icons.translate, color: Colors.white, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Playing "${appleMusicTrack.title}" (closest match)',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    duration: const Duration(seconds: 3),
                    backgroundColor: Colors.blue.withOpacity(0.9),
                  ),
                );
              }
            } catch (e) {
              if (kDebugMode) {
                print('⚠️ [AI Search] Chinese normalization failed: $e');
              }
              // Fallback to first result
              print(
                  '🔄 [AI Search] Chinese normalization failed - falling back to first result: "${appleMusicTrack.title}" by "${appleMusicTrack.artist}"');

              // Play the first result
              appleMusicProvider.playTrack(appleMusicTrack);

              // Show fallback feedback
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.play_circle,
                          color: Colors.white, size: 16),
                      const SizedBox(width: 8),
                      const Icon(Icons.translate, color: Colors.white, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Playing "${appleMusicTrack.title}" (closest match)',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  duration: const Duration(seconds: 3),
                  backgroundColor: Colors.blue.withOpacity(0.9),
                ),
              );
            }
          }
          // Only play if we have a reliable match to ensure users get the exact song they want
          else if (track.isExcellentMatch(appleMusicTrack)) {
            print(
                '✅ [AI Search] EXCELLENT match found - playing exact song on Apple Music');

            // Play the exact match
            appleMusicProvider.playTrack(appleMusicTrack);

            // Show success feedback with match confidence
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.check_circle,
                        color: Colors.white, size: 16),
                    const SizedBox(width: 8),
                    const Icon(Icons.music_note, color: Colors.white, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Exact match! Playing "${appleMusicTrack.title}"',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                duration: const Duration(seconds: 2),
                backgroundColor: Colors.green.withOpacity(0.9),
              ),
            );
          } else if (track.isGoodMatch(appleMusicTrack)) {
            print(
                '✅ [AI Search] GOOD match found - playing similar song on Apple Music');

            // Play the good match
            appleMusicProvider.playTrack(appleMusicTrack);

            // Show match feedback with slight warning
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.info, color: Colors.white, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Closest match: "${appleMusicTrack.title}" by ${appleMusicTrack.artist}',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                duration: const Duration(seconds: 3),
                backgroundColor: Colors.orange.withOpacity(0.9),
              ),
            );
          } else {
            // 🔧 FIX: Fallback to first result instead of rejecting
            print(
                '🔄 [AI Search] Match quality low (${matchScore.toStringAsFixed(1)}) - falling back to first result: "${appleMusicTrack.title}" by "${appleMusicTrack.artist}"');

            // Play the first result
            appleMusicProvider.playTrack(appleMusicTrack);

            // Show fallback feedback
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.play_circle,
                        color: Colors.white, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Playing "${appleMusicTrack.title}" (closest match)',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                duration: const Duration(seconds: 3),
                backgroundColor: Colors.blue.withOpacity(0.9),
              ),
            );
          }
        } else {
          print(
              '❌ No Apple Music match found for: ${track.title} by ${track.artist}');

          // Show specific error message for no exact match
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.search_off, color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'No exact match found on Apple Music for "${track.title}"',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              duration: const Duration(seconds: 3),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Search',
                textColor: Colors.white,
                onPressed: () {
                  // Trigger a more lenient search in Apple Music app
                  print(
                      '🔍 User requested manual search for: ${track.title} by ${track.artist}');
                },
              ),
            ),
          );
        }
      } catch (e) {
        print('❌ Error searching for Apple Music track: $e');

        // Clear the loading snackbar
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        // Show specific error message
        if (e.toString().toLowerCase().contains('unauthorized') ||
            e.toString().toLowerCase().contains('401') ||
            e.toString().toLowerCase().contains('authentication')) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.lock_outline, color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  const Text('Apple Music authentication needed'),
                ],
              ),
              duration: const Duration(seconds: 3),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'Reconnect',
                textColor: Colors.white,
                onPressed: () {
                  // Navigate to settings or trigger re-authentication
                  print('🔄 User requested Apple Music reconnection');
                },
              ),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error_outline,
                      color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  const Text('Error searching Apple Music catalog'),
                ],
              ),
              duration: const Duration(seconds: 3),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
      return;
    }

    await youtubeProvider.initialize();

    // Try YouTube as fallback if available
    if (youtubeProvider.isInitialized) {
      print(
          '🎬 YouTube fallback - attempting to play: ${track.title} by ${track.artist}');

      try {
        // Show loading state for YouTube
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Searching YouTube for "${track.title}"...',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            duration: const Duration(seconds: 3),
            backgroundColor: aiProvider.moods[aiProvider.selectedMoodIndex]
                ['color'] as Color,
          ),
        );

        // Play track through YouTube
        final success = await youtubeProvider.playTrack(track);

        // Clear the loading snackbar
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        if (success) {
          print('✅ [AI Search] YouTube playback started successfully');

          // Show success feedback
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.play_circle, color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  const Icon(Icons.video_library,
                      color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Playing "${track.title}" on YouTube',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              duration: const Duration(seconds: 2),
              backgroundColor: Colors.red.withOpacity(0.9),
            ),
          );
        } else {
          print('❌ [AI Search] YouTube playback failed');

          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error_outline,
                      color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Could not find "${track.title}" on YouTube',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              duration: const Duration(seconds: 3),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        print('❌ Error playing track on YouTube: $e');

        // Clear the loading snackbar
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                const Text('YouTube playback error'),
              ],
            ),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // No music service connected
    print('❌ No music service connected');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.music_off, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            const Text(
                'Connect Spotify, Apple Music, or enable YouTube to play tracks'),
          ],
        ),
        duration: const Duration(seconds: 4),
        backgroundColor: Colors.grey.shade700,
        action: SnackBarAction(
          label: 'Connect',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to music service connection screen
            Navigator.pushNamed(context, '/music-settings');
          },
        ),
      ),
    );
  }

  /// Scroll to top of the list
  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeOutCubic,
      );
      HapticFeedback.lightImpact();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _searchTimer?.cancel();
    _preloadTimer?.cancel();
    _idlePreloadTimer?.cancel();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _colorController.dispose();
    _scrollController.dispose();
    _genreSearchProvider.dispose();
    super.dispose();
  }

  Future<void> _refreshRecommendations() async {
    final provider = Provider.of<AISearchProvider>(context, listen: false);
    await provider.refreshRecommendations(context);
    
    // Also refresh suggested songs for cross-screen synchronization
    // This ensures profile tabs and track select screen get updated suggestions
    try {
      if (kDebugMode) {
        print('🔄 [AI Search] Refreshing suggested songs across all screens...');
      }
      await SuggestedSongsProvider.instance.refresh(context);
      if (kDebugMode) {
        print('✅ [AI Search] Suggested songs refreshed across all screens');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AI Search] Error refreshing suggested songs: $e');
      }
      // Don't block the main refresh if suggested songs refresh fails
    }
    
    HapticFeedback.lightImpact();
  }

  /// Open the AI search customization bottom sheet
  void _openCustomizationBottomSheet() {
    HapticFeedback.lightImpact();

    final provider = Provider.of<AISearchProvider>(context, listen: false);
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    // Get current settings
    final currentArtists = userProvider.currentUser?.topArtists ?? [];
    final currentGenres =
        provider.userTopGenres; // Get from AISearchProvider instead
    final currentExplorationSettings = provider.getCurrentExplorationSettings();

    // Debug logging
    print('🎵 [AISearchView] Opening customization with:');
    print('🎵 [AISearchView] User loaded: ${userProvider.currentUser != null}');
    print('🎵 [AISearchView] Current artists: $currentArtists');
    print('🎵 [AISearchView] Current genres: $currentGenres');
    print(
        '🎵 [AISearchView] AISearchProvider userTopGenres: ${provider.userTopGenres}');

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AICustomizationBottomSheet(
        initialArtists: currentArtists,
        initialGenres: currentGenres,
        initialExplorationSettings: currentExplorationSettings,
        onSave: _handleCustomizationSave,
        onCancel: _handleCustomizationCancel,
      ),
    );
  }

  /// Handle saving customization data
  Future<void> _handleCustomizationSave(CustomizationData data) async {
    try {
      final provider = Provider.of<AISearchProvider>(context, listen: false);
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      if (kDebugMode) {
        print('🎵 [AI Customization] Starting save process...');
        print('🎤 Artists to save: ${data.preferredArtists}');
        print('🎼 Genres to save: ${data.preferredGenres}');
        print('⚙️ Exploration settings: ${data.explorationSettings}');
      }

      // Step 1: Save music preferences to backend via UserProvider
      await userProvider.updateMusicPreferences(
        topArtists:
            data.preferredArtists.isNotEmpty ? data.preferredArtists : null,
        topGenres:
            data.preferredGenres.isNotEmpty ? data.preferredGenres : null,
        artistGenres: data.artistGenres,
        artistImageUrls: data.artistImageUrls,
        artistSpotifyIds: data.artistSpotifyIds,
      );
      
      // Step 1.5: Refresh user profile to load the newly saved metadata
      if (kDebugMode) {
        print('🔄 [AI Customization] Refreshing user profile to load saved metadata...');
      }
      await userProvider.fetchFullProfile();

      // Step 2: Apply exploration settings to AI provider
      provider.applyExplorationSettings(data.explorationSettings);

      // Step 3: Update AI provider with new user preferences
      provider.updateUserPreferences(
        topArtists: data.preferredArtists,
        topGenres: data.preferredGenres,
      );

      if (kDebugMode) {
        print('✅ [AI Customization] Preferences applied to AI provider');
      }

      // Step 4: Do exactly what the refresh button does
      await _refreshRecommendations();

      if (kDebugMode) {
        print(
            '✅ [AI Customization] Refreshed recommendations like refresh button');
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'AI search preferences saved! Fresh recommendations loaded.',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            action: SnackBarAction(
              label: 'Got it',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }

      if (kDebugMode) {
        print('🎉 [AI Customization] Save process completed successfully!');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AI Customization] Save process failed: $e');
      }

      // Re-throw to let the bottom sheet handle the error
      rethrow;
    }
  }

  /// Handle canceling customization
  void _handleCustomizationCancel() {
    // Nothing special needed for cancel
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = context.watch<AISearchProvider>();
    final selectedMood = provider.moods[provider.selectedMoodIndex];
    final moodColor = selectedMood['color'] as Color;

    return Consumer3<SpotifyProvider, AppleMusicProvider, YouTubeProvider>(
      builder: (context, spotifyProvider, appleMusicProvider, youtubeProvider,
          child) {
        // Only show now playing bar for Spotify and Apple Music, NOT for YouTube
        // YouTube has its own embedded player so we don't want to show an additional bar
        final hasActiveMusic = (spotifyProvider.hasActivePlayback &&
                spotifyProvider.currentTrack != null) ||
            (appleMusicProvider.hasActivePlayback &&
                appleMusicProvider.currentTrack != null);

        return Scaffold(
          extendBody: true,
          backgroundColor: theme.scaffoldBackgroundColor,
          body: Stack(
            children: [
              RefreshIndicator(
                onRefresh: _refreshRecommendations,
                color: moodColor,
                backgroundColor: theme.cardColor,
                child: CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    // App Bar
                    SliverAppBar(
                      floating: true,
                      snap: true,
                      elevation: 0,
                      backgroundColor: theme.scaffoldBackgroundColor,
                      leading: IconButton(
                        icon: Icon(
                          Icons.arrow_back,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => Navigator.pop(context),
                      ),
                      title: Row(
                        children: [
                          SizedBox(
                            width: 40,
                            height: 40,
                            child: Lottie.asset(
                              'assets/anim/ai_search.json',
                              fit: BoxFit.contain,
                              repeat: true,
                              animate: true,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'BOP AI',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface,
                              fontWeight: FontWeight.w600,
                              fontSize: 20,
                            ),
                          ),
                        ],
                      ),
                      actions: [
                        IconButton(
                          icon: Icon(
                            Icons.tune,
                            color: moodColor,
                          ),
                          onPressed: _openCustomizationBottomSheet,
                          tooltip: 'Customize AI search',
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.refresh,
                            color: moodColor,
                          ),
                          onPressed: _refreshRecommendations,
                          tooltip: 'Fresh recommendations',
                        ),
                      ],
                    ),

                    // Search Bar
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            Container(
                              height: 44, // sleek pill-shaped height
                              decoration: BoxDecoration(
                                color: theme.colorScheme
                                    .surfaceVariant, // Material 3 style surface
                                borderRadius: BorderRadius.circular(24),
                              ),
                              alignment: Alignment.center,
                              child: TextField(
                                style: TextStyle(
                                  fontSize: 14,
                                  color: theme.colorScheme.onSurface,
                                ),
                                textInputAction: TextInputAction.search,
                                controller: _searchController,
                                focusNode: _searchFocusNode,
                                onChanged: _onSearchChanged,
                                onSubmitted: _performSearch,
                                decoration: InputDecoration(
                                  hintText: 'Search songs, artists, albums',
                                  hintStyle: TextStyle(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.6),
                                  ),
                                  prefixIcon: Icon(
                                    Icons.search,
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.7),
                                  ),
                                  suffixIcon: _searchController.text.isNotEmpty
                                      ? IconButton(
                                          icon: Icon(
                                            Icons.clear,
                                            color: theme.colorScheme.onSurface
                                                .withOpacity(0.7),
                                            size: 20,
                                          ),
                                          onPressed: () {
                                            _searchController.clear();
                                            _searchFocusNode.unfocus();
                                            _searchTimer?.cancel();
                                            setState(() {
                                              _searchResults = [];
                                              _hasSearched = false;
                                              _currentQuery = '';
                                              _isSearching = false;
                                            });
                                          },
                                        )
                                      : null,
                                  border: InputBorder.none,
                                  isDense: true,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 0,
                                  ),
                                ),
                              ),
                            ),
                            if (_searchController.text.isNotEmpty &&
                                _searchController.text.length < 2)
                              Padding(
                                padding: const EdgeInsets.only(top: 8),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      size: 16,
                                      color: theme.colorScheme.onSurface
                                          .withOpacity(0.6),
                                    ),
                                    const SizedBox(width: 8),
                                    Flexible(
                                      child: Text(
                                        'Type ${2 - _searchController.text.length} more character${2 - _searchController.text.length == 1 ? '' : 's'} to search',
                                        style: TextStyle(
                                          color: theme.colorScheme.onSurface
                                              .withOpacity(0.6),
                                          fontSize: 12,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),

                    // AI Elements - Hide when searching
                    if (!_hasSearched || _searchResults.isEmpty) ...[
                      // Mood Selector
                      SliverToBoxAdapter(
                        child: Container(
                          height: 80,
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: provider.moods.length,
                            itemBuilder: (context, index) {
                              final mood = provider.moods[index];
                              final isSelected =
                                  provider.selectedMoodIndex == index;
                              final color = mood['color'] as Color;

                              // Check if any mood is currently loading
                              final isAnyMoodLoading = provider.isLoading || provider.isMoodLoading;
                              final isDisabled = isAnyMoodLoading && !isSelected;

                              return GestureDetector(
                                onTap: isDisabled ? null : () => _onMoodChanged(index),
                                child: AnimatedOpacity(
                                  duration: const Duration(milliseconds: 200),
                                  opacity: isDisabled ? 0.4 : 1.0,
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    margin: const EdgeInsets.only(right: 12),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      color: isSelected ? color : theme.cardColor,
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: isSelected
                                            ? color
                                            : theme.dividerColor.withOpacity(0.2),
                                      ),
                                    ),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          mood['emoji'],
                                          style: const TextStyle(fontSize: 20),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          mood['name'],
                                          style: TextStyle(
                                            color: isSelected
                                                ? Colors.white
                                                : theme.colorScheme.onSurface,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),

                      // AI Message
                      if (provider.showAiMessage)
                        SliverToBoxAdapter(
                          child: Container(
                            margin: const EdgeInsets.all(16),
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  moodColor.withOpacity(0.1),
                                  moodColor.withOpacity(0.05),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: moodColor.withOpacity(0.2),
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: moodColor.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.auto_awesome,
                                    color: moodColor,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    provider.currentAiMessage,
                                    style: TextStyle(
                                      color: theme.colorScheme.onSurface,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                      // Category Tabs
                      SliverToBoxAdapter(
                        child: Container(
                          height: 50,
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: provider.getCategories(context).length,
                            itemBuilder: (context, index) {
                              final category =
                                  provider.getCategories(context)[index];
                              final isSelected =
                                  provider.currentCategory == category;

                              return GestureDetector(
                                onTap: () => _onCategoryChanged(category),
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  margin: const EdgeInsets.only(right: 8),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? moodColor.withOpacity(0.1)
                                        : Colors.transparent,
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: isSelected
                                          ? moodColor.withOpacity(0.3)
                                          : theme.dividerColor.withOpacity(0.2),
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      provider.categoryLabels[category] ??
                                          category,
                                      style: TextStyle(
                                        color: isSelected
                                            ? moodColor
                                            : theme.colorScheme.onSurface
                                                .withOpacity(0.7),
                                        fontWeight: FontWeight.w600,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),

                      // Genre Filter Chips (when genreBased category is selected)
                      if (provider.currentCategory == 'genreBased')
                        SliverToBoxAdapter(
                          child: _buildGenreFilterSection(
                              theme, moodColor, provider),
                        ),
                    ],

                    // Content Area - Separate search from AI recommendations
                    if (_hasSearched && _currentQuery.isNotEmpty)
                      _buildSimpleSearchResults(theme)
                    else if (provider.isLoading)
                      _buildLoadingShimmer(moodColor)
                    else
                      _buildCurrentTab(provider.currentCategory),

                    // Loading More Indicator - Show at bottom when loading more content
                    if (!provider.isLoading && !_isSearching)
                      SliverToBoxAdapter(
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          height: (provider.currentCategory == 'genreBased'
                                  ? _genreSearchProvider.isLoadingMore
                                  : provider.isLoadingMore)
                              ? 80
                              : 0,
                          child: (provider.currentCategory == 'genreBased'
                                  ? _genreSearchProvider.isLoadingMore
                                  : provider.isLoadingMore)
                              ? Container(
                                  padding: const EdgeInsets.all(20),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  moodColor),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        'Loading more tracks...',
                                        style: TextStyle(
                                          color: theme.colorScheme.onSurface
                                              .withOpacity(0.7),
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ),
                      ),
                  ],
                ),
              ),

              // Now Playing Card Overlay
              if (!provider.isLoading && !_isSearching && hasActiveMusic)
                Positioned(
                  left: 20,
                  right: 20,
                  bottom: 20 + MediaQuery.of(context).padding.bottom,
                  child: Card(
                    margin: EdgeInsets.zero,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(16)),
                    ),
                    elevation: 8,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: spotifyProvider.hasActivePlayback &&
                              spotifyProvider.currentTrack != null
                          ? NowPlayingBar(
                              bottomPadding: 0,
                              showProgressBar: true,
                              onTap: () {
                                // Handle tap to expand
                              },
                              onDismiss: () {
                                spotifyProvider.pause();
                                spotifyProvider.clearMockTrack();
                              },
                            )
                          : appleMusicProvider.hasActivePlayback &&
                                  appleMusicProvider.currentTrack != null
                              ? const AppleNowPlayingBar()
                              : const SizedBox.shrink(), // Remove YouTube bar
                    ),
                  ),
                ),
            ],
          ),

          // Floating Action Button for scroll to top - positioned to avoid now playing card
          floatingActionButton: AnimatedOpacity(
            opacity: _showScrollToTop ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 300),
            child: AnimatedSlide(
              offset: _showScrollToTop ? Offset.zero : const Offset(0, 2),
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Container(
                margin: EdgeInsets.only(
                  bottom:
                      (!provider.isLoading && !_isSearching && hasActiveMusic)
                          ? 100
                          : 16,
                ),
                child: FloatingActionButton(
                  onPressed: _showScrollToTop ? _scrollToTop : null,
                  backgroundColor: moodColor,
                  foregroundColor: Colors.white,
                  elevation: 8,
                  child: const Icon(Icons.keyboard_arrow_up),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCurrentTab(String category) {
    switch (category) {
      case 'all':
        return AllTab(onTrackTap: _playTrack);
      case 'genreBased':
        return ChangeNotifierProvider<GenreSearchProvider>.value(
          value: _genreSearchProvider,
          child: GenreBasedTab(onTrackTap: _playTrack),
        );
      case 'artistBased':
        return ArtistBasedTab(onTrackTap: _playTrack);
      case 'moodBased':
        return MoodBasedTab(onTrackTap: _playTrack);
      case 'topTracks':
        return TopTracksTab(onTrackTap: _playTrack);
      case 'likedSongs':
        return LikedSongsTab(onTrackTap: _playTrack);
      case 'recentlyPlayed':
        return RecentlyPlayedTab(onTrackTap: _playTrack);
      case 'recentlyAdded':
        return RecentlyAddedTab(onTrackTap: _playTrack);
      case 'discover':
        return DiscoverTab(onTrackTap: _playTrack);
      default:
        return const SliverToBoxAdapter(child: SizedBox.shrink());
    }
  }

  Widget _buildGenreFilterSection(
      ThemeData theme, Color moodColor, AISearchProvider provider) {
    // Check if any genre is currently loading (including initial loading)
    final isAnyGenreLoading = provider.isLoading || provider.isLoadingGenres || _genreSearchProvider.isLoading;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          if (provider.userTopGenres.isEmpty && !provider.isLoadingGenres)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.cardColor.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'No genres found. Make sure you have listening history on Spotify.',
                      style: TextStyle(
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            )
          else
            SizedBox(
              height: 36,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount:
                    provider.userTopGenres.length + 1, // +1 for "All Genres"
                itemBuilder: (context, index) {
                  if (index == 0) {
                    // "All Genres" chip
                    final isAllGenresSelected = provider.selectedGenre == null ||
                        provider.selectedGenre!.isEmpty;
                    final isDisabled = isAnyGenreLoading && !isAllGenresSelected;

                    return GestureDetector(
                      onTap: isDisabled ? null : () {
                        _onGenreSelected(null);
                      },
                      child: AnimatedOpacity(
                        duration: const Duration(milliseconds: 200),
                        opacity: isDisabled ? 0.4 : 1.0,
                        child: Container(
                          margin: const EdgeInsets.only(right: 8),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: isAllGenresSelected
                                ? moodColor
                                : theme.cardColor,
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: isAllGenresSelected
                                  ? moodColor
                                  : theme.dividerColor.withOpacity(0.3),
                            ),
                          ),
                          child: Center(
                            child: Text(
                              'all genres',
                              style: TextStyle(
                                color: isAllGenresSelected
                                    ? Colors.white
                                    : theme.colorScheme.onSurface,
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  }

                  final genre = provider.userTopGenres[index - 1];
                  final isSelected = provider.selectedGenre == genre;
                  final isDisabled = isAnyGenreLoading && !isSelected;

                  return GestureDetector(
                    onTap: isDisabled ? null : () => _onGenreSelected(genre),
                    child: AnimatedOpacity(
                      duration: const Duration(milliseconds: 200),
                      opacity: isDisabled ? 0.4 : 1.0,
                      child: Container(
                        margin: const EdgeInsets.only(right: 8),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: isSelected ? moodColor : theme.cardColor,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected
                                ? moodColor
                                : theme.dividerColor.withOpacity(0.3),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            genre.toLowerCase(),
                            style: TextStyle(
                              color: isSelected
                                  ? Colors.white
                                  : theme.colorScheme.onSurface,
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingShimmer(Color moodColor) {
    return SliverToBoxAdapter(
      child: Container(
        height: 400,
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Lottie animation
            SizedBox(
              width: 200,
              height: 200,
              child: Lottie.asset(
                'assets/anim/ai_search.json',
                width: 160,
                height: 160,
                fit: BoxFit.contain,
                repeat: true,
                animate: true,
              ),
            ),
            const SizedBox(height: 24),
            // Loading text
            Text(
              'Discovering your perfect tracks...',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'AI is analyzing your music taste',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            // Progress indicator with mood color
            SizedBox(
              width: 200,
              child: LinearProgressIndicator(
                backgroundColor: moodColor.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(moodColor),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleSearchResults(ThemeData theme) {
    if (_isSearching) {
      return SliverToBoxAdapter(
        child: Container(
          height: 200,
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: theme.colorScheme.primary,
                strokeWidth: 2,
              ),
              const SizedBox(height: 16),
              Text(
                'Searching for "$_currentQuery"...',
                style: TextStyle(
                  color: theme.colorScheme.onSurface,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return SliverToBoxAdapter(
        child: Container(
          height: 300,
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: theme.colorScheme.onSurface.withOpacity(0.3),
              ),
              const SizedBox(height: 16),
              Text(
                'No results found',
                style: TextStyle(
                  color: theme.colorScheme.onSurface.withOpacity(0.8),
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Try searching for "$_currentQuery" with different terms',
                style: TextStyle(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Simple list of search results
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index >= _searchResults.length) return null;

          final track = _searchResults[index];
          return Padding(
            padding: EdgeInsets.fromLTRB(
              16,
              index == 0 ? 16 : 8,
              16,
              index == _searchResults.length - 1 ? 32 : 8,
            ),
            child: TrackCard(
              track: track,
              onTap: () => _playTrack(track),
            ),
          );
        },
        childCount: _searchResults.length,
      ),
    );
  }

  /// Get tracks from the current tab for queueing
  Future<List<MusicTrack>> _getTracksFromCurrentTab(MusicTrack currentTrack) async {
    final aiProvider = Provider.of<AISearchProvider>(context, listen: false);
    final List<MusicTrack> tracksToQueue = [];

    try {
      // Check if we're in genre-based tab
      if (aiProvider.currentCategory == 'genreBased' &&
          _genreSearchProvider.selectedGenre != null) {
        // Get tracks from genre search provider
        final genreTracks = _genreSearchProvider.currentTracks;

        // Find the index of the current track
        final currentIndex = genreTracks.indexWhere((track) =>
            track.id == currentTrack.id ||
            (track.title.toLowerCase() == currentTrack.title.toLowerCase() &&
             track.artist.toLowerCase() == currentTrack.artist.toLowerCase()));

        if (currentIndex != -1) {
          // Add the current track first, then up to 49 more tracks after it
          tracksToQueue.add(currentTrack);

          // Add tracks after the current track
          for (int i = currentIndex + 1; i < genreTracks.length && tracksToQueue.length < 50; i++) {
            tracksToQueue.add(genreTracks[i]);
          }

          // If we need more tracks, wrap around to the beginning
          if (tracksToQueue.length < 50) {
            for (int i = 0; i < currentIndex && tracksToQueue.length < 50; i++) {
              tracksToQueue.add(genreTracks[i]);
            }
          }
        } else {
          // Current track not found, just add it and then other tracks
          tracksToQueue.add(currentTrack);
          tracksToQueue.addAll(genreTracks.take(49));
        }
      } else {
        // Get tracks from AI search provider for other categories
        final aiTracks = aiProvider.currentRecommendations;

        // Find the index of the current track
        final currentIndex = aiTracks.indexWhere((track) =>
            track.id == currentTrack.id ||
            (track.title.toLowerCase() == currentTrack.title.toLowerCase() &&
             track.artist.toLowerCase() == currentTrack.artist.toLowerCase()));

        if (currentIndex != -1) {
          // Add the current track first, then up to 49 more tracks after it
          tracksToQueue.add(currentTrack);

          // Add tracks after the current track
          for (int i = currentIndex + 1; i < aiTracks.length && tracksToQueue.length < 50; i++) {
            tracksToQueue.add(aiTracks[i]);
          }

          // If we need more tracks, wrap around to the beginning
          if (tracksToQueue.length < 50) {
            for (int i = 0; i < currentIndex && tracksToQueue.length < 50; i++) {
              tracksToQueue.add(aiTracks[i]);
            }
          }
        } else {
          // Current track not found, just add it and then other tracks
          tracksToQueue.add(currentTrack);
          tracksToQueue.addAll(aiTracks.take(49));
        }
      }

      print('🎵 [Queue] Prepared ${tracksToQueue.length} tracks for Apple Music queue');
      return tracksToQueue;

    } catch (e) {
      print('❌ [Queue] Error getting tracks from current tab: $e');
      // Fallback to just the current track
      return [currentTrack];
    }
  }

  /// Play track with Apple Music queue using the queue manager
  Future<void> _playWithAppleMusicQueue(
      MusicTrack currentTrack,
      List<MusicTrack> tracksToQueue,
      AppleMusicProvider appleMusicProvider) async {

    try {
      print('🍎 [Queue] Setting up Apple Music queue with ${tracksToQueue.length} tracks');

      // Use the queue manager from the Apple Music provider
      final queueManager = appleMusicProvider.queueManager;

      // Set up the queue with the tracks
      final success = await queueManager.setQueue(
        tracks: tracksToQueue,
        collectionType: 'ai_search',
        collectionId: 'ai_search_${DateTime.now().millisecondsSinceEpoch}',
        collectionMetadata: {
          'name': 'AI Search Queue',
          'type': 'ai_search',
        },
        startIndex: 0,
      );

      if (success) {
        print('✅ [Queue] Apple Music queue set up successfully');

        // Show success feedback
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.queue_music, color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  const Icon(Icons.music_note, color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Playing "${currentTrack.title}" with ${tracksToQueue.length - 1} tracks queued',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              duration: const Duration(seconds: 3),
              backgroundColor: Colors.green.withValues(alpha: 0.9),
            ),
          );
        }
      } else {
        throw Exception('Failed to set up Apple Music queue');
      }

    } catch (e) {
      print('❌ [Queue] Error setting up Apple Music queue: $e');

      // Fallback to single track playback
      try {
        final appleMusicTrack = await appleMusicProvider.searchForTrack(
          currentTrack.artist,
          currentTrack.title,
        );

        if (appleMusicTrack != null) {
          appleMusicProvider.playTrack(appleMusicTrack);

          // Show fallback feedback
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.play_circle, color: Colors.white, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Playing "${appleMusicTrack.title}" (queue setup failed)',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                duration: const Duration(seconds: 3),
                backgroundColor: Colors.orange.withValues(alpha: 0.9),
              ),
            );
          }
        } else {
          throw Exception('No Apple Music track found');
        }
      } catch (fallbackError) {
        print('❌ [Queue] Fallback playback also failed: $fallbackError');

        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.white, size: 16),
                  SizedBox(width: 8),
                  Text('Failed to play track on Apple Music'),
                ],
              ),
              duration: Duration(seconds: 3),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
