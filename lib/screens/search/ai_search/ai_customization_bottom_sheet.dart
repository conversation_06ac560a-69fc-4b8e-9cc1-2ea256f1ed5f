import 'package:bop_maps/screens/search/ai_search/tabs/artist_preferences_tab.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/exploration_settings_tab.dart';
import 'package:bop_maps/screens/search/ai_search/tabs/genre_prefrences_tab.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';

import '../../../models/artist_with_genre.dart';
import '../../../models/customization_data.dart';
import '../../../models/exploration_settings.dart';
import '../../../services/music/spotify_genre_service.dart';
import '../../../services/music/spotify_service.dart';

class AICustomizationBottomSheet extends StatefulWidget {
  final List<String> initialArtists;
  final List<String> initialGenres;
  final ExplorationSettings initialExplorationSettings;
  final Function(CustomizationData) onSave;
  final VoidCallback onCancel;

  const AICustomizationBottomSheet({
    super.key,
    required this.initialArtists,
    required this.initialGenres,
    required this.initialExplorationSettings,
    required this.onSave,
    required this.onCancel,
  });

  @override
  State<AICustomizationBottomSheet> createState() => _AICustomizationBottomSheetState();
}

class _AICustomizationBottomSheetState extends State<AICustomizationBottomSheet>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;
  late AnimationController _slideController;
  late AnimationController _fadeController;
  
  // Local state for tracking unsaved changes
  late List<String> _selectedArtists;
  late List<String> _selectedGenres;
  late ExplorationSettings _selectedExplorationSettings;
  
  // Track full artist objects with metadata
  List<ArtistWithGenre> _selectedArtistObjects = [];

  // Loading and validation states
  bool _isSaving = false;
  bool _hasUnsavedChanges = false;

  // Keep alive to preserve tab states
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    // Initialize tab controller with 3 tabs
    _tabController = TabController(length: 3, vsync: this);
    
    // Initialize local state with provided values
    _selectedArtists = List<String>.from(widget.initialArtists);
    _selectedGenres = List<String>.from(widget.initialGenres);
    _selectedExplorationSettings = widget.initialExplorationSettings;
    
    // Listen for tab changes to provide haptic feedback
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        HapticFeedback.selectionClick();
        _checkForChanges();
      }
    });
    
    // Start animations
    _slideController.forward();
    _fadeController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  /// Check if there are unsaved changes
  void _checkForChanges() {
    final hasChanges = !_listEquals(_selectedArtists, widget.initialArtists) ||
        !_listEquals(_selectedGenres, widget.initialGenres) ||
        _selectedExplorationSettings != widget.initialExplorationSettings;
    
    if (hasChanges != _hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = hasChanges;
      });
    }
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  /// Handle save action
  Future<void> _handleSave() async {
    if (_isSaving) return;
    
    setState(() {
      _isSaving = true;
    });
    
    try {
      final customizationData = CustomizationData(
        preferredArtists: _selectedArtists,
        preferredGenres: _selectedGenres,
        explorationSettings: _selectedExplorationSettings,
      );
      
      // Extract metadata from selected artist objects
      final Map<String, String> artistImageUrls = {};
      final Map<String, String> artistSpotifyIds = {};
      final Map<String, List<String>> artistGenres = {};
      
      for (final artist in _selectedArtistObjects) {
        if (artist.imageUrl != null) {
          artistImageUrls[artist.name] = artist.imageUrl!;
        }
        if (artist.id.isNotEmpty) {
          artistSpotifyIds[artist.name] = artist.id;
        }
        if (artist.genres.isNotEmpty) {
          artistGenres[artist.name] = artist.genres;
        }
      }
      
      print('💾 [CustomizationSheet] Extracted metadata for save:');
      print('   - Artist images: ${artistImageUrls.length}');
      print('   - Spotify IDs: ${artistSpotifyIds.length}');
      print('   - Artist genres: ${artistGenres.length}');
      
      // Create a local copy of the data to pass to the save function
      final dataToSave = CustomizationData(
        preferredArtists: List<String>.from(_selectedArtists),
        preferredGenres: List<String>.from(_selectedGenres),
        explorationSettings: _selectedExplorationSettings,
        artistImageUrls: artistImageUrls.isNotEmpty ? artistImageUrls : null,
        artistSpotifyIds: artistSpotifyIds.isNotEmpty ? artistSpotifyIds : null,
        artistGenres: artistGenres.isNotEmpty ? artistGenres : null,
      );
      
      // Close the bottom sheet immediately so user can see loading animation
      if (mounted) {
        Navigator.of(context).pop();
      }
      
      // Execute the save operation after closing the sheet
      await widget.onSave(dataToSave);
    } catch (e) {
      // Show error message in the parent view since we've already closed this sheet
      print('Failed to save preferences: ${e.toString()}');
      // We can't show a snackbar here since the sheet is already closed
    }
    // No need for finally block with _isSaving = false since the sheet is closed
  }

  /// Handle cancel action
  void _handleCancel() {
    if (_hasUnsavedChanges) {
      // Show confirmation dialog for unsaved changes
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Discard Changes?'),
          content: const Text('You have unsaved changes. Are you sure you want to discard them?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Keep Editing'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Close bottom sheet
                widget.onCancel();
              },
              child: const Text('Discard'),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
      widget.onCancel();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    // Use fullscreen when keyboard is visible, otherwise 85% of screen height
    final bottomSheetHeight = isKeyboardVisible ? screenHeight : screenHeight * 0.85;

    return Container(
      height: bottomSheetHeight,
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: isKeyboardVisible
            ? BorderRadius.zero // No border radius for fullscreen
            : const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Add top padding when fullscreen to account for status bar
          if (isKeyboardVisible)
            SizedBox(height: MediaQuery.of(context).padding.top),

          // Header with drag handle and title
          _buildHeader(theme),

          // Tab bar
          _buildTabBar(theme),

          // Tab content - this will automatically adjust to available space
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildArtistsTab(),
                _buildGenresTab(),
                _buildExplorationTab(),
              ],
            ),
          ),

          // Action buttons
          _buildActionButtons(theme),
        ],
      ),
    );
  }

  /// Build the header with drag handle, title, and close button
  Widget _buildHeader(ThemeData theme) {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    return FadeTransition(
      opacity: _fadeController,
      child: Container(
        padding: const EdgeInsets.fromLTRB(20, 12, 20, 16),
        child: Column(
          children: [
            // Drag handle (hide when fullscreen)
            if (!isKeyboardVisible)
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: theme.dividerColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

            if (!isKeyboardVisible) const SizedBox(height: 16),

            // Title and close button
            Row(
              children: [
                // Back button when fullscreen
                if (isKeyboardVisible)
                  IconButton(
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      _handleCancel();
                    },
                    icon: const Icon(Icons.arrow_back),
                    tooltip: 'Back',
                  ),

                Expanded(
                  child: Text(
                    'Customize AI Search',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                // Close button (always visible)
                if (!isKeyboardVisible)
                  IconButton(
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      _handleCancel();
                    },
                    icon: const Icon(Icons.close),
                    tooltip: 'Close',
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build the tab bar
  Widget _buildTabBar(ThemeData theme) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, -0.5),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeOutCubic,
      )),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12),
        ),
        child: TabBar(
          controller: _tabController,
          indicator: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(10),
          ),
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: Colors.transparent,
          labelColor: Colors.white,
          unselectedLabelColor: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
          tabs: const [
            Tab(
              icon: Icon(Icons.person, size: 20),
              text: 'Artists',
            ),
            Tab(
              icon: Icon(Icons.music_note, size: 20),
              text: 'Genres',
            ),
            Tab(
              icon: Icon(Icons.tune, size: 20),
              text: 'Exploration',
            ),
          ],
        ),
      ),
    );
  }

  /// Build the artists tab with search and selection capabilities
  Widget _buildArtistsTab() {
    return AutomaticKeepAlive(
      child: ArtistPreferencesTab(
        initialArtists: _selectedArtists,
        onArtistsChanged: (artists) {
          setState(() {
            _selectedArtists = artists;
            _checkForChanges();
          });
        },
        onArtistObjectsChanged: (artistObjects) {
          setState(() {
            _selectedArtistObjects = artistObjects;
          });
          print('🎨 [CustomizationSheet] Updated artist objects: ${artistObjects.length}');
          for (final artist in artistObjects) {
            print('   - ${artist.name}: Image=${artist.imageUrl != null}, SpotifyId=${artist.id}');
          }
        },
      ),
    );
  }

  /// Build the genres tab with search and selection capabilities
  Widget _buildGenresTab() {
    return AutomaticKeepAlive(
      child: GenrePreferencesTab(
        initialGenres: _selectedGenres,
        onGenresChanged: (genres) {
          setState(() {
            _selectedGenres = genres;
            _checkForChanges();
          });
        },
      ),
    );
  }

  /// Build the exploration settings tab with predefined modes
  Widget _buildExplorationTab() {
    return AutomaticKeepAlive(
      child: ExplorationSettingsTab(
        initialSettings: _selectedExplorationSettings,
        onSettingsChanged: (settings) {
          setState(() {
            _selectedExplorationSettings = settings;
            _checkForChanges();
          });
        },
      ),
    );
  }

  /// Build the action buttons (Save/Cancel)
  Widget _buildActionButtons(ThemeData theme) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 1),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeOutCubic,
      )),
      child: Container(
        padding: EdgeInsets.fromLTRB(
          20,
          16,
          20,
          16 + MediaQuery.of(context).padding.bottom,
        ),
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          border: Border(
            top: BorderSide(
              color: theme.dividerColor.withValues(alpha: 0.2),
            ),
          ),
        ),
        child: Row(
          children: [
            // Cancel button
            Expanded(
              child: OutlinedButton(
                onPressed: _isSaving ? null : () {
                  HapticFeedback.lightImpact();
                  _handleCancel();
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Cancel',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Save button
            Expanded(
              child: ElevatedButton(
                onPressed: (_hasUnsavedChanges && !_isSaving) ? () {
                  HapticFeedback.lightImpact();
                  _handleSave();
                } : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isSaving
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        _hasUnsavedChanges ? 'Save & Refresh' : 'Save',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}