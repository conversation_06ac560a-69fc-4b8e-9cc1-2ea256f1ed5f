import 'package:bop_maps/models/music_track.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_animate/flutter_animate.dart';

class RecommendationsGrid extends StatelessWidget {
  final List<MusicTrack> tracks;
  final bool isLoadingMore;
  final bool hasMoreContent;
  final Color moodColor;
  final bool isDark;
  final Function(MusicTrack) onTrackTap;

  const RecommendationsGrid({
    super.key,
    required this.tracks,
    required this.isLoadingMore,
    required this.hasMoreContent,
    required this.moodColor,
    required this.isDark,
    required this.onTrackTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final totalItems = tracks.length;

    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      sliver: SliverGrid(
        gridDelegate: SliverQuiltedGridDelegate(
          crossAxisCount: 2,
          mainAxisSpacing: 12,
          crossAxisSpacing: 12,
          repeatPattern: QuiltedGridRepeatPattern.inverted,
          pattern: [
            const QuiltedGridTile(2, 2), // Large tile (index 0)
            const QuiltedGridTile(1, 1), // Regular tile (index 1)
            const QuiltedGridTile(1, 1), // Regular tile (index 2)
            const QuiltedGridTile(2, 1), // Wide tile (index 3)
            const QuiltedGridTile(1, 1), // Regular tile (index 4)
            const QuiltedGridTile(1, 1), // Regular tile (index 5)
            const QuiltedGridTile(1, 2), // Wide tile bottom (index 6)
            const QuiltedGridTile(1, 1), // Regular tile (index 7)
            const QuiltedGridTile(1, 1), // Regular tile (index 8)
          ],
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            // Show loading indicator at the bottom while loading more
            if (index >= tracks.length) {
              if (isLoadingMore && index == tracks.length) {
                return Container(
                  decoration: BoxDecoration(
                    color: theme.cardColor.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: theme.dividerColor.withOpacity(0.2),
                    ),
                  ),
                  child: Center(
                    child: CircularProgressIndicator(
                      color: moodColor,
                      strokeWidth: 2,
                    ),
                  ),
                );
              } else if (!hasMoreContent && index == tracks.length) {
                return Container(
                  decoration: BoxDecoration(
                    color: theme.cardColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: theme.dividerColor.withOpacity(0.2),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      'No more\nrecommendations',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                );
              }
              return null;
            }

            final track = tracks[index];
            const tilePattern = [
              QuiltedGridTile(2, 2), // Large tile
              QuiltedGridTile(1, 1), // Regular tile
              QuiltedGridTile(1, 1), // Regular tile
              QuiltedGridTile(2, 1), // Wide tile
              QuiltedGridTile(1, 1), // Regular tile
              QuiltedGridTile(1, 1), // Regular tile
              QuiltedGridTile(1, 2), // Wide tile (bottom)
              QuiltedGridTile(1, 1), // Regular tile
              QuiltedGridTile(1, 1), // Regular tile
            ];

            final patternIndex = index % tilePattern.length;
            final tile = tilePattern[patternIndex];
            final isLarge = tile.crossAxisCount == 2 && tile.mainAxisCount == 2;
            final isWide = tile.crossAxisCount == 2 && tile.mainAxisCount == 1 || tile.crossAxisCount == 1 && tile.mainAxisCount == 2;

            return ScaleOnTap(
              onTap: () => onTrackTap(track),
              child: Container(
                height: isLarge ? null : isWide ? 120 : 140,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: _getCardGradient(index, moodColor, isDark),
                  ),
                  border: Border.all(
                    color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: moodColor,
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(isDark ? 0.4 : 0.1),
                      blurRadius: 15,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Stack(
                    children: [
                      // Album art background
                      if (track.albumArt.isNotEmpty)
                        Positioned.fill(
                          child: CachedNetworkImage(
                            imageUrl: track.albumArt,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: moodColor,
                              child: Center(
                                child: Icon(
                                  Icons.music_note,
                                  color: moodColor,
                                  size: isLarge ? 48 : 32,
                                ),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: moodColor,
                              child: Center(
                                child: Icon(
                                  Icons.music_note,
                                  color: moodColor,
                                  size: isLarge ? 48 : 32,
                                ),
                              ),
                            ),
                          ),
                        ),

                      // Gradient overlay
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.3),
                                Colors.black.withOpacity(0.7),
                              ],
                            ),
                          ),
                        ),
                      ),

                      // Content
                      Positioned.fill(
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Top row with play button for larger tiles
                              if (isLarge) ...[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: moodColor,
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: const Icon(
                                        Icons.play_arrow,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ],
                                ),
                                const Spacer(),
                              ],

                              // Track info
                              if (isLarge) ...[
                                // Large tile layout
                                Text(
                                  track.title,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    height: 1.1,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  track.artist,
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ] else ...[
                                // Small tile layout - push content to bottom
                                const Spacer(),
                                Text(
                                  track.title,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    height: 1.1,
                                  ),
                                  maxLines: isWide ? 2 : 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  track.artist,
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.8),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),

                      // Play button overlay for small tiles
                      if (!isLarge)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: moodColor,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: const Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
          childCount: totalItems + (isLoadingMore ? 1 : 0),
        ),
      ),
    );
  }

  List<Color> _getCardGradient(int index, Color moodColor, bool isDark) {
    // Create varied gradients for visual interest
    final gradients = [
      [
        moodColor.withOpacity(0.8),
        moodColor.withOpacity(0.4),
      ],
      [
        Colors.purple.withOpacity(0.6),
        Colors.blue.withOpacity(0.4),
      ],
      [
        Colors.orange.withOpacity(0.6),
        Colors.pink.withOpacity(0.4),
      ],
      [
        Colors.teal.withOpacity(0.6),
        Colors.green.withOpacity(0.4),
      ],
      [
        Colors.indigo.withOpacity(0.6),
        Colors.purple.withOpacity(0.4),
      ],
    ];

    return gradients[index % gradients.length];
  }
}

class ScaleOnTap extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;
  const ScaleOnTap({Key? key, required this.child, required this.onTap}) : super(key: key);

  @override
  State<ScaleOnTap> createState() => _ScaleOnTapState();
}

class _ScaleOnTapState extends State<ScaleOnTap> {
  bool _pressed = false;

  void _handleTapDown(TapDownDetails details) {
    setState(() => _pressed = true);
  }

  void _handleTapUp(TapUpDetails details) {
    widget.onTap();
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() => _pressed = false);
      }
    });
  }

  void _handleTapCancel() {
    setState(() => _pressed = false);
  }

  @override
  Widget build(BuildContext context) {
    // Using flutter_animate to apply a quick scale effect when _pressed toggles.
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: widget.child.animate(target: _pressed ? 1 : 0).scale(
            begin: const Offset(1, 1),
            end: const Offset(0.93, 0.93),
            duration: 100.ms,
            curve: Curves.easeOut,
          ),
    );
  }
} 