import 'package:bop_maps/models/music_track.dart';
import 'package:bop_maps/models/exploration_settings.dart';
import 'package:bop_maps/providers/spotify_provider.dart';
import 'package:bop_maps/services/ai/ai_recommendation_service.dart';
import 'package:bop_maps/services/music/lastfm_service.dart';
import 'package:bop_maps/services/music/musicbrainz_service.dart';
import 'package:bop_maps/services/music/spotify_genre_service.dart';
import 'package:bop_maps/services/music/spotify_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;

import 'package:provider/provider.dart';
import '../../../providers/user_provider.dart';

class AISearchProvider with ChangeNotifier {
  // Services
  final SpotifyService _spotifyService = SpotifyService();
  final AIRecommendationService _aiRecommendationService =
      AIRecommendationService();
  final MusicBrainzService _musicBrainzService = MusicBrainzService();

  // State variables
  List<MusicTrack> _currentRecommendations = [];
  bool _debugShuffling =
      false; // Set to true to enable shuffle distribution analysis
  Map<String, List<MusicTrack>> _categorizedRecommendations = {};
  // Keep a dedicated list of recommendations for every individual genre that the
  // user can select in the "Your Genres" tab.  These lists start out empty and
  // are only populated once the user actually requests that genre so we don't
  // waste network calls.
  final Map<String, List<MusicTrack>> _genreRecommendations = {};
  String _currentCategory = 'all';
  bool _isLoading = false;

  // Pagination state
  bool _isLoadingMore = false;
  bool _hasMoreContent = true;
  int _currentPage = 0;
  final int _pageSize = 60; // OPTIMIZED: Increased from 20 to 60 for much richer feed and faster loading

  // Exclusion sets for avoiding duplicates with pins tab
  Set<String> _excludedTrackIds = {};
  Set<String> _likedSongIds = {};
  Set<String> _topTrackIds = {};
  // Rolling list to track recent track IDs to prevent immediate repeats
  List<String> _recentTrackIds = [];
  static const int _maxTrackIdHistory =
      100; // Rolling window size - reduced for better performance

  // Mood system
  int _selectedMoodIndex = 0;
  String _currentAiMessage = '';
  bool _showAiMessage = false;
  bool _isMoodLoading = false;
  DateTime? _lastMoodChange;

  // Genre and artist state
  List<String> _userTopGenres = [];
  List<String> _userTopArtists = [];
  String? _selectedGenre;
  bool _isLoadingGenres = false;
  String? _currentGenreFilter;

  // Anti-repetition tracking
  final Set<String> _usedQueries = <String>{};
  final Set<String> _recentArtists = <String>{};
  final Set<String> _usedArtistQueryCombos = <String>{};
  int _queryVariationSeed = 0;
  DateTime? _lastResetTime;

  // Mood-specific anti-repetition tracking
  final Set<String> _usedMoodQueries = <String>{};
  final Set<String> _usedMoodPlaylists = <String>{};
  final Map<String, Set<String>> _moodQueryHistory = {};
  DateTime? _lastMoodResetTime;

  // MusicBrainz validation cache for AI search
  final Map<String, bool> _artistGenreValidationCache = {};
  final Map<String, DateTime> _validationCacheTimestamps = {};
  static const Duration _validationCacheExpiry = Duration(hours: 2);

  // Artist track cache for comprehensive artist coverage - OPTIMIZED FOR LARGE ARTIST LISTS
  final Map<String, List<MusicTrack>> _artistTrackCache = {};
  final Map<String, DateTime> _artistCacheTimestamps = {};
  static const Duration _artistCacheExpiry = Duration(hours: 6); // Increased for better performance

  // Batch processing for artist operations - NEW OPTIMIZATION
  final Set<String> _pendingArtistRequests = {};
  Timer? _artistBatchTimer;
  static const Duration _artistBatchDelay = Duration(milliseconds: 300);
  static const int _maxArtistBatchSize = 8; // Process artists in batches of 8

  // Progressive artist exploration system
  final Set<String> _exploredArtists = {};
  final List<String> _discoveryQueue = [];
  final Map<String, List<String>> _similarArtistCache = {};
  int _explorationDepth = 0;

  // 🚀 EXPLORATION CONFIGURATION - Easily tune discovery rates
  static const int _maxExplorationDepth = 4;

  // Configurable exploration rates
  int _fastExpansionRate = 15; // Artists per primary expansion
  int _secondLevelExpansionRate = 10; // Artists per second-level expansion
  int _backgroundExpansionRate = 12; // Artists per background expansion
  int _continuousDiscoveryRate = 8; // Artists per continuous discovery
  int _primaryTargetsCount = 5; // Seed artists to expand per cycle
  int _secondLevelTargetsCount = 3; // Second-level artists per cycle
  int _backgroundTargetsCount = 2; // Background artists per cycle
  int _discoveryQueueTarget = 50; // Target queue size for background expansion
  int _artistsPerPage = 12; // OPTIMIZED: Increased from 8 to 12 artists processed per pagination
  int _tracksPerArtist = 40; // OPTIMIZED: Increased from 25 to 40 tracks fetched per artist
  Duration _backgroundExpansionInterval = const Duration(seconds: 30);
  DateTime? _lastBackgroundExpansion;

  // Current exploration mode tracking
  String _currentExplorationMode = 'normal';

  // Store context reference for background operations
  BuildContext? _currentContext;

  // Getters
  List<MusicTrack> get currentRecommendations => _currentRecommendations;
  Map<String, List<MusicTrack>> get categorizedRecommendations =>
      _categorizedRecommendations;
  String get currentCategory => _currentCategory;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  bool get hasMoreContent => _hasMoreContent;
  int get selectedMoodIndex => _selectedMoodIndex;
  String get currentAiMessage => _currentAiMessage;
  bool get showAiMessage => _showAiMessage;
  bool get isMoodLoading => _isMoodLoading;
  List<String> get userTopGenres => _userTopGenres;
  List<String> get userTopArtists => _userTopArtists;
  String? get selectedGenre => _selectedGenre;
  bool get isLoadingGenres => _isLoadingGenres;
  List<Map<String, dynamic>> get moods => _moods;
  Map<String, String> get categoryLabels => _categoryLabels;
  String get currentExplorationMode => _currentExplorationMode;

  /// Enable or disable shuffle debugging output
  void setShuffleDebugging(bool enabled) {
    _debugShuffling = enabled;
    if (enabled) {
      print('🔧 Shuffle debugging ENABLED - will show distribution analysis');
    } else {
      print('🔧 Shuffle debugging DISABLED');
    }
  }

  /// Get artist cache statistics for debugging
  Map<String, dynamic> getArtistCacheStats() {
    final now = DateTime.now();
    int totalTracks = 0;
    int expiredEntries = 0;

    _artistTrackCache.forEach((artist, tracks) {
      totalTracks += tracks.length;
      final cacheTime = _artistCacheTimestamps[artist];
      if (cacheTime != null && now.difference(cacheTime) > _artistCacheExpiry) {
        expiredEntries++;
      }
    });

    final stats = {
      'cached_artists': _artistTrackCache.length,
      'total_cached_tracks': totalTracks,
      'expired_entries': expiredEntries,
      'cache_hit_potential': _artistTrackCache.isNotEmpty
          ? ((_artistTrackCache.length - expiredEntries) /
                  _artistTrackCache.length *
                  100)
              .toStringAsFixed(1)
          : '0.0',
    };

    print('🗄️ [Artist Cache Stats] ${stats.toString()}');
    return stats;
  }

  /// Print detailed artist cache information
  void debugArtistCache() {
    print('\n🗄️ === ARTIST CACHE DEBUG INFO ===');
    if (_artistTrackCache.isEmpty) {
      print('Cache is empty');
      return;
    }

    final now = DateTime.now();
    _artistTrackCache.forEach((artist, tracks) {
      final cacheTime = _artistCacheTimestamps[artist];
      final age = cacheTime != null ? now.difference(cacheTime) : null;
      final isExpired = age != null && age > _artistCacheExpiry;

      print(
          '🎤 $artist: ${tracks.length} tracks, age: ${age?.inMinutes ?? "unknown"}min${isExpired ? " (EXPIRED)" : ""}');
    });
    print('🗄️ === END CACHE DEBUG ===\n');
  }

  /// Diagnose duplicate and performance issues
  void diagnoseDuplicatesAndPerformance() {
    print('\n🔍 === DUPLICATE & PERFORMANCE DIAGNOSIS ===');

    // Check current recommendations for duplicates
    final currentTrackIds = <String>{};
    final currentTitleArtist = <String>{};
    int duplicateIds = 0;
    int duplicateTitleArtist = 0;

    for (final track in _currentRecommendations) {
      if (currentTrackIds.contains(track.id)) {
        duplicateIds++;
        print(
            '   🚨 DUPLICATE ID: "${track.title}" by ${track.artist} (${track.id})');
      } else {
        currentTrackIds.add(track.id);
      }

      final titleArtistKey =
          '${_cleanTrackTitle(track.title)}-${track.artist.toLowerCase().trim()}';
      if (currentTitleArtist.contains(titleArtistKey)) {
        duplicateTitleArtist++;
        print(
            '   🚨 DUPLICATE TITLE+ARTIST: "${track.title}" by ${track.artist}');
      } else {
        currentTitleArtist.add(titleArtistKey);
      }
    }

    // Performance metrics
    print('📊 CURRENT STATE:');
    print('   🎵 Current recommendations: ${_currentRecommendations.length}');
    print('   🆔 Duplicate IDs found: $duplicateIds');
    print('   🎭 Duplicate title+artist: $duplicateTitleArtist');
    print(
        '   📝 Recent track IDs: ${_recentTrackIds.length}/$_maxTrackIdHistory');
    print('   🗄️ Artist cache size: ${_artistTrackCache.length}');
    print('   📄 Current page: $_currentPage');
    print('   🔄 Has more content: $_hasMoreContent');

    // Check for clustering (same artists appearing close together)
    print('🔍 CLUSTERING ANALYSIS (last 20 tracks):');
    final lastTracks = _currentRecommendations.length > 20
        ? _currentRecommendations
            .skip(_currentRecommendations.length - 20)
            .toList()
        : _currentRecommendations;

    for (int i = 1; i < lastTracks.length; i++) {
      final prevTrack = lastTracks[i - 1];
      final currTrack = lastTracks[i];

      if (prevTrack.artist.toLowerCase() == currTrack.artist.toLowerCase()) {
        print(
            '   ⚠️ CONSECUTIVE ARTISTS: ${prevTrack.artist} at positions ${_currentRecommendations.length - lastTracks.length + i - 1} and ${_currentRecommendations.length - lastTracks.length + i}');
      }

      if (prevTrack.album.toLowerCase() == currTrack.album.toLowerCase()) {
        print(
            '   ⚠️ CONSECUTIVE ALBUMS: ${prevTrack.album} at positions ${_currentRecommendations.length - lastTracks.length + i - 1} and ${_currentRecommendations.length - lastTracks.length + i}');
      }
    }

    print('🔍 === END DIAGNOSIS ===\n');

    // Show exploration stats
    final explorationStats = getExplorationStats();
    print('🚀 EXPLORATION STATS: $explorationStats');
  }

  /// Check current track list for duplicates and log details
  Map<String, int> checkCurrentTrackDuplicates() {
    print('\n🔍 === CURRENT TRACK DUPLICATE CHECK ===');

    final seenIds = <String>{};
    final seenTitleArtist = <String>{};
    final idDuplicates = <String>[];
    final titleArtistDuplicates = <String>[];

    int idDuplicateCount = 0;
    int titleArtistDuplicateCount = 0;

    for (int i = 0; i < _currentRecommendations.length; i++) {
      final track = _currentRecommendations[i];

      // Check for ID duplicates
      if (seenIds.contains(track.id)) {
        idDuplicateCount++;
        idDuplicates.add(
            'Position $i: "${track.title}" by ${track.artist} (ID: ${track.id})');
      } else {
        seenIds.add(track.id);
      }

      // Check for title-artist duplicates
      final cleanTitle = _cleanTrackTitle(track.title);
      final cleanArtist = track.artist.toLowerCase().trim();
      final titleArtistKey = '$cleanTitle-$cleanArtist';

      if (seenTitleArtist.contains(titleArtistKey)) {
        titleArtistDuplicateCount++;
        titleArtistDuplicates.add(
            'Position $i: "${track.title}" by ${track.artist} (Key: $titleArtistKey)');
      } else {
        seenTitleArtist.add(titleArtistKey);
      }
    }

    print('📊 DUPLICATE ANALYSIS RESULTS:');
    print('   🎵 Total tracks: ${_currentRecommendations.length}');
    print('   🆔 ID duplicates: $idDuplicateCount');
    print('   🎭 Title-artist duplicates: $titleArtistDuplicateCount');

    if (idDuplicates.isNotEmpty) {
      print('🚨 ID DUPLICATES FOUND:');
      for (final duplicate in idDuplicates) {
        print('   - $duplicate');
      }
    }

    if (titleArtistDuplicates.isNotEmpty) {
      print('🚨 TITLE-ARTIST DUPLICATES FOUND:');
      for (final duplicate in titleArtistDuplicates) {
        print('   - $duplicate');
      }
    }

    if (idDuplicates.isEmpty && titleArtistDuplicates.isEmpty) {
      print('✅ No duplicates found in current track list');
    }

    print('🔍 === END DUPLICATE CHECK ===\n');

    return {
      'total_tracks': _currentRecommendations.length,
      'id_duplicates': idDuplicateCount,
      'title_artist_duplicates': titleArtistDuplicateCount,
    };
  }

  /// Diagnose and fix artist cache issues
  Future<void> diagnoseAndFixArtistCache(BuildContext context) async {
    print('\n🔧 === ARTIST CACHE DIAGNOSIS & FIX ===');

    // Check current cache state
    print('🗄️ Current artist cache size: ${_artistTrackCache.length}');
    print('🔄 Recent track IDs: ${_recentTrackIds.length}');

    // Get user's top artists and try to cache them
    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      List<String> testArtists = [];

      if (spotifyProvider.isConnected) {
        final topTracks = await _spotifyService.getTopTracks(limit: 10);
        testArtists =
            topTracks.map((track) => track.artist).toSet().take(5).toList();
        print(
            '🎵 Found ${testArtists.length} artists from Spotify top tracks: $testArtists');
      }

      // Test caching for each artist
      for (final artist in testArtists) {
        print('🧪 Testing cache for artist: "$artist"');
        final tracks = await _getCachedArtistTracks(artist, limit: 10);
        print('   ✅ Got ${tracks.length} tracks for "$artist"');
      }

      print('🗄️ Artist cache size after test: ${_artistTrackCache.length}');
    } catch (e) {
      print('❌ Error during artist cache diagnosis: $e');
    }

    print('🔧 === END ARTIST CACHE DIAGNOSIS ===\n');
  }

  /// Populate user's music preferences if missing
  Future<void> populateUserMusicPreferences(BuildContext context) async {
    print('\n🎵 === POPULATE USER MUSIC PREFERENCES ===');

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final currentUser = userProvider.currentUser;

      if (currentUser == null) {
        print('❌ No user logged in, cannot populate preferences');
        return;
      }

      final hasTopArtists =
          currentUser.topArtists != null && currentUser.topArtists!.isNotEmpty;
      final hasTopGenres =
          currentUser.topGenres != null && currentUser.topGenres!.isNotEmpty;

      print('🎵 Current user preferences:');
      print(
          '   🎤 Top artists: ${hasTopArtists ? currentUser.topArtists!.length : 0}');
      print(
          '   🎼 Top genres: ${hasTopGenres ? currentUser.topGenres!.length : 0}');

      if (hasTopArtists && hasTopGenres) {
        print('✅ User already has music preferences, no action needed');
        return;
      }

      // Use existing user profile data
      if (currentUser.topArtists != null) {
        _userTopArtists = List<String>.from(currentUser.topArtists!);
        print('✅ Loaded ${_userTopArtists.length} artists from user profile');
      }

      if (currentUser.topGenres != null) {
        _userTopGenres = List<String>.from(currentUser.topGenres!);
        print('✅ Loaded ${_userTopGenres.length} genres from user profile');
      }

      if (currentUser.favoriteGenres.isNotEmpty) {
        _userTopGenres.addAll(currentUser.favoriteGenres);
        _userTopGenres = _userTopGenres.toSet().toList(); // Remove duplicates
        print('✅ Added ${currentUser.favoriteGenres.length} favorite genres from user profile');
      }
    } catch (e) {
      print('❌ Error populating user music preferences: $e');
    }

    print('🎵 === END POPULATE USER PREFERENCES ===\n');
  }

  // Mood definitions with Spotify audio features</text>
  final List<Map<String, dynamic>> _moods = [
    {
      'name': 'Happy',
      'emoji': '😊',
      'color': const Color(0xFFFFB74D),
      'description': 'Upbeat and joyful',
      'aiMessage': 'I\'ve found some upbeat tracks to brighten your day! 😊',
    },
    {
      'name': 'Energetic',
      'emoji': '🔥',
      'color': const Color(0xFFE57373),
      'description': 'High energy and powerful',
      'aiMessage':
          'Ready to pump up the energy? These tracks will get you moving! 🔥',
    },
    {
      'name': 'Calm',
      'emoji': '🌊',
      'color': const Color(0xFF64B5F6),
      'description': 'Peaceful and relaxing',
      'aiMessage':
          'Time to unwind... these peaceful tracks will help you relax 🌊',
    },
    {
      'name': 'Focused',
      'emoji': '🎯',
      'color': const Color(0xFF81C784),
      'description': 'For deep work and concentration',
      'aiMessage':
          'Perfect focus music coming up! These tracks enhance concentration 🎯',
    },
    {
      'name': 'Dreamy',
      'emoji': '💭',
      'color': const Color(0xFFBA68C8),
      'description': 'Ethereal and atmospheric',
      'aiMessage': 'Drift away with these dreamy, atmospheric sounds 💭',
    },
    {
      'name': 'Chill',
      'emoji': '🌿',
      'color': const Color(0xFF4DB6AC),
      'description': 'Laid-back vibes',
      'aiMessage': 'Chill vibes activated! Perfect tracks for hanging out 🌿',
    },
  ];

  List<String> getCategories(BuildContext context) {
    final spotifyProvider =
        Provider.of<SpotifyProvider>(context, listen: false);

    // Base categories available for all services
    final categories = [
      'all',
      'genreBased',
      'artistBased',
      'moodBased',
    ];

    // Add service-specific categories
    if (spotifyProvider.isConnected) {
      // Spotify has all categories including topTracks
      categories.addAll([
        'topTracks',
        'likedSongs',
        'recentlyPlayed',
        'recentlyAdded',
        'discover',
      ]);
    } else {
      categories.addAll([
        'discover',
      ]);
    }

    return categories;
  }

  final Map<String, String> _categoryLabels = {
    'all': 'All',
    'genreBased': 'Your Genres',
    'artistBased': 'Similar Artists',
    'moodBased': 'For Your Mood',
    'topTracks': 'Your Top Tracks',
    'likedSongs': 'Liked Songs',
    'recentlyPlayed': 'Recently Played',
    'recentlyAdded': 'Recently Added',
    'discover': 'Discover',
  };

  AISearchProvider(BuildContext context, {String initialCategory = 'all'}) {
    initialize(context, initialCategory);
  }

  void initialize(BuildContext context, [String initialCategory = 'all']) {
    _currentCategory = initialCategory;
    _loadExclusionData(context);
    _loadUserArtists(context);
    _loadUserGenres(context);
    _setInitialMoodMessage();
    _aiRecommendationService.setCurrentContext(
        category: initialCategory, genre: null);

    // For suggested songs (artistBased), only load artist-based content
    if (initialCategory == 'artistBased') {
      _loadArtistBasedRecommendationsOnly(context);
    } else {
      _loadInitialRecommendations(context);
    }
  }

  void _setInitialMoodMessage() {
    _currentAiMessage = _moods[_selectedMoodIndex]['aiMessage'];
    _showAiMessage = true;
    notifyListeners();
  }

  /// Load data from pins tab to exclude from recommendations
  Future<void> _loadExclusionData(BuildContext context) async {
    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      if (spotifyProvider.isConnected) {
        // Load liked songs to exclude
        if (spotifyProvider.likedSongs.isNotEmpty) {
          _likedSongIds =
              spotifyProvider.likedSongs.map((track) => track.id).toSet();
        }

        // Load top tracks to exclude (but keep some for similarity)
        final topTracks = await _spotifyService.getTopTracks(limit: 50);
        _topTrackIds = topTracks.map((track) => track.id).toSet();

        // Load recently played to exclude
        final recentTracks = await _spotifyService.getRecentlyPlayed(limit: 50);
        _recentTrackIds = recentTracks.map((track) => track.id).toList();

        // Combine all exclusion sets
        _excludedTrackIds = {
          ..._likedSongIds,
          ..._topTrackIds
              .take(30), // Only exclude top 30 to allow some similarity
          ..._recentTrackIds
              .take(20), // Only exclude top 20 recent to allow some similarity
        };

        // Pass exclusion data to AI service
        _aiRecommendationService.setExclusionData(_excludedTrackIds);

        print(
            '🚫 Loaded ${_excludedTrackIds.length} tracks to exclude from recommendations');
      }
    } catch (e) {
      print('❌ Error loading exclusion data: $e');
    }
  }

  /// Load user's top artists from user profile
  /// PRIORITIZES USER PROFILE DATA exclusively
  Future<void> _loadUserArtists(BuildContext context) async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // If the user profile isn't loaded yet, fetch it.
      if (userProvider.currentUser == null) {
        print('👤 User profile not found, fetching now...');
        await userProvider.fetchFullProfile();
      }

      // Get artists from user profile
      final profileArtists = userProvider.currentUser?.topArtists ?? [];
      print('🎵 Loaded ${profileArtists.length} artists from user profile');

      // Use profile artists as the source
      _userTopArtists = List<String>.from(profileArtists);

      print('🎵 FINAL: ${_userTopArtists.length} total artists from user profile');

      // Pass the loaded artists to the AI recommendation service
      _aiRecommendationService.setUserTopArtists(_userTopArtists);
    } catch (e) {
      print('❌ Error loading user artists: $e');
      // Ultimate fallback - use empty list to avoid crashes
      _userTopArtists = [];
    }
  }

  /// Load user's top genres from user profile exclusively
  Future<void> _loadUserGenres(BuildContext context) async {
    _isLoadingGenres = true;
    notifyListeners();

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // Get genres from user profile
      final userProfileGenres = userProvider.currentUser?.topGenres ?? [];
      final userFavoriteGenres = userProvider.currentUser?.favoriteGenres ?? [];

      // Combine user profile genres
      final profileGenres = <String>{};
      profileGenres.addAll(userProfileGenres);
      profileGenres.addAll(userFavoriteGenres);

      print('🎵 Loaded ${profileGenres.length} genres from user profile');
      print('🎵 Profile genres: $profileGenres');

      // Use profile genres as the source
      _userTopGenres = profileGenres.toList();

      // Create empty buckets for each genre so we can fill it lazily when
      // the user eventually selects it from the UI.
      for (final g in _userTopGenres) {
        _genreRecommendations.putIfAbsent(g, () => []);
      }

      // Simple alphabetical sort
      _userTopGenres.sort();

      print('🎵 FINAL: ${_userTopGenres.length} total genres from user profile');
    } catch (e) {
      print('❌ Error loading user genres: $e');
      // Ultimate fallback - use empty list to avoid crashes
      _userTopGenres = [];
    } finally {
      _isLoadingGenres = false;
      notifyListeners();
    }
  }

  /// Get user's playlists from Spotify
  Future<List<Map<String, dynamic>>> _getUserPlaylists(
      BuildContext context) async {
    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);
      if (!spotifyProvider.isConnected) {
        print('❌ Spotify not connected for playlists');
        return [];
      }

      print('📚 Getting real user playlists from Spotify...');

      // Get actual user playlists from Spotify
      final playlists = await _spotifyService.getUserPlaylists(limit: 50);

      print('🔍 [DEBUG] Raw playlists response: ${playlists.length} items');
      if (playlists.isNotEmpty) {
        print(
            '🔍 [DEBUG] First playlist keys: ${playlists.first.keys.toList()}');
        print('🔍 [DEBUG] First playlist: ${playlists.first}');
      }

      if (playlists.isEmpty) {
        print('❌ No playlists returned from Spotify API');

        // Fallback: Add some default collections
        return [
          {
            'id': 'liked_songs_collection',
            'name': 'Liked Songs',
            'description': 'Your liked songs collection',
            'trackCount': spotifyProvider.likedSongs.length,
            'imageUrl': '',
            'owner': 'You',
            'public': false,
          },
          {
            'id': 'recently_played_collection',
            'name': 'Recently Played',
            'description': 'Your recently played tracks',
            'trackCount': 50,
            'imageUrl': '',
            'owner': 'You',
            'public': false,
          },
        ];
      }

      // Transform playlist data to our format
      final formattedPlaylists = playlists
          .map((playlist) {
            try {
              final images = playlist['images'] as List? ?? [];
              final owner = playlist['owner'] as Map<String, dynamic>? ?? {};
              final tracks = playlist['tracks'] as Map<String, dynamic>? ?? {};

              final formatted = {
                'id': playlist['id'] ?? '',
                'name': playlist['name'] ?? 'Untitled Playlist',
                'description': playlist['description'] ?? '',
                'trackCount': tracks['total'] ?? 0,
                'imageUrl': images.isNotEmpty ? images[0]['url'] ?? '' : '',
                'owner': owner['display_name'] ?? 'Unknown',
                'public': playlist['public'] ?? false,
              };

              print(
                  '🔍 [DEBUG] Formatted playlist: ${formatted['name']} (${formatted['trackCount']} tracks)');
              return formatted;
            } catch (e) {
              print('❌ Error formatting playlist: $e');
              print('❌ Problematic playlist data: $playlist');
              return null;
            }
          })
          .whereType<Map<String, dynamic>>()
          .toList(); // Filter out null entries

      print(
          '✅ Got ${formattedPlaylists.length} formatted playlists from Spotify');

      // Add some logging for the UI
      for (final playlist in formattedPlaylists.take(3)) {
        print(
            '📋 Playlist: "${playlist['name']}" - ${playlist['trackCount']} tracks - Owner: ${playlist['owner']}');
      }

      return formattedPlaylists;
    } catch (e) {
      print('❌ Error getting user playlists: $e');
      print('❌ Stack trace: ${StackTrace.current}');

      // Return fallback collections on error
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);
      return [
        {
          'id': 'liked_songs_collection',
          'name': 'Liked Songs',
          'description': 'Your liked songs collection',
          'trackCount': spotifyProvider.likedSongs.length,
          'imageUrl': '',
          'owner': 'You',
          'public': false,
        },
      ];
    }
  }

  /// Get related genres based on user's top genres
  Future<List<String>> _getUserRelatedGenres(List<String> userTopGenres) async {
    final relatedGenres = <String>{};

    // Genre relationship mapping
    final genreRelationships = {
      // Hip-Hop family
      'hip hop': [
        'rap',
        'trap',
        'uk drill',
        'chicago drill',
        'conscious hip hop',
        'underground hip hop',
        'boom bap'
      ],
      'rap': [
        'hip hop',
        'trap',
        'uk drill',
        'chicago drill',
        'gangsta rap',
        'mumble rap',
        'cloud rap'
      ],
      'trap': ['hip hop', 'rap', 'uk drill', 'chicago drill', 'southern hip hop', 'mumble rap'],
      'uk drill': ['hip hop', 'rap', 'grime', 'uk hip hop'],
      'chicago drill': ['hip hop', 'rap', 'trap', 'gangsta rap'],

      // Pop family
      'pop': ['electropop', 'dance pop', 'indie pop', 'pop rock', 'synth pop'],
      'indie pop': ['pop', 'indie rock', 'dream pop', 'bedroom pop', 'art pop'],
      'electropop': ['pop', 'electronic', 'synth pop', 'dance pop', 'edm'],

      // Electronic family
      'electronic': [
        'edm',
        'house',
        'techno',
        'ambient',
        'synthwave',
        'electropop'
      ],
      'house': [
        'electronic',
        'deep house',
        'tech house',
        'progressive house',
        'edm'
      ],
      'techno': [
        'electronic',
        'house',
        'minimal techno',
        'tech house',
        'industrial'
      ],
      'dubstep': [
        'electronic',
        'bass music',
        'future bass',
        'melodic dubstep',
        'riddim'
      ],
      'ambient': ['electronic', 'chillout', 'downtempo', 'new age', 'drone'],

      // Rock family
      'rock': [
        'indie rock',
        'alternative',
        'classic rock',
        'hard rock',
        'pop rock'
      ],
      'indie rock': [
        'rock',
        'alternative',
        'indie pop',
        'post-rock',
        'garage rock'
      ],
      'alternative': [
        'rock',
        'indie rock',
        'grunge',
        'post-punk',
        'alternative rock'
      ],
      'metal': [
        'heavy metal',
        'hard rock',
        'metalcore',
        'death metal',
        'thrash metal'
      ],
      'punk': ['pop punk', 'hardcore punk', 'post-punk', 'punk rock', 'ska'],

      // R&B family
      'r&b': [
        'soul',
        'neo soul',
        'contemporary r&b',
        'alternative r&b',
        'funk'
      ],
      'soul': ['r&b', 'neo soul', 'funk', 'gospel', 'motown'],
      'funk': ['soul', 'r&b', 'disco', 'jazz fusion', 'afrobeat'],

      // Jazz family
      'jazz': [
        'smooth jazz',
        'jazz fusion',
        'bebop',
        'contemporary jazz',
        'swing'
      ],
      'smooth jazz': ['jazz', 'contemporary jazz', 'jazz fusion', 'soft rock'],
      'jazz fusion': ['jazz', 'fusion', 'funk', 'progressive rock'],

      // Folk family
      'folk': [
        'indie folk',
        'folk rock',
        'singer-songwriter',
        'acoustic',
        'americana'
      ],
      'country': [
        'americana',
        'folk',
        'bluegrass',
        'alt-country',
        'country pop'
      ],
      'singer-songwriter': ['folk', 'indie folk', 'acoustic', 'alternative'],

      // World music
      'reggae': ['dub', 'ska', 'dancehall', 'reggaeton', 'afrobeat'],
      'latin': ['reggaeton', 'salsa', 'bachata', 'latin pop', 'bossa nova'],
      'afrobeat': ['afro-pop', 'reggae', 'funk', 'world music', 'dancehall'],

      // Modern/Internet genres
      'lo-fi': [
        'lo-fi hip hop',
        'chillhop',
        'study beats',
        'ambient',
        'downtempo'
      ],
      'bedroom pop': [
        'indie pop',
        'dream pop',
        'chillwave',
        'lo-fi',
        'shoegaze'
      ],
      'synthwave': [
        'electronic',
        'vaporwave',
        'synthpop',
        'new wave',
        'retrowave'
      ],
      'hyperpop': [
        'pop',
        'electronic',
        'experimental',
        'pc music',
        'digital hardcore'
      ],

      // Chill/Study music
      'chillout': ['ambient', 'downtempo', 'lo-fi', 'trip hop', 'chillhop'],
      'study beats': [
        'lo-fi',
        'lo-fi hip hop',
        'chillhop',
        'ambient',
        'instrumental'
      ],

      // Dance music
      'edm': ['electronic', 'house', 'trance', 'dubstep', 'progressive house'],
      'dance': ['house', 'electronic', 'edm', 'disco', 'dance pop'],
      'trance': [
        'electronic',
        'edm',
        'progressive trance',
        'uplifting trance',
        'psytrance'
      ],
    };

    // Add related genres for each user genre
    for (final userGenre in userTopGenres.take(20)) {
      // Limit to avoid too many relationships
      final userGenreLower = userGenre.toLowerCase();

      // Direct relationships
      if (genreRelationships.containsKey(userGenreLower)) {
        relatedGenres.addAll(genreRelationships[userGenreLower]!);
      }

      // Find genres that list this user genre as related
      genreRelationships.forEach((genre, related) {
        if (related.contains(userGenreLower)) {
          relatedGenres.add(genre);
        }
      });
    }

    // Remove user's existing genres from related list
    relatedGenres.removeWhere((genre) => userTopGenres
        .any((userGenre) => userGenre.toLowerCase() == genre.toLowerCase()));

    return relatedGenres.toList();
  }

  /// Reset anti-repetition tracking periodically
  void _resetAntiRepetitionTracking() {
    final now = DateTime.now();
    if (_lastResetTime == null ||
        now.difference(_lastResetTime!).inMinutes > 30) {
      print('🔄 [Anti-Repeat] Resetting tracking after 30 minutes');
      _usedQueries.clear();
      _recentArtists.clear();
      _usedArtistQueryCombos.clear();
      _queryVariationSeed = math.Random().nextInt(1000);
      _lastResetTime = now;
    }
  }

  /// Reset mood-specific anti-repetition tracking periodically
  void _resetMoodAntiRepetitionTracking() {
    final now = DateTime.now();
    if (_lastMoodResetTime == null ||
        now.difference(_lastMoodResetTime!).inMinutes > 20) {
      print('🔄 [Mood Anti-Repeat] Resetting mood tracking after 20 minutes');
      _usedMoodQueries.clear();
      _usedMoodPlaylists.clear();
      _moodQueryHistory.clear();
      _lastMoodResetTime = now;
    }
  }

  /// Check if a mood query should be avoided due to recent use
  bool _shouldAvoidMoodQuery(String query, String moodName) {
    final normalizedQuery = query.toLowerCase().trim();

    // Check global mood query usage
    if (_usedMoodQueries.contains(normalizedQuery)) {
      return true;
    }

    // Check mood-specific query history
    final moodHistory = _moodQueryHistory[moodName] ?? <String>{};
    return moodHistory.contains(normalizedQuery);
  }

  /// Mark a mood query as used
  void _markMoodQueryAsUsed(String query, String moodName) {
    final normalizedQuery = query.toLowerCase().trim();
    _usedMoodQueries.add(normalizedQuery);

    // Track per-mood usage
    _moodQueryHistory.putIfAbsent(moodName, () => <String>{});
    _moodQueryHistory[moodName]!.add(normalizedQuery);

    // Keep only last 50 queries per mood to prevent memory bloat
    if (_moodQueryHistory[moodName]!.length > 50) {
      final queriesList = _moodQueryHistory[moodName]!.toList();
      _moodQueryHistory[moodName]!.clear();
      _moodQueryHistory[moodName]!.addAll(queriesList.skip(10)); // Keep last 40
    }
  }

  /// Check if a query should be avoided due to recent use
  bool _shouldAvoidQuery(String query) {
    final normalizedQuery = query.toLowerCase().trim();
    return _usedQueries.contains(normalizedQuery);
  }

  /// Mark a query as used
  void _markQueryAsUsed(String query) {
    final normalizedQuery = query.toLowerCase().trim();
    _usedQueries.add(normalizedQuery);

    // Keep only last 100 queries to prevent memory bloat
    if (_usedQueries.length > 100) {
      final queriesList = _usedQueries.toList();
      _usedQueries.clear();
      _usedQueries.addAll(queriesList.skip(20)); // Keep last 80
    }
  }

  /// Generate proper search context based on current category and genre
  String _getSearchContext() {
    final baseContext = switch (_currentCategory) {
      'moodBased' => 'AI_MOOD_BASED',
      'genreBased' => 'AI_GENRE_BASED',
      'artistBased' => 'AI_ARTIST_BASED',
      'diverse' => 'AI_DIVERSE_DISCOVERY',
      _ => 'AI_GENERAL',
    };

    String finalContext = baseContext;

    if (_selectedGenre != null && _selectedGenre!.isNotEmpty) {
      finalContext = '${baseContext}_${_selectedGenre!.toUpperCase()}';
    } else if (_currentCategory == 'moodBased' &&
        _selectedMoodIndex >= 0 &&
        _selectedMoodIndex < _moods.length) {
      final moodName = _moods[_selectedMoodIndex]['name'] as String;
      finalContext = '${baseContext}_${moodName.toUpperCase()}';
    }

    print(
        '🔧 [Search Context] Category: $_currentCategory, Genre: $_selectedGenre, Context: $finalContext');
    return finalContext;
  }

  /// Generate varied query for an artist with anti-repetition
  List<String> _generateVariedArtistQueries(
      String artist, String context, int offset) {
    final baseQueries = [
      // Prioritize similar artist discovery queries
      'sounds like $artist',
      '$artist similar artists',
      '$artist radio',
      '$artist style music',
      '$artist inspired music',
      'fans of $artist',
      'like $artist music',
      '$artist genre artists',
      '$artist fans also like',
      'artists similar to $artist',
      '$artist related artists',
      '$artist influenced by',
      '$artist musical style',
      // Some direct queries but fewer than before
      '$artist collaborations',
      '$artist remix',
      '$artist discoveries',
      '$artist alternate versions',
    ];

    // Add context-specific queries
    if (context == 'underground' || context == 'hip hop') {
      baseQueries.addAll([
        '$artist underground',
        '$artist indie',
        '$artist experimental',
        '$artist alternative',
        '$artist unsigned',
        '$artist emerging',
      ]);
    }

    // Use randomization with variation seed to prevent patterns
    final random = math.Random(_queryVariationSeed + offset + artist.hashCode);
    final shuffledQueries = List<String>.from(baseQueries)..shuffle(random);

    // Filter out recently used queries
    final freshQueries =
        shuffledQueries.where((q) => !_shouldAvoidQuery(q)).toList();

    // If all queries were used, reset and use all
    if (freshQueries.isEmpty) {
      print('🔄 [Anti-Repeat] All $artist queries used, resetting');
      return shuffledQueries.take(6).toList();
    }

    return freshQueries.take(6).toList();
  }

  /// 🚀 OPTIMIZED: Load more recommendations with progressive loading for speed  
  Future<void> loadMoreRecommendations(BuildContext context) async {
    if (_isLoadingMore) return; // Prevent concurrent loading

    _currentContext = context; // Store context for background operations

    final stopwatch = Stopwatch()..start();
    print(
        '⏱️ [Performance] Starting optimized loadMoreRecommendations (page: $_currentPage)');

    // 🚀 Show quick results first for initial page
    if (_currentPage == 0 && _currentRecommendations.isEmpty) {
      print('🚀 [Progressive] Loading quick results first...');
      await _loadQuickResults(context);
      
      // Start background loading of personalized content
      _loadBackgroundPersonalizedContent(context);
      
      stopwatch.stop();
      print('⏱️ [Performance] Quick results loaded in ${stopwatch.elapsedMilliseconds}ms');
      return;
    }

    // Quick artist cache test - only run once when cache is empty
    if (_artistTrackCache.isEmpty && _currentPage < 2) {
      print(
          '🧪 [Quick Test] Artist cache is empty, testing with popular artist...');
      try {
        final testTracks =
            await _getCachedArtistTracks('Taylor Swift', limit: 5);
        print(
            '🧪 [Quick Test] Got ${testTracks.length} tracks for Taylor Swift');
        print(
            '🧪 [Quick Test] Artist cache size after test: ${_artistTrackCache.length}');
      } catch (e) {
        print('🧪 [Quick Test] Artist cache test failed: $e');
      }
    }

    // Background artist expansion during exploration
    await _performBackgroundExpansion();

    _isLoadingMore = true;
    notifyListeners();

    // 🚀 ENHANCEMENT: Clear caches more aggressively to prevent repetition
    if (_currentPage > 0 && _currentPage % 3 == 0) {
      print(
          '🧹 [Pagination] Clearing caches on page $_currentPage for fresh content');
      _spotifyService.clearSearchCache();

      // Clear Last.fm cache periodically for variety
      final lastFmService = LastFmService();
      lastFmService.clearCache();

      // Clear MusicBrainz validation caches
      _artistGenreValidationCache.clear();
      _validationCacheTimestamps.clear();

      // Clear artist cache periodically for variety but keep some
      if (_currentPage % 6 == 0) {
        _clearArtistCache();
      } else {
        // Clean up expired entries regularly
        _cleanupArtistCache();
      }

      print('🧹 [Pagination] Cleared all caches for fresh variety');

      // Reset exploration state periodically for fresh discovery
      if (_currentPage % 10 == 0) {
        resetExplorationState();
      }
    }

    // Reset tracking periodically to prevent exhaustion
    _resetAntiRepetitionTracking();

    // Reset mood tracking for endless variety in mood-based searches
    if (_currentCategory == 'moodBased') {
      _resetMoodAntiRepetitionTracking();
    }

    try {
      _currentPage++;

      // Calculate scroll percentage to determine load intensity
      final batchSize = _pageSize;

      print(
          '📄 Loading page $_currentPage for category: $_currentCategory (batch: $batchSize)');

      List<MusicTrack> filteredNewTracks = [];
      final loadMoreTimer = Stopwatch()..start();
      
      // Optimize: Try concurrent requests to different pages for better variety
      final recentTrackIdSet = _recentTrackIds.toSet(); // Convert to set for O(1) lookup
      final targetTrackCount = batchSize ~/ 2;
      
      print('🚀 [LoadMore Parallel] Starting concurrent track fetching (target: $targetTrackCount)');
      
      // Create concurrent futures for different page offsets
      final concurrentFutures = <Future<List<MusicTrack>>>[];
      final currentPageBackup = _currentPage;
      
      for (int i = 0; i < 3; i++) {
        _currentPage = currentPageBackup + i;
        concurrentFutures.add(_getMoreRecommendationsForCategory(_currentCategory, context));
      }
      
      try {
        final concurrentResults = await Future.wait(concurrentFutures);
        _currentPage = currentPageBackup; // Restore original page
        
        // Process all concurrent results
        for (int i = 0; i < concurrentResults.length; i++) {
          final newTracks = concurrentResults[i];
          print('📥 [Concurrent $i] Got ${newTracks.length} new tracks from API');
          
          final uniqueNewTracks = newTracks.where((track) {
            if (recentTrackIdSet.contains(track.id)) return false;
            
            // For emergency situations, be more permissive
            if (filteredNewTracks.length < 3 && _currentRecommendations.length < 5) {
              return true;
            }
            
            return true;
          }).toList();
          
          filteredNewTracks.addAll(uniqueNewTracks);
          print('✅ [Concurrent $i] Added ${uniqueNewTracks.length} unique tracks (total: ${filteredNewTracks.length})');
          
          // Break early if we have enough tracks
          if (filteredNewTracks.length >= targetTrackCount) {
            break;
          }
        }
        
        loadMoreTimer.stop();
        print('🚀 [LoadMore Parallel] Concurrent fetching completed in ${loadMoreTimer.elapsedMilliseconds}ms');
        
      } catch (e) {
        print('❌ [LoadMore Parallel] Concurrent fetching failed: $e, falling back to sequential');
        _currentPage = currentPageBackup;
        
        // Fallback to original sequential approach
        final newTracks = await _getMoreRecommendationsForCategory(_currentCategory, context);
        filteredNewTracks = newTracks.where((track) => !recentTrackIdSet.contains(track.id)).toList();
      }

      // Remove duplicates within the new batch
      final seenIds = <String>{};
      filteredNewTracks = filteredNewTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      // 🚀 ENHANCEMENT: More aggressive clearing for better variety
      if (filteredNewTracks.isEmpty) {
        print('🧹 Clearing old track IDs to allow more variety...');
        final trackCount = _recentTrackIds.length;
        if (trackCount > 100) {
          // Keep only the most recent 50 track IDs
          final recentTrackIds = _currentRecommendations.length > 50
              ? _currentRecommendations
                  .skip(_currentRecommendations.length - 50)
                  .map((track) => track.id)
                  .toList()
              : _currentRecommendations.map((track) => track.id).toList();
          _recentTrackIds = recentTrackIds;

          print(
              '🧹 Cleared old IDs. Before: $trackCount, After: ${_recentTrackIds.length}');

          // Clear additional caches for truly fresh content
          _spotifyService.clearSearchCache();
          _artistGenreValidationCache.clear();
          _validationCacheTimestamps.clear();
          _clearArtistCache();

          // Clear Last.fm cache for fresh artist discovery
          final lastFmService = LastFmService();
          lastFmService.clearCache();
          print('🧹 Cleared all caches for emergency fresh content');

          // Try one more time with reduced tracking
          final newTracks = await _getMoreRecommendationsForCategory(
              _currentCategory, context);
          final recentTrackIdSet = _recentTrackIds.toSet();
          filteredNewTracks = newTracks
              .where((track) => !recentTrackIdSet.contains(track.id))
              .toList();
          print(
              '🔄 After clearing old IDs: ${filteredNewTracks.length} tracks available');
        } else {
          // Emergency clear for completely fresh content
          print(
              '🆘 Emergency clear: Clearing recent track IDs for fresh content');
          _recentTrackIds.clear();

          // Clear all caches for emergency refresh
          _spotifyService.clearSearchCache();
          _artistGenreValidationCache.clear();
          _validationCacheTimestamps.clear();
          _clearArtistCache();

          final lastFmService = LastFmService();
          lastFmService.clearCache();

          final newTracks = await _getMoreRecommendationsForCategory(
              _currentCategory, context);
          filteredNewTracks = newTracks;
          print(
              '🔄 After emergency clear: ${filteredNewTracks.length} tracks available');
        }
      }

      if (filteredNewTracks.isNotEmpty) {
        // Take only what we need for this batch
        final tracksToAdd = filteredNewTracks.take(batchSize).toList();

        // Add new track IDs to rolling tracking list
        _addToRecentTrackIds(tracksToAdd.map((track) => track.id));

        // Also update AI service tracking
        _aiRecommendationService.addLoadedTracks(tracksToAdd);

        // Apply intelligent shuffling to new tracks considering existing tracks
        _debugTrackDistribution(tracksToAdd,
            label: "NEW tracks BEFORE shuffle");
        final shuffledNewTracks = _intelligentShuffleNewTracks(tracksToAdd);
        _debugTrackDistribution(shuffledNewTracks,
            label: "NEW tracks AFTER shuffle");
        _currentRecommendations.addAll(shuffledNewTracks);

        _categorizedRecommendations[_currentCategory] = _currentRecommendations;
        notifyListeners();

        print(
            '✅ Added ${tracksToAdd.length} tracks to UI. Total: ${_currentRecommendations.length}');
      } else {
        print('❌ No new tracks could be loaded after all attempts');
      }
    } catch (e) {
      print('❌ Error loading more recommendations: $e');
    } finally {
      stopwatch.stop();
      print(
          '⏱️ [Performance] loadMoreRecommendations completed in ${stopwatch.elapsedMilliseconds}ms');
      print(
          '⏱️ [Performance] Total tracks loaded: ${_currentRecommendations.length}');
      print(
          '⏱️ [Performance] Recent track IDs tracking: ${_recentTrackIds.length}');
      print('⏱️ [Performance] Artist cache size: ${_artistTrackCache.length}');

      _isLoadingMore = false;
      notifyListeners();
    }
  }

  /// 🚀 OPTIMIZED: Load quick results for immediate display - ENHANCED FOR LARGE ARTIST LISTS
  Future<void> _loadQuickResults(BuildContext context) async {
    _isLoadingMore = true;
    notifyListeners();

    try {
      List<MusicTrack> quickTracks = [];

      // OPTIMIZATION: For large artist lists, prioritize cached content
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      if (spotifyProvider.isConnected) {
        await spotifyProvider.loadLikedSongs();
        final likedSongs = spotifyProvider.likedSongs;

        // If user has many liked songs (indicating large music library), use cache-first approach
        if (likedSongs.length > 100) {
          print('🚀 [Quick Results] Large music library detected (${likedSongs.length} liked songs), using cache-first approach');
          quickTracks = await _getQuickCachedResults(context);

          if (quickTracks.length >= 15) {
            print('✅ [Quick Results] Got ${quickTracks.length} cached tracks, showing immediately');
            _currentRecommendations = quickTracks;
            _isLoadingMore = false;
            notifyListeners();
            return;
          }
        }
      }

      // Standard quick loading based on category
      switch (_currentCategory) {
        case 'moodBased':
          quickTracks = await _getQuickMoodDiscovery(40); // OPTIMIZED: Increased from 20 to 40
          break;
        case 'artistBased':
          quickTracks = await _getQuickSimilarArtists(40, context); // OPTIMIZED: Increased from 20 to 40
          break;
        case 'discover':
          quickTracks = await _getQuickPlaylistDiscovery('discover', 'new music discover', 40); // OPTIMIZED: Increased from 20 to 40
          break;
        case 'topTracks':
        case 'likedSongs':
        case 'recentlyPlayed':
        case 'recentlyAdded':
          quickTracks = await _getQuickUserTracks(_currentCategory, 40); // OPTIMIZED: Increased from 20 to 40
          break;
        default:
          // For 'all' and other categories, mix of different quick sources
          final futures = [
            _getQuickMoodDiscovery(20), // OPTIMIZED: Increased from 10 to 20
            _getQuickSimilarArtists(20, context), // OPTIMIZED: Increased from 10 to 20
          ];
          final results = await Future.wait(futures);
          quickTracks = results.expand((t) => t).toList();
          break;
      }

      if (quickTracks.isNotEmpty) {
        // Filter out duplicates
        final seenIds = _currentRecommendations.map((t) => t.id).toSet();
        final uniqueTracks = quickTracks.where((t) => !seenIds.contains(t.id)).toList();
        
        _currentRecommendations.addAll(uniqueTracks);
        _updateTrackHistory(uniqueTracks);
        
        print('🚀 [Progressive] Added ${uniqueTracks.length} quick tracks');
        notifyListeners();
      }
    } catch (e) {
      print('❌ [Progressive] Error loading quick results: $e');
    } finally {
      _isLoadingMore = false;
    }
  }

  /// 🚀 NEW: Load personalized content in background
  Future<void> _loadBackgroundPersonalizedContent(BuildContext context) async {
    try {
      print('🔄 [Background] Loading personalized content...');
      
      final personalizedTracks = await _getMoreRecommendationsForCategory(_currentCategory, context);
      
      if (personalizedTracks.isNotEmpty) {
        // Filter out tracks we already have
        final seenIds = _currentRecommendations.map((t) => t.id).toSet();
        final newTracks = personalizedTracks.where((t) => !seenIds.contains(t.id)).toList();
        
        if (newTracks.isNotEmpty) {
          _currentRecommendations.addAll(newTracks);
          _updateTrackHistory(newTracks);
          
          print('🔄 [Background] Added ${newTracks.length} personalized tracks');
          notifyListeners();
        }
      }
    } catch (e) {
      print('❌ [Background] Error loading personalized content: $e');
    }
  }

  /// 🚀 NEW: Get quick discovery tracks from playlists for immediate results
  Future<List<MusicTrack>> _getQuickPlaylistDiscovery(String category, String query, int limit) async {
    try {
      print('🎵 [Quick Discovery] Fetching playlist tracks for $category');
      
      final playlists = await _spotifyService.searchPlaylists(
        '$query playlist',
        limit: 2,
        offset: 0,
      );
      
      if (playlists.isEmpty) return [];
      
      final playlistId = playlists.first['id'] as String?;
      if (playlistId == null) return [];
      
      final playlistData = await _spotifyService.getPlaylistTracks(
        playlistId,
        limit: limit,
        offset: 0,
      );
      
      final tracks = playlistData['tracks'] as List<MusicTrack>? ?? [];
      print('🎵 [Quick Discovery] Got ${tracks.length} quick tracks for $category');
      return tracks;
    } catch (e) {
      print('⚠️ [Quick Discovery] Error getting quick tracks for $category: $e');
      return [];
    }
  }

  /// 🚀 NEW: Get quick discovery tracks for mood-based search from playlists only
  Future<List<MusicTrack>> _getQuickMoodDiscovery(int limit) async {
    try {
      final mood = _moods[_selectedMoodIndex];
      final moodName = mood['name'] as String;

      print('🎭 [Quick Mood] Fetching quick playlist tracks for mood: $moodName');

      // Get mood-specific playlist queries
      final playlistQueries = _getMoodPlaylistQueries(moodName.toLowerCase());

      // Try the first few playlist queries for quick results
      for (final query in playlistQueries.take(3)) {
        final tracks = await _getQuickPlaylistDiscovery('moodBased', query, limit);
        if (tracks.isNotEmpty) {
          print('🎭 [Quick Mood] Found ${tracks.length} tracks from playlist query: "$query"');
          return tracks;
        }
      }

      return [];
    } catch (e) {
      print('⚠️ [Quick Mood Discovery] Error getting quick mood playlist tracks: $e');
      return [];
    }
  }

  /// 🚀 NEW: Optimized Similar Artists loading without playlists
  Future<List<MusicTrack>> _getQuickSimilarArtists(int limit, BuildContext context) async {
    try {
      print('🎤 [Quick Artists] Getting quick similar artist tracks');
      
      List<String> quickArtists = [];
      
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final topArtists = userProvider.currentUser?.topArtists ?? [];
        quickArtists = topArtists.take(3).toList();
      } catch (e) {
        print('⚠️ [Quick Artists] Error getting user artists: $e');
      }
      
      if (quickArtists.isEmpty) {
        quickArtists = _userTopArtists.take(3).toList();
      }
      
      if (quickArtists.isEmpty) {
        return [];
      }
      
      final allTracks = <MusicTrack>[];
      for (final artist in quickArtists) {
        try {
          final tracks = await _getCachedArtistTracks(artist, limit: limit ~/ quickArtists.length);
          allTracks.addAll(tracks);
        } catch (e) {
          print('⚠️ [Quick Artists] Error getting tracks for $artist: $e');
        }
      }
      
      allTracks.shuffle();
      print('🎤 [Quick Artists] Got ${allTracks.length} quick artist tracks');
      return allTracks.take(limit).toList();
    } catch (e) {
      print('⚠️ [Quick Artists] Error getting quick similar artists: $e');
      return [];
    }
  }

  /// 🚀 NEW: Get quick tracks from user's music data
  Future<List<MusicTrack>> _getQuickUserTracks(String category, int limit) async {
    try {
      switch (category) {
        case 'topTracks':
          final topTracks = await _spotifyService.getTopTracks(limit: limit);
          return topTracks;
        case 'likedSongs':
          // Use existing method but with minimal offset for quick results  
          if (_currentContext != null) {
            return await _getMoreSimilarToLikedSongs(0, _currentContext!);
          }
          return [];
        case 'recentlyPlayed':
          if (_currentContext != null) {
            return await _getMoreSimilarToRecentlyPlayed(0, _currentContext!);
          }
          return [];
        case 'recentlyAdded':
          if (_currentContext != null) {
            return await _getMoreSimilarToRecentlyAdded(0, _currentContext!);
          }
          return [];
        default:
          return [];
      }
    } catch (e) {
      print('⚠️ [Quick User] Error getting quick user tracks for $category: $e');
      return [];
    }
  }

  /// Helper to update track history
  void _updateTrackHistory(List<MusicTrack> tracks) {
    for (final track in tracks) {
      _recentTrackIds.add(track.id);
      if (_recentTrackIds.length > _maxTrackIdHistory) {
        _recentTrackIds.removeAt(0);
      }
    }
  }

  Future<void> _loadInitialRecommendations(BuildContext context) async {
    final stopwatch = Stopwatch()..start();
    print('⏱️ [Performance] Starting _loadInitialRecommendations');

    _isLoading = true;
    notifyListeners();

    try {
      // Clear cache for fresh results
      _spotifyService.clearSearchCache();

      // Load recommendations based on available services
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      // Try different loading strategies based on available services
      if (spotifyProvider.isConnected) {
        print('🎵 Loading with Spotify as primary service');
        await _loadPersonalizedRecommendations(context);
      } else {
        print('⚠️ No music service connected, loading mood-based content');
        await _loadMoodBasedRecommendations(context);
      }

      // If we still don't have content, ensure we have some basic tracks
      if (_currentRecommendations.isEmpty) {
        print('🔄 No recommendations loaded, using mood-based fallback');
        await _loadMoodBasedRecommendations(context);
      }

      // Add extra shuffling for initial load
      _shuffleCurrentRecommendations();
    } catch (e) {
      print('❌ Error loading initial recommendations: $e');
      // Final fallback to ensure user always sees content
      try {
        await _loadMoodBasedRecommendations(context);
      } catch (fallbackError) {
        print('❌ Even mood-based fallback failed: $fallbackError');
        // Show helpful message in UI - the UI will handle empty state gracefully
        _currentAiMessage =
            'Unable to load recommendations. Please check your internet connection and try again 📶';
        _showAiMessage = true;
        _currentRecommendations = [];
        notifyListeners();
      }
    } finally {
      stopwatch.stop();
      print(
          '⏱️ [Performance] _loadInitialRecommendations completed in ${stopwatch.elapsedMilliseconds}ms');

      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load only artist-based recommendations for suggested songs tab
  /// This is a lightweight version that doesn't load all categories
  Future<void> _loadArtistBasedRecommendationsOnly(BuildContext context) async {
    final stopwatch = Stopwatch()..start();
    print('⏱️ [Performance] Starting _loadArtistBasedRecommendationsOnly for suggested songs');

    _isLoading = true;
    notifyListeners();

    try {
      // Get artist-based recommendations directly
      final artistTracks = await _getRecommendationsForCurrentUserArtists();

      if (artistTracks.isNotEmpty) {
        _currentRecommendations = _intelligentShuffle(artistTracks);
        _categorizedRecommendations['artistBased'] = _currentRecommendations;

        // Track all initially loaded tracks to prevent repeats
        _recentTrackIds.clear();
        _addToRecentTrackIds(_currentRecommendations.map((track) => track.id));

        print('✅ Loaded ${_currentRecommendations.length} artist-based tracks for suggested songs');
      } else {
        print('⚠️ No artist-based tracks found, using fallback');
        // Fallback to mood-based if no artist tracks available
        await _loadMoodBasedRecommendations(context);
      }
    } catch (e) {
      print('❌ Error loading artist-based recommendations: $e');
      // Fallback to mood-based recommendations
      try {
        await _loadMoodBasedRecommendations(context);
      } catch (fallbackError) {
        print('❌ Even mood-based fallback failed: $fallbackError');
        _currentRecommendations = [];
      }
    } finally {
      stopwatch.stop();
      print('⏱️ [Performance] _loadArtistBasedRecommendationsOnly completed in ${stopwatch.elapsedMilliseconds}ms');

      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load recommendations specifically focused on the current mood
  Future<void> _loadMoodBasedRecommendations(BuildContext context) async {
    _isLoading = true;
    notifyListeners();

    try {
      print(
          '🎭 Loading mood-based recommendations for: ${_moods[_selectedMoodIndex]['name']}');

      // Load mood-specific and personalized recommendations concurrently
      final contentTimer = Stopwatch()..start();
      final contentFutures = [
        _getSpotifyRecommendationsForMood(),
        _loadLimitedPersonalizedContent(context),
      ];
      
      final contentResults = await Future.wait(contentFutures);
      final moodTracks = contentResults[0];
      final personalizedTracks = contentResults[1];
      
      contentTimer.stop();
      print('🎭 [Content Load] Concurrent mood+personalized loading completed in ${contentTimer.elapsedMilliseconds}ms');

      // Combine with mood content prioritized
      final allTracks = <MusicTrack>[];
      allTracks.addAll(moodTracks.take(15)); // Prioritize mood content
      allTracks
          .addAll(personalizedTracks.take(10)); // Add some personal content

      // Remove duplicates
      final seenIds = <String>{};
      final uniqueTracks = allTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      // Generate other categories for mood-based recommendations concurrently
      final categoryTimer = Stopwatch()..start();
      final categoryFutures = [
        _getRecommendationsForCurrentUserGenres(),
        _getRecommendationsForCurrentUserArtists(),
      ];
      
      final categoryResults = await Future.wait(categoryFutures);
      final genreBased = categoryResults[0];
      final artistBased = categoryResults[1];
      final discoverList = <MusicTrack>[];
      
      categoryTimer.stop();
      print('🎭 [Mood Categories] Concurrent category generation completed in ${categoryTimer.elapsedMilliseconds}ms');

      print(
          '🎭 [Mood Change] Generated categories: genreBased(${genreBased.length}), artistBased(${artistBased.length}), discover(${discoverList.length})');

      // Combine all categories for 'all' and shuffle
      final combinedAll = <MusicTrack>[];
      combinedAll.addAll(uniqueTracks);
      combinedAll.addAll(genreBased);
      combinedAll.addAll(artistBased);
      // Remove duplicates across combinedAll
      final allIds = <String>{};
      final finalAllList = combinedAll.where((track) {
        if (allIds.contains(track.id)) return false;
        allIds.add(track.id);
        return true;
      }).toList();
      finalAllList.shuffle();

      // Preserve existing category data that shouldn't change with mood
      final existingTopTracks = _categorizedRecommendations['topTracks'];
      final existingLikedSongs = _categorizedRecommendations['likedSongs'];
      final existingRecentlyPlayed =
          _categorizedRecommendations['recentlyPlayed'];

      print(
          '🎭 [Mood Change] Preserving categories: topTracks(${existingTopTracks?.length ?? 0}), likedSongs(${existingLikedSongs?.length ?? 0}), recentlyPlayed(${existingRecentlyPlayed?.length ?? 0})');

      final Map<String, List<MusicTrack>> categorized = {
        'all': finalAllList,
        'moodBased': moodTracks,
        'genreBased': genreBased.isNotEmpty
            ? genreBased
            : (_categorizedRecommendations['genreBased'] ?? []),
        'artistBased': artistBased.isNotEmpty
            ? artistBased
            : (_categorizedRecommendations['artistBased'] ?? []),
        'topTracks': existingTopTracks ?? personalizedTracks.take(10).toList(),
        'likedSongs': existingLikedSongs ?? [],
        'recentlyPlayed': existingRecentlyPlayed ?? [],
        'discover': discoverList.isNotEmpty
            ? discoverList
            : (_categorizedRecommendations['discover'] ?? []),
      };

      _categorizedRecommendations = categorized;
      _currentRecommendations =
          _intelligentShuffle(categorized[_currentCategory] ?? []);

      // Track all initially loaded tracks to prevent repeats
      _recentTrackIds.clear();
      _addToRecentTrackIds(_currentRecommendations.map((track) => track.id));

      // Update AI message for the current mood
      _currentAiMessage = _moods[_selectedMoodIndex]['aiMessage'];
      _showAiMessage = true;
      notifyListeners();

      print(
          '✅ Mood-based recommendations loaded: ${_currentRecommendations.length} tracks');
    } catch (e) {
      print('❌ Error loading mood-based recommendations: $e');

      // Safer fallback: just clear current recommendations and show error state
      _currentRecommendations = [];
      _categorizedRecommendations['moodBased'] = [];
      _currentAiMessage =
          'Failed to load mood recommendations. Try selecting a different mood.';
      _showAiMessage = true;
      notifyListeners();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load limited personalized content for mood-based recommendations
  Future<List<MusicTrack>> _loadLimitedPersonalizedContent(
      BuildContext context) async {
    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      if (!spotifyProvider.isConnected) {
        return [];
      }

      // Get a small sample of user's content
      final topTracks = await _spotifyService.getTopTracks(limit: 10);
      final recentTracks = await _spotifyService.getRecentlyPlayed(limit: 5);

      final allTracks = <MusicTrack>[];
      allTracks.addAll(topTracks);
      allTracks.addAll(recentTracks);

      return _filterAndDeduplicateTracks(allTracks);
    } catch (e) {
      print('Error loading limited personalized content: $e');
      return [];
    }
  }

  /// Get recommendations for current user's genres (smaller set for mood focus)
  Future<List<MusicTrack>> _getRecommendationsForCurrentUserGenres(
      [BuildContext? context]) async {
    try {
      // IMPORTANT: When we're in genreBased category with a specific genre selected,
      // respect that genre selection even in mood-based loading
      if (_currentCategory == 'genreBased' && _selectedGenre != null) {
        print(
            '🎯 [Mood+Genre] Respecting selected genre: $_selectedGenre in mood context');
        // Get tracks only for the selected genre using simple search
        final queries = [
          'genre:"$_selectedGenre"',
          '$_selectedGenre music',
          '$_selectedGenre songs',
        ];

        final allTracks = <MusicTrack>[];
        for (final query in queries.take(2)) {
          final tracks = await _spotifyService.searchTracks(
            query,
            limit: 10,
            context: _getSearchContext(),
          );
          allTracks.addAll(tracks);
        }

        return _filterAndDeduplicateTracks(allTracks);
      }

      // Otherwise, get tracks from user's top genres
      final topGenres = await _spotifyService.getTopGenres(limit: 3);
      if (topGenres.isEmpty) {
        return [];
      }

      // Get tracks for each genre separately to maintain focus
      final allTracks = <MusicTrack>[];
      for (final genre in topGenres) {
        // Simple genre search without context
        final tracks = await _spotifyService.searchTracks(
          'genre:"$genre"',
          limit: 10,
          context: _getSearchContext(),
        );
        allTracks.addAll(tracks.take(5)); // Limit per genre
      }

      return _filterAndDeduplicateTracks(allTracks);
    } catch (e) {
      print('Error getting current user genre recommendations: $e');
      return [];
    }
  }

  /// Get recommendations for current user's artists (smaller set for mood focus)
  Future<List<MusicTrack>> _getRecommendationsForCurrentUserArtists() async {
    try {
      print('🎤 [Artist Recs] Getting current user artist recommendations...');

      List<MusicTrack> topTracks = [];
      List<String> favoriteArtists = [];

      // Attempt to get top tracks from the connected service
      try {
        topTracks = await _spotifyService.getTopTracks(limit: 5);
        favoriteArtists =
            topTracks.map((track) => track.artist).toSet().take(3).toList();
        print(
            '🎤 [Artist Recs] Got ${favoriteArtists.length} favorite artists from top tracks: $favoriteArtists');
      } catch (e) {
        print('⚠️ [Artist Recs] Could not get top tracks from service: $e');
      }

      // Fallback: use cached user top artists (from profile or previous fetch)
      if (favoriteArtists.isEmpty) {
        favoriteArtists = _userTopArtists.take(5).toList();
        print(
            '🎤 [Artist Recs] Falling back to user top artists: $favoriteArtists');
      }

      if (favoriteArtists.isEmpty) {
        print('❌ [Artist Recs] No artists available for recommendations');
        return [];
      }

      final recommendations =
          await _getRecommendationsForArtists(favoriteArtists);
      print(
          '🎤 [Artist Recs] Generated ${recommendations.length} artist-based recommendations');

      return recommendations;
    } catch (e) {
      print(
          '❌ [Artist Recs] Error getting current user artist recommendations: $e');
      return [];
    }
  }

  Future<void> _loadPersonalizedRecommendations(BuildContext context) async {
    try {
      print('🎵 Loading personalized recommendations...');
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      List<String> topGenres = [];
      List<String> favoriteArtists = [];

      if (spotifyProvider.isConnected) {
        print('🎵 Getting user preferences from Spotify...');
        topGenres = await _spotifyService.getTopGenres(limit: 5);
        final topArtists = await _spotifyService.getTopTracks(
            limit: 5); // We'll extract artists from top tracks
        favoriteArtists =
            topArtists.map((track) => track.artist).toSet().take(5).toList();
      } else {
        print(
            '❌ No music service connected, falling back to mood recommendations');
        await _loadSpotifyRecommendations(context);
        return;
      }

      print('🎵 Top genres: $topGenres');
      print('🎵 Favorite artists: $favoriteArtists');

      // Load different types of personalized recommendations
      print('🎵 Loading recommendation types...');
      // 🚀 ENHANCEMENT: Mix different types of recommendations for variety
      print(
          '🔄 [Personalized Load] About to call _getRecommendationsForArtists with: $favoriteArtists');
      final futures = <Future<List<MusicTrack>>>[
        _getRecommendationsForGenres(topGenres, context),
        _getRecommendationsForArtists(favoriteArtists),
        _getSimilarToTopTracks(context),
        _getSimilarToLikedSongs(context),
        _getSimilarToRecentlyPlayed(context),
      ];

      final results = await Future.wait(futures);

      print('🎵 Results: ${results.map((r) => r.length).toList()}');

      // Categorize the results and preserve any existing data
      final Map<String, List<MusicTrack>> categorized = {
        'genreBased': results[0],
        'artistBased': results[1],
        'topTracks': results[2].isNotEmpty
            ? results[2]
            : (_categorizedRecommendations['topTracks'] ?? []),
        'likedSongs': results[3].isNotEmpty
            ? results[3]
            : (_categorizedRecommendations['likedSongs'] ?? []),
        'recentlyPlayed': results[4].isNotEmpty
            ? results[4]
            : (_categorizedRecommendations['recentlyPlayed'] ?? []),
      };

      // Get mood-based recommendations as well
      categorized['moodBased'] = await _getSpotifyRecommendationsForMood();

      // Combine all for 'all' category with smart mixing
      final allTracks = <MusicTrack>[];

      // Prioritize personalized content
      allTracks.addAll(results[0].take(8)); // Top genre recommendations
      allTracks.addAll(results[1].take(6)); // Top artist recommendations
      allTracks.addAll(results[2].take(5)); // User's top tracks
      allTracks.addAll(results[3].take(5)); // Liked songs
      allTracks.addAll(results[4].take(4)); // Recently played
      allTracks.addAll(categorized['moodBased']!.take(4)); // Some mood-based

      // Remove duplicates based on track ID
      final seenIds = <String>{};
      final uniqueTracks = allTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      categorized['all'] = uniqueTracks;
      categorized['discover'] = [];

      print(
          '🎵 Final categorized counts: ${categorized.map((k, v) => MapEntry(k, v.length))}');

      _categorizedRecommendations = categorized;
      _currentRecommendations =
          _intelligentShuffle(categorized[_currentCategory] ?? []);

      // Track all initially loaded tracks to prevent repeats
      _recentTrackIds.clear();
      _addToRecentTrackIds(_currentRecommendations.map((track) => track.id));

      // Update AI message to reflect personalized content
      _currentAiMessage = _getPersonalizedAiMessage(topGenres, favoriteArtists);
      _showAiMessage = true;
      notifyListeners();

      print(
          '✅ Personalized recommendations loaded successfully. Current category: $_currentCategory, tracks: ${_currentRecommendations.length}');
    } catch (e) {
      print('❌ Error loading personalized recommendations: $e');
      // Fallback to standard recommendations
      await _loadSpotifyRecommendations(context);
    }
  }

  /// Get recommendations based on user's favorite genres
  Future<List<MusicTrack>> _getRecommendationsForGenres(
      List<String> genres, BuildContext context) async {
    final allTracks = <MusicTrack>[];

    // IMPORTANT: When a specific genre is selected, only use that genre
    List<String> genresToProcess;
    if (_selectedGenre != null) {
      genresToProcess = [
        SpotifyGenreService.getCanonicalGenre(_selectedGenre!)
      ];
      print('🎯 Processing single selected genre: ${genresToProcess.first}');
    } else {
      // Convert to canonical genres and remove duplicates
      genresToProcess = genres
          .map((genre) => SpotifyGenreService.getCanonicalGenre(genre))
          .toSet()
          .toList();

      // Only shuffle when no specific genre is selected
      genresToProcess.shuffle();
      genresToProcess =
          genresToProcess.take(3).toList(); // Limit to top 3 genres
      print('🎵 Processing multiple genres: $genresToProcess');
    }

    for (final genre in genresToProcess) {
      try {
        // 🚀 ENHANCEMENT: Get user's favorite artists within this genre for better personalization
        final userArtistsInGenre =
            await _getUserArtistsForGenre(genre, context);
        final artistNames = userArtistsInGenre
            .take(5)
            .map((artist) => artist['name'] as String)
            .toList();

        print(
            '🎯 [Genre Recommendations] Found ${artistNames.length} user artists for $genre: $artistNames');

        // Enhanced search queries that incorporate user's artists
        final queries = <String>[];

        // Add user-personalized queries if we have artists in this genre
        if (artistNames.isNotEmpty) {
          for (final artist in artistNames.take(3)) {
            queries.addAll([
              'genre:"$genre" similar to "$artist"',
              '$artist $genre songs',
              'fans of $artist $genre',
              'like $artist $genre music',
            ]);
          }
        }

        // Add general discovery queries for variety
        queries.addAll([
          '$genre new releases 2024',
          '$genre trending songs',
          '$genre underground',
          '$genre indie artists',
          '$genre viral hits',
          '$genre fresh sounds',
          'emerging $genre artists',
          '$genre breakthrough',
          'genre:"$genre" popular',
          'genre:"$genre" hits',
        ]);

        // Pick multiple queries for this genre, prioritizing user-personalized ones
        final randomQueries = List<String>.from(queries)..shuffle();
        final selectedQueries = randomQueries
            .take(5)
            .toList(); // Increased from 3 to 5 for more variety

        for (final query in selectedQueries) {
          try {
            final tracks = await _spotifyService.searchTracks(
              query,
              limit: 12, // OPTIMIZED: Increased from 4 to 12
              context: _getSearchContext(),
            );
            allTracks.addAll(tracks);
            print(
                '📥 [Genre Recommendations] Got ${tracks.length} tracks for "$query"');
          } catch (e) {
            print('❌ [Genre Recommendations] Error with query "$query": $e');
          }
        }
      } catch (e) {
        print('Error getting recommendations for genre "$genre": $e');
      }
    }

    // Shuffle the results for freshness
    allTracks.shuffle();

    print(
        '🎯 [Genre Recommendations] Total tracks collected: ${allTracks.length}');

    // Filter out excluded tracks and remove duplicates
    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Get recommendations based on user's favorite artists with comprehensive caching
  Future<List<MusicTrack>> _getRecommendationsForArtists(List<String> artists,
      [BuildContext? context]) async {
    print(
        '🎯 [Artist Recs] *** ENTRY POINT *** Getting recommendations for ${artists.length} artists: $artists');
    print(
        '🎯 [Artist Recs] Current artist cache size: ${_artistTrackCache.length}');

    if (artists.isEmpty) {
      print('❌ [Artist Recs] No artists provided, returning empty list');
      return [];
    }

    final allTracks = <MusicTrack>[];

    // Shuffle artists for variety
    final shuffledArtists = List<String>.from(artists)..shuffle();
    print('🎯 [Artist Recs] Shuffled artists: $shuffledArtists');

    // 🎵 FIRST: Get direct tracks from selected artists using caching system (PARALLELIZED)
    print(
        '🎤 [Artist Recs] Adding direct tracks from selected artists (cached hybrid method)...');
    print(
        '🗄️ [Artist Cache] Cache size before artist processing: ${_artistTrackCache.length}');

    final selectedArtists = shuffledArtists.take(4).toList();
    final artistFutures = selectedArtists.map((artist) async {
      final artistTimer = Stopwatch()..start();
      try {
        print('🎤 [Artist Recs] Requesting tracks for "$artist"...');
        final tracks = await _getCachedArtistTracks(artist, limit: 35); // OPTIMIZED: Increased from 20 to 35
        print(
            '🎤 [Artist Recs] Cached tracks for "$artist": ${tracks.length} tracks (${artistTimer.elapsedMilliseconds}ms)');
        return tracks;
      } catch (e) {
        print('❌ Error getting cached tracks for artist "$artist": $e');
        // Fallback to direct API call if cache fails
        try {
          final fallbackTracks = await _spotifyService.getArtistTracksHybrid(
            artist,
            limit: 35, // OPTIMIZED: Increased from 20 to 35
            includeAppearsOn: true,
            context: _getSearchContext(),
          );
          print(
              '🎤 [Artist Recs] Fallback tracks for "$artist": ${fallbackTracks.length} tracks (${artistTimer.elapsedMilliseconds}ms)');
          return fallbackTracks;
        } catch (fallbackError) {
          print('❌ Error in fallback for artist "$artist": $fallbackError');
          return <MusicTrack>[];
        }
      } finally {
        artistTimer.stop();
      }
    }).toList();

    final artistResults = await Future.wait(artistFutures);
    for (final tracks in artistResults) {
      allTracks.addAll(tracks);
    }

    print(
        '🗄️ [Artist Cache] Cache size after artist processing: ${_artistTrackCache.length}');

    // 🚀 SECOND: Use Last.fm for better similar artist discovery
    final lastFmService = LastFmService();

    // Get similar artists from Last.fm for better discovery
    final similarArtistsFromLastFm = <String>[];

    if (lastFmService.isConfigured) {
      print('🎵 [Artist Recs] Using Last.fm for similar artist discovery (PARALLELIZED)');
      try {
        // Step 1: Get similar artists from Last.fm in parallel
        final lastFmTimer = Stopwatch()..start();
        final similarArtistFutures = shuffledArtists.take(3).map((artist) async {
          try {
            final similarArtists = await lastFmService.getSimilarArtists(artist, limit: 8);
            print(
                '🎯 [Artist Recs] Last.fm found ${similarArtists.length} similar artists for: $artist');
            return similarArtists;
          } catch (e) {
            print('❌ Error getting similar artists for "$artist": $e');
            return <String>[];
          }
        }).toList();

        final similarArtistResults = await Future.wait(similarArtistFutures);
        for (final artists in similarArtistResults) {
          similarArtistsFromLastFm.addAll(artists);
        }
        lastFmTimer.stop();
        print('🎯 [Artist Recs] Last.fm discovery completed in ${lastFmTimer.elapsedMilliseconds}ms');

        // Remove duplicates and shuffle similar artists
        final uniqueSimilarArtists = similarArtistsFromLastFm.toSet().toList();
        uniqueSimilarArtists.shuffle();

        print(
            '🎯 [Artist Recs] Found ${uniqueSimilarArtists.length} unique similar artists');

        // Step 2: Search for tracks from similar artists in parallel
        final similarArtistTrackTimer = Stopwatch()..start();
        final similarTrackFutures = uniqueSimilarArtists.take(6).map((artist) async {
          final similarTimer = Stopwatch()..start();
          try {
            final tracks = await _getCachedArtistTracks(artist, limit: 20); // OPTIMIZED: Increased from 8 to 20
            print(
                '🎯 [Artist Recs] Similar artist cached search for "$artist" returned ${tracks.length} tracks (${similarTimer.elapsedMilliseconds}ms)');
            return tracks;
          } catch (e) {
            print(
                'Error getting cached recommendations for similar artist "$artist": $e');
            // Fallback to direct API call
            try {
              final fallbackTracks =
                  await _spotifyService.getArtistTracksHybrid(
                artist,
                limit: 20, // OPTIMIZED: Increased from 8 to 20
                includeAppearsOn: true,
                context: _getSearchContext(),
              );
              print(
                  '🎯 [Artist Recs] Fallback tracks for similar artist "$artist": ${fallbackTracks.length} tracks (${similarTimer.elapsedMilliseconds}ms)');
              return fallbackTracks;
            } catch (fallbackError) {
              print(
                  'Error in fallback for similar artist "$artist": $fallbackError');
              return <MusicTrack>[];
            }
          } finally {
            similarTimer.stop();
          }
        }).toList();

        final similarTrackResults = await Future.wait(similarTrackFutures);
        for (final tracks in similarTrackResults) {
          allTracks.addAll(tracks);
        }
        similarArtistTrackTimer.stop();
        print('🎯 [Artist Recs] Similar artist track fetching completed in ${similarArtistTrackTimer.elapsedMilliseconds}ms');
      } catch (e) {
        print(
            '❌ [Artist Recs] Error using Last.fm: $e, falling back to basic search');
        // Fallback to basic search if Last.fm fails
        await _performBasicArtistSearch(shuffledArtists, allTracks);
      }
    } else {
      print('⚠️ [Artist Recs] Last.fm not configured, using basic search');
      await _performBasicArtistSearch(shuffledArtists, allTracks);
    }

    // Shuffle the results for freshness
    allTracks.shuffle();
    print(
        '🎯 [Artist Recs] Total tracks before filtering: ${allTracks.length}');

    // Use the improved filtering method for consistency
    final filteredTracks = _filterAndDeduplicateTracks(allTracks);
    print('🎯 [Artist Recs] Final filtered tracks: ${filteredTracks.length}');

    return filteredTracks;
  }

  /// Fallback method for basic artist search without Last.fm
  Future<void> _performBasicArtistSearch(
      List<String> artists, List<MusicTrack> allTracks) async {
    for (final artist in artists.take(3)) {
      // Limit to top 3 artists
      try {
        // Enhanced search queries for better similar artist discovery
        final queries = [
          '$artist similar artists',
          '$artist radio',
          '$artist inspired by',
          '$artist style music',
          '$artist genre artists',
          'sounds like $artist',
          '$artist fans also like',
        ];

        // Get similar artists by searching for collaborative terms
        final similarArtistQueries = await _getSimilarArtistQueries(artist);
        queries.addAll(similarArtistQueries);

        // Use multiple queries for better diversity
        for (final query in queries.take(4)) {
          final tracks = await _spotifyService.searchTracks(
            query,
            limit: 8, // OPTIMIZED: Increased from 3 to 8
            context: _getSearchContext(),
          );
          allTracks.addAll(tracks);
          print(
              '🎯 [Artist Recs] Query "$query" returned ${tracks.length} tracks');
        }
      } catch (e) {
        print('Error getting recommendations for artist "$artist": $e');
      }
    }
  }

  /// Get more advanced similar artist queries
  Future<List<String>> _getSimilarArtistQueries(String artist) async {
    // Map of artist to similar artists (in a real app, this would come from an API)
    final similarArtistsMap = {
      'lil uzi vert': [
        'gunna',
        'lil baby',
        'playboi carti',
        'travis scott',
        'future'
      ],
      'drake': [
        'future',
        'lil wayne',
        'j cole',
        'kendrick lamar',
        'kanye west'
      ],
      'the weeknd': [
        'post malone',
        'frank ocean',
        'travis scott',
        'bryson tiller',
        'party next door'
      ],
      'ariana grande': [
        'doja cat',
        'olivia rodrigo',
        'billie eilish',
        'taylor swift',
        'dua lipa'
      ],
      'taylor swift': [
        'olivia rodrigo',
        'phoebe bridgers',
        'lorde',
        'clairo',
        'gracie abrams'
      ],
      'kendrick lamar': [
        'j cole',
        'joey badass',
        'danny brown',
        'earl sweatshirt',
        'schoolboy q'
      ],
      'billie eilish': [
        'clairo',
        'phoebe bridgers',
        'lorde',
        'lana del rey',
        'mitski'
      ],
    };

    final queries = <String>[];
    final artistLower = artist.toLowerCase();

    if (similarArtistsMap.containsKey(artistLower)) {
      for (final similarArtist in similarArtistsMap[artistLower]!) {
        queries.addAll([
          '$similarArtist new songs',
          '$similarArtist popular',
          '$similarArtist latest',
        ]);
      }
    }

    return queries;
  }

  /// Get more recommendations for a specific category (pagination)
  Future<List<MusicTrack>> _getMoreRecommendationsForCategory(
      String category, BuildContext context) async {
    final offset = _currentPage * 10;
    final randomOffsetModifier = math.Random().nextInt(5);

    print(
        '📂 Getting recommendations for category: $category (page: $_currentPage, offset: $offset, randomMod: $randomOffsetModifier)');
    print(
        '📊 Current state: _currentRecommendations=${_currentRecommendations.length}, _recentTrackIds=${_recentTrackIds.length}');
    print(
        '🔍 [Category Debug] Processing category: "$category" - checking if artist-based...');

    List<MusicTrack> tracks = [];

    switch (category) {
      case 'all':
        tracks = await _getMoreDiverseRecommendations(offset, context);
        break;
      case 'genreBased':
        if (_selectedGenre != null) {
          // IMPORTANT: When a specific genre is selected, ONLY get tracks for that genre
          print('🎯 Loading more tracks for specific genre: $_selectedGenre');
          tracks = await _getTracksForGenre(_selectedGenre!, offset, context);
        } else {
          // When no specific genre is selected, show user's top genre tracks
          print(
              '🎵 Loading general genre-based recommendations (no specific genre selected)');
          var topGenres = await _spotifyService.getTopGenres(
              limit: 5); // Reduced from 10 to 5
          // Don't shuffle genres - keep them consistent
          tracks = await _getMoreRecommendationsForGenres(
              topGenres, offset, context);
        }
        break;
      case 'artistBased':
        print(
            '🎤 [ArtistBased Category] *** PROCESSING ARTIST-BASED PAGINATION ***');
        print('🎤 [ArtistBased Category] Offset: $offset, Page: $_currentPage');
        print(
            '🎤 [ArtistBased Category] Current artist cache size: ${_artistTrackCache.length}');

        // Get artists from user profile first, then expand with Last.fm
        List<String> favoriteArtists = [];

        // 1. Try to get user's favorite artists from profile
        try {
          final userProvider =
              Provider.of<UserProvider>(context, listen: false);
          if (userProvider.currentUser?.topArtists != null &&
              userProvider.currentUser!.topArtists!.isNotEmpty) {
            favoriteArtists =
                List<String>.from(userProvider.currentUser!.topArtists!);
            print(
                '🎤 [ArtistBased Category] Got ${favoriteArtists.length} artists from user profile: $favoriteArtists');
          } else {
            print(
                '🎤 [ArtistBased Category] User profile has no top artists, attempting to populate...');
            await populateUserMusicPreferences(context);
            // Try again after population
            if (userProvider.currentUser?.topArtists != null &&
                userProvider.currentUser!.topArtists!.isNotEmpty) {
              favoriteArtists =
                  List<String>.from(userProvider.currentUser!.topArtists!);
              print(
                  '🎤 [ArtistBased Category] Got ${favoriteArtists.length} artists from newly populated profile: $favoriteArtists');
            }
          }
        } catch (e) {
          print(
              '🎤 [ArtistBased Category] Failed to get user profile artists: $e');
        }

        // 2. Fallback to Spotify top tracks if no user profile data
        if (favoriteArtists.isEmpty) {
          try {
            print(
                '🎤 [ArtistBased Category] No user profile artists, fetching from Spotify...');
            final topTracks = await _spotifyService.getTopTracks(limit: 20);
            favoriteArtists =
                topTracks.map((track) => track.artist).toSet().toList();
            print(
                '🎤 [ArtistBased Category] Got ${favoriteArtists.length} artists from Spotify: $favoriteArtists');
          } catch (e) {
            print(
                '🎤 [ArtistBased Category] Spotify failed: $e, using cached user artists: $_userTopArtists');
            favoriteArtists = _userTopArtists;
          }
        }

        // 3. Fast progressive expansion with Last.fm similar artists
        final lastFmService = LastFmService();
        if (lastFmService.isConfigured && favoriteArtists.isNotEmpty) {
          print(
              '🎤 [ArtistBased Category] Starting fast progressive expansion...');
          favoriteArtists = await _performFastProgressiveExpansion(
              favoriteArtists, lastFmService);
          print(
              '🎤 [ArtistBased Category] Total artists after fast expansion: ${favoriteArtists.length}');
        }

        // 4. Final fallback if still no artists available
        if (favoriteArtists.isEmpty) {
          print(
              '⚠️ [ArtistBased Category] No artists available from any source, using popular fallback artists');
          favoriteArtists = [
            'Taylor Swift',
            'Ed Sheeran',
            'Billie Eilish',
            'The Weeknd',
            'Ariana Grande',
            'Drake',
            'Dua Lipa',
            'Post Malone'
          ];
          print(
              '🎤 [ArtistBased Category] Using fallback artists: $favoriteArtists');
        }

        // 5. Shuffle and select artists for processing
        favoriteArtists.shuffle();
        final artistsToProcess = favoriteArtists
            .take(_artistsPerPage)
            .toList(); // Configurable artists per page
        print(
            '🎤 [ArtistBased Category] Final artists for processing: $artistsToProcess');

        // Show current exploration stats
        final explorationStats = getExplorationStats();
        print('🚀 [ArtistBased Category] Exploration stats: $explorationStats');

        tracks = await _getTracksFromArtistList(
            artistsToProcess, offset, 'artistBased pagination');
        print(
            '🎤 [ArtistBased Category] _getTracksFromArtistList returned ${tracks.length} tracks');
        print(
            '🎤 [ArtistBased Category] Artist cache size after processing: ${_artistTrackCache.length}');
        break;
      case 'topTracks':
        print('🏆 [Search Type Debug] Using top tracks search');
        tracks = await _getMoreSimilarToTopTracks(offset, context);
        break;
      case 'likedSongs':
        print('❤️ [Search Type Debug] Using liked songs search');
        tracks = await _getMoreSimilarToLikedSongs(offset, context);
        break;
      case 'recentlyPlayed':
        print('🕒 [Search Type Debug] Using recently played search');
        tracks = await _getMoreSimilarToRecentlyPlayed(offset, context);
        break;
      case 'recentlyAdded':
        print('📥 [Search Type Debug] Using recently added search');
        tracks = await _getMoreSimilarToRecentlyAdded(offset, context);
        break;
      case 'moodBased':
        print('🎭 [Search Type Debug] Using mood-based search');
        tracks = await _getMoreMoodBasedRecommendations(offset);
        break;
      case 'discover':
        print('🔍 [Search Type Debug] Using discovery search');
        tracks = await _getMoreDiscoveryTracks(offset, context);
        break;
      default:
        tracks = await _getMoreDiscoveryTracks(offset, context);
        break;
    }

    // Add final shuffle for extra randomization
    tracks.shuffle();

    print(
        '📂 Category $category returned ${tracks.length} tracks (after shuffle)');
    return tracks;
  }

  /// Get more genre-based recommendations with pagination
  Future<List<MusicTrack>> _getMoreRecommendationsForGenres(
      List<String> genres, int offset, BuildContext context) async {
    print(
        '🎵 Getting genre-based recommendations for multiple genres (offset: $offset)...');
    print('🎵 Genres to process: $genres');

    final allTracks = <MusicTrack>[];

    // IMPORTANT: Keep genre recommendations focused by processing fewer genres at once
    // and ensuring we don't mix too many different genres together

    // When showing "all genres", focus on the user's top 3 genres to avoid contamination
    final focusedGenres = genres.take(3).toList();
    print('🎵 Focusing on top ${focusedGenres.length} genres: $focusedGenres');

    // Process each genre separately to maintain genre integrity
    for (final genre in focusedGenres) {
      print('🎵 Processing genre: $genre');

      try {
        // Get tracks specifically for this genre
        final genreTracks =
            await _getTracksForSingleGenre(genre, offset, context);
        allTracks.addAll(genreTracks);
        print('📥 Got ${genreTracks.length} tracks for genre: $genre');
      } catch (e) {
        print('❌ Error getting tracks for genre $genre: $e');
      }
    }

    print('🎵 Total genre tracks collected: ${allTracks.length}');

    // PAGINATION ENHANCEMENT: If we don't have enough tracks after filtering, use Last.fm expansion
    var filteredTracks = _filterAndDeduplicateTracks(allTracks);

    if (filteredTracks.length < 8) {
      print(
          '📈 [Pagination] Only ${filteredTracks.length} tracks after filtering - expanding with Last.fm...');

      try {
        final lastFmService = LastFmService();
        if (lastFmService.isConfigured) {
          // Get user artists for the focused genres to use as seeds
          final allUserArtists = <String>[];
          for (final genre in focusedGenres) {
            final userArtistsInGenre =
                await _getUserArtistsForGenre(genre, context);
            final artistNames = userArtistsInGenre
                .take(2)
                .map((artist) => artist['name'] as String)
                .toList();
            allUserArtists.addAll(artistNames);
          }

          if (allUserArtists.isNotEmpty) {
            // Get additional similar artists from Last.fm using pagination offset for variety
            final expandedArtists =
                await lastFmService.getWeightedSimilarArtists(
              seedArtists: allUserArtists
                  .take(3)
                  .toList(), // Limit seeds for faster response
              limit: 15,
              minSimilarity: 0.1,
            );

            print(
                '🔍 [Pagination Last.fm] Found ${expandedArtists.length} expansion candidates');

            // Use anti-repetition system for artist selection
            final availableArtists = expandedArtists
                .where(
                    (artist) => !_recentArtists.contains(artist.toLowerCase()))
                .toList();

            final selectedArtists = <String>[];
            if (availableArtists.isNotEmpty) {
              // Use fresh artists first
              final random = math.Random(_queryVariationSeed + offset);
              final shuffledFresh = List<String>.from(availableArtists)
                ..shuffle(random);
              selectedArtists.addAll(shuffledFresh.take(6));
            }

            // If we need more artists, use some from the full list
            if (selectedArtists.length < 6) {
              final remaining = expandedArtists
                  .where((artist) => !selectedArtists.contains(artist))
                  .toList();
              final random = math.Random(_queryVariationSeed + offset + 100);
              final shuffledRemaining = List<String>.from(remaining)
                ..shuffle(random);
              selectedArtists
                  .addAll(shuffledRemaining.take(6 - selectedArtists.length));
            }

            // Search for tracks from these selected artists with anti-repetition
            final expansionTracks = <MusicTrack>[];
            for (final artist in selectedArtists) {
              if (expansionTracks.length >= 15) break;

              try {
                final market = focusedGenres.isNotEmpty
                    ? _getMarketForGenre(focusedGenres.first)
                    : null;

                // Use anti-repetition query generation
                final queries =
                    _generateVariedArtistQueries(artist, 'general', offset);

                // Mark artist as recently used
                _recentArtists.add(artist.toLowerCase());
                if (_recentArtists.length > 50) {
                  final recentList = _recentArtists.toList();
                  _recentArtists.clear();
                  _recentArtists.addAll(recentList.skip(10)); // Keep last 40
                }

                // Use 2 fresh queries per artist
                for (final query in queries.take(2)) {
                  if (_shouldAvoidQuery(query)) continue;

                  _markQueryAsUsed(query);

                  try {
                    final tracks = await _spotifyService.searchTracks(
                      query,
                      limit: 2,
                      market: market,
                      context: _getSearchContext(),
                    );
                    expansionTracks.addAll(tracks);
                    print(
                        '📥 [Anti-Repeat] Got ${tracks.length} tracks for "$query"${market != null ? " [Market: $market]" : ""}');
                  } catch (e) {
                    print('❌ [Anti-Repeat] Error with query "$query": $e');
                  }
                }
              } catch (e) {
                print('❌ [Pagination Last.fm] Error for artist $artist: $e');
              }
            }

            // Filter the expansion tracks and add them to our results
            final filteredExpansionTracks =
                _filterAndDeduplicateTracks(expansionTracks);
            filteredTracks.addAll(filteredExpansionTracks);

            print(
                '📊 [Pagination Last.fm] Added ${filteredExpansionTracks.length} valid tracks. Total: ${filteredTracks.length}');
          }
        }
      } catch (e) {
        print('❌ [Pagination Last.fm] Error expanding artist pool: $e');
      }
    }

    // Shuffle to mix the genres together AFTER getting them separately
    filteredTracks.shuffle();

    return filteredTracks;
  }

  /// Get tracks for a single genre to maintain genre purity
  Future<List<MusicTrack>> _getTracksForSingleGenre(
      String genre, int offset, BuildContext context) async {
    final tracks = <MusicTrack>[];

    try {
      // Get user's favorite artists within this genre for better personalization
      final userArtistsInGenre = await _getUserArtistsForGenre(genre, context);
      final artistNames = userArtistsInGenre
          .take(3)
          .map((artist) => artist['name'] as String)
          .toList();

      print(
          '🎯 [Single Genre] Found ${artistNames.length} user artists for $genre: $artistNames');

      // Build queries specific to this genre only
      final queries = <String>[];

      // Add user-personalized queries if we have artists in this genre
      if (artistNames.isNotEmpty) {
        for (final artist in artistNames.take(2)) {
          queries.addAll([
            'genre:"$genre" artist:"$artist"',
            'genre:"$genre" similar to "$artist"',
            '$artist $genre music',
          ]);
        }
      }

      // Add general genre-specific queries
      queries.addAll([
        'genre:"$genre" popular',
        'genre:"$genre" hits',
        'genre:"$genre" trending',
        '$genre new releases ${DateTime.now().year}',
        '$genre essential tracks',
      ]);

      // Use anti-repetition to select fresh queries
      final freshQueries = queries.where((q) => !_shouldAvoidQuery(q)).toList();
      List<String> selectedQueries;

      if (freshQueries.length >= 3) {
        // Use fresh queries first
        final random = math.Random(_queryVariationSeed + offset);
        selectedQueries =
            (List<String>.from(freshQueries)..shuffle(random)).take(3).toList();
      } else {
        // If we don't have enough fresh queries, use some from all
        final random = math.Random(_queryVariationSeed + offset + 200);
        selectedQueries =
            (List<String>.from(queries)..shuffle(random)).take(3).toList();
      }

      // Execute anti-repetition queries for this specific genre
      for (final query in selectedQueries) {
        if (_shouldAvoidQuery(query)) continue;

        _markQueryAsUsed(query);

        try {
          final searchTracks = await _spotifyService.searchTracks(
            query,
            limit: 15, // OPTIMIZED: Increased from 5 to 15
            context: _getSearchContext(),
          );
          tracks.addAll(searchTracks);
          print(
              '📥 [Anti-Repeat Genre] Got ${searchTracks.length} tracks for "$query"');
        } catch (e) {
          print('❌ [Anti-Repeat Genre] Error with query "$query": $e');
        }
      }
    } catch (e) {
      print('❌ Error getting tracks for single genre $genre: $e');
    }

    return tracks;
  }

  /// Get related genres for more variety
  List<String> _getRelatedGenres(String genre) {
    return SpotifyGenreService.getRelatedGenres(genre);
  }

  /// Get more artist-based recommendations with pagination using AI service
  Future<List<MusicTrack>> _getMoreRecommendationsForArtists(
      List<String> artists, int offset, BuildContext context) async {
    // Calculate current page from offset
    final currentPage = (offset / 30).floor();
    print(
        '🎤 [Artist Pagination] Getting recommendations (page: $currentPage, offset: $offset)');

    if (artists.isEmpty) {
      print('❌ [Artist Pagination] No artists provided, returning empty list');
      return [];
    }

    try {
      // Use AI recommendation service for better personalized results
      final tracks =
          await _aiRecommendationService.getMoreRecommendationsForCategory(
        'artistBased',
        artists,
        {
          'currentPage': currentPage,
          'pageSize': 30,
          'selectedGenre': null,
        },
      );

      print(
          '🎤 [Artist Pagination] AI service returned ${tracks.length} tracks for page $currentPage');
      return tracks;
    } catch (e) {
      print('❌ [Artist Pagination] Error with AI service: $e');
      // Fallback to simple direct search of user's favorite artists
      return await _fallbackArtistSearch(artists, offset);
    }
  }

  /// Fallback artist search if AI service fails
  Future<List<MusicTrack>> _fallbackArtistSearch(
      List<String> artists, int offset) async {
    print(
        '🔄 [Artist Pagination] Using fallback artist search with anti-repetition');

    final allTracks = <MusicTrack>[];

    // Use anti-repetition for artist selection
    final availableArtists = artists
        .where((artist) => !_recentArtists.contains(artist.toLowerCase()))
        .toList();

    List<String> selectedArtists;
    if (availableArtists.length >= 4) {
      final random = math.Random(_queryVariationSeed + offset);
      selectedArtists = (List<String>.from(availableArtists)..shuffle(random))
          .take(4)
          .toList();
    } else {
      final random = math.Random(_queryVariationSeed + offset + 300);
      selectedArtists =
          (List<String>.from(artists)..shuffle(random)).take(4).toList();
    }

    for (final artist in selectedArtists) {
      try {
        // Mark artist as recently used
        _recentArtists.add(artist.toLowerCase());

        // First: Get direct tracks from the selected artist using hybrid approach
        try {
          final tracks = await _spotifyService.getArtistTracksHybrid(
            artist,
            limit: 25, // OPTIMIZED: Increased from 12 to 25
            includeAppearsOn: true,
            context: _getSearchContext(),
          );
          allTracks.addAll(tracks);
          print(
              '🎤 [Fallback Hybrid] Got ${tracks.length} tracks from "$artist"\'s albums');
        } catch (e) {
          print('❌ [Fallback Hybrid] Error with artist "$artist": $e');
        }

        // Second: Use varied queries for similar artists with anti-repetition
        final queries =
            _generateVariedArtistQueries(artist, 'fallback', offset);

        for (final query in queries.take(2)) {
          if (_shouldAvoidQuery(query)) continue;

          _markQueryAsUsed(query);

          try {
            final tracks = await _spotifyService.searchTracks(
              query,
              limit: 2,
              context: _getSearchContext(),
            );
            allTracks.addAll(tracks);
            print(
                '📥 [Anti-Repeat Fallback] Got ${tracks.length} tracks for "$query"');
          } catch (e) {
            print('❌ [Anti-Repeat Fallback] Error with query "$query": $e');
          }
        }
      } catch (e) {
        print('❌ [Fallback] Error for artist $artist: $e');
      }
    }

    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Perform artist search with given artist list
  Future<void> _performArtistSearch(
      List<String> artists, int offset, List<MusicTrack> allTracks) async {
    // Use more artists and more diverse queries
    final startIndex = (offset * 3) % artists.length;

    for (int i = 0; i < math.min(6, artists.length); i++) {
      // Increased from 4 to 6
      final artistIndex = (startIndex + i) % artists.length;
      final artist = artists[artistIndex];

      print('🎤 Searching for artist: $artist (index: $artistIndex)');

      try {
        // Much more diverse query types
        final queries = [
          '$artist new songs',
          '$artist remix',
          '$artist collaborations',
          '$artist deep cuts',
          '$artist rare tracks',
          '$artist b-sides',
          '$artist unreleased',
          '$artist latest',
          '$artist popular',
          '$artist underground',
          'artist:"$artist" year:2020..2024',
          'artist:"$artist" popular',
          '$artist style music',
          '$artist similar artists',
          '$artist inspired music',
        ];

        // Use different queries based on offset to ensure variety
        final primaryQuery = queries[(offset + i) % queries.length];
        final secondaryQuery = queries[(offset + i + 5) % queries.length];

        // Try primary query
        final primaryTracks = await _spotifyService.searchTracks(
          primaryQuery,
          limit: 5,
          context: _getSearchContext(),
        );
        allTracks.addAll(primaryTracks);
        print('📥 Got ${primaryTracks.length} tracks for "$primaryQuery"');

        // Try secondary query for more variety
        final secondaryTracks = await _spotifyService.searchTracks(
          secondaryQuery,
          limit: 3,
          context: _getSearchContext(),
        );
        allTracks.addAll(secondaryTracks);
        print('📥 Got ${secondaryTracks.length} tracks for "$secondaryQuery"');
      } catch (e) {
        print('❌ Error getting tracks for artist $artist: $e');
      }
    }
  }

  /// Get more mood-based recommendations with pagination and endless loading
  /// Focuses exclusively on curated playlists that match the selected mood
  /// Following the infinite loading pattern from genre_search_provider.dart
  Future<List<MusicTrack>> _getMoreMoodBasedRecommendations(int offset) async {
    final mood = _moods[_selectedMoodIndex];
    final moodName = mood['name'].toString().toLowerCase();

    print(
        '🎭 Getting mood-based playlist recommendations for: $moodName (offset: $offset, page: $_currentPage)');

    try {
      // Focus exclusively on mood-specific playlist discovery
      final playlistTracks = await _getMoodPlaylistTracks(moodName, offset);
      print('🎵 Got ${playlistTracks.length} tracks from mood playlists');

      if (playlistTracks.isNotEmpty) {
        // Apply intelligent randomization for endless variety
        final randomizedTracks = _applyMoodRandomization(playlistTracks, offset);
        final filteredTracks = _filterAndDeduplicateTracks(randomizedTracks);

        print('🎭 Total mood playlist tracks collected: ${filteredTracks.length} (after randomization and filtering)');
        return filteredTracks;
      } else {
        // Fallback: Try alternative mood queries if no tracks found
        print('🔄 No tracks from primary mood search, trying fallback...');
        final fallbackTracks = await _getMoodFallbackTracks(moodName, offset);
        print('🔄 Fallback returned ${fallbackTracks.length} tracks');

        if (fallbackTracks.isNotEmpty) {
          final randomizedTracks = _applyMoodRandomization(fallbackTracks, offset);
          final filteredTracks = _filterAndDeduplicateTracks(randomizedTracks);
          return filteredTracks;
        }

        // Last resort: Return some tracks to maintain infinite loading
        print('🆘 Using emergency mood tracks to maintain infinite loading');
        return await _getEmergencyMoodTracks(moodName);
      }
    } catch (e) {
      print('❌ Error in mood-based recommendations: $e');
      // Always return something to maintain infinite loading
      return await _getEmergencyMoodTracks(moodName);
    }
  }

  /// Get tracks from mood-specific playlists with comprehensive search strategy
  /// Following the exact pattern from genre_search_provider.dart for infinite loading
  Future<List<MusicTrack>> _getMoodPlaylistTracks(String moodName, int offset) async {
    final allTracks = <MusicTrack>[];
    final processedPlaylistIds = <String>{};

    // Reset mood tracking periodically for endless variety
    _resetMoodAntiRepetitionTracking();

    // Get comprehensive mood-specific playlist queries
    final playlistQueries = _getMoodPlaylistQueries(moodName);

    // Filter out recently used queries for variety
    final freshQueries = playlistQueries.where((query) =>
        !_shouldAvoidMoodQuery(query, moodName)).toList();

    // If all queries were used, reset and use all (infinite pattern)
    final queriesToUse = freshQueries.isNotEmpty ? freshQueries : playlistQueries;
    print('🎭 Using ${queriesToUse.length} fresh playlist queries out of ${playlistQueries.length} total');

    // Use offset to cycle through different playlist queries for endless variety
    final startIndex = (offset * 2) % queriesToUse.length;
    final queriesToProcess = <String>[];

    // Process 3 queries for more focused results (following genre pattern)
    for (int i = 0; i < 3; i++) {
      final queryIndex = (startIndex + i) % queriesToUse.length;
      queriesToProcess.add(queriesToUse[queryIndex]);
    }

    for (final playlistQuery in queriesToProcess) {
      try {
        print('🎵 Searching mood playlists: "$playlistQuery"');

        // Mark query as used for anti-repetition
        _markMoodQueryAsUsed(playlistQuery, moodName);

        final playlists = await _spotifyService.searchPlaylists(
          playlistQuery,
          limit: 15, // Increased for more variety
        );

        // Shuffle playlists and pick random ones for variety (following genre pattern)
        final shuffledPlaylists = List.from(playlists)..shuffle();
        final selectedPlaylists = shuffledPlaylists.take(3).toList(); // Use 3 random playlists

        for (final playlist in selectedPlaylists) {
          final playlistId = playlist['id'] as String?;
          final playlistName = playlist['name'] as String? ?? '';
          if (playlistId == null) {
            print('⚠️ Skipping playlist with null ID');
            continue;
          }

          if (processedPlaylistIds.contains(playlistId) || _usedMoodPlaylists.contains(playlistId)) {
            print('⚠️ Skipping already processed playlist: "$playlistName"');
            continue;
          }

          processedPlaylistIds.add(playlistId);
          _usedMoodPlaylists.add(playlistId);

          print('🎵 [Mood] Using validated playlist "$playlistName" (ID: $playlistId)');

          try {
            // Use random offset for each playlist (following genre pattern)
            final random = math.Random();
            final randomOffset = random.nextInt(30); // Random offset 0-29
            final trackLimit = math.max(20, (60 / selectedPlaylists.length).ceil());

            print('🔍 [Mood] Fetching $trackLimit tracks from playlist "$playlistName" with offset $randomOffset');

            final playlistResponse = await _spotifyService.getPlaylistTracks(
              playlistId,
              limit: trackLimit,
              offset: randomOffset,
            );

            print('🔍 [Mood] Playlist response keys: ${playlistResponse.keys.toList()}');

            final playlistTracks = playlistResponse['tracks'] as List<MusicTrack>? ?? [];
            allTracks.addAll(playlistTracks);
            print('✅ Added ${playlistTracks.length} tracks from mood playlist: "$playlistName"');

            // Debug: Show some track info if available
            if (playlistTracks.isNotEmpty) {
              final firstTrack = playlistTracks.first;
              print('🎵 Sample track: "${firstTrack.title}" by ${firstTrack.artist}');
            } else {
              print('⚠️ No tracks returned from playlist "$playlistName"');
            }

          } catch (e) {
            print('❌ Error getting tracks from playlist $playlistId: $e');
          }
        }

      } catch (e) {
        print('❌ Error searching playlists for "$playlistQuery": $e');
      }
    }

    // Shuffle tracks for final variety (following genre pattern)
    final shuffledTracks = List<MusicTrack>.from(allTracks)..shuffle();

    print('🎭 Collected ${shuffledTracks.length} tracks from mood-specific playlists');
    return shuffledTracks;
  }

  /// Fallback method for mood-based tracks when primary search fails
  Future<List<MusicTrack>> _getMoodFallbackTracks(String moodName, int offset) async {
    final allTracks = <MusicTrack>[];

    try {
      // Use broader mood-related search terms
      final fallbackQueries = [
        '$moodName music',
        '$moodName vibes',
        '$moodName mood',
        'feel $moodName',
        '$moodName playlist',
      ];

      for (final query in fallbackQueries.take(2)) {
        try {
          final playlists = await _spotifyService.searchPlaylists(query, limit: 10);

          for (final playlist in playlists.take(2)) {
            final playlistId = playlist['id'] as String?;
            if (playlistId == null) continue;

            final playlistResponse = await _spotifyService.getPlaylistTracks(
              playlistId,
              limit: 20,
              offset: offset % 50,
            );

            final tracks = playlistResponse['tracks'] as List<MusicTrack>? ?? [];
            allTracks.addAll(tracks);

            if (allTracks.length >= 40) break;
          }

          if (allTracks.length >= 40) break;
        } catch (e) {
          print('❌ Error in fallback query "$query": $e');
        }
      }
    } catch (e) {
      print('❌ Error in mood fallback: $e');
    }

    return allTracks;
  }

  /// Emergency method to always return some tracks for infinite loading
  Future<List<MusicTrack>> _getEmergencyMoodTracks(String moodName) async {
    try {
      // Use very broad search terms that should always return results
      final emergencyQueries = [
        'popular music',
        'top hits',
        'best songs',
        'music playlist',
      ];

      for (final query in emergencyQueries) {
        try {
          final playlists = await _spotifyService.searchPlaylists(query, limit: 5);

          if (playlists.isNotEmpty) {
            final playlist = playlists.first;
            final playlistId = playlist['id'] as String?;
            if (playlistId != null) {
              final playlistResponse = await _spotifyService.getPlaylistTracks(
                playlistId,
                limit: 20,
                offset: 0,
              );

              final tracks = playlistResponse['tracks'] as List<MusicTrack>? ?? [];
              if (tracks.isNotEmpty) {
                print('🆘 Emergency: Found ${tracks.length} tracks from "$query"');
                return tracks;
              }
            }
          }
        } catch (e) {
          print('❌ Error in emergency query "$query": $e');
        }
      }
    } catch (e) {
      print('❌ Error in emergency mood tracks: $e');
    }

    // Absolute last resort: return empty list but log it
    print('🆘 CRITICAL: No emergency tracks found - returning empty list');
    return [];
  }



  /// Apply intelligent randomization for mood-based tracks
  List<MusicTrack> _applyMoodRandomization(List<MusicTrack> tracks, int offset) {
    if (tracks.isEmpty) return tracks;

    // Create multiple randomization seeds for variety
    final baseSeed = offset + _selectedMoodIndex + DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final random = math.Random(baseSeed);

    // Shuffle the tracks
    final shuffledTracks = List<MusicTrack>.from(tracks)..shuffle(random);

    // Apply mood-specific sorting preferences
    final mood = _moods[_selectedMoodIndex];
    final moodName = mood['name'].toString().toLowerCase();

    // For certain moods, prefer more popular tracks at the beginning
    if (['happy', 'energetic'].contains(moodName)) {
      shuffledTracks.sort((a, b) {
        final aPopularity = a.popularity ?? 0;
        final bPopularity = b.popularity ?? 0;
        // Add some randomness to avoid always showing the same popular tracks
        final randomFactor = random.nextDouble() * 20; // Up to 20 point random boost
        return (bPopularity + randomFactor).compareTo(aPopularity + randomFactor);
      });
    }

    // For calm/focused moods, prefer less mainstream tracks
    if (['calm', 'focused', 'dreamy'].contains(moodName)) {
      shuffledTracks.sort((a, b) {
        final aPopularity = a.popularity ?? 50;
        final bPopularity = b.popularity ?? 50;
        final randomFactor = random.nextDouble() * 30; // More randomness for discovery
        return (aPopularity + randomFactor).compareTo(bPopularity + randomFactor);
      });
    }

    return shuffledTracks;
  }

  /// Get comprehensive mood queries with maximum variety
  List<String> _getComprehensiveMoodQueries(String mood) {
    final baseQueries = _getMoodSearchQueries(mood);
    final enhancedQueries = _getEnhancedMoodSearchQueries(mood, 0);

    // Add even more comprehensive mood-specific queries
    final comprehensiveQueries = <String>[];
    comprehensiveQueries.addAll(baseQueries);
    comprehensiveQueries.addAll(enhancedQueries);

    switch (mood) {
      case 'happy':
        comprehensiveQueries.addAll([
          'feel good hits',
          'positive energy songs',
          'uplifting tracks',
          'mood boosters',
          'happy vibes playlist',
          'cheerful music',
          'joyful songs',
          'sunshine music',
          'good mood anthems',
          'celebration hits',
          'upbeat classics',
          'happy pop songs',
          'feel good rap',
          'positive hip hop',
          'uplifting indie',
        ]);
        break;
      case 'energetic':
        comprehensiveQueries.addAll([
          'high energy workout',
          'pump up music',
          'adrenaline rush songs',
          'intense workout beats',
          'power music',
          'energetic rock',
          'high tempo electronic',
          'motivational tracks',
          'beast mode music',
          'explosive energy',
          'hardcore beats',
          'energetic rap',
          'intense electronic',
          'power anthems',
          'gym motivation',
        ]);
        break;
      case 'calm':
        comprehensiveQueries.addAll([
          'peaceful ambient',
          'relaxing instrumental',
          'calming nature sounds',
          'meditation music',
          'zen ambient',
          'tranquil soundscapes',
          'soothing melodies',
          'calm acoustic',
          'peaceful piano',
          'relaxing classical',
          'serene music',
          'gentle sounds',
          'calm indie',
          'peaceful folk',
          'relaxing lo-fi',
        ]);
        break;
      case 'focused':
        comprehensiveQueries.addAll([
          'concentration music',
          'focus instrumental',
          'study beats',
          'deep work music',
          'productivity sounds',
          'focus ambient',
          'concentration classical',
          'study lo-fi',
          'work music',
          'focus electronic',
          'brain music',
          'cognitive enhancement',
          'focus soundscape',
          'study ambient',
          'concentration aid',
        ]);
        break;
      case 'dreamy':
        comprehensiveQueries.addAll([
          'ethereal soundscapes',
          'dreamy indie',
          'atmospheric music',
          'floating melodies',
          'dreamy electronic',
          'ambient dreams',
          'shoegaze atmosphere',
          'dreamy pop',
          'ethereal vocals',
          'cosmic ambient',
          'dream sequences',
          'floating sounds',
          'otherworldly music',
          'ethereal indie',
          'dreamy alternative',
        ]);
        break;
      case 'chill':
        comprehensiveQueries.addAll([
          'chill hop beats',
          'laid back vibes',
          'relaxed atmosphere',
          'chill indie',
          'smooth sounds',
          'easy listening',
          'chill electronic',
          'mellow beats',
          'casual listening',
          'chill rap',
          'relaxed hip hop',
          'smooth jazz',
          'chill acoustic',
          'lazy day music',
          'weekend vibes',
        ]);
        break;
    }

    return comprehensiveQueries;
  }

  /// Get enhanced mood search queries with pagination variety
  List<String> _getEnhancedMoodSearchQueries(String mood, int offset) {
    final baseQueries = _getMoodSearchQueries(mood);

    // Add pagination-specific variations
    final additionalQueries = <String>[];

    switch (mood) {
      case 'happy':
        additionalQueries.addAll([
          'uplifting beats',
          'sunshine vibes',
          'good times music',
          'party anthems',
          'celebration songs',
          'joyful melodies'
        ]);
        break;
      case 'energetic':
        additionalQueries.addAll([
          'adrenaline rush',
          'high octane music',
          'power workout',
          'intense beats',
          'explosive energy',
          'motivational tracks'
        ]);
        break;
      case 'calm':
        additionalQueries.addAll([
          'serene soundscapes',
          'tranquil moments',
          'peaceful reflection',
          'soothing melodies',
          'zen music',
          'mindful listening'
        ]);
        break;
      case 'focused':
        additionalQueries.addAll([
          'deep concentration',
          'productivity music',
          'mental clarity',
          'ambient focus',
          'study session',
          'work flow music'
        ]);
        break;
      case 'dreamy':
        additionalQueries.addAll([
          'ethereal sounds',
          'floating melodies',
          'ambient dreams',
          'atmospheric bliss',
          'cosmic vibes',
          'transcendent music'
        ]);
        break;
      case 'chill':
        additionalQueries.addAll([
          'laid back grooves',
          'easy listening',
          'sunset vibes',
          'relaxed atmosphere',
          'smooth sounds',
          'casual listening'
        ]);
        break;
    }

    // Combine and rotate based on offset
    final allQueries = [...baseQueries, ...additionalQueries];
    final rotatedQueries = <String>[];

    for (int i = 0; i < allQueries.length; i++) {
      final index = (i + offset) % allQueries.length;
      rotatedQueries.add(allQueries[index]);
    }

    return rotatedQueries;
  }

  /// Get more diverse recommendations for the "all" category
  Future<List<MusicTrack>> _getMoreDiverseRecommendations(
      int offset, BuildContext context) async {
    print('🎲 Getting diverse recommendations (offset: $offset)...');

    final allTracks = <MusicTrack>[];

    // Expanded recommendation strategies similar to artists tab
    final strategies = [
      'liked_artists',
      'top_artists',
      'mood',
      'genre',
      'discovery',
      'trending',
      'underground',
      'recent_artists',
      'deep_cuts',
      'viral_hits',
      'new_releases',
      'indie_gems',
      'chart_toppers',
      'breakout_artists',
      'fresh_finds',
      'hidden_gems'
    ];

    // Use multiple strategies per call for maximum variety (like artists tab)
    final startIndex = (offset * 2) % strategies.length;

    for (int i = 0; i < 4; i++) {
      // Process 4 different strategies
      final strategyIndex = (startIndex + i) % strategies.length;
      final strategy = strategies[strategyIndex];

      print('🎲 Processing strategy: $strategy (index: $strategyIndex)');

      try {
        List<MusicTrack> strategyTracks = [];

        switch (strategy) {
          case 'liked_artists':
            strategyTracks =
                await _getMoreFromLikedArtists(offset + i, context);
            break;
          case 'top_artists':
            strategyTracks = await _getMoreFromTopArtists(offset + i);
            break;
          case 'recent_artists':
            strategyTracks = await _getMoreFromRecentArtists(offset + i);
            break;
          case 'deep_cuts':
            strategyTracks = await _getMoreDeepCuts(offset + i, context);
            break;
          case 'mood':
            strategyTracks = await _getMoreMoodBasedRecommendations(offset + i);
            break;
          case 'genre':
            final topGenres = await _spotifyService.getTopGenres(limit: 5);
            strategyTracks = await _getMoreRecommendationsForGenres(
                topGenres, offset + i, context);
            break;
          case 'discovery':
            strategyTracks = await _getMoreDiscoveryTracks(offset + i, context);
            break;
          case 'trending':
            strategyTracks = await _getTrendingTracks(offset + i);
            break;
          case 'underground':
            strategyTracks = await _getUndergroundTracks(offset + i);
            break;
          case 'viral_hits':
            strategyTracks = await _getViralHits(offset + i);
            break;
          case 'new_releases':
            strategyTracks = await _getNewReleases(offset + i);
            break;
          case 'indie_gems':
            strategyTracks = await _getIndieGems(offset + i);
            break;
          case 'chart_toppers':
            strategyTracks = await _getChartToppers(offset + i);
            break;
          case 'breakout_artists':
            strategyTracks = await _getBreakoutArtists(offset + i);
            break;
          case 'fresh_finds':
            strategyTracks = await _getFreshFinds(offset + i);
            break;
          case 'hidden_gems':
            strategyTracks = await _getHiddenGems(offset + i);
            break;
        }

        allTracks.addAll(strategyTracks);
        print(
            '📥 Got ${strategyTracks.length} tracks from strategy: $strategy');
      } catch (e) {
        print('❌ Error getting tracks for strategy $strategy: $e');
      }
    }

    // If we don't have enough tracks, supplement with liked artists (our most reliable source)
    if (allTracks.length < 20) {
      print('🔄 Supplementing with additional liked artist tracks...');
      final supplementTracks =
          await _getMoreFromLikedArtists(offset + 10, context);
      allTracks.addAll(supplementTracks);
    }

    print('🎲 Total diverse tracks collected: ${allTracks.length}');
    final filteredTracks = _filterAndDeduplicateTracks(allTracks);
    print(
        '✅ Diverse recommendations returning ${filteredTracks.length} tracks after filtering');

    return filteredTracks;
  }

  /// Get more tracks from artists in user's liked songs
  Future<List<MusicTrack>> _getMoreFromLikedArtists(
      int offset, BuildContext context) async {
    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);
      await spotifyProvider.loadLikedSongs();

      final allLikedSongs = spotifyProvider.likedSongs;
      if (allLikedSongs.isEmpty) {
        print('❌ No liked songs available for artist cycling');
        return [];
      }

      // Extract unique artists and cycle through them
      final allArtists =
          allLikedSongs.map((track) => track.artist).toSet().toList();
      final startIndex = (offset * 2) % allArtists.length;
      final allTracks = <MusicTrack>[];

      print(
          '🎵 Liked artists cycling: offset=$offset, startIndex=$startIndex, totalArtists=${allArtists.length}');

      // OPTIMIZATION: Fetch tracks from multiple artists in PARALLEL for much faster performance
      final artistFutures = <Future<List<MusicTrack>>>[];
      final selectedArtists = <String>[];

      for (int i = 0; i < 3; i++) {
        final artistIndex = (startIndex + i) % allArtists.length;
        final artist = allArtists[artistIndex];
        selectedArtists.add(artist);

        artistFutures.add(
          _spotifyService.getArtistTracksHybrid(
            artist,
            limit: 30, // OPTIMIZED: Increased from 15 to 30
            includeAppearsOn: true, // Include collaborations for liked artists
            context: _getSearchContext(),
          ).catchError((e) {
            print('❌ Error getting tracks for artist $artist: $e');
            return <MusicTrack>[];
          })
        );
      }

      print('🔍 Fetching tracks for artists in PARALLEL: ${selectedArtists.join(", ")}');

      // Wait for all artist track fetches to complete in parallel
      final results = await Future.wait(artistFutures);

      for (int i = 0; i < results.length; i++) {
        final tracks = results[i];
        final artist = selectedArtists[i];
        print('📥 Got ${tracks.length} tracks for artist: $artist (PARALLEL)');
        allTracks.addAll(tracks);
      }

      print('✅ Total tracks from liked artists: ${allTracks.length}');
      return allTracks;
    } catch (e) {
      print('❌ Error getting more from liked artists: $e');
      return [];
    }
  }

  /// Get more tracks from user's top artists
  Future<List<MusicTrack>> _getMoreFromTopArtists(int offset) async {
    try {
      final topTracks = await _spotifyService.getTopTracks(limit: 50);
      if (topTracks.isEmpty) return [];

      final topArtists =
          topTracks.map((track) => track.artist).toSet().toList();
      final startIndex = (offset * 2) % topArtists.length;
      final allTracks = <MusicTrack>[];

      // OPTIMIZATION: Fetch tracks from multiple top artists in PARALLEL
      final artistFutures = <Future<List<MusicTrack>>>[];
      final selectedArtists = <String>[];

      for (int i = 0; i < 3; i++) {
        final artistIndex = (startIndex + i) % topArtists.length;
        final artist = topArtists[artistIndex];
        selectedArtists.add(artist);

        artistFutures.add(
          _spotifyService.getArtistTracksHybrid(
            artist,
            limit: 30, // OPTIMIZED: Increased from 15 to 30
            includeAppearsOn: false, // For top artists, focus on main releases
            context: _getSearchContext(),
          ).catchError((e) {
            print('Error getting tracks for top artist $artist: $e');
            return <MusicTrack>[];
          })
        );
      }

      print('🔍 Fetching top artist tracks in PARALLEL: ${selectedArtists.join(", ")}');

      // Wait for all top artist track fetches to complete in parallel
      final results = await Future.wait(artistFutures);

      for (final tracks in results) {
        allTracks.addAll(tracks);
      }

      return allTracks;
    } catch (e) {
      print('Error getting more from top artists: $e');
      return [];
    }
  }

  /// Get more tracks from recently played artists
  Future<List<MusicTrack>> _getMoreFromRecentArtists(int offset) async {
    try {
      final recentTracks = await _spotifyService.getRecentlyPlayed(limit: 50);
      if (recentTracks.isEmpty) return [];

      final recentArtists =
          recentTracks.map((track) => track.artist).toSet().toList();
      final startIndex = (offset * 2) % recentArtists.length;
      final allTracks = <MusicTrack>[];

      // OPTIMIZATION: Fetch tracks from multiple recent artists in PARALLEL
      final artistFutures = <Future<List<MusicTrack>>>[];
      final selectedArtists = <String>[];

      for (int i = 0; i < 3; i++) {
        final artistIndex = (startIndex + i) % recentArtists.length;
        final artist = recentArtists[artistIndex];
        selectedArtists.add(artist);

        artistFutures.add(
          _spotifyService.getArtistTracksHybrid(
            artist,
            limit: 30, // OPTIMIZED: Increased from 15 to 30
            includeAppearsOn:
                false, // Focus on main releases for recent artists
            context: _getSearchContext(),
          ).catchError((e) {
            print('Error getting tracks for recent artist $artist: $e');
            return <MusicTrack>[];
          })
        );
      }

      print('🔍 Fetching recent artist tracks in PARALLEL: ${selectedArtists.join(", ")}');

      // Wait for all recent artist track fetches to complete in parallel
      final results = await Future.wait(artistFutures);

      for (final tracks in results) {
        allTracks.addAll(tracks);
      }

      return allTracks;
    } catch (e) {
      print('Error getting more from recent artists: $e');
      return [];
    }
  }

  /// Get deep cuts and hidden gems from user's artists
  Future<List<MusicTrack>> _getMoreDeepCuts(
      int offset, BuildContext context) async {
    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);
      await spotifyProvider.loadLikedSongs();

      final allLikedSongs = spotifyProvider.likedSongs;
      if (allLikedSongs.isEmpty) return [];

      final allArtists =
          allLikedSongs.map((track) => track.artist).toSet().toList();
      final startIndex = (offset * 2) % allArtists.length;
      final allTracks = <MusicTrack>[];

      for (int i = 0; i < 3; i++) {
        final artistIndex = (startIndex + i) % allArtists.length;
        final artist = allArtists[artistIndex];

        try {
          final deepCutQueries = [
            '$artist deep cuts',
            '$artist rare',
            '$artist b-sides'
          ];
          final query = deepCutQueries[offset % deepCutQueries.length];
          final tracks = await _spotifyService.searchTracks(
            query,
            limit: 15, // OPTIMIZED: Increased from 6 to 15
            context: _getSearchContext(),
          );
          allTracks.addAll(tracks);
        } catch (e) {
          print('Error getting deep cuts for artist $artist: $e');
        }
      }

      return allTracks;
    } catch (e) {
      print('Error getting more deep cuts: $e');
      return [];
    }
  }

  /// Get more discovery tracks for pagination - TRUE discovery of genres user doesn't have
  Future<List<MusicTrack>> _getMoreDiscoveryTracks(
      int offset, BuildContext context) async {
    print('🔍 [DISCOVERY] Getting REAL discovery tracks (offset: $offset)...');

    // Clear discovery cache to prevent endless loops
    _spotifyService.clearSearchCache();

    final allTracks = <MusicTrack>[];

    try {
      // Get genres the user is MISSING for true discovery
      final discoveryGenres = await _getDiscoveryGenres();
      print(
          '🎯 [DISCOVERY] Found ${discoveryGenres.length} discovery genres: $discoveryGenres');

      if (discoveryGenres.isEmpty) {
        print(
            '⚠️ [DISCOVERY] No discovery genres found, using mainstream fallback');
        return await _getMainstreamDiscoveryTracks(offset);
      }

      // Get tracks from genres the user doesn't listen to
      final genreIndex = offset % discoveryGenres.length;
      final discoveryGenre = discoveryGenres[genreIndex];

      print('🎵 [DISCOVERY] Exploring new genre for user: $discoveryGenre');

      // Use different strategies for discovering this genre
      final discoveryStrategies = [
        '$discoveryGenre hits ${DateTime.now().year}',
        '$discoveryGenre popular artists',
        'best $discoveryGenre songs',
        '$discoveryGenre essential tracks',
        '$discoveryGenre mainstream',
        'top $discoveryGenre artists ${DateTime.now().year}',
        '$discoveryGenre chart hits',
        'famous $discoveryGenre songs',
      ];

      // Use different queries based on offset to avoid cache
      final strategyIndex =
          (offset ~/ discoveryGenres.length) % discoveryStrategies.length;
      final queries = [
        discoveryStrategies[strategyIndex],
        discoveryStrategies[(strategyIndex + 1) % discoveryStrategies.length],
        discoveryStrategies[(strategyIndex + 2) % discoveryStrategies.length],
      ];

      for (final query in queries) {
        try {
          print('🔍 [DISCOVERY] Searching: "$query"');
          final tracks = await _spotifyService.searchTracks(
            query,
            limit: 25, // OPTIMIZED: Increased from 12 to 25
            context: 'DISCOVERY_NEW_GENRE',
          );
          allTracks.addAll(tracks);
          print('📥 [DISCOVERY] Got ${tracks.length} tracks for "$query"');
        } catch (e) {
          print('❌ [DISCOVERY] Error with query "$query": $e');
        }
      }

      print('✅ [DISCOVERY] Total discovery tracks: ${allTracks.length}');
      if (allTracks.isEmpty) {
        print(
            '⚠️ [DISCOVERY] No tracks found with primary discovery strategy, using mainstream fallback');
        return await _getMainstreamDiscoveryTracks(offset);
      }
      return _filterAndDeduplicateTracks(allTracks, isDiscovery: true);
    } catch (e) {
      print('❌ [DISCOVERY] Error in discovery logic: $e');
      return await _getMainstreamDiscoveryTracks(offset);
    }
  }

  /// Get genres that the user doesn't listen to for true discovery
  Future<List<String>> _getDiscoveryGenres() async {
    try {
      // All major genres that could be discovered
      final allMajorGenres = [
        'pop',
        'rock',
        'hip hop',
        'r&b',
        'country',
        'electronic',
        'indie',
        'jazz',
        'classical',
        'folk',
        'reggae',
        'latin',
        'blues',
        'punk',
        'metal',
        'alternative',
        'dance',
        'funk',
        'soul',
        'gospel',
        'world music',
        'new age',
        'ambient',
        'experimental',
        'soundtrack',
        'k-pop',
        'j-pop',
        'afrobeat',
        'reggaeton',
        'bossa nova',
        'flamenco',
        'celtic',
      ];

      // Get user's current top genres
      final userGenres = _userTopGenres.map((g) => g.toLowerCase()).toSet();
      print('🎵 [DISCOVERY] User current genres: $userGenres');

      // Find genres user doesn't listen to
      final missingGenres = allMajorGenres.where((genre) {
        // Check if user has this genre or similar genres
        return !userGenres.any((userGenre) =>
            userGenre.contains(genre) ||
            genre.contains(userGenre) ||
            _areGenresRelated(userGenre, genre));
      }).toList();

      // Prioritize popular discovery genres
      final priorityDiscoveryGenres = [
        'pop',
        'country',
        'classical',
        'jazz',
        'latin',
        'k-pop'
      ];
      final priorityMissing = missingGenres
          .where((g) => priorityDiscoveryGenres.contains(g))
          .toList();
      final otherMissing = missingGenres
          .where((g) => !priorityDiscoveryGenres.contains(g))
          .toList();

      // Shuffle for variety
      priorityMissing.shuffle();
      otherMissing.shuffle();

      final discoveryGenres =
          [...priorityMissing, ...otherMissing].take(8).toList();

      print(
          '🎯 [DISCOVERY] Discovery genres (missing from user): $discoveryGenres');
      return discoveryGenres;
    } catch (e) {
      print('❌ [DISCOVERY] Error getting discovery genres: $e');
      return ['pop', 'country', 'classical', 'jazz']; // Safe fallback
    }
  }

  /// Fallback discovery for mainstream hits
  Future<List<MusicTrack>> _getMainstreamDiscoveryTracks(int offset) async {
    print('🌟 [DISCOVERY] Using mainstream discovery fallback');

    final mainstreamQueries = [
      'taylor swift popular',
      'olivia rodrigo hits',
      'katy perry songs',
      'phoebe bridgers best',
      'billie eilish top',
      'harry styles hits',
      'dua lipa popular',
      'the weeknd best',
      'ariana grande hits',
      'ed sheeran popular',
      'post malone hits',
      'drake popular',
    ];

    // Shuffle and pick different queries based on offset
    final shuffledQueries = List<String>.from(mainstreamQueries)..shuffle();
    final startIndex = (offset * 3) % shuffledQueries.length;

    final allTracks = <MusicTrack>[];

    for (int i = 0; i < 4; i++) {
      final queryIndex = (startIndex + i) % shuffledQueries.length;
      final query = shuffledQueries[queryIndex];

      try {
        print('🎵 [DISCOVERY] Mainstream query: "$query"');
        final tracks = await _spotifyService.searchTracks(
          query,
          limit: 20, // OPTIMIZED: Increased from 8 to 20
          context: 'DISCOVERY_MAINSTREAM',
        );
        allTracks.addAll(tracks);
      } catch (e) {
        print('❌ [DISCOVERY] Error with mainstream query "$query": $e');
      }
    }

    return _filterAndDeduplicateTracks(allTracks, isDiscovery: true);
  }

  /// Get viral hits with cycling queries
  Future<List<MusicTrack>> _getViralHits(int offset) async {
    final viralQueries = [
      'viral hits 2024',
      'tiktok viral',
      'viral songs',
      'trending viral',
      'social media hits',
      'viral music',
      'internet famous',
      'meme songs',
    ];

    final allTracks = <MusicTrack>[];
    final startIndex = offset % viralQueries.length;

    for (int i = 0; i < 3; i++) {
      final queryIndex = (startIndex + i) % viralQueries.length;
      final query = viralQueries[queryIndex];

      try {
        final tracks = await _spotifyService.searchTracks(
          query,
          limit: 6,
          context: _getSearchContext(),
        );
        allTracks.addAll(tracks);
      } catch (e) {
        print('Error getting viral hits: $e');
      }
    }

    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Get new releases with cycling queries
  Future<List<MusicTrack>> _getNewReleases(int offset) async {
    final releaseQueries = [
      'new releases 2024',
      'latest songs',
      'new music friday',
      'fresh releases',
      'just dropped',
      'new tracks',
      'recent releases',
      'brand new music',
    ];

    final allTracks = <MusicTrack>[];
    final startIndex = offset % releaseQueries.length;

    for (int i = 0; i < 3; i++) {
      final queryIndex = (startIndex + i) % releaseQueries.length;
      final query = releaseQueries[queryIndex];

      try {
        final tracks = await _spotifyService.searchTracks(
          query,
          limit: 6,
          context: _getSearchContext(),
        );
        allTracks.addAll(tracks);
      } catch (e) {
        print('Error getting new releases: $e');
      }
    }

    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Get indie gems with cycling queries
  Future<List<MusicTrack>> _getIndieGems(int offset) async {
    final indieQueries = [
      'indie gems',
      'indie underground',
      'indie discoveries',
      'hidden indie',
      'indie breakthrough',
      'indie artists',
      'indie music',
      'independent artists',
    ];

    final allTracks = <MusicTrack>[];
    final startIndex = offset % indieQueries.length;

    for (int i = 0; i < 3; i++) {
      final queryIndex = (startIndex + i) % indieQueries.length;
      final query = indieQueries[queryIndex];

      try {
        final tracks = await _spotifyService.searchTracks(
          query,
          limit: 6,
          context: _getSearchContext(),
        );
        allTracks.addAll(tracks);
      } catch (e) {
        print('Error getting indie gems: $e');
      }
    }

    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Get chart toppers with cycling queries
  Future<List<MusicTrack>> _getChartToppers(int offset) async {
    final chartQueries = [
      'top charts 2024',
      'hot 100',
      'chart toppers',
      'number one hits',
      'chart hits',
      'popular charts',
      'billboard hits',
      'mainstream hits',
    ];

    final allTracks = <MusicTrack>[];
    final startIndex = offset % chartQueries.length;

    for (int i = 0; i < 3; i++) {
      final queryIndex = (startIndex + i) % chartQueries.length;
      final query = chartQueries[queryIndex];

      try {
        final tracks = await _spotifyService.searchTracks(
          query,
          limit: 6,
          context: _getSearchContext(),
        );
        allTracks.addAll(tracks);
      } catch (e) {
        print('Error getting chart toppers: $e');
      }
    }

    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Get breakout artists with cycling queries
  Future<List<MusicTrack>> _getBreakoutArtists(int offset) async {
    final breakoutQueries = [
      'breakout artists',
      'rising stars',
      'up and coming',
      'next big thing',
      'breakthrough music',
      'emerging artists',
      'new artists',
      'breakthrough hits',
    ];

    final allTracks = <MusicTrack>[];
    final startIndex = offset % breakoutQueries.length;

    for (int i = 0; i < 3; i++) {
      final queryIndex = (startIndex + i) % breakoutQueries.length;
      final query = breakoutQueries[queryIndex];

      try {
        final tracks = await _spotifyService.searchTracks(
          query,
          limit: 6,
          context: _getSearchContext(),
        );
        allTracks.addAll(tracks);
      } catch (e) {
        print('Error getting breakout artists: $e');
      }
    }

    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Get fresh finds with cycling queries
  Future<List<MusicTrack>> _getFreshFinds(int offset) async {
    final freshQueries = [
      'fresh finds',
      'music discoveries',
      'new discoveries',
      'fresh music',
      'undiscovered gems',
      'fresh sounds',
      'discovery playlist',
      'new finds',
    ];

    final allTracks = <MusicTrack>[];
    final startIndex = offset % freshQueries.length;

    for (int i = 0; i < 3; i++) {
      final queryIndex = (startIndex + i) % freshQueries.length;
      final query = freshQueries[queryIndex];

      try {
        final tracks = await _spotifyService.searchTracks(
          query,
          limit: 6,
          context: _getSearchContext(),
        );
        allTracks.addAll(tracks);
      } catch (e) {
        print('Error getting fresh finds: $e');
      }
    }

    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Get hidden gems with cycling queries
  Future<List<MusicTrack>> _getHiddenGems(int offset) async {
    final hiddenQueries = [
      'hidden gems music',
      'undiscovered artists',
      'secret gems',
      'hidden treasures',
      'overlooked music',
      'underrated songs',
      'deep cuts',
      'rare finds',
    ];

    final allTracks = <MusicTrack>[];
    final startIndex = offset % hiddenQueries.length;

    for (int i = 0; i < 3; i++) {
      final queryIndex = (startIndex + i) % hiddenQueries.length;
      final query = hiddenQueries[queryIndex];

      try {
        final tracks = await _spotifyService.searchTracks(
          query,
          limit: 6,
          context: _getSearchContext(),
        );
        allTracks.addAll(tracks);
      } catch (e) {
        print('Error getting hidden gems: $e');
      }
    }

    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Get trending tracks for pagination
  Future<List<MusicTrack>> _getTrendingTracks(int offset) async {
    final trendingQueries = [
      'charts 2024',
      'viral music',
      'trending hits',
      'popular now',
      'hot tracks',
      'top charts',
    ];

    final allTracks = <MusicTrack>[];
    final query = trendingQueries[offset % trendingQueries.length];

    try {
      final tracks = await _spotifyService.searchTracks(
        query,
        limit: 15,
        context: _getSearchContext(),
      );
      allTracks.addAll(tracks);
    } catch (e) {
      print('Error getting trending tracks: $e');
    }

    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Get underground/indie tracks for pagination
  Future<List<MusicTrack>> _getUndergroundTracks(int offset) async {
    final undergroundQueries = [
      'indie artists',
      'underground music',
      'hidden gems',
      'bedroom pop',
      'lo-fi indie',
      'indie folk',
      'experimental music',
      'emerging artists',
    ];

    final allTracks = <MusicTrack>[];
    final query = undergroundQueries[offset % undergroundQueries.length];

    try {
      final tracks = await _spotifyService.searchTracks(
        query,
        limit: 12,
        context: _getSearchContext(),
      );
      allTracks.addAll(tracks);
    } catch (e) {
      print('Error getting underground tracks: $e');
    }

    return _filterAndDeduplicateTracks(allTracks);
  }

  /// Get more similar tracks to top tracks with pagination
  Future<List<MusicTrack>> _getMoreSimilarToTopTracks(
      int offset, BuildContext context) async {
    print('🏆 Getting similar to top tracks (offset: $offset)...');

    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      if (spotifyProvider.isConnected) {
        // Get user's extensive liked songs for more artist variety
        await spotifyProvider.loadLikedSongs();

        final allLikedSongs = spotifyProvider.likedSongs;
        if (allLikedSongs.isEmpty) {
          print('❌ No liked songs available, using top tracks');
          final topTracks = await _spotifyService.getTopTracks(limit: 20);
          final topArtists =
              topTracks.map((track) => track.artist).toSet().take(10).toList();
          return await _getTracksFromArtistList(
              topArtists, offset, 'top tracks');
        }

        // Use all liked song artists for maximum diversity (like the working artists tab)
        final allArtists =
            allLikedSongs.map((track) => track.artist).toSet().toList();
        print('🏆 Available artists from liked songs: ${allArtists.length}');

        final allSimilarTracks = <MusicTrack>[];

        // Cycle through artists like the successful artists tab method
        final startIndex = (offset * 3) % allArtists.length;

        for (int i = 0; i < 4; i++) {
          // Process 4 artists like artists tab
          final artistIndex = (startIndex + i) % allArtists.length;
          final artist = allArtists[artistIndex];

          print('🏆 Processing artist: $artist (index: $artistIndex)');

          try {
            // Use diverse queries like the successful artists tab
            final queries = [
              '$artist deep cuts',
              '$artist b-sides',
              '$artist rare tracks',
              '$artist unreleased',
              'hidden $artist gems',
              '$artist fan favorites',
              '$artist latest release',
              '$artist acoustic',
              '$artist remix',
              '$artist live',
              '$artist underground',
              '$artist vault tracks',
              '$artist demos',
              '$artist alternate versions',
              '$artist special edition',
            ];

            // Use primary and secondary queries like artists tab
            final primaryQuery = queries[(offset + i) % queries.length];
            final secondaryQuery = queries[(offset + i + 5) % queries.length];

            // Primary query
            final primaryTracks = await _spotifyService.searchTracks(
              primaryQuery,
              limit: 6,
              context: _getSearchContext(),
            );
            allSimilarTracks.addAll(primaryTracks);
            print('📥 Got ${primaryTracks.length} tracks for "$primaryQuery"');

            // Secondary query for more variety
            final secondaryTracks = await _spotifyService.searchTracks(
              secondaryQuery,
              limit: 4,
              context: _getSearchContext(),
            );
            allSimilarTracks.addAll(secondaryTracks);
            print(
                '📥 Got ${secondaryTracks.length} tracks for "$secondaryQuery"');
          } catch (e) {
            print('❌ Error getting tracks for artist $artist: $e');
          }
        }

        print(
            '🏆 Total similar to top tracks collected: ${allSimilarTracks.length}');
        return _filterAndDeduplicateTracks(allSimilarTracks);
      }

      return [];
    } catch (e) {
      print('❌ Error getting more similar to top tracks: $e');
      return [];
    }
  }

  /// Helper method to get tracks from artist list using cached tracks
  /// OPTIMIZED: Intelligent batching and progressive loading for large artist lists (50+)
  Future<List<MusicTrack>> _getTracksFromArtistList(
      List<String> artists, int offset, String context) async {
    print(
        '🎤 [TracksFromArtistList] *** ENTRY *** Processing ${artists.length} artists for $context');
    print('🎤 [TracksFromArtistList] Artists: $artists, offset: $offset');
    print(
        '🎤 [TracksFromArtistList] Current artist cache size: ${_artistTrackCache.length}');

    if (artists.isEmpty) {
      return [];
    }

    // OPTIMIZATION: For large artist lists (30+), use progressive loading strategy
    if (artists.length >= 30) {
      return await _getTracksFromLargeArtistList(artists, offset, context);
    }

    // Standard processing for smaller lists
    return await _getTracksFromStandardArtistList(artists, offset, context);
  }

  /// Optimized processing for large artist lists (30+ artists)
  Future<List<MusicTrack>> _getTracksFromLargeArtistList(
      List<String> artists, int offset, String context) async {
    print('🚀 [Large Artist List] Processing ${artists.length} artists with progressive loading');

    // Phase 1: Get immediate results from cached artists
    final cachedTracks = await _getImmediateTracksFromCache(artists, context);

    // Phase 2: If we have enough cached tracks, return them immediately
    if (cachedTracks.length >= 15) {
      print('✅ [Large Artist List] Returning ${cachedTracks.length} cached tracks immediately');

      // Start background loading for remaining artists
      _startBackgroundArtistLoading(artists, offset, context);

      return cachedTracks.take(20).toList();
    }

    // Phase 3: If not enough cached, do smart batching
    return await _getTracksWithSmartBatching(artists, offset, context);
  }

  /// Standard processing for smaller artist lists (< 30 artists)
  Future<List<MusicTrack>> _getTracksFromStandardArtistList(
      List<String> artists, int offset, String context) async {
    // Determine which artists to process on this page
    final startIndex = (offset * 2) % artists.length;
    final selectedArtists = <String>[];
    for (int i = 0; i < math.min(_artistsPerPage, artists.length); i++) {
      final artistIndex = (startIndex + i) % artists.length;
      selectedArtists.add(artists[artistIndex]);
    }

    print(
        '🎤 [Standard List] Selected artists for concurrent fetch: $selectedArtists');

    // Fetch each artist's tracks concurrently
    final fetchFutures = selectedArtists.map((artist) async {
      final artistStopwatch = Stopwatch()..start();
      try {
        print(
            '🎤 [Standard List] Fetching tracks for artist: "$artist"...');
        final tracks =
            await _getCachedArtistTracks(artist, limit: _tracksPerArtist);
        print(
            '📥 Got ${tracks.length} tracks for $context artist: $artist in ${artistStopwatch.elapsedMilliseconds}ms');
        return tracks;
      } catch (e) {
        print(
            '❌ Error getting cached tracks for $context artist $artist: $e');
        return <MusicTrack>[];
      } finally {
        artistStopwatch.stop();
      }
    }).toList();

    final results = await Future.wait(fetchFutures);
    final allTracks = results.expand((list) => list).toList();

    print(
        '🎤 [Standard List] Total tracks before filtering: ${allTracks.length}');
    final filtered = _filterAndDeduplicateTracks(allTracks);
    print(
        '🎤 [Standard List] Total tracks after filtering: ${filtered.length}');
    return filtered;
  }

  /// Get immediate tracks from cache for fast initial response
  Future<List<MusicTrack>> _getImmediateTracksFromCache(
      List<String> artists, String context) async {
    final cachedTracks = <MusicTrack>[];
    final now = DateTime.now();

    for (final artist in artists) {
      final artistKey = artist.toLowerCase().trim();

      // Check if we have valid cached data
      if (_artistTrackCache.containsKey(artistKey) &&
          _artistCacheTimestamps.containsKey(artistKey)) {
        final cacheTime = _artistCacheTimestamps[artistKey]!;
        if (now.difference(cacheTime) < _artistCacheExpiry) {
          final artistTracks = List<MusicTrack>.from(_artistTrackCache[artistKey]!);
          artistTracks.shuffle();
          cachedTracks.addAll(artistTracks.take(4)); // Take 4 tracks per cached artist
        }
      }

      // Stop if we have enough for immediate response
      if (cachedTracks.length >= 20) break;
    }

    print('⚡ [Cache] Found ${cachedTracks.length} immediate tracks from cache');
    return cachedTracks;
  }

  /// Start background loading for remaining artists
  void _startBackgroundArtistLoading(List<String> artists, int offset, String context) {
    // Cancel any existing background loading
    _artistBatchTimer?.cancel();

    // Start background loading after a short delay
    _artistBatchTimer = Timer(const Duration(milliseconds: 500), () async {
      try {
        print('🔄 [Background] Starting background artist loading for ${artists.length} artists');

        // Process uncached artists in background
        final uncachedArtists = artists.where((artist) {
          final artistKey = artist.toLowerCase().trim();
          return !_artistTrackCache.containsKey(artistKey);
        }).toList();

        if (uncachedArtists.isNotEmpty) {
          await _getTracksWithSmartBatching(uncachedArtists, offset, context);
          print('✅ [Background] Completed background loading for ${uncachedArtists.length} artists');
        }
      } catch (e) {
        print('❌ [Background] Error in background artist loading: $e');
      }
    });
  }

  /// Smart batching for efficient artist processing
  Future<List<MusicTrack>> _getTracksWithSmartBatching(
      List<String> artists, int offset, String context) async {
    print('🧠 [Smart Batch] Processing ${artists.length} artists with intelligent batching');

    final allTracks = <MusicTrack>[];
    final batchSize = math.min(_maxArtistBatchSize, artists.length);

    // Process artists in batches
    for (int i = 0; i < artists.length; i += batchSize) {
      final batch = artists.skip(i).take(batchSize).toList();
      print('📦 [Smart Batch] Processing batch ${(i ~/ batchSize) + 1}: ${batch.length} artists');

      // Process batch concurrently
      final batchFutures = batch.map((artist) async {
        try {
          final tracks = await _getCachedArtistTracks(artist, limit: 15); // OPTIMIZED: Increased from 6 to 15 for better batching results
          return tracks;
        } catch (e) {
          print('❌ [Smart Batch] Error processing artist $artist: $e');
          return <MusicTrack>[];
        }
      }).toList();

      final batchResults = await Future.wait(batchFutures);
      final batchTracks = batchResults.expand((list) => list).toList();
      allTracks.addAll(batchTracks);

      print('📥 [Smart Batch] Batch completed: ${batchTracks.length} tracks');

      // Add small delay between batches to prevent overwhelming the API
      if (i + batchSize < artists.length) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }

    final filtered = _filterAndDeduplicateTracks(allTracks);
    print('✅ [Smart Batch] Completed processing: ${filtered.length} tracks after filtering');
    return filtered;
  }

  /// Get quick cached results for immediate display with large artist lists
  Future<List<MusicTrack>> _getQuickCachedResults(BuildContext context) async {
    print('⚡ [Quick Cache] Getting immediate results from cache');
    final cachedTracks = <MusicTrack>[];
    final now = DateTime.now();

    // Get tracks from recently cached artists
    final recentArtists = <String>[];
    _artistCacheTimestamps.forEach((artist, timestamp) {
      if (now.difference(timestamp) < const Duration(hours: 2)) {
        recentArtists.add(artist);
      }
    });

    // Sort by most recent first
    recentArtists.sort((a, b) {
      final timeA = _artistCacheTimestamps[a]!;
      final timeB = _artistCacheTimestamps[b]!;
      return timeB.compareTo(timeA);
    });

    // Get tracks from top 10 most recently cached artists
    for (final artist in recentArtists.take(10)) {
      final artistTracks = _artistTrackCache[artist];
      if (artistTracks != null && artistTracks.isNotEmpty) {
        final shuffledTracks = List<MusicTrack>.from(artistTracks);
        shuffledTracks.shuffle();
        cachedTracks.addAll(shuffledTracks.take(3)); // 3 tracks per artist
      }

      if (cachedTracks.length >= 20) break;
    }

    // If not enough cached tracks, supplement with liked songs
    if (cachedTracks.length < 15) {
      try {
        final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
        final likedSongs = spotifyProvider.likedSongs;
        if (likedSongs.isNotEmpty) {
          final shuffledLiked = List<MusicTrack>.from(likedSongs);
          shuffledLiked.shuffle();
          cachedTracks.addAll(shuffledLiked.take(20 - cachedTracks.length));
        }
      } catch (e) {
        print('❌ [Quick Cache] Error supplementing with liked songs: $e');
      }
    }

    final filtered = _filterAndDeduplicateTracks(cachedTracks);
    print('⚡ [Quick Cache] Returning ${filtered.length} quick cached tracks');
    return filtered;
  }

  /// Get more similar tracks to liked songs with pagination
  Future<List<MusicTrack>> _getMoreSimilarToLikedSongs(
      int offset, BuildContext context) async {
    print('❤️ Getting similar to liked songs (offset: $offset)...');

    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      if (spotifyProvider.isConnected) {
        await spotifyProvider.loadLikedSongs(); // Ensure we have liked songs

        final allLikedSongs = spotifyProvider.likedSongs;
        if (allLikedSongs.isEmpty) {
          print('❌ No liked songs available');
          return [];
        }

        // Use the same successful approach as artists tab - focus on artists from liked songs
        final allArtists =
            allLikedSongs.map((track) => track.artist).toSet().toList();
        print('❤️ Available artists from liked songs: ${allArtists.length}');

        final allSimilarTracks = <MusicTrack>[];

        // Cycle through artists like the successful artists tab method
        final startIndex = (offset * 3) % allArtists.length;

        for (int i = 0; i < 4; i++) {
          // Process 4 artists like artists tab
          final artistIndex = (startIndex + i) % allArtists.length;
          final artist = allArtists[artistIndex];

          print('❤️ Processing artist: $artist (index: $artistIndex)');

          try {
            // Use diverse queries like the successful artists tab, but focused on discovery
            final queries = [
              'artist:"$artist" new songs',
              '$artist similar artists',
              'fans of $artist',
              '$artist collaborations',
              'recommended for $artist listeners',
              'like $artist music',
              '$artist style',
              'inspired by $artist',
              '$artist influenced',
              'sounds like $artist',
              '$artist genre artists',
              '$artist radio',
              '$artist station',
              'discover $artist',
              '$artist recommendations',
            ];

            // Use primary and secondary queries like artists tab
            final primaryQuery = queries[(offset + i) % queries.length];
            final secondaryQuery = queries[(offset + i + 5) % queries.length];

            // Primary query
            final primaryTracks = await _spotifyService.searchTracks(
              primaryQuery,
              limit: 6,
              context: _getSearchContext(),
            );
            allSimilarTracks.addAll(primaryTracks);
            print('📥 Got ${primaryTracks.length} tracks for "$primaryQuery"');

            // Secondary query for more variety
            final secondaryTracks = await _spotifyService.searchTracks(
              secondaryQuery,
              limit: 4,
              context: _getSearchContext(),
            );
            allSimilarTracks.addAll(secondaryTracks);
            print(
                '📥 Got ${secondaryTracks.length} tracks for "$secondaryQuery"');
          } catch (e) {
            print('❌ Error getting tracks for liked artist $artist: $e');
          }
        }

        print(
            '❤️ Total similar to liked songs collected: ${allSimilarTracks.length}');
        return _filterAndDeduplicateTracks(allSimilarTracks);
      }
      return [];
    } catch (e) {
      print('❌ Error getting more similar to liked songs: $e');
      return [];
    }
  }



  // Cache for recently played data to ensure consistency across pagination
  static List<MusicTrack>? _cachedRecentlyPlayed;
  static DateTime? _recentlyPlayedCacheTime;

  // Cache for recently added data to ensure consistency across pagination
  static List<MusicTrack>? _cachedRecentlyAdded;
  static DateTime? _recentlyAddedCacheTime;

  /// Get more similar tracks to recently added with pagination
  Future<List<MusicTrack>> _getMoreSimilarToRecentlyAdded(
      int offset, BuildContext context) async {
    print('🕒 Getting similar to recently added (offset: $offset)...');

    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);
      List<MusicTrack> recentlyAddedTracks = [];

      // 🚀 ENHANCEMENT: Clear search cache periodically to prevent endless cached results
      if (offset > 0 && offset % 5 == 0) {
        print(
            '🧹 [Recently Added] Clearing search cache at offset $offset for fresh results');
        _spotifyService.clearSearchCache();
      }

      // 🚀 ENHANCEMENT: Use cached recently added data for consistency across pagination
      final now = DateTime.now();
      final cacheValid = _cachedRecentlyAdded != null &&
          _recentlyAddedCacheTime != null &&
          now.difference(_recentlyAddedCacheTime!).inMinutes <
              15; // 15 minute cache

      if (cacheValid) {
        recentlyAddedTracks = _cachedRecentlyAdded!;
        print(
            '🕒 Using cached recently added data (${recentlyAddedTracks.length} tracks)');
      } else {
        print(
            '🕒 Cache expired or missing, fetching fresh recently added data...');

        if (spotifyProvider.isConnected) {
          final result =
              await _spotifyService.getSavedTracks(limit: 50, offset: 0);
          recentlyAddedTracks = result['tracks'] as List<MusicTrack>? ?? [];
        }

        // Cache the data
        _cachedRecentlyAdded = recentlyAddedTracks;
        _recentlyAddedCacheTime = now;
        print('🕒 Cached ${recentlyAddedTracks.length} recently added tracks');
      }

      if (recentlyAddedTracks.isEmpty) {
        print('❌ No recently added tracks available');
        return [];
      }

      // For pagination, use the cached tracks and cycle through artists
      final allSimilarTracks = <MusicTrack>[];

      // Extract unique artists from recently added and sort by frequency for consistency
      final artistFrequency = <String, int>{};
      for (final track in recentlyAddedTracks) {
        artistFrequency[track.artist] =
            (artistFrequency[track.artist] ?? 0) + 1;
      }

      // Sort artists by frequency (most frequent first) for consistent pagination
      final recentlyAddedArtists = artistFrequency.keys.toList()
        ..sort((a, b) => artistFrequency[b]!.compareTo(artistFrequency[a]!));

      print(
          '🕒 Found ${recentlyAddedArtists.length} unique artists from recently added (sorted by frequency): ${recentlyAddedArtists.take(7).join(", ")}');

      // 🚀 ENHANCEMENT: Use expanded query variety for infinite scrolling with proper offset variation
      final startIndex = (offset * 3) %
          recentlyAddedArtists
              .length; // Increased multiplier for more variation

      for (int i = 0; i < math.min(8, recentlyAddedArtists.length); i++) {
        // Increased from 6 to 8 for more variety
        final artistIndex = (startIndex + i) % recentlyAddedArtists.length;
        final artist = recentlyAddedArtists[artistIndex];

        print(
            '🕒 Processing recently added artist: $artist (index: $artistIndex)');

        try {
          // 🚀 ENHANCEMENT: Much more diverse queries for infinite variety
          final expandedQueries = [
            'artist:"$artist" popular',
            'artist:"$artist" hits',
            '$artist new songs',
            '$artist best tracks',
            '$artist latest release',
            '$artist similar artists',
            '$artist radio',
            '$artist deep cuts',
            '$artist collaborations',
            'fans of $artist',
            'like $artist music',
            '$artist style music',
            '$artist inspired by',
            '$artist remix',
            'sounds like $artist',
            '$artist radio station',
            '$artist discoveries',
            '$artist underground',
            '$artist rare tracks',
            '$artist b-sides',
            '$artist alternative',
            '$artist covers',
            '$artist live',
            '$artist acoustic',
            '$artist unreleased',
          ];

          // 🚀 ENHANCEMENT: Use offset-based query selection to ensure different queries each time
          final queryStart = (offset * 7 + i * 5) %
              expandedQueries
                  .length; // Different multipliers for more variation
          for (int q = 0; q < 3; q++) {
            // 3 queries per artist
            final queryIndex = (queryStart + q) % expandedQueries.length;
            final query = expandedQueries[queryIndex];

            try {
              // 🚀 ENHANCEMENT: Add small random offset to search results for variety
              final searchOffset =
                  (offset + q) % 3; // Small variation in search offset
              final tracks = await _spotifyService.searchTracks(
                query,
                limit: 4,
                offset:
                    searchOffset * 20, // Use offset to get different results
              );
              allSimilarTracks.addAll(tracks);
              print(
                  '📥 Got ${tracks.length} tracks for "$query" (searchOffset: $searchOffset)');
            } catch (e) {
              print('❌ Error with query "$query": $e');
            }
          }
        } catch (e) {
          print('❌ Error getting tracks for recently added artist $artist: $e');
        }
      }

      print(
          '🕒 Total similar to recently added collected: ${allSimilarTracks.length}');

      // 🚀 ENHANCEMENT: Use relaxed filtering for infinite scrolling (discovery mode)
      return _filterAndDeduplicateTracks(allSimilarTracks, isDiscovery: true);
    } catch (e) {
      print('❌ Error getting more similar to recently added: $e');
      return [];
    }
  }

  /// Get more similar tracks to recently played with pagination
  Future<List<MusicTrack>> _getMoreSimilarToRecentlyPlayed(
      int offset, BuildContext context) async {
    print('🎵 Getting similar to recently played (offset: $offset)...');

    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);
      List<MusicTrack> recentTracks = [];

      // 🚀 ENHANCEMENT: Clear search cache periodically to prevent endless cached results
      if (offset > 0 && offset % 5 == 0) {
        print(
            '🧹 [Recently Played] Clearing search cache at offset $offset for fresh results');
        _spotifyService.clearSearchCache();
      }

      // 🚀 ENHANCEMENT: Cache recently played data for consistency across pagination
      final now = DateTime.now();
      final cacheValid = _cachedRecentlyPlayed != null &&
          _recentlyPlayedCacheTime != null &&
          now.difference(_recentlyPlayedCacheTime!).inMinutes <
              10; // 10 minute cache

      if (cacheValid) {
        recentTracks = _cachedRecentlyPlayed!;
        print(
            '🎵 Using cached recently played data (${recentTracks.length} tracks)');
      } else {
        print('🎵 Fetching fresh recently played data...');

        if (spotifyProvider.isConnected) {
          recentTracks = await _spotifyService.getRecentlyPlayed(limit: 50);
        }

        // Cache the data
        _cachedRecentlyPlayed = recentTracks;
        _recentlyPlayedCacheTime = now;
        print('🎵 Cached ${recentTracks.length} recently played tracks');
      }

      if (recentTracks.isEmpty) {
        print('❌ No recently played tracks available');
        return [];
      }

      final allSimilarTracks = <MusicTrack>[];

      // Extract unique artists from recently played and sort by frequency for consistency
      final artistFrequency = <String, int>{};
      for (final track in recentTracks) {
        artistFrequency[track.artist] =
            (artistFrequency[track.artist] ?? 0) + 1;
      }

      // Sort artists by frequency (most played first) for consistent pagination
      final recentArtists = artistFrequency.keys.toList()
        ..sort((a, b) => artistFrequency[b]!.compareTo(artistFrequency[a]!));

      print(
          '🎵 Found ${recentArtists.length} unique artists from recently played (sorted by frequency): ${recentArtists.take(7).join(", ")}');

      // 🚀 ENHANCEMENT: Use Last.fm for better similar artist discovery
      final lastFmService = LastFmService();

      if (lastFmService.isConfigured && recentArtists.isNotEmpty) {
        print(
            '🎵 [Recent + Last.fm] Using Last.fm to expand recently played artists...');

        try {
          final expandedArtists = <String>[];

          // Get similar artists for top recently played artists (consistent across pagination)
          final seedArtists =
              recentArtists.take(3).toList(); // Always use same seed artists

          for (final artist in seedArtists) {
            final similarArtists =
                await lastFmService.getSimilarArtists(artist, limit: 8);
            expandedArtists.addAll(similarArtists);
            print(
                '🎯 [Recent + Last.fm] Found ${similarArtists.length} similar artists for: $artist');
          }

          // Combine original and similar artists, prioritizing original
          final allArtistsToSearch = <String>[];
          allArtistsToSearch.addAll(recentArtists
              .take(8)); // Prioritize original recently played artists
          allArtistsToSearch
              .addAll(expandedArtists.take(6)); // Add some similar artists

          print(
              '🎯 [Recent + Last.fm] Total artists pool: ${allArtistsToSearch.length}');

          // 🚀 ENHANCEMENT: Use proper offset variation for infinite scrolling
          final startIndex = (offset * 3) %
              allArtistsToSearch
                  .length; // Increased multiplier for more variation

          for (int i = 0; i < math.min(6, allArtistsToSearch.length); i++) {
            // Increased from 4 to 6
            final artistIndex = (startIndex + i) % allArtistsToSearch.length;
            final artist = allArtistsToSearch[artistIndex];

            print('🎵 Processing artist: $artist (index: $artistIndex)');

            try {
              // 🚀 ENHANCEMENT: Expanded query variety for infinite scrolling
              final expandedQueries = [
                'artist:"$artist" popular',
                'artist:"$artist" hits',
                '$artist new songs',
                '$artist best tracks',
                '$artist latest release',
                '$artist similar artists',
                '$artist radio',
                '$artist deep cuts',
                '$artist collaborations',
                'fans of $artist',
                'like $artist music',
                '$artist style music',
                '$artist inspired by',
                '$artist remix',
                'sounds like $artist',
              ];

              // Use offset-based query selection to ensure different queries each time
              final queryStart = (offset * 5 + i * 3) %
                  expandedQueries.length; // Different multipliers for variation
              for (int q = 0; q < 2; q++) {
                // 2 queries per artist
                final queryIndex = (queryStart + q) % expandedQueries.length;
                final query = expandedQueries[queryIndex];

                try {
                  // Add small search offset for variety
                  final searchOffset = (offset + q) % 3;
                  final tracks = await _spotifyService.searchTracks(
                    query,
                    limit: 6,
                    offset: searchOffset * 20,
                  );
                  allSimilarTracks.addAll(tracks);
                  print(
                      '📥 Got ${tracks.length} tracks for "$query" (searchOffset: $searchOffset)');
                } catch (e) {
                  print('❌ Error with query "$query": $e');
                }
              }
            } catch (e) {
              print('❌ Error getting tracks for artist $artist: $e');
            }
          }
        } catch (e) {
          print(
              '❌ [Recent + Last.fm] Error using Last.fm: $e, falling back to basic search');
          await _performBasicRecentlyPlayedSearch(
              recentArtists, offset, allSimilarTracks);
        }
      } else {
        print('⚠️ [Recent] Last.fm not configured, using basic artist search');
        await _performBasicRecentlyPlayedSearch(
            recentArtists, offset, allSimilarTracks);
      }

      print(
          '🎵 Total similar to recently played collected: ${allSimilarTracks.length}');
      // Use relaxed filtering (discovery mode) so we don't prematurely exhaust new content
      return _filterAndDeduplicateTracks(allSimilarTracks, isDiscovery: true);
    } catch (e) {
      print('❌ Error getting more similar to recently played: $e');
      return [];
    }
  }

  /// Fallback method for basic recently played search
  Future<void> _performBasicRecentlyPlayedSearch(List<String> recentArtists,
      int offset, List<MusicTrack> allSimilarTracks) async {
    // 🚀 ENHANCEMENT: Use proper offset variation for infinite scrolling
    final startIndex =
        (offset * 3) % recentArtists.length; // Increased multiplier

    for (int i = 0; i < math.min(6, recentArtists.length); i++) {
      // Increased from 4 to 6
      final artistIndex = (startIndex + i) % recentArtists.length;
      final artist = recentArtists[artistIndex];

      try {
        // 🚀 ENHANCEMENT: Expanded query variety for infinite scrolling
        final expandedQueries = [
          'artist:"$artist" popular',
          'artist:"$artist" hits',
          '$artist new songs',
          '$artist best tracks',
          '$artist latest release',
          '$artist similar artists',
          '$artist radio',
          '$artist deep cuts',
          '$artist collaborations',
          'fans of $artist',
          'like $artist music',
          '$artist style music',
        ];

        // Use offset-based query selection to ensure different queries each time
        final queryStart = (offset * 4 + i * 2) % expandedQueries.length;
        for (int q = 0; q < 2; q++) {
          // 2 queries per artist
          final queryIndex = (queryStart + q) % expandedQueries.length;
          final query = expandedQueries[queryIndex];

          try {
            // Add small search offset for variety
            final searchOffset = (offset + q) % 3;
            final tracks = await _spotifyService.searchTracks(
              query,
              limit: 5,
              offset: searchOffset * 20,
            );
            allSimilarTracks.addAll(tracks);
            print(
                '📥 Got ${tracks.length} tracks for "$query" (searchOffset: $searchOffset)');
          } catch (e) {
            print('❌ Error with query "$query": $e');
          }
        }
      } catch (e) {
        print('❌ Error getting tracks for recently played artist $artist: $e');
      }
    }
  }

  /// Get similar tracks to user's top tracks instead of the top tracks themselves
  Future<List<MusicTrack>> _getSimilarToTopTracks(BuildContext context) async {
    // Only use the artists from the user's current top tracks as seeds.
    return _retryWithBackoff(() async {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);
      List<MusicTrack> topTracks = [];

      if (spotifyProvider.isConnected) {
        topTracks = await _spotifyService.getTopTracks(limit: 10);
      }

      if (topTracks.isEmpty) return [];

      // Keep a small set of the original tracks so the user still recognises their favourites
      final sprinkleOriginal = topTracks.take(3).toList();

      // Build a unique list of seed artists from the top tracks
      final seedArtists = topTracks.map((t) => t.artist).toSet().toList();

      final allSimilarTracks = <MusicTrack>[];

      // Generate similar tracks strictly using those seed artists
      for (final artist in seedArtists.take(5)) {
        try {
          final queries = [
            '$artist similar songs',
            'songs like $artist',
            '$artist fans also like',
          ];

          for (final query in queries) {
            final similar = await _spotifyService.searchTracks(
              query,
              limit: 4,
              context: _getSearchContext(),
            );
            allSimilarTracks.addAll(similar);
          }
        } catch (e) {
          print('Error getting similar tracks for artist $artist: $e');
        }
      }

      // First deduplicate all tracks (similar + originals combined)
      final cleaned = _filterAndDeduplicateTracks(
          [...allSimilarTracks, ...sprinkleOriginal]);

      // Create sets for efficient duplicate checking during sprinkling
      final cleanedIds = cleaned.map((t) => t.id).toSet();
      final cleanedTitleArtist = cleaned
          .map((t) => '${t.title.toLowerCase()}_${t.artist.toLowerCase()}')
          .toSet();

      // Interleave original top tracks every 6 positions, but avoid duplicates
      final withSprinkles = <MusicTrack>[];
      int sprinkleIndex = 0;

      for (int i = 0; i < cleaned.length; i++) {
        // Try to add a sprinkle track if it's time and we have more to add
        if (i % 6 == 0 && sprinkleIndex < sprinkleOriginal.length) {
          final sprinkleTrack = sprinkleOriginal[sprinkleIndex];
          final sprinkleTitleArtist =
              '${sprinkleTrack.title.toLowerCase()}_${sprinkleTrack.artist.toLowerCase()}';

          // Only add if it's not already in our current list
          if (!cleanedIds.contains(sprinkleTrack.id) &&
              !cleanedTitleArtist.contains(sprinkleTitleArtist)) {
            withSprinkles.add(sprinkleTrack);
            cleanedIds.add(sprinkleTrack.id);
            cleanedTitleArtist.add(sprinkleTitleArtist);
          }
          sprinkleIndex++;
        }

        // Add the regular track if it's not a duplicate
        final track = cleaned[i];
        final trackTitleArtist =
            '${track.title.toLowerCase()}_${track.artist.toLowerCase()}';
        if (!withSprinkles.any((t) =>
            t.id == track.id ||
            '${t.title.toLowerCase()}_${t.artist.toLowerCase()}' ==
                trackTitleArtist)) {
          withSprinkles.add(track);
        }
      }

      return withSprinkles;
    }, 'top tracks');
  }

  /// Get similar tracks to user's liked songs instead of the liked songs themselves
  Future<List<MusicTrack>> _getSimilarToLikedSongs(BuildContext context) async {
    return _retryWithBackoff(() async {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);
      List<MusicTrack> likedSongs = [];

      if (spotifyProvider.isConnected) {
        print('🎵 [Spotify] Getting liked songs for recommendations...');
        await spotifyProvider.loadLikedSongs();
        likedSongs = spotifyProvider.likedSongs.take(15).toList();
        print('🎵 [Spotify] Got ${likedSongs.length} liked songs');
      }

      if (likedSongs.isEmpty) {
        print('❌ No liked songs available for recommendations');
        return [];
      }

      print(
          '🎯 Processing ${likedSongs.length} liked songs for similar track discovery...');

      // 🚀 ENHANCEMENT: Use Last.fm for Apple Music users to get better recommendations
      final lastFmService = LastFmService();
      final allSimilarTracks = <MusicTrack>[];

      // Extract unique artists from liked songs
      final likedArtists =
          likedSongs.map((track) => track.artist).toSet().toList();
      print(
          '🎤 Extracted ${likedArtists.length} unique artists from liked songs');

      // Standard search for liked songs
      await _performBasicLikedSongsSearch(likedSongs, allSimilarTracks);

      // Keep a sprinkle of originals for familiarity
      final sprinkleOriginal = likedSongs.take(4).toList();
      final cleaned = _filterAndDeduplicateTracks(
          [...allSimilarTracks, ...sprinkleOriginal]);

      // Create sets for efficient duplicate checking during sprinkling
      final cleanedIds = cleaned.map((t) => t.id).toSet();
      final cleanedTitleArtist = cleaned
          .map((t) => '${t.title.toLowerCase()}_${t.artist.toLowerCase()}')
          .toSet();

      // Interleave original liked songs every 8 items, but avoid duplicates
      final withSprinkles = <MusicTrack>[];
      int sprinkleIndex = 0;

      for (int i = 0; i < cleaned.length; i++) {
        // Try to add a sprinkle track if it's time and we have more to add
        if (i % 8 == 0 && sprinkleIndex < sprinkleOriginal.length) {
          final sprinkleTrack = sprinkleOriginal[sprinkleIndex];
          final sprinkleTitleArtist =
              '${sprinkleTrack.title.toLowerCase()}_${sprinkleTrack.artist.toLowerCase()}';

          // Only add if it's not already in our current list
          if (!cleanedIds.contains(sprinkleTrack.id) &&
              !cleanedTitleArtist.contains(sprinkleTitleArtist)) {
            withSprinkles.add(sprinkleTrack);
            cleanedIds.add(sprinkleTrack.id);
            cleanedTitleArtist.add(sprinkleTitleArtist);
          }
          sprinkleIndex++;
        }

        // Add the regular track if it's not a duplicate
        final track = cleaned[i];
        final trackTitleArtist =
            '${track.title.toLowerCase()}_${track.artist.toLowerCase()}';
        if (!withSprinkles.any((t) =>
            t.id == track.id ||
            '${t.title.toLowerCase()}_${t.artist.toLowerCase()}' ==
                trackTitleArtist)) {
          withSprinkles.add(track);
        }
      }

      print(
          '✅ Final liked songs recommendations: ${withSprinkles.length} tracks');
      return withSprinkles;
    }, 'liked songs');
  }

  /// Perform basic liked songs search (fallback method)
  Future<void> _performBasicLikedSongsSearch(
      List<MusicTrack> likedSongs, List<MusicTrack> allSimilarTracks) async {
    print('🔍 Using basic search for liked songs recommendations...');

    for (final track in likedSongs.take(7)) {
      try {
        final queries = [
          'songs like ${track.title}',
          '${track.artist} similar songs',
          'fans of ${track.artist}',
          '${track.artist} popular',
        ];

        for (final query in queries.take(2)) {
          // Reduced to 2 queries to avoid too many API calls
          final similar = await _spotifyService.searchTracks(
            query,
            limit: 3,
            context: _getSearchContext(),
          );
          allSimilarTracks.addAll(similar);
        }
      } catch (e) {
        print(
            '❌ Error getting similar tracks for liked song ${track.title}: $e');
      }
    }

    print('✅ Basic search got ${allSimilarTracks.length} similar tracks');
  }

  /// Get similar tracks to user's recently played instead of the recently played themselves
  Future<List<MusicTrack>> _getSimilarToRecentlyPlayed(
      BuildContext context) async {
    return _retryWithBackoff(() async {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);
      List<MusicTrack> recentTracks = [];

      if (spotifyProvider.isConnected) {
        recentTracks = await _spotifyService.getRecentlyPlayed(limit: 50);
      }

      if (recentTracks.isEmpty) return [];

      // Count occurrences to find significant repeats
      final Map<String, int> playCounts = {};
      for (final t in recentTracks) {
        playCounts[t.id] = (playCounts[t.id] ?? 0) + 1;
      }

      // Consider tracks played more than once as significant
      final significant =
          recentTracks.where((t) => (playCounts[t.id] ?? 0) > 1).toList();

      // If none repeated, fallback to latest 5
      final seedTracks = significant.isNotEmpty
          ? significant.take(10).toList()
          : recentTracks.take(5).toList();

      // Sprinkle originals
      final sprinkleOriginal = seedTracks.take(3).toList();

      final allSimilarTracks = <MusicTrack>[];
      for (final track in seedTracks) {
        try {
          final queries = [
            'tracks like ${track.title}',
            '${track.artist} new release',
            'similar to ${track.artist}',
          ];
          for (final q in queries) {
            final similar = await _spotifyService.searchTracks(
              q,
              limit: 3,
              context: _getSearchContext(),
            );
            allSimilarTracks.addAll(similar);
          }
        } catch (e) {
          print(
              'Error getting similar tracks for recent song ${track.title}: $e');
        }
      }

      final cleaned = _filterAndDeduplicateTracks(
          [...allSimilarTracks, ...sprinkleOriginal]);

      // Create sets for efficient duplicate checking during sprinkling
      final cleanedIds = cleaned.map((t) => t.id).toSet();
      final cleanedTitleArtist = cleaned
          .map((t) => '${t.title.toLowerCase()}_${t.artist.toLowerCase()}')
          .toSet();

      // Interleave originals every 10 items, but avoid duplicates
      final withSprinkles = <MusicTrack>[];
      int sprinkleIndex = 0;

      for (int i = 0; i < cleaned.length; i++) {
        // Try to add a sprinkle track if it's time and we have more to add
        if (i % 10 == 0 && sprinkleIndex < sprinkleOriginal.length) {
          final sprinkleTrack = sprinkleOriginal[sprinkleIndex];
          final sprinkleTitleArtist =
              '${sprinkleTrack.title.toLowerCase()}_${sprinkleTrack.artist.toLowerCase()}';

          // Only add if it's not already in our current list
          if (!cleanedIds.contains(sprinkleTrack.id) &&
              !cleanedTitleArtist.contains(sprinkleTitleArtist)) {
            withSprinkles.add(sprinkleTrack);
            cleanedIds.add(sprinkleTrack.id);
            cleanedTitleArtist.add(sprinkleTitleArtist);
          }
          sprinkleIndex++;
        }

        // Add the regular track if it's not a duplicate
        final track = cleaned[i];
        final trackTitleArtist =
            '${track.title.toLowerCase()}_${track.artist.toLowerCase()}';
        if (!withSprinkles.any((t) =>
            t.id == track.id ||
            '${t.title.toLowerCase()}_${t.artist.toLowerCase()}' ==
                trackTitleArtist)) {
          withSprinkles.add(track);
        }
      }

      return withSprinkles;
    }, 'recently played');
  }

  /// Filter out excluded tracks and remove duplicates using AI service intelligent filtering
  List<MusicTrack> _filterAndDeduplicateTracks(List<MusicTrack> tracks,
      {bool isDiscovery = false}) {
    if (tracks.isEmpty) return [];

    print('🧹 [Dedup] Starting deduplication of ${tracks.length} tracks...');

    // Use AI service intelligent filtering for better deduplication
    List<MusicTrack> intelligentlyFiltered;
    try {
      intelligentlyFiltered = _aiRecommendationService
          .filterAndDeduplicateTracksIntelligent(tracks);
      print(
          '🧹 [Local Filter] AI intelligent filtering: ${tracks.length} → ${intelligentlyFiltered.length} tracks');
    } catch (e) {
      print('⚠️ [Local Filter] AI filtering failed, using fallback: $e');
      intelligentlyFiltered = tracks; // Fallback to original tracks
    }

    // For discovery mode, use more relaxed filtering to keep content fresh
    final minDuration = isDiscovery ? 20 : 30; // seconds
    final maxDuration = isDiscovery ? 600 : 480; // seconds (10 vs 8 minutes)

    final seenIds = <String>{};
    final seenTitleArtist = <String>{};
    final filteredTracks = <MusicTrack>[];
    final recentTrackIdSet =
        _recentTrackIds.toSet(); // Convert to set for O(1) lookup

    int duplicateIdCount = 0;
    int duplicateTitleArtistCount = 0;
    int recentTrackCount = 0;
    int durationFilterCount = 0;

    for (final track in intelligentlyFiltered) {
      // Skip if already seen by ID in this batch
      if (seenIds.contains(track.id)) {
        duplicateIdCount++;
        continue;
      }

      // Skip if recently played/loaded
      if (recentTrackIdSet.contains(track.id)) {
        recentTrackCount++;
        continue;
      }

      // Create normalized key for title+artist deduplication
      // Use proper format and clean up title variations
      final cleanTitle = _cleanTrackTitle(track.title);
      final cleanArtist = track.artist.toLowerCase().trim();
      final titleArtistKey = '$cleanTitle-$cleanArtist';

      // Skip if we've seen this title+artist combination
      if (seenTitleArtist.contains(titleArtistKey)) {
        duplicateTitleArtistCount++;
        // However, if this is a better version (explicit, non-deluxe, etc.), prefer it
        final existingTrackIndex = filteredTracks.indexWhere((t) =>
            '${_cleanTrackTitle(t.title)}-${t.artist.toLowerCase().trim()}' ==
            titleArtistKey);

        if (existingTrackIndex != -1) {
          final existingTrack = filteredTracks[existingTrackIndex];

          // Priority rules for replacing tracks:
          // 1. Prefer explicit versions
          // 2. Prefer non-deluxe/remaster versions (original releases)
          // 3. Prefer higher popularity
          bool shouldReplace = false;

          if (track.explicit && !existingTrack.explicit) {
            shouldReplace = true; // Explicit version preferred
          } else if (!track.explicit && existingTrack.explicit) {
            shouldReplace = false; // Keep existing explicit
          } else if (_isOriginalVersion(track.title) &&
              !_isOriginalVersion(existingTrack.title)) {
            shouldReplace =
                true; // Original version preferred over deluxe/remaster
          } else if (!_isOriginalVersion(track.title) &&
              _isOriginalVersion(existingTrack.title)) {
            shouldReplace = false; // Keep existing original
          } else if ((track.popularity ?? 0) >
              (existingTrack.popularity ?? 0)) {
            shouldReplace = true; // Higher popularity
          }

          if (shouldReplace) {
            print(
                '🔄 Replacing duplicate: "${existingTrack.title}" → "${track.title}" by ${track.artist}');
            seenIds.remove(existingTrack.id);
            filteredTracks[existingTrackIndex] = track;
            seenIds.add(track.id);
          }
        }
        continue;
      }

      // Duration filtering
      if (track.durationMs < minDuration * 1000 ||
          track.durationMs > maxDuration * 1000) {
        durationFilterCount++;
        continue;
      }

      // 🚀 Enhanced genre validation for current search context
      if (_currentGenreFilter != null && _currentGenreFilter!.isNotEmpty) {
        if (!_validateTrackForCurrentGenre(track, _currentGenreFilter!)) {
          print(
              '🚫 Filtered out track not matching genre "${_currentGenreFilter}": "${track.title}" by ${track.artist}');
          continue;
        }
      }

      // Filter out instrumental versions of popular songs (unless specifically searching for instrumentals)
      if (!isDiscovery &&
          (track.title.toLowerCase().contains('instrumental') ||
              track.title.toLowerCase().contains('karaoke') ||
              track.title.toLowerCase().contains('backing track'))) {
        print('🚫 Filtered out instrumental/karaoke: "${track.title}"');
        continue;
      }

      seenIds.add(track.id);
      seenTitleArtist.add(titleArtistKey);
      filteredTracks.add(track);
    }

    print('🧹 [Dedup] Filtering results:');
    print('   📊 Input: ${tracks.length} → Output: ${filteredTracks.length}');
    print('   🔄 Duplicate IDs: $duplicateIdCount');
    print('   🎵 Duplicate title+artist: $duplicateTitleArtistCount');
    print('   ⏱️ Recent tracks filtered: $recentTrackCount');
    print('   ⏰ Duration filtered: $durationFilterCount');

    return filteredTracks;
  }

  /// Clean track title for better deduplication
  String _cleanTrackTitle(String title) {
    return title
        .toLowerCase()
        .trim()
        // Remove common suffixes that indicate different versions
        .replaceAll(RegExp(r'\s*\(.*?\)\s*$'), '') // Remove parentheses at end
        .replaceAll(RegExp(r'\s*\[.*?\]\s*$'), '') // Remove brackets at end
        // Remove common version indicators
        .replaceAll(
            RegExp(
                r'\s*-\s*(remaster|remastered|deluxe|expanded|anniversary|edition|version|radio edit|clean|explicit).*$'),
            '')
        .replaceAll(
            RegExp(
                r'\s*(remaster|remastered|deluxe|expanded|anniversary|edition|version|radio edit|clean|explicit).*$'),
            '')
        .trim();
  }

  /// Check if this is an original version (not deluxe/remaster/etc.)
  bool _isOriginalVersion(String title) {
    final titleLower = title.toLowerCase();
    final nonOriginalKeywords = [
      'deluxe',
      'remaster',
      'remastered',
      'expanded',
      'anniversary',
      'special edition',
      'collector',
      'bonus',
      'live',
      'acoustic',
      'radio edit',
      'extended',
      'remix',
      'alt version',
      'alternate'
    ];

    return !nonOriginalKeywords.any((keyword) => titleLower.contains(keyword));
  }

  /// Validate track against current genre search context with MusicBrainz enhancement
  bool _validateTrackForCurrentGenre(MusicTrack track, String genre) {
    // First run fast local validation
    if (!_validateTrackForCurrentGenreLocal(track, genre)) {
      return false;
    }

    // For high-precision genres, use MusicBrainz validation
    if (_shouldUseMusicBrainzValidationAI(track, genre)) {
      return _validateTrackWithMusicBrainzAI(track, genre);
    }

    return true; // Passed local validation
  }

  /// Fast local validation (existing logic)
  bool _validateTrackForCurrentGenreLocal(MusicTrack track, String genre) {
    final genreLower = genre.toLowerCase();
    final artistLower = track.artist.toLowerCase();
    final titleLower = track.title.toLowerCase();
    final trackGenres = track.genres.map((g) => g.toLowerCase()).toList();

    // Hip hop / rap validation - prevent Chinese pop contamination
    if (genreLower.contains('hip hop') ||
        genreLower.contains('rap') ||
        genreLower.contains('trap') ||
        genreLower.contains('drill')) {
      // Exclude Asian artists/tracks from hip hop searches
      if (RegExp(r'[\u4E00-\u9FFF]')
              .hasMatch(track.artist) || // Chinese characters
          RegExp(r'[\u4E00-\u9FFF]')
              .hasMatch(track.title) || // Chinese characters
          RegExp(r'[\uAC00-\uD7AF]').hasMatch(track.artist) || // Korean characters
          RegExp(r'[\u3040-\u309F\u30A0-\u30FF]').hasMatch(track.artist)) { // Japanese characters
        // Japanese artists
        print(
            '🚫 Genre Filter: Asian track filtered from hip hop: "${track.title}" by ${track.artist}');
        return false;
      }

      // Exclude obviously non-hip hop genres
      if (trackGenres.isNotEmpty) {
        final nonHipHopGenres = [
          'c-pop',
          'k-pop',
          'j-pop',
          'chinese',
          'korean',
          'japanese',
          'mandopop',
          'cantopop',
          'cpop',
          'kpop',
          'jpop',
          'country',
          'classical',
          'opera',
          'folk',
          'bluegrass'
        ];
        if (trackGenres
            .any((tg) => nonHipHopGenres.any((nhg) => tg.contains(nhg)))) {
          print(
              '🚫 Genre Filter: Non-hip hop genre filtered: "${track.title}" by ${track.artist} (Genres: ${track.genres})');
          return false;
        }
      }
    }

    // Rock validation
    else if (genreLower.contains('rock') ||
        genreLower.contains('metal') ||
        genreLower.contains('punk')) {
      // Exclude Asian pop from rock searches unless explicitly rock
      if (RegExp(r'[\u4E00-\u9FFF]').hasMatch(track.artist) ||
          RegExp(r'[\u4E00-\u9FFF]').hasMatch(track.title)) {
        if (!titleLower.contains('rock') &&
            !artistLower.contains('rock') &&
            !trackGenres.any((tg) => tg.contains('rock'))) {
          print(
              '🚫 Genre Filter: Asian track filtered from rock: "${track.title}" by ${track.artist}');
          return false;
        }
      }
    }

    // Pop validation - exclude Asian pop from Western pop searches
    else if (genreLower.contains('pop') &&
        !genreLower.contains('c-pop') &&
        !genreLower.contains('k-pop') &&
        !genreLower.contains('j-pop')) {
      if (trackGenres.isNotEmpty) {
        final asianPopGenres = [
          'c-pop',
          'k-pop',
          'j-pop',
          'chinese',
          'korean',
          'japanese'
        ];
        if (trackGenres
            .any((tg) => asianPopGenres.any((apg) => tg.contains(apg)))) {
          print(
              '🚫 Genre Filter: Asian pop filtered from Western pop: "${track.title}" by ${track.artist}');
          return false;
        }
      }
    }

    // Anime / Japanese pop validation – ensure real JP connection
    else if (genreLower.contains('anime') ||
        genreLower.contains('j-pop') ||
        genreLower.contains('jpop')) {
      // First, explicitly reject classical and other obviously non-anime genres
      if (trackGenres.isNotEmpty) {
        final nonAnimeGenres = [
          'classical',
          'opera',
          'orchestral',
          'symphony',
          'chamber music',
          'baroque',
          'romantic',
          'contemporary classical',
          'string quartet',
          'country',
          'folk',
          'bluegrass',
          'americana',
          'blues',
          'gospel',
          'hip hop',
          'rap',
          'trap',
          'uk drill',
          'chicago drill',
          'r&b',
          'soul',
          'funk',
          'reggae',
          'reggaeton',
          'latin',
          'salsa',
          'bachata',
          'merengue',
          'edm',
          'house',
          'techno',
          'dubstep',
          'drum and bass',
          'metal',
          'death metal',
          'black metal',
          'thrash metal',
          'punk',
          'hardcore',
          'post-punk',
          'alternative rock'
        ];
        if (trackGenres
            .any((tg) => nonAnimeGenres.any((nag) => tg.contains(nag)))) {
          print(
              '🚫 Genre Filter: Non-anime genre filtered from anime search: "${track.title}" by ${track.artist} (Genres: ${track.genres})');
          return false;
        }
      }

      // Explicitly reject classical composers and orchestras by artist name
      final classicalKeywords = [
        'orchestra',
        'symphony',
        'philharmonic',
        'chamber',
        'quartet',
        'ensemble',
        'bach',
        'mozart',
        'beethoven',
        'chopin',
        'brahms',
        'tchaikovsky',
        'vivaldi',
        'debussy',
        'ravel',
        'stravinsky',
        'mahler',
        'wagner',
        'handel',
        'haydn',
        'schubert',
        'schumann',
        'liszt',
        'mendelssohn',
        'rachmaninoff'
      ];
      final artistLower = track.artist.toLowerCase();
      if (classicalKeywords.any((keyword) => artistLower.contains(keyword))) {
        print(
            '🚫 Genre Filter: Classical artist filtered from anime search: "${track.title}" by ${track.artist}');
        return false;
      }

      // Explicitly reject classical music titles
      final classicalTitleKeywords = [
        'sonata',
        'concerto',
        'symphony',
        'prelude',
        'etude',
        'nocturne',
        'waltz',
        'mazurka',
        'polonaise',
        'fugue',
        'canon',
        'suite',
        'minuet',
        'scherzo',
        'rondo',
        'variation',
        'ballade',
        'impromptu'
      ];
      final titleLower = track.title.toLowerCase();
      if (classicalTitleKeywords
          .any((keyword) => titleLower.contains(keyword))) {
        print(
            '🚫 Genre Filter: Classical title filtered from anime search: "${track.title}" by ${track.artist}');
        return false;
      }

      // Accept if artist or title contains Japanese characters
      final hasJapaneseChars =
          RegExp(r'[\u3040-\u30FF\u4E00-\u9FFF]').hasMatch(track.artist) ||
              RegExp(r'[\u3040-\u30FF\u4E00-\u9FFF]').hasMatch(track.title);

      // Accept if Spotify genres explicitly tag it as anime / j-pop / j-rock
      final jpGenres = ['anime', 'j-pop', 'jpop', 'j-rock', 'jrock', 'anisong'];
      final genresMatch =
          trackGenres.any((tg) => jpGenres.any((j) => tg.contains(j)));

      // Accept if has Japanese characters
      final curatedOk = RegExp(r'[\u3040-\u309F\u30A0-\u30FF]').hasMatch(track.artist);

      if (!(hasJapaneseChars || genresMatch || curatedOk)) {
        print(
            '🚫 Genre Filter: Non-JP track filtered from anime/j-pop: "${track.title}" by ${track.artist}');
        return false;
      }
    }

    return true; // Track passes validation
  }

  /// Determine if we should use MusicBrainz validation for AI search
  bool _shouldUseMusicBrainzValidationAI(MusicTrack track, String genre) {
    final genreLower = genre.toLowerCase();

    // Use MusicBrainz for genres that benefit from precise validation
    final precisionGenres = [
      'jazz',
      'classical',
      'blues',
      'country',
      'folk',
      'reggae',
      'metal',
      'punk',
      'experimental',
      'ambient'
    ];

    return precisionGenres.any((pg) => genreLower.contains(pg));
  }

  /// Enhanced validation using MusicBrainz for AI search
  bool _validateTrackWithMusicBrainzAI(MusicTrack track, String genre) {
    // Check cache first to avoid repeated API calls
    final cacheKey = '${track.artist.toLowerCase()}_${genre.toLowerCase()}';

    if (_artistGenreValidationCache.containsKey(cacheKey)) {
      final cacheTime = _validationCacheTimestamps[cacheKey];
      if (cacheTime != null &&
          DateTime.now().difference(cacheTime) < _validationCacheExpiry) {
        print(
            '🧠 [AI Validation] Using cached MusicBrainz result for ${track.artist} in $genre');
        return _artistGenreValidationCache[cacheKey]!;
      }
    }

    // Perform async validation in background
    _performAsyncMusicBrainzValidationAI(track.artist, genre, cacheKey);

    // Return optimistic result to avoid blocking UI
    return true;
  }

  /// Perform MusicBrainz validation asynchronously for AI search
  void _performAsyncMusicBrainzValidationAI(
      String artistName, String genre, String cacheKey) async {
    try {
      print(
          '🎯 [AI Validation] Checking ${artistName} against $genre using MusicBrainz...');

      // Split collaborative artists before MusicBrainz lookup
      final individualArtists = _separateCollaborativeArtistsAI(artistName);
      if (individualArtists.isEmpty) {
        print(
            '⚠️ [AI Validation] No valid individual artists found for $artistName');
        _artistGenreValidationCache[cacheKey] = true;
        _validationCacheTimestamps[cacheKey] = DateTime.now();
        return;
      }

      print(
          '🎯 [AI Validation] Split "$artistName" into ${individualArtists.length} individual artists: ${individualArtists.join(", ")}');

      // Check each individual artist and combine results
      bool anyValidArtist = false;
      final allGenres = <String>{};

      for (final individualArtist in individualArtists.take(3)) {
        // Limit to first 3 to avoid too many API calls
        try {
          final artistGenres =
              await _musicBrainzService.getArtistGenres(individualArtist);
          if (artistGenres.isNotEmpty) {
            allGenres.addAll(artistGenres);
            print(
                '🎵 [AI Validation] MusicBrainz genres for "$individualArtist": $artistGenres');

            // Check if this artist's genres match the target genre
            if (_doesArtistGenreMatchTargetAI(artistGenres, genre)) {
              anyValidArtist = true;
              print(
                  '✅ [AI Validation] Individual artist "$individualArtist" validated for $genre');
            }
          }
        } catch (e) {
          print(
              '❌ [AI Validation] Error for individual artist "$individualArtist": $e');
        }
      }

      if (allGenres.isEmpty) {
        print(
            '⚠️ [AI Validation] No MusicBrainz genres found for any individual artist in $artistName');
        _artistGenreValidationCache[cacheKey] = true;
        _validationCacheTimestamps[cacheKey] = DateTime.now();
        return;
      }

      // Cache the result based on whether any individual artist was valid
      _artistGenreValidationCache[cacheKey] = anyValidArtist;
      _validationCacheTimestamps[cacheKey] = DateTime.now();

      if (anyValidArtist) {
        print(
            '✅ [AI Validation] $artistName validated for $genre via MusicBrainz (${allGenres.length} genres found)');
      } else {
        print(
            '❌ [AI Validation] $artistName rejected for $genre via MusicBrainz');
      }
    } catch (e) {
      print(
          '❌ [AI Validation] MusicBrainz validation error for $artistName: $e');
      // Cache as valid on error
      _artistGenreValidationCache[cacheKey] = true;
      _validationCacheTimestamps[cacheKey] = DateTime.now();
    }
  }

  /// Check if artist's MusicBrainz genres match the target genre for AI search
  bool _doesArtistGenreMatchTargetAI(
      List<String> artistGenres, String targetGenre) {
    final targetLower = targetGenre.toLowerCase();

    // Direct match
    for (final artistGenre in artistGenres) {
      final artistGenreLower = artistGenre.toLowerCase();

      if (artistGenreLower == targetLower) {
        return true;
      }

      // Use Spotify's genre relationship system
      if (SpotifyGenreService.areGenresRelated(artistGenre, targetGenre)) {
        return true;
      }
    }

    return false;
  }

  /// Separate collaborative artists into individual artist names for AI search
  List<String> _separateCollaborativeArtistsAI(String artistName) {
    if (artistName.trim().isEmpty) return [];

    // List of collaboration separators (ordered by specificity)
    final separators = [
      ' featuring ',
      ' feat. ',
      ' feat ',
      ' ft. ',
      ' ft ',
      ' with ',
      ' vs. ',
      ' vs ',
      ' and ',
      ' & ',
      ', ',
      ' x ',
      ' + ',
      ' / ',
      ' ; ',
      ' | ',
      ' - ',
    ];

    String workingString = artistName.trim();

    // Apply separators in order of specificity
    for (final separator in separators) {
      if (workingString.toLowerCase().contains(separator.toLowerCase())) {
        final parts =
            workingString.split(RegExp(separator, caseSensitive: false));
        final cleanedParts = parts
            .map((part) => part.trim())
            .where((part) => part.isNotEmpty)
            .map((part) => _cleanArtistNameAI(part))
            .where((part) => part.isNotEmpty)
            .toSet() // Remove duplicates
            .toList();

        if (cleanedParts.length > 1) {
          print(
              '🎵 [AI Collab Split] Separated "$artistName" into: ${cleanedParts.join(", ")}');
          return cleanedParts;
        }
        break; // Only apply the first matching separator
      }
    }

    // No collaboration found, return the cleaned original name
    final cleaned = _cleanArtistNameAI(workingString);
    return cleaned.isNotEmpty ? [cleaned] : [];
  }

  /// Clean up artist names by removing collaboration remnants for AI search
  String _cleanArtistNameAI(String name) {
    if (name.trim().isEmpty) return '';

    String cleaned = name.trim();

    // Remove common collaboration remnants
    final cleanupPatterns = [
      RegExp(r'\s*\(.*feat.*\).*$', caseSensitive: false),
      RegExp(r'\s*\(.*featuring.*\).*$', caseSensitive: false),
      RegExp(r'\s*\(.*with.*\).*$', caseSensitive: false),
      RegExp(r'\s*\[.*feat.*\].*$', caseSensitive: false),
      RegExp(r'\s*\[.*featuring.*\].*$', caseSensitive: false),
    ];

    for (final pattern in cleanupPatterns) {
      cleaned = cleaned.replaceAll(pattern, '');
    }

    return cleaned.trim();
  }

  /// Generate a personalized AI message based on user preferences
  String _getPersonalizedAiMessage(
      List<String> topGenres, List<String> favoriteArtists) {
    final messages = [
      if (topGenres.isNotEmpty && favoriteArtists.isNotEmpty)
        "Discovered fresh tracks similar to your ${topGenres.first} favorites and ${favoriteArtists.first}! 🎵",
      if (topGenres.isNotEmpty)
        "Found new ${topGenres.first} gems based on your taste! ✨",
      if (favoriteArtists.isNotEmpty)
        "New discoveries inspired by your love for ${favoriteArtists.first}! 🎤",
      "Fresh tracks inspired by your listening habits! 🎧",
      "New music discoveries tailored to your unique taste! 🌟",
    ];

    return messages.isNotEmpty
        ? messages.first
        : "Discover new music based on your personal taste! 🎵";
  }

  /// Generate a personalized AI message for genre filtering
  String _getPersonalizedGenreMessage(String genre) {
    final messages = [
      "Diving deep into $genre with your favorite artists! 🎯",
      "Personalized $genre discoveries based on your taste! 🎵",
      "Exploring $genre through artists you love! ✨",
      "Custom $genre playlist featuring your musical DNA! 🧬",
      "Your personal $genre journey starts here! 🚀",
      "$genre tracks curated from your listening history! 📚",
    ];

    return messages[math.Random().nextInt(messages.length)];
  }

  Future<void> _loadSpotifyRecommendations(BuildContext context) async {
    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      if (!spotifyProvider.isConnected) {
        print('Spotify not connected');
        return;
      }

      // Load different types of recommendations in parallel
      final futures = <Future<List<MusicTrack>>>[
        _spotifyService.getTopTracks(limit: 10),
        spotifyProvider
            .loadLikedSongs()
            .then((_) => spotifyProvider.likedSongs.take(10).toList()),
        _spotifyService.getRecentlyPlayed(limit: 10),
        _getSpotifyRecommendationsForMood(),
      ];

      final results = await Future.wait(futures);

      // Categorize the results and preserve any existing data
      final Map<String, List<MusicTrack>> categorized = {
        'topTracks': results[0].isNotEmpty
            ? results[0]
            : (_categorizedRecommendations['topTracks'] ?? []),
        'likedSongs': results[1].isNotEmpty
            ? results[1]
            : (_categorizedRecommendations['likedSongs'] ?? []),
        'recentlyPlayed': results[2].isNotEmpty
            ? results[2]
            : (_categorizedRecommendations['recentlyPlayed'] ?? []),
        'moodBased': results[3],
      };

      // Combine all for 'all' category
      final allTracks = <MusicTrack>[];
      for (final trackList in categorized.values) {
        allTracks.addAll(trackList);
      }

      // Remove duplicates based on track ID
      final seenIds = <String>{};
      final uniqueTracks = allTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      categorized['all'] = uniqueTracks;
      categorized['discover'] = [];

      _categorizedRecommendations = categorized;
      _currentRecommendations =
          _intelligentShuffle(categorized[_currentCategory] ?? []);
      notifyListeners();
    } catch (e) {
      print('Error loading Spotify recommendations: $e');
    }
  }

  Future<List<MusicTrack>> _getSpotifyRecommendationsForMood() async {
    try {
      final mood = _moods[_selectedMoodIndex];
      final moodName = mood['name'].toString().toLowerCase();

      print('🎭 Getting mood recommendations for: $moodName');

      final allTracks = <MusicTrack>[];
      final processedPlaylistIds = <String>{};

      // Strategy 1: Search for mood-specific playlists (similar to genre search)
      final moodPlaylistQueries = _getMoodPlaylistQueries(moodName);

      for (final playlistQuery in moodPlaylistQueries.take(4)) {
        try {
          print('🎵 Searching for mood playlists: "$playlistQuery"');

          // Search for playlists with randomized offset for variety
          final randomOffset = math.Random().nextInt(3) * 10;
          final playlists = await _spotifyService.searchPlaylists(
            playlistQuery,
            limit: 25, // OPTIMIZED: Increased from 15 to 25
            offset: randomOffset,
          );

          if (playlists.isEmpty) {
            print('⚠️ No playlists found for "$playlistQuery"');
            continue;
          }

          print(
              '📚 Found ${playlists.length} playlists for mood "$playlistQuery"');

          // Get tracks from each playlist
          for (final playlist in playlists.take(5)) {
            final playlistId = playlist['id'] as String?;
            final playlistName = playlist['name'] as String? ?? '';

            if (playlistId == null ||
                processedPlaylistIds.contains(playlistId)) {
              continue;
            }

            processedPlaylistIds.add(playlistId);

            try {
              // Get tracks from this playlist with random offset
              final randomTrackOffset = math.Random().nextInt(5) * 20;
              final playlistTracksData =
                  await _spotifyService.getPlaylistTracks(
                playlistId,
                limit: 80, // OPTIMIZED: Increased from 50 to 80
                offset: randomTrackOffset,
              );

              final playlistTracks =
                  playlistTracksData['tracks'] as List<MusicTrack>? ?? [];

              if (playlistTracks.isNotEmpty) {
                // Mix tracks for variety
                final shuffledTracks = List<MusicTrack>.from(playlistTracks)
                  ..shuffle();
                allTracks.addAll(shuffledTracks.take(20));
                print(
                    '✅ Added ${math.min(20, playlistTracks.length)} tracks from "$playlistName"');
              }
            } catch (e) {
              print('❌ Error getting tracks from playlist $playlistId: $e');
            }

            // Stop if we have enough tracks
            if (allTracks.length >= 100) {
              break;
            }
          }

          if (allTracks.length >= 100) {
            break;
          }
        } catch (e) {
          print('❌ Error searching playlists for query "$playlistQuery": $e');
        }
      }

      // Strategy 2: Direct track search with mood queries
      final moodQueries = _getMoodSearchQueries(moodName);

      // Randomize query selection
      final shuffledQueries = List<String>.from(moodQueries)..shuffle();

      for (final query in shuffledQueries.take(3)) {
        try {
          final tracks = await _spotifyService.searchTracks(
            query,
            limit: 10,
            context: _getSearchContext(),
          );
          allTracks.addAll(tracks);
        } catch (e) {
          print('Error searching for mood query "$query": $e');
        }
      }

      // Remove duplicates using both ID and title+artist
      final seenIds = <String>{};
      final seenTitleArtist = <String>{};
      final uniqueTracks = <MusicTrack>[];

      for (final track in allTracks) {
        final titleArtistKey =
            '${track.title.toLowerCase()}_${track.artist.toLowerCase()}';

        if (!seenIds.contains(track.id) &&
            !seenTitleArtist.contains(titleArtistKey)) {
          seenIds.add(track.id);
          seenTitleArtist.add(titleArtistKey);
          uniqueTracks.add(track);
        } else if (seenTitleArtist.contains(titleArtistKey) && track.explicit) {
          // Replace with explicit version if available
          final existingIndex = uniqueTracks.indexWhere((t) =>
              '${t.title.toLowerCase()}_${t.artist.toLowerCase()}' ==
              titleArtistKey);
          if (existingIndex != -1 && !uniqueTracks[existingIndex].explicit) {
            uniqueTracks[existingIndex] = track;
          }
        }
      }

      // Shuffle for variety
      uniqueTracks.shuffle();

      print('✅ Total mood tracks collected: ${uniqueTracks.length}');
      return uniqueTracks;
    } catch (e) {
      print('Error getting mood-based recommendations: $e');
      // Fallback to simple search-based approach
      try {
        final mood = _moods[_selectedMoodIndex];
        final moodName = mood['name'].toString().toLowerCase();
        return await _spotifyService.searchTracks(
          '$moodName music',
          limit: 30, // OPTIMIZED: Increased from 15 to 30
          context: _getSearchContext(),
        );
      } catch (fallbackError) {
        print('Fallback mood search failed: $fallbackError');
        return [];
      }
    }
  }

  /// Get search queries for each mood (inspired by mood-for-spotify approach)
  List<String> _getMoodSearchQueries(String mood) {
    switch (mood) {
      case 'happy':
        return [
          'happy pop music',
          'feel good songs',
          'upbeat cheerful',
          'positive vibes',
          'good mood music',
        ];
      case 'energetic':
        return [
          'high energy music',
          'pump up songs',
          'workout music',
          'electronic dance',
          'rock anthems',
        ];
      case 'calm':
        return [
          'calm relaxing music',
          'peaceful ambient',
          'chill acoustic',
          'meditation music',
          'soft instrumental',
        ];
      case 'focused':
        return [
          'focus music instrumental',
          'study music concentration',
          'ambient work music',
          'lo-fi study beats',
          'classical focus',
        ];
      case 'dreamy':
        return [
          'dreamy atmospheric music',
          'ethereal ambient',
          'dream pop',
          'shoegaze atmospheric',
          'floating ambient',
        ];
      case 'chill':
        return [
          'chill music',
          'downtempo chill',
          'lo-fi chill hop',
          'indie chill vibes',
          'relaxed mellow',
        ];
      default:
        return [
          'popular music',
          'indie alternative',
          'trending songs',
        ];
    }
  }

  /// Get playlist search queries for each mood
  List<String> _getMoodPlaylistQueries(String mood) {
    switch (mood) {
      case 'happy':
        return [
          'happy mood playlist',
          'feel good vibes',
          'upbeat happy hits',
          'good mood music',
          'positive energy playlist',
          'happy songs ${DateTime.now().year}',
          'mood booster playlist',
        ];
      case 'energetic':
        return [
          'high energy workout',
          'pump up playlist',
          'energy boost music',
          'workout motivation',
          'power playlist',
          'gym beast mode',
          'adrenaline rush songs',
        ];
      case 'calm':
        return [
          'calm relaxing playlist',
          'peaceful vibes',
          'stress relief music',
          'meditation playlist',
          'chill ambient sounds',
          'relaxation station',
          'zen music playlist',
        ];
      case 'focused':
        return [
          'focus study playlist',
          'concentration music',
          'deep work sounds',
          'study lo-fi playlist',
          'productivity playlist',
          'brain food music',
          'focus flow state',
        ];
      case 'dreamy':
        return [
          'dreamy vibes playlist',
          'ethereal sounds',
          'atmospheric playlist',
          'dream pop collection',
          'floating music',
          'cosmic playlist',
          'ethereal electronic',
        ];
      case 'chill':
        return [
          'chill vibes playlist',
          'laid back music',
          'chill out lounge',
          'sunday chill playlist',
          'mellow beats',
          'chill hop essentials',
          'relax unwind playlist',
        ];
      default:
        return [
          'mood playlist',
          'vibes collection',
          'curated playlist',
        ];
    }
  }

  Future<List<MusicTrack>> _getDiscoverTracks(BuildContext context) async {
    try {
      print(
          '🔍 [DISCOVERY] Getting initial discovery tracks with TRUE discovery logic...');

      // Use the real discovery logic
      return await _getMoreDiscoveryTracks(0, context);
    } catch (e) {
      print('❌ [DISCOVERY] Error getting discover tracks: $e');
      return [];
    }
  }

  void onMoodChanged(int index, BuildContext context) {
    if (_selectedMoodIndex == index) return;

    // Prevent too rapid mood switching (debounce)
    final now = DateTime.now();
    if (_lastMoodChange != null &&
        now.difference(_lastMoodChange!).inMilliseconds < 500) {
      print('🎭 Mood change too rapid, ignoring');
      return;
    }
    _lastMoodChange = now;

    final oldMood = _moods[_selectedMoodIndex]['name'];
    final newMood = _moods[index]['name'];
    print(
        '🎭 [Mood Change] Switching from $oldMood to $newMood (current category: $_currentCategory)');

    _selectedMoodIndex = index;
    _currentAiMessage = _moods[index]['aiMessage'];
    _showAiMessage = true;
    notifyListeners();

    // Clear tracking and reset pagination for new mood
    _recentTrackIds.clear();
    _currentPage = 0;
    _hasMoreContent = true;

    // Handle mood change asynchronously with proper error handling
    _handleMoodChangeAsync(index, context);
  }

  /// Handle mood change asynchronously with error handling and concurrency control
  Future<void> _handleMoodChangeAsync(
      int moodIndex, BuildContext context) async {
    // Prevent concurrent mood loading
    if (_isLoading || _isMoodLoading) {
      print(
          '🎭 Mood change skipped - already loading (isLoading: $_isLoading, isMoodLoading: $_isMoodLoading)');
      return;
    }

    _isMoodLoading = true;
    notifyListeners();

    try {
      // Always reload with mood-specific content when mood changes
      print('🎭 Loading mood-based recommendations for index: $moodIndex');
      await _loadMoodBasedRecommendations(context);

      // Use a small delay to ensure state is stable
      Future.delayed(const Duration(milliseconds: 100), () {
        if (_categorizedRecommendations.containsKey(_currentCategory)) {
          _currentRecommendations = _intelligentShuffle(
              _categorizedRecommendations[_currentCategory] ?? []);

          // Update tracking for the current category
          _recentTrackIds.clear();
          _addToRecentTrackIds(
              _currentRecommendations.map((track) => track.id));
          notifyListeners();
        }
        print(
            '✅ [Mood Change] Updated $_currentCategory view with ${_currentRecommendations.length} tracks after mood change');
      });
    } catch (e) {
      print('❌ Error handling mood change: $e');

      // Fallback: clear current recommendations to prevent stale data
      _currentRecommendations = [];
      _isLoading = false;
      _isMoodLoading = false;
      notifyListeners();

      // Show error message to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Failed to load ${_moods[moodIndex]['name']} recommendations'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    } finally {
      // Always clear the mood loading flag
      _isMoodLoading = false;
      notifyListeners();
    }
  }

  void onCategoryChanged(String category, BuildContext context) {
    if (_currentCategory == category) return;

    _currentCategory = category;
    _currentPage = 0; // Reset pagination
    _hasMoreContent = true; // Reset pagination state

    // Clear any genre selection when switching categories
    if (category != 'genreBased') {
      _selectedGenre = null;
      _currentGenreFilter = null; // 🔓 Unlock genre filter
    }

    // Genre category keeps its data per-genre.
    if (category == 'genreBased' && _selectedGenre != null) {
      _currentRecommendations =
          _intelligentShuffle(_genreRecommendations[_selectedGenre!] ?? []);
    } else {
      _currentRecommendations =
          _intelligentShuffle(_categorizedRecommendations[category] ?? []);
    }

    // Update tracking of loaded tracks for this category
    _recentTrackIds.clear();
    _addToRecentTrackIds(_currentRecommendations.map((track) => track.id));

    // Update AI service context
    _aiRecommendationService.setCurrentContext(
        category: category, genre: _selectedGenre);
    notifyListeners();

    // If the new category is empty, load data for it
    if ((_categorizedRecommendations[category] ?? []).isEmpty) {
      final personalCategories = [
        'topTracks',
        'likedSongs',
        'recentlyPlayed',
        'recentlyAdded',
        'discover'
      ];
      if (personalCategories.contains(category)) {
        _loadDataForCategory(category, context);
      }
    }
  }

  Future<void> _loadDataForCategory(
      String category, BuildContext context) async {
    if (_isLoading) return;

    _isLoading = true;
    notifyListeners();

    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      // Only bail out if the category truly requires a connected service
      // (e.g. likedSongs, recentlyPlayed). For discovery we can proceed with
      // public catalogue searches using the backend client-credentials flow.
      const serviceRequiredCategories = [
        'likedSongs',
        'recentlyPlayed',
        'recentlyAdded',
        'topTracks'
      ];
      if (!spotifyProvider.isConnected &&
          serviceRequiredCategories.contains(category)) {
        print(
            '❌ No music service connected for lazy loading category $category');
        _isLoading = false;
        notifyListeners();
        return;
      }

      List<MusicTrack> tracks = [];
      switch (category) {
        case 'topTracks':
          print('🏆 Lazy loading Top Tracks...');
          tracks = await _getSimilarToTopTracks(context);
          break;
        case 'likedSongs':
          print('❤️ Lazy loading Liked Songs...');
          tracks = await _getSimilarToLikedSongs(context);
          break;
        case 'recentlyPlayed':
          print('🕒 Lazy loading Recently Played...');
          tracks = await _getSimilarToRecentlyPlayed(context);
          break;
        case 'recentlyAdded':
          print('🕒 Lazy loading Recently Added...');
          tracks = await _getRecentlyAdded(context);
          break;
        case 'discover':
          print('🔍 Lazy loading Discover...');
          tracks = await _getDiscoverTracks(context);
          break;
      }
      _categorizedRecommendations[category] = tracks;
      // Only update current recommendations if the category is still the same
      if (_currentCategory == category) {
        _currentRecommendations = _intelligentShuffle(tracks);
        _recentTrackIds.clear();
        _addToRecentTrackIds(_currentRecommendations.map((track) => track.id));
      }
      notifyListeners();
    } catch (e) {
      print('❌ Error lazy loading data for category $category: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void onGenreSelected(String? genre, BuildContext context) {
    _selectedGenre = genre;
    notifyListeners();
    return;
    // if (_selectedGenre == genre) return;

    // _selectedGenre = genre;
    // _currentPage = 0;
    // _hasMoreContent = true;
    // _currentRecommendations = [];
    // _recentTrackIds.clear();
    // _isLoading = true;
    // notifyListeners();

    // // increment request id to cancel any previous pending loads
    // final int requestId = _nextGenreRequestId();

    // if (genre != null && genre.isNotEmpty) {
    //   _currentGenreFilter = genre; // 🔒 Lock genre filter for validation
    //   _loadGenreSpecificTracks(genre, context, requestId);
    // } else {
    //   // Handle "All Genres" selection – clear filter so other categories aren't constrained
    //   _currentGenreFilter = null;
    //   _currentRecommendations =
    //       _intelligentShuffle(_categorizedRecommendations['genreBased'] ?? []);
    //   _addToRecentTrackIds(_currentRecommendations.map((track) => track.id));
    //   _isLoading = false;
    //   notifyListeners();
    // }
  }

  /// Load tracks for a specific genre
  Future<void> _loadGenreSpecificTracks(
      String genre, BuildContext context, int requestId) async {
    try {
      print('🎯 Loading tracks for genre: $genre (requestId: $requestId)');

      final tracks = await _getTracksForGenre(genre, 0, context);

      // ignore if a newer request was started
      if (requestId != _genreRequestId) {
        print('⏭️ Discarded stale genre load for $genre');
        return;
      }

      // Persist into the dedicated bucket for this genre and update the UI
      final shuffledTracks = _intelligentShuffle(tracks);
      _genreRecommendations[genre] = shuffledTracks;
      _currentRecommendations = shuffledTracks;
      _addToRecentTrackIds(shuffledTracks.map((track) => track.id));
      _isLoading = false;
      _currentAiMessage = _getPersonalizedGenreMessage(genre);
      _showAiMessage = true;
      notifyListeners();
    } catch (e) {
      print('❌ Error loading genre-specific tracks: $e');
      if (requestId == _genreRequestId) {
        _isLoading = false;
        notifyListeners();
      }
    }
  }

  /// Get tracks for a specific genre - fast Last.fm-focused approach
  Future<List<MusicTrack>> _getTracksForGenre(
      String genre, int offset, BuildContext context) async {
    try {
      print(
          '🎯 Getting tracks for genre: $genre (offset: $offset) - using AI Recommendation Service');

      _aiRecommendationService.setCurrentContext(
          category: 'genreBased', genre: genre);

      final allTracks = <MusicTrack>[];

      // Get user's favorite artists within this genre
      final favoriteArtistsInGenre =
          await _getFavoriteArtistsForGenre(genre, context);
      print(
          '🎤 Found ${favoriteArtistsInGenre.length} favorite artists for $genre: $favoriteArtistsInGenre');

      // PHASE 1: Get personalized recommendations from AI service (80% of content)
      if (favoriteArtistsInGenre.isNotEmpty) {
        final aiTracks =
            await _aiRecommendationService.getMoreRecommendationsForCategory(
          'genreBased',
          favoriteArtistsInGenre,
          {
            'selectedGenre': genre,
            'currentPage': offset ~/ _pageSize, // Calculate page from offset
            'pageSize': _pageSize * 3, // Get more tracks for better variety
          },
        );

        _aiRecommendationService.markTracksAsAISuggested(aiTracks);

        allTracks.addAll(aiTracks);
        print('🤖 Got ${aiTracks.length} personalized AI tracks for $genre');
      }

      // PHASE 2: Supplement with general genre tracks if needed (20% of content)
      if (allTracks.length < _pageSize * 2) {
        final genreTracks = await _getGeneralGenreTracks(genre, offset);
        allTracks.addAll(
            genreTracks.take(_pageSize ~/ 2)); // Only take a small portion
        print(
            '📥 Added ${math.min(genreTracks.length, _pageSize ~/ 2)} general tracks for variety');
      }

      return _filterAndDeduplicateTracks(allTracks);
    } catch (e) {
      print('❌ Error getting tracks for genre $genre: $e');
      return [];
    }
  }

  /// Get comprehensive artists for a genre - fast Last.fm-focused approach for better accuracy
  Future<List<String>> _getFavoriteArtistsForGenre(
      String genre, BuildContext context) async {
    try {
      print('🎤 Getting artist data for genre: $genre (Last.fm enhanced mode)');

      final allArtists = <String>{};
      final artistPopularity = <String, int>{};

      // Strategy 1: Get user's personal top artists that match the genre (primary source)
      print('📊 Getting user\'s personal top artists...');
      final userArtists = await _getUserArtistsForGenre(genre, context);
      final validUserArtists = <String>[];

      for (final artist in userArtists) {
        final artistName = artist['name'] as String;
        // Filter out problematic seeds
        if (!_isProblematicSeed(artistName)) {
          allArtists.add(artistName);
          artistPopularity[artistName] = artist['popularity'] as int? ?? 0;
          validUserArtists.add(artistName);
        } else {
          print('🚫 Filtered out problematic seed: $artistName');
        }
      }
      print(
          '✓ Found ${validUserArtists.length} valid user artists for $genre: $validUserArtists');

      // Strategy 2: Use Last.fm for similar artist discovery when few user artists found
      if (validUserArtists.length < 5 && validUserArtists.isNotEmpty) {
        print(
            '🎵 Few user artists found, using Last.fm for similar artist discovery...');

        final lastFmService = LastFmService();
        if (lastFmService.isConfigured) {
          try {
            // Get similar artists for each valid user artist
            final similarArtists = <String>{};

            for (final userArtist in validUserArtists.take(3)) {
              // Limit to top 3 user artists
              print('🔍 [Last.fm] Getting similar artists for: $userArtist');
              final similar =
                  await lastFmService.getSimilarArtists(userArtist, limit: 15);

              // Filter and add similar artists
              for (final similarArtist in similar) {
                if (!_isProblematicSeed(similarArtist) &&
                    !allArtists.contains(similarArtist)) {
                  similarArtists.add(similarArtist);
                  artistPopularity[similarArtist] =
                      70; // Good popularity for Last.fm discovered artists
                }
              }

              print(
                  '🎯 [Last.fm] Found ${similar.length} similar artists for $userArtist');
            }

            allArtists.addAll(similarArtists);
            print(
                '✓ Added ${similarArtists.length} artists from Last.fm discovery: ${similarArtists.take(5).join(", ")}${similarArtists.length > 5 ? "..." : ""}');
          } catch (e) {
            print(
                '❌ Last.fm discovery failed: $e, falling back to Spotify search');
            // Fallback to Spotify search if Last.fm fails
            await _fallbackSpotifyGenreSearch(
                genre, allArtists, artistPopularity);
          }
        } else {
          print('⚠️ Last.fm not configured, using Spotify fallback');
          await _fallbackSpotifyGenreSearch(
              genre, allArtists, artistPopularity);
        }
      }

      // Strategy 3: If still not enough artists, add minimal Spotify search
      if (allArtists.length < 8) {
        print('🔍 Still need more artists, using minimal Spotify search...');
        await _fallbackSpotifyGenreSearch(genre, allArtists, artistPopularity);
      }

      // Convert to list, filter problematic seeds, and sort by popularity
      final artistsList =
          allArtists.where((artist) => !_isProblematicSeed(artist)).toList();
      artistsList.sort((a, b) =>
          (artistPopularity[b] ?? 0).compareTo(artistPopularity[a] ?? 0));

      final finalArtists = artistsList.take(12).toList();
      print(
          '🎤 Final: Found ${finalArtists.length} GENRE-SPECIFIC artists for $genre: $finalArtists');
      return finalArtists;
    } catch (e) {
      print('❌ Error getting artists for genre $genre: $e');
      // Fallback to basic user artists only
      final userArtists = await _getUserArtistsForGenre(genre, context);
      return userArtists
          .take(8)
          .map((artist) => artist['name'] as String)
          .where((artist) => !_isProblematicSeed(artist))
          .toList();
    }
  }

  /// Check if an artist name is problematic and should be filtered out
  bool _isProblematicSeed(String artist) {
    final artistLower = artist.toLowerCase().trim();

    // Filter out problematic seeds
    final problematicSeeds = [
      'various artists',
      'various',
      'compilation',
      'soundtrack',
      'original soundtrack',
      'ost',
      'unknown artist',
      'unknown',
      'mixed artists',
      'multiple artists',
      'cast',
      'original cast',
      'ensemble',
      'choir',
      'orchestra',
      'symphony',
    ];

    return problematicSeeds.any((problematic) =>
        artistLower == problematic ||
        artistLower.contains(problematic) ||
        artistLower.startsWith(problematic));
  }

  /// Fallback Spotify search for genre discovery
  Future<void> _fallbackSpotifyGenreSearch(String genre, Set<String> allArtists,
      Map<String, int> artistPopularity) async {
    try {
      // More targeted genre searches
      final genreSearchQueries = [
        'genre:"$genre"',
        '$genre artists',
        '$genre music',
      ];

      for (final query in genreSearchQueries) {
        try {
          final genreSearchResults = await _spotifyService.searchTracks(
            query,
            limit: 20, // OPTIMIZED: Increased from 8 to 20
            context: _getSearchContext(),
          );
          final searchArtists = genreSearchResults
              .map((track) => track.artist)
              .where((artist) => !_isProblematicSeed(artist))
              .toSet();

          for (final artist in searchArtists.take(5)) {
            if (!allArtists.contains(artist)) {
              allArtists.add(artist);
              artistPopularity[artist] = 50;
            }
          }

          print(
              '✓ Added ${searchArtists.length} VALIDATED artists from "$query"');

          // Stop if we have enough artists
          if (allArtists.length >= 10) break;
        } catch (e) {
          print('❌ Error with query "$query": $e');
        }
      }
    } catch (e) {
      print('❌ Fallback Spotify search failed: $e');
    }
  }

  /// Get user's personal top artists that match a specific genre
  Future<List<Map<String, dynamic>>> _getUserArtistsForGenre(
      String genre, BuildContext context) async {
    try {
      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);
      final allTopArtists = <String, Map<String, dynamic>>{};

      if (spotifyProvider.isConnected) {
        print(
            '🎵 [Spotify] Getting comprehensive user artists for genre: $genre');

        // 🚀 ENHANCEMENT: Get comprehensive user data from multiple sources
        final futures = <Future<List<Map<String, dynamic>>>>[
          _spotifyService.getTopArtists(
              limit: 50, timeRange: 'medium_term'), // Last 6 months
          _spotifyService.getTopArtists(
              limit: 50, timeRange: 'long_term'), // All time
        ];

        final results = await Future.wait(futures);
        for (final result in results) {
          for (final artist in result) {
            final artistName = (artist['name'] as String).toLowerCase();
            if (!allTopArtists.containsKey(artistName)) {
              allTopArtists[artistName] = artist;
            }
          }
        }
      } else {
        print('❌ No music service connected for user artists');
        return [];
      }

      final uniqueArtists = allTopArtists.values.toList();
      print(
          '🎯 [Genre Artists] After deduplication: ${uniqueArtists.length} unique artists');

      // Filter artists that actually match the genre
      final genreArtists = <Map<String, dynamic>>[];

      for (final artistInfo in uniqueArtists) {
        final artistGenres = List<String>.from(artistInfo['genres'] ?? []);

        if (_isArtistInGenre(artistGenres, genre)) {
          genreArtists.add(artistInfo);
          print(
              '✅ ${artistInfo['name']} matches $genre - genres: $artistGenres (popularity: ${artistInfo['popularity']})');
        }
      }

      // Sort by popularity (higher popularity = more user engagement)
      genreArtists.sort((a, b) => (b['popularity'] as int? ?? 0)
          .compareTo(a['popularity'] as int? ?? 0));

      print(
          '🎯 Found ${genreArtists.length} user artists matching genre $genre from comprehensive sources');
      return genreArtists;
    } catch (e) {
      print('❌ Error getting user artists for genre $genre: $e');
      return [];
    }
  }

  /// Search for the genre and see which user artists appear in results
  Future<List<String>> _searchGenreForUserArtists(
      String genre, List<String> userArtists) async {
    final matchedArtists = <String>[];

    try {
      // Search for the genre with various terms to get authentic genre tracks
      final genreQueries = [
        '$genre music',
        '$genre songs',
        '$genre artists',
        'best $genre',
        '$genre playlist',
      ];

      final allGenreTracks = <MusicTrack>[];

      for (final query in genreQueries.take(3)) {
        // Limit queries to avoid too many API calls
        try {
          final tracks = await _spotifyService.searchTracks(
            query,
            limit: 25, // OPTIMIZED: Increased from 10 to 25
            context: _getSearchContext(),
          );
          allGenreTracks.addAll(tracks);
        } catch (e) {
          print('❌ Error searching for "$query": $e');
        }
      }

      // Check which of our user's artists appear in these authentic genre results
      for (final userArtist in userArtists.take(15)) {
        final artistFoundInGenre = allGenreTracks
            .any((track) => _isArtistMatch(track.artist, userArtist));

        if (artistFoundInGenre) {
          matchedArtists.add(userArtist);
          print('✅ $userArtist found in authentic $genre search results');
        }
      }
    } catch (e) {
      print('❌ Error in genre reverse lookup: $e');
    }

    return matchedArtists;
  }

  /// Validate specific artists for a genre using more conservative matching
  Future<List<String>> _validateArtistsForGenre(String genre,
      List<String> allArtists, List<String> alreadyMatched) async {
    final additionalMatches = <String>[];

    // Only do this for well-known genres to avoid false positives
    final knownGenres = {
      // Hip-Hop & Rap
      'hip hop',
      'rap',
      'trap',
      'uk drill',
      'chicago drill',
      'boom bap',
      'conscious hip hop',
      'gangsta rap',
      'mumble rap',
      'cloud rap',
      'underground hip hop',
      'old school hip hop',
      'east coast hip hop',
      'west coast hip hop',
      'southern hip hop',
      'afro trap',

      // Pop & Mainstream
      'pop',
      'electropop',
      'synth pop',
      'dance pop',
      'pop rock',
      'indie pop',
      'k-pop',
      'j-pop',
      'teen pop',
      'bubblegum pop',
      'power pop',
      'art pop',
      'hyperpop',

      // Electronic & Dance
      'electronic',
      'dance',
      'edm',
      'house',
      'techno',
      'trance',
      'dubstep',
      'drum and bass',
      'breakbeat',
      'garage',
      'uk garage',
      'future garage',
      'ambient',
      'downtempo',
      'chillout',
      'idm',
      'glitch',
      'synthwave',
      'vaporwave',
      'future bass',
      'trap music',
      'hardstyle',
      'hardcore',
      'deep house',
      'tech house',
      'progressive house',
      'melodic dubstep',
      'riddim',
      'bass music',
      'experimental electronic',

      // Rock & Metal
      'rock',
      'metal',
      'punk',
      'alternative',
      'indie rock',
      'classic rock',
      'hard rock',
      'heavy metal',
      'death metal',
      'black metal',
      'thrash metal',
      'metalcore',
      'post-rock',
      'progressive rock',
      'psychedelic rock',
      'garage rock',
      'grunge',
      'shoegaze',
      'post-punk',
      'hardcore punk',
      'pop punk',
      'emo',
      'screamo',
      'math rock',
      'stoner rock',
      'doom metal',
      'sludge metal',
      'nu metal',

      // R&B, Soul & Funk
      'r&b',
      'soul',
      'funk',
      'neo soul',
      'contemporary r&b',
      'alternative r&b',
      'motown',
      'disco',
      'gospel',
      'blues',
      'rhythm and blues',
      'quiet storm',

      // Jazz & Classical
      'jazz',
      'classical',
      'bebop',
      'smooth jazz',
      'fusion',
      'contemporary jazz',
      'free jazz',
      'swing',
      'big band',
      'chamber music',
      'opera',
      'baroque',
      'romantic',
      'modern classical',
      'minimalism',
      'neo-classical',

      // Folk & Country
      'folk',
      'country',
      'americana',
      'bluegrass',
      'indie folk',
      'folk rock',
      'singer-songwriter',
      'acoustic',
      'alt-country',
      'outlaw country',
      'country pop',
      'folk pop',
      'new folk',
      'freak folk',

      // World & Regional
      'reggae',
      'ska',
      'dub',
      'dancehall',
      'afrobeat',
      'afro-pop',
      'latin',
      'salsa',
      'bachata',
      'reggaeton',
      'latin pop',
      'bossa nova',
      'samba',
      'tango',
      'flamenco',
      'celtic',
      'irish folk',
      'world music',
      'indian classical',
      'bollywood',
      'arabic music',
      'african music',
      'caribbean',

      // Alternative & Experimental
      'experimental',
      'avant-garde',
      'noise',
      'dark ambient',
      'drone',
      'field recording',
      'sound art',
      'musique concrète',
      'electroacoustic',
      'new age',
      'meditation music',

      // Modern & Internet Genres
      'lo-fi',
      'lo-fi hip hop',
      'chillhop',
      'study beats',
      'bedroom pop',
      'dream pop',
      'chillwave',
      'witch house',
      'seapunk',
      'phonk',
      'jersey club',
      'baltimore club',
      'footwork',
      'juke',
      'uk funky',
      'grime',
      'bassline',
      'speed garage',

      // Fusion & Crossover
      'jazz fusion',
      'rock fusion',
      'nu-disco',
      'electro swing',
      'folktronica',
      'trip hop',
      'downtempo',
      'chillstep',
      'liquid drum and bass',
      'neurofunk',
      'atmospheric drum and bass',
      'jungle',
      'ragga jungle',
      'hardtek',
      'gabber',

      // Seasonal & Mood
      'christmas music',
      'holiday music',
      'summer hits',
      'chill music',
      'workout music',
      'party music',
      'romantic music',
      'sad music',
      'happy music',
      'relaxing music'
    };

    final genreLower = genre.toLowerCase();
    final isKnownGenre = knownGenres.any((knownGenre) =>
        genreLower.contains(knownGenre) || knownGenre.contains(genreLower));

    if (!isKnownGenre) {
      print('⚠️ Skipping artist validation for unknown genre: $genre');
      return additionalMatches;
    }

    // Try a few artists with conservative matching
    for (final artist in allArtists.take(10)) {
      if (alreadyMatched.contains(artist)) continue;
      if (additionalMatches.length >= 3) break; // Limit additional matches

      try {
        // Use artist name only in genre context (not "artist + genre")
        final artistTracks = await _spotifyService.searchTracks(
          artist,
          limit: 5,
          context: _getSearchContext(),
        );

        // Then check if any of these tracks appear when searching for the genre
        var artistMatchesGenre = false;
        for (final track in artistTracks.take(3)) {
          final genreCheck = await _spotifyService.searchTracks(
            '$genre ${track.title}',
            limit: 3,
            context: _getSearchContext(),
          );
          if (genreCheck
              .any((genreTrack) => _isArtistMatch(genreTrack.artist, artist))) {
            artistMatchesGenre = true;
            break;
          }
        }

        if (artistMatchesGenre) {
          additionalMatches.add(artist);
          print('✅ $artist validated for $genre through track matching');
        }
      } catch (e) {
        print('❌ Error validating artist $artist for genre $genre: $e');
      }
    }

    return additionalMatches;
  }

  /// Helper method to check if two artist names match (accounting for variations)
  bool _isArtistMatch(String spotifyArtist, String userArtist) {
    final spotifyLower = spotifyArtist.toLowerCase();
    final userLower = userArtist.toLowerCase();

    // Exact match
    if (spotifyLower == userLower) return true;

    // One contains the other (for cases like "Artist" vs "Artist feat. Someone")
    if (spotifyLower.contains(userLower) || userLower.contains(spotifyLower)) {
      // Make sure it's not a substring of a larger word
      final words = spotifyLower.split(' ');
      final userWords = userLower.split(' ');
      return words.any((word) => userWords.contains(word));
    }

    return false;
  }

  /// Check if an artist's genres match a target genre (strict matching)
  bool _isArtistInGenre(List<String> artistGenres, String targetGenre) {
    if (artistGenres.isEmpty) return false;

    // Use the proper validation from SpotifyGenreService for strict genre matching
    return SpotifyGenreService.isValidArtistForTargetGenre(
        artistGenres, targetGenre);
  }

  /// Safe partial matching for compound genres only
  bool _isSafePartialMatch(String artistGenre, String targetGenre) {
    // Only allow partial matches for specific safe cases
    final safePartialMatches = <String, List<String>>{
      'edm': ['electronic dance music', 'dance music', 'electronic music'],
      'hip-hop': ['hip hop', 'hip hop music'],
      'hip hop': ['hip-hop', 'hip hop music'],
      'r&b': ['rhythm and blues', 'contemporary r&b', 'alternative r&b'],
      'drum-and-bass': ['drum and bass', 'dnb'],
      'drum and bass': ['drum-and-bass', 'dnb'],
    };

    final allowedMatches = safePartialMatches[targetGenre] ?? [];
    return allowedMatches
        .any((allowed) => artistGenre == allowed || allowed == artistGenre);
  }

  /// Check if two genres are related (strict separation between major genres)
  bool _areGenresRelated(String genre1, String genre2) {
    return SpotifyGenreService.areGenresRelated(genre1, genre2);
  }

  /// Get tracks from user's favorite artists within the genre using proper genre filtering
  Future<List<MusicTrack>> _getTracksFromFavoriteArtistsInGenre(
      List<String> artists, String genre, int offset) async {
    final allTracks = <MusicTrack>[];

    try {
      print(
          '🎯 [Genre Artists] Getting tracks for ${artists.length} artists in $genre');

      // 🚀 ENHANCEMENT: Use Last.fm for better similar artist discovery within genre
      final lastFmService = LastFmService();
      final expandedArtists = <String>[];

      if (lastFmService.isConfigured && artists.isNotEmpty) {
        print(
            '🎵 [Genre Artists] Using Last.fm to expand artist pool for $genre');

        try {
          // Get similar artists for each of the user's favorite artists
          for (final artist in artists.take(3)) {
            final similarArtists =
                await lastFmService.getSimilarArtists(artist, limit: 8);
            expandedArtists.addAll(similarArtists);
            print(
                '🎯 [Genre Artists] Last.fm found ${similarArtists.length} similar artists for: $artist');
          }

          // Combine original and similar artists, remove duplicates
          final allArtistsToSearch =
              <String>{...artists, ...expandedArtists}.toList();
          allArtistsToSearch.shuffle();

          print(
              '🎯 [Genre Artists] Expanded from ${artists.length} to ${allArtistsToSearch.length} artists with Last.fm');

          // Search for tracks from expanded artist list
          final startIndex =
              (offset * 2) % math.max(allArtistsToSearch.length, 1);

          for (int i = 0; i < math.min(6, allArtistsToSearch.length); i++) {
            // Increased from 3 to 6
            final int artistIndex =
                ((startIndex + i) % allArtistsToSearch.length).toInt();
            final artist = allArtistsToSearch[artistIndex];

            try {
              // Use both genre-specific and artist-specific queries
              final queries = [
                'genre:"$genre" artist:"$artist"',
                '$artist $genre music',
                'artist:"$artist" popular',
                '$artist hits',
              ];

              final query = queries[i % queries.length];
              final tracks = await _spotifyService.searchTracks(
                query,
                limit: 15, // OPTIMIZED: Increased from 6 to 15
                context: _getSearchContext(),
              );
              allTracks.addAll(tracks);
              print(
                  '📥 [Genre Artists] Got ${tracks.length} tracks for "$query"');
            } catch (e) {
              print(
                  '❌ [Genre Artists] Error getting tracks for artist $artist: $e');
            }
          }
        } catch (e) {
          print(
              '❌ [Genre Artists] Error using Last.fm: $e, falling back to basic search');
          await _performBasicGenreArtistSearch(
              artists, genre, offset, allTracks);
        }
      } else {
        print(
            '⚠️ [Genre Artists] Last.fm not configured or no artists, using basic search');
        await _performBasicGenreArtistSearch(artists, genre, offset, allTracks);
      }

      // Try advanced Spotify methods if available
      if (allTracks.length < 20) {
        try {
          // Use the new multi-artist inspiration method with genre constraint
          final inspirationTracks =
              await _spotifyService.searchTracksInspiredByArtists(
            artists.take(3).toList(), // Use top 3 artists
            genre: genre,
            limit: 40, // OPTIMIZED: Increased from 20 to 40
          );
          allTracks.addAll(inspirationTracks);
          print(
              '📥 [Genre Artists] Got ${inspirationTracks.length} tracks inspired by ${artists.length} artists in $genre');

          // Also try the artist seeds method for more variety
          final seedTracks = await _spotifyService.searchWithArtistSeeds(
            artists.take(2).toList(),
            targetGenre: genre,
            limit: 30, // OPTIMIZED: Increased from 15 to 30
            excludeArtists:
                artists, // Exclude the seed artists to get new discoveries
          );
          allTracks.addAll(seedTracks);
          print(
              '📥 [Genre Artists] Got ${seedTracks.length} tracks using artist seeds in $genre');
        } catch (e) {
          print('❌ [Genre Artists] Error with advanced Spotify methods: $e');
        }
      }
    } catch (e) {
      print('❌ [Genre Artists] Error in main method: $e');
      await _performBasicGenreArtistSearch(artists, genre, offset, allTracks);
    }

    print('🎯 [Genre Artists] Total tracks collected: ${allTracks.length}');
    return allTracks;
  }

  /// Fallback method for basic genre artist search
  Future<void> _performBasicGenreArtistSearch(List<String> artists,
      String genre, int offset, List<MusicTrack> allTracks) async {
    final startIndex = (offset * 2) % math.max(artists.length, 1).toInt();

    for (int i = 0; i < math.min(3, artists.length); i++) {
      final artistIndex = (startIndex + i) % artists.length;
      final artist = artists[artistIndex];

      try {
        // Use proper genre syntax first
        final queries = [
          'genre:"$genre" similar to "$artist"',
          'genre:"$genre" inspired by "$artist"',
          'genre:"$genre" fans of "$artist"',
          'genre:"$genre" like "$artist"',
        ];

        final query = queries[i % queries.length];
        final tracks = await _spotifyService.searchTracks(
          query,
          limit: 8,
          context: _getSearchContext(),
        );
        allTracks.addAll(tracks);
        print('📥 [Genre Artists] Got ${tracks.length} tracks for "$query"');
      } catch (e) {
        print(
            '❌ [Genre Artists] Error getting tracks similar to artist $artist in genre $genre: $e');
      }
    }
  }

  /// Get general genre tracks using proper Spotify genre filtering
  Future<List<MusicTrack>> _getGeneralGenreTracks(
      String genre, int offset) async {
    final allTracks = <MusicTrack>[];

    try {
      final genreAliases = _getGenreAliases(genre);

      // First try proper genre filtering
      for (final alias in genreAliases) {
        try {
          final genreFilteredTracks = await _spotifyService.searchTracksByGenre(
            alias,
            limit: 8,
            offset: offset * 8,
          );
          allTracks.addAll(genreFilteredTracks);
          print(
              '📥 Got ${genreFilteredTracks.length} tracks using genre filter for "$alias"');
        } catch (e) {
          print('⚠️ Genre filter failed for "$alias": $e');
        }
      }

      // If we don't have enough tracks, supplement with targeted searches
      if (allTracks.length < 6) {
        final queries = [
          'genre:"$genre"', // Proper genre syntax
          '$genre music',
          '$genre songs',
          '$genre new releases',
          '$genre trending',
          '$genre popular',
          'best $genre music',
          '$genre hits',
          '$genre fresh',
          '$genre playlist',
          '$genre radio',
          'top $genre tracks',
          '$genre classics',
        ];

        final startIndex = (offset * 3) % queries.length;

        // Get 3 queries per page for more variety
        for (int i = 0; i < 3; i++) {
          final queryIndex = (startIndex + i) % queries.length;
          final query = queries[queryIndex];

          try {
            final tracks = await _spotifyService.searchTracks(
              query,
              limit: 4,
              offset: (offset * 12) + (i * 4),
            );
            allTracks.addAll(tracks);
            print('📥 Got ${tracks.length} tracks for general "$query"');
          } catch (e) {
            print('❌ Error getting general tracks for "$query": $e');
          }
        }
      }
    } catch (e) {
      print('❌ Error in general genre tracks: $e');
    }

    return allTracks;
  }

  /// Get discovery tracks within the genre using proper genre filtering
  Future<List<MusicTrack>> _getGenreDiscoveryTracks(
      String genre, List<String> favoriteArtists, int offset) async {
    final allTracks = <MusicTrack>[];

    try {
      final aliases = _getGenreAliases(genre);
      final pureGenreQueries = <String>[];
      for (final alias in aliases) {
        pureGenreQueries.addAll([
          'genre:"$alias" tag:new',
          'genre:"$alias" underground',
          'genre:"$alias" indie',
          'genre:"$alias" emerging',
          'genre:"$alias" rising',
          'genre:"$alias" breakthrough',
        ]);
      }

      for (final query in pureGenreQueries.take(3)) {
        try {
          final tracks = await _spotifyService.searchTracks(
            query,
            limit: 5,
            offset: (offset * 15),
          );
          allTracks.addAll(tracks);
          print('📥 Got ${tracks.length} discovery tracks for "$query"');
        } catch (e) {
          print('❌ Error getting discovery tracks for "$query": $e');
        }
      }

      // If we have favorite artists, use them for discovery within the genre
      if (favoriteArtists.isNotEmpty && allTracks.length < 10) {
        final artistDiscoveryTracks =
            await _spotifyService.searchWithArtistSeeds(
          favoriteArtists.take(2).toList(),
          targetGenre: genre,
          limit: 10,
          excludeArtists:
              favoriteArtists, // Exclude known artists for discovery
        );
        allTracks.addAll(artistDiscoveryTracks);
        print(
            '📥 Got ${artistDiscoveryTracks.length} artist-based discovery tracks in $genre');
      }
    } catch (e) {
      print('❌ Error in genre discovery: $e');

      // Fallback to text-based discovery queries
      final discoveryQueries = [
        'genre:"$genre" underground',
        'genre:"$genre" indie',
        'genre:"$genre" emerging artists',
        'genre:"$genre" new artists',
        'genre:"$genre" breakthrough',
        'genre:"$genre" rising stars',
        'genre:"$genre" discoveries',
        'hidden $genre gems',
      ];

      final startIndex = (offset * 2) % discoveryQueries.length;

      for (int i = 0; i < 3; i++) {
        final queryIndex = (startIndex + i) % discoveryQueries.length;
        final query = discoveryQueries[queryIndex];

        try {
          final tracks = await _spotifyService.searchTracks(
            query,
            limit: 5,
            offset: (offset * 15) + (i * 5),
          );
          allTracks.addAll(tracks);
          print('📥 Got ${tracks.length} discovery tracks for "$query"');
        } catch (e) {
          print('❌ Error getting discovery tracks for "$query": $e');
        }
      }
    }

    return allTracks;
  }

  /// Get tracks for a specific playlist with pagination
  Future<List<MusicTrack>> _getTracksForPlaylist(
      String playlistId, int offset, BuildContext context) async {
    try {
      print('📚 Getting tracks for playlist: $playlistId (offset: $offset)');

      final spotifyProvider =
          Provider.of<SpotifyProvider>(context, listen: false);

      // Handle special collection IDs (fallback collections)
      if (playlistId == 'liked_songs_collection') {
        print('📚 Getting tracks from liked songs collection');
        await spotifyProvider.loadLikedSongs();
        final likedSongs = spotifyProvider.likedSongs;
        final startIndex = offset * _pageSize;
        final endIndex = math.min(startIndex + _pageSize, likedSongs.length);

        if (startIndex >= likedSongs.length) return [];

        final tracks = likedSongs.sublist(startIndex, endIndex);
        tracks.shuffle(); // Add randomization
        return tracks;
      }

      if (playlistId == 'recently_played_collection') {
        print('📚 Getting tracks from recently played collection');
        final recentTracks = await _spotifyService.getRecentlyPlayed(limit: 50);
        final startIndex = offset * _pageSize;
        final endIndex = math.min(startIndex + _pageSize, recentTracks.length);

        if (startIndex >= recentTracks.length) return [];

        final tracks = recentTracks.sublist(startIndex, endIndex);
        tracks.shuffle(); // Add randomization
        return tracks;
      }

      // Handle real Spotify playlists
      print('📚 Getting tracks from real Spotify playlist: $playlistId');

      final playlistData = await _spotifyService.getPlaylistTracks(
        playlistId,
        limit: _pageSize * 3, // OPTIMIZED: Increased from 2x to 3x page size for more variety
        offset: offset * _pageSize,
      );

      print('🔍 [DEBUG] Playlist data keys: ${playlistData.keys.toList()}');

      final tracks = playlistData['tracks'] as List<MusicTrack>? ?? [];

      if (tracks.isEmpty) {
        print('⚠️ No tracks found in playlist $playlistId');
        print('🔍 [DEBUG] Full playlist data: $playlistData');
        return [];
      }

      // Add randomization by shuffling tracks
      final shuffledTracks = List<MusicTrack>.from(tracks)..shuffle();

      print('✅ Got ${tracks.length} tracks from playlist $playlistId');
      return shuffledTracks;
    } catch (e) {
      print('❌ Error getting tracks for playlist $playlistId: $e');
      print('❌ Stack trace: ${StackTrace.current}');
      return [];
    }
  }

  /// Refresh recommendations with fresh content
  Future<void> refreshRecommendations(BuildContext context) async {
    print(
        '🔄 [REFRESH] Starting refresh for category: $_currentCategory, genre: $_selectedGenre');

    // Clear all caches and tracking for truly fresh content
    _currentRecommendations.clear();
    _recentTrackIds.clear();
    _excludedTrackIds.clear();
    _usedQueries.clear();
    _recentArtists.clear();
    _usedArtistQueryCombos.clear();
    _queryVariationSeed = math.Random().nextInt(1000);
    _currentPage = 0;
    _hasMoreContent = true;

    // Clear service caches
    _aiRecommendationService.resetPagination();
    _spotifyService.clearSearchCache();

    // Clear local caches
    _cachedRecentlyPlayed = null;
    _recentlyPlayedCacheTime = null;
    _cachedRecentlyAdded = null;
    _recentlyAddedCacheTime = null;

    // Clear categorized recommendations for fresh start
    _categorizedRecommendations.clear();

    // Clear genre-specific recommendations
    _genreRecommendations.clear();
    for (final genre in _userTopGenres) {
      _genreRecommendations.putIfAbsent(genre, () => []);
    }

    // Shuffle the order of artists and genres for varied exploration
    if (_userTopArtists.isNotEmpty) {
      _userTopArtists = List<String>.from(_userTopArtists)..shuffle();
      _aiRecommendationService.setUserTopArtists(_userTopArtists);
      print(
          '🔀 [REFRESH] Shuffled ${_userTopArtists.length} artists for varied exploration');
    }

    // if (_userTopGenres.isNotEmpty) {
    //   _userTopGenres = List<String>.from(_userTopGenres)..shuffle();
    //   print(
    //       '🔀 [REFRESH] Shuffled ${_userTopGenres.length} genres for varied recommendations');
    // }

    print('🧹 [REFRESH] Cleared all caches and tracking');

    // Notify UI that we're starting fresh
    notifyListeners();

    try {
      // Reload exclusion data for fresh filtering
      await _loadExclusionData(context);

      // Use the same initialization logic that works on app start
      print('🔄 [REFRESH] Calling _loadInitialRecommendations...');
      await _loadInitialRecommendations(context);

      // Show a fresh AI message
      _updateAiMessageForRefresh();

      print(
          '✅ [REFRESH] Recommendations refreshed successfully for $_currentCategory! Got ${_currentRecommendations.length} tracks');
    } catch (e) {
      print('❌ [REFRESH] Error refreshing recommendations: $e');

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to refresh recommendations'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Refresh based on current category and genre context
  Future<void> _refreshCurrentContext(BuildContext context) async {
    switch (_currentCategory) {
      case 'genreBased':
        if (_selectedGenre != null) {
          // Refresh specific genre
          print('🎯 Refreshing specific genre: $_selectedGenre');
          await _loadGenreSpecificTracks(
              _selectedGenre!, context, _nextGenreRequestId());
        } else {
          // Refresh general genre-based recommendations
          print('🎵 Refreshing general genre-based recommendations');
          final topGenres = await _spotifyService.getTopGenres(limit: 5);
          final genreTracks =
              await _getRecommendationsForGenres(topGenres, context);
          final shuffledGenreTracks = _intelligentShuffle(genreTracks);
          _categorizedRecommendations['genreBased'] = shuffledGenreTracks;
          _currentRecommendations = shuffledGenreTracks;
        }
        break;

      case 'moodBased':
        print('🎭 Refreshing mood-based recommendations');
        await _loadMoodBasedRecommendations(context);
        break;

      case 'artistBased':
        print('🎤 Refreshing artist-based recommendations');
        List<MusicTrack> topTracks = [];
        List<String> favoriteArtists = [];
        try {
          topTracks = await _spotifyService.getTopTracks(limit: 5);
          favoriteArtists =
              topTracks.map((track) => track.artist).toSet().take(5).toList();
        } catch (_) {
          favoriteArtists = _userTopArtists.take(5).toList();
        }
        final artistTracks =
            await _getRecommendationsForArtists(favoriteArtists);
        final shuffledArtistTracks = _intelligentShuffle(artistTracks);
        _categorizedRecommendations['artistBased'] = shuffledArtistTracks;
        _currentRecommendations = shuffledArtistTracks;
        break;

      case 'topTracks':
        print('🏆 Refreshing top tracks recommendations');
        final similarTracks = await _getSimilarToTopTracks(context);
        final shuffledSimilarTracks = _intelligentShuffle(similarTracks);
        _categorizedRecommendations['topTracks'] = shuffledSimilarTracks;
        _currentRecommendations = shuffledSimilarTracks;
        break;

      case 'likedSongs':
        print('❤️ Refreshing liked songs recommendations');
        final likedTracks = await _getSimilarToLikedSongs(context);
        final shuffledLikedTracks = _intelligentShuffle(likedTracks);
        _categorizedRecommendations['likedSongs'] = shuffledLikedTracks;
        _currentRecommendations = shuffledLikedTracks;
        break;

      case 'recentlyPlayed':
        print('🕒 Refreshing recently played recommendations');
        final recentTracks = await _getSimilarToRecentlyPlayed(context);
        final shuffledRecentTracks = _intelligentShuffle(recentTracks);
        _categorizedRecommendations['recentlyPlayed'] = shuffledRecentTracks;
        _currentRecommendations = shuffledRecentTracks;
        break;

      case 'recentlyAdded':
        print('🕒 Refreshing recently added recommendations');
        // Reset recently added state for fresh results
        _currentPage = 0;
        _recentTrackIds.clear();
        _spotifyService.clearSearchCache(); // Clear cache to prevent loops
        final recentlyAddedTracks = await _getRecentlyAdded(context);
        final shuffledRecentlyAddedTracks =
            _intelligentShuffle(recentlyAddedTracks);
        _categorizedRecommendations['recentlyAdded'] =
            shuffledRecentlyAddedTracks;
        _currentRecommendations = shuffledRecentlyAddedTracks;
        break;

      case 'discover':
        print('🔍 Refreshing discovery recommendations');
        // Reset discovery state for fresh results
        _currentPage = 0;
        _recentTrackIds.clear();
        _spotifyService.clearSearchCache(); // Clear cache to prevent loops
        final discoverTracks = await _getDiscoverTracks(context);
        final shuffledDiscoverTracks = _intelligentShuffle(discoverTracks);
        _categorizedRecommendations['discover'] = shuffledDiscoverTracks;
        _currentRecommendations = shuffledDiscoverTracks;
        break;

      case 'all':
      default:
        print('🌍 Refreshing all recommendations');
        // For 'all' category, do a comprehensive refresh
        if (math.Random().nextBool()) {
          await _loadPersonalizedRecommendations(context);
        } else {
          await _loadMoodBasedRecommendations(context);
        }
        break;
    }
  }

  /// Shuffle current recommendations for initial loads and refreshes only
  /// Note: This shuffles the entire list, so only use for fresh content, not when adding to existing list
  void _shuffleCurrentRecommendations() {
    if (_currentRecommendations.isNotEmpty) {
      _debugTrackDistribution(_currentRecommendations,
          label: "CURRENT tracks BEFORE shuffle");
      _currentRecommendations = _intelligentShuffle(_currentRecommendations);
      _debugTrackDistribution(_currentRecommendations,
          label: "CURRENT tracks AFTER shuffle");

      // Update the categorized recommendations for the current category
      if (_categorizedRecommendations.containsKey(_currentCategory)) {
        _categorizedRecommendations[_currentCategory] = _currentRecommendations;
      }
      notifyListeners();
    }
  }

  /// Intelligent shuffle that distributes songs from same artist/album evenly
  List<MusicTrack> _intelligentShuffle(List<MusicTrack> tracks) {
    if (tracks.length <= 2) {
      tracks.shuffle();
      return tracks;
    }

    // Group tracks by artist and album for better distribution
    final Map<String, List<MusicTrack>> artistGroups = {};
    final Map<String, List<MusicTrack>> albumGroups = {};

    for (final track in tracks) {
      final artistKey = track.artist.toLowerCase().trim();
      final albumKey =
          '${track.artist.toLowerCase().trim()}_${track.album.toLowerCase().trim()}';

      artistGroups.putIfAbsent(artistKey, () => []).add(track);
      albumGroups.putIfAbsent(albumKey, () => []).add(track);
    }

    // Shuffle each group internally
    for (final group in artistGroups.values) {
      group.shuffle();
    }

    final List<MusicTrack> result = [];
    final List<MusicTrack> remaining = List.from(tracks)..shuffle();

    // Track recent placements to avoid clustering
    final List<String> recentArtists = [];
    final List<String> recentAlbums = [];
    const int lookBackDistance =
        12; // Don't place same artist/album within 12 positions

    while (remaining.isNotEmpty) {
      MusicTrack? bestTrack;
      int bestScore = -1;

      // Find the best track to place next
      for (int i = 0; i < remaining.length; i++) {
        final track = remaining[i];
        final artistKey = track.artist.toLowerCase().trim();
        final albumKey =
            '${track.artist.toLowerCase().trim()}_${track.album.toLowerCase().trim()}';

        int score = 0;

        // Prefer tracks from artists/albums not recently placed
        if (!recentArtists.contains(artistKey)) score += 3;
        if (!recentAlbums.contains(albumKey)) score += 2;

        // Prefer tracks from artists with more songs (to distribute them better)
        final artistGroupSize = artistGroups[artistKey]?.length ?? 1;
        if (artistGroupSize > 1) score += 1;

        // Add minimal randomness only as tiebreaker
        if (score > 0) score += math.Random().nextInt(2);

        if (score > bestScore) {
          bestScore = score;
          bestTrack = track;
        }
      }

      if (bestTrack != null) {
        result.add(bestTrack);
        remaining.remove(bestTrack);

        final artistKey = bestTrack.artist.toLowerCase().trim();
        final albumKey =
            '${bestTrack.artist.toLowerCase().trim()}_${bestTrack.album.toLowerCase().trim()}';

        // Update recent tracking
        recentArtists.insert(0, artistKey);
        recentAlbums.insert(0, albumKey);

        // Keep only recent items within lookback distance
        if (recentArtists.length > lookBackDistance) {
          recentArtists.removeLast();
        }
        if (recentAlbums.length > lookBackDistance) {
          recentAlbums.removeLast();
        }
      } else {
        // Fallback: find track that conflicts least with recent placements
        MusicTrack fallbackTrack = remaining.first;
        int minConflicts = 999;

        for (final track in remaining) {
          final artistKey = track.artist.toLowerCase().trim();
          final albumKey =
              '${track.artist.toLowerCase().trim()}_${track.album.toLowerCase().trim()}';

          int conflicts = 0;
          if (recentArtists.contains(artistKey)) conflicts += 2;
          if (recentAlbums.contains(albumKey)) conflicts += 1;

          if (conflicts < minConflicts) {
            minConflicts = conflicts;
            fallbackTrack = track;
          }
        }

        result.add(fallbackTrack);
        remaining.remove(fallbackTrack);

        final artistKey = fallbackTrack.artist.toLowerCase().trim();
        final albumKey =
            '${fallbackTrack.artist.toLowerCase().trim()}_${fallbackTrack.album.toLowerCase().trim()}';

        recentArtists.insert(0, artistKey);
        recentAlbums.insert(0, albumKey);

        if (recentArtists.length > lookBackDistance) {
          recentArtists.removeLast();
        }
        if (recentAlbums.length > lookBackDistance) {
          recentAlbums.removeLast();
        }
      }
    }

    return result;
  }

  /// Intelligently shuffle new tracks while considering existing tracks to avoid clustering
  List<MusicTrack> _intelligentShuffleNewTracks(List<MusicTrack> newTracks) {
    if (newTracks.isEmpty) return [];
    if (newTracks.length <= 2) {
      newTracks.shuffle();
      return newTracks;
    }

    // Group tracks by artist and album for better distribution
    final Map<String, List<MusicTrack>> artistGroups = {};
    final Map<String, List<MusicTrack>> albumGroups = {};

    for (final track in newTracks) {
      final artistKey = track.artist.toLowerCase().trim();
      final albumKey =
          '${track.artist.toLowerCase().trim()}_${track.album.toLowerCase().trim()}';

      artistGroups.putIfAbsent(artistKey, () => []).add(track);
      albumGroups.putIfAbsent(albumKey, () => []).add(track);
    }

    // Shuffle each group internally
    for (final group in artistGroups.values) {
      group.shuffle();
    }

    final List<MusicTrack> result = [];
    final List<MusicTrack> remaining = List.from(newTracks)..shuffle();

    // Get recent artists/albums from end of existing list to avoid clustering
    final List<String> recentArtists = [];
    final List<String> recentAlbums = [];

    if (_currentRecommendations.isNotEmpty) {
      final int checkDistance = math.min(12, _currentRecommendations.length);
      final recentTracks = _currentRecommendations
          .skip(_currentRecommendations.length - checkDistance)
          .toList();

      for (final track in recentTracks) {
        recentArtists.add(track.artist.toLowerCase().trim());
        recentAlbums.add(
            '${track.artist.toLowerCase().trim()}_${track.album.toLowerCase().trim()}');
      }
    }

    const int lookBackDistance = 12;

    while (remaining.isNotEmpty) {
      MusicTrack? bestTrack;
      int bestScore = -1;

      // Find the best track to place next
      for (int i = 0; i < remaining.length; i++) {
        final track = remaining[i];
        final artistKey = track.artist.toLowerCase().trim();
        final albumKey =
            '${track.artist.toLowerCase().trim()}_${track.album.toLowerCase().trim()}';

        int score = 0;

        // Prefer tracks from artists/albums not recently placed
        if (!recentArtists.contains(artistKey)) score += 3;
        if (!recentAlbums.contains(albumKey)) score += 2;

        // Prefer tracks from artists with more songs (to distribute them better)
        final artistGroupSize = artistGroups[artistKey]?.length ?? 1;
        if (artistGroupSize > 1) score += 1;

        // Add minimal randomness only as tiebreaker
        if (score > 0) score += math.Random().nextInt(2);

        if (score > bestScore) {
          bestScore = score;
          bestTrack = track;
        }
      }

      if (bestTrack != null) {
        result.add(bestTrack);
        remaining.remove(bestTrack);

        final artistKey = bestTrack.artist.toLowerCase().trim();
        final albumKey =
            '${bestTrack.artist.toLowerCase().trim()}_${bestTrack.album.toLowerCase().trim()}';

        // Update recent tracking
        recentArtists.insert(0, artistKey);
        recentAlbums.insert(0, albumKey);

        // Keep only recent items within lookback distance
        if (recentArtists.length > lookBackDistance) {
          recentArtists.removeLast();
        }
        if (recentAlbums.length > lookBackDistance) {
          recentAlbums.removeLast();
        }
      } else {
        // Fallback: find track that conflicts least with recent placements
        MusicTrack fallbackTrack = remaining.first;
        int minConflicts = 999;

        for (final track in remaining) {
          final artistKey = track.artist.toLowerCase().trim();
          final albumKey =
              '${track.artist.toLowerCase().trim()}_${track.album.toLowerCase().trim()}';

          int conflicts = 0;
          if (recentArtists.contains(artistKey)) conflicts += 2;
          if (recentAlbums.contains(albumKey)) conflicts += 1;

          if (conflicts < minConflicts) {
            minConflicts = conflicts;
            fallbackTrack = track;
          }
        }

        result.add(fallbackTrack);
        remaining.remove(fallbackTrack);

        final artistKey = fallbackTrack.artist.toLowerCase().trim();
        final albumKey =
            '${fallbackTrack.artist.toLowerCase().trim()}_${fallbackTrack.album.toLowerCase().trim()}';

        recentArtists.insert(0, artistKey);
        recentAlbums.insert(0, albumKey);

        if (recentArtists.length > lookBackDistance) {
          recentArtists.removeLast();
        }
        if (recentAlbums.length > lookBackDistance) {
          recentAlbums.removeLast();
        }
      }
    }

    return result;
  }

  /// Get cached tracks for an artist, fetching and storing ALL tracks if not cached
  Future<List<MusicTrack>> _getCachedArtistTracks(String artist,
      {int limit = 20}) async {
    print(
        '🗄️ [Artist Cache] ENTRY: _getCachedArtistTracks called for "$artist" (limit: $limit)');
    final stopwatch = Stopwatch()..start();
    final artistKey = artist.toLowerCase().trim();
    final now = DateTime.now();

    // Check if we have valid cached data
    if (_artistTrackCache.containsKey(artistKey) &&
        _artistCacheTimestamps.containsKey(artistKey)) {
      final cacheTime = _artistCacheTimestamps[artistKey]!;
      if (now.difference(cacheTime) < _artistCacheExpiry) {
        print(
            '🗄️ [Artist Cache] Cache HIT for "$artist" (${_artistTrackCache[artistKey]!.length} total tracks)');

        // Return random selection from cache for variety
        final allCachedTracks =
            List<MusicTrack>.from(_artistTrackCache[artistKey]!);
        allCachedTracks.shuffle();

        // Update access time for LRU management
        _artistCacheTimestamps[artistKey] = now;

        stopwatch.stop();
        print(
            '⏱️ [Artist Cache] Cache hit for "$artist" in ${stopwatch.elapsedMilliseconds}ms');

        return allCachedTracks.take(limit).toList();
      } else {
        print('🗄️ [Artist Cache] Cache EXPIRED for "$artist", refreshing...');
      }
    } else {
      print('🗄️ [Artist Cache] Cache MISS for "$artist", fetching...');
    }

    // Fetch tracks for this artist and cache them - OPTIMIZED FOR LARGE LISTS
    print('🗄️ [Artist Cache] Fetching tracks for "$artist"...');
    try {
      // Get tracks from multiple sources for comprehensive coverage
      final List<MusicTrack> allArtistTracks = [];

      // OPTIMIZATION: For large artist lists, use more efficient fetching strategy
      if (_artistTrackCache.length > 20) {
        // Use lighter approach when processing many artists
        print('🗄️ [Artist Cache] Using optimized fetching for "$artist" (large list mode)...');
        final hybridTracks = await _spotifyService.getArtistTracksHybrid(
          artist,
          limit: 30, // Reduced from 50 for faster processing
          includeAppearsOn: false, // Skip appears-on for speed
          context: _getSearchContext(),
        );
        allArtistTracks.addAll(hybridTracks);
        print('🗄️ [Artist Cache] Got ${hybridTracks.length} optimized tracks for "$artist"');
      } else {
        // Use comprehensive approach for smaller lists - OPTIMIZED: PARALLEL FETCHING
        print('🗄️ [Artist Cache] Using comprehensive parallel fetching for "$artist"...');

        // OPTIMIZATION: Fetch album tracks and hybrid tracks in PARALLEL for much faster performance
        final fetchFutures = [
          // 1. Get tracks from artist's albums (comprehensive)
          _spotifyService.getTracksFromArtistAlbums(
            artist,
            maxTracks: 100, // Reduced from 200 for better performance
            context: _getSearchContext(),
          ),
          // 2. Get additional tracks using hybrid method
          _spotifyService.getArtistTracksHybrid(
            artist,
            limit: 30, // Reduced from 50
            includeAppearsOn: true,
            context: _getSearchContext(),
          ),
        ];

        // Wait for both operations to complete in parallel
        final results = await Future.wait(fetchFutures);
        final albumTracks = results[0];
        final hybridTracks = results[1];

        allArtistTracks.addAll(albumTracks);
        allArtistTracks.addAll(hybridTracks);
        print('🗄️ [Artist Cache] Got ${albumTracks.length} album tracks + ${hybridTracks.length} hybrid tracks for "$artist" (PARALLEL)');
      }

      // 3. Deduplicate all tracks for this artist
      print(
          '🗄️ [Artist Cache] Step 3: Deduplicating ${allArtistTracks.length} tracks for "$artist"...');
      final seenIds = <String>{};
      final uniqueTracks = allArtistTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      // Sort by popularity for better quality selection
      uniqueTracks
          .sort((a, b) => (b.popularity ?? 0).compareTo(a.popularity ?? 0));

      // Cache ALL tracks for this artist
      _artistTrackCache[artistKey] = uniqueTracks;
      _artistCacheTimestamps[artistKey] = now;

      print(
          '🗄️ [Artist Cache] ✅ Successfully cached ${uniqueTracks.length} unique tracks for "$artist"');

      // Return random selection for immediate use
      final shuffledTracks = List<MusicTrack>.from(uniqueTracks);
      shuffledTracks.shuffle();
      stopwatch.stop();
      print(
          '⏱️ [Artist Cache] Fetched and cached "$artist" in ${stopwatch.elapsedMilliseconds}ms');

      // Cleanup cache if it's getting too large
      _cleanupArtistCache();

      return shuffledTracks.take(limit).toList();
    } catch (e) {
      stopwatch.stop();
      print(
          '❌ [Artist Cache] Error fetching tracks for "$artist" in ${stopwatch.elapsedMilliseconds}ms: $e');
      print('❌ [Artist Cache] Stack trace: ${StackTrace.current}');
      // Fallback to basic search if caching fails
      try {
        final fallbackTracks = await _spotifyService.getArtistTracksHybrid(
          artist,
        limit: limit,
          includeAppearsOn: true,
          context: _getSearchContext(),
        );
        print(
            '🗄️ [Artist Cache] Fallback returned ${fallbackTracks.length} tracks for "$artist"');
        return fallbackTracks;
      } catch (fallbackError) {
        print(
            '❌ [Artist Cache] Fallback also failed for "$artist": $fallbackError');
      return [];
      }
    }
  }

  /// Clear artist cache (useful for refresh or memory management)
  void _clearArtistCache() {
    final cacheSize = _artistTrackCache.length;
    _artistTrackCache.clear();
    _artistCacheTimestamps.clear();
    print('🗄️ [Artist Cache] Cleared cache for $cacheSize artists');
  }

  /// Add track IDs to rolling recent tracks list with size management
  void _addToRecentTrackIds(Iterable<String> trackIds) {
    final trackIdList = trackIds.toList();

    for (final id in trackIdList) {
      // Remove if already exists to maintain order
      _recentTrackIds.remove(id);
      // Add to end
      _recentTrackIds.add(id);
    }

    // Maintain rolling window of recent track IDs
    while (_recentTrackIds.length > _maxTrackIdHistory) {
      _recentTrackIds.removeAt(0);
    }

    // Sync with AI service for consistent filtering
    _aiRecommendationService.syncTrackIds(_recentTrackIds);

    print(
        '🔄 [Track Management] Added ${trackIdList.length} track IDs. Total: ${_recentTrackIds.length}/$_maxTrackIdHistory');
  }

  /// Fast progressive artist expansion system for infinite exploration
  Future<List<String>> _performFastProgressiveExpansion(
      List<String> seedArtists, LastFmService lastFmService) async {
    final expandedArtists = <String>{};
    expandedArtists.addAll(seedArtists);

    // Add seed artists to explored set
    _exploredArtists.addAll(seedArtists);

    print(
        '🚀 [Fast Expansion] Starting expansion with ${seedArtists.length} seed artists');
    print(
        '🚀 [Fast Expansion] Current exploration depth: $_explorationDepth/$_maxExplorationDepth');

    // Level 1: Expand each seed artist rapidly
    final primaryTargets = seedArtists
        .take(_primaryTargetsCount)
        .toList(); // Configurable primary targets
    for (final artist in primaryTargets) {
      try {
        List<String> similarArtists;

        // Check cache first
        if (_similarArtistCache.containsKey(artist)) {
          similarArtists = _similarArtistCache[artist]!;
          print(
              '🗄️ [Fast Expansion] Cache hit for "$artist": ${similarArtists.length} artists');
        } else {
          // Fast expansion: get more artists per request
          similarArtists = await lastFmService.getSimilarArtists(artist,
              limit: _fastExpansionRate);
          _similarArtistCache[artist] = similarArtists;
          print(
              '🚀 [Fast Expansion] API call for "$artist": ${similarArtists.length} new artists');
        }

        expandedArtists.addAll(similarArtists);

        // Add promising artists to discovery queue for future exploration
        final newArtists =
            similarArtists.where((a) => !_exploredArtists.contains(a)).take(8);
        _discoveryQueue.addAll(newArtists);
    } catch (e) {
        print('❌ [Fast Expansion] Failed for "$artist": $e');
      }
    }

    // Level 2: Multi-level expansion from discovery queue
    if (_explorationDepth < _maxExplorationDepth &&
        _discoveryQueue.isNotEmpty) {
      print(
          '🌊 [Multi-Level] Expanding from discovery queue (${_discoveryQueue.length} artists)...');

      final secondLevelTargets =
          _discoveryQueue.take(_secondLevelTargetsCount).toList();
      _discoveryQueue.removeRange(
          0, math.min(_secondLevelTargetsCount, _discoveryQueue.length));

      for (final artist in secondLevelTargets) {
        if (_exploredArtists.contains(artist)) continue;

        try {
          List<String> similarArtists;

          if (_similarArtistCache.containsKey(artist)) {
            similarArtists = _similarArtistCache[artist]!;
          } else {
            // Smaller expansion for second level to maintain speed
            similarArtists = await lastFmService.getSimilarArtists(artist,
                limit: _secondLevelExpansionRate);
            _similarArtistCache[artist] = similarArtists;
          }

          expandedArtists
              .addAll(similarArtists.take(6)); // Add fewer to maintain quality
          _exploredArtists.add(artist);

          // Add new discoveries to queue
          final newDiscoveries = similarArtists
              .where((a) => !_exploredArtists.contains(a))
              .take(4);
          _discoveryQueue.addAll(newDiscoveries);

          print(
              '🌊 [Multi-Level] "$artist" → ${similarArtists.length} similar artists');
      } catch (e) {
          print('❌ [Multi-Level] Failed for "$artist": $e');
        }
      }
    }

    // Level 3: Continuous discovery from high-potential artists
    await _performContinuousDiscovery(expandedArtists, lastFmService);

    _explorationDepth++;

    // Reset exploration depth periodically to allow fresh discovery
    if (_explorationDepth >= _maxExplorationDepth) {
      _explorationDepth = 0;
      print('🔄 [Fast Expansion] Reset exploration depth for fresh discovery');
    }

    final finalArtists = expandedArtists.toList();
    print(
        '🚀 [Fast Expansion] Completed: ${seedArtists.length} → ${finalArtists.length} artists');
    print(
        '🚀 [Fast Expansion] Discovery queue size: ${_discoveryQueue.length}');
    print('🚀 [Fast Expansion] Explored artists: ${_exploredArtists.length}');

    return finalArtists;
  }

  /// Continuous discovery system for infinite exploration
  Future<void> _performContinuousDiscovery(
      Set<String> currentArtists, LastFmService lastFmService) async {
    if (_discoveryQueue.length < _discoveryQueueTarget &&
        currentArtists.length > 10) {
      print(
          '🔍 [Continuous Discovery] Queue low, discovering from current artists...');

      // Pick random artists from current expanded set for discovery
      final artistList = currentArtists.toList()..shuffle();
      final discoveryTargets = artistList.take(_backgroundTargetsCount);

      for (final artist in discoveryTargets) {
        if (_exploredArtists.contains(artist)) continue;

        try {
          final similarArtists =
              await lastFmService.getSimilarArtists(artist, limit: 8);

          // Add best candidates to discovery queue
          final newCandidates = similarArtists
              .where((a) => !_exploredArtists.contains(a))
              .take(6);

          _discoveryQueue.addAll(newCandidates);
          _exploredArtists.add(artist);

          print(
              '🔍 [Continuous Discovery] "$artist" → ${newCandidates.length} new candidates');
        } catch (e) {
          print('❌ [Continuous Discovery] Failed for "$artist": $e');
        }
      }
    }
  }

  /// Background expansion system for continuous discovery during exploration
  Future<void> _performBackgroundExpansion() async {
    final now = DateTime.now();

    // Rate limiting: only expand every 30 seconds
    if (_lastBackgroundExpansion != null &&
        now.difference(_lastBackgroundExpansion!) <
            _backgroundExpansionInterval) {
      return;
    }

    // Only expand if we have a reasonable discovery queue and Last.fm is available
    if (_discoveryQueue.length < _discoveryQueueTarget &&
        _discoveryQueue.isNotEmpty) {
      final lastFmService = LastFmService();
      if (!lastFmService.isConfigured) return;

      print('🚀 [Background Expansion] Starting background discovery...');
      _lastBackgroundExpansion = now;

      try {
        // Take artists from queue for background expansion
        final backgroundTargets =
            _discoveryQueue.take(_backgroundTargetsCount).toList();

        for (final artist in backgroundTargets) {
          if (_exploredArtists.contains(artist)) continue;

          try {
            // Get similar artists in background
            final similarArtists = await lastFmService.getSimilarArtists(artist,
                limit: _backgroundExpansionRate);
            _similarArtistCache[artist] = similarArtists;

            // Add new discoveries to queue
            final newCandidates = similarArtists
                .where((a) => !_exploredArtists.contains(a))
                .take(8);
            _discoveryQueue.addAll(newCandidates);
            _exploredArtists.add(artist);

            print(
                '🚀 [Background Expansion] "$artist" → ${newCandidates.length} new candidates');
    } catch (e) {
            print('❌ [Background Expansion] Failed for "$artist": $e');
          }
        }

        // Remove processed artists from queue
        _discoveryQueue
            .removeWhere((artist) => backgroundTargets.contains(artist));

        print(
            '🚀 [Background Expansion] Queue size after expansion: ${_discoveryQueue.length}');
      } catch (e) {
        print('❌ [Background Expansion] General error: $e');
      }
    }
  }

  /// Clear exploration state for fresh discovery
  void resetExplorationState() {
    _exploredArtists.clear();
    _discoveryQueue.clear();
    _similarArtistCache.clear();
    _explorationDepth = 0;
    _lastBackgroundExpansion = null;
    print('🔄 [Exploration] Reset exploration state for fresh discovery');
  }

  /// Get exploration statistics for debugging
  Map<String, dynamic> getExplorationStats() {
    final now = DateTime.now();
    final timeSinceLastExpansion = _lastBackgroundExpansion != null
        ? now.difference(_lastBackgroundExpansion!).inSeconds
        : null;

    return {
      'explored_artists': _exploredArtists.length,
      'discovery_queue': _discoveryQueue.length,
      'cached_similar_artists': _similarArtistCache.length,
      'exploration_depth': _explorationDepth,
      'seconds_since_last_expansion': timeSinceLastExpansion,
      'background_expansion_ready':
          timeSinceLastExpansion == null || timeSinceLastExpansion >= 30,
      'current_exploration_rate': {
        'fast_expansion': _fastExpansionRate,
        'background_expansion': _backgroundExpansionRate,
        'artists_per_page': _artistsPerPage,
        'tracks_per_artist': _tracksPerArtist,
        'background_interval_seconds': _backgroundExpansionInterval.inSeconds,
      },
    };
  }

  /// Set custom exploration rates
  void setExplorationRates({
    int? fastExpansionRate,
    int? secondLevelExpansionRate,
    int? backgroundExpansionRate,
    int? primaryTargetsCount,
    int? secondLevelTargetsCount,
    int? backgroundTargetsCount,
    int? discoveryQueueTarget,
    int? artistsPerPage,
    int? tracksPerArtist,
    Duration? backgroundExpansionInterval,
  }) {
    if (fastExpansionRate != null) _fastExpansionRate = fastExpansionRate;
    if (secondLevelExpansionRate != null)
      _secondLevelExpansionRate = secondLevelExpansionRate;
    if (backgroundExpansionRate != null)
      _backgroundExpansionRate = backgroundExpansionRate;
    if (primaryTargetsCount != null) _primaryTargetsCount = primaryTargetsCount;
    if (secondLevelTargetsCount != null)
      _secondLevelTargetsCount = secondLevelTargetsCount;
    if (backgroundTargetsCount != null)
      _backgroundTargetsCount = backgroundTargetsCount;
    if (discoveryQueueTarget != null)
      _discoveryQueueTarget = discoveryQueueTarget;
    if (artistsPerPage != null) _artistsPerPage = artistsPerPage;
    if (tracksPerArtist != null) _tracksPerArtist = tracksPerArtist;
    if (backgroundExpansionInterval != null)
      _backgroundExpansionInterval = backgroundExpansionInterval;

    print('🚀 [Exploration Config] Updated exploration rates');
    print('   Fast expansion: $_fastExpansionRate artists');
    print('   Background expansion: $_backgroundExpansionRate artists');
    print('   Artists per page: $_artistsPerPage');
    print('   Tracks per artist: $_tracksPerArtist');
    print('   Background interval: ${_backgroundExpansionInterval.inSeconds}s');
  }

  /// Apply preset exploration modes
  void setExplorationMode(String mode) {
    _currentExplorationMode = mode.toLowerCase();
    switch (mode.toLowerCase()) {
      case 'conservative':
        setExplorationRates(
          fastExpansionRate: 8,
          secondLevelExpansionRate: 5,
          backgroundExpansionRate: 6,
          primaryTargetsCount: 3,
          secondLevelTargetsCount: 2,
          backgroundTargetsCount: 1,
          discoveryQueueTarget: 30,
          artistsPerPage: 6,
          tracksPerArtist: 20,
          backgroundExpansionInterval: const Duration(seconds: 60),
        );
        print(
            '🐌 [Exploration Mode] Set to CONSERVATIVE - slower, focused discovery');
          break;

      case 'normal':
        setExplorationRates(
          fastExpansionRate: 15,
          secondLevelExpansionRate: 10,
          backgroundExpansionRate: 12,
          primaryTargetsCount: 5,
          secondLevelTargetsCount: 3,
          backgroundTargetsCount: 2,
          discoveryQueueTarget: 50,
          artistsPerPage: 8,
          tracksPerArtist: 25,
          backgroundExpansionInterval: const Duration(seconds: 30),
        );
        print('⚡ [Exploration Mode] Set to NORMAL - balanced discovery');
          break;

      case 'aggressive':
        setExplorationRates(
          fastExpansionRate: 25,
          secondLevelExpansionRate: 18,
          backgroundExpansionRate: 20,
          primaryTargetsCount: 8,
          secondLevelTargetsCount: 5,
          backgroundTargetsCount: 3,
          discoveryQueueTarget: 80,
          artistsPerPage: 12,
          tracksPerArtist: 35,
          backgroundExpansionInterval: const Duration(seconds: 15),
        );
        print(
            '🚀 [Exploration Mode] Set to AGGRESSIVE - maximum discovery speed');
          break;

      case 'insane':
        setExplorationRates(
          fastExpansionRate: 40,
          secondLevelExpansionRate: 30,
          backgroundExpansionRate: 35,
          primaryTargetsCount: 10,
          secondLevelTargetsCount: 8,
          backgroundTargetsCount: 5,
          discoveryQueueTarget: 150,
          artistsPerPage: 16,
          tracksPerArtist: 50,
          backgroundExpansionInterval: const Duration(seconds: 10),
        );
        print('🔥 [Exploration Mode] Set to INSANE - infinite discovery mode');
          break;

        default:
        print('❌ [Exploration Mode] Unknown mode: $mode');
        print('   Available modes: conservative, normal, aggressive, insane');
          break;
    }
    notifyListeners();
  }

  /// Get current exploration settings as ExplorationSettings object
  ExplorationSettings getCurrentExplorationSettings() {
    return ExplorationSettings(
      fastExpansionRate: _fastExpansionRate,
      secondLevelExpansionRate: _secondLevelExpansionRate,
      backgroundExpansionRate: _backgroundExpansionRate,
      continuousDiscoveryRate: _continuousDiscoveryRate,
      primaryTargetsCount: _primaryTargetsCount,
      secondLevelTargetsCount: _secondLevelTargetsCount,
      backgroundTargetsCount: _backgroundTargetsCount,
      discoveryQueueTarget: _discoveryQueueTarget,
      artistsPerPage: _artistsPerPage,
      tracksPerArtist: _tracksPerArtist,
      backgroundExpansionInterval: _backgroundExpansionInterval,
      mode: _currentExplorationMode,
    );
  }

  /// Apply exploration settings from ExplorationSettings object
  void applyExplorationSettings(ExplorationSettings settings) {
    _fastExpansionRate = settings.fastExpansionRate;
    _secondLevelExpansionRate = settings.secondLevelExpansionRate;
    _backgroundExpansionRate = settings.backgroundExpansionRate;
    _continuousDiscoveryRate = settings.continuousDiscoveryRate;
    _primaryTargetsCount = settings.primaryTargetsCount;
    _secondLevelTargetsCount = settings.secondLevelTargetsCount;
    _backgroundTargetsCount = settings.backgroundTargetsCount;
    _discoveryQueueTarget = settings.discoveryQueueTarget;
    _artistsPerPage = settings.artistsPerPage;
    _tracksPerArtist = settings.tracksPerArtist;
    _backgroundExpansionInterval = settings.backgroundExpansionInterval;
    _currentExplorationMode = settings.mode;

    print(
        '🔧 [Exploration Settings] Applied custom settings: ${settings.mode} mode');
        notifyListeners();
      }

  /// Clean up old artist cache entries to prevent memory issues
  void _cleanupArtistCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    _artistCacheTimestamps.forEach((artist, cacheTime) {
      if (now.difference(cacheTime) > _artistCacheExpiry) {
        expiredKeys.add(artist);
      }
    });

    for (final key in expiredKeys) {
      _artistTrackCache.remove(key);
      _artistCacheTimestamps.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      print(
          '🗄️ [Artist Cache] Cleaned up ${expiredKeys.length} expired entries');
    }

    // If cache is still too large, remove oldest entries - OPTIMIZED FOR LARGE ARTIST LISTS
    if (_artistTrackCache.length > 100) {
      // Increased from 30 to handle 50+ preferred artists efficiently
      final sortedEntries = _artistCacheTimestamps.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      final toRemove = sortedEntries
          .take(_artistTrackCache.length - 75) // Keep 75 most recent (was 20)
          .map((e) => e.key)
          .toList();

      for (final key in toRemove) {
        _artistTrackCache.remove(key);
        _artistCacheTimestamps.remove(key);
      }

      print(
          '🗄️ [Artist Cache] Removed ${toRemove.length} oldest entries for memory management (cache size: ${_artistTrackCache.length})');
    }
  }

  /// Debug method to analyze track distribution and identify clustering issues
  void _debugTrackDistribution(List<MusicTrack> tracks, {String label = ""}) {
    if (!_debugShuffling || tracks.isEmpty) return;

    print(
        '\n🔍 === TRACK DISTRIBUTION ANALYSIS ${label.isNotEmpty ? "($label)" : ""} ===');
    print('📊 Total tracks: ${tracks.length}');

    // Track consecutive artists and albums
    final List<String> consecutiveArtists = [];
    final List<String> consecutiveAlbums = [];
    final Map<String, List<int>> artistPositions = {};
    final Map<String, List<int>> albumPositions = {};

    String? lastArtist;
    String? lastAlbum;
    int consecutiveArtistCount = 0;
    int consecutiveAlbumCount = 0;

    for (int i = 0; i < tracks.length; i++) {
      final track = tracks[i];
      final artist = track.artist.toLowerCase().trim();
      final album =
          '${track.artist.toLowerCase().trim()}_${track.album.toLowerCase().trim()}';

      // Track positions for each artist/album
      artistPositions.putIfAbsent(artist, () => []).add(i);
      albumPositions.putIfAbsent(album, () => []).add(i);

      // Check for consecutive artists
      if (artist == lastArtist) {
        consecutiveArtistCount++;
        if (consecutiveArtistCount == 1) {
          consecutiveArtists.add('${track.artist} at positions ${i - 1}-$i');
        } else {
          // Update the last entry to show the range
          consecutiveArtists[consecutiveArtists.length - 1] =
              '${track.artist} at positions ${i - consecutiveArtistCount}-$i';
        }
      } else {
        consecutiveArtistCount = 0;
      }

      // Check for consecutive albums
      if (album == lastAlbum) {
        consecutiveAlbumCount++;
        if (consecutiveAlbumCount == 1) {
          consecutiveAlbums
              .add('${track.artist} - ${track.album} at positions ${i - 1}-$i');
        } else {
          consecutiveAlbums[consecutiveAlbums.length - 1] =
              '${track.artist} - ${track.album} at positions ${i - consecutiveAlbumCount}-$i';
        }
      } else {
        consecutiveAlbumCount = 0;
      }

      lastArtist = artist;
      lastAlbum = album;
    }

    // Report consecutive issues
    if (consecutiveArtists.isNotEmpty) {
      print('⚠️ CONSECUTIVE ARTISTS FOUND:');
      for (final issue in consecutiveArtists) {
        print('   - $issue');
      }
    } else {
      print('✅ No consecutive artists found');
    }

    if (consecutiveAlbums.isNotEmpty) {
      print('⚠️ CONSECUTIVE ALBUMS FOUND:');
      for (final issue in consecutiveAlbums) {
        print('   - $issue');
      }
    } else {
      print('✅ No consecutive albums found');
    }

    // Analyze artist distribution - check for clusters
    print('\n📈 ARTIST DISTRIBUTION ANALYSIS:');
    artistPositions.forEach((artist, positions) {
      if (positions.length > 1) {
        final gaps = <int>[];
        for (int i = 1; i < positions.length; i++) {
          gaps.add(positions[i] - positions[i - 1]);
        }
        final minGap = gaps.reduce((a, b) => a < b ? a : b);
        final avgGap = gaps.reduce((a, b) => a + b) / gaps.length;

        if (minGap <= 12) {
          // Within our lookback distance
          print(
              '   ⚠️ $artist: ${positions.length} tracks, min gap: $minGap, avg gap: ${avgGap.toStringAsFixed(1)}, positions: $positions');
        }
      }
    });

    print('🔍 === END ANALYSIS ===\n');
  }

  /// Update AI message for refresh action

  void _updateAiMessageForRefresh() {
    String refreshMessage;

    // Generate context-specific refresh messages
    if (_currentCategory == 'genreBased' && _selectedGenre != null) {
      final genreMessages = [
        "Fresh $_selectedGenre discoveries just for you! 🎯✨",
        "New $_selectedGenre gems uncovered! 🎵",
        "Your $_selectedGenre journey continues with fresh beats! 🎧",
        "Curated fresh $_selectedGenre tracks! 💫",
        "More $_selectedGenre goodness incoming! 🔥",
      ];
      refreshMessage =
          genreMessages[math.Random().nextInt(genreMessages.length)];
    } else if (_currentCategory == 'moodBased') {
      final moodName = _moods[_selectedMoodIndex]['name'];
      final moodMessages = [
        "Fresh $moodName vibes just for you! ${_moods[_selectedMoodIndex]['emoji']}✨",
        "New $moodName discoveries await! 🌟",
        "Your $moodName playlist just got refreshed! 🎧",
        "Curated fresh $moodName tracks! 💫",
      ];
      refreshMessage = moodMessages[math.Random().nextInt(moodMessages.length)];
    } else {
      // Generic refresh messages for other categories
      final genericMessages = [
        "Fresh picks just for you! 🎵✨",
        "New discoveries await! 🌟",
        "Your music journey continues with fresh vibes! 🎧",
        "Curated something special just now! 💫",
        "Fresh beats, just dropped! 🔥",
        "New musical adventures incoming! 🚀",
      ];
      refreshMessage =
          genericMessages[math.Random().nextInt(genericMessages.length)];
    }

    _currentAiMessage = refreshMessage;
    _showAiMessage = true;
    notifyListeners();
  }

  /// Return genre aliases to improve search & filtering for niche/hyphenated genres
  List<String> _getGenreAliases(String genre) {
    return SpotifyGenreService.getGenreAliases(genre);
  }

  /// Retry function with exponential backoff for Spotify API calls
  Future<List<MusicTrack>> _retryWithBackoff(
    Future<List<MusicTrack>> Function() operation,
    String operationName,
  ) async {
    const maxRetries = 3;
    const baseDelay = Duration(seconds: 1);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        print('🔄 [$operationName] Attempt $attempt/$maxRetries');
        final result = await operation();

        if (result.isNotEmpty) {
          print(
              '✅ [$operationName] Success on attempt $attempt with ${result.length} tracks');
          return result;
        } else if (attempt == maxRetries) {
          print('⚠️ [$operationName] All attempts returned empty results');
          return [];
        } else {
          print(
              '⚠️ [$operationName] Empty result on attempt $attempt, retrying...');
      }
    } catch (e) {
        print('❌ [$operationName] Error on attempt $attempt: $e');

        if (attempt == maxRetries) {
          print('❌ [$operationName] All retry attempts failed');
      return [];
        }

        // Exponential backoff delay
        final delay = Duration(
            milliseconds: baseDelay.inMilliseconds * (1 << (attempt - 1)));
        print(
            '⏳ [$operationName] Waiting ${delay.inMilliseconds}ms before retry...');
        await Future.delayed(delay);
      }
    }

    return [];
  }

  Future<List<MusicTrack>> _getRecentlyAdded(BuildContext context) async {
    final spotifyProvider =
        Provider.of<SpotifyProvider>(context, listen: false);
    List<MusicTrack> recentlyAddedTracks = [];

    // 🚀 ENHANCEMENT: Cache recently added data for consistency across pagination
    final now = DateTime.now();
    final cacheValid = _cachedRecentlyAdded != null &&
        _recentlyAddedCacheTime != null &&
        now.difference(_recentlyAddedCacheTime!).inMinutes <
            15; // 15 minute cache for recently added

    if (cacheValid) {
      recentlyAddedTracks = _cachedRecentlyAdded!;
      print(
          '🕒 Using cached recently added data (${recentlyAddedTracks.length} tracks)');
      return _filterAndDeduplicateTracks(recentlyAddedTracks,
          isDiscovery: true);
    }

    print('🕒 Fetching fresh recently added data...');

    if (spotifyProvider.isConnected) {
      print(
          '🕒 [Spotify] Getting recently added tracks (recently saved to library)...');
      try {
        // 🚀 ENHANCEMENT: Use proper Spotify recently saved tracks endpoint
        // The getSavedTracks method with limit should return most recently saved tracks first
        final result = await _spotifyService.getSavedTracks(
          limit: 25, // Get more tracks for better selection
          offset: 0, // Start from most recent
        );

        final savedTracks = result['tracks'] as List<MusicTrack>? ?? [];
        print('🎵 [Spotify] Got ${savedTracks.length} saved tracks from API');

        if (savedTracks.isNotEmpty) {
          // The Spotify API should return these in recently saved order (newest first)
          recentlyAddedTracks = savedTracks.take(20).toList();
          print(
              '✅ [Spotify] Using ${recentlyAddedTracks.length} most recently saved tracks');

          // Debug: Print first few tracks to verify recency
          for (int i = 0; i < math.min(3, recentlyAddedTracks.length); i++) {
            final track = recentlyAddedTracks[i];
            print(
                '🎵 [Spotify] Recent track ${i + 1}: "${track.title}" by "${track.artist}"');
          }
        } else {
          print('⚠️ [Spotify] No saved tracks available, trying fallback...');

          // Fallback: Use provider's liked songs if available
          await spotifyProvider.loadLikedSongs();
          if (spotifyProvider.likedSongs.isNotEmpty) {
            recentlyAddedTracks = spotifyProvider.likedSongs.take(20).toList();
            print(
                '🔄 [Spotify] Fallback: Using ${recentlyAddedTracks.length} tracks from provider');
          } else {
            print('❌ [Spotify] No liked songs available in provider either');
        }
      }
    } catch (e) {
        print('❌ [Spotify] Error getting recently saved tracks: $e');

        // Final fallback: Try to use provider's liked songs
        try {
          print('🔄 [Spotify] Final fallback: Trying provider liked songs...');
          await spotifyProvider.loadLikedSongs();
          if (spotifyProvider.likedSongs.isNotEmpty) {
            recentlyAddedTracks = spotifyProvider.likedSongs.take(20).toList();
            print(
                '🔄 [Spotify] Final fallback: Got ${recentlyAddedTracks.length} tracks from provider');
          }
        } catch (fallbackError) {
          print('❌ [Spotify] Final fallback also failed: $fallbackError');
        }
      }
    } else {
      print('❌ No music service connected for recently added tracks');
      return [];
    }

    print('✅ Recently added: ${recentlyAddedTracks.length} tracks total');

    // Cache the data for future pagination calls
    _cachedRecentlyAdded = recentlyAddedTracks;
    _recentlyAddedCacheTime = now;
    print('🕒 Cached ${recentlyAddedTracks.length} recently added tracks');

    // 🚀 ENHANCEMENT: Use relaxed filtering (discovery mode) for infinite scrolling
    // This allows recently added to continue loading more content without being blocked by exclusions
    return _filterAndDeduplicateTracks(recentlyAddedTracks, isDiscovery: true);
  }



  @override
  void dispose() {
    _aiRecommendationService.dispose();

    // Clear MusicBrainz validation caches
    _artistGenreValidationCache.clear();
    _validationCacheTimestamps.clear();

    // Clear artist track caches
    _artistTrackCache.clear();
    _artistCacheTimestamps.clear();

    // Clear exploration state
    _exploredArtists.clear();
    _discoveryQueue.clear();
    _similarArtistCache.clear();
    _explorationDepth = 0;
    _lastBackgroundExpansion = null;

    super.dispose();
  }










  /// Get the appropriate market for international genres to improve content availability
  String? _getMarketForGenre(String genre) {
    final genreLower = genre.toLowerCase();

    // Chinese genres - use Hong Kong for best C-pop/Cantopop availability
    if (genreLower.contains('c-pop') ||
        genreLower.contains('cantopop') ||
        genreLower.contains('mandopop') ||
        genreLower.contains('chinese') ||
        genreLower.contains('cantonese')) {
      return 'HK'; // Hong Kong
    }

    // Korean genres - use South Korea
    if (genreLower.contains('k-pop') ||
        genreLower.contains('korean') ||
        genreLower == 'k-indie' ||
        genreLower == 'k-rock') {
      return 'KR'; // South Korea
    }

    // Japanese genres - use Japan
    if (genreLower.contains('j-pop') ||
        genreLower.contains('japanese') ||
        genreLower == 'j-rock' ||
        genreLower == 'anime' ||
        genreLower.contains('visual kei')) {
      return 'JP'; // Japan
    }

    // Latin genres - use Mexico for good Latin music availability
    if (genreLower.contains('reggaeton') ||
        genreLower.contains('latin') ||
        genreLower.contains('salsa') ||
        genreLower.contains('bachata') ||
        genreLower.contains('merengue')) {
      return 'MX'; // Mexico
    }

    // Brazilian genres - use Brazil
    if (genreLower.contains('mpb') ||
        genreLower.contains('samba') ||
        genreLower.contains('bossa nova') ||
        genreLower.contains('brazilian') ||
        genreLower.contains('funk carioca')) {
      return 'BR'; // Brazil
    }

    // Indian genres - use India
    if (genreLower.contains('bollywood') ||
        genreLower.contains('indian') ||
        genreLower.contains('bhangra') ||
        genreLower.contains('carnatic')) {
      return 'IN'; // India
    }

    // For Western genres, don't specify a market (use default/user's market)
    return null;
  }









  // Update within _getTracksForGenre playlist section
  int _genreRequestId = 0;
  int _nextGenreRequestId() => ++_genreRequestId;

  /// Update user preferences in the AI search provider
  void updateUserPreferences({
    List<String>? topArtists,
    List<String>? topGenres,
  }) {
    if (kDebugMode) {
      print('🎵 [AI Search] Updating user preferences...');
      print('🎤 New top artists: $topArtists');
      print('🎼 New top genres: $topGenres');
    }

    // Update user top artists
    if (topArtists != null) {
      // Create a shuffled copy of the artists to vary exploration order
      final shuffledArtists = List<String>.from(topArtists)..shuffle();
      _userTopArtists = shuffledArtists;
      _aiRecommendationService.setUserTopArtists(_userTopArtists);

      if (kDebugMode) {
        print(
            '✅ [AI Search] Updated user top artists: ${_userTopArtists.length} artists');
        print('🔀 [AI Search] Artists shuffled for varied exploration order');
      }
    }

    // Update user top genres
    if (topGenres != null) {
      // Create a shuffled copy of the genres for varied recommendations
      final shuffledGenres = List<String>.from(topGenres)..shuffle();
      _userTopGenres = shuffledGenres;

      // Update genre recommendations cache
      _genreRecommendations.clear();
      for (final genre in _userTopGenres) {
        _genreRecommendations.putIfAbsent(genre, () => []);
      }

      if (kDebugMode) {
        print(
            '✅ [AI Search] Updated user top genres: ${_userTopGenres.length} genres');
        print('🔀 [AI Search] Genres shuffled for varied recommendations');
      }
    }

      notifyListeners();
    }

  /// Clear all caches for fresh recommendations
  void clearAllCaches() {
    if (kDebugMode) {
      print('🧹 [AI Search] Clearing all caches for fresh recommendations...');
    }

    // Clear Spotify service cache
    _spotifyService.clearSearchCache();

    // Clear artist cache
    _clearArtistCache();

    // Clear genre recommendations cache
    _genreRecommendations.clear();

    // Clear categorized recommendations
    _categorizedRecommendations.clear();

    // Clear recent track IDs for fresh content
    _recentTrackIds.clear();

    // Clear anti-repetition tracking
    _usedQueries.clear();
    _recentArtists.clear();
    _usedArtistQueryCombos.clear();

    // Clear validation caches
    _artistGenreValidationCache.clear();
    _validationCacheTimestamps.clear();

    // Clear exploration state
    _exploredArtists.clear();
    _discoveryQueue.clear();
    _similarArtistCache.clear();

    // Reset pagination
    _currentPage = 0;
    _hasMoreContent = true;

    if (kDebugMode) {
      print('✅ [AI Search] All caches cleared successfully');
    }
  }
}
