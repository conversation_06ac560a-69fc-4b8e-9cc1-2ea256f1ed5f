import 'package:bop_maps/models/music_track.dart';
import 'package:bop_maps/screens/search/ai_search/ai_search_provider.dart';
import 'package:bop_maps/screens/search/ai_search/widgets/recommendations_grid.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AllTab extends StatelessWidget {
  final Function(MusicTrack) onTrackTap;
  const AllTab({super.key, required this.onTrackTap});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<AISearchProvider>();
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final selectedMood = provider.moods[provider.selectedMoodIndex];
    final moodColor = selectedMood['color'] as Color;

    return RecommendationsGrid(
      tracks: provider.currentRecommendations,
      isLoadingMore: provider.isLoadingMore,
      hasMoreContent: provider.hasMoreContent,
      moodColor: moodColor,
      isDark: isDark,
      onTrackTap: onTrackTap,
    );
  }
} 