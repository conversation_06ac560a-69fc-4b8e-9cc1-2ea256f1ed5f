import 'package:bop_maps/services/music/spotify_genre_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Genre preferences management tab widget
class GenrePreferencesTab extends StatefulWidget {
  final List<String> initialGenres;
  final Function(List<String>) onGenresChanged;

  const GenrePreferencesTab({
    super.key,
    required this.initialGenres,
    required this.onGenresChanged,
  });

  @override
  State<GenrePreferencesTab> createState() => _GenrePreferencesTabState();
}

class _GenrePreferencesTabState extends State<GenrePreferencesTab>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();

  // State
  List<String> _selectedGenres = [];
  List<String> _filteredGenres = [];
  List<String> _allGenres = [];

  // Keep alive to preserve tab state
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _selectedGenres = List<String>.from(widget.initialGenres);
    _allGenres = SpotifyGenreService.getMainGenresForSelection();
    _filteredGenres = []; // Start empty so the logic works correctly
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Filter genres based on search query
  void _filterGenres(String query) {
    setState(() {
      if (query.trim().isEmpty) {
        _filteredGenres = []; // Keep empty so selected genres show first
      } else {
        _filteredGenres = _allGenres
            .where((genre) => genre.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  /// Toggle genre selection
  void _toggleGenre(String genre) {
    setState(() {
      if (_selectedGenres.contains(genre)) {
        _selectedGenres.remove(genre);
      } else {
        // Check if we're at the limit (20 genres max)
        if (_selectedGenres.length >= 20) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Maximum 20 genres allowed'),
              backgroundColor: Colors.orange,
            ),
          );
          return;
        }
        _selectedGenres.add(genre);
      }
    });

    // Update parent
    widget.onGenresChanged(_selectedGenres);
    HapticFeedback.lightImpact();
  }


  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final theme = Theme.of(context);
    
    // Create a combined list with selected genres first, then available genres
    final List<String> combinedGenres = [];
    
    // Add selected genres first
    combinedGenres.addAll(_selectedGenres);
    
    // Add available genres that aren't already selected
    final availableGenres = _filteredGenres.isNotEmpty ? _filteredGenres : _allGenres;
    for (final genre in availableGenres) {
      if (!_selectedGenres.contains(genre)) {
        combinedGenres.add(genre);
      }
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search field
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search for genres...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _filterGenres('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: _filterGenres,
          ),

          const SizedBox(height: 16),

          // Header with count
          Row(
            children: [
              Text(
                'Your Genres',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_selectedGenres.length}/20',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Single horizontal scrollable list of all genres
          Expanded(
            child: SingleChildScrollView(
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: combinedGenres.map((genre) {
                  final isSelected = _selectedGenres.contains(genre);
                  return FilterChip(
                    label: Text(genre),
                    selected: isSelected,
                    onSelected: (_) => _toggleGenre(genre),
                    selectedColor: theme.colorScheme.primary.withValues(alpha: 0.3),
                    checkmarkColor: theme.colorScheme.primary,
                    backgroundColor: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                    side: BorderSide(
                      color: isSelected
                          ? theme.colorScheme.primary.withValues(alpha: 0.5)
                          : theme.dividerColor.withValues(alpha: 0.3),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
