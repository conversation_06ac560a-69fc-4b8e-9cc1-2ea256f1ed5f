import 'package:bop_maps/models/exploration_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Exploration settings tab widget with predefined modes
class ExplorationSettingsTab extends StatefulWidget {
  final ExplorationSettings initialSettings;
  final Function(ExplorationSettings) onSettingsChanged;

  const ExplorationSettingsTab({
    super.key,
    required this.initialSettings,
    required this.onSettingsChanged,
  });

  @override
  State<ExplorationSettingsTab> createState() => _ExplorationSettingsTabState();
}

class _ExplorationSettingsTabState extends State<ExplorationSettingsTab>
    with AutomaticKeepAliveClientMixin {
  late String _selectedMode;

  // Keep alive to preserve tab state
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _selectedMode = widget.initialSettings.mode;
  }

  /// Handle mode selection
  void _selectMode(String mode) {
    setState(() {
      _selectedMode = mode;
    });

    // Create new settings for the selected mode
    final newSettings = ExplorationSettings.fromMode(mode);
    widget.onSettingsChanged(newSettings);
    
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final theme = Theme.of(context);
    final availableModes = ExplorationSettings.availableModes;
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ListView.builder(
        itemCount: availableModes.length,
        itemBuilder: (context, index) {
          final mode = availableModes[index];
          final isSelected = _selectedMode == mode.mode;
          
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            child: _buildModeCard(mode, isSelected, theme),
          );
        },
      ),
    );
  }

  /// Build mode selection card
  Widget _buildModeCard(ExplorationModeInfo mode, bool isSelected, ThemeData theme) {
    return Card(
      elevation: isSelected ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isSelected ? theme.colorScheme.primary : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () => _selectMode(mode.mode),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: isSelected
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      theme.colorScheme.primary.withValues(alpha: 0.1),
                      theme.colorScheme.primary.withValues(alpha: 0.05),
                    ],
                  )
                : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with emoji, name, and selection indicator
              Row(
                children: [
                  // Mode emoji and name
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? theme.colorScheme.primary.withValues(alpha: 0.2)
                          : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          mode.emoji,
                          style: const TextStyle(fontSize: 24),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          mode.name,
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isSelected ? theme.colorScheme.primary : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Selection indicator
                  if (isSelected)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Description
              Text(
                mode.description,
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              
              const SizedBox(height: 12),
              
              // Best for section
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Best for: ${mode.bestFor}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }


}
