import 'package:bop_maps/models/music_track.dart';
import 'package:bop_maps/screens/search/ai_search/genre_search_provider.dart';
import 'package:bop_maps/screens/search/ai_search/widgets/recommendations_grid.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'dart:ui';

class GenreBasedTab extends StatelessWidget {
  final Function(MusicTrack) onTrackTap;
  const GenreBasedTab({super.key, required this.onTrackTap});

  @override
  Widget build(BuildContext context) {
    final genreProvider = context.watch<GenreSearchProvider>();
    // Removed AISearchProvider dependency to prevent interference with genre search
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Use a default mood color for genre-based search instead of AI provider's mood
    final moodColor = isDark 
        ? const Color(0xFF6B73FF) // Purple-blue for dark mode
        : const Color(0xFF4F46E5); // Indigo for light mode
    
    // Show loading state when loading initial genre data
    if (genreProvider.isLoading && genreProvider.currentTracks.isEmpty) {
      return SliverToBoxAdapter(
        child: _buildLoadingState(context, theme, moodColor, genreProvider.selectedGenre),
      );
    }
    
    // Show empty state if no genre is selected
    if (genreProvider.selectedGenre == null && genreProvider.currentTracks.isEmpty) {
      return SliverToBoxAdapter(
        child: _buildEmptyState(context, theme, moodColor),
      );
    }

    // Always show tracks if we have any - the system is now truly infinite
    // so we should never show error states that block content loading
    return RecommendationsGrid(
      tracks: genreProvider.currentTracks,
      isLoadingMore: genreProvider.isLoadingMore,
      hasMoreContent: genreProvider.hasMore, // This is now always true
      moodColor: moodColor,
      isDark: isDark,
      onTrackTap: onTrackTap,
    );
  }
  
  Widget _buildLoadingState(BuildContext context, ThemeData theme, Color moodColor, String? genre) {
    final genreProvider = context.watch<GenreSearchProvider>();
    
    return Container(
      height: 400,
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Lottie animation
          SizedBox(
            width: 160,
            height: 160,
            child: Lottie.asset(
              'assets/anim/ai_search.json',
              width: 160,
              height: 160,
              fit: BoxFit.contain,
              repeat: true,
              animate: true,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Loading ${genre ?? 'genre'} tracks...',
            style: TextStyle(
              color: theme.colorScheme.onSurface,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Text(
              genreProvider.statusMessage,
              key: ValueKey(genreProvider.statusMessage),
              style: TextStyle(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
                fontSize: 14,
                height: 1.3,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: 200,
            child: LinearProgressIndicator(
              backgroundColor: moodColor.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(moodColor),
            ),
          ),
        ],
      ),
    );
  }
  
     Widget _buildErrorState(BuildContext context, ThemeData theme, Color moodColor, String genre, String message) {
     return Container(
       height: 400,
       padding: const EdgeInsets.all(32),
       child: Column(
         mainAxisAlignment: MainAxisAlignment.center,
         children: [
           // Error icon with glow effect
           Container(
             width: 120,
             height: 120,
             decoration: BoxDecoration(
               shape: BoxShape.circle,
               color: moodColor.withOpacity(0.1),
             ),
             child: Icon(
               Icons.music_off_outlined,
               size: 60,
               color: moodColor.withOpacity(0.7),
             ),
           ),
           const SizedBox(height: 32),
           Text(
             'No $genre tracks found',
             style: TextStyle(
               color: theme.colorScheme.onSurface,
               fontSize: 18,
               fontWeight: FontWeight.w600,
             ),
             textAlign: TextAlign.center,
           ),
           const SizedBox(height: 12),
           Text(
             message,
             style: TextStyle(
               color: theme.colorScheme.onSurface.withOpacity(0.6),
               fontSize: 14,
               height: 1.4,
             ),
             textAlign: TextAlign.center,
           ),
           const SizedBox(height: 24),
           // Retry button
           ElevatedButton.icon(
             onPressed: () {
               // Trigger a retry by calling the provider's loadGenre again
               final genreProvider = context.read<GenreSearchProvider>();
               if (genreProvider.selectedGenre != null) {
                 genreProvider.loadGenre(genreProvider.selectedGenre!, context);
               }
             },
             icon: const Icon(Icons.refresh),
             label: const Text('Try Again'),
             style: ElevatedButton.styleFrom(
               backgroundColor: moodColor,
               foregroundColor: Colors.white,
               padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
               shape: RoundedRectangleBorder(
                 borderRadius: BorderRadius.circular(20),
               ),
             ),
           ),
         ],
       ),
     );
   }
  
  Widget _buildEmptyState(BuildContext context, ThemeData theme, Color moodColor) {
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      height: 450,
      padding: const EdgeInsets.all(20),
      child: Center(
        child: ClipRRect(
          borderRadius: BorderRadius.circular(32),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    isDark 
                        ? moodColor.withOpacity(0.15)
                        : moodColor.withOpacity(0.25),
                    isDark 
                        ? Colors.white.withOpacity(0.08)
                        : Colors.white.withOpacity(0.4),
                  ],
                ),
                borderRadius: BorderRadius.circular(32),
                border: Border.all(
                  color: isDark 
                      ? Colors.white.withOpacity(0.2)
                      : Colors.white.withOpacity(0.6),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: isDark 
                        ? Colors.black.withOpacity(0.3)
                        : Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Glassmorphism container for the animation
                  Container(
                    width: 156,
                    height: 156,
                    child: Lottie.asset(
                      'assets/anim/ai_search.json',
                      width: 156,
                      height: 156,
                      fit: BoxFit.contain,
                      repeat: true,
                      animate: true,

                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Select a genre to explore',
                    style: TextStyle(
                      color: theme.colorScheme.onSurface,
                      fontSize: 22,
                      fontWeight: FontWeight.w700,
                      letterSpacing: -0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Choose from your top genres above to discover music tailored to your taste',
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  // Decorative elements
                  Container(
                    width: 80,
                    height: 4,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      gradient: LinearGradient(
                        colors: [
                          moodColor.withOpacity(0.6),
                          moodColor.withOpacity(0.3),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
} 