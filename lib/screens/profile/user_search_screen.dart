import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/public_profile_provider.dart';
import '../../widgets/profile/user_search_widget.dart';
import 'user_profile_screen.dart';

class UserSearchScreen extends StatelessWidget {
  const UserSearchScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => PublicProfileProvider(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Find Users'),
          backgroundColor: Theme.of(context).colorScheme.surface,
          actions: [
            IconButton(
              onPressed: () {
                UserSearchWidget.showAsBottomSheet(context);
              },
              icon: const Icon(Icons.search),
              tooltip: 'Search Users',
            ),
          ],
        ),
        body: const UserSearchWidget(),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () {
            UserSearchWidget.showAsBottomSheet(context);
          },
          icon: const Icon(Icons.search),
          label: const Text('Search Users'),
        ),
      ),
    );
  }
}

// Example of how to integrate user search into existing screens
class ExampleIntegration {
  // Add this to your friends screen or any other screen where you want user search
  static Widget buildSearchButton(BuildContext context) {
    return IconButton(
      onPressed: () {
        UserSearchWidget.showAsBottomSheet(context);
      },
      icon: const Icon(Icons.person_search),
      tooltip: 'Search Users',
    );
  }

  // Example of how to navigate to a user profile directly
  static void navigateToUserProfile(BuildContext context, int userId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (context) => PublicProfileProvider(),
          child: UserProfileScreen(
            userId: userId,
            showBottomNav: false,
          ),
        ),
      ),
    );
  }
}

// Import this in your main app file and add the provider
class AppWithPublicProfiles extends StatelessWidget {
  const AppWithPublicProfiles({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Your existing providers...
        ChangeNotifierProvider(create: (context) => PublicProfileProvider()),
      ],
      child: MaterialApp(
        // Your app configuration...
        home: const UserSearchScreen(), // Or your main screen
      ),
    );
  }
} 