import 'package:bop_maps/config/route_constants.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../config/themes.dart';
import '../../utils/auth_manager.dart';
import '../../features/indoor_scanning/widgets/indoor_scan_settings_tile.dart';
import '../../widgets/navigation/bottom_navigation_bar.dart';
import '../../utils/navigation_helper.dart';
import '../../providers/map_settings_provider.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/settings_provider.dart';
import '../settings/privacy_policy_screen.dart';
import '../settings/terms_of_service_screen.dart';
import '../settings/about_bopmaps_screen.dart';
import '../settings/help_support_screen.dart';
import 'package:flutter/foundation.dart';

class SettingsScreen extends StatefulWidget {
  final bool showBottomNav;

  const SettingsScreen({
    Key? key,
    this.showBottomNav = false,
  }) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _locationEnabled = true;
  bool _isTestingConnection = false;
  Map<String, dynamic>? _connectionTestResult;
  bool _showNotifications = true;
  bool _useLocationServices = true;

  // Auth manager instance
  final AuthManager _authManager = AuthManager();

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final theme = Theme.of(context);
    final isSmallScreen = MediaQuery.of(context).size.width < 360;

    return Scaffold(
      appBar: widget.showBottomNav
          ? null
          : AppBar(
              title: const Text('Settings'),
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withOpacity(0.9)
                      : Colors.black87,
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              elevation: 0,
            ),
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          children: [
            if (widget.showBottomNav)
              Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 0, 20, 8),
                    child: Row(
                      children: [
                        IconButton(
                          icon: Icon(
                            Icons.arrow_back,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white.withOpacity(0.9)
                                    : Colors.black87,
                          ),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Settings',
                          style: theme.textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onBackground,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Divider(height: 1),
                  const SizedBox(height: 8),
                ],
              ),

            const SizedBox(height: 8),

            // Appearance (Visual customization)
            _buildSectionHeader(context, 'Appearance'),

            // Dark Mode Toggle
            SwitchListTile(
              title: const Text('Dark Mode'),
              subtitle: const Text('Enable dark theme'),
              value: themeProvider.isDarkMode,
              onChanged: (bool value) {
                themeProvider.setThemeMode(
                  value ? ThemeMode.dark : ThemeMode.light,
                );
              },
              secondary: Icon(
                themeProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                color: theme.colorScheme.primary,
              ),
            ),

            // Theme Color Selection
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.palette,
                        size: 20,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'App Color',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.outline.withOpacity(0.1),
                      ),
                    ),
                    padding: const EdgeInsets.all(16),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: themeProvider.availableColors
                            .asMap()
                            .entries
                            .map((entry) {
                          final index = entry.key;
                          final color = entry.value;
                          final isSelected =
                              color.value == themeProvider.primaryColor.value;

                          return Padding(
                            padding: EdgeInsets.only(
                              right: index <
                                      themeProvider.availableColors.length - 1
                                  ? 12
                                  : 0,
                            ),
                            child: GestureDetector(
                              onTap: () => themeProvider.setPrimaryColor(color),
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  color: color,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: isSelected
                                        ? Colors.white
                                        : Colors.transparent,
                                    width: 2,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: color.withOpacity(0.4),
                                      blurRadius: isSelected ? 8 : 4,
                                      spreadRadius: isSelected ? 2 : 0,
                                    ),
                                  ],
                                ),
                                child: isSelected
                                    ? const Icon(
                                        Icons.check,
                                        color: Colors.white,
                                        size: 24,
                                      )
                                    : null,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Animated Background Toggle
            Consumer<SettingsProvider>(
              builder: (context, settings, child) {
                return SwitchListTile(
                  value: settings.isAnimatedBackgroundEnabled,
                  onChanged: (value) {
                    settings.setAnimatedBackground(value);
                  },
                  title: const Text('Animated Background'),
                  subtitle: const Text('Enable smooth background animations'),
                  secondary: Icon(
                    Icons.animation,
                    color: theme.colorScheme.primary,
                  ),
                );
              },
            ),

            const Divider(),

            // Privacy & Account (Important user settings)
            _buildSectionHeader(context, 'Privacy & Account'),

            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('Account Information'),
              subtitle:
                  Text(authProvider.currentUser?.email ?? 'Not logged in'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // Navigate to account info screen
              },
            ),

            ListTile(
              leading: const Icon(Icons.lock),
              title: const Text('Privacy Settings'),
              subtitle: const Text('Manage your privacy preferences'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // Navigate to privacy settings
              },
            ),

            const Divider(),

            // Language & Region
            _buildSectionHeader(context, 'Language & Region'),

            ListTile(
              leading: const Icon(Icons.language),
              title: const Text('Language'),
              subtitle: const Text('English (US)'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                showModalBottomSheet(
                  context: context,
                  builder: (context) => _buildLanguageSheet(context),
                );
              },
            ),

            const Divider(),

            // Help & Support
            _buildSectionHeader(context, 'Help & Support'),

            ListTile(
              leading: const Icon(Icons.help),
              title: const Text('Help & Support'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const HelpSupportScreen(),
                  ),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('About BOPMaps'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AboutBOPMapsScreen(),
                  ),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.description),
              title: const Text('Terms of Service'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const TermsOfServiceScreen(),
                  ),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.privacy_tip),
              title: const Text('Privacy Policy'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PrivacyPolicyScreen(),
                  ),
                );
              },
            ),

            const Divider(),

            // Sign out button
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                onPressed: () {
                  _showSignOutDialog(context, authProvider);
                },
                child: const Text('Sign Out'),
              ),
            ),

            // App version at bottom
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'BOPMaps v1.0.0',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSheet(BuildContext context) {
    final theme = Theme.of(context);
    final languages = [
      'English (US)',
      'English (UK)',
      'Español',
      'Français',
      'Deutsch',
      'Italiano',
      '日本語',
      '한국어',
      '中文 (简体)',
      '中文 (繁體)',
    ];

    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: theme.dividerColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Text(
                  'Select Language',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: languages.length,
              itemBuilder: (context, index) {
                final language = languages[index];
                final isSelected = language == 'English (US)';

                return ListTile(
                  title: Text(language),
                  trailing: isSelected
                      ? Icon(
                          Icons.check,
                          color: theme.colorScheme.primary,
                        )
                      : null,
                  onTap: () {
                    // Handle language selection
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 4),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Future<void> _showSignOutDialog(
      BuildContext context, AuthProvider authProvider) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Sign Out'),
          content: const Text('Are you sure you want to sign out?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text(
                'Sign Out',
                style: TextStyle(color: Colors.red),
              ),
              onPressed: () async {
                // Show loading indicator
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Signing out...'),
                    duration: Duration(seconds: 1),
                  ),
                );

                // Clear Spotify tokens first if available
                try {
                  final spotifyProvider =
                      Provider.of<SpotifyProvider>(context, listen: false);
                  if (spotifyProvider.isConnected) {
                    await spotifyProvider.disconnect();
                    if (kDebugMode) {
                      print(
                          '✅ Successfully disconnected from Spotify during sign out');
                    }
                  }
                } catch (e) {
                  if (kDebugMode) {
                    print(
                        '⚠️ Error disconnecting from Spotify during sign out: $e');
                    print('Continuing with sign out anyway...');
                  }
                }

                // Sign out from the auth provider
                await authProvider.logout();

                // Close the dialog
                Navigator.pop(context);

                // Navigate to login screen
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  RouteConstants.aiOnboarding,
                  (route) => false,
                );
              },
            ),
          ],
        );
      },
    );
  }
}
