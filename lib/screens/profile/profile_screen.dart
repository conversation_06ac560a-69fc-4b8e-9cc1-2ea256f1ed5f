import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/youtube_provider.dart';
import '../../providers/user_stats_provider.dart';
import '../../widgets/profile/profile_header.dart';
import '../../widgets/profile/profile_stats.dart';
import '../../widgets/profile/my_pins_tab.dart';
import '../../widgets/profile/apple_music_pins_tab.dart';
import '../../widgets/profile/bop_history_tab.dart';
import '../../widgets/profile/collections_tab.dart';
import '../../widgets/navigation/bottom_navigation_bar.dart';
import '../../widgets/music/now_playing_bar.dart';
import '../../widgets/music/apple_now_playing_bar.dart';
import '../../widgets/music/youtube_now_playing_bar.dart.dart';
import '../../utils/navigation_helper.dart';
import '../../utils/app_tab.dart';
import '../../config/themes.dart';
import 'settings_screen.dart';
import 'package:flutter/scheduler.dart';
import '../../screens/notifications/notifications_screen.dart';
import '../../widgets/profile/leaderboard_tab.dart';
import '../../widgets/profile/components/filter_header.dart';
import '../../screens/auth/services/auth_service.dart';
import '../search/ai_search/ai_search_view.dart';
import '../search/ai_search/ai_search_provider.dart';
import '../../services/ai/global_ai_provider_service.dart';

class ProfileScreen extends StatefulWidget {
  final bool showBottomNav;
  
  const ProfileScreen({
    Key? key,
    this.showBottomNav = true,
  }) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  double _scrollOffset = 0;
  
  static final _bucket = PageStorageBucket();
  
  // Service selection state
  String _selectedService = 'spotify'; // 'spotify' or 'apple_music'
  bool _isLoadingService = false;
  
  // Filter state for Bops tab
  String _bopsCurrentFilter = 'Liked'; // Will be set properly in initState
  
  // Different filter options based on selected service
  List<String> _getBopsFilterOptions(bool isSpotifyConnected) {
    if (_selectedService == 'apple_music') {
      return [
        'Liked Songs',
        'Pins',
        'Collected',
        'Upvoted',
        'Playlists',
        'Recently Added',
        'Heavy Rotation',
        'Recently Played',
        'Suggested Songs'
      ];
    } else {
      // Spotify filters
      final baseFilters = ['My Pins', 'Collected', 'Upvoted'];
      final spotifyFilters = [
        'Liked',
        'Top Tracks',
        'Playlists',
        'Recent',
        'Top Artists',
      ];
      final suggestedSongs = ['Suggested Songs'];

      if (isSpotifyConnected) {
        return [...spotifyFilters, ...baseFilters, ...suggestedSongs];
      } else {
        return [...baseFilters, ...suggestedSongs];
      }
    }
  }
  
  @override
  bool get wantKeepAlive => true;
  
  @override
  void initState() {
    super.initState();
    
    // Load saved service preference
    _loadSavedService();
    
    // Initialize tab controller with default index
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: 0,
    );
    
    // Restore state in post-frame callback
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      
      final savedState = _bucket.readState(context, identifier: 'profile_state') as Map<String, dynamic>?;
      if (savedState != null) {
        final tabIndex = savedState['tab_index'] as int? ?? 0;
        final scrollPosition = savedState['scroll_position'] as double? ?? 0.0;
        
        // Update tab index without animation
        if (_tabController.index != tabIndex) {
          _tabController.index = tabIndex;
        }
        
        // Restore scroll position if possible
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(scrollPosition);
        }
        
        // Note: Service and filter state are handled by _loadSavedService()
        // to ensure proper initialization order and avoid conflicts
      }
      
      // Add listeners after state restoration
      _tabController.addListener(_handleTabChange);
      _scrollController.addListener(_handleScroll);
    });
  }

  /// Load the saved service preference
  Future<void> _loadSavedService() async {
    try {
      final savedService = await AuthService.getSavedSelectedService();
      if (mounted) {
        // Check if we have saved state that should override defaults
        final savedState = _bucket.readState(context, identifier: 'profile_state') as Map<String, dynamic>?;
        final savedFilter = savedState?['bops_current_filter'] as String?;
        
        setState(() {
          _selectedService = savedService;
          
          // Only update filter if no saved filter exists, otherwise preserve user's choice
          if (savedFilter == null) {
            // Set default filter based on service when no previous filter is saved
            if (_selectedService == 'apple_music') {
              _bopsCurrentFilter = 'Liked Songs';
            } else {
              _bopsCurrentFilter = 'Liked';
            }
          } else {
            // Use the saved filter, but validate it's still valid for the current service
            final availableFilters = _getBopsFilterOptions(true); // Get all possible filters
            if (availableFilters.contains(savedFilter)) {
              _bopsCurrentFilter = savedFilter;
            } else {
              // If saved filter is not valid for current service, use default
              if (_selectedService == 'apple_music') {
                _bopsCurrentFilter = 'Liked Songs';
              } else {
                _bopsCurrentFilter = 'Liked';
              }
            }
          }
        });
      }
    } catch (e) {
      print('Error loading saved service: $e');
    }
  }

  void _handleTabChange() {
    if (!mounted || !_tabController.indexIsChanging) return;
    
    _saveCurrentState();
    
    // Ensure the new tab is properly built
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      setState(() {});
    });
  }

  void _handleScroll() {
    if (!mounted) return;
    
    setState(() {
      _scrollOffset = _scrollController.offset;
    });
    
    _saveCurrentState();
  }

  void _saveCurrentState() {
    if (!mounted) return;
    
    final currentState = {
      'tab_index': _tabController.index,
      'scroll_position': _scrollController.hasClients ? _scrollController.position.pixels : 0.0,
      'bops_current_filter': _bopsCurrentFilter, // Save current filter state
      'selected_service': _selectedService, // Save current service state
    };
    
    _bucket.writeState(context, currentState, identifier: 'profile_state');
  }

  /// Handle service selection with authentication check
  Future<void> _handleServiceSelection(String service) async {
    if (_isLoadingService) return;
    
    setState(() => _isLoadingService = true);
    
    try {
      bool isAuthenticated = false;
      
      if (service == 'spotify') {
        // For Spotify, just switch to my_pins_tab without authentication
        isAuthenticated = true;
      } else if (service == 'apple_music') {
        final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        isAuthenticated = appleMusicProvider.isConnected;
        
        if (!isAuthenticated) {
          // Request Apple Music permissions without backend communication
          final success = await appleMusicProvider.connect();
          if (!success && mounted) {
            // Show error if authentication failed
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to connect to Apple Music. Please try again.'),
                backgroundColor: Colors.red,
              ),
            );
            return;
          }
          isAuthenticated = success;
        }
      }
      
      if (isAuthenticated) {
        // Save the selected service
        await AuthService.saveSelectedService(service);
        
        setState(() {
          _selectedService = service;
          
          // Smart filter preservation: try to keep current filter if valid for new service
          final newServiceFilters = _getBopsFilterOptions(service == 'spotify'); // Get filters for new service
          if (!newServiceFilters.contains(_bopsCurrentFilter)) {
            // Current filter not available in new service, use appropriate default
            if (service == 'apple_music') {
              _bopsCurrentFilter = 'Liked Songs';
            } else {
              _bopsCurrentFilter = 'Liked';
            }
          }
          // If current filter is available in new service, keep it as is
        });
        
        // Reload all profile data after service change
        await _reloadProfileData();
        
        // If currently on Collections tab, switch to Bops tab to show service-specific content
        if (_tabController.index == 1) {
          _tabController.animateTo(0);
        }
        
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Switched to ${service == 'spotify' ? 'BOPmaps' : 'Apple Music'}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error switching service: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingService = false);
      }
    }
  }

  /// Authenticate with the specified service
  Future<void> _authenticateService(String service) async {
    if (_isLoadingService) return;
    
    setState(() => _isLoadingService = true);
    
    try {
      bool success = false;
      
      if (service == 'spotify') {
        // For Spotify, just return success since we're not doing auth
        success = true;
      } else if (service == 'apple_music') {
        // Request Apple Music permissions without backend communication
        final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        success = await appleMusicProvider.connect();
      }
      
      if (success && mounted) {
        // Save the selected service and update state
        await AuthService.saveSelectedService(service);
        setState(() {
          _selectedService = service;
          
          // Smart filter preservation: try to keep current filter if valid for new service
          final newServiceFilters = _getBopsFilterOptions(service == 'spotify'); // Get filters for new service
          if (!newServiceFilters.contains(_bopsCurrentFilter)) {
            // Current filter not available in new service, use appropriate default
            if (service == 'apple_music') {
              _bopsCurrentFilter = 'Liked Songs';
            } else {
              _bopsCurrentFilter = 'Liked';
            }
          }
          // If current filter is available in new service, keep it as is
        });
        
        // Reload all profile data after successful authentication
        await _reloadProfileData();
        
        // If currently on Collections tab, switch to Bops tab to show service-specific content
        if (_tabController.index == 1) {
          _tabController.animateTo(0);
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully connected to ${service == 'spotify' ? 'BOPmaps' : 'Apple Music'}'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to connect to ${service == 'spotify' ? 'BOPmaps' : 'Apple Music'}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Connection error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingService = false);
      }
    }
  }

  /// Reload all profile data after service change or authentication
  Future<void> _reloadProfileData() async {
    try {
      if (_selectedService == 'spotify') {
        // For Spotify, just force a rebuild since we're not loading external data
        if (kDebugMode) {
          print('✅ Switched to BOPmaps pins tab');
        }
      } else if (_selectedService == 'apple_music') {
        final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        if (appleMusicProvider.isConnected) {
          // Wait for provider to be fully connected and then load data
          await Future.delayed(const Duration(milliseconds: 500));
          
          // Load user data
          await appleMusicProvider.loadUserData();
          
          if (kDebugMode) {
            print('✅ Reloaded Apple Music profile data');
          }
        }
      }
      
      // Force rebuild of the pins tab with new data
      setState(() {});
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error reloading profile data: $e');
      }
    }
  }

  /// Refresh pins tab data after service change
  void _refreshPinsTabData() {
    // This method is now replaced by _reloadProfileData()
    // Force a rebuild of the pins tab by calling setState
    // The tab will automatically reload data based on the new selected service
    setState(() {});
  }

  @override
  void dispose() {
    // Save final state
    _saveCurrentState();
    
    // Remove listeners
    _tabController.removeListener(_handleTabChange);
    _scrollController.removeListener(_handleScroll);
    
    // Dispose controllers
    _tabController.dispose();
    _scrollController.dispose();
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    final authProvider = Provider.of<AuthProvider>(context);
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context);
    final youtubeProvider = Provider.of<YouTubeProvider>(context);
    final user = authProvider.currentUser;
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;
    final hasActiveMusic = (spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null) ||
                          (appleMusicProvider.hasActivePlayback && appleMusicProvider.currentTrack != null) ||
                          (youtubeProvider.hasActivePlayback && youtubeProvider.currentTrack != null);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final bopsFilterOptions = _getBopsFilterOptions(spotifyProvider.isConnected);

    // If the current filter is no longer valid, default to 'Pins'
        if (!bopsFilterOptions.contains(_bopsCurrentFilter)) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _bopsCurrentFilter = 'My Pins';
          });
        }
      });
    }
    
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        automaticallyImplyLeading: false,
        elevation: 0,
        toolbarHeight: 0,
      ),
      body: Stack(
        children: [
          // Main content
          NestedScrollView(
            key: const PageStorageKey('profile_nested_scroll'),
            controller: _scrollController,
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  expandedHeight: size.height * 0.27,
                  toolbarHeight: 56.0,
                  collapsedHeight: 56.0,
                  floating: false,
                  pinned: true,
                  stretch: true,
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  elevation: 0,
                  shadowColor: Colors.transparent,
                  automaticallyImplyLeading: false,
                  leading: widget.showBottomNav
                    ? IconButton(
                        icon: Icon(
                          Icons.settings,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withOpacity(0.9)
                              : Colors.black87,
                        ),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SettingsScreen(showBottomNav: true),
                            ),
                          );
                        },
                      )
                    : null,
                  leadingWidth: widget.showBottomNav ? 48 : null,
                  actions: widget.showBottomNav 
                    ? [
                        Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: IconButton(
                            icon: Icon(
                              Icons.search,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white.withOpacity(0.9)
                                  : Colors.black87,
                              size: 24,
                            ),
                            onPressed: () async {
                              final globalAIProvider = GlobalAIProviderService.instance;
                        
                              // Make sure the global provider is initialized
                              if (!globalAIProvider.isInitialized) {
                                await globalAIProvider.initializeIfAuthenticated(context);
                              }

                              Navigator.push(
                                context,
                                PageRouteBuilder(
                                  pageBuilder: (context, animation, secondaryAnimation) => 
                                      const AISearchView(),
                                  transitionsBuilder: (context, animation, secondaryAnimation, child) {
                                    return SlideTransition(
                                      position: Tween<Offset>(
                                        begin: const Offset(0.0, 1.0),
                                        end: Offset.zero,
                                      ).animate(CurvedAnimation(
                                        parent: animation,
                                        curve: Curves.easeInOut,
                                      )),
                                      child: child,
                                    );
                                  },
                                  transitionDuration: const Duration(milliseconds: 300),
                                ),
                              );
                            },
                            tooltip: 'Search & Discover Music',
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: IconButton(
                            icon: Icon(
                              Icons.notifications_outlined,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white.withOpacity(0.9)
                                  : Colors.black87,
                              size: 24,
                            ),
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const NotificationsScreen(),
                                ),
                              );
                            },
                            tooltip: 'Notifications',
                          ),
                        ),
                      ]
                    : null,
                  flexibleSpace: FlexibleSpaceBar(
                    background: SafeArea(
                      bottom: false,
                      child: ProfileHeader(
                        user: user,
                        size: size,
                        selectedService: _selectedService,
                        onServiceSelected: _handleServiceSelection,
                      ),
                    ),
                    stretchModes: const [
                      StretchMode.zoomBackground,
                      StretchMode.blurBackground,
                    ],
                  ),
                ),
                SliverToBoxAdapter(
                  child: Consumer<UserStatsProvider>(
                    builder: (context, statsProvider, child) {
                      return ProfileStats(
                        size: size,
                        stats: statsProvider.stats,
                      );
                    },
                  ),
                ),
                // Add a small spacing
                const SliverToBoxAdapter(
                  child: SizedBox(height: 8),
                ),
                SliverPersistentHeader(
                  pinned: true,
                  delegate: _SliverAppBarDelegate(
                    TabBar(
                      controller: _tabController,
                      labelColor: Theme.of(context).colorScheme.primary,
                      unselectedLabelColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      labelStyle: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                        letterSpacing: -0.2,
                      ),
                      unselectedLabelStyle: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 13,
                        letterSpacing: -0.2,
                      ),
                      indicator: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.08),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelPadding: EdgeInsets.symmetric(
                        horizontal: screenWidth < 360 ? 12 : 16,
                        vertical: 8,
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: screenWidth < 360 ? 24 : 32,
                        vertical: 8,
                      ),
                      dividerColor: Colors.transparent,
                      splashFactory: NoSplash.splashFactory,
                      overlayColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
                        return states.contains(MaterialState.focused) ? null : Colors.transparent;
                      }),
                      tabs: [
                        _buildTab(
                          Icons.push_pin_outlined,
                          'Bops',
                          screenWidth < 360 ? 18 : 20,
                          36,
                        ),
                        _buildTab(
                          Icons.collections,
                          'Collections',
                          screenWidth < 360 ? 18 : 20,
                          36,
                        ),
                      ],
                    ),
                  ),
                ),
                // Only show filter chips if Bops tab is selected
                if (_tabController.index == 0)
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: SliverFilterHeaderDelegate(
                      filterOptions: bopsFilterOptions,
                      currentFilter: _bopsCurrentFilter,
                      onFilterSelected: (filter) {
                        setState(() {
                          _bopsCurrentFilter = filter;
                        });
                      },
                      isSmallScreen: MediaQuery.of(context).size.width < 360,
                    ),
                  ),
              ];
            },
            body: TabBarView(
              controller: _tabController,
              children: [
                KeepAliveWrapper(
                  child: _selectedService == 'spotify'
                      ? MyPinsTab(
                          key: const PageStorageKey('my_pins_tab'),
                          currentFilter: _bopsCurrentFilter,
                          onFilterSelected: (filter) {
                            setState(() {
                              _bopsCurrentFilter = filter;
                            });
                          },
                        )
                      : AppleMusicPinsTab(
                          key: const PageStorageKey('apple_music_pins_tab'),
                          currentFilter: _bopsCurrentFilter,
                          onFilterSelected: (filter) {
                            setState(() {
                              _bopsCurrentFilter = filter;
                            });
                          },
                        ),
                ),
                const KeepAliveWrapper(
                  child: CollectionsTab(
                    key: PageStorageKey('collections_tab'),
                  ),
                ),
              ],
            ),
          ),
          
          // Loading overlay - positioned last to appear on top
          if (_isLoadingService)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Switching services...',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
      bottomNavigationBar: widget.showBottomNav ? Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // --- RECONNECT BUTTON ---
          // Consumer2<SpotifyProvider, AppleMusicProvider>(
          //   builder: (context, spotifyProvider, appleMusicProvider, child) {
          //     final isSpotifySelected = _selectedService == 'spotify';
          //     final isAppleMusicSelected = _selectedService == 'apple_music';
              
          //     final showSpotifyReconnect = isSpotifySelected && !spotifyProvider.isConnected && !spotifyProvider.isConnecting;
          //     final showAppleMusicReconnect = isAppleMusicSelected && !appleMusicProvider.isConnected && !appleMusicProvider.isConnecting;
              
          //     if (!showSpotifyReconnect && !showAppleMusicReconnect) {
          //       return const SizedBox.shrink();
          //     }
              
          //     return Padding(
          //       padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
          //       child: ElevatedButton.icon(
          //         style: ElevatedButton.styleFrom(
          //           backgroundColor: Theme.of(context).colorScheme.surface,
          //           foregroundColor: isSpotifySelected 
          //               ? const Color(0xFF1DB954)
          //               : const Color(0xFFFA243C),
          //           elevation: 1,
          //           shape: RoundedRectangleBorder(
          //             borderRadius: BorderRadius.circular(24),
          //           ),
          //           padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
          //         ),
          //         icon: const Icon(Icons.refresh, size: 20),
          //         label: Text(
          //           isSpotifySelected ? 'Reconnect to Spotify' : 'Connect to Apple Music',
          //           style: const TextStyle(fontSize: 15),
          //         ),
          //         onPressed: () => _authenticateService(_selectedService),
          //       ),
          //     );
          //   },
          // ),
          // // --- END RECONNECT BUTTON ---
          // Show appropriate now playing bar based on active service
          if (spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null)
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: const NowPlayingBar(),
            )
          else if (appleMusicProvider.hasActivePlayback && appleMusicProvider.currentTrack != null)
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: const AppleNowPlayingBar(),
            ),
          // YouTube removed - it has its own embedded player
          MusicPinBottomNavBar.auto(
            context: context,
            onTabSelected: _handleNavigation,
            onAddPinPressed: _handleAddPin,
          ),
        ],
      ) : null,
    );
  }

  Widget _buildTab(IconData icon, String text, double iconSize, [double height = 36]) {
    return Tab(
      height: height,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: iconSize),
            const SizedBox(width: 8),
            Text(
              text,
              style: const TextStyle(height: 1.2),
            ),
          ],
        ),
      ),
    );
  }

  void _handleAddPin() {
    // Handle add pin action
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add pin feature coming soon')),
    );
  }

  // Handle navigation to other tabs
  void _handleNavigation(int index) {
    if (index == 0) return; // Already on profile
    
    // Save current state before navigation
    _saveCurrentState();
    
    NavigationHelper.navigateToTab(context, index).then((_) {
      if (mounted) {
        // Restore state after returning
        SchedulerBinding.instance.addPostFrameCallback((_) {
          final savedState = _bucket.readState(context, identifier: 'profile_state') as Map<String, dynamic>?;
          if (savedState != null) {
            final tabIndex = savedState['tab_index'] as int? ?? 0;
            final scrollPosition = savedState['scroll_position'] as double? ?? 0.0;
            final savedFilter = savedState['bops_current_filter'] as String? ?? 'Liked';
            final savedService = savedState['selected_service'] as String? ?? 'spotify';
            
            if (tabIndex != _tabController.index) {
              _tabController.index = tabIndex;
            }
            
            if (_scrollController.hasClients) {
              _scrollController.jumpTo(scrollPosition);
            }

            // Restore filter state
            if (_bopsCurrentFilter != savedFilter) {
              setState(() {
                _bopsCurrentFilter = savedFilter;
              });
            }

            // Restore service state
            if (_selectedService != savedService) {
              setState(() {
                _selectedService = savedService;
                // Update filter based on loaded service
                if (_selectedService == 'apple_music') {
                  _bopsCurrentFilter = 'Liked Songs';
                } else {
                  _bopsCurrentFilter = 'Liked';
                }
              });
            }
          }
        });
      }
    });
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      height: maxExtent,
      color: Theme.of(context).scaffoldBackgroundColor,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) {
    return true; // Always rebuild to handle theme changes
  }
}

// Add KeepAliveWrapper widget
class KeepAliveWrapper extends StatefulWidget {
  final Widget child;

  const KeepAliveWrapper({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<KeepAliveWrapper> createState() => _KeepAliveWrapperState();
}

class _KeepAliveWrapperState extends State<KeepAliveWrapper>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
} 