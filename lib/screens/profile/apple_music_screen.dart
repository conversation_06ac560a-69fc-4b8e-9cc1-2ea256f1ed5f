import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../widgets/profile/apple_music_pins_tab.dart';
import '../../widgets/profile/components/filter_header.dart';
import '../../widgets/navigation/bottom_navigation_bar.dart';
import '../../widgets/music/now_playing_bar.dart';
import '../../utils/navigation_helper.dart';
import '../../utils/app_tab.dart';

class AppleMusicScreen extends StatefulWidget {
  final bool showBottomNav;
  
  const AppleMusicScreen({
    Key? key,
    this.showBottomNav = true,
  }) : super(key: key);

  @override
  State<AppleMusicScreen> createState() => _AppleMusicScreenState();
}

class _AppleMusicScreenState extends State<AppleMusicScreen> {
  String _currentFilter = 'Liked Songs';
  final List<String> _filters = ['Liked Songs', 'Pins', 'Playlists', 'Recently Added', 'Heavy Rotation', 'Recently Played'];

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: isDark ? Colors.white : Colors.black87,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Apple Music',
          style: TextStyle(
            color: isDark ? Colors.white : Colors.black87,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          Consumer<AppleMusicProvider>(
            builder: (context, appleMusicProvider, child) {
              if (!appleMusicProvider.isConnected) {
                return IconButton(
                  icon: Icon(
                    Icons.link,
                    color: isDark ? Colors.white70 : Colors.black54,
                  ),
                  onPressed: () async {
                    await appleMusicProvider.connect();
                  },
                  tooltip: 'Connect to Apple Music',
                );
              }
              return IconButton(
                icon: Icon(
                  Icons.refresh,
                  color: isDark ? Colors.white70 : Colors.black54,
                ),
                onPressed: () async {
                  await appleMusicProvider.refreshUserData();
                },
                tooltip: 'Refresh data',
              );
            },
          ),
        ],
      ),
      body: Consumer<AppleMusicProvider>(
        builder: (context, appleMusicProvider, child) {
          return Column(
            children: [
              // Connection status banner
              if (!appleMusicProvider.isConnected)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFA243C).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0xFFFA243C).withOpacity(0.3),
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.link_off,
                        size: 32,
                        color: const Color(0xFFFA243C),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Connect to Apple Music',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Connect your Apple Music account to see your music pins, liked songs, and playlists.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.white70 : Colors.black54,
                        ),
                      ),
                      const SizedBox(height: 12),
                      ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFFA243C),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 10,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        icon: const Icon(Icons.music_note),
                        label: const Text('Connect Apple Music'),
                        onPressed: () async {
                          await appleMusicProvider.connect();
                        },
                      ),
                    ],
                  ),
                ),
              
              // Filter header
              if (appleMusicProvider.isConnected)
                FilterHeader(
                  filterOptions: _filters,
                  currentFilter: _currentFilter,
                  onFilterSelected: (filter) {
                    setState(() {
                      _currentFilter = filter;
                    });
                  },
                  isSmallScreen: MediaQuery.of(context).size.width < 360,
                ),
              
              // Content
              Expanded(
                child: appleMusicProvider.isConnected
                    ? AppleMusicPinsTab(
                        currentFilter: _currentFilter,
                        onFilterSelected: (filter) {
                          setState(() {
                            _currentFilter = filter;
                          });
                        },
                      )
                    : const Center(
                        child: Text(
                          'Connect to Apple Music to view your content',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
              ),
            ],
          );
        },
      ),
      bottomNavigationBar: widget.showBottomNav
          ? Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Consumer<AppleMusicProvider>(
                  builder: (context, appleMusicProvider, child) {
                    if (appleMusicProvider.hasActivePlayback && 
                        appleMusicProvider.currentTrack != null) {
                      return Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, -2),
                            ),
                          ],
                        ),
                        child: const NowPlayingBar(),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
                MusicPinBottomNavBar.auto(
                  context: context,
                  onTabSelected: _handleNavigation,
                  onAddPinPressed: _handleAddPin,
                ),
              ],
            )
          : null,
    );
  }

  void _handleNavigation(int index) {
    NavigationHelper.navigateToTab(context, index);
  }

  void _handleAddPin() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add pin feature coming soon')),
    );
  }
} 