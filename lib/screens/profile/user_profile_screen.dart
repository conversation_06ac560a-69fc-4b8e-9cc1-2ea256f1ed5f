import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/public_profile_provider.dart';
import '../../providers/pin_provider.dart'; // Added import for PinProvider
import '../../models/friendship_status.dart';
import '../../models/public_user_profile.dart';
import '../../models/pin.dart';
import '../../models/collection_model.dart';
import '../../models/music_track.dart';
import '../../models/music/bop_drop.dart';
import '../../services/api/bop_drops_service.dart';
import '../../widgets/profile/components/user_profile_header.dart';
import '../../widgets/profile/components/user_profile_stats.dart';
import '../../widgets/profile/components/user_profile_actions.dart';
import '../../widgets/profile/components/user_profile_skeleton.dart';
import '../../widgets/navigation/bottom_navigation_bar.dart';
import '../../widgets/music/now_playing_bar.dart';
import '../../widgets/common/cached_avatar.dart';
import '../../widgets/common/cloudinary_image.dart';
import '../../screens/map/my_bop_map.dart';
import '../../screens/friends/music_chat_screen.dart';
import '../../screens/collections/collection_detail_screen.dart';
import '../../widgets/bottomsheets/block_user_bottomsheet.dart';
import '../../widgets/bottomsheets/report_user_bottomsheet.dart';
import '../../widgets/bottomsheets/nearby_pin_bottomsheet.dart';
import '../../widgets/bottomsheets/distant_pin_bottomsheet.dart';
import 'package:flutter/scheduler.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:ui';
import '../../models/user.dart';
import 'package:geolocator/geolocator.dart';

class UserProfileScreen extends StatefulWidget {
  final int userId;
  final bool showBottomNav;
  
  const UserProfileScreen({
    Key? key,
    required this.userId,
    this.showBottomNav = false,
  }) : super(key: key);

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;
  List<BopDrop> _userBopDrops = [];
  bool _isLoadingBopDrops = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadUserData();
      }
    });
  }

  Future<void> _loadUserData() async {
    print('🚀 Starting to load user data for userId: ${widget.userId}');
    try {
      // Load real user profile data
      print('📊 Loading user profile...');
      await context.read<PublicProfileProvider>().loadUserProfile(widget.userId);
      print('✅ User profile loaded successfully');
      
      // Load bop drops after profile is loaded
      if (mounted) {
        print('📺 Loading bop drops...');
        await _loadUserBopDrops();
        print('✅ Bop drops loaded successfully');
      }
    } catch (e) {
      print('Error loading user data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load user profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadUserBopDrops() async {
    if (!mounted) return;
    
    setState(() => _isLoadingBopDrops = true);
    try {
      // Get combined feed (includes both friends' and user's own bop drops)
      final allBopDrops = await BopDropsService.getCombinedFeed(includeOwnDrops: true);
      if (mounted) {
        setState(() {
          _userBopDrops = allBopDrops
              .where((drop) => drop.user.id == widget.userId)
              .take(6)
              .toList();
        });
      }
    } catch (e) {
      print('Error loading user bop drops: $e');
      // Don't show error for bop drops as it's not critical
    } finally {
      if (mounted) {
        setState(() => _isLoadingBopDrops = false);
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final publicProfileProvider = Provider.of<PublicProfileProvider>(context);
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final profile = publicProfileProvider.currentProfile;
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;
    
    // Check if viewing own profile
    final isOwnProfile = context.read<AuthProvider>().currentUser?.id == widget.userId;
    
    if (publicProfileProvider.isLoading && profile == null) {
      return const UserProfileSkeleton();
    }
    
    if (profile == null) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            publicProfileProvider.error ?? 'Profile Not Found',
            style: const TextStyle(fontSize: 16),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.person_off_outlined,
                size: 64,
                color: theme.colorScheme.onSurface.withOpacity(0.3),
              ),
              const SizedBox(height: 16),
              Text(
                publicProfileProvider.error ?? 'User profile not found',
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _loadUserData(),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        automaticallyImplyLeading: false,
        elevation: 0,
        toolbarHeight: 0,
      ),
      body: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            // Main header like your profile screen
            SliverAppBar(
              expandedHeight: size.height * 0.22, // Reduced from 0.27
              toolbarHeight: 56.0,
              collapsedHeight: 56.0,
              floating: false,
              pinned: false,
              stretch: true,
              backgroundColor: Colors.transparent,
              elevation: 0,
              shadowColor: Colors.transparent,
              automaticallyImplyLeading: false,
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Colors.white.withOpacity(0.9),
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
              actions: _buildAppBarActions(profile.displayName),
              flexibleSpace: FlexibleSpaceBar(
                title: null,
                background: Stack(
                  children: [
                    // Amazing gradient background like your profile screen
                    _buildGradientBackground(size.height * 0.22, theme.brightness == Brightness.dark), // Reduced from 0.27
                    
                    // Subtle pattern overlay like your profile screen
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          backgroundBlendMode: theme.brightness == Brightness.dark ? BlendMode.overlay : BlendMode.multiply,
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              (theme.brightness == Brightness.dark ? Colors.white : Colors.black).withOpacity(0.03),
                              (theme.brightness == Brightness.dark ? Colors.white : Colors.black).withOpacity(0.01),
                              (theme.brightness == Brightness.dark ? Colors.black : Colors.white).withOpacity(0.01),
                              (theme.brightness == Brightness.dark ? Colors.black : Colors.white).withOpacity(0.03),
                            ],
                  ),
                        ),
                      ),
                    ),
                    
                    // User profile content (positioned at bottom to separate from gradient)
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        // Add a semi-transparent background to separate profile section from gradient
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              theme.scaffoldBackgroundColor.withOpacity(0.8),
                              theme.scaffoldBackgroundColor,
                            ],
                            stops: const [0.0, 0.7, 1.0],
                          ),
                        ),
                        child: SafeArea(
                  bottom: false,
                  child: UserProfileHeader(
                    profile: profile,
                    size: size,
                    bopDropsCount: _userBopDrops.length,
                  ),
                        ),
                      ),
                    ),
                  ],
                ),
                stretchModes: const [
                  StretchMode.zoomBackground,
                  StretchMode.blurBackground,
                ],
              ),
            ),
            
            // Stats section like your profile screen
            SliverToBoxAdapter(
              child: UserProfileStats(
                profile: profile,
                size: size,
                bopDropsCount: _userBopDrops.length,
              ),
            ),
            
            // Action buttons section (only for non-own profiles)
            if (!isOwnProfile)
              SliverToBoxAdapter(
                child: UserProfileActions(profile: profile),
              ),
            
            // Bio section
            if (profile.bio != null && profile.bio!.isNotEmpty)
              SliverToBoxAdapter(
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                    ),
                  ),
                  child: Text(
                    profile.bio!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            
            // Add spacing
            const SliverToBoxAdapter(
              child: SizedBox(height: 8),
            ),
            
            // Tab bar like your profile screen
            SliverPersistentHeader(
              pinned: false,
              delegate: _SliverAppBarDelegate(
                TabBar(
                  controller: _tabController,
                  isScrollable: screenWidth < 360,
                  labelColor: theme.colorScheme.primary,
                  unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.6),
                  labelStyle: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                    letterSpacing: -0.2,
                  ),
                  unselectedLabelStyle: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                    letterSpacing: -0.2,
                  ),
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: theme.colorScheme.primary.withOpacity(0.08),
                    border: Border.all(
                      color: theme.colorScheme.primary.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelPadding: EdgeInsets.symmetric(
                    horizontal: screenWidth < 360 ? 8 : 12,
                    vertical: 8,
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: screenWidth < 360 ? 16 : 24,
                    vertical: 8,
                  ),
                  dividerColor: Colors.transparent,
                  splashFactory: NoSplash.splashFactory,
                  overlayColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
                    return states.contains(MaterialState.focused) ? null : Colors.transparent;
                  }),
                  tabs: [
                    _buildTab(Icons.music_note_outlined, 'Music', screenWidth < 360 ? 18 : 20, 36),
                    _buildTab(Icons.push_pin_outlined, 'Pins', screenWidth < 360 ? 18 : 20, 36),
                    _buildTab(Icons.collections, screenWidth < 360 ? 'Items' : 'Collections', screenWidth < 360 ? 18 : 20, 36),
                  ],
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildMusicTab(profile, theme),
            _buildPinsTab(theme),
            _buildCollectionsTab(theme),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(spotifyProvider),
    );
  }

  Widget _buildTab(IconData icon, String text, double iconSize, double height) {
    return SizedBox(
      height: height,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: iconSize),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMusicTab(PublicUserProfile profile, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent Recs
          _buildSectionHeader('Recent Recs', '${_userBopDrops.length}', theme),
          
          if (_isLoadingBopDrops) 
            _buildInlineSkeletonGrid(theme)
          else if (_userBopDrops.isEmpty)
            _buildEmptyMusicState(theme)
          else
            _buildBopDropsGrid(theme),
          
          const SizedBox(height: 20),
          
          // Music Stats
          _buildSectionHeader('Music Stats', '', theme),
          _buildMusicStats(theme),
          
          const SizedBox(height: 20),
          
          // Top Genres
          _buildSectionHeader('Top Genres', '', theme),
          _buildTopGenres(theme),
        ],
      ),
    );
  }

  Widget _buildPinsTab(ThemeData theme) {
    return Consumer<PublicProfileProvider>(
      builder: (context, provider, child) {
        final pins = provider.userPins;
        print('🎨 Building pins tab - pins count: ${pins.length}, isLoading: ${provider.isLoadingPins}, error: ${provider.error}');
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Pins', '${pins.length}', theme),
              
              if (provider.isLoadingPins)
                _buildInlineSkeletonGrid(theme)
              else if (pins.isEmpty)
                _buildEmptyState(
                  'No pins yet',
                  'This user hasn\'t dropped any pins yet.',
                  Icons.push_pin_outlined,
                  theme,
                )
              else
                _buildModernPinsGrid(pins, theme),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCollectionsTab(ThemeData theme) {
    return Consumer<PublicProfileProvider>(
      builder: (context, provider, child) {
        final collections = provider.userCollections;
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Collections', '${collections.length}', theme),
              
              if (provider.isLoadingCollections)
                _buildInlineSkeletonGrid(theme)
              else if (collections.isEmpty)
                _buildEmptyState(
                  'No collections yet',
                  'This user hasn\'t created any collections yet.',
                  Icons.collections_outlined,
                  theme,
                )
              else
                _buildModernCollectionsGrid(collections, theme),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title, String count, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              height: 1.0,
            ),
          ),
          if (count.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                count,
                style: TextStyle(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  height: 1.0,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBopDropsGrid(ThemeData theme) {
    return Container(
      margin: EdgeInsets.zero,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          childAspectRatio: 1.1,
        ),
        itemCount: _userBopDrops.length,
        itemBuilder: (context, index) {
          final bopDrop = _userBopDrops[index];
          return _buildBopDropCard(bopDrop, theme);
        },
      ),
    );
  }

  Widget _buildBopDropCard(BopDrop bopDrop, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(0.08),
            theme.colorScheme.primary.withOpacity(0.03),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.15),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // Main content
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Music icon and mood
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.music_note_rounded,
                        size: 18,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    if (bopDrop.mood != null)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _getMoodEmoji(bopDrop.mood!),
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Caption
                Expanded(
                  child: Text(
                    bopDrop.caption ?? 'New recommendation',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // Stats row
                Row(
                  children: [
                    Icon(
                      Icons.favorite_rounded,
                      size: 14,
                      color: Colors.red.withOpacity(0.8),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${bopDrop.likeCount}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 11,
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      Icons.visibility_rounded,
                      size: 14,
                      color: theme.colorScheme.primary.withOpacity(0.7),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${bopDrop.viewCount}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 11,
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Subtle gradient overlay for depth
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    theme.colorScheme.primary.withOpacity(0.1),
                    Colors.transparent,
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to convert mood string to emoji
  String _getMoodEmoji(String mood) {
    switch (mood.toLowerCase()) {
      case 'happy':
        return '😊';
      case 'sad':
        return '😢';
      case 'energetic':
        return '⚡';
      case 'chill':
        return '😎';
      case 'nostalgic':
        return '🌅';
      case 'party':
        return '🎉';
      case 'focus':
        return '🎯';
      case 'romantic':
        return '💕';
      case 'angry':
        return '😠';
      default:
        return '🎵';
    }
  }

  // Helper method to get rarity color
  Color _getRarityColor(String rarity, ThemeData theme) {
    switch (rarity.toLowerCase()) {
      case 'common':
        return Colors.grey;
      case 'uncommon':
        return Colors.green;
      case 'rare':
        return Colors.blue;
      case 'epic':
        return Colors.purple;
      case 'legendary':
        return Colors.orange;
      default:
        return theme.colorScheme.secondary;
    }
  }

  // Helper method to get rarity emoji
  String _getRarityEmoji(String rarity) {
    switch (rarity.toLowerCase()) {
      case 'common':
        return '⚪';
      case 'uncommon':
        return '🟢';
      case 'rare':
        return '🔵';
      case 'epic':
        return '🟣';
      case 'legendary':
        return '🟠';
      default:
        return '⚪';
    }
  }

  Widget _buildMusicStats(ThemeData theme) {
    // Mock stats for now - could be enhanced with real Spotify data
    final stats = [
      ('Recommendations', '${_userBopDrops.length}'),
      ('Likes Received', '${_userBopDrops.fold(0, (sum, drop) => sum + drop.likeCount)}'),
      ('Active Days', '12'),
      ('Favorite Genre', 'Hip-Hop'),
    ];

    return Container(
      margin: EdgeInsets.zero,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 2.2,
        ),
        itemCount: stats.length,
        itemBuilder: (context, index) {
          final stat = stats[index];
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withOpacity(0.1),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Text(
                    stat.$2,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 2),
                Flexible(
                  child: Text(
                    stat.$1,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                      fontSize: 11,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTopGenres(ThemeData theme) {
    final genres = ['Hip-Hop', 'Pop', 'R&B', 'Jazz', 'Electronic'];
    
    return Container(
      margin: EdgeInsets.zero,
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: genres.map((genre) => Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: theme.colorScheme.primary.withOpacity(0.2),
            ),
          ),
          child: Text(
            genre,
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        )).toList(),
      ),
    );
  }

  Widget _buildEmptyMusicState(ThemeData theme) {
    return _buildEmptyState(
      'No recommendations yet',
      'This user hasn\'t shared any recommendations yet.',
      Icons.music_off_outlined,
      theme,
    );
  }

  Widget _buildInlineSkeletonGrid(ThemeData theme) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
        childAspectRatio: 1.1,
      ),
      itemCount: 6, // Number of skeleton items
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.onSurface.withOpacity(0.1),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 10,
                width: 100,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 10,
                width: 80,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildModernPinsGrid(List<Pin> pins, ThemeData theme) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 0.85,
      ),
      itemCount: pins.length,
      itemBuilder: (context, index) {
        final pin = pins[index];
        return _buildPinCard(pin, theme);
      },
    );
  }

  Widget _buildPinCard(Pin pin, ThemeData theme) {
    return GestureDetector(
      onTap: () => _onPinTapped(pin),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.surface,
              theme.colorScheme.surface.withOpacity(0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: theme.colorScheme.primary.withOpacity(0.15),
          ),
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.primary.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 8),
            
            // Album artwork or pin icon
            Stack(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: _buildPinImage(pin, theme),
                  ),
                ),
                
                // Service badge
                if (pin.service.isNotEmpty)
                  Positioned(
                    bottom: 2,
                    right: 2,
                    child: _buildServiceBadge(pin.service, theme),
                  ),
                
                // Music note indicator if it has a track
                if (pin.trackUrl.isNotEmpty)
                  Positioned(
                    top: 2,
                    left: 2,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.secondary,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: const Icon(
                        Icons.music_note,
                        size: 10,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // Track title or pin title
            Flexible(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: Text(
                  pin.trackTitle.isNotEmpty 
                      ? pin.trackTitle 
                      : pin.title?.isNotEmpty == true 
                          ? pin.title! 
                          : pin.locationName?.isNotEmpty == true 
                              ? pin.locationName! 
                              : 'Pin',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            
            // Artist name if available
            if (pin.trackArtist.isNotEmpty) ...[
              const SizedBox(height: 2),
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Text(
                    pin.trackArtist,
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontSize: 9,
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                      height: 1.1,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 4),
            
            // Rarity indicator
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: _getRarityColor(pin.rarity, theme).withOpacity(0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                _getRarityEmoji(pin.rarity),
                style: const TextStyle(fontSize: 8),
              ),
            ),
            
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  void _onPinTapped(Pin pin) async {
    final publicProfileProvider = context.read<PublicProfileProvider>();
    
    // If they're friends, play the pin's track
    if (publicProfileProvider.friendshipStatus == FriendshipStatus.friends) {
      if (pin.trackUrl.isNotEmpty && pin.trackTitle.isNotEmpty) {
        _playPin(pin);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Pin "${pin.title}" - no track available'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } else {
      // If they're not friends, show the appropriate bottom sheet based on distance
      try {
        final currentPosition = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        );
        
        final distance = Geolocator.distanceBetween(
          currentPosition.latitude,
          currentPosition.longitude,
          pin.latitude,
          pin.longitude,
        );
        
        // Convert pin to Map for bottom sheets
        final pinData = pin.toJson();
        
        if (!mounted) return;
        
        if (distance < 500) {
          // Show nearby pin bottom sheet
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (context) => NearbyPinBottomSheet(
              pinData: pinData,
              onPlayPressed: () {
                Navigator.pop(context);
                _playPin(pin);
              },
            ),
          );
        } else {
          // Show distant pin bottom sheet
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (context) => DistantPinBottomSheet(
              pinData: pinData,
              currentPosition: currentPosition,
            ),
          );
        }
      } catch (e) {
        print('Error getting location: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to get your location'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Play a pin using the available music service
  Future<void> _playPin(Pin pin) async {
    try {
      // Get providers
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
      final pinProvider = Provider.of<PinProvider>(context, listen: false);
      
      // Set the currently playing pin in PinProvider to record interactions
      final pinData = {
        'id': pin.id.toString(),
        'title': pin.title,
        'track_title': pin.trackTitle,
        'artist': pin.trackArtist,
        'track_artist': pin.trackArtist,
        'album': pin.album ?? '',
        'artwork_url': pin.artworkUrl,
        'service': pin.service,
        'track_url': pin.trackUrl,
        'caption': pin.caption ?? '',
        'description': pin.description ?? '',
        'location': {
          'type': 'Point',
          'coordinates': [pin.longitude, pin.latitude],
        },
        'latitude': pin.latitude,
        'longitude': pin.longitude,
        'aura_radius': pin.auraRadius,
        'rarity': pin.rarity,
        'skin': pin.skin,
        'skin_details': pin.skinDetails,
        'is_private': pin.isPrivate,
        'created_at': pin.createdAt.toIso8601String(),
        'updated_at': pin.updatedAt.toIso8601String(),
        'duration_ms': pin.durationMs,
        'location_name': pin.locationName,
        'upvote_count': pin.upvoteCount,
        'downvote_count': pin.downvoteCount,
        'owner': {
          'id': pin.owner.id,
          'username': pin.owner.username,
          'email': pin.owner.email,
          'profile_pic': pin.owner.profilePicUrl,
          'is_verified': pin.owner.isVerified,
          'favorite_genres': pin.owner.favoriteGenres,
          'connected_services': pin.owner.connectedServices,
          'created_at': pin.owner.createdAt.toIso8601String(),
        },
        'interaction_count': pin.interactionCount,
        'engagement_counts': pin.engagementCounts,
      };
      
      // Set currently playing pin (this will automatically record view and collect interactions)
      pinProvider.setCurrentlyPlayingPin(pin.id.toString(), pinData);
      
      // Create a MusicTrack from the pin
      final track = MusicTrack(
        id: pin.trackUrl.split('/').last.split('?').first, // Extract ID from URL
        title: pin.trackTitle,
        artist: pin.trackArtist,
        album: pin.album ?? '',
        albumArt: pin.artworkUrl ?? '',
        url: pin.trackUrl,
        service: pin.service,
        serviceType: pin.service,
        genres: [],
        durationMs: pin.durationMs ?? 0,
        popularity: 50,
        uri: _constructTrackUri(pin),
        isLibrary: false,
      );
      
      bool success = false;
      
      // Try the appropriate service based on the pin's service
      if (pin.service.toLowerCase().contains('spotify') && spotifyProvider.isConnected) {
        success = await spotifyProvider.playTrack(track, context: context);
      } else if (pin.service.toLowerCase().contains('apple') && appleMusicProvider.isConnected) {
        success = await appleMusicProvider.playTrack(track);
      } else {
        // Fallback: try Spotify with automatic Apple Music fallback
        success = await spotifyProvider.playTrack(track, context: context);
      }
      
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playing "${pin.trackTitle}" by ${pin.trackArtist}'),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        // Clear the pin data if playback failed
        pinProvider.clearCurrentlyPlayingPin();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to play "${pin.trackTitle}"'),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Clear the pin data on error
      final pinProvider = Provider.of<PinProvider>(context, listen: false);
      pinProvider.clearCurrentlyPlayingPin();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error playing "${pin.trackTitle}": $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// Construct track URI from pin data
  String _constructTrackUri(Pin pin) {
    final service = pin.service.toLowerCase();
    
    if (service.contains('spotify')) {
      if (pin.trackUrl.startsWith('spotify:track:')) {
        return pin.trackUrl;
      } else if (pin.trackUrl.contains('spotify.com/track/')) {
        final id = pin.trackUrl.split('/track/').last.split('?').first;
        return 'spotify:track:$id';
      } else {
        // Assume the trackUrl contains an ID
        return 'spotify:track:${pin.trackUrl}';
      }
    } else if (service.contains('apple')) {
      if (pin.trackUrl.contains('music.apple.com') && pin.trackUrl.contains('?i=')) {
        final trackId = pin.trackUrl.split('?i=').last.split('&').first;
        return 'apple:track:$trackId';
      } else if (pin.trackUrl.contains('music.apple.com/') && pin.trackUrl.contains('/song/')) {
        final parts = pin.trackUrl.split('/');
        if (parts.isNotEmpty) {
          final trackId = parts.last.split('?').first;
          return 'apple:track:$trackId';
        }
      }
      return pin.trackUrl;
    } else {
      return pin.trackUrl;
    }
  }

  Widget _buildModernCollectionsGrid(List<Collection> collections, ThemeData theme) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.1,
      ),
      itemCount: collections.length,
      itemBuilder: (context, index) {
        final collection = collections[index];
        return _buildCollectionCard(collection, theme);
      },
    );
  }

  Widget _buildCollectionCard(Collection collection, ThemeData theme) {
    final primaryColor = collection.primaryColor ?? theme.colorScheme.primary;
    
    return GestureDetector(
      onTap: () => _onCollectionTapped(collection),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.surface,
              theme.colorScheme.surface.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: primaryColor.withOpacity(0.15),
          ),
          boxShadow: [
            BoxShadow(
              color: primaryColor.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Collection cover image or icon
              Stack(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: collection.coverImageUrls.isNotEmpty
                          ? _buildCollectionCoverImage(collection.coverImageUrls.first, primaryColor)
                          : Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    primaryColor.withOpacity(0.2),
                                    primaryColor.withOpacity(0.1),
                                  ],
                                ),
                              ),
                              child: Icon(
                                Icons.collections_rounded,
                                size: 32,
                                color: primaryColor,
                              ),
                            ),
                    ),
                  ),
                  
                  // Item count badge
                  if (collection.itemCount > 0)
                    Positioned(
                      bottom: -2,
                      right: -2,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.secondary,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.white, width: 1),
                        ),
                        child: Text(
                          '${collection.itemCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Collection name
              Flexible(
                child: Text(
                  collection.name,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              const SizedBox(height: 4),
              
              // Privacy indicator with smaller icons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    collection.isPublic ? Icons.public : Icons.lock,
                    size: 10,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 2),
                  Text(
                    collection.isPublic ? 'Public' : 'Private',
                    style: TextStyle(
                      fontSize: 9,
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCollectionCoverImage(String imageUrl, Color primaryColor) {
    // Check if it's a Cloudinary URL
    if (imageUrl.contains('res.cloudinary.com')) {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      if (pathSegments.length >= 6) {
        // Extract public ID from Cloudinary URL
        final publicId = pathSegments.sublist(6).join('/').replaceAll(RegExp(r'\.[^.]*$'), '');
        return CloudinaryImage(
          publicId: publicId,
          width: 60,
          height: 60,
          quality: 85,
          format: 'webp',
          fit: BoxFit.cover,
          errorWidget: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primaryColor.withOpacity(0.2),
                  primaryColor.withOpacity(0.1),
                ],
              ),
            ),
            child: Icon(
              Icons.collections_rounded,
              size: 32,
              color: primaryColor,
            ),
          ),
        );
      }
    }

    // Fallback to regular network image
    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              primaryColor.withOpacity(0.2),
              primaryColor.withOpacity(0.1),
            ],
          ),
        ),
        child: Icon(
          Icons.collections_rounded,
          size: 32,
          color: primaryColor,
        ),
      ),
      errorWidget: (context, url, error) => Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              primaryColor.withOpacity(0.2),
              primaryColor.withOpacity(0.1),
            ],
          ),
        ),
        child: Icon(
          Icons.collections_rounded,
          size: 32,
          color: primaryColor,
        ),
      ),
    );
  }

  void _onCollectionTapped(Collection collection) {
    // Navigate to collection detail screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CollectionDetailScreen(collection: collection),
      ),
    );
  }

  Widget _buildPinImage(Pin pin, ThemeData theme) {
    final publicProfileProvider = context.read<PublicProfileProvider>();
    
    // If they're not friends and the pin has a skin image, show the skin image
    if (publicProfileProvider.friendshipStatus != FriendshipStatus.friends && 
        pin.skinDetails != null && 
        pin.skinDetails!['image'] != null) {
      return CachedNetworkImage(
        imageUrl: pin.skinDetails!['image'],
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: theme.colorScheme.primary.withOpacity(0.1),
          child: Icon(
            Icons.push_pin_rounded,
            color: theme.colorScheme.primary,
            size: 24,
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: theme.colorScheme.primary.withOpacity(0.1),
          child: Icon(
            Icons.push_pin_rounded,
            color: theme.colorScheme.primary,
            size: 24,
          ),
        ),
      );
    }
    
    // Otherwise, show the artwork or a default pin icon
    if (pin.artworkUrl?.isNotEmpty == true) {
      return CachedNetworkImage(
        imageUrl: pin.artworkUrl!,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: theme.colorScheme.primary.withOpacity(0.1),
          child: Icon(
            Icons.music_note,
            color: theme.colorScheme.primary,
            size: 24,
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: theme.colorScheme.primary.withOpacity(0.1),
          child: Icon(
            Icons.music_note,
            color: theme.colorScheme.primary,
            size: 24,
          ),
        ),
      );
    } else {
      return Container(
        color: theme.colorScheme.primary.withOpacity(0.1),
        child: Icon(
          Icons.push_pin_rounded,
          color: theme.colorScheme.primary,
          size: 24,
        ),
      );
    }
  }

  Widget _buildServiceBadge(String service, ThemeData theme) {
    Color badgeColor;
    IconData icon;
    
    switch (service.toLowerCase()) {
      case 'spotify':
        badgeColor = const Color(0xFF1DB954);
        icon = Icons.music_note;
        break;
      case 'apple_music':
        badgeColor = const Color(0xFFFA243C);
        icon = Icons.music_note;
        break;
      case 'youtube_music':
        badgeColor = const Color(0xFFFF0000);
        icon = Icons.play_circle_fill;
        break;
      default:
        badgeColor = Colors.grey;
        icon = Icons.music_note;
    }
    
    return Container(
      width: 16,
      height: 16,
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Icon(icon, color: Colors.white, size: 10),
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon, ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.08),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              size: 40,
              color: theme.colorScheme.primary.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 6),
          Text(
            subtitle,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<Widget> _buildAppBarActions(String username) {
    return [
      PopupMenuButton<String>(
        icon: const Icon(
          Icons.more_vert,
          color: Colors.white,
        ),
        onSelected: (value) => _handleMenuAction(value, username, widget.userId),
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'block',
            child: Row(
              children: [
                Icon(Icons.block, color: Colors.red),
                SizedBox(width: 8),
                Text('Block User'),
              ],
            ),
          ),
          const PopupMenuItem(
            value: 'report',
            child: Row(
              children: [
                Icon(Icons.flag, color: Colors.orange),
                SizedBox(width: 8),
                Text('Report User'),
              ],
            ),
          ),
        ],
      ),
    ];
  }

  Widget _buildBottomBar(SpotifyProvider spotifyProvider) {
    if (!widget.showBottomNav) return const SizedBox.shrink();
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (spotifyProvider.hasActivePlayback)
          const NowPlayingBar(),
        MusicPinBottomNavBar.auto(
          context: context,
          onTabSelected: (index) {
            // Handle navigation
          },
          onAddPinPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Add pin feature coming soon')),
            );
          },
        ),
      ],
    );
  }

  Future<void> _handleMenuAction(String action, String username, int userId) async {
    if (action == 'block') {
      HapticFeedback.mediumImpact();
      
      // Show beautiful block bottom sheet
      BlockUserBottomSheet.show(
        context,
        userId: userId,
        username: username,
        onBlocked: () {
          // Handle successful block - could navigate back or update UI
          print('User $username has been blocked');
        },
        );
    } else if (action == 'report') {
      HapticFeedback.mediumImpact();
      
      // Show beautiful report bottom sheet
      ReportUserBottomSheet.show(
        context,
        userId: userId,
        username: username,
        onReported: () {
          // Handle successful report
          print('User $username has been reported');
        },
        );
    }
  }

  void _openMusicChat(PublicUserProfile profile) {
    // Create a User object from PublicUserProfile for MusicChatScreen
    final user = User(
      id: profile.id,
      username: profile.username,
      email: '', // Not available in public profile
      profilePicUrl: profile.profilePicUrl,
      bio: profile.bio,
      location: profile.location,
      isVerified: false, // Remove verified status as requested
      favoriteGenres: [], // Not available in public profile
      connectedServices: {}, // Not available in public profile
      createdAt: DateTime.now(), // Not available in public profile
      lastActive: profile.lastActive,
    );
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MusicChatScreen(friend: user),
      ),
    );
  }

  void _openMap() {
    final profile = context.read<PublicProfileProvider>().currentProfile;
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MyBOPMap(
          userId: widget.userId.toString(),
          username: profile?.displayName ?? 'User',
        ),
      ),
    );
  }

  // Add the same gradient method from your profile screen
  Widget _buildGradientBackground(double headerHeight, bool isDark) {
    final theme = Theme.of(context);
    
    return Container(
      height: headerHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: const Alignment(-0.5, -0.5),
          end: const Alignment(1.5, 1.5),
          colors: [
            theme.colorScheme.primary.withOpacity(isDark ? 0.8 : 0.7),
            theme.colorScheme.secondary.withOpacity(isDark ? 0.7 : 0.6),
            theme.colorScheme.tertiary.withOpacity(isDark ? 0.6 : 0.5),
          ],
          stops: const [0.2, 0.5, 0.8],
        ),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.05 : 0.02),
                (isDark ? Colors.white : Colors.black).withOpacity(0.0),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 

// Delegate for the tab bar header - copied from your profile screen
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate(this._tabBar);

  final TabBar _tabBar;

  @override
  double get minExtent => _tabBar.preferredSize.height;
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
} 