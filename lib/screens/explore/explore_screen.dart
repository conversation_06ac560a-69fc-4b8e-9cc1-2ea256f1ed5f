import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/youtube_provider.dart';
import '../../providers/weekly_challenges_provider.dart';
import '../../providers/user_provider.dart';
import '../../widgets/navigation/bottom_navigation_bar.dart';
import '../../widgets/music/now_playing_bar.dart';
import '../../widgets/music/youtube_now_playing_bar.dart.dart';
import '../../utils/navigation_helper.dart';
import '../../utils/app_tab.dart';
import '../../config/themes.dart';
import 'components/bop_of_week_section.dart';
import 'components/drop_challenge_section.dart';
import 'components/new_drops_section.dart';
import 'components/hot_locations_section.dart';
import 'components/top_curators_section.dart';
import 'components/leaderboards_section.dart';
import '../../screens/search/ai_search/ai_search_view.dart';
import '../../services/ai/global_ai_provider_service.dart';

class ExploreScreen extends StatefulWidget {
  final bool showBottomNav;
  
  const ExploreScreen({
    Key? key,
    this.showBottomNav = true,
  }) : super(key: key);

  @override
  State<ExploreScreen> createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  double _scrollOffset = 0;
  static final _bucket = PageStorageBucket();

  // Track which sections are collapsed
  final Map<String, bool> _collapsedSections = {
    'bop': false,
    'leaderboards': false,
    'challenge': false,
    'drops': false,
    'locations': false,
    'curators': false,
  };

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _scrollController.addListener(_onScroll);

    // Initialize user profile data early to ensure it's available for AI search
    _initializeUserProfile();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      // Restore saved state
      final savedState = _bucket.readState(context, identifier: 'explore_state') as Map<String, dynamic>?;
      if (savedState != null) {
        final tabIndex = savedState['tab_index'] as int? ?? 0;
        final scrollPosition = savedState['scroll_position'] as double? ?? 0.0;
        if (_tabController.index != tabIndex) _tabController.index = tabIndex;
        if (_scrollController.hasClients) _scrollController.jumpTo(scrollPosition);
      }
      // Add listeners after restoring
      _tabController.addListener(_handleTabChange);
      _scrollController.addListener(_handleScroll);
    });
  }

  /// Initialize user profile data to ensure it's available for AI search and other features
  void _initializeUserProfile() {
    if (!mounted) return;

    // Initialize user profile in background to ensure it's available for AI search
    Future.microtask(() async {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);

        // Check if user profile is already loaded
        if (userProvider.currentUser == null) {
          print('👤 ExploreScreen: User profile not loaded, initializing...');
          await userProvider.initialize();
          print('✅ ExploreScreen: User profile initialized successfully');
        } else {
          print('✅ ExploreScreen: User profile already loaded');
        }
      } catch (e) {
        print('❌ ExploreScreen: Error initializing user profile: $e');
        // Don't block explore screen initialization if user profile fails
      }
    });
  }

  void _handleTabChange() {
    if (!mounted || !_tabController.indexIsChanging) return;
    _saveCurrentState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      setState(() {});
    });
  }

  void _handleScroll() {
    if (!mounted) return;
    setState(() {
      _scrollOffset = _scrollController.offset;
    });
    _saveCurrentState();
  }

  void _saveCurrentState() {
    if (!mounted) return;
    final state = {
      'tab_index': _tabController.index,
      'scroll_position': _scrollController.hasClients ? _scrollController.position.pixels : 0.0,
    };
    _bucket.writeState(context, state, identifier: 'explore_state');
  }

  @override
  void dispose() {
    _saveCurrentState();
    _tabController.removeListener(_handleTabChange);
    _scrollController.removeListener(_handleScroll);
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    setState(() {
      _scrollOffset = _scrollController.offset;
    });
  }
  
  void _handleNavigation(int index) {
    if (index == 1) return; // Already on explore
    
    if (index == 2) { // Map tab
      // Use NavigationHelper to return to map
      NavigationHelper.navigateToScreen(context, '/map');
      return;
    }
    
    // Use navigation helper for other tabs
    NavigationHelper.navigateToTab(context, index);
  }

  void _toggleSection(String section) {
    setState(() {
      _collapsedSections[section] = !(_collapsedSections[section] ?? false);
    });
  }

  Future<void> _handleRefresh() async {
    HapticFeedback.mediumImpact();
    
    // Refresh weekly challenges using the provider directly
    await context.read<WeeklyChallengesProvider>().loadChallenges();
    
    // Refresh leaderboards using the public method
    await LeaderboardsSection.refresh();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final youtubeProvider = Provider.of<YouTubeProvider>(context);
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;
    final hasActiveMusic = (spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null) ||
                          (youtubeProvider.hasActivePlayback && youtubeProvider.currentTrack != null);
    
    final theme = Theme.of(context);
    
    final bottomPadding = hasActiveMusic ? 68.0 + kBottomNavigationBarHeight : kBottomNavigationBarHeight;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: _handleRefresh,
        child: CustomScrollView(
          controller: _scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            // Minimal app bar with search
            SliverAppBar(
              floating: true,
              snap: true,
              centerTitle: false,
              automaticallyImplyLeading: false,
              backgroundColor: theme.scaffoldBackgroundColor,
              elevation: 0,
              systemOverlayStyle: SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness: theme.brightness == Brightness.dark ? Brightness.light : Brightness.dark,
              ),
              title: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.explore_rounded,
                      color: theme.colorScheme.primary,
                      size: 22,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Explore',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                      color: theme.colorScheme.onSurface,
                      letterSpacing: -0.5,
                    ),
                  ),
                ],
              ),
              actions: [
                // Enhanced search button
                Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: IconButton(
                    icon: Icon(
                      Icons.search_rounded,
                      color: theme.colorScheme.onBackground,
                      size: 26,
                    ),
                    onPressed: () async {
                      HapticFeedback.mediumImpact();
                      
                      // Use global AI provider instead of creating a new instance
                      final globalAIProvider = GlobalAIProviderService.instance;
                      await globalAIProvider.initializeIfAuthenticated(context);
                      
                      if (!mounted) return;
                      
                      Navigator.push(
                        context,
                        PageRouteBuilder(
                          pageBuilder: (context, animation, secondaryAnimation) => 
                              const AISearchView(),
                          transitionsBuilder: (context, animation, secondaryAnimation, child) {
                            return SlideTransition(
                              position: Tween<Offset>(
                                begin: const Offset(0.0, 1.0),
                                end: Offset.zero,
                              ).animate(CurvedAnimation(
                                parent: animation,
                                curve: Curves.easeInOut,
                              )),
                              child: child,
                            );
                          },
                          transitionDuration: const Duration(milliseconds: 300),
                        ),
                      );
                    },
                    tooltip: 'Search',
                  ),
                ),
              ],
            ),

            // Main content
            SliverPadding(
              padding: EdgeInsets.fromLTRB(12, 0, 12, bottomPadding),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  // Bops of the Week - Temporarily commented out
                  /*
                  _buildCollapsibleSection(
                    'bop',
                    'Bops of the Week',
                    Icons.trending_up_rounded,
                    theme.colorScheme.primary,
                    const BopOfWeekSection(),
                  ),
                  
                  // Add proper spacing between sections
                  const SizedBox(height: 16),
                  */
                  
                  // Drop Challenge - Collapsible (original name restored)
                  _buildCollapsibleSection(
                    'challenge',
                    'Weekly Challenge',
                    Icons.emoji_events_rounded,
                    theme.colorScheme.secondary,
                    const DropChallengeSection(),
                  ),
                  
                  // Add proper spacing between sections
                  const SizedBox(height: 16),
                  
                  // Leaderboards - Collapsible (placed after Weekly Challenge)
                  _buildCollapsibleSection(
                    'leaderboards',
                    'Leaderboards',
                    Icons.leaderboard_rounded,
                    const Color(0xFF8E44AD),
                    LeaderboardsSection(key: LeaderboardsSection.leaderboardKey),
                  ),
                  
                  // Add bottom padding for scrolling comfort
                  const SizedBox(height: 24),
                  
                  // Commented out sections - to be revisited later
                  /*
                  const SizedBox(height: 16),
                  
                  // New Drops - Collapsible
                  _buildCollapsibleSection(
                    'drops',
                    'New Near You',
                    Icons.near_me_rounded,
                    Colors.green,
                    const NewDropsSection(),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Hot Locations - Collapsible
                  _buildCollapsibleSection(
                    'locations',
                    'Hot Spots',
                    Icons.local_fire_department_rounded,
                    Colors.orange,
                    const HotLocationsSection(),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Top Curators - Collapsible
                  _buildCollapsibleSection(
                    'curators',
                    'Top Curators',
                    Icons.stars_rounded,
                    Colors.purple,
                    const TopCuratorsSection(),
                  ),
                  */
                ]),
              ),
            ),
          ],
        ),
      ),
      
      // Now Playing Bar
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null)
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: const NowPlayingBar(),
            ),
          // YouTube removed - it has its own embedded player
          if (widget.showBottomNav)
            MusicPinBottomNavBar.auto(
              context: context,
              onTabSelected: _handleNavigation,
            ),
        ],
      ),
    );
  }

  Widget _buildCollapsibleSection(
    String key,
    String title,
    IconData icon,
    Color color,
    Widget content,
  ) {
    final theme = Theme.of(context);
    final isCollapsed = _collapsedSections[key] ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with collapse button
        InkWell(
          onTap: () => _toggleSection(key),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onBackground,
                    ),
                  ),
                ),
                AnimatedRotation(
                  turns: isCollapsed ? 0.5 : 0,
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    Icons.keyboard_arrow_up,
                    color: theme.colorScheme.onBackground.withOpacity(0.5),
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // Animated content
        AnimatedCrossFade(
          firstChild: content,
          secondChild: const SizedBox.shrink(),
          crossFadeState: isCollapsed ? CrossFadeState.showSecond : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 300),
        ),
      ],
    );
  }

  Widget _buildTab(IconData icon, String text, double iconSize) {
    return Tab(
      height: 32,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: iconSize),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                text,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderTab(String title) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.construction_rounded,
            size: 48,
            color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Coming Soon!',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }
} 