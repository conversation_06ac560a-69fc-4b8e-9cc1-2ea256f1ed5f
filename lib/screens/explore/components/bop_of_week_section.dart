import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../providers/spotify_provider.dart';
import '../../../models/music_track.dart';
import '../../../widgets/music/track_artwork.dart';

// Mock dropper data - TODO: Replace with actual model
class Dropper {
  final String id;
  final String name;
  final String? avatarUrl;
  final String location;
  final DateTime droppedAt;

  const Dropper({
    required this.id,
    required this.name,
    this.avatarUrl,
    required this.location,
    required this.droppedAt,
  });
}

class BopOfWeekSection extends StatefulWidget {
  const BopOfWeekSection({Key? key}) : super(key: key);

  @override
  State<BopOfWeekSection> createState() => _BopOfWeekSectionState();
}

class _BopOfWeekSectionState extends State<BopOfWeekSection> {
  final PageController _pageController = PageController(viewportFraction: 0.93);
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;
    final spotifyProvider = Provider.of<SpotifyProvider>(context);

    // TODO: Replace with actual data from provider
    final topTracks = [
      (
        track: MusicTrack(
          id: '1',
          uri: 'spotify:track:**********',
          title: 'Greedy',
          artist: 'Tate McRae',
          albumArt: 'https://is1-ssl.mzstatic.com/image/thumb/Music116/v4/5c/bf/c2/5cbfc2c2-5f6c-e531-0f03-05e5b00f20f7/196871668498.jpg/800x800bb.jpg',
          url: 'https://open.spotify.com/track/**********',
          service: 'spotify',
          serviceType: 'spotify',
          durationMs: 180000,
        ),
        dropper: Dropper(
          id: 'user1',
          name: 'Sarah K.',
          avatarUrl: 'https://i.pravatar.cc/150?img=1',
          location: 'Central Park',
          droppedAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
      ),
      (
        track: MusicTrack(
          id: '2',
          uri: 'spotify:track:**********',
          title: 'vampire',
          artist: 'Olivia Rodrigo',
          albumArt: 'https://is1-ssl.mzstatic.com/image/thumb/Music126/v4/9b/d8/9c/9bd89c9e-b44d-ad25-1516-b9b30f64fd2a/23UMGIM71510.rgb.jpg/800x800bb.jpg',
          url: 'https://open.spotify.com/track/**********',
          service: 'spotify',
          serviceType: 'spotify',
          durationMs: 210000,
        ),
        dropper: Dropper(
          id: 'user2',
          name: 'Mike R.',
          avatarUrl: 'https://i.pravatar.cc/150?img=2',
          location: 'Times Square',
          droppedAt: DateTime.now().subtract(const Duration(hours: 4)),
        ),
      ),
      (
        track: MusicTrack(
          id: '3',
          uri: 'spotify:track:5678901234',
          title: 'Cruel Summer',
          artist: 'Taylor Swift',
          albumArt: 'https://is1-ssl.mzstatic.com/image/thumb/Music125/v4/49/3d/ab/493dab54-f920-9043-6181-80993b8116c9/19UMGIM53909.rgb.jpg/800x800bb.jpg',
          url: 'https://open.spotify.com/track/5678901234',
          service: 'spotify',
          serviceType: 'spotify',
          durationMs: 178000,
        ),
        dropper: Dropper(
          id: 'user3',
          name: 'Alex J.',
          avatarUrl: 'https://i.pravatar.cc/150?img=3',
          location: 'Brooklyn Bridge',
          droppedAt: DateTime.now().subtract(const Duration(hours: 6)),
        ),
      ),
    ];

    return Column(
      children: [
        // Carousel of top tracks
        SizedBox(
          height: isSmallScreen ? 100 : 110,
          child: PageView.builder(
            controller: _pageController,
            itemCount: topTracks.length,
            onPageChanged: (index) => setState(() => _currentPage = index),
            itemBuilder: (context, index) {
              final trackData = topTracks[index];
              return _buildTrackCard(
                context,
                trackData.track,
                trackData.dropper,
                spotifyProvider,
                isSmallScreen,
                index == _currentPage,
              );
            },
          ),
        ),

        // Page indicator
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            topTracks.length,
            (index) => Container(
              width: 4,
              height: 4,
              margin: const EdgeInsets.symmetric(horizontal: 3),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == _currentPage
                    ? theme.colorScheme.primary
                    : theme.colorScheme.primary.withOpacity(0.2),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTrackCard(
    BuildContext context,
    MusicTrack track,
    Dropper dropper,
    SpotifyProvider spotifyProvider,
    bool isSmallScreen,
    bool isCurrentPage,
  ) {
    final theme = Theme.of(context);
    final artworkSize = isSmallScreen ? 65.0 : 75.0;

    return AnimatedScale(
      scale: isCurrentPage ? 1.0 : 0.95,
      duration: const Duration(milliseconds: 300),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 3),
        decoration: BoxDecoration(
          color: theme.brightness == Brightness.dark
              ? theme.colorScheme.surface.withOpacity(0.8)
              : theme.colorScheme.background,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              HapticFeedback.mediumImpact();
              spotifyProvider.playTrack(track);
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                children: [
                  // Main track info row
                  Expanded(
                    child: Row(
                      children: [
                        // Track Artwork
                        Hero(
                          tag: 'bop_artwork_${track.id}',
                          child: TrackArtwork(
                            artworkUrl: track.albumArt ?? '',
                            size: artworkSize,
                            borderRadius: 8,
                            showPlayButton: true,
                            onTap: () {
                              HapticFeedback.mediumImpact();
                              spotifyProvider.playTrack(track);
                            },
                          ),
                        ),
                        const SizedBox(width: 8),

                        // Track Info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Track title
                              Text(
                                track.title,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  height: 1.1,
                                  fontSize: isSmallScreen ? 13 : 14,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),

                              // Artist name
                              Text(
                                track.artist,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.textTheme.bodySmall?.color,
                                  height: 1.1,
                                  fontSize: isSmallScreen ? 11 : 12,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),

                              // Stats row
                              Row(
                                children: [
                                  _buildStat(
                                    context,
                                    Icons.favorite,
                                    '2.5K',
                                    Colors.red.shade400,
                                  ),
                                  const SizedBox(width: 12),
                                  _buildStat(
                                    context,
                                    Icons.place,
                                    '156',
                                    Colors.green,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // Quick actions
                        SizedBox(
                          width: 32,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                height: 28,
                                child: IconButton(
                                  icon: Icon(
                                    Icons.favorite_border,
                                    color: theme.colorScheme.primary,
                                    size: 16,
                                  ),
                                  onPressed: () {
                                    HapticFeedback.mediumImpact();
                                    // TODO: Implement like
                                  },
                                  padding: EdgeInsets.zero,
                                  visualDensity: VisualDensity.compact,
                                  constraints: const BoxConstraints(
                                    minWidth: 24,
                                    maxWidth: 24,
                                    minHeight: 24,
                                    maxHeight: 24,
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 28,
                                child: IconButton(
                                  icon: Icon(
                                    Icons.share,
                                    color: theme.colorScheme.primary,
                                    size: 16,
                                  ),
                                  onPressed: () {
                                    HapticFeedback.mediumImpact();
                                    // TODO: Implement share
                                  },
                                  padding: EdgeInsets.zero,
                                  visualDensity: VisualDensity.compact,
                                  constraints: const BoxConstraints(
                                    minWidth: 24,
                                    maxWidth: 24,
                                    minHeight: 24,
                                    maxHeight: 24,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Dropper info row
                  const SizedBox(height: 4),
                  InkWell(
                    onTap: () {
                      // TODO: Navigate to dropper's profile
                    },
                    child: Row(
                      children: [
                        // Dropper avatar
                        Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            image: DecorationImage(
                              image: NetworkImage(dropper.avatarUrl ?? 'https://i.pravatar.cc/150'),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        // Dropper name and location
                        Expanded(
                          child: Row(
                            children: [
                              Text(
                                'Dropped by ',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                                  fontSize: 9,
                                ),
                              ),
                              Text(
                                dropper.name,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.primary,
                                  fontSize: 9,
                                ),
                              ),
                              Text(
                                ' at ',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                                  fontSize: 9,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  dropper.location,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 9,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStat(BuildContext context, IconData icon, String value, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 12,
          color: color,
        ),
        const SizedBox(width: 3),
        Text(
          value,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 9,
          ),
        ),
      ],
    );
  }
} 
