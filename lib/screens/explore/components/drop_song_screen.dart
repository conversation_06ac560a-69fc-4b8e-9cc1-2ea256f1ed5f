import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'dart:async';  // Add this import for TimeoutException
import '../../../widgets/music/track_selector.dart';
import '../../../models/music_track.dart';
import '../../../widgets/animations/fade_in_animation.dart';
import 'package:provider/provider.dart';
import '../../../providers/settings_provider.dart';
import '../../../screens/challenges/challenge_playlist_screen.dart';
import '../../../providers/challenge_playlist_provider.dart';
import '../../../providers/weekly_challenges_provider.dart';
import '../../../services/location/location_service.dart';
import '../../../services/api/weekly_challenges_api_service.dart';
import '../../../services/api_service.dart';
import '../../../services/auth_service.dart';

class DropSongScreen extends StatefulWidget {
  final String challengeTitle;
  final String challengeId;
  final List<Color> gradientColors;
  final String description;
  
  const DropSongScreen({
    Key? key,
    required this.challengeTitle,
    required this.challengeId,
    required this.gradientColors,
    required this.description,
  }) : super(key: key);

  @override
  State<DropSongScreen> createState() => _DropSongScreenState();
}

class _DropSongScreenState extends State<DropSongScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _gradientAnimation;
  final LocationService _locationService = LocationService();
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );

    _gradientAnimation = Tween<double>(
      begin: -0.5,
      end: 1.5,
    ).animate(_animationController);
    
    // Start animation
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleTrackSelected(MusicTrack track) async {
    if (_isSubmitting) return;

    try {
      setState(() => _isSubmitting = true);
      HapticFeedback.mediumImpact();

      // Prepare song data first
      final songData = {
        'spotify_id': track.id,
        'title': track.title,
        'artist': track.artist,
        'album': track.album,
        'duration_ms': track.durationMs,
        'album_art': track.albumArtUrl,
        'preview_url': track.previewUrl,
      };

      try {
        // Try to get location only when submitting
        double? latitude;
        double? longitude;
        try {
          final position = await _locationService.getCurrentPosition().timeout(
            const Duration(seconds: 3),
            onTimeout: () => throw TimeoutException('Location timeout'),
          );
          latitude = position.latitude;
          longitude = position.longitude;
        } catch (e) {
          debugPrint('Location not available: $e');
          // Continue without location
        }

        // Submit the song
        final success = await context.read<WeeklyChallengesProvider>().participateInChallenge(
          challengeId: widget.challengeId,
          songData: songData,
          latitude: latitude,
          longitude: longitude,
        );

        if (!mounted) return;

        if (success) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Song submitted successfully!'),
              behavior: SnackBarBehavior.floating,
            ),
          );

          // Get the challenge details from provider
          final challenge = context.read<WeeklyChallengesProvider>()
              .challenges.firstWhere((c) => c.id == widget.challengeId);

          // Get services
          final apiService = context.read<ApiService>();
          final authService = context.read<AuthService>();

          // Navigate to challenge playlist screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => ChangeNotifierProvider(
                create: (_) => ChallengePlaylistProvider(
                  WeeklyChallengesApiService(apiService, authService),
                ),
                child: ChallengePlaylistScreen(
                  challengeId: widget.challengeId,
                  title: widget.challengeTitle,
                  description: widget.description,
                  timeLeft: challenge.timeLeft,
                  requiredParticipants: 0,
                  gradientColors: widget.gradientColors,
                ),
              ),
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;

        // Check if the error is due to already participating
        if (e.toString().contains('already participated')) {
          // Remove this snackbar - it was for testing
          // ScaffoldMessenger.of(context).showSnackBar(
          //   const SnackBar(
          //     content: Text('You\'ve already submitted a song to this challenge!'),
          //     behavior: SnackBarBehavior.floating,
          //     duration: Duration(seconds: 3),
          //   ),
          // );

          // Get the challenge details from provider
          final challenge = context.read<WeeklyChallengesProvider>()
              .challenges.firstWhere((c) => c.id == widget.challengeId);

          // Get services
          final apiService = context.read<ApiService>();
          final authService = context.read<AuthService>();

          // Navigate to challenge playlist screen to view entries
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => ChangeNotifierProvider(
                create: (_) => ChallengePlaylistProvider(
                  WeeklyChallengesApiService(apiService, authService),
                ),
                child: ChallengePlaylistScreen(
                  challengeId: widget.challengeId,
                  title: widget.challengeTitle,
                  description: widget.description,
                  timeLeft: challenge.timeLeft,
                  requiredParticipants: 0,
                  gradientColors: widget.gradientColors,
                ),
              ),
            ),
          );
        } else {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.read<WeeklyChallengesProvider>().error ?? 'Failed to submit song'),
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 4),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
          
          // Log the error for debugging
          debugPrint('Error submitting song: $e');
        }
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            expandedHeight: size.height * 0.28,  // Set reasonable initial height
            floating: true,
            pinned: true,
            stretch: true,
            backgroundColor: Colors.transparent,
            automaticallyImplyLeading: false,
            leading: Container(
              margin: const EdgeInsets.only(left: 8),
              child: IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDark ? Colors.black38 : Colors.white38,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  child: const Icon(Icons.arrow_back_ios_new, size: 16, color: Colors.white),
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: AnimatedBuilder(
                animation: _gradientAnimation,
                builder: (context, child) {
                  return Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment(
                          _gradientAnimation.value * 0.8,
                          -0.5,
                        ),
                        end: Alignment(
                          1 - _gradientAnimation.value * 0.8,
                          1.5,
                        ),
                        colors: widget.gradientColors,
                        stops: const [0.0, 1.0],
                      ),
                    ),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
                      child: SafeArea(
                        child: Align(
                          alignment: Alignment.center,
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxWidth: size.width * 0.85,
                              minHeight: 100,
                            ),
                            child: Container(
                              margin: const EdgeInsets.only(top: 48),
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.15),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    widget.challengeTitle,
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 0.3,
                                      height: 1.3,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    widget.description,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.9),
                                      fontSize: 15,
                                      fontWeight: FontWeight.w400,
                                      letterSpacing: 0.2,
                                      height: 1.4,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
        body: FadeInAnimation(
          delay: const Duration(milliseconds: 200),
          child: TrackSelector(
            showRecentlyPlayed: true,
            showTopTracks: true,
            showLikedSongs: true,
            showPlaylists: true,
            showSearchBar: true,
            onTrackSelected: _handleTrackSelected,
          ),
        ),
      ),
    );
  }
} 