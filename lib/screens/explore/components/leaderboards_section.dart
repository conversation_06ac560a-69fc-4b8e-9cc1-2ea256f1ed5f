import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:shimmer/shimmer.dart';
import 'package:geocoding/geocoding.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../services/location/location_manager.dart';
import '../../../services/ranking_service.dart';
import '../../../services/api_service.dart';
import '../../../services/auth_service.dart';
import '../../../services/verification/school_verification_service.dart';
import '../../../widgets/profile/components/leaderboard_card.dart';
import '../../../widgets/profile/components/leaderboard_filter_chip.dart';
import '../../../widgets/profile/components/leaderboard_podium.dart';
import '../../../widgets/bottomsheets/school_verification_bottomsheet.dart';
import '../../../screens/profile/user_profile_screen.dart';

class LeaderboardsSection extends StatefulWidget {
  const LeaderboardsSection({Key? key}) : super(key: key);

  static final GlobalKey<_LeaderboardsSectionState> leaderboardKey = GlobalKey<_LeaderboardsSectionState>();

  @override
  State<LeaderboardsSection> createState() => _LeaderboardsSectionState();

  // Public method to refresh leaderboards
  static Future<void> refresh() async {
    final state = leaderboardKey.currentState;
    if (state != null) {
      await state._refreshLeaderboard();
    }
  }
}

class _LeaderboardsSectionState extends State<LeaderboardsSection> with AutomaticKeepAliveClientMixin {
  final LocationManager _locationManager = LocationManager();
  late final RankingService _rankingService;
  final SchoolVerificationService _schoolVerificationService = SchoolVerificationService();
  String? _currentLocation;
  double? _currentLatitude;
  double? _currentLongitude;
  String _currentFilter = 'Local';
  bool _isLoadingLocation = false;
  bool _isLoadingLeaderboard = true;
  bool _isInitialized = false;
  bool _isUserVerified = false; // Track user verification status
  String? _userSchoolName; // Store user's school name
  
  // Infinite scrolling state
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  int _currentPage = 1;
  final int _pageSize = 20;
  
  // Advanced cache for leaderboard data with timestamps
  final Map<String, Map<String, dynamic>> _cachedLeaderboardData = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Duration _cacheExpiration = const Duration(minutes: 3); // Reduced from 5 to 3 minutes for fresher data
  final int _maxCacheEntries = 10; // Limit cache size for memory optimization
  
  List<Map<String, dynamic>> _leaderboardData = [];
  
  // Track last successful API call to prevent rapid successive calls
  DateTime? _lastApiCall;
  String? _lastApiFilter; // Track which filter was called last
  final Duration _minApiInterval = const Duration(milliseconds: 500);
  
  // Cache keys for different filter types
  String _getCacheKey(String filter) {
    String key;
    if (filter == 'Local' && _currentLatitude != null && _currentLongitude != null) {
      // Include coordinates in cache key for location-based data
      final lat = _currentLatitude!.toStringAsFixed(2);
      final lng = _currentLongitude!.toStringAsFixed(2);
      key = '${filter}_${lat}_$lng';
    } else {
      key = filter;
    }
    print('🔑 Generated cache key for $filter: $key');
    return key;
  }
  
  // Check if cached data is still valid
  bool _isCacheValid(String cacheKey) {
    if (!_cachedLeaderboardData.containsKey(cacheKey) || !_cacheTimestamps.containsKey(cacheKey)) {
      print('❌ No cache entry for key: $cacheKey');
      return false;
    }
    
    final cacheTime = _cacheTimestamps[cacheKey]!;
    final now = DateTime.now();
    final age = now.difference(cacheTime);
    final isValid = age < _cacheExpiration;
    
    print('⏰ Cache for $cacheKey: ${age.inMinutes}m ${age.inSeconds % 60}s old, valid: $isValid');
    return isValid;
  }
  
  // Get cached data if valid
  List<Map<String, dynamic>>? _getCachedData(String cacheKey) {
    if (_isCacheValid(cacheKey)) {
      final cached = _cachedLeaderboardData[cacheKey]!;
      final data = List<Map<String, dynamic>>.from(cached['leaderboard']);
      print('📦 Retrieved ${data.length} users from cache for $cacheKey');
      return data;
    }
    print('🚫 No valid cached data for $cacheKey');
    return null;
  }
  
  // Store data in cache
  void _setCachedData(String cacheKey, Map<String, dynamic> apiResponse) {
    // Implement LRU cache management
    if (_cachedLeaderboardData.length >= _maxCacheEntries) {
      _evictOldestCacheEntry();
    }
    
    _cachedLeaderboardData[cacheKey] = Map<String, dynamic>.from(apiResponse);
    _cacheTimestamps[cacheKey] = DateTime.now();
    final dataCount = (apiResponse['leaderboard'] as List).length;
    print('💾 Stored $dataCount users in cache for $cacheKey');
    _debugCacheStatus();
  }
  
  // Evict oldest cache entry when cache is full
  void _evictOldestCacheEntry() {
    if (_cacheTimestamps.isEmpty) return;
    
    String? oldestKey;
    DateTime? oldestTime;
    
    for (final entry in _cacheTimestamps.entries) {
      if (oldestTime == null || entry.value.isBefore(oldestTime)) {
        oldestTime = entry.value;
        oldestKey = entry.key;
      }
    }
    
    if (oldestKey != null) {
      _cachedLeaderboardData.remove(oldestKey);
      _cacheTimestamps.remove(oldestKey);
      print('🗑️ Evicted oldest cache entry: $oldestKey');
    }
  }
  
  // Debug method to show all cache entries
  void _debugCacheStatus() {
    print('📋 === CACHE STATUS ===');
    for (final key in _cachedLeaderboardData.keys) {
      final timestamp = _cacheTimestamps[key];
      final data = _cachedLeaderboardData[key];
      final userCount = (data?['leaderboard'] as List?)?.length ?? 0;
      final age = timestamp != null ? DateTime.now().difference(timestamp) : null;
      print('  📦 $key: $userCount users, age: ${age?.inMinutes ?? 'unknown'}m');
    }
    print('📋 ================');
  }
  
  // Clear expired cache entries
  void _clearExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) >= _cacheExpiration) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _cachedLeaderboardData.remove(key);
      _cacheTimestamps.remove(key);
    }
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _rankingService = RankingService(ApiService(), AuthService(ApiService()));
    
    // Add scroll listener for infinite scrolling
    _scrollController.addListener(_onScroll);
    
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      setState(() => _isInitialized = true);
      
      // Check verification status first
      await _checkVerificationStatus();
      
      // Load initial data based on filter
      if (_currentFilter == 'Local') {
        await _initializeLocation();
      } else if (_currentFilter == 'School') {
        // If user is verified, load school data directly
        if (_isUserVerified) {
          await _loadLeaderboardData(filter: 'School');
        }
      } else {
        await _loadLeaderboardData();
      }
      
      // Preload other filters after initial load
      _preloadOtherFilters();
    });
  }

  /// Handle scroll events for infinite scrolling
  void _onScroll() {
    if (_scrollController.hasClients) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;
      final threshold = maxScroll * 0.8; // Load more when 80% scrolled
      
      if (currentScroll >= threshold && 
          !_isLoadingMore && 
          _hasMoreData && 
          !_isLoadingLeaderboard) {
        _loadMoreData();
      }
    }
  }

  /// Load more data for infinite scrolling
  Future<void> _loadMoreData() async {
    if (_isLoadingMore || !_hasMoreData) return;
    
    setState(() => _isLoadingMore = true);
    
    try {
      final nextPage = _currentPage + 1;
      print('📄 Loading page $nextPage for $_currentFilter...');
      
      Map<String, dynamic> apiResponse;
      
      if (_currentFilter == 'Local') {
        if (_currentLatitude != null && _currentLongitude != null) {
          apiResponse = await _rankingService.getLeaderboard(
            scope: 'local',
            latitude: _currentLatitude!,
            longitude: _currentLongitude!,
            radiusKm: 5.0,
            limit: _pageSize,
            page: nextPage,
          );
        } else {
          apiResponse = {'leaderboard': [], 'has_more': false};
        }
      } else if (_currentFilter == 'Friends') {
        apiResponse = await _rankingService.getLeaderboard(
          scope: 'friends',
          limit: _pageSize,
          page: nextPage,
        );
      } else if (_currentFilter == 'School') {
        if (_isUserVerified) {
          apiResponse = await _rankingService.getLeaderboard(
            scope: 'school',
            limit: _pageSize,
            page: nextPage,
          );
        } else {
          apiResponse = {'leaderboard': [], 'has_more': false};
        }
      } else {
        apiResponse = await _rankingService.getLeaderboard(
          scope: 'all',
          limit: _pageSize,
          page: nextPage,
        );
      }
      
      final newData = List<Map<String, dynamic>>.from(apiResponse['leaderboard']);
      final hasMore = apiResponse['has_more'] ?? false;
      
      setState(() {
        _leaderboardData.addAll(newData);
        _currentPage = nextPage;
        _hasMoreData = hasMore;
        _isLoadingMore = false;
      });
      
      // Update cache with new combined data
      final cacheKey = _getCacheKey(_currentFilter);
      final updatedCache = {
        'leaderboard': _leaderboardData,
        'location': apiResponse['location'],
        'scope': apiResponse['scope'] ?? _currentFilter,
        'school_info': apiResponse['school_info'],
        'has_more': hasMore,
        'current_page': nextPage,
      };
      _setCachedData(cacheKey, updatedCache);
      
      print('✅ Loaded ${newData.length} more users. Total: ${_leaderboardData.length}');
      
    } catch (e) {
      print('❌ Error loading more data: $e');
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  /// Check user's school verification status
  Future<void> _checkVerificationStatus() async {
    try {
      // Use AuthService convenience methods for quick access
      final authService = AuthService(_rankingService.apiService);
      final schoolInfo = await authService.getQuickSchoolInfo();
      
      setState(() {
        _isUserVerified = schoolInfo['is_verified'];
        _userSchoolName = schoolInfo['school_name'];
      });
      
      if (kDebugMode) {
        print('🎓 User verification status: ${_isUserVerified ? "VERIFIED" : "NOT VERIFIED"}');
        if (_userSchoolName != null) {
          print('🏫 User school: $_userSchoolName');
        }
      }
      
      // If not verified in cache, check with verification service for fresh data
      if (!_isUserVerified) {
        final verificationStatus = await _schoolVerificationService.checkVerificationStatus();
        
        setState(() {
          _isUserVerified = verificationStatus['is_verified'] ?? false;
          _userSchoolName = verificationStatus['school_name'];
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking verification status: $e');
      }
      setState(() {
        _isUserVerified = false;
        _userSchoolName = null;
      });
    }
  }

  Future<void> _initializeData() async {
    final cacheKey = _getCacheKey(_currentFilter);
    
    // Try to get cached data first for immediate display
    final cachedData = _getCachedData(cacheKey);
    if (cachedData != null) {
      setState(() {
        _leaderboardData = cachedData;
        _isLoadingLeaderboard = false;
      });
      print('🎯 Using cached data for $_currentFilter');
    }

    // If we need location data and don't have it yet, get it
    if (_currentFilter == 'Local' && _currentLocation == null) {
      await _initializeLocation();
    } else {
      // If we don't have valid cached data, load it from API
      if (cachedData == null) {
        await _loadLeaderboardData(filter: _currentFilter);
      }
    }
    
    // 🚀 PERFORMANCE BOOST: Preload other filters in background
    _preloadOtherFilters();
  }
  
  // Preload data for other filters in background for instant switching
  void _preloadOtherFilters() {
    final filters = ['Local', 'Friends', 'School'];
    
    // Preload filters that aren't currently active and don't have fresh cache
    for (final filter in filters) {
      if (filter != _currentFilter) {
        final cacheKey = _getCacheKey(filter);
        if (!_isCacheValid(cacheKey)) {
          print('🔄 Preloading $filter data in background...');
          // Load in background without updating UI
          _loadLeaderboardData(filter: filter, updateUI: false);
        }
      }
    }
  }

  Future<void> _initializeLocation() async {
    // Don't initialize location if we're not on Local filter
    if (_currentFilter != 'Local') return;
    
    setState(() => _isLoadingLocation = true);
    try {
      final position = await _locationManager.requestCurrentLocation();
      if (position != null) {
        _currentLatitude = position.latitude;
        _currentLongitude = position.longitude;
        
        // Update user location in backend for ranking purposes
        await _rankingService.updateUserLocation(position.latitude, position.longitude);
        
        final placemarks = await placemarkFromCoordinates(
          position.latitude,
          position.longitude,
        );
        
        if (placemarks.isNotEmpty) {
          final city = placemarks.first.locality ?? placemarks.first.subAdministrativeArea;
          setState(() {
            _currentLocation = city;
            _isLoadingLocation = false;
          });
          
          // Only load data if we're still on Local filter
          if (_currentFilter == 'Local') {
            await _loadLeaderboardData(filter: 'Local');
          }
        }
      }
    } catch (e) {
      print('Location error: $e');
      setState(() {
        _isLoadingLocation = false;
        // Only change filter if we're currently on Local
        if (_currentFilter == 'Local') {
          _currentFilter = 'Friends';
          _loadLeaderboardData(filter: 'Friends');
        }
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Could not get your location. Showing friends rankings.'),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () {
                _initializeLocation();
              },
            ),
          ),
        );
      }
    }
  }

  Future<void> _loadLeaderboardData({
    String? filter, 
    bool forceRefresh = false, 
    bool updateUI = true
  }) async {
    final targetFilter = filter ?? _currentFilter;
    final cacheKey = _getCacheKey(targetFilter);
    
    // Reset pagination when changing filters or force refreshing
    if (filter != null && filter != _currentFilter || forceRefresh) {
      _currentPage = 1;
      _hasMoreData = true;
      if (updateUI) {
        setState(() {
          _leaderboardData.clear();
        });
      }
    }
    
    // Check cached data first (before any throttling)
    if (!forceRefresh) {
      final cachedData = _getCachedData(cacheKey);
      if (cachedData != null) {
        if (updateUI) {
          setState(() {
            _leaderboardData = List<Map<String, dynamic>>.from(cachedData);
            _isLoadingLeaderboard = false;
          });
          // Restore pagination state from cache if available
          final cachedResponse = _cachedLeaderboardData[cacheKey];
          if (cachedResponse != null) {
            _currentPage = cachedResponse['current_page'] ?? 1;
            _hasMoreData = cachedResponse['has_more'] ?? false;
          }
        }
        print('✅ Using cached data for $targetFilter (${cachedData.length} users)');
        return;
      }
    }
    
    // Check if we should throttle API calls (only for the same filter)
    if (_lastApiCall != null && !forceRefresh && _lastApiFilter == targetFilter) {
      final timeSinceLastCall = DateTime.now().difference(_lastApiCall!);
      if (timeSinceLastCall < _minApiInterval) {
        print('🚫 API call throttled for $targetFilter - waiting ${_minApiInterval.inSeconds}s between calls');
        // Set loading to false if we're throttled and have no data
        if (updateUI && _leaderboardData.isEmpty) {
          setState(() => _isLoadingLeaderboard = false);
        }
        return;
      }
    }
    
    if (updateUI) {
      setState(() => _isLoadingLeaderboard = true);
    }
    
    try {
      _lastApiCall = DateTime.now();
      _lastApiFilter = targetFilter; // Track which filter we called
      Map<String, dynamic> apiResponse;
      
      print('🌐 Making API call for $targetFilter leaderboard (page $_currentPage)...');
      
      if (targetFilter == 'Local') {
        // Call backend API for local rankings
        if (_currentLatitude != null && _currentLongitude != null) {
          apiResponse = await _rankingService.getLeaderboard(
            scope: 'local',
            latitude: _currentLatitude!,
            longitude: _currentLongitude!,
            radiusKm: 5.0, // 5km radius
            limit: _pageSize,
            page: _currentPage,
          );
        } else {
          // No location available, return empty data
          apiResponse = {
            'leaderboard': <Map<String, dynamic>>[],
            'location': null,
            'scope': 'local',
            'has_more': false,
          };
        }
      } else if (targetFilter == 'Friends') {
        // Call backend API for friends rankings
        apiResponse = await _rankingService.getLeaderboard(
          scope: 'friends',
          limit: _pageSize,
          page: _currentPage,
        );
      } else if (targetFilter == 'School') {
        // For school, check if user is verified first
        if (_isUserVerified) {
          print('🎓 Loading school rankings for verified user...');
          apiResponse = await _rankingService.getLeaderboard(
            scope: 'school',
            limit: _pageSize,
            page: _currentPage,
          );
        } else {
          // User not verified, return empty data to show verification prompt
        apiResponse = {
          'leaderboard': <Map<String, dynamic>>[],
          'location': null,
          'scope': 'school',
            'school_info': null,
            'has_more': false,
        };
        }
      } else {
        // Fallback to all/global rankings
        apiResponse = await _rankingService.getLeaderboard(
          scope: 'all',
          limit: _pageSize,
          page: _currentPage,
        );
      }
      
      final leaderboardData = List<Map<String, dynamic>>.from(apiResponse['leaderboard']);
      final hasMore = apiResponse['has_more'] ?? false;
      
      // Update current location if provided by API (for local rankings)
      if (apiResponse['location'] != null && targetFilter == 'Local' && updateUI) {
        setState(() {
          _currentLocation = apiResponse['location'];
        });
      }
      
      // Store in cache
      _setCachedData(cacheKey, apiResponse);
      
      if (updateUI) {
        setState(() {
          _leaderboardData = leaderboardData;
          _hasMoreData = hasMore;
          _isLoadingLeaderboard = false;
        });
      }
      
      print('✅ Successfully loaded ${leaderboardData.length} users for $targetFilter (page $_currentPage)');
      
    } catch (e) {
      print('❌ Error loading leaderboard: $e');
      if (updateUI) {
        setState(() => _isLoadingLeaderboard = false);
      }
      
      // Try to use stale cache data as fallback
      final staleCache = _cachedLeaderboardData[cacheKey];
      if (staleCache != null) {
        final staleData = List<Map<String, dynamic>>.from(staleCache['leaderboard']);
        if (updateUI) {
          setState(() {
            _leaderboardData = staleData;
            _hasMoreData = staleCache['has_more'] ?? false;
          });
        }
        print('🔄 Using stale cache data for $targetFilter');
        
        if (mounted && updateUI) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Using cached ${targetFilter.toLowerCase()} rankings - connection issues.'),
              behavior: SnackBarBehavior.floating,
              action: SnackBarAction(
                label: 'Retry',
                onPressed: () => _loadLeaderboardData(filter: targetFilter, forceRefresh: true),
              ),
            ),
          );
        }
      } else if (updateUI) {
        // Fallback to mock data on error to keep UI functional
        final fallbackData = _generateFallbackData(targetFilter);
        setState(() {
          _leaderboardData = fallbackData;
          _hasMoreData = false; // No more mock data
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Using demo data - ${targetFilter.toLowerCase()} rankings unavailable.'),
              behavior: SnackBarBehavior.floating,
              action: SnackBarAction(
                label: 'Retry',
                onPressed: () => _loadLeaderboardData(filter: targetFilter, forceRefresh: true),
              ),
            ),
          );
        }
      }
    }
  }

  /// Generate fallback mock data when API is unavailable
  List<Map<String, dynamic>> _generateFallbackData(String filter) {
    return List.generate(10, (index) => {
      'id': 'demo_${index + 1}',
      'name': filter == 'Friends' 
          ? 'Friend ${index + 1}' 
          : 'Demo User ${index + 1}',
      'username': filter == 'Friends'
          ? 'friend${index + 1}'
          : 'demo${index + 1}',
      'avatar': 'https://i.pravatar.cc/150?img=${index + 1}',
      'score': '${1000 - (index * 50)}',
      'change': index % 3 == 0 ? 'up' : (index % 3 == 1 ? 'down' : 'same'),
      'changeAmount': '${(index % 5 + 1) * 10}',
      'isCurrentUser': index == 5, // Simulate current user at rank 6
      'rank': index + 1,
    });
  }

  Future<void> _refreshLeaderboard() async {
    // Clear expired cache entries
    _clearExpiredCache();
    
    // Force reload by passing current filter with forceRefresh
    await _loadLeaderboardData(filter: _currentFilter, forceRefresh: true);
    print('🔄 Force refreshed $_currentFilter leaderboard');
  }

  /// Handle school action based on verification status
  void _handleSchoolAction() {
    if (_isUserVerified) {
      // User is already verified, refresh the school rankings
      _refreshLeaderboard();
    } else {
      // User not verified, show verification flow
      SchoolVerificationBottomSheet.show(
        context,
        onVerificationComplete: (result) async {
          if (result['success']) {
            // Save verification data to AuthService
            final authService = AuthService(_rankingService.apiService);
            await authService.saveSchoolVerification(
              isVerified: true,
              schoolName: result['school_name'],
              schoolDomain: result['school_domain'],
              schoolId: result['school_id'],
              verifiedAt: DateTime.now(),
            );
            
            // Update verification status and refresh leaderboard
      setState(() {
              _isUserVerified = true;
              _userSchoolName = result['school_name'];
            });
            _refreshLeaderboard();
          }
        },
      );
    }
  }

  /// Handle filter change with proper verification checking
  void _handleFilterChange(String newFilter) async {
    if (newFilter == _currentFilter) return;
    
    HapticFeedback.selectionClick();
    print('🔀 Filter changing from $_currentFilter to $newFilter');
    
        setState(() {
      _currentFilter = newFilter;
          _isLoadingLeaderboard = true;
        });
        
    // Special handling for School filter
    if (newFilter == 'School') {
      // Re-check verification status before loading school data
      await _checkVerificationStatus();
            }
    
    await _loadLeaderboardData(filter: newFilter);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Location display if available (or add padding when not available)
        if (_currentFilter == 'Local' && _currentLocation != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.location_on,
                    size: 12,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _currentLocation!,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          // Add smaller padding at the top when location is not shown
          const SizedBox(height: 8),

        // Filter chips with animation
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.only(left: 8, right: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                LeaderboardFilterChip(
                  label: 'Local',
                  icon: Icons.location_on_rounded,
                  isSelected: _currentFilter == 'Local',
                  onTap: () => _handleFilterChange('Local'),
                  isSmallScreen: isSmallScreen,
                ).animate(target: _currentFilter == 'Local' ? 1 : 0)
                  .scale(begin: const Offset(0.95, 0.95), end: const Offset(1.05, 1.05), duration: 300.ms)
                  .then(delay: 100.ms)
                  .scale(begin: const Offset(1.05, 1.05), end: const Offset(1.0, 1.0), duration: 200.ms),
                LeaderboardFilterChip(
                  label: 'Friends',
                  icon: Icons.people_rounded,
                  isSelected: _currentFilter == 'Friends',
                  onTap: () => _handleFilterChange('Friends'),
                  isSmallScreen: isSmallScreen,
                ).animate(target: _currentFilter == 'Friends' ? 1 : 0)
                  .scale(begin: const Offset(0.95, 0.95), end: const Offset(1.05, 1.05), duration: 300.ms)
                  .then(delay: 100.ms)
                  .scale(begin: const Offset(1.05, 1.05), end: const Offset(1.0, 1.0), duration: 200.ms),
                LeaderboardFilterChip(
                  label: 'School',
                  icon: Icons.school_rounded,
                  isSelected: _currentFilter == 'School',
                  onTap: () => _handleFilterChange('School'),
                  isSmallScreen: isSmallScreen,
                ).animate(target: _currentFilter == 'School' ? 1 : 0)
                  .scale(begin: const Offset(0.95, 0.95), end: const Offset(1.05, 1.05), duration: 300.ms)
                  .then(delay: 100.ms)
                  .scale(begin: const Offset(1.05, 1.05), end: const Offset(1.0, 1.0), duration: 200.ms),
              ],
            ),
          ),
        ),

        // Content based on state with animations
        if (_isLoadingLeaderboard)
          _buildLoadingState(isSmallScreen)
            .animate()
            .fadeIn(duration: 400.ms)
        else if (_leaderboardData.isEmpty)
          _buildEmptyState(isSmallScreen)
            .animate()
            .fadeIn(duration: 400.ms)
            .slideY(begin: 0.1, end: 0, duration: 500.ms, curve: Curves.easeOutQuad)
        else
          Column(
            children: [
              // School name display for School filter
              if (_currentFilter == 'School' && _isUserVerified && _userSchoolName != null)
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 16),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        theme.colorScheme.primary.withOpacity(0.1),
                        theme.colorScheme.secondary.withOpacity(0.05),
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: theme.colorScheme.primary.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.school_rounded,
                          color: theme.colorScheme.primary,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _userSchoolName!,
                              style: TextStyle(
                                fontSize: isSmallScreen ? 16 : 18,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                                letterSpacing: -0.3,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'School Rankings',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 12 : 13,
                                fontWeight: FontWeight.w500,
                                color: theme.colorScheme.onSurface.withOpacity(0.6),
                                letterSpacing: 0.2,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (_leaderboardData.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '${_leaderboardData.length} students',
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ),
                    ],
                  ),
                ).animate()
                  .fadeIn(duration: 600.ms)
                  .slideY(begin: -0.1, end: 0, duration: 500.ms, curve: Curves.easeOutQuad),
              
              // Leaderboard content
          _buildLeaderboardContent(isSmallScreen)
            .animate()
            .fadeIn(duration: 400.ms),
            ],
          ),
      ],
    );
  }

  Widget _buildLoadingState(bool isSmallScreen) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Shimmer for podium
        Shimmer.fromColors(
          baseColor: isDark ? Colors.grey[800]! : Colors.grey[300]!,
          highlightColor: isDark ? Colors.grey[700]! : Colors.grey[100]!,
          child: Container(
            height: isSmallScreen ? 140 : 160,
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[800] : Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
          ),
        ),
        const SizedBox(height: 12),
        // Shimmer for leaderboard cards
        ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          itemCount: 5,
          itemBuilder: (context, index) => Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Shimmer.fromColors(
              baseColor: isDark ? Colors.grey[800]! : Colors.grey[300]!,
              highlightColor: isDark ? Colors.grey[700]! : Colors.grey[100]!,
              child: Container(
                height: isSmallScreen ? 52 : 60,
                decoration: BoxDecoration(
                  color: isDark ? Colors.grey[800] : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(bool isSmallScreen) {
    final theme = Theme.of(context);
    String message = '';
    IconData icon = Icons.emoji_events_rounded;
    
    switch (_currentFilter) {
      case 'Local':
        message = 'No local rankings yet. Complete challenges to get ranked!';
        icon = Icons.location_off_rounded;
        break;
      case 'Friends':
        message = 'No friends in the rankings yet. Invite them to join!';
        icon = Icons.people_outline_rounded;
        break;
      case 'School':
        if (_isUserVerified) {
          if (_userSchoolName != null) {
            message = 'No rankings yet for $_userSchoolName. Complete challenges to get ranked!';
          } else {
            message = 'No school rankings available yet. Complete challenges to get ranked!';
          }
        } else {
        message = 'Connect your school account to see school rankings.';
        }
        icon = _isUserVerified ? Icons.emoji_events_outlined : Icons.school_outlined;
        break;
    }
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 30,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                HapticFeedback.mediumImpact();
                if (_currentFilter == 'Local') {
                  // Navigate to challenges
                } else if (_currentFilter == 'Friends') {
                  // Navigate to invite friends
                } else if (_currentFilter == 'School') {
                  // Handle school verification or refresh based on status
                  _handleSchoolAction();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                _currentFilter == 'Local'
                    ? 'View Challenges'
                    : _currentFilter == 'Friends'
                        ? 'Invite Friends'
                        : _isUserVerified 
                            ? 'Refresh Rankings'
                        : 'Connect School',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLeaderboardContent(bool isSmallScreen) {
    // Get top 3 for podium (or whatever users we have, up to 3)
    final top3 = _leaderboardData.take(3).toList();
    // Get ranks 4+ for vertical list (skip the top 3 if we have them)
    final otherRanks = _leaderboardData.length > 3 
        ? _leaderboardData.skip(3).toList()
        : <Map<String, dynamic>>[];
    
    // Find if current user is in the list
    final currentUserIndex = _leaderboardData.indexWhere((user) => user['isCurrentUser'] == true);
    final currentUserRank = currentUserIndex + 1;
    final showCurrentUserCard = currentUserIndex >= 0 && currentUserIndex >= 10;

    return CustomScrollView(
      controller: _scrollController,
      shrinkWrap: true,
      physics: const BouncingScrollPhysics(),
      slivers: [
        // Podium as first sliver (always show, even if just 1 or 2 users)
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: LeaderboardPodium(
          leaderboardData: top3,
          isSmallScreen: isSmallScreen,
          onUserTap: _navigateToUserProfile,
        ).animate()
          .fadeIn(duration: 600.ms)
          .slideY(begin: 0.1, end: 0, duration: 500.ms, curve: Curves.easeOutQuad),
          ),
        ),
        
        // Ranks 4+ as scrollable sliver list
        if (otherRanks.isNotEmpty)
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
              final rank = index + 4; // Starting from rank 4
                final user = otherRanks[index];
                
              return Padding(
                padding: const EdgeInsets.only(bottom: 6),
                child: LeaderboardCard(
                  rank: rank,
                    user: user,
                  isSmallScreen: isSmallScreen,
                  onTap: _navigateToUserProfile,
                ),
              ).animate(delay: (50 * index).ms)
                .fadeIn(duration: 300.ms)
                .slideY(begin: 0.1, end: 0, duration: 300.ms, curve: Curves.easeOutQuad);
            },
              childCount: otherRanks.length,
            ),
          ),
        
        // Loading more indicator
        if (_isLoadingMore)
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Loading more...',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        
        // End of list indicator when no more data
        if (!_hasMoreData && _leaderboardData.isNotEmpty && !_isLoadingMore)
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.emoji_events_outlined,
                        size: 16,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'You\'ve reached the end!',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        
        // Current user card if they're not in the visible list
        if (showCurrentUserCard && currentUserRank > 10)
          SliverToBoxAdapter(
            child: Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 8, bottom: 6),
                  child: Text(
                    'Your Rank',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: LeaderboardCard(
                    rank: currentUserRank,
                    user: _leaderboardData[currentUserIndex],
                    isSmallScreen: isSmallScreen,
                    onTap: _navigateToUserProfile,
                  ),
                ),
              ],
            ),
          ).animate(delay: 300.ms)
            .fadeIn(duration: 400.ms)
            .slideY(begin: 0.1, end: 0, duration: 400.ms, curve: Curves.easeOutQuad),
          ),
        
        // Add some bottom padding for better scrolling experience
        const SliverToBoxAdapter(
          child: SizedBox(height: 20),
        ),
      ],
    );
  }

  void _navigateToUserProfile(Map<String, dynamic> userData) {
    // Skip navigation for demo users
    final userId = userData['id']?.toString() ?? '0';
    if (userId.startsWith('demo_')) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Demo user profile - feature coming soon!'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }
    
    // Navigate to real user profile
    final userIdInt = int.tryParse(userId) ?? 0;
    if (userIdInt > 0) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(
            userId: userIdInt,
            showBottomNav: false,
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    // Clean up scroll controller
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    
    // Clear cache on dispose to free memory
    _cachedLeaderboardData.clear();
    _cacheTimestamps.clear();
    super.dispose();
  }
  
  // Get cache status for current filter
  String _getCacheStatus() {
    final cacheKey = _getCacheKey(_currentFilter);
    if (!_cacheTimestamps.containsKey(cacheKey)) {
      return 'No cache';
    }
    
    final cacheTime = _cacheTimestamps[cacheKey]!;
    final age = DateTime.now().difference(cacheTime);
    
    if (age.inMinutes < 1) {
      return 'Fresh data';
    } else if (age.inMinutes < 5) {
      return '${age.inMinutes}m old';
    } else {
      return 'Stale data';
    }
  }

  // Background refresh of current filter data
  void _scheduleBackgroundRefresh() {
    Future.delayed(const Duration(minutes: 2), () {
      if (mounted) {
        final cacheKey = _getCacheKey(_currentFilter);
        final cacheTime = _cacheTimestamps[cacheKey];
        if (cacheTime != null) {
          final age = DateTime.now().difference(cacheTime);
          if (age > const Duration(minutes: 1, seconds: 30)) {
            print('⏰ Background refresh triggered for $_currentFilter');
            _loadLeaderboardData(filter: _currentFilter, updateUI: false);
          }
        }
        // Schedule next background refresh
        _scheduleBackgroundRefresh();
      }
    });
  }
} 