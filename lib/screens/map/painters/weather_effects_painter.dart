import 'package:bop_maps/screens/map/models/cyberpunk_glitch.dart';
import 'package:bop_maps/screens/map/models/matrix_character.dart';
import 'package:bop_maps/screens/map/models/particles.dart';
import 'package:bop_maps/screens/map/models/retro_vhs_effect.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

 // Weather effects painter for particle animations and lightning
 class WeatherEffectsPainter extends CustomPainter {
   final List<Particle> particles;
   final List<Particle> lightningBolts;
   final bool lightningFlashActive;
   final double lightningFlashIntensity;
   final double animationValue;
   final List<MatrixCharacter> matrixCharacters;
   final bool matrixGlitchActive;
   final double matrixGlitchIntensity;
   final List<CyberpunkGlitch> cyberpunkGlitches;
   final bool cyberpunkGlitchActive;
   final double cyberpunkGlitchIntensity;
   final List<RetroVHSEffect> retroVHSEffects;
   final bool retroGlitchActive;
   final double retroGlitchIntensity;
   final Size screenSize;
   final bool isTVSwitching;
   final String tvSwitchingPhase;
   
   WeatherEffectsPainter({
     required this.particles,
     required this.lightningBolts,
     required this.lightningFlashActive,
     required this.lightningFlashIntensity,
     required this.animationValue,
     this.matrixCharacters = const [],
     this.matrixGlitchActive = false,
     this.matrixGlitchIntensity = 0.0,
     this.cyberpunkGlitches = const [],
     this.cyberpunkGlitchActive = false,
     this.cyberpunkGlitchIntensity = 0.0,
     this.retroVHSEffects = const [],
     this.retroGlitchActive = false,
     this.retroGlitchIntensity = 0.0,
     required this.screenSize,
     this.isTVSwitching = false,
     this.tvSwitchingPhase = 'none',
   });
   
   @override
   void paint(Canvas canvas, Size size) {
     // Draw lightning flash overlay first (more visible)
     if (lightningFlashActive && lightningFlashIntensity > 0) {
       final safeFlashIntensity = lightningFlashIntensity.clamp(0.0, 1.0);
       
       final flashPaint = Paint()
         ..color = Colors.white.withOpacity((safeFlashIntensity * 0.5).clamp(0.0, 1.0))
         ..blendMode = BlendMode.screen;
       canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), flashPaint);
       
       // Add blue lightning tint for more realism
       final tintPaint = Paint()
         ..color = Colors.lightBlueAccent.withOpacity((safeFlashIntensity * 0.2).clamp(0.0, 1.0))
         ..blendMode = BlendMode.overlay;
       canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), tintPaint);
     }
     
     // Draw lightning bolts
     for (final bolt in lightningBolts) {
       if (bolt.opacity > 0) {
         _drawLightningBolt(canvas, bolt, size);
       }
     }
     
     // Draw weather particles (aura radius handled on map)
     bool hasActiveParticles = false;
     for (final particle in particles) {
       if (particle.opacity > 0) {
         hasActiveParticles = true;
         final safeParticleOpacity = particle.opacity.clamp(0.0, 1.0);
         final particlePaint = Paint()
           ..color = particle.color.withOpacity((safeParticleOpacity * (0.6 + particle.z * 0.8)).clamp(0.0, 1.0));

        // Apply blur for realism, more blur for distant particles
        final blurRadius = (particle.size * 0.6 * (1.5 - particle.z)).clamp(0.0, 20.0);
        particlePaint.maskFilter = MaskFilter.blur(BlurStyle.normal, blurRadius);
        
        final particleSize = (particle.size * (0.7 + particle.z * 1.8)).clamp(1.0, 50.0); // Clamp particle size

        if (particle.color.value == Colors.white.withOpacity(0.9).value) {
          // Snowflake
          _drawSnowflake(canvas, particle.position, particleSize, particlePaint);
        } else if (particle.size > 20) {
          // Cloud
          _drawCloud(canvas, particle.position, particleSize, particlePaint);
        } else {
          // Raindrop
          _drawRaindrop(canvas, particle.position, particleSize, particlePaint);
        }
      }
    }

    // Only draw lightning if there are active particles
    if (!hasActiveParticles) {
      lightningBolts.clear(); // Ensure lightning bolts are cleared
    }

    // Draw matrix characters
    if (matrixCharacters.isNotEmpty) {
      _drawMatrixCharacters(canvas, size);
    }
    
    // Matrix style only shows falling characters - no glitch overlay
    
    // Draw cyberpunk glitch effects
    if (cyberpunkGlitches.isNotEmpty) {
      _drawCyberpunkGlitches(canvas, size);
    }
    if (cyberpunkGlitchActive && cyberpunkGlitchIntensity > 0) {
      _drawCyberpunkGlitchOverlay(canvas, size);
    }
    
    // Draw retro VHS effects
    if (retroVHSEffects.isNotEmpty) {
      _drawRetroVHSEffects(canvas, size);
    }
    if (retroGlitchActive && retroGlitchIntensity > 0) {
      _drawRetroGlitchOverlay(canvas, size);
    }

    // Draw vignette effect for depth (more subtle now)
    final vignettePaint = Paint()
      ..shader = RadialGradient(
        center: Alignment.center,
        radius: 1.1,
        colors: [
          Colors.transparent,
          Colors.black.withOpacity(0.2), // Reduced from 0.35 to let effects show through better
        ],
        stops: const [0.7, 1.0],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), vignettePaint);
  }
  
  void _drawRaindrop(Canvas canvas, Offset position, double size, Paint paint) {
    // Stretched, blurred line for rain
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(center: position, width: size * 0.6, height: size * 5),
        Radius.circular(size * 0.3),
      ),
      paint,
    );
  }
  
  void _drawSnowflake(Canvas canvas, Offset position, double size, Paint paint) {
    // Blurred circle for a soft snowflake effect
    canvas.drawCircle(position, size * 0.7, paint);
  }
  
  void _drawCloud(Canvas canvas, Offset position, double size, Paint paint) {
    // Simple cloud with multiple circles
    final cloudPaint = Paint()
      ..color = paint.color
      ..style = PaintingStyle.fill;
    
    // Main cloud body
    canvas.drawCircle(position, size * 0.5, cloudPaint);
    canvas.drawCircle(Offset(position.dx - size * 0.3, position.dy), size * 0.4, cloudPaint);
    canvas.drawCircle(Offset(position.dx + size * 0.3, position.dy), size * 0.4, cloudPaint);
    canvas.drawCircle(Offset(position.dx - size * 0.1, position.dy - size * 0.2), size * 0.3, cloudPaint);
    canvas.drawCircle(Offset(position.dx + size * 0.1, position.dy - size * 0.2), size * 0.3, cloudPaint);
  }
  
  void _drawLightningBolt(Canvas canvas, Particle bolt, Size size) {
    final safeBoltOpacity = bolt.opacity.clamp(0.0, 1.0);
    final safeBoltSize = bolt.size.clamp(1.0, 10.0);
    
    // Create jagged lightning effect (more visible)
    final lightningPaint = Paint()
      ..color = Colors.white.withOpacity(safeBoltOpacity)
      ..strokeWidth = safeBoltSize * 1.5 // Thicker bolts
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4); // More blur
    
    // Create the lightning path with the velocity as the direction
    final start = bolt.position;
    final end = Offset(
      bolt.position.dx + bolt.velocity.dx,
      bolt.position.dy + bolt.velocity.dy,
    );
    
    // Draw main lightning bolt
    canvas.drawLine(start, end, lightningPaint);
    
    // Add enhanced glow effect
    final glowPaint = Paint()
      ..color = Colors.cyan.withOpacity((safeBoltOpacity * 0.7).clamp(0.0, 1.0))
      ..strokeWidth = safeBoltSize * 4 // Larger glow
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 12); // More blur
    
    canvas.drawLine(start, end, glowPaint);
    
    // Add enhanced electric aura
    final auraPaint = Paint()
      ..color = Colors.lightBlueAccent.withOpacity((safeBoltOpacity * 0.4).clamp(0.0, 1.0))
      ..strokeWidth = safeBoltSize * 7 // Larger aura
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 20); // Much more blur
    
    canvas.drawLine(start, end, auraPaint);
  }
  
  void _drawMatrixCharacters(Canvas canvas, Size size) {
    for (final char in matrixCharacters) {
      if (char.opacity <= 0) continue;
      
      final safeCharOpacity = char.opacity.clamp(0.0, 1.0);
      
      // Draw character with enhanced green glow
      final textStyle = TextStyle(
        color: const Color(0xFF00FF00).withOpacity(safeCharOpacity), // Bright matrix green
        fontSize: 18, // Larger text for better visibility
        fontWeight: FontWeight.bold,
        fontFamily: 'monospace',
        shadows: [
          Shadow(
            color: const Color(0xFF00FF00).withOpacity((safeCharOpacity * 1.0).clamp(0.0, 1.0)),
            blurRadius: 8,
            offset: const Offset(0, 0),
          ),
          Shadow(
            color: const Color(0xFF00FF41).withOpacity((safeCharOpacity * 0.6).clamp(0.0, 1.0)),
            blurRadius: 16,
            offset: const Offset(0, 0),
          ),
        ],
      );
      
      final textPainter = TextPainter(
        text: TextSpan(text: char.character, style: textStyle),
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(canvas, char.position);
      
      // Minimal trail for performance - only for bright characters
      if (safeCharOpacity > 0.7) {
        final trailOpacity = (safeCharOpacity * 0.3).clamp(0.0, 1.0);
        
        final trailStyle = TextStyle(
          color: const Color(0xFF00FF00).withOpacity(trailOpacity),
          fontSize: 18,
          fontWeight: FontWeight.bold,
          fontFamily: 'monospace',
        );
        
        final trailPainter = TextPainter(
          text: TextSpan(text: char.character, style: trailStyle),
          textDirection: TextDirection.ltr,
        );
        
        trailPainter.layout();
        trailPainter.paint(
          canvas, 
          Offset(char.position.dx, char.position.dy - 22),
        );
      }
    }
  }
  
  void _drawMatrixGlitchOverlay(Canvas canvas, Size size) {
    // Ensure valid intensity range
    final safeIntensity = matrixGlitchIntensity.clamp(0.0, 1.0);
    
    // Digital noise overlay (more visible)
    final glitchPaint = Paint()
      ..color = Colors.green.withOpacity((safeIntensity * 0.25).clamp(0.0, 1.0))
      ..blendMode = BlendMode.screen;
    
    // Create horizontal scan lines for glitch effect
    for (int i = 0; i < size.height; i += 3) { // Closer lines (was 4)
      if ((i ~/ 3) % 2 == 0) continue; // Skip fewer lines for more effect
      
      canvas.drawRect(
        Rect.fromLTWH(0, i.toDouble(), size.width, 2),
        glitchPaint,
      );
    }
    
    // Add screen distortion effect (more visible)
    final distortionPaint = Paint()
      ..color = Colors.green.withOpacity((safeIntensity * 0.15).clamp(0.0, 1.0))
      ..blendMode = BlendMode.overlay;
    
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      distortionPaint,
    );
    
    // Add digital glitch bars (more prominent)
    final random = math.Random((animationValue * 100).floor()); // Use animation value instead of fixed seed
    for (int i = 0; i < 8; i++) { // More bars (was 5)
      final y = random.nextDouble() * size.height;
      final height = (30 + random.nextDouble() * 60).clamp(10.0, 100.0); // Clamp height
      
      final glitchBarPaint = Paint()
        ..color = Colors.green.withOpacity((safeIntensity * 0.5).clamp(0.0, 1.0))
        ..blendMode = BlendMode.screen;
      
      canvas.drawRect(
        Rect.fromLTWH(0, y, size.width, height),
        glitchBarPaint,
      );
    }
  }
  
  void _drawCyberpunkGlitches(Canvas canvas, Size size) {
    for (final glitch in cyberpunkGlitches) {
      if (glitch.opacity <= 0) continue;
      
      final safeOpacity = glitch.opacity.clamp(0.0, 1.0);
      final glitchPaint = Paint()
        ..color = glitch.color.withOpacity(safeOpacity)
        ..blendMode = BlendMode.screen;
      
      switch (glitch.type) {
        case 'scan':
          // Horizontal scan lines
          canvas.drawRect(
            Rect.fromLTWH(0, glitch.position.dy, size.width, glitch.height),
            glitchPaint,
          );
          break;
        case 'noise':
          // Digital noise blocks
          final random = math.Random(glitch.position.dx.toInt());
          for (int i = 0; i < 10; i++) {
            final x = glitch.position.dx + random.nextDouble() * glitch.width;
            final y = glitch.position.dy + random.nextDouble() * glitch.height;
            canvas.drawRect(
              Rect.fromLTWH(x, y, 2 + random.nextDouble() * 4, 2 + random.nextDouble() * 4),
              glitchPaint,
            );
          }
          break;
        case 'chromatic':
          // Chromatic aberration effect
          final aberrationPaint1 = Paint()
            ..color = Colors.red.withOpacity((safeOpacity * 0.7).clamp(0.0, 1.0))
            ..blendMode = BlendMode.screen;
          final aberrationPaint2 = Paint()
            ..color = Colors.cyan.withOpacity((safeOpacity * 0.7).clamp(0.0, 1.0))
            ..blendMode = BlendMode.screen;
          
          canvas.drawRect(
            Rect.fromLTWH(glitch.position.dx - 2, glitch.position.dy, glitch.width, glitch.height),
            aberrationPaint1,
          );
          canvas.drawRect(
            Rect.fromLTWH(glitch.position.dx + 2, glitch.position.dy, glitch.width, glitch.height),
            aberrationPaint2,
          );
          break;
        case 'neon':
          // Neon glow rectangles
          final glowPaint = Paint()
            ..color = glitch.color.withOpacity((safeOpacity * 0.3).clamp(0.0, 1.0))
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
          
          canvas.drawRect(
            Rect.fromLTWH(glitch.position.dx, glitch.position.dy, glitch.width, glitch.height),
            glowPaint,
          );
          canvas.drawRect(
            Rect.fromLTWH(glitch.position.dx, glitch.position.dy, glitch.width, glitch.height),
            glitchPaint,
          );
          break;
      }
    }
  }
  
  void _drawCyberpunkGlitchOverlay(Canvas canvas, Size size) {
    // Ensure valid intensity range
    final safeIntensity = cyberpunkGlitchIntensity.clamp(0.0, 1.0);
    
    // Neon scan lines overlay (more visible)
    final scanPaint = Paint()
      ..color = Colors.cyan.withOpacity((safeIntensity * 0.2).clamp(0.0, 1.0))
      ..blendMode = BlendMode.screen;
    
    for (int i = 0; i < size.height; i += 2) { // Closer lines (was 3)
      if (i % 4 == 0) continue; // Skip fewer lines
      canvas.drawRect(
        Rect.fromLTWH(0, i.toDouble(), size.width, 1),
        scanPaint,
      );
    }
    
    // Chromatic aberration flash (more visible)
    final chromPaint1 = Paint()
      ..color = Colors.red.withOpacity((safeIntensity * 0.25).clamp(0.0, 1.0))
      ..blendMode = BlendMode.screen;
    final chromPaint2 = Paint()
      ..color = Colors.cyan.withOpacity((safeIntensity * 0.25).clamp(0.0, 1.0))
      ..blendMode = BlendMode.screen;
    
    canvas.drawRect(Rect.fromLTWH(-4, 0, size.width, size.height), chromPaint1); // Increased offset
    canvas.drawRect(Rect.fromLTWH(4, 0, size.width, size.height), chromPaint2); // Increased offset
  }
  
  void _drawRetroVHSEffects(Canvas canvas, Size size) {
    for (final effect in retroVHSEffects) {
      if (effect.opacity <= 0) continue;
      
      final safeOpacity = effect.opacity.clamp(0.0, 1.0);
      
      switch (effect.type) {
        case 'static':
          // TV static noise (more visible)
          final staticPaint = Paint()
            ..color = Colors.white.withOpacity((safeOpacity * 0.7).clamp(0.0, 1.0))
            ..blendMode = BlendMode.overlay;
          
          final random = math.Random(effect.position.dx.toInt());
          for (int i = 0; i < 80; i++) { // More static particles (was 50)
            final x = effect.position.dx + random.nextDouble() * effect.width;
            final y = effect.position.dy + random.nextDouble() * effect.height;
            final staticSize = (1 + random.nextDouble() * 3).clamp(0.5, 15.0);
            canvas.drawRect(
              Rect.fromLTWH(x, y, staticSize, staticSize),
              staticPaint,
            );
          }
          break;
        case 'lines':
          // Horizontal interference lines (more visible)
          final linePaint = Paint()
            ..color = Colors.white.withOpacity((safeOpacity * 0.8).clamp(0.0, 1.0))
            ..blendMode = BlendMode.overlay;
          
          canvas.drawRect(
            Rect.fromLTWH(0, effect.position.dy, size.width, effect.height),
            linePaint,
          );
          break;
        case 'tracking':
          // VHS tracking errors (moving distortion) - more visible
          final trackingPaint = Paint()
            ..color = Colors.white.withOpacity((safeOpacity * 0.5).clamp(0.0, 1.0))
            ..blendMode = BlendMode.difference;
          
          canvas.drawRect(
            Rect.fromLTWH(effect.position.dx, effect.position.dy, effect.width, effect.height),
            trackingPaint,
          );
          break;
        case 'color_bleed':
          // Color bleeding effect (more visible)
          final bleedPaint1 = Paint()
            ..color = Colors.red.withOpacity((safeOpacity * 0.3).clamp(0.0, 1.0))
            ..blendMode = BlendMode.multiply;
          final bleedPaint2 = Paint()
            ..color = Colors.blue.withOpacity((safeOpacity * 0.3).clamp(0.0, 1.0))
            ..blendMode = BlendMode.multiply;
          
          canvas.drawRect(
            Rect.fromLTWH(effect.position.dx - 2, effect.position.dy, effect.width, effect.height), // Increased offset
            bleedPaint1,
          );
          canvas.drawRect(
            Rect.fromLTWH(effect.position.dx + 2, effect.position.dy, effect.width, effect.height), // Increased offset
            bleedPaint2,
          );
          break;
      }
    }
  }
  
  void _drawRetroGlitchOverlay(Canvas canvas, Size size) {
    // Handle TV switching sequence with enhanced effects
    if (isTVSwitching) {
      _drawTVSwitchingEffects(canvas, size);
      return;
    }
    
    // Ensure valid intensity range
    final safeIntensity = retroGlitchIntensity.clamp(0.0, 1.0);
    
    // Old school TV scan lines (like a CRT monitor)
    final scanLinePaint = Paint()
      ..color = Colors.black.withOpacity((safeIntensity * 0.15).clamp(0.0, 1.0))
      ..blendMode = BlendMode.multiply;
    
    // Use animation value instead of DateTime for moving scan line
    final scanLinePosition = (animationValue * size.height) % size.height;
    
    // Draw multiple scan lines for authentic CRT effect
    for (int i = 0; i < size.height; i += 2) {
      if (i % 4 == 0) { // Every other line is darker
        canvas.drawRect(
          Rect.fromLTWH(0, i.toDouble(), size.width, 1),
          scanLinePaint,
        );
      }
    }
    
    // Moving brightness sweep (like old TV turning on)
    final sweepPaint = Paint()
      ..color = Colors.white.withOpacity((safeIntensity * 0.1).clamp(0.0, 1.0))
      ..blendMode = BlendMode.overlay;
    
    canvas.drawRect(
      Rect.fromLTWH(0, scanLinePosition - 5, size.width, 10),
      sweepPaint,
    );
    
    // Add enhanced TV static for retro effect
    final random = math.Random((animationValue * 100).floor());
    final staticPaint = Paint()
      ..color = Colors.white.withOpacity((safeIntensity * 0.3).clamp(0.0, 1.0))
      ..blendMode = BlendMode.overlay;
    
    // Add periodic static bursts
    final glitchCycle = (animationValue * 0.1) % 1.0; // Faster cycle for static
    if (glitchCycle < 0.02) { // Show for first 2% of cycle (very rarely)
      for (int i = 0; i < 40; i++) { // Less static particles
        final x = random.nextDouble() * size.width;
        final y = random.nextDouble() * size.height;
        final staticSize = (1 + random.nextDouble() * 3).clamp(0.5, 15.0);
        canvas.drawRect(
          Rect.fromLTWH(x, y, staticSize, staticSize),
          staticPaint,
        );
      }
    }
    
    // VHS warping effect (more visible)
    final warpPaint = Paint()
      ..color = Colors.white.withOpacity((safeIntensity * 0.2).clamp(0.0, 1.0))
      ..blendMode = BlendMode.overlay;
    
    // Create horizontal bands with distortion
    for (int i = 0; i < 15; i++) { // More bands (was 10)
      final y = (i / 15) * size.height;
      final offset = math.sin(i * 0.7 + animationValue * 6.28) * safeIntensity * 15; // Use animation value
      canvas.drawRect(
        Rect.fromLTWH(offset, y, size.width, size.height / 15),
        warpPaint,
      );
    }
    
    // Add subtle tube curvature effect
    final curvaturePaint = Paint()
      ..color = Colors.black.withOpacity((safeIntensity * 0.1).clamp(0.0, 1.0))
      ..blendMode = BlendMode.multiply;
    
    // Darker edges to simulate CRT tube curvature
    canvas.drawRect(Rect.fromLTWH(0, 0, 20, size.height), curvaturePaint);
    canvas.drawRect(Rect.fromLTWH(size.width - 20, 0, 20, size.height), curvaturePaint);
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, 20), curvaturePaint);
    canvas.drawRect(Rect.fromLTWH(0, size.height - 20, size.width, 20), curvaturePaint);
  }
  
  void _drawTVSwitchingEffects(Canvas canvas, Size size) {
    final random = math.Random((animationValue * 100).floor());
    
    switch (tvSwitchingPhase) {
      case 'glitch':
        // Intense glitch effect during switching
        _drawIntenseGlitchEffect(canvas, size, random);
        break;
        
      case 'no_signal':
        // Flashing "NO SIGNAL" text with heavy static
        _drawNoSignalEffect(canvas, size, random);
        break;
        
      case 'black_screen':
        // Complete black screen
        _drawBlackScreenEffect(canvas, size);
        break;
    }
  }
  
  void _drawIntenseGlitchEffect(Canvas canvas, Size size, math.Random random) {
    // Very intense static covering the entire screen
    final staticPaint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..blendMode = BlendMode.overlay;
    
    for (int i = 0; i < 200; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final staticSize = (2 + random.nextDouble() * 8).clamp(1.0, 30.0);
      canvas.drawRect(
        Rect.fromLTWH(x, y, staticSize, staticSize),
        staticPaint,
      );
    }
    
    // Horizontal distortion lines
    final distortionPaint = Paint()
      ..color = Colors.white.withOpacity(0.9)
      ..blendMode = BlendMode.difference;
    
    for (int i = 0; i < 20; i++) {
      final y = random.nextDouble() * size.height;
      final height = (5 + random.nextDouble() * 15).clamp(2.0, 50.0);
      final offset = (random.nextDouble() - 0.5) * 100;
      
      canvas.drawRect(
        Rect.fromLTWH(offset, y, size.width, height),
        distortionPaint,
      );
    }
    
    // Color bleeding effect
    final bleedPaint1 = Paint()
      ..color = Colors.red.withOpacity(0.6)
      ..blendMode = BlendMode.multiply;
    final bleedPaint2 = Paint()
      ..color = Colors.blue.withOpacity(0.6)
      ..blendMode = BlendMode.multiply;
    
    canvas.drawRect(Rect.fromLTWH(-8, 0, size.width, size.height), bleedPaint1);
    canvas.drawRect(Rect.fromLTWH(8, 0, size.width, size.height), bleedPaint2);
  }
  
  void _drawNoSignalEffect(Canvas canvas, Size size, math.Random random) {
    // Heavy static background
    final staticPaint = Paint()
      ..color = Colors.white.withOpacity(0.6)
      ..blendMode = BlendMode.overlay;
    
    for (int i = 0; i < 150; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final staticSize = (1 + random.nextDouble() * 5).clamp(1.0, 20.0);
      canvas.drawRect(
        Rect.fromLTWH(x, y, staticSize, staticSize),
        staticPaint,
      );
    }
    
    // Flashing "NO SIGNAL" text - always visible but with flicker effect
    final flickerPattern = (animationValue * 8).floor() % 4; // Slower flicker
    final textOpacity = flickerPattern == 3 ? 0.3 : 1.0; // Brief dim, mostly bright
    
    final textPainter = TextPainter(
      text: TextSpan(
        text: 'NO SIGNAL',
        style: TextStyle(
          color: Colors.white.withOpacity(textOpacity),
          fontSize: 48, // Even larger text
          fontWeight: FontWeight.w900, // Extra bold
          fontFamily: 'monospace',
          shadows: [
            Shadow(
              color: Colors.black,
              blurRadius: 12,
              offset: Offset(6, 6),
            ),
            Shadow(
              color: Colors.red,
              blurRadius: 16,
              offset: Offset(3, 3),
            ),
            Shadow(
              color: Colors.white,
              blurRadius: 4,
              offset: Offset(1, 1),
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );
      
    textPainter.layout();
    final textX = (size.width - textPainter.width) / 2;
    final textY = (size.height - textPainter.height) / 2;
    
    // Add some random offset for the "broken TV" effect
    final offsetX = (random.nextDouble() - 0.5) * 10;
    final offsetY = (random.nextDouble() - 0.5) * 10;
    
    textPainter.paint(canvas, Offset(textX + offsetX, textY + offsetY));
    
    // Horizontal scan lines
    final scanPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..blendMode = BlendMode.multiply;
    
    for (int i = 0; i < size.height; i += 4) {
      canvas.drawRect(
        Rect.fromLTWH(0, i.toDouble(), size.width, 2),
        scanPaint,
      );
    }
  }
  
  void _drawBlackScreenEffect(Canvas canvas, Size size) {
    // Complete black screen with slight static texture
    final blackPaint = Paint()
      ..color = Colors.black.withOpacity(0.95);
    
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      blackPaint,
    );
    
    // Very subtle static for realism
    final random = math.Random((animationValue * 50).floor());
    final subtleStaticPaint = Paint()
      ..color = Colors.white.withOpacity(0.03)
      ..blendMode = BlendMode.overlay;
    
    for (int i = 0; i < 20; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final staticSize = (1 + random.nextDouble() * 2).clamp(1.0, 5.0);
      canvas.drawRect(
        Rect.fromLTWH(x, y, staticSize, staticSize),
        subtleStaticPaint,
      );
    }
  }
  
  @override
  bool shouldRepaint(WeatherEffectsPainter oldDelegate) {
    return particles != oldDelegate.particles ||
           lightningBolts != oldDelegate.lightningBolts ||
           lightningFlashActive != oldDelegate.lightningFlashActive ||
           lightningFlashIntensity != oldDelegate.lightningFlashIntensity ||
           animationValue != oldDelegate.animationValue ||
           matrixCharacters != oldDelegate.matrixCharacters ||
           matrixGlitchActive != oldDelegate.matrixGlitchActive ||
           matrixGlitchIntensity != oldDelegate.matrixGlitchIntensity ||
           cyberpunkGlitches != oldDelegate.cyberpunkGlitches ||
           cyberpunkGlitchActive != oldDelegate.cyberpunkGlitchActive ||
           cyberpunkGlitchIntensity != oldDelegate.cyberpunkGlitchIntensity ||
           retroVHSEffects != oldDelegate.retroVHSEffects ||
           retroGlitchActive != oldDelegate.retroGlitchActive ||
           retroGlitchIntensity != oldDelegate.retroGlitchIntensity ||
           isTVSwitching != oldDelegate.isTVSwitching ||
           tvSwitchingPhase != oldDelegate.tvSwitchingPhase;
  }
 }