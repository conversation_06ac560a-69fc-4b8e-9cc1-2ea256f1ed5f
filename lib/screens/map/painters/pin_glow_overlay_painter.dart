import 'package:flutter/material.dart';
import 'package:maplibre_gl/maplibre_gl.dart';
import 'package:geolocator/geolocator.dart';

// Custom painter for pin glow overlay
class PinGlowOverlayPainter extends CustomPainter {
  final List<Map<String, dynamic>> testPinsData;
  final Position? currentPosition;
  final double glowIntensity;
  final MaplibreMapController? mapController;
  
  PinGlowOverlayPainter({
    required this.testPinsData,
    required this.currentPosition,
    required this.glowIntensity,
    required this.mapController,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // For now, we'll create a simple pulsing glow effect across the entire screen
    // This provides the glowing animation effect without needing precise screen coordinates
    
    if (testPinsData.isEmpty) return;
    
    // Create a subtle full-screen glow effect that pulses
    final glowPaint = Paint()
      ..color = Colors.blue.withOpacity(0.05 * glowIntensity)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 20 * glowIntensity);
    
    // Draw multiple concentric circles for a more dynamic effect
    for (int i = 0; i < 3; i++) {
      final radius = (size.width * 0.3) + (i * 50 * glowIntensity);
      final opacity = (0.02 * glowIntensity) / (i + 1);
      
      final paint = Paint()
        ..color = Colors.cyan.withOpacity(opacity)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 10 + (i * 5));
      
      canvas.drawCircle(
        Offset(size.width * 0.5, size.height * 0.5),
        radius,
        paint,
      );
    }
    
    // Add some sparkle effects in corners for legendary pins
    if (testPinsData.any((pin) => pin['rarity'] == 'legendary')) {
      _drawSparkleEffects(canvas, size);
    }
  }
  
  void _drawSparkleEffects(Canvas canvas, Size size) {
    final sparklePaint = Paint()
      ..color = Colors.yellow.withOpacity(0.3 * glowIntensity)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);
    
    // Draw sparkles at various positions
    final sparklePositions = [
      Offset(size.width * 0.2, size.height * 0.3),
      Offset(size.width * 0.8, size.height * 0.2),
      Offset(size.width * 0.7, size.height * 0.8),
      Offset(size.width * 0.3, size.height * 0.7),
    ];
    
    for (final pos in sparklePositions) {
      canvas.drawCircle(pos, 5 * glowIntensity, sparklePaint);
    }
  }
  
  @override
  bool shouldRepaint(PinGlowOverlayPainter oldDelegate) {
    return oldDelegate.glowIntensity != glowIntensity ||
           oldDelegate.currentPosition != currentPosition;
  }
}
 