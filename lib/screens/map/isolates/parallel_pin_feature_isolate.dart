import 'dart:isolate';
import 'dart:math' as math;

/// Enhanced isolate for parallel pin feature computation
/// Processes pins in parallel chunks for maximum performance
void parallelPinFeatureComputeIsolate(SendPort sendPort) {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  receivePort.listen((message) {
    final data = message as Map<String, dynamic>;
    final pins = (data['pins'] as List).cast<Map<String, dynamic>>();
    final responsePort = data['responsePort'] as SendPort;
    final chunkSize = data['chunkSize'] as int? ?? 10;

    try {
      final features = _computePinFeaturesInParallel(pins, chunkSize);
      responsePort.send({'success': true, 'features': features});
    } catch (e) {
      responsePort.send({'success': false, 'error': e.toString()});
    }
  });
}

/// Compute pin features in parallel chunks for maximum performance
List<Map<String, dynamic>> _computePinFeaturesInParallel(
    List<Map<String, dynamic>> pins, int chunkSize) {
  final features = <Map<String, dynamic>>[];
  final chunks = <List<Map<String, dynamic>>>[];
  
  // Split pins into chunks for parallel processing
  for (int i = 0; i < pins.length; i += chunkSize) {
    final end = math.min(i + chunkSize, pins.length);
    chunks.add(pins.sublist(i, end));
  }
  
  // Process chunks in parallel using Future.wait equivalent in isolate
  for (final chunk in chunks) {
    final chunkFeatures = _computePinFeaturesChunk(chunk);
    features.addAll(chunkFeatures);
  }
  
  return features;
}

/// Process a chunk of pins
List<Map<String, dynamic>> _computePinFeaturesChunk(List<Map<String, dynamic>> pins) {
  final features = <Map<String, dynamic>>[];
  
  for (final pin in pins) {
    final feature = _createPinFeatureInIsolate(pin);
    if (feature != null) {
      features.add(feature);
    }
  }
  
  return features;
}

/// Create pin feature in isolate with enhanced error handling
Map<String, dynamic>? _createPinFeatureInIsolate(Map<String, dynamic> pin) {
  final pinId = pin['id']?.toString();
  if (pinId == null) return null;
  
  try {
    // Extract coordinates with enhanced parsing
    double? lat = pin['latitude'] as double?;
    double? lng = pin['longitude'] as double?;

    if ((lat == null || lng == null) && pin['location'] != null) {
      final location = pin['location'];

      // PostGIS string format parsing (CPU intensive)
      if (location is String && location.contains('POINT')) {
        try {
          final coordsStr = location.split('POINT')[1].replaceAll(RegExp('[() ]'), '');
          final parts = coordsStr.split(RegExp('[,]')).where((p) => p.isNotEmpty).toList();
          if (parts.length >= 2) {
            lng = double.parse(parts[0]);
            lat = double.parse(parts[1]);
          }
        } catch (_) {}
      }

      // GeoJSON map format parsing
      if ((lat == null || lng == null) &&
          location is Map<String, dynamic> &&
          location['type'] == 'Point' &&
          location['coordinates'] != null) {
        try {
          final coords = location['coordinates'] as List;
          if (coords.length >= 2) {
            lng = (coords[0] as num).toDouble();
            lat = (coords[1] as num).toDouble();
          }
        } catch (_) {}
      }
    }

    if (lat == null || lng == null) return null;
    
    // Compute pin rarity (heavy calculation)
    final rarity = _computePinRarity(pin);
    String iconImageId = 'music-pin-$rarity';
    
    // Handle custom skin logic (isolate can't add images, so just pass the URL)
    String? skinImageUrl;
    if (pin['skinDetails'] != null && pin['skinDetails']['image'] != null) {
      skinImageUrl = pin['skinDetails']['image'] as String?;
      // Note: Isolate cannot call _addSkinImage, so we pass the URL and let main thread handle it
      if (skinImageUrl != null && skinImageUrl.isNotEmpty) {
        iconImageId = 'pin-skin-$pinId'; // Use consistent naming with main thread
      }
    }
    
    // Compute visual effects and scale
    final visualEffect = _computeVisualEffect(pin);
    final scale = _computePinScale(pin, rarity);
    
    // Return GeoJSON feature with enhanced properties
    return {
      'type': 'Feature',
      'geometry': {
        'type': 'Point',
        'coordinates': [lng, lat],
      },
      'properties': {
        'id': pinId,
        'icon': iconImageId,
        'text-field': pin['song_title'] ?? '',
        'text-size': 12,
        'text-color': '#FFFFFF',
        'text-halo-color': '#000000',
        'text-halo-width': 2,
        'text-anchor': 'top',
        'text-offset': [0, 1.5],
        'icon-size': scale,
        'icon-allow-overlap': true,
        'icon-ignore-placement': true,
        'icon-opacity': 1.0,
        'rarity': rarity,
        'visualEffect': visualEffect,
        'skinImageUrl': skinImageUrl,
        'originalData': pin, // Store original data for tap handling
        'isPlaying': false, // Will be updated by main thread
        'processed_at': DateTime.now().millisecondsSinceEpoch,
      },
    };
  } catch (e) {
    // Enhanced error handling - return a basic feature if processing fails
    return {
      'type': 'Feature',
      'geometry': {
        'type': 'Point',
        'coordinates': [
          pin['longitude'] as double? ?? 0.0,
          pin['latitude'] as double? ?? 0.0,
        ],
      },
      'properties': {
        'id': pinId,
        'icon': 'music-pin-common',
        'text-field': pin['song_title'] ?? '',
        'text-size': 12,
        'text-color': '#FFFFFF',
        'text-halo-color': '#000000',
        'text-halo-width': 2,
        'text-anchor': 'top',
        'text-offset': [0, 1.5],
        'icon-size': 1.0,
        'icon-allow-overlap': true,
        'icon-ignore-placement': true,
        'icon-opacity': 1.0,
        'rarity': 'common',
        'visualEffect': 'none',
        'skinImageUrl': null,
        'originalData': pin,
        'isPlaying': false,
        'processed_at': DateTime.now().millisecondsSinceEpoch,
        'error': e.toString(),
      },
    };
  }
}

/// Rarity calculation (moved to isolate)
String _computePinRarity(Map<String, dynamic> pin) {
  final upvotes = pin['upvotes'] as int? ?? 0;
  final downvotes = pin['downvotes'] as int? ?? 0;
  final netVotes = upvotes - downvotes;
  
  // Calculate pin age in days
  final ageInDays = _calculatePinAge(pin);
  
  // Age-weighted scoring
  final ageBonus = math.max(0, 7 - ageInDays) * 0.5;
  final totalScore = netVotes + ageBonus;
  
  if (totalScore >= 50) return 'legendary';
  if (totalScore >= 20) return 'epic';
  if (totalScore >= 10) return 'rare';
  if (totalScore >= 5) return 'uncommon';
  return 'common';
}

/// Calculate pin age in days
double _calculatePinAge(Map<String, dynamic> pin) {
  final createdAt = pin['created_at'] as String?;
  if (createdAt == null) return 0;
  
  try {
    final createdDate = DateTime.parse(createdAt);
    final now = DateTime.now();
    return now.difference(createdDate).inDays.toDouble();
  } catch (_) {
    return 0;
  }
}

/// Visual effect computation
String _computeVisualEffect(Map<String, dynamic> pin) {
  final upvotes = pin['upvotes'] as int? ?? 0;
  final downvotes = pin['downvotes'] as int? ?? 0;
  final netVotes = upvotes - downvotes;
  
  if (netVotes >= 100) return 'rainbow';
  if (netVotes >= 50) return 'gold';
  if (netVotes >= 20) return 'silver';
  if (netVotes >= 10) return 'bronze';
  return 'none';
}

/// Pin scale computation
double _computePinScale(Map<String, dynamic> pin, String rarity) {
  // Base scale by rarity
  double baseScale = 1.0;
  switch (rarity) {
    case 'legendary':
      baseScale = 1.5;
      break;
    case 'epic':
      baseScale = 1.3;
      break;
    case 'rare':
      baseScale = 1.2;
      break;
    case 'uncommon':
      baseScale = 1.1;
      break;
    case 'common':
    default:
      baseScale = 1.0;
      break;
  }
  
  // Apply upvote scaling
  final upvotes = pin['upvotes'] as int? ?? 0;
  final upvoteScale = 1.0 + (upvotes * 0.02);
  
  // Apply age scaling (newer pins are slightly larger)
  final ageInDays = _calculatePinAge(pin);
  final ageScale = 1.0 + math.max(0, (7 - ageInDays) * 0.01);
  
  return math.min(baseScale * upvoteScale * ageScale, 2.0);
}
