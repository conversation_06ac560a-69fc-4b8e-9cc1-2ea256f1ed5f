import 'dart:isolate';
import 'dart:math' as math;

// Isolate entry point for pin feature computation
void pinFeatureComputeIsolate(SendPort sendPort) {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  receivePort.listen((message) {
    final data = message as Map<String, dynamic>;
    final pins = (data['pins'] as List).cast<Map<String, dynamic>>();
    final responsePort = data['responsePort'] as SendPort;

    try {
      final features = _computePinFeatures(pins);
      responsePort.send({'success': true, 'features': features});
    } catch (e) {
      responsePort.send({'success': false, 'error': e.toString()});
    }
  });
}

// Pin feature computation algorithm (runs in isolate)
List<Map<String, dynamic>> _computePinFeatures(List<Map<String, dynamic>> pins) {
  final features = <Map<String, dynamic>>[];
  
  for (final pin in pins) {
    final feature = _createPinFeatureInIsolate(pin);
    if (feature != null) {
      features.add(feature);
    }
  }
  
  return features;
}

// Create pin feature in isolate (heavy coordinate parsing, rarity calc, etc.)
Map<String, dynamic>? _createPinFeatureInIsolate(Map<String, dynamic> pin) {
  final pinId = pin['id']?.toString();
  if (pinId == null) return null;
  
  // Extract coordinates - this is the heavy parsing operation
  double? lat = pin['latitude'] as double?;
  double? lng = pin['longitude'] as double?;

  if ((lat == null || lng == null) && pin['location'] != null) {
    final location = pin['location'];

    // PostGIS string format parsing (CPU intensive)
    if (location is String && location.contains('POINT')) {
      try {
        final coordsStr = location.split('POINT')[1].replaceAll(RegExp('[() ]'), '');
        final parts = coordsStr.split(RegExp('[,]')).where((p) => p.isNotEmpty).toList();
        if (parts.length >= 2) {
          lng = double.parse(parts[0]);
          lat = double.parse(parts[1]);
        }
      } catch (_) {}
    }

    // GeoJSON map format parsing
    if ((lat == null || lng == null) &&
        location is Map<String, dynamic> &&
        location['type'] == 'Point' &&
        location['coordinates'] != null) {
      try {
        final coords = location['coordinates'] as List;
        if (coords.length >= 2) {
          lng = (coords[0] as num).toDouble();
          lat = (coords[1] as num).toDouble();
        }
      } catch (_) {}
    }
  }

  if (lat == null || lng == null) return null;
  
  // Compute pin rarity (heavy calculation)
  final rarity = _computePinRarity(pin);
  String iconImageId = 'music-pin-$rarity';
  
  // Handle custom skin logic (isolate can't add images, so just pass the URL)
  String? skinImageUrl;
  if (pin['skinDetails'] != null && pin['skinDetails']['image'] != null) {
    skinImageUrl = pin['skinDetails']['image'] as String?;
    // Note: Isolate cannot call _addSkinImage, so we pass the URL and let main thread handle it
    if (skinImageUrl != null && skinImageUrl.isNotEmpty) {
      iconImageId = 'pin-skin-$pinId'; // Use consistent naming with main thread
    }
  }
  
  // Compute visual effects and scale
  final visualEffect = _computeVisualEffect(pin);
  final scale = _computePinScale(pin, rarity);
  
  // Return GeoJSON feature (this structure is expensive to build)
  return {
    'type': 'Feature',
    'geometry': {
      'type': 'Point',
      'coordinates': [lng, lat],
    },
    'properties': {
      'id': pinId,
      'rarity': rarity,
      'icon': iconImageId,
      'skinImageUrl': skinImageUrl,
      'scale': scale,
      'visualEffect': visualEffect,
      'latitude': lat,
      'longitude': lng,
      // Include original pin data for later use
      'originalData': pin,
    },
  };
}

// Rarity calculation (moved to isolate)
String _computePinRarity(Map<String, dynamic> pin) {
  final playCount = pin['play_count'] as int? ?? 0;
  final likeCount = pin['like_count'] as int? ?? 0;
  final ageInDays = _calculatePinAge(pin);
  
  // Complex rarity algorithm
  final totalInteractions = playCount + (likeCount * 2);
  final popularityScore = totalInteractions / math.max(1, ageInDays);
  
  if (popularityScore > 50) return 'legendary';
  if (popularityScore > 20) return 'epic';  
  if (popularityScore > 10) return 'rare';
  if (popularityScore > 5) return 'uncommon';
  return 'common';
}

// Pin age calculation
int _calculatePinAge(Map<String, dynamic> pin) {
  try {
    final createdAt = pin['created_at'] as String?;
    if (createdAt != null) {
      final pinDate = DateTime.parse(createdAt);
      final now = DateTime.now();
      return now.difference(pinDate).inDays;
    }
  } catch (_) {}
  return 1; // Default to 1 day if parse fails
}

// Visual effect computation
String? _computeVisualEffect(Map<String, dynamic> pin) {
  final isFresh = pin['is_fresh'] == true;
  final isFromFriend = pin['is_from_friend'] == true;
  final playCount = pin['play_count'] as int? ?? 0;
  
  if (isFresh) return 'glow';
  if (isFromFriend) return 'pulse';
  if (playCount > 100) return 'sparkle';
  return null;
}

// Pin scale computation
double _computePinScale(Map<String, dynamic> pin, String rarity) {
  final baseScale = 1.0;
  final isFromFriend = pin['is_from_friend'] == true;
  
  // Scale based on rarity
  double rarityMultiplier = 1.0;
  switch (rarity) {
    case 'legendary':
      rarityMultiplier = 1.4;
      break;
    case 'epic':
      rarityMultiplier = 1.3;
      break;
    case 'rare':
      rarityMultiplier = 1.2;
      break;
    case 'uncommon':
      rarityMultiplier = 1.1;
      break;
  }
  
  // Friends get extra scale
  final friendMultiplier = isFromFriend ? 1.2 : 1.0;
  
  return baseScale * rarityMultiplier * friendMultiplier;
}
