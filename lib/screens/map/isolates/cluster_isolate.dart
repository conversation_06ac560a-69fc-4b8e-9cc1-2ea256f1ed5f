import 'dart:isolate';
import 'dart:math' as math;

// Isolate entry point for cluster computation
void clusterComputeIsolate(SendPort sendPort) {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  receivePort.listen((message) {
    final data = message as Map<String, dynamic>;
    final pins = (data['pins'] as List).cast<Map<String, dynamic>>();
    final clusterRadius = data['clusterRadius'] as double;
    final minClusterSize = data['minClusterSize'] as int;
    final responsePort = data['responsePort'] as SendPort;

    try {
      final clusters = _computeClusters(pins, clusterRadius, minClusterSize);
      responsePort.send({'success': true, 'clusters': clusters});
    } catch (e) {
      responsePort.send({'success': false, 'error': e.toString()});
    }
  });
}

// Cluster computation algorithm (runs in isolate)
List<Map<String, dynamic>> _computeClusters(
  List<Map<String, dynamic>> pins,
  double clusterRadius,
  int minClusterSize,
) {
  if (pins.length < minClusterSize) {
    // Return all pins as individual items
    return pins.map((pin) => {
      'isCluster': false,
      'pin': pin,
      'latitude': pin['latitude'],
      'longitude': pin['longitude'],
      'id': pin['id'],
    }).toList();
  }

  final clusters = <Map<String, dynamic>>[];
  final processed = <bool>[];
  
  // Initialize processed array
  for (int i = 0; i < pins.length; i++) {
    processed.add(false);
  }

  for (int i = 0; i < pins.length; i++) {
    if (processed[i]) continue;

    final pin = pins[i];
    final pinLat = pin['latitude'] as double;
    final pinLng = pin['longitude'] as double;
    
    final nearbyPins = <Map<String, dynamic>>[pin];
    processed[i] = true;

    // Find nearby pins within cluster radius
    for (int j = i + 1; j < pins.length; j++) {
      if (processed[j]) continue;

      final otherPin = pins[j];
      final otherLat = otherPin['latitude'] as double;
      final otherLng = otherPin['longitude'] as double;
      
      final distance = _calculateDistance(pinLat, pinLng, otherLat, otherLng);
      
      if (distance <= clusterRadius) {
        nearbyPins.add(otherPin);
        processed[j] = true;
      }
    }

    if (nearbyPins.length >= minClusterSize) {
      // Create a cluster
      final centerLat = nearbyPins.map((p) => p['latitude'] as double).reduce((a, b) => a + b) / nearbyPins.length;
      final centerLng = nearbyPins.map((p) => p['longitude'] as double).reduce((a, b) => a + b) / nearbyPins.length;
      
      clusters.add({
        'isCluster': true,
        'count': nearbyPins.length,
        'latitude': centerLat,
        'longitude': centerLng,
        'id': 'cluster_${centerLat}_${centerLng}',
        'pins': nearbyPins,
      });
    } else {
      // Add as individual pins
      for (final singlePin in nearbyPins) {
        clusters.add({
          'isCluster': false,
          'pin': singlePin,
          'latitude': singlePin['latitude'],
          'longitude': singlePin['longitude'],
          'id': singlePin['id'],
        });
      }
    }
  }

  return clusters;
}

// Calculate distance between two lat/lng points in meters
double _calculateDistance(double lat1, double lng1, double lat2, double lng2) {
  const double earthRadius = 6371000; // Earth radius in meters
  
  final dLat = (lat2 - lat1) * (math.pi / 180);
  final dLng = (lng2 - lng1) * (math.pi / 180);
  
  final a = math.sin(dLat / 2) * math.sin(dLat / 2) +
      math.cos(lat1 * (math.pi / 180)) * math.cos(lat2 * (math.pi / 180)) *
      math.sin(dLng / 2) * math.sin(dLng / 2);
  
  final c = 2 * math.asin(math.sqrt(a));
  
  return earthRadius * c;
}
