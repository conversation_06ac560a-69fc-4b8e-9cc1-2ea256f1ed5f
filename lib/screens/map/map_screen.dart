import 'dart:ui';
import 'dart:async';
import 'dart:math' as math;
import 'package:bop_maps/services/music/apple_music_auth_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';
import 'package:maplibre_gl/maplibre_gl.dart' as maplibre;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
// Temporarily commenting out mapbox import
// import 'package:mapbox_gl/mapbox_gl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:geolocator/geolocator.dart';
import '../../config/constants.dart';
import '../../config/themes.dart';
import '../../providers/map_provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/pin_provider.dart';
import '../../providers/user_provider.dart';
import '../../models/music_track.dart';
// import '../../widgets/map/aura_effect_widget.dart';
// import '../../widgets/map/pin_widget.dart';
import '../../widgets/music/track_preview_modal.dart';
import '../music/track_select_screen.dart';
import '../music/create_pin_track_select_screen.dart';
import '../../widgets/common/network_error_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
// import '../../widgets/map/shimmer_pin_widget.dart';
import '../../widgets/bottomsheets/create_pin_bottomsheet.dart';
import '../../widgets/animations/fade_in_animation.dart';
import 'snapchat_style_map_screen.dart';
import '../../widgets/map/components/map_control_button.dart';
import '../../widgets/navigation/bottom_navigation_bar.dart';
import '../../screens/profile/profile_screen.dart';
import '../../screens/profile/settings_screen.dart';
import '../../utils/navigation_helper.dart';
import '../../widgets/map/components/expandable_search_bar.dart';
import '../../widgets/search/search_results_overlay.dart';
import '../../providers/search_provider.dart';
import '../../widgets/camera/camera_pin_view.dart';
import '../../widgets/map/components/play_pause_button.dart';
import '../../widgets/map/components/map_now_playing_bar.dart';
import '../../widgets/map/components/map_track_details_bar.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../widgets/map/components/track_navigation_buttons.dart';
import '../../widgets/map/components/map_note_bar.dart';
import '../../widgets/map/flutter_map_widget.dart';
import '../../screens/ar/ar_pin_placement_screen.dart';
import '../search/ai_search/ai_search_view.dart';
import 'package:auto_scroll_text/auto_scroll_text.dart';
import '../../services/ai/global_ai_provider_service.dart';
import '../../widgets/bottomsheets/comment_bottom_sheet.dart';
import '../../providers/gamification_provider.dart';
import '../../providers/youtube_provider.dart';
import '../../services/api/pin_engagement_service.dart';
import '../../services/auth_service.dart';
import '../../services/api_service.dart';
import '../../screens/profile/user_profile_screen.dart';
import '../../providers/onesignal_provider.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({Key? key}) : super(key: key);

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // Controller for the bottom sheet
  final PersistentBottomSheetController? _bottomSheetController = null;

  GlobalKey<FlutterMapWidgetState> _mapKey = GlobalKey<FlutterMapWidgetState>();

  // Snapchat map state key
  final GlobalKey<SnapchatStyleMapScreenState> _snapchatMapKey =
      GlobalKey<SnapchatStyleMapScreenState>();

  // Map and location state
  bool _isInitializing = true;
  bool _hasRequestedLocation = false;
  bool _hasMapInitialized = false;
  DateTime _screenLoadTime = DateTime.now();

  // UI state
  bool _showLocation = false; // Controls animation of the location button
  bool _showLocationPulse =
      false; // Controls the user location indicator pulsing
  bool _showControls = false; // Controls animation of the zoom controls
  bool _isControlsActive =
      false; // Tracks if map controls are being interacted with
  bool _isPlaying = false; // Controls play/pause state

  // Variables for intent processing
  DateTime? _lastIntentProcessTime;
  static const _minIntentProcessingInterval = Duration(milliseconds: 500);

  // Animation controllers
  late AnimationController _loadingAnimationController;
  late Animation<double> _loadingAnimation;

  // Controls fade timer
  Timer? _controlsFadeTimer;

  // Add a field to track the current navigation tab
  int _currentNavIndex = 2;

  bool _showSearchResults = false;

  // Add initialization flag
  bool _hasStartedInitialization = false;

  // Snapchat map controls state
  bool _show3DBuildings = true;
  bool _showVegetation = true;
  bool _isFollowingUser = false;
  maplibre.MaplibreMapController? _snapchatMapController;

  // Caption display state
  final Map<String, bool> _visibleCaptionPopups = {};
  final Map<String, AnimationController> _captionAnimationControllers = {};
  final Map<String, Animation<double>> _captionAnimations = {};
  static const double _captionZoomThreshold = 17.0;
  Timer? _captionUpdateTimer;

  // Add provider references to avoid context lookup in dispose
  late SpotifyProvider _spotifyProviderRef;
  late AppleMusicProvider _appleMusicProviderRef;
  late YouTubeProvider _youtubeProviderRef;
  late PinProvider _pinProviderRef;
  
  // Stream subscription for Apple Music queue manager
  StreamSubscription? _appleMusicQueueSubscription;

  // YouTube engagement service state
  PinEngagementService? _youtubeEngagementService;
  int _youtubeUpvotes = 0;
  int _youtubeDownvotes = 0;
  bool? _youtubeIsUpvoted; // null = no vote, true = upvoted, false = downvoted
  bool _youtubeIsLoading = false;
  String? _youtubeUserProfilePic;
  String? _youtubeUsername;
  String? _youtubeUserDisplayName;
  int _youtubeCommentCount = 0;

  @override
  void initState() {
    super.initState();

    // Add app lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    // Store provider references for cleanup
    Future.microtask(() {
      if (mounted) {
        _spotifyProviderRef =
            Provider.of<SpotifyProvider>(context, listen: false);
        _appleMusicProviderRef =
            Provider.of<AppleMusicProvider>(context, listen: false);
        _youtubeProviderRef =
            Provider.of<YouTubeProvider>(context, listen: false);
        _pinProviderRef = Provider.of<PinProvider>(context, listen: false);
      }
    });

    // Initialize loading animation controller
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _loadingAnimation = Tween<double>(begin: 0.0, end: 1.0)
        .animate(_loadingAnimationController);
    _loadingAnimationController.repeat();

    // Initialize the map screen
    _initializeMapScreen();

    // NEW: Check for pending challenge completions when returning to map screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkForPendingCompletions();
      // Check and show Apple Music intro dialog on first launch
      _checkAndShowAppleMusicIntro();
      // Check and request notification permissions on first launch
      _checkAndRequestNotificationPermission();
    });
  }

  /// Initialize user profile data to ensure it's available for AI search and other features
  void _initializeUserProfile() {
    if (!mounted) return;

    // Initialize user profile in background to ensure it's available for AI search
    Future.microtask(() async {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);

        // Check if user profile is already loaded
        if (userProvider.currentUser == null) {
          print('👤 MapScreen: User profile not loaded, initializing...');
          await userProvider.initialize();
          print('✅ MapScreen: User profile initialized successfully');
        } else {
          print('✅ MapScreen: User profile already loaded');
        }
      } catch (e) {
        print('❌ MapScreen: Error initializing user profile: $e');
        // Don't block map initialization if user profile fails
      }
    });
  }



  /// NEW: Check for pending challenge completions and show them
  void _checkForPendingCompletions() {
    if (!mounted) return;

    final gamificationProvider =
        Provider.of<GamificationProvider>(context, listen: false);

    // Set the context for notifications
    gamificationProvider.setContext(context);

    // Show pending completions if any
    if (gamificationProvider.hasPendingCompletions) {
      // Small delay to ensure the map screen is fully loaded
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          gamificationProvider.showPendingCompletions();
        }
      });
    }
  }

  /// Check and show Apple Music intro dialog on first app launch
  Future<void> _checkAndShowAppleMusicIntro() async {
    if (!mounted) return;
    
    final prefs = await SharedPreferences.getInstance();
    final hasShown = prefs.getBool('has_shown_apple_music_intro') ?? false;
    
    if (!hasShown && mounted) {
      // Mark as shown in preferences
      await prefs.setBool('has_shown_apple_music_intro', true);
      
      // Show the dialog after a short delay for better UX
      await Future.delayed(const Duration(milliseconds: 1000));
      
      if (mounted) {
        _showAppleMusicIntroDialog();
      }
    }
  }
  
  /// Check and request notification permission on first app launch
  Future<void> _checkAndRequestNotificationPermission() async {
    if (!mounted) return;
    
    final prefs = await SharedPreferences.getInstance();
    final hasRequested = prefs.getBool('has_requested_notification_permission') ?? false;
    
    if (!hasRequested && mounted) {
      // Mark as requested in preferences
      await prefs.setBool('has_requested_notification_permission', true);
      
      // Add a short delay for better UX - don't show immediately with other dialogs
      await Future.delayed(const Duration(milliseconds: 1500));
      
      if (mounted) {
        final oneSignalProvider = Provider.of<OneSignalProvider>(context, listen: false);
        final granted = await oneSignalProvider.requestPermission();
        
        if (kDebugMode) {
          print('🔔 MapScreen: Notification permission request result: $granted');
        }
      }
    }
  }

  /// Show the sleek Apple Music intro dialog
  void _showAppleMusicIntroDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.7),
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            constraints: const BoxConstraints(maxWidth: 456),
            margin: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.95),
                  Colors.white.withOpacity(0.9),
                ],
              ),
              borderRadius: BorderRadius.circular(28),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 30,
                  offset: const Offset(0, 10),
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(28),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Apple Music icon with gradient
                      Container(
                        width: 64,
                        height: 64,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Color(0xFFFF6B9D),
                              Color(0xFFC44CEA),
                              Color(0xFF4FACFE),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFFFF6B9D).withOpacity(0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: const Center(
                          child: FaIcon(
                            FontAwesomeIcons.appleWhole,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      
                      // Title
                      const Text(
                        'Connect to Apple Music',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.w700,
                          color: Color(0xFF1A1A1A),
                          letterSpacing: -0.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      
                      // Description
                      Text(
                        'Get the full experience by connecting your Apple Music account. Enjoy high-quality audio and seamless playback.\n\nDon\'t have Apple Music? No worries — we\'ll use YouTube for your music discovery.',
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF1A1A1A).withOpacity(0.7),
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 28),
                      
                      // Action buttons
                      Column(
                        children: [
                          // Connect button
                          SizedBox(
                            width: double.infinity,
                            height: 48,
                            child: ElevatedButton(
                              onPressed: () async {
                                Navigator.of(context).pop();
                                final appleMusicAuth = AppleMusicAuthService();
                                await appleMusicAuth.initialize();
                                
                                if (mounted) {
                                  final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
                                  final success = await appleMusicAuth.requestAuthorization();
                                  
                                  if (success && mounted) {
                                    await appleMusicProvider.connect();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Apple Music connected successfully'),
                                        backgroundColor: Colors.green,
                                      ),
                                    );
                                  }
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF1A1A1A),
                                foregroundColor: Colors.white,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(24),
                                ),
                              ),
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  FaIcon(
                                    FontAwesomeIcons.apple,
                                    size: 18,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Connect Apple Music',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 12),
                          
                          // Continue with YouTube button
                          SizedBox(
                            width: double.infinity,
                            height: 48,
                            child: TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              style: TextButton.styleFrom(
                                foregroundColor: const Color(0xFF1A1A1A).withOpacity(0.6),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(24),
                                ),
                              ),
                              child: const Text(
                                'Continue with YouTube',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }



  // Consolidated initialization method
  Future<void> _initializeMapScreen() async {
    if (!mounted) return;

    // Prevent duplicate initialization
    if (_hasStartedInitialization) {
      print("🚫 MapScreen: Preventing duplicate initialization");
      return;
    }
    _hasStartedInitialization = true;

    final screenInitTime =
        DateTime.now().difference(_screenLoadTime).inMilliseconds;
    print("🚀 MapScreen: Time to init: ${screenInitTime}ms");

    final mapProvider = Provider.of<MapProvider>(context, listen: false);

    // Initialize user profile data early to ensure it's available for AI search and other features
    _initializeUserProfile();

    // Initialize YouTube engagement service
    _initializeYouTubeEngagementService();

    setState(() {
      _isInitializing = true;
      _hasRequestedLocation = true;
    });

    try {
      print("🚀 MapScreen: Requesting location permission");
      await mapProvider.requestLocationPermission();
      print("🚀 MapScreen: Location permission requested");

      if (mounted) {
        setState(() {
          _showLocation = true;
          _showControls = true;
          if (mapProvider.currentPosition != null) {
            _showLocationPulse = true;
          }
        });
      }

      // Pin refreshing is now handled by SmartLocationFetchManager in SnapchatStyleMapScreen
      print("🚀 MapScreen: Pin refreshing disabled - handled by SmartLocationFetchManager");
    } catch (e) {
      print("❌ MapScreen: Error during initialization: $e");
    } finally {
      if (mounted) {
        setState(() {
          _isInitializing = false;
          _hasMapInitialized = true;
        });

        final fullInitTime =
            DateTime.now().difference(_screenLoadTime).inMilliseconds;
        print(
            "🚀 MapScreen: Full initialization completed in: ${fullInitTime}ms");
      }
    }
  }

  // Start timer to auto-hide controls
  void _startControlsFadeTimer() {
    // Remove timer functionality to prevent controls from fading
    if (!_showControls) {
      setState(() {
        _showControls = true;
      });
    }

    // Clear any existing timer
    _controlsFadeTimer?.cancel();
    _controlsFadeTimer = null;
  }

  // Show controls when user interacts with map
  void _showMapControls() {
    if (!_showControls) {
      setState(() {
        _showControls = true;
      });
    }

    // Set controls to active state
    setState(() {
      _isControlsActive = true;

      // Reset to inactive after delay
      Future.delayed(const Duration(seconds: 5), () {
        if (mounted) {
          setState(() {
            _isControlsActive = false;
          });
        }
      });
    });
  }

  @override
  void dispose() {
    // Remove lifecycle observer
    WidgetsBinding.instance.removeObserver(this);

    _controlsFadeTimer?.cancel();
    _captionUpdateTimer?.cancel();
    _clearPinDataTimer?.cancel();
    _loadingAnimationController.dispose();

    // Dispose caption animation controllers
    for (var controller in _captionAnimationControllers.values) {
      controller.dispose();
    }
    _captionAnimationControllers.clear();
    _captionAnimations.clear();

    // Remove listeners using stored provider refs to avoid context lookup
    _spotifyProviderRef.removeListener(_onMusicSourceChanged);
    _appleMusicProviderRef.removeListener(_onMusicSourceChanged);
    _youtubeProviderRef.removeListener(_onMusicSourceChanged);
    _pinProviderRef.removeListener(_onPinProviderChanged);
    
    // Cancel Apple Music queue manager subscription
    _appleMusicQueueSubscription?.cancel();
    _appleMusicQueueSubscription = null;

    super.dispose();
  }

  /// Initialize YouTube engagement service
  void _initializeYouTubeEngagementService() {
    try {
      final apiService = context.read<ApiService>();
      final authService = context.read<AuthService>();
      _youtubeEngagementService = PinEngagementService(apiService, authService);
    } catch (e) {
      print('❌ Failed to initialize YouTube engagement service: $e');
    }
  }

  /// Load YouTube vote data for current pin
  Future<void> _loadYouTubeVoteData(String pinId) async {
    if (_youtubeEngagementService == null) return;

    try {
      final pinIdInt = int.tryParse(pinId);
      if (pinIdInt == null) return;

      setState(() {
        _youtubeIsLoading = true;
      });

      final voteStats =
          await _youtubeEngagementService!.getQuickVoteInfo(pinIdInt);

      print(
          '🎬 YouTube Panel: Loaded vote data - upvotes: ${voteStats.upvotes}, downvotes: ${voteStats.downvotes}, userVote: ${voteStats.userVote}');

      if (mounted) {
        setState(() {
          _youtubeUpvotes = voteStats.upvotes;
          _youtubeDownvotes = voteStats.downvotes;
          _youtubeIsUpvoted = voteStats.userVote == 1
              ? true
              : voteStats.userVote == -1
                  ? false
                  : null;

          // Update user info from API response
          _youtubeUserProfilePic = voteStats.pinInfo?['owner']?['profile_pic'];
          _youtubeUsername = voteStats.ownerUsername;
          _youtubeUserDisplayName = voteStats.pinInfo?['owner']
                  ?['display_name'] ??
              voteStats.pinInfo?['owner']?['name'];

          _youtubeIsLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _youtubeIsLoading = false;
        });
      }
      print('❌ Failed to load YouTube vote data: $e');
      // Reset vote data on error
      setState(() {
        _youtubeUpvotes = 0;
        _youtubeDownvotes = 0;
        _youtubeIsUpvoted = null;
        _youtubeCommentCount = 0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<MapProvider, AuthProvider, PinProvider>(
      builder: (context, mapProvider, authProvider, pinProvider, child) {
        // Get currently playing pin data from PinProvider instead of local state
        final currentlyPlayingPinId = pinProvider.currentlyPlayingPinId;
        final currentlyPlayingPinData = pinProvider.currentlyPlayingPinData;

        debugPrint(
            '🎵 [MapScreen] Build - currentlyPlayingPinId: $currentlyPlayingPinId');
        debugPrint(
            '🎵 [MapScreen] Build - hasCurrentlyPlayingPin: ${pinProvider.hasCurrentlyPlayingPin}');

        return PopScope(
          canPop: false, // Prevent users from navigating back from the map screen
          onPopInvoked: (didPop) {
            // Optional: Show a message or handle the back gesture attempt
            if (!didPop) {
              // You can add custom logic here if needed, like showing a dialog
              // For now, we simply prevent the back navigation
              debugPrint('🚫 Back navigation prevented from MapScreen');
            }
          },
          child: Shortcuts(
            shortcuts: <LogicalKeySet, Intent>{
              LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyF):
                  const ActivateIntent(),
              LogicalKeySet(LogicalKeyboardKey.meta, LogicalKeyboardKey.keyF):
                  const ActivateIntent(),
              // Add escape key to close search results
              LogicalKeySet(LogicalKeyboardKey.escape): const DismissIntent(),
            },
            child: Actions(
              actions: <Type, Action<Intent>>{
                ActivateIntent: CallbackAction<ActivateIntent>(
                  onInvoke: (intent) {
                    // Trigger search bar expansion
                    final searchBar = _findSearchBar(context);
                    if (searchBar != null) {
                      _expandSearchBar(searchBar);
                    }
                    return null;
                  },
                ),
                DismissIntent: CallbackAction<DismissIntent>(
                  onInvoke: (intent) {
                    // Close search results if they're open
                    if (_showSearchResults) {
                      _closeSearchResults();
                    }
                    return null;
                  },
                ),
              },
              child: Scaffold(
              // Remove AppBar completely for true fullscreen
              extendBodyBehindAppBar: true,
              extendBody: true,
              appBar: null,
              resizeToAvoidBottomInset:
                  false, // Prevent keyboard from affecting background content
              body: GestureDetector(
                onTap:
                    _showMapControls, // Keep this to ensure controls stay shown
                child: Stack(
                  children: [
                    // Main map widget takes full screen
                    SnapchatStyleMapScreen(
                      key: _snapchatMapKey,
                      showBottomNav: false,
                      onSymbolTap: _showPinDetails,
                      onMapCreated: _onSnapchatMapCreated,
                      onZoomIn: _snapchatZoomIn,
                      onZoomOut: _snapchatZoomOut,
                      onToggle3DBuildings: _snapchatToggle3DBuildings,
                      onToggleVegetation: _snapchatToggleVegetation,
                      onAdjustTilt: _snapchatAdjustTilt,
                      onToggleFollowUser: _snapchatToggleFollowUser,
                      show3DBuildings: _show3DBuildings,
                      showVegetation: _showVegetation,
                      isFollowingUser: _isFollowingUser,
                      // New callbacks for moved buttons
                      onSettingsPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const SettingsScreen(showBottomNav: true),
                          ),
                        );
                      },
                      onSearchPressed: () async {
                        // Use global AI provider instead of creating a new instance
                        // It should already be initialized in _initializeGlobalAIProvider
                        // but we'll ensure it's ready just in case
                        final globalAIProvider = GlobalAIProviderService.instance;
                        
                        // Make sure the global provider is initialized
                        if (!globalAIProvider.isInitialized) {
                          await globalAIProvider.initializeIfAuthenticated(context);
                        }
                        
                        if (!mounted) return;
                        
                        Navigator.push(
                          context,
                          PageRouteBuilder(
                            pageBuilder:
                                (context, animation, secondaryAnimation) =>
                                    const AISearchView(),
                            transitionsBuilder: (context, animation,
                                secondaryAnimation, child) {
                              return SlideTransition(
                                position: Tween<Offset>(
                                  begin: const Offset(0.0, 1.0),
                                  end: Offset.zero,
                                ).animate(CurvedAnimation(
                                  parent: animation,
                                  curve: Curves.easeInOut,
                                )),
                                child: child,
                              );
                            },
                            transitionDuration:
                                const Duration(milliseconds: 300),
                          ),
                        );
                      },
                      onAddPinPressed: () {
                        HapticFeedback.mediumImpact();
                        _showCreatePinBottomSheet();
                      },
                      onCommentPressed: () {
                        // Handle comment functionality - can be moved to a separate method
                        _showCommentBottomSheet();
                      },
                      onNotePressed: () {
                        // Handle note tap functionality
                      },
                      // Add callbacks for pin highlighting to update caption and user details
                      onPinHighlightStarted: _onPinHighlightStarted,
                      onPinHighlightStopped: _onPinHighlightStopped,

                      // Forward caption display from SnapchatStyleMapScreen to this screen
                      onShowCaptionToParent: _showCaptionPopup,
                    ),

                    // Compact glassmorphic music controls - hide when YouTube is active
                    Positioned(
                      bottom: 120,
                      right: 16,
                      child: Consumer3<SpotifyProvider, AppleMusicProvider,
                          YouTubeProvider>(
                        builder: (context, spotifyProvider, appleMusicProvider,
                            youtubeProvider, _) {
                          // Check if any service is playing
                          final spotifyPlaying = spotifyProvider.isPlaying;
                          final appleMusicPlaying =
                              appleMusicProvider.isPlaying;
                          final youtubePlaying = youtubeProvider.isPlaying;
                          final spotifyTrack = spotifyProvider.currentTrack;
                          final appleMusicTrack =
                              appleMusicProvider.currentTrack;
                          final youtubeTrack = youtubeProvider.currentTrack;

                          // Hide controls when YouTube is active (has its own embedded player)
                          if (youtubeTrack != null) {
                            return const SizedBox.shrink();
                          }

                          // Determine which service is currently active (Spotify or Apple Music only)
                          final useAppleMusic = !youtubePlaying &&
                              ((appleMusicPlaying && appleMusicTrack != null) ||
                                  (appleMusicTrack != null && !spotifyPlaying));
                          final useSpotify = !youtubePlaying &&
                              !useAppleMusic &&
                              spotifyTrack != null;

                          if (!useAppleMusic && !useSpotify)
                            return const SizedBox.shrink();

                          return ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: Container(
                              height: 82,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 8,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Previous button
                                  _buildGlassMorphicControlButton(
                                    icon: Icons.skip_previous_rounded,
                                    tooltip: 'Previous Track',
                                    onPressed: () async {
                                      if (useAppleMusic) {
                                        await appleMusicProvider
                                            .skipToPreviousItem();
                                      } else if (useSpotify) {
                                        await spotifyProvider.skipPrevious();
                                      }
                                    },
                                  ),
                                  const SizedBox(width: 4),
                                  // Play/Pause button (larger)
                                  _buildGlassMorphicControlButton(
                                    icon: (spotifyPlaying ||
                                            appleMusicPlaying)
                                        ? Icons.pause_rounded
                                        : Icons.play_arrow_rounded,
                                    tooltip: (spotifyPlaying ||
                                            appleMusicPlaying)
                                        ? 'Pause'
                                        : 'Play',
                                    onPressed: () =>
                                        _togglePlayPauseWithService(
                                      isPlaying: spotifyPlaying ||
                                          appleMusicPlaying,
                                      useAppleMusic: useAppleMusic,
                                      useSpotify: useSpotify,
                                      useYouTube: false, // YouTube is excluded
                                    ),
                                    isMainButton: true,
                                  ),
                                  const SizedBox(width: 4),
                                  // Next button
                                  _buildGlassMorphicControlButton(
                                    icon: Icons.skip_next_rounded,
                                    tooltip: 'Next Track',
                                    onPressed: () async {
                                      if (useAppleMusic) {
                                        await appleMusicProvider
                                            .skipToNextItem();
                                      } else if (useSpotify) {
                                        await spotifyProvider.skipNext();
                                      }
                                    },
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    // Compact now playing info - show when track exists (Spotify or Apple Music, not YouTube)
                    Consumer3<SpotifyProvider, AppleMusicProvider,
                        YouTubeProvider>(
                      builder: (context, spotifyProvider, appleMusicProvider,
                          youtubeProvider, _) {
                        final spotifyTrack = spotifyProvider.currentTrack;
                        final appleMusicTrack = appleMusicProvider.currentTrack;
                        final youtubeTrack = youtubeProvider.currentTrack;

                        // Only show for Spotify/Apple Music when YouTube is not active
                        if (youtubeTrack != null) {
                          return const SizedBox
                              .shrink(); // YouTube has its own UI
                        }

                        // Check if either service has a current track
                        if (spotifyTrack == null && appleMusicTrack == null) {
                          return const SizedBox.shrink();
                        }

                        return Positioned(
                          bottom: 120,
                          left: 16,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(16),
                            child: Container(
                              constraints: const BoxConstraints(maxWidth: 212),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.3),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                              child: const MapNowPlayingBar(),
                            ),
                          ),
                        );
                      },
                    ),

                    // YouTube Unique UI - Show comprehensive info on bottom right when YouTube is playing
                    Consumer3<SpotifyProvider, AppleMusicProvider,
                        YouTubeProvider>(
                      builder: (context, spotifyProvider, appleMusicProvider,
                          youtubeProvider, _) {
                        final youtubeTrack = youtubeProvider.currentTrack;
                        final isYoutubePlaying = youtubeProvider.isPlaying;

                        // Only show when YouTube is active
                        if (youtubeTrack == null) {
                          return const SizedBox.shrink();
                        }

                        return Positioned(
                          bottom: 108,
                          right: 8, // Moved to the right side (was right: 140)
                          left: 140, // Leave space for video player on the left (was left: 8)
                          child: _buildYouTubeInfoPanel(
                              youtubeTrack, isYoutubePlaying, pinProvider),
                        );
                      },
                    ),

                    // Caption panel above YouTube info panel
                    Consumer3<SpotifyProvider, AppleMusicProvider,
                        YouTubeProvider>(
                      builder: (context, spotifyProvider, appleMusicProvider,
                          youtubeProvider, _) {
                        final youtubeTrack = youtubeProvider.currentTrack;
                        final pinProvider =
                            Provider.of<PinProvider>(context, listen: false);
                        final currentlyPlayingPinId =
                            pinProvider.currentlyPlayingPinId;
                        final currentlyPlayingPinData =
                            pinProvider.currentlyPlayingPinData;
                        final caption = currentlyPlayingPinData?['caption'] ??
                            currentlyPlayingPinData?['description'] ??
                            '';
                        if (youtubeTrack == null || caption.isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return Positioned(
                          left: 140, // Adjusted to match info panel positioning (was left: 8)
                          right: 8, // Adjusted to match info panel positioning (was right: 208)
                          bottom: 205, // Raised slightly for proper spacing (was 190)
                          child: GestureDetector(
                            onTap: () {
                              if (currentlyPlayingPinId != null &&
                                  caption.isNotEmpty) {
                                _showCaptionPopup(
                                    currentlyPlayingPinId, caption);
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 10),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.7),
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.15),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.15),
                                    blurRadius: 10,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    caption,
                                    maxLines: 2,
                                    textAlign: TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    // Compact user interaction buttons - show when track exists (Spotify or Apple Music, not YouTube)
                    Consumer3<SpotifyProvider, AppleMusicProvider,
                        YouTubeProvider>(
                      builder: (context, spotifyProvider, appleMusicProvider,
                          youtubeProvider, _) {
                        final spotifyTrack = spotifyProvider.currentTrack;
                        final appleMusicTrack = appleMusicProvider.currentTrack;
                        final youtubeTrack = youtubeProvider.currentTrack;

                        // Don't show when YouTube is active (has its own comprehensive UI)
                        if (youtubeTrack != null) {
                          return const SizedBox.shrink();
                        }

                        // Check if either service has a current track AND it's from a pin (using PinProvider)
                        if ((spotifyTrack == null && appleMusicTrack == null) ||
                            !pinProvider.hasCurrentlyPlayingPin) {
                          return const SizedBox.shrink();
                        }

                        return Positioned(
                          bottom: 208,
                          left: 16,
                          right: 16,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Track details bar on the left
                              Expanded(
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  clipBehavior: Clip.antiAlias,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          Colors.black.withOpacity(0.6),
                                          Colors.black.withOpacity(0.8),
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Colors.white.withOpacity(0.25),
                                        width: 1,
                                      ),
                                    ),
                                    padding: const EdgeInsets.all(4),
                                    child: MapTrackDetailsBar(
                                      pinId: currentlyPlayingPinId != null
                                          ? int.tryParse(
                                                  currentlyPlayingPinId) ??
                                              (currentlyPlayingPinData?['id'] ??
                                                  1)
                                          : (currentlyPlayingPinData?['id'] ??
                                              1),
                                      username: currentlyPlayingPinData != null
                                          ? (currentlyPlayingPinData['owner']
                                                  ?['username'] ??
                                              currentlyPlayingPinData[
                                                  'username'] ??
                                              currentlyPlayingPinData[
                                                  'owner_name'] ??
                                              'Anonymous')
                                          : 'John Doe',
                                      userImage: currentlyPlayingPinData != null
                                          ? (currentlyPlayingPinData['owner']
                                                  ?['profile_pic'] ??
                                              currentlyPlayingPinData[
                                                  'profile_pic'] ??
                                              'https://example.com/avatar.jpg')
                                          : 'https://example.com/avatar.jpg',
                                      initialUpvotes:
                                          currentlyPlayingPinData != null
                                              ? (currentlyPlayingPinData[
                                                          'interaction_count']
                                                      ?['like'] ??
                                                  currentlyPlayingPinData[
                                                      'like_count'] ??
                                                  0)
                                              : 0,
                                      initialDownvotes:
                                          currentlyPlayingPinData != null
                                              ? (currentlyPlayingPinData[
                                                          'interaction_count']
                                                      ?['dislike'] ??
                                                  currentlyPlayingPinData[
                                                      'dislike_count'] ??
                                                  0)
                                              : 0,
                                      onComment: _showCommentBottomSheet,
                                      transparent: true,
                                    ),
                                  ),
                                ),
                              ),

                              const SizedBox(width: 8),

                              // Note widget on the right
                              ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                clipBehavior: Clip.antiAlias,
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Colors.white.withOpacity(0.12),
                                        Colors.white.withOpacity(0.04),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.25),
                                      width: 1,
                                    ),
                                  ),
                                  child: MapNoteBar(
                                    note: currentlyPlayingPinData != null
                                        ? (currentlyPlayingPinData['caption'] ??
                                            currentlyPlayingPinData[
                                                'description'] ??
                                            "Perfect spot for a sunset vibe 🌅")
                                        : "Perfect spot for a sunset vibe 🌅",
                                    onTap: () {
                                      // Handle note tap - could show full caption or edit
                                      if (currentlyPlayingPinData != null) {
                                        final caption = currentlyPlayingPinData[
                                                'caption'] ??
                                            currentlyPlayingPinData[
                                                'description'];
                                        if (caption != null &&
                                            caption.isNotEmpty) {
                                          _showCaptionPopup(
                                              currentlyPlayingPinId!, caption);
                                        }
                                      }
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    // Initial loading indicator
                    if (_isInitializing)
                      Container(
                        color: Colors.white.withOpacity(0.7),
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: 60,
                                height: 60,
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Theme.of(context).colorScheme.primary,
                                  ),
                                  strokeWidth: 3,
                                ),
                              ),
                              const SizedBox(height: 20),
                              Text(
                                "Finding your location...",
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              if (_hasRequestedLocation &&
                                  mapProvider.currentPosition == null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 8.0),
                                  child: Text(
                                    "Please allow location access",
                                    style: TextStyle(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),

                    // Caption popup overlay for currently playing pin (using PinProvider data)
                    _buildCaptionPopup(
                        currentlyPlayingPinId, currentlyPlayingPinData),

                    // Search results overlay (conditionally shown)
                    if (_showSearchResults)
                      SearchResultsOverlay(
                        onClose: _closeSearchResults,
                      ),
                  ],
                ),
              ),

              // Bottom navigation
              bottomNavigationBar: _buildBottomNavBar(context),
            ),
          ),
        )); // Close PopScope
      },
    );
  }

  // Build bottom navigation bar - Optimized
  Widget _buildBottomNavBar(BuildContext context) {
    return MusicPinBottomNavBar.auto(
      context: context,
      onTabSelected: _handleNavigationChange,
      onAddPinPressed: _showCreatePinBottomSheet, // Add the missing callback
    );
  }

  // Show pin details with enhanced user information
  void _showPinDetails(Map<String, dynamic> pin) {
    // UPDATED: Use PinProvider to set currently playing pin data
    final pinProvider = Provider.of<PinProvider>(context, listen: false);
    final pinId = pin['id']?.toString();

    if (pinId != null) {
      debugPrint(
          '🎵 [MapScreen] Setting currently playing pin from symbol tap: $pinId');
      pinProvider.setCurrentlyPlayingPin(pinId, pin);
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildEnhancedPinDetailsModal(pin),
    );
  }

  // UPDATED: Update currently playing pin through PinProvider instead of local state
  void _updateCurrentlyPlayingPin(Map<String, dynamic> pin) {
    debugPrint(
        '🎵 [MapScreen] _updateCurrentlyPlayingPin called with pin: ${pin['id']}');

    final pinProvider = Provider.of<PinProvider>(context, listen: false);
    final pinId = pin['id']?.toString();

    if (pinId != null) {
      debugPrint('🎵 [MapScreen] Setting pin in provider: $pinId');
      pinProvider.setCurrentlyPlayingPin(pinId, pin);
    }

    // Show caption for the currently playing pin
    final caption = pin['caption'] as String? ?? pin['description'] as String?;
    debugPrint('🎵 [MapScreen] Extracted caption: "$caption"');

    if (caption != null &&
        caption.isNotEmpty &&
        caption.trim().isNotEmpty &&
        pinId != null) {
      debugPrint('🎵 [MapScreen] Caption is valid, calling _showCaptionPopup');
      _showCaptionPopup(pinId, caption);
      debugPrint('🎵 [MapScreen] Showing caption for pin: $pinId');
    } else {
      debugPrint(
          '🎵 [MapScreen] ❌ Caption is empty or null: caption="$caption"');
    }

    // Log user details that will be displayed
    final owner = pin['owner'] as Map<String, dynamic>? ?? {};
    final username = owner['username'] ??
        pin['username'] ??
        pin['owner_name'] ??
        'Anonymous';
    final trackTitle = pin['title'] ?? pin['track_title'] ?? 'Unknown Track';
    final trackArtist =
        pin['artist'] ?? pin['track_artist'] ?? 'Unknown Artist';

    debugPrint(
        '🎵 [MapScreen] Updated playing pin - Track: $trackTitle by $trackArtist, User: @$username');
  }

  // Enhanced pin details modal with comprehensive user information
  Widget _buildEnhancedPinDetailsModal(Map<String, dynamic> pin) {
    final mediaQuery = MediaQuery.of(context);
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final textTheme = theme.textTheme;

    // Extract pin data with enhanced user information
    final title = pin['title'] ?? pin['track_title'] ?? 'Unknown Track';
    final artist = pin['artist'] ?? pin['track_artist'] ?? 'Unknown Artist';
    final albumName = pin['album'] ?? 'Unknown Album';
    final caption = pin['caption'] ?? pin['description'] ?? '';
    final timestamp = pin['created_at'] ?? pin['dateCreated'] ?? 'Recently';
    final rarity = pin['rarity']?.toLowerCase() ?? 'common';
    final artworkUrl =
        pin['artwork_url'] ?? pin['albumArt'] ?? pin['album_art'] ?? '';

    // Enhanced user information extraction
    final owner = pin['owner'] as Map<String, dynamic>? ?? {};
    final username = owner['username'] ??
        pin['username'] ??
        pin['owner_name'] ??
        'Anonymous';
    final userProfilePic = owner['profile_pic'] ?? pin['profile_pic'] ?? '';
    final userDisplayName = owner['name'] ?? username;

    // Interaction counts
    final interactionCount =
        pin['interaction_count'] as Map<String, dynamic>? ?? {};
    final likeCount = interactionCount['like'] ?? pin['like_count'] ?? 0;
    final collectCount = interactionCount['collect'] ??
        pin['collect_count'] ??
        pin['collectionCount'] ??
        0;
    final viewCount = interactionCount['view'] ?? pin['view_count'] ?? 0;
    final shareCount = interactionCount['share'] ?? pin['share_count'] ?? 0;

    // Pin rarity color
    final rarityColor = _getPinRarityColor(rarity);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      constraints: BoxConstraints(
        maxHeight: mediaQuery.size.height * 0.85,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Drag handle
          Center(
            child: Container(
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // User information header
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24.0, vertical: 16.0),
                    child: Row(
                      children: [
                        // User avatar
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: rarityColor.withOpacity(0.3),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: rarityColor.withOpacity(0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(24),
                            child: userProfilePic.isNotEmpty
                                ? CachedNetworkImage(
                                    imageUrl: userProfilePic,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) => Container(
                                      color: primaryColor.withOpacity(0.1),
                                      child: Icon(
                                        Icons.person,
                                        color: primaryColor,
                                        size: 24,
                                      ),
                                    ),
                                    errorWidget: (context, url, error) =>
                                        Container(
                                      color: primaryColor.withOpacity(0.1),
                                      child: Icon(
                                        Icons.person,
                                        color: primaryColor,
                                        size: 24,
                                      ),
                                    ),
                                  )
                                : Container(
                                    color: primaryColor.withOpacity(0.1),
                                    child: Icon(
                                      Icons.person,
                                      color: primaryColor,
                                      size: 24,
                                    ),
                                  ),
                          ),
                        ),

                        const SizedBox(width: 12),

                        // User info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                userDisplayName,
                                style: textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                '@$username',
                                style: textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface
                                      .withOpacity(0.6),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),

                        // Rarity badge
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: rarityColor.withOpacity(0.15),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: rarityColor.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            rarity.toUpperCase(),
                            style: textTheme.bodySmall?.copyWith(
                              color: rarityColor,
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Caption display (if available)
                  if (caption.isNotEmpty) ...[
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.05),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: theme.colorScheme.primary.withOpacity(0.1),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: 6,
                              height: 6,
                              margin: const EdgeInsets.only(top: 6),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Note',
                                    style: textTheme.labelMedium?.copyWith(
                                      color: theme.colorScheme.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    caption,
                                    style: textTheme.bodyMedium?.copyWith(
                                      height: 1.4,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Cover art and track info
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Album art with enhanced styling
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: rarityColor.withOpacity(0.3),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Stack(
                              children: [
                                // Main artwork
                                artworkUrl.isNotEmpty
                                    ? CachedNetworkImage(
                                        imageUrl: artworkUrl,
                                        width: 100,
                                        height: 100,
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) =>
                                            Container(
                                          color: rarityColor.withOpacity(0.1),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: rarityColor,
                                              strokeWidth: 2,
                                            ),
                                          ),
                                        ),
                                        errorWidget: (context, url, error) =>
                                            Container(
                                          color: rarityColor.withOpacity(0.2),
                                          child: Icon(
                                            Icons.music_note,
                                            size: 40,
                                            color: rarityColor,
                                          ),
                                        ),
                                      )
                                    : Container(
                                        color: rarityColor.withOpacity(0.2),
                                        child: Icon(
                                          Icons.music_note,
                                          size: 40,
                                          color: rarityColor,
                                        ),
                                      ),

                                // Gradient overlay for rarity effect
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Colors.transparent,
                                        rarityColor.withOpacity(0.1),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(width: 20),

                        // Track information
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                title,
                                style: textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  height: 1.2,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                artist,
                                style: textTheme.titleMedium?.copyWith(
                                  color: theme.colorScheme.onSurface
                                      .withOpacity(0.8),
                                  height: 1.2,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                albumName,
                                style: textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface
                                      .withOpacity(0.6),
                                  fontStyle: FontStyle.italic,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 12),

                              // Interaction stats
                              Wrap(
                                spacing: 8,
                                runSpacing: 8,
                                children: [
                                  _buildStatChip(
                                    icon: Icons.favorite,
                                    count: likeCount,
                                    label: 'Likes',
                                    color: Colors.red,
                                  ),
                                  _buildStatChip(
                                    icon: Icons.bookmark,
                                    count: collectCount,
                                    label: 'Collects',
                                    color: Colors.blue,
                                  ),
                                  if (viewCount > 0)
                                    _buildStatChip(
                                      icon: Icons.visibility,
                                      count: viewCount,
                                      label: 'Views',
                                      color: Colors.green,
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Pin metadata
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Pin Details',
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Details grid with enhanced information
                        Row(
                          children: [
                            _buildDetailItem(
                              context,
                              icon: Icons.access_time_rounded,
                              title: 'Dropped',
                              value: _formatTimestamp(timestamp),
                            ),
                            _buildDetailItem(
                              context,
                              icon: Icons.location_on_outlined,
                              title: 'Location',
                              value: pin['locationName'] ?? 'Unknown Location',
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            _buildDetailItem(
                              context,
                              icon: Icons.music_note_outlined,
                              title: 'Service',
                              value: pin['service'] ?? 'Unknown',
                            ),
                            _buildDetailItem(
                              context,
                              icon: Icons.radio_button_checked,
                              title: 'Aura Range',
                              value: '${pin['aura_radius'] ?? 25}m',
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // Action buttons
          Padding(
            padding:
                EdgeInsets.fromLTRB(24, 16, 24, 24 + mediaQuery.padding.bottom),
            child: Column(
              children: [
                // Primary action button
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    // Update caption display for the playing pin
                    _updateCurrentlyPlayingPin(pin);

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Now playing: $title'),
                        behavior: SnackBarBehavior.floating,
                        action: SnackBarAction(
                          label: 'Show Caption',
                          onPressed: () {
                            if (caption.isNotEmpty) {
                              _showCaptionPopup(
                                  pin['id']?.toString() ?? '', caption);
                            }
                          },
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.play_circle_filled_rounded),
                  label: const Text('Play Music'),
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size.fromHeight(56),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                    backgroundColor: rarityColor,
                    foregroundColor: Colors.white,
                  ),
                ),

                const SizedBox(height: 12),

                // Secondary actions row
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Added to collection'),
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        },
                        icon: const Icon(Icons.bookmark_add_outlined),
                        label: const Text('Collect'),
                        style: OutlinedButton.styleFrom(
                          minimumSize: const Size.fromHeight(48),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Shared pin by @$username'),
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        },
                        icon: const Icon(Icons.share_rounded),
                        label: const Text('Share'),
                        style: OutlinedButton.styleFrom(
                          minimumSize: const Size.fromHeight(48),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build interaction stat chip
  Widget _buildStatChip({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            count.toString(),
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Get pin rarity color
  Color _getPinRarityColor(String rarity) {
    switch (rarity) {
      case 'legendary':
        return const Color(0xFFFFE55C); // Gold
      case 'epic':
        return const Color(0xFFE91E63); // Pink/Magenta
      case 'rare':
        return const Color(0xFF00E5FF); // Electric Blue
      case 'uncommon':
        return const Color(0xFF4CAF50); // Green
      default:
        return const Color(0xFF2196F3); // Blue
    }
  }

  // Format timestamp for display
  String _formatTimestamp(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays > 7) {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      } else if (difference.inDays > 0) {
        return '${difference.inDays}d ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}h ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}m ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return timestamp;
    }
  }

  // Show animated caption popup for the selected/playing pin
  void _showCaptionPopup(String pinId, String caption) {
    debugPrint(
        '🎵 [MapScreen] _showCaptionPopup called for pin: $pinId, caption: "$caption"');

    if (_visibleCaptionPopups[pinId] == true) {
      debugPrint('🎵 [MapScreen] Caption already visible for pin: $pinId');
      return; // Already visible
    }

    // Create animation controller if it doesn't exist
    if (!_captionAnimationControllers.containsKey(pinId)) {
      debugPrint(
          '🎵 [MapScreen] Creating new animation controller for pin: $pinId');
      final controller = AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      );

      final animation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.elasticOut,
      ));

      _captionAnimationControllers[pinId] = controller;
      _captionAnimations[pinId] = animation;
    } else {
      debugPrint(
          '🎵 [MapScreen] Using existing animation controller for pin: $pinId');
    }

    debugPrint(
        '🎵 [MapScreen] Setting caption visible and starting animation for pin: $pinId');
    _visibleCaptionPopups[pinId] = true;
    _captionAnimationControllers[pinId]?.forward();

    // Add haptic feedback
    HapticFeedback.lightImpact();

    if (mounted) {
      setState(() {});
      debugPrint(
          '🎵 [MapScreen] setState called to rebuild with caption for pin: $pinId');
    }

    // Auto-hide caption after 5 seconds
    Timer(const Duration(seconds: 5), () {
      debugPrint(
          '🎵 [MapScreen] Auto-hiding caption for pin: $pinId after 5 seconds');
      _hideCaptionPopup(pinId);
    });
  }

  // Hide animated caption popup
  void _hideCaptionPopup(String pinId) {
    if (_visibleCaptionPopups[pinId] != true) return; // Already hidden

    _captionAnimationControllers[pinId]?.reverse().then((_) {
      if (mounted) {
        setState(() {
          _visibleCaptionPopups[pinId] = false;
        });
      }
    });
  }

  // Build caption popup overlay for currently playing pin (using PinProvider data)
  Widget _buildCaptionPopup(String? currentlyPlayingPinId,
      Map<String, dynamic>? currentlyPlayingPinData) {
    debugPrint('🎵 [MapScreen] _buildCaptionPopup called');
    debugPrint(
        '🎵 [MapScreen] - currentlyPlayingPinId: $currentlyPlayingPinId');
    debugPrint(
        '🎵 [MapScreen] - currentlyPlayingPinData: ${currentlyPlayingPinData != null ? 'not null' : 'null'}');

    if (currentlyPlayingPinId == null || currentlyPlayingPinData == null) {
      debugPrint('🎵 [MapScreen] ❌ No currently playing pin or pin data');
      return const SizedBox.shrink();
    }

    // Safely extract caption with null checks and type safety
    final caption = currentlyPlayingPinData['caption'] is String
        ? currentlyPlayingPinData['caption'] as String
        : currentlyPlayingPinData['description'] is String
            ? currentlyPlayingPinData['description'] as String
            : '';

    debugPrint('🎵 [MapScreen] - caption: "$caption"');
    debugPrint(
        '🎵 [MapScreen] - _visibleCaptionPopups[$currentlyPlayingPinId]: ${_visibleCaptionPopups[currentlyPlayingPinId]}');

    if (caption.isEmpty ||
        _visibleCaptionPopups[currentlyPlayingPinId] != true) {
      debugPrint('🎵 [MapScreen] ❌ Caption is empty or not marked as visible');
      return const SizedBox.shrink();
    }

    final animation = _captionAnimations[currentlyPlayingPinId];
    debugPrint(
        '🎵 [MapScreen] - animation: ${animation != null ? 'not null' : 'null'}');

    if (animation == null) {
      debugPrint('🎵 [MapScreen] ❌ No animation found for pin');
      return const SizedBox.shrink();
    }

    print('upvote_count: ${currentlyPlayingPinData['upvote_count']}');
    print('downvote_count: ${currentlyPlayingPinData['downvote_count']}');
    print('upvote_count_type: ${currentlyPlayingPinData['upvote_count'].runtimeType}');
    print('downvote_count_type: ${currentlyPlayingPinData['downvote_count'].runtimeType}');
    
    // Ensure vote counts are integers to prevent type errors
    final upvotes = currentlyPlayingPinData['upvote_count'] is int
        ? currentlyPlayingPinData['upvote_count'] as int
        : currentlyPlayingPinData['upvote_count'] is String
            ? int.tryParse(currentlyPlayingPinData['upvote_count'] as String) ?? 0
            : 0;
            
    final downvotes = currentlyPlayingPinData['downvote_count'] is int
        ? currentlyPlayingPinData['downvote_count'] as int
        : currentlyPlayingPinData['downvote_count'] is String
            ? int.tryParse(currentlyPlayingPinData['downvote_count'] as String) ?? 0
            : 0;
            
    debugPrint('🎵 [MapScreen] - Vote counts - up: $upvotes, down: $downvotes');

    debugPrint(
        '🎵 [MapScreen] ✅ Building caption popup widget for pin: $currentlyPlayingPinId');

    return Positioned(
      top: 120, // Position below the pins in aura indicator
      left: 16,
      right: 16,
      child: AnimatedBuilder(
        animation: animation,
        builder: (context, child) {
          return Transform.scale(
            scale: animation.value.clamp(0.0, 1.2),
            child: Transform.translate(
              offset: Offset(0, (1 - animation.value.clamp(0.0, 1.0)) * -20),
              child: Opacity(
                opacity: animation.value.clamp(0.0, 1.0),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 32),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.black.withOpacity(0.8),
                        Colors.grey[900]!.withOpacity(0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.4),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                      BoxShadow(
                        color: Colors.cyan.withOpacity(0.2 * animation.value),
                        blurRadius: 30,
                        offset: const Offset(0, 0),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.cyan,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.cyan.withOpacity(0.6),
                              blurRadius: 6,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Now Playing Note',
                              style: TextStyle(
                                color: Colors.cyan.withOpacity(0.9),
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.5,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              caption,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                height: 1.3,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white70,
                          size: 20,
                        ),
                        onPressed: () =>
                            _hideCaptionPopup(currentlyPlayingPinId),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(
                          minWidth: 24,
                          minHeight: 24,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // Handle bottom navigation - Optimized for performance
  void _handleNavigationChange(int index) {
    // Immediately update the UI to feel responsive
    setState(() {
      _currentNavIndex = index;
    });

    // Handle navigation based on index
    switch (index) {
      case 0: // Profile
        NavigationHelper.navigateToScreen(context, '/profile').then((_) {
          if (mounted) {
            setState(() => _currentNavIndex = 2);
            // Defer heavy operations
            Future.microtask(() {
              final mapProvider =
                  Provider.of<MapProvider>(context, listen: false);
              mapProvider.resumeSubscriptions();
            });
          }
        });
        break;

      case 1: // Explore
        NavigationHelper.navigateToScreen(context, '/explore').then((_) {
          if (mounted) {
            setState(() => _currentNavIndex = 2);
            // Defer heavy operations
            Future.microtask(() {
              final mapProvider =
                  Provider.of<MapProvider>(context, listen: false);
              mapProvider.resumeSubscriptions();
            });
          }
        });
        break;

      case 2: // Map/Explore tab
        if (Navigator.canPop(context)) {
          Navigator.popUntil(context, (route) => route.isFirst);
        }

        // NEW: Check for pending completions when returning to map tab
        _checkForPendingCompletions();

        // Defer pin intent check to next frame
        Future.microtask(() {
          if (!mounted) return;
          final mapProvider = Provider.of<MapProvider>(context, listen: false);
          if (mapProvider.hasAddPinIntent) {
            mapProvider.setAddPinIntent(false);
          }
        });
        break;

      case 3: // Challenges
        NavigationHelper.navigateToScreen(context, '/challenges').then((_) {
          if (mounted) {
            setState(() => _currentNavIndex = 2);
            // Defer heavy operations
            Future.microtask(() {
              final mapProvider =
                  Provider.of<MapProvider>(context, listen: false);
              mapProvider.resumeSubscriptions();
            });
            // NEW: Check for pending completions when returning from challenges
            _checkForPendingCompletions();
          }
        });
        break;

      case 4: // Friends
        NavigationHelper.navigateToScreen(context, '/friends').then((_) {
          if (mounted) {
            setState(() => _currentNavIndex = 2);
            // Defer heavy operations
            Future.microtask(() {
              final mapProvider =
                  Provider.of<MapProvider>(context, listen: false);
              mapProvider.resumeSubscriptions();
            });
            // NEW: Check for pending completions when returning from friends
            _checkForPendingCompletions();
          }
        });
        break;
    }
  }

  // Cleanup method optimized to be non-blocking
  void _cleanupPendingNavigationJobs() {
    Future.microtask(() {
      _controlsFadeTimer?.cancel();
      if (_mapKey.currentState != null) {
        _mapKey.currentState!.preserveState();
      }
    });
  }

  // Add this method to handle proper cleanup when leaving the screen
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // If app is paused or inactive, clean up navigation state
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive) {
      _cleanupPendingNavigationJobs();

      // Also clear any pin intents when app goes to background
      final mapProvider = Provider.of<MapProvider>(context, listen: false);
      if (mapProvider.hasAddPinIntent) {
        debugPrint('📱 App going to background - clearing pin intent');
        mapProvider.forceResetPinIntent();
      }
    }

    // NEW: Check for pending completions when app resumes
    if (state == AppLifecycleState.resumed) {
      debugPrint('📱 App resumed - checking for pending challenge completions');
      _checkForPendingCompletions();
    }
  }

  // Handle search
  void _handleSearch(String query) {
    final searchProvider = Provider.of<SearchProvider>(context, listen: false);
    searchProvider.search(query);

    setState(() {
      _showSearchResults = query.isNotEmpty;
    });
  }

  // Close search results
  void _closeSearchResults() {
    setState(() {
      _showSearchResults = false;
    });

    // Clear search after animation completes
    Future.delayed(const Duration(milliseconds: 300), () {
      final searchProvider =
          Provider.of<SearchProvider>(context, listen: false);
      searchProvider.clearSearch();
    });
  }

  void _handleSearchBarExpanded() {
    // Optionally dim the background or pause certain animations while searching
    setState(() {
      // Set any state that should change when search is expanded
      // e.g., pause carousel animations, lower map opacity, etc.
    });

    // Log analytics event (would be implemented in a real app)
    debugPrint('Search bar expanded');
  }

  void _handleSearchBarCollapsed() {
    // Reset any state changes made when the search was expanded
    setState(() {
      // If you made any visual changes when search expanded, revert them here
    });

    // If search was active but now collapsed without selecting a result,
    // you might want to clear search in the provider
    if (_showSearchResults) {
      _closeSearchResults();
    }

    // Log analytics event (would be implemented in a real app)
    debugPrint('Search bar collapsed');
  }

  // Helper method to find the search bar in the widget tree
  ExpandableSearchBar? _findSearchBar(BuildContext context) {
    ExpandableSearchBar? searchBar;
    void visitor(Element element) {
      if (element.widget is ExpandableSearchBar) {
        searchBar = element.widget as ExpandableSearchBar;
      } else {
        element.visitChildren(visitor);
      }
    }

    context.visitChildElements(visitor);
    return searchBar;
  }

  // Helper method to programmatically expand the search bar
  void _expandSearchBar(ExpandableSearchBar searchBar) {
    // This would ideally call a method on the search bar's state
    // For now, we'll just trigger the search directly
    setState(() {
      _showSearchResults = true;
    });
  }

  // Location button and PlayPause button positioning
  void _togglePlayPause(bool isPlaying) async {
    final spotifyProvider =
        Provider.of<SpotifyProvider>(context, listen: false);
    final appleMusicProvider =
        Provider.of<AppleMusicProvider>(context, listen: false);

    if (isPlaying) {
      // Pause whichever service is currently playing
      if (spotifyProvider.currentTrack != null && spotifyProvider.isPlaying) {
        await spotifyProvider.pause();
      } else if (appleMusicProvider.currentTrack != null &&
          appleMusicProvider.isPlaying) {
        await appleMusicProvider.togglePlayPause();
      }
    } else {
      // Resume whichever service has a current track
      if (spotifyProvider.currentTrack != null) {
        await spotifyProvider.resume();
      } else if (appleMusicProvider.currentTrack != null) {
        await appleMusicProvider.togglePlayPause();
      }
    }

    setState(() {
      _isPlaying = spotifyProvider.isPlaying || appleMusicProvider.isPlaying;
    });

    // Provide haptic feedback
    HapticFeedback.mediumImpact();
  }

  // Enhanced play/pause with service prioritization
  void _togglePlayPauseWithService({
    required bool isPlaying,
    required bool useAppleMusic,
    required bool useSpotify,
    required bool useYouTube,
  }) async {
    final spotifyProvider =
        Provider.of<SpotifyProvider>(context, listen: false);
    final appleMusicProvider =
        Provider.of<AppleMusicProvider>(context, listen: false);
    final youtubeProvider =
        Provider.of<YouTubeProvider>(context, listen: false);

    if (isPlaying) {
      // Pause the active service
      if (useYouTube) {
        await youtubeProvider.pause();
      } else if (useAppleMusic) {
        await appleMusicProvider.togglePlayPause();
      } else if (useSpotify) {
        await spotifyProvider.pause();
      }
    } else {
      // Resume the active service
      if (useYouTube) {
        await youtubeProvider.play();
      } else if (useAppleMusic) {
        await appleMusicProvider.togglePlayPause();
      } else if (useSpotify) {
        await spotifyProvider.resume();
      }
    }

    setState(() {
      _isPlaying = spotifyProvider.isPlaying ||
          appleMusicProvider.isPlaying ||
          youtubeProvider.isPlaying;
    });

    // Provide haptic feedback
    HapticFeedback.mediumImpact();

    // NEW: ensure caption is shown when playback starts/resumes
    final pinProvider = Provider.of<PinProvider>(context, listen: false);
    if (!_isPlaying && pinProvider.hasCurrentlyPlayingPin) {
      final caption = pinProvider.getCurrentlyPlayingPinCaption();
      if (caption != null && caption.trim().isNotEmpty) {
        _showCaptionPopup(pinProvider.currentlyPlayingPinId!, caption);
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Obtain providers with listen:false once and store references
    _spotifyProviderRef = Provider.of<SpotifyProvider>(context, listen: false);
    _appleMusicProviderRef =
        Provider.of<AppleMusicProvider>(context, listen: false);
    _youtubeProviderRef = Provider.of<YouTubeProvider>(context, listen: false);
    _pinProviderRef = Provider.of<PinProvider>(context, listen: false);

    // Register listeners only once
    _spotifyProviderRef.addListener(_onMusicSourceChanged);
    _appleMusicProviderRef.addListener(_onMusicSourceChanged);
    _youtubeProviderRef.addListener(_onMusicSourceChanged);
    _pinProviderRef.addListener(_onPinProviderChanged);
    
    // Listen to Apple Music queue manager's current track stream
    _appleMusicQueueSubscription?.cancel(); // Cancel any existing subscription
    _appleMusicQueueSubscription = _appleMusicProviderRef.queueManager.currentTrackStream.listen((_) {
      if (mounted) {
        debugPrint('🎵 [MapScreen] Apple Music queue manager track changed - triggering music source change');
        _onMusicSourceChanged();
      }
    });
  }

  // Snapchat map control callbacks
  void _onSnapchatMapCreated(maplibre.MaplibreMapController controller) {
    _snapchatMapController = controller;
  }

  void _snapchatZoomIn() {
    _snapchatMapController?.animateCamera(maplibre.CameraUpdate.zoomIn());
  }

  void _snapchatZoomOut() {
    _snapchatMapController?.animateCamera(maplibre.CameraUpdate.zoomOut());
  }

  void _snapchatToggle3DBuildings() {
    setState(() {
      _show3DBuildings = !_show3DBuildings;
    });
    // The actual 3D building logic will be handled in the SnapchatStyleMapScreen
  }

  void _snapchatToggleVegetation() {
    setState(() {
      _showVegetation = !_showVegetation;
    });
    // The actual vegetation logic will be handled in the SnapchatStyleMapScreen
  }

  void _snapchatAdjustTilt() {
    if (_snapchatMapController != null) {
      // Get current camera position
      final currentTilt = _snapchatMapController!.cameraPosition?.tilt ?? 0.0;
      final newTilt = currentTilt < 30.0 ? 60.0 : 0.0;

      _snapchatMapController!.animateCamera(
        maplibre.CameraUpdate.tiltTo(newTilt),
        duration: const Duration(milliseconds: 500),
      );
    }
  }

  void _snapchatToggleFollowUser() {
    setState(() {
      _isFollowingUser = !_isFollowingUser;
    });

    // Always snap to user location when GPS button is pressed, regardless of follow state
    final mapProvider = Provider.of<MapProvider>(context, listen: false);
    if (mapProvider.currentPosition != null &&
        _snapchatMapController != null) {
      _snapchatMapController!.animateCamera(
        maplibre.CameraUpdate.newLatLng(
          maplibre.LatLng(
            mapProvider.currentPosition!.latitude,
            mapProvider.currentPosition!.longitude,
          ),
        ),
        duration: const Duration(milliseconds: 800),
      );
      debugPrint('📍 GPS button pressed - snapped to user location');
    } else {
      debugPrint('⚠️ GPS button pressed but no current position available');
    }
  }

  // Build glassmorphic control button
  Widget _buildGlassMorphicControlButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    bool isMainButton = false,
  }) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final userColor = themeProvider.primaryColor;

    return Tooltip(
      message: tooltip,
      child: Container(
        width: isMainButton ? 44 : 36,
        height: isMainButton ? 44 : 36,
        decoration: BoxDecoration(
          color: userColor,
          borderRadius: BorderRadius.circular(isMainButton ? 22 : 18),
          border: Border.all(
            color: userColor,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: userColor.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(isMainButton ? 22 : 18),
            onTap: () {
              HapticFeedback.lightImpact();
              onPressed();
            },
            child: Center(
              child: Icon(
                icon,
                size: isMainButton ? 24 : 20,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Show comment bottom sheet
  void _showCommentBottomSheet() {
    final pinProvider = Provider.of<PinProvider>(context, listen: false);

    // Get the current pin data for the comment sheet
    final currentPinId = pinProvider.currentlyPlayingPinId;
    final currentPinData = pinProvider.currentlyPlayingPinData;

    if (currentPinId != null && currentPinData != null) {
      // Extract pin ID as integer
      final pinId = int.tryParse(currentPinId) ?? (currentPinData['id'] ?? 1);

      // Get track title for context
      final trackTitle = currentPinData['title'] ??
          currentPinData['track_title'] ??
          'Unknown Track';
      final trackArtist = currentPinData['artist'] ??
          currentPinData['track_artist'] ??
          'Unknown Artist';
      final pinTitle = '$trackTitle • $trackArtist';

      // Show the separated comment bottom sheet
      CommentBottomSheet.show(
        context,
        pinId: pinId,
        pinTitle: pinTitle,
        onCommentPosted: () {
          // Optional: Handle any post-comment actions here
          debugPrint('🗨️ Comment posted for pin $pinId');
        },
      );
    } else {
      // Fallback: show with default pin ID
      debugPrint('⚠️ No current pin data available, using fallback');
      CommentBottomSheet.show(
        context,
        pinId: 1, // Default fallback
        pinTitle: 'Unknown Track',
      );
    }
  }

  // Helper method to build detail items with consistent styling
  Widget _buildDetailItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
  }) {
    final theme = Theme.of(context);

    return Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Show create pin bottom sheet
  void _showCreatePinBottomSheet() {
    debugPrint('🎵 Showing create pin bottom sheet');

    // Always provide haptic feedback regardless of state
    try {
      HapticFeedback.mediumImpact();
    } catch (e) {
      // Ignore haptic errors - not critical
    }

    // IMPORTANT: Force set initialization flags to ensure add pin works
    // This ensures the button works even if the app just initialized
    if (_isInitializing) {
      debugPrint('🔄 Forcing initialization completion to allow add pin');
      _isInitializing = false;
      _hasMapInitialized = true;
    }

    // Execute the pin creation flow immediately without any checks
    try {
      // Show track selection screen
      Navigator.of(context)
          .push(
        MaterialPageRoute(
          fullscreenDialog: true,
          builder: (context) => const CreatePinTrackSelectScreen(),
        ),
      )
          .then((result) {
        // Track selection and AR navigation is now handled in CreatePinTrackSelectScreen
        if (result == null) {
          debugPrint('ℹ️ Pin creation cancelled');
        }
      });
    } catch (e) {
      debugPrint('🔴 Error showing create pin bottom sheet: $e');

      // Show error message to user
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Could not create pin. Please try again.'),
          behavior: SnackBarBehavior.floating,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  // Stub for what's playing nearby button
  Widget _buildWhatsPlayingNearbyButton(
      BuildContext context, MapProvider mapProvider) {
    return const SizedBox.shrink();
  }

  // SIMPLIFIED: Legacy callbacks for backwards compatibility, but primary data flow is through PinProvider
  void _onPinHighlightStarted(String pinId) {
    debugPrint('🎵 [MapScreen] Legacy pin highlight started callback: $pinId');
    // The pin data should already be set in PinProvider by the bottomsheet, so we don't need to do much here
    // This callback is mainly for backwards compatibility with SnapchatStyleMapScreen
  }

  void _onPinHighlightStopped(String pinId) {
    debugPrint('🎵 [MapScreen] Legacy pin highlight stopped callback: $pinId');

    // Clear currently playing pin info and hide caption
    final pinProvider = Provider.of<PinProvider>(context, listen: false);
    if (pinProvider.currentlyPlayingPinId == pinId) {
      _hideCaptionPopup(pinId);
      pinProvider.clearCurrentlyPlayingPin();
      debugPrint('🎵 [MapScreen] Cleared currently playing pin: $pinId');
    }
  }

  void _onPinProviderChanged() {
    if (!mounted) return;

    final pinProvider = Provider.of<PinProvider>(context, listen: false);

    debugPrint(
        '🎵 [MapScreen] PinProvider changed - hasCurrentlyPlayingPin: ${pinProvider.hasCurrentlyPlayingPin}');

    if (pinProvider.hasCurrentlyPlayingPin) {
      final pinId = pinProvider.currentlyPlayingPinId!;
      final caption = pinProvider.getCurrentlyPlayingPinCaption();

      debugPrint(
          '🎵 [MapScreen] Pin set in provider: $pinId, caption: "$caption"');

      // Load fresh vote data for YouTube panel
      _loadYouTubeVoteData(pinId);

      if (caption != null && caption.trim().isNotEmpty) {
        debugPrint('🎵 [MapScreen] Auto-showing caption for pin: $pinId');
        _showCaptionPopup(pinId, caption);
      }
    } else {
      // Clear all captions when no pin is playing
      debugPrint('🎵 [MapScreen] No pin playing - clearing all captions');
      for (final pinId in _visibleCaptionPopups.keys.toList()) {
        _hideCaptionPopup(pinId);
      }
    }
  }

  // Timer to delay clearing pin data when non-pin music is detected
  Timer? _clearPinDataTimer;

  // Helper method for improved cross-service track matching
  bool _isTrackMatch(String pinTitle, String pinArtist, String trackTitle, String trackArtist) {
    // Normalize titles by removing common variations
    String normalizeTitle(String title) {
      return title
          .replaceAll(RegExp(r'\s*\([^)]*\)\s*'), '') // Remove content in parentheses
          .replaceAll(RegExp(r'\s*\[[^\]]*\]\s*'), '') // Remove content in brackets
          .replaceAll(RegExp(r'\s*feat\.?\s*[^,]*', caseSensitive: false), '') // Remove "feat." parts
          .replaceAll(RegExp(r'\s*ft\.?\s*[^,]*', caseSensitive: false), '') // Remove "ft." parts
          .replaceAll(RegExp(r'\s*with\s*[^,]*', caseSensitive: false), '') // Remove "with" parts
          .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
          .trim();
    }

    // Normalize artists by removing common variations
    String normalizeArtist(String artist) {
      return artist
          .replaceAll(RegExp(r'\s*feat\.?\s*', caseSensitive: false), ', ') // Replace "feat." with comma
          .replaceAll(RegExp(r'\s*ft\.?\s*', caseSensitive: false), ', ') // Replace "ft." with comma
          .replaceAll(RegExp(r'\s*with\s*', caseSensitive: false), ', ') // Replace "with" with comma
          .replaceAll(RegExp(r'\s*&\s*'), ', ') // Replace "&" with comma
          .replaceAll(RegExp(r'\s*,\s*'), ', ') // Normalize comma spacing
          .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
          .trim();
    }

    final normalizedPinTitle = normalizeTitle(pinTitle);
    final normalizedTrackTitle = normalizeTitle(trackTitle);
    final normalizedPinArtist = normalizeArtist(pinArtist);
    final normalizedTrackArtist = normalizeArtist(trackArtist);

    // Check if normalized titles match
    final titleMatch = normalizedPinTitle == normalizedTrackTitle ||
        normalizedPinTitle.contains(normalizedTrackTitle) ||
        normalizedTrackTitle.contains(normalizedPinTitle);

    // Check if artists match (more flexible - check if any artist from one list is in the other)
    final pinArtists = normalizedPinArtist.split(',').map((a) => a.trim()).where((a) => a.isNotEmpty).toList();
    final trackArtists = normalizedTrackArtist.split(',').map((a) => a.trim()).where((a) => a.isNotEmpty).toList();

    bool artistMatch = false;
    for (final pinArt in pinArtists) {
      for (final trackArt in trackArtists) {
        if (pinArt == trackArt ||
            pinArt.contains(trackArt) ||
            trackArt.contains(pinArt)) {
          artistMatch = true;
          break;
        }
      }
      if (artistMatch) break;
    }

    final result = titleMatch && artistMatch;
    if (result) {
      debugPrint('🎵 [MapScreen] Track match found:');
      debugPrint('  - Pin: "$normalizedPinTitle" by "$normalizedPinArtist"');
      debugPrint('  - Track: "$normalizedTrackTitle" by "$normalizedTrackArtist"');
    }

    return result;
  }

  void _onMusicSourceChanged() {
    debugPrint('🎵 [MapScreen] _onMusicSourceChanged() called');
    final pinProvider = Provider.of<PinProvider>(context, listen: false);

    if (!mounted || !pinProvider.hasCurrentlyPlayingPin) {
      debugPrint('🎵 [MapScreen] Early return - mounted: $mounted, hasPin: ${pinProvider.hasCurrentlyPlayingPin}');
      return;
    }
    
    debugPrint('🎵 [MapScreen] Processing music source change for pin: ${pinProvider.currentlyPlayingPinId}');

    final spotifyProvider =
        Provider.of<SpotifyProvider>(context, listen: false);
    final appleMusicProvider =
        Provider.of<AppleMusicProvider>(context, listen: false);
    final youtubeProvider =
        Provider.of<YouTubeProvider>(context, listen: false);

    final currentSpotifyTrack = spotifyProvider.currentTrack;
    final currentAppleMusicTrack = appleMusicProvider.queueManager.currentTrack;
    final currentYoutubeTrack = youtubeProvider.currentTrack;
    
    debugPrint('🎵 [MapScreen] Current tracks:');
    debugPrint('  - Spotify: ${currentSpotifyTrack?.title} (${currentSpotifyTrack?.id})');
    debugPrint('  - Apple Music: ${currentAppleMusicTrack?.title} (${currentAppleMusicTrack?.id})');
    debugPrint('  - YouTube: ${currentYoutubeTrack?.title}');

    final currentlyPlayingPinData = pinProvider.currentlyPlayingPinData!;
    final pinService = currentlyPlayingPinData['service'];
    final pinTrackUrl = currentlyPlayingPinData['track_url'] as String?;
    final pinTitle = currentlyPlayingPinData['title'] ?? currentlyPlayingPinData['track_title'] ?? '';
    final pinArtist = currentlyPlayingPinData['artist'] ?? currentlyPlayingPinData['track_artist'] ?? '';
    
    debugPrint('🎵 [MapScreen] Pin data:');
    debugPrint('  - Pin ID: ${currentlyPlayingPinData['id']}');
    debugPrint('  - Service: $pinService');
    debugPrint('  - Title: $pinTitle');
    debugPrint('  - Artist: $pinArtist');
    debugPrint('  - URL: $pinTrackUrl');
    debugPrint('  - Original URL: ${currentlyPlayingPinData['original_url']}');
    
    // Handle null track URL case - we still need to check if other music is playing
    // that would require clearing the pin data
    if (pinTrackUrl == null) {
      debugPrint('🎵 [MapScreen] Pin track URL is null - will check if other music is playing');
    }

    String? pinTrackId;
    if (pinTrackUrl != null) {
      if (pinService == 'spotify') {
        // Basic parsing, assuming format is '.../track/TRACK_ID'
        final uri = Uri.tryParse(pinTrackUrl);
        if (uri != null &&
            uri.pathSegments.length > 1 &&
            uri.pathSegments[uri.pathSegments.length - 2] == 'track') {
          pinTrackId = uri.pathSegments.last;
        }
      } else if (pinService == 'applemusic' || pinService == 'apple' || pinService == 'apple_music') {
        // Extract Apple Music track ID from URL or URI for reliable matching
        pinTrackId = _extractAppleMusicTrackId(pinTrackUrl) ?? pinTrackUrl;
      } else if (pinService == 'youtube') {
        pinTrackId = pinTrackUrl; // Assuming the url is the id
      }
    } else {
      // For pins with null URLs, we'll rely on title/artist matching only
      debugPrint('🎵 [MapScreen] No track URL available - will use title/artist matching only');
    }

    bool isPinCurrentlyPlaying = false;
    
    debugPrint('🎵 [MapScreen] Cross-service matching check:');
    debugPrint('  - Pin: "$pinTitle" by "$pinArtist" (service: $pinService)');
    
    // Check if the currently playing track matches the pin's service and track
    if (pinService == 'spotify' &&
        spotifyProvider.isConnected &&
        currentSpotifyTrack != null) {
      if (currentSpotifyTrack.id == pinTrackId) {
        isPinCurrentlyPlaying = true;
        debugPrint('🎵 [MapScreen] Spotify match by ID');
      }
    } else if ((pinService == 'applemusic' || pinService == 'apple' || pinService == 'apple_music') &&
         appleMusicProvider.isConnected &&
         currentAppleMusicTrack != null) {
       debugPrint('  - Apple Music playing: "${currentAppleMusicTrack.title}" by "${currentAppleMusicTrack.artist}"');
       
       // Check for exact URL/ID match first
       if ((pinTrackId != null && currentAppleMusicTrack.id == pinTrackId) ||
           (pinTrackUrl != null && currentAppleMusicTrack.url.isNotEmpty && 
            currentAppleMusicTrack.url == pinTrackUrl)) {
         debugPrint('🎵 [MapScreen] Apple Music match by ID/URL');
         isPinCurrentlyPlaying = true;
       } 
       // Then try title/artist matching if we have both
       else if (pinTitle.isNotEmpty && pinArtist.isNotEmpty) {
         final trackTitle = currentAppleMusicTrack.title.toLowerCase().trim();
         final trackArtist = currentAppleMusicTrack.artist.toLowerCase().trim();
         final pinTitleLower = pinTitle.toLowerCase().trim();
         final pinArtistLower = pinArtist.toLowerCase().trim();

         // Use improved matching logic
         if (_isTrackMatch(pinTitleLower, pinArtistLower, trackTitle, trackArtist)) {
           debugPrint('🎵 [MapScreen] Apple Music match by title/artist');
           isPinCurrentlyPlaying = true;
         } else {
           debugPrint('🎵 [MapScreen] No Apple Music match - Title/artist mismatch');
         }
       }
    } else if (pinService == 'youtube' &&
        youtubeProvider.isInitialized &&
        currentYoutubeTrack != null) {
      debugPrint('  - YouTube playing: "${currentYoutubeTrack.title}" by "${currentYoutubeTrack.artist}"');
      
      if (pinTitle.isNotEmpty && pinArtist.isNotEmpty) {
        final trackTitle = currentYoutubeTrack.title.toLowerCase().trim();
        final trackArtist = currentYoutubeTrack.artist.toLowerCase().trim();
        final pinTitleLower = pinTitle.toLowerCase().trim();
        final pinArtistLower = pinArtist.toLowerCase().trim();

        // Use improved matching logic
        if (_isTrackMatch(pinTitleLower, pinArtistLower, trackTitle, trackArtist)) {
          isPinCurrentlyPlaying = true;
          debugPrint('🎵 [MapScreen] YouTube match by title/artist');
        } else {
          debugPrint('🎵 [MapScreen] No YouTube match - Title/artist mismatch');
        }
      }
    }
    
    // CROSS-SERVICE MATCHING: Check if any other service is playing the same song
    if (!isPinCurrentlyPlaying && pinTitle.isNotEmpty && pinArtist.isNotEmpty) {
      final pinTitleLower = pinTitle.toLowerCase().trim();
      final pinArtistLower = pinArtist.toLowerCase().trim();
      
      // Check Apple Music if pin is not Apple Music
      if (pinService != 'applemusic' && pinService != 'apple' && pinService != 'apple_music' &&
          appleMusicProvider.isConnected && currentAppleMusicTrack != null) {
        final trackTitle = currentAppleMusicTrack.title.toLowerCase().trim();
        final trackArtist = currentAppleMusicTrack.artist.toLowerCase().trim();

        if (_isTrackMatch(pinTitleLower, pinArtistLower, trackTitle, trackArtist)) {
          isPinCurrentlyPlaying = true;
          debugPrint('🎵 [MapScreen] ✨ CROSS-SERVICE MATCH: Apple Music playing same song as ${pinService} pin');
        }
      }
      
      // Check Spotify if pin is not Spotify
      if (!isPinCurrentlyPlaying && pinService != 'spotify' &&
          spotifyProvider.isConnected && currentSpotifyTrack != null) {
        final trackTitle = currentSpotifyTrack.title.toLowerCase().trim();
        final trackArtist = currentSpotifyTrack.artist.toLowerCase().trim();

        if (_isTrackMatch(pinTitleLower, pinArtistLower, trackTitle, trackArtist)) {
          isPinCurrentlyPlaying = true;
          debugPrint('🎵 [MapScreen] ✨ CROSS-SERVICE MATCH: Spotify playing same song as ${pinService} pin');
        }
      }
      
      // Check YouTube if pin is not YouTube
      if (!isPinCurrentlyPlaying && pinService != 'youtube' &&
          youtubeProvider.isInitialized && currentYoutubeTrack != null) {
        final trackTitle = currentYoutubeTrack.title.toLowerCase().trim();
        final trackArtist = currentYoutubeTrack.artist.toLowerCase().trim();

        if (_isTrackMatch(pinTitleLower, pinArtistLower, trackTitle, trackArtist)) {
          isPinCurrentlyPlaying = true;
          debugPrint('🎵 [MapScreen] ✨ CROSS-SERVICE MATCH: YouTube playing same song as ${pinService} pin');
        }
      }
    }

    debugPrint('🎵 [MapScreen] Pin matching result: isPinCurrentlyPlaying = $isPinCurrentlyPlaying');
    
    // Cancel any pending clear operation if the pin track is now playing
    if (isPinCurrentlyPlaying) {
      debugPrint('🎵 [MapScreen] Pin is currently playing - keeping pin data');
      _clearPinDataTimer?.cancel();
      _clearPinDataTimer = null;
      return;
    }

    // If a track is playing but it's not the pinned track, wait before clearing
    // This gives the pin track time to start playing
    final hasAnyMusicPlaying = (currentSpotifyTrack != null ||
            currentAppleMusicTrack != null ||
            currentYoutubeTrack != null);
            
    debugPrint('🎵 [MapScreen] hasAnyMusicPlaying: $hasAnyMusicPlaying');
    
    // Special case: If no music is playing at all, keep the pin UI
    // This handles the case where a pin was playing and then stopped
    if (!hasAnyMusicPlaying) {
      debugPrint('🎵 [MapScreen] No music is currently playing - keeping pin UI');
      // Don't clear the pin UI if no music is playing
      // This allows the pin UI to persist when music is paused/stopped
      return;
    }
    
    // Special case: if pin track URL is null and any music is playing, we should clear the pin
    if (pinTrackUrl == null && hasAnyMusicPlaying) {
      debugPrint('🎵 [MapScreen] Pin has null track URL and music is playing - should clear pin');
      isPinCurrentlyPlaying = false;
    }
    
    if (!isPinCurrentlyPlaying && hasAnyMusicPlaying) {
      // If a timer is already pending, don't schedule a new one.
      // This prevents spamming timers during rapid state changes.
      if (_clearPinDataTimer?.isActive ?? false) {
        debugPrint('🎵 [MapScreen] Clear timer already active - skipping');
        return;
      }

      // Schedule clearing pin data after a delay to allow pin track to start
      debugPrint('🎵 [MapScreen] Scheduling pin data clear in 3 seconds...');
      _clearPinDataTimer = Timer(const Duration(seconds: 3), () {
        debugPrint('🎵 [MapScreen] ⏰ Timer fired - checking if pin should be cleared');

        if (!mounted) {
          return;
        }

        // Re-check if pin track is still not playing
        final currentPinProvider =
            Provider.of<PinProvider>(context, listen: false);

        if (!currentPinProvider.hasCurrentlyPlayingPin) {
          return; // Already cleared
        }

        final currentSpotifyProvider =
            Provider.of<SpotifyProvider>(context, listen: false);
        final currentAppleMusicProvider =
            Provider.of<AppleMusicProvider>(context, listen: false);
        final currentYoutubeProvider =
            Provider.of<YouTubeProvider>(context, listen: false);

        final latestSpotifyTrack = currentSpotifyProvider.currentTrack;
        final latestAppleMusicTrack = currentAppleMusicProvider.queueManager.currentTrack;
        final latestYoutubeTrack = currentYoutubeProvider.currentTrack;

        // Check again if pin track is playing
        bool isStillPinNotPlaying = true;

        // Check if services are connected and playing
        final spotifyConnected = currentSpotifyProvider.isConnected;
        final appleMusicConnected = currentAppleMusicProvider.isConnected;
        final youtubeConnected = currentYoutubeProvider.isInitialized;

        // Flexible track matching across services
        final pinTitle = currentlyPlayingPinData?['title'] ??
            currentlyPlayingPinData?['track_title'] ??
            '';
        final pinArtist = currentlyPlayingPinData?['artist'] ??
            currentlyPlayingPinData?['track_artist'] ??
            '';

        if (pinTitle.isNotEmpty && pinArtist.isNotEmpty) {
          bool matchFound = false;

          // Check Spotify first
          if (pinService == 'spotify' &&
              spotifyConnected &&
              latestSpotifyTrack != null &&
              latestSpotifyTrack.id == pinTrackId) {
            matchFound = true;
            debugPrint('🎵 ⏰ Pin track IS playing on Spotify');
          }

          // Check Apple Music with title/artist matching
          else if ((pinService == 'applemusic' || pinService == 'apple' || pinService == 'apple_music') &&
                appleMusicConnected &&
                latestAppleMusicTrack != null) {
                
             debugPrint('🎵 ⏰ Timer Apple Music match check:');
             debugPrint('  - Pin: "$pinTitle" by "$pinArtist"');
             debugPrint('  - Playing: "${latestAppleMusicTrack.title}" by "${latestAppleMusicTrack.artist}"');
             
             // First try exact ID/URL match
             if ((pinTrackId != null && latestAppleMusicTrack.id == pinTrackId) ||
                 (pinTrackUrl != null && latestAppleMusicTrack.url.isNotEmpty && latestAppleMusicTrack.url == pinTrackUrl)) {
               matchFound = true;
               debugPrint('🎵 ⏰ Pin track IS playing on Apple Music (matched by ID/URL)');
             }
             // Then try title/artist matching
             else {
               final trackTitle = latestAppleMusicTrack.title.toLowerCase().trim();
               final trackArtist = latestAppleMusicTrack.artist.toLowerCase().trim();
               final pinTitleLower = pinTitle.toLowerCase().trim();
               final pinArtistLower = pinArtist.toLowerCase().trim();
               
               if ((trackTitle.contains(pinTitleLower) || pinTitleLower.contains(trackTitle)) &&
                   (trackArtist.contains(pinArtistLower) || pinArtistLower.contains(trackArtist))) {
                 matchFound = true;
                 debugPrint('🎵 ⏰ Pin track IS playing on Apple Music (matched by title/artist)');
               } else {
                 debugPrint('🎵 ⏰ Apple Music track title/artist mismatch');
               }
             }
           }

          // Check YouTube with flexible matching
          else if (youtubeConnected && latestYoutubeTrack != null) {
            final trackTitle = latestYoutubeTrack.title.toLowerCase().trim();
            final trackArtist = latestYoutubeTrack.artist.toLowerCase().trim();
            final pinTitleLower = pinTitle.toLowerCase().trim();
            final pinArtistLower = pinArtist.toLowerCase().trim();

            // Check if titles and artists match (allowing for slight variations)
            if ((trackTitle.contains(pinTitleLower) ||
                    pinTitleLower.contains(trackTitle)) &&
                (trackArtist.contains(pinArtistLower) ||
                    pinArtistLower.contains(trackArtist))) {
              matchFound = true;
              debugPrint(
                  '🎵 ⏰ Pin track IS playing on YouTube (matched by title/artist)');
            } else {
              debugPrint(
                  '🎵 ⏰ YouTube track title/artist mismatch - Track: "$trackTitle" by "$trackArtist", Pin: "$pinTitleLower" by "$pinArtistLower"');
            }
          }

          // Update playing state based on match
          isStillPinNotPlaying = !matchFound;
        }

        final hasAnyMusic = (latestSpotifyTrack != null ||
            latestAppleMusicTrack != null ||
            latestYoutubeTrack != null);

        if (isStillPinNotPlaying && hasAnyMusic) {
          final currentlyPlayingPinId =
              currentPinProvider.currentlyPlayingPinId;
          if (currentlyPlayingPinId != null) {
            _hideCaptionPopup(currentlyPlayingPinId);
          }
          currentPinProvider.clearCurrentlyPlayingPin();
        } else if (!hasAnyMusic) {
          debugPrint('🎵 ⏰ No music playing anymore - keeping pin data');
        } else {
          debugPrint(
              '🎵 ⏰ Pin track started playing during delay - keeping pin data');
        }

        _clearPinDataTimer = null;
      });
    }
  }

  /// Extract Apple Music track ID from a URL or URI
  String? _extractAppleMusicTrackId(String url) {
    try {
      if (url.isEmpty) return null;
      // Handle MusicKit URI format like 'apple:track:123456789'
      if (url.startsWith('apple:')) {
        final parts = url.split(':');
        return parts.isNotEmpty ? parts.last : null;
      }
      final uri = Uri.tryParse(url);
      if (uri == null) return null;
      // Check for '?i=' query parameter used by Apple Music share links
      if (uri.queryParameters.containsKey('i')) {
        return uri.queryParameters['i'];
      }
      // Fallback: return the last numeric path segment if present
      for (final segment in uri.pathSegments.reversed) {
        if (RegExp(r'^\d+ ?$').hasMatch(segment)) {
          return segment;
        }
      }
      return null;
    } catch (_) {
      return null;
    }
  }

  // Build YouTube comprehensive info panel for bottom right
  Widget _buildYouTubeInfoPanel(
      MusicTrack youtubeTrack, bool isPlaying, PinProvider pinProvider) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final userColor = themeProvider.primaryColor;
    
    final currentlyPlayingPinData = pinProvider.currentlyPlayingPinData;
    final currentPinId = pinProvider.currentlyPlayingPinId;

    // Use fresh data from engagement service if available, otherwise fallback to pin data
    final owner =
        currentlyPlayingPinData?['owner'] as Map<String, dynamic>? ?? {};
    final username = _youtubeUsername ??
        owner['username'] ??
        currentlyPlayingPinData?['username'] ??
        currentlyPlayingPinData?['owner_name'] ??
        'Anonymous';
    final userProfilePic = _youtubeUserProfilePic ??
        owner['profile_pic'] ??
        currentlyPlayingPinData?['profile_pic'] ??
        '';
    final userDisplayName = _youtubeUserDisplayName ??
        owner['display_name'] ??
        owner['name'] ??
        username;

    // Use fresh interaction counts from engagement service
    final likeCount = _youtubeUpvotes;
    final dislikeCount = _youtubeDownvotes;
    final commentCount = _youtubeCommentCount;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            userColor.withOpacity(0.35), // Increased from 0.25 for consistency with panel
            userColor.withOpacity(0.22), // Increased from 0.12 for better visibility
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.4), // Increased from 0.3 for better definition
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2), // Increased from 0.15 for better depth
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Track information and user details
            Container(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Top row: Album art and track info - cleaner without user info
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Album art
                      Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.25),
                            width: 1,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(7),
                          child: youtubeTrack.albumArt.isNotEmpty
                              ? Image.network(
                                  youtubeTrack.albumArt,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) =>
                                      Container(
                                    color: Colors.white.withOpacity(0.2),
                                    child: const Icon(
                                      Icons.music_note,
                                      color: Colors.white,
                                      size: 18,
                                    ),
                                  ),
                                )
                              : Container(
                                  color: Colors.white.withOpacity(0.2),
                                  child: const Icon(
                                    Icons.music_note,
                                    color: Colors.white,
                                    size: 18,
                                  ),
                                ),
                        ),
                      ),
                      
                      const SizedBox(width: 10),
                      
                      // Track info column - now takes full remaining space with overflow protection
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Title with auto-scroll and overflow protection
                            SizedBox(
                              height: 16, // Fixed height to prevent layout shifts
                              child: AutoScrollText(
                              youtubeTrack.title,
                              style: const TextStyle(
                                color: Colors.white,
                                  fontSize: 14,
                                fontWeight: FontWeight.w600,
                                  height: 1.2,
                              ),
                              mode: AutoScrollTextMode.endless,
                                velocity: const Velocity(pixelsPerSecond: Offset(8, 0))
                              ),
                            ),
                            const SizedBox(height: 2),
                            // Artist with overflow protection
                            Text(
                              youtubeTrack.artist,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.75),
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                height: 1.1,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                      
                  const SizedBox(height: 10),
                      
                  // Interaction buttons with user info on the right - responsive layout
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Left side: Interaction buttons - flexible but maintains minimum size
                      Flexible(
                        flex: 2,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildYouTubeInteractionButton(
                        icon: Icons.thumb_up,
                        count: likeCount,
                        color: _youtubeIsUpvoted == true
                            ? Colors.green
                            : Colors.white,
                        showCount: true,
                        isLoading: _youtubeIsLoading,
                        onPressed: () async {
                          if (_youtubeIsLoading ||
                              _youtubeEngagementService == null) return;

                          HapticFeedback.lightImpact();
                          if (currentPinId != null) {
                            setState(() {
                              _youtubeIsLoading = true;
                            });

                            try {
                              final pinIdInt = int.tryParse(currentPinId);
                              if (pinIdInt != null) {
                                print(
                                    '🎬 YouTube Panel: Attempting upvote for pin $pinIdInt');
                                final newStats =
                                    await _youtubeEngagementService!
                                        .upvotePin(pinIdInt);
                                print(
                                    '🎬 YouTube Panel: Upvote result - upvotes: ${newStats.upvotes}, downvotes: ${newStats.downvotes}, userVote: ${newStats.userVote}');
                                if (mounted) {
                                  setState(() {
                                    _youtubeUpvotes = newStats.upvotes;
                                    _youtubeDownvotes = newStats.downvotes;
                                    _youtubeIsUpvoted = newStats.userVote == 1
                                        ? true
                                        : newStats.userVote == -1
                                            ? false
                                            : null;
                                    _youtubeIsLoading = false;

                                    // Update user info if available
                                    if (newStats.pinInfo?['owner']
                                            ?['profile_pic'] !=
                                        null) {
                                      _youtubeUserProfilePic = newStats
                                          .pinInfo!['owner']['profile_pic'];
                                    }
                                    if (newStats.ownerUsername != null) {
                                      _youtubeUsername = newStats.ownerUsername;
                                    }
                                  });
                                  print(
                                      '🎬 YouTube Panel: State updated after upvote');
                                }
                              }
                            } catch (e) {
                              if (mounted) {
                                setState(() {
                                  _youtubeIsLoading = false;
                                });
                              }
                              print('❌ YouTube Panel: Upvote failed: $e');
                            }
                          }
                        },
                      ),
                            const SizedBox(width: 8),
                      _buildYouTubeInteractionButton(
                        icon: Icons.thumb_down,
                        count: dislikeCount,
                        color: _youtubeIsUpvoted == false
                            ? Colors.red
                            : Colors.white.withOpacity(0.7),
                        showCount: true,
                        isLoading: _youtubeIsLoading,
                        onPressed: () async {
                          if (_youtubeIsLoading ||
                              _youtubeEngagementService == null) return;

                          HapticFeedback.lightImpact();
                          if (currentPinId != null) {
                            setState(() {
                              _youtubeIsLoading = true;
                            });

                            try {
                              final pinIdInt = int.tryParse(currentPinId);
                              if (pinIdInt != null) {
                                print(
                                    '🎬 YouTube Panel: Attempting downvote for pin $pinIdInt');
                                final newStats =
                                    await _youtubeEngagementService!
                                        .downvotePin(pinIdInt);
                                print(
                                    '🎬 YouTube Panel: Downvote result - upvotes: ${newStats.upvotes}, downvotes: ${newStats.downvotes}, userVote: ${newStats.userVote}');
                                if (mounted) {
                                  setState(() {
                                    _youtubeUpvotes = newStats.upvotes;
                                    _youtubeDownvotes = newStats.downvotes;
                                    _youtubeIsUpvoted = newStats.userVote == 1
                                        ? true
                                        : newStats.userVote == -1
                                            ? false
                                            : null;
                                    _youtubeIsLoading = false;

                                    // Update user info if available
                                    if (newStats.pinInfo?['owner']
                                            ?['profile_pic'] !=
                                        null) {
                                      _youtubeUserProfilePic = newStats
                                          .pinInfo!['owner']['profile_pic'];
                                    }
                                    if (newStats.ownerUsername != null) {
                                      _youtubeUsername = newStats.ownerUsername;
                                    }
                                  });
                                  print(
                                      '🎬 YouTube Panel: State updated after downvote');
                                }
                              }
                            } catch (e) {
                              if (mounted) {
                                setState(() {
                                  _youtubeIsLoading = false;
                                });
                              }
                              print('❌ YouTube Panel: Downvote failed: $e');
                            }
                          }
                        },
                      ),
                            const SizedBox(width: 8),
                      _buildYouTubeInteractionButton(
                        icon: Icons.comment_outlined,
                        count: commentCount,
                        color: Colors.white.withOpacity(0.8),
                        showCount: commentCount > 0,
                        isLoading: false,
                        onPressed: _showCommentBottomSheet,
                      ),
                    ],
                  ),
                      ),

                      // Right side: User info (clickable) - flexible with constraints
                      Flexible(
                        flex: 1,
                        child: InkWell(
                          onTap: () => _navigateToUserProfile(currentlyPlayingPinData),
                          borderRadius: BorderRadius.circular(12),
                          child: Padding(
                            padding: const EdgeInsets.all(4),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // Profile pic with fixed size
                                Container(
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.3),
                                      width: 0.5,
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child: userProfilePic.isNotEmpty
                                        ? Image.network(
                                            userProfilePic,
                                            fit: BoxFit.cover,
                                            errorBuilder: (context, error, stackTrace) =>
                                                Container(
                                              color: Colors.white.withOpacity(0.2),
                                              child: const Icon(
                                                Icons.person,
                                                color: Colors.white,
                                                size: 12,
                                              ),
                                            ),
                                          )
                                        : Container(
                                            color: Colors.white.withOpacity(0.2),
                                            child: const Icon(
                                              Icons.person,
                                              color: Colors.white,
                                              size: 12,
                                            ),
                                          ),
                                  ),
                                ),
                                const SizedBox(width: 6),
                                // Username with overflow protection and flexible width
                                Flexible(
                                  child: Text(
                                    '@$username',
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.65),
                                      fontSize: 9,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    textAlign: TextAlign.right,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build YouTube interaction button
  Widget _buildYouTubeInteractionButton({
    required IconData icon,
    required int count,
    required Color color,
    required bool showCount,
    required bool isLoading,
    required VoidCallback onPressed,
  }) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final userColor = themeProvider.primaryColor;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                userColor.withOpacity(0.25), // Increased from 0.15 for consistency with panel
                userColor.withOpacity(0.12), // Increased from 0.05 for better visibility
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.white.withOpacity(0.3), // Increased from 0.2 for better definition
              width: 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15), // Increased from 0.1 for better depth
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              isLoading
                  ? SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 1.5,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.white.withOpacity(0.8),
                        ),
                      ),
                    )
                  : Icon(
                      icon,
                      size: 16,
                      color: color == Colors.green || color == Colors.red
                          ? color
                          : Colors.white.withOpacity(0.9),
                    ),
              if (showCount) ...[
                const SizedBox(width: 4),
                Text(
                  count.toString(),
                  style: TextStyle(
                    color: color == Colors.green || color == Colors.red
                        ? color
                        : Colors.white.withOpacity(0.9),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.2,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Navigate to user profile screen
  void _navigateToUserProfile(Map<String, dynamic>? currentlyPlayingPinData) {
    if (currentlyPlayingPinData == null) return;

    // Get user ID from pin data
    final owner = currentlyPlayingPinData['owner'] as Map<String, dynamic>? ?? {};
    final userId = owner['id'] ?? currentlyPlayingPinData['user_id'] ?? currentlyPlayingPinData['owner_id'];
    final username = _youtubeUsername ??
        owner['username'] ??
        currentlyPlayingPinData['username'] ??
        currentlyPlayingPinData['owner_name'] ??
        'Anonymous';

    // Skip navigation for demo users or invalid IDs
    final userIdStr = userId?.toString() ?? '0';
    if (userIdStr == '0' || userIdStr.isEmpty || userIdStr.startsWith('demo_')) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Profile for @$username - feature coming soon!'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }
    
    // Navigate to real user profile
    final userIdInt = int.tryParse(userIdStr) ?? 0;
    if (userIdInt > 0) {
      HapticFeedback.lightImpact();
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(
            userId: userIdInt,
            showBottomNav: false,
          ),
        ),
      );
    }
  }
}
