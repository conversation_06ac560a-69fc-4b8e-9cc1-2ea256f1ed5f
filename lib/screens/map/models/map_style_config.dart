import 'package:flutter/material.dart';

// Map style configuration
class MapStyleConfig {
  final String name;
  final String lightUrl;
  final String darkUrl;
  final IconData icon;
  final Color accentColor;
  final String? previewImagePath; // Optional preview image path
  final bool isAnimated; // Whether the map style has animations

  const MapStyleConfig({
    required this.name,
    required this.lightUrl,
    required this.darkUrl,
    required this.icon,
    required this.accentColor,
    this.previewImagePath,
    this.isAnimated = false,
  });
}