import 'package:flutter/material.dart';

// Matrix character class for digital rain effect
class MatrixCharacter {
  Offset position;
  String character;
  double opacity;
  double life;
  double maxLife;
  double speed;
  double trail; // Trail position for fading effect
  
  MatrixCharacter({
    required this.position,
    required this.character,
    required this.opacity,
    required this.life,
    required this.maxLife,
    required this.speed,
    required this.trail,
  });
  
  void update(double dt) {
    position = Offset(position.dx, position.dy + speed * dt);
    trail = position.dy - 30; // Trail behind main character
    life -= dt;
    opacity = (life / maxLife).clamp(0.0, 1.0);
  }
  
  bool get isDead => life <= 0;
}
