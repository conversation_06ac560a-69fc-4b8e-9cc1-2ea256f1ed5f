import 'package:flutter/material.dart';

// Retro VHS effect class
class RetroVHSEffect {
  Offset position;
  double width;
  double height;
  double opacity;
  double life;
  double maxLife;
  double speed;
  String type; // 'static', 'lines', 'tracking', 'color_bleed'
  
  RetroVHSEffect({
    required this.position,
    required this.width,
    required this.height,
    required this.opacity,
    required this.life,
    required this.maxLife,
    required this.speed,
    required this.type,
  });
  
  void update(double dt) {
    // Move effect for tracking errors
    if (type == 'tracking') {
      position = Offset(position.dx + speed * dt, position.dy);
    }
    life -= dt;
    opacity = (life / maxLife).clamp(0.0, 1.0);
  }
  
  bool get isDead => life <= 0;
}