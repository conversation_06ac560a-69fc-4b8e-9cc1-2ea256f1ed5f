import 'package:flutter/material.dart';

// Cyberpunk glitch effect class
class CyberpunkGlitch {
  Offset position;
  double width;
  double height;
  Color color;
  double opacity;
  double life;
  double maxLife;
  double intensity;
  String type; // 'scan', 'noise', 'chromatic', 'neon'
  
  CyberpunkGlitch({
    required this.position,
    required this.width,
    required this.height,
    required this.color,
    required this.opacity,
    required this.life,
    required this.maxLife,
    required this.intensity,
    required this.type,
  });
  
  void update(double dt) {
    life -= dt;
    opacity = (life / maxLife).clamp(0.0, 1.0) * intensity;
  }
  
  bool get isDead => life <= 0;
}