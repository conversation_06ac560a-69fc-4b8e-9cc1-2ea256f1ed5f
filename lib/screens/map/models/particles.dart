import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Weather particle class for animations
class Particle {
  Offset position;
  Offset velocity;
  double size;
  Color color;
  double opacity;
  double life;
  double maxLife;
  double z; // Depth for parallax effect
  
  Particle({
    required this.position,
    required this.velocity,
    required this.size,
    required this.color,
    required this.opacity,
    required this.life,
    required this.maxLife,
    required this.z,
  });
  
  void update(double dt, Offset wind) {
    // Apply wind and parallax effect based on z-depth
    final parallaxFactor = 0.5 + z * 0.8;
    position = Offset(
      position.dx + (velocity.dx + wind.dx) * parallaxFactor * dt,
      position.dy + (velocity.dy + wind.dy) * parallaxFactor * dt,
    );
    life -= dt;
    opacity = (life / maxLife).clamp(0.0, 1.0);
  }
  
  bool get isDead => life <= 0;
}