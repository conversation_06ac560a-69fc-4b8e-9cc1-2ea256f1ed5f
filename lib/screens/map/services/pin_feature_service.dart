import 'dart:isolate';
import 'dart:async';
import '../isolates/parallel_pin_feature_isolate.dart';

class PinFeatureService {
  static PinFeatureService? _instance;
  static PinFeatureService get instance => _instance ??= PinFeatureService._();
  
  PinFeatureService._();

  Isolate? _isolate;
  SendPort? _sendPort;
  bool _isInitialized = false;
  
  // Performance tracking
  final List<Duration> _processingTimes = [];
  int _totalPinsProcessed = 0;
  
  // Configuration
  int _chunkSize = 10; // Process pins in chunks of 10 for optimal performance

  // Initialize the parallel isolate
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final receivePort = ReceivePort();
      _isolate = await Isolate.spawn(parallelPinFeatureComputeIsolate, receivePort.sendPort);
      
      // Get the SendPort from the isolate
      final completer = Completer<SendPort>();
      receivePort.listen((message) {
        if (message is SendPort) {
          completer.complete(message);
        }
      });
      
      _sendPort = await completer.future;
      _isInitialized = true;
      
      print('🚀 Parallel pin feature isolate initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize parallel pin feature isolate: $e');
      rethrow;
    }
  }

  // Compute pin features using parallel isolate
  Future<List<Map<String, dynamic>>> computePinFeatures({
    required List<Map<String, dynamic>> pins,
    int? chunkSize,
  }) async {
    if (pins.isEmpty) return [];
    
    final startTime = DateTime.now();
    
    if (!_isInitialized || _sendPort == null) {
      await initialize();
    }

    if (_sendPort == null) {
      throw Exception('Parallel pin feature isolate not properly initialized');
    }

    try {
      final completer = Completer<List<Map<String, dynamic>>>();
      final responsePort = ReceivePort();
      
      // Listen for response
      responsePort.listen((response) {
        responsePort.close();
        final data = response as Map<String, dynamic>;
        
        if (data['success'] == true) {
          final features = (data['features'] as List)
              .cast<Map<String, dynamic>>();
          completer.complete(features);
        } else {
          completer.completeError(Exception(data['error']));
        }
      });

      // Send computation request to isolate with parallel processing
      _sendPort!.send({
        'pins': pins,
        'chunkSize': chunkSize ?? _chunkSize,
        'responsePort': responsePort.sendPort,
      });

      final result = await completer.future;
      
      // Track performance
      final processingTime = DateTime.now().difference(startTime);
      _processingTimes.add(processingTime);
      _totalPinsProcessed += pins.length;
      
      // Keep only recent performance data
      if (_processingTimes.length > 100) {
        _processingTimes.removeAt(0);
      }
      
      print('📊 Parallel processing: ${pins.length} pins in ${processingTime.inMilliseconds}ms');
      print('📊 Average processing time: ${getAverageProcessingTime().inMilliseconds}ms per batch');
      print('📊 Total pins processed: $_totalPinsProcessed');
      
      return result;
    } catch (e) {
      print('❌ Error computing pin features in parallel isolate: $e');
      rethrow;
    }
  }

  // Get performance statistics
  Duration getAverageProcessingTime() {
    if (_processingTimes.isEmpty) return Duration.zero;
    
    final totalMs = _processingTimes.fold<int>(
      0, 
      (sum, duration) => sum + duration.inMilliseconds,
    );
    
    return Duration(milliseconds: totalMs ~/ _processingTimes.length);
  }
  
  // Get processing statistics
  Map<String, dynamic> getPerformanceStats() {
    return {
      'totalPinsProcessed': _totalPinsProcessed,
      'averageProcessingTime': getAverageProcessingTime().inMilliseconds,
      'recentBatches': _processingTimes.length,
      'chunkSize': _chunkSize,
    };
  }
  
  // Update chunk size for optimization
  void updateChunkSize(int newChunkSize) {
    _chunkSize = newChunkSize;
    print('📈 Updated chunk size to $_chunkSize');
  }
  
  // Dispose the isolate
  void dispose() {
    _isolate?.kill();
    _isolate = null;
    _sendPort = null;
    _isInitialized = false;
    _processingTimes.clear();
    _totalPinsProcessed = 0;
    print('🛑 Parallel pin feature isolate disposed');
  }
}
