import 'dart:isolate';
import 'dart:async';
import '../isolates/cluster_isolate.dart';

class ClusterService {
  static ClusterService? _instance;
  static ClusterService get instance => _instance ??= ClusterService._();
  
  ClusterService._();

  Isolate? _isolate;
  SendPort? _sendPort;
  bool _isInitialized = false;

  // Initialize the isolate
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final receivePort = ReceivePort();
      _isolate = await Isolate.spawn(clusterComputeIsolate, receivePort.sendPort);
      
      // Get the SendPort from the isolate
      final completer = Completer<SendPort>();
      receivePort.listen((message) {
        if (message is SendPort) {
          completer.complete(message);
        }
      });
      
      _sendPort = await completer.future;
      _isInitialized = true;
      
      print('🚀 Cluster isolate initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize cluster isolate: $e');
      rethrow;
    }
  }

  // Compute clusters using isolate
  Future<List<Map<String, dynamic>>> computeClusters({
    required List<Map<String, dynamic>> pins,
    double clusterRadius = 100.0, // meters
    int minClusterSize = 2,
  }) async {
    if (!_isInitialized || _sendPort == null) {
      await initialize();
    }

    if (_sendPort == null) {
      throw Exception('Cluster isolate not properly initialized');
    }

    try {
      final completer = Completer<List<Map<String, dynamic>>>();
      final responsePort = ReceivePort();
      
      // Listen for response
      responsePort.listen((response) {
        responsePort.close();
        final data = response as Map<String, dynamic>;
        
        if (data['success'] == true) {
          final clusters = (data['clusters'] as List)
              .cast<Map<String, dynamic>>();
          completer.complete(clusters);
        } else {
          completer.completeError(Exception(data['error']));
        }
      });

      // Send computation request to isolate
      _sendPort!.send({
        'pins': pins,
        'clusterRadius': clusterRadius,
        'minClusterSize': minClusterSize,
        'responsePort': responsePort.sendPort,
      });

      return await completer.future;
    } catch (e) {
      print('❌ Error computing clusters in isolate: $e');
      rethrow;
    }
  }

  // Dispose the isolate
  void dispose() {
    _isolate?.kill();
    _isolate = null;
    _sendPort = null;
    _isInitialized = false;
    print('🛑 Cluster isolate disposed');
  }
}
