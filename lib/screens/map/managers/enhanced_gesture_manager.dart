import 'dart:async';
import 'package:flutter/foundation.dart';

/// Enhanced gesture management system to prevent gesture conflicts
/// Implements gesture priority system: pinch > pan > tap
class EnhancedGestureManager {
  static const Duration _gestureTimeout = Duration(milliseconds: 500);
  static const Duration _tapDebounceDelay = Duration(milliseconds: 200); // Increased for better pinch detection
  
  GestureState _currentGesture = GestureState.none;
  Timer? _gestureTimeoutTimer;
  Timer? _tapDebounceTimer;
  DateTime? _lastGestureEndTime;
  int _activeTouchPoints = 0;
  
  // Callbacks for gesture state changes
  VoidCallback? onGestureStateChanged;
  
  /// Current gesture state
  GestureState get currentGesture => _currentGesture;

  /// Current number of active touch points
  int get activeTouchPoints => _activeTouchPoints;
  
  /// Whether a tap gesture should be allowed
  bool get shouldAllowTap {
    // Don't allow tap if another gesture is active
    if (_currentGesture != GestureState.none) {
      debugPrint('🎯 Tap blocked - current gesture: $_currentGesture');
      return false;
    }
    
    // Don't allow tap immediately after other gestures
    if (_lastGestureEndTime != null) {
      final timeSinceLastGesture = DateTime.now().difference(_lastGestureEndTime!);
      if (timeSinceLastGesture < _tapDebounceDelay) {
        debugPrint('🎯 Tap blocked - debounce period (${timeSinceLastGesture.inMilliseconds}ms)');
        return false;
      }
    }
    
    // Don't allow tap if multiple touch points are active
    if (_activeTouchPoints > 1) {
      debugPrint('🎯 Tap blocked - multiple touch points: $_activeTouchPoints');
      return false;
    }
    
    return true;
  }
  
  /// Start a gesture with the specified type
  void startGesture(GestureType type) {
    final newState = GestureState.fromType(type);
    
    // Only update if it's a higher priority gesture or none is active
    if (_shouldAllowGestureTransition(_currentGesture, newState)) {
      debugPrint('🎯 Starting gesture: $type (was: $_currentGesture)');
      _currentGesture = newState;
      _resetGestureTimeout();
      onGestureStateChanged?.call();
    } else {
      debugPrint('🎯 Gesture blocked: $type (current: $_currentGesture has higher priority)');
    }
  }
  
  /// End the current gesture
  void endGesture(GestureType type) {
    final stateForType = GestureState.fromType(type);

    // Only end if it matches the current gesture
    if (_currentGesture == stateForType) {
      debugPrint('🎯 Ending gesture: $type');
      _currentGesture = GestureState.none;
      // For pan gestures, allow immediate taps; for pinch, add debounce
      if (type == GestureType.pan) {
        _lastGestureEndTime = null; // Allow immediate taps after pan
      } else {
        _lastGestureEndTime = DateTime.now(); // Add debounce for pinch
      }
      _cancelGestureTimeout();
      onGestureStateChanged?.call();
    }
  }
  
  /// Update the number of active touch points
  void updateTouchPoints(int count) {
    if (_activeTouchPoints != count) {
      debugPrint('🎯 Touch points changed: $_activeTouchPoints → $count');
      _activeTouchPoints = count;

      // Auto-detect pinch gesture based on touch points
      if (count >= 2 && _currentGesture != GestureState.pinching) {
        startGesture(GestureType.pinch);
      } else if (count < 2 && _currentGesture == GestureState.pinching) {
        endGesture(GestureType.pinch);
      }

      // Clear debounce time when returning to single touch for immediate tap availability
      if (count <= 1) {
        _lastGestureEndTime = null;
      }
    }
  }
  
  /// Force end all gestures (emergency reset)
  void forceEndAllGestures() {
    debugPrint('🎯 Force ending all gestures');
    _currentGesture = GestureState.none;
    _activeTouchPoints = 0;
    _lastGestureEndTime = null; // Clear debounce time for immediate availability
    _cancelGestureTimeout();
    onGestureStateChanged?.call();
  }
  
  /// Process a tap attempt with gesture validation
  bool processTapAttempt(String symbolId) {
    if (!shouldAllowTap) {
      return false;
    }

    // For immediate validation, check if we're clearly not in a pinch gesture
    if (_activeTouchPoints <= 1 && _currentGesture == GestureState.none) {
      debugPrint('🎯 Tap immediately validated and allowed for symbol: $symbolId');
      return true;
    }

    // Add a small delay to ensure no other gestures are starting
    _tapDebounceTimer?.cancel();
    _tapDebounceTimer = Timer(_tapDebounceDelay, () {
      if (shouldAllowTap) {
        debugPrint('🎯 Tap validated and allowed after debounce for symbol: $symbolId');
        // The actual tap processing will be handled by the caller
      }
    });

    return true;
  }

  /// Immediate tap validation without debounce (for quick response)
  bool canTapImmediately() {
    return _activeTouchPoints <= 1 &&
           _currentGesture == GestureState.none &&
           (_lastGestureEndTime == null ||
            DateTime.now().difference(_lastGestureEndTime!) > const Duration(milliseconds: 100));
  }
  
  /// Reset the gesture timeout timer
  void _resetGestureTimeout() {
    _cancelGestureTimeout();
    _gestureTimeoutTimer = Timer(_gestureTimeout, () {
      debugPrint('🎯 Gesture timeout - clearing state');
      _currentGesture = GestureState.none;
      _lastGestureEndTime = DateTime.now();
      onGestureStateChanged?.call();
    });
  }
  
  /// Cancel the gesture timeout timer
  void _cancelGestureTimeout() {
    _gestureTimeoutTimer?.cancel();
    _gestureTimeoutTimer = null;
  }
  
  /// Check if a gesture transition should be allowed based on priority
  bool _shouldAllowGestureTransition(GestureState current, GestureState new_) {
    // Always allow if no current gesture
    if (current == GestureState.none) return true;
    
    // Get priorities (lower number = higher priority)
    final currentPriority = _getGesturePriority(current);
    final newPriority = _getGesturePriority(new_);
    
    // Allow if new gesture has higher priority (lower number)
    return newPriority <= currentPriority;
  }
  
  /// Get gesture priority (lower number = higher priority)
  int _getGesturePriority(GestureState state) {
    switch (state) {
      case GestureState.pinching:
        return 1; // Highest priority
      case GestureState.panning:
        return 2; // Medium priority
      case GestureState.tapping:
        return 3; // Lowest priority
      case GestureState.none:
        return 999; // No priority
    }
  }
  
  /// Dispose of timers and resources
  void dispose() {
    _cancelGestureTimeout();
    _tapDebounceTimer?.cancel();
    _tapDebounceTimer = null;
    onGestureStateChanged = null;
    debugPrint('🎯 Enhanced gesture manager disposed');
  }
}

/// Types of gestures that can be detected
enum GestureType {
  tap,
  pan,
  pinch,
}

/// Current state of gesture handling
enum GestureState {
  none,
  tapping,
  panning,
  pinching;
  
  /// Create a gesture state from a gesture type
  static GestureState fromType(GestureType type) {
    switch (type) {
      case GestureType.tap:
        return GestureState.tapping;
      case GestureType.pan:
        return GestureState.panning;
      case GestureType.pinch:
        return GestureState.pinching;
    }
  }
}
