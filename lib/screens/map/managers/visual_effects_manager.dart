import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math' as math;

import '../constants/map_style.dart';
import '../models/particles.dart';
import '../models/matrix_character.dart';
import '../models/cyberpunk_glitch.dart';
import '../models/retro_vhs_effect.dart';

class VisualEffectsManager {
  final TickerProviderStateMixin vsync;
  final VoidCallback? onStateChanged;
  
  /// Tracks whether the map screen is currently visible to the user
  bool _isScreenVisible = true;
  
  /// Tracks whether the app is currently in the foreground
  bool _isAppInForeground = true;
  
  // Animation controllers
  late AnimationController _weatherAnimationController;
  late AnimationController _lightningFlashController;
  late AnimationController _matrixGlitchController;
  late AnimationController _cyberpunkGlitchController;
  late AnimationController _retroGlitchController;
  
  // Animations
  late Animation<double> _weatherAnimation;
  late Animation<double> _lightningFlashAnimation;
  late Animation<double> _matrixGlitchAnimation;
  late Animation<double> _cyberpunkGlitchAnimation;
  late Animation<double> _retroGlitchAnimation;
  
  // Weather effects
  final List<Particle> _weatherParticles = [];
  Timer? _weatherTimer;
  bool _weatherEnabled = true;
  String _currentWeather = 'clear'; // clear, rain, snow, clouds
  final Offset _wind = Offset(20, 0); // Wind from left to right
  
  // Lightning effects for rain
  bool _lightningFlashActive = false;
  Timer? _lightningTimer;
  final List<Particle> _lightningBolts = [];
  final double _lastLightningTime = 0;
  static const double _minLightningInterval = 15000; // 15 seconds minimum
  static const double _maxLightningInterval = 75000; // 75 seconds maximum
  
  // Matrix glitch effects
  final List<MatrixCharacter> _matrixCharacters = [];
  Timer? _matrixGlitchTimer;
  bool _matrixGlitchActive = false;
  static const double _minMatrixGlitchInterval = 15000; // 15 seconds minimum
  static const double _maxMatrixGlitchInterval = 75000; // 75 seconds maximum
  
  // Cyberpunk glitch effects
  final List<CyberpunkGlitch> _cyberpunkGlitches = [];
  Timer? _cyberpunkGlitchTimer;
  bool _cyberpunkGlitchActive = false;
  static const double _minCyberpunkGlitchInterval = 10000; // 10 seconds minimum - more frequent but subtle
  static const double _maxCyberpunkGlitchInterval = 60000; // 60 seconds maximum
  
  // Retro VHS effects
  final List<RetroVHSEffect> _retroVHSEffects = [];
  Timer? _retroGlitchTimer;
  bool _retroGlitchActive = false;
  static const double _minRetroGlitchInterval = 6000; // 6 seconds minimum - more frequent but subtle
  static const double _maxRetroGlitchInterval = 30000; // 30 seconds maximum
  
  // Black and white TV effect state
  bool _isBlackAndWhiteActive = false;
  Timer? _blackAndWhiteTimer;
  Timer? _periodicBWTimer;
  static const double _minBWInterval = 30000; // 30 seconds minimum
  static const double _maxBWInterval = 60000; // 60 seconds maximum
  
  // TV switching sequence state
  bool _isTVSwitching = false;
  String _tvSwitchingPhase = 'none'; // 'glitch', 'no_signal', 'black_screen', 'none'
  Timer? _tvSwitchingTimer;
  bool _targetBWState = false; // The state we're switching TO
  
  // Current map style
  MapStyle _currentMapStyle = MapStyle.standard;
  
  // Keep the latest screen dimensions so we can spawn particles every frame
  Size? _screenSize;
  
  VisualEffectsManager({
    required this.vsync,
    this.onStateChanged,
  }) {
    _initializeAnimations();
    _initializeWeatherEffects();
  }
  
  // Getters for the painter
  List<Particle> get weatherParticles => _weatherParticles;
  List<Particle> get lightningBolts => _lightningBolts;
  bool get lightningFlashActive => _lightningFlashActive;
  double get lightningFlashIntensity => _lightningFlashAnimation.value;
  double get weatherAnimationValue => _weatherAnimation.value;
  List<MatrixCharacter> get matrixCharacters => _matrixCharacters;
  bool get matrixGlitchActive => _matrixGlitchActive;
  double get matrixGlitchIntensity => _matrixGlitchAnimation.value;
  List<CyberpunkGlitch> get cyberpunkGlitches => _cyberpunkGlitches;
  bool get cyberpunkGlitchActive => _cyberpunkGlitchActive;
  double get cyberpunkGlitchIntensity => _cyberpunkGlitchAnimation.value;
  List<RetroVHSEffect> get retroVHSEffects => _retroVHSEffects;
  bool get retroGlitchActive => _retroGlitchActive;
  double get retroGlitchIntensity => _retroGlitchAnimation.value;
  bool get isTVSwitching => _isTVSwitching;
  String get tvSwitchingPhase => _tvSwitchingPhase;
  bool get weatherEnabled => _weatherEnabled;
  bool get isBlackAndWhiteActive => _isBlackAndWhiteActive;
  String get currentWeather => _currentWeather;
  
  // Check if any effects are active
  bool get hasActiveEffects {
    final weatherActive = _weatherEnabled && (_weatherParticles.isNotEmpty || _lightningBolts.isNotEmpty || _lightningFlashActive);
    final matrixActive = _currentMapStyle == MapStyle.matrix && (_matrixCharacters.isNotEmpty || _matrixGlitchActive);
    final cyberpunkActive = _currentMapStyle == MapStyle.cyberpunk && (_cyberpunkGlitches.isNotEmpty || _cyberpunkGlitchActive);
    final retroActive = _currentMapStyle == MapStyle.retro && (_retroVHSEffects.isNotEmpty || _retroGlitchActive || _isTVSwitching);
    
    final hasEffects = weatherActive || matrixActive || cyberpunkActive || retroActive;
    return hasEffects;
  }
  
  void _initializeAnimations() {
    // Weather animation controller
    _weatherAnimationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: vsync,
    )..repeat();
    
    _weatherAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _weatherAnimationController,
      curve: Curves.linear,
    ));
    
    // Lightning flash animation controller
    _lightningFlashController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: vsync,
    );
    
    _lightningFlashAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _lightningFlashController,
      curve: Curves.easeOut,
    ));
    
    // Matrix glitch animation controller
    _matrixGlitchController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: vsync,
    );
    
    _matrixGlitchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _matrixGlitchController,
      curve: Curves.easeInOut,
    ));
    
    // Cyberpunk glitch animation controller
    _cyberpunkGlitchController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: vsync,
    );
    
    _cyberpunkGlitchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cyberpunkGlitchController,
      curve: Curves.easeInOut,
    ));
    
    // Retro VHS glitch animation controller
    _retroGlitchController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: vsync,
    );
    
    _retroGlitchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _retroGlitchController,
      curve: Curves.easeInOut,
    ));
  }
  
  void _initializeWeatherEffects() {
    // Start weather particle system
    _weatherTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) { // Optimized for performance and smoothness
      if (!_weatherEnabled) return;
      
      _updateWeatherParticles();
      _updateLightningEffects();
      
      // Update style-specific effects
      if (_currentMapStyle == MapStyle.matrix) {
        _updateMatrixEffects();
      } else if (_currentMapStyle == MapStyle.cyberpunk) {
        _updateCyberpunkEffects();
      } else if (_currentMapStyle == MapStyle.retro) {
        _updateRetroEffects();
        _updateBlackAndWhiteEffect();
      }
      
      // Trigger state change callback if any effects are active
      if (hasActiveEffects) {
        onStateChanged?.call();
      }
    });
    
    // Initialize lightning timer for rain
    _initializeLightningEffects();
    
    // Initialize weather based on current conditions
    _setWeatherType('rain'); // You can change this or make it dynamic
  }
  
  void _updateWeatherParticles() {
    if (_screenSize == null) return;
    
    final size = _screenSize!;
    final center = size.center(Offset.zero);
    final random = math.Random();
    final now = DateTime.now().millisecondsSinceEpoch.toDouble();

    // Remove dead particles (no more ripple effects - aura radius is handled on map)
    _weatherParticles.removeWhere((particle) {
      return particle.isDead || particle.position.dy > size.height + 20;
    });
    
    // Limit particle count for performance - increased for more intense effect
    if (_weatherParticles.length > 1500) {
      _weatherParticles.removeRange(0, _weatherParticles.length - 1500);
    }
    
    // Update existing particles
    for (final particle in _weatherParticles) {
      particle.update(0.016, _wind); // 60fps
    }
    
    // Add new particles
    if (_weatherParticles.length < 1200) {
      _addWeatherParticles();
    }
  }
  
  void _addWeatherParticles() {
    if (_screenSize == null) return;
    final now = DateTime.now().millisecondsSinceEpoch.toDouble();
    
    final size = _screenSize!;
    final center = size.center(Offset.zero);
    final random = math.Random();

    // Center-thinning effect
    final spawnX = random.nextDouble() * size.width;
    final distFromCenter = (spawnX - center.dx).abs();
    // Remove center-thinning for rain to cover entire screen
    if (_currentWeather != 'rain') {
      if (distFromCenter < size.width * 0.15 && random.nextDouble() > 0.4) {
        // Reduce particle creation rate near the center (only for non-rain)
        return;
      }
    }
    
    switch (_currentWeather) {
      case 'rain':
        // Add raindrops
        for (int i = 0; i < 35; i++) { // EXTREME amount of raindrops per spawn
          _weatherParticles.add(Particle(
            position: Offset(spawnX, -20),
            velocity: Offset(
              _wind.dx * 0.1, // Less horizontal velocity for rain
              600 + random.nextDouble() * 400, // Much faster: 600-1000 px/s
            ),
            size: 0.6 + random.nextDouble() * 1.0, // Slightly thinner droplets: 0.6-1.6
            color: Colors.blueGrey.withOpacity(0.5),
            opacity: 0.3 + random.nextDouble() * 0.3,
            life: 2.0, // Shorter life since they fall faster
            maxLife: 2.0,
            z: random.nextDouble(),
          ));
        }
        break;
      case 'snow':
        // Add snowflakes
        for (int i = 0; i < 3; i++) { // Reduced snowflakes per spawn
          // Use center-thinning for snow only
          final spawnX = random.nextDouble() * size.width;
          final distFromCenter = (spawnX - center.dx).abs();
          if (distFromCenter < size.width * 0.15 && random.nextDouble() > 0.4) {
            // Reduce particle creation rate near the center for snow
            continue;
          }
          
          _weatherParticles.add(Particle(
            position: Offset(spawnX, -20),
            velocity: Offset(
              math.sin(now / (800 + random.nextDouble() * 400)) * 40,
              180 + random.nextDouble() * 120, // Much faster: 180-300 px/s
            ),
            size: 2 + random.nextDouble() * 3,
            color: Colors.white.withOpacity(0.9),
            opacity: 0.7 + random.nextDouble() * 0.3,
            life: 4.0, // Shorter life since they fall faster
            maxLife: 4.0,
            z: random.nextDouble(),
          ));
        }
        break;
      case 'clouds':
        // Add subtle cloud particles
        if (random.nextDouble() < 0.1) {
          // Clouds spawn from the left side
          _weatherParticles.add(Particle(
            position: Offset(
              -50,
              random.nextDouble() * size.height * 0.3,
            ),
            velocity: Offset(30 + random.nextDouble() * 20, 0),
            size: 50 + random.nextDouble() * 70,
            color: Colors.white.withOpacity(0.2),
            opacity: 0.15 + random.nextDouble() * 0.2,
            life: 20.0,
            maxLife: 20.0,
            z: random.nextDouble() * 0.5, // Clouds are further away
          ));
        }
        break;
    }
  }
  
  void _setWeatherType(String weather) {
    debugPrint('🌧️ Setting weather type to: $weather');
    _currentWeather = weather;
    _weatherParticles.clear();
    _lightningBolts.clear();
    
    if (weather == 'rain') {
      _scheduleLightning();
    } else {
      _lightningTimer?.cancel();
      _lightningFlashActive = false;
    }
  }
  
  void _initializeLightningEffects() {
    if (_currentWeather == 'rain' && _isScreenVisible && _isAppInForeground) {
      _scheduleLightning();
    }
  }
  
  void _scheduleLightning() {
    if (_currentWeather != 'rain' || !_isScreenVisible || !_isAppInForeground) return;
    
    final random = math.Random();
    final delay = _minLightningInterval + 
                  random.nextDouble() * (_maxLightningInterval - _minLightningInterval);
    
    _lightningTimer?.cancel();
    _lightningTimer = Timer(Duration(milliseconds: delay.toInt()), () {
      if (_currentWeather == 'rain' && _isScreenVisible && _isAppInForeground) {
        _triggerLightning();
        _scheduleLightning(); // Schedule next lightning
      }
    });
  }
  
  void _triggerLightning() {
    if (_lightningFlashActive || _currentWeather != 'rain' || !_isScreenVisible || !_isAppInForeground) return;
    
    _lightningFlashActive = true;
    _lightningFlashController.forward().then((_) {
      _lightningFlashController.reverse().then((_) {
        _lightningFlashActive = false;
        onStateChanged?.call();
      });
    });
    
    HapticFeedback.lightImpact();
    onStateChanged?.call();
  }
  
  void createLightningBolt(Size size) {
    final random = math.Random();
    final startX = size.width * (0.2 + random.nextDouble() * 0.6);
    final endX = startX + (random.nextDouble() - 0.5) * size.width * 0.3;
    final endY = size.height * (0.4 + random.nextDouble() * 0.4);
    
    final segments = 8 + random.nextInt(5);
    double currentX = startX;
    double currentY = 0;
    
    for (int i = 0; i < segments; i++) {
      final targetX = startX + (endX - startX) * (i + 1) / segments +
                     (random.nextDouble() - 0.5) * 30;
      final targetY = endY * (i + 1) / segments;
      
      _lightningBolts.add(Particle(
        position: Offset(currentX, currentY),
        velocity: Offset(targetX - currentX, targetY - currentY),
        size: 2 + random.nextDouble() * 2,
        color: Colors.white.withOpacity(0.9),
        opacity: 0.8 + random.nextDouble() * 0.2,
        life: 0.3 + random.nextDouble() * 0.2,
        maxLife: 0.5,
        z: 1.0,
      ));
      
      currentX = targetX;
      currentY = targetY;
    }
  }
  
  void _updateLightningEffects() {
    if (!_isScreenVisible || !_isAppInForeground) return;
    
    _lightningBolts.removeWhere((bolt) => bolt.isDead);
    
    for (final bolt in _lightningBolts) {
      bolt.update(0.016, Offset.zero);
    }
  }
  
  // Matrix effects
  void _scheduleMatrixGlitch() {
    if (_currentMapStyle != MapStyle.matrix || !_isScreenVisible || !_isAppInForeground) return;
    
    final random = math.Random();
    final delay = _minMatrixGlitchInterval + 
                  random.nextDouble() * (_maxMatrixGlitchInterval - _minMatrixGlitchInterval);
    
    _matrixGlitchTimer?.cancel();
    _matrixGlitchTimer = Timer(Duration(milliseconds: delay.toInt()), () {
      if (_currentMapStyle == MapStyle.matrix && _isScreenVisible && _isAppInForeground) {
        _triggerMatrixGlitch();
        _scheduleMatrixGlitch();
      }
    });
  }
  
  void _triggerMatrixGlitch() {
    if (_matrixGlitchActive || _currentMapStyle != MapStyle.matrix || !_isScreenVisible || !_isAppInForeground) return;
    
    _matrixGlitchActive = true;
    _matrixGlitchController.forward().then((_) {
      _matrixGlitchController.reverse().then((_) {
        _matrixGlitchActive = false;
        onStateChanged?.call();
      });
    });
    
    HapticFeedback.lightImpact();
    onStateChanged?.call();
  }
  
  void createMatrixCharacters(Size size) {
    if (!_isScreenVisible || !_isAppInForeground) return;
    
    debugPrint('💊 Creating matrix characters for screen size: ${size.width}x${size.height}');
    final random = math.Random();
    final matrixChars = [
      'ア', 'イ', 'ウ', 'エ', 'オ', 'カ', 'キ', 'ク', 'ケ', 'コ',
      'サ', 'シ', 'ス', 'セ', 'ソ', 'タ', 'チ', 'ツ', 'テ', 'ト',
      'ナ', 'ニ', 'ヌ', 'ネ', 'ノ', 'ハ', 'ヒ', 'フ', 'ヘ', 'ホ',
      'マ', 'ミ', 'ム', 'メ', 'モ', 'ヤ', 'ユ', 'ヨ', 'ラ', 'リ',
      'ル', 'レ', 'ロ', 'ワ', 'ヲ', 'ン',
      '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
    ];
    
    final numColumns = (size.width / 8).round();
    
    for (int i = 0; i < numColumns; i++) {
      if (random.nextDouble() > 0.15) continue; // Increased back to 0.15 for more visible matrix effect
      
      final x = (i * 8.0) + (random.nextDouble() * 6 - 3);
      final numChars = 3 + random.nextInt(5); // Reduced from 8-20 to 3-7 characters per column
      
      for (int j = 0; j < numChars; j++) {
        final char = matrixChars[random.nextInt(matrixChars.length)];
        final y = -80.0 - (j * 28.0) - (random.nextDouble() * 150);
        
        _matrixCharacters.add(MatrixCharacter(
          position: Offset(x, y),
          character: char,
          opacity: 0.5 + random.nextDouble() * 0.4, // Increased visibility: 0.5-0.9
          life: 6.0 + random.nextDouble() * 2.0, // Longer life: 6-8s so they reach the bottom
          maxLife: 8.0, // Increased from 3.0 to 8.0
          speed: 140 + random.nextDouble() * 60,
          trail: y - 25,
        ));
      }
    }
    
    debugPrint('💊 Created ${_matrixCharacters.length} total matrix characters');
  }
  
  void _updateMatrixEffects() {
    if (!_isScreenVisible || !_isAppInForeground) return;
    
    // Only update existing characters - don't continuously add new ones
    if (_matrixCharacters.length > 100) { // Reduced from 2500 for performance
      _matrixCharacters.removeRange(0, _matrixCharacters.length - 100);
    }
    
    _matrixCharacters.removeWhere((char) => char.isDead);
    
    for (final char in _matrixCharacters) {
      char.update(0.02); // Reduced from 0.05 to 0.02 - slower fade rate
    }
  }
  
  // Cyberpunk effects
  void _scheduleCyberpunkGlitch() {
    if (_currentMapStyle != MapStyle.cyberpunk || !_isScreenVisible || !_isAppInForeground) return;
    
    final random = math.Random();
    final delay = _minCyberpunkGlitchInterval + 
                  random.nextDouble() * (_maxCyberpunkGlitchInterval - _minCyberpunkGlitchInterval);
    
    _cyberpunkGlitchTimer?.cancel();
    _cyberpunkGlitchTimer = Timer(Duration(milliseconds: delay.toInt()), () {
      if (_currentMapStyle == MapStyle.cyberpunk && _isScreenVisible && _isAppInForeground) {
        _triggerCyberpunkGlitch();
        _scheduleCyberpunkGlitch();
      }
    });
  }
  
  void _triggerCyberpunkGlitch() {
    if (_cyberpunkGlitchActive || _currentMapStyle != MapStyle.cyberpunk || !_isScreenVisible || !_isAppInForeground) return;
    
    _cyberpunkGlitchActive = true;
    _cyberpunkGlitchController.forward().then((_) {
      _cyberpunkGlitchController.reverse().then((_) {
        _cyberpunkGlitchActive = false;
        onStateChanged?.call();
      });
    });
    
    HapticFeedback.mediumImpact();
    onStateChanged?.call();
  }
  
  void createCyberpunkGlitches(Size size) {
    if (!_isScreenVisible || !_isAppInForeground) return;
    
    final random = math.Random();
    final glitchTypes = ['scan', 'noise', 'chromatic', 'neon'];
    final neonColors = [
      const Color(0xFF00FFFF),
      const Color(0xFFFF00FF),
      const Color(0xFFFF0080),
      const Color(0xFF8000FF),
      const Color(0xFF00FF80),
    ];
    
    for (int i = 0; i < 3 + random.nextInt(3); i++) { // Reduced from 8-12 to 3-5 glitches
      final type = glitchTypes[random.nextInt(glitchTypes.length)];
      final color = neonColors[random.nextInt(neonColors.length)];
      
      _cyberpunkGlitches.add(CyberpunkGlitch(
        position: Offset(
          random.nextDouble() * size.width,
          random.nextDouble() * size.height,
        ),
        width: type == 'scan' ? size.width : (50 + random.nextDouble() * 200),
        height: type == 'scan' ? (5 + random.nextDouble() * 3) : (20 + random.nextDouble() * 50),
        color: color,
        opacity: 0.2 + random.nextDouble() * 0.3, // Reduced from 0.6-1.0 to 0.2-0.5
        life: 0.2 + random.nextDouble() * 0.3, // Shorter life: 0.2-0.5s instead of 0.3-0.7s
        maxLife: 0.5, // Reduced from 0.7
        intensity: 0.4 + random.nextDouble() * 0.3, // Reduced from 0.7-1.0 to 0.4-0.7
        type: type,
      ));
    }
  }
  
  void _updateCyberpunkEffects() {
    if (!_isScreenVisible || !_isAppInForeground) return;
    
    // Only update existing glitches - don't continuously add new ones
    if (_cyberpunkGlitches.length > 20) { // Limit glitches for performance
      _cyberpunkGlitches.removeRange(0, _cyberpunkGlitches.length - 20);
    }
    
    _cyberpunkGlitches.removeWhere((glitch) => glitch.isDead);
    
    for (final glitch in _cyberpunkGlitches) {
      glitch.update(0.016);
    }
  }
  
  // Retro VHS effects
  void _scheduleRetroGlitch() {
    if (_currentMapStyle != MapStyle.retro || !_isScreenVisible || !_isAppInForeground) return;
    
    final random = math.Random();
    final delay = _minRetroGlitchInterval + 
                  random.nextDouble() * (_maxRetroGlitchInterval - _minRetroGlitchInterval);
    
    _retroGlitchTimer?.cancel();
    _retroGlitchTimer = Timer(Duration(milliseconds: delay.toInt()), () {
      if (_currentMapStyle == MapStyle.retro) {
        _triggerRetroGlitch();
        _scheduleRetroGlitch();
      }
    });
  }
  
  void _triggerRetroGlitch() {
    _retroGlitchActive = true;
    _retroGlitchController.forward().then((_) {
      _retroGlitchController.reverse().then((_) {
        _retroGlitchActive = false;
        onStateChanged?.call();
      });
    });
    
    HapticFeedback.lightImpact();
    onStateChanged?.call();
  }
  
  void createRetroVHSEffects(Size size) {
    final random = math.Random();
    final vhsTypes = ['static', 'lines', 'tracking', 'color_bleed'];
    
    for (int i = 0; i < 2 + random.nextInt(3); i++) { // Reduced from 6-9 to 2-4 effects
      final type = vhsTypes[random.nextInt(vhsTypes.length)];
      
      _retroVHSEffects.add(RetroVHSEffect(
        position: Offset(
          type == 'tracking' ? -size.width * 0.1 : 0,
          random.nextDouble() * size.height,
        ),
        width: type == 'lines' ? size.width : (size.width * 0.3 + random.nextDouble() * size.width * 0.4),
        height: type == 'static' ? (50 + random.nextDouble() * 100) : (8 + random.nextDouble() * 12),
        opacity: 0.1 + random.nextDouble() * 0.3, // Reduced from 0.3-0.8 to 0.1-0.4
        life: 0.3 + random.nextDouble() * 0.5, // Shorter life: 0.3-0.8s instead of 0.5-1.5s
        maxLife: 0.8, // Reduced from 1.5
        speed: type == 'tracking' ? (200 + random.nextDouble() * 100) : 0,
        type: type,
      ));
    }
  }
  
  void _updateRetroEffects() {
    // Only update existing effects - don't continuously add new ones
    if (_retroVHSEffects.length > 10) { // Limit effects for performance
      _retroVHSEffects.removeRange(0, _retroVHSEffects.length - 10);
    }
    
    _retroVHSEffects.removeWhere((effect) => effect.isDead);
    
    for (final effect in _retroVHSEffects) {
      effect.update(0.016);
    }
  }
  
  void _updateBlackAndWhiteEffect() {
    if (_currentMapStyle != MapStyle.retro) {
      if (_isBlackAndWhiteActive) {
        debugPrint('📺 Resetting B&W state - not in retro mode');
        _setBlackAndWhiteActive(false);
      }
      _periodicBWTimer?.cancel();
      return;
    }

    // Only schedule if screen is visible and app is in foreground
    if (!_isScreenVisible || !_isAppInForeground) {
      return;
    }

    // Schedule periodic black and white switching if not already scheduled
    if (_periodicBWTimer == null || !_periodicBWTimer!.isActive) {
      debugPrint('📺 B&W timer not active - scheduling new toggle');
      _schedulePeriodicBWToggle();
    }
  }
  
  void _schedulePeriodicBWToggle() {
    if (_currentMapStyle != MapStyle.retro) {
      debugPrint('📺 Skipping B&W toggle schedule - not in retro mode');
      return;
    }

    if (!_isScreenVisible || !_isAppInForeground) {
      debugPrint('📺 Skipping B&W toggle schedule - screen not visible or app in background');
      return;
    }

    final random = math.Random();
    final delay = _minBWInterval +
                  random.nextDouble() * (_maxBWInterval - _minBWInterval);

    debugPrint('📺 Scheduling next B&W toggle in ${delay.toInt()}ms');

    _periodicBWTimer?.cancel();
    _periodicBWTimer = Timer(Duration(milliseconds: delay.toInt()), () {
      if (_currentMapStyle == MapStyle.retro && _isScreenVisible && _isAppInForeground) {
        debugPrint('📺 Triggering scheduled B&W toggle');
        _triggerBWToggle();
        _schedulePeriodicBWToggle();
      } else {
        debugPrint('📺 Skipping scheduled B&W toggle - conditions not met (style: $_currentMapStyle, visible: $_isScreenVisible, foreground: $_isAppInForeground)');
      }
    });
  }
  
  void _triggerBWToggle() {
    // Don't trigger if map style isn't retro, already switching, or screen not visible
    if (_currentMapStyle != MapStyle.retro || _isTVSwitching || !_isScreenVisible || !_isAppInForeground) {
      debugPrint('📺 Skipping B&W toggle - conditions not met:');
      debugPrint('  - Map style: $_currentMapStyle (should be retro)');
      debugPrint('  - Already switching: $_isTVSwitching');
      debugPrint('  - Screen visible: $_isScreenVisible');
      debugPrint('  - App in foreground: $_isAppInForeground');
      return;
    }

    final newState = !_isBlackAndWhiteActive;
    _targetBWState = newState;
    debugPrint('📺 B&W toggle triggered: $_isBlackAndWhiteActive → $newState');
    _startTVSwitchingSequence();
  }
  
  void _startTVSwitchingSequence() {
    // Don't start animation if screen not visible or app in background
    if (!_isScreenVisible || !_isAppInForeground) {
      return;
    }
    
    _tvSwitchingTimer?.cancel();
    
    debugPrint('📺 Starting TV switching sequence: ${_isBlackAndWhiteActive ? 'B&W' : 'Color'} → ${_targetBWState ? 'B&W' : 'Color'}');
    
    _isTVSwitching = true;
    _tvSwitchingPhase = 'glitch';
    // Kick off a retro-glitch burst so the first frame shows distortion.
    _triggerRetroGlitch();
    debugPrint('📺 Phase 1: Glitch (0-500ms)');
    onStateChanged?.call();
    
    HapticFeedback.mediumImpact();
    
    // Phase 1: short glitch burst (500 ms) then switch to "NO SIGNAL"
    _tvSwitchingTimer = Timer(const Duration(milliseconds: 500), () {
      if (!_isTVSwitching || _currentMapStyle != MapStyle.retro) {
        _resetTVSwitchingState();
        return;
      }
      
      debugPrint('📺 Phase 2: NO SIGNAL (500-1700ms)');
      _tvSwitchingPhase = 'no_signal';
      onStateChanged?.call();
      
      // Phase 2: NO SIGNAL for 1200ms, then black screen
      _tvSwitchingTimer = Timer(const Duration(milliseconds: 1200), () {
        if (!_isTVSwitching || _currentMapStyle != MapStyle.retro) {
          _resetTVSwitchingState();
          return;
        }
        
        debugPrint('📺 Phase 3: Black screen + invisible switch (1700-2000ms)');
        _tvSwitchingPhase = 'black_screen';
        onStateChanged?.call();
        
        // Phase 3: Black screen for 300 ms BEFORE switching (so screen is fully black)
        _tvSwitchingTimer = Timer(const Duration(milliseconds: 300), () {
          if (!_isTVSwitching || _currentMapStyle != MapStyle.retro) {
            _resetTVSwitchingState();
            return;
          }
          
          // INVISIBLE SWITCH - happens during black screen
          debugPrint('📺 Switching to ${_targetBWState ? 'B&W' : 'Color'} mode (invisible)');
          debugPrint('📺 Before switch: _isBlackAndWhiteActive = $_isBlackAndWhiteActive');
          _setBlackAndWhiteActive(_targetBWState);
          debugPrint('📺 After switch: _isBlackAndWhiteActive = $_isBlackAndWhiteActive');
          
          // Phase 4: Keep black screen for another 400 ms after switch
          _tvSwitchingTimer = Timer(const Duration(milliseconds: 400), () {
            if (_currentMapStyle != MapStyle.retro) {
              _resetTVSwitchingState();
              return;
            }
            
            // Phase 5: Return to normal - map reappears with new colors
            debugPrint('📺 TV switching complete - returning to normal');
            _isTVSwitching = false;
            _tvSwitchingPhase = 'none';
            onStateChanged?.call();
            
            HapticFeedback.lightImpact();
          });
        });
      });
    });
  }
  
  void _resetTVSwitchingState() {
    _tvSwitchingTimer?.cancel();
    _isTVSwitching = false;
    _tvSwitchingPhase = 'none';
    onStateChanged?.call();
  }
  
  void _setBlackAndWhiteActive(bool active, {bool isInitialization = false}) {
    debugPrint('📺 _setBlackAndWhiteActive called with: $active (initialization: $isInitialization)');
    debugPrint('📺 Current state: _isBlackAndWhiteActive = $_isBlackAndWhiteActive');
    debugPrint('📺 Current map style: $_currentMapStyle');
    debugPrint('📺 TV switching active: $_isTVSwitching');

    // Prevent accidental colour changes that bypass the TV-switching animation.
    // Inside Retro mode we only allow the switch while the dedicated TV sequence
    // is running ( _isTVSwitching == true ) OR during initialization.
    // Outside Retro mode we still allow an immediate reset so that leaving the style returns the map to colour.
    if (_currentMapStyle == MapStyle.retro && !_isTVSwitching && !isInitialization) {
      debugPrint('📺 Ignoring direct B&W change outside TV sequence (not initialization)');
      return;
    }

    if (_isBlackAndWhiteActive == active) {
      debugPrint('📺 B&W state already $active - no change needed');
      return;
    }

    debugPrint('📺 Setting B&W state: $_isBlackAndWhiteActive → $active');
    _isBlackAndWhiteActive = active;
    debugPrint('📺 Calling onStateChanged callback...');
    onStateChanged?.call();
    debugPrint('📺 onStateChanged callback completed');
  }
  
  // Public methods
  void changeMapStyle(MapStyle newStyle) {
    final oldStyle = _currentMapStyle;
    _currentMapStyle = newStyle;
    
    // Stop style-specific effects when switching away
    if (oldStyle == MapStyle.matrix && newStyle != MapStyle.matrix) {
      _matrixGlitchTimer?.cancel();
      _matrixCharacters.clear();
      _matrixGlitchActive = false;
    }
    if (oldStyle == MapStyle.cyberpunk && newStyle != MapStyle.cyberpunk) {
      _cyberpunkGlitchTimer?.cancel();
      _cyberpunkGlitches.clear();
      _cyberpunkGlitchActive = false;
    }
    if (oldStyle == MapStyle.retro && newStyle != MapStyle.retro) {
      _retroGlitchTimer?.cancel();
      _periodicBWTimer?.cancel();
      _retroVHSEffects.clear();
      _retroGlitchActive = false;
      if (_isBlackAndWhiteActive) {
        _setBlackAndWhiteActive(false);
      }
    }
    
    // Start style-specific effects for new style - PERIODIC, not continuous
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_currentMapStyle == MapStyle.matrix) {
        debugPrint('💊 Starting periodic Matrix effects - first effect in 2-5 seconds');
        _scheduleMatrixGlitch(); // Only schedule periodic glitches
      } else if (_currentMapStyle == MapStyle.cyberpunk) {
        debugPrint('🌃 Starting periodic Cyberpunk effects');
        _scheduleCyberpunkGlitch(); // Only schedule periodic glitches
      } else if (_currentMapStyle == MapStyle.retro) {
        debugPrint('📺 Starting periodic Retro effects');
        _scheduleRetroGlitch(); // Only schedule periodic glitches
        _schedulePeriodicBWToggle(); // Start black and white switching
        _setBlackAndWhiteActive(false); // Start in color mode
      }
    });
  }
  
  void toggleWeatherEffects() {
    if (_weatherEnabled) {
      _weatherEnabled = false;
      _weatherParticles.clear();
    } else {
      _weatherEnabled = true;
      _setWeatherType(_currentWeather == 'rain' ? 'snow' : 'rain');
    }
    onStateChanged?.call();
  }
  
  String getWeatherIcon() {
    return _currentWeather == 'snow' ? 'snow' : 'rain';
  }
  
  void updateWithScreenSize(Size screenSize) {
    // Cache the latest size so the weather system can spawn particles on every
    // timer tick without needing the widget to call us again in the interim.
    _screenSize = screenSize;

    if (_lightningFlashActive && _lightningBolts.isEmpty) {
      createLightningBolt(screenSize);
    }
    
    // Create effects only during active glitch periods (periodic, not continuous)
    if (_currentMapStyle == MapStyle.matrix && _matrixGlitchActive && _matrixCharacters.length < 20) {
      createMatrixCharacters(screenSize);
    }
    
    if (_currentMapStyle == MapStyle.cyberpunk && _cyberpunkGlitchActive && _cyberpunkGlitches.length < 5) {
      createCyberpunkGlitches(screenSize);
    }
    
    if (_currentMapStyle == MapStyle.retro && _retroGlitchActive && _retroVHSEffects.length < 3) {
      createRetroVHSEffects(screenSize);
    }
  }
  
  /// Update visibility state when the screen becomes visible or hidden
  void setScreenVisibility(bool isVisible) {
    debugPrint('🔄 VisualEffectsManager: Screen visibility changed to $isVisible');
    debugPrint('🔄 Previous state - visible: $_isScreenVisible, foreground: $_isAppInForeground');

    _isScreenVisible = isVisible;

    // If becoming visible and app is in foreground, resume animations that were previously running
    if (isVisible && _isAppInForeground) {
      debugPrint('🔄 Screen became visible and app in foreground - resuming animations');
      _resumeAnimationTimers();
    } else if (!isVisible) {
      // If becoming invisible, pause all animations
      debugPrint('🔄 Screen became invisible - pausing animations');
      _pauseAnimationTimers();
    } else {
      debugPrint('🔄 Screen visible but app not in foreground - keeping animations paused');
    }
  }
  
  /// Update app foreground state when app lifecycle changes
  void setAppForegroundState(bool isInForeground) {
    debugPrint('🔄 VisualEffectsManager: App foreground state changed to $isInForeground');
    debugPrint('🔄 Previous state - visible: $_isScreenVisible, foreground: $_isAppInForeground');

    _isAppInForeground = isInForeground;

    // If coming to foreground and screen is visible, resume animations
    if (isInForeground && _isScreenVisible) {
      debugPrint('🔄 App came to foreground and screen visible - resuming animations');
      _resumeAnimationTimers();
    } else if (!isInForeground) {
      // If going to background, pause all animations
      debugPrint('🔄 App went to background - pausing animations');
      _pauseAnimationTimers();
    } else {
      debugPrint('🔄 App in foreground but screen not visible - keeping animations paused');
    }
  }
  
  /// Pause all animation timers
  void _pauseAnimationTimers() {
    debugPrint('🔄 VisualEffectsManager: Pausing all animation timers...');

    _weatherTimer?.cancel();
    _lightningTimer?.cancel();
    _matrixGlitchTimer?.cancel();
    _cyberpunkGlitchTimer?.cancel();
    _retroGlitchTimer?.cancel();
    _blackAndWhiteTimer?.cancel();

    // Cancel retro-specific timers with logging
    if (_periodicBWTimer?.isActive == true) {
      debugPrint('📺 Cancelling active B&W periodic timer');
      _periodicBWTimer?.cancel();
    }
    if (_tvSwitchingTimer?.isActive == true) {
      debugPrint('📺 Cancelling active TV switching timer');
      _tvSwitchingTimer?.cancel();
    }

    debugPrint('🔄 All animation timers paused');
  }
  
  /// Resume animation timers based on current state
  void _resumeAnimationTimers() {
    debugPrint('🔄 VisualEffectsManager: Resuming animation timers...');
    debugPrint('🔄 Current map style: $_currentMapStyle');
    debugPrint('🔄 Weather enabled: $_weatherEnabled');
    debugPrint('🔄 Screen visible: $_isScreenVisible');
    debugPrint('🔄 App in foreground: $_isAppInForeground');

    // Only restart timers if screen is visible and app is in foreground
    if (!_isScreenVisible || !_isAppInForeground) {
      debugPrint('🔄 Skipping timer resume - screen not visible or app in background');
      return;
    }

    // Only restart weather timer if weather was enabled
    if (_weatherEnabled) {
      debugPrint('🔄 Restarting weather effects timer...');
      _initializeWeatherEffects();
    }

    // Resume style-specific effects based on current map style
    // Use a small delay to ensure screen visibility state is fully updated
    Future.delayed(const Duration(milliseconds: 100), () {
      if (!_isScreenVisible || !_isAppInForeground) {
        debugPrint('🔄 Screen became invisible during timer restart - aborting');
        return;
      }

      if (_currentMapStyle == MapStyle.retro) {
        debugPrint('📺 Resuming retro effects...');
        _scheduleRetroGlitch();
        // Force restart the periodic B&W toggle timer
        _periodicBWTimer?.cancel();
        _schedulePeriodicBWToggle();
        debugPrint('📺 Retro effects resumed - B&W timer restarted');
      } else if (_currentMapStyle == MapStyle.matrix) {
        debugPrint('💊 Resuming matrix effects...');
        _scheduleMatrixGlitch();
      } else if (_currentMapStyle == MapStyle.cyberpunk) {
        debugPrint('🌃 Resuming cyberpunk effects...');
        _scheduleCyberpunkGlitch();
      }

      // Resume weather-specific effects
      if (_currentWeather == 'rain') {
        debugPrint('⚡ Resuming lightning effects...');
        _scheduleLightning();
      }

      debugPrint('🔄 All animation timers resumed successfully');
    });
  }
  
  void dispose() {
    _weatherTimer?.cancel();
    _lightningTimer?.cancel();
    _matrixGlitchTimer?.cancel();
    _cyberpunkGlitchTimer?.cancel();
    _retroGlitchTimer?.cancel();
    _blackAndWhiteTimer?.cancel();
    _periodicBWTimer?.cancel();
    _tvSwitchingTimer?.cancel();
    
    _weatherAnimationController.dispose();
    _lightningFlashController.dispose();
    _matrixGlitchController.dispose();
    _cyberpunkGlitchController.dispose();
    _retroGlitchController.dispose();
  }
}
