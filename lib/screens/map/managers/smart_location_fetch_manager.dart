import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:geolocator/geolocator.dart';
import '../../../services/api/pins_service.dart';
import '../../../models/pin.dart';
import '../../../config/constants.dart';

/// Smart location-based fetching manager that optimizes API calls for nearby pins
/// 
/// This manager implements intelligent fetching strategies to reduce unnecessary API calls:
/// - Initial load: Fetches pins when the map first loads
/// - Time-based refresh: Fetches every 10 minutes regardless of location
/// - Distance-based refresh: Fetches when user moves more than 10 meters
/// - Proper debouncing and throttling to prevent rapid successive calls
class SmartLocationFetchManager {
  // Fetch conditions
  static const Duration _timeBasedRefreshInterval = Duration(minutes: 10);
  static const double _distanceBasedRefreshThreshold = 10.0; // meters
  static const Duration _debounceDelay = Duration(milliseconds: 2000); // 2 seconds
  static const Duration _throttleInterval = Duration(milliseconds: 5000); // 5 seconds minimum between API calls
  
  // State tracking
  DateTime? _lastFetchTime;
  Position? _lastFetchPosition;
  bool _isInitialLoad = true;
  bool _isFetchInProgress = false;
  Timer? _debounceTimer;
  DateTime? _lastApiCallTime;
  Position? _lastLocationUpdatePosition;
  DateTime? _lastLocationUpdateTime;
  
  // Callbacks
  Function(List<Pin> pins)? onPinsFetched;
  Function(String message)? onError;
  Function()? onFetchStarted;
  Function()? onFetchCompleted;
  
  // Current filter for API calls
  PinFilterType _currentFilter = PinFilterType.all;
  
  SmartLocationFetchManager({
    this.onPinsFetched,
    this.onError,
    this.onFetchStarted,
    this.onFetchCompleted,
  });
  
  /// Updates the current filter type for pin fetching
  void setFilter(PinFilterType filter) {
    if (_currentFilter != filter) {
      _currentFilter = filter;
      // Force refresh when filter changes by clearing cache
      _isInitialLoad = true;
      _lastFetchTime = null;
      _lastFetchPosition = null;
      debugPrint('📍 SmartFetch: Filter changed to $filter, cleared cache and will force refresh on next location update');
    }
  }
  
  /// Main method to handle location updates and determine if pins should be fetched
  Future<void> handleLocationUpdate(Position position) async {
    debugPrint('📍 SmartFetch: Handling location update at ${position.latitude}, ${position.longitude}');

    // Prevent multiple simultaneous calls
    if (_isFetchInProgress) {
      debugPrint('📍 SmartFetch: Fetch already in progress, ignoring location update');
      return;
    }

    // Prevent duplicate location updates within a short time period
    final now = DateTime.now();
    if (_lastLocationUpdateTime != null && _lastLocationUpdatePosition != null) {
      final timeSinceLastUpdate = now.difference(_lastLocationUpdateTime!);
      if (timeSinceLastUpdate < const Duration(seconds: 1)) {
        final distance = Geolocator.distanceBetween(
          _lastLocationUpdatePosition!.latitude,
          _lastLocationUpdatePosition!.longitude,
          position.latitude,
          position.longitude,
        );
        if (distance < 1.0) { // Less than 1 meter difference
          debugPrint('📍 SmartFetch: Ignoring duplicate location update (${distance.toStringAsFixed(1)}m, ${timeSinceLastUpdate.inMilliseconds}ms)');
          return;
        }
      }
    }

    // Update last location update tracking
    _lastLocationUpdateTime = now;
    _lastLocationUpdatePosition = position;

    // Cancel any pending debounced calls
    _debounceTimer?.cancel();

    // Check if we should fetch immediately (initial load or significant conditions)
    if (_shouldFetchImmediately(position)) {
      debugPrint('📍 SmartFetch: Immediate fetch required');
      await _performFetch(position);
      return;
    }

    // Check if we should fetch after debounce delay
    if (_shouldFetchAfterDebounce(position)) {
      debugPrint('📍 SmartFetch: Scheduling debounced fetch');
      _debounceTimer = Timer(_debounceDelay, () async {
        // Double-check conditions before actually fetching
        if (!_isFetchInProgress && _shouldFetchAfterDebounce(position)) {
          await _performFetch(position);
        } else {
          debugPrint('📍 SmartFetch: Debounced fetch cancelled - conditions changed');
        }
      });
    } else {
      debugPrint('📍 SmartFetch: No fetch needed - using cached data');
    }
  }
  
  /// Force a refresh regardless of conditions (useful for manual refresh)
  Future<void> forceRefresh(Position position) async {
    debugPrint('📍 SmartFetch: Force refresh requested');
    _isInitialLoad = true;
    await _performFetch(position);
  }
  
  /// Clear all cache and force fresh fetch (useful for filter changes)
  void clearCache() {
    debugPrint('📍 SmartFetch: Clearing all cache');
    _lastFetchTime = null;
    _lastFetchPosition = null;
    _isInitialLoad = true;
  }

  /// Handle app lifecycle changes (backgrounding/foregrounding)
  void onAppLifecycleChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint('📍 SmartFetch: App resumed - will refresh on next location update');
        // Force refresh when app comes back to foreground
        _isInitialLoad = true;
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        debugPrint('📍 SmartFetch: App backgrounded - canceling pending operations');
        // Cancel any pending debounced calls when app goes to background
        _debounceTimer?.cancel();
        break;
      case AppLifecycleState.hidden:
        // Handle hidden state if needed
        break;
    }
  }

  /// Handle location permission changes
  void onLocationPermissionChanged(bool hasPermission) {
    if (hasPermission) {
      debugPrint('📍 SmartFetch: Location permission granted - will refresh on next location update');
      _isInitialLoad = true;
    } else {
      debugPrint('📍 SmartFetch: Location permission denied - canceling operations');
      _debounceTimer?.cancel();
      _isFetchInProgress = false;
    }
  }
  
  /// Check if we should fetch immediately without debouncing
  bool _shouldFetchImmediately(Position position) {
    // Initial load
    if (_isInitialLoad) {
      debugPrint('📍 SmartFetch: Initial load required');
      return true;
    }
    
    // No previous fetch
    if (_lastFetchTime == null || _lastFetchPosition == null) {
      debugPrint('📍 SmartFetch: No previous fetch data');
      return true;
    }
    
    // Time-based refresh (10 minutes)
    final timeSinceLastFetch = DateTime.now().difference(_lastFetchTime!);
    if (timeSinceLastFetch >= _timeBasedRefreshInterval) {
      debugPrint('📍 SmartFetch: Time-based refresh needed (${timeSinceLastFetch.inMinutes} minutes)');
      return true;
    }
    
    return false;
  }
  
  /// Check if we should fetch after debounce delay
  bool _shouldFetchAfterDebounce(Position position) {
    // Already covered by immediate fetch
    if (_shouldFetchImmediately(position)) {
      return false;
    }
    
    // Check distance-based refresh
    if (_lastFetchPosition != null) {
      final distance = Geolocator.distanceBetween(
        _lastFetchPosition!.latitude,
        _lastFetchPosition!.longitude,
        position.latitude,
        position.longitude,
      );
      
      if (distance >= _distanceBasedRefreshThreshold) {
        debugPrint('📍 SmartFetch: Distance-based refresh needed (${distance.toStringAsFixed(1)}m)');
        return true;
      }
    }
    
    return false;
  }
  
  /// Perform the actual API fetch with throttling
  Future<void> _performFetch(Position position) async {
    // Check if we're already fetching
    if (_isFetchInProgress) {
      debugPrint('📍 SmartFetch: Fetch already in progress, skipping');
      return;
    }

    // Check throttling - minimum time between API calls
    if (_lastApiCallTime != null) {
      final timeSinceLastCall = DateTime.now().difference(_lastApiCallTime!);
      if (timeSinceLastCall < _throttleInterval) {
        debugPrint('📍 SmartFetch: Throttled - only ${timeSinceLastCall.inMilliseconds}ms since last call');
        return;
      }
    }

    // Basic connectivity check - if we can't reach the API, fail gracefully
    // This is a simple check; more sophisticated connectivity checking could be added
    
    _isFetchInProgress = true;
    _lastApiCallTime = DateTime.now();
    onFetchStarted?.call();
    
    try {
      debugPrint('📍 SmartFetch: Starting API call for filter $_currentFilter');
      
      final pinsService = PinsService();
      List<Pin> pins;
      
      // Use appropriate API method based on current filter
      switch (_currentFilter) {
        case PinFilterType.all:
          pins = await pinsService.getNearbyPins(
            position.latitude,
            position.longitude,
            AppConstants.pinDiscoveryRadius,
          );
          break;
        case PinFilterType.fresh:
          pins = await pinsService.getNearbyFreshPins(
            position.latitude,
            position.longitude,
            AppConstants.pinDiscoveryRadius,
          );
          break;
        case PinFilterType.friends:
          pins = await pinsService.getNearbyFriendsPins(
            position.latitude,
            position.longitude,
            AppConstants.pinDiscoveryRadius,
          );
          break;
      }
      
      // Update state
      _lastFetchTime = DateTime.now();
      _lastFetchPosition = position;
      _isInitialLoad = false;
      
      debugPrint('📍 SmartFetch: Successfully fetched ${pins.length} pins');
      onPinsFetched?.call(pins);
      
    } catch (e) {
      debugPrint('📍 SmartFetch: Error fetching pins: $e');

      // Provide user-friendly error messages
      String errorMessage;
      if (e.toString().contains('No internet connection') ||
          e.toString().contains('SocketException')) {
        errorMessage = 'No internet connection. Please check your network and try again.';
      } else if (e.toString().contains('401') || e.toString().contains('Authentication')) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (e.toString().contains('timeout')) {
        errorMessage = 'Request timed out. Please try again.';
      } else {
        errorMessage = 'Failed to fetch nearby pins. Please try again later.';
      }

      onError?.call(errorMessage);

      // Don't reset initial load flag on error to allow retry
      if (_isInitialLoad) {
        debugPrint('📍 SmartFetch: Keeping initial load flag for retry');
      }
    } finally {
      _isFetchInProgress = false;
      onFetchCompleted?.call();
    }
  }
  
  /// Get debug information about the current state
  Map<String, dynamic> getDebugInfo() {
    return {
      'isInitialLoad': _isInitialLoad,
      'isFetchInProgress': _isFetchInProgress,
      'lastFetchTime': _lastFetchTime?.toIso8601String(),
      'lastFetchPosition': _lastFetchPosition != null 
          ? '${_lastFetchPosition!.latitude}, ${_lastFetchPosition!.longitude}'
          : null,
      'currentFilter': _currentFilter.toString(),
      'timeSinceLastFetch': _lastFetchTime != null 
          ? DateTime.now().difference(_lastFetchTime!).inMinutes
          : null,
      'hasDebounceTimer': _debounceTimer?.isActive ?? false,
    };
  }
  
  /// Check if a fetch is currently needed based on current conditions
  bool shouldFetchNow(Position position) {
    return _shouldFetchImmediately(position) || _shouldFetchAfterDebounce(position);
  }

  /// Get time until next allowed API call (for UI feedback)
  Duration? getTimeUntilNextAllowedCall() {
    if (_lastApiCallTime == null) return null;

    final timeSinceLastCall = DateTime.now().difference(_lastApiCallTime!);
    final remainingTime = _throttleInterval - timeSinceLastCall;

    return remainingTime.isNegative ? null : remainingTime;
  }

  /// Reset the manager state (useful for testing or when user logs out)
  void reset() {
    _debounceTimer?.cancel();
    _lastFetchTime = null;
    _lastFetchPosition = null;
    _isInitialLoad = true;
    _isFetchInProgress = false;
    _lastApiCallTime = null;
    _lastLocationUpdatePosition = null;
    _lastLocationUpdateTime = null;
    debugPrint('📍 SmartFetch: Manager reset');
  }
  
  /// Dispose of resources
  void dispose() {
    _debounceTimer?.cancel();
    debugPrint('📍 SmartFetch: Manager disposed');
  }
}
