import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math' as math;
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';

import '../../../config/constants.dart';
import '../models/particles.dart';

class WeatherVisualEffectsManager {
  final TickerProviderStateMixin vsync;
  final VoidCallback? onStateChanged;
  
  // Animation controllers
  late AnimationController _weatherAnimationController;
  late AnimationController _lightningFlashController;
  
  // Animations
  late Animation<double> _weatherAnimation;
  late Animation<double> _lightningFlashAnimation;
  
  // Weather effects
  List<Particle> _weatherParticles = [];
  Timer? _weatherTimer;
  bool _weatherEnabled = true;
  String _currentWeather = 'clear'; // clear, rain, snow, clouds, fog
  Offset _wind = Offset(20, 0); // Wind from left to right
  
  // Lightning effects for rain
  bool _lightningFlashActive = false;
  Timer? _lightningTimer;
  List<Particle> _lightningBolts = [];
  double _lastLightningTime = 0;
  static const double _minLightningInterval = 8000; // 8 seconds minimum
  static const double _maxLightningInterval = 60000; // 60 seconds maximum
  
  // Weather API integration
  Timer? _weatherUpdateTimer;
  Timer? _locationCheckTimer;
  Position? _lastKnownPosition;
  String? _lastKnownCity;
  DateTime? _lastWeatherUpdate;
  Map<String, dynamic>? _cachedWeatherData;
  
  // Weather API configuration
  static String get _weatherApiKey => AppConstants.weatherApiKey;
  static const String _weatherApiBaseUrl = 'http://api.weatherapi.com/v1';
  static const Duration _weatherUpdateInterval = Duration(minutes: 8); // 8 minutes
  static const Duration _locationCheckInterval = Duration(minutes: 2); // Check location every 2 minutes
  static const double _significantDistanceThreshold = 5000; // 5km in meters
  
  // Keep the latest screen dimensions so we can spawn particles every frame
  Size? _screenSize;
  
  WeatherVisualEffectsManager({
    required this.vsync,
    this.onStateChanged,
  }) {
    _initializeAnimations();
    _initializeWeatherEffects();
    _startLocationMonitoring();
  }
  
  // Getters for the painter
  List<Particle> get weatherParticles => _weatherParticles;
  List<Particle> get lightningBolts => _lightningBolts;
  bool get lightningFlashActive => _lightningFlashActive;
  double get lightningFlashIntensity => _lightningFlashAnimation.value;
  double get weatherAnimationValue => _weatherAnimation.value;
  bool get weatherEnabled => _weatherEnabled;
  String get currentWeather => _currentWeather;
  
  // Check if weather effects are active
  bool get hasActiveEffects {
    final weatherActive = _weatherEnabled && (_weatherParticles.isNotEmpty || _lightningBolts.isNotEmpty || _lightningFlashActive);
    return weatherActive;
  }
  
  void _initializeAnimations() {
    // Weather animation controller
    _weatherAnimationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: vsync,
    )..repeat();
    
    _weatherAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _weatherAnimationController,
      curve: Curves.linear,
    ));
    
    // Lightning flash animation controller
    _lightningFlashController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: vsync,
    );
    
    _lightningFlashAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _lightningFlashController,
      curve: Curves.easeOut,
    ));
  }
  
  void _initializeWeatherEffects() {
    // Start weather particle system
    _weatherTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (!_weatherEnabled) return;
      
      _updateWeatherParticles();
      _updateLightningEffects();
      
      // Trigger state change callback if any effects are active
      if (hasActiveEffects) {
        onStateChanged?.call();
      }
    });
    
    // Initialize lightning timer for rain
    _initializeLightningEffects();
    
    debugPrint('🌧️ Weather visual effects initialized');
  }
  
  void _startLocationMonitoring() {
    // Start periodic location checks
    _locationCheckTimer = Timer.periodic(_locationCheckInterval, (timer) {
      _checkLocationAndUpdateWeather();
    });
    
    // Initial weather fetch
    _checkLocationAndUpdateWeather();
    
    debugPrint('🌍 Location monitoring started');
  }
  
  Future<void> _checkLocationAndUpdateWeather() async {
    try {
      debugPrint('🌧️ Checking location and updating weather...');
      
      // Get current position
      final position = await _getCurrentPosition();
      if (position == null) {
        debugPrint('🌧️ Could not get current position, skipping weather update');
        return;
      }
      
      debugPrint('🌧️ Got position: ${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)}');
      
      // Check if we've moved significantly or enough time has passed
      final shouldUpdate = _shouldUpdateWeather(position);
      if (!shouldUpdate) {
        debugPrint('🌧️ Weather update not needed (recent update or minimal movement)');
        return;
      }
      
      // Get city name for this position
      final cityName = await _getCityName(position);
      if (cityName == null) {
        debugPrint('🌧️ Could not determine city name for position');
        return;
      }
      
      debugPrint('🌧️ Updating weather for location: $cityName');
      
      // Fetch weather data
      await _fetchAndApplyWeatherData(cityName, position);
      
    } catch (e) {
      debugPrint('🌧️ Error checking location and updating weather: $e');
    }
  }
  
  Future<Position?> _getCurrentPosition() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
        debugPrint('🌧️ Location permission denied');
        return null;
      }
      
      // Try to get the last known position first for faster response
      try {
        final lastKnown = await Geolocator.getLastKnownPosition();
        if (lastKnown != null) {
          final age = DateTime.now().difference(lastKnown.timestamp);
          final ageMinutes = age.inMinutes.toDouble();
          
          debugPrint('🌧️ Found last known position: ${lastKnown.latitude}, ${lastKnown.longitude} (${ageMinutes.toStringAsFixed(1)} minutes old)');
          
          // If the last known position is relatively recent (< 10 minutes), use it
          if (ageMinutes < 10) {
            debugPrint('🌧️ Using recent last known position');
            return lastKnown;
          } else {
            debugPrint('🌧️ Last known position too old, getting fresh position');
          }
        }
      } catch (e) {
        debugPrint('🌧️ Could not get last known position: $e');
      }
      
      // Get current position with longer timeout for first fix
      return await _getCurrentPositionWithRetry();
      
    } catch (e) {
      debugPrint('🌧️ Error getting current position: $e');
      return null;
    }
  }
  
  Future<Position?> _getCurrentPositionWithRetry() async {
    const maxRetries = 3;
    const timeouts = [Duration(seconds: 15), Duration(seconds: 25), Duration(seconds: 30)];
    
    for (int attempt = 0; attempt < maxRetries; attempt++) {
      try {
        debugPrint('🌧️ Getting current position (attempt ${attempt + 1}/$maxRetries)...');
        
        final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.low, // Low accuracy is fine for weather
          timeLimit: timeouts[attempt],
        );
        
        debugPrint('🌧️ Successfully got current position: ${position.latitude}, ${position.longitude}');
        return position;
        
      } catch (e) {
        debugPrint('🌧️ Position attempt ${attempt + 1} failed: $e');
        
        if (attempt < maxRetries - 1) {
          // Wait a bit before retrying
          await Future.delayed(Duration(seconds: 1 + attempt));
        }
      }
    }
    
    debugPrint('🌧️ All position attempts failed');
    return null;
  }
  
  bool _shouldUpdateWeather(Position currentPosition) {
    // Always update if we don't have cached data
    if (_lastWeatherUpdate == null || _cachedWeatherData == null) {
      return true;
    }
    
    // Update if enough time has passed
    final timeSinceLastUpdate = DateTime.now().difference(_lastWeatherUpdate!);
    if (timeSinceLastUpdate >= _weatherUpdateInterval) {
      return true;
    }
    
    // Update if we've moved significantly
    if (_lastKnownPosition != null) {
      final distance = Geolocator.distanceBetween(
        _lastKnownPosition!.latitude,
        _lastKnownPosition!.longitude,
        currentPosition.latitude,
        currentPosition.longitude,
      );
      
      if (distance >= _significantDistanceThreshold) {
        debugPrint('🌧️ Moved ${(distance / 1000).toStringAsFixed(1)}km, updating weather');
        return true;
      }
    }
    
    return false;
  }
  
  Future<String?> _getCityName(Position position) async {
    try {
      // Use reverse geocoding or a simple city lookup
      // For now, we'll use coordinates directly with WeatherAPI
      return '${position.latitude.toStringAsFixed(4)},${position.longitude.toStringAsFixed(4)}';
    } catch (e) {
      debugPrint('🌧️ Error getting city name: $e');
      return null;
    }
  }
  
  Future<void> _fetchAndApplyWeatherData(String location, Position position) async {
    try {
      debugPrint('🌧️ Fetching weather data for: $location');
      
      final weatherData = await _fetchWeatherData(location);
      if (weatherData == null) return;
      
      // Log the complete weather response for debugging
      debugPrint('🌧️ Weather API Response: $weatherData');
      
      // Cache the data
      _cachedWeatherData = weatherData;
      _lastWeatherUpdate = DateTime.now();
      _lastKnownPosition = position;
      _lastKnownCity = location;
      
      // Parse and apply weather conditions
      final weatherCondition = _parseWeatherCondition(weatherData);
      final windData = _parseWindData(weatherData);
      
      // Log parsed weather details
      final current = weatherData['current'] as Map<String, dynamic>?;
      final location_data = weatherData['location'] as Map<String, dynamic>?;
      final condition = current?['condition'] as Map<String, dynamic>?;
      
      debugPrint('🌧️ ═══ WEATHER DETAILS ═══');
      debugPrint('🌧️ Location: ${location_data?['name']}, ${location_data?['region']}, ${location_data?['country']}');
      debugPrint('🌧️ Temperature: ${current?['temp_c']}°C (${current?['temp_f']}°F)');
      debugPrint('🌧️ Condition: ${condition?['text']} (code: ${condition?['code']})');
      debugPrint('🌧️ Precipitation: ${current?['precip_mm']}mm');
      debugPrint('🌧️ Wind: ${current?['wind_kph']}kph at ${current?['wind_degree']}° (${current?['wind_dir']})');
      debugPrint('🌧️ Humidity: ${current?['humidity']}%');
      debugPrint('🌧️ Cloud Cover: ${current?['cloud']}%');
      debugPrint('🌧️ Visibility: ${current?['vis_km']}km');
      debugPrint('🌧️ UV Index: ${current?['uv']}');
      debugPrint('🌧️ Is Day: ${current?['is_day'] == 1 ? 'Yes' : 'No'}');
      debugPrint('🌧️ ═══════════════════════');
      debugPrint('🌧️ Parsed Weather Type: $weatherCondition');
      debugPrint('🌧️ Wind Vector: (${windData.dx.toStringAsFixed(1)}, ${windData.dy.toStringAsFixed(1)})');
      
      _setWeatherType(weatherCondition);
      _setWind(windData);
      
    } catch (e) {
      debugPrint('🌧️ Error fetching and applying weather data: $e');
    }
  }
  
  Future<Map<String, dynamic>?> _fetchWeatherData(String location) async {
    try {
      final url = '$_weatherApiBaseUrl/current.json?key=$_weatherApiKey&q=$location&aqi=no';
      
      debugPrint('🌧️ Making weather API request to: $url');
      
      final response = await http.get(
        Uri.parse(url),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        debugPrint('🌧️ Weather API response received successfully');
        return data;
      } else {
        debugPrint('🌧️ Weather API error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('🌧️ Error calling weather API: $e');
      return null;
    }
  }
  
  String _parseWeatherCondition(Map<String, dynamic> weatherData) {
    try {
      final current = weatherData['current'] as Map<String, dynamic>?;
      if (current == null) return 'clear';
      
      final condition = current['condition'] as Map<String, dynamic>?;
      if (condition == null) return 'clear';
      
      final conditionText = (condition['text'] as String? ?? '').toLowerCase();
      final conditionCode = condition['code'] as int? ?? 1000;
      
      // Check for precipitation
      final precipMm = current['precip_mm'] as num? ?? 0.0;
      final isDay = (current['is_day'] as int? ?? 1) == 1;
      
      debugPrint('🌧️ Weather details: text="$conditionText", code=$conditionCode, precip=${precipMm}mm, isDay=$isDay');
      
      // Determine weather type based on condition code first (more accurate than precipitation)
      
      // Clear/Sunny conditions
      if (conditionCode == 1000) {
        return 'clear';
      }
      
      // Fog/Mist conditions
      if ([1030, 1135, 1147].contains(conditionCode)) {
        return 'fog';
      }
      
      // Active precipitation conditions (rain)
      if ([1063, 1150, 1153, 1168, 1171, 1180, 1183, 1186, 1189, 1192, 1195, 1198, 1201, 1240, 1243, 1246, 1273, 1276].contains(conditionCode)) {
        return 'rain';
      }
      
      // Snow conditions
      if ([1066, 1069, 1072, 1114, 1117, 1204, 1207, 1210, 1213, 1216, 1219, 1222, 1225, 1237, 1249, 1252, 1255, 1258, 1261, 1264, 1279, 1282].contains(conditionCode)) {
        return 'snow';
      }
      
      // Thunder conditions (treat as rain with lightning)
      if ([1087, 1273, 1276, 1279, 1282].contains(conditionCode)) {
        return 'rain';
      }
      
      // Cloudy conditions (partly cloudy, cloudy, overcast)
      if ([1003, 1006, 1009].contains(conditionCode)) {
        return 'clouds';
      }
      
      // Fallback: check cloud cover percentage for unlisted codes
      final cloudCover = current['cloud'] as int? ?? 0;
      if (cloudCover > 50) {
        return 'clouds';
      }
      
      // Default to clear
      return 'clear';
      
    } catch (e) {
      debugPrint('🌧️ Error parsing weather condition: $e');
      return 'clear';
    }
  }
  
  Offset _parseWindData(Map<String, dynamic> weatherData) {
    try {
      final current = weatherData['current'] as Map<String, dynamic>?;
      if (current == null) return Offset(20, 0);
      
      final windSpeedKph = current['wind_kph'] as num? ?? 0.0;
      final windDegree = current['wind_degree'] as num? ?? 0.0;
      
      // Convert wind direction and speed to offset
      final windSpeedPixels = (windSpeedKph * 2).clamp(0, 100); // Scale wind speed to pixels
      final windRadians = (windDegree * math.pi) / 180.0;
      
      final windX = math.cos(windRadians) * windSpeedPixels;
      final windY = math.sin(windRadians) * windSpeedPixels;
      
      debugPrint('🌧️ Wind: ${windSpeedKph}kph at ${windDegree}° -> offset($windX, $windY)');
      
      return Offset(windX, windY);
      
    } catch (e) {
      debugPrint('🌧️ Error parsing wind data: $e');
      return Offset(20, 0);
    }
  }
  
  void _updateWeatherParticles() {
    if (_screenSize == null) return;
    
    final size = _screenSize!;
    final random = math.Random();
    final now = DateTime.now().millisecondsSinceEpoch.toDouble();

    // Remove dead particles
    _weatherParticles.removeWhere((particle) {
      return particle.isDead || particle.position.dy > size.height + 20;
    });
    
    // Limit particle count for performance
    if (_weatherParticles.length > 1500) {
      _weatherParticles.removeRange(0, _weatherParticles.length - 1500);
    }
    
    // Update existing particles
    for (final particle in _weatherParticles) {
      particle.update(0.016, _wind); // 60fps
    }
    
    // Add new particles based on weather type
    if (_weatherParticles.length < 1200 && _currentWeather != 'clear') {
      _addWeatherParticles();
    }
  }
  
  void _addWeatherParticles() {
    if (_screenSize == null) return;
    
    final size = _screenSize!;
    final center = size.center(Offset.zero);
    final random = math.Random();

    // Center-thinning effect
    final spawnX = random.nextDouble() * size.width;
    final distFromCenter = (spawnX - center.dx).abs();
    
    switch (_currentWeather) {
      case 'rain':
        // Remove center-thinning for rain to cover entire screen
        for (int i = 0; i < 35; i++) {
          _weatherParticles.add(Particle(
            position: Offset(spawnX, -20),
            velocity: Offset(
              _wind.dx * 0.1, // Horizontal velocity from wind
              600 + random.nextDouble() * 400, // Vertical velocity: 600-1000 px/s
            ),
            size: 0.6 + random.nextDouble() * 1.0, // Droplet size
            color: Colors.blueGrey.withOpacity(0.5),
            opacity: 0.3 + random.nextDouble() * 0.3,
            life: 2.0,
            maxLife: 2.0,
            z: random.nextDouble(),
          ));
        }
        break;
        
      case 'snow':
        // Add snowflakes with center-thinning
        for (int i = 0; i < 3; i++) {
          if (distFromCenter < size.width * 0.15 && random.nextDouble() > 0.4) {
            continue;
          }
          
          _weatherParticles.add(Particle(
            position: Offset(spawnX, -20),
            velocity: Offset(
              math.sin(DateTime.now().millisecondsSinceEpoch / (800 + random.nextDouble() * 400)) * 40 + _wind.dx * 0.05,
              180 + random.nextDouble() * 120, // Slower fall: 180-300 px/s
            ),
            size: 2 + random.nextDouble() * 3,
            color: Colors.white.withOpacity(0.9),
            opacity: 0.7 + random.nextDouble() * 0.3,
            life: 4.0,
            maxLife: 4.0,
            z: random.nextDouble(),
          ));
        }
        break;
        
      case 'clouds':
        // Add subtle cloud particles - move straight from left to right
        if (random.nextDouble() < 0.1) {
          _weatherParticles.add(Particle(
            position: Offset(-50, random.nextDouble() * size.height * 0.1),
            velocity: Offset(30 + random.nextDouble() * 20 + _wind.dx * 0.1, 0), // Only horizontal movement
            size: 50 + random.nextDouble() * 70,
            color: Colors.white.withOpacity(0.2),
            opacity: 0.15 + random.nextDouble() * 0.2,
            life: 20.0,
            maxLife: 20.0,
            z: random.nextDouble() * 0.5,
          ));
        }
        break;
        
      case 'fog':
        // Add fog particles - like clouds but more transparent and covering more screen
        if (random.nextDouble() < 0.05) { // Much lower spawn rate to prevent stacking
          _weatherParticles.add(Particle(
            position: Offset(
              -80, // Start from left edge like clouds
              random.nextDouble() * size.height, // Cover full height
            ),
            velocity: Offset(
              15 + random.nextDouble() * 25 + _wind.dx * 0.1, // Horizontal movement only
              0, // No vertical movement
            ),
            size: 80 + random.nextDouble() * 140, // Moderate size
            color: Colors.grey.withOpacity(0.0005), // Even more transparent white/grey
            opacity: 0.015 + random.nextDouble() * 0.0035, // Ultra low opacity (1.5-5%)
            life: 25.0, // Longer life for slow movement
            maxLife: 25.0,
            z: random.nextDouble() * 0.3,
          ));
        }
        break;
    }
  }
  
  void _setWeatherType(String weather) {
    if (_currentWeather == weather) return;
    
    debugPrint('🌧️ Setting weather type to: $weather');
    _currentWeather = weather;
    _weatherParticles.clear();
    _lightningBolts.clear();
    
    if (weather == 'rain') {
      _scheduleLightning();
    } else {
      _lightningTimer?.cancel();
      _lightningFlashActive = false;
    }
    
    onStateChanged?.call();
  }
  
  void _setWind(Offset wind) {
    _wind = wind;
  }
  
  void _initializeLightningEffects() {
    if (_currentWeather == 'rain') {
      _scheduleLightning();
    }
  }
  
  void _scheduleLightning() {
    if (_currentWeather != 'rain') return;
    
    final random = math.Random();
    final delay = _minLightningInterval + 
                  random.nextDouble() * (_maxLightningInterval - _minLightningInterval);
    
    _lightningTimer?.cancel();
    _lightningTimer = Timer(Duration(milliseconds: delay.toInt()), () {
      if (_currentWeather == 'rain') {
        _triggerLightning();
        _scheduleLightning(); // Schedule next lightning
      }
    });
  }
  
  void _triggerLightning() {
    _lightningFlashActive = true;
    _lightningFlashController.forward().then((_) {
      _lightningFlashController.reverse().then((_) {
        _lightningFlashActive = false;
        onStateChanged?.call();
      });
    });
    
    HapticFeedback.lightImpact();
    onStateChanged?.call();
  }
  
  void createLightningBolt(Size size) {
    final random = math.Random();
    final startX = size.width * (0.2 + random.nextDouble() * 0.6);
    final endX = startX + (random.nextDouble() - 0.5) * size.width * 0.3;
    final endY = size.height * (0.4 + random.nextDouble() * 0.4);
    
    final segments = 8 + random.nextInt(5);
    double currentX = startX;
    double currentY = 0;
    
    for (int i = 0; i < segments; i++) {
      final targetX = startX + (endX - startX) * (i + 1) / segments +
                     (random.nextDouble() - 0.5) * 30;
      final targetY = endY * (i + 1) / segments;
      
      _lightningBolts.add(Particle(
        position: Offset(currentX, currentY),
        velocity: Offset(targetX - currentX, targetY - currentY),
        size: 2 + random.nextDouble() * 2,
        color: Colors.white.withOpacity(0.9),
        opacity: 0.8 + random.nextDouble() * 0.2,
        life: 0.3 + random.nextDouble() * 0.2,
        maxLife: 0.5,
        z: 1.0,
      ));
      
      currentX = targetX;
      currentY = targetY;
    }
  }
  
  void _updateLightningEffects() {
    _lightningBolts.removeWhere((bolt) => bolt.isDead);
    
    for (final bolt in _lightningBolts) {
      bolt.update(0.016, Offset.zero);
    }
  }
  
  // Public methods
  void toggleWeatherEffects() {
    _weatherEnabled = !_weatherEnabled;
    if (!_weatherEnabled) {
      _weatherParticles.clear();
      _lightningBolts.clear();
      _lightningFlashActive = false;
      _lightningTimer?.cancel();
    }
    onStateChanged?.call();
    debugPrint('🌧️ Weather effects ${_weatherEnabled ? 'enabled' : 'disabled'}');
  }
  
  String getWeatherIcon() {
    switch (_currentWeather) {
      case 'rain':
        return 'water_drop';
      case 'snow':
        return 'ac_unit';
      case 'clouds':
        return 'cloud';
      case 'fog':
        return 'foggy';
      default:
        return 'wb_sunny';
    }
  }
  
  String getWeatherDescription() {
    switch (_currentWeather) {
      case 'rain':
        return 'Rainy';
      case 'snow':
        return 'Snowy';
      case 'clouds':
        return 'Cloudy';
      case 'fog':
        return 'Foggy';
      default:
        return 'Clear';
    }
  }
  
  void updateWithScreenSize(Size screenSize) {
    _screenSize = screenSize;
    
    if (_lightningFlashActive && _lightningBolts.isEmpty) {
      createLightningBolt(screenSize);
    }
  }
  
  // Force refresh weather data (useful for manual refresh)
  Future<void> refreshWeather() async {
    debugPrint('🌧️ Forcing weather refresh...');
    _lastWeatherUpdate = null; // Reset last update time to force refresh
    await _checkLocationAndUpdateWeather();
  }
  
  // Get current weather info for display
  Map<String, dynamic> getCurrentWeatherInfo() {
    if (_cachedWeatherData == null) {
      return {
        'condition': _currentWeather,
        'description': getWeatherDescription(),
        'icon': getWeatherIcon(),
        'temperature': null,
        'location': null,
        'lastUpdate': null,
      };
    }
    
    try {
      final current = _cachedWeatherData!['current'] as Map<String, dynamic>?;
      final location = _cachedWeatherData!['location'] as Map<String, dynamic>?;
      
      return {
        'condition': _currentWeather,
        'description': getWeatherDescription(),
        'icon': getWeatherIcon(),
        'temperature': current?['temp_c'],
        'location': location?['name'],
        'lastUpdate': _lastWeatherUpdate,
        'windSpeed': current?['wind_kph'],
        'humidity': current?['humidity'],
        'visibility': current?['vis_km'],
      };
    } catch (e) {
      debugPrint('🌧️ Error getting weather info: $e');
      return {
        'condition': _currentWeather,
        'description': getWeatherDescription(),
        'icon': getWeatherIcon(),
        'temperature': null,
        'location': null,
        'lastUpdate': _lastWeatherUpdate,
      };
    }
  }
  
  void dispose() {
    _weatherTimer?.cancel();
    _lightningTimer?.cancel();
    _weatherUpdateTimer?.cancel();
    _locationCheckTimer?.cancel();
    
    _weatherAnimationController.dispose();
    _lightningFlashController.dispose();
    
    debugPrint('🌧️ Weather visual effects manager disposed');
  }
}
