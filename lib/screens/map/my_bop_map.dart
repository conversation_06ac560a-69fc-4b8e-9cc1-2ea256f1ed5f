import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_earth_globe/flutter_earth_globe.dart';
import 'package:flutter_earth_globe/flutter_earth_globe_controller.dart';
import 'package:flutter_earth_globe/globe_coordinates.dart';
import 'package:flutter_earth_globe/point.dart';
import 'package:flutter_earth_globe/point_connection.dart';
import 'package:flutter_earth_globe/point_connection_style.dart';
import 'package:flutter_earth_globe/sphere_style.dart';
import 'dart:math' as math;
import 'dart:async';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../widgets/common/app_loading_indicator.dart';
import '../../widgets/music/now_playing_bar.dart';
import '../../widgets/music/apple_now_playing_bar.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/now_playing_provider.dart';
import '../../models/music_track.dart';
import '../../models/pin.dart';
import '../../services/api/pins_service.dart';
import '../../widgets/music/sound_wave_overlay.dart';
import 'package:flutter/services.dart';
import '../../providers/pin_provider.dart';

class MyBOPMap extends StatefulWidget {
  final String? userId; // Add optional userId parameter
  final String? username; // Add optional username for better UX
  
  const MyBOPMap({
    super.key,
    this.userId,
    this.username,
  });

  @override
  State<MyBOPMap> createState() => _MyBOPMapState();
}

class _MyBOPMapState extends State<MyBOPMap> with SingleTickerProviderStateMixin {
  late FlutterEarthGlobeController _controller;
  bool _isLoading = true;
  bool _isShuffling = false;
  Point? _selectedPoint;
  late AnimationController _detailsAnimationController;
  late Animation<double> _detailsAnimation;
  
  // Now playing state
  Map<String, dynamic>? _nowPlayingTrack;
  bool _isPlaying = false;
  double _playbackProgress = 0.0;
  
  // Real pins data
  List<Pin> _userPins = [];
  final PinsService _pinsService = PinsService();
  String? _errorMessage;
  
  // New amazing features
  bool _isDayMode = false; // Default to night mode (dark) instead of day mode
  
  // Track connections for proper removal
  List<String> _activeConnectionIds = [];

  // =================== CLUSTERING SYSTEM ===================
  // Pin clustering state
  List<Point> _clusterPoints = [];
  List<Map<String, dynamic>> _clusters = [];
  static const double _clusterDistanceKm = 100.0; // 100km clustering distance for globe view
  static const double _maxZoomForClustering = 0.15; // Globe zoom threshold
  bool _isClusteredView = false;
  double _currentZoom = 0.1;
  
  // Cluster cache for performance
  final Map<String, List<Map<String, dynamic>>> _clusterCache = {};
  
  // Track individual pin points for switching
  List<Point> _individualPinPoints = [];

  @override
  void initState() {
    super.initState();
    
    // Initialize details animation controller
    _detailsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _detailsAnimation = CurvedAnimation(
      parent: _detailsAnimationController,
      curve: Curves.easeOutQuad,
    );
    
    _initializeGlobe();
    _loadUserPins(); // Load real pins
    
    // Start simulated playback progress
    _startPlaybackSimulation();
  }
  
  void _startPlaybackSimulation() {
    // Simulate playback progress for demo purposes
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted && _isPlaying && _nowPlayingTrack != null) {
        setState(() {
          _playbackProgress = (_playbackProgress + 0.01) % 1.0;
        });
        _startPlaybackSimulation();
      }
    });
  }

  void _initializeGlobe() {
    // Initialize controller with enhanced features - default to night mode
    _controller = FlutterEarthGlobeController(
      rotationSpeed: 0.02,
      isBackgroundFollowingSphereRotation: true,
      surface: Image.asset('assets/textures/earth_night.jpg').image, // Start with night texture
      // background: Image.asset('assets/textures/stars_background.jpg').image, // Will add via loadBackground
      zoom: 0.1,
    );

    _controller.onLoaded = () {
      debugPrint('🌍 Globe loaded with enhanced features!');
      
      // Apply stunning atmospheric effects
      _applyAtmosphericEffects();
      
      // Load dynamic background (with fallback)
      _loadAtmosphericBackground();
      
      _addPinsToGlobe();
      
      if (mounted) {
        setState(() {
          // _isLoading is handled by _addPinsToGlobe
        });
      }
    };
  }
  
  void _applyAtmosphericEffects() {
    _controller.setSphereStyle(SphereStyle(
      shadowColor: _isDayMode 
          ? Colors.blue.withOpacity(0.3)  // Day: blue atmospheric glow
          : Colors.purple.withOpacity(0.4), // Night: purple glow
      shadowBlurSigma: 25,
      showShadow: true,
      showGradientOverlay: true,
    ));
  }
  
  void _loadAtmosphericBackground() {
    // Load the beautiful star field background that rotates with the Earth
    try {
      _controller.loadBackground(
        Image.asset('assets/textures/stars_background.jpg').image,
      );
      print('✨ Stars background loaded to globe - will rotate with Earth!');
    } catch (e) {
      print('⚠️ Could not load stars background to globe: $e');
      print('ℹ️ Using main screen stars background as fallback');
    }
  }
  
  void _toggleDayNightMode() {
    setState(() {
      _isDayMode = !_isDayMode;
    });
    
    // Dynamically switch earth texture
    if (_isDayMode) {
      _controller.loadSurface(Image.asset('assets/textures/earth_day.jpg').image);
    } else {
      _controller.loadSurface(Image.asset('assets/textures/earth_night.jpg').image);
    }
    
    // Update atmospheric effects based on current setting
    _applyAtmosphericEffects();
    
    // Show feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isDayMode ? '☀️ Day Mode Activated' : '🌙 Night Mode Activated'),
        duration: const Duration(seconds: 1),
        backgroundColor: _isDayMode ? Colors.orange : Colors.indigo,
      ),
    );
  }
  
  void _addConnectionsBetweenPins() {
    // Create beautiful connections between pins with varied styles
    if (_userPins.length < 2) return;
    
    // Clear any existing connections first
    _clearAllConnections();
    
    // Create different types of connections for visual variety
    for (int i = 0; i < _userPins.length - 1; i++) {
      if (i % 2 == 0 && i + 1 < _userPins.length) { // Connect every other pin for better coverage
        final pin1 = _userPins[i];
        final pin2 = _userPins[i + 1];
        
        // Skip if pins are too close (same city/area)
        final distance = _calculateDistance(pin1.latitude, pin1.longitude, pin2.latitude, pin2.longitude);
        if (distance < 50) continue; // Reduced threshold to show more connections
        
        final connectionId = 'connection_${pin1.id}_${pin2.id}';
        final connectionStyle = _createBeautifulConnectionStyle(pin1, pin2, i);
        
        _controller.addPointConnection(PointConnection(
          id: connectionId,
          start: GlobeCoordinates(pin1.latitude, pin1.longitude),
          end: GlobeCoordinates(pin2.latitude, pin2.longitude),
          style: connectionStyle,
          isLabelVisible: false, // Keep clean, no labels on connections
          curveScale: _getCurveScale(distance) * 1.5, // Amplify curves for better 3D effect
        ));
        
        _activeConnectionIds.add(connectionId);
        print('🔗 Added connection: ${pin1.title} → ${pin2.title} (${distance.round()}km) - Color: ${connectionStyle.color}, Width: ${connectionStyle.lineWidth}');
      }
    }
    
    // Also create some cross-connections between distant pins for more interesting patterns
    if (_userPins.length > 4) {
      _addCrossConnections();
    }
    
    print('✨ Added ${_activeConnectionIds.length} beautiful connections between pins');
  }
  
  void _addCrossConnections() {
    // Create some long-distance connections for visual interest
    final random = math.Random();
    final maxCrossConnections = math.min(3, _userPins.length ~/ 4);
    
    for (int i = 0; i < maxCrossConnections; i++) {
      final pin1 = _userPins[random.nextInt(_userPins.length)];
      final pin2 = _userPins[random.nextInt(_userPins.length)];
      
      if (pin1.id == pin2.id) continue; // Skip same pin
      
      final distance = _calculateDistance(pin1.latitude, pin1.longitude, pin2.latitude, pin2.longitude);
      if (distance < 500) continue; // Only long-distance cross-connections
      
      final connectionId = 'cross_connection_${pin1.id}_${pin2.id}';
      
      _controller.addPointConnection(PointConnection(
        id: connectionId,
        start: GlobeCoordinates(pin1.latitude, pin1.longitude),
        end: GlobeCoordinates(pin2.latitude, pin2.longitude),
        style: PointConnectionStyle(
          type: PointConnectionType.dashed,
          color: Colors.cyan.withOpacity(1.0), // Bright cyan for high visibility
          lineWidth: 3.0, // Thicker line
          dashSize: 20, // Larger dashes
          spacing: 25,  // More spacing
        ),
        curveScale: 2.5, // Even higher curve for more dramatic 3D effect
      ));
      
      _activeConnectionIds.add(connectionId);
    }
  }
  
  PointConnectionStyle _createBeautifulConnectionStyle(Pin pin1, Pin pin2, int index) {
    // Create much more visual styles for better 3D visibility
    final service1Color = _getColorByService(pin1.service);
    final service2Color = _getColorByService(pin2.service);
    
    // Make colors much brighter and more prominent
    Color connectionColor;
    if (pin1.service == pin2.service) {
      connectionColor = service1Color.withOpacity(1.0); // Full opacity for visibility
    } else {
      // Create a bright blend for cross-platform connections
      connectionColor = Color.lerp(service1Color, service2Color, 0.5)!.withOpacity(0.9);
    }
    
    // Make all connections much more prominent
    PointConnectionType connectionType;
    double lineWidth;
    
    switch (index % 3) { // Simplified to 3 types for more variety
      case 0:
        connectionType = PointConnectionType.solid;
        lineWidth = 5.0; // Much thicker for visibility
        break;
      case 1:
        connectionType = PointConnectionType.dashed;
        lineWidth = 4.0; // Thicker dashed lines
        break;
      default:
        connectionType = PointConnectionType.dotted;
        lineWidth = 6.0; // Very thick dotted lines
        connectionColor = connectionColor.withOpacity(1.0); // Full brightness
    }
    
    return PointConnectionStyle(
      type: connectionType,
      color: connectionColor,
      lineWidth: lineWidth,
      dashSize: 15, // Larger dashes for visibility
      dotSize: 8,   // Larger dots for visibility
      spacing: 20,  // More spacing for clarity
    );
  }
  
  double _getCurveScale(double distance) {
    // Dynamic curve scale based on distance for natural-looking arcs
    if (distance < 500) return 1.2;      // Subtle curve for short distances
    if (distance < 2000) return 1.5;     // Medium curve for medium distances
    if (distance < 5000) return 1.8;     // Higher curve for long distances
    return 2.2;                          // Dramatic curve for intercontinental
  }
  
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    // Calculate distance between two points using Haversine formula (in km)
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    final double dLat = (lat2 - lat1) * (math.pi / 180);
    final double dLon = (lon2 - lon1) * (math.pi / 180);
    
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(lat1 * (math.pi / 180)) * math.cos(lat2 * (math.pi / 180)) *
        math.sin(dLon / 2) * math.sin(dLon / 2);
    
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }
  
  void _removeAllConnections() {
    _clearAllConnections();
    print('🚫 Removed all connections');
  }
  
  void _clearAllConnections() {
    // Remove all tracked connections
    for (String connectionId in _activeConnectionIds) {
      try {
        _controller.removePointConnection(connectionId);
      } catch (e) {
        print('⚠️ Could not remove connection $connectionId: $e');
      }
    }
    _activeConnectionIds.clear();
  }

  Future<void> _loadUserPins() async {
    try {
      final bool isViewingFriend = widget.userId != null;
      print('🔍 Loading ${isViewingFriend ? "friend" : "user"} pins${isViewingFriend ? " for user ${widget.userId}" : ""}...');
      
      final List<Pin> pins;
      if (isViewingFriend) {
        pins = await _pinsService.getFriendPins(widget.userId!);
      } else {
        pins = await _pinsService.getMyPins();
      }
      
      if (mounted) {
        setState(() {
          _userPins = pins;
          _errorMessage = null;
        });
        
        print('📍 Loaded ${pins.length} ${isViewingFriend ? "friend" : "user"} pins');
        
        // Add pins to globe after loading
        if (pins.isNotEmpty && _controller != null) {
          _addPinsToGlobe();
        } else if (pins.isEmpty) {
          // If no real pins, show message and still stop loading
          setState(() {
            _isLoading = false;
          });
          print('ℹ️ No ${isViewingFriend ? "friend" : "user"} pins found');
        }
      }
    } catch (e) {
      final bool isViewingFriend = widget.userId != null;
      print('❌ Error loading ${isViewingFriend ? "friend" : "user"} pins: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load ${isViewingFriend ? "friend's" : "your"} pins: $e';
          _isLoading = false;
        });
      }
    }
  }

  void _addPinsToGlobe() {
    try {
      print('🌍 ===== ADDING PINS TO GLOBE =====');
      print('🌍 Total user pins: ${_userPins.length}');
      print('🌍 Current zoom level: $_currentZoom');
      print('🌍 Max zoom for clustering: $_maxZoomForClustering');
      
      // Clear existing points manually since clearPoints() doesn't exist
      _clearExistingPoints();
      _clusterPoints.clear();
      _individualPinPoints.clear();
      _clusters.clear();
      
      if (_userPins.isEmpty) {
        print('🌍 No pins to display, stopping loading');
        setState(() {
          _isLoading = false;
        });
        return;
      }
      
      // Determine if we should cluster based on zoom level
      final shouldCluster = _currentZoom < _maxZoomForClustering;
      
      print('🌍 Should cluster? $shouldCluster (zoom $_currentZoom ${shouldCluster ? '<' : '>='} $_maxZoomForClustering)');
      
      if (shouldCluster && _userPins.length > 1) {
        print('🎯 ==> CLUSTERING MODE: Creating clusters for ${_userPins.length} pins');
        _createAndDisplayClusters();
      } else {
        print('📍 ==> INDIVIDUAL MODE: Displaying ${_userPins.length} individual pins');
        _displayIndividualPins();
      }
      
      // Add connections if they should be shown (only for individual pins)
      if (!shouldCluster && _userPins.length >= 2) {
        print('🔗 Adding connections after pins are loaded...');
        _addConnectionsBetweenPins();
      } else if (shouldCluster) {
        print('🔗 Skipping connections (clustered mode)');
      } else {
        print('🔗 Skipping connections (not enough pins: ${_userPins.length})');
      }
      
      _isClusteredView = shouldCluster;
      
      print('🌍 ===== PINS ADDED TO GLOBE =====');
      print('🌍 Clustered view: $_isClusteredView');
      print('🌍 Clusters created: ${_clusters.where((c) => c['isCluster'] == true).length}');
      print('🌍 Individual items: ${_clusters.where((c) => c['isCluster'] != true).length}');
      print('🌍 Cluster points on globe: ${_clusterPoints.length}');
      print('🌍 Individual pin points on globe: ${_individualPinPoints.length}');
      
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      
      print('✅ Successfully added pins to globe (clustered: $shouldCluster)');
    } catch (e) {
      print('❌ Error adding pins to globe: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to display pins on globe: $e';
        });
      }
    }
  }

  /// Clear existing points manually since FlutterEarthGlobeController doesn't have clearPoints
  void _clearExistingPoints() {
    // Remove cluster points
    for (final point in _clusterPoints) {
      try {
        _controller.removePoint(point.id);
      } catch (e) {
        print('Warning: Could not remove cluster point ${point.id}: $e');
      }
    }
    
    // Remove individual pin points  
    for (final point in _individualPinPoints) {
      try {
        _controller.removePoint(point.id);
      } catch (e) {
        print('Warning: Could not remove individual point ${point.id}: $e');
      }
    }
  }

  // =================== CLUSTERING METHODS ===================
  
  /// Create clusters from user pins using geographic distance
  List<Map<String, dynamic>> _createClusters(List<Pin> pins) {
    if (pins.length <= 1) {
      print('🎯 Not clustering: only ${pins.length} pins');
      return [];
    }
    
    // Check cache first
    final cacheKey = 'zoom_${_currentZoom.toStringAsFixed(2)}';
    if (_clusterCache.containsKey(cacheKey)) {
      print('🎯 Using cached clusters for zoom level $_currentZoom');
      return _clusterCache[cacheKey]!;
    }
    
    print('🎯 Creating fresh clusters for ${pins.length} pins at zoom $_currentZoom');
    print('🎯 Cluster distance threshold: ${_clusterDistanceKm}km');
    
    final clusters = <Map<String, dynamic>>[];
    final remainingPins = List<Pin>.from(pins);
    
    while (remainingPins.isNotEmpty) {
      final currentPin = remainingPins.removeAt(0);
      final clusterPins = [currentPin];
      
      print('🎯 Processing pin: ${currentPin.title} at (${currentPin.latitude}, ${currentPin.longitude})');
      
      // Find nearby pins to cluster with
      remainingPins.removeWhere((pin) {
        final distance = _calculateDistanceInKm(
          currentPin.latitude, currentPin.longitude,
          pin.latitude, pin.longitude,
        );
        
        print('🎯 Distance from ${currentPin.title} to ${pin.title}: ${distance.toStringAsFixed(1)}km');
        
        if (distance < _clusterDistanceKm) {
          clusterPins.add(pin);
          print('🎯 Added ${pin.title} to cluster (${distance.toStringAsFixed(1)}km < ${_clusterDistanceKm}km)');
          return true;
        }
        return false;
      });
      
      if (clusterPins.length > 1) {
        // Create cluster
        final clusterCenter = _calculateClusterCenter(clusterPins);
        final clusterId = 'cluster_${clusters.length}_${DateTime.now().millisecondsSinceEpoch}';
        clusters.add({
          'id': clusterId,
          'isCluster': true,
          'count': clusterPins.length,
          'pins': clusterPins,
          'latitude': clusterCenter.latitude,
          'longitude': clusterCenter.longitude,
          'center': clusterCenter,
        });
        
        print('🎯 ✅ Created cluster "$clusterId" with ${clusterPins.length} pins at (${clusterCenter.latitude}, ${clusterCenter.longitude})');
        print('🎯    Pins in cluster: ${clusterPins.map((p) => p.title).join(", ")}');
      } else {
        // Single pin - add as individual
        clusters.add({
          'id': currentPin.id.toString(),
          'isCluster': false,
          'pin': currentPin,
          'latitude': currentPin.latitude,
          'longitude': currentPin.longitude,
        });
        
        print('🎯 Added individual pin: ${currentPin.title}');
      }
    }
    
    final clusterCount = clusters.where((c) => c['isCluster']).length;
    final individualCount = clusters.where((c) => !c['isCluster']).length;
    
    print('🎯 ===== CLUSTERING COMPLETE =====');
    print('🎯 Created $clusterCount clusters and $individualCount individual pins');
    print('🎯 Total cluster items: ${clusters.length}');
    
    // Cache the result
    _clusterCache[cacheKey] = clusters;
    _cleanClusterCache();
    
    return clusters;
  }
  
  /// Calculate distance between two points in kilometers
  double _calculateDistanceInKm(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    final double dLat = (lat2 - lat1) * (math.pi / 180);
    final double dLon = (lon2 - lon1) * (math.pi / 180);
    
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(lat1 * (math.pi / 180)) * math.cos(lat2 * (math.pi / 180)) *
        math.sin(dLon / 2) * math.sin(dLon / 2);
    
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }
  
  /// Calculate the center point of a cluster
  GlobeCoordinates _calculateClusterCenter(List<Pin> pins) {
    double totalLat = 0;
    double totalLng = 0;
    
    for (final pin in pins) {
      totalLat += pin.latitude;
      totalLng += pin.longitude;
    }
    
    return GlobeCoordinates(totalLat / pins.length, totalLng / pins.length);
  }
  
  /// Create and display clusters on the globe
  void _createAndDisplayClusters() {
    print('🎯 ===== CREATING AND DISPLAYING CLUSTERS =====');
    final clusters = _createClusters(_userPins);
    _clusters = clusters;
    
    print('🎯 Processing ${clusters.length} cluster items for display');
    
    for (final cluster in clusters) {
      if (cluster['isCluster'] == true) {
        print('🎯 Adding cluster to globe: ${cluster['id']} with ${cluster['count']} pins');
        _addClusterToGlobe(cluster);
      } else {
        print('🎯 Adding individual pin to globe: ${cluster['pin'].title}');
        _addIndividualPinToGlobe(cluster['pin'] as Pin);
      }
    }
    
    print('🎯 ===== CLUSTER DISPLAY COMPLETE =====');
    print('🎯 Total cluster points on globe: ${_clusterPoints.length}');
    print('🎯 Total individual pin points on globe: ${_individualPinPoints.length}');
  }
  
  /// Add a cluster point to the globe
  void _addClusterToGlobe(Map<String, dynamic> cluster) {
    final count = cluster['count'] as int;
    final center = cluster['center'] as GlobeCoordinates;
    
    final clusterPoint = Point(
      id: cluster['id'].toString(),
      coordinates: center,
      label: '$count pins',
      isLabelVisible: true,
      style: const PointStyle(color: Colors.transparent, size: 0),
      labelBuilder: (context, point, isHovering, visible) {
        return Transform.translate(
          offset: const Offset(0, -100),
          child: GestureDetector(
            onTap: () => _handleClusterTap(cluster),
            child: _buildClusterWidget(count, isHovering),
          ),
        );
      },
      onTap: () => _handleClusterTap(cluster),
      onHover: () => print('🎯 Hovering over cluster with $count pins'),
    );
    
    _controller.addPoint(clusterPoint);
    _clusterPoints.add(clusterPoint);
  }
  
  /// Display individual pins without clustering
  void _displayIndividualPins() {
    print('📍 ===== DISPLAYING INDIVIDUAL PINS =====');
    print('📍 Processing ${_userPins.length} individual pins');
    
    for (final pin in _userPins) {
      print('📍 Adding individual pin: ${pin.title}');
      _addIndividualPinToGlobe(pin);
    }
    
    print('📍 ===== INDIVIDUAL PINS DISPLAY COMPLETE =====');
    print('📍 Individual pin points on globe: ${_individualPinPoints.length}');
  }
  
  /// Add an individual pin to the globe
  void _addIndividualPinToGlobe(Pin pin) {
    final latitude = pin.latitude;
    final longitude = pin.longitude;
    
    print('📌 Adding individual pin: ${pin.title} at lat: $latitude, lng: $longitude');
    
    final point = Point(
      id: pin.id.toString(),
      coordinates: GlobeCoordinates(latitude, longitude),
      label: pin.title,
      isLabelVisible: true,
      style: const PointStyle(color: Colors.transparent, size: 0),
      labelBuilder: (context, point, isHovering, visible) {
        final bool isSelected = _selectedPoint?.id == point.id;
        final double iconSize = _getPointSizeByRarity(pin.rarity) * 3;
        final Color pinColor = _getColorByService(pin.service);
        
        final pinWidget = _buildGlobePinWidget(pin, iconSize, pinColor, isSelected);
        
        return Transform.translate(
          offset: const Offset(0, -130),
          child: GestureDetector(
            onTap: () => _handlePointTap(pin),
            child: pinWidget,
          ),
        );
      },
      onTap: () => _handlePointTap(pin),
      onHover: () => _handlePointHover(pin),
    );
    
    _controller.addPoint(point);
    _individualPinPoints.add(point);
  }
  
  /// Clean cluster cache to prevent memory leaks
  void _cleanClusterCache() {
    if (_clusterCache.length > 10) {
      final sortedKeys = _clusterCache.keys.toList()..sort();
      final keysToRemove = sortedKeys.take(_clusterCache.length - 5).toList();
      for (final key in keysToRemove) {
        _clusterCache.remove(key);
      }
      print('🎯 Cleaned cluster cache, removed ${keysToRemove.length} old entries');
    }
  }

  double _getPointSizeByRarity(String rarity) {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return 12.0;
      case 'epic':
        return 10.0;
      case 'rare':
        return 8.0;
      case 'common':
      default:
        return 6.0;
    }
  }

  void _handlePointTap(Pin pin) {
    // Focus on the pin by moving camera
    _controller.focusOnCoordinates(
      GlobeCoordinates(pin.latitude, pin.longitude),
      animate: true,
    );
    
    setState(() {
      _selectedPoint = Point(
        id: pin.id.toString(),
        coordinates: GlobeCoordinates(pin.latitude, pin.longitude),
      );
    });
    
    _detailsAnimationController.forward();
  }

  void _handlePointHover(Pin pin) {
    // Show a subtle hover effect or tooltip
    debugPrint('Hovering over: ${pin.title}');
  }

  void _closeDetails() {
    _detailsAnimationController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _selectedPoint = null;
        });
      }
    });
  }
  
  // Play the selected track using the comprehensive playback system from pin_card.dart
  Future<void> _playTrack(Pin pin) async {
    try {
      final service = pin.service;
      
      if (kDebugMode) {
        print('🎵 [MyBOPMap] Playing track: ${pin.trackTitle} by ${pin.trackArtist} (service: $service)');
      }
      
      // Get PinProvider to record interactions
      final pinProvider = Provider.of<PinProvider>(context, listen: false);
      
      // Attempt to normalize Apple Music URIs to the `apple:track:` form so
      // the AppleMusicProvider can bypass the search step and play directly.
      String? originalUri = pin.trackUrl;
      String normalizedUri = originalUri;

      if ((service == 'apple_music' || service == 'apple') && originalUri.isNotEmpty) {
        if (originalUri.contains('music.apple.com') && originalUri.contains('?i=')) {
          // Album-style URL with ?i=trackId parameter
          final trackId = originalUri.split('?i=').last.split('&').first;
          if (trackId.isNotEmpty) {
            normalizedUri = 'apple:track:$trackId';
            if (kDebugMode) {
              print('🍎 [MyBOPMap] Extracted Apple Music track ID "$trackId" from album URL');
            }
          }
        } else if (originalUri.contains('music.apple.com') && originalUri.contains('/song/')) {
          // Direct song URL format …/song/name/trackId
          final parts = originalUri.split('/');
          final trackId = parts.isNotEmpty ? parts.last.split('?').first : '';
          if (trackId.isNotEmpty) {
            normalizedUri = 'apple:track:$trackId';
            if (kDebugMode) {
              print('🍎 [MyBOPMap] Extracted Apple Music track ID "$trackId" from song URL');
            }
          }
        }
      }

      final pinData = {
        'id': pin.id.toString(),
        'title': pin.title,
        'track_title': pin.trackTitle,
        'artist': pin.trackArtist,
        'track_artist': pin.trackArtist,
        'album': pin.album ?? '',
        'artwork_url': pin.artworkUrl,
        'uri': normalizedUri.isNotEmpty ? normalizedUri : originalUri,
        'url': originalUri,
        'service': pin.service,
        'track_url': pin.trackUrl,
        'caption': pin.caption ?? '',
        'description': pin.description ?? '',
        'location': {
          'type': 'Point',
          'coordinates': [pin.longitude, pin.latitude],
        },
        'latitude': pin.latitude,
        'longitude': pin.longitude,
        'aura_radius': pin.auraRadius,
        'rarity': pin.rarity,
        'skin': pin.skin,
        'skin_details': pin.skinDetails,
        'is_private': pin.isPrivate,
        'created_at': pin.createdAt.toIso8601String(),
        'updated_at': pin.updatedAt.toIso8601String(),
        'duration_ms': pin.durationMs,
        'location_name': pin.locationName,
        'upvote_count': pin.upvoteCount,
        'downvote_count': pin.downvoteCount,
        'owner': {
          'id': pin.owner.id,
          'username': pin.owner.username,
          'email': pin.owner.email,
          'profile_pic': pin.owner.profilePicUrl,
          'is_verified': pin.owner.isVerified,
          'favorite_genres': pin.owner.favoriteGenres,
          'connected_services': pin.owner.connectedServices,
          'created_at': pin.owner.createdAt.toIso8601String(),
        },
        'interaction_count': pin.interactionCount,
        'engagement_counts': pin.engagementCounts,
      };

      // Set currently playing pin (this will automatically record view and collect interactions)
      pinProvider.setCurrentlyPlayingPin(pin.id.toString(), pinData);

      bool success = false;
      
      if (service == 'spotify') {
        final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
        
        // Use automatic connection and fallback system
        success = await spotifyProvider.playTrackFromPin(pinData, context: context);
      } else if (service == 'apple_music' || service == 'apple') {
        success = await _tryAppleMusicWithSpotifyFallback(pinData);
      } else {
        _showError('Unsupported music service: $service');
        return;
      }

      if (!success) {
        // Clear the pin data if playback failed
        pinProvider.clearCurrentlyPlayingPin();
        _showError('Failed to play track');
      } else {
        _showSuccess('Now playing: ${pin.trackTitle}');
      }
    } catch (e) {
      // Clear the pin data on error
      final pinProvider = Provider.of<PinProvider>(context, listen: false);
      pinProvider.clearCurrentlyPlayingPin();
      
      if (kDebugMode) {
        print('❌ [MyBOPMap] Error playing track: $e');
      }
      
      // Provide more specific error messages
      String errorMessage = 'Error playing track';
      if (e.toString().toLowerCase().contains('unauthorized') ||
          e.toString().toLowerCase().contains('401')) {
        errorMessage = 'Authentication expired. Please reconnect in Settings.';
      } else if (e.toString().toLowerCase().contains('subscription')) {
        errorMessage = 'Music subscription required';
      } else if (e.toString().toLowerCase().contains('not found')) {
        errorMessage = 'Track not available';
      } else {
        errorMessage = 'Error playing track: ${e.toString().split(':').last.trim()}';
      }
      
      _showError(errorMessage);
    }
  }

  /// Try Apple Music first, fallback to Spotify if needed
  Future<bool> _tryAppleMusicWithSpotifyFallback(Map<String, dynamic> pinData) async {
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    
    
    try {
      // Try Apple Music first
      if (!appleMusicProvider.isConnected) {
        final connected = await appleMusicProvider.connect();
        if (!connected) {
          if (kDebugMode) {
            print('🎵 [MyBOPMap] Apple Music connection failed, trying Spotify fallback...');
          }
          return await _trySpotifyFallback(pinData, 'Apple Music not available');
        }
      }

    // Show loading state
    _showInfo('Trying Apple Music...');
      
      final success = await appleMusicProvider.playTrackFromPin(pinData);
      
      if (success) {
        final trackTitle = pinData['title'] ?? pinData['track_title'] ?? 'Unknown Song';
        _showSuccess('Now playing: $trackTitle (Apple Music)');
        return true;
      }
      
      // Check if we should try Spotify fallback
      if (appleMusicProvider.errorMessage != null) {
        final errorMsg = appleMusicProvider.errorMessage!;
        
        // Check for fallback indicator from the provider
        if (errorMsg.startsWith('FALLBACK_TO_SPOTIFY:')) {
          final reason = errorMsg.substring('FALLBACK_TO_SPOTIFY:'.length);
          if (kDebugMode) {
            print('🎵 [MyBOPMap] Apple Music provider requested Spotify fallback: $reason');
          }
          return await _trySpotifyFallback(pinData, reason);
        }
        
        // Check for specific error types that warrant fallback
        if (errorMsg.toLowerCase().contains('not available') ||
            errorMsg.toLowerCase().contains('authentication') ||
            errorMsg.toLowerCase().contains('subscription') ||
            errorMsg.toLowerCase().contains('unauthorized')) {
          
          if (kDebugMode) {
            print('🎵 [MyBOPMap] Apple Music failed with recoverable error, trying Spotify: $errorMsg');
          }
          return await _trySpotifyFallback(pinData, errorMsg);
        }
        
        // For other errors, show the Apple Music error directly
        _showError(errorMsg);
        return false;
      }
      
      // Generic Apple Music failure
      if (kDebugMode) {
        print('🎵 [MyBOPMap] Apple Music playback failed, trying Spotify fallback...');
      }
      return await _trySpotifyFallback(pinData, 'Apple Music playback failed');
      
    } catch (e) {
      if (kDebugMode) {
        print('🎵 [MyBOPMap] Apple Music error, trying Spotify fallback: $e');
      }
      return await _trySpotifyFallback(pinData, 'Apple Music error: ${e.toString()}');
    }
  }

  /// Try Spotify as a fallback using the automatic system
  Future<bool> _trySpotifyFallback(Map<String, dynamic> pinData, String appleMusicReason) async {
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    
    try {
      if (kDebugMode) {
        print('🎵 [MyBOPMap] Using automatic Spotify fallback for: ${pinData['title']}');
        print('🎵 [MyBOPMap] Apple Music reason: $appleMusicReason');
      }
      
      // Simply use the SpotifyProvider's automatic fallback system
      // This will handle connection, premium checks, and Apple Music fallback internally
      final success = await spotifyProvider.playTrackFromPin(pinData, context: context);
      
      if (success) {
        if (kDebugMode) {
          print('✅ [MyBOPMap] Successfully played track via automatic fallback: ${pinData['title']}');
        }
        
        // Note: SpotifyProvider will show its own success/fallback messages,
        // so we don't need to show a duplicate message here
        return true;
      } else {
        // Both services failed (SpotifyProvider handles the error messages)
        if (kDebugMode) {
          print('❌ [MyBOPMap] Automatic fallback failed for: ${pinData['title']}');
        }
        return false;
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [MyBOPMap] Error in automatic fallback: $e');
      }
      return false;
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showInfo(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
            ),
            const SizedBox(width: 12),
            Text(message),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Zoom in to show individual pins instead of cluster
  void _zoomToShowIndividualPins(Map<String, dynamic> cluster) {
    final clusterCenter = cluster['center'] as GlobeCoordinates;
    
    // Increase zoom level to show individual pins
    _currentZoom = _maxZoomForClustering + 0.05;
    
    // Focus and zoom to the cluster center
    _controller.focusOnCoordinates(clusterCenter, animate: true);
    
    // Wait for animation, then refresh pins
    Timer(const Duration(milliseconds: 800), () {
      if (mounted) {
        _addPinsToGlobe();
      }
    });
  }

  /// Toggle between clustered and individual view
  void _toggleClusterView() {
    // Determine what the new state should be based on current zoom, not _isClusteredView flag
    final currentlyShowingClusters = _currentZoom < _maxZoomForClustering;
    
    if (currentlyShowingClusters) {
      // Currently showing clusters, switch to individual view
      _currentZoom = _maxZoomForClustering + 0.05; // 0.20
      print('🎯 Switching to individual view (zoom: $_currentZoom)');
    } else {
      // Currently showing individual pins, switch to clustered view  
      _currentZoom = _maxZoomForClustering - 0.05; // 0.10
      print('🎯 Switching to clustered view (zoom: $_currentZoom)');
    }
    
    // Clear cache to force fresh clustering calculation
    _clusterCache.clear();
    
    // Refresh pins with new zoom level
    _addPinsToGlobe();
    
    print('🎯 Toggle complete. New state - isClusteredView: $_isClusteredView, zoom: $_currentZoom');
  }

  /// Zoom in to show more detail
  void _zoomIn() {
    final newZoom = math.min(1.0, _currentZoom + 0.1);
    _currentZoom = newZoom;
    print('🔍 Zooming in to: $_currentZoom');
    
    // Clear cache if crossing clustering threshold
    if (_currentZoom >= _maxZoomForClustering) {
      _clusterCache.clear();
    }
    
    Timer(const Duration(milliseconds: 200), () {
      if (mounted) {
        _addPinsToGlobe();
      }
    });
  }

  /// Zoom out to show broader view (clusters)
  void _zoomOut() {
    final newZoom = math.max(0.05, _currentZoom - 0.1);
    _currentZoom = newZoom;
    print('🔍 Zooming out to: $_currentZoom');
    
    // Clear cache if crossing clustering threshold
    if (_currentZoom < _maxZoomForClustering) {
      _clusterCache.clear();
    }
    
    Timer(const Duration(milliseconds: 200), () {
      if (mounted) {
        _addPinsToGlobe();
      }
    });
  }

  Future<void> _shuffleAndSelectRandomPin() async {
    if (_isShuffling || _userPins.isEmpty) return;
    
    setState(() {
      _isShuffling = true;
    });
    
    // Close current details if open
    if (_selectedPoint != null) {
      _closeDetails();
      await Future.delayed(const Duration(milliseconds: 300));
    }
    
    // Start fast rotation for shuffle effect
    _controller.startRotation();
    _controller.setRotationSpeed(0.15); // Much faster rotation for shuffle effect
    
    // Let it spin for 3-4 seconds
    await Future.delayed(const Duration(milliseconds: 3500));
    
    // Reset to normal rotation speed
    _controller.setRotationSpeed(0.02);
    
    // Select a random pin
    final randomPin = _userPins[math.Random().nextInt(_userPins.length)];
    
    // If we're in clustered view and the random pin is in a cluster, zoom in first
    if (_isClusteredView) {
      // Find the cluster containing this pin
      for (final cluster in _clusters) {
        if (cluster['isCluster'] == true) {
          final pins = cluster['pins'] as List<Pin>;
          if (pins.any((p) => p.id == randomPin.id)) {
            print('🎲 Random pin is in a cluster, zooming in first');
            _zoomToShowIndividualPins(cluster);
            await Future.delayed(const Duration(milliseconds: 1000));
            break;
          }
        }
      }
    }
    
    // Focus on the random pin
    _controller.focusOnCoordinates(
      GlobeCoordinates(randomPin.latitude, randomPin.longitude),
      animate: true,
    );
    
    // Wait a bit for the focus animation, then show details
    await Future.delayed(const Duration(milliseconds: 800));
    
    // Stop the globe movement after shuffle and focus
    _controller.stopRotation();

    setState(() {
      _selectedPoint = _controller.points.firstWhere(
        (point) => point.id == randomPin.id.toString(),
        orElse: () => Point(
          id: randomPin.id.toString(),
          coordinates: GlobeCoordinates(randomPin.latitude, randomPin.longitude),
        ),
      );
      _isShuffling = false;
    });
    
    _detailsAnimationController.forward();
  }

  Pin? _getSelectedPinData() {
    if (_selectedPoint == null) return null;
    try {
      return _userPins.firstWhere(
        (pin) => pin.id.toString() == _selectedPoint!.id,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  void dispose() {
    // Clean up connections before disposing
    _clearAllConnections();
    
    // Clean up clustering resources
    _clusterCache.clear();
    _clusters.clear();
    _clusterPoints.clear();
    _individualPinPoints.clear();
    
    _detailsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Consumer3<SpotifyProvider, AppleMusicProvider, NowPlayingProvider>(
      builder: (context, spotifyProvider, appleMusicProvider, nowPlayingProvider, child) {
        // Calculate layout adjustments - check both music providers
        final hasNowPlaying = spotifyProvider.hasActivePlayback || 
                              (appleMusicProvider.isPlaying && appleMusicProvider.currentTrack != null);
        final hasDetailPanel = _selectedPoint != null;
        final isNowPlayingExpanded = nowPlayingProvider.isExpanded;
        
        // Globe should have static positioning - no adjustments for detail panel
        final double globeBottomPadding = hasNowPlaying ? 100 : 20; // Only adjust for now playing
        
        return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isDark ? Colors.black38 : Colors.white38,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.pop(context),
                tooltip: 'Back',
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isDark ? Colors.black38 : Colors.white38,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.info_outline),
                  onPressed: () => _showInfoDialog(),
                  tooltip: 'Info',
                ),
              ),
            ],
          ),
          body: Stack(
            children: [
              // Stars background covering the entire screen
              Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/textures/stars_background.jpg'),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              
              // Earth Globe - always visible, not hidden during loading
              SafeArea(
                child: Padding(
                  padding: EdgeInsets.only(bottom: globeBottomPadding),
                  child: FlutterEarthGlobe(
                    controller: _controller,
                    radius: 150,
                      alignment: const Alignment(0, -0.3), // Static position - never changes
                  ),
                ),
              ),
                
              // Epic Sound Wave Animation when music is playing! 🎵✨
              if (!_isLoading && _userPins.isNotEmpty)
                SoundWaveOverlay(
                  isPlaying: (spotifyProvider.isPlaying && spotifyProvider.currentTrack != null) || 
                           (appleMusicProvider.isPlaying && appleMusicProvider.currentTrack != null),
                  isDayMode: _isDayMode,
                  currentPosition: spotifyProvider.isPlaying 
                      ? spotifyProvider.position 
                      : (appleMusicProvider.position?.inMilliseconds),
                  duration: spotifyProvider.isPlaying 
                      ? spotifyProvider.duration 
                      : (appleMusicProvider.duration?.inMilliseconds),
                  trackId: spotifyProvider.isPlaying ? spotifyProvider.currentTrack?.id : appleMusicProvider.currentTrack?.id,
                ),
                
              // Empty state removed - just show clean globe while pins load
              
              // Error state
              if (!_isLoading && _errorMessage != null)
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 80,
                        color: Colors.red.withOpacity(0.7),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Failed to Load Pins',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage!,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.6),
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _isLoading = true;
                            _errorMessage = null;
                          });
                          _loadUserPins();
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('Retry'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
              
              // Control buttons - static positioning, never moves
              if (!_isLoading && _userPins.isNotEmpty && !isNowPlayingExpanded)
                Positioned(
                  right: 16,
                  bottom: hasNowPlaying ? 120 : 80, // Only adjust for now playing bar, never for detail panel
                  child: Column(
                    children: [
                      
                      // Cluster Toggle button
                      _buildControlButton(
                        icon: (_currentZoom < _maxZoomForClustering) ? Icons.scatter_plot : Icons.hub,
                        onPressed: _toggleClusterView,
                        tooltip: (_currentZoom < _maxZoomForClustering) ? 'Show Individual Pins' : 'Show Clusters',
                        backgroundColor: (_currentZoom < _maxZoomForClustering) ? Colors.orange : Colors.indigo,
                      ),
                      const SizedBox(height: 8),
                      
                      // Day/Night Mode Toggle
                      _buildControlButton(
                        icon: _isDayMode ? Icons.wb_sunny : Icons.nights_stay,
                        onPressed: _toggleDayNightMode,
                        tooltip: _isDayMode ? 'Switch to Night Mode' : 'Switch to Day Mode',
                        backgroundColor: _isDayMode ? Colors.orange : Colors.indigo,
                      ),
                      const SizedBox(height: 8),
                      
                      _buildControlButton(
                        icon: _isShuffling ? Icons.hourglass_empty : Icons.casino,
                        onPressed: _isShuffling ? () {} : _shuffleAndSelectRandomPin,
                        tooltip: _isShuffling ? 'Shuffling...' : 'Shuffle Random Track',
                      ),
                      const SizedBox(height: 8),
                      _buildControlButton(
                        icon: Icons.share,
                        onPressed: () => _sharePin(_getSelectedPinData()!),
                        tooltip: 'Share Pin',
                      ),
                    ],
                  ),
                ),
              
              // Pin details panel
              if (_selectedPoint != null && !isNowPlayingExpanded)
                AnimatedBuilder(
                  animation: _detailsAnimation,
                  builder: (context, child) {
                    return Positioned(
                      left: 0,
                      right: 0,
                      bottom: ((hasNowPlaying ? 80 : 0) + 40) + (-300 * (1 - _detailsAnimation.value)),
                      child: _buildPinDetailsPanel(),
                    );
                  },
                ),
              
              // Now Playing bar - show appropriate bar based on active service
              if (hasNowPlaying)
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Card(
                    margin: const EdgeInsets.all(20),
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(16)),
                    ),
                    elevation: 8,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: _buildNowPlayingBar(spotifyProvider, appleMusicProvider),
                    ),
                  ),
                ),
              
              // Stats overlay - static positioning, never moves
              if (!_isLoading && !isNowPlayingExpanded)
                Positioned(
                  top: 100,
                  left: 16,
                  child: _buildStatsOverlay(),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    Color? backgroundColor,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? (isDark ? Colors.black54 : Colors.white54),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(icon),
        onPressed: onPressed,
        tooltip: tooltip,
        color: backgroundColor != null ? Colors.white : (isDark ? Colors.white : Colors.black87),
      ),
    );
  }

  Widget _buildStatsOverlay() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: (isDark ? Colors.black : Colors.white).withOpacity(0.8),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                Icons.music_note,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 4),
              Text(
                'Music Globe',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${_userPins.length} tracks discovered',
            style: TextStyle(
              fontSize: 10,
              color: (isDark ? Colors.white : Colors.black87).withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 6),
          
          // Clustering status
          if (_userPins.length > 1) ...[
            Row(
              children: [
                Icon(
                  _isClusteredView ? Icons.hub : Icons.scatter_plot,
                  size: 12,
                  color: _isClusteredView ? Colors.orange : Colors.indigo,
                ),
                const SizedBox(width: 4),
                Text(
                  _isClusteredView 
                      ? '${_clusters.where((c) => c['isCluster'] == true).length} clusters'
                      : 'Individual pins',
                  style: TextStyle(
                    fontSize: 9,
                    color: (isDark ? Colors.white : Colors.black87).withOpacity(0.6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            
            // Show cluster details if in clustered mode
            if (_isClusteredView && _clusters.isNotEmpty) ...[
              Row(
                children: [
                  Icon(
                    Icons.scatter_plot,
                    size: 12,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${_clusters.where((c) => c['isCluster'] != true).length} individual',
                    style: TextStyle(
                      fontSize: 9,
                      color: (isDark ? Colors.white : Colors.black87).withOpacity(0.6),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 2),
            ],
          ],
          
          // Enhanced status indicators
          Row(
            children: [
              Icon(
                _isDayMode ? Icons.wb_sunny : Icons.nights_stay,
                size: 12,
                color: _isDayMode ? Colors.orange : Colors.indigo,
              ),
              const SizedBox(width: 4),
              Text(
                _isDayMode ? 'Day' : 'Night',
                style: TextStyle(
                  fontSize: 9,
                  color: (isDark ? Colors.white : Colors.black87).withOpacity(0.6),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 2),
          Row(
            children: [
              Icon(
                Icons.blur_on,
                size: 12,
                color: Colors.blue,
              ),
              const SizedBox(width: 4),
              Text(
                'Atmosphere',
                style: TextStyle(
                  fontSize: 9,
                  color: (isDark ? Colors.white : Colors.black87).withOpacity(0.6),
                ),
              ),
            ],
          ),
          
          // Zoom level indicator
          const SizedBox(height: 2),
          Row(
            children: [
              Icon(
                Icons.zoom_in,
                size: 12,
                color: Colors.green,
              ),
              const SizedBox(width: 4),
              Text(
                'Zoom: ${(_currentZoom * 100).round()}%',
                style: TextStyle(
                  fontSize: 9,
                  color: (isDark ? Colors.white : Colors.black87).withOpacity(0.6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPinDetailsPanel() {
    final pinData = _getSelectedPinData();
    if (pinData == null) return const SizedBox.shrink();
    
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      height: 450, // Increased height to fit all content
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[900] : Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with album art and info
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Album artwork
                      Container(
                        width: 70,
                        height: 70,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.grey.withOpacity(0.2),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: pinData.artworkUrl != null && pinData.artworkUrl!.isNotEmpty
                              ? CachedNetworkImage(
                                  imageUrl: pinData.artworkUrl!,
                                  imageBuilder: (context, imageProvider) => Image(
                                    image: imageProvider,
                                    fit: BoxFit.cover,
                                  ),
                                  placeholder: (context, url) => const Icon(Icons.music_note, color: Colors.white, size: 28),
                                  errorWidget: (context, url, error) => Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          _getColorByService(pinData.service).withOpacity(0.7),
                                          _getColorByService(pinData.service).withOpacity(0.3),
                                        ],
                                      ),
                                    ),
                                    child: const Icon(Icons.music_note, color: Colors.white, size: 28),
                                  ),
                                )
                              : Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        _getColorByService(pinData.service).withOpacity(0.7),
                                        _getColorByService(pinData.service).withOpacity(0.3),
                                      ],
                                    ),
                                  ),
                                  child: const Icon(Icons.music_note, color: Colors.white, size: 28),
                                ),
                        ),
                      ),
                      
                      const SizedBox(width: 12),
                      
                      // Pin info and close button
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    pinData.title,
                              style: TextStyle(
                                      fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.close, size: 20),
                                  onPressed: _closeDetails,
                                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                                  padding: EdgeInsets.zero,
                                ),
                              ],
                            ),
                            Text(
                              '${pinData.trackTitle} • ${pinData.trackArtist}',
                              style: TextStyle(
                                fontSize: 13,
                                color: theme.colorScheme.onSurface.withOpacity(0.7),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (pinData.locationName != null)
                              Row(
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    size: 11,
                                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                                  ),
                                  const SizedBox(width: 2),
                                  Expanded(
                                    child: Text(
                                      pinData.locationName!,
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: theme.colorScheme.onSurface.withOpacity(0.5),
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                            // Owner info
                            Row(
                              children: [
                                Icon(
                                  Icons.person,
                                  size: 11,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                                const SizedBox(width: 2),
                                Expanded(
                                  child: Text(
                                    'by ${pinData.owner.displayName ?? pinData.owner.username}',
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 14),
                  
                  // Service, album, rarity, and skin badges
                  Wrap(
                    spacing: 6,
                    runSpacing: 6,
                    children: [
                      _buildBadge(pinData.service.toUpperCase(), _getColorByService(pinData.service)),
                      if (pinData.album != null && pinData.album!.isNotEmpty)
                        _buildBadge(pinData.album!, Colors.purple),
                      _buildBadge(pinData.rarity.toUpperCase(), _getRarityColor(pinData.rarity)),
                      if (pinData.skinDetails != null && pinData.skinDetails!['name'] != null)
                        _buildBadge(pinData.skinDetails!['name'], Colors.cyan),
                      if (pinData.isPrivate)
                        _buildBadge('PRIVATE', Colors.orange),
                      if (pinData.auraRadius != null && pinData.auraRadius! > 0)
                        _buildBadge('${pinData.auraRadius!.round()}m RADIUS', Colors.indigo),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Caption or description
                  if (pinData.caption != null && pinData.caption!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Text(
                        pinData.caption!,
                    style: TextStyle(
                          fontSize: 13,
                      color: theme.colorScheme.onSurface.withOpacity(0.8),
                          height: 1.3,
                    ),
                        maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                    )
                  else if (pinData.description != null && pinData.description!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Text(
                        pinData.description!,
                        style: TextStyle(
                          fontSize: 13,
                          color: theme.colorScheme.onSurface.withOpacity(0.8),
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  
                  // Enhanced stats row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatColumn(Icons.visibility, 'Views', '${pinData.interactionCount['view'] ?? 0}'),
                      _buildStatColumn(Icons.favorite, 'Likes', '${pinData.interactionCount['like'] ?? 0}'),
                      _buildStatColumn(Icons.bookmark, 'Saves', '${pinData.interactionCount['collect'] ?? 0}'),
                      _buildStatColumn(Icons.share, 'Shares', '${pinData.interactionCount['share'] ?? 0}'),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Creation and update info
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Created ${_formatRelativeDate(pinData.createdAt)}',
                        style: TextStyle(
                          fontSize: 11,
                          color: theme.colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                      if (pinData.updatedAt.isAfter(pinData.createdAt.add(const Duration(minutes: 1))))
                        Text(
                          'Updated ${_formatRelativeDate(pinData.updatedAt)}',
                          style: TextStyle(
                            fontSize: 11,
                            color: theme.colorScheme.onSurface.withOpacity(0.5),
                          ),
                        ),
                      Text(
                        'Duration: ${_formatDuration(pinData.durationMs)}',
                        style: TextStyle(
                          fontSize: 11,
                          color: theme.colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                    ],
                  ),
                  
                  const Spacer(),
                  
                  // Enhanced action buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _playTrack(pinData),
                          icon: const Icon(Icons.play_arrow, color: Colors.white, size: 18),
                          label: const Text('Play', style: TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.bold)),
                            style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 6),
                      if (pinData.trackUrl.isNotEmpty)
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _openInService(pinData),
                            icon: Icon(_getServiceIcon(pinData.service), size: 14),
                            label: Text('Open', style: TextStyle(fontSize: 13)),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _getColorByService(pinData.service),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                            ),
                            ),
                          ),
                        ),
                      const SizedBox(width: 6),
                      ElevatedButton(
                        onPressed: () => _sharePin(pinData),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.surface,
                          foregroundColor: theme.colorScheme.onSurface,
                          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Icon(Icons.share, size: 14),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBadge(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  Widget _buildStatColumn(IconData icon, String label, String value) {
    final theme = Theme.of(context);
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 24,
          color: theme.colorScheme.onSurface,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Color _getRarityColor(String rarity) {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return Colors.purple;
      case 'epic':
        return Colors.orange;
      case 'rare':
        return Colors.blue;
      case 'common':
      default:
        return Colors.grey;
    }
  }

  Color _getColorByService(String service) {
    switch (service.toLowerCase()) {
      case 'spotify':
        return Colors.green;
      case 'apple_music':
        return Colors.red;
      case 'soundcloud':
        return Colors.orange;
      case 'youtube':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  void _showInfoDialog() {
    final bool isViewingFriend = widget.userId != null;
    final String username = widget.username ?? 'this friend';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isViewingFriend ? "$username's Music Globe" : 'My Music Globe'),
        content: Text(
          isViewingFriend 
            ? 'Explore $username\'s music pins from around the world! Tap on pins to discover their tracks, zoom and rotate to navigate the globe.'
            : 'Explore your music from around the world! Tap on pins to discover tracks, zoom and rotate to navigate the globe.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  // Helper method to format duration from milliseconds
  String _formatDuration(int? durationMs) {
    if (durationMs == null || durationMs <= 0) return 'Unknown';
    
    final seconds = (durationMs / 1000).round();
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    
    return '${minutes}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  String _formatRelativeDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} years ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else if (difference.inDays > 7) {
      return '${(difference.inDays / 7).floor()} weeks ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  IconData _getServiceIcon(String service) {
    switch (service.toLowerCase()) {
      case 'spotify':
        return Icons.library_music; // Changed from Icons.spotify
      case 'apple_music':
      case 'apple':
        return Icons.music_note;
      case 'youtube':
      case 'youtube_music':
        return Icons.play_circle;
      case 'soundcloud':
        return Icons.cloud;
      default:
        return Icons.music_note;
    }
  }

  void _openInService(Pin pin) {
    // TODO: Implement URL launcher to open track in native app
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${pin.trackTitle} in ${pin.service}...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _sharePin(Pin pin) {
    // TODO: Implement proper sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing "${pin.title}"...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Build the appropriate now playing bar based on which service is currently active
  Widget _buildNowPlayingBar(SpotifyProvider spotifyProvider, AppleMusicProvider appleMusicProvider) {
    // Determine which service is currently playing
    final spotifyActive = spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null;
    final appleActive = appleMusicProvider.isPlaying && appleMusicProvider.currentTrack != null;
    
    if (kDebugMode) {
      print('🎵 [MyBOPMap] Now Playing Bar - Spotify: $spotifyActive, Apple: $appleActive');
    }
    
    // Show Apple Music bar if Apple Music is playing
    if (appleActive && !spotifyActive) {
      return AppleNowPlayingBar(
        bottomPadding: MediaQuery.of(context).padding.bottom,
        showProgressBar: true,
        onTap: () {
          // Handle tap to expand Apple Music player
        },
        onDismiss: () {
          // Handle dismiss Apple Music
          // Apple Music dismissal is handled internally by the widget
        },
      );
    }
    
    // Show Spotify bar by default (or if Spotify is playing)
    return NowPlayingBar(
      bottomPadding: MediaQuery.of(context).padding.bottom,
      showProgressBar: true,
      onTap: () {
        // Handle tap to expand Spotify player
      },
      onDismiss: () {
        // Handle dismiss Spotify
        if (spotifyProvider.isPlaying) {
          spotifyProvider.pause();
          spotifyProvider.clearMockTrack();
        }
      },
    );
  }

  Widget _buildGlobePinWidget(Pin pin, double iconSize, Color pinColor, bool isSelected) {
    final skinImageUrl = pin.skinDetails?['image']?.toString();
    final hasImage = skinImageUrl != null && skinImageUrl.isNotEmpty;
    
    // Pin dimensions based on icon size - increased total height for taller stem
    final pinRadius = iconSize * 0.5;
    final pinHeight = iconSize * 2.0; // Increased from 1.4 to 2.0 for taller stem
    
    return SizedBox(
      width: iconSize * 1.2,
      height: pinHeight,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          // Selection glow effect
          if (isSelected)
            Positioned(
              top: 2,
              child: Container(
                width: pinRadius * 2.4,
                height: pinRadius * 2.4,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: pinColor.withOpacity(0.3),
                  boxShadow: [
                    BoxShadow(
                      color: pinColor.withOpacity(0.4),
                      blurRadius: 12,
                      spreadRadius: 2,
                    ),
                  ],
                ),
              ),
            ),
          
          // Pin stem - made taller for better 3D visibility
          Positioned(
            top: pinRadius * 1.5,
            child: Container(
              width: 4,
              height: pinRadius * 1.8, // Increased from 0.8 to 1.8 for much taller stem
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.9),
                    Colors.grey[300]!,
                    Colors.grey[600]!,
                    Colors.grey[300]!,
                    Colors.white.withOpacity(0.8),
                  ],
                  stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 4,
                    offset: const Offset(1, 1),
                  ),
                ],
              ),
            ),
          ),
          
          // Pin head with glassmorphism effect
          Positioned(
            top: 0,
            child: Container(
              width: pinRadius * 2,
              height: pinRadius * 2,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  center: const Alignment(-0.3, -0.3),
                  radius: 1.2,
                  colors: [
                    Colors.white.withOpacity(0.4),
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withOpacity(0.6),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 8,
                    offset: const Offset(2, 2),
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(-1, -1),
                  ),
                ],
              ),
              child: ClipOval(
                child: Stack(
                  children: [
                    // Skin image or fallback
                    if (hasImage)
                      CachedNetworkImage(
                        imageUrl: skinImageUrl,
                        fit: BoxFit.cover,
                        width: pinRadius * 2,
                        height: pinRadius * 2,
                        placeholder: (context, url) => Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                pinColor.withOpacity(0.7),
                                pinColor.withOpacity(0.3),
                              ],
                            ),
                          ),
                          child: Icon(
                            Icons.music_note,
                            color: Colors.white,
                            size: pinRadius * 0.8,
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                pinColor.withOpacity(0.7),
                                pinColor.withOpacity(0.3),
                              ],
                            ),
                          ),
                          child: Icon(
                            Icons.push_pin,
                            color: Colors.white,
                            size: pinRadius * 0.8,
                          ),
                        ),
                      )
                    else
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              pinColor.withOpacity(0.7),
                              pinColor.withOpacity(0.3),
                            ],
                          ),
                        ),
                        child: Icon(
                          Icons.push_pin,
                          color: Colors.white,
                          size: pinRadius * 0.8,
                        ),
                      ),
                    
                    // Glassmorphism overlay
                    Container(
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          center: const Alignment(-0.4, -0.4),
                          radius: 0.6,
                          colors: [
                            Colors.white.withOpacity(0.5),
                            Colors.white.withOpacity(0.1),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Sparkles for premium effect
          if (pin.rarity.toLowerCase() == 'legendary' || pin.rarity.toLowerCase() == 'epic')
            ...List.generate(3, (index) {
              final angle = (index * 120) * (math.pi / 180);
              final distance = pinRadius * 1.3;
              final x = math.cos(angle) * distance;
              final y = math.sin(angle) * distance;
              
              return Positioned(
                left: iconSize * 0.6 + x,
                top: pinRadius + y,
                child: Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: pinColor.withOpacity(0.6),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              );
            }),
          
          // Rarity indicator
          if (pin.rarity.toLowerCase() != 'common')
            Positioned(
              top: pinRadius * 0.2,
              right: pinRadius * 0.2,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getRarityColor(pin.rarity),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: _getRarityColor(pin.rarity).withOpacity(0.5),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  _getRarityIcon(pin.rarity),
                  color: Colors.white,
                  size: 8,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  IconData _getRarityIcon(String rarity) {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return Icons.star;
      case 'epic':
        return Icons.diamond;
      case 'rare':
        return Icons.auto_awesome;
      default:
        return Icons.circle;
    }
  }

  /// Handle cluster tap to show pins in cluster
  void _handleClusterTap(Map<String, dynamic> cluster) {
    final pins = cluster['pins'] as List<Pin>;
    final clusterCenter = cluster['center'] as GlobeCoordinates;
    
    print('🎯 Cluster tapped with ${pins.length} pins');
    
    // Focus on cluster center
    _controller.focusOnCoordinates(clusterCenter, animate: true);
    
    // Show cluster details dialog
    _showClusterDialog(cluster);
  }
  
  /// Build cluster widget for display on globe
  Widget _buildClusterWidget(int count, bool isHovering) {
    final baseSize = 50.0; // Reduced from 80.0 to 50.0
    final size = isHovering ? baseSize * 1.1 : baseSize; // Reduced hover effect
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          center: const Alignment(-0.3, -0.3),
          radius: 1.2,
          colors: [
            Colors.white.withOpacity(0.9),
            Colors.cyan.withOpacity(0.8),
            Colors.blue.withOpacity(0.9),
          ],
        ),
        border: Border.all(
          color: Colors.white.withOpacity(0.8),
          width: 2, // Reduced from 3
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.cyan.withOpacity(0.4),
            blurRadius: 12, // Reduced from 15
            spreadRadius: 1, // Reduced from 2
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 6, // Reduced from 8
            offset: const Offset(0, 3), // Reduced from 4
          ),
        ],
      ),
      child: Stack(
        children: [
          // Glassmorphism highlight
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                center: const Alignment(-0.4, -0.4),
                radius: 0.6,
                colors: [
                  Colors.white.withOpacity(0.6),
                  Colors.white.withOpacity(0.1),
                  Colors.transparent,
                ],
              ),
            ),
          ),
          
          // Count text
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  count.toString(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: count > 99 ? 12 : count > 9 ? 16 : 18, // Reduced font sizes
                    fontWeight: FontWeight.bold,
                    shadows: const [
                      Shadow(
                        color: Colors.black54,
                        offset: Offset(1, 1),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                ),
                Text(
                  'pins',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 8, // Reduced from 12
                    fontWeight: FontWeight.w500,
                    shadows: const [
                      Shadow(
                        color: Colors.black54,
                        offset: Offset(1, 1),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Sparkles for large clusters
          if (count > 5) ..._buildClusterSparkles(size),
        ],
      ),
    );
  }
  
  /// Build sparkles for cluster decoration
  List<Widget> _buildClusterSparkles(double size) {
    return [
      Positioned(
        top: size * 0.1,
        right: size * 0.2,
        child: Container(
          width: 4, // Reduced from 6
          height: 4, // Reduced from 6
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.8),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.cyan.withOpacity(0.6),
                blurRadius: 3, // Reduced from 4
                spreadRadius: 0.5, // Reduced from 1
              ),
            ],
          ),
        ),
      ),
      Positioned(
        bottom: size * 0.15,
        left: size * 0.15,
        child: Container(
          width: 3, // Reduced from 4
          height: 3, // Reduced from 4
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.7),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.5),
                blurRadius: 2, // Reduced from 3
                spreadRadius: 0.5, // Reduced from 1
              ),
            ],
          ),
        ),
      ),
      Positioned(
        top: size * 0.25,
        left: size * 0.1,
        child: Container(
          width: 3, // Reduced from 5
          height: 3, // Reduced from 5
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.cyan.withOpacity(0.7),
                blurRadius: 3, // Reduced from 4
                spreadRadius: 0.5, // Reduced from 1
              ),
            ],
          ),
        ),
      ),
    ];
  }
  
  /// Show cluster details dialog with pin list
  void _showClusterDialog(Map<String, dynamic> cluster) {
    final pins = cluster['pins'] as List<Pin>;
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        constraints: BoxConstraints(
          maxHeight: mediaQuery.size.height * 0.85,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Center(
              child: Container(
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            
            // Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          theme.colorScheme.primary,
                          theme.colorScheme.secondary,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: theme.colorScheme.primary.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.my_location,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Music Cluster',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${pins.length} pins in this area',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                  OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _zoomToShowIndividualPins(cluster);
                    },
                    icon: const Icon(Icons.zoom_in),
                    label: const Text('Zoom In'),
                    style: OutlinedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                  ),
                ],
              ),
            ),
            
            // Pins list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                itemCount: pins.length,
                itemBuilder: (context, index) {
                  final pin = pins[index];
                  final serviceColor = _getColorByService(pin.service);
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.onSurface.withOpacity(0.1),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: serviceColor.withOpacity(0.1),
                          blurRadius: 8,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.pop(context);
                          _handlePointTap(pin);
                        },
                        borderRadius: BorderRadius.circular(16),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Row(
                            children: [
                              // Album art
                              Container(
                                width: 64,
                                height: 64,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: serviceColor.withOpacity(0.2),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: pin.artworkUrl != null && pin.artworkUrl!.isNotEmpty
                                    ? CachedNetworkImage(
                                        imageUrl: pin.artworkUrl!,
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) => Container(
                                          color: serviceColor.withOpacity(0.1),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(serviceColor),
                                            ),
                                          ),
                                        ),
                                        errorWidget: (context, url, error) => Container(
                                          color: serviceColor.withOpacity(0.1),
                                          child: Icon(
                                            Icons.music_note,
                                            color: serviceColor,
                                            size: 32,
                                          ),
                                        ),
                                      )
                                    : Container(
                                        color: serviceColor.withOpacity(0.1),
                                        child: Icon(
                                          Icons.music_note,
                                          color: serviceColor,
                                          size: 32,
                                        ),
                                      ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              
                              // Track info with tap handling
                              Expanded(
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () {
                                      Navigator.pop(context); // Close cluster sheet
                                      _handlePointTap(pin); // Show individual pin details
                                    },
                                    borderRadius: BorderRadius.circular(12),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          pin.trackTitle,
                                          style: theme.textTheme.titleMedium?.copyWith(
                                            fontWeight: FontWeight.w600,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          pin.trackArtist,
                                          style: theme.textTheme.bodyMedium?.copyWith(
                                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Icon(
                                              _getServiceIcon(pin.service),
                                              color: serviceColor,
                                              size: 16,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              pin.service.toUpperCase(),
                                              style: theme.textTheme.bodySmall?.copyWith(
                                                color: serviceColor,
                                                fontWeight: FontWeight.w600,
                                                letterSpacing: 0.5,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              
                              // Play button
                              Container(
                                width: 44,
                                height: 44,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: serviceColor,
                                  boxShadow: [
                                    BoxShadow(
                                      color: serviceColor.withOpacity(0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () async {
                                      // Add haptic feedback
                                      HapticFeedback.mediumImpact();
                                      Navigator.pop(context);
                                      await _playTrack(pin);
                                    },
                                    customBorder: const CircleBorder(),
                                    child: Icon(
                                      Icons.play_arrow,
                                      color: Colors.white,
                                      size: 28,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Bottom padding
            SizedBox(height: mediaQuery.padding.bottom + 16),
          ],
        ),
      ),
    );
  }
} 