import 'package:flutter/material.dart';
import '../constants/map_style.dart';
import '../models/map_style_config.dart';
import 'style_selector_bottomsheet.dart';

class MapStyleToggleWidget extends StatelessWidget {
  final MapStyle currentMapStyle;
  final Map<MapStyle, MapStyleConfig> mapStyles;
  final Function(MapStyle) onStyleSelected;
  final bool isDarkMode;

  const MapStyleToggleWidget({
    super.key,
    required this.currentMapStyle,
    required this.mapStyles,
    required this.onStyleSelected,
    required this.isDarkMode,
  });

  @override
  Widget build(BuildContext context) {
    final currentStyle = mapStyles[currentMapStyle]!;
    
    return Column(
      children: [
        Container(
          width: 44,
          height: 44,
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.black.withOpacity(0.8) : Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: currentStyle.accentColor.withOpacity(0.5),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: GestureDetector(
            onTap: () => _showStyleSelector(context),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                gradient: RadialGradient(
                  colors: [
                    currentStyle.accentColor.withOpacity(0.2),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Icon(
                currentStyle.icon,
                color: currentStyle.accentColor,
                size: 20,
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.black.withOpacity(0.8) : Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Text(
            currentStyle.name,
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black87,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  void _showStyleSelector(BuildContext context) {
    StyleSelectorBottomSheet.show(
      context,
      currentStyle: currentMapStyle,
      onStyleSelected: onStyleSelected,
    );
  }
} 