import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import 'package:provider/provider.dart';
import '../../../providers/apple_music_provider.dart';
import '../../../providers/youtube_provider.dart';
import '../../../providers/pin_provider.dart';
import '../../../models/music_track.dart';

class PinsInAuraBottomSheetWidget extends StatefulWidget {
  final List<Map<String, dynamic>> pinsInAura;
  final String Function(Map<String, dynamic>) getPinRarity;
  final Color Function(String) getPinColor;
  final int Function(Map<String, dynamic>) getPinUpvotes;
  final int Function(Map<String, dynamic>) getPinDownvotes;
  final Function(Map<String, dynamic>) onPinTap;

  const PinsInAuraBottomSheetWidget({
    super.key,
    required this.pinsInAura,
    required this.getPinRarity,
    required this.getPinColor,
    required this.getPinUpvotes,
    required this.getPinDownvotes,
    required this.onPinTap,
  });

  @override
  State<PinsInAuraBottomSheetWidget> createState() =>
      _PinsInAuraBottomSheetWidgetState();
}

class _PinsInAuraBottomSheetWidgetState
    extends State<PinsInAuraBottomSheetWidget> {
  @override
  void initState() {
    super.initState();
    // Update PiP params when bottomsheet is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PictureInPicture.updatePiPParams(
        pipParams: PiPParams(
          pipWindowHeight: 125,
          pipWindowWidth: 125,
          bottomSpace: MediaQuery.of(context).size.height *
              0.7, // Match bottomsheet height
          leftSpace: 0,
          rightSpace: 0,
          topSpace: 0,
          maxSize: const Size(125, 125),
          minSize: const Size(125, 125),
          movable: true,
          resizable: false,
          initialCorner: PIPViewCorner.bottomLeft,
        ),
      );
    });
  }

  /// Shuffle and play all pins in the aura
  void _shuffleAndPlayPins() {
    if (widget.pinsInAura.isEmpty) return;

    // Shuffle the pins
    final shuffledPins = List<Map<String, dynamic>>.from(widget.pinsInAura);
    shuffledPins.shuffle();

    // Close the bottom sheet first
    Navigator.pop(context);

    // Play the first shuffled pin directly
    if (shuffledPins.isNotEmpty) {
      _handlePlayMusicDirectly(shuffledPins.first);
    }
  }

  /// Play music directly without opening pin details (same logic as NearbyPinBottomSheet)
  /// Priority: Apple Music -> YouTube (final fallback)
  Future<void> _handlePlayMusicDirectly(
      Map<String, dynamic> originalPinData) async {
    try {
      // Create a mutable copy of the pin data to modify
      final pinData = Map<String, dynamic>.from(originalPinData);

      // Attempt to extract duration from various possible keys
      final duration = pinData['duration'] ??
          pinData['duration_ms'] ??
          pinData['durationMs'] ??
          pinData['track_duration'] ??
          pinData['length'] ??
          pinData['length_ms'];

      // Explicitly set duration_ms if found
      if (duration != null) {
        pinData['duration_ms'] =
            duration is int ? duration : int.tryParse(duration.toString());
      }

      // Get pin provider
      final pinProvider = Provider.of<PinProvider>(context, listen: false);

      // IMMEDIATELY set the currently playing pin data in PinProvider before attempting playback
      final pinId = pinData['id']?.toString();
      if (pinId != null) {
        debugPrint(
            '🎵 [PinsInAuraBottomSheet] Setting currently playing pin in provider: $pinId');
        pinProvider.setCurrentlyPlayingPin(pinId, pinData);
        debugPrint(
            '🎵 [PinsInAuraBottomSheet] Pin data set in provider successfully');
      }

      // Try Apple Music first with YouTube fallback
      bool playbackSuccess = await _tryAppleMusicWithYouTubeFallback(context, pinData);

      // Check if widget is still mounted after async operations
      if (!mounted) return;

      // Show error if playback failed on all services
      if (!playbackSuccess) {
        // All services failed - clear the pin data
        pinProvider.clearCurrentlyPlayingPin();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not play track on any available service'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ [PinsInAuraBottomSheet] Error playing music: $e');

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error playing music: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Try Apple Music first, fallback to YouTube if needed
  Future<bool> _tryAppleMusicWithYouTubeFallback(BuildContext context, Map<String, dynamic> pinData) async {
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);

    bool success = false;

    // Always try Apple Music first using the same approach as TrackCard
    try {
      if (kDebugMode) {
        print('🎵 [PinsInAuraBottomSheet] Attempting Apple Music playback for pin');
        print('🎵 [PinsInAuraBottomSheet] Pin data keys: ${pinData.keys.toList()}');
        print('🎵 [PinsInAuraBottomSheet] Track title: ${pinData['track_title'] ?? pinData['title']}');
        print('🎵 [PinsInAuraBottomSheet] Track artist: ${pinData['track_artist'] ?? pinData['artist']}');
        print('🎵 [PinsInAuraBottomSheet] Artwork URL: ${pinData['artwork_url'] ?? pinData['image']}');
      }

      // Convert pin data to MusicTrack using correct pin field names
      final originalUri = pinData['uri'] ?? pinData['track_uri'] ?? '';
      final originalUrl = pinData['track_url'] ?? pinData['url'] ?? '';
      final service = pinData['service'] ?? 'spotify'; // Default to spotify for pins

      // If the original URI is from Spotify, clear it for Apple Music search
      final cleanUri = originalUri.contains('spotify:') ? '' : originalUri;
      final cleanUrl = originalUrl.contains('spotify.com') ? '' : originalUrl;

      final musicTrack = MusicTrack(
        id: pinData['id']?.toString() ?? '',
        title: pinData['track_title'] ?? pinData['title'] ?? pinData['songTitle'] ?? 'Unknown Song',
        artist: pinData['track_artist'] ?? pinData['artist'] ?? 'Unknown Artist',
        album: pinData['album'] ?? '',
        albumArt: pinData['artwork_url'] ?? pinData['image'] ?? '',
        uri: cleanUri, // Use cleaned URI to force Apple Music search
        durationMs: (pinData['duration_ms'] is int) ? pinData['duration_ms'] :
                   (pinData['duration'] is int) ? pinData['duration'] : 0,
        url: cleanUrl, // Use cleaned URL
        service: service,
        serviceType: service == 'apple_music' || service == 'apple' ? 'apple' : 'spotify',
        genres: [],
        explicit: false,
        popularity: 0,
      );

      if (kDebugMode) {
        print('🎵 [PinsInAuraBottomSheet] Created MusicTrack: ${musicTrack.title} by ${musicTrack.artist}');
        print('🎵 [PinsInAuraBottomSheet] MusicTrack service: ${musicTrack.service}');
        print('🎵 [PinsInAuraBottomSheet] MusicTrack serviceType: ${musicTrack.serviceType}');
        print('🎵 [PinsInAuraBottomSheet] Attempting Apple Music first, then YouTube fallback if needed');
      }

      // Use the same approach as TrackCard
      final needsSearch = musicTrack.service != 'apple_music' && musicTrack.service != 'apple';
      
      if (needsSearch) {
        // Use the method that shows snackbar for no exact match (same as TrackCard)
        success = await appleMusicProvider.playTrackBySearchWithFallback(musicTrack, context);
      } else {
        // For Apple Music tracks, use queue manager directly (same as TrackCard)
        final queueManager = appleMusicProvider.queueManager;
        success = await queueManager.setQueue(
          tracks: [musicTrack],
          collectionType: 'single_track',
          startIndex: 0,
        );
      }

      if (kDebugMode) {
        print('🎵 [PinsInAuraBottomSheet] Apple Music result: success=$success, needsSearch=$needsSearch');
      }

      if (success) {
        final trackTitle = pinData['title'] ?? pinData['track_title'] ?? 'Unknown Song';
        if (kDebugMode) {
          print('✅ [PinsInAuraBottomSheet] Apple Music playback successful: $trackTitle');
        }
        
        // Show success message with service used
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Playing on Apple Music'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
        
        return true;
      }
      
      // Only fallback to YouTube automatically if it's not a search scenario
      // For search scenarios, the snackbar will handle user-controlled fallback (same as TrackCard)
      if (!success) {
        if (kDebugMode) {
          print('🎵 [PinsInAuraBottomSheet] Apple Music failed, trying YouTube fallback...');
        }
        if (context.mounted) {
          return await _tryYouTubeFallback(context, pinData);
        }
        return false;
      }
      
      // For search scenarios that failed, don't auto-fallback (user gets snackbar option)
      return false;

    } catch (e) {
      if (kDebugMode) {
        print('🎵 [PinsInAuraBottomSheet] Apple Music error, trying YouTube fallback: $e');
      }
      if (context.mounted) {
        return await _tryYouTubeFallback(context, pinData);
      }
      return false;
    }
  }

  /// Try YouTube as final fallback when all other services fail
  Future<bool> _tryYouTubeFallback(BuildContext context, Map<String, dynamic> pinData) async {
    try {
      if (kDebugMode) {
        print('🎵 [PinsInAuraBottomSheet] Trying YouTube fallback for: ${pinData['title']}');
      }
      
      // Get YouTube provider
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
      
      // Initialize YouTube if needed
      if (!youtubeProvider.isInitialized) {
        await youtubeProvider.initialize();
      }
      
      // Use YouTube provider's playTrackFromPin method for proper pin communication
      final success = await youtubeProvider.playTrackFromPin(pinData, context: context);
      
      if (success) {
        final trackTitle = pinData['title'] ?? pinData['track_title'] ?? 'Unknown Song';
        if (kDebugMode) {
          print('✅ [PinsInAuraBottomSheet] YouTube fallback successful: $trackTitle');
        }
        
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Playing on YouTube'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
        
        return true;
      } else {
        if (kDebugMode) {
          print('❌ [PinsInAuraBottomSheet] YouTube fallback failed');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [PinsInAuraBottomSheet] Error in YouTube fallback: $e');
      }
      return false;
    }
  }

  @override
  void dispose() {
    // Restore default PiP params when bottomsheet is closed
    PictureInPicture.updatePiPParams(
      pipParams: const PiPParams(
        pipWindowHeight: 125,
        pipWindowWidth: 125,
        bottomSpace: 64,
        leftSpace: 0,
        rightSpace: 0,
        topSpace: 64,
        maxSize: Size(125, 125),
        minSize: Size(125, 125),
        movable: true,
        resizable: false,
        initialCorner: PIPViewCorner.bottomLeft,
      ),
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Shuffle button at the very top
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            child: Container(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  // Shuffle and play the pins
                  _shuffleAndPlayPins();
                },
                icon: const Icon(
                  Icons.shuffle,
                  color: Colors.white,
                  size: 18,
                ),
                label: const Text(
                  'Shuffle All Pins',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.cyan.withOpacity(0.8),
                  foregroundColor: Colors.white,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Pins in your aura',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${widget.pinsInAura.length} pin${widget.pinsInAura.length != 1 ? 's' : ''} within range • sorted by votes',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Pin list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: widget.pinsInAura.length,
              itemBuilder: (context, index) {
                final pin = widget.pinsInAura[index];
                final distance = pin['current_distance'] as double;
                final auraRadius =
                    (pin['aura_radius'] as num?)?.toDouble() ?? 25.0;
                final rarity = widget.getPinRarity(pin);
                final pinColor = widget.getPinColor(rarity);

                // Attempt to resolve artwork (album cover)
                final artworkUrl = pin['artwork_url'] ??
                    pin['albumArt'] ??
                    pin['album_art'] ??
                    pin['image_url'];

                return GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    widget.onPinTap(pin);
                  },
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: pinColor.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        // Artwork/Album cover with aura effect
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.cyan.withOpacity(0.5),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.cyan.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Stack(
                              children: [
                                // Artwork or fallback
                                if (artworkUrl != null && artworkUrl.isNotEmpty)
                                  CachedNetworkImage(
                                    imageUrl: artworkUrl,
                                    width: 50,
                                    height: 50,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) => Container(
                                      color: Colors.white.withOpacity(0.1),
                                      child: const Center(
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2),
                                      ),
                                    ),
                                    errorWidget: (context, url, error) =>
                                        Container(
                                      color: pinColor.withOpacity(0.2),
                                      child: Icon(
                                        Icons.music_note,
                                        color: pinColor,
                                        size: 24,
                                      ),
                                    ),
                                  )
                                else
                                  Container(
                                    color: pinColor.withOpacity(0.2),
                                    child: Icon(
                                      Icons.music_note,
                                      color: pinColor,
                                      size: 24,
                                    ),
                                  ),
                                // Aura overlay indicator
                                Positioned(
                                  top: 2,
                                  right: 2,
                                  child: Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: Colors.cyan,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: Colors.white,
                                        width: 1,
                                      ),
                                    ),
                                    child: const Icon(
                                      Icons.radio_button_checked,
                                      color: Colors.white,
                                      size: 8,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        // Pin details
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                pin['title'] ??
                                    pin['track_title'] ??
                                    'Unknown Track',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                pin['artist'] ??
                                    pin['track_artist'] ??
                                    'Unknown Artist',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 14,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    size: 12,
                                    color: Colors.cyan.withOpacity(0.7),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${distance.toStringAsFixed(0)}m away • ${auraRadius.toStringAsFixed(0)}m aura',
                                    style: TextStyle(
                                      color: Colors.cyan.withOpacity(0.7),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              // Vote counts row - Enhanced visibility
                              Row(
                                children: [
                                  // Enhanced Upvote button
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Colors.green.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: Colors.green.withOpacity(0.4),
                                        width: 1,
                                      ),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.thumb_up,
                                          size: 16, // Increased from 12 to 16
                                          color: Colors.green, // Full opacity
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '${widget.getPinUpvotes(pin)}',
                                          style: const TextStyle(
                                            color: Colors.green, // Full opacity
                                            fontSize:
                                                13, // Increased from 11 to 13
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(width: 8),

                                  // Enhanced Downvote button
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Colors.red.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: Colors.red.withOpacity(0.4),
                                        width: 1,
                                      ),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.thumb_down,
                                          size: 16, // Increased from 12 to 16
                                          color: Colors.red, // Full opacity
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '${widget.getPinDownvotes(pin)}',
                                          style: const TextStyle(
                                            color: Colors.red, // Full opacity
                                            fontSize:
                                                13, // Increased from 11 to 13
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(width: 12),

                                  // Enhanced Net score
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 4), // Increased padding
                                    decoration: BoxDecoration(
                                      color: () {
                                        final netScore =
                                            widget.getPinUpvotes(pin) -
                                                widget.getPinDownvotes(pin);
                                        if (netScore > 0)
                                          return Colors.green.withOpacity(
                                              0.3); // Increased opacity
                                        if (netScore < 0)
                                          return Colors.red.withOpacity(
                                              0.3); // Increased opacity
                                        return Colors.grey.withOpacity(
                                            0.3); // Increased opacity
                                      }(),
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                        color: () {
                                          final netScore =
                                              widget.getPinUpvotes(pin) -
                                                  widget.getPinDownvotes(pin);
                                          if (netScore > 0)
                                            return Colors.green
                                                .withOpacity(0.5);
                                          if (netScore < 0)
                                            return Colors.red.withOpacity(0.5);
                                          return Colors.grey.withOpacity(0.5);
                                        }(),
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      () {
                                        final netScore =
                                            widget.getPinUpvotes(pin) -
                                                widget.getPinDownvotes(pin);
                                        return netScore >= 0
                                            ? '+$netScore'
                                            : '$netScore';
                                      }(),
                                      style: TextStyle(
                                        color: () {
                                          final netScore =
                                              widget.getPinUpvotes(pin) -
                                                  widget.getPinDownvotes(pin);
                                          if (netScore > 0) return Colors.green;
                                          if (netScore < 0) return Colors.red;
                                          return Colors.grey[300];
                                        }(),
                                        fontSize: 13, // Increased from 11 to 13
                                        fontWeight:
                                            FontWeight.w700, // Made bolder
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // Play button - Enhanced for direct playback
                        GestureDetector(
                          onTap: () {
                            // Close the bottom sheet first
                            Navigator.pop(context);
                            // Play music directly
                            _handlePlayMusicDirectly(pin);
                          },
                          child: Container(
                            width: 44,
                            height: 44,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                colors: [
                                  pinColor.withOpacity(0.8),
                                  pinColor.withOpacity(0.6),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.2),
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: pinColor.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
