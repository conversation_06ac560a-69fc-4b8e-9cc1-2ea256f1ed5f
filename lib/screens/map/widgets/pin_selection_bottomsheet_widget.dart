import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';

class PinSelectionBottomSheetWidget extends StatefulWidget {
  final List<Map<String, dynamic>> pins;
  final Position? currentPosition;
  final String Function(Map<String, dynamic>) getPinRarity;
  final Color Function(String) getPinColor;
  final double Function(double, double, double, double)
      calculateDistanceInMeters;
  final Function(Map<String, dynamic>) onPinTap;

  const PinSelectionBottomSheetWidget({
    super.key,
    required this.pins,
    required this.currentPosition,
    required this.getPinRarity,
    required this.getPinColor,
    required this.calculateDistanceInMeters,
    required this.onPinTap,
  });

  @override
  State<PinSelectionBottomSheetWidget> createState() =>
      _PinSelectionBottomSheetWidgetState();
}

class _PinSelectionBottomSheetWidgetState
    extends State<PinSelectionBottomSheetWidget> {
  @override
  void initState() {
    super.initState();
    // Update PiP params when bottomsheet is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PictureInPicture.updatePiPParams(
        pipParams: PiPParams(
          pipWindowHeight: 125,
          pipWindowWidth: 125,
          bottomSpace: MediaQuery.of(context).size.height *
              0.7, // Match bottomsheet height
          leftSpace: 0,
          rightSpace: 0,
          topSpace: 0,
          maxSize: const Size(125, 125),
          minSize: const Size(125, 125),
          movable: true,
          resizable: false,
          initialCorner: PIPViewCorner.bottomLeft,
        ),
      );
    });
  }

  @override
  void dispose() {
    // Restore default PiP params when bottomsheet is closed
    PictureInPicture.updatePiPParams(
      pipParams: const PiPParams(
        pipWindowHeight: 125,
        pipWindowWidth: 125,
        bottomSpace: 64,
        leftSpace: 0,
        rightSpace: 0,
        topSpace: 64,
        maxSize: Size(125, 125),
        minSize: Size(125, 125),
        movable: true,
        resizable: false,
        initialCorner: PIPViewCorner.bottomLeft,
      ),
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Categorize pins based on distance from user
    final nearbyPins = <Map<String, dynamic>>[];
    final distantPins = <Map<String, dynamic>>[];

    if (widget.currentPosition != null) {
      for (final pin in widget.pins) {
        final pinLat = pin['latitude'] as double;
        final pinLng = pin['longitude'] as double;
        final auraRadius = (pin['aura_radius'] as num?)?.toDouble() ?? 25.0;

        final distance = widget.calculateDistanceInMeters(
          widget.currentPosition!.latitude,
          widget.currentPosition!.longitude,
          pinLat,
          pinLng,
        );

        if (distance <= auraRadius) {
          pin['current_distance'] = distance;
          nearbyPins.add(pin);
        } else {
          pin['current_distance'] = distance;
          distantPins.add(pin);
        }
      }
    } else {
      // If no position, treat all as distant
      distantPins.addAll(widget.pins);
    }

    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${widget.pins.length} pins found',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (nearbyPins.isNotEmpty || distantPins.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        '${nearbyPins.length} nearby • ${distantPins.length} distant',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ],
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Pin list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: widget.pins.length,
              itemBuilder: (context, index) {
                final pin = widget.pins[index];
                final rarity = widget.getPinRarity(pin);
                final pinColor = widget.getPinColor(rarity);
                final distance = pin['current_distance'] as double?;
                final auraRadius =
                    (pin['aura_radius'] as num?)?.toDouble() ?? 25.0;
                final isNearby = distance != null && distance <= auraRadius;

                // Attempt to resolve pin skin image
                String? skinImageUrl;
                if (pin['skinDetails'] != null &&
                    pin['skinDetails']['image'] != null) {
                  skinImageUrl = pin['skinDetails']['image'].toString();
                }

                // Attempt to resolve artwork (album cover) - only for nearby pins
                final artworkUrl = isNearby
                    ? (pin['artwork_url'] ??
                        pin['albumArt'] ??
                        pin['album_art'] ??
                        pin['image_url'])
                    : null;

                return GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    widget.onPinTap(pin);
                  },
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(isNearby ? 0.12 : 0.08),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isNearby
                            ? Colors.cyan.withOpacity(0.4)
                            : pinColor.withOpacity(0.3),
                        width: isNearby ? 1.5 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        // Pin image (skin for distant, artwork for nearby)
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isNearby
                                  ? Colors.cyan.withOpacity(0.5)
                                  : pinColor.withOpacity(0.5),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: isNearby
                                    ? Colors.cyan.withOpacity(0.3)
                                    : pinColor.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Stack(
                              children: [
                                // Main image
                                if (isNearby &&
                                    artworkUrl != null &&
                                    artworkUrl.isNotEmpty)
                                  // Show artwork for nearby pins
                                  CachedNetworkImage(
                                    imageUrl: artworkUrl,
                                    width: 50,
                                    height: 50,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) => Container(
                                      color: Colors.white.withOpacity(0.1),
                                      child: const Center(
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2),
                                      ),
                                    ),
                                    errorWidget: (context, url, error) =>
                                        Container(
                                      color: pinColor.withOpacity(0.2),
                                      child: Icon(
                                        Icons.music_note,
                                        color: pinColor,
                                        size: 24,
                                      ),
                                    ),
                                  )
                                else if (!isNearby &&
                                    skinImageUrl != null &&
                                    skinImageUrl.isNotEmpty)
                                  // Show pin skin for distant pins
                                  CachedNetworkImage(
                                    imageUrl: skinImageUrl,
                                    width: 50,
                                    height: 50,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) => Container(
                                      color: Colors.white.withOpacity(0.1),
                                      child: const Center(
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2),
                                      ),
                                    ),
                                    errorWidget: (context, url, error) =>
                                        Container(
                                      color: pinColor.withOpacity(0.2),
                                      child: Icon(
                                        Icons.push_pin,
                                        color: pinColor,
                                        size: 24,
                                      ),
                                    ),
                                  )
                                else
                                  // Fallback for no image
                                  Container(
                                    color: pinColor.withOpacity(0.2),
                                    child: Icon(
                                      isNearby
                                          ? Icons.music_note
                                          : Icons.push_pin,
                                      color: pinColor,
                                      size: 24,
                                    ),
                                  ),

                                // Distance/aura indicator overlay
                                if (isNearby)
                                  Positioned(
                                    top: 2,
                                    right: 2,
                                    child: Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: Colors.cyan,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 1,
                                        ),
                                      ),
                                      child: const Icon(
                                        Icons.radio_button_checked,
                                        color: Colors.white,
                                        size: 8,
                                      ),
                                    ),
                                  )
                                else if (!isNearby)
                                  Positioned(
                                    top: 2,
                                    right: 2,
                                    child: Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: Colors.orange,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 1,
                                        ),
                                      ),
                                      child: const Icon(
                                        Icons.lock_outline,
                                        color: Colors.white,
                                        size: 6,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        // Pin details
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (isNearby) ...[
                                // Full details for nearby pins
                                Text(
                                  pin['title'] ??
                                      pin['track_title'] ??
                                      'Unknown Track',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  pin['artist'] ??
                                      pin['track_artist'] ??
                                      'Unknown Artist',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.7),
                                    fontSize: 14,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ] else ...[
                                // Limited details for distant pins
                                Text(
                                  'Hidden Track',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Get closer to unlock',
                                  style: TextStyle(
                                    color: Colors.orange.withOpacity(0.8),
                                    fontSize: 14,
                                  ),
                                ),
                              ],

                              const SizedBox(height: 4),

                              // Creator and distance info
                              Row(
                                children: [
                                  Icon(
                                    Icons.person,
                                    size: 12,
                                    color: Colors.white.withOpacity(0.5),
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      '@${pin['owner']?['username'] ?? pin['username'] ?? 'Unknown'}',
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.5),
                                        fontSize: 12,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  if (distance != null) ...[
                                    const SizedBox(width: 8),
                                    Icon(
                                      Icons.location_on,
                                      size: 12,
                                      color: isNearby
                                          ? Colors.cyan.withOpacity(0.7)
                                          : Colors.orange.withOpacity(0.7),
                                    ),
                                    const SizedBox(width: 2),
                                    Text(
                                      distance > 1000
                                          ? '${(distance / 1000).toStringAsFixed(1)}km'
                                          : '${distance.toStringAsFixed(0)}m',
                                      style: TextStyle(
                                        color: isNearby
                                            ? Colors.cyan.withOpacity(0.7)
                                            : Colors.orange.withOpacity(0.7),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ],
                          ),
                        ),

                        // Play button or lock indicator
                        if (isNearby)
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                              widget.onPinTap(pin);
                            },
                            child: Container(
                              width: 44,
                              height: 44,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  colors: [
                                    pinColor.withOpacity(0.8),
                                    pinColor.withOpacity(0.6),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: pinColor.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.play_arrow,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                          )
                        else
                          // Lock indicator for distant pins
                          Container(
                            width: 44,
                            height: 44,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.orange.withOpacity(0.2),
                              border: Border.all(
                                color: Colors.orange.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.lock_outline,
                              color: Colors.orange.withOpacity(0.8),
                              size: 20,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
