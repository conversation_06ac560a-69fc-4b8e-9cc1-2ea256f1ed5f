import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';

import '../../../providers/theme_provider.dart';
import '../constants/map_style.dart';
import '../models/map_style_config.dart';
import '../../../config/constants.dart';

class StyleSelectorBottomSheet extends StatefulWidget {
  final MapStyle currentMapStyle;
  final Function(MapStyle) onStyleSelected;

  const StyleSelectorBottomSheet({
    Key? key,
    required this.currentMapStyle,
    required this.onStyleSelected,
  }) : super(key: key);

  // Map style configurations (moved from main file) - made public
  static final String _stadiaApiKey = AppConstants.stadiaApiKey;

  static final Map<MapStyle, MapStyleConfig> mapStyles = {
    MapStyle.minimal: MapStyleConfig(
      name: 'Minimal',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/osm_bright.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.circle_outlined,
      accentColor: Colors.grey,
      previewImagePath: 'assets/images/map_skin/minimal.png',
    ),
    MapStyle.vaporwave: MapStyleConfig(
      name: 'Vaporwave',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.waves,
      accentColor: Colors.pinkAccent,
      previewImagePath: 'assets/images/map_skin/vaporwave.png',
    ),
    MapStyle.standard: MapStyleConfig(
      name: 'Standard',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/osm_bright.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.map,
      accentColor: Colors.blue,
      previewImagePath: 'assets/images/map_skin/standard.png',
    ),
    MapStyle.neon: MapStyleConfig(
      name: 'Neon',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.electric_bolt,
      accentColor: Colors.cyan,
      previewImagePath: 'assets/images/map_skin/neon.png',
    ),
    MapStyle.cyberpunk: MapStyleConfig(
      name: 'Cyberpunk',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.computer,
      accentColor: Colors.purple,
      previewImagePath: 'assets/images/map_skin/cyberpunk.png',
      isAnimated: true,
    ),
    MapStyle.oldSchool: MapStyleConfig(
      name: 'Old School',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/stamen_toner_lite.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/stamen_toner.json?api_key=$_stadiaApiKey',
      icon: Icons.history,
      accentColor: Colors.grey,
      previewImagePath: 'assets/images/map_skin/old_school.png',
    ),
    MapStyle.watercolor: MapStyleConfig(
      name: 'Watercolor',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/stamen_watercolor.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/stamen_watercolor.json?api_key=$_stadiaApiKey',
      icon: Icons.brush,
      accentColor: Colors.brown,
      previewImagePath: 'assets/images/map_skin/watercolor.png',
    ),
    MapStyle.retro: MapStyleConfig(
      name: 'Retro',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth.json?api_key=$_stadiaApiKey',
      icon: Icons.games,
      accentColor: Colors.pink,
      previewImagePath: 'assets/images/map_skin/retro.png',
      isAnimated: true,
    ),
    MapStyle.matrix: const MapStyleConfig(
      name: 'Matrix',
      
      lightUrl: 'https://f005.backblazeb2.com/file/bopmaps/matrix.json',
      darkUrl: 'https://f005.backblazeb2.com/file/bopmaps/matrix.json',
      icon: Icons.code,
      accentColor: Colors.green,
      previewImagePath: 'assets/images/map_skin/matrix.png',
      isAnimated: true,
    ),
    MapStyle.futuristic: const MapStyleConfig(
      name: 'Futuristic',
      lightUrl: 'https://f005.backblazeb2.com/file/bopmaps/matrix.json',
      darkUrl: 'https://f005.backblazeb2.com/file/bopmaps/matrix.json',
      icon: Icons.grid_on,
      accentColor: Colors.cyan,
      previewImagePath: 'assets/images/map_skin/futuristic.png',
    ),
  };

  // Show the style selector bottom sheet
  static void show(
    BuildContext context, {
    required MapStyle currentStyle,
    required Function(MapStyle) onStyleSelected,
  }) {
    HapticFeedback.lightImpact();

    // Update PiP params before showing the sheet
    PictureInPicture.updatePiPParams(
      pipParams: PiPParams(
        pipWindowHeight: 125,
        pipWindowWidth: 125,
        bottomSpace: MediaQuery.of(context).size.height *
            0.6, // Match bottomsheet height
        leftSpace: 0,
        rightSpace: 0,
        topSpace: 0,
        maxSize: const Size(125, 125),
        minSize: const Size(125, 125),
        movable: true,
        resizable: false,
        initialCorner: PIPViewCorner.bottomLeft,
      ),
    );

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => StyleSelectorBottomSheet(
        currentMapStyle: currentStyle,
        onStyleSelected: onStyleSelected,
      ),
    ).then((_) {
      // Restore default PiP params when sheet is closed
      PictureInPicture.updatePiPParams(
        pipParams: const PiPParams(
          pipWindowHeight: 125,
          pipWindowWidth: 125,
          bottomSpace: 64,
          leftSpace: 0,
          rightSpace: 0,
          topSpace: 64,
          maxSize: Size(125, 125),
          minSize: Size(125, 125),
          movable: true,
          resizable: false,
          initialCorner: PIPViewCorner.bottomLeft,
        ),
      );
    });
  }

  @override
  State<StyleSelectorBottomSheet> createState() =>
      _StyleSelectorBottomSheetState();
}

class _StyleSelectorBottomSheetState extends State<StyleSelectorBottomSheet> {
  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.black87 : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: isDarkMode
                  ? Colors.white.withOpacity(0.3)
                  : Colors.black.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Text(
              'Choose Map Style',
              style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black87,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // Style options
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 0.9,
              children: MapStyle.values.map((style) {
                final styleConfig = StyleSelectorBottomSheet.mapStyles[style]!;
                final isSelected = style == widget.currentMapStyle;

                return GestureDetector(
                  onTap: () {
                    widget.onStyleSelected(style);
                    Navigator.pop(context);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      border: isSelected
                          ? Border.all(
                              color: styleConfig.accentColor,
                              width: 3,
                            )
                          : null,
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: styleConfig.accentColor.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: Stack(
                      children: [
                        // Main content
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Preview image or fallback icon
                            Center(
                              child: SizedBox(
                                width: 84,
                                height: 84,
                                child: styleConfig.previewImagePath != null
                                    ? ClipOval(
                                        child: Image.asset(
                                          styleConfig.previewImagePath!,
                                          fit: BoxFit.cover,
                                          errorBuilder: (context, error, stackTrace) {
                                            // Fallback to icon if image fails to load
                                            return Container(
                                              decoration: BoxDecoration(
                                                color: styleConfig.accentColor.withOpacity(0.1),
                                                shape: BoxShape.circle,
                                              ),
                                              child: Icon(
                                                styleConfig.icon,
                                                color: styleConfig.accentColor,
                                                size: 28,
                                              ),
                                            );
                                          },
                                        ),
                                      )
                                    : Container(
                                        decoration: BoxDecoration(
                                          color: styleConfig.accentColor.withOpacity(0.1),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          styleConfig.icon,
                                          color: styleConfig.accentColor,
                                          size: 28,
                                        ),
                                      ),
                              ),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              styleConfig.name,
                              style: TextStyle(
                                color: isDarkMode ? Colors.white : Colors.black87,
                                fontSize: 14,
                                fontWeight:
                                    isSelected ? FontWeight.w600 : FontWeight.w500,
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(height: 4),
                              Container(
                                width: 6,
                                height: 6,
                                decoration: BoxDecoration(
                                  color: styleConfig.accentColor,
                                  shape: BoxShape.circle,
                                ),
                              ),
                            ],
                          ],
                        ),
                        // Animated indicator in top left with glassmorphism
                        if (styleConfig.isAnimated)
                          Positioned(
                            top: 8,
                            left: 8,
                            child: TweenAnimationBuilder<double>(
                              duration: const Duration(milliseconds: 2000),
                              tween: Tween(begin: 0.0, end: 1.0),
                              builder: (context, value, child) {
                                return Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 3,
                                  ),
                                  decoration: BoxDecoration(
                                    // Glassmorphism background
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        styleConfig.accentColor.withOpacity(0.3 + (value * 0.2)),
                                        styleConfig.accentColor.withOpacity(0.1 + (value * 0.1)),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: styleConfig.accentColor.withOpacity(0.4 + (value * 0.3)),
                                      width: 1,
                                    ),
                                    // Glassmorphism blur effect simulation
                                    boxShadow: [
                                      BoxShadow(
                                        color: styleConfig.accentColor.withOpacity(0.2 + (value * 0.2)),
                                        blurRadius: 8,
                                        spreadRadius: 1,
                                      ),
                                      BoxShadow(
                                        color: Colors.white.withOpacity(0.1 + (value * 0.1)),
                                        blurRadius: 4,
                                        offset: const Offset(-1, -1),
                                      ),
                                    ],
                                  ),
                                  child: Text(
                                    'Animated',
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.9 + (value * 0.1)),
                                      fontSize: 9,
                                      fontWeight: FontWeight.w600,
                                      shadows: [
                                        Shadow(
                                          color: styleConfig.accentColor.withOpacity(0.5),
                                          blurRadius: 2,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        // Lock indicator in top right
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.black.withOpacity(0.7)
                                  : Colors.white.withOpacity(0.9),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isDarkMode
                                    ? Colors.white.withOpacity(0.2)
                                    : Colors.black.withOpacity(0.1),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.lock,
                              size: 12,
                              color: isDarkMode
                                  ? Colors.white.withOpacity(0.7)
                                  : Colors.black.withOpacity(0.6),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
