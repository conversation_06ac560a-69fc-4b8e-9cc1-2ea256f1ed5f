import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PinsInAuraIndicatorWidget extends StatelessWidget {
  final List<Map<String, dynamic>> pinsInAura;
  final Animation<double> pulseAnimation;
  final VoidCallback onTap;
  final bool isDarkMode;

  const PinsInAuraIndicatorWidget({
    super.key,
    required this.pinsInAura,
    required this.pulseAnimation,
    required this.onTap,
    required this.isDarkMode,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          debugPrint('🎯 InkWell onTap triggered!');
          HapticFeedback.lightImpact();
          onTap();
        },
        borderRadius: BorderRadius.circular(25),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.black.withOpacity(0.8) : Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Pulsing aura icon
              AnimatedBuilder(
                animation: pulseAnimation,
                builder: (context, child) {
                  return Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.cyan.withOpacity(0.3 + (pulseAnimation.value * 0.4)),
                    ),
                    child: Icon(
                      Icons.radio_button_checked,
                      size: 14,
                      color: Colors.cyan.withOpacity(0.8 + (pulseAnimation.value * 0.2)),
                    ),
                  );
                },
              ),
              
              const SizedBox(width: 8),
              
              // Text
              Text(
                '${pinsInAura.length} pin${pinsInAura.length != 1 ? 's' : ''} nearby',
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              
              const SizedBox(width: 8),
              
              // Arrow icon
              Icon(
                Icons.arrow_forward_ios,
                size: 12,
                color: isDarkMode ? Colors.white.withOpacity(0.7) : Colors.black.withOpacity(0.7),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 