import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../../widgets/music/track_selector.dart';
import '../../models/music_track.dart';
import '../../widgets/animations/fade_in_animation.dart';
import '../../services/api/bop_drops_service.dart';
import '../../models/music/bop_drop.dart';
import '../../config/themes.dart';

class BopDropScreen extends StatefulWidget {
  const BopDropScreen({Key? key}) : super(key: key);

  @override
  State<BopDropScreen> createState() => _BopDropScreenState();
}

class _BopDropScreenState extends State<BopDropScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _gradientAnimation;
  bool _isSubmitting = false;
  String? _selectedMood;
  final TextEditingController _captionController = TextEditingController();
  final FocusNode _captionFocusNode = FocusNode();

  // Bop drop gradient colors (same style as challenge)
  final List<Color> gradientColors = [
    const Color(0xFF667eea),
    const Color(0xFF764ba2),
  ];

  // Available moods
  final List<Map<String, dynamic>> moods = [
    {'value': 'happy', 'label': 'Happy', 'emoji': '😊'},
    {'value': 'energetic', 'label': 'Energetic', 'emoji': '⚡'},
    {'value': 'chill', 'label': 'Chill', 'emoji': '😎'},
    {'value': 'nostalgic', 'label': 'Nostalgic', 'emoji': '🌅'},
    {'value': 'party', 'label': 'Party', 'emoji': '🎉'},
    {'value': 'romantic', 'label': 'Romantic', 'emoji': '💕'},
    {'value': 'focus', 'label': 'Focus', 'emoji': '🎯'},
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );

    _gradientAnimation = Tween<double>(
      begin: -0.5,
      end: 1.5,
    ).animate(_animationController);
    
    // Start animation
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _captionController.dispose();
    _captionFocusNode.dispose();
    super.dispose();
  }

  void _dismissKeyboard() {
    if (_captionFocusNode.hasFocus) {
      _captionFocusNode.unfocus();
    }
  }

  Future<void> _handleTrackSelected(MusicTrack track) async {
    if (_isSubmitting) return;

    // Show customization modal first
    final result = await _showCustomizationModal(track);
    if (result != true) return;

    try {
      setState(() => _isSubmitting = true);
      HapticFeedback.mediumImpact();

      // Create the bop drop
      final bopDrop = await BopDropsService.createBopDrop(
        trackId: track.id,
        trackTitle: track.title,
        trackArtist: track.artist,
        trackAlbum: track.album,
        albumArtUrl: track.albumArtUrl,
        previewUrl: track.previewUrl,
        musicService: 'spotify',
        caption: _captionController.text.trim().isEmpty ? null : _captionController.text.trim(),
        mood: _selectedMood,
        isCurrentlyPlaying: false,
        friendsOnly: true,
      );

      if (!mounted) return;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text('Bop drop shared! 🎵\n"${track.title}" by ${track.artist}'),
              ),
            ],
          ),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.green.shade600,
          duration: const Duration(seconds: 3),
        ),
      );

      // Navigate back to friends screen
      Navigator.pop(context, true); // Return true to indicate success

    } catch (e) {
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text('Failed to share bop drop: ${e.toString().replaceAll('Exception: ', '')}'),
              ),
            ],
          ),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Theme.of(context).colorScheme.error,
          duration: const Duration(seconds: 4),
        ),
      );
      
      debugPrint('Error creating bop drop: $e');
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  Future<bool?> _showCustomizationModal(MusicTrack track) async {
    return showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => GestureDetector(
          onTap: _dismissKeyboard,
          child: DraggableScrollableSheet(
            initialChildSize: 0.7,
            minChildSize: 0.5,
            maxChildSize: 0.9,
            builder: (context, scrollController) => Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  
                  Expanded(
                    child: SingleChildScrollView(
                      controller: scrollController,
                      padding: const EdgeInsets.all(24),
                      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Track preview
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: 60,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    image: track.albumArtUrl != null
                                        ? DecorationImage(
                                            image: NetworkImage(track.albumArtUrl!),
                                            fit: BoxFit.cover,
                                          )
                                        : null,
                                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                                  ),
                                  child: track.albumArtUrl == null
                                      ? Icon(
                                          Icons.music_note,
                                          color: Theme.of(context).colorScheme.primary,
                                        )
                                      : null,
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        track.title,
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        track.artist,
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // Caption input
                          Text(
                            'Add a caption (optional)',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 12),
                          TextField(
                            controller: _captionController,
                            focusNode: _captionFocusNode,
                            maxLines: 3,
                            maxLength: 280,
                            textInputAction: TextInputAction.done,
                            textCapitalization: TextCapitalization.sentences,
                            keyboardType: TextInputType.multiline,
                            onSubmitted: (value) => _dismissKeyboard(),
                            onTapOutside: (event) => _dismissKeyboard(),
                            decoration: InputDecoration(
                              hintText: 'What makes this song special?',
                              hintStyle: TextStyle(
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: Theme.of(context).colorScheme.primary,
                                  width: 2,
                                ),
                              ),
                              filled: true,
                              fillColor: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                              counterStyle: TextStyle(
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                fontSize: 12,
                              ),
                            ),
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // Mood selection
                          Text(
                            'Choose a mood (optional)',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: moods.map((mood) => FilterChip(
                              label: Text(
                                '${mood['emoji']} ${mood['label']}',
                                style: TextStyle(
                                  color: _selectedMood == mood['value'] 
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(context).colorScheme.onSurface,
                                  fontWeight: _selectedMood == mood['value'] 
                                      ? FontWeight.w600 
                                      : FontWeight.normal,
                                ),
                              ),
                              selected: _selectedMood == mood['value'],
                              onSelected: (selected) {
                                setModalState(() {
                                  _selectedMood = selected ? mood['value'] : null;
                                });
                                // Also update the main state
                                setState(() {
                                  _selectedMood = selected ? mood['value'] : null;
                                });
                              },
                              backgroundColor: Theme.of(context).colorScheme.surface,
                              selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.15),
                              checkmarkColor: Theme.of(context).colorScheme.primary,
                              side: BorderSide(
                                color: _selectedMood == mood['value']
                                    ? Theme.of(context).colorScheme.primary.withOpacity(0.5)
                                    : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                                width: _selectedMood == mood['value'] ? 1.5 : 1,
                              ),
                              elevation: _selectedMood == mood['value'] ? 2 : 0,
                              shadowColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                            )).toList(),
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Action buttons
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton(
                                  onPressed: () {
                                    _dismissKeyboard();
                                    Navigator.pop(context, false);
                                  },
                                  style: OutlinedButton.styleFrom(
                                    side: BorderSide(
                                      color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
                                    ),
                                    padding: const EdgeInsets.symmetric(vertical: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  child: Text(
                                    'Cancel',
                                    style: TextStyle(
                                      color: Theme.of(context).colorScheme.onSurface,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: () {
                                    _dismissKeyboard();
                                    Navigator.pop(context, true);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Theme.of(context).colorScheme.primary,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(vertical: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    elevation: 2,
                                    shadowColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                                  ),
                                  child: const Text(
                                    'Share Bop',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          
                          // Extra padding for keyboard
                          SizedBox(height: MediaQuery.of(context).viewInsets.bottom + 16),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            expandedHeight: size.height * 0.28,
            floating: true,
            pinned: true,
            stretch: true,
            backgroundColor: Colors.transparent,
            automaticallyImplyLeading: false,
            leading: Container(
              margin: const EdgeInsets.only(left: 8),
              child: IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDark ? Colors.black38 : Colors.white38,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  child: const Icon(Icons.arrow_back_ios_new, size: 16, color: Colors.white),
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: AnimatedBuilder(
                animation: _gradientAnimation,
                builder: (context, child) {
                  return Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment(
                          _gradientAnimation.value * 0.8,
                          -0.5,
                        ),
                        end: Alignment(
                          1 - _gradientAnimation.value * 0.8,
                          1.5,
                        ),
                        colors: gradientColors,
                        stops: const [0.0, 1.0],
                      ),
                    ),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
                      child: SafeArea(
                        child: Align(
                          alignment: Alignment.center,
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxWidth: size.width * 0.85,
                              minHeight: 100,
                            ),
                            child: Container(
                              margin: const EdgeInsets.only(top: 48),
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.15),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.2),
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.music_note_rounded,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      const Text(
                                        'Share a Bop',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 20,
                                          fontWeight: FontWeight.w600,
                                          letterSpacing: 0.3,
                                          height: 1.3,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    'Recommend a song to your friends and share your music taste',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.9),
                                      fontSize: 15,
                                      fontWeight: FontWeight.w400,
                                      letterSpacing: 0.2,
                                      height: 1.4,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
        body: FadeInAnimation(
          delay: const Duration(milliseconds: 200),
          child: TrackSelector(
            showRecentlyPlayed: true,
            showTopTracks: true,
            showLikedSongs: true,
            showPlaylists: true,
            showSearchBar: true,
            onTrackSelected: _handleTrackSelected,
          ),
        ),
      ),
    );
  }
} 