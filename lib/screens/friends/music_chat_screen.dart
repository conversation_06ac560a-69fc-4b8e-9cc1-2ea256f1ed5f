import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../models/user.dart';
import '../../providers/music_chat_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/friends/music_chat/music_chat_header.dart';
import '../../widgets/friends/music_chat/music_chat_messages.dart';
import '../../widgets/friends/music_chat/music_recommendation_input.dart';
import '../../widgets/friends/music_chat/music_message_bubble.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/loading_indicator.dart';

class MusicChatScreen extends StatefulWidget {
  final User friend;

  const MusicChatScreen({
    Key? key,
    required this.friend,
  }) : super(key: key);

  @override
  State<MusicChatScreen> createState() => _MusicChatScreenState();
}

class _MusicChatScreenState extends State<MusicChatScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Load chat history when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadChatHistory();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadChatHistory() async {
    final chatProvider = Provider.of<MusicChatProvider>(context, listen: false);
    await chatProvider.loadChatHistory(widget.friend.id);
    
    // Automatically scroll to bottom to show latest messages after loading
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      // Small delay to ensure UI is fully rendered
      Future.delayed(const Duration(milliseconds: 100), () {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent + 50, // Add small buffer
            duration: const Duration(milliseconds: 400), // Smooth animation
            curve: Curves.easeOutCubic,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Custom header with friend info and streak
            MusicChatHeader(friend: widget.friend),
            
            // Messages list with recommendations
            Expanded(
              child: Consumer<MusicChatProvider>(
                builder: (context, provider, child) {
                  if (kDebugMode) {
                    print('🎵 UI: Building with ${provider.messages.length} messages');
                    print('🎵 UI: Loading state: ${provider.isLoading}');
                    print('🎵 UI: Error: ${provider.error}');
                  }
                  
                  if (provider.isLoading && provider.messages.isEmpty) {
                    return const Center(
                      child: LoadingIndicator(
                        message: 'Loading chat history...',
                      ),
                    );
                  }
                  
                  if (provider.error != null && provider.messages.isEmpty) {
                    return ErrorView(
                      message: 'Failed to load messages',
                      onRetry: _loadChatHistory,
                    );
                  }
                  
                  return Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final currentUserId = authProvider.currentUser?.id ?? 1;
                      
                      if (kDebugMode) {
                        print('🎵 UI: Current user ID: $currentUserId');
                        print('🎵 UI: Building MusicChatMessages with ${provider.messages.length} messages');
                      }
                      
                      return MusicChatMessages(
                        messages: provider.messages,
                        currentUserId: currentUserId,
                        scrollController: _scrollController,
                        itemBuilder: (context, index) {
                          final message = provider.messages[index];
                          
                          if (kDebugMode) {
                            print('🎵 UI: Building message $index: ${message.id} - ${message.track.title}');
                          }
                          
                          // Auto-scroll to bottom when messages are first displayed
                          if (index == provider.messages.length - 1) {
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (_scrollController.hasClients && 
                                  _scrollController.position.pixels == 0.0 &&
                                  provider.messages.isNotEmpty) {
                                _scrollToBottom();
                              }
                            });
                          }
                          
                          return Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            child: MusicMessageBubble(
                              track: message.track,
                              isFromMe: message.senderId == currentUserId,
                              timestamp: message.timestamp,
                              message: message.message,
                              messageId: message.id,
                              reactions: message.reactions.map((r) => r.emoji).toList(),
                            ),
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
            
            // Bottom input for sending recommendations
            Consumer<MusicChatProvider>(
              builder: (context, provider, child) {
                return MusicRecommendationInput(
                  onSendRecommendation: (track, message) async {
                    final success = await provider.sendRecommendation(
                      friend: widget.friend,
                      track: track,
                      message: message,
                    );
                    
                    if (success) {
                      _scrollToBottom();
                    } else if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Failed to send recommendation'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }
} 