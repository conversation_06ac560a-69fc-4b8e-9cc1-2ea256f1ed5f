import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/themes.dart';
import '../../providers/auth_provider.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/youtube_provider.dart';
import '../../providers/user_provider.dart';
import '../../widgets/navigation/bottom_navigation_bar.dart';
import '../../widgets/music/now_playing_bar.dart';
import '../../widgets/music/apple_now_playing_bar.dart';
import '../../widgets/music/youtube_now_playing_bar.dart.dart';
import '../../utils/navigation_helper.dart';
import '../../utils/app_tab.dart';
import '../../widgets/friends/friends_header.dart';
import '../../widgets/friends/friends_tab.dart';
import '../../widgets/friends/friend_requests_tab.dart';
import '../../widgets/common/keep_alive_wrapper.dart';
import '../../providers/friends_provider.dart';
import '../../screens/skins/skin_collection_screen.dart';
import '../../widgets/navigation/app_navigation_system.dart';

class FriendsScreen extends StatefulWidget {
  final bool showBottomNav;
  final double collapsedHeight;
  final double expandedHeight;
  final Color? collapsedBackgroundColor;
  final Color? expandedBackgroundColor;
  
  const FriendsScreen({
    Key? key,
    this.showBottomNav = true,
    this.collapsedHeight = 0,
    this.expandedHeight = 160.0,
    this.collapsedBackgroundColor,
    this.expandedBackgroundColor,
  }) : super(key: key);

  @override
  State<FriendsScreen> createState() => _FriendsScreenState();
}

class _FriendsScreenState extends State<FriendsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  double _scrollOffset = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 2,
      vsync: this,
    );
    _tabController.addListener(_handleTabChange);
    _scrollController.addListener(_onScroll);

    // Initialize user profile data early to ensure it's available for AI search
    _initializeUserProfile();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final friendsProvider = context.read<FriendsProvider>();
      // Load both friends and requests data when screen initializes
      if (friendsProvider.isInitialized) {
        friendsProvider.loadFriends();
        friendsProvider.loadFriendRequests();
      }
    });
  }

  /// Initialize user profile data to ensure it's available for AI search and other features
  void _initializeUserProfile() {
    if (!mounted) return;

    // Initialize user profile in background to ensure it's available for AI search
    Future.microtask(() async {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);

        // Check if user profile is already loaded
        if (userProvider.currentUser == null) {
          print('👤 FriendsScreen: User profile not loaded, initializing...');
          await userProvider.initialize();
          print('✅ FriendsScreen: User profile initialized successfully');
        } else {
          print('✅ FriendsScreen: User profile already loaded');
        }
      } catch (e) {
        print('❌ FriendsScreen: Error initializing user profile: $e');
        // Don't block friends screen initialization if user profile fails
      }
    });
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      // Only handle the change when the animation is complete
      setState(() {});
      // Refresh data when switching tabs
      final friendsProvider = context.read<FriendsProvider>();
      if (_tabController.index == 0) {
        friendsProvider.loadFriends();
      } else {
        friendsProvider.loadFriendRequests();
      }
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    setState(() {
      _scrollOffset = _scrollController.offset;
    });
  }
  
  // Handle navigation to other tabs
  void _handleNavigation(int index) {
    if (index == 4) return; // Already on friends
    
    if (index == 2) { // Map tab
      // Use NavigationHelper to return to map
      NavigationHelper.navigateToScreen(context, '/map');
      return;
    }
    
    // Use navigation helper for other tabs
    NavigationHelper.navigateToTab(context, index);
  }
  
  void _handleAddPin() {
    // Handle add pin action
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add pin feature coming soon')),
    );
  }

  // Handle navigation to skins screen
  void _handleOpenSkins() {
    AppNavigationSystem().navigateToSkins(context);
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context);
    final youtubeProvider = Provider.of<YouTubeProvider>(context);
    final friendsProvider = Provider.of<FriendsProvider>(context);
    final user = authProvider.currentUser;
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;
    
    // Check for active music from all providers
    final hasAppleMusicActive = appleMusicProvider.hasActivePlayback && appleMusicProvider.currentTrack != null;
    final hasSpotifyActive = spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null;
    // YouTube excluded - it has its own embedded player
    final hasActiveMusic = hasAppleMusicActive || hasSpotifyActive; // Exclude YouTube
    
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Calculate bottom padding for content
    final bottomPadding = widget.showBottomNav ? kBottomNavigationBarHeight : 0.0;
    final nowPlayingHeight = 68.0; // Height of the now playing bar
    final totalBottomPadding = hasActiveMusic ? bottomPadding + nowPlayingHeight : bottomPadding;
    
    // Get theme colors
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final backgroundColor = theme.scaffoldBackgroundColor;
    final surfaceColor = theme.colorScheme.surface;
    
    // Custom colors for the app bar
    final collapsedColor = widget.collapsedBackgroundColor ?? theme.colorScheme.surface;
    final expandedColor = widget.expandedBackgroundColor ?? theme.colorScheme.surface;
    
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        automaticallyImplyLeading: false,
        elevation: 0,
        toolbarHeight: 0,
      ),
      body: Stack(
        children: [
          // Main content
          NestedScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  expandedHeight: widget.expandedHeight,
                  toolbarHeight: widget.collapsedHeight,
                  floating: false,
                  pinned: true,
                  stretch: true,
                  backgroundColor: collapsedColor,
                  automaticallyImplyLeading: false,
                  flexibleSpace: LayoutBuilder(
                    builder: (BuildContext context, BoxConstraints constraints) {
                      final top = constraints.biggest.height;
                      final expandRatio = (top - widget.collapsedHeight) / (widget.expandedHeight - widget.collapsedHeight);
                      final isCollapsed = expandRatio < 0.5;
                      
                      return FlexibleSpaceBar(
                        background: Container(
                          color: expandedColor,
                          child: SafeArea(
                            bottom: false,
                            child: Opacity(
                              opacity: expandRatio.clamp(0.0, 1.0),
                              child: FriendsHeader(
                                size: size,
                                onCreateChallenge: () => _handleNavigation(AppTab.challenges.index),
                                onOpenSkins: _handleOpenSkins,
                              ),
                            ),
                          ),
                        ),
                        expandedTitleScale: 1.0,
                      );
                    },
                  ),
                ),
                SliverPersistentHeader(
                  pinned: true,
                  delegate: _SliverAppBarDelegate(
                    TabBar(
                      controller: _tabController,
                      labelColor: theme.colorScheme.primary,
                      unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.5),
                      labelStyle: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: screenWidth < 360 ? 11 : 12,
                        letterSpacing: -0.3,
                      ),
                      unselectedLabelStyle: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: screenWidth < 360 ? 11 : 12,
                        letterSpacing: -0.3,
                      ),
                      indicator: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: theme.colorScheme.primary.withOpacity(0.1),
                      ),
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelPadding: EdgeInsets.symmetric(
                        horizontal: screenWidth < 360 ? 4 : 6,
                        vertical: 0,
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: screenWidth < 360 ? 4 : 8,
                        vertical: 12,
                      ),
                      dividerColor: Colors.transparent,
                      splashFactory: NoSplash.splashFactory,
                      overlayColor: MaterialStateProperty.resolveWith<Color?>(
                        (Set<MaterialState> states) {
                          return states.contains(MaterialState.focused) ? null : Colors.transparent;
                        },
                      ),
                      tabs: [
                        _buildTab(
                          Icons.people,
                          'Friends (${friendsProvider.friendsCount})',
                          screenWidth < 360 ? 14 : 16,
                        ),
                        _buildTab(
                          Icons.person_add_alt_1,
                          'Requests (${friendsProvider.pendingRequestsCount})',
                          screenWidth < 360 ? 14 : 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ];
            },
            body: TabBarView(
              controller: _tabController,
              children: [
                KeepAliveWrapper(
                  child: FriendsTab(
                    key: const PageStorageKey('friends_tab'),
                    tabController: _tabController,
                    scrollController: _scrollController,
                    headerHeight: widget.expandedHeight,
                  ),
                ),
                KeepAliveWrapper(
                  child: FriendRequestsTab(
                    key: const PageStorageKey('friend_requests_tab'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Show Apple Music now playing bar if Apple Music is active
          if (hasAppleMusicActive)
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: const AppleNowPlayingBar(),
            )
          // Show Spotify now playing bar if Spotify is active and Apple Music is not
          else if (hasSpotifyActive)
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: const NowPlayingBar(),
            ),
          // YouTube removed - it has its own embedded player
          if (widget.showBottomNav)
            MusicPinBottomNavBar.auto(
              context: context,
              onTabSelected: _handleNavigation,
              onAddPinPressed: _handleAddPin,
            ),
        ],
      ),
    );
  }

  Widget _buildTab(IconData icon, String text, double iconSize) {
    return Tab(
      height: 36,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: iconSize),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                text,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _SliverAppBarDelegate(this.child);

  @override
  double get minExtent => 64.0;

  @override
  double get maxExtent => 64.0;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      height: maxExtent,
      color: Theme.of(context).scaffoldBackgroundColor,
      child: child,
    );
  }

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
} 