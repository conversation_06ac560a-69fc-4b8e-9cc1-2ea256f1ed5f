import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../models/collection_model.dart';
import '../../models/music_track.dart';
import '../../config/themes.dart';
import '../../widgets/navigation/bottom_navigation_bar.dart';
import '../../widgets/music/now_playing_bar.dart';
import '../../widgets/music/apple_now_playing_bar.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../services/collection_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../services/cloudinary_service.dart';
import '../../utils/event_bus.dart';
import '../../widgets/common/cloudinary_image.dart';
import '../../utils/text_encoding_utils.dart';
import 'dart:async';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import '../../services/music/shuffle_service.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map_marker_cluster/flutter_map_marker_cluster.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../../screens/music/create_pin_track_select_screen.dart';
import '../../screens/ar/ar_pin_placement_screen.dart';
import '../../screens/music/track_select_screen.dart';
import '../../providers/map_provider.dart';
import '../../widgets/music/track_selector.dart';
import '../../providers/pin_provider.dart';

class CollectionDetailScreen extends StatefulWidget {
  final Collection collection;
  
  const CollectionDetailScreen({
    Key? key,
    required this.collection,
  }) : super(key: key);

  @override
  State<CollectionDetailScreen> createState() => _CollectionDetailScreenState();
}

class _CollectionDetailScreenState extends State<CollectionDetailScreen> with TickerProviderStateMixin {
  late Collection _collection;
  bool _isLoading = false;
  bool _isEditMode = false;
  bool _isDeletingCollection = false;
  Set<String> _selectedPins = {};
  late AnimationController _headerAnimController;
  late AnimationController _listAnimController;
  late Animation<double> _headerAnimation;
  late Animation<double> _listAnimation;
  
  // Filter and sort options
  String _sortBy = 'Recent';
  
  // Services
  late CollectionService _collectionService;
  late final StreamSubscription _eventSub;

  @override
  void initState() {
    super.initState();
    _collection = widget.collection;
    
    // Debug log to check if emoji cleaning is working for collection name
    if (kDebugMode) {
      print('🎨 [CollectionDetail] Collection name: "${_collection.name}"');
      if (TextEncodingUtils.hasEncodingIssues(_collection.name)) {
        print('⚠️ [CollectionDetail] Collection name has encoding issues');
      } else {
        print('✅ [CollectionDetail] Collection name is clean');
      }
    }
    
    // Initialize services
    final apiService = ApiService();
    _collectionService = CollectionService(
      apiService,
      AuthService(apiService),
    );
    
    // Initialize animations
    _headerAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _listAnimController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _headerAnimation = CurvedAnimation(
      parent: _headerAnimController,
      curve: Curves.easeOutCubic,
    );
    _listAnimation = CurvedAnimation(
      parent: _listAnimController,
      curve: Curves.easeOutCubic,
    );
    
    // Start animations
    _headerAnimController.forward();
    _loadCollectionDetails();

    // Listen for collection updates
    _eventSub = EventBus().stream.listen((event) {
      if (event is CollectionUpdatedEvent && event.collectionId == _collection.id.toString()) {
        // Only reload details if the screen is still mounted and we haven't initiated deletion
        if (mounted && !_isDeletingCollection) {
          _loadCollectionDetails();
        }
      }
    });
  }
  
  @override
  void dispose() {
    _headerAnimController.dispose();
    _listAnimController.dispose();
    _eventSub.cancel();
    super.dispose();
  }
  
  Future<void> _loadCollectionDetails() async {
    setState(() => _isLoading = true);
    
    try {
      final collectionDetails = await _collectionService.getCollectionById(_collection.id);
      
      if (mounted && collectionDetails != null) {
        setState(() {
          _collection = collectionDetails;
          _isLoading = false;
        });
        _listAnimController.forward();
      } else if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to load collection details'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading collection: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
  
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    if (difference.inDays > 7) {
      return '${(difference.inDays / 7).floor()}w ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'now';
    }
  }

  /// Convert pin data to MusicTrack object
  MusicTrack? _convertPinToMusicTrack(Map<String, dynamic> pin) {
    try {
      final platform = pin['platform']?.toString().toLowerCase() ?? 'spotify';
      final constructedUri = _constructTrackUri(pin);

      // Extract the actual track ID from the URI
      String trackId = pin['id'].toString();
      if (platform.contains('apple') && constructedUri.startsWith('apple:track:')) {
        trackId = constructedUri.split(':').last;
      } else if (platform.contains('spotify') && constructedUri.startsWith('spotify:track:')) {
        trackId = constructedUri.split(':').last;
      }

      return MusicTrack(
        id: trackId,
        title: pin['title']?.toString() ?? 'Unknown Track',
        artist: pin['artist']?.toString() ?? 'Unknown Artist',
        album: pin['album']?.toString() ?? '',
        albumArt: pin['imageUrl']?.toString() ?? '',
        url: pin['track_url'] ?? pin['url'] ?? '',
        service: platform,
        serviceType: platform,
        genres: [],
        durationMs: pin['duration'] != null ? _parseDurationMs(pin['duration']) : 0,
        popularity: pin['popularity']?.toInt() ?? 50,
        uri: constructedUri,
        isLibrary: false,
      );
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [CollectionDetail] Error converting pin to MusicTrack: $e');
      }
      return null;
    }
  }
  
  List<Map<String, dynamic>> get _mockPins {
    // If we have real pins from the API, use them
    if (_collection.pins != null && _collection.pins!.isNotEmpty) {
      final pins = _collection.pins!.map((collectionPin) {
        final pinDetails = collectionPin.pinDetails ?? {};
        final isVirtual = collectionPin.isVirtual;
        
        // Check if this pin has serialization errors from the backend
        final hasError = pinDetails.containsKey('error');
        if (hasError) {
          print('⚠️ Pin ${collectionPin.id} has incomplete data: ${pinDetails['error']}');
        }
        
        // Helper function to safely extract string values with multiple fallbacks
        String getStringValue(Map<String, dynamic> data, List<String> keys, String defaultValue) {
          for (final key in keys) {
            final value = data[key];
            if (value != null && value.toString().trim().isNotEmpty) {
              return value.toString();
            }
          }
          return defaultValue;
        }
        
        // Helper function to safely extract owner information
        String getOwnerName(Map<String, dynamic> data) {
          // Try owner object first (this is the correct structure from Django)
          final ownerObj = data['owner'];
          if (ownerObj is Map<String, dynamic>) {
            final username = ownerObj['username'];
            if (username != null && username.toString().trim().isNotEmpty) {
              return '@${username.toString()}';
            }
            
            // Also try 'name' field as fallback
            final name = ownerObj['name'];
            if (name != null && name.toString().trim().isNotEmpty) {
              return '@${name.toString()}';
            }
          }
          
          // Try direct owner_name field (fallback for other data structures)
          final ownerName = getStringValue(data, ['owner_name'], '');
          if (ownerName.isNotEmpty) {
            return ownerName.startsWith('@') ? ownerName : '@$ownerName';
          }
          
          // Try direct username field (fallback)
          final username = getStringValue(data, ['username'], '');
          if (username.isNotEmpty) {
            return username.startsWith('@') ? username : '@$username';
          }
          
          return '@unknown';
        }
        
        // Helper function to format duration from various sources
        String getDuration(Map<String, dynamic> data) {
          // First check if the pin object has duration_ms directly
          if (collectionPin.pinDetails != null) {
            final pinDetailsObj = collectionPin.pinDetails!;
            
            // Try duration_ms field first
            final durationMs = pinDetailsObj['duration_ms'];
            if (durationMs != null) {
              final ms = durationMs is int ? durationMs : int.tryParse(durationMs.toString()) ?? 0;
              if (ms > 0) {
                final minutes = (ms / 60000).floor();
                final seconds = ((ms % 60000) / 1000).floor();
                return '$minutes:${seconds.toString().padLeft(2, '0')}';
              }
            }
            
            // Try duration field as fallback
            final duration = pinDetailsObj['duration'];
            if (duration != null && duration.toString().trim().isNotEmpty) {
              return duration.toString();
            }
          }
          
          return '3:20'; // Default fallback
        }
        
        // Extract track information with proper fallbacks
        // Use the basic 'title' field from the error response if track_title is not available
        final rawTitle = getStringValue(pinDetails, ['track_title', 'title', 'name'], hasError ? 'Incomplete Data' : 'Unknown Track');
        final rawArtist = getStringValue(pinDetails, ['track_artist', 'artist', 'artistName'], hasError ? 'Missing Info' : 'Unknown Artist');
        final rawAlbum = getStringValue(pinDetails, ['album', 'track_album', 'albumName'], hasError ? 'No Album' : 'Unknown Album');
        
        // Clean track info to fix any encoding issues
        final title = TextEncodingUtils.cleanCollectionText(rawTitle, isDescription: false);
        final artist = TextEncodingUtils.cleanCollectionText(rawArtist, isDescription: false);
        final album = TextEncodingUtils.cleanCollectionText(rawAlbum, isDescription: false);
        final service = getStringValue(pinDetails, ['service'], 'spotify');
        final ownerName = getOwnerName(pinDetails);
        final duration = getDuration(pinDetails);
        
        // Get album art URL with fallbacks
        String getAlbumArt() {
          // First check if the pin object has artwork_url directly
          if (collectionPin.pinDetails != null) {
            final pinDetailsObj = collectionPin.pinDetails!;
            
            // Try to get artwork_url from pin details
            final artworkUrl = pinDetailsObj['artwork_url'];
            if (artworkUrl != null && artworkUrl.toString().trim().isNotEmpty) {
              return artworkUrl.toString();
            }
            
            // Fallback to artwork nested object (for Apple Music)
            final artwork = pinDetailsObj['artwork'];
            if (artwork is Map<String, dynamic> && artwork['url'] != null) {
              return artwork['url'].toString().replaceAll('{w}', '300').replaceAll('{h}', '300');
            }
            
            // Other fallbacks
            final albumArt = getStringValue(pinDetailsObj, ['album_art', 'albumArt', 'imageUrl'], '');
            if (albumArt.isNotEmpty) {
              return albumArt;
            }
          }
          
          // If there's an error, use a placeholder indicating missing data
          if (hasError) {
            return 'https://via.placeholder.com/300x300/FF6B6B/FFFFFF?text=Incomplete+Data';
          }
          
          // Service-specific default images
          switch (service.toLowerCase()) {
            case 'spotify':
              return 'https://i.scdn.co/image/ab67616d0000b273d1d6c90b97b9f8d6e5495df7';
            case 'apple_music':
              return 'https://via.placeholder.com/300x300/FA243C/FFFFFF?text=${Uri.encodeComponent(title)}';
            default:
              return 'https://via.placeholder.com/300x300/8A2BE2/FFFFFF?text=${Uri.encodeComponent(title)}';
          }
        }
        
        // Get track URL with fallbacks
        final trackUrl = getStringValue(pinDetails, ['track_url', 'trackUrl', 'url'], '');
        
        // Create a unique ID for each collection pin to avoid duplicates
        final uniqueId = 'collection_pin_${collectionPin.id}_${isVirtual ? 'virtual_${collectionPin.virtualPin}' : 'pin_${collectionPin.pin}'}';
        
        return {
          'id': uniqueId,
          'originalPinId': isVirtual ? collectionPin.virtualPin : collectionPin.pin,
          'collectionPinId': collectionPin.id,
          'title': title,
          'artist': artist,
          'album': album,
          'platform': service,
          'track_url': trackUrl, // Add the track URL here
          'imageUrl': getAlbumArt(),
          'originUser': ownerName,
          'originAvatar': pinDetails['owner']?['profile_picture_url'] ?? pinDetails['owner']?['profile_pic'],
          'originPlace': isVirtual ? 'Virtual Pin' : getStringValue(pinDetails, ['location_name'], 'Unknown Location'),
          'venue': isVirtual ? 'Personal Collection' : getStringValue(pinDetails, ['title', 'venue'], getStringValue(pinDetails, ['location_name'], 'Unknown Venue')),
          'isLocationPin': !isVirtual,
          'isVirtual': isVirtual,
          'pinType': isVirtual ? 'virtual' : 'discovery',
          'dateCollected': collectionPin.addedAt,
          'duration': duration,
          'isExplicit': pinDetails['explicit'] == true || pinDetails['is_explicit'] == true,
          'popularity': 85,
          'coordinates': isVirtual ? null : {
            'lat': 40.7589,
            'lng': -73.9851,
          },
          'hasError': hasError, // Add this flag to indicate incomplete data
        };
      }).toList();
      
      // Debug logging to check for duplicate IDs
      if (kDebugMode) {
        final allIds = pins.map((pin) => pin['id'].toString()).toList();
        final uniqueIds = allIds.toSet();
        if (allIds.length != uniqueIds.length) {
          print('⚠️ [CollectionDetail] Found duplicate pin IDs!');
          print('   Total pins: ${allIds.length}');
          print('   Unique IDs: ${uniqueIds.length}');
          print('   IDs: $allIds');
        }
        // Removed repetitive success log
      }
      
      return pins;
    }
    
    // Fallback to mock data if no real pins
    final platforms = ['spotify', 'apple_music', 'youtube_music'];
    final venues = [
      'Central Park',
      'Times Square',
      'Brooklyn Bridge',
      'Golden Gate Bridge',
      'Venice Beach',
      'Hollywood Bowl',
      'Hyde Park',
      'Tower Bridge',
      'Shibuya Crossing',
      'Tokyo Tower',
      'Eiffel Tower',
      'Louvre Museum',
      'Sydney Opera House',
      'Bondi Beach',
      'CN Tower',
      'Queen Street',
      'Brandenburg Gate',
      'Museum Island'
    ];
    
    return List.generate(
      _collection.itemCount,
      (i) => {
        'id': 'pin_$i',
        'title': [
          'Blinding Lights',
          'As It Was',
          'Heat Waves',
          'Anti-Hero',
          'Flowers',
          'Unholy',
          'Bad Habit',
          'About Damn Time',
          'Running Up That Hill',
          'Music For a Sushi Restaurant'
        ][i % 10],
        'artist': [
          'The Weeknd',
          'Harry Styles', 
          'Glass Animals',
          'Taylor Swift',
          'Miley Cyrus',
          'Sam Smith ft. Kim Petras',
          'Steve Lacy',
          'Lizzo',
          'Kate Bush',
          'Harry Styles'
        ][i % 10],
        'album': [
          'After Hours',
          'Harry\'s House',
          'Dreamland',
          'Midnights',
          'Endless Summer Vacation',
          'Gloria',
          'Gemini Rights',
          'Special',
          'Hounds of Love',
          'Harry\'s House'
        ][i % 10],
        'platform': platforms[i % platforms.length],
        'track_url': [
          'https://open.spotify.com/track/0VjIjW4GlUZAMYd2vXMi3b',
          'https://open.spotify.com/track/4Dvkj6JhhA12EX05fT7y2e',
          'https://open.spotify.com/track/02MWAaffLxlfxAUY7c5dvx',
          'https://open.spotify.com/track/1BxfuPKGuaTgP7aM0Bbdwr',
          'https://open.spotify.com/track/1qi6NeYDtWoFgCsKaMBwLs',
          'https://open.spotify.com/track/3nqQXoyQOWXiESFLlDF1hG',
          'https://open.spotify.com/track/4k6Uh1HXdhtusDW5y8Gbvy',
          'https://open.spotify.com/track/2u4WuekZiSJqQwbqXkjqgZ',
          'https://open.spotify.com/track/75FEaRjZTKLhTrFGsfMUXR',
          'https://open.spotify.com/track/5xTtaWoae3wi06K5WfVUUH'
        ][i % 10],
        'imageUrl': [
          'https://i.scdn.co/image/ab67616d0000b273d1d6c90b97b9f8d6e5495df7',
          'https://i.scdn.co/image/ab67616d0000b273c5649add07ed3720be9d5526',
          'https://i.scdn.co/image/ab67616d0000b273a048415db06a5b6fa7ec4e1a',
          'https://i.scdn.co/image/ab67616d0000b273bb54dde68cd23e2a268ae0f5',
          'https://i.scdn.co/image/ab67616d0000b273f4b2e7b1b1b3b3b3b3b3b3b3',
        ][i % 5],
        'originUser': '@bopper${(i % 5) + 1}',
        'originAvatar': 'https://i.pravatar.cc/150?img=${(i % 10) + 1}',
        'originPlace': [
          'New York, NY',
          'Los Angeles, CA', 
          'London, UK',
          'Tokyo, JP',
          'Paris, FR',
          'Sydney, AU',
          'Toronto, CA',
          'Berlin, DE'
        ][(i % 8)],
        'venue': venues[i % venues.length],
        'isLocationPin': true, // All songs in collections are location-based pins
        'pinType': i % 3 == 0 ? 'discovery' : i % 3 == 1 ? 'memory' : 'recommendation',
        'dateCollected': DateTime.now().subtract(Duration(days: i * 2)),
        'duration': '${(i % 4 + 2)}:${(i % 60).toString().padLeft(2, '0')}',
        'isExplicit': i % 7 == 0,
        'popularity': 85 - (i * 3) % 40,
        'coordinates': {
          'lat': 40.7589 + (i * 0.1) % 2,
          'lng': -73.9851 + (i * 0.1) % 2,
        },
      },
    );
  }

  List<Map<String, dynamic>> get _filteredPins {
    var pins = _mockPins;
    
    // Apply sorting
    switch (_sortBy) {
      case 'Title':
        pins.sort((a, b) => a['title'].compareTo(b['title']));
        break;
      case 'Artist':
        pins.sort((a, b) => a['artist'].compareTo(b['artist']));
        break;
      case 'Popularity':
        pins.sort((a, b) => b['popularity'].compareTo(a['popularity']));
        break;
      case 'Recent':
      default:
        pins.sort((a, b) => b['dateCollected'].compareTo(a['dateCollected']));
    }
    
    return pins;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final theme = Theme.of(context);
    final primaryColor = _collection.primaryColor ?? theme.colorScheme.primary;
    final isSmallScreen = size.width < 360;
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context);
    
    final hasActiveMusic = (spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null) ||
                          (appleMusicProvider.hasActivePlayback && appleMusicProvider.currentTrack != null);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: CustomScrollView(
        slivers: [
          // Modern app bar with collection info
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            stretch: true,
            backgroundColor: primaryColor,
            foregroundColor: Colors.white,
            elevation: 0,
            leading: Container(
              margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  ),
                child: IconButton(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onPressed: () => _showOptionsMenu(),
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: AnimatedBuilder(
                animation: _headerAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(0, 50 * (1 - _headerAnimation.value)),
                    child: Opacity(
                      opacity: _headerAnimation.value,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                              colors: [
                              primaryColor,
                              primaryColor.withOpacity(0.8),
                              ],
                            ),
                          ),
                  child: SafeArea(
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
                    child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // Collection cover
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Hero(
                                      tag: 'collection_${_collection.id}',
                                      child: _buildModernCoverStack(),
                                    ),
                                    const SizedBox(width: 20),
                                    Expanded(
                                      child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                                          Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                            decoration: BoxDecoration(
                                              color: Colors.white.withOpacity(0.2),
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                            child: Text(
                                              'COLLECTION',
                                              style: TextStyle(
                                                color: Colors.white.withOpacity(0.9),
                                                fontSize: 11,
                                                fontWeight: FontWeight.w600,
                                                letterSpacing: 1,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                        Text(
                          TextEncodingUtils.cleanCollectionText(_collection.name, isDescription: false),
                                            style: const TextStyle(
                            color: Colors.white,
                                              fontSize: 28,
                            fontWeight: FontWeight.bold,
                                              height: 1.1,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                                          const SizedBox(height: 8),
                                          _buildCollectionStats(),
                                        ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                      ),
                        ),
                  );
                },
                      ),
                    ),
                  ),
          
          // Action buttons
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: _isEditMode ? _buildEditModeActions() : _buildNormalModeActions(),
            ),
          ),
          
          // Sort controls
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: theme.dividerColor.withOpacity(0.5)),
              ),
              child: Row(
                children: [
                  Icon(Icons.sort, size: 18, color: theme.colorScheme.onSurface.withOpacity(0.7)),
                  const SizedBox(width: 6),
                  Text(
                    'Sort by',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                      fontSize: 13,
                    ),
                  ),
                  const Spacer(),
                  _buildFilterChip(
                    'Sort by',
                    _sortBy,
                    ['Recent', 'Title', 'Artist', 'Popularity'],
                    (value) => setState(() => _sortBy = value),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${_filteredPins.length} songs',
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SliverToBoxAdapter(child: SizedBox(height: 20)),
          
          // Songs list
          AnimatedBuilder(
            animation: _listAnimation,
            builder: (context, child) {
              return SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                    if (_isLoading) {
                      return _buildShimmerSongTile();
                    }
                    
                    final pins = _filteredPins;
                    if (pins.isEmpty) {
                      return _buildEmptyState();
                    }
                    
                    final pin = pins[index];
                    return Transform.translate(
                      offset: Offset(0, 30 * (1 - _listAnimation.value)),
                      child: Opacity(
                        opacity: _listAnimation.value,
                        child: _buildModernSongTile(pin, index),
                      ),
                    );
                  },
                  childCount: _isLoading ? 8 : _filteredPins.isEmpty ? 1 : _filteredPins.length,
                ),
              );
            },
          ),
          
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _addSongToCollection(),
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 4,
        child: const Icon(Icons.add),
      ),
    );
  }
  
  Widget _buildModernCoverStack() {
    final coverUrls = _collection.coverImageUrls;
    const size = 120.0;
    final primaryColor = _collection.primaryColor ?? Theme.of(context).colorScheme.primary;
    
    if (coverUrls.isEmpty) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.25),
              Colors.white.withOpacity(0.15),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.4),
              blurRadius: 25,
              offset: const Offset(0, 12),
            ),
            BoxShadow(
              color: primaryColor.withOpacity(0.3),
              blurRadius: 40,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Stack(
          children: [
            Center(
        child: Icon(
                Icons.collections_outlined,
                size: size * 0.35,
          color: Colors.white.withOpacity(0.9),
              ),
            ),
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.location_on,
                  size: 16,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
            ),
          ],
        ),
      );
    }
    
    if (coverUrls.length == 1) {
      return Stack(
        children: [
          Container(
            width: size,
            height: size,
        decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 2,
              ),
          boxShadow: [
            BoxShadow(
                  color: Colors.black.withOpacity(0.4),
                  blurRadius: 25,
                  offset: const Offset(0, 12),
                ),
                BoxShadow(
                  color: primaryColor.withOpacity(0.3),
                  blurRadius: 40,
                  offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
              borderRadius: BorderRadius.circular(18),
          child: Image.network(
            coverUrls.first,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withOpacity(0.25),
                        Colors.white.withOpacity(0.15),
                      ],
                    ),
                  ),
                  child: Icon(Icons.image_not_supported, color: Colors.white.withOpacity(0.8)),
              ),
            ),
            ),
          ),
            Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
            ),
              child: Icon(
                Icons.location_on,
                size: 14,
                color: Colors.white.withOpacity(0.9),
          ),
            ),
      ),
        ],
    );
  }
  
    // Multi-image mosaic for collections with multiple covers
    return Stack(
      children: [
        Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 2,
            ),
        boxShadow: [
          BoxShadow(
                color: Colors.black.withOpacity(0.4),
                blurRadius: 25,
                offset: const Offset(0, 12),
              ),
              BoxShadow(
                color: primaryColor.withOpacity(0.3),
                blurRadius: 40,
                offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
            borderRadius: BorderRadius.circular(18),
            child: coverUrls.length >= 4 ? _buildMosaic4(coverUrls) : _buildMosaic2(coverUrls),
          ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.location_on,
              size: 14,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMosaic4(List<String> urls) {
    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              Expanded(child: _buildMosaicImage(urls[0])),
              Expanded(child: _buildMosaicImage(urls[1])),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Expanded(child: _buildMosaicImage(urls[2])),
              Expanded(child: _buildMosaicImage(urls.length > 3 ? urls[3] : urls[0])),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMosaic2(List<String> urls) {
    return Row(
      children: [
        Expanded(child: _buildMosaicImage(urls[0])),
        if (urls.length > 1) Expanded(child: _buildMosaicImage(urls[1])),
      ],
    );
  }

  Widget _buildMosaicImage(String url) {
    // Check if it's a Cloudinary URL to extract public ID
    String? publicId;
    if (url.contains('res.cloudinary.com')) {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      if (pathSegments.length >= 6) {
        // Extract public ID from Cloudinary URL
        publicId = pathSegments.sublist(6).join('/').replaceAll(RegExp(r'\.[^.]*$'), '');
      }
    }

    return publicId != null
        ? CloudinaryImage(
            publicId: publicId,
            quality: 85,
            format: 'webp',
            fit: BoxFit.cover,
            errorWidget: Container(
              color: Colors.white.withOpacity(0.2),
              child: Icon(Icons.image_not_supported, color: Colors.white.withOpacity(0.8)),
            ),
          )
        : Image.network(
            url,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
              color: Colors.white.withOpacity(0.2),
              child: Icon(Icons.image_not_supported, color: Colors.white.withOpacity(0.8)),
            ),
          );
  }

  Widget _buildCollectionStats() {
    final totalPins = _filteredPins.length;
    final platforms = _mockPins.map((p) => p['platform']).toSet().length;
    
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: [
        _buildStatItem(Icons.library_music, '$totalPins songs'),
        _buildStatItem(Icons.apps, '$platforms platforms'),
        if (_collection.isPublic) 
          _buildStatItem(Icons.public, 'Public')
        else
          _buildStatItem(Icons.lock, 'Private'),
      ],
    );
  }

  Widget _buildStatItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.white.withOpacity(0.8)),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 13,
            fontWeight: FontWeight.w500,
        ),
      ),
      ],
    );
  }
  
  Widget _buildFilterChip(String label, String currentValue, List<String> options, Function(String) onChanged) {
    return PopupMenuButton<String>(
      onSelected: onChanged,
      itemBuilder: (context) => options.map((option) {
        return PopupMenuItem<String>(
          value: option,
          child: Row(
            children: [
              if (option == currentValue) 
                Icon(Icons.check, size: 16, color: Theme.of(context).colorScheme.primary),
              if (option == currentValue) const SizedBox(width: 6),
              Text(_formatPlatformName(option)),
            ],
          ),
        );
      }).toList(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _formatPlatformName(currentValue),
              style: const TextStyle(fontSize: 12),
            ),
            const SizedBox(width: 2),
            Icon(Icons.arrow_drop_down, size: 16),
          ],
        ),
      ),
    );
  }

  String _formatPlatformName(String platform) {
    switch (platform) {
      case 'spotify': return 'Spotify';
      case 'apple_music': return 'Apple Music';
      case 'youtube_music': return 'YouTube Music';
      default: return platform;
    }
  }

  String _formatPinType(String pinType) {
    switch (pinType) {
      case 'discovery': return 'DISCOVERY';
      case 'memory': return 'MEMORY';
      case 'recommendation': return 'RECOMMEND';
      case 'virtual': return 'VIRTUAL';
      default: return pinType.toUpperCase();
    }
  }

  Color _getPinTypeColor(String pinType) {
    final theme = Theme.of(context);
    switch (pinType) {
      case 'discovery': return Colors.green;
      case 'memory': return Colors.blue;
      case 'recommendation': return Colors.orange;
      case 'virtual': return Colors.purple;
      default: return theme.colorScheme.primary;
    }
  }

  Widget _buildModernSongTile(Map<String, dynamic> pin, int index) {
    final theme = Theme.of(context);
    final primaryColor = _collection.primaryColor ?? theme.colorScheme.primary;
    final pinId = pin['id'].toString();
    final isSelected = _selectedPins.contains(pinId);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      decoration: BoxDecoration(
        color: _isEditMode && isSelected 
          ? primaryColor.withOpacity(0.1) 
          : theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _isEditMode && isSelected 
            ? primaryColor.withOpacity(0.5)
            : theme.dividerColor.withOpacity(0.3),
          width: _isEditMode && isSelected ? 2 : 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: _isEditMode ? _buildSelectionCheckbox(pinId, isSelected) : _buildAlbumArt(pin, primaryColor),
        title: Row(
          children: [
            Expanded(
              child: Text(
                pin['title'],
                style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (pin['hasError'] == true)
              Container(
                margin: const EdgeInsets.only(left: 8),
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.orange.withOpacity(0.5), width: 1),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      size: 12,
                      color: Colors.orange.shade700,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      'Incomplete',
                      style: TextStyle(
                        fontSize: 9,
                        fontWeight: FontWeight.w600,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            if (pin['isExplicit'] && pin['hasError'] != true)
              Container(
                margin: const EdgeInsets.only(left: 8),
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'E',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              pin['artist'],
              style: TextStyle(
                fontSize: 13,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  pin['isVirtual'] == true ? Icons.music_note : Icons.location_on,
                  size: 12,
                  color: theme.colorScheme.primary.withOpacity(0.7),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    pin['isVirtual'] == true 
                      ? 'Virtual Pin • ${pin['originPlace']}'
                      : pin['originPlace'] != 'Unknown Location' 
                        ? pin['originPlace']
                        : '${pin['venue']} • ${pin['originPlace']}',
                    style: TextStyle(
                      fontSize: 11,
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            Row(
              children: [
                CircleAvatar(
                  radius: 8,
                  backgroundImage: CachedNetworkImageProvider(pin['originAvatar']),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    '${pin['originUser']}',
                    style: TextStyle(
                      fontSize: 10,
                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getPinTypeColor(pin['pinType']).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _formatPinType(pin['pinType']),
                    style: TextStyle(
                      fontSize: 9,
                      color: _getPinTypeColor(pin['pinType']),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _formatDate(pin['dateCollected']),
                  style: TextStyle(
                    fontSize: 10,
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: _isEditMode ? null : Text(
          pin['duration'],
          style: TextStyle(
            fontSize: 12,
            color: theme.colorScheme.onSurface.withOpacity(0.5),
          ),
        ),
        onTap: _isEditMode ? () => _selectPin(pinId) : () => _playSongFromCollection(pin, index),
      ),
    );
  }

  Widget _buildSelectionCheckbox(String pinId, bool isSelected) {
    final theme = Theme.of(context);
    final primaryColor = _collection.primaryColor ?? theme.colorScheme.primary;
    
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? primaryColor : theme.dividerColor,
          width: 2,
        ),
        color: isSelected ? primaryColor.withOpacity(0.1) : Colors.transparent,
      ),
      child: Center(
        child: isSelected
          ? Icon(Icons.check, color: primaryColor, size: 24)
          : Icon(Icons.circle_outlined, color: theme.dividerColor, size: 24),
      ),
    );
  }

  Widget _buildAlbumArt(Map<String, dynamic> pin, Color primaryColor) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Image.network(
            pin['imageUrl'],
            width: 56,
            height: 56,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
              width: 56,
              height: 56,
              color: primaryColor.withOpacity(0.1),
              child: Icon(Icons.music_note, color: primaryColor),
            ),
          ),
        ),
        Positioned(
          bottom: 2,
          right: 2,
          child: _buildPlatformBadge(pin['platform']),
        ),
      ],
    );
  }

  Widget _buildPlatformBadge(String platform) {
    Color badgeColor;
    IconData icon;
    
    switch (platform) {
      case 'spotify':
        badgeColor = const Color(0xFF1DB954);
        icon = Icons.music_note;
        break;
      case 'apple_music':
        badgeColor = const Color(0xFFFA243C);
        icon = Icons.music_note;
        break;
      case 'youtube_music':
        badgeColor = const Color(0xFFFF0000);
        icon = Icons.play_circle_fill;
        break;
      default:
        badgeColor = Colors.grey;
        icon = Icons.music_note;
    }
    
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
                ),
              ],
            ),
      child: Icon(icon, color: Colors.white, size: 12),
    );
  }

  Widget _buildShimmerSongTile() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
                children: [
          Container(
                          width: 56,
                          height: 56,
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                Container(
                  height: 16,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                            ),
                const SizedBox(height: 8),
                Container(
                  height: 14,
                  width: 150,
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
                    children: [
          Icon(
            Icons.library_music,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No songs match your filters',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your filters or add some songs to this collection',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: Text(_isEditMode ? 'Exit Edit Mode' : 'Edit Collection'),
              onTap: () {
                Navigator.pop(context);
                if (_isEditMode) {
                  _toggleEditMode();
                } else {
                  _editCollection();
                }
              },
            ),
            if (!_isEditMode)
              ListTile(
                leading: const Icon(Icons.edit_outlined),
                title: const Text('Edit Pins'),
                onTap: () {
                  Navigator.pop(context);
                  _toggleEditMode();
                },
              ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share Collection'),
              onTap: () {
                Navigator.pop(context);
                _shareCollection();
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete Collection', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmation();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _toggleEditMode() {
    setState(() {
      _isEditMode = !_isEditMode;
      if (!_isEditMode) {
        _selectedPins.clear();
      }
    });
  }

  void _selectPin(String pinId) {
    setState(() {
      if (_selectedPins.contains(pinId)) {
        _selectedPins.remove(pinId);
      } else {
        _selectedPins.add(pinId);
      }
    });
    
    // Debug logging to help understand selection issues
    if (kDebugMode) {
      print('🔘 Pin selection changed: $pinId');
      print('   Total selected: ${_selectedPins.length}');
      print('   Selected pins: ${_selectedPins.toList()}');
    }
  }

  void _selectAllPins() {
    setState(() {
      if (_selectedPins.length == _filteredPins.length) {
        _selectedPins.clear();
      } else {
        _selectedPins = _filteredPins.map((pin) => pin['id'].toString()).toSet();
      }
    });
  }

  Future<void> _deleteSelectedPins() async {
    if (_selectedPins.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Songs'),
        content: Text(
          'Are you sure you want to delete ${_selectedPins.length} song${_selectedPins.length == 1 ? '' : 's'} from this collection?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);

    try {
      // Prepare pins data for removal
      List<Map<String, dynamic>> pinsToRemove = [];
      
      for (final pinId in _selectedPins) {
        // Find the corresponding pin data in our filtered pins list
        final pinData = _filteredPins.firstWhere(
          (pin) => pin['id'].toString() == pinId,
          orElse: () => <String, dynamic>{},
        );
        
        if (pinData.isNotEmpty) {
          final isVirtual = pinData['isVirtual'] == true;
          final originalPinId = pinData['originalPinId'];
          
          if (isVirtual && originalPinId != null) {
            pinsToRemove.add({
              'isVirtual': true,
              'virtualPinId': originalPinId,
            });
          } else if (originalPinId != null) {
            pinsToRemove.add({
              'isVirtual': false,
              'pinId': originalPinId,
            });
          }
        }
      }
      
      // Remove pins using the batch method
      final deletedCount = await _collectionService.removePinsFromCollection(
        _collection.id,
        pinsToRemove,
      );
      
      if (deletedCount > 0) {
        // Reload the collection details to get the updated data
        await _loadCollectionDetails();
        
        // Emit collection updated event for real-time updates
        EventBus().emit(CollectionUpdatedEvent(_collection.id.toString()));
        
        setState(() {
          _selectedPins.clear();
          _isEditMode = false;
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.check, color: Colors.white, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Deleted $deletedCount song${deletedCount == 1 ? '' : 's'} from collection',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            margin: const EdgeInsets.all(16),
            duration: const Duration(seconds: 3),
          ),
        );
      } else {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to delete songs from collection'),
            backgroundColor: Colors.red.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error deleting songs: $e'),
          backgroundColor: Colors.red.shade600,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  Future<void> _playCollection() async {
    if (_filteredPins.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No songs to play')),
      );
      return;
    }

    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);

      // Convert pins to MusicTrack objects
      final tracks = _filteredPins.map((pin) => _convertPinToMusicTrack(pin)).where((track) => track != null).cast<MusicTrack>().toList();

      if (tracks.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No valid tracks found in collection'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Determine which service to use based on the collection content
      final appleMusicTracks = tracks.where((track) => track.service.toLowerCase().contains('apple')).length;
      final spotifyTracks = tracks.where((track) => track.service.toLowerCase().contains('spotify')).length;

      bool success = false;

      // Always try Apple Music first for any tracks (bypass connection check)
      try {
        if (kDebugMode) {
          print('🎵 [CollectionDetail] Attempting Apple Music playback for ${tracks.length} tracks');
        }

        final queueManager = appleMusicProvider.queueManager;
        success = await queueManager.setQueue(
          tracks: tracks,
          collectionType: 'collection',
          collectionId: _collection.id.toString(),
          collectionMetadata: {
            'name': _collection.name,
            'type': 'collection',
          },
          startIndex: 0,
        );

        if (kDebugMode) {
          print('🎵 [CollectionDetail] Apple Music queue result: success=$success');
        }
      } catch (e) {
        if (kDebugMode) {
          print('🎵 [CollectionDetail] Apple Music queue failed: $e');
        }
        success = false;
      }

      // Fallback to Spotify if Apple Music failed or wasn't preferred
      if (!success && spotifyProvider.isConnected) {
        success = await spotifyProvider.playMultipleTracks(tracks);

        if (kDebugMode) {
          print('🎵 [CollectionDetail] Set Spotify queue with ${tracks.length} tracks, success: $success');
        }
      }

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playing "${TextEncodingUtils.cleanCollectionText(_collection.name, isDescription: false)}" collection (${tracks.length} songs)'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to start collection playback - no music service available'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to play collection: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
  
  Future<void> _shuffleCollection() async {
    if (_filteredPins.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No songs to shuffle')),
      );
      return;
    }

    try {
      // Show shuffle options dialog
      final shuffleMode = await _showShuffleOptionsDialog();
      if (shuffleMode == null) return; // User cancelled
      
      // Show loading state
      final loadingSnackBar = ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
              const SizedBox(width: 12),
                              Text('Shuffling "${TextEncodingUtils.cleanCollectionText(_collection.name, isDescription: false)}" collection...'),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.primary,
          duration: const Duration(seconds: 10), // Longer duration for shuffle operation
        ),
      );
      
      // Get providers
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
      
      // Use the new shuffle service
      final result = await ShuffleService.shuffleCollection(
        pins: _filteredPins,
        spotifyProvider: spotifyProvider,
        appleMusicProvider: appleMusicProvider,
        mode: shuffleMode,
        clearExistingQueue: true,
        maxTracks: 50, // Reasonable limit
      );
      
      // Clear loading snackbar
      ScaffoldMessenger.of(context).clearSnackBars();
      
      // Show result
      if (result.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.shuffle_rounded,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(child: Text(result.message)),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Theme.of(context).colorScheme.onError,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(child: Text(result.message)),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      // Clear any existing snackbars
      ScaffoldMessenger.of(context).clearSnackBars();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to shuffle collection: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
  
  /// Show dialog to select shuffle mode
  Future<ShuffleMode?> _showShuffleOptionsDialog() async {
    final theme = Theme.of(context);
    final primaryColor = _collection.primaryColor ?? theme.colorScheme.primary;
    
    return showModalBottomSheet<ShuffleMode>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.75,
          decoration: BoxDecoration(
            color: theme.scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: theme.dividerColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Header section
              Container(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
                child: Column(
                  children: [
                    // Icon and title
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: primaryColor.withOpacity(0.15),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.shuffle_rounded,
                            color: primaryColor,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Choose Shuffle Style',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                              Text(
                                '${_filteredPins.length} songs ready',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Options list
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    children: [
                      _buildEnhancedShuffleOption(
                        title: 'Random Shuffle',
                        subtitle: 'Classic random order - completely unpredictable',
                        description: 'Every song has an equal chance of being played next',
                        icon: Icons.casino_rounded,
                        gradient: LinearGradient(colors: [Colors.purple.shade400, Colors.purple.shade600]),
                        mode: ShuffleMode.random,
                        theme: theme,
                        primaryColor: primaryColor,
                      ),
                      const SizedBox(height: 8),
                      
                      _buildEnhancedShuffleOption(
                        title: 'Smart Shuffle',
                        subtitle: 'Popular songs appear earlier in the mix',
                        description: 'Balances discovery with your favorites',
                        icon: Icons.trending_up_rounded,
                        gradient: LinearGradient(colors: [Colors.orange.shade400, Colors.red.shade500]),
                        mode: ShuffleMode.weighted,
                        theme: theme,
                        primaryColor: primaryColor,
                        isRecommended: true,
                      ),
                      const SizedBox(height: 8),
                      

                      
                      _buildEnhancedShuffleOption(
                        title: 'Artist Spaced',
                        subtitle: 'Avoids back-to-back songs by the same artist',
                        description: 'Creates a more diverse listening experience',
                        icon: Icons.people_outline_rounded,
                        gradient: LinearGradient(colors: [Colors.blue.shade400, Colors.indigo.shade500]),
                        mode: ShuffleMode.artistSpaced,
                        theme: theme,
                        primaryColor: primaryColor,
                      ),
                    ],
                  ),
                ),
              ),
              
              // Cancel button
              Container(
                padding: const EdgeInsets.fromLTRB(24, 12, 24, 20),
                child: SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(color: theme.dividerColor),
                      ),
                    ),
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  /// Build an enhanced shuffle option tile with beautiful design
  Widget _buildEnhancedShuffleOption({
    required String title,
    required String subtitle,
    required String description,
    required IconData icon,
    required Gradient gradient,
    required ShuffleMode mode,
    required ThemeData theme,
    required Color primaryColor,
    bool isRecommended = false,
  }) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(mode),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isRecommended 
                ? primaryColor.withOpacity(0.3) 
                : theme.dividerColor.withOpacity(0.5),
            width: isRecommended ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isRecommended 
                  ? primaryColor.withOpacity(0.1)
                  : Colors.black.withOpacity(0.05),
              blurRadius: isRecommended ? 8 : 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Recommended badge
            if (isRecommended)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: primaryColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'REC',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Icon with gradient background
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: gradient,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: gradient.colors.first.withOpacity(0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Title only
                  Expanded(
                    child: Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                  
                  // Arrow
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.4),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _shareCollection() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sharing collection...')),
    );
  }

  // Helper method to check if a track already exists in the collection
  bool _isTrackDuplicate(MusicTrack track) {
    if (_collection.pins == null || _collection.pins!.isEmpty) return false;
    
    return _collection.pins!.any((collectionPin) {
      final pinDetails = collectionPin.pinDetails;
      if (pinDetails == null) return false;
      
      // Check if track URL matches
      if (track.url.isNotEmpty && 
          (pinDetails['track_url'] == track.url || pinDetails['url'] == track.url)) {
        return true;
      }
      
      // If no URL match, check title and artist combination
      final pinTitle = pinDetails['track_title'] ?? pinDetails['title'] ?? '';
      final pinArtist = pinDetails['track_artist'] ?? pinDetails['artist'] ?? '';
      
      return pinTitle.toString().toLowerCase() == track.title.toLowerCase() &&
             pinArtist.toString().toLowerCase() == track.artist.toLowerCase();
    });
  }

  void _addSongToCollection() {
    if (_collection.isPublic) {
      // For public collections, show track selector then AR placement
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => DraggableScrollableSheet(
          initialChildSize: 0.9,
          maxChildSize: 0.9,
          minChildSize: 0.5,
          builder: (context, scrollController) => Container(
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: TrackSelector(
              onTrackSelected: (track) {
                // Check for duplicates before proceeding
                if (_isTrackDuplicate(track)) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${track.title} is already in this collection'),
                      backgroundColor: Colors.orange,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                  return;
                }
                
                Navigator.pop(context); // Close track selector
                // Open AR placement screen
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    fullscreenDialog: true,
                    builder: (context) => ARPinPlacementScreen(
                      selectedTrack: track,
                      initialLatitude: Provider.of<MapProvider>(context, listen: false).currentPosition?.latitude,
                      initialLongitude: Provider.of<MapProvider>(context, listen: false).currentPosition?.longitude,
                    ),
                  ),
                ).then((success) async {
                  if (success == true) {
                    // Add to collection after successful AR placement
                    final apiService = ApiService();
                    final collectionService = CollectionService(
                      apiService,
                      AuthService(apiService),
                    );
                    
                    try {
                      final mapProvider = Provider.of<MapProvider>(context, listen: false);
                      final currentPos = mapProvider.currentPosition;
                      
                      if (currentPos != null) {
                        final locationName = await _getLocationName(currentPos.latitude, currentPos.longitude);
                        
                        final success = await collectionService.addTrackToCollection(
                          collectionId: _collection.id,
                          trackTitle: track.title,
                          trackArtist: track.artist,
                          trackUrl: track.url,
                          service: track.service,
                          latitude: currentPos.latitude,
                          longitude: currentPos.longitude,
                          album: track.album,
                          artworkUrl: track.albumArt,
                          durationMs: track.durationMs ?? 0,
                          description: 'Added from $locationName',
                          locationName: locationName,
                        );
                        
                        if (success) {
                          EventBus().emit(CollectionUpdatedEvent(_collection.id.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Added "${track.title}" to collection'),
                              backgroundColor: Colors.green,
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      }
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Failed to add track: $e'),
                          backgroundColor: Colors.red,
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    }
                  }
                });
              },
              showSearchBar: true,
              showRecentlyPlayed: true,
              showTopTracks: true,
              showLikedSongs: true,
              showPlaylists: true,
            ),
          ),
        ),
      );
    } else {
      // For private collections, show track selector directly
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => DraggableScrollableSheet(
          initialChildSize: 0.9,
          maxChildSize: 0.9,
          minChildSize: 0.5,
          builder: (context, scrollController) => Container(
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: TrackSelector(
              onTrackSelected: (track) async {
                // Check for duplicates before proceeding
                if (_isTrackDuplicate(track)) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${track.title} is already in this collection'),
                      backgroundColor: Colors.orange,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                  return;
                }
                
                Navigator.pop(context); // Close track selector
                
                // Add virtual pin directly to private collection
                final apiService = ApiService();
                final collectionService = CollectionService(
                  apiService,
                  AuthService(apiService),
                );
                
                try {
                  // Try to get location name for context
                  String description = 'Added to collection';
                  String? locationName;
                  try {
                    final mapProvider = Provider.of<MapProvider>(context, listen: false);
                    final currentPos = mapProvider.currentPosition;
                    if (currentPos != null) {
                      locationName = await _getLocationName(currentPos.latitude, currentPos.longitude);
                      description = 'Added from $locationName';
                    }
                  } catch (e) {
                    debugPrint('Could not get location for virtual pin description: $e');
                  }

                  final result = await collectionService.addVirtualTrackToCollectionWithDetails(
                    collectionId: _collection.id,
                    trackTitle: track.title,
                    trackArtist: track.artist,
                    trackUrl: track.url,
                    service: track.service,
                    album: track.album,
                    artworkUrl: track.albumArt,
                    durationMs: track.durationMs ?? 0,
                    description: description,
                    locationName: locationName,
                  );

                  final success = result['success'] == true;
                  String message;
                  Color backgroundColor;

                  if (success) {
                    message = 'Added "${track.title}" to collection (virtual pin)';
                    backgroundColor = Colors.green;
                    EventBus().emit(CollectionUpdatedEvent(_collection.id.toString()));
                  } else {
                    // Handle specific error cases
                    final statusCode = result['statusCode'];
                    if (statusCode == 409) {
                      message = 'This track is already in the collection';
                      backgroundColor = Colors.orange;
                    } else {
                      message = result['message'] ?? 'Failed to add track to collection';
                      backgroundColor = Colors.red;
                    }
                  }

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(message),
                      backgroundColor: backgroundColor,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to add track: $e'),
                      backgroundColor: Colors.red,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                }
              },
              showSearchBar: true,
              showRecentlyPlayed: true,
              showTopTracks: true,
              showLikedSongs: true,
              showPlaylists: true,
            ),
          ),
        ),
      );
    }
  }

  Future<String> _getLocationName(double latitude, double longitude) async {
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        final place = placemarks.first;
        final components = [
          place.name,
          place.locality,
          place.administrativeArea,
        ].where((e) => e != null && e.isNotEmpty).toList();
        return components.join(', ');
      }
    } catch (e) {
      debugPrint('Error getting location name: $e');
    }
    return 'Unknown Location';
  }

  /// Play a song from the collection, setting up the entire collection as a queue
  Future<void> _playSongFromCollection(Map<String, dynamic> pin, int startIndex) async {
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);

    try {
      // Convert all pins to MusicTrack objects
      final tracks = _filteredPins.map((pin) => _convertPinToMusicTrack(pin)).where((track) => track != null).cast<MusicTrack>().toList();

      if (tracks.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No valid tracks found in collection'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Ensure start index is valid
      final validStartIndex = startIndex.clamp(0, tracks.length - 1);

      // Determine which service to use based on the selected track
      final selectedTrack = tracks[validStartIndex];
      final platform = selectedTrack.service.toLowerCase();

      bool success = false;

      // Always try Apple Music first (bypass connection check)
      try {
        if (kDebugMode) {
          print('🎵 [CollectionDetail] Attempting Apple Music queue for track: ${selectedTrack.title}');
        }

        final queueManager = appleMusicProvider.queueManager;
        success = await queueManager.setQueue(
          tracks: tracks,
          collectionType: 'collection',
          collectionId: _collection.id.toString(),
          collectionMetadata: {
            'name': _collection.name,
            'type': 'collection',
          },
          startIndex: validStartIndex,
        );

        if (kDebugMode) {
          print('🎵 [CollectionDetail] Apple Music queue result: success=$success, startIndex=$validStartIndex');
        }
      } catch (e) {
        if (kDebugMode) {
          print('🎵 [CollectionDetail] Apple Music queue failed: $e');
        }
        success = false;
      }

      // Fallback to Spotify if Apple Music failed or wasn't preferred
      if (!success && spotifyProvider.isConnected) {
        // Use Spotify - for now, play all tracks from the beginning
        // TODO: Add support for starting at a specific index in Spotify
        success = await spotifyProvider.playMultipleTracks(tracks);

        if (kDebugMode) {
          print('🎵 [CollectionDetail] Set Spotify queue with ${tracks.length} tracks, success: $success');
        }
      }

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playing "${selectedTrack.title}" from collection (${tracks.length} songs)'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else {
        // Fallback to single track playback
        await _playSong(pin);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [CollectionDetail] Error playing from collection: $e');
      }
      // Fallback to single track playback
      await _playSong(pin);
    }
  }

  Future<void> _playSong(Map<String, dynamic> pin) async {
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
    final pinProvider = Provider.of<PinProvider>(context, listen: false);
    
    // Create a MusicTrack object from the pin data
    final constructedUri = _constructTrackUri(pin);
    
    // Extract the actual track ID from the URI for proper MusicKit playback
    String trackId = pin['id'].toString();
    final platform = pin['platform'].toString().toLowerCase();
    
    if (platform.contains('apple') && constructedUri.startsWith('apple:track:')) {
      // Use the extracted Apple Music track ID
      trackId = constructedUri.split(':').last;
    } else if (platform.contains('spotify') && constructedUri.startsWith('spotify:track:')) {
      // Use the extracted Spotify track ID
      trackId = constructedUri.split(':').last;
    }
    
    // Debug logging to help troubleshoot URI construction
    if (kDebugMode) {
      print('🎵 [CollectionDetail] Constructing track for playback:');
      print('   Title: ${pin['title']}');
      print('   Platform: ${pin['platform']}');
      print('   Original URL/ID: ${pin['track_url'] ?? pin['url'] ?? pin['id']}');
      print('   Constructed URI: $constructedUri');
      print('   Extracted Track ID: $trackId');
    }
    
    // Set currently playing pin if this is a pin (not just a track)
    final pinId = pin['pin_id']?.toString() ?? pin['id']?.toString();
    if (pinId != null && pin.containsKey('pin_id')) {
      // This is a pin, so record interactions
      final pinData = {
        'id': pinId,
        'location': {
          'type': 'Point',
          'coordinates': [pin['longitude'] ?? 0.0, pin['latitude'] ?? 0.0] // [lng, lat]
        },
        'latitude': pin['latitude'] ?? 0.0,
        'longitude': pin['longitude'] ?? 0.0,
        'title': pin['title'],
        'track_title': pin['title'],
        'track_artist': pin['artist'],
        'caption': pin['caption'] ?? '',
        'description': pin['description'] ?? '',
        'service': pin['platform'],
        'owner': {
          'username': pin['owner_username'] ?? 'Unknown',
          'profile_pic': pin['owner_profile_pic'],
        },
        'aura_radius': pin['aura_radius'] ?? 50.0,
        'created_at': pin['created_at'] ?? DateTime.now().toIso8601String(),
        'interaction_count': {
          'view': pin['interaction_count']?['view'] ?? 0,
          'like': pin['interaction_count']?['like'] ?? 0,
          'collect': pin['interaction_count']?['collect'] ?? 0,
          'share': pin['interaction_count']?['share'] ?? 0,
        },
        'rarity': pin['rarity'] ?? 'common',
        'skin': pin['skin'] ?? 1,
        'skin_details': pin['skin_details'],
        'track_url': pin['track_url'] ?? pin['url'] ?? '',
        'is_private': pin['is_private'] ?? false,
        'artwork_url': pin['imageUrl'],
        'album': pin['album'] ?? '',
        'duration_ms': pin['duration'] != null ? _parseDurationMs(pin['duration']) : 0,
        'upvote_count': pin['upvote_count'] ?? 0,
        'downvote_count': pin['downvote_count'] ?? 0,
      };
      
      pinProvider.setCurrentlyPlayingPin(pinId, pinData);
    }
    
    final track = MusicTrack(
      id: trackId, // Use the extracted track ID instead of the collection pin ID
      title: pin['title'],
      artist: pin['artist'],
      album: pin['album'] ?? '',
      albumArt: pin['imageUrl'],
      url: pin['track_url'] ?? pin['url'] ?? '', // Use the original URL
      service: pin['platform'],
      serviceType: pin['platform'],
      genres: [],
      durationMs: pin['duration'] != null ? _parseDurationMs(pin['duration']) : 0,
      popularity: pin['popularity'] ?? 50,
      uri: constructedUri,
      isLibrary: false, // Collection tracks are typically catalog tracks, not library tracks
    );

    try {
      bool success = false;
      
      // Determine which service to use based on track platform and provider availability
      final platform = pin['platform'].toString().toLowerCase();
      
      if (platform.contains('spotify') && spotifyProvider.isConnected) {
        // Use Spotify with automatic Apple Music fallback
        success = await spotifyProvider.playTrack(track, context: context);
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Playing "${pin['title']}"'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else if (platform.contains('apple') && appleMusicProvider.isConnected) {
        // Use Apple Music
        success = await appleMusicProvider.playTrack(track);
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Playing "${pin['title']}" on Apple Music'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        // Fallback: try Spotify with automatic Apple Music fallback
        success = await spotifyProvider.playTrack(track, context: context);
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Playing "${pin['title']}"'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
      
      if (!success) {
        // Clear the pin data if playback failed
        if (pinId != null && pin.containsKey('pin_id')) {
          pinProvider.clearCurrentlyPlayingPin();
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to play "${pin['title']}" - no music service available'),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      // Clear the pin data on error
      if (pinId != null && pin.containsKey('pin_id')) {
        pinProvider.clearCurrentlyPlayingPin();
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error playing "${pin['title']}": $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  // Helper method to parse duration from string format (e.g. "3:20") to milliseconds
  int _parseDurationMs(String duration) {
    try {
      final parts = duration.split(':');
      if (parts.length == 2) {
        final minutes = int.parse(parts[0]);
        final seconds = int.parse(parts[1]);
        return (minutes * 60 + seconds) * 1000;
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return 0;
  }

  // Helper method to construct track URI based on platform
  String _constructTrackUri(Map<String, dynamic> pin) {
    final platform = pin['platform'].toString().toLowerCase();
    
    // Try to get track URL from various fields in the pin data
    String? trackUrl;
    if (pin.containsKey('track_url') && pin['track_url'] != null) {
      trackUrl = pin['track_url'].toString();
    } else if (pin.containsKey('trackUrl') && pin['trackUrl'] != null) {
      trackUrl = pin['trackUrl'].toString();
    } else if (pin.containsKey('url') && pin['url'] != null) {
      trackUrl = pin['url'].toString();
    }
    
    if (platform.contains('spotify')) {
      // For Spotify, convert URL to URI format
      if (trackUrl != null && trackUrl.isNotEmpty) {
        if (trackUrl.startsWith('spotify:track:')) {
          // Already in URI format
          return trackUrl;
        } else if (trackUrl.contains('spotify.com/track/')) {
          // Convert from URL to URI: https://open.spotify.com/track/7LFe3zbZmZ8dEDBd6oIKkl -> spotify:track:7LFe3zbZmZ8dEDBd6oIKkl
          final id = trackUrl.split('/track/').last.split('?').first;
          return 'spotify:track:$id';
        }
      }
      
      // Fallback: try to use the pin ID directly
      final trackId = pin['id'].toString();
      if (trackId.startsWith('spotify:track:')) {
        return trackId;
      } else if (trackId.contains('spotify.com/track/')) {
        final id = trackId.split('/track/').last.split('?').first;
        return 'spotify:track:$id';
      } else {
        // Assume the ID is a Spotify track ID
        return 'spotify:track:$trackId';
      }
    } else if (platform.contains('apple')) {
      // For Apple Music, extract track ID from URL if possible
      if (trackUrl != null && trackUrl.isNotEmpty) {
        // Handle Apple Music URLs with track parameter: https://music.apple.com/us/album/name/albumId?i=trackId
        if (trackUrl.contains('music.apple.com') && trackUrl.contains('?i=')) {
          final trackId = trackUrl.split('?i=').last.split('&').first;
          if (trackId.isNotEmpty && trackId != trackUrl) {
            if (kDebugMode) {
              print('🍎 [CollectionDetail] Extracted Apple Music track ID: $trackId from URL: $trackUrl');
            }
            return 'apple:track:$trackId';
          }
        }
        // Handle direct Apple Music song URLs: https://music.apple.com/us/song/name/trackId
        else if (trackUrl.contains('music.apple.com/') && trackUrl.contains('/song/')) {
          final parts = trackUrl.split('/');
          if (parts.length > 2) {
            final trackId = parts.last.split('?').first;
            if (trackId.isNotEmpty && trackId != trackUrl) {
              if (kDebugMode) {
                print('🍎 [CollectionDetail] Extracted Apple Music track ID: $trackId from song URL: $trackUrl');
              }
              return 'apple:track:$trackId';
            }
          }
        }
        // For other Apple Music URLs, return as-is for now
        if (kDebugMode) {
          print('🍎 [CollectionDetail] Using Apple Music URL as-is: $trackUrl');
        }
        return trackUrl;
      }
      
      // Fallback: construct from pin ID (but this is likely incorrect)
      final pinId = pin['id'].toString();
      if (kDebugMode) {
        print('⚠️ [CollectionDetail] No valid Apple Music URL, using pin ID as fallback: $pinId');
      }
      return 'apple:track:$pinId';
    } else {
      // Generic fallback
      return trackUrl ?? pin['id'].toString();
    }
  }

  void _handleSongAction(String action, Map<String, dynamic> pin) {
    switch (action) {
      case 'play':
        _playSong(pin);
        break;
      case 'add_queue':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Added ${pin['title']} to queue')),
        );
        break;
      case 'details':
        // TODO: Show song details
        break;
      case 'remove':
        // TODO: Remove from collection
        break;
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Collection'),
                    content: Text('Are you sure you want to delete "${TextEncodingUtils.cleanCollectionText(_collection.name, isDescription: false)}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteCollection();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteCollection() async {
    // Set flag to prevent EventBus listener from trying to reload deleted collection
    setState(() {
      _isDeletingCollection = true;
    });
    
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      final success = await _collectionService.deleteCollection(_collection.id);
      
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        
        if (success) {
          // Emit collection updated event for real-time updates
          if (kDebugMode) {
            print('🗑️ [CollectionDetail] Emitting CollectionUpdatedEvent for deleted collection: ${_collection.id}');
          }
          EventBus().emit(CollectionUpdatedEvent(_collection.id.toString()));
          
          // Navigate back to collections list first
          Navigator.pop(context);
          
          // Show success message on the collections tab context
          // Use a delay to ensure the navigation is complete
          Future.delayed(const Duration(milliseconds: 100), () {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(Icons.check, color: Colors.white, size: 20),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Text(
                              'Collection Deleted',
                              style: TextStyle(fontWeight: FontWeight.w600),
                            ),
                            Text(
                              '${TextEncodingUtils.cleanCollectionText(_collection.name, isDescription: false)} has been deleted',
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  backgroundColor: Colors.green.shade600,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  margin: const EdgeInsets.all(16),
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Failed to delete collection'),
              backgroundColor: Colors.red.shade600,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting collection: $e'),
            backgroundColor: Colors.red.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    }
  }

  Future<void> _editCollection() async {
    // Show edit collection dialog
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _EditCollectionDialog(collection: _collection),
    );
    
    if (result != null) {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const _LoadingDialog(),
      );

      try {
        // Handle cover image upload/deletion
        List<String>? newCoverImageUrls;
        
        if (result.containsKey('newImageFile') && result['newImageFile'] != null) {
          // New image was selected - upload it
          try {
            final cloudinaryService = CloudinaryService();
            final uploadResult = await cloudinaryService.uploadImage(
              file: result['newImageFile'] as File,
              folder: 'collections',
              tags: ['collection_cover', 'user_upload'],
              quality: 85,
            );
            newCoverImageUrls = [uploadResult.secureUrl];
            
            // Delete old image from Cloudinary if it exists
            if (_collection.coverImageUrls.isNotEmpty) {
              for (final oldImageUrl in _collection.coverImageUrls) {
                await _deleteOldCloudinaryImage(oldImageUrl);
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('Failed to upload new cover image: $e');
            }
            // Close loading dialog before showing error
            if (mounted) Navigator.pop(context);
            
            // Show error but continue with update
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Failed to upload new cover image. Other changes will be saved.'),
                backgroundColor: Colors.orange,
                behavior: SnackBarBehavior.floating,
              ),
            );
            
            // Show loading dialog again for the collection update
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => const _LoadingDialog(),
            );
          }
        } else if (result.containsKey('removeImage') && result['removeImage'] == true) {
          // Image was removed
          newCoverImageUrls = [];
          
          // Delete old image from Cloudinary
          if (_collection.coverImageUrls.isNotEmpty) {
            for (final oldImageUrl in _collection.coverImageUrls) {
              await _deleteOldCloudinaryImage(oldImageUrl);
            }
          }
        }
        
        final updatedCollection = await _collectionService.updateCollection(
          _collection.id,
          name: result['name'],
          description: result['description'],
          primaryColor: result['primaryColor'],
          coverImageUrls: newCoverImageUrls,
        );
        
        if (mounted) {
          // Close loading dialog
          Navigator.pop(context);
          
          if (updatedCollection != null) {
            setState(() {
              _collection = updatedCollection;
            });
            
            // Emit collection updated event for real-time updates
            EventBus().emit(CollectionUpdatedEvent(_collection.id.toString()));
            
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.check, color: Colors.white, size: 20),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Collection Updated',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.green.shade600,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                margin: const EdgeInsets.all(16),
                duration: const Duration(seconds: 3),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Failed to update collection'),
                backgroundColor: Colors.red.shade600,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                margin: const EdgeInsets.all(16),
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          // Close loading dialog
          Navigator.pop(context);
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error updating collection: $e'),
              backgroundColor: Colors.red.shade600,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      }
    }
  }

  /// Helper method to delete old Cloudinary images
  Future<void> _deleteOldCloudinaryImage(String imageUrl) async {
    try {
      if (imageUrl.contains('res.cloudinary.com')) {
        final uri = Uri.parse(imageUrl);
        final pathSegments = uri.pathSegments;
        if (pathSegments.length >= 6) {
          // Extract public ID from Cloudinary URL
          final publicId = pathSegments.sublist(6).join('/').replaceAll(RegExp(r'\.[^.]*$'), '');
          final cloudinaryService = CloudinaryService();
          final deleted = await cloudinaryService.deleteImage(publicId);
          if (kDebugMode) {
            print('🗑️ Deleted old cover image: $publicId (success: $deleted)');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Failed to delete old cover image: $e');
      }
      // Continue silently - don't block the update for deletion failures
    }
  }

  Widget _buildNormalModeActions() {
    final theme = Theme.of(context);
    final primaryColor = _collection.primaryColor ?? theme.colorScheme.primary;
    
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _playCollection(),
            icon: const Icon(Icons.play_arrow, size: 24),
            label: const Text('Play All'),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 2,
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: () => _shuffleCollection(),
          icon: const Icon(Icons.shuffle, size: 20),
          label: const Text('Shuffle'),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.surface,
            foregroundColor: primaryColor,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: primaryColor.withOpacity(0.3)),
            ),
            elevation: 0,
          ),
        ),
        const SizedBox(width: 12),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: theme.dividerColor),
          ),
          child: IconButton(
            onPressed: () => _shareCollection(),
            icon: Icon(Icons.share_outlined, color: theme.colorScheme.onSurface),
            padding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }

  Widget _buildEditModeActions() {
    final theme = Theme.of(context);
    final primaryColor = _collection.primaryColor ?? theme.colorScheme.primary;
    final selectedCount = _selectedPins.length;
    final totalCount = _filteredPins.length;
    
    return Column(
      children: [
        // Selection info and controls
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: primaryColor.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.edit, color: primaryColor, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  selectedCount == 0 
                    ? 'Select songs to delete'
                    : '$selectedCount of $totalCount selected',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: primaryColor,
                  ),
                ),
              ),
              TextButton(
                onPressed: _selectAllPins,
                child: Text(
                  selectedCount == totalCount ? 'Deselect All' : 'Select All',
                  style: TextStyle(color: primaryColor),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // Action buttons
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: selectedCount > 0 ? _deleteSelectedPins : null,
                icon: const Icon(Icons.delete, size: 20),
                label: Text('Delete ($selectedCount)'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: selectedCount > 0 ? Colors.red : Colors.grey,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: selectedCount > 0 ? 2 : 0,
                ),
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton.icon(
              onPressed: _toggleEditMode,
              icon: const Icon(Icons.close, size: 20),
              label: const Text('Cancel'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.surface,
                foregroundColor: theme.colorScheme.onSurface,
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(color: theme.dividerColor),
                ),
                elevation: 0,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _LoadingDialog extends StatelessWidget {
  const _LoadingDialog();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 48,
              height: 48,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Updating Collection...',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Please wait while we save your changes',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _EditCollectionDialog extends StatefulWidget {
  final Collection collection;

  const _EditCollectionDialog({required this.collection});

  @override
  State<_EditCollectionDialog> createState() => _EditCollectionDialogState();
}

class _EditCollectionDialogState extends State<_EditCollectionDialog> {
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;
  late Color? _primaryColor;
  File? _newImageFile;
  bool _removeCurrentImage = false;
  bool _isLoadingImage = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: TextEncodingUtils.cleanCollectionText(widget.collection.name, isDescription: false));
    _descriptionController = TextEditingController(text: TextEncodingUtils.cleanCollectionText(widget.collection.description, isDescription: true));
    _primaryColor = widget.collection.primaryColor;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      setState(() => _isLoadingImage = true);
      
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        imageQuality: 85,
        maxWidth: 1024,
        maxHeight: 1024,
      );
      
      if (pickedFile != null) {
        setState(() {
          _newImageFile = File(pickedFile.path);
          _removeCurrentImage = false; // Reset remove flag if new image is selected
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to pick image: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      setState(() => _isLoadingImage = false);
    }
  }

  void _showImageSourceDialog() {
    final theme = Theme.of(context);
    final primaryColor = _primaryColor ?? theme.colorScheme.primary;
    
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Change Cover Image',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(Icons.photo_library, color: primaryColor),
              ),
              title: const Text('Photo Library'),
              subtitle: const Text('Choose from your photos'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.gallery);
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(Icons.photo_camera, color: primaryColor),
              ),
              title: const Text('Camera'),
              subtitle: const Text('Take a new photo'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.camera);
              },
            ),
            if (widget.collection.coverImageUrls.isNotEmpty || _newImageFile != null)
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.delete, color: Colors.red),
                ),
                title: const Text('Remove Image'),
                subtitle: const Text('Use default collection icon'),
                onTap: () {
                  Navigator.pop(context);
                  setState(() {
                    _newImageFile = null;
                    _removeCurrentImage = true;
                  });
                },
              ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  String? _extractCloudinaryPublicId(String url) {
    if (url.contains('res.cloudinary.com')) {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      if (pathSegments.length >= 6) {
        return pathSegments.sublist(6).join('/').replaceAll(RegExp(r'\.[^.]*$'), '');
      }
    }
    return null;
  }

  Widget _buildCurrentCoverImage() {
    final theme = Theme.of(context);
    final primaryColor = _primaryColor ?? theme.colorScheme.primary;
    
    if (_newImageFile != null) {
      // Show new selected image
      return Stack(
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Image.file(
                _newImageFile!,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ],
      );
    } else if (_removeCurrentImage) {
      // Show empty state when image is removed
      return Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              primaryColor.withOpacity(0.1),
              primaryColor.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: primaryColor.withOpacity(0.3),
            width: 2,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.collections_outlined,
              size: 40,
              color: primaryColor.withOpacity(0.7),
            ),
            const SizedBox(height: 8),
            Text(
              'No Cover',
              style: TextStyle(
                color: primaryColor.withOpacity(0.7),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    } else if (widget.collection.coverImageUrls.isNotEmpty) {
      // Show current cover image
      final coverUrl = widget.collection.coverImageUrls.first;
      final publicId = _extractCloudinaryPublicId(coverUrl);
      
      return Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: primaryColor.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: publicId != null
              ? CloudinaryImage(
                  publicId: publicId,
                  width: 120,
                  height: 120,
                  quality: 85,
                  format: 'webp',
                  errorWidget: Container(
                    color: primaryColor.withOpacity(0.1),
                    child: Icon(Icons.image_not_supported, color: primaryColor),
                  ),
                )
              : Image.network(
                  coverUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    color: primaryColor.withOpacity(0.1),
                    child: Icon(Icons.image_not_supported, color: primaryColor),
                  ),
                ),
        ),
      );
    } else {
      // Show empty state for collection without cover
      return Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              primaryColor.withOpacity(0.1),
              primaryColor.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: primaryColor.withOpacity(0.3),
            width: 2,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 32,
              color: primaryColor.withOpacity(0.7),
            ),
            const SizedBox(height: 8),
            Text(
              'Add Cover',
              style: TextStyle(
                color: primaryColor.withOpacity(0.7),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: const Text('Edit Collection'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cover Image Section
            Center(
              child: Column(
                children: [
                  Text(
                    'Cover Image',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface.withOpacity(0.8),
                    ),
                  ),
                  const SizedBox(height: 12),
                  GestureDetector(
                    onTap: _isLoadingImage ? null : _showImageSourceDialog,
                    child: _isLoadingImage
                        ? Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              color: theme.colorScheme.surfaceVariant,
                              border: Border.all(
                                color: theme.colorScheme.primary.withOpacity(0.3),
                                width: 2,
                              ),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 32,
                                  height: 32,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 3,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      theme.colorScheme.primary,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Loading...',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : _buildCurrentCoverImage(),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Tap to change cover image',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Collection Name
            TextField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'Collection Name',
                hintText: 'Enter collection name',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: const Icon(Icons.collections_bookmark),
              ),
              maxLength: 50,
            ),
            const SizedBox(height: 16),
            
            // Description
            TextField(
              controller: _descriptionController,
              decoration: InputDecoration(
                labelText: 'Description',
                hintText: 'Enter collection description',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: const Icon(Icons.description),
                alignLabelWithHint: true,
              ),
              maxLines: 3,
              maxLength: 200,
            ),
            const SizedBox(height: 16),
            
            // Collection Color
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _primaryColor ?? theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: theme.dividerColor),
                ),
                child: const Icon(Icons.palette, color: Colors.white),
              ),
              title: const Text('Collection Color'),
              subtitle: const Text('Choose a theme color'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () => _showColorPicker(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _nameController.text.trim().isNotEmpty ? () {
            final result = <String, dynamic>{
              'name': _nameController.text.trim(),
              'description': _descriptionController.text.trim(),
              'primaryColor': _primaryColor != null 
                  ? '#${_primaryColor!.value.toRadixString(16).substring(2).toUpperCase()}' 
                  : null,
            };
            
            // Add image-related flags
            if (_newImageFile != null) {
              result['newImageFile'] = _newImageFile;
            } else if (_removeCurrentImage) {
              result['removeImage'] = true;
            }
            
            Navigator.pop(context, result);
          } : null,
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _showColorPicker() {
    final colors = [
      const Color(0xFF6366F1), // Indigo
      const Color(0xFF8B5CF6), // Purple
      const Color(0xFFEC4899), // Pink
      const Color(0xFFEF4444), // Red
      const Color(0xFFF59E0B), // Orange
      const Color(0xFF10B981), // Green
      const Color(0xFF06B6D4), // Cyan
      const Color(0xFF3B82F6), // Blue
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Color'),
        content: Wrap(
          spacing: 12,
          runSpacing: 12,
          children: colors.map((color) {
            final isSelected = _primaryColor == color;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _primaryColor = color;
                });
                Navigator.pop(context);
              },
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected ? Colors.white : Colors.transparent,
                    width: 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: color.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: isSelected 
                  ? const Icon(Icons.check, color: Colors.white, size: 24)
                  : null,
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }
} 