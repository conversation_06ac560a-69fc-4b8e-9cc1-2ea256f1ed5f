import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'dart:math' as math;
import '../../config/themes.dart';
import '../../models/collection_model.dart';
import 'package:image_picker/image_picker.dart';
import '../../services/collection_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../services/cloudinary_service.dart';
import '../../utils/event_bus.dart';
import '../../utils/text_encoding_utils.dart';

class CreateCollectionScreen extends StatefulWidget {
  const CreateCollectionScreen({Key? key}) : super(key: key);

  @override
  State<CreateCollectionScreen> createState() => _CreateCollectionScreenState();
}

class _CreateCollectionScreenState extends State<CreateCollectionScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _nameFocusNode = FocusNode();
  final _descriptionFocusNode = FocusNode();
  
  bool _isPublic = true;
  File? _imageFile;
  bool _isLoading = false;
  bool _hasInteracted = false;
  
  late AnimationController _slideAnimController;
  late AnimationController _fadeAnimController;
  late AnimationController _scaleAnimController;
  late AnimationController _colorAnimController;
  
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _colorAnimation;
  
  final List<Color> _availableColors = [
    AppTheme.popColor,
    AppTheme.rockColor,
    AppTheme.electronicColor,
    AppTheme.hiphopColor,
    AppTheme.jazzColor,
    Colors.teal,
    Colors.purple,
    Colors.indigo,
    Colors.pink,
    Colors.orange,
    Colors.cyan,
    Colors.amber,
  ];
  
  late Color _selectedColor;
  int _selectedColorIndex = 0;

  // Services
  late CollectionService _collectionService;
  late CloudinaryService _cloudinaryService;

  @override
  void initState() {
    super.initState();
    
    // Initialize services
    final apiService = ApiService();
    _collectionService = CollectionService(
      apiService,
      AuthService(apiService),
    );
    _cloudinaryService = CloudinaryService();
    
    // Initialize animations
    _slideAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleAnimController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _colorAnimController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimController,
      curve: Curves.easeOutCubic,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _fadeAnimController,
      curve: Curves.easeOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _scaleAnimController,
      curve: Curves.elasticOut,
    ));
    
    _colorAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _colorAnimController,
      curve: Curves.easeInOut,
    ));
    
    // Select random initial color
    _selectedColorIndex = math.Random().nextInt(_availableColors.length);
    _selectedColor = _availableColors[_selectedColorIndex];
    
    // Start animations
    Future.delayed(const Duration(milliseconds: 100), () {
      _slideAnimController.forward();
      _fadeAnimController.forward();
      _scaleAnimController.forward();
      _colorAnimController.forward();
    });
    
    // Listen to focus changes for interactions
    _nameFocusNode.addListener(() {
      if (_nameFocusNode.hasFocus && !_hasInteracted) {
        setState(() => _hasInteracted = true);
        HapticFeedback.lightImpact();
      }
    });
  }

  @override
  void dispose() {
    _slideAnimController.dispose();
    _fadeAnimController.dispose();
    _scaleAnimController.dispose();
    _colorAnimController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _nameFocusNode.dispose();
    _descriptionFocusNode.dispose();
    super.dispose();
  }

  Future<void> _selectImage() async {
    HapticFeedback.mediumImpact();
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(20),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(context).dividerColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: _selectedColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.photo_library, color: _selectedColor),
                ),
                title: const Text('Photo Library'),
                subtitle: const Text('Choose from your photos'),
                onTap: () {
                  Navigator.pop(context);
                  Future.delayed(const Duration(milliseconds: 200), () {
                    _getImage(ImageSource.gallery);
                  });
                },
              ),
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: _selectedColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.photo_camera, color: _selectedColor),
                ),
                title: const Text('Camera'),
                subtitle: const Text('Take a new photo'),
                onTap: () {
                  Navigator.pop(context);
                  Future.delayed(const Duration(milliseconds: 200), () {
                    _getImage(ImageSource.camera);
                  });
                },
              ),
              if (_imageFile != null)
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(Icons.delete, color: Colors.red),
                  ),
                  title: const Text('Remove Image'),
                  subtitle: const Text('Use default collection icon'),
                  onTap: () {
                    Navigator.pop(context);
                    setState(() => _imageFile = null);
                    HapticFeedback.mediumImpact();
                  },
                ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _getImage(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        imageQuality: 80,
        maxWidth: 1024,
      );
      
      if (pickedFile != null) {
        setState(() => _imageFile = File(pickedFile.path));
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to select image: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _selectColor(int index) {
    if (_selectedColorIndex == index) return;
    
    HapticFeedback.selectionClick();
    setState(() {
      _selectedColorIndex = index;
      _selectedColor = _availableColors[index];
    });
    
    _colorAnimController.reset();
    _colorAnimController.forward();
  }

  Future<Collection?> _createCollection() async {
    if (!_formKey.currentState!.validate()) {
      HapticFeedback.heavyImpact();
      return null;
    }
    
    setState(() => _isLoading = true);
    HapticFeedback.mediumImpact();
    
    try {
      List<String> coverImageUrls = [];
      if (_imageFile != null) {
        try {
          // Upload image to Cloudinary
          final uploadResult = await _cloudinaryService.uploadImage(
            file: _imageFile!,
            folder: 'collections',
            tags: ['collection_cover', 'user_upload'],
            quality: 85,
          );
          
          coverImageUrls = [uploadResult.secureUrl];
        } catch (e) {
          // If upload fails, show error but continue with creation
          debugPrint('Failed to upload collection cover image: $e');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Failed to upload cover image. Collection will be created without cover.'),
                backgroundColor: Colors.orange,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
          // Continue without cover image
        }
      }
      
      final collection = await _collectionService.createCollection(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        isPublic: _isPublic,
        primaryColor: _selectedColor.value.toRadixString(16).substring(2).toUpperCase(),
        coverImageUrls: coverImageUrls.isNotEmpty ? coverImageUrls : null,
      );
      
      if (collection != null) {
        HapticFeedback.lightImpact();
        return collection;
      } else {
        HapticFeedback.heavyImpact();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Failed to create collection. Please try again.'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return null;
      }
    } catch (e) {
      HapticFeedback.heavyImpact();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create collection: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      return null;
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: AnimatedBuilder(
        animation: Listenable.merge([_slideAnimation, _fadeAnimation]),
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: CustomScrollView(
                slivers: [
                  // Modern App Bar
                  SliverAppBar(
                    expandedHeight: 120,
                    floating: false,
                    pinned: true,
                    stretch: true,
                    backgroundColor: _selectedColor,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    leading: IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () {
                        HapticFeedback.lightImpact();
                        Navigator.pop(context);
                      },
                    ),
                    actions: [
                      if (_hasInteracted)
                        AnimatedBuilder(
                          animation: _scaleAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _scaleAnimation.value,
                              child: TextButton(
                                onPressed: _isLoading ? null : () async {
                                  final collection = await _createCollection();
                                  if (collection != null && mounted) {
                                    Navigator.pop(context, collection);
                                  }
                                },
                                child: _isLoading
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : const Text(
                                        'Create',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                          fontSize: 16,
                                        ),
                                      ),
                              ),
                            );
                          },
                        ),
                    ],
                    flexibleSpace: FlexibleSpaceBar(
                      background: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              _selectedColor,
                              _selectedColor.withOpacity(0.8),
                            ],
                          ),
                        ),
                        child: SafeArea(
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(20, 50, 20, 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'New Collection',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 26,
                                    fontWeight: FontWeight.bold,
                                    height: 1.1,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  'Organize your music pins',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: 15,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  // Form Content
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Cover Image Section
                            Center(
                              child: AnimatedBuilder(
                                animation: _scaleAnimation,
                                builder: (context, child) {
                                  return Transform.scale(
                                    scale: _scaleAnimation.value,
                                    child: GestureDetector(
                                      onTap: _selectImage,
                                      child: AnimatedContainer(
                                        duration: const Duration(milliseconds: 300),
                                        width: 140,
                                        height: 140,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(24),
                                          gradient: _imageFile == null
                                              ? LinearGradient(
                                                  begin: Alignment.topLeft,
                                                  end: Alignment.bottomRight,
                                                  colors: [
                                                    _selectedColor.withOpacity(0.1),
                                                    _selectedColor.withOpacity(0.05),
                                                  ],
                                                )
                                              : null,
                                          border: Border.all(
                                            color: _selectedColor.withOpacity(0.3),
                                            width: 2,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: _selectedColor.withOpacity(0.2),
                                              blurRadius: 20,
                                              offset: const Offset(0, 8),
                                            ),
                                          ],
                                        ),
                                        child: _imageFile != null
                                            ? ClipRRect(
                                                borderRadius: BorderRadius.circular(22),
                                                child: Image.file(
                                                  _imageFile!,
                                                  fit: BoxFit.cover,
                                                ),
                                              )
                                            : Column(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    padding: const EdgeInsets.all(16),
                                                    decoration: BoxDecoration(
                                                      color: _selectedColor.withOpacity(0.1),
                                                      borderRadius: BorderRadius.circular(16),
                                                    ),
                                                    child: Icon(
                                                      Icons.add_photo_alternate_outlined,
                                                      size: 32,
                                                      color: _selectedColor,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 12),
                                                  Text(
                                                    'Add Cover',
                                                    style: TextStyle(
                                                      color: _selectedColor,
                                                      fontWeight: FontWeight.w600,
                                                      fontSize: 16,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    'Optional',
                                                    style: TextStyle(
                                                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            
                            const SizedBox(height: 32),
                            
                            // Color Selector
                            Text(
                              'Collection Theme',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w700,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            Container(
                              height: 60,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: _availableColors.length,
                                itemBuilder: (context, index) {
                                  final color = _availableColors[index];
                                  final isSelected = _selectedColorIndex == index;
                                  
                                  return AnimatedBuilder(
                                    animation: _colorAnimation,
                                    builder: (context, child) {
                                      return Padding(
                                        padding: const EdgeInsets.only(right: 12),
                                        child: GestureDetector(
                                          onTap: () => _selectColor(index),
                                          child: AnimatedContainer(
                                            duration: const Duration(milliseconds: 200),
                                            width: 48,
                                            height: 48,
                                            decoration: BoxDecoration(
                                              color: color,
                                              shape: BoxShape.circle,
                                              border: isSelected
                                                  ? Border.all(
                                                      color: Colors.white,
                                                      width: 3,
                                                    )
                                                  : null,
                                              boxShadow: [
                                                BoxShadow(
                                                  color: color.withOpacity(0.4),
                                                  blurRadius: isSelected ? 16 : 8,
                                                  offset: const Offset(0, 4),
                                                ),
                                              ],
                                            ),
                                            child: isSelected
                                                ? Transform.scale(
                                                    scale: _colorAnimation.value,
                                                    child: const Icon(
                                                      Icons.check,
                                                      color: Colors.white,
                                                      size: 24,
                                                    ),
                                                  )
                                                : null,
                                          ),
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                            
                            const SizedBox(height: 32),
                            
                            // Collection Name
                            Text(
                              'Collection Name',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w700,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 12),
                            
                            TextFormField(
                              controller: _nameController,
                              focusNode: _nameFocusNode,
                              decoration: InputDecoration(
                                hintText: 'Enter collection name...',
                                prefixIcon: Icon(
                                  Icons.collections_outlined,
                                  color: _selectedColor,
                                ),
                                filled: true,
                                fillColor: theme.cardColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16),
                                  borderSide: BorderSide.none,
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16),
                                  borderSide: BorderSide(
                                    color: _selectedColor,
                                    width: 2,
                                  ),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 16,
                                ),
                              ),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Please enter a collection name';
                                }
                                if (value.trim().length < 2) {
                                  return 'Name must be at least 2 characters';
                                }
                                return null;
                              },
                              onChanged: (value) {
                                if (!_hasInteracted && value.isNotEmpty) {
                                  setState(() => _hasInteracted = true);
                                }
                              },
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // Description
                            Text(
                              'Description',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w700,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 12),
                            
                            TextFormField(
                              controller: _descriptionController,
                              focusNode: _descriptionFocusNode,
                              maxLines: 3,
                              decoration: InputDecoration(
                                hintText: 'Describe your collection...',
                                prefixIcon: Padding(
                                  padding: const EdgeInsets.only(bottom: 40),
                                  child: Icon(
                                    Icons.description_outlined,
                                    color: _selectedColor,
                                  ),
                                ),
                                filled: true,
                                fillColor: theme.cardColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16),
                                  borderSide: BorderSide.none,
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16),
                                  borderSide: BorderSide(
                                    color: _selectedColor,
                                    width: 2,
                                  ),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 16,
                                ),
                              ),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Please enter a description';
                                }
                                return null;
                              },
                            ),
                            
                            const SizedBox(height: 32),
                            
                            // Privacy Toggle
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: theme.cardColor,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: theme.dividerColor.withOpacity(0.5),
                                ),
                              ),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: _selectedColor.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: Icon(
                                          _isPublic ? Icons.public : Icons.lock_outlined,
                                          color: _selectedColor,
                                          size: 24,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              _isPublic ? 'Public Collection' : 'Private Collection',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                                color: theme.colorScheme.onSurface,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              _isPublic
                                                  ? 'Anyone can view and follow'
                                                  : 'Only you can view • Supports virtual pins',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: theme.colorScheme.onSurface.withOpacity(0.6),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      AnimatedContainer(
                                        duration: const Duration(milliseconds: 200),
                                        child: Switch.adaptive(
                                          value: _isPublic,
                                          activeColor: _selectedColor,
                                          onChanged: (value) {
                                            setState(() => _isPublic = value);
                                            HapticFeedback.selectionClick();
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  // Warning about unchangeable visibility
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.amber.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Colors.amber.withOpacity(0.3),
                                        width: 1,
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.info_outline,
                                          color: Colors.amber[700],
                                          size: 20,
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Text(
                                            'Collection visibility cannot be changed after creation',
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: Colors.amber[800],
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (!_isPublic) ...[
                                    const SizedBox(height: 12),
                                    // Virtual pins explanation for private collections
                                    Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: _selectedColor.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: _selectedColor.withOpacity(0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.music_note,
                                            color: _selectedColor,
                                            size: 20,
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: Text(
                                              'Private collections support virtual pins (no location required)',
                                              style: TextStyle(
                                                fontSize: 13,
                                                color: _selectedColor,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                            
                            const SizedBox(height: 40),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
} 