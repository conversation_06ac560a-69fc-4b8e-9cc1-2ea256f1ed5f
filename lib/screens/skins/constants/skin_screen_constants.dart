import 'package:flutter/material.dart';

class SkinScreenConstants {
  // Layout
  static const double gridSpacing = 12.0;
  static const double cardAspectRatio = 0.75;
  static const double cardBorderRadius = 16.0;
  static const double searchBarHeight = 48.0;
  static const double tabBarHeight = 48.0;
  static const EdgeInsets screenPadding = EdgeInsets.all(16.0);
  static const EdgeInsets cardPadding = EdgeInsets.all(12.0);
  
  // Animations
  static const Duration tabTransitionDuration = Duration(milliseconds: 300);
  static const Duration searchDebounceDelay = Duration(milliseconds: 300);
  static const Duration cardHoverDuration = Duration(milliseconds: 200);
  static const Duration loadingShimmerDuration = Duration(milliseconds: 1500);
  static const Duration celebrationDuration = Duration(milliseconds: 2000);
  static const Duration carouselTransitionDuration = Duration(milliseconds: 400);
  
  // Grid
  static const int gridCrossAxisCount = 2;
  static const double gridMaxCrossAxisExtent = 200.0;
  
  // Text Styles
  static const TextStyle titleStyle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
  );
  
  static const TextStyle subtitleStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );
  
  static const TextStyle cardTitleStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
  );
  
  static const TextStyle cardDescriptionStyle = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
  );
  
  // Categories
  static const String myCollectionTab = 'My Collection';
  static const String featuredTab = 'Featured';
  static const String communityTab = 'Community';
  static const String achievementsTab = 'Achievements';
  
  // Status Labels
  static const String equipped = 'Currently Equipped';
  static const String readyToEquip = 'Ready to Equip';
  static const String locked = 'Locked';
  static const String timeLimited = 'Limited Time';
  static const String eventExclusive = 'Event Exclusive';
  static const String regionalExclusive = 'Regional Exclusive';
  
  // Rarity Levels
  static const String commonRarity = 'Common';
  static const String uncommonRarity = 'Uncommon';
  static const String rareRarity = 'Rare';
  static const String epicRarity = 'Epic';
  static const String legendaryRarity = 'Legendary';
  
  // Action Labels
  static const String equip = 'Equip';
  static const String share = 'Share';
  static const String viewProgress = 'View Progress';
  static const String viewChallenges = 'View Challenges';
  static const String viewFriends = 'View Friends';
  static const String searchHint = 'Search skins...';
  
  // Empty States
  static const String noSkinsFound = 'No skins found';
  static const String noUnlockedSkins = 'No unlocked skins yet';
  static const String noFeaturedSkins = 'No featured skins available';
  static const String noCommunityData = 'No community data available';
  static const String noAchievements = 'Complete challenges to unlock achievements';
  
  // Icons
  static const IconData searchIcon = Icons.search;
  static const IconData clearIcon = Icons.clear;
  static const IconData premiumIcon = Icons.star;
  static const IconData lockedIcon = Icons.lock;
  static const IconData unlockedIcon = Icons.lock_open;
  static const IconData equippedIcon = Icons.check_circle;
  static const IconData timerIcon = Icons.timer;
  static const IconData trendingIcon = Icons.trending_up;
  static const IconData achievementIcon = Icons.emoji_events;
  static const IconData communityIcon = Icons.people;
  static const IconData regionIcon = Icons.location_on;
  static const IconData shareIcon = Icons.share;
  static const IconData rotateIcon = Icons.rotate_90_degrees_ccw;
  
  // Colors (to be overridden by theme)
  static const Color shimmerBaseColor = Color(0xFFE0E0E0);
  static const Color shimmerHighlightColor = Color(0xFFF5F5F5);
  
  // Rarity Colors
  static const Color commonColor = Color(0xFF9E9E9E);
  static const Color uncommonColor = Color(0xFF4CAF50);
  static const Color rareColor = Color(0xFF2196F3);
  static const Color epicColor = Color(0xFF9C27B0);
  static const Color legendaryColor = Color(0xFFFFB300);
} 