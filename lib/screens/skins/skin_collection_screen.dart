import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/skin_provider.dart';
import '../../providers/gamification_provider.dart';
import 'controllers/skin_screen_controller.dart';
import 'constants/skin_screen_constants.dart';
import 'components/navigation/skin_tabs_bar.dart';
import 'components/navigation/skin_search_bar.dart';
import 'components/grids/base_skin_grid.dart';
import 'components/preview/skin_preview_sheet.dart';
import '../../models/pin_skin.dart';
import 'components/collection/equipped_skin_showcase.dart';
import 'components/featured/featured_carousel.dart';
import 'components/featured/featured_tab_content.dart';
import 'components/shared/celebration_overlay.dart';
import 'components/shared/rarity_badge.dart';
import '../../widgets/common/pin_preview_widget.dart';
import '../../widgets/common/equipped_pin_preview.dart';
import 'components/shared/sliver_filter_header_delegate.dart';
import 'components/gallery/gallery_tab_content.dart';

class SkinCollectionScreen extends StatefulWidget {
  const SkinCollectionScreen({Key? key}) : super(key: key);

  @override
  State<SkinCollectionScreen> createState() => _SkinCollectionScreenState();
}

class _SkinCollectionScreenState extends State<SkinCollectionScreen> with SingleTickerProviderStateMixin {
  late final TabController _tabController;
  late final SkinScreenController _screenController;
  final ScrollController _scrollController = ScrollController();
  
  // Pin skins data - will be populated from backend
  List<PinSkin> get _pinSkins {
    final skinProvider = context.read<SkinProvider>();
    if (skinProvider.availableSkins.isNotEmpty) {
      return skinProvider.availableSkins;
    }
    
    // Fallback data if backend is unavailable
    return [
      PinSkin(
        id: 1,
        name: "Default",
        image: "assets/images/pins/default_pin.png",
        description: "Classic pin design",
        createdAt: DateTime.now(),
        isUnlocked: true,
      ),
      PinSkin(
        id: 2,
        name: "Neon",
        image: "assets/images/pins/neon_pin.png",
        description: "Glowing neon effect",
        isPremium: true,
        createdAt: DateTime.now(),
        isUnlocked: true,
      ),
      PinSkin(
        id: 3,
        name: "Crystal",
        image: "assets/images/pins/crystal_pin.png",
        description: "Shimmering crystal",
        createdAt: DateTime.now(),
        isUnlocked: false,
      ),
      PinSkin(
        id: 4,
        name: "Retro",
        image: "assets/images/pins/retro_pin.png",
        description: "Vintage vibes",
        createdAt: DateTime.now(),
        isUnlocked: true,
      ),
    ];
  }
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 2,
      vsync: this,
    );
    
    // Initialize the controller without loading data
    _screenController = SkinScreenController(
      Provider.of<GamificationProvider>(context, listen: false),
      autoLoad: false, // Add this parameter to prevent auto-loading
    );
    
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        _screenController.setSelectedTab(_tabController.index);
      }
    });

    // Load data after initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSkinData();
      _screenController.loadData();
    });
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _screenController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
  
  Future<void> _loadSkinData() async {
    final skinProvider = context.read<SkinProvider>();
    await skinProvider.refreshSkins();
  }
  
  void _showSkinPreview(BuildContext context, PinSkin skin) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SkinPreviewSheet(
        skin: skin,
        onEquip: () async {
          final skinProvider = context.read<SkinProvider>();
          final gamificationProvider = context.read<GamificationProvider>();
          
          // Try backend first, fallback to local
          final success = await skinProvider.equipSkin(skin);
          if (!success) {
            gamificationProvider.equipSkin(skin);
          }
          
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Equipped ${skin.name}!'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        },
        onClaim: () async {
          final skinProvider = context.read<SkinProvider>();
          final success = await skinProvider.claimSkin(skin);
          
          if (success) {
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Claimed ${skin.name}!'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            );
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _screenController,
      child: Consumer<SkinProvider>(
        builder: (context, skinProvider, child) {
          return Scaffold(
            body: NestedScrollView(
              controller: _scrollController,
              headerSliverBuilder: (context, innerBoxIsScrolled) => [
                // App Bar with Tabs
                SliverAppBar(
                  pinned: true,
                  floating: true,
                  snap: true,
                  elevation: 0,
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  scrolledUnderElevation: 2,
                  centerTitle: true,
                  title: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Text(
                      'Skins',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 22,
                        color: Theme.of(context).colorScheme.onSurface,
                        letterSpacing: -0.5,
                      ),
                    ),
                  ),
                  actions: [
                    if (skinProvider.isLoading)
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.0),
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      )
                    else
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: IconButton(
                          icon: Icon(
                            Icons.refresh,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                          onPressed: _loadSkinData,
                        ),
                      ),
                  ],
                  bottom: SkinTabsBar(
                    tabController: _tabController,
                  ),
                ),
              ],
              body: TabBarView(
                controller: _tabController,
                children: [
                  // Gallery Tab
                  _buildGalleryTab(),
                  
                  // Featured Tab
                  _buildFeaturedTab(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildGalleryTab() {
    final skinProvider = context.read<SkinProvider>();
    final equippedSkin = skinProvider.equippedSkin ?? 
        _pinSkins.firstWhere(
          (skin) => skin.isEquipped, 
          orElse: () => _pinSkins.isNotEmpty ? _pinSkins.first : PinSkin(
            id: 1,
            name: 'Default Pin',
            image: 'assets/images/pins/default_pin.png',
            description: 'The standard pin for marking your favorite music spots.',
            createdAt: DateTime.now(),
            isUnlocked: true,
          ),
        );
    
    return GalleryTabContent(
      onSkinTap: _showSkinDetails,
      onEquippedTap: _showEquippedDetails,
      onShareSkin: _shareSkin,
      skins: _pinSkins,
      equippedSkin: equippedSkin,
    );
  }
  
  Widget _buildFeaturedTab() {
    final skinProvider = context.read<SkinProvider>();
    final featuredSkins = skinProvider.premiumSkins.isNotEmpty 
        ? skinProvider.premiumSkins 
        : _pinSkins.where((skin) => skin.isPremium).toList();
    
    return FeaturedTabContent(
      onSkinTap: _showSkinDetails,
      featuredSkins: featuredSkins,
      limitedTimeSkins: _pinSkins,
    );
  }
  
  void _showEquippedDetails() {
    final skinProvider = context.read<SkinProvider>();
    final equippedSkin = skinProvider.equippedSkin ?? PinSkin(
      id: 1,
      name: 'Default Pin',
      image: 'assets/images/pins/default_pin.png',
      description: 'The standard pin for marking your favorite music spots.',
      createdAt: DateTime.now(),
    );
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, controller) => EquippedSkinShowcase(
          skin: equippedSkin,
          onShare: _shareSkin,
          onUnequip: () => Navigator.pop(context),
        ),
      ),
    );
  }
  
  void _showSkinDetails(PinSkin? skin) {
    if (skin != null) {
      _showSkinPreview(context, skin);
    }
  }
  
  void _shareSkin() {
    // TODO: Implement skin sharing
  }
} 