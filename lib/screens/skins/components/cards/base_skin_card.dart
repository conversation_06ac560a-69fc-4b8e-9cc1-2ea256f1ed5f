import 'package:flutter/material.dart';
import '../../../../models/pin_skin.dart';
import '../../constants/skin_screen_constants.dart';

class BaseSkinCard extends StatelessWidget {
  final PinSkin skin;
  final VoidCallback? onTap;
  final bool showStatus;
  final Widget? statusWidget;
  final bool isInteractive;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  
  const BaseSkinCard({
    Key? key,
    required this.skin,
    this.onTap,
    this.showStatus = true,
    this.statusWidget,
    this.isInteractive = true,
    this.padding,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final actualPadding = padding ?? SkinScreenConstants.cardPadding;
    final actualBorderRadius = borderRadius ?? BorderRadius.circular(SkinScreenConstants.cardBorderRadius);
    
    return Material(
      color: Colors.transparent,
      child: AnimatedContainer(
        duration: SkinScreenConstants.cardHoverDuration,
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: actualBorderRadius,
          border: Border.all(
            color: skin.isEquipped
                ? theme.colorScheme.primary
                : theme.dividerColor.withOpacity(0.1),
            width: skin.isEquipped ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: InkWell(
          onTap: isInteractive ? onTap : null,
          borderRadius: actualBorderRadius,
          child: Padding(
            padding: actualPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Image container with status overlay
                Expanded(
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Skin image
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          skin.image,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Container(
                            color: theme.colorScheme.surface,
                            child: Icon(
                              Icons.broken_image,
                              color: theme.colorScheme.onSurface.withOpacity(0.5),
                            ),
                          ),
                        ),
                      ),
                      
                      // Premium indicator
                      if (skin.isPremium)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.secondary,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              SkinScreenConstants.premiumIcon,
                              size: 16,
                              color: theme.colorScheme.onSecondary,
                            ),
                          ),
                        ),
                      
                      // Status widget (locked/unlocked/equipped)
                      if (showStatus && statusWidget != null)
                        Positioned(
                          top: 8,
                          left: 8,
                          child: statusWidget!,
                        ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // Skin name
                Text(
                  skin.name,
                  style: SkinScreenConstants.cardTitleStyle.copyWith(
                    color: theme.colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
                
                if (skin.description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    skin.description!,
                    style: SkinScreenConstants.cardDescriptionStyle.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
} 