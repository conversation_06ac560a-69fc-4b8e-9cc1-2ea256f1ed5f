import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart' as vector;

class Pin3DPreview extends StatefulWidget {
  final String modelPath;
  final double initialScale;
  final bool autoRotate;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  
  const Pin3DPreview({
    Key? key,
    required this.modelPath,
    this.initialScale = 1.0,
    this.autoRotate = false,
    this.backgroundColor,
    this.onTap,
  }) : super(key: key);

  @override
  State<Pin3DPreview> createState() => _Pin3DPreviewState();
}

class _Pin3DPreviewState extends State<Pin3DPreview> with SingleTickerProviderStateMixin {
  late AnimationController _autoRotateController;
  
  // Transform state
  double _scale = 1.0;
  double _rotationX = 0.0;
  double _rotationY = 0.0;
  
  // Gesture state
  Offset? _lastFocalPoint;
  double? _lastScale;
  bool _isDragging = false;
  
  @override
  void initState() {
    super.initState();
    _scale = widget.initialScale;
    
    _autoRotateController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..addListener(() {
      if (widget.autoRotate && !_isDragging) {
        setState(() {
          _rotationY = _autoRotateController.value * 2 * math.pi;
        });
      }
    });
    
    if (widget.autoRotate) {
      _autoRotateController.repeat();
    }
  }
  
  @override
  void dispose() {
    _autoRotateController.dispose();
    super.dispose();
  }
  
  void _handleScaleStart(ScaleStartDetails details) {
    _lastFocalPoint = details.focalPoint;
    _lastScale = 1.0;
    _isDragging = true;
    if (widget.autoRotate) {
      _autoRotateController.stop();
    }
  }
  
  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (_lastFocalPoint == null) return;
    
    final delta = details.focalPoint - _lastFocalPoint!;
    _lastFocalPoint = details.focalPoint;
    
    setState(() {
      // Handle rotation
      _rotationY += delta.dx * 0.01;
      _rotationX += delta.dy * 0.01;
      
      // Handle scaling
      if (_lastScale != null) {
        final double scaleDiff = details.scale - _lastScale!;
        _scale = (_scale + scaleDiff).clamp(0.5, 3.0);
      }
      _lastScale = details.scale;
    });
  }
  
  void _handleScaleEnd(ScaleEndDetails details) {
    _lastFocalPoint = null;
    _lastScale = null;
    _isDragging = false;
    if (widget.autoRotate) {
      _autoRotateController.forward(from: _rotationY / (2 * math.pi));
    }
  }
  
  Matrix4 _computeMatrix4() {
    return Matrix4.identity()
      ..setEntry(3, 2, 0.001) // perspective
      ..scale(_scale, _scale, _scale)
      ..rotateX(_rotationX)
      ..rotateY(_rotationY);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onScaleStart: _handleScaleStart,
      onScaleUpdate: _handleScaleUpdate,
      onScaleEnd: _handleScaleEnd,
      onTap: widget.onTap,
      child: Container(
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? theme.cardColor,
          borderRadius: BorderRadius.circular(16),
        ),
        clipBehavior: Clip.antiAlias,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Sonar Animation
            if (widget.autoRotate)
              CustomPaint(
                painter: SonarPainter(
                  animation: _autoRotateController,
                  sonarColor: theme.colorScheme.primary,
                ),
              ),

            // 3D Transform
            Transform(
              transform: _computeMatrix4(),
              alignment: Alignment.center,
              child: Stack(
                alignment: Alignment.topCenter,
                children: [
                  // Pin Stem with enhanced 3D effect
                  Positioned(
                    top: widget.initialScale * 40,
                    child: Transform(
                      transform: Matrix4.identity()
                        ..setEntry(3, 2, 0.002)
                        ..rotateX(-math.pi / 6),
                      alignment: Alignment.topCenter,
                      child: Container(
                        width: widget.initialScale * 6,
                        height: widget.initialScale * 80,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              const Color(0xFFEEEEEE),
                              const Color(0xFFD8D8D8),
                              const Color(0xFFC9C9C9),
                              const Color(0xFFB0B0B0),
                              const Color(0xFFA0A0A0),
                            ],
                            stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
                          ),
                          borderRadius: BorderRadius.circular(widget.initialScale * 1.5),
                        ),
                        child: Stack(
                          children: [
                            // Base metal texture
                            Positioned.fill(
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(widget.initialScale * 1.5),
                                  gradient: LinearGradient(
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                    colors: [
                                      const Color(0xFFDDDDDD),
                                      const Color(0xFFCCCCCC),
                                      const Color(0xFFE5E5E5),
                                      const Color(0xFFCCCCCC),
                                    ],
                                    stops: const [0.0, 0.3, 0.6, 1.0],
                                  ),
                                ),
                              ),
                            ),
                            
                            // Central highlight line
                            Positioned(
                              left: widget.initialScale * 3 - (widget.initialScale * 1 / 2),
                              top: 0,
                              bottom: 0,
                              width: widget.initialScale * 1,
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.white.withOpacity(0.9),
                                      Colors.white.withOpacity(0.7),
                                      Colors.white.withOpacity(0.2),
                                      Colors.white.withOpacity(0.0),
                                    ],
                                    stops: const [0.0, 0.2, 0.5, 0.8],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  
                  // Pin Head/Circle
                  Container(
                    width: widget.initialScale * 80,
                    height: widget.initialScale * 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.4),
                          blurRadius: 12,
                          offset: const Offset(4, 6),
                        ),
                        BoxShadow(
                          color: Colors.white.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(-2, -2),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: Stack(
                        children: [
                          // Background image/texture
                          if (widget.modelPath.isNotEmpty)
                            Positioned.fill(
                              child: (widget.modelPath.startsWith('http://') || widget.modelPath.startsWith('https://'))
                                  ? Image.network(
                                      widget.modelPath,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) => Container(
                                        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                                        child: Icon(
                                          Icons.music_note,
                                          color: Colors.white,
                                          size: widget.initialScale * 30,
                                        ),
                                      ),
                                    )
                                  : Image.asset(
                                      widget.modelPath,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) => Container(
                                        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                                        child: Icon(
                                          Icons.music_note,
                                          color: Colors.white,
                                          size: widget.initialScale * 30,
                                        ),
                                      ),
                                    ),
                            ),
                          
                          // Gloss effect for realism
                          Positioned.fill(
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: RadialGradient(
                                  center: const Alignment(-0.3, -0.3),
                                  colors: [
                                    Colors.white.withOpacity(0.4),
                                    Colors.transparent,
                                    Colors.black.withOpacity(0.1),
                                  ],
                                  stops: const [0.0, 0.5, 1.0],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Gesture hint overlay
            if (!_isDragging) ...[
              Positioned(
                right: 16,
                bottom: 16,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.touch_app,
                        size: 16,
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Drag to rotate',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class SonarPainter extends CustomPainter {
  final Animation<double> animation;
  final Color sonarColor;

  SonarPainter({required this.animation, required this.sonarColor})
      : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height * 0.75);
    final animationValue = animation.value;

    // Draw three sonar rings
    for (int i = 0; i < 3; i++) {
      final wave = (animationValue + (i * 0.33)) % 1.0;
      final radius = wave * size.width * 0.5;
      final opacity = (1.0 - wave).clamp(0.0, 1.0);

      final paint = Paint()
        ..color = sonarColor.withOpacity(opacity * 0.5)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      if (radius > 0) {
        canvas.drawCircle(center, radius, paint);
      }
    }
  }

  @override
  bool shouldRepaint(SonarPainter oldDelegate) {
    return sonarColor != oldDelegate.sonarColor;
  }
} 