import 'package:flutter/material.dart';
import 'filter_chips.dart';

class SliverFilterHeaderDelegate extends SliverPersistentHeaderDelegate {
  final List<String> filterOptions;
  final String currentFilter;
  final Function(String) onFilterSelected;
  final bool isSmallScreen;

  SliverFilterHeaderDelegate({
    required this.filterOptions,
    required this.currentFilter,
    required this.onFilterSelected,
    required this.isSmallScreen,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return FilterChips(
      filterOptions: filterOptions,
      currentFilter: currentFilter,
      onFilterSelected: onFilterSelected,
      isSmallScreen: isSmallScreen,
    );
  }

  @override
  double get maxExtent => isSmallScreen ? 40 : 48;

  @override
  double get minExtent => isSmallScreen ? 40 : 48;

  @override
  bool shouldRebuild(SliverFilterHeaderDelegate oldDelegate) {
    return oldDelegate.currentFilter != currentFilter ||
           oldDelegate.isSmallScreen != isSmallScreen;
  }
} 