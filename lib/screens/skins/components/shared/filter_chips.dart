import 'package:flutter/material.dart';

class FilterChips extends StatelessWidget {
  final List<String> filterOptions;
  final String currentFilter;
  final Function(String) onFilterSelected;
  final bool isSmallScreen;

  const FilterChips({
    Key? key,
    required this.filterOptions,
    required this.currentFilter,
    required this.onFilterSelected,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: isSmallScreen ? 40 : 48,
      color: Theme.of(context).scaffoldBackgroundColor,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: filterOptions.map((filter) {
            final isSelected = filter == currentFilter;
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: ChoiceChip(
                label: Text(
                  filter,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                  ),
                ),
                selected: isSelected,
                backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                side: BorderSide(
                  color: Theme.of(context).dividerColor.withOpacity(0.1),
                  width: 1,
                ),
                labelStyle: TextStyle(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurface,
                ),
                onSelected: (selected) {
                  if (selected) {
                    onFilterSelected(filter);
                  }
                },
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

// Add SliverPersistentHeader delegate for pinned behavior
class SliverFilterHeaderDelegate extends SliverPersistentHeaderDelegate {
  final List<String> filterOptions;
  final String currentFilter;
  final Function(String) onFilterSelected;
  final bool isSmallScreen;

  SliverFilterHeaderDelegate({
    required this.filterOptions,
    required this.currentFilter,
    required this.onFilterSelected,
    required this.isSmallScreen,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return FilterChips(
      filterOptions: filterOptions,
      currentFilter: currentFilter,
      onFilterSelected: onFilterSelected,
      isSmallScreen: isSmallScreen,
    );
  }

  @override
  double get maxExtent => isSmallScreen ? 40 : 48;

  @override
  double get minExtent => isSmallScreen ? 40 : 48;

  @override
  bool shouldRebuild(SliverFilterHeaderDelegate oldDelegate) {
    return oldDelegate.currentFilter != currentFilter ||
           oldDelegate.isSmallScreen != isSmallScreen;
  }
} 