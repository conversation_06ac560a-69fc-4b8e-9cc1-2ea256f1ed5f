import 'package:flutter/material.dart';
import '../../constants/skin_screen_constants.dart';

enum SkinRarity {
  common,
  uncommon,
  rare,
  epic,
  legendary,
}

class RarityBadge extends StatelessWidget {
  final SkinRarity rarity;
  final bool showLabel;
  final double? size;
  final EdgeInsets? padding;
  
  const RarityBadge({
    Key? key,
    required this.rarity,
    this.showLabel = true,
    this.size,
    this.padding,
  }) : super(key: key);
  
  String get _rarityLabel {
    switch (rarity) {
      case SkinRarity.common:
        return SkinScreenConstants.commonRarity;
      case SkinRarity.uncommon:
        return SkinScreenConstants.uncommonRarity;
      case SkinRarity.rare:
        return SkinScreenConstants.rareRarity;
      case SkinRarity.epic:
        return SkinScreenConstants.epicRarity;
      case SkinRarity.legendary:
        return SkinScreenConstants.legendaryRarity;
    }
  }
  
  Color get _rarityColor {
    switch (rarity) {
      case SkinRarity.common:
        return SkinScreenConstants.commonColor;
      case SkinRarity.uncommon:
        return SkinScreenConstants.uncommonColor;
      case SkinRarity.rare:
        return SkinScreenConstants.rareColor;
      case SkinRarity.epic:
        return SkinScreenConstants.epicColor;
      case SkinRarity.legendary:
        return SkinScreenConstants.legendaryColor;
    }
  }
  
  IconData get _rarityIcon {
    switch (rarity) {
      case SkinRarity.common:
        return Icons.star_border;
      case SkinRarity.uncommon:
        return Icons.star_half;
      case SkinRarity.rare:
        return Icons.star;
      case SkinRarity.epic:
        return Icons.auto_awesome;
      case SkinRarity.legendary:
        return Icons.workspace_premium;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final actualSize = size ?? 32.0;
    final actualPadding = padding ?? const EdgeInsets.symmetric(
      horizontal: 8,
      vertical: 4,
    );
    
    return Container(
      padding: actualPadding,
      decoration: BoxDecoration(
        color: _rarityColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(actualSize / 2),
        border: Border.all(
          color: _rarityColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _rarityIcon,
            color: _rarityColor,
            size: actualSize * 0.6,
          ),
          if (showLabel) ...[
            const SizedBox(width: 4),
            Text(
              _rarityLabel,
              style: theme.textTheme.bodySmall?.copyWith(
                color: _rarityColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }
} 