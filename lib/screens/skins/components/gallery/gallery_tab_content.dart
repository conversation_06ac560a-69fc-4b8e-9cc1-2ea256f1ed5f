import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../models/pin_skin.dart';
import '../../../../widgets/common/pin_preview_widget.dart';
import '../../../../widgets/common/equipped_pin_preview.dart';
import '../shared/rarity_badge.dart';

class GalleryTabContent extends StatefulWidget {
  final Function(PinSkin) onSkinTap;
  final VoidCallback onEquippedTap;
  final VoidCallback onShareSkin;
  final List<PinSkin> skins;
  final PinSkin? equippedSkin;

  const GalleryTabContent({
    Key? key,
    required this.onSkinTap,
    required this.onEquippedTap,
    required this.onShareSkin,
    required this.skins,
    this.equippedSkin,
  }) : super(key: key);

  @override
  State<GalleryTabContent> createState() => _GalleryTabContentState();
}

class _GalleryTabContentState extends State<GalleryTabContent> {
  String _currentFilter = 'All';
  final List<String> _filterOptions = ['All', 'Premium', 'Unlocked', 'New'];
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return CustomScrollView(
      slivers: [
        // Equipped Pin Showcase with glassmorphism
        SliverToBoxAdapter(
          child: _buildEquippedShowcase(context, isDark),
        ),
        
        // Filter Pills
        SliverToBoxAdapter(
          child: _buildFilterPills(context, isDark),
        ),
        
        // Skin Grid with glassmorphism cards
        SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.85,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final filteredSkins = _getFilteredSkins();
                if (index < filteredSkins.length) {
                  return _buildGlassmorphismSkinCard(
                    context, 
                    skin: filteredSkins[index], 
                    index: index,
                    isDark: isDark,
                  );
                }
                return null;
              },
              childCount: _getFilteredSkins().length,
            ),
          ),
        ),
        
        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }

  // Modern Equipped Showcase
  Widget _buildEquippedShowcase(BuildContext context, bool isDark) {
    final theme = Theme.of(context);
    final equippedSkin = widget.equippedSkin ?? 
        (widget.skins.isNotEmpty ? widget.skins.first : _getPlaceholderSkin(0));
    
    return Container(
      margin: const EdgeInsets.all(16),
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(isDark ? 0.3 : 0.2),
            theme.colorScheme.primary.withOpacity(isDark ? 0.1 : 0.05),
            Colors.purple.withOpacity(isDark ? 0.2 : 0.1),
          ],
        ),
        border: Border.all(
          color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.4 : 0.1),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Stack(
          children: [
            // Background blur effect
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(isDark ? 0.05 : 0.1),
                      Colors.white.withOpacity(isDark ? 0.02 : 0.05),
                    ],
                  ),
                ),
              ),
            ),
            
            // Content
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.onEquippedTap,
                borderRadius: BorderRadius.circular(24),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      // Pin with glow effect
                      Stack(
                        children: [
                          // Glow effect
                          Container(
                            width: 80,
                            height: 100,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: theme.colorScheme.primary.withOpacity(0.4),
                                  blurRadius: 30,
                                  spreadRadius: 10,
                                ),
                              ],
                            ),
                          ),
                          // Pin preview
                          Container(
                            width: 80,
                            height: 100,
                            child: EquippedPinPreview(
                              skin: equippedSkin,
                              size: 80,
                              onTap: widget.onEquippedTap,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(width: 20),
                      
                      // Info section
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Equipped badge
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.check_circle,
                                    size: 16,
                                    color: Colors.green,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    'EQUIPPED',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            
                            const SizedBox(height: 12),
                            
                            // Skin name
                            Text(
                              equippedSkin.name,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            
                            const SizedBox(height: 4),
                            
                            // Description
                            Text(
                              equippedSkin.description ?? 'Your currently equipped pin skin',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.7),
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Modern Filter Pills
  Widget _buildFilterPills(BuildContext context, bool isDark) {
    final theme = Theme.of(context);
    
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _filterOptions.length,
        itemBuilder: (context, index) {
          final filter = _filterOptions[index];
          final isSelected = _currentFilter == filter;
          final gradientColors = _getFilterGradient(index, isDark);
          
          return GestureDetector(
            onTap: () {
              setState(() {
                _currentFilter = filter;
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                gradient: isSelected 
                  ? LinearGradient(
                      colors: gradientColors,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : null,
                color: isSelected ? null : theme.cardColor.withOpacity(0.8),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected 
                    ? Colors.white.withOpacity(0.3)
                    : theme.dividerColor.withOpacity(0.2),
                  width: 1,
                ),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: gradientColors.first.withOpacity(0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 4),
                  ),
                ] : null,
              ),
              child: Center(
                child: Text(
                  filter,
                  style: TextStyle(
                    color: isSelected 
                      ? Colors.white 
                      : theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // Glassmorphism Skin Card
  Widget _buildGlassmorphismSkinCard(
    BuildContext context, {
    required PinSkin skin,
    required int index,
    required bool isDark,
  }) {
    final theme = Theme.of(context);
    final gradientColors = _getCardGradient(index, isDark);
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
        ),
        border: Border.all(
          color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: gradientColors.first.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 6),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.3 : 0.08),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // Background blur effect
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(isDark ? 0.03 : 0.08),
                      Colors.white.withOpacity(isDark ? 0.01 : 0.03),
                    ],
                  ),
                ),
              ),
            ),
            
            // Content
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => widget.onSkinTap(skin),
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Stack(
                    children: [
                      // Main content column
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Pin preview - now takes full available space
                          Expanded(
                            child: Center(
                              child: Stack(
                                children: [
                                  // Glow effect
                                  Container(
                                    width: 84,
                                    height: 112,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: gradientColors.first.withOpacity(0.5),
                                          blurRadius: 20,
                                          spreadRadius: 5,
                                        ),
                                      ],
                                    ),
                                  ),
                                  // Pin widget
                                  Container(
                                    width: 84,
                                    height: 112,
                                    child: PinPreviewWidget(
                                      skin: skin,
                                      size: 84,
                                      onTap: () => widget.onSkinTap(skin),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      
                      const SizedBox(height: 12),
                          
                          // Skin info
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                skin.name,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _getRarityColor(skin.skinType).withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: _getRarityColor(skin.skinType).withOpacity(0.4),
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      skin.skinType,
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                        color: _getRarityColor(skin.skinType),
                                        letterSpacing: 0.5,
                                      ),
                                    ),
                                  ),
                                  const Spacer(),
                                  if (skin.isPremium)
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.amber.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Colors.amber.withOpacity(0.4),
                                          width: 1,
                                        ),
                                      ),
                                      child: Text(
                                        'PREMIUM',
                                        style: TextStyle(
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.amber,
                                          letterSpacing: 0.5,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                      
                      // Overlay lock icon for premium locked skins
                      if (skin.isPremium && !skin.isUnlocked)
                        Positioned(
                          top: 0,
                          left: 0,
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.amber.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.amber.withOpacity(0.4),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.lock_rounded,
                              size: 16,
                              color: Colors.amber,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  List<PinSkin> _getFilteredSkins() {
    switch (_currentFilter) {
      case 'Premium':
        return widget.skins.where((skin) => skin.isPremium).toList();
      case 'Unlocked':
        return widget.skins.where((skin) => skin.isUnlocked).toList();
      case 'New':
        return widget.skins.where((skin) => 
          DateTime.now().difference(skin.createdAt).inDays <= 7
        ).toList();
      default:
        return widget.skins;
    }
  }

  PinSkin _getPlaceholderSkin(int index) {
    return PinSkin(
      id: index,
      name: 'Default Pin',
      description: 'Classic pin design',
      image: '',
      isPremium: false,
      isUnlocked: true,
      createdAt: DateTime.now(),
    );
  }

  List<Color> _getFilterGradient(int index, bool isDark) {
    final gradients = [
      [Colors.blue, Colors.purple],
      [Colors.purple, Colors.pink],
      [Colors.green, Colors.teal],
      [Colors.orange, Colors.red],
    ];
    
    final colors = gradients[index % gradients.length];
    return [
      colors[0].withOpacity(isDark ? 0.8 : 0.7),
      colors[1].withOpacity(isDark ? 0.6 : 0.5),
    ];
  }

  List<Color> _getCardGradient(int index, bool isDark) {
    final gradients = [
      [Colors.blue, Colors.indigo],
      [Colors.purple, Colors.deepPurple],
      [Colors.teal, Colors.cyan],
      [Colors.orange, Colors.deepOrange],
      [Colors.pink, Colors.red],
      [Colors.green, Colors.lightGreen],
    ];
    
    final colors = gradients[index % gradients.length];
    return [
      colors[0].withOpacity(isDark ? 0.4 : 0.3),
      colors[1].withOpacity(isDark ? 0.2 : 0.15),
      colors[0].withOpacity(isDark ? 0.1 : 0.05),
    ];
  }

  Color _getRarityColor(String skinType) {
    switch (skinType) {
      case 'ARTIST':
        return Colors.purple;
      case 'HOUSE':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
} 