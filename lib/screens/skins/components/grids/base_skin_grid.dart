import 'package:flutter/material.dart';
import '../../../../models/pin_skin.dart';
import '../../constants/skin_screen_constants.dart';
import '../cards/base_skin_card.dart';
import '../status/skin_status_badge.dart';

class BaseSkinGrid extends StatelessWidget {
  final List<PinSkin> skins;
  final Function(PinSkin) onSkinTap;
  final bool isLoading;
  final String? emptyMessage;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final EdgeInsets? padding;
  final ScrollController? scrollController;
  final bool shrinkWrap;
  
  const BaseSkinGrid({
    Key? key,
    required this.skins,
    required this.onSkinTap,
    this.isLoading = false,
    this.emptyMessage,
    this.loadingWidget,
    this.emptyWidget,
    this.padding,
    this.scrollController,
    this.shrinkWrap = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return loadingWidget ?? _buildLoadingGrid(context);
    }
    
    if (skins.isEmpty) {
      return emptyWidget ?? _buildEmptyState(context);
    }
    
    return GridView.builder(
      controller: scrollController,
      padding: padding ?? SkinScreenConstants.screenPadding,
      shrinkWrap: shrinkWrap,
      physics: shrinkWrap ? const NeverScrollableScrollPhysics() : null,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: SkinScreenConstants.gridCrossAxisCount,
        childAspectRatio: SkinScreenConstants.cardAspectRatio,
        crossAxisSpacing: SkinScreenConstants.gridSpacing,
        mainAxisSpacing: SkinScreenConstants.gridSpacing,
      ),
      itemCount: skins.length,
      itemBuilder: (context, index) {
        final skin = skins[index];
        return BaseSkinCard(
          skin: skin,
          onTap: () => onSkinTap(skin),
          statusWidget: _buildStatusBadge(context, skin),
        );
      },
    );
  }
  
  Widget _buildStatusBadge(BuildContext context, PinSkin skin) {
    if (skin.isEquipped) {
      return const SkinStatusBadge(status: SkinStatus.equipped);
    }
    if (skin.isUnlocked) {
      return const SkinStatusBadge(status: SkinStatus.unlocked);
    }
    return const SkinStatusBadge(status: SkinStatus.locked);
  }
  
  Widget _buildLoadingGrid(BuildContext context) {
    return GridView.builder(
      padding: padding ?? SkinScreenConstants.screenPadding,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: SkinScreenConstants.gridCrossAxisCount,
        childAspectRatio: SkinScreenConstants.cardAspectRatio,
        crossAxisSpacing: SkinScreenConstants.gridSpacing,
        mainAxisSpacing: SkinScreenConstants.gridSpacing,
      ),
      itemCount: 6,
      itemBuilder: (context, index) => _buildShimmerCard(context),
    );
  }
  
  Widget _buildShimmerCard(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(SkinScreenConstants.cardBorderRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 16,
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 24),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 12,
            width: 80,
            margin: const EdgeInsets.symmetric(horizontal: 24),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 12),
        ],
      ),
    );
  }
  
  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.style_outlined,
            size: 64,
            color: theme.colorScheme.primary.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            emptyMessage ?? SkinScreenConstants.noSkinsFound,
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
} 