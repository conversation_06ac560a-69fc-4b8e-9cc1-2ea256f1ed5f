import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:async';
import '../../../../models/pin_skin.dart';
import '../../../../widgets/common/pin_preview_widget.dart';
import 'package:provider/provider.dart';
import '../../../../providers/skin_provider.dart';

class FeaturedTabContent extends StatefulWidget {
  final Function(PinSkin) onSkinTap;
  final List<PinSkin> featuredSkins;
  final List<PinSkin> limitedTimeSkins;

  const FeaturedTabContent({
    Key? key,
    required this.onSkinTap,
    required this.featuredSkins,
    required this.limitedTimeSkins,
  }) : super(key: key);

  @override
  State<FeaturedTabContent> createState() => _FeaturedTabContentState();
}

class _FeaturedTabContentState extends State<FeaturedTabContent> with TickerProviderStateMixin {
  late AnimationController _headerAnimationController;
  Timer? _countdownTimer;

  @override
  void initState() {
    super.initState();
    
    // Initialize header animation
    _headerAnimationController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    
    _headerAnimationController.repeat(reverse: true);
    
    // Start countdown timer for limited-time skins
    _startCountdownTimer();
    
    // Load featured and limited skins from API
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadFeaturedSkins();
    });
  }

  @override
  void dispose() {
    _headerAnimationController.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdownTimer() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) {
        setState(() {
          // This will trigger a rebuild to update the time remaining
        });
      }
    });
  }

  Future<void> _loadFeaturedSkins() async {
    final skinProvider = context.read<SkinProvider>();
    await Future.wait([
      skinProvider.loadFeaturedSkins(),
      skinProvider.loadLimitedSkins(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final skinProvider = context.watch<SkinProvider>();
    
    return RefreshIndicator(
      onRefresh: _loadFeaturedSkins,
      child: CustomScrollView(
      slivers: [
          // Cool animated header
          SliverToBoxAdapter(
            child: _buildAnimatedHeader(context, isDark),
          ),
          
        // Featured Carousel
        SliverToBoxAdapter(
            child: _buildFeaturedCarousel(context, isDark, skinProvider),
        ),
        
        // Limited Time Offers
          if (skinProvider.activeLimitedSkins.isNotEmpty)
        SliverToBoxAdapter(
              child: _buildLimitedTimeSection(context, isDark, skinProvider),
        ),
        
        // Spacing between limited section and premium grid
        const SliverToBoxAdapter(
          child: SizedBox(height: 16),
        ),
        
        // Premium Grid
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.85,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                  final allPremiumSkins = _getAllPremiumSkins(skinProvider);
                if (index < allPremiumSkins.length) {
                  return _buildGlassmorphismCard(
                    context,
                    skin: allPremiumSkins[index],
                    index: index,
                    isDark: isDark,
                  );
                }
                return null;
              },
                childCount: _getAllPremiumSkins(skinProvider).length,
            ),
          ),
        ),
        
        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
      ),
    );
  }

  // Cool animated header with graphics
  Widget _buildAnimatedHeader(BuildContext context, bool isDark) {
    final theme = Theme.of(context);
    return Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(0.8),
            theme.colorScheme.secondary.withOpacity(0.6),
            Colors.purple.withOpacity(0.4),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Stack(
          children: [
            // Animated background pattern
            AnimatedBuilder(
              animation: _headerAnimationController,
              builder: (context, child) {
                return CustomPaint(
                  size: Size.infinite,
                  painter: AnimatedBackgroundPainter(
                    animation: _headerAnimationController.value,
                    isDark: isDark,
                  ),
                );
              },
            ),
            
            // Floating pins animation
            AnimatedBuilder(
              animation: _headerAnimationController,
              builder: (context, child) {
                return Stack(
                  children: [
                    // Removed pin icons
                  ],
                );
              },
            ),
            
            // Content
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Featured Skins',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Discover exclusive and limited-time pin designs',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.9),
                      shadows: [
                        Shadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 8,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Featured Carousel with glassmorphism
  Widget _buildFeaturedCarousel(BuildContext context, bool isDark, SkinProvider skinProvider) {
    final theme = Theme.of(context);
    final featuredSkins = [
      ...skinProvider.latestSkins.take(3),
      ...skinProvider.topSkins.take(3),
    ].toSet().toList(); // Remove duplicates
    
    if (featuredSkins.isEmpty) {
      return const SizedBox(height: 300);
    }
    
    return Container(
      height: 300,
      margin: const EdgeInsets.all(16),
      child: PageView.builder(
        controller: PageController(viewportFraction: 0.85),
        itemCount: featuredSkins.length,
        itemBuilder: (context, index) {
          final skin = featuredSkins[index];
          return _buildFeatureCard(context, skin: skin, index: index, isDark: isDark);
        },
      ),
    );
  }

  // Featured Card with glassmorphism
  Widget _buildFeatureCard(
    BuildContext context, {
    required PinSkin skin,
    required int index,
    required bool isDark,
  }) {
    final theme = Theme.of(context);
    final gradientColors = _getFeatureGradient(index, isDark);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
        ),
        border: Border.all(
          color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: gradientColors.first.withOpacity(0.4),
            blurRadius: 30,
            offset: const Offset(0, 12),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.4 : 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Stack(
          children: [
            // Background blur effect
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(isDark ? 0.03 : 0.08),
                      Colors.white.withOpacity(isDark ? 0.01 : 0.03),
                    ],
                  ),
                ),
              ),
            ),
            
            // Overlay lock icon (top-left) for premium locked skins
            if (skin.isPremium && !skin.isUnlocked)
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.amber.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.amber.withOpacity(0.4),
                      width: 1,
                    ),
                  ),
                  child: const Icon(
                    Icons.lock_rounded,
                    size: 16,
                    color: Colors.amber,
                  ),
                ),
              ),
            
            // Content
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => widget.onSkinTap(skin),
                borderRadius: BorderRadius.circular(24),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Featured badge and lock icon
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.amber.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.amber.withOpacity(0.4),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.star_rounded,
                                  size: 16,
                                  color: Colors.amber,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  'FEATURED',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.amber,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Spacer(),
                          // Lock icon in top-right
                          if (skin.isPremium && !skin.isUnlocked)
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: Icon(
                                Icons.lock_rounded,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                        ],
                      ),
                      
                      // Pin preview
                      Expanded(
                        child: Center(
                          child: Stack(
                            children: [
                              // Glow effect
                              Container(
                                width: 100,
                                height: 120,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.amber.withOpacity(0.6),
                                      blurRadius: 40,
                                      spreadRadius: 15,
                                    ),
                                  ],
                                ),
                              ),
                              // Pin widget
                              Container(
                                width: 100,
                                height: 120,
                                child: PinPreviewWidget(
                                  skin: skin,
                                  size: 100,
                                  onTap: () => widget.onSkinTap(skin),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      // Skin info
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            skin.name,
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 6),
                          Text(
                            skin.description ?? 'Exclusive featured skin',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white.withOpacity(0.8),
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Limited Time Section with individual timers
  Widget _buildLimitedTimeSection(BuildContext context, bool isDark, SkinProvider skinProvider) {
    final theme = Theme.of(context);
    final limitedSkins = skinProvider.activeLimitedSkins
        .where((skin) => !skin.isExpired)
        .toList()
      ..sort((a, b) {
        // Sort by expiry date, soonest first
        if (a.expiryDate == null) return 1;
        if (b.expiryDate == null) return -1;
        return a.expiryDate!.compareTo(b.expiryDate!);
      });
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Section header
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.red.withOpacity(isDark ? 0.3 : 0.2),
                  Colors.orange.withOpacity(isDark ? 0.2 : 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.local_fire_department_rounded,
                    color: Colors.red,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Limited Time Offers',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        '${limitedSkins.length} exclusive skins available',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Limited time skins horizontal scroll
          SizedBox(
            height: 220,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: limitedSkins.length,
              itemBuilder: (context, index) {
                final skin = limitedSkins[index];
                return _buildLimitedTimeCard(context, skin: skin, index: index, isDark: isDark);
              },
            ),
          ),
        ],
      ),
    );
  }

  // Limited Time Card with individual timer
  Widget _buildLimitedTimeCard(
    BuildContext context, {
    required PinSkin skin,
    required int index,
    required bool isDark,
  }) {
    final timeRemaining = skin.timeRemaining ?? 0;
    final isUrgent = timeRemaining < 86400; // Less than 24 hours
    
    final gradientColors = isUrgent
        ? [
            Colors.red.withOpacity(isDark ? 0.5 : 0.4),
            Colors.orange.withOpacity(isDark ? 0.3 : 0.2),
          ]
        : [
            Colors.orange.withOpacity(isDark ? 0.4 : 0.3),
            Colors.amber.withOpacity(isDark ? 0.2 : 0.15),
    ];
    
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
        ),
        border: Border.all(
          color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: (isUrgent ? Colors.red : Colors.orange).withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => widget.onSkinTap(skin),
            borderRadius: BorderRadius.circular(20),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Timer badge at top
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: (isUrgent ? Colors.red : Colors.orange).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: (isUrgent ? Colors.red : Colors.orange).withOpacity(0.4),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.timer_rounded,
                          size: 12,
                          color: isUrgent ? Colors.red : Colors.orange,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          skin.formattedTimeRemaining,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: isUrgent ? Colors.red : Colors.orange,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Lock icon if needed
                  if (skin.isPremium && !skin.isUnlocked)
                    Align(
                      alignment: Alignment.topRight,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.lock_rounded,
                          size: 14,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  
                  // Pin preview
                  Expanded(
                    child: Center(
                      child: Container(
                        width: 84,
                        height: 112,
                        child: PinPreviewWidget(
                          skin: skin,
                          size: 84,
                          onTap: () => widget.onSkinTap(skin),
                        ),
                      ),
                    ),
                  ),
                  
                  // Skin info
                  Text(
                    skin.name,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 4),
                  
                  if (isUrgent)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                        'ENDING SOON',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Glassmorphism Premium Card
  Widget _buildGlassmorphismCard(
    BuildContext context, {
    required PinSkin skin,
    required int index,
    required bool isDark,
  }) {
    final theme = Theme.of(context);
    final gradientColors = _getCardGradient(index, isDark);
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
        ),
        border: Border.all(
          color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: gradientColors.first.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 6),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.3 : 0.08),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // Background blur effect
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(isDark ? 0.03 : 0.08),
                      Colors.white.withOpacity(isDark ? 0.01 : 0.03),
                    ],
                  ),
                ),
              ),
            ),
            
            // Overlay lock icon (top-left) for premium locked skins
            if (skin.isPremium && !skin.isUnlocked)
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.amber.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.amber.withOpacity(0.4),
                      width: 1,
                    ),
                  ),
                  child: const Icon(
                    Icons.lock_rounded,
                    size: 16,
                    color: Colors.amber,
                  ),
                ),
              ),
            
            // Content
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => widget.onSkinTap(skin),
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Pin preview
                      Expanded(
                        child: Center(
                          child: Stack(
                            children: [
                              // Glow effect
                              Container(
                                width: 84,
                                height: 112,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: gradientColors.first.withOpacity(0.5),
                                      blurRadius: 20,
                                      spreadRadius: 5,
                                    ),
                                  ],
                                ),
                              ),
                              // Pin widget
                              Container(
                                width: 84,
                                height: 112,
                                child: PinPreviewWidget(
                                  skin: skin,
                                  size: 84,
                                  onTap: () => widget.onSkinTap(skin),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Skin info
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            skin.name,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: _getSkinTypeColor(skin.skinType).withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: _getSkinTypeColor(skin.skinType).withOpacity(0.4),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  skin.skinType,
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: _getSkinTypeColor(skin.skinType),
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ),
                              const Spacer(),
                              if (skin.isPremium)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.amber.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.amber.withOpacity(0.4),
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    'PREMIUM',
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.amber,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  List<PinSkin> _getAllPremiumSkins(SkinProvider skinProvider) {
    final combinedSkins = <PinSkin>[];
    combinedSkins.addAll(skinProvider.latestSkins);
    combinedSkins.addAll(skinProvider.topSkins);
    
    if (combinedSkins.isEmpty) {
      return List.generate(6, (i) => _getPlaceholderSkin(i + 20, isPremium: true));
    }
    
    return combinedSkins.where((skin) => skin.isPremium).toList();
  }

  PinSkin _getPlaceholderSkin(int index, {bool isPremium = false}) {
    final names = [
      "Premium Crystal",
      "Golden Melody", 
      "Diamond Beat",
      "Platinum Vibes",
      "Ruby Rhythms",
      "Sapphire Sound"
    ];
    
    return PinSkin(
      id: index,
      name: names[index % names.length],
      description: isPremium ? 'Exclusive premium skin' : 'Standard pin design',
      image: '',
      isPremium: isPremium,
      isUnlocked: false,
      createdAt: DateTime.now(),
    );
  }

  List<Color> _getFeatureGradient(int index, bool isDark) {
    final gradients = [
      [Colors.amber, Colors.orange],
      [Colors.purple, Colors.pink],
      [Colors.blue, Colors.indigo],
    ];
    
    final colors = gradients[index % gradients.length];
    return [
      colors[0].withOpacity(isDark ? 0.6 : 0.5),
      colors[1].withOpacity(isDark ? 0.4 : 0.3),
      colors[0].withOpacity(isDark ? 0.2 : 0.1),
    ];
  }

  List<Color> _getCardGradient(int index, bool isDark) {
    final gradients = [
      [Colors.blue, Colors.indigo],
      [Colors.purple, Colors.deepPurple],
      [Colors.teal, Colors.cyan],
      [Colors.orange, Colors.deepOrange],
      [Colors.pink, Colors.red],
      [Colors.green, Colors.lightGreen],
    ];
    
    final colors = gradients[index % gradients.length];
    return [
      colors[0].withOpacity(isDark ? 0.4 : 0.3),
      colors[1].withOpacity(isDark ? 0.2 : 0.15),
      colors[0].withOpacity(isDark ? 0.1 : 0.05),
    ];
  }

  Color _getSkinTypeColor(String skinType) {
    switch (skinType) {
      case 'ARTIST':
        return Colors.purple;
      case 'HOUSE':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
} 

// Custom painter for animated background
class AnimatedBackgroundPainter extends CustomPainter {
  final double animation;
  final bool isDark;
  
  AnimatedBackgroundPainter({
    required this.animation,
    required this.isDark,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.white.withOpacity(0.1);
    
    // Draw animated stars
    for (int i = 0; i < 15; i++) {
      final random = math.Random(i);
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final starSize = 5.0 + random.nextDouble() * 15.0;
      final rotation = animation * 2 * math.pi + random.nextDouble() * math.pi;
      final opacity = 0.2 + (math.sin(animation * 2 * math.pi + i) * 0.3).abs();
      
      _drawStar(
        canvas, 
        Offset(x, y), 
        starSize, 
        rotation, 
        opacity,
        paint,
      );
    }
  }
  
  void _drawStar(
    Canvas canvas, 
    Offset center, 
    double size, 
    double rotation,
    double opacity,
    Paint paint,
  ) {
    final path = Path();
    const int points = 5;
    final outerRadius = size;
    final innerRadius = size * 0.4;
    
    for (int i = 0; i < points * 2; i++) {
      final radius = i % 2 == 0 ? outerRadius : innerRadius;
      final angle = (math.pi / points) * i - math.pi / 2;
      final point = Offset(
        center.dx + math.cos(angle + rotation) * radius,
        center.dy + math.sin(angle + rotation) * radius,
      );
      
      if (i == 0) {
        path.moveTo(point.dx, point.dy);
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }
    path.close();
    
    paint
      ..color = Colors.white.withOpacity(opacity)
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(AnimatedBackgroundPainter oldDelegate) {
    return animation != oldDelegate.animation || isDark != oldDelegate.isDark;
  }
} 