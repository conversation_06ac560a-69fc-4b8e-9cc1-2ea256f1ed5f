import 'package:flutter/material.dart';
import '../../../../models/pin_skin.dart';
import '../../constants/skin_screen_constants.dart';
import '../shared/rarity_badge.dart';

class FeaturedCarousel extends StatefulWidget {
  final List<PinSkin> skins;
  final Function(PinSkin) onSkinTap;
  final bool isLoading;
  
  const FeaturedCarousel({
    Key? key,
    required this.skins,
    required this.onSkinTap,
    this.isLoading = false,
  }) : super(key: key);

  @override
  State<FeaturedCarousel> createState() => _FeaturedCarouselState();
}

class _FeaturedCarouselState extends State<FeaturedCarousel> {
  late final PageController _pageController;
  int _currentPage = 0;
  
  @override
  void initState() {
    super.initState();
    _pageController = PageController(
      viewportFraction: 0.85,
      initialPage: _currentPage,
    );
  }
  
  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingCarousel();
    }
    
    if (widget.skins.isEmpty) {
      return _buildEmptyState();
    }
    
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() => _currentPage = index);
        },
        itemCount: widget.skins.length,
        itemBuilder: (context, index) {
          final skin = widget.skins[index];
          return _FeaturedCard(
            skin: skin,
            onTap: () => widget.onSkinTap(skin),
            isActive: index == _currentPage,
          );
        },
      ),
    );
  }
  
  Widget _buildLoadingCarousel() {
    final theme = Theme.of(context);
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 24),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation(theme.colorScheme.primary),
          ),
        ),
      ),
    );
  }
  
  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 24),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.style_outlined,
              size: 48,
              color: theme.colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              SkinScreenConstants.noFeaturedSkins,
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _FeaturedCard extends StatelessWidget {
  final PinSkin skin;
  final VoidCallback onTap;
  final bool isActive;
  
  const _FeaturedCard({
    Key? key,
    required this.skin,
    required this.onTap,
    this.isActive = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedScale(
      scale: isActive ? 1.0 : 0.9,
      duration: SkinScreenConstants.carouselTransitionDuration,
      child: AnimatedOpacity(
        opacity: isActive ? 1.0 : 0.5,
        duration: SkinScreenConstants.carouselTransitionDuration,
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              color: theme.cardColor,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: theme.shadowColor.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: _buildCardContent(context),
          ),
        ),
      ),
    );
  }

  Widget _buildCardContent(BuildContext context) {
    final theme = Theme.of(context);
    
    return Stack(
      children: [
        // Background with gradient
        _buildBackground(theme),
        
        // Pin image
        _buildPinImage(),
        
        // Content overlay
        _buildContentOverlay(theme),
      ],
    );
  }

  Widget _buildBackground(ThemeData theme) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(24),
      child: Container(
        color: theme.colorScheme.primary.withOpacity(0.1),
        child: Stack(
          children: [
            // Gradient background
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    theme.colorScheme.primary.withOpacity(0.3),
                    theme.colorScheme.secondary.withOpacity(0.2),
                  ],
                ),
              ),
            ),
            // Gradient overlay for text contrast
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                  stops: const [0.3, 1.0],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPinImage() {
    return Positioned(
      right: 24,
      bottom: 24,
      child: Image.asset(
        "assets/images/pins/default_pin.png",
        width: 100,
        height: 100,
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _buildContentOverlay(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Removed limited time badge
          
          const Spacer(),
          
          // Skin info (bottom section)
          _buildSkinInfo(theme),
        ],
      ),
    );
  }

  Widget _buildSkinInfo(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Rarity badge
        RarityBadge(
          rarity: SkinRarity.legendary,
          size: 32,
        ),
        const SizedBox(height: 8),
        
        // Name
        Text(
          skin.name,
          style: theme.textTheme.headlineSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        // Description (if available)
        if (skin.description != null) ...[
          const SizedBox(height: 4),
          Text(
            skin.description!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.white.withOpacity(0.7),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }
}

// Keeping PageIndicator just in case it's needed later
class _PageIndicator extends StatelessWidget {
  final bool isActive;
  final VoidCallback? onTap;
  
  const _PageIndicator({
    Key? key,
    this.isActive = false,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: isActive ? 24 : 8,
        height: 8,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: isActive
              ? theme.colorScheme.primary
              : theme.colorScheme.primary.withOpacity(0.2),
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }
} 