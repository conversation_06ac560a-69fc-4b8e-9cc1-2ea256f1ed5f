import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../models/pin_skin.dart';
import '../../../../widgets/common/pin_preview_widget.dart';

class FriendsTabContent extends StatelessWidget {
  final Function(PinSkin) onSkinTap;
  final VoidCallback onFriendTap;
  final List<PinSkin> trendingSkins;

  const FriendsTabContent({
    Key? key,
    required this.onSkinTap,
    required this.onFriendTap,
    required this.trendingSkins,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return CustomScrollView(
      slivers: [
        // Friends Collections Showcase
        SliverToBoxAdapter(
          child: _buildFriendsShowcase(context, isDark),
        ),
        
        // Popular in Area
        SliverToBoxAdapter(
          child: _buildPopularInAreaSection(context, isDark),
        ),
        
        // Community Activity
        SliverToBoxAdapter(
          child: _buildCommunityActivitySection(context, isDark),
        ),
        
        // Trending Skins Grid
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.85,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final allTrendingSkins = _getAllTrendingSkins();
                if (index < allTrendingSkins.length) {
                  return _buildGlassmorphismSkinCard(
                    context,
                    skin: allTrendingSkins[index],
                    index: index,
                    isDark: isDark,
                  );
                }
                return null;
              },
              childCount: _getAllTrendingSkins().length,
            ),
          ),
        ),
        
        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }

  // Friends Showcase with glassmorphism
  Widget _buildFriendsShowcase(BuildContext context, bool isDark) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Friends info card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue.withOpacity(isDark ? 0.3 : 0.2),
                  Colors.purple.withOpacity(isDark ? 0.2 : 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.people_alt_rounded,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Friends Collections',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'See what your friends are using',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: onFriendTap,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.blue.withOpacity(0.4),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'CONNECT',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Friends horizontal scroll
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: 4, // Mock friends
              itemBuilder: (context, index) => _buildFriendCard(context, index: index, isDark: isDark),
            ),
          ),
        ],
      ),
    );
  }

  // Friend Card
  Widget _buildFriendCard(BuildContext context, {required int index, required bool isDark}) {
    final gradientColors = _getFriendGradient(index, isDark);
    final mockNames = ['Alex', 'Jordan', 'Casey', 'Taylor'];
    final mockSkins = ['Crystal Pin', 'Golden Beat', 'Neon Pulse', 'Digital Wave'];
    
    return Container(
      width: 140,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
        ),
        border: Border.all(
          color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: gradientColors.first.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onFriendTap,
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Avatar
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withOpacity(0.2),
                          Colors.white.withOpacity(0.1),
                        ],
                      ),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.person,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Name
                  Text(
                    mockNames[index],
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // Currently using
                  Text(
                    'Using ${mockSkins[index]}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.8),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Popular in Area Section
  Widget _buildPopularInAreaSection(BuildContext context, bool isDark) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Section info card
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.green.withOpacity(isDark ? 0.3 : 0.2),
                  Colors.teal.withOpacity(isDark ? 0.2 : 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.location_on_rounded,
                    color: Colors.green,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Popular in Your Area',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'Cape Town, South Africa',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.green.withOpacity(0.4),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    'HOT',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Popular skins horizontal scroll
          SizedBox(
            height: 160,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: 3,
              itemBuilder: (context, index) => _buildPopularSkinCard(context, index: index, isDark: isDark),
            ),
          ),
        ],
      ),
    );
  }

  // Popular Skin Card
  Widget _buildPopularSkinCard(BuildContext context, {required int index, required bool isDark}) {
    final mockSkin = _getPlaceholderSkin(index + 30);
    final gradientColors = [
      Colors.green.withOpacity(isDark ? 0.4 : 0.3),
      Colors.teal.withOpacity(isDark ? 0.2 : 0.15),
      Colors.green.withOpacity(isDark ? 0.1 : 0.05),
    ];
    
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
        ),
        border: Border.all(
          color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.2),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => onSkinTap(mockSkin),
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                children: [
                  // Pin preview
                  Expanded(
                    child: Center(
                      child: Container(
                        width: 50,
                        height: 70,
                        child: PinPreviewWidget(
                          skin: mockSkin,
                          size: 50,
                          onTap: () => onSkinTap(mockSkin),
                        ),
                      ),
                    ),
                  ),
                  
                  // Skin name
                  Text(
                    mockSkin.name,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // Usage stats
                  Text(
                    '${15 + index * 3} using',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Community Activity Section
  Widget _buildCommunityActivitySection(BuildContext context, bool isDark) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Section info card
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.orange.withOpacity(isDark ? 0.3 : 0.2),
                  Colors.pink.withOpacity(isDark ? 0.2 : 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.trending_up_rounded,
                    color: Colors.orange,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Community Activity',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'Latest trends and updates',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.orange.withOpacity(0.4),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    'LIVE',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Activity items
          ...List.generate(3, (index) => _buildActivityItem(context, index: index, isDark: isDark)),
        ],
      ),
    );
  }

  // Activity Item
  Widget _buildActivityItem(BuildContext context, {required int index, required bool isDark}) {
    final activities = [
      {'user': 'Alex', 'action': 'unlocked', 'skin': 'Crystal Pin', 'time': '2m ago'},
      {'user': 'Jordan', 'action': 'equipped', 'skin': 'Golden Beat', 'time': '5m ago'},
      {'user': 'Casey', 'action': 'shared', 'skin': 'Neon Pulse', 'time': '8m ago'},
    ];
    
    final activity = activities[index];
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(isDark ? 0.05 : 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  Colors.orange.withOpacity(0.3),
                  Colors.pink.withOpacity(0.2),
                ],
              ),
            ),
            child: Icon(
              Icons.person,
              color: Colors.white,
              size: 16,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Activity text
          Expanded(
            child: RichText(
              text: TextSpan(
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
                children: [
                  TextSpan(
                    text: activity['user'],
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  TextSpan(text: ' ${activity['action']} '),
                  TextSpan(
                    text: activity['skin'],
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                ],
              ),
            ),
          ),
          
          // Time
          Text(
            activity['time']!,
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }

  // Glassmorphism Trending Skin Card
  Widget _buildGlassmorphismSkinCard(
    BuildContext context, {
    required PinSkin skin,
    required int index,
    required bool isDark,
  }) {
    final theme = Theme.of(context);
    final gradientColors = _getCardGradient(index, isDark);
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
        ),
        border: Border.all(
          color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: gradientColors.first.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 6),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.3 : 0.08),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // Background blur effect
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(isDark ? 0.03 : 0.08),
                      Colors.white.withOpacity(isDark ? 0.01 : 0.03),
                    ],
                  ),
                ),
              ),
            ),
            
            // Content
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => onSkinTap(skin),
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Lock icon in top-right
                      if (skin.isPremium && !skin.isUnlocked)
                        Align(
                          alignment: Alignment.topRight,
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.amber.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.amber.withOpacity(0.4),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.lock_rounded,
                              size: 16,
                              color: Colors.amber,
                            ),
                          ),
                        ),
                      
                      // Pin preview
                      Expanded(
                        child: Center(
                          child: Stack(
                            children: [
                              // Glow effect
                              Container(
                                width: 60,
                                height: 80,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: gradientColors.first.withOpacity(0.5),
                                      blurRadius: 20,
                                      spreadRadius: 5,
                                    ),
                                  ],
                                ),
                              ),
                              // Pin widget
                              Container(
                                width: 60,
                                height: 80,
                                child: PinPreviewWidget(
                                  skin: skin,
                                  size: 60,
                                  onTap: () => onSkinTap(skin),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Skin info
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            skin.name,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: _getSkinTypeColor(skin.skinType).withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: _getSkinTypeColor(skin.skinType).withOpacity(0.4),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  skin.skinType,
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: _getSkinTypeColor(skin.skinType),
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.pink.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.pink.withOpacity(0.4),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  'TRENDING',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.pink,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  List<PinSkin> _getAllTrendingSkins() {
    if (trendingSkins.isNotEmpty) {
      return trendingSkins;
    }
    
    return List.generate(6, (i) => _getPlaceholderSkin(i + 40));
  }

  PinSkin _getPlaceholderSkin(int index) {
    final names = [
      "Trending Beat",
      "Popular Pulse", 
      "Hot Drop",
      "Viral Vibe",
      "Buzz Pin",
      "Trend Wave"
    ];
    
    return PinSkin(
      id: index,
      name: names[index % names.length],
      description: 'Trending skin in the community',
      image: '',
      isPremium: index % 3 == 0,
      isUnlocked: index % 2 == 0,
      createdAt: DateTime.now(),
    );
  }

  List<Color> _getFriendGradient(int index, bool isDark) {
    final gradients = [
      [Colors.blue, Colors.purple],
      [Colors.green, Colors.teal],
      [Colors.orange, Colors.red],
      [Colors.purple, Colors.pink],
    ];
    
    final colors = gradients[index % gradients.length];
    return [
      colors[0].withOpacity(isDark ? 0.4 : 0.3),
      colors[1].withOpacity(isDark ? 0.2 : 0.15),
      colors[0].withOpacity(isDark ? 0.1 : 0.05),
    ];
  }

  List<Color> _getCardGradient(int index, bool isDark) {
    final gradients = [
      [Colors.blue, Colors.indigo],
      [Colors.purple, Colors.deepPurple],
      [Colors.teal, Colors.cyan],
      [Colors.orange, Colors.deepOrange],
      [Colors.pink, Colors.red],
      [Colors.green, Colors.lightGreen],
    ];
    
    final colors = gradients[index % gradients.length];
    return [
      colors[0].withOpacity(isDark ? 0.4 : 0.3),
      colors[1].withOpacity(isDark ? 0.2 : 0.15),
      colors[0].withOpacity(isDark ? 0.1 : 0.05),
    ];
  }

  Color _getSkinTypeColor(String skinType) {
    switch (skinType) {
      case 'ARTIST':
        return Colors.purple;
      case 'HOUSE':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
} 