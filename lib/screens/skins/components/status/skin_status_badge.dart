import 'package:flutter/material.dart';
import '../../constants/skin_screen_constants.dart';

enum SkinStatus {
  locked,
  unlocked,
  equipped,
}

class SkinStatusBadge extends StatelessWidget {
  final SkinStatus status;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? size;
  
  const SkinStatusBadge({
    Key? key,
    required this.status,
    this.backgroundColor,
    this.iconColor,
    this.size,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final actualSize = size ?? 32.0;
    
    IconData statusIcon;
    Color defaultBgColor;
    Color defaultIconColor;
    
    switch (status) {
      case SkinStatus.locked:
        statusIcon = SkinScreenConstants.lockedIcon;
        defaultBgColor = theme.colorScheme.surface.withOpacity(0.9);
        defaultIconColor = theme.colorScheme.onSurface.withOpacity(0.5);
        break;
      case SkinStatus.unlocked:
        statusIcon = SkinScreenConstants.unlockedIcon;
        defaultBgColor = theme.colorScheme.primary.withOpacity(0.2);
        defaultIconColor = theme.colorScheme.primary;
        break;
      case SkinStatus.equipped:
        statusIcon = SkinScreenConstants.equippedIcon;
        defaultBgColor = theme.colorScheme.primary;
        defaultIconColor = theme.colorScheme.onPrimary;
        break;
    }
    
    return Container(
      width: actualSize,
      height: actualSize,
      decoration: BoxDecoration(
        color: backgroundColor ?? defaultBgColor,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Icon(
          statusIcon,
          color: iconColor ?? defaultIconColor,
          size: actualSize * 0.6,
        ),
      ),
    );
  }
} 