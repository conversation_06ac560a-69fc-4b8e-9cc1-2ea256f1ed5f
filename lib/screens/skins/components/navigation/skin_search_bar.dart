import 'package:flutter/material.dart';
import 'dart:async';
import '../../constants/skin_screen_constants.dart';

class SkinSearchBar extends StatefulWidget {
  final Function(String) onSearch;
  final String? initialQuery;
  final bool autofocus;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  
  const SkinSearchBar({
    Key? key,
    required this.onSearch,
    this.initialQuery,
    this.autofocus = false,
    this.textStyle,
    this.backgroundColor,
    this.borderRadius,
  }) : super(key: key);

  @override
  State<SkinSearchBar> createState() => _SkinSearchBarState();
}

class _SkinSearchBarState extends State<SkinSearchBar> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;
  Timer? _debounceTimer;
  bool _hasFocus = false;
  
  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
    _focusNode = FocusNode();
    
    _focusNode.addListener(() {
      setState(() {
        _hasFocus = _focusNode.hasFocus;
      });
    });
  }
  
  @override
  void dispose() {
    _debounceTimer?.cancel();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }
  
  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(
      SkinScreenConstants.searchDebounceDelay,
      () => widget.onSearch(query),
    );
  }
  
  void _clearSearch() {
    _controller.clear();
    widget.onSearch('');
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final actualBorderRadius = widget.borderRadius ?? 
        BorderRadius.circular(SkinScreenConstants.cardBorderRadius);
    
    return AnimatedContainer(
      duration: SkinScreenConstants.cardHoverDuration,
      height: SkinScreenConstants.searchBarHeight,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? theme.cardColor,
        borderRadius: actualBorderRadius,
        border: Border.all(
          color: _hasFocus
              ? theme.colorScheme.primary
              : theme.dividerColor.withOpacity(0.1),
          width: _hasFocus ? 2 : 1,
        ),
        boxShadow: [
          if (_hasFocus)
            BoxShadow(
              color: theme.colorScheme.primary.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        style: widget.textStyle ?? theme.textTheme.bodyLarge,
        decoration: InputDecoration(
          hintText: SkinScreenConstants.searchHint,
          hintStyle: widget.textStyle?.copyWith(
            color: theme.hintColor,
          ) ?? theme.textTheme.bodyLarge?.copyWith(
            color: theme.hintColor,
          ),
          prefixIcon: Icon(
            SkinScreenConstants.searchIcon,
            color: _hasFocus
                ? theme.colorScheme.primary
                : theme.iconTheme.color?.withOpacity(0.5),
          ),
          suffixIcon: _controller.text.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    SkinScreenConstants.clearIcon,
                    color: theme.iconTheme.color?.withOpacity(0.5),
                  ),
                  onPressed: _clearSearch,
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onChanged: _onSearchChanged,
        textInputAction: TextInputAction.search,
        onSubmitted: widget.onSearch,
      ),
    );
  }
} 