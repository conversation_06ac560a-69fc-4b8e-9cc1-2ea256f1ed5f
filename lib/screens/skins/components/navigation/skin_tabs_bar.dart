import 'package:flutter/material.dart';

class SkinTabsBar extends StatelessWidget implements PreferredSizeWidget {
  final TabController tabController;
  
  const SkinTabsBar({
    Key? key,
    required this.tabController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    
    return SizedBox(
      height: 48,
      child: TabBar(
        controller: tabController,
        labelColor: theme.colorScheme.primary,
        unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.5),
        labelStyle: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: screenWidth < 360 ? 11 : 12,
          letterSpacing: -0.3,
        ),
        unselectedLabelStyle: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: screenWidth < 360 ? 11 : 12,
          letterSpacing: -0.3,
        ),
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: theme.colorScheme.primary.withOpacity(0.1),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        labelPadding: EdgeInsets.symmetric(
          horizontal: screenWidth < 360 ? 4 : 6,
          vertical: 0,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: screenWidth < 360 ? 4 : 8,
          vertical: 8,
        ),
        dividerColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        overlayColor: MaterialStateProperty.resolveWith<Color?>(
          (Set<MaterialState> states) {
            return states.contains(MaterialState.focused) ? null : Colors.transparent;
          },
        ),
        tabs: [
          _buildTab(Icons.grid_view_rounded, 'Gallery', screenWidth < 360 ? 16 : 18),
          _buildTab(Icons.star_rounded, 'Featured', screenWidth < 360 ? 16 : 18),
        ],
      ),
    );
  }

  Widget _buildTab(IconData icon, String text, double iconSize) {
    return Tab(
      height: 32,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: iconSize),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                text,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);
} 