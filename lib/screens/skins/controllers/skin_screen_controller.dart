import 'package:flutter/material.dart';
import '../../../providers/gamification_provider.dart';

class SkinScreenController extends ChangeNotifier {
  final GamificationProvider gamificationProvider;
  
  // State
  String _searchQuery = '';
  int _selectedTabIndex = 0;
  bool _isLoading = false;
  String? _error;
  String _currentFilter = 'All';
  
  // Getters
  String get searchQuery => _searchQuery;
  int get selectedTabIndex => _selectedTabIndex;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get currentFilter => _currentFilter;
  
  SkinScreenController(this.gamificationProvider, {bool autoLoad = true}) {
    if (autoLoad) {
      loadData();
    }
  }
  
  Future<void> loadData() async {
    if (_isLoading) return;
    _setLoading(true);
    try {
      await gamificationProvider.loadPinSkins();
      await gamificationProvider.loadUnlockedSkins();
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }
  
  void updateSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }
  
  void setSelectedTab(int index) {
    _selectedTabIndex = index;
    notifyListeners();
  }

  void setCurrentFilter(String filter) {
    _currentFilter = filter;
    notifyListeners();
  }
  
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }
  
  void clearError() {
    _error = null;
    notifyListeners();
  }
  
  Future<void> refresh() async {
    await loadData();
  }
} 