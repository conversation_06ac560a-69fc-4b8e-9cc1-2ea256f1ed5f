import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import '../../config/themes.dart';
import '../../widgets/music/track_selector.dart';
import '../../widgets/animations/fade_in_animation.dart';
import '../../services/music/spotify_auth_service.dart';
import '../../providers/spotify_provider.dart';
import '../../models/music_track.dart';
import 'package:flutter/foundation.dart';
import '../../providers/map_provider.dart';
import '../../screens/ar/ar_pin_placement_screen.dart';
import '../../screens/map/snapchat_style_map_screen.dart';
import '../../services/ai/global_ai_provider_service.dart';

/// A specialized track selection screen for pin creation flow
/// This screen is optimized for all device sizes and provides a consistent UI/UX
class CreatePinTrackSelectScreen extends StatefulWidget {
  const CreatePinTrackSelectScreen({Key? key}) : super(key: key);

  @override
  State<CreatePinTrackSelectScreen> createState() => _CreatePinTrackSelectScreenState();
}

class _CreatePinTrackSelectScreenState extends State<CreatePinTrackSelectScreen> with SingleTickerProviderStateMixin {
  bool isSpotifyConnected = false;
  bool isLoading = true;
  bool _isSearchExpanded = false;
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _animationController;

  // Key to force TrackSelector rebuild after shuffle
  Key _trackSelectorKey = UniqueKey();


  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    // Start the animation at 0.7 to ensure it's never completely invisible
    // and forward to fully visible
    _animationController.value = 0.7;
    _checkSpotifyConnection();
    
    // Add system UI styling for a more immersive experience
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light, // Change to light for dark AppBar
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }
  
  Future<void> _checkSpotifyConnection() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      // Get the spotify provider
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // Check existing connection state
      final connected = spotifyProvider.isConnected;
      
      setState(() {
        isSpotifyConnected = connected;
        isLoading = false;
      });
      
      if (connected) {
        // Start loading animation
        _animationController.forward();
        
        // Preload data for a better experience
        spotifyProvider.loadLikedSongs();
      } else {
        // Not connected but still allow track selection using backend client credentials
        print('⚠️ Spotify not connected - using backend client credentials for track search');
        
        // Still start the animation since we can search tracks
        _animationController.forward();
      }
    } catch (e) {
      print('❌ Error checking Spotify connection: $e');
      setState(() {
        isSpotifyConnected = false;
        isLoading = false;
      });
      
      // Still allow track selection even if there's an error
      _animationController.forward();
    }
  }
  
  Future<void> _connectToSpotify() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      // Haptic feedback
      HapticFeedback.mediumImpact();
      
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // Check if we're using prioritized production auth
      final isPrioritizingProduction = spotifyProvider.prioritizeProductionAuth;
      
      // Add a message about which authentication mode is being used
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isPrioritizingProduction
                ? 'Connecting to Spotify (prioritizing real authentication)...'
                : 'Connecting to Spotify...'
          ),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
      
      // Connect to Spotify
      final connected = await spotifyProvider.connect();
      
      setState(() {
        isSpotifyConnected = connected;
        isLoading = false;
      });
      
      if (!connected) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isPrioritizingProduction && kDebugMode
                  ? 'Could not connect to real Spotify. Using debug tokens instead.'
                  : 'Failed to connect to Spotify'
            ),
            backgroundColor: isPrioritizingProduction && kDebugMode ? Colors.orange : Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
        
        // Even if real authentication failed, in debug mode with prioritizeProductionAuth
        // we might still get debug tokens, so check connection again
        if (kDebugMode && isPrioritizingProduction) {
          // Small delay to allow debug tokens to be created
          await Future.delayed(const Duration(milliseconds: 300));
          
          // Check if we now have debug tokens
          final isConnectedWithDebug = spotifyProvider.isConnected;
          
          setState(() {
            isSpotifyConnected = isConnectedWithDebug;
          });
          
          if (isConnectedWithDebug) {
            print('✅ Connected with debug tokens after real auth failed');
            
            // Show appropriate message
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Connected with debug tokens'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
            
            // Load user's music
            spotifyProvider.loadLikedSongs();
            
            // Start loading animation
            _animationController.forward();
          }
        }
      } else {
        // Successfully connected - show appropriate message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isPrioritizingProduction
                  ? 'Successfully connected to Spotify!'
                  : kDebugMode 
                      ? 'Connected with debug tokens'
                      : 'Connected to Spotify!'
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
        
        // Now preload the user's liked songs
        spotifyProvider.loadLikedSongs();
        
        // Start loading animation
        _animationController.forward();
      }
    } catch (e) {
      print('❌ Error connecting to Spotify: $e');
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error connecting to Spotify: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive layout
    final screenSize = MediaQuery.of(context).size;
    final isSmallDevice = screenSize.width < 360 || screenSize.height < 600;
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface, // Match profile screen background
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.primary,
        elevation: 1.0,
        leadingWidth: 42, // Make leading smaller to fit more title space
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 22),
          onPressed: () => Navigator.pop(context),
          padding: const EdgeInsets.only(left: 8.0),
        ),
        title: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Select Your Track',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18, // Slightly smaller on smaller devices
              ),
            ),
            Text(
              'Step 1 of 2 - Choose Music',
              style: TextStyle(
                fontSize: isSmallDevice ? 10 : 12,
                color: Colors.white.withOpacity(0.9),
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.casino, color: Colors.white),
            tooltip: 'Shuffle suggested songs',
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(), // Tighter constraints for more title space
            onPressed: () async {
              // Light haptic feedback
              HapticFeedback.selectionClick();

              // Shuffle cached AI-suggested songs from GlobalAIProviderService
              final globalAIProvider = GlobalAIProviderService.instance;

              if (globalAIProvider.isInitialized && globalAIProvider.aiProvider != null) {
                final cachedArtistTracks = globalAIProvider.aiProvider!.categorizedRecommendations['artistBased'];

                if (cachedArtistTracks != null && cachedArtistTracks.isNotEmpty) {
                  // Shuffle the cached recommendations instantly
                  final shuffledTracks = List<MusicTrack>.from(cachedArtistTracks);
                  shuffledTracks.shuffle();

                  // Update the categorized recommendations
                  globalAIProvider.aiProvider!.categorizedRecommendations['artistBased'] = shuffledTracks;

                  // Force TrackSelector to rebuild by generating a new key
                  setState(() {
                    _trackSelectorKey = UniqueKey();
                  });

                  // Check if widget is still mounted before showing SnackBar
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Shuffled suggested songs!'),
                        duration: Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                } else {
                  // Fallback: refresh connection if no cached tracks available
                  _checkSpotifyConnection();

                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Loading suggested songs...'),
                        duration: Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                }
              } else {
                // Fallback: refresh connection if AI provider not initialized
                _checkSpotifyConnection();

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Loading suggested songs...'),
                      duration: Duration(seconds: 1),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                }
              }
            },
          ),
          // Add extra padding to the end
          const SizedBox(width: 4),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Track selector with improved visibility - Always show for all users
            Expanded(
              child: Container(
                height: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  // Add subtle shadow at top for visual separation
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      offset: const Offset(0, -1),
                      blurRadius: 3,
                    ),
                  ],
                ),
                child: AnimatedOpacity(
                  opacity: 1.0, // Always fully visible
                  duration: const Duration(milliseconds: 300),
                                      child: TrackSelector(
                      key: _trackSelectorKey,
                      onTrackSelected: (track) {
                        // Enhanced feedback with both haptic and visual effects
                        HapticFeedback.mediumImpact();
                        
                        // Get current location for AR
                        final mapProvider = Provider.of<MapProvider>(context, listen: false);
                        final currentPos = mapProvider.currentPosition;
                        
                        // Navigate directly to AR screen
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            fullscreenDialog: true,
                            builder: (context) => ARPinPlacementScreen( 
                              initialLatitude: currentPos?.latitude,
                              initialLongitude: currentPos?.longitude,
                              selectedTrack: track,
                            ),
                          ),
                        ).then((success) {
                          if (success == true) {
                            // New pin added successfully! SmartLocationFetchManager will detect it automatically
                            // No need to clear or rebuild anything - the pin will appear with natural animation
                            debugPrint('🎉 AR Pin created successfully - SmartLocationFetchManager will detect and add it automatically');
                            
                            // Close the track selection screen
                            Navigator.pop(context);
                            
                            // Show success message
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('🎵 AR Pin dropped successfully! Your music is now on the map! 🎉'),
                                duration: Duration(seconds: 4),
                                behavior: SnackBarBehavior.floating,
                              ),
                            );
                          }
                        });
                      },
                      showSearchBar: true,
                      showLikedSongs: isSpotifyConnected, // Only show Spotify features if connected
                      showRecentlyPlayed: isSpotifyConnected, // Only show Spotify features if connected
                      showTopTracks: isSpotifyConnected, // Only show Spotify features if connected
                    ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.music_note, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 12),
            const Text('Creating a Music Pin'),
          ],
        ),
        content: Text(
          isSpotifyConnected 
            ? 'To create a music pin, first select a track from your Spotify library. Then you\'ll take a photo to complete your pin. The pin will appear on the map for others to discover and listen to your music selection.'
            : 'Search for any track to create a music pin! You can search for songs, artists, or albums without needing a Spotify connection. Then you\'ll take a photo to complete your pin. The pin will appear on the map for others to discover.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
  
  void _showNoSpotifyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('Enhanced Music Search'),
        content: const Text(
          'Good news! You can now search for any track to create music pins without needing a Spotify connection. Our enhanced search works for all users!\n\nSimply use the search bar to find your favorite songs and create pins on the map.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Great!'),
          ),
        ],
      ),
    );
  }

  /// Test method to verify Apple Music only users can access track selection
  void _testAppleMusicOnlyAccess() {
    print('🎵 [CREATE PIN] Track selection now available for Apple Music only users!');
    print('🔍 [CREATE PIN] Search functionality works without Spotify connection');
    print('✅ [CREATE PIN] Backend client credentials provide track search for all users');
  }
} 