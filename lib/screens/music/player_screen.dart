import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../providers/spotify_provider.dart';
import '../../models/music_track.dart';
import 'dart:ui';

class PlayerScreen extends StatefulWidget {
  final MusicTrack? track;
  
  const PlayerScreen({
    Key? key,
    this.track,
  }) : super(key: key);

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isDragging = false;
  double _dragValue = 0.0;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    );
    
    // Start animation if already playing
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      if (spotifyProvider.isPlaying) {
        _animationController.repeat();
      }
    });
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleAddToCollection(BuildContext context, MusicTrack? track) {
    if (track == null) return;
    final trackTitle = track.title;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Adding $trackTitle to collection (soon)!')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SpotifyProvider>(
      builder: (context, spotifyProvider, child) {
        final currentTrack = spotifyProvider.currentTrack ?? widget.track;
        
        // Update animation based on playback state
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (spotifyProvider.isPlaying && !_animationController.isAnimating) {
            _animationController.repeat();
          } else if (!spotifyProvider.isPlaying && _animationController.isAnimating) {
            _animationController.stop();
          }
        });
        
        if (currentTrack == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Now Playing'),
            ),
            body: const Center(
              child: Text('No track selected'),
            ),
          );
        }
        
        return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
            title: const Text(
              'Now Playing',
              style: TextStyle(color: Colors.white),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.more_vert, color: Colors.white),
                onPressed: () => _showMoreOptions(currentTrack),
              ),
            ],
          ),
          body: Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary.withOpacity(0.8),
                  Theme.of(context).colorScheme.secondary,
                ],
              ),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Spacer(),
                      
                      // Album Art
                      _buildAlbumArt(currentTrack, spotifyProvider.isPlaying),
                      
                      const Spacer(),
                      
                      // Track Info
                      Text(
                        currentTrack.title,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        currentTrack.artist,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white.withOpacity(0.8),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 32),
                      
                      // Progress bar
                      _buildProgressBar(spotifyProvider),
                      
                      const SizedBox(height: 32),
                      
                      // Controls
                      _buildControls(spotifyProvider),
                      
                      const Spacer(),
                      
                      // Additional info and actions
                      _buildBottomActions(currentTrack),
                      
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildAlbumArt(MusicTrack track, bool isPlaying) {
    // Convert Spotify URI to HTTP URL if needed
    String imageUrl = track.albumArt;
    if (imageUrl.startsWith('spotify:image:')) {
      final imageId = imageUrl.split(':').last;
      imageUrl = 'https://i.scdn.co/image/$imageId';
    }
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (_, child) {
        return Transform.rotate(
          angle: isPlaying ? _animationController.value * 2 * 3.14159 : 0,
          child: child,
        );
      },
      child: Container(
        width: 280,
        height: 280,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: ClipOval(
          child: imageUrl.isNotEmpty
              ? Image.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    if (kDebugMode) {
                      print('Error loading album art: $error');
                    }
                    return Container(
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.music_note,
                        size: 100,
                        color: Colors.grey,
                      ),
                    );
                  },
                )
              : Container(
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.music_note,
                    size: 100,
                    color: Colors.grey,
                  ),
                ),
        ),
      ),
    );
  }
  
  Widget _buildProgressBar(SpotifyProvider provider) {
    return StreamBuilder<int>(
      stream: provider.positionStream,
      builder: (context, snapshot) {
        final position = _isDragging ? _dragValue.round() : (snapshot.data ?? provider.position);
        final duration = provider.duration;
        final progress = duration > 0 ? position / duration : 0.0;
        
        return Column(
          children: [
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Colors.white,
                inactiveTrackColor: Colors.white.withOpacity(0.3),
                thumbColor: Colors.white,
                trackHeight: 3.0,
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8.0),
                overlayShape: const RoundSliderOverlayShape(overlayRadius: 16.0),
              ),
              child: Slider(
                value: progress.clamp(0.0, 1.0),
                onChanged: (value) {
                  setState(() {
                    _isDragging = true;
                    _dragValue = value * duration;
                  });
                },
                onChangeEnd: (value) async {
                  final newPosition = (value * duration).round();
                  await provider.seekTo(newPosition);
                  setState(() {
                    _isDragging = false;
                  });
                },
                min: 0.0,
                max: 1.0,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _formatDuration(Duration(milliseconds: position)),
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    _formatDuration(Duration(milliseconds: duration)),
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
  
  Widget _buildControls(SpotifyProvider provider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: const Icon(Icons.skip_previous, color: Colors.white, size: 32),
          onPressed: provider.hasActivePlayback ? () async {
            await provider.skipPrevious();
          } : null,
        ),
        const SizedBox(width: 16),
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: IconButton(
            icon: Icon(
              provider.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Theme.of(context).colorScheme.primary,
              size: 36,
            ),
            onPressed: provider.hasActivePlayback ? () async {
              if (provider.isPlaying) {
                await provider.pause();
              } else {
                await provider.resume();
              }
            } : null,
          ),
        ),
        const SizedBox(width: 16),
        IconButton(
          icon: const Icon(Icons.skip_next, color: Colors.white, size: 32),
          onPressed: provider.hasActivePlayback ? () async {
            await provider.skipNext();
          } : null,
        ),
      ],
    );
  }
  
  Widget _buildBottomActions(MusicTrack track) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(Icons.shuffle, 'Shuffle', () async {
          final provider = Provider.of<SpotifyProvider>(context, listen: false);
          await provider.toggleShuffle();
        }),
        _buildActionButton(Icons.repeat, 'Repeat', () async {
          final provider = Provider.of<SpotifyProvider>(context, listen: false);
          await provider.toggleRepeatMode();
        }),
        _buildActionButton(Icons.playlist_add, 'Add to Collection', () {
          _handleAddToCollection(context, track);
        }),
        _buildActionButton(Icons.share, 'Share', () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Share feature coming soon!')),
          );
        }),
      ],
    );
  }
  
  Widget _buildActionButton(IconData icon, String label, VoidCallback onPressed) {
    return Column(
      children: [
        IconButton(
          icon: Icon(icon, color: Colors.white.withOpacity(0.8)),
          onPressed: onPressed,
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }
  
  void _showMoreOptions(MusicTrack track) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.collections_bookmark_outlined),
              title: const Text('Add to Collection'),
              onTap: () {
                Navigator.pop(context);
                _handleAddToCollection(context, track);
              },
            ),
            ListTile(
              leading: const Icon(Icons.add_to_queue),
              title: const Text('Add to Queue'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Add to queue coming soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.playlist_add),
              title: const Text('Save to Playlist'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Save to playlist coming soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.album),
              title: const Text('View Album'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('View album coming soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('View Artist'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('View artist coming soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Share coming soon')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    final twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return '$twoDigitMinutes:$twoDigitSeconds';
  }
} 