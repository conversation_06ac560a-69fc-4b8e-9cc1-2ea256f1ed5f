import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../config/themes.dart';
import '../../widgets/music/track_selector.dart';
import '../../widgets/animations/fade_in_animation.dart';
import '../../providers/spotify_provider.dart';
import '../../models/music_track.dart';
import '../../models/music/track.dart';
import '../../widgets/music/now_playing_bar.dart';

class TrackSelectScreen extends StatefulWidget {
  const TrackSelectScreen({Key? key}) : super(key: key);

  @override
  State<TrackSelectScreen> createState() => _TrackSelectScreenState();
}

class _TrackSelectScreenState extends State<TrackSelectScreen> with WidgetsBindingObserver {
  bool isSpotifyConnected = false;
  bool isLoading = true;
  
  @override
  void initState() {
    super.initState();
    // Register lifecycle observer
    WidgetsBinding.instance.addObserver(this);
    _checkSpotifyConnection();
  }
  
  @override
  void dispose() {
    // Remove lifecycle observer
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // When app resumes, check Spotify connection and reconnect if needed
    if (state == AppLifecycleState.resumed) {
      print('🎵 Track select screen: App resumed, checking Spotify connection');
      _checkSpotifyConnection();
    }
  }
  
  Future<void> _checkSpotifyConnection() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      // Use the spotify provider instead to check connection status
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      final isConnected = await spotifyProvider.connect();
      
      setState(() {
        isSpotifyConnected = isConnected;
        isLoading = false;
      });
      
      // If not connected, try to check if we should connect automatically
      if (!isConnected) {
        // No longer check with SpotifyAuthService, just rely on SpotifyProvider
      }
    } catch (e) {
      print('Error checking Spotify connection: $e');
      setState(() {
        isSpotifyConnected = false;
        isLoading = false;
      });
    }
  }
  
  Future<void> _connectToSpotify() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      final connected = await spotifyProvider.connect();
      
      setState(() {
        isSpotifyConnected = connected;
        isLoading = false;
      });
      
      if (!connected) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to connect to Spotify'),
            backgroundColor: Colors.red,
          ),
        );
      } else {
        // Now preload the user's liked songs
        spotifyProvider.loadLikedSongs();
      }
    } catch (e) {
      print('Error connecting to Spotify: $e');
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error connecting to Spotify: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  Future<void> _disconnectFromSpotify() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      await spotifyProvider.disconnect();
      
      setState(() {
        isSpotifyConnected = false;
        isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Disconnected from Spotify'),
        ),
      );
    } catch (e) {
      print('Error disconnecting from Spotify: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  void _playTrack(MusicTrack track) {
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    try {
      spotifyProvider.playTrack(track);
    } catch (e) {
      print('Error playing track: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final isPlayingTrack = spotifyProvider.currentTrack != null && spotifyProvider.isConnected;
    
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: const Text('Select a Track'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Refresh tracks and connection status
              _checkSpotifyConnection();
              
              // Also refresh liked songs if connected
              if (isSpotifyConnected) {
                final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
                spotifyProvider.loadLikedSongs(refresh: true);
              }
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Refreshing...'),
                  duration: Duration(seconds: 1),
                ),
              );
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          Column(
            children: [
              // Spotify connection status
              _buildSpotifyConnectionStatus(),
              
              // Track selector (with bottom padding for the now playing bar if visible)
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(bottom: isPlayingTrack ? 72 : 0),
                  child: isSpotifyConnected
                      ? TrackSelector(
                          onTrackSelected: (track) {
                            // Provide haptic feedback
                            HapticFeedback.mediumImpact();
                            
                            // Play the track before returning
                            if (isSpotifyConnected) {
                              _playTrack(track);
                            }
                            
                            // Return selected track to previous screen
                            Navigator.pop(context, {'track': track});
                          },
                          showLikedSongs: true,
                          showRecentlyPlayed: true,
                          showTopTracks: true,
                        )
                      : Center(
                          child: ElevatedButton.icon(
                            onPressed: _connectToSpotify,
                            icon: const Icon(Icons.login),
                            label: const Text('Connect to Spotify'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.accentColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                            ),
                          ),
                        ),
                ),
              ),
            ],
          ),
          
          // Now Playing Bar - only show if there's a track playing
          if (isPlayingTrack)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: NowPlayingBar(),
            ),
        ],
      ),
      bottomSheet: isSpotifyConnected ? _buildConnectBar(context) : null,
    );
  }
  
  Widget _buildSpotifyConnectionStatus() {
    if (isLoading) {
      return FadeInAnimation(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          color: Colors.grey[200],
          child: Row(
            children: [
              SizedBox(
                height: 16,
                width: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppTheme.accentColor),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Checking connection status...',
                style: TextStyle(
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    if (!isSpotifyConnected) {
      return FadeInAnimation(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          color: Colors.grey[200],
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.grey[700],
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'Connect to Spotify to view your tracks',
                style: TextStyle(
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    // Check if the user is premium or not
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    final isPremium = spotifyProvider.isPremiumUser;
    
    return FadeInAnimation(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        color: AppTheme.accentColor.withOpacity(0.1),
        child: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: AppTheme.accentColor,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              'Connected to Spotify${isPremium ? " (Premium)" : ""}',
              style: TextStyle(
                color: AppTheme.accentColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            Consumer<SpotifyProvider>(
              builder: (context, provider, child) {
                // Show currently active tab
                if (provider.likedSongs.isNotEmpty) {
                  return Text(
                    'Liked songs available',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  );
                }
                return Text(
                  'Your music',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                );
              }
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildConnectBar(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 400;
    final fontSize = isSmallScreen ? 12.0 : 14.0;
    
    return FadeInAnimation(
      delay: const Duration(milliseconds: 300),
      offset: const Offset(0, 0.5),
      child: Container(
        padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Spotify button
            Expanded(
              child: isSpotifyConnected
                ? ElevatedButton.icon(
                    onPressed: _disconnectFromSpotify,
                    icon: const Icon(Icons.logout),
                    label: Text(
                      'Disconnect from Spotify',
                      style: TextStyle(fontSize: fontSize),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.red,
                      elevation: 0,
                      side: const BorderSide(color: Colors.red),
                    ),
                  )
                : ElevatedButton.icon(
                    onPressed: _connectToSpotify,
                    icon: Image.asset(
                      'assets/images/icons/spotify_icon.png',
                      width: 18,
                      height: 18,
                    ),
                    label: Text(
                      'Connect to Spotify',
                      style: TextStyle(fontSize: fontSize),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1DB954), // Spotify green
                      foregroundColor: Colors.white,
                    ),
                  ),
            ),
          ],
        ),
      ),
    );
  }
} 