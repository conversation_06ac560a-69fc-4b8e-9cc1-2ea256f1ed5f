import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'package:flutter/services.dart';

import '../../models/music_track.dart';
import '../../providers/music_provider.dart';
import '../../services/ai/music_recommendation_service.dart';
import '../../config/themes.dart';
import '../../widgets/music/track_card.dart';
import '../../widgets/common/shimmer_loading.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/animations/staggered_animation.dart';

/// A beautiful and modern AI Music Recommendations screen
class AIRecommendationsScreen extends StatefulWidget {
  const AIRecommendationsScreen({Key? key}) : super(key: key);

  @override
  State<AIRecommendationsScreen> createState() => _AIRecommendationsScreenState();
}

class _AIRecommendationsScreenState extends State<AIRecommendationsScreen> with SingleTickerProviderStateMixin {
  final MusicRecommendationService _recommendationService = MusicRecommendationService();
  
  // Screen state
  bool _isLoading = true;
  bool _isRefreshing = false;
  bool _hasError = false;
  String? _errorMessage;
  List<MusicRecommendation> _recommendations = [];
  RecommendationCategory _selectedCategory = RecommendationCategory.general;
  
  // Animation controller for the content
  late AnimationController _animationController;
  
  // For shimmer effect
  final int _shimmerItemCount = 6;
  
  // For pull to refresh
  Completer<void> _refreshCompleter = Completer<void>();
  
  // Refresh control
  bool _canRefresh = true;
  Timer? _refreshCooldownTimer;
  
  // Group recommendations by category
  Map<RecommendationCategory, List<MusicRecommendation>> _groupedRecommendations = {};
  
  // Categories to display (ordered)
  final List<RecommendationCategory> _displayCategories = [
    RecommendationCategory.locationBased,
    RecommendationCategory.moodBased,
    RecommendationCategory.timeBased,
    RecommendationCategory.genreBased,
    RecommendationCategory.artistBased,
    RecommendationCategory.activityBased,
  ];
  
  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    // Load recommendations
    _loadRecommendations();
    
    // Refresh every 15 minutes automatically
    Timer.periodic(const Duration(minutes: 15), (_) {
      if (mounted && !_isRefreshing) {
        _refreshRecommendations(showLoading: false);
      }
    });
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    _refreshCooldownTimer?.cancel();
    super.dispose();
  }
  
  Future<void> _loadRecommendations() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });
    
    try {
      // Initialize the service if needed
      if (!_recommendationService.isInitialized) {
        await _recommendationService.initialize();
      }
      
      // Get recommendations
      final recommendations = await _recommendationService.getRecommendations(limit: 20);
      
      if (!mounted) return;
      
      // Group by category
      _groupRecommendations(recommendations);
      
      setState(() {
        _recommendations = recommendations;
        _isLoading = false;
      });
      
      // Play animation
      _animationController.forward();
      
    } catch (e) {
      if (!mounted) return;
      
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Failed to load recommendations: $e';
      });
    }
  }
  
  Future<void> _refreshRecommendations({bool showLoading = true}) async {
    if (!_canRefresh) return;
    
    // Set refresh cooldown
    _canRefresh = false;
    _refreshCooldownTimer = Timer(const Duration(seconds: 10), () {
      _canRefresh = true;
    });
    
    if (showLoading) {
      setState(() {
        _isRefreshing = true;
      });
    }
    
    try {
      // Get fresh recommendations
      final recommendations = await _recommendationService.getRecommendations(
        limit: 20,
        forceRefresh: true,
      );
      
      if (!mounted) return;
      
      // Group by category
      _groupRecommendations(recommendations);
      
      setState(() {
        _recommendations = recommendations;
        _isRefreshing = false;
        _hasError = false;
        _errorMessage = null;
      });
      
      // Reset animation
      _animationController.reset();
      _animationController.forward();
      
    } catch (e) {
      if (!mounted) return;
      
      setState(() {
        _isRefreshing = false;
        _hasError = true;
        _errorMessage = 'Failed to refresh recommendations: $e';
      });
    }
    
    // Complete the refresh completer to stop the refresh indicator
    _refreshCompleter.complete();
    
    // Create a new completer for the next refresh
    if (mounted) {
      setState(() {
        _refreshCompleter = Completer<void>(); // Create a new completer
      });
    }
  }
  
  void _groupRecommendations(List<MusicRecommendation> recommendations) {
    final grouped = <RecommendationCategory, List<MusicRecommendation>>{};
    
    for (final category in RecommendationCategory.values) {
      grouped[category] = [];
    }
    
    for (final recommendation in recommendations) {
      grouped[recommendation.category]!.add(recommendation);
    }
    
    _groupedRecommendations = grouped;
  }
  
  void _selectCategory(RecommendationCategory category) {
    HapticFeedback.lightImpact();
    
    setState(() {
      _selectedCategory = category;
    });
  }
  
  // Helper to get a human-readable category name
  String _getCategoryName(RecommendationCategory category) {
    switch (category) {
      case RecommendationCategory.locationBased:
        return 'Based on Your Location';
      case RecommendationCategory.timeBased:
        return 'Perfect for Now';
      case RecommendationCategory.moodBased:
        return 'Matching Your Mood';
      case RecommendationCategory.weatherBased:
        return 'For Current Weather';
      case RecommendationCategory.activityBased:
        return 'For Your Activity';
      case RecommendationCategory.genreBased:
        return 'Genres You Love';
      case RecommendationCategory.artistBased:
        return 'Artists You Follow';
      case RecommendationCategory.similar:
        return 'Similar Tracks';
      case RecommendationCategory.recentlyPlayed:
        return 'Recently Played';
      case RecommendationCategory.popular:
        return 'Popular Tracks';
      case RecommendationCategory.trending:
        return 'Trending Now';
      case RecommendationCategory.discovery:
        return 'Discover New Music';
      case RecommendationCategory.general:
        return 'For You';
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 200.0,
              floating: false,
              pinned: true,
              elevation: 0,
              stretch: true,
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              flexibleSpace: FlexibleSpaceBar(
                centerTitle: false,
                title: Text(
                  'AI Music Recommendations',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: innerBoxIsScrolled ? Theme.of(context).textTheme.headlineMedium?.color : Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Background gradient
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Theme.of(context).primaryColor,
                            Theme.of(context).scaffoldBackgroundColor,
                          ],
                        ),
                      ),
                    ),
                    
                    // Background patterns
                    Positioned(
                      right: -50,
                      top: -50,
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                    
                    Positioned(
                      left: -30,
                      bottom: 0,
                      child: Container(
                        width: 150,
                        height: 150,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.05),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                    
                    // Content
                    Positioned(
                      left: 16,
                      right: 16,
                      bottom: 60,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Personalized Music',
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'AI-powered recommendations based on your location, time, weather, and preferences',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh recommendations',
                  onPressed: () => _refreshRecommendations(),
                ),
                IconButton(
                  icon: const Icon(Icons.info_outline),
                  tooltip: 'About AI Recommendations',
                  onPressed: _showAboutDialog,
                ),
              ],
            ),
            
            // Category tabs
            SliverPersistentHeader(
              delegate: _CategoryTabBarDelegate(
                selectedCategory: _selectedCategory,
                groupedRecommendations: _groupedRecommendations,
                displayCategories: _displayCategories,
                getCategoryName: _getCategoryName,
                onSelectCategory: _selectCategory,
              ),
              pinned: true,
            ),
          ];
        },
        body: _buildBody(),
      ),
    );
  }
  
  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingView();
    }
    
    if (_hasError) {
      return ErrorView(
        message: _errorMessage ?? 'Failed to load recommendations',
        onRetry: _loadRecommendations,
      );
    }
    
    return RefreshIndicator(
      onRefresh: () {
        _refreshRecommendations();
        return _refreshCompleter.future;
      },
      child: _buildRecommendationsList(),
    );
  }
  
  Widget _buildLoadingView() {
    return ShimmerLoading(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _shimmerItemCount,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Row(
              children: [
                // Album art placeholder
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title placeholder
                      Container(
                        width: double.infinity,
                        height: 16,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Artist placeholder
                      Container(
                        width: 150,
                        height: 12,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Reason placeholder
                      Container(
                        width: 200,
                        height: 10,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildRecommendationsList() {
    final recommendationsToShow = _selectedCategory == RecommendationCategory.general
        ? _recommendations
        : _groupedRecommendations[_selectedCategory] ?? [];
    
    if (recommendationsToShow.isEmpty) {
      return _buildEmptyView();
    }
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: recommendationsToShow.length,
      itemBuilder: (context, index) {
        return StaggeredAnimation(
          index: index,
          controller: _animationController,
          child: _buildRecommendationItem(recommendationsToShow[index]),
        );
      },
    );
  }
  
  Widget _buildRecommendationItem(MusicRecommendation recommendation) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TrackCard(
        track: recommendation.track,
        subtitle: recommendation.reason,
        confidence: recommendation.confidence,
        onTap: () => _playTrack(recommendation.track),
      ),
    );
  }
  
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.playlist_add,
            size: 70,
            color: Theme.of(context).disabledColor,
          ),
          const SizedBox(height: 16),
          Text(
            'No recommendations in this category',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Pull to refresh or try another category',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => _refreshRecommendations(showLoading: true),
            child: const Text('Refresh Recommendations'),
          ),
        ],
      ),
    );
  }
  
  void _playTrack(MusicTrack track) {
    // Get the music provider
    final musicProvider = Provider.of<MusicProvider>(context, listen: false);
    
    // Convert to provider's Track model if needed
    final providerTrack = Track(
      id: track.id,
      title: track.title,
      artist: track.artist,
      albumArtUrl: track.albumArtUrl,
      previewUrl: track.previewUrl ?? '',
    );
    
    // Play the track
    musicProvider.setCurrentTrack(providerTrack);
  }
  
  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About AI Recommendations'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How it works',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Our AI recommendation system analyzes your music preferences, current location, time of day, weather, and activity level to suggest music tailored to your current situation.',
            ),
            const SizedBox(height: 16),
            Text(
              'Categories',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Location-based: Tracks popular in your current area\n'
              '• Time-based: Music that fits the current time of day\n'
              '• Mood-based: Matches your current emotional state\n'
              '• Genre-based: Based on your favorite music genres\n'
              '• Artist-based: Similar to artists you love\n'
              '• Activity-based: Perfect for what you\'re doing now',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CLOSE'),
          ),
        ],
      ),
    );
  }
}

class _CategoryTabBarDelegate extends SliverPersistentHeaderDelegate {
  final RecommendationCategory selectedCategory;
  final Map<RecommendationCategory, List<MusicRecommendation>> groupedRecommendations;
  final List<RecommendationCategory> displayCategories;
  final String Function(RecommendationCategory) getCategoryName;
  final void Function(RecommendationCategory) onSelectCategory;
  
  _CategoryTabBarDelegate({
    required this.selectedCategory,
    required this.groupedRecommendations,
    required this.displayCategories,
    required this.getCategoryName,
    required this.onSelectCategory,
  });
  
  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    // Add "For You" as the first category
    final categories = [RecommendationCategory.general, ...displayCategories];
    
    return Container(
      height: 56,
      color: Theme.of(context).scaffoldBackgroundColor,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = category == selectedCategory;
          final hasRecommendations = category == RecommendationCategory.general || 
                                    (groupedRecommendations[category]?.isNotEmpty ?? false);
          
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: ChoiceChip(
              label: Text(getCategoryName(category)),
              selected: isSelected,
              onSelected: hasRecommendations ? (_) => onSelectCategory(category) : null,
              backgroundColor: Theme.of(context).cardColor,
              selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
              labelStyle: TextStyle(
                color: isSelected 
                    ? Theme.of(context).primaryColor 
                    : hasRecommendations
                        ? Theme.of(context).textTheme.bodyMedium?.color
                        : Theme.of(context).disabledColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          );
        },
      ),
    );
  }
  
  @override
  double get maxExtent => 56;
  
  @override
  double get minExtent => 56;
  
  @override
  bool shouldRebuild(covariant _CategoryTabBarDelegate oldDelegate) {
    return selectedCategory != oldDelegate.selectedCategory ||
           groupedRecommendations != oldDelegate.groupedRecommendations;
  }
} 