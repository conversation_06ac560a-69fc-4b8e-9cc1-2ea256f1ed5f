import 'package:flutter/material.dart';
import '../../config/themes.dart';

class FriendsStats extends StatelessWidget {
  final Size size;
  final Map<String, String> stats;

  const FriendsStats({
    Key? key,
    required this.size,
    required this.stats,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 600;
        
        return Container(
          margin: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.grey.withOpacity(0.05), width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
              child: isSmallScreen
                ? _buildVerticalLayout(context)
                : _buildHorizontalLayout(context),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHorizontalLayout(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildStatItem(context, 'Online', stats['online'] ?? '0', Icons.circle, Colors.green),
        _buildDivider(),
        _buildStatItem(context, 'Friends', stats['total'] ?? '0', Icons.people, AppTheme.jazzColor),
        _buildDivider(),
        _buildStatItem(context, 'Pins Shared', stats['shared_pins'] ?? '0', Icons.push_pin, AppTheme.musicPinColor),
        _buildDivider(),
        _buildStatItem(context, 'Mutual', stats['mutual'] ?? '0', Icons.handshake, AppTheme.electronicColor),
      ],
    );
  }

  Widget _buildVerticalLayout(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildStatItem(context, 'Online', stats['online'] ?? '0', Icons.circle, Colors.green),
            _buildDivider(),
            _buildStatItem(context, 'Friends', stats['total'] ?? '0', Icons.people, AppTheme.jazzColor),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Divider(height: 24, thickness: 0.5, color: Colors.grey.withOpacity(0.2)),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildStatItem(context, 'Pins Shared', stats['shared_pins'] ?? '0', Icons.push_pin, AppTheme.musicPinColor),
            _buildDivider(),
            _buildStatItem(context, 'Mutual', stats['mutual'] ?? '0', Icons.handshake, AppTheme.electronicColor),
          ],
        ),
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 36,
      width: 1,
      color: Colors.grey.withOpacity(0.15),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, IconData icon, Color color) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeOutQuint,
      builder: (context, animationValue, child) {
        return Opacity(
          opacity: animationValue,
          child: Transform.translate(
            offset: Offset(0, (1 - animationValue) * 10),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Small icon with color
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      size: 14,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Value
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                  ),
                  const SizedBox(height: 2),
                  // Label
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
} 