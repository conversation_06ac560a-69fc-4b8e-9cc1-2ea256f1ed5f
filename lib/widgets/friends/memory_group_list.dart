import 'package:flutter/material.dart';
import '../../models/memory_group.dart';
import 'memory_group_card.dart';

class MemoryGroupList extends StatelessWidget {
  final List<MemoryGroup> groups;
  const MemoryGroupList({Key? key, required this.groups}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (groups.isEmpty) {
      return Center(
        child: Text(
          'No shared memories yet. Start your first Memory Lane!',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
          textAlign: TextAlign.center,
        ),
      );
    }
    return ListView.builder(
      itemCount: groups.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: MemoryGroupCard(group: groups[index]),
        );
      },
    );
  }
} 