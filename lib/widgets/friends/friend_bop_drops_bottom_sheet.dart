import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../models/music/bop_drop.dart';
import '../../models/music_track.dart';
import '../../services/api/bop_drops_service.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/recommendation_context_provider.dart';
import '../common/cached_avatar.dart';
import '../common/shimmer_loading.dart';

class FriendBopDropsBottomSheet extends StatefulWidget {
  final String friendId;
  final String friendName;
  final String? friendAvatarUrl;
  final BopDrop? initialBopDrop;

  const FriendBopDropsBottomSheet({
    Key? key,
    required this.friendId,
    required this.friendName,
    this.friendAvatarUrl,
    this.initialBopDrop,
  }) : super(key: key);

  @override
  State<FriendBopDropsBottomSheet> createState() => _FriendBopDropsBottomSheetState();
}

class _FriendBopDropsBottomSheetState extends State<FriendBopDropsBottomSheet> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  List<BopDrop> _bopDrops = [];
  bool _isLoading = true;
  String? _errorMessage;
  BopDrop? _currentlyPlaying;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    
    _loadFriendBopDrops();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadFriendBopDrops() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final bopDrops = await BopDropsService.getUserBopDrops(widget.friendId, limit: 20);
      
      if (mounted) {
        setState(() {
          _bopDrops = bopDrops;
          _isLoading = false;
          // If we have an initial bop drop, set it as currently playing
          if (widget.initialBopDrop != null) {
            _currentlyPlaying = widget.initialBopDrop;
          } else if (bopDrops.isNotEmpty) {
            _currentlyPlaying = bopDrops.first;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = e.toString().replaceAll('Exception: ', '');
        });
      }
    }
  }

  Future<void> _playBopDrop(BopDrop bopDrop) async {
    try {
      HapticFeedback.mediumImpact();
      
      setState(() {
        _currentlyPlaying = bopDrop;
      });

      // Record view
      try {
        await BopDropsService.recordView(bopDrop.id);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Failed to record view: $e');
        }
      }

      // Set recommendation context
      final recContext = Provider.of<RecommendationContextProvider>(context, listen: false);
      recContext.setRecommendations([bopDrop]);

      // Create MusicTrack object
      final track = MusicTrack(
        id: bopDrop.trackId,
        title: bopDrop.trackTitle,
        artist: bopDrop.trackArtist,
        album: bopDrop.trackAlbum ?? '',
        albumArt: bopDrop.albumArtUrl ?? '',
        uri: bopDrop.trackId,
        url: bopDrop.previewUrl ?? '',
        service: bopDrop.musicService,
        serviceType: bopDrop.musicService,
        durationMs: 180000, // Default 3 minutes
      );

      // Use automatic fallback system based on the bop drop's original service
      bool playbackSuccess = false;
      
      if (bopDrop.musicService.toLowerCase() == 'spotify') {
        // Start with Spotify (includes automatic Apple Music fallback)
        final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
        playbackSuccess = await spotifyProvider.playTrack(track, context: context);
      } else if (bopDrop.musicService.toLowerCase() == 'apple_music') {
        // Start with Apple Music (then fallback to Spotify if needed)
        final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        try {
          playbackSuccess = await appleMusicProvider.playTrack(track);
          
          // If Apple Music failed, try Spotify fallback
          if (!playbackSuccess) {
            if (kDebugMode) {
              print('🎵 [FriendBopDrops] Apple Music failed, trying Spotify fallback...');
            }
            final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
            playbackSuccess = await spotifyProvider.playTrack(track, context: context);
          }
        } catch (e) {
          if (kDebugMode) {
            print('🎵 [FriendBopDrops] Apple Music error, trying Spotify fallback: $e');
          }
          final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
          playbackSuccess = await spotifyProvider.playTrack(track, context: context);
        }
      } else {
        // Unknown service, try Spotify first with automatic Apple Music fallback
        final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
        playbackSuccess = await spotifyProvider.playTrack(track, context: context);
      }

      // Note: Success/error messages are now handled by the providers automatically
      // Only show a brief success confirmation for this specific context
      if (playbackSuccess && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🎵 Playing "${bopDrop.trackTitle}" by ${bopDrop.trackArtist}'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error playing track: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _toggleLike(BopDrop bopDrop) async {
    try {
      HapticFeedback.lightImpact();
      
      if (bopDrop.isLikedByUser) {
        await BopDropsService.unlikeBopDrop(bopDrop.id);
      } else {
        await BopDropsService.likeBopDrop(bopDrop.id);
      }
      
      // Refresh the list to update like status
      await _loadFriendBopDrops();
      
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to ${bopDrop.isLikedByUser ? 'unlike' : 'like'} track'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Container(
      height: size.height * 0.8,
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // Friend avatar
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: theme.colorScheme.primary.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(30),
                    child: widget.friendAvatarUrl != null && widget.friendAvatarUrl!.isNotEmpty
                        ? Image.network(
                            widget.friendAvatarUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => _buildAvatarFallback(theme),
                          )
                        : _buildAvatarFallback(theme),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Friend info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${widget.friendName}\'s Bops',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _isLoading 
                            ? 'Loading...' 
                            : _errorMessage != null 
                                ? 'Error loading tracks'
                                : '${_bopDrops.length} track${_bopDrops.length != 1 ? 's' : ''}',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Close button
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close,
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: _isLoading
                ? const FriendBopDropsBottomSheetSkeleton()
                : _errorMessage != null
                    ? _buildErrorState()
                    : _bopDrops.isEmpty
                        ? _buildEmptyState()
                        : _buildBopDropsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatarFallback(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(0.7),
            theme.colorScheme.secondary.withOpacity(0.7),
          ],
        ),
      ),
      child: Center(
        child: Text(
          widget.friendName.isNotEmpty ? widget.friendName[0].toUpperCase() : '?',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load tracks',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadFriendBopDrops,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.music_note_outlined,
            size: 48,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
          ),
          const SizedBox(height: 16),
          Text(
            '${widget.friendName} hasn\'t shared any music yet',
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Check back later for their recommendations!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBopDropsList() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: _bopDrops.length,
        itemBuilder: (context, index) {
          final bopDrop = _bopDrops[index];
          final isCurrentlyPlaying = _currentlyPlaying?.id == bopDrop.id;
          
          return _buildBopDropCard(bopDrop, isCurrentlyPlaying);
        },
      ),
    );
  }

  Widget _buildBopDropCard(BopDrop bopDrop, bool isCurrentlyPlaying) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        borderRadius: BorderRadius.circular(16),
        color: isCurrentlyPlaying 
            ? theme.colorScheme.primary.withOpacity(0.1)
            : theme.colorScheme.surface,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _playBopDrop(bopDrop),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Album artwork
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: theme.colorScheme.surfaceVariant,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: bopDrop.albumArtUrl != null && bopDrop.albumArtUrl!.isNotEmpty
                        ? Image.network(
                            bopDrop.albumArtUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Icon(
                              Icons.music_note,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          )
                        : Icon(
                            Icons.music_note,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Track info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        bopDrop.trackTitle,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isCurrentlyPlaying ? theme.colorScheme.primary : null,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        bopDrop.trackArtist,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (bopDrop.caption != null && bopDrop.caption!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          bopDrop.caption!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                            fontStyle: FontStyle.italic,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 14,
                            color: theme.colorScheme.onSurface.withOpacity(0.5),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            bopDrop.timeSinceCreated,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withOpacity(0.5),
                            ),
                          ),
                          if (bopDrop.mood != null) ...[
                            const SizedBox(width: 12),
                            Text(
                              _getMoodEmoji(bopDrop.mood!),
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Action buttons
                Column(
                  children: [
                    // Play indicator or play button
                    if (isCurrentlyPlaying)
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.play_arrow,
                          color: theme.colorScheme.onPrimary,
                          size: 20,
                        ),
                      )
                    else
                      IconButton(
                        onPressed: () => _playBopDrop(bopDrop),
                        icon: Icon(
                          Icons.play_circle_outline,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    
                    // Like button
                    IconButton(
                      onPressed: () => _toggleLike(bopDrop),
                      icon: Icon(
                        bopDrop.isLikedByUser ? Icons.favorite : Icons.favorite_border,
                        color: bopDrop.isLikedByUser 
                            ? Colors.red 
                            : theme.colorScheme.onSurface.withOpacity(0.6),
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getMoodEmoji(String mood) {
    switch (mood.toLowerCase()) {
      case 'happy':
        return '😊';
      case 'sad':
        return '😢';
      case 'energetic':
        return '⚡';
      case 'chill':
        return '😎';
      case 'nostalgic':
        return '🌅';
      case 'party':
        return '🎉';
      case 'focus':
        return '🎯';
      case 'romantic':
        return '💕';
      case 'angry':
        return '😠';
      default:
        return '🎵';
    }
  }
} 