import 'package:flutter/material.dart';

class FriendsHeaderShimmer extends StatefulWidget {
  final bool isSmallScreen;
  
  const FriendsHeaderShimmer({
    Key? key,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  State<FriendsHeaderShimmer> createState() => _FriendsHeaderShimmerState();
}

class _FriendsHeaderShimmerState extends State<FriendsHeaderShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _shimmerController;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOutSine,
    ));

    _shimmerController.repeat();
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final avatarSize = widget.isSmallScreen ? 50.0 : 54.0;
    
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: List.generate(5, (index) => 
          _buildShimmerFriendItem(theme, avatarSize, index)),
      ),
    );
  }

  Widget _buildShimmerFriendItem(ThemeData theme, double avatarSize, int index) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: SizedBox(
        width: avatarSize + 12,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Shimmer avatar with gradient border
            Container(
              width: avatarSize + 4,
              height: avatarSize + 4,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Animated gradient border shimmer
                  AnimatedBuilder(
                    animation: _shimmerAnimation,
                    builder: (context, child) => Container(
                      width: avatarSize + 4,
                      height: avatarSize + 4,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment(-1.0 + _shimmerAnimation.value, -1.0),
                          end: Alignment(1.0 + _shimmerAnimation.value, 1.0),
                          colors: [
                            theme.colorScheme.primary.withOpacity(0.1),
                            theme.colorScheme.primary.withOpacity(0.3),
                            theme.colorScheme.secondary.withOpacity(0.3),
                            theme.colorScheme.primary.withOpacity(0.1),
                          ],
                          stops: const [0.0, 0.4, 0.6, 1.0],
                        ),
                      ),
                    ),
                  ),
                  
                  // Avatar shimmer container
                  AnimatedBuilder(
                    animation: _shimmerAnimation,
                    builder: (context, child) => Container(
                      width: avatarSize,
                      height: avatarSize,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment(-1.0 + _shimmerAnimation.value, -1.0),
                          end: Alignment(1.0 + _shimmerAnimation.value, 1.0),
                          colors: [
                            theme.colorScheme.surface.withOpacity(0.6),
                            theme.colorScheme.surface.withOpacity(0.9),
                            Colors.white.withOpacity(0.8),
                            theme.colorScheme.surface.withOpacity(0.6),
                          ],
                          stops: const [0.0, 0.3, 0.7, 1.0],
                        ),
                      ),
                    ),
                  ),
                  
                  // Badge shimmer
                  if (index < 3) // Only show on first 3 items
                    Positioned(
                      top: -1,
                      right: -1,
                      child: AnimatedBuilder(
                        animation: _shimmerAnimation,
                        builder: (context, child) => Container(
                          width: 18,
                          height: 18,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              begin: Alignment(-1.0 + _shimmerAnimation.value, -1.0),
                              end: Alignment(1.0 + _shimmerAnimation.value, 1.0),
                              colors: [
                                theme.colorScheme.primary.withOpacity(0.4),
                                theme.colorScheme.primary.withOpacity(0.8),
                                theme.colorScheme.primary.withOpacity(0.9),
                                theme.colorScheme.primary.withOpacity(0.4),
                              ],
                              stops: const [0.0, 0.3, 0.7, 1.0],
                            ),
                            border: Border.all(
                              color: theme.scaffoldBackgroundColor,
                              width: 1.5,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            
            SizedBox(height: widget.isSmallScreen ? 3 : 4),
            
            // Name shimmer - Two lines for variety
            Column(
              children: [
                // First line
                AnimatedBuilder(
                  animation: _shimmerAnimation,
                  builder: (context, child) => Container(
                    width: avatarSize * 0.8,
                    height: widget.isSmallScreen ? 8 : 9,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      gradient: LinearGradient(
                        begin: Alignment(-1.0 + _shimmerAnimation.value, 0),
                        end: Alignment(1.0 + _shimmerAnimation.value, 0),
                        colors: [
                          theme.colorScheme.surface.withOpacity(0.6),
                          Colors.white.withOpacity(0.7),
                          theme.colorScheme.surface.withOpacity(0.6),
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 2),
                
                // Second line (shorter, only for some items)
                if (index % 2 == 0) // Vary the name lengths
                  AnimatedBuilder(
                    animation: _shimmerAnimation,
                    builder: (context, child) => Container(
                      width: avatarSize * 0.5,
                      height: widget.isSmallScreen ? 8 : 9,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        gradient: LinearGradient(
                          begin: Alignment(-1.0 + _shimmerAnimation.value, 0),
                          end: Alignment(1.0 + _shimmerAnimation.value, 0),
                          colors: [
                            theme.colorScheme.surface.withOpacity(0.6),
                            Colors.white.withOpacity(0.7),
                            theme.colorScheme.surface.withOpacity(0.6),
                          ],
                          stops: const [0.0, 0.5, 1.0],
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 