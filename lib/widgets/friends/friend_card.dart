import 'package:flutter/material.dart';
import '../../config/themes.dart';
import '../../models/user.dart';
import '../../screens/friends/music_chat_screen.dart';
import '../../widgets/common/cached_avatar.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';

class FriendCard extends StatefulWidget {
  final Map<String, dynamic> friend;
  final VoidCallback onTap;
  final VoidCallback onMorePressed;
  final Duration animationDelay;

  const FriendCard({
    Key? key,
    required this.friend,
    required this.onTap,
    required this.onMorePressed,
    this.animationDelay = Duration.zero,
  }) : super(key: key);

  @override
  State<FriendCard> createState() => _FriendCardState();
}

class _FriendCardState extends State<FriendCard> with SingleTickerProviderStateMixin {
  bool _showAnimation = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    // Setup animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    
    // Handle the delay with Future.delayed
    if (widget.animationDelay > Duration.zero) {
      Future.delayed(widget.animationDelay, () {
        if (mounted) {
          _animationController.forward();
        }
      });
    } else {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  String _getTimeAgo(DateTime dateTime) {
    final duration = DateTime.now().difference(dateTime);
    
    if (duration.inSeconds < 60) {
      return 'just now';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ago';
    } else if (duration.inHours < 24) {
      return '${duration.inHours}h ago';
    } else if (duration.inDays < 7) {
      return '${duration.inDays}d ago';
    } else {
      return '${(duration.inDays / 7).floor()}w ago';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isOnline = widget.friend['is_online'] as bool;
    final lastActive = widget.friend['last_active'] as DateTime;
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, 15 * (1 - _animationController.value)),
            child: child,
          );
        },
        child: Container(
          margin: EdgeInsets.only(bottom: 8),
          child: Material(
            color: Theme.of(context).brightness == Brightness.light
                ? Theme.of(context).scaffoldBackgroundColor
                : Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            child: InkWell(
              onTap: () {
                final user = User(
                  id: widget.friend['id'] as int,
                  username: widget.friend['username'] as String,
                  email: '',  // Not needed for display
                  isVerified: true,
                  profilePicUrl: widget.friend['avatar_url'] as String?,
                  bio: widget.friend['bio'] as String?,
                  createdAt: DateTime.now(),  // Not needed for display
                  favoriteGenres: [],  // Not needed for display
                  connectedServices: {},  // Not needed for display
                );
                
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => MusicChatScreen(friend: user),
                  ),
                );
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).dividerColor.withOpacity(0.05),
                    width: 1,
                  ),
                ),
                padding: const EdgeInsets.all(8),
                child: Row(
                  children: [
                    _buildAvatar(isOnline),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            children: [
                              Text(
                                widget.friend['username'],
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Theme.of(context).colorScheme.onSurface
                                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.9),
                                ),
                              ),
                              const SizedBox(width: 4),
                              if (widget.friend['name'] != null)
                                Expanded(
                                  child: Text(
                                    '(${widget.friend['name']})',
                                    style: TextStyle(
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? Theme.of(context).colorScheme.onSurface.withOpacity(0.6)
                                          : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                      fontSize: 12,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                            ],
                          ),
                          if (widget.friend['bio'] != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 1),
                              child: Text(
                                widget.friend['bio'],
                                style: TextStyle(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Theme.of(context).colorScheme.onSurface.withOpacity(0.8)
                                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.85),
                                  fontSize: 11,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Container(
                                width: 6,
                                height: 6,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: isOnline 
                                    ? Colors.green.withOpacity(Theme.of(context).brightness == Brightness.dark ? 1.0 : 0.8)
                                    : Colors.grey.withOpacity(Theme.of(context).brightness == Brightness.dark ? 0.7 : 0.5),
                                ),
                              ),
                              const SizedBox(width: 3),
                              Text(
                                isOnline ? 'Online now' : 'Last seen ${_getTimeAgo(lastActive)}',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Theme.of(context).colorScheme.onSurface.withOpacity(0.6)
                                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.more_vert, size: 18),
                      onPressed: widget.onMorePressed,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(bool isOnline) {
    final String? avatarUrl = widget.friend['avatar_url'] as String?;
    
    return Stack(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: CachedAvatar(
            imageUrl: avatarUrl,
            radius: 20,
          ),
        ),
        if (isOnline)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              width: 10,
              height: 10,
              decoration: BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 1,
                ),
              ),
            ),
          ),
      ],
    );
  }
} 