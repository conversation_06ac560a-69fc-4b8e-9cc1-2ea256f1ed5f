import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/themes.dart';
import '../../providers/friends_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/friend.dart';
import 'package:shimmer/shimmer.dart';

class FriendRequestsTab extends StatefulWidget {
  const FriendRequestsTab({Key? key}) : super(key: key);

  @override
  State<FriendRequestsTab> createState() => _FriendRequestsTabState();
}

class _FriendRequestsTabState extends State<FriendRequestsTab> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    
    // Load friend requests when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final friendsProvider = context.read<FriendsProvider>();
        if (friendsProvider.isInitialized) {
          friendsProvider.loadFriendRequests();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final friendsProvider = Provider.of<FriendsProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUserId = authProvider.currentUser?.id;
    
    // Show loading shimmer if provider is still initializing
    if (!friendsProvider.isInitialized || 
        (friendsProvider.isLoading && 
         friendsProvider.receivedRequests.isEmpty && 
         friendsProvider.sentRequests.isEmpty)) {
      return _buildLoadingShimmer(isSmallScreen);
    }
    
    final hasContent = friendsProvider.receivedRequests.isNotEmpty || 
                      friendsProvider.sentRequests.isNotEmpty;
    
    return RefreshIndicator(
      onRefresh: () => friendsProvider.loadFriendRequests(),
      child: hasContent 
        ? CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // Incoming requests section
              if (friendsProvider.receivedRequests.isNotEmpty) ...[
                _buildSectionHeader(context, 'Incoming Requests', isSmallScreen),
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) => _buildIncomingRequestCard(
                      friendsProvider.receivedRequests[index], 
                      isSmallScreen,
                      currentUserId,
                    ),
                    childCount: friendsProvider.receivedRequests.length,
                  ),
                ),
              ],
              
              // Outgoing requests section
              if (friendsProvider.sentRequests.isNotEmpty) ...[
                _buildSectionHeader(context, 'Sent Requests', isSmallScreen),
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) => _buildOutgoingRequestCard(
                      friendsProvider.sentRequests[index], 
                      isSmallScreen,
                      currentUserId,
                    ),
                    childCount: friendsProvider.sentRequests.length,
                  ),
                ),
              ],
              
              // Empty space at bottom for better scrolling
              const SliverToBoxAdapter(
                child: SizedBox(height: 80),
              ),
            ],
          )
        : _buildEmptyState(),
    );
  }
  
  Widget _buildLoadingShimmer(bool isSmallScreen) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Shimmer.fromColors(
      baseColor: isDark ? Colors.grey[800]! : Colors.grey[300]!,
      highlightColor: isDark ? Colors.grey[700]! : Colors.grey[100]!,
      child: ListView.builder(
        itemCount: 5,
        padding: const EdgeInsets.all(16),
        itemBuilder: (context, index) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Container(
            height: 64,
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[850] : Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildEmptyState() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.1,
          vertical: isSmallScreen ? 24 : 32,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: isSmallScreen ? 80 : 90,
              height: isSmallScreen ? 80 : 90,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.08),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.primary.withOpacity(0.15),
                    blurRadius: 15,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                Icons.person_add_outlined,
                size: isSmallScreen ? 36 : 40,
                color: theme.colorScheme.primary,
              ),
            ),
            SizedBox(height: isSmallScreen ? 16 : 20),
            Text(
              'No Friend Requests',
              style: TextStyle(
                fontSize: isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.primary,
                letterSpacing: 0.3,
              ),
            ),
            SizedBox(height: isSmallScreen ? 6 : 8),
            Text(
              'When you receive friend requests,\nthey\'ll appear here',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: isSmallScreen ? 13 : 14,
                height: 1.3,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSectionHeader(BuildContext context, String title, bool isSmallScreen) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.fromLTRB(
          16,
          isSmallScreen ? 12 : 16,
          16,
          isSmallScreen ? 8 : 12,
        ),
        child: Text(
          title,
          style: TextStyle(
            fontSize: isSmallScreen ? 16 : 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.titleLarge?.color,
          ),
        ),
      ),
    );
  }
  
  Widget _buildIncomingRequestCard(FriendRequest request, bool isSmallScreen, int? currentUserId) {
    final theme = Theme.of(context);
    final requester = request.requester;
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Material(
        color: Theme.of(context).brightness == Brightness.light
            ? Theme.of(context).scaffoldBackgroundColor
            : Theme.of(context).cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: theme.colorScheme.onSurface.withOpacity(0.05),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Avatar
              CircleAvatar(
                radius: isSmallScreen ? 20 : 24,
                backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                backgroundImage: requester.profilePicUrl != null
                    ? CachedNetworkImageProvider(requester.profilePicUrl!)
                    : null,
                child: requester.profilePicUrl == null
                    ? Icon(
                        Icons.person,
                        size: isSmallScreen ? 20 : 24,
                        color: theme.colorScheme.primary,
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              
              // User info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      requester.displayName ?? requester.username ?? 'Unknown User',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 14 : 15,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '@${requester.username ?? 'unknown'}',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 12 : 13,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      request.getTimeSinceCreated(),
                      style: TextStyle(
                        fontSize: isSmallScreen ? 11 : 12,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildActionButton(
                    onPressed: () async {
                      final success = await context.read<FriendsProvider>()
                          .acceptFriendRequest(request.id.toString());
                      if (success && mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Friend request accepted'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    },
                    icon: Icons.check,
                    color: Colors.green,
                    isSmallScreen: isSmallScreen,
                  ),
                  const SizedBox(width: 8),
                  _buildActionButton(
                    onPressed: () async {
                      final success = await context.read<FriendsProvider>()
                          .rejectFriendRequest(request.id.toString());
                      if (success && mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Friend request declined'),
                          ),
                        );
                      }
                    },
                    icon: Icons.close,
                    color: Colors.red,
                    isSmallScreen: isSmallScreen,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildOutgoingRequestCard(FriendRequest request, bool isSmallScreen, int? currentUserId) {
    final theme = Theme.of(context);
    final recipient = request.recipient;
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Material(
        color: Theme.of(context).brightness == Brightness.light
            ? Theme.of(context).scaffoldBackgroundColor
            : Theme.of(context).cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: theme.colorScheme.onSurface.withOpacity(0.05),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Avatar
              CircleAvatar(
                radius: isSmallScreen ? 20 : 24,
                backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                backgroundImage: recipient.profilePicUrl != null
                    ? CachedNetworkImageProvider(recipient.profilePicUrl!)
                    : null,
                child: recipient.profilePicUrl == null
                    ? Icon(
                        Icons.person,
                        size: isSmallScreen ? 20 : 24,
                        color: theme.colorScheme.primary,
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              
              // User info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      recipient.displayName ?? recipient.username ?? 'Unknown User',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 14 : 15,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '@${recipient.username ?? 'unknown'}',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 12 : 13,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'Sent ${request.getTimeSinceCreated()}',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 11 : 12,
                        color: theme.colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Cancel button
              TextButton(
                onPressed: () async {
                  final confirmed = await showDialog<bool>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Cancel Request'),
                      content: Text('Cancel friend request to ${recipient.displayName ?? recipient.username ?? 'this user'}?'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: const Text('No'),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          child: const Text('Yes, Cancel'),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  );
                  
                  if (confirmed == true) {
                    final success = await context.read<FriendsProvider>()
                        .cancelFriendRequest(request.id.toString());
                    if (success && mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Friend request cancelled'),
                        ),
                      );
                    }
                  }
                },
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 12 : 16,
                    vertical: isSmallScreen ? 4 : 8,
                  ),
                ),
                child: Text(
                  'Cancel',
                  style: TextStyle(fontSize: isSmallScreen ? 12 : 13),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildActionButton({
    required VoidCallback onPressed,
    required IconData icon,
    required Color color,
    required bool isSmallScreen,
  }) {
    return Material(
      color: color.withOpacity(0.1),
      borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
        child: Container(
          padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
          child: Icon(
            icon,
            size: isSmallScreen ? 18 : 20,
            color: color,
          ),
        ),
      ),
    );
  }
} 