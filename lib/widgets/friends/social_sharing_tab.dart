import 'package:flutter/material.dart';
import 'dart:ui';
import '../../config/themes.dart';

class SocialSharingTab extends StatefulWidget {
  const SocialSharingTab({Key? key}) : super(key: key);

  @override
  State<SocialSharingTab> createState() => _SocialSharingTabState();
}

class _SocialSharingTabState extends State<SocialSharingTab> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final List<Map<String, dynamic>> _platforms = [
    {
      'name': 'Instagram',
      'color': const Color(0xFFE1306C),
      'gradient': [
        const Color(0xFFFED373),
        const Color(0xFFF15245),
        const Color(0xFFD92E7F),
      ],
    },
    {
      'name': 'Snapchat',
      'color': const Color(0xFFFFFC00),
    },
    {
      'name': 'WhatsApp',
      'color': const Color(0xFF25D366),
    },
    {
      'name': 'Facebook',
      'color': const Color(0xFF1877F2),
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // Quick actions section
              SliverToBoxAdapter(
                child: Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.05,
                    vertical: 16,
                  ),
                  child: Card(
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: BorderSide(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        width: 1,
                      ),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        vertical: isSmallScreen ? 16 : 20,
                        horizontal: 8,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildQuickAction(
                            context,
                            icon: Icons.qr_code,
                            label: 'QR Code',
                            onTap: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('QR Code generator coming soon')),
                              );
                            },
                            isSmallScreen: isSmallScreen,
                          ),
                          _buildDivider(),
                          _buildQuickAction(
                            context,
                            icon: Icons.link,
                            label: 'Copy Link',
                            onTap: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Link copied to clipboard')),
                              );
                            },
                            isSmallScreen: isSmallScreen,
                          ),
                          _buildDivider(),
                          _buildQuickAction(
                            context,
                            icon: Icons.share,
                            label: 'More',
                            onTap: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Opening native share dialog')),
                              );
                            },
                            isSmallScreen: isSmallScreen,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              
              // Social platforms section
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                    screenWidth * 0.05,
                    16,
                    screenWidth * 0.05,
                    bottomPadding + (isSmallScreen ? 80 : 90),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: _platforms.map((platform) {
                      return _buildSocialButton(
                        platform: platform,
                        size: isSmallScreen ? 40 : 44,
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShareOption(
    BuildContext context,
    String title,
    IconData icon,
    String description,
    Color color,
    VoidCallback onTap,
    bool isSmallScreen,
  ) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: color.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                icon,
                color: color,
                size: isSmallScreen ? 24 : 28,
              ),
              SizedBox(height: isSmallScreen ? 8 : 10),
              Text(
                title,
                style: TextStyle(
                  fontSize: isSmallScreen ? 14 : 16,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              SizedBox(height: isSmallScreen ? 4 : 6),
              Text(
                description,
                style: TextStyle(
                  fontSize: isSmallScreen ? 11 : 12,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildSocialButton({
    required Map<String, dynamic> platform,
    required double size,
  }) {
    return GestureDetector(
      onTap: () => _showShareConfirmation(platform),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: platform['name'] == 'Instagram' 
              ? LinearGradient(
                  colors: platform['gradient'],
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                )
              : null,
          color: platform['name'] != 'Instagram' ? platform['color'] : null,
          boxShadow: [
            BoxShadow(
              color: platform['color'].withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.all(size * 0.25),
        child: _buildPlatformIcon(platform['name'], size * 0.5),
      ),
    );
  }

  Widget _buildPlatformIcon(String platform, double size) {
    switch (platform) {
      case 'Instagram':
        return CustomPaint(
          size: Size(size, size),
          painter: InstagramPainter(color: Colors.white),
        );
      case 'Snapchat':
        return CustomPaint(
          size: Size(size, size),
          painter: SnapchatPainter(color: Colors.black87),
        );
      case 'WhatsApp':
        return CustomPaint(
          size: Size(size, size),
          painter: WhatsAppPainter(color: Colors.white),
        );
      case 'Facebook':
        return CustomPaint(
          size: Size(size, size),
          painter: FacebookPainter(color: Colors.white),
        );
      default:
        return const SizedBox();
    }
  }
  
  Widget _buildQuickAction(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required bool isSmallScreen,
  }) {
    final theme = Theme.of(context);
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isSmallScreen ? 12 : 16,
          vertical: isSmallScreen ? 8 : 10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: theme.colorScheme.primary,
              size: isSmallScreen ? 22 : 24,
            ),
            const SizedBox(height: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: isSmallScreen ? 12 : 13,
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDivider() {
    return Container(
      height: 32,
      width: 1,
      color: Colors.grey.withOpacity(0.2),
    );
  }
  
  void _showShareConfirmation(Map<String, dynamic> platform) {
    final platformName = platform['name'];
    final Color color = platform['color'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: _buildPlatformIcon(platformName, 18),
            ),
            const SizedBox(width: 12),
            Text('Share to $platformName'),
          ],
        ),
        content: Text('This will open $platformName to share your BOP Map. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Sharing to $platformName...')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Share'),
          ),
        ],
      ),
    );
  }
}

// Custom painters for social media icons
class InstagramPainter extends CustomPainter {
  final Color color;
  InstagramPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.12
      ..strokeCap = StrokeCap.round;

    // Draw camera outline
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          size.width * 0.1,
          size.width * 0.1,
          size.width * 0.8,
          size.height * 0.8,
        ),
        Radius.circular(size.width * 0.2),
      ),
      paint,
    );

    // Draw camera lens
    canvas.drawCircle(
      Offset(size.width * 0.5, size.height * 0.5),
      size.width * 0.2,
      paint,
    );

    // Draw flash dot
    canvas.drawCircle(
      Offset(size.width * 0.75, size.height * 0.25),
      size.width * 0.08,
      paint..style = PaintingStyle.fill,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class SnapchatPainter extends CustomPainter {
  final Color color;
  SnapchatPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final Path path = Path();
    
    // Draw ghost body
    path.moveTo(size.width * 0.5, size.width * 0.1);
    path.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.1,
      size.width * 0.2,
      size.height * 0.4,
    );
    path.lineTo(size.width * 0.2, size.height * 0.7);
    path.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.9,
      size.width * 0.4,
      size.height * 0.9,
    );
    path.lineTo(size.width * 0.6, size.height * 0.9);
    path.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.9,
      size.width * 0.8,
      size.height * 0.7,
    );
    path.lineTo(size.width * 0.8, size.height * 0.4);
    path.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.1,
      size.width * 0.5,
      size.height * 0.1,
    );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class WhatsAppPainter extends CustomPainter {
  final Color color;
  WhatsAppPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.12
      ..strokeCap = StrokeCap.round;

    final Path path = Path();
    
    // Draw phone receiver
    path.moveTo(size.width * 0.2, size.height * 0.8);
    path.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.6,
      size.width * 0.4,
      size.height * 0.6,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.6,
      size.width * 0.6,
      size.height * 0.4,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.2,
      size.width * 0.8,
      size.height * 0.2,
    );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class FacebookPainter extends CustomPainter {
  final Color color;
  FacebookPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final Path path = Path();
    
    // Draw 'f' letter
    path.moveTo(size.width * 0.55, size.height * 0.9);
    path.lineTo(size.width * 0.55, size.height * 0.5);
    path.lineTo(size.width * 0.7, size.height * 0.5);
    path.lineTo(size.width * 0.7, size.height * 0.35);
    path.lineTo(size.width * 0.55, size.height * 0.35);
    path.lineTo(size.width * 0.55, size.height * 0.25);
    path.quadraticBezierTo(
      size.width * 0.55,
      size.height * 0.15,
      size.width * 0.65,
      size.height * 0.15,
    );
    path.lineTo(size.width * 0.7, size.height * 0.15);
    path.lineTo(size.width * 0.7, size.height * 0.1);
    path.lineTo(size.width * 0.6, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.1,
      size.width * 0.4,
      size.height * 0.3,
    );
    path.lineTo(size.width * 0.4, size.height * 0.35);
    path.lineTo(size.width * 0.3, size.height * 0.35);
    path.lineTo(size.width * 0.3, size.height * 0.5);
    path.lineTo(size.width * 0.4, size.height * 0.5);
    path.lineTo(size.width * 0.4, size.height * 0.9);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
} 