import 'package:flutter/material.dart';
import '../../../models/music/music_chat_message.dart';
import 'music_message_bubble.dart';

class MusicChatMessages extends StatelessWidget {
  final List<MusicChatMessage> messages;
  final int currentUserId;
  final Widget Function(BuildContext, int) itemBuilder;
  final ScrollController? scrollController;
  final EdgeInsetsGeometry? padding;

  const MusicChatMessages({
    Key? key,
    required this.messages,
    required this.currentUserId,
    required this.itemBuilder,
    this.scrollController,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          // Handle scroll notifications if needed
          return false;
        },
        child: ListView.builder(
          controller: scrollController,
          padding: padding ?? const EdgeInsets.symmetric(vertical: 20),
          // Normal order: newest messages at bottom like standard chat apps
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: messages.length,
          itemBuilder: itemBuilder,
        ),
      ),
    );
  }
} 