import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:provider/provider.dart';
import '../../../models/music_track.dart';
import '../../../providers/music_chat_provider.dart';
import '../../../providers/spotify_provider.dart';
import '../../../providers/apple_music_provider.dart';

class MusicMessageBubble extends StatefulWidget {
  final MusicTrack track;
  final bool isFromMe;
  final DateTime timestamp;
  final String? message;
  final String? messageId;
  final List<dynamic>? reactions;

  const MusicMessageBubble({
    Key? key,
    required this.track,
    required this.isFromMe,
    required this.timestamp,
    this.message,
    this.messageId,
    this.reactions,
  }) : super(key: key);

  @override
  State<MusicMessageBubble> createState() => _MusicMessageBubbleState();
}

class _MusicMessageBubbleState extends State<MusicMessageBubble> {
  String? _selectedReaction;
  bool _isSaved = false;
  
  // Available reactions
  final List<String> _availableReactions = ['❤️', '🔥', '😍', '🎵', '👍', '😢'];

  void _onReactionTap(String reaction) async {
    HapticFeedback.lightImpact();
    
    // Handle API call for reactions
    if (widget.messageId != null) {
      final provider = Provider.of<MusicChatProvider>(context, listen: false);
      
      if (_selectedReaction == reaction) {
        // Remove reaction
        final success = await provider.removeReaction(widget.messageId!);
        if (success && mounted) {
          setState(() {
            _selectedReaction = null;
          });
        }
      } else {
        // Add reaction
        final success = await provider.addReaction(widget.messageId!, reaction);
        if (success && mounted) {
          setState(() {
            _selectedReaction = reaction;
          });
        }
      }
    } else {
      // Fallback to local state if no messageId
      setState(() {
        _selectedReaction = _selectedReaction == reaction ? null : reaction;
      });
    }
  }

  void _onSaveTap() {
    HapticFeedback.mediumImpact();
    setState(() {
      _isSaved = !_isSaved;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isSaved 
            ? 'Song saved to your library!' 
            : 'Song removed from library'),
        duration: const Duration(seconds: 2),
        backgroundColor: Theme.of(context).colorScheme.primary,
        action: _isSaved ? SnackBarAction(
          label: 'View',
          textColor: Colors.white,
          onPressed: () {
            // TODO: Navigate to saved songs
          },
        ) : null,
      ),
    );
  }

  Future<void> _playTrack() async {
    try {
      HapticFeedback.mediumImpact();

      // Use automatic fallback system based on the track's original service
      bool playbackSuccess = false;
      
      if (widget.track.service.toLowerCase() == 'spotify') {
        // Start with Spotify (includes automatic Apple Music fallback)
        final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
        playbackSuccess = await spotifyProvider.playTrack(widget.track, context: context);
      } else if (widget.track.service.toLowerCase() == 'apple_music') {
        // Start with Apple Music (then fallback to Spotify if needed)
        final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        try {
          playbackSuccess = await appleMusicProvider.playTrack(widget.track);
          
          // If Apple Music failed, try Spotify fallback
          if (!playbackSuccess) {
            if (kDebugMode) {
              print('🎵 [MusicChat] Apple Music failed, trying Spotify fallback...');
            }
            final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
            playbackSuccess = await spotifyProvider.playTrack(widget.track, context: context);
          }
        } catch (e) {
          if (kDebugMode) {
            print('🎵 [MusicChat] Apple Music error, trying Spotify fallback: $e');
          }
          final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
          playbackSuccess = await spotifyProvider.playTrack(widget.track, context: context);
        }
      } else {
        // Unknown service, try Spotify first with automatic Apple Music fallback
        final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
        playbackSuccess = await spotifyProvider.playTrack(widget.track, context: context);
      }

      // Note: Success/error messages are now handled by the providers automatically
      // Only show a brief success confirmation for this specific context
      if (playbackSuccess && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🎵 Playing "${widget.track.title}" by ${widget.track.artist}'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error playing track: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showReactionPicker(BuildContext context) {
    final theme = Theme.of(context);
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withOpacity(0.1),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                'React to this song',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Reaction grid
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  mainAxisSpacing: 12,
                  crossAxisSpacing: 12,
                  childAspectRatio: 1,
                ),
                itemCount: _availableReactions.length,
                itemBuilder: (context, index) {
                  final reaction = _availableReactions[index];
                  final isSelected = _selectedReaction == reaction;
                  
                  return GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                      _onReactionTap(reaction);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? theme.colorScheme.primary.withOpacity(0.1)
                            : theme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: isSelected
                              ? theme.colorScheme.primary.withOpacity(0.3)
                              : theme.colorScheme.onSurface.withOpacity(0.1),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          reaction,
                          style: const TextStyle(fontSize: 28),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Remove reaction button if one is selected
            if (_selectedReaction != null) ...[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _onReactionTap(_selectedReaction!); // This will remove it
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      backgroundColor: theme.colorScheme.surface,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'Remove reaction',
                      style: TextStyle(
                        color: theme.colorScheme.error,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ],
            
            // Bottom padding for safe area
            SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Align(
      alignment: widget.isFromMe ? Alignment.centerRight : Alignment.centerLeft,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        child: Column(
          crossAxisAlignment: widget.isFromMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            // Optional message above song card
            if (widget.message != null) ...[
              Container(
                margin: EdgeInsets.only(
                  left: widget.isFromMe ? 0 : 8,
                  right: widget.isFromMe ? 8 : 0,
                  bottom: 8,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                decoration: BoxDecoration(
                  color: widget.isFromMe 
                    ? theme.colorScheme.primary
                    : isDark
                        ? theme.colorScheme.surface
                        : theme.colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  widget.message!,
                  style: TextStyle(
                    color: widget.isFromMe 
                      ? Colors.white
                      : theme.colorScheme.onSurface,
                    fontSize: 15,
                  ),
                ),
              ),
            ],
            
            // Song card
            Container(
              decoration: BoxDecoration(
                color: isDark
                    ? theme.colorScheme.surface
                    : theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _playTrack,
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        children: [
                          // Album art with service icon
                          Stack(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: Image.network(
                                  widget.track.albumArt,
                                  width: 56,
                                  height: 56,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      width: 56,
                                      height: 56,
                                      color: theme.colorScheme.primary.withOpacity(0.1),
                                      child: Icon(
                                        Icons.music_note_rounded,
                                        color: theme.colorScheme.primary,
                                        size: 24,
                                      ),
                                    );
                                  },
                                ),
                              ),
                              Positioned(
                                right: 4,
                                bottom: 4,
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.5),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Image.asset(
                                    'assets/icons/${widget.track.service.toLowerCase()}.png',
                                    width: 12,
                                    height: 12,
                                    errorBuilder: (context, error, stackTrace) {
                                      debugPrint('Error loading service icon: $error');
                                      return Icon(
                                        Icons.music_note_rounded,
                                        size: 12,
                                        color: Colors.white,
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(width: 12),
                          
                          // Song info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  widget.track.title,
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  widget.track.artistAndAlbum,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.timer_outlined,
                                      size: 12,
                                      color: theme.colorScheme.primary,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      widget.track.formattedDuration,
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: theme.colorScheme.primary,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          
                          // Play button
                          Container(
                            width: 36,
                            height: 36,
                            margin: const EdgeInsets.only(left: 8),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.play_arrow_rounded,
                              color: theme.colorScheme.primary,
                              size: 24,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            
            // Reaction and save actions (only for friend's messages)
            if (!widget.isFromMe) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // Reaction picker button
                  GestureDetector(
                    onTap: () => _showReactionPicker(context),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _selectedReaction != null
                            ? theme.colorScheme.primary.withOpacity(0.1)
                            : theme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: _selectedReaction != null
                              ? theme.colorScheme.primary.withOpacity(0.3)
                              : theme.colorScheme.onSurface.withOpacity(0.1),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _selectedReaction ?? '😊',
                            style: const TextStyle(fontSize: 14),
                          ),
                          if (_selectedReaction == null) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.add_rounded,
                              size: 14,
                              color: theme.colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  
                  // Save button
                  GestureDetector(
                    onTap: _onSaveTap,
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: _isSaved
                            ? theme.colorScheme.primary.withOpacity(0.1)
                            : theme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _isSaved
                              ? theme.colorScheme.primary.withOpacity(0.3)
                              : theme.colorScheme.onSurface.withOpacity(0.1),
                        ),
                      ),
                      child: Icon(
                        _isSaved ? Icons.bookmark_rounded : Icons.bookmark_border_rounded,
                        size: 16,
                        color: _isSaved
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ),
                ],
              ),
            ],
            
            // Timestamp
            Padding(
              padding: const EdgeInsets.only(top: 4, left: 4, right: 4),
              child: Text(
                '${DateFormat.jm().format(widget.timestamp)} · ${timeago.format(widget.timestamp)}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                  fontSize: 11,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 