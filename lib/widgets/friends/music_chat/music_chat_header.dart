import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../models/user.dart';
import '../../../theme/app_colors.dart';
import '../../../screens/profile/user_profile_screen.dart';
import '../../common/cached_avatar.dart';
import '../../bottomsheets/block_user_bottomsheet.dart';
import '../../bottomsheets/report_user_bottomsheet.dart';

class MusicChatHeader extends StatelessWidget {
  final User friend;

  const MusicChatHeader({
    Key? key,
    required this.friend,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 16,
        vertical: isSmallScreen ? 8 : 12,
      ),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Left section: Back button and friend info
            Expanded(
              child: Row(
                children: [
                  // Back button with modern design
                  Material(
                    color: theme.colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    child: InkWell(
                      onTap: () => Navigator.pop(context),
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(8),
                        child: Icon(
                          Icons.arrow_back_ios_new_rounded,
                          size: 18,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // Friend avatar with hero animation
                  Hero(
                    tag: 'music_chat_avatar_${friend.id}',
                    child: CachedAvatar(
                      imageUrl: friend.profilePicUrl,
                      radius: isSmallScreen ? 16 : 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // Friend info with streak
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Friend name with online indicator (not username)
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Flexible(
                              child: Text(
                                friend.username,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: theme.scaffoldBackgroundColor,
                                  width: 1.5,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        
                        // Streak and last active info
                        Row(
                          children: [
                            // Streak pill
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: theme.colorScheme.primary.withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.local_fire_department_rounded,
                                    size: 14,
                                    color: Colors.orange.shade600,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '3 day streak',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Right section: Action buttons
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Profile button
                _buildActionButton(
                  context: context,
                  icon: Icons.person_outline_rounded,
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => UserProfileScreen(
                          userId: friend.id,
                          showBottomNav: false,
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(width: 8),
                // More options button
                _buildActionButton(
                  context: context,
                  icon: Icons.more_vert_rounded,
                  onTap: () {
                    _showMoreOptions(context);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return Material(
      color: theme.colorScheme.primary.withOpacity(0.1),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Icon(
            icon,
            size: 20,
            color: theme.colorScheme.primary,
          ),
        ),
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    final theme = Theme.of(context);
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withOpacity(0.1),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Options list
            ..._buildOptionItems(context),
            
            // Bottom padding for safe area
            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildOptionItems(BuildContext context) {
    return [
      _buildOptionItem(
        context: context,
        icon: Icons.notifications_outlined,
        label: 'Mute notifications',
        onTap: () {
          Navigator.pop(context);
          HapticFeedback.mediumImpact();
          // TODO: Implement mute notifications
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Notifications muted'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        },
      ),
      _buildOptionItem(
        context: context,
        icon: Icons.block_outlined,
        label: 'Block ${friend.username}',
        onTap: () {
          Navigator.pop(context);
          HapticFeedback.mediumImpact();
          
          // Show beautiful block bottom sheet
          BlockUserBottomSheet.show(
            context,
            userId: friend.id,
            username: friend.username,
            onBlocked: () {
              // Handle successful block - could navigate back or update UI
              print('User ${friend.username} has been blocked');
            },
          );
        },
        isDestructive: true,
      ),
      _buildOptionItem(
        context: context,
        icon: Icons.report_outlined,
        label: 'Report',
        onTap: () {
          Navigator.pop(context);
          HapticFeedback.mediumImpact();
          
          // Show beautiful report bottom sheet
          ReportUserBottomSheet.show(
            context,
            userId: friend.id,
            username: friend.username,
            onReported: () {
              // Handle successful report
              print('User ${friend.username} has been reported');
            },
          );
        },
        isDestructive: true,
      ),
    ];
  }

  Widget _buildOptionItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);
    final color = isDestructive ? theme.colorScheme.error : theme.colorScheme.onSurface;
    
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(
        label,
        style: theme.textTheme.bodyLarge?.copyWith(color: color),
      ),
      onTap: onTap,
      minLeadingWidth: 24,
    );
  }
} 