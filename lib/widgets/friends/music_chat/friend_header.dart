// Add skin icon tap handler
void _onSkinTap(BuildContext context) {
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => const SkinCollectionScreen(),
    ),
  );
}

// In the build method, add the skin icon
Row(
  children: [
    // ... existing avatar and name
    const Spacer(),
    // Add skin icon button
    IconButton(
      icon: const Icon(Icons.style),
      onPressed: () => _onSkinTap(context),
      tooltip: 'Pin Skins',
    ),
    // ... existing actions
  ],
) 