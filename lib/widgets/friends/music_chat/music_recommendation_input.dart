import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../models/music_track.dart';
import '../../../providers/music_chat_provider.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

class MusicRecommendationInput extends StatefulWidget {
  final Function(MusicTrack track, String? message) onSendRecommendation;

  const MusicRecommendationInput({
    Key? key,
    required this.onSendRecommendation,
  }) : super(key: key);

  @override
  State<MusicRecommendationInput> createState() => _MusicRecommendationInputState();
}

class _MusicRecommendationInputState extends State<MusicRecommendationInput> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final FocusNode _messageFocusNode = FocusNode();
  final GlobalKey _searchFieldKey = GlobalKey();
  final GlobalKey _messageFieldKey = GlobalKey();
  
  bool _showMessage = false;
  MusicTrack? _selectedTrack;
  
  // Add debouncer for search
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    
    // Add focus listener to maintain keyboard focus
    _searchFocusNode.addListener(() {
      if (kDebugMode) {
        print('🔍 Search focus changed: ${_searchFocusNode.hasFocus}');
      }
      
      // If focus is lost while user is typing, try to restore it
      if (!_searchFocusNode.hasFocus && 
          !_showMessage && 
          _searchController.text.isNotEmpty &&
          _debounceTimer?.isActive == true) {
        if (kDebugMode) {
          print('🔍 Focus lost during typing! Attempting to restore...');
        }
        // Small delay to avoid conflicts
        Future.delayed(const Duration(milliseconds: 50), () {
          if (mounted && !_showMessage) {
            _searchFocusNode.requestFocus();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _messageController.dispose();
    _searchFocusNode.dispose();
    _messageFocusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (kDebugMode) {
      print('🔍 _onSearchChanged: "$query", hasFocus: ${_searchFocusNode.hasFocus}');
    }
    
    // Cancel previous timer
    _debounceTimer?.cancel();
    
    // Handle empty query - clear results but don't change searching state
    if (query.isEmpty) {
      final provider = Provider.of<MusicChatProvider>(context, listen: false);
      provider.clearSearchResults();
      return;
    }
    
    // NO setState calls here to avoid any rebuilds that interfere with focus
    // The _isSearching state will be updated when results come back
    
    // Fast debounce with focus protection - 250ms for real-time results
    _debounceTimer = Timer(const Duration(milliseconds: 250), () {
      if (kDebugMode) {
        print('🔍 Timer fired for: "$query", mounted: $mounted, hasFocus: ${_searchFocusNode.hasFocus}');
      }
      // Only search if query is long enough and conditions are met
      if (mounted && query.length >= 2 && _searchFocusNode.hasFocus) {
        final provider = Provider.of<MusicChatProvider>(context, listen: false);
        provider.searchSongs(query);
      }
    });
  }

  void _selectTrack(MusicTrack track) {
    if (mounted) {
      setState(() {
        _selectedTrack = track;
        _showMessage = true;
      });
      _searchController.clear();
      // Transition to message input
      Future.microtask(() {
        if (mounted) {
          _messageFocusNode.requestFocus();
          final provider = Provider.of<MusicChatProvider>(context, listen: false);
          provider.clearSearchResults();
        }
      });
    }
  }

  void _sendRecommendation() {
    if (_selectedTrack != null && mounted) {
      widget.onSendRecommendation(
        _selectedTrack!,
        _messageController.text.isEmpty ? null : _messageController.text,
      );
      setState(() {
        _selectedTrack = null;
        _showMessage = false;
      });
      _messageController.clear();
    }
  }

  void _clearSearch() {
    _searchController.clear();
    final provider = Provider.of<MusicChatProvider>(context, listen: false);
    provider.clearSearchResults();
    // Keep focus on search field after clearing
    _searchFocusNode.requestFocus();
  }

  void _cancelSelection() {
    if (mounted) {
      setState(() {
        _selectedTrack = null;
        _showMessage = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Modern design colors - use proper primary colors
    final surfaceColor = isDark 
        ? theme.colorScheme.surface
        : theme.colorScheme.surface;
    
    // Use consistent primary color for active search state
    final searchBarColor = _searchFocusNode.hasFocus || _searchController.text.isNotEmpty
        ? theme.colorScheme.primary.withOpacity(0.1)
        : (isDark
            ? theme.colorScheme.surfaceVariant.withOpacity(0.3)
            : theme.colorScheme.surfaceVariant.withOpacity(0.5));
    
    final searchBarBorder = _searchFocusNode.hasFocus || _searchController.text.isNotEmpty
        ? Colors.transparent
        : theme.colorScheme.primary.withOpacity(0.1);

    return Selector<MusicChatProvider, List<MusicTrack>>(
      selector: (context, provider) => provider.searchResults,
      builder: (context, searchResults, child) {
        if (kDebugMode) {
          print('🔍 Selector rebuilding with ${searchResults.length} results, searchFocus: ${_searchFocusNode.hasFocus}');
        }
        final provider = Provider.of<MusicChatProvider>(context, listen: false);
        return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search results with modern design
          if (_searchController.text.isNotEmpty && searchResults.isNotEmpty && !_showMessage)
            Container(
              constraints: const BoxConstraints(maxHeight: 300),
              margin: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: surfaceColor,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.1),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(isDark ? 0.3 : 0.1),
                    offset: const Offset(0, 4),
                    blurRadius: 12,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: ListView.separated(
                  shrinkWrap: true,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: searchResults.length,
                  separatorBuilder: (context, index) => Divider(
                    height: 1,
                    color: theme.colorScheme.outline.withOpacity(0.1),
                    indent: 72,
                  ),
                  itemBuilder: (context, index) {
                    final track = searchResults[index];
                    return Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () => _selectTrack(track),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          child: Row(
                            children: [
                              // Modern album art
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.1),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Image.network(
                                    track.albumArt,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) => Container(
                                      decoration: BoxDecoration(
                                        color: theme.colorScheme.surfaceVariant,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Icon(
                                        Icons.music_note_rounded,
                                        color: theme.colorScheme.primary,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              
                              // Track info with modern typography
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      track.title,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: theme.textTheme.bodyLarge?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    Text(
                                      track.artist,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: theme.textTheme.bodyMedium?.copyWith(
                                        color: theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              
                              // Modern add button
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.add_rounded,
                                  color: theme.colorScheme.primary,
                                  size: 18,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

          // Modern input section
          Container(
            padding: EdgeInsets.fromLTRB(
              12,
              8,
              12,
              8 + MediaQuery.of(context).padding.bottom,
            ),
            decoration: BoxDecoration(
              color: surfaceColor,
              border: Border(
                top: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.1),
                  width: 1,
                ),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Selected track preview with modern design
                if (_selectedTrack != null)
                  Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.primary.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        // Album art
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Image.network(
                              _selectedTrack!.albumArt,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => Container(
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.surfaceVariant,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Icon(
                                  Icons.music_note_rounded,
                                  color: theme.colorScheme.primary,
                                  size: 20,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        
                        // Track info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _selectedTrack!.title,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                              Text(
                                _selectedTrack!.artist,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        // Close button
                        IconButton(
                          icon: Icon(
                            Icons.close_rounded,
                            size: 18,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          onPressed: _cancelSelection,
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                          padding: EdgeInsets.zero,
                        ),
                      ],
                    ),
                  ),

                // Modern input row
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Input field with modern design
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: searchBarColor,
                          borderRadius: BorderRadius.circular(24),
                          border: _searchFocusNode.hasFocus || _searchController.text.isNotEmpty
                              ? null // Completely remove border when active
                              : Border.all(
                                  color: searchBarBorder,
                                  width: 1,
                                ),
                        ),
                        child: Row(
                          children: [
                            // Icon with proper primary color when active
                            Padding(
                              padding: const EdgeInsets.only(left: 16),
                              child: Icon(
                                _showMessage ? Icons.message_outlined : Icons.search_rounded,
                                size: 20,
                                color: _searchFocusNode.hasFocus || _searchController.text.isNotEmpty
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            
                            // Text field - Simplified approach
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  if (!_showMessage) {
                                    // Ensure focus is gained and maintained
                                    if (!_searchFocusNode.hasFocus) {
                                      _searchFocusNode.requestFocus();
                                    }
                                  } else {
                                    _messageFocusNode.requestFocus();
                                  }
                                },
                                child: TextField(
                                  key: _showMessage ? _messageFieldKey : _searchFieldKey,
                                  controller: _showMessage ? _messageController : _searchController,
                                  focusNode: _showMessage ? _messageFocusNode : _searchFocusNode,
                                  onChanged: _showMessage ? null : _onSearchChanged,
                                  enabled: true,
                                  readOnly: false,
                                  enableSuggestions: false,
                                  autocorrect: false,
                                  keyboardType: TextInputType.text,
                                  textInputAction: _showMessage
                                      ? TextInputAction.send
                                      : TextInputAction.done,
                                  onSubmitted: (value) {
                                    if (_showMessage) {
                                      _sendRecommendation();
                                    } else {
                                      // Give a moment for any search results to load
                                      Future.delayed(const Duration(milliseconds: 100), () {
                                        if (mounted) {
                                          // Only close if truly empty and no results
                                          if (value.trim().isEmpty && searchResults.isEmpty) {
                                            _searchFocusNode.unfocus();
                                          }
                                          // Otherwise keep focus - user is still searching
                                        }
                                      });
                                    }
                                  },
                                  decoration: InputDecoration(
                                    hintText: _showMessage
                                        ? 'Add a message (optional)'
                                        : 'Search for a song to share...',
                                    hintStyle: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.colorScheme.onSurfaceVariant,
                                    ),
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    errorBorder: InputBorder.none,
                                    disabledBorder: InputBorder.none,
                                    focusedErrorBorder: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 14,
                                    ),
                                  ),
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                  textCapitalization: TextCapitalization.sentences,
                                ),
                              ),
                            ),
                            
                            // Clear button using ValueListenableBuilder
                            ValueListenableBuilder<TextEditingValue>(
                              valueListenable: _showMessage ? _messageController : _searchController,
                              builder: (context, value, child) {
                                if (value.text.isEmpty) return const SizedBox.shrink();
                                
                                return IconButton(
                                  icon: Icon(
                                    Icons.close_rounded,
                                    size: 18,
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                  onPressed: _showMessage
                                      ? () => _messageController.clear()
                                      : _clearSearch,
                                  constraints: const BoxConstraints(
                                    minWidth: 32,
                                    minHeight: 32,
                                  ),
                                  padding: EdgeInsets.zero,
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    // Send button with modern design
                    if (_selectedTrack != null) ...[
                      const SizedBox(width: 8),
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              theme.colorScheme.primary,
                              theme.colorScheme.primary.withOpacity(0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: theme.colorScheme.primary.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(24),
                          child: InkWell(
                            onTap: _sendRecommendation,
                            borderRadius: BorderRadius.circular(24),
                            child: Center(
                              child: Icon(
                                Icons.send_rounded,
                                color: theme.colorScheme.onPrimary,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      );
    },
    );
  }
} 