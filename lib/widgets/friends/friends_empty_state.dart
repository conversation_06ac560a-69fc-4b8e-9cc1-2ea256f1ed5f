import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../config/themes.dart';

class FriendsEmptyState extends StatefulWidget {
  final VoidCallback onAddFriends;

  const FriendsEmptyState({
    Key? key,
    required this.onAddFriends,
  }) : super(key: key);

  @override
  State<FriendsEmptyState> createState() => _FriendsEmptyStateState();
}

class _FriendsEmptyStateState extends State<FriendsEmptyState> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotateAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat(reverse: true);

    _scaleAnimation = Tween<double>(begin: 0.98, end: 1.02).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    
    _rotateAnimation = Tween<double>(begin: -0.02, end: 0.02).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    
    _opacityAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.1,
        vertical: isSmallScreen ? 24 : 32,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            theme.colorScheme.surface,
            theme.colorScheme.primary.withOpacity(0.05),
          ],
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildAnimatedIllustration(),
          SizedBox(height: isSmallScreen ? 20 : 24),
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _opacityAnimation,
                child: child,
              );
            },
            child: Text(
              'Connect With Friends',
              style: TextStyle(
                fontSize: isSmallScreen ? 20 : 22,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.primary,
                letterSpacing: 0.3,
              ),
            ),
          ),
          SizedBox(height: isSmallScreen ? 8 : 10),
          Text(
            'Share your music journey with friends',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: isSmallScreen ? 14 : 15,
              color: theme.colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
          SizedBox(height: isSmallScreen ? 20 : 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildActionButton(
                context,
                'Find Friends',
                Icons.person_add_alt_1,
                widget.onAddFriends,
                isPrimary: true,
                isSmallScreen: isSmallScreen,
              ),
              SizedBox(width: isSmallScreen ? 12 : 16),
              _buildActionButton(
                context,
                'Invite',
                Icons.share,
                () {},
                isPrimary: false,
                isSmallScreen: isSmallScreen,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedIllustration() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final size = isSmallScreen ? 120.0 : 140.0;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotateAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.08),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.15),
                    blurRadius: 15,
                    spreadRadius: 3,
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  ...List.generate(4, (index) {
                    final angle = index * (math.pi / 2);
                    return Positioned(
                      left: size/2 + (size/3) * math.cos(angle + _animationController.value * math.pi * 2),
                      top: size/2 + (size/3) * math.sin(angle + _animationController.value * math.pi * 2),
                      child: Container(
                        width: isSmallScreen ? 12 : 14,
                        height: isSmallScreen ? 12 : 14,
                        decoration: BoxDecoration(
                          color: _getColorForIndex(index),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: _getColorForIndex(index).withOpacity(0.2),
                              blurRadius: 6,
                              spreadRadius: 0,
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                  Container(
                    width: size * 0.6,
                    height: size * 0.6,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 8,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.people,
                      size: size * 0.3,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onPressed,
    {required bool isPrimary,
    required bool isSmallScreen}
  ) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 12 : 16,
            vertical: isSmallScreen ? 8 : 10,
          ),
          decoration: BoxDecoration(
            color: isPrimary
                ? theme.colorScheme.primary
                : (isDark ? Colors.white.withOpacity(0.1) : Colors.black.withOpacity(0.05)),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isPrimary
                  ? Colors.transparent
                  : theme.colorScheme.primary.withOpacity(isDark ? 0.3 : 0.2),
              width: 1,
            ),
            boxShadow: isPrimary
                ? [
                    BoxShadow(
                      color: theme.colorScheme.primary.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: isSmallScreen ? 16 : 18,
                color: isPrimary
                    ? Colors.white
                    : theme.colorScheme.primary,
              ),
              SizedBox(width: isSmallScreen ? 6 : 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: isSmallScreen ? 13 : 14,
                  fontWeight: FontWeight.w600,
                  color: isPrimary
                      ? Colors.white
                      : theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getColorForIndex(int index) {
    switch (index) {
      case 0:
        return AppTheme.musicPinColor;
      case 1:
        return AppTheme.electronicColor;
      case 2:
        return AppTheme.rockColor;
      case 3:
        return AppTheme.jazzColor;
      default:
        return AppTheme.primaryColor;
    }
  }
} 