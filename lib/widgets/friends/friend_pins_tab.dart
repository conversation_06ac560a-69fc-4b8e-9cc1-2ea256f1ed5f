import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import '../../config/themes.dart';

class FriendPinsTab extends StatefulWidget {
  final String friendName;
  final String? avatarUrl;

  const FriendPinsTab({
    Key? key,
    required this.friendName,
    this.avatarUrl,
  }) : super(key: key);

  @override
  _FriendPinsTabState createState() => _FriendPinsTabState();
}

class _FriendPinsTabState extends State<FriendPinsTab> with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  List<Map<String, dynamic>> _pins = [];
  String _selectedFilter = 'All';
  final List<String> _filters = ['All', 'Music', 'Food', 'Event', 'Photo'];
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300), // Faster animation
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    
    _loadPins();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadPins() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });
    }

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Create random pins for demo
      final random = Random();
      final pinTypes = ['Music', 'Food', 'Event', 'Photo'];
      
      // Generate a manageable number of pins
      final pins = List.generate(10, (index) {
        final type = pinTypes[random.nextInt(pinTypes.length)];
        final iconData = _getIconForType(type);
        final timeAgo = _getRandomTimeAgo(random);
        
        return {
          'id': 'pin${index + 1}',
          'title': 'Pin ${index + 1}',
          'type': type,
          'icon': iconData,
          'timeAgo': timeAgo,
          'likes': random.nextInt(100),
          'comments': random.nextInt(20),
        };
      });
      
      if (mounted) {
        setState(() {
          _pins = pins;
          _isLoading = false;
        });
        _animationController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'Failed to load pins. Please try again.';
          _isLoading = false;
        });
      }
    }
  }
  
  IconData _getIconForType(String type) {
    switch (type) {
      case 'Music':
        return Icons.music_note;
      case 'Food':
        return Icons.restaurant;
      case 'Event':
        return Icons.event;
      case 'Photo':
        return Icons.photo;
      default:
        return Icons.location_on;
    }
  }
  
  String _getRandomTimeAgo(Random random) {
    final options = [
      '5m ago',
      '10m ago',
      '30m ago',
      '1h ago',
      '2h ago',
      '1d ago',
      '2d ago',
      '1w ago',
    ];
    
    return options[random.nextInt(options.length)];
  }
  
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    if (_hasError) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _errorMessage,
              style: TextStyle(color: Theme.of(context).colorScheme.error),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8), // Smaller spacing
            ElevatedButton(
              onPressed: _loadPins,
              child: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(100, 36), // Smaller button
              ),
            ),
          ],
        ),
      );
    }
    
    if (_pins.isEmpty) {
      return _buildEmptyState();
    }
    
    return Column(
      children: [
        _buildFilterBar(),
        Expanded(
          child: RefreshIndicator(
            onRefresh: _loadPins,
            child: _buildPinsList(),
          ),
        ),
      ],
    );
  }
  
  Widget _buildFilterBar() {
    return Container(
      height: 40, // Reduced height
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // Smaller padding
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _filters.length,
        itemBuilder: (context, index) {
          final filter = _filters[index];
          final isSelected = _selectedFilter == filter;
          
          return Padding(
            padding: const EdgeInsets.only(right: 6), // Smaller spacing
            child: FilterChip(
              label: Text(
                filter,
                style: TextStyle(
                  fontSize: 12, // Smaller font
                  color: isSelected 
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurface,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = filter;
                  _animationController.reset();
                  _animationController.forward();
                });
              },
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0), // Minimal padding
              visualDensity: VisualDensity.compact, // Use compact density
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap, // Minimum tap target
              backgroundColor: Theme.of(context).colorScheme.surface,
              selectedColor: Theme.of(context).colorScheme.primary,
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildPinsList() {
    final filteredPins = _selectedFilter == 'All'
        ? _pins
        : _pins.where((pin) => pin['type'] == _selectedFilter).toList();
    
    if (filteredPins.isEmpty) {
      return _buildFilterEmptyState();
    }
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // Smaller padding
            itemCount: filteredPins.length,
            itemBuilder: (context, index) {
              return _buildPinCard(filteredPins[index], index);
            },
          ),
        );
      },
    );
  }
  
  Widget _buildPinCard(Map<String, dynamic> pin, int index) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8), // Smaller padding
      child: Card(
        elevation: 0, // Remove elevation
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10), // Smaller radius
        ),
        child: InkWell(
          onTap: () {
            // Navigate to pin detail
          },
          borderRadius: BorderRadius.circular(10), // Match card radius
          child: Padding(
            padding: const EdgeInsets.all(8), // Smaller padding
            child: Row(
              children: [
                CircleAvatar(
                  radius: 18, // Smaller avatar
                  backgroundColor: _getColorForType(pin['type']),
                  child: Icon(
                    pin['icon'],
                    size: 16, // Smaller icon
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 8), // Smaller spacing
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        pin['title'],
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14, // Smaller font
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            pin['type'],
                            style: TextStyle(
                              color: _getColorForType(pin['type']),
                              fontSize: 12, // Smaller font
                            ),
                          ),
                          const SizedBox(width: 4), // Smaller spacing
                          Text(
                            '• ${pin['timeAgo']}',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              fontSize: 12, // Smaller font
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildStatIcon(Icons.favorite_border, pin['likes'].toString()),
                    const SizedBox(width: 4), // Smaller spacing
                    _buildStatIcon(Icons.chat_bubble_outline, pin['comments'].toString()),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildStatIcon(IconData icon, String count) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14, // Smaller icon
          color: Colors.grey,
        ),
        const SizedBox(width: 2), // Minimal spacing
        Text(
          count,
          style: const TextStyle(
            fontSize: 12, // Smaller font
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
  
  Color _getColorForType(String type) {
    switch (type) {
      case 'Music':
        return Colors.purple;
      case 'Food':
        return Colors.orange;
      case 'Event':
        return Colors.blue;
      case 'Photo':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
  
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.location_off,
            size: 40, // Smaller icon
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 8), // Smaller spacing
          Text(
            "${widget.friendName} hasn't dropped any pins yet",
            style: TextStyle(
              fontSize: 14, // Smaller font
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 6), // Smaller spacing
          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 250), // Constrain width
            child: Text(
              "When they do, you'll see them here",
              style: TextStyle(
                fontSize: 12, // Smaller font
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFilterEmptyState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getIconForType(_selectedFilter),
            size: 40, // Smaller icon
            color: _getColorForType(_selectedFilter).withOpacity(0.5),
          ),
          const SizedBox(height: 8), // Smaller spacing
          Text(
            "No $_selectedFilter pins found",
            style: TextStyle(
              fontSize: 14, // Smaller font
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 6), // Smaller spacing
          ElevatedButton(
            onPressed: () {
              setState(() {
                _selectedFilter = 'All';
              });
            },
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(100, 36), // Smaller button
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0), // Smaller padding
            ),
            child: const Text(
              'Show all pins',
              style: TextStyle(fontSize: 12), // Smaller font
            ),
          ),
        ],
      ),
    );
  }
} 