import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'package:provider/provider.dart';
import '../../config/themes.dart';
import '../../providers/friends_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/friend.dart';
import '../../models/user.dart';
import '../../models/music/bop_drop.dart';
import '../../services/api/bop_drops_service.dart';
import 'package:shimmer/shimmer.dart';
import '../../screens/friends/music_chat_screen.dart';
import '../../screens/map/my_bop_map.dart';
import '../../screens/user_search/user_search_screen.dart';
import '../../screens/friends/bop_drop_screen.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../services/music/shuffle_service.dart';
import '../../providers/recommendation_context_provider.dart';

class FriendsTab extends StatefulWidget {
  final TabController? tabController;
  final ScrollController? scrollController;
  final double? headerHeight;
  
  const FriendsTab({
    Key? key,
    this.tabController,
    this.scrollController,
    this.headerHeight,
  }) : super(key: key);

  @override
  State<FriendsTab> createState() => _FriendsTabState();
}

class _FriendsTabState extends State<FriendsTab> with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  String _searchQuery = '';
  String _currentFilter = 'All';  // Default to 'All'
  late final FocusNode _searchFocusNode;
  static final _bucket = PageStorageBucket();
  
  // Add animation controller and animation
  late final AnimationController _fadeController;
  late final Animation<double> _fadeAnimation;
  
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    print('🔥 FriendsTab: Initializing with default filter: $_currentFilter');
    
    // Initialize animation controller
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // Initialize fade animation
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));
    
    // Start the animation
    _fadeController.forward();
    
    _searchFocusNode = FocusNode();
    _searchFocusNode.addListener(() {
      if (_searchFocusNode.hasFocus && widget.scrollController != null && widget.headerHeight != null) {
        widget.scrollController!.animateTo(
          widget.headerHeight!,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
    
    // Add tab controller listener
    widget.tabController?.addListener(_handleTabChange);
    
    // Reset state on init
    _clearSavedState();
    setState(() {
      _currentFilter = 'All';
      _searchQuery = '';
    });
    
    // Load friends data if needed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final friendsProvider = context.read<FriendsProvider>();
      if (friendsProvider.isInitialized) {
        friendsProvider.loadFriendsIfNeeded();
      }
    });
  }
  
  void _handleTabChange() {
    if (widget.tabController?.index != 0) {  // If not on friends tab
      print('🔥 FriendsTab: Left friends tab, resetting filter to All');
      _clearSavedState();
      if (mounted) {
        setState(() {
          _currentFilter = 'All';
          _searchQuery = '';
        });
      }
    }
  }
  
  @override
  void dispose() {
    widget.tabController?.removeListener(_handleTabChange);
    _fadeController.dispose();
    _searchFocusNode.dispose();
    _clearSavedState();  // Clear state on dispose
    super.dispose();
  }

  void _saveCurrentState() {
    if (!mounted) return;
    print('🔥 FriendsTab: Saving current filter: $_currentFilter');
    final state = {
      'search_query': _searchQuery,
      'current_filter': _currentFilter,
    };
    _bucket.writeState(context, state, identifier: 'friends_tab_state');
  }

  void _clearSavedState() {
    print('🔥 FriendsTab: Clearing saved state');
    _bucket.writeState(context, null, identifier: 'friends_tab_state');
  }

  // Filter options
  final List<String> _filterOptions = ['All', 'Online', 'Recent'];

  void _onFilterSelected(String filter) {
    print('🔥 FriendsTab: Filter selected: $filter');
    setState(() {
      _currentFilter = filter;
    });
  }

  List<FriendshipData> _getFilteredFriends(FriendsProvider provider) {
    // Use search results only for queries with at least 3 characters
    final trimmed = _searchQuery.trim();
    final isSearchActive = trimmed.length >= 3;
    List<FriendshipData> friends = isSearchActive
        ? provider.searchResults
        : provider.friends;
    
    // Deduplicate friends by friendId to prevent duplicate Hero tags
    final seenIds = <String>{};
    friends = friends.where((friend) {
      if (seenIds.contains(friend.friendId)) {
        print('🚨 [FriendsTab] Removing duplicate friend: ${friend.name} (ID: ${friend.friendId})');
        return false;
      }
      seenIds.add(friend.friendId);
      return true;
    }).toList();
    
    switch (_currentFilter) {
      case 'Online':
        friends = friends.where((f) => f.isOnline || (f.lastActive != null && DateTime.now().difference(f.lastActive!).inMinutes < 15)).toList();
        break;
      case 'Recent':
        friends = List.from(friends)
          ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        break;
    }
    
    return friends;
  }

  Widget _buildLoadingShimmer() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Shimmer.fromColors(
      baseColor: isDark ? Colors.grey[800]! : Colors.grey[300]!,
      highlightColor: isDark ? Colors.grey[700]! : Colors.grey[100]!,
      child: ListView.builder(
        itemCount: 3,
        padding: const EdgeInsets.all(16),
        itemBuilder: (context, index) => Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Container(
            height: 80,
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[850] : Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final friendsProvider = Provider.of<FriendsProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUserId = authProvider.currentUser?.id;
    
    // Show loading shimmer if provider is still initializing
    if (!friendsProvider.isInitialized || 
        (friendsProvider.isLoading && friendsProvider.friends.isEmpty)) {
      return _buildLoadingShimmer();
    }

    if (friendsProvider.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              friendsProvider.error!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => friendsProvider.loadFriends(),
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ],
        ),
      );
    }

    final filteredFriends = _getFilteredFriends(friendsProvider);

    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final isSmallScreen = MediaQuery.of(context).size.width < 360;
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    return RefreshIndicator(
      onRefresh: () async {
        print('🔥 FriendsTab: Refreshing and resetting filter to All');
        // Clear saved state and reset filter to All
        _clearSavedState();
        setState(() {
          _searchQuery = '';
          _currentFilter = 'All';
        });
        // Refresh both friends and requests
        await friendsProvider.refresh();
      },
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          // Filter chips section
          SliverToBoxAdapter(
            child: Container(
              width: double.infinity,
              height: isSmallScreen ? 40 : 48,
              decoration: BoxDecoration(
                color: theme.scaffoldBackgroundColor,
              ),
              padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8.0 : 12.0),
              margin: EdgeInsets.zero,
              child: Stack(
                children: [
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: _filterOptions.map((filter) {
                        final isSelected = _currentFilter == filter;
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: ChoiceChip(
                            label: Text(
                              filter,
                              style: TextStyle(
                                fontSize: isSmallScreen ? 11 : 12,
                                fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                              ),
                            ),
                            selected: isSelected,
                            backgroundColor: theme.colorScheme.surface,
                            selectedColor: theme.colorScheme.primary.withOpacity(0.1),
                            labelStyle: TextStyle(
                              color: isSelected
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.onSurface,
                            ),
                            onSelected: (selected) {
                              if (selected) {
                                print('🔥 FriendsTab: Filter chip selected: $filter');
                                _onFilterSelected(filter);
                              }
                            },
                            padding: EdgeInsets.symmetric(
                              horizontal: isSmallScreen ? 6 : 8,
                              vertical: 0,
                            ),
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            visualDensity: VisualDensity.compact,
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  Positioned(
                    right: 0,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            theme.scaffoldBackgroundColor.withOpacity(0.0),
                            theme.scaffoldBackgroundColor,
                            theme.scaffoldBackgroundColor,
                          ],
                          stops: const [0.0, 0.3, 1.0],
                        ),
                      ),
                      padding: const EdgeInsets.only(left: 24),
                      child: Center(
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              // TODO: Navigate to user search screen
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const UserSearchScreen(),
                                ),
                              );
                            },
                            borderRadius: BorderRadius.circular(20),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.person_add_rounded,
                                size: isSmallScreen ? 20 : 22,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Shuffle & Add Recommendation Section
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // Add Recommendation Button (Secondary)
                  Expanded(
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () => _onAddRecommendation(context),
                        borderRadius: BorderRadius.circular(22),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: EdgeInsets.symmetric(
                            horizontal: isSmallScreen ? 14 : 16,
                            vertical: isSmallScreen ? 11 : 13,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(22),
                            border: Border.all(
                              color: theme.colorScheme.primary.withOpacity(0.2),
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.add_circle_outline_rounded,
                                color: theme.colorScheme.primary,
                                size: isSmallScreen ? 18 : 20,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'Add Rec',
                                style: TextStyle(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.w600,
                                  fontSize: isSmallScreen ? 13 : 14,
                                  letterSpacing: 0.2,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 12), // Proper spacing between buttons
                  
                  // Shuffle Recommendations Button (Primary)
                  Expanded(
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () => _onShuffleRecommendations(context),
                        borderRadius: BorderRadius.circular(22),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: EdgeInsets.symmetric(
                            horizontal: isSmallScreen ? 14 : 16,
                            vertical: isSmallScreen ? 11 : 13,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                theme.colorScheme.primary,
                                theme.colorScheme.primary.withOpacity(0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(22),
                            boxShadow: [
                              BoxShadow(
                                color: theme.colorScheme.primary.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                                spreadRadius: 0,
                              ),
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.shuffle_rounded,
                                color: theme.colorScheme.onPrimary,
                                size: isSmallScreen ? 18 : 20,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'Shuffle Recs',
                                style: TextStyle(
                                  color: theme.colorScheme.onPrimary,
                                  fontWeight: FontWeight.w600,
                                  fontSize: isSmallScreen ? 13 : 14,
                                  letterSpacing: 0.2,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Friends list
          if (filteredFriends.isEmpty)
            SliverFillRemaining(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _searchQuery.trim().length >= 3 ? Icons.search_off : Icons.people_outline,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _searchQuery.trim().length >= 3
                          ? 'No friends found matching "${_searchQuery.trim()}"'
                          : 'No friends yet',
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    if (_searchQuery.trim().length < 3) ...[
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const UserSearchScreen(),
                            ),
                          );
                        },
                        icon: const Icon(Icons.person_add),
                        label: const Text('Find Friends'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          elevation: 0,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            )
          else
            SliverPadding(
              padding: const EdgeInsets.only(top: 12),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) => _buildFriendCard(
                    context,
                    filteredFriends[index],
                    index,
                    isSmallScreen,
                  ),
                  childCount: filteredFriends.length,
                ),
              ),
            ),
            
          // Bottom padding
          SliverToBoxAdapter(
            child: SizedBox(height: bottomPadding + (isSmallScreen ? 80 : 90)),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFriendCard(
    BuildContext context,
    FriendshipData friendship,
    int index,
    bool isSmallScreen
  ) {
    final theme = Theme.of(context);
    final bool isOnline = friendship.isOnline;
    final DateTime? latestPinTime = friendship.latestPinTime;
    final timeDiff = latestPinTime != null 
        ? DateTime.now().difference(latestPinTime)
        : Duration.zero;
    final String timeAgo = _getTimeAgo(timeDiff);
    final isDark = theme.brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isDark 
            ? theme.colorScheme.surface.withOpacity(0.7)
            : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark
              ? theme.colorScheme.primary.withOpacity(0.1)
              : theme.colorScheme.primary.withOpacity(0.05),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: () {
            final user = User(
              id: int.parse(friendship.friendId),
              username: friendship.name,
              email: '',  // Not needed for chat
              isVerified: true,
              profilePicUrl: friendship.avatarUrl,
              bio: null,  // Bio is not available in FriendshipData
              createdAt: DateTime.now(),  // Not needed for chat
              favoriteGenres: [],  // Not needed for chat
              connectedServices: {},  // Not needed for chat
            );
            
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => MusicChatScreen(friend: user),
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 12,
              vertical: isSmallScreen ? 10 : 12,
            ),
            child: Row(
              children: [
                // Profile picture with online indicator
                Stack(
                  children: [
                    Hero(
                      tag: 'friends_tab_avatar_${friendship.friendId}_index_$index',
                      child: Container(
                        width: isSmallScreen ? 44 : 48,
                        height: isSmallScreen ? 44 : 48,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: theme.colorScheme.primary.withOpacity(0.2),
                            width: 1.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: theme.colorScheme.primary.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(24),
                          child: friendship.avatarUrl != null && friendship.avatarUrl!.isNotEmpty
                              ? Image.network(
                                  friendship.avatarUrl!,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) => Container(
                                    color: theme.colorScheme.primary.withOpacity(0.1),
                                    child: Icon(
                                      Icons.person,
                                      size: isSmallScreen ? 22 : 24,
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                  loadingBuilder: (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Container(
                                      color: theme.colorScheme.surface,
                                      child: Center(
                                        child: SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(
                                              theme.colorScheme.primary.withOpacity(0.7),
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                )
                              : Container(
                                  color: theme.colorScheme.primary.withOpacity(0.1),
                                  child: Icon(
                                    Icons.person,
                                    size: isSmallScreen ? 22 : 24,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                        ),
                      ),
                    ),
                    if (isOnline)
                      Positioned(
                        right: 0,
                        bottom: 0,
                        child: Container(
                          width: isSmallScreen ? 12 : 14,
                          height: isSmallScreen ? 12 : 14,
                          decoration: BoxDecoration(
                            color: Colors.green.shade400,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: theme.colorScheme.surface,
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: 12),
                
                // Friend info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Just the friend's name - no username
                      Text(
                        friendship.name,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 13 : 14,
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface.withOpacity(0.9),
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primaryContainer.withOpacity(isDark ? 0.2 : 0.15),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.push_pin_rounded,
                                  size: 12,
                                  color: theme.colorScheme.primary.withOpacity(0.8),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${friendship.pinsCount} pins',
                                  style: TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                    color: theme.colorScheme.primary.withOpacity(0.8),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (friendship.latestPin != null) ...[
                            const SizedBox(width: 8),
                            Flexible(
                              child: Text(
                                '${friendship.latestPin} • $timeAgo',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Action buttons with visual feedback
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Map button
                    Material(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => MyBOPMap(
                                userId: friendship.friendId,
                                username: friendship.name,
                              ),
                            ),
                          );
                        },
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            Icons.public_rounded,
                            size: isSmallScreen ? 20 : 22,
                            color: theme.colorScheme.primary.withOpacity(0.9),
                          ),
                        ),
                      ),
                    ),
                    // More options button
                    Material(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          showModalBottomSheet(
                            context: context,
                            builder: (context) {
                              return SafeArea(
                                child: Wrap(
                                  children: [
                                    ListTile(
                                      leading: const Icon(Icons.person_remove, color: Colors.red),
                                      title: Text('Unfriend ${friendship.name}'),
                                      onTap: () async {
                                        Navigator.pop(context);
                                        final confirmed = await showDialog<bool>(
                                          context: context,
                                          builder: (context) => AlertDialog(
                                            title: Text('Unfriend ${friendship.name}?'),
                                            content: Text('Are you sure you want to unfriend ${friendship.name}?'),
                                            actions: [
                                              TextButton(
                                                onPressed: () => Navigator.of(context).pop(false),
                                                child: const Text('No'),
                                              ),
                                              TextButton(
                                                onPressed: () => Navigator.of(context).pop(true),
                                                child: const Text('Yes, Unfriend'),
                                                style: TextButton.styleFrom(foregroundColor: Colors.red),
                                              ),
                                            ],
                                          ),
                                        );
                                        if (confirmed == true) {
                                          final success = await context.read<FriendsProvider>().unfriend(friendship.friendId);
                                          if (success && mounted) {
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              const SnackBar(content: Text('Friend removed'), backgroundColor: Colors.red),
                                            );
                                          }
                                        }
                                      },
                                    ),
                                  ],
                                ),
                              );
                            },
                          );
                        },
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            Icons.more_horiz,
                            size: isSmallScreen ? 20 : 22,
                            color: theme.colorScheme.onSurface.withOpacity(0.5),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  String _getTimeAgo(Duration difference) {
    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onPressed,
    {required bool isPrimary,
    required bool isSmallScreen}
  ) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 12 : 16,
            vertical: isSmallScreen ? 8 : 10,
          ),
          decoration: BoxDecoration(
            color: isPrimary
                ? theme.colorScheme.primary
                : (isDark ? Colors.white.withOpacity(0.1) : Colors.black.withOpacity(0.05)),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isPrimary
                  ? Colors.transparent
                  : theme.colorScheme.primary.withOpacity(isDark ? 0.3 : 0.2),
              width: 1,
            ),
            boxShadow: isPrimary
                ? [
                    BoxShadow(
                      color: theme.colorScheme.primary.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: isSmallScreen ? 16 : 18,
                color: isPrimary
                    ? Colors.white
                    : theme.colorScheme.primary,
              ),
              SizedBox(width: isSmallScreen ? 6 : 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: isSmallScreen ? 13 : 14,
                  fontWeight: FontWeight.w600,
                  color: isPrimary
                      ? Colors.white
                      : theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Bop Drops API integration methods
  
  Future<void> _onShuffleRecommendations(BuildContext context) async {
    try {
      HapticFeedback.mediumImpact();
      
      // Show loading state
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
              const SizedBox(width: 12),
              const Text('Shuffling recommendations...'),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.primary,
          duration: const Duration(seconds: 3),
        ),
      );
      
      // Use combined feed that includes user's own bop drops
      final combinedDrops = await BopDropsService.getCombinedFeed(includeOwnDrops: true, pageSize: 15);
      
      if (combinedDrops.isNotEmpty && mounted) {
        // Start shuffling with combined recommendations (friends + own)
        final spotifyProvider = context.read<SpotifyProvider>();
        final appleMusicProvider = context.read<AppleMusicProvider>();
        
        final shuffleResult = await ShuffleService.shuffleRecommendations(
          bopDrops: combinedDrops,
          spotifyProvider: spotifyProvider,
          appleMusicProvider: appleMusicProvider,
          mode: ShuffleMode.random,
          clearExistingQueue: false,
          maxTracks: 15,
        );
        
        if (mounted) {
          ScaffoldMessenger.of(context).clearSnackBars();
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                shuffleResult.success 
                    ? '🎵 Shuffled ${shuffleResult.tracksQueued} tracks from ${combinedDrops.length} recommendations!'
                    : 'Failed to shuffle: ${shuffleResult.message}',
              ),
              backgroundColor: shuffleResult.success 
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.error,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('No recommendations available to shuffle'),
            backgroundColor: Theme.of(context).colorScheme.secondary,
          ),
        );
      }
      
    } catch (e) {
      print('Error shuffling recommendations: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to shuffle recommendations: ${e.toString().replaceAll('Exception: ', '')}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _onAddRecommendation(BuildContext context) async {
    try {
      HapticFeedback.lightImpact();
      
      // Navigate to the BopDropScreen
      final result = await Navigator.push<bool>(
        context,
        MaterialPageRoute(
          builder: (context) => const BopDropScreen(),
        ),
      );
      
      // If a bop drop was successfully created, show confirmation and refresh
      if (result == true && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Bop drop shared successfully! 🎵'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: const Duration(seconds: 2),
          ),
        );
        
        // Optionally refresh the friends list to show updated data
        final friendsProvider = context.read<FriendsProvider>();
        friendsProvider.loadFriendsIfNeeded();
      }
      
    } catch (e) {
      print('Error navigating to bop drop screen: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to open bop drop screen'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
} 