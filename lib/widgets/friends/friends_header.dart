import 'package:bop_maps/screens/search/ai_search/ai_search_provider.dart';
import 'package:bop_maps/screens/search/ai_search/ai_search_view.dart';
import '../../services/ai/global_ai_provider_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui';
import '../../config/themes.dart';
import '../../models/user_activity.dart';
import '../../models/music/bop_drop.dart';
import '../../models/music/my_bop_drop.dart';
import '../../models/music_track.dart';
import '../../services/api/bop_drops_service.dart';
import '../../widgets/map/components/map_control_button.dart';
import '../../screens/profile/settings_screen.dart';
import '../../screens/map/my_bop_map.dart';
import 'package:provider/provider.dart';
import '../../providers/settings_provider.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/recommendation_context_provider.dart';
import '../../services/music/shuffle_service.dart';
import 'friends_header_shimmer.dart'; // Import the new shimmer widget
import 'friend_bop_drops_bottom_sheet.dart';
import 'my_bop_drops_bottom_sheet.dart';

// Real data structure for friend bop recommendations from API
class FriendBopRecommendation {
  final String id;
  final String name;
  final String? avatarUrl;
  final int newBopsCount;
  final DateTime lastRecommendation;
  final bool hasNewBops;
  final BopDrop? lastBopDrop;
  final bool isCurrentUser; // Add this flag

  const FriendBopRecommendation({
    required this.id,
    required this.name,
    this.avatarUrl,
    required this.newBopsCount,
    required this.lastRecommendation,
    required this.hasNewBops,
    this.lastBopDrop,
    this.isCurrentUser = false, // Default to false
  });

  factory FriendBopRecommendation.fromBopDropData(FriendRecommendationData data) {
    return FriendBopRecommendation(
      id: data.id,
      name: data.name,
      avatarUrl: data.avatar.isNotEmpty ? data.avatar : null,
      newBopsCount: data.count,
      lastRecommendation: data.lastBopDrop?.createdAt ?? DateTime.now(),
      hasNewBops: data.hasNew && data.count > 0,
      lastBopDrop: data.lastBopDrop,
      isCurrentUser: data.isCurrentUser ?? false,
    );
  }

  // Add factory for current user's bops
  factory FriendBopRecommendation.currentUser(MyBopDropData data) {
    return FriendBopRecommendation(
      id: data.userId,
      name: "My Bops",
      avatarUrl: data.userAvatar,
      newBopsCount: data.totalDrops,
      lastRecommendation: data.lastBopDrop?.createdAt ?? DateTime.now(),
      hasNewBops: true, // Always show as active
      lastBopDrop: data.lastBopDrop,
      isCurrentUser: true,
    );
  }
}

class FriendsHeader extends StatefulWidget {
  final Size size;
  final List<UserActivity>? activeUsers;
  final VoidCallback? onCreateChallenge;
  final VoidCallback? onOpenSkins;

  const FriendsHeader({
    Key? key,
    required this.size,
    this.activeUsers,
    this.onCreateChallenge,
    this.onOpenSkins,
  }) : super(key: key);

  @override
  State<FriendsHeader> createState() => _FriendsHeaderState();
}

class _FriendsHeaderState extends State<FriendsHeader> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _gradientAnimation;
  
  List<FriendBopRecommendation> _friendsWithBops = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _gradientAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);
    
    // Start subtle animation for gradient borders
    _animationController.repeat(reverse: true);
    
    // Load real bop drops data
    _loadFriendBopRecommendations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadFriendBopRecommendations() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // First, load the user's own bop drops
      final myBopDrops = await BopDropsService.getMyBopDrop();
      if (kDebugMode) {
        print('🎧 [FriendsHeader] ----- MyBopDrop API Raw Data -----');
        if (myBopDrops == null) {
          print('🎧 [FriendsHeader] myBopDrops is null');
        } else {
          print('🎧 [FriendsHeader] userId: ${myBopDrops.userId}');
          print('🎧 [FriendsHeader] totalDrops: ${myBopDrops.totalDrops}');
          print('🎧 [FriendsHeader] lastBopDrop id: ${myBopDrops.lastBopDrop?.id}');
          print('🎧 [FriendsHeader] lastBopDrop createdAt: ${myBopDrops.lastBopDrop?.createdAt}');
        }
        print('🎧 [FriendsHeader] -------------------------------');
      }
      
      // Then load friend recommendations
      final friendRecommendationsMap = await BopDropsService.getFriendRecommendations();
      final allFriendRecommendations = friendRecommendationsMap.values
          .map((data) => FriendBopRecommendation.fromBopDropData(data))
          .toList();
      
      if (kDebugMode) {
        print('🎭 [FriendsHeader] === RECOMMENDATION FILTERING DEBUG ===');
        print('🎭 [FriendsHeader] Total friends with bops: ${allFriendRecommendations.length}');
        for (final friend in allFriendRecommendations) {
          print('🎭 [FriendsHeader] ${friend.name}: hasNewBops=${friend.hasNewBops}, count=${friend.newBopsCount}');
        }
      }
      
      final friendRecommendations = allFriendRecommendations
          .where((friend) => friend.hasNewBops) // Only show friends with new bops
          .toList();

      // Sort by most recent recommendations first
      friendRecommendations.sort((a, b) => b.lastRecommendation.compareTo(a.lastRecommendation));

      // Add user's bop drops at the start if they exist
      if (myBopDrops != null) {
        final userBops = FriendBopRecommendation.currentUser(myBopDrops);
        if (kDebugMode) {
          print('🎧 [FriendsHeader] === CREATING CURRENT USER BOP RECOMMENDATION ===');
          print('🎧 [FriendsHeader] Created userBops.isCurrentUser: ${userBops.isCurrentUser}');
          print('🎧 [FriendsHeader] userBops.name: ${userBops.name}');
          print('🎧 [FriendsHeader] userBops.id: ${userBops.id}');
          print('🎧 [FriendsHeader] ===============================================');
        }
        friendRecommendations.insert(0, userBops);
      }

      if (mounted) {
        setState(() {
          _friendsWithBops = friendRecommendations;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load recommendations';
          _friendsWithBops = [];
        });
      }
      print('Error loading bop recommendations: $e');
    }
  }

  Future<void> _onShuffleRecommendations() async {
    try {
      HapticFeedback.mediumImpact();
      final shuffledDrops = await BopDropsService.getShuffledBopDrops(limit: 10);
      
      if (shuffledDrops.isNotEmpty && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Found ${shuffledDrops.length} shuffled recommendations!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
        // TODO: Navigate to shuffled recommendations view
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to shuffle recommendations'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _onAddRecommendation() async {
    try {
      HapticFeedback.lightImpact();
      // TODO: Navigate to music selection/search screen for adding recommendation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Opening music selection...'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open music selection'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      height: isSmallScreen ? 210 : 220, // Expanded height for better avatar visibility
      color: theme.scaffoldBackgroundColor,
      child: Stack(
        children: [
          // Settings button (top left)
          Positioned(
            top: isSmallScreen ? 8 : 12,
            left: 12,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.primary.withOpacity(0.08),
                    blurRadius: 6,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: MapControlButton(
                icon: Icons.settings,
                tooltip: 'Settings',
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SettingsScreen(showBottomNav: true),
                    ),
                  );
                },
                isTransparent: true,
                foregroundColor: theme.colorScheme.onSurface,
              ),
            ),
          ),

          // Search button (top right)
          Positioned(
            top: isSmallScreen ? 8 : 12,
            right: 12,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.primary.withOpacity(0.08),
                    blurRadius: 6,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: MapControlButton(
                icon: Icons.search,
                tooltip: 'Search',
                onPressed: () async {
                  // Use global AI provider instead of creating a new instance
                  final globalAIProvider = GlobalAIProviderService.instance;
                  
                  // Make sure the global provider is initialized
                  if (!globalAIProvider.isInitialized) {
                    await globalAIProvider.initializeIfAuthenticated(context);
                  }
                  
                  if (!mounted) return;
                  
                  Navigator.push(
                    context,
                    PageRouteBuilder(
                      pageBuilder: (context, animation, secondaryAnimation) => 
                          const AISearchView(),
                      transitionsBuilder: (context, animation, secondaryAnimation, child) {
                        return SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0.0, 1.0),
                            end: Offset.zero,
                          ).animate(CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeInOut,
                          )),
                          child: child,
                        );
                      },
                      transitionDuration: const Duration(milliseconds: 300),
                    ),
                  );
                },
                isTransparent: true,
                foregroundColor: theme.colorScheme.onSurface,
              ),
            ),
          ),

          // Title - Higher up 
          Positioned(
            top: isSmallScreen ? 8 : 12,
            left: 0,
            right: 0,
            child: Center(
              child: Text(
                'Recommendations',
                style: TextStyle(
                  fontSize: isSmallScreen ? 14 : 15,
                  fontWeight: FontWeight.w700,
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                  letterSpacing: -0.2,
                ),
              ),
            ),
          ),

          // Friends stories - More space allocated
          Positioned(
            top: isSmallScreen ? 60 : 65, // Higher position for more space
            left: 0,
            right: 0,
            bottom: 12, // More bottom space
            child: _isLoading
                ? FriendsHeaderShimmer(isSmallScreen: isSmallScreen) // Use the new shimmer widget
                : _friendsWithBops.any((friend) => friend.hasNewBops)
                    ? SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: _friendsWithBops
                            .fold<Map<String, FriendBopRecommendation>>({}, (map, friend) {
                              // Keep only the first occurrence of each user ID
                              if (!map.containsKey(friend.id)) {
                                map[friend.id] = friend;
                              }
                              
                              return map;
                            })
                            .values
                            .map((friend) => _buildFriendBopStatus(context, friend, isSmallScreen))
                            .toList(),
                        ),
                      )
                    : _buildEmptyState(context, isSmallScreen),
          ),
        ],
      ),
    );
  }

  Widget _buildFriendBopStatus(BuildContext context, FriendBopRecommendation friend, bool isSmallScreen) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final avatarSize = isSmallScreen ? 50.0 : 54.0;
    
    // Special gradient for current user's bops
    final List<Color> gradientColors = friend.isCurrentUser ? [
      theme.colorScheme.primary,
      theme.colorScheme.tertiary,
      theme.colorScheme.secondary,
      theme.colorScheme.primary,
    ] : [
      theme.colorScheme.primary,
      theme.colorScheme.secondary,
      theme.colorScheme.tertiary,
      theme.colorScheme.primary,
    ];
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2),
      child: SizedBox(
        width: avatarSize + 16,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Center(
              child: GestureDetector(
                onTap: () {
                  HapticFeedback.mediumImpact();
                  _showFriendBopRecommendations(context, friend);
                },
                child: Container(
                  width: avatarSize + 4,
                  height: avatarSize + 4,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Animated gradient border
                      if (friend.hasNewBops)
                        AnimatedBuilder(
                          animation: _gradientAnimation,
                          builder: (context, child) => Container(
                            width: avatarSize + 4,
                            height: avatarSize + 4,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: gradientColors,
                                stops: [
                                  _gradientAnimation.value * 0.3,
                                  (_gradientAnimation.value * 0.3) + 0.3,
                                  (_gradientAnimation.value * 0.3) + 0.6,
                                  1.0,
                                ],
                              ),
                              // Add extra glow for current user
                              boxShadow: friend.isCurrentUser ? [
                                BoxShadow(
                                  color: theme.colorScheme.primary.withOpacity(0.3),
                                  blurRadius: 8,
                                  spreadRadius: 2,
                                ),
                              ] : null,
                            ),
                          ),
                        ),
                      
                      // Avatar container
                      Container(
                        width: avatarSize,
                        height: avatarSize,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: theme.colorScheme.surface,
                          border: friend.hasNewBops ? null : Border.all(
                            color: theme.colorScheme.onSurface.withOpacity(0.15),
                            width: 1.5,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(avatarSize / 2),
                          child: friend.avatarUrl != null && friend.avatarUrl!.isNotEmpty
                              ? Image.network(
                                  friend.avatarUrl!,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) => _buildAvatarFallback(theme, friend.name, avatarSize),
                                  loadingBuilder: (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Container(
                                      width: avatarSize,
                                      height: avatarSize,
                                      color: theme.colorScheme.surface,
                                      child: Center(
                                        child: SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(
                                              theme.colorScheme.primary.withOpacity(0.7),
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                )
                              : _buildAvatarFallback(theme, friend.name, avatarSize),
                        ),
                      ),
                      
                      // New bops count badge
                      if (friend.hasNewBops && friend.newBopsCount > 0)
                        Positioned(
                          top: -1,
                          right: -1,
                          child: Container(
                            padding: const EdgeInsets.all(3),
                            decoration: BoxDecoration(
                              color: friend.isCurrentUser 
                                  ? theme.colorScheme.tertiary 
                                  : theme.colorScheme.primary,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: theme.scaffoldBackgroundColor,
                                width: 1.5,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.15),
                                  blurRadius: 3,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Text(
                              friend.newBopsCount.toString(),
                              style: TextStyle(
                                color: theme.colorScheme.onPrimary,
                                fontSize: 9,
                                fontWeight: FontWeight.w800,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            
            SizedBox(height: isSmallScreen ? 3 : 4),
            
            // Friend name with special style for current user
            Flexible(
              child: Container(
                width: avatarSize + 16,
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(horizontal: 2),
                child: Text(
                  friend.name,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 9.0 : 10.0,
                    fontWeight: friend.isCurrentUser ? FontWeight.w700 : FontWeight.w600,
                    color: friend.isCurrentUser 
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withOpacity(0.7),
                    letterSpacing: -0.1,
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatarFallback(ThemeData theme, String name, double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(0.7),
            theme.colorScheme.secondary.withOpacity(0.7),
          ],
        ),
      ),
      child: Center(
        child: Text(
          name.isNotEmpty ? name[0].toUpperCase() : '?',
          style: TextStyle(
            color: Colors.white,
            fontSize: size * 0.4,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isSmallScreen) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.music_note_outlined,
            size: isSmallScreen ? 24 : 28,
            color: theme.colorScheme.onSurface.withOpacity(0.4),
          ),
          SizedBox(height: isSmallScreen ? 4 : 6),
          Text(
            _errorMessage ?? 'No new bop recommendations',
            style: TextStyle(
              fontSize: isSmallScreen ? 11 : 12,
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: isSmallScreen ? 2 : 3),
          Text(
            _errorMessage != null 
                ? 'Pull to refresh and try again'
                : 'Check back later for friends\' picks!',
            style: TextStyle(
              fontSize: isSmallScreen ? 9 : 10,
              color: theme.colorScheme.onSurface.withOpacity(0.4),
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _showFriendBopRecommendations(BuildContext context, FriendBopRecommendation friend) async {
    try {
      if (kDebugMode) {
        print('🎯 [FriendsHeader] ======== INDIVIDUAL FRIEND CLICK ========');
        print('🎯 [FriendsHeader] Friend: ${friend.name} (ID: ${friend.id})');
        print('🎯 [FriendsHeader] isCurrentUser: ${friend.isCurrentUser}');
        print('🎯 [FriendsHeader] Opening bottom sheet with all bop drops');
        print('🎯 [FriendsHeader] ============================================');
      }
      
      HapticFeedback.mediumImpact();
      
      if (friend.isCurrentUser) {
        if (kDebugMode) {
          print('🎯 [FriendsHeader] ✅ Using MyBopDropsBottomSheet for current user');
        }
        // Show my bop drops with engagement data
        await showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => MyBopDropsBottomSheet(
            userId: friend.id,
            userName: friend.name,
            userAvatarUrl: friend.avatarUrl,
          ),
        );
      } else {
        if (kDebugMode) {
          print('🎯 [FriendsHeader] ⚠️ Using FriendBopDropsBottomSheet for friend');
        }
        // Show the bottom sheet with all the friend's bop drops
        await showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => FriendBopDropsBottomSheet(
            friendId: friend.id,
            friendName: friend.name,
            friendAvatarUrl: friend.avatarUrl,
            initialBopDrop: friend.lastBopDrop,
          ),
        );
      }
      
      if (kDebugMode) {
        print('🎯 [FriendsHeader] Bottom sheet closed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🚨 [FriendsHeader] ======== CRITICAL ERROR ========');
        print('🚨 [FriendsHeader] Friend: ${friend.name} (ID: ${friend.id})');
        print('🚨 [FriendsHeader] Error: $e');
        print('🚨 [FriendsHeader] ==============================');
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening ${friend.name}\'s music: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}

// Add custom painter for background pattern
class BopMapPatternPainter extends CustomPainter {
  final Color color;
  
  BopMapPatternPainter({required this.color});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;
    
    const spacing = 20.0;
    final rows = (size.height / spacing).ceil();
    final cols = (size.width / spacing).ceil();
    
    // Draw grid pattern
    for (var i = 0; i < rows; i++) {
      for (var j = 0; j < cols; j++) {
        final x = j * spacing;
        final y = i * spacing;
        
        // Draw music note pattern
        if ((i + j) % 2 == 0) {
          canvas.drawCircle(
            Offset(x + spacing / 2, y + spacing / 2),
            2,
            paint..style = PaintingStyle.fill,
          );
        }
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 