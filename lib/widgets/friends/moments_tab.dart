import 'package:flutter/material.dart';
import '../../models/memory_group.dart';
import 'memory_group_list.dart';

class MomentsTab extends StatelessWidget {
  const MomentsTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Moments',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                // Optionally add a filter or search button here
              ],
            ),
          ),
          Expanded(
            child: MemoryGroupList(groups: const []), // Placeholder for now
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // TODO: Show create memory group dialog
        },
        icon: const Icon(Icons.add),
        label: const Text('New Moment'),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      ),
    );
  }
} 