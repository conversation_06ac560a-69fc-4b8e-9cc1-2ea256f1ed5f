import 'package:flutter/material.dart';
import '../../models/memory_group.dart';

class MemoryGroupCard extends StatelessWidget {
  final MemoryGroup group;
  final VoidCallback? onTap;
  const MemoryGroupCard({Key? key, required this.group, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cover = group.coverImage;
    final memberAvatars = group.members.take(5).toList();
    return InkWell(
      borderRadius: BorderRadius.circular(16),
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Row(
          children: [
            // Cover image or placeholder
            Container(
              width: 72,
              height: 72,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.horizontal(left: Radius.circular(16)),
                color: Theme.of(context).colorScheme.primary.withOpacity(0.13),
                image: cover != null
                    ? DecorationImage(image: NetworkImage(cover), fit: BoxFit.cover)
                    : null,
              ),
              child: cover == null
                  ? Icon(Icons.group, size: 36, color: Theme.of(context).colorScheme.primary)
                  : null,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      group.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        ...memberAvatars.map((user) => Padding(
                              padding: const EdgeInsets.only(right: 4),
                              child: CircleAvatar(
                                radius: 13,
                                backgroundImage: user.profilePicUrl != null ? NetworkImage(user.profilePicUrl!) : null,
                                backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.18),
                                child: user.profilePicUrl == null ? Icon(Icons.person, size: 16, color: Theme.of(context).colorScheme.primary) : null,
                              ),
                            )),
                        if (group.members.length > 5)
                          Text('+${group.members.length - 5}', style: Theme.of(context).textTheme.bodySmall),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '${group.pins.length} memories',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 