import 'package:flutter/material.dart';

/// Custom painter for drawing a beautiful music pin icon
class MusicPinIconPainter extends CustomPainter {
  final Color color;
  final Color shadowColor;
  
  MusicPinIconPainter({
    required this.color,
    required this.shadowColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final Paint mainPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
      
    final Paint shadowPaint = Paint()
      ..color = shadowColor
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);
    
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.25;
    
    // Draw shadow for pin shape
    final pinPath = Path();
    pinPath.moveTo(center.dx, center.dy + radius * 0.9);
    pinPath.quadraticBezierTo(
      center.dx - radius * 1.5, center.dy - radius * 0.8,
      center.dx, center.dy - radius * 1.2,
    );
    pinPath.quadraticBezierTo(
      center.dx + radius * 1.5, center.dy - radius * 0.8,
      center.dx, center.dy + radius * 0.9,
    );
    
    // Draw shadow offset slightly
    canvas.save();
    canvas.translate(1, 1);
    canvas.drawPath(pinPath, shadowPaint);
    canvas.restore();
    
    // Draw main pin shape 
    canvas.drawPath(pinPath, mainPaint);
    
    // Draw music note
    final noteSize = radius * 0.8;
    final noteCenterX = center.dx;
    final noteCenterY = center.dy - noteSize * 0.3;
    
    // Create a gradual note stem
    final stemPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [color.withOpacity(0.7), color],
      ).createShader(Rect.fromLTWH(
        noteCenterX - noteSize * 0.1,
        noteCenterY - noteSize * 0.8,
        noteSize * 0.2,
        noteSize * 1.2,
      ))
      ..style = PaintingStyle.fill;
    
    // Draw the note head shadow
    canvas.save();
    canvas.translate(1, 1);
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(noteCenterX, noteCenterY),
        width: noteSize * 0.6,
        height: noteSize * 0.5,
      ),
      shadowPaint,
    );
    canvas.restore();
    
    // Draw the note stem shadow
    canvas.save();
    canvas.translate(1, 1);
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          noteCenterX - noteSize * 0.1,
          noteCenterY - noteSize * 0.8,
          noteSize * 0.2,
          noteSize * 0.8,
        ),
        Radius.circular(noteSize * 0.1),
      ),
      shadowPaint,
    );
    canvas.restore();
    
    // Draw the note head
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(noteCenterX, noteCenterY),
        width: noteSize * 0.6,
        height: noteSize * 0.5,
      ),
      mainPaint,
    );
    
    // Draw the note stem
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          noteCenterX - noteSize * 0.1,
          noteCenterY - noteSize * 0.8,
          noteSize * 0.2,
          noteSize * 0.8,
        ),
        Radius.circular(noteSize * 0.1),
      ),
      stemPaint,
    );
    
    // Add a subtle highlight to the note
    final highlightPaint = Paint()
      ..color = color.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
      
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(noteCenterX - noteSize * 0.05, noteCenterY - noteSize * 0.05),
        width: noteSize * 0.3,
        height: noteSize * 0.25,
      ),
      highlightPaint,
    );
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
} 