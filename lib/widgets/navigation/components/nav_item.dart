import 'package:flutter/material.dart';
import '../../../config/themes.dart';

/// A reusable navigation item for the bottom navigation bar
class NavItem extends StatelessWidget {
  final double width;
  final int index;
  final int currentIndex;
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final Function(int) onTabSelected;
  final bool isDarkMode;

  const NavItem({
    Key? key,
    required this.width,
    required this.index,
    required this.currentIndex,
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.onTabSelected,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isSelected = currentIndex == index;
    final iconColor = isSelected
        ? Theme.of(context).colorScheme.primary
        : isDarkMode
            ? Colors.grey[400]
            : Colors.grey[600];

    return GestureDetector(
      onTap: () => onTabSelected(index),
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: width,
        constraints: const BoxConstraints(
          minHeight: 48, // Reduced from 56 to 48
          maxHeight: 48, // Reduced from 56 to 48
        ),
        padding: const EdgeInsets.symmetric(vertical: 4), // Reduced from 8 to 4
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isSelected ? activeIcon : icon,
                color: iconColor,
                size: 22, // Slightly reduced from 24 to 22
              ),
              const SizedBox(height: 2), // Reduced from 4 to 2
              Text(
                label,
                style: TextStyle(
                  color: iconColor,
                  fontSize: 10, // Reduced from 11 to 10
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
} 