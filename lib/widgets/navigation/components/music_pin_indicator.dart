import 'package:flutter/material.dart';

/// Minimal indicator widget to match the sleek design
class MusicPinIndicator extends StatelessWidget {
  final bool isActive;
  final Color color;
  
  const MusicPinIndicator({
    Key? key,
    required this.isActive,
    required this.color,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: isActive ? 16 : 0,
      height: 3,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(1.5),
      ),
    );
  }
} 