import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../config/theme_constants.dart';
import '../../../providers/map_provider.dart';
import '../../../utils/app_tab.dart';
import 'music_pin_icon_painter.dart';

class AddPinButton extends StatefulWidget {
  final double width;
  final int currentIndex;
  final Function(int) onTabSelected;
  final VoidCallback? onAddPinPressed;
  final bool isDarkMode;
  
  // Static variables for handling tap debouncing across instances
  static DateTime? _lastAddPinTap;
  static bool _isAddPinInProgress = false;
  static const _cooldownPeriodMs = 500;
  
  const AddPinButton({
    Key? key,
    required this.width,
    required this.currentIndex,
    required this.onTabSelected,
    this.onAddPinPressed,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  State<AddPinButton> createState() => _AddPinButtonState();
}

class _AddPinButtonState extends State<AddPinButton> {
  // Variables to track swipe gesture
  double _dragStartY = 0;
  double _dragDistance = 0;
  bool _isDragging = false;
  
  // Threshold for swipe up gesture to trigger action (in pixels)
  static const double _swipeThreshold = 30.0;

  @override
  Widget build(BuildContext context) {
    // Get the map provider to access map state
    final mapProvider = Provider.of<MapProvider>(context, listen: true);
    
    // Enhanced detection of map screen state with fallback
    bool isOnMapScreen = widget.currentIndex == AppTab.map.index;

    // Fallback check for more robust detection
    if (!isOnMapScreen) {
      final navigator = Navigator.of(context);
      navigator.popUntil((route) {
        if (route.settings.name == '/map' ||
            (route.isFirst && (route.settings.name == null || route.settings.name == '/map'))) {
          isOnMapScreen = true;
        }
        return true; // Don't actually pop
      });
    }

    // If we're not on the map screen and hasAddPinIntent is true, force reset it
    // This ensures we don't have a stale intent when not on the map screen
    if (!isOnMapScreen && mapProvider.hasAddPinIntent) {
      // Use a post frame callback to avoid changing state during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        debugPrint('⚠️ Detected stuck pin intent while not on map screen - force resetting');
        mapProvider.forceResetPinIntent();
      });
    }
    
    // REMOVED RESTRICTIONS: Always allow adding pins when on map screen
    // Visual state for the button - always active on map screen
    final bool isAddPinActive = isOnMapScreen;

    // Create a stunning triple-color gradient with deeper, richer tones
    // Maintaining the cyan-blue tones while adding brand elements and darker accents
    final List<Color> buttonGradient = [
      const Color(0xFF09C3DB), // Deeper cyan for richness
      primaryColorLight.withOpacity(0.85), // Pink primary brand color
      const Color(0xFF3B7DD8), // Deeper, richer blue
    ];
    
    // Add a dark base undertone for more depth
    final Color darkUndertone = const Color(0xFF0A1A2F);
    
    // Set gradient stops for a more beautiful color blend
    final List<double> gradientStops = [0.0, 0.55, 1.0];
    
    // Apply the opacity to the entire button based on state
    final double buttonOpacity = isOnMapScreen ? 1.0 : 0.85;
    
    // Button tooltip based on context
    final String tooltip = isOnMapScreen 
        ? 'Drop Music Pin' 
        : 'Go to Map';
    
    return SizedBox(
      width: widget.width,
      height: 48, // Reduced from 56 to 48 to match nav items
      child: GestureDetector(
        // Add GestureDetector for vertical swipe
        onVerticalDragStart: (details) {
          _dragStartY = details.globalPosition.dy;
          _dragDistance = 0;
          _isDragging = true;
        },
        onVerticalDragUpdate: (details) {
          if (_isDragging) {
            _dragDistance = _dragStartY - details.globalPosition.dy;
            // Optional: add visual feedback during drag
            // This could be animating the button slightly or showing a hint
          }
        },
        onVerticalDragEnd: (details) {
          if (_isDragging && _dragDistance > _swipeThreshold) {
            // Swipe up detected, trigger the same action as tap
            debugPrint('⬆️ SWIPE UP DETECTED on Add Pin Button! Distance: $_dragDistance');
            _handleTap();
            
            // Add a small extra haptic feedback for swipe gesture
            HapticFeedback.lightImpact();
          }
          _isDragging = false;
        },
        // Make sure the gesture detector doesn't interfere with other gestures
        behavior: HitTestBehavior.translucent,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Subtle dark undertone shadow for depth
            Container(
              height: 56, // Reduced from 64 to 56
              width: 56,  // Reduced from 64 to 56
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: widget.isDarkMode ? darkUndertone.withOpacity(0.5) : Colors.transparent,
                boxShadow: widget.isDarkMode ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.4),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 3),
                  ),
                ] : null,
              ),
            ),
            
            // Button container - larger more prominent button with enhanced design
            AnimatedOpacity(
              duration: const Duration(milliseconds: 100),
              opacity: buttonOpacity,
              child: Container(
                height: 54, // Reduced from 62 to 54
                width: 54,  // Reduced from 62 to 54
                decoration: BoxDecoration(
                  // Use a sweeping angular gradient for more visual interest
                  gradient: SweepGradient(
                    colors: buttonGradient,
                    stops: gradientStops,
                    startAngle: 0.0,
                    endAngle: 3.14 * 2,
                    center: Alignment(0.0, 0.0),
                  ),
                  shape: BoxShape.circle,
                  boxShadow: widget.isDarkMode ? [
                    // Deep shadow undertone for richness
                    BoxShadow(
                      color: darkUndertone.withOpacity(0.6),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                    // Top-left highlight for 3D effect
                    BoxShadow(
                      color: buttonGradient[0].withOpacity(0.5),
                      blurRadius: 15,
                      spreadRadius: 0,
                      offset: const Offset(-3, -3),
                    ),
                    // Bottom-right shadow for depth
                    BoxShadow(
                      color: buttonGradient[2].withOpacity(0.6),
                      blurRadius: 12,
                      spreadRadius: 0,
                      offset: const Offset(3, 3),
                    ),
                    // Additional outer glow with brand color
                    BoxShadow(
                      color: musicPinColor.withOpacity(0.25),
                      blurRadius: 20,
                      spreadRadius: 0,
                    ),
                    // Add inner glow effect (slightly reduced)
                    BoxShadow(
                      color: Colors.white.withOpacity(0.18),
                      blurRadius: 20,
                      spreadRadius: -5,
                    ),
                  ] : [],
                  // Add subtle darker border for definition
                  border: Border.all(
                    color: widget.isDarkMode ? darkUndertone.withOpacity(0.4) : Colors.grey.withOpacity(0.2),
                    width: widget.isDarkMode ? 1 : 0.5,
                  ),
                ),
                // Second container for overlay effects
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    // Add subtle radial gradient overlay with deeper dark tones
                    gradient: RadialGradient(
                      colors: [
                        Colors.white.withOpacity(0.12),
                        darkUndertone.withOpacity(0.05),
                        Colors.transparent,
                      ],
                      stops: const [0.0, 0.4, 0.8],
                      radius: 0.8,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: Tooltip(
                      message: tooltip,
                      child: InkWell(
                        onTap: _handleTap,
                        customBorder: const CircleBorder(),
                        splashColor: Colors.white.withOpacity(0.3),
                        highlightColor: Colors.white.withOpacity(0.2),
                        child: Center(
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              // Dark tone background for the icon
                              Container(
                                height: 36, // Reduced from 42 to 36
                                width: 36,  // Reduced from 42 to 36
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: darkUndertone.withOpacity(0.2),
                                ),
                              ),
                              
                              // Beautiful background glow for the icon with brand color hints
                              Container(
                                height: 40, // Reduced from 46 to 40
                                width: 40,  // Reduced from 46 to 40
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: RadialGradient(
                                    colors: [
                                      primaryColorLight.withOpacity(0.15), // Reduced pink hint
                                      Colors.white.withOpacity(0.25), // Reduced white intensity
                                      Colors.transparent,
                                    ],
                                    stops: const [0.0, 0.4, 1.0],
                                    radius: 0.9,
                                  ),
                                ),
                              ),
                              
                              // Custom modern music note + location pin icon
                              SizedBox(
                                height: 32, // Reduced from 38 to 32
                                width: 32,  // Reduced from 38 to 32
                                child: CustomPaint(
                                  painter: MusicPinIconPainter(
                                    color: Colors.white.withOpacity(0.9), // Slightly reduce white intensity
                                    shadowColor: darkUndertone.withOpacity(0.6), // Deeper shadow
                                  ),
                                  child: Container(), // Empty container for the custom paint
                                ),
                              ),
                              
                              // Enhanced highlight/reflection effect mimicking light source (reduced)
                              Positioned(
                                top: 6,
                                left: 6,
                                child: Container(
                                  height: 10, // Reduced size
                                  width: 10, // Reduced size
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: RadialGradient(
                                      colors: [
                                        Colors.white.withOpacity(0.7), // Reduced opacity
                                        Colors.white.withOpacity(0.0),
                                      ],
                                      stops: const [0.0, 1.0],
                                    ),
                                  ),
                                ),
                              ),
                              
                              // Additional smaller shine dot for extra polish (reduced)
                              Positioned(
                                top: 3,
                                left: 3,
                                child: Container(
                                  height: 3, // Smaller size
                                  width: 3, // Smaller size
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withOpacity(0.8), // Reduced opacity
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            
            // Optional: Add a subtle upward arrow indicator to hint at swipe gesture
            // Only show this in certain contexts if desired
            if (isOnMapScreen)
              Positioned(
                top: 0,
                child: AnimatedOpacity(
                  opacity: 0.7,
                  duration: Duration(milliseconds: 300),
                  child: Icon(
                    Icons.keyboard_arrow_up,
                    color: Colors.white.withOpacity(0.5),
                    size: 16,
                  ),
                ),
              ),
            
            // Enhanced indicator with pulse animation when add pin is fully active
            if (isOnMapScreen)
              Positioned(
                bottom: 2,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: isAddPinActive ? 8 : 6,
                  height: isAddPinActive ? 8 : 6,
                  decoration: BoxDecoration(
                    color: isAddPinActive ? Colors.white.withOpacity(0.9) : Colors.white.withOpacity(0.5),
                    shape: BoxShape.circle,
                    boxShadow: isAddPinActive ? [
                      BoxShadow(
                        color: Colors.white.withOpacity(0.7),
                        blurRadius: 6,
                        spreadRadius: 0,
                      )
                    ] : null,
                  ),
                ),
              ),
              
            // Add rich pulsing animation when active using brand color hints
            if (isAddPinActive)
              TweenAnimationBuilder<double>(
                tween: Tween(begin: 0.0, end: 1.0),
                duration: const Duration(milliseconds: 500),
                builder: (context, value, child) {
                  // Create a beautiful color transition that includes brand colors and dark tones
                  final pulseColor = Color.lerp(
                    darkUndertone, 
                    Color.lerp(
                      buttonGradient[0],
                      primaryColorLight, 
                      value * 0.6
                    )!,
                    value * 0.5,
                  )!;
                  
                  return Stack(
                    alignment: Alignment.center,
                    children: [
                      // Outer pulse ring
                      Opacity(
                        opacity: (0.4 * (1 - value)) + 0.1,
                        child: Container(
                          height: 62 + (value * 12), // Reduced from 70+15 to 62+12
                          width: 62 + (value * 12),   // Reduced from 70+15 to 62+12
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.transparent,
                            border: Border.all(
                              color: pulseColor.withOpacity(0.6 - (value * 0.6)),
                              width: 1.8 - (value * 0.8),
                            ),
                          ),
                        ),
                      ),
                      
                      // Second pulse ring for richer effect (slightly delayed)
                      Opacity(
                        opacity: value > 0.2 ? (0.35 * (1 - (value - 0.2) / 0.8)) : 0,
                        child: Container(
                          height: 54 + ((value - 0.2) > 0 ? (value - 0.2) / 0.8 * 15 : 0), // Reduced from 62+18 to 54+15
                          width: 54 + ((value - 0.2) > 0 ? (value - 0.2) / 0.8 * 15 : 0),   // Reduced from 62+18 to 54+15
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.transparent,
                            border: Border.all(
                              color: primaryColorLight.withOpacity(0.45 - (value * 0.45)),
                              width: 1.5 - (value * 0.5),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
                // Make it loop
                onEnd: () => isAddPinActive ? setState(() {}) : null,
              ),
          ],
        ),
      ),
    );
  }
  
  void _handleTap() {
    final mapProvider = Provider.of<MapProvider>(context, listen: false);
    final isOnMapScreen = widget.currentIndex == AppTab.map.index;

    // Log tap event with timestamp
    final now = DateTime.now();
    debugPrint('⭐ ADD PIN BUTTON TAPPED! [${now.toString()}]');
    debugPrint('  - Current index: ${widget.currentIndex}');
    debugPrint('  - AppTab.map.index: ${AppTab.map.index}');
    debugPrint('  - isOnMapScreen: $isOnMapScreen');

    // Additional debugging for navigation state
    final navigator = Navigator.of(context);
    navigator.popUntil((route) {
      debugPrint('  - Current route name: ${route.settings.name}');
      debugPrint('  - Is first route: ${route.isFirst}');
      return true; // Don't actually pop
    });
    
    // ALWAYS EXECUTE the add pin action immediately without ANY checks
    // Reset previous flags to make sure we're in a clean state
    AddPinButton._isAddPinInProgress = false;

    try {
      // Execute haptic feedback immediately
      HapticFeedback.mediumImpact();

      // Double-check if we're actually on the map screen using a more robust method
      bool actuallyOnMapScreen = isOnMapScreen;

      // Fallback check: if the current widget thinks we're not on map but we might be
      if (!isOnMapScreen) {
        // Check if the current route is actually the map
        final navigator = Navigator.of(context);
        navigator.popUntil((route) {
          if (route.settings.name == '/map' ||
              (route.isFirst && (route.settings.name == null || route.settings.name == '/map'))) {
            actuallyOnMapScreen = true;
            debugPrint('  - Fallback detection: Actually on map screen!');
          }
          return true; // Don't actually pop
        });
      }

      if (!actuallyOnMapScreen) {
        // Case 1: Not on map screen - navigate to map first
        debugPrint('🚀 Not on map screen, navigating to map with intent');

        // Set the pin intent flag - this will be processed when we reach the map screen
        debugPrint('  - Setting add pin intent to TRUE before navigation');
        _safelySetAddPinIntent(true);

        // Log mapProvider state after setting intent
        debugPrint('  - MapProvider hasAddPinIntent after setting: ${mapProvider.hasAddPinIntent}');

        // Navigate to map tab
        debugPrint('  - Calling onTabSelected with AppTab.map.index (${AppTab.map.index})');
        widget.onTabSelected(AppTab.map.index);
      } else {
        // Case 2: Already on map screen, execute add pin immediately without ANY checks
        debugPrint('📍 On map screen, executing add pin action directly');
        
        // Cancel any existing pending operations
        debugPrint('  - Cancelling any existing pending operations');
        _cancelPendingOperations();
        
        // ALWAYS EXECUTE the callback directly without any checks
        if (widget.onAddPinPressed != null) {
          // Execute pin action immediately with no checks or delays
          debugPrint('  - Executing onAddPinPressed callback directly');
          widget.onAddPinPressed!();
          debugPrint('  - onAddPinPressed callback completed');
        } else {
          debugPrint('⚠️ ERROR: onAddPinPressed is null');
        }
      }
    } catch (e) {
      debugPrint('🔴 ERROR in add pin button action: $e');
      debugPrint('  - Stack trace: ${StackTrace.current}');
    } finally {
      // Always log the final state
      debugPrint('📊 FINAL STATE after add pin button action:');
      debugPrint('  - _isAddPinInProgress: ${AddPinButton._isAddPinInProgress}');
      debugPrint('  - MapProvider hasAddPinIntent: ${mapProvider.hasAddPinIntent}');
    }
  }
  
  // Enhanced helper method to cancel any pending operations with thorough cleanup
  void _cancelPendingOperations() {
    debugPrint('🧹 CANCELLING PENDING OPERATIONS');
    final mapProvider = Provider.of<MapProvider>(context, listen: false);
    
    // Log initial state
    debugPrint('  - Initial state:');
    debugPrint('    - mapProvider.hasAddPinIntent: ${mapProvider.hasAddPinIntent}');
    debugPrint('    - _isAddPinInProgress: ${AddPinButton._isAddPinInProgress}');
    
    // Use the new force reset method instead of standard setAddPinIntent
    // This provides more thorough cleanup
    mapProvider.forceResetPinIntent();
    debugPrint('  - Used forceResetPinIntent to ensure pin intent is cleared');
    
    // Reset the in-progress flag in case it was stuck
    if (AddPinButton._isAddPinInProgress) {
      debugPrint('  - Resetting _isAddPinInProgress from TRUE to FALSE');
      AddPinButton._isAddPinInProgress = false;
    } else {
      debugPrint('  - _isAddPinInProgress already FALSE, no need to reset');
    }
    
    Future.delayed(const Duration(milliseconds: 200), () {
      debugPrint('  - Delayed check (200ms):');
      debugPrint('    - mapProvider.hasAddPinIntent: ${mapProvider.hasAddPinIntent}');
      debugPrint('    - _isAddPinInProgress: ${AddPinButton._isAddPinInProgress}');
      
      AddPinButton._isAddPinInProgress = false;
    });
  }

  void _safelySetAddPinIntent(bool value) {
    final mapProvider = Provider.of<MapProvider>(context, listen: false);
    
    // Log before setting
    debugPrint('📝 SETTING ADD PIN INTENT:');
    debugPrint('  - Current value: ${mapProvider.hasAddPinIntent}');
    debugPrint('  - Setting to: $value');
    
    // Set the value
    mapProvider.setAddPinIntent(value);
    
    // Log after setting
    debugPrint('  - New value after setting: ${mapProvider.hasAddPinIntent}');
  }
} 