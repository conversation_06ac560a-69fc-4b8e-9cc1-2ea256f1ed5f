import 'package:flutter/material.dart';
import '../../config/route_constants.dart';
import '../../screens/profile/profile_screen.dart';
import '../../screens/profile/settings_screen.dart';
import '../../screens/friends/friends_screen.dart';
import '../../screens/gamification/challenges_screen.dart';
import '../../screens/skins/skin_collection_screen.dart';
import '../../utils/navigation_helper.dart';
import '../../utils/app_tab.dart';
import 'bottom_navigation_bar.dart';
import 'route_transitions.dart';

/// A central class that manages all app navigation
class AppNavigationSystem {
  /// The singleton instance of the navigation system
  static final AppNavigationSystem _instance = AppNavigationSystem._internal();
  
  /// Factory constructor that returns the singleton instance
  factory AppNavigationSystem() => _instance;
  
  /// Private constructor for singleton
  AppNavigationSystem._internal();
  
  /// Access to the global navigator key
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Default transition duration
  static const Duration _defaultTransitionDuration = Duration(milliseconds: 150);
  
  /// Navigate to a specific tab with proper animation and state preservation
  Future<void> navigateToTab(BuildContext context, int tabIndex) async {
    // Use our existing navigation helper for now
    return NavigationHelper.navigateToTab(context, tabIndex);
  }
  
  /// Navigate to the skins screen with animation
  Future<void> navigateToSkins(BuildContext context) async {
    final navigator = Navigator.of(context);
    
    // Check if already on this route
    if (_isCurrentRoute(navigator, RouteConstants.skins)) return;
    
    await navigator.push(
      AppRouteTransitions.fadeTransition(
        page: const SkinCollectionScreen(),
        routeName: RouteConstants.skins,
      ),
    );
  }
  
  /// Navigate to the challenges screen with animation
  Future<void> navigateToChallenges(BuildContext context) async {
    final navigator = Navigator.of(context);
    
    // Check if already on this route
    if (_isCurrentRoute(navigator, RouteConstants.challenges)) return;
    
    await navigator.push(
      AppRouteTransitions.fadeTransition(
        page: const ChallengesScreen(showBottomNav: true),
        routeName: RouteConstants.challenges,
      ),
    );
  }
  
  /// Navigate to the friends screen with animation
  Future<void> navigateToFriends(BuildContext context) async {
    final navigator = Navigator.of(context);
    
    // Check if already on this route
    if (_isCurrentRoute(navigator, RouteConstants.friends)) return;
    
    await navigator.push(
      AppRouteTransitions.fadeTransition(
        page: const FriendsScreen(showBottomNav: true),
        routeName: RouteConstants.friends,
      ),
    );
  }
  
  /// Navigate to the profile screen with animation
  Future<void> navigateToProfile(BuildContext context) async {
    final navigator = Navigator.of(context);
    
    // Check if already on this route
    if (_isCurrentRoute(navigator, RouteConstants.profile)) return;
    
    await navigator.push(
      AppRouteTransitions.fadeTransition(
        page: const ProfileScreen(showBottomNav: true),
        routeName: RouteConstants.profile,
      ),
    );
  }
  
  /// Navigate to a named route with proper animation
  Future<dynamic> navigateTo(BuildContext context, String routeName, {Object? arguments}) {
    return Navigator.of(context).pushNamed(routeName, arguments: arguments);
  }
  
  /// Replace the current route with a new one
  Future<dynamic> replaceTo(BuildContext context, String routeName, {Object? arguments}) {
    return Navigator.of(context).pushReplacementNamed(routeName, arguments: arguments);
  }
  
  /// Clear all routes and navigate to a specific route
  Future<dynamic> clearStackAndNavigateTo(BuildContext context, String routeName, {Object? arguments}) {
    return Navigator.of(context).pushNamedAndRemoveUntil(
      routeName, 
      (route) => false,
      arguments: arguments
    );
  }
  
  /// Go back to previous screen
  void goBack(BuildContext context) {
    Navigator.of(context).pop();
  }
  
  /// Check if can go back
  bool canGoBack(BuildContext context) {
    return Navigator.of(context).canPop();
  }
  
  /// Go back to a specific route
  void goBackToRoute(BuildContext context, String routeName) {
    Navigator.of(context).popUntil((route) => route.settings.name == routeName);
  }
  
  /// Check if the current/top route matches the given name
  bool _isCurrentRoute(NavigatorState navigator, String routeName) {
    bool isCurrentRoute = false;
    
    // The first route in popUntil is the current route
    navigator.popUntil((route) {
      isCurrentRoute = route.settings.name == routeName;
      return true; // Don't actually pop anything
    });
    
    return isCurrentRoute;
  }
} 