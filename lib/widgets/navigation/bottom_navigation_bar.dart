import 'dart:ui';
import 'package:flutter/material.dart';
import '../../utils/navigation_helper.dart';
import '../../utils/app_tab.dart';
import '../../config/themes.dart';

// Import extracted components
import 'components/nav_item.dart';
import 'components/add_pin_button.dart';

/// The main bottom navigation bar for the app
/// Provides a modern, sleek UI with haptic feedback
/// Updated to use a streamlined 3-button layout
class MusicPinBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTabSelected;
  final VoidCallback? onAddPinPressed;
  final bool isDarkMode;

  const MusicPinBottomNavBar({
    Key? key, 
    required this.currentIndex,
    required this.onTabSelected,
    this.onAddPinPressed,
    required this.isDarkMode,
  }) : super(key: key);
  
  /// Constructor that automatically determines the current tab index
  /// Use this constructor to avoid index inconsistency bugs
  factory MusicPinBottomNavBar.auto({
    required BuildContext context,
    required Function(int) onTabSelected,
    VoidCallback? onAddPinPressed,
  }) {
    // Get the current tab from the NavigationHelper
    final currentTab = NavigationHelper.getCurrentTab(context);
    
    return MusicPinBottomNavBar(
      currentIndex: currentTab.index,
      onTabSelected: onTabSelected,
      onAddPinPressed: onAddPinPressed,
      isDarkMode: Theme.of(context).brightness == Brightness.dark,
    );
  }

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    
    return Material(
      color: Colors.transparent,
      child: Container(
        // Reduced height for more compact design
        height: 58.0 + bottomPadding, // Reduced from 68.0 to 58.0
        decoration: BoxDecoration(
          color: isDarkMode ? AppTheme.darkScaffold : AppTheme.lightScaffold,
          boxShadow: isDarkMode ? [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, -2),
              spreadRadius: 1,
            ),
          ] : [], // Remove shadow in light mode for perfect blending
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.black.withOpacity(0.3)
                    : AppTheme.lightScaffold, // Use exact map color
                border: Border(
                  top: BorderSide(
                    color: isDarkMode
                        ? Colors.grey[900]!
                        : Colors.grey[200]!, // Lighter border in light mode
                    width: 0.5,
                  ),
                ),
              ),
              child: SafeArea(
                bottom: true,
                // Reduce minimum padding for more compact design  
                minimum: EdgeInsets.only(bottom: bottomPadding > 0 ? 4 : 8), // Reduced padding
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // Use LayoutBuilder to get available space
                    final totalWidth = constraints.maxWidth;
                    
                    // Adjust for 5-button layout with center pin
                    // Give slightly more space to the pin dropper button
                    final pinButtonWidth = totalWidth * 0.22;  // Slightly larger than other buttons
                    final sideButtonWidth = (totalWidth - pinButtonWidth) / 4;  // Divide remaining space by 4
                    
                    // Add padding to create visual separation
                    final horizontalPadding = totalWidth * 0.02;  // 2% padding on each side
                    final adjustedPinButtonWidth = pinButtonWidth - (horizontalPadding * 2);
                    final adjustedSideButtonWidth = sideButtonWidth - (horizontalPadding * 2);
                    
                    return Padding(
                      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // Profile button
                          NavItem(
                            width: adjustedSideButtonWidth,
                            index: AppTab.profile.index,
                            currentIndex: currentIndex,
                            icon: Icons.person_outline,
                            activeIcon: Icons.person,
                            label: 'Profile',
                            onTabSelected: onTabSelected,
                            isDarkMode: isDarkMode,
                          ),

                          // Explore button
                          NavItem(
                            width: adjustedSideButtonWidth,
                            index: AppTab.explore.index,
                            currentIndex: currentIndex,
                            icon: Icons.explore_outlined,
                            activeIcon: Icons.explore,
                            label: 'Explore',
                            onTabSelected: onTabSelected,
                            isDarkMode: isDarkMode,
                          ),
                          
                          // Center add pin button - larger than other items
                          AddPinButton(
                            width: adjustedPinButtonWidth,
                            currentIndex: currentIndex,
                            onTabSelected: onTabSelected,
                            onAddPinPressed: onAddPinPressed,
                            isDarkMode: isDarkMode,
                          ),

                          // Challenges button
                          NavItem(
                            width: adjustedSideButtonWidth,
                            index: AppTab.challenges.index,
                            currentIndex: currentIndex,
                            icon: Icons.emoji_events_outlined,
                            activeIcon: Icons.emoji_events,
                            label: 'Challenges',
                            onTabSelected: onTabSelected,
                            isDarkMode: isDarkMode,
                          ),
                          
                          // Friends button
                          NavItem(
                            width: adjustedSideButtonWidth,
                            index: AppTab.friends.index,
                            currentIndex: currentIndex,
                            icon: Icons.people_outline,
                            activeIcon: Icons.people,
                            label: 'Friends',
                            onTabSelected: onTabSelected,
                            isDarkMode: isDarkMode,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
} 