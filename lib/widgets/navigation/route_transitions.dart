import 'package:flutter/material.dart';

/// Standard route transitions for the app
class AppRouteTransitions {
  /// Default fade transition
  static PageRouteBuilder<T> fadeTransition<T>({
    required Widget page,
    required String routeName,
    Duration? duration,
    bool maintainState = true,
    bool fullscreenDialog = false,
    RouteSettings? settings,
    Object? arguments,
  }) {
    final actualDuration = duration ?? const Duration(milliseconds: 150);
    
    return PageRouteBuilder<T>(
      transitionDuration: actualDuration,
      reverseTransitionDuration: actualDuration,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      settings: settings ?? RouteSettings(
        name: routeName,
        arguments: arguments,
      ),
      maintainState: maintainState,
      fullscreenDialog: fullscreenDialog,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
    );
  }
  
  /// Slide transition from right
  static PageRouteBuilder<T> slideRightTransition<T>({
    required Widget page,
    required String routeName,
    Duration? duration,
    bool maintainState = true,
    bool fullscreenDialog = false,
    RouteSettings? settings,
    Object? arguments,
  }) {
    final actualDuration = duration ?? const Duration(milliseconds: 200);
    
    return PageRouteBuilder<T>(
      transitionDuration: actualDuration,
      reverseTransitionDuration: actualDuration,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      settings: settings ?? RouteSettings(
        name: routeName,
        arguments: arguments,
      ),
      maintainState: maintainState,
      fullscreenDialog: fullscreenDialog,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = animation.drive(tween);
        
        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }
  
  /// Scale and fade transition
  static PageRouteBuilder<T> scaleTransition<T>({
    required Widget page,
    required String routeName,
    Duration? duration,
    bool maintainState = true,
    bool fullscreenDialog = false,
    RouteSettings? settings,
    Object? arguments,
  }) {
    final actualDuration = duration ?? const Duration(milliseconds: 250);
    
    return PageRouteBuilder<T>(
      transitionDuration: actualDuration,
      reverseTransitionDuration: actualDuration,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      settings: settings ?? RouteSettings(
        name: routeName,
        arguments: arguments,
      ),
      maintainState: maintainState,
      fullscreenDialog: fullscreenDialog,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final scaleAnimation = Tween<double>(
          begin: 0.95,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        ));
        
        final fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOut,
        ));
        
        return FadeTransition(
          opacity: fadeAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        );
      },
    );
  }
} 