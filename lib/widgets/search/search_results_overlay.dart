import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/search_provider.dart';

class SearchResultsOverlay extends StatefulWidget {
  final VoidCallback? onClose;
  
  const SearchResultsOverlay({
    Key? key,
    this.onClose,
  }) : super(key: key);

  @override
  State<SearchResultsOverlay> createState() => _SearchResultsOverlayState();
}

class _SearchResultsOverlayState extends State<SearchResultsOverlay> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _opacityAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );
    
    _slideAnimation = Tween<double>(
      begin: -0.3,  // Less aggressive slide-in
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
      reverseCurve: Curves.easeInCubic,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    // Start animation when initialized
    _animationController.forward();

    // Add keyboard listener
    ServicesBinding.instance.keyboard.addHandler(_handleKeyPress);
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    ServicesBinding.instance.keyboard.removeHandler(_handleKeyPress);
    super.dispose();
  }
  
  // Handle keyboard shortcuts
  bool _handleKeyPress(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.escape) {
        _closeOverlay();
        return true; // Key handled
      }
    }
    return false; // Continue propagating the event
  }

  void _closeOverlay() {
    _animationController.reverse().then((_) {
      if (widget.onClose != null) {
        widget.onClose!();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final searchProvider = Provider.of<SearchProvider>(context);
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final topPadding = MediaQuery.of(context).padding.top;
    final isSmallScreen = size.width < 400;
    
    // Calculate position for search results to start below the search bar
    final yOffset = topPadding + 60; // 60 = search bar height + padding
    
    // Adjust width for better readability on different screen sizes
    final horizontalPadding = isSmallScreen ? 8.0 : 16.0;

    // Limit max height on larger screens for better focus
    final maxHeight = size.height > 800 ? size.height * 0.5 : size.height * 0.6;
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Positioned(
          top: yOffset,
          left: horizontalPadding,
          right: horizontalPadding,
          height: _slideAnimation.value < 0 
              ? 0 
              : maxHeight * (1 + _slideAnimation.value),
          child: FadeTransition(
            opacity: _opacityAnimation,
            child: Transform.translate(
              offset: Offset(0, size.height * 0.1 * _slideAnimation.value),
              child: Semantics(
                container: true,
                label: 'Search results',
                hint: 'List of music pins matching your search',
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: Container(
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surface.withOpacity(0.85),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 0.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 15,
                            spreadRadius: -2,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          _buildHeader(context, searchProvider),
                          Expanded(
                            child: _buildResultsList(context, searchProvider),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildHeader(BuildContext context, SearchProvider searchProvider) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 8, 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.dividerColor.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                Icon(
                  searchProvider.isLoading ? Icons.search : Icons.manage_search,
                  size: 18,
                  color: theme.colorScheme.primary.withOpacity(0.8),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    searchProvider.isLoading 
                        ? 'Searching...' 
                        : 'Results for "${searchProvider.query}"',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.onSurface.withOpacity(0.9),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          CloseButton(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
            onPressed: _closeOverlay,
          ),
        ],
      ),
    );
  }
  
  Widget _buildResultsList(BuildContext context, SearchProvider searchProvider) {
    final theme = Theme.of(context);
    
    if (searchProvider.isLoading) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Searching for music pins...',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      );
    }
    
    if (searchProvider.searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: theme.colorScheme.onSurface.withOpacity(0.4),
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try different keywords or check your spelling',
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: searchProvider.searchResults.length,
      itemBuilder: (context, index) {
        final pin = searchProvider.searchResults[index];
        return _buildSearchResultItem(context, pin, searchProvider);
      },
    );
  }
  
  Widget _buildSearchResultItem(BuildContext context, dynamic pin, SearchProvider searchProvider) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          searchProvider.navigateToPin(pin);
          _closeOverlay();
        },
        splashColor: theme.colorScheme.primary.withOpacity(0.1),
        highlightColor: theme.colorScheme.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: ListTile(
            leading: Hero(
              tag: 'pin_image_${pin['id']}',
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  image: DecorationImage(
                    image: NetworkImage(pin['albumArtUrl'] ?? 'https://via.placeholder.com/48'),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            title: Text(
              pin['title'] ?? 'Unknown Track',
              style: TextStyle(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Row(
              children: [
                Expanded(
                  child: Text(
                    pin['artist'] ?? 'Unknown Artist',
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  '${(pin['distance'] ?? 0.0).toStringAsFixed(1)} km',
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
                ),
              ],
            ),
            trailing: IconButton(
              icon: Icon(
                Icons.location_on_outlined,
                color: theme.colorScheme.primary,
              ),
              onPressed: () {
                HapticFeedback.mediumImpact();
                searchProvider.navigateToPin(pin);
                _closeOverlay();
              },
              tooltip: 'Show on map',
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            dense: true,
          ),
        ),
      ),
    );
  }
} 