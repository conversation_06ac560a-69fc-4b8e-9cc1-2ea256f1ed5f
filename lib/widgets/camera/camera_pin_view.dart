import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'dart:io';

import '../../providers/map_provider.dart';
import '../../config/themes.dart';
import '../../models/music_track.dart';
import '../../services/gamification_integration_service.dart';

class CameraPinView extends StatefulWidget {
  final VoidCallback onClose;
  final Function(bool success) onPinCreated;
  final MusicTrack selectedTrack;

  const CameraPinView({
    Key? key,
    required this.onClose,
    required this.onPinCreated,
    required this.selectedTrack,
  }) : super(key: key);

  @override
  State<CameraPinView> createState() => _CameraPinViewState();
}

class _CameraPinViewState extends State<CameraPinView> with SingleTickerProviderStateMixin {
  final ImagePicker _picker = ImagePicker();
  late AnimationController _dropAnimationController;
  bool _isProcessing = false;
  bool _isPinCreated = false;
  String? _errorMessage;
  
  // Variables for enhanced effects
  final List<ParticleEffect> _particles = [];
  final math.Random _random = math.Random();
  double _pinScale = 1.0;
  bool _showSuccessEffects = false;
  bool _showPulseEffect = false;
  File? _capturedImage;
  
  // Static variable for tracking pin creation time
  static DateTime? _lastPinCreatedAt;

  @override
  void initState() {
    super.initState();
    _dropAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200), // Slightly longer animation
      vsync: this,
    );
    
    _dropAnimationController.addListener(_updateParticles);
    
    // Automatically open the camera when the widget is created
    _openCamera();
  }

  @override
  void dispose() {
    _dropAnimationController.removeListener(_updateParticles);
    _dropAnimationController.dispose();
    super.dispose();
  }
  
  void _updateParticles() {
    if (_dropAnimationController.value > 0.8 && _particles.isEmpty) {
      _generateParticles();
    }
    
    if (_dropAnimationController.value > 0.95 && !_showPulseEffect) {
      setState(() {
        _showPulseEffect = true;
      });
    }
    
    if (_dropAnimationController.value == 1.0 && mounted) {
      setState(() {}); // Trigger a rebuild for particle animations
    }
  }
  
  void _generateParticles() {
    if (!mounted) return;
    
    setState(() {
      _particles.clear();
      // Create a burst of particles
      for (int i = 0; i < 30; i++) {
        _particles.add(ParticleEffect(
          color: _getRandomParticleColor(),
          size: _random.nextDouble() * 12 + 4,
          speed: _random.nextDouble() * 100 + 50,
          angle: _random.nextDouble() * 2 * math.pi,
          decaySpeed: _random.nextDouble() * 0.1 + 0.05,
        ));
      }
    });
  }
  
  Color _getRandomParticleColor() {
    final colors = [
      Colors.blue.shade400,
      Colors.purple.shade300,
      Colors.pink.shade300,
      Colors.amber.shade300,
      Colors.red.shade300,
      Colors.green.shade300,
    ];
    return colors[_random.nextInt(colors.length)];
  }

  // Open the camera to take a picture
  Future<void> _openCamera() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.rear,
        imageQuality: 85,
      );

      if (photo != null) {
        // User took a photo, now animate the pin drop
        _capturedImage = File(photo.path);
        
        setState(() {
          _isProcessing = true;
        });
        
        // Provide haptic feedback - initial smaller impact
        HapticFeedback.selectionClick();
        
        // Short pause before starting animation for dramatic effect
        await Future.delayed(const Duration(milliseconds: 300));
        
        // Medium vibration before drop starts
        HapticFeedback.mediumImpact();
        
        // Start the pin drop animation
        await _dropAnimationController.forward(from: 0.0);
        
        // Create the pin with the current location
        await _createPinAtCurrentLocation();
      } else {
        // User canceled the camera
        widget.onClose();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error accessing camera: $e';
        _isProcessing = false;
      });
    }
  }

  // Create a pin at the current location
  Future<void> _createPinAtCurrentLocation() async {
    try {
      final mapProvider = Provider.of<MapProvider>(context, listen: false);
      
      // Get current location from the map provider
      final currentLocation = mapProvider.currentPosition;
      
      // Use fallback location if current location is not available
      double latitude = 0.0;
      double longitude = 0.0;
      
      if (currentLocation != null) {
        // Use actual location if available
        latitude = currentLocation.latitude;
        longitude = currentLocation.longitude;
      } else {
        // If no location is available, use the map center or a fallback
        if (mapProvider.currentCenter != null) {
          latitude = mapProvider.currentCenter.latitude;
          longitude = mapProvider.currentCenter.longitude;
        } else {
          // Default fallback coordinates (can be adjusted to your preference)
          latitude = 40.7128; // New York City coordinates as fallback
          longitude = -74.0060;
        }
        
        print('Using fallback location: $latitude, $longitude');
      }
      
      // Add safety catch to prevent rapid duplicate pin creation
      if (_lastPinCreatedAt != null && 
          DateTime.now().difference(_lastPinCreatedAt!).inSeconds < 5) {
        // Too soon since last pin - prevent duplicate
        print('Preventing duplicate pin creation (too soon)');
        setState(() {
          _errorMessage = 'You recently created a pin. Please wait a moment.';
          _isProcessing = false;
        });
        return;
      }
      
      // Create a pin with the selected track information
      final createdPin = await mapProvider.addPin(
        latitude: latitude,
        longitude: longitude,
        title: widget.selectedTrack.title,
        trackTitle: widget.selectedTrack.title,
        trackArtist: widget.selectedTrack.artist,
        album: widget.selectedTrack.album,
        trackUrl: widget.selectedTrack.url,
        service: 'spotify', // TODO: Make this dynamic based on track source
        skin: 1, // TODO: Make this configurable
        auraRadius: 50.0,
      );
      
      // Record the pin creation time
      _lastPinCreatedAt = DateTime.now();
      
      if (createdPin != null) {
        // Successfully created pin - amplify feedback
        setState(() {
          _isPinCreated = true;
          _isProcessing = false;
          _showSuccessEffects = true;
        });
        
        // 🎯 CRITICAL FIX: Track gamification for artist/genre achievements
        try {
          final trackingData = {
            'latitude': latitude,
            'longitude': longitude,
            'artist_name': widget.selectedTrack.artist,
            'genre': widget.selectedTrack.genres.isNotEmpty ? widget.selectedTrack.genres.first : '', // Use first genre from list
            'album_name': widget.selectedTrack.album,
            'track_title': widget.selectedTrack.title,
            'service': 'spotify',
            'is_private': false,
            'created_at': DateTime.now().toIso8601String(),
          };
          
          // Track pin creation for achievements (artist, genre, location)
          GamificationIntegrationService.trackPinCreation(
            context,
            createdPin['id'].toString(), // Access id from map, not as property
            trackingData,
          );
          
          debugPrint('🎯 Camera pin gamification tracking initiated');
        } catch (e) {
          debugPrint('⚠️ Camera pin gamification tracking failed: $e');
        }
        
        // Notify parent of success - add retries in case of navigation issues
        int retryCount = 0;
        const maxRetries = 3;
        
        Future<void> notifySuccess() async {
          try {
            if (mounted) {
              widget.onPinCreated(true);
            }
          } catch (e) {
            // Error notifying parent - retry with exponential backoff
            if (retryCount < maxRetries) {
              retryCount++;
              print('Error notifying parent of pin creation, retrying ($retryCount/$maxRetries): $e');
              await Future.delayed(Duration(milliseconds: 200 * retryCount));
              await notifySuccess();
            } else {
              print('Failed to notify parent of pin creation after $maxRetries retries');
            }
          }
        }
        
        // Start the notification process
        await notifySuccess();
        
        // Combo of haptic feedback for max impact
        try {
          HapticFeedback.heavyImpact();
          await Future.delayed(const Duration(milliseconds: 100));
          HapticFeedback.vibrate();
        } catch (e) {
          // Ignore haptic errors - they're not critical
        }
        
        // Dramatic scale effect
        setState(() {
          _pinScale = 1.2;
        });
        
        await Future.delayed(const Duration(milliseconds: 200));
        
        setState(() {
          _pinScale = 1.0;
        });
        
        // Close the camera view after a delay to let animations finish
        await Future.delayed(const Duration(seconds: 2));
        
        // Only close if still mounted
        if (mounted) {
          widget.onClose();
        }
      } else {
        setState(() {
          _errorMessage = 'Failed to create pin';
          _isProcessing = false;
        });
      }
    } catch (e) {
      // Handle errors gracefully
      print('Error creating pin: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Error creating pin: ${e.toString().split('\n')[0]}';
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Background with captured image (blurred)
          if (_capturedImage != null)
            Positioned.fill(
              child: ShaderMask(
                shaderCallback: (Rect bounds) {
                  return LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.black.withOpacity(0.7), Colors.black],
                  ).createShader(bounds);
                },
                blendMode: BlendMode.dstIn,
                child: Image.file(
                  _capturedImage!,
                  fit: BoxFit.cover,
                  color: Colors.black.withOpacity(0.8),
                  colorBlendMode: BlendMode.darken,
                ),
              ),
            )
          else
            // Fallback solid background
            Container(
              color: Colors.black,
              width: double.infinity,
              height: double.infinity,
            ),
          
          // Selected track info overlay (new)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: BackdropFilter(
                filter: ui.ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.white.withOpacity(0.1)),
                  ),
                  child: Row(
                    children: [
                      // Album art
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          widget.selectedTrack.albumArt,
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Container(
                            width: 60,
                            height: 60,
                            color: Colors.grey.shade800,
                            child: Icon(Icons.music_note, color: AppTheme.primaryColor),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Track details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.selectedTrack.title,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              widget.selectedTrack.artist,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 14,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (widget.selectedTrack.album.isNotEmpty)
                              Text(
                                widget.selectedTrack.album,
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.6),
                                  fontSize: 12,
                                  fontStyle: FontStyle.italic,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                      // Service icon
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          widget.selectedTrack.serviceType.toLowerCase() == 'spotify'
                              ? Icons.music_note
                              : Icons.music_note,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          // Pin Drop Animation
          AnimatedBuilder(
            animation: _dropAnimationController,
            builder: (context, child) {
              return Center(
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Base animation
                    CustomPaint(
                      painter: PinDropPainter(
                        animation: _dropAnimationController,
                        color: AppTheme.primaryColor,
                      ),
                      size: Size(MediaQuery.of(context).size.width, 
                             MediaQuery.of(context).size.height / 2),
                    ),
                    
                    // Particles after impact
                    if (_particles.isNotEmpty && _dropAnimationController.value > 0.8)
                      ...buildParticles(MediaQuery.of(context).size),
                    
                    // Pulse rings after landing
                    if (_showPulseEffect)
                      ...buildPulseRings(MediaQuery.of(context).size),
                  ],
                ),
              );
            },
          ),
          
          // Status overlay with glowing effects
          if (_isProcessing || _isPinCreated || _errorMessage != null)
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(_isPinCreated ? 0.3 : 0.5),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (_isProcessing)
                        Column(
                          children: [
                            SizedBox(
                              width: 70,
                              height: 70,
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppTheme.primaryColor.withOpacity(0.8),
                                ),
                                strokeWidth: 4,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.6),
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppTheme.primaryColor.withOpacity(0.2),
                                    blurRadius: 15,
                                    spreadRadius: 1,
                                  ),
                                ],
                              ),
                              child: const Text(
                                'Dropping pin...',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                          ],
                        ),
                      if (_isPinCreated)
                        Column(
                          children: [
                            // Success animation with scale effect
                            TweenAnimationBuilder(
                              tween: Tween<double>(begin: 0.5, end: _pinScale),
                              duration: const Duration(milliseconds: 400),
                              curve: Curves.elasticOut,
                              builder: (context, double value, child) {
                                return Transform.scale(
                                  scale: value,
                                  child: Container(
                                    width: 120,
                                    height: 120,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.green.shade400,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.green.withOpacity(0.6),
                                          blurRadius: 20,
                                          spreadRadius: 5,
                                        ),
                                      ],
                                    ),
                                    child: const Icon(
                                      Icons.check,
                                      color: Colors.white,
                                      size: 80,
                                    ),
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 24),
                            ShaderMask(
                              shaderCallback: (bounds) {
                                return LinearGradient(
                                  colors: [
                                    Colors.white,
                                    AppTheme.primaryColor,
                                    Colors.purpleAccent,
                                    Colors.white,
                                  ],
                                  stops: const [0.0, 0.3, 0.6, 1.0],
                                ).createShader(bounds);
                              },
                              child: const Text(
                                'PIN DROPPED SUCCESSFULLY!',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1.0,
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Your music pin is now on the map',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      if (_errorMessage != null)
                        Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.red.withOpacity(0.2),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.error_outline,
                                color: Colors.red.shade300,
                                size: 64,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Container(
                              margin: const EdgeInsets.symmetric(horizontal: 40),
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.7),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.red.withOpacity(0.5),
                                  width: 2,
                                ),
                              ),
                              child: Text(
                                _errorMessage!,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton.icon(
                              onPressed: () {
                                setState(() {
                                  _errorMessage = null;
                                });
                                _openCamera();
                              },
                              icon: const Icon(Icons.refresh),
                              label: const Text('Try Again'),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                                textStyle: const TextStyle(
                                  fontSize: 16, 
                                  fontWeight: FontWeight.bold
                                ),
                                elevation: 8,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ),
            ),
          
          // Close button with glowing effect
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: GestureDetector(
              onTap: widget.onClose,
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withOpacity(0.2),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 30,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Build particle effects
  List<Widget> buildParticles(Size size) {
    return _particles.map((particle) {
      // Calculate position based on animation progress
      final progress = _dropAnimationController.value - 0.8;
      final normalizedProgress = math.min(1.0, progress * 5); // Scale to 0-1 range
      
      final centerX = size.width / 2;
      final centerY = size.height / 2;
      
      final distance = particle.speed * normalizedProgress;
      final x = centerX + math.cos(particle.angle) * distance;
      final y = centerY + math.sin(particle.angle) * distance;
      
      // Fade out over time
      final opacity = math.max(0.0, 1.0 - normalizedProgress * 1.2);
      
      return Positioned(
        left: x - particle.size / 2,
        top: y - particle.size / 2,
        child: Opacity(
          opacity: opacity,
          child: Container(
            width: particle.size,
            height: particle.size,
            decoration: BoxDecoration(
              color: particle.color,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: particle.color.withOpacity(0.6),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
          ),
        ),
      );
    }).toList();
  }
  
  // Build pulse rings
  List<Widget> buildPulseRings(Size size) {
    return [1, 2, 3].map((i) {
      return Center(
        child: TweenAnimationBuilder(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          duration: Duration(milliseconds: 1500 + (i * 300)),
          curve: Curves.easeOut,
          builder: (context, double value, child) {
            return Opacity(
              opacity: (1.0 - value) * 0.6,
              child: Transform.scale(
                scale: value * 3,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppTheme.primaryColor.withOpacity(0.7),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryColor.withOpacity(0.3),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      );
    }).toList();
  }
}

// Data class for particle effects
class ParticleEffect {
  final Color color;
  final double size;
  final double speed;
  final double angle;
  final double decaySpeed;
  
  ParticleEffect({
    required this.color,
    required this.size,
    required this.speed,
    required this.angle,
    required this.decaySpeed,
  });
}

// Enhanced pin drop animation
class PinDropPainter extends CustomPainter {
  final Animation<double> animation;
  final Color color;
  
  PinDropPainter({
    required this.animation,
    required this.color,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Pin size
    final pinSize = size.width * 0.15;
    
    // Calculate the drop path with a bounce effect
    final startY = -pinSize * 2; // Start further above the visible area
    final endY = size.height / 2; // End at middle of the area
    
    // Apply easing for better feel - fast drop with bounce
    double progress = animation.value;
    double currentY;
    
    // Add bouncing effect near the end of the animation
    if (progress < 0.7) {
      // Initial fast drop
      progress = Curves.easeIn.transform(progress / 0.7);
      currentY = startY + ((endY - startY) * progress);
    } else if (progress < 0.85) {
      // First bounce up
      final bounceProgress = (progress - 0.7) / 0.15;
      final bounceHeight = pinSize * 0.8;
      currentY = endY - bounceHeight * math.sin(bounceProgress * math.pi);
    } else if (progress < 0.95) {
      // Second smaller bounce
      final bounceProgress = (progress - 0.85) / 0.1;
      final bounceHeight = pinSize * 0.3;
      currentY = endY - bounceHeight * math.sin(bounceProgress * math.pi);
    } else {
      // Final settling
      currentY = endY;
    }
    
    // Pin glow effect
    if (animation.value > 0.7) {
      final glowPaint = Paint()
        ..color = color.withOpacity(0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);
      
      canvas.drawCircle(
        Offset(size.width / 2, currentY),
        pinSize * 0.8,
        glowPaint,
      );
    }
    
    // Draw a pin with enhanced styling
    final pinPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Shadow below pin
    if (animation.value > 0.7) {
      final shadowPaint = Paint()
        ..color = Colors.black.withOpacity(0.4)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
      
      canvas.drawOval(
        Rect.fromCenter(
          center: Offset(size.width / 2, endY + pinSize * 0.8),
          width: pinSize,
          height: pinSize * 0.4,
        ),
        shadowPaint,
      );
    }
    
    // Draw pin circle with highlight
    canvas.drawCircle(
      Offset(size.width / 2, currentY),
      pinSize / 2,
      pinPaint,
    );
    
    // Add highlight to pin
    final highlightPaint = Paint()
      ..color = Colors.white.withOpacity(0.4)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(size.width / 2 - pinSize * 0.15, currentY - pinSize * 0.15),
      pinSize * 0.15,
      highlightPaint,
    );
    
    // Draw pin triangle
    final path = Path();
    path.moveTo(size.width / 2, currentY + pinSize / 2);
    path.lineTo(size.width / 2 - pinSize / 3, currentY + pinSize * 1.2);
    path.lineTo(size.width / 2 + pinSize / 3, currentY + pinSize * 1.2);
    path.close();
    
    canvas.drawPath(path, pinPaint);
    
    // Add a shadow and glow effect on impact
    if (animation.value > 0.7) {
      final impactProgress = math.min(1.0, (animation.value - 0.7) / 0.2);
      
      // Draw impact circle
      final impactPaint = Paint()
        ..color = color.withOpacity(0.3 * (1 - impactProgress))
        ..style = PaintingStyle.fill;
      
      final impactRadius = pinSize + (pinSize * 3 * impactProgress);
      canvas.drawCircle(
        Offset(size.width / 2, endY + pinSize / 2),
        impactRadius,
        impactPaint,
      );
      
      // Draw ground cracking effect
      if (animation.value > 0.75) {
        final crackPaint = Paint()
          ..color = color.withOpacity(0.7)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;
        
        final random = math.Random(42); // Fixed seed for consistent cracks
        
        for (int i = 0; i < 8; i++) {
          final crackPath = Path();
          final angle = i * (math.pi / 4);
          final startX = size.width / 2 + math.cos(angle) * pinSize * 0.6;
          final startY = endY + pinSize / 2 + math.sin(angle) * pinSize * 0.2;
          
          crackPath.moveTo(startX, startY);
          
          // Create jagged crack lines
          double currentX = startX;
          double currentY = startY;
          final crackLength = pinSize * (0.5 + random.nextDouble() * 1.0) * impactProgress;
          final segments = 3 + random.nextInt(3);
          
          for (int j = 0; j < segments; j++) {
            final segmentLength = crackLength / segments;
            final jitter = pinSize * 0.1;
            
            currentX += math.cos(angle + random.nextDouble() * 0.5 - 0.25) * segmentLength;
            currentY += math.sin(angle + random.nextDouble() * 0.5 - 0.25) * segmentLength;
            
            crackPath.lineTo(
              currentX + (random.nextDouble() * jitter - jitter / 2),
              currentY + (random.nextDouble() * jitter - jitter / 2),
            );
          }
          
          canvas.drawPath(crackPath, crackPaint);
        }
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant PinDropPainter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
} 