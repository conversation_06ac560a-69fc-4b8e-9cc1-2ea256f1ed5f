import 'package:flutter/material.dart';
import '../../providers/gamification_provider.dart';

class ChallengesHeader extends StatelessWidget {
  final Size size;
  final GamificationProvider gamificationProvider;

  const ChallengesHeader({
    Key? key,
    required this.size,
    required this.gamificationProvider,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = size.width;
    final isCompact = screenWidth < 360;
    
    // Get stats from gamification provider
    final completedCount = gamificationProvider.completedAchievements.length;
    final totalCount = gamificationProvider.achievements.length;
    
    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: theme.colorScheme.outline.withOpacity(0.1),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            _buildStatCard(
              context,
              title: 'Completed',
              value: '$completedCount',
              icon: Icons.check_circle,
              color: Colors.green,
            ),
            const SizedBox(width: 12),
            _buildStatCard(
              context,
              title: 'In Progress',
              value: '${gamificationProvider.inProgressAchievements.length}',
              icon: Icons.pending,
              color: Colors.orange,
            ),
            const SizedBox(width: 12),
            _buildStatCard(
              context,
              title: 'Total',
              value: '$totalCount',
              icon: Icons.emoji_events,
              color: theme.colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);
    
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 18, color: color),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 10,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 