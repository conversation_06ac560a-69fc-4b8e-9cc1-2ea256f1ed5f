import 'package:flutter/material.dart';
import '../../models/achievement.dart';
import '../../models/pin_skin.dart';

class AchievementCard extends StatelessWidget {
  final Achievement achievement;
  final bool showProgress;
  final bool showCompletionDate;
  final VoidCallback? onTap;

  const AchievementCard({
    Key? key,
    required this.achievement,
    this.showProgress = false,
    this.showCompletionDate = false,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isCompleted = achievement.isCompleted;
    final progressPercentage = achievement.calculateProgressPercentage();
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isCompleted 
                  ? Colors.green.withOpacity(0.3)
                  : theme.colorScheme.outline.withOpacity(0.2),
              width: isCompleted ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with icon and status
              Row(
                children: [
                  // Achievement icon
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: isCompleted 
                          ? Colors.green.withOpacity(0.1)
                          : theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Text(
                        achievement.icon,
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Title and description
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                achievement.name,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            if (isCompleted)
                              Icon(
                                Icons.check_circle,
                                color: Colors.green,
                                size: 20,
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          achievement.description,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Progress or completion info
              if (showProgress && !isCompleted) ...[
                _buildProgressSection(context, progressPercentage),
              ] else if (showCompletionDate && isCompleted) ...[
                _buildCompletionSection(context),
              ] else if (!isCompleted) ...[
                _buildRequirementsSection(context),
              ],
              
              // Reward section
              if (achievement.rewardSkin != null) ...[
                const SizedBox(height: 12),
                _buildRewardSection(context, achievement.rewardSkin!),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context, double progressPercentage) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.primary,
              ),
            ),
            Text(
              '${progressPercentage.toInt()}%',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: progressPercentage / 100,
            backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
            minHeight: 6,
          ),
        ),
        const SizedBox(height: 8),
        _buildDetailedProgress(context),
      ],
    );
  }

  Widget _buildDetailedProgress(BuildContext context) {
    final theme = Theme.of(context);
    final progress = achievement.progress ?? {};
    final criteria = achievement.criteria;
    
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: criteria.entries.where((entry) => entry.value is num).map((entry) {
        final key = entry.key;
        final required = entry.value as num;
        final current = progress[key] ?? 0;
        
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            '$current / $required ${_getKeyDisplayName(key)}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary,
              fontSize: 11,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCompletionSection(BuildContext context) {
    final theme = Theme.of(context);
    final completedAt = achievement.completedAt;
    
    if (completedAt == null) return const SizedBox.shrink();
    
    final timeAgo = _getTimeAgo(completedAt);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.celebration,
            color: Colors.green,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'Completed $timeAgo',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.green,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirementsSection(BuildContext context) {
    final theme = Theme.of(context);
    final criteria = achievement.criteria;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Requirements',
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: criteria.entries.where((entry) => entry.value is num).map((entry) {
            final key = entry.key;
            final required = entry.value as num;
            
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: theme.colorScheme.outline.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$required ${_getKeyDisplayName(key)}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                  fontSize: 11,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRewardSection(BuildContext context, PinSkin rewardSkin) {
    final theme = Theme.of(context);
    final isUnlocked = achievement.isCompleted;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isUnlocked 
            ? Colors.amber.withOpacity(0.1)
            : theme.colorScheme.outline.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isUnlocked 
              ? Colors.amber.withOpacity(0.3)
              : theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isUnlocked 
                  ? Colors.amber.withOpacity(0.2)
                  : theme.colorScheme.outline.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.star,
              color: isUnlocked ? Colors.amber : theme.colorScheme.outline,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Reward: ${rewardSkin.name}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isUnlocked 
                        ? Colors.amber.shade700
                        : theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                if (rewardSkin.description != null)
                  Text(
                    rewardSkin.description!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isUnlocked 
                          ? Colors.amber.shade600
                          : theme.colorScheme.onSurface.withOpacity(0.5),
                      fontSize: 11,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          if (isUnlocked)
            Icon(
              Icons.lock_open,
              color: Colors.amber.shade700,
              size: 16,
            )
          else
            Icon(
              Icons.lock,
              color: theme.colorScheme.outline,
              size: 16,
            ),
        ],
      ),
    );
  }

  String _getKeyDisplayName(String key) {
    switch (key) {
      case 'artist_pins':
        return 'artist pins';
      case 'genre_pins':
        return 'genre pins';
      case 'location_badges':
        return 'location pins';
      case 'upvotes':
        return 'upvotes';
      case 'total_pins':
        return 'total pins';
      case 'early_user':
        return '';
      default:
        return key.replaceAll('_', ' ');
    }
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
} 