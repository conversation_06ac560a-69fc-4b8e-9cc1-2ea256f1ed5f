import 'package:flutter/material.dart';

class AchievementIconBadge extends StatelessWidget {
  final IconData icon;
  final Color? color;
  final double size;
  final bool isCompleted;
  final bool showCheckmark;

  const AchievementIconBadge({
    Key? key,
    required this.icon,
    this.color,
    this.size = 20,
    this.isCompleted = false,
    this.showCheckmark = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final iconColor = isCompleted ? Colors.green : (color ?? theme.colorScheme.primary);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: size,
          ),
        ),
        if (isCompleted && showCheckmark) ...[
          const SizedBox(width: 4),
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: size - 2,
          ),
        ],
      ],
    );
  }
} 