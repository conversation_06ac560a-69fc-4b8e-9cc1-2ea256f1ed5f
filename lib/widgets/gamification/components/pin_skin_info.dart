import 'package:flutter/material.dart';
import '../../../models/pin_skin.dart';

class PinSkinInfo extends StatelessWidget {
  final PinSkin skin;
  final bool isUnlocked;
  final bool isEquipped;
  final bool isSmallScreen;

  const PinSkinInfo({
    Key? key,
    required this.skin,
    required this.isUnlocked,
    required this.isEquipped,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return LayoutBuilder(
      builder: (context, constraints) {
        final hasDescription = skin.description != null && !isSmallScreen;
        final titleSize = isSmallScreen ? 10.0 : 12.0;
        final descSize = 9.0;
        final spacing = isSmallScreen ? 1.0 : 2.0;
        
        // Calculate available height for text
        final availableHeight = constraints.maxHeight;
        final titleHeight = titleSize * 1.2; // Line height factor
        final descHeight = hasDescription ? (descSize * 1.2) : 0.0;
        final totalSpacing = hasDescription ? spacing : 0.0;
        
        // Check if we need to adjust sizes
        final totalDesiredHeight = titleHeight + descHeight + totalSpacing;
        final scale = totalDesiredHeight > availableHeight 
          ? availableHeight / totalDesiredHeight 
          : 1.0;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              skin.name,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: isEquipped 
                  ? theme.colorScheme.primary
                  : isUnlocked
                    ? theme.colorScheme.onSurface
                    : theme.colorScheme.onSurface.withOpacity(0.6),
                letterSpacing: -0.2,
                fontSize: titleSize * scale,
                height: 1.2,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            
            if (hasDescription) ...[
              SizedBox(height: spacing * scale),
              Text(
                skin.description!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                  height: 1.2,
                  fontSize: descSize * scale,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        );
      },
    );
  }
} 