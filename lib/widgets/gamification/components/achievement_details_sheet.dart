import 'package:flutter/material.dart';
import '../../../models/achievement.dart';
import '../achievement_card.dart';

class AchievementDetailsSheet extends StatelessWidget {
  final Achievement achievement;

  const AchievementDetailsSheet({
    Key? key,
    required this.achievement,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      builder: (context, scrollController) => Container(
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: ListView(
          controller: scrollController,
          padding: const EdgeInsets.all(24),
          children: [
            // Handle bar with theme styling
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            // Achievement details with original card for complete info
            AchievementCard(
              achievement: achievement,
              showProgress: !achievement.isCompleted,
              showCompletionDate: achievement.isCompleted,
            ),
            const SizedBox(height: 20),
            // Additional achievement info
            if (achievement.description != null) ...[
              Text(
                'Description',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                achievement.description!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 20),
            ],
            // Requirements section
            Text(
              'Requirements',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildRequirementsList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildRequirementsList(BuildContext context) {
    final theme = Theme.of(context);
    final requirements = achievement.criteria.entries.where((e) => e.value is num).toList();

    return Column(
      children: requirements.map((req) {
        final current = achievement.progress?[req.key] ?? 0;
        final target = req.value as num;
        final progress = current / target;

        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _formatRequirementKey(req.key),
                    style: theme.textTheme.bodyMedium,
                  ),
                  Text(
                    '$current / $target',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: progress,
                backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  String _formatRequirementKey(String key) {
    return key.split('_').map((word) => 
      word[0].toUpperCase() + word.substring(1)
    ).join(' ');
  }
} 