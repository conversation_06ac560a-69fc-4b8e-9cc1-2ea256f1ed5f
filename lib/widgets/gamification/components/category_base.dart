import 'package:flutter/material.dart';
import 'dart:ui';
import '../../../models/achievement.dart';
import '../../../theme/spacing.dart';
import 'achievement_card.dart';

abstract class CategoryBase extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Achievement> achievements;
  final Color color;
  final VoidCallback? onTap;

  const CategoryBase({
    Key? key,
    required this.title,
    required this.icon,
    required this.achievements,
    required this.color,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (achievements.isEmpty) return const SizedBox.shrink();

    return GestureDetector(
      onTap: onTap,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.2),
          borderRadius: BorderRadius.circular(Spacing.radiusXLarge),
          border: Border.all(
            color: color.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(Spacing.large),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              const SizedBox(height: Spacing.medium),
              _buildPreviewItems(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(Spacing.small),
          decoration: BoxDecoration(
            color: color.withOpacity(0.15),
            borderRadius: BorderRadius.circular(Spacing.radiusMedium),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: Spacing.medium),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            Text(
              '${achievements.length} available',
              style: TextStyle(
                color: color.withOpacity(0.7),
                fontSize: 13,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPreviewItems(BuildContext context) {
    return Column(
      children: achievements.take(2).map((achievement) => 
        Padding(
          padding: const EdgeInsets.only(top: Spacing.small),
          child: AchievementCard(
            achievement: achievement,
            color: color,
            isCompleted: achievement.isCompleted,
            showProgress: !achievement.isCompleted,
            isCompact: true,
            onTap: onTap,
          ),
        ),
      ).toList(),
    );
  }
} 