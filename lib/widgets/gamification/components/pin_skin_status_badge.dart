import 'package:flutter/material.dart';

class PinSkinStatusBadge extends StatelessWidget {
  final bool isEquipped;
  final bool isUnlocked;
  final bool isPremium;
  final bool isSmallScreen;

  const PinSkinStatusBadge({
    Key? key,
    required this.isEquipped,
    required this.isUnlocked,
    required this.isPremium,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;
        final showBadges = availableWidth > 50;
        final showText = availableWidth > 70;
        final showPremium = availableWidth > 90 && isPremium;
        
        if (!showBadges) {
          return const SizedBox.shrink();
        }
        
        return Row(
          children: [
            if (isEquipped) ...[
              _buildEquippedBadge(theme, showText),
            ] else if (!isUnlocked) ...[
              _buildLockedBadge(theme),
            ],
            const Spacer(),
            if (showPremium) ...[
              _buildPremiumBadge(theme),
            ],
          ],
        );
      },
    );
  }

  Widget _buildEquippedBadge(ThemeData theme, bool showText) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 2 : 3),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star_rounded,
            color: Colors.white,
            size: isSmallScreen ? 8 : 10,
          ),
          if (showText) ...[
            const SizedBox(width: 2),
            Text(
              isSmallScreen ? 'ON' : 'ACTIVE',
              style: TextStyle(
                color: Colors.white,
                fontSize: isSmallScreen ? 6 : 7,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLockedBadge(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 2 : 3),
      decoration: BoxDecoration(
        color: theme.colorScheme.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: theme.colorScheme.error.withOpacity(0.3),
          width: 0.5,
        ),
      ),
      child: Icon(
        Icons.lock_outline,
        color: theme.colorScheme.error,
        size: isSmallScreen ? 8 : 10,
      ),
    );
  }

  Widget _buildPremiumBadge(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
      decoration: BoxDecoration(
        color: Colors.amber.withOpacity(0.2),
        borderRadius: BorderRadius.circular(3),
      ),
      child: Text(
        'PRO',
        style: TextStyle(
          color: Colors.amber.shade700,
          fontSize: 6,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }
} 