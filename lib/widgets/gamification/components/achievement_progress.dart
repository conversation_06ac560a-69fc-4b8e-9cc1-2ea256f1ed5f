import 'package:flutter/material.dart';

class AchievementProgress extends StatelessWidget {
  final double progressPercentage;
  final Color? color;
  final bool showLabel;
  final double height;
  final bool isLight;

  const AchievementProgress({
    Key? key,
    required this.progressPercentage,
    this.color,
    this.showLabel = true,
    this.height = 4,
    this.isLight = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = color ?? theme.colorScheme.primary;
    final labelColor = isLight ? Colors.white : progressColor;
    final backgroundColor = isLight 
        ? Colors.white.withOpacity(0.2)
        : progressColor.withOpacity(0.1);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(height / 2),
          child: LinearProgressIndicator(
            value: progressPercentage / 100,
            backgroundColor: backgroundColor,
            valueColor: AlwaysStoppedAnimation<Color>(
              isLight ? Colors.white.withOpacity(0.9) : progressColor,
            ),
            minHeight: height,
          ),
        ),
        if (showLabel) ...[
          const SizedBox(height: 4),
          Text(
            '${progressPercentage.round()}% Complete',
            style: theme.textTheme.bodySmall?.copyWith(
              color: labelColor.withOpacity(0.9),
              fontWeight: FontWeight.w500,
              fontSize: 11,
            ),
          ),
        ],
      ],
    );
  }
} 