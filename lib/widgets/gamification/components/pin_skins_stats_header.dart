import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class PinSkinsStatsHeader extends StatelessWidget {
  final int unlockedCount;
  final int totalCount;

  const PinSkinsStatsHeader({
    Key? key,
    required this.unlockedCount,
    required this.totalCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final lockedCount = totalCount - unlockedCount;
    final completionPercentage = totalCount > 0 ? ((unlockedCount / totalCount) * 100).round() : 0;
    
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 12, 16, 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withOpacity(0.08),
            theme.colorScheme.secondary.withOpacity(0.04),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.1),
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isNarrow = constraints.maxWidth < 300;
          return Column(
            children: [
              Row(
                children: [
                  Flexible(
                    child: _buildStatChip(
                      context,
                      Icons.star_rounded,
                      '$unlockedCount',
                      'Unlocked',
                      theme.colorScheme.primary,
                      isNarrow,
                    ),
                  ),
                  SizedBox(width: isNarrow ? 8 : 12),
                  Flexible(
                    child: _buildStatChip(
                      context,
                      Icons.lock_outline,
                      '$lockedCount',
                      'Locked',
                      theme.colorScheme.error,
                      isNarrow,
                    ),
                  ),
                ],
              ),
              if (!isNarrow) ...[
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '$completionPercentage% Complete',
                        style: TextStyle(
                          color: theme.colorScheme.primary,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          );
        },
      ),
    ).animate()
      .fadeIn(duration: 400.ms)
      .slideY(begin: -0.1, end: 0);
  }

  Widget _buildStatChip(
    BuildContext context,
    IconData icon,
    String value,
    String label,
    Color color,
    bool isCompact,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: isCompact ? 14 : 16),
        SizedBox(width: isCompact ? 2 : 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w700,
            fontSize: isCompact ? 12 : 14,
          ),
        ),
        SizedBox(width: isCompact ? 1 : 2),
        Flexible(
          child: Text(
            label,
            style: TextStyle(
              color: color.withOpacity(0.8),
              fontWeight: FontWeight.w500,
              fontSize: isCompact ? 10 : 12,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
} 