import 'package:flutter/material.dart';
import '../../../theme/spacing.dart';

class AchievementGridLayout extends StatelessWidget {
  final List<Widget> children;
  final EdgeInsets? padding;
  final bool isWideScreen;
  final double childAspectRatio;

  const AchievementGridLayout({
    Key? key,
    required this.children,
    this.padding,
    this.isWideScreen = false,
    this.childAspectRatio = 0.85,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: padding ?? EdgeInsets.all(Spacing.large),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: isWideScreen ? 3 : 2,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: Spacing.gridSpacing,
        mainAxisSpacing: Spacing.gridSpacing,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
} 