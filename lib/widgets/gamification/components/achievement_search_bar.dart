import 'package:flutter/material.dart';

class AchievementSearchBar extends StatelessWidget {
  final Function(String) onSearch;
  final Function(String) onFilterChanged;
  final String selectedFilter;

  const AchievementSearchBar({
    Key? key,
    required this.onSearch,
    required this.onFilterChanged,
    required this.selectedFilter,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 44,
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: TextField(
                onChanged: onSearch,
                decoration: InputDecoration(
                  hintText: 'Search challenges...',
                  hintStyle: TextStyle(
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          PopupMenuButton<String>(
            icon: Icon(
              Icons.filter_list,
              color: theme.colorScheme.primary,
            ),
            onSelected: onFilterChanged,
            itemBuilder: (context) => [
              'All',
              'Recent',
              'Popular',
              'Almost Complete',
              'New',
            ].map((filter) => PopupMenuItem(
              value: filter,
              child: Row(
                children: [
                  Icon(
                    filter == selectedFilter
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked,
                    color: filter == selectedFilter
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withOpacity(0.6),
                    size: 18,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    filter,
                    style: TextStyle(
                      color: filter == selectedFilter
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface,
                      fontWeight: filter == selectedFilter
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }
} 