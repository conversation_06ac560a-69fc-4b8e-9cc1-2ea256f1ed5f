import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../models/user_rank.dart';
import 'package:provider/provider.dart';
import '../../../providers/gamification_provider.dart';

class UserRankBadge extends StatelessWidget {
  final bool showDetails;
  final double size;
  final VoidCallback? onTap;

  const UserRankBadge({
    Key? key,
    this.showDetails = true,
    this.size = 90,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<GamificationProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface.withOpacity(0.8),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (provider.error != null) {
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.error.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.error.withOpacity(0.3),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Failed to load rank data',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () {
                    provider.clearError();
                    provider.initialize();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final currentRank = provider.currentRank;
        final nextRank = provider.nextRank;
        final theme = Theme.of(context);
        final userLevel = provider.userLevel;
        final xpProgress = provider.xpProgress;

        final backgroundBrightness = ThemeData.estimateBrightnessForColor(currentRank.backgroundColor);
        final textColor = backgroundBrightness == Brightness.dark 
            ? Colors.white.withOpacity(0.9) 
            : Colors.black.withOpacity(0.9);
        
        // Better contrast color calculation for progress bar
        final contrastColor = currentRank.id == 'basement_bopper' 
            ? const Color(0xFF4A90E2) // Use a bright blue for better contrast
            : currentRank.primaryColor;
        
        // Background color for progress bar
        final progressBackgroundColor = currentRank.id == 'basement_bopper'
            ? Colors.white.withOpacity(0.3) // Slightly more visible background
            : contrastColor.withOpacity(0.2);
            
        // Ensure minimum visible progress if there's any XP
        final displayProgress = xpProgress > 0 && xpProgress < 3 ? 3.0 : xpProgress;

        return GestureDetector(
          onTap: () {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (context) => _buildRankDetailsModal(context, provider),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  currentRank.primaryColor.withOpacity(0.2),
                  currentRank.backgroundColor.withOpacity(0.85),
                ],
                stops: const [0.1, 1.0],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: currentRank.primaryColor.withOpacity(0.25),
                  blurRadius: 10,
                  spreadRadius: 1,
                  offset: const Offset(0, 3),
                ),
              ],
              border: Border.all(
                color: currentRank.primaryColor.withOpacity(0.3),
                width: 1.0,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Container(
                      width: size,
                      height: size,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            currentRank.primaryColor.withOpacity(0.8),
                            currentRank.backgroundColor,
                          ],
                          stops: const [0.1, 1.0],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: currentRank.primaryColor.withOpacity(0.4),
                            blurRadius: 15,
                            spreadRadius: 1,
                          ),
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 8,
                            spreadRadius: -2,
                            offset: const Offset(0, 3),
                          ),
                        ],
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1.0,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          currentRank.emoji,
                          style: TextStyle(
                            fontSize: size * 0.5,
                            shadows: [
                              Shadow(
                                color: currentRank.primaryColor.withOpacity(0.7),
                                blurRadius: 10,
                              ),
                              Shadow(
                                color: Colors.black.withOpacity(0.5),
                                blurRadius: 4,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    if (showDetails) ...[
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              currentRank.name,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: textColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Level $userLevel',
                              style: TextStyle(
                                fontSize: 14,
                                color: textColor.withOpacity(0.7),
                              ),
                            ),
                            const SizedBox(height: 8),
                            // XP Progress Bar
                            Stack(
                              children: [
                                // Background bar
                                Container(
                                  height: 6,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: progressBackgroundColor,
                                    borderRadius: BorderRadius.circular(3),
                                  ),
                                ),
                                // Progress fill
                                FractionallySizedBox(
                                  widthFactor: (displayProgress / 100).clamp(0.0, 1.0),
                                  child: Container(
                                    height: 6,
                                    decoration: BoxDecoration(
                                      color: contrastColor,
                                      borderRadius: BorderRadius.circular(3),
                                      boxShadow: [
                                        BoxShadow(
                                          color: contrastColor.withOpacity(0.3),
                                          blurRadius: 2,
                                          offset: const Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'XP Progress',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: textColor.withOpacity(0.6),
                                  ),
                                ),
                                Text(
                                  '${provider.currentXp} / ${provider.nextLevelXp} XP',
                                  style: TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w600,
                                    color: contrastColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRankDetailsModal(BuildContext context, GamificationProvider provider) {
    final theme = Theme.of(context);
    final currentRank = provider.currentRank;
    final allRanks = UserRank.allRanks;
    final userLevel = provider.userLevel;
    
    return Container(
      margin: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 20,
      ),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'All Ranks',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Grid of all ranks
          Flexible(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 0.85,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: allRanks.length,
                  itemBuilder: (context, index) {
                    final rank = allRanks[index];
                    final isCurrentRank = rank.id == currentRank.id;
                    final isLocked = rank.requiredLevel > userLevel;
                    
                    return Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            isLocked 
                              ? Colors.grey.withOpacity(0.1)
                              : rank.primaryColor.withOpacity(0.2),
                            isLocked
                              ? Colors.grey.withOpacity(0.05)
                              : rank.backgroundColor.withOpacity(0.85),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isCurrentRank
                              ? rank.primaryColor
                              : (isLocked ? Colors.grey.withOpacity(0.2) : rank.primaryColor.withOpacity(0.3)),
                          width: isCurrentRank ? 2.0 : 1.0,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (isLocked) ...[
                            Icon(
                              Icons.lock_outline,
                              color: Colors.grey.withOpacity(0.5),
                              size: 20,
                            ),
                            const SizedBox(height: 4),
                          ],
                          Text(
                            rank.emoji,
                            style: TextStyle(
                              fontSize: 32,
                              color: isLocked ? Colors.grey : null,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            rank.name,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: isLocked 
                                ? Colors.grey
                                : theme.colorScheme.onSurface,
                            ),
                          ),
                          if (isLocked) ...[
                            const SizedBox(height: 4),
                            Text(
                              'Level ${rank.requiredLevel}',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                          if (isCurrentRank) ...[
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: rank.primaryColor.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Current',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                  color: rank.primaryColor,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 24),
        ],
      ),
    );
  }
} 