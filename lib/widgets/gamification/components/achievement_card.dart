import 'package:flutter/material.dart';
import '../../../models/achievement.dart';
import '../../../theme/spacing.dart';

class AchievementCard extends StatelessWidget {
  final Achievement achievement;
  final Color color;
  final bool isCompleted;
  final bool showProgress;
  final VoidCallback? onTap;
  final bool isCompact;

  const AchievementCard({
    Key? key,
    required this.achievement,
    required this.color,
    this.isCompleted = false,
    this.showProgress = true,
    this.onTap,
    this.isCompact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 72, // Fixed height for consistency
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.surface.withOpacity(0.8),
                theme.colorScheme.surface.withOpacity(0.6),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isCompleted 
                ? color.withOpacity(0.3)
                : theme.colorScheme.onSurface.withOpacity(0.05),
              width: 1,
            ),
          ),
          child: Stack(
            children: [
              // Background Pattern (if completed)
              if (isCompleted)
                Positioned.fill(
                  child: _CompletionPattern(color: color),
                ),
              
              // Main Content
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Row(
                  children: [
                    // Achievement Type Badge
                    _AchievementBadge(
                      icon: achievement.iconData,
                      color: color,
                      isCompleted: isCompleted,
                    ),
                    const SizedBox(width: 12),
                    
                    // Title and Progress Section
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title Row
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  achievement.name,
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    color: theme.colorScheme.onSurface,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (isCompleted)
                                Icon(
                                  Icons.check_circle,
                                  color: color,
                                  size: 16,
                                ),
                            ],
                          ),
                          
                          const SizedBox(height: 4),
                          
                          // Progress Section
                          if (!isCompleted && showProgress)
                            _ProgressIndicator(
                              progress: achievement.progressPercentage,
                              color: color,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _AchievementBadge extends StatelessWidget {
  final IconData icon;
  final Color color;
  final bool isCompleted;

  const _AchievementBadge({
    required this.icon,
    required this.color,
    required this.isCompleted,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
        border: isCompleted ? Border.all(
          color: color.withOpacity(0.3),
          width: 1.5,
        ) : null,
      ),
      child: Icon(
        icon,
        color: color,
        size: 20,
      ),
    );
  }
}

class _ProgressIndicator extends StatelessWidget {
  final double progress;
  final Color color;

  const _ProgressIndicator({
    required this.progress,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Stack(
            children: [
              // Background Track
              Container(
                height: 4,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Progress Bar
              FractionallySizedBox(
                widthFactor: progress / 100,
                child: Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${progress.round()}%',
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

class _CompletionPattern extends StatelessWidget {
  final Color color;

  const _CompletionPattern({required this.color});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _PatternPainter(color: color),
    );
  }
}

class _PatternPainter extends CustomPainter {
  final Color color;

  _PatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(0.03)
      ..style = PaintingStyle.fill;

    final patternSize = 24.0;
    final xCount = (size.width / patternSize).ceil();
    final yCount = (size.height / patternSize).ceil();

    for (var i = 0; i < xCount; i++) {
      for (var j = 0; j < yCount; j++) {
        final x = i * patternSize;
        final y = j * patternSize;
        
        if ((i + j) % 2 == 0) {
          canvas.drawCircle(
            Offset(x + patternSize / 2, y + patternSize / 2),
            2,
            paint,
          );
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 