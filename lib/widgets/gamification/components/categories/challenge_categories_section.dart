import 'package:flutter/material.dart';
import '../../../../providers/gamification_provider.dart';
import '../../../../models/achievement.dart';
import './challenge_category_card.dart';
import '../../../../screens/gamification/category_detail_screen.dart';

class ChallengeCategoriesSection extends StatelessWidget {
  final GamificationProvider gamificationProvider;
  final Function(Achievement) onAchievementSelected;

  const ChallengeCategoriesSection({
    Key? key,
    required this.gamificationProvider,
    required this.onAchievementSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final categories = [
      (
        title: 'Artist Challenges',
        icon: Icons.mic,
        categoryId: 'artist',
        color: Colors.purple,
        isLoading: gamificationProvider.isCategoryLoading('artist'),
      ),
      (
        title: 'Genre Challenges',
        icon: Icons.music_note,
        categoryId: 'genre',
        color: Colors.blue,
        isLoading: gamificationProvider.isCategoryLoading('genre'),
      ),
      (
        title: 'Location Challenges',
        icon: Icons.location_on,
        categoryId: 'location',
        color: Colors.green,
        isLoading: gamificationProvider.isCategoryLoading('location'),
      ),
      (
        title: 'Social Challenges',
        icon: Icons.people,
        categoryId: 'social',
        color: Colors.orange,
        isLoading: gamificationProvider.isCategoryLoading('social'),
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 4, 16, 12),
          child: _buildSectionHeader(
            context,
            'Challenge Categories',
            Icons.category_rounded,
            Colors.indigo,
          ),
        ),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];

              return ChallengeCategoryCard(
                title: category.title,
                icon: category.icon,
                color: category.color,
                categoryId: category.categoryId,
                onTap: () => _navigateToCategoryDetail(
                  context,
                  category.title,
                  category.icon,
                  category.categoryId,
                  category.color,
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 8), // Add consistent bottom spacing
      ],
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withOpacity(0.2),
            color.withOpacity(0.05),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.arrow_forward_ios_rounded,
              color: color,
              size: 10,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToCategoryDetail(
    BuildContext context,
    String title,
    IconData icon,
    String categoryId,
    Color color,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CategoryDetailScreen(
          title: title,
          icon: icon,
          categoryId: categoryId,
          color: color,
        ),
      ),
    );
  }
} 