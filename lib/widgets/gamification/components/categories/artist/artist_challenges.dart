import '../../../../../models/gamification/challenge.dart';

class ArtistChallenges {
  static List<Challenge> getArtistChallenges() {
    return [
      Challenge.create(
        id: 1,
        name: 'Local Artist Supporter',
        description: 'Share songs from 5 local artists',
        categoryId: 'artist',
        criteria: {'artist_pins': 5},
        xpReward: 120,
      ),
      Challenge.create(
        id: 2,
        name: 'Indie Music Explorer',
        description: 'Drop pins for 10 different indie artists',
        categoryId: 'artist',
        criteria: {'artist_pins': 10},
        xpReward: 200,
      ),
      Challenge.create(
        id: 3,
        name: 'New Artist Discoverer',
        description: 'Be the first to share 3 emerging artists',
        categoryId: 'artist',
        criteria: {'new_artist_pins': 3},
        xpReward: 150,
      ),
      Challenge.create(
        id: 4,
        name: 'Artist Collection Creator',
        description: 'Create a collection featuring 5 artists from the same city',
        categoryId: 'artist',
        criteria: {
          'artist_collections': 1,
          'artists_per_collection': 5,
        },
        xpReward: 180,
      ),
      Challenge.create(
        id: 5,
        name: 'Artist Advocate',
        description: 'Get 50 likes on your artist pins',
        categoryId: 'artist',
        criteria: {'artist_pin_likes': 50},
        xpReward: 250,
      ),
    ];
  }

  // Helper method to get a specific challenge by ID
  static Challenge? getChallengeById(int id) {
    try {
      return getArtistChallenges().firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  // Helper method to get challenges by completion status
  static List<Challenge> getChallengesByStatus({bool completed = false}) {
    return getArtistChallenges().where((c) => c.isCompleted == completed).toList();
  }

  // Helper method to get in-progress challenges
  static List<Challenge> getInProgressChallenges() {
    return getArtistChallenges()
        .where((c) => !c.isCompleted && c.progressPercentage > 0)
        .toList();
  }
} 