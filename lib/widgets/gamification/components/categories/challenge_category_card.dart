import 'package:flutter/material.dart';
import '../../../../models/achievement.dart';
import 'package:provider/provider.dart';
import '../../../../providers/gamification_provider.dart';

class ChallengeCategoryCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color color;
  final String categoryId;
  final VoidCallback onTap;

  const ChallengeCategoryCard({
    Key? key,
    required this.title,
    required this.icon,
    required this.color,
    required this.categoryId,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Consumer<GamificationProvider>(
      builder: (context, gamificationProvider, child) {
        final progressSummary = gamificationProvider.getCategoryProgressSummary(categoryId);
        final isLoading = gamificationProvider.isCategoryLoading(categoryId);
        
        final totalCount = progressSummary['total'] as int;
        final completedCount = progressSummary['completed'] as int;
        final inProgressCount = progressSummary['in_progress'] as int;
        final completionRate = (progressSummary['completion_percentage'] as double) / 100;
        
        return Container(
          width: 160,
          margin: const EdgeInsets.only(right: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withOpacity(0.15),
                color.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: color.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(20),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(14),
                            boxShadow: [
                              BoxShadow(
                                color: color.withOpacity(0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Stack(
                            children: [
                              Icon(
                                icon,
                                color: color,
                                size: 22,
                              ),
                              if (isLoading)
                                Positioned.fill(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(color),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 12),
                        
                        Text(
                          title,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 15,
                            color: theme.colorScheme.onSurface,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        const SizedBox(height: 2),
                        
                        Text(
                          totalCount > 0 
                            ? '$totalCount Challenges'
                            : 'Loading...',
                          style: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                            fontSize: 11,
                          ),
                        ),
                        
                        if (inProgressCount > 0)
                          Text(
                            '$inProgressCount in progress',
                            style: TextStyle(
                              color: Colors.orange,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        
                        const SizedBox(height: 12),
                        
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '$completedCount/$totalCount',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: color,
                                fontSize: 11,
                              ),
                            ),
                            Text(
                              '${(completionRate * 100).toInt()}%',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: color,
                                fontSize: 11,
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 6),
                        
                        Stack(
                          children: [
                            Container(
                              height: 4,
                              decoration: BoxDecoration(
                                color: color.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              width: constraints.maxWidth * completionRate,
                              height: 4,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    color,
                                    color.withOpacity(0.8),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(2),
                                boxShadow: completionRate > 0 ? [
                                  BoxShadow(
                                    color: color.withOpacity(0.3),
                                    blurRadius: 2,
                                    offset: const Offset(0, 1),
                                  ),
                                ] : [],
                              ),
                            ),
                          ],
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
} 