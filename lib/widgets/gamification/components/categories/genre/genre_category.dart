import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../../models/gamification/category.dart';
import '../../../../../models/gamification/challenge.dart';

class GenreCategoryWidget extends StatelessWidget {
  final List<Challenge> challenges;
  final VoidCallback onTap;

  const GenreCategoryWidget({
    Key? key,
    required this.challenges,
    required this.onTap,
  }) : super(key: key);

  String get shortTitle {
    return 'Genre'; // Fixed short title for genre category
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final category = GamificationCategory.genre();
    
    // Count completed challenges
    final completedCount = challenges.where((c) => c.isCompleted).length;
    final totalCount = challenges.length;
    final completionRate = totalCount > 0 ? (completedCount / totalCount) : 0.0;
    
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            category.color.withOpacity(0.15),
            category.color.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: category.color.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category icon
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: category.color.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    category.icon,
                    color: category.color,
                    size: 24,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Category name and count
                Text(
                  shortTitle,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                
                const SizedBox(height: 4),
                
                Text(
                  '$totalCount Challenges',
                  style: TextStyle(
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                    fontSize: 12,
                  ),
                ),
                
                const Spacer(),
                
                // Completion status
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '$completedCount/$totalCount',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: category.color,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '${(completionRate * 100).toInt()}%',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: category.color,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Progress bar
                Stack(
                  children: [
                    // Background
                    Container(
                      height: 4,
                      decoration: BoxDecoration(
                        color: category.color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    // Progress
                    FractionallySizedBox(
                      widthFactor: completionRate,
                      child: Container(
                        height: 4,
                        decoration: BoxDecoration(
                          color: category.color,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate()
      .fadeIn(duration: const Duration(milliseconds: 400))
      .slideX(begin: 0.2, end: 0, duration: const Duration(milliseconds: 400), curve: Curves.easeOutQuad);
  }
} 