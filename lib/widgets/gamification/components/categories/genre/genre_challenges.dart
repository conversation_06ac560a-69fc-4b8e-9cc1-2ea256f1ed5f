import '../../../../../models/gamification/challenge.dart';

class GenreChallenges {
  static List<Challenge> getGenreChallenges() {
    return [
      Challenge.create(
        id: 1,
        name: 'Hip-Hop Pioneer',
        description: 'Drop 5 hip-hop pins in different locations',
        categoryId: 'genre',
        criteria: {'genre_pins': 5},
        xpReward: 150,
      ),
      Challenge.create(
        id: 2,
        name: 'Rock Legend',
        description: 'Share classic rock songs in 10 different spots',
        categoryId: 'genre',
        criteria: {'genre_pins': 10},
        xpReward: 200,
      ),
      Challenge.create(
        id: 3,
        name: 'Jazz Explorer',
        description: 'Create a collection of jazz pins',
        categoryId: 'genre',
        criteria: {'genre_pins': 3},
        xpReward: 100,
      ),
      Challenge.create(
        id: 4,
        name: 'Genre Diversity',
        description: 'Share songs from 5 different genres',
        categoryId: 'genre',
        criteria: {
          'unique_genres': 5,
          'genre_pins': 10,
        },
        xpReward: 180,
      ),
      Challenge.create(
        id: 5,
        name: 'Electronic Music Pioneer',
        description: 'Drop electronic music pins in 3 different neighborhoods',
        categoryId: 'genre',
        criteria: {
          'genre_pins': 3,
          'unique_locations': 3,
        },
        xpReward: 150,
      ),
    ];
  }

  // Helper method to get a specific challenge by ID
  static Challenge? getChallengeById(int id) {
    try {
      return getGenreChallenges().firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  // Helper method to get challenges by completion status
  static List<Challenge> getChallengesByStatus({bool completed = false}) {
    return getGenreChallenges().where((c) => c.isCompleted == completed).toList();
  }

  // Helper method to get in-progress challenges
  static List<Challenge> getInProgressChallenges() {
    return getGenreChallenges()
        .where((c) => !c.isCompleted && c.progressPercentage > 0)
        .toList();
  }
} 