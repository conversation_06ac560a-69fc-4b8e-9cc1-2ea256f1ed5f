import 'package:flutter/material.dart';
import '../../../../models/achievement.dart';
import '../category_base.dart';

class ArtistCategory extends CategoryBase {
  const ArtistCategory({
    Key? key,
    required List<Achievement> achievements,
    required VoidCallback? onTap,
  }) : super(
    key: key,
    title: 'Artist',
    icon: Icons.mic,
    achievements: achievements,
    color: Colors.purple,
    onTap: onTap,
  );

  @override
  Widget buildHeaderExtra(BuildContext context) {
    final completedCount = achievements.where((a) => a.isCompleted).length;
    final progress = completedCount / achievements.length;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.purple.withOpacity(0.1),
            Colors.deepPurple.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.purple.withOpacity(0.1),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              value: progress,
              backgroundColor: Colors.purple.withOpacity(0.1),
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.purple),
              strokeWidth: 2,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            '$completedCount/${achievements.length}',
            style: const TextStyle(
              color: Colors.purple,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget buildPreviewItem(BuildContext context, Achievement achievement) {
    return Stack(
      children: [
        super.buildPreviewItem(context, achievement),
        if (achievement.isCompleted)
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: CustomPaint(
                painter: _MusicPatternPainter(
                  color: Colors.purple.withOpacity(0.05),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

class _MusicPatternPainter extends CustomPainter {
  final Color color;

  _MusicPatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final icons = [Icons.music_note, Icons.audiotrack];
    final random = DateTime.now().millisecondsSinceEpoch;

    for (var i = 0; i < 3; i++) {
      final icon = icons[i % icons.length];
      final dx = (random + i * 50) % size.width;
      final dy = (random + i * 70) % size.height;
      
      TextPainter(
        text: TextSpan(
          text: String.fromCharCode(icon.codePoint),
          style: TextStyle(
            fontSize: 12,
            fontFamily: icon.fontFamily,
            color: color,
          ),
        ),
        textDirection: TextDirection.ltr,
      )
        ..layout()
        ..paint(canvas, Offset(dx, dy));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 