import 'package:flutter/material.dart';
import '../../../models/pin_skin.dart';
import '../../profile/components/pin_3d_preview.dart';

class PinSkinPreview extends StatefulWidget {
  final PinSkin skin;
  final bool isUnlocked;
  final bool isHovered;
  final bool isSmallScreen;

  const PinSkinPreview({
    Key? key,
    required this.skin,
    required this.isUnlocked,
    required this.isHovered,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  State<PinSkinPreview> createState() => _PinSkinPreviewState();
}

class _PinSkinPreviewState extends State<PinSkinPreview> {
  bool _imageLoadError = false;
  bool _modelLoadError = false;
  late final ImageProvider _imageProvider;
  late final ImageStream _imageStream;
  late final ImageStreamListener _imageListener;

  @override
  void initState() {
    super.initState();
    _imageProvider = widget.skin.image.startsWith('http')
        ? NetworkImage(widget.skin.image) as ImageProvider
        : AssetImage(widget.skin.image) as ImageProvider;
    _imageStream = _imageProvider.resolve(ImageConfiguration.empty);
    _imageListener = ImageStreamListener(
      (_, __) {
        // Image loaded successfully
      },
      onError: (_, __) {
        if (mounted) {
          setState(() => _imageLoadError = true);
        }
      },
    );
    _imageStream.addListener(_imageListener);
  }

  @override
  void dispose() {
    _imageStream.removeListener(_imageListener);
    super.dispose();
  }

  Widget _buildFallbackIcon(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.center,
          radius: 1.2,
          colors: [
            theme.colorScheme.primary.withOpacity(0.15),
            theme.colorScheme.primary.withOpacity(0.05),
          ],
          stops: const [0.1, 1.0],
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Icon(
          Icons.music_note_rounded,
          size: widget.isSmallScreen ? 24 : 32,
          color: theme.colorScheme.primary.withOpacity(0.6),
        ),
      ),
    );
  }

  Widget _buildLockedOverlay(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.6),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Icon(
          Icons.lock_outline,
          color: Colors.white.withOpacity(0.8),
          size: widget.isSmallScreen ? 16 : 20,
        ),
      ),
    );
  }

  Widget _build3DPreview(ThemeData theme) {
    try {
      return Pin3DPreview(
        modelPath: widget.skin.modelPath!,
        autoRotate: widget.isHovered && widget.isUnlocked,
        isLocked: !widget.isUnlocked,
        backgroundColor: theme.colorScheme.surface,
      );
    } catch (e) {
      if (mounted) {
        setState(() => _modelLoadError = true);
      }
      return _buildFallbackIcon(theme);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AspectRatio(
      aspectRatio: 1.0,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.2,
            colors: [
              theme.colorScheme.surface.withOpacity(0.1),
              theme.colorScheme.surface.withOpacity(0.02),
            ],
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Main content
              if (widget.skin.modelPath != null && !_modelLoadError)
                _build3DPreview(theme)
              else if (!_imageLoadError)
                Image(
                  image: _imageProvider,
                  fit: BoxFit.contain,
                )
              else
                _buildFallbackIcon(theme),

              // Locked overlay
              if (!widget.isUnlocked)
                _buildLockedOverlay(theme),

              // Hover effect
              if (widget.isHovered && widget.isUnlocked)
                Container(
                  decoration: BoxDecoration(
                    gradient: RadialGradient(
                      center: Alignment.center,
                      radius: 1.2,
                      colors: [
                        theme.colorScheme.primary.withOpacity(0.1),
                        theme.colorScheme.primary.withOpacity(0.02),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
} 