import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../models/achievement.dart';
import 'components/achievement_progress.dart';
import 'components/achievement_icon_badge.dart';

class AchievementGridItem extends StatelessWidget {
  final Achievement achievement;
  final bool isCompleted;
  final bool showProgress;
  final VoidCallback onTap;
  final Color? customColor;

  const AchievementGridItem({
    Key? key,
    required this.achievement,
    this.isCompleted = false,
    this.showProgress = true,
    required this.onTap,
    this.customColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardColor = customColor ?? _getCategoryColor(achievement);
    
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(16),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        splashColor: cardColor.withOpacity(0.1),
        highlightColor: cardColor.withOpacity(0.05),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.surface,
                theme.colorScheme.surface.withOpacity(0.95),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: isCompleted 
                  ? cardColor.withOpacity(0.5)
                  : theme.colorScheme.outline.withOpacity(0.08),
              width: isCompleted ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Stack(
            children: [
              // Background Pattern
              if (isCompleted)
                Positioned.fill(
                  child: _CompletionPattern(color: cardColor),
                ),
              
              // Corner Decoration
              if (isCompleted)
                Positioned(
                  top: -15,
                  right: -15,
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: cardColor.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              
              // Main Content
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Top Row: Badge and Status
                    Row(
                      children: [
                        // Challenge Icon with Category-Specific Color
                        AchievementIconBadge(
                          icon: achievement.iconData,
                          isCompleted: isCompleted,
                          color: cardColor,
                          size: 32,
                        ),
                        const SizedBox(width: 8),
                        if (isCompleted)
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: cardColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: cardColor.withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.check_circle,
                                    color: cardColor,
                                    size: 12,
                                  ),
                                  const SizedBox(width: 4),
                                  Flexible(
                                    child: Text(
                                      'Completed',
                                      style: TextStyle(
                                        color: cardColor,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 10,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Challenge Type Pill
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                      decoration: BoxDecoration(
                        color: cardColor.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: cardColor.withOpacity(0.2),
                          width: 0.5,
                        ),
                      ),
                      child: Text(
                        achievement.category,
                        style: TextStyle(
                          color: cardColor,
                          fontSize: 9,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.3,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Title with subtle gradient
                    ShaderMask(
                      shaderCallback: (bounds) => LinearGradient(
                        colors: [
                          cardColor,
                          cardColor.withOpacity(0.8),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ).createShader(bounds),
                      child: Text(
                        achievement.name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          height: 1.2,
                          color: Colors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Description
                    Expanded(
                      child: Text(
                        achievement.description,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                          height: 1.4,
                          fontSize: 12,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    
                    // Points indicator
                    Row(
                      children: [
                        Icon(
                          Icons.stars_rounded,
                          size: 14,
                          color: cardColor.withOpacity(0.7),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${achievement.points} pts',
                          style: TextStyle(
                            color: cardColor.withOpacity(0.9),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    
                    // Progress Section
                    if (showProgress && !isCompleted) ...[
                      const SizedBox(height: 12),
                      _ProgressSection(
                        progress: achievement.progressPercentage,
                        color: cardColor,
                      ),
                    ],
                    
                    // Completion Date
                    if (isCompleted && achievement.completedAt != null) ...[
                      const SizedBox(height: 8),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.calendar_today_rounded,
                            size: 12,
                            color: theme.colorScheme.onSurface.withOpacity(0.4),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _formatDate(achievement.completedAt!),
                            style: TextStyle(
                              fontSize: 10,
                              color: theme.colorScheme.onSurface.withOpacity(0.4),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays < 1) {
      return 'Today';
    } else if (difference.inDays < 2) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
  
  Color _getCategoryColor(Achievement achievement) {
    final category = achievement.category.toLowerCase();
    
    if (category.contains('artist')) {
      return Colors.purple;
    } else if (category.contains('genre')) {
      return Colors.blue;
    } else if (category.contains('location')) {
      return Colors.green;
    } else if (category.contains('social')) {
      return Colors.red;
    } else if (category.contains('daily')) {
      return Colors.orange;
    }
    return Colors.teal;
  }
}

class _ProgressSection extends StatelessWidget {
  final double progress;
  final Color color;

  const _ProgressSection({
    required this.progress,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Progress',
              style: TextStyle(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.2,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${progress.round()}%',
                style: TextStyle(
                  color: color,
                  fontSize: 10,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Stack(
          children: [
            // Background Track
            Container(
              height: 6,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            // Progress Bar with Gradient
            ClipRRect(
              borderRadius: BorderRadius.circular(3),
              child: FractionallySizedBox(
                widthFactor: progress / 100,
                child: Container(
                  height: 6,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        color.withOpacity(0.7),
                        color,
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(3),
                    boxShadow: [
                      BoxShadow(
                        color: color.withOpacity(0.25),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Progress Indicator Dot
            Positioned(
              left: (progress / 100 * (MediaQuery.of(context).size.width - 80)) - 4,
              top: -1,
              child: Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: color,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: color.withOpacity(0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _CompletionPattern extends StatelessWidget {
  final Color color;

  const _CompletionPattern({required this.color});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _PatternPainter(color: color),
    );
  }
}

class _PatternPainter extends CustomPainter {
  final Color color;

  _PatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(0.05)
      ..style = PaintingStyle.fill;

    // Draw diagonal stripes
    const spacing = 20.0;
    const lineWidth = 2.0;
    const angle = math.pi / 4; // 45 degrees
    
    final diagonalLength = math.sqrt(size.width * size.width + size.height * size.height);
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    
    for (double i = -diagonalLength; i <= diagonalLength; i += spacing) {
      final x1 = centerX + i * math.cos(angle);
      final y1 = centerY + i * math.sin(angle);
      
      final x2 = x1 + diagonalLength * math.cos(angle + math.pi/2);
      final y2 = y1 + diagonalLength * math.sin(angle + math.pi/2);
      
      canvas.drawLine(
        Offset(x1, y1),
        Offset(x2, y2),
        paint..strokeWidth = lineWidth
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 