import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../models/pin_skin.dart';
import 'components/pin_skin_status_badge.dart';
import 'components/pin_skin_preview.dart';
import 'components/pin_skin_info.dart';

class PinSkinCard extends StatefulWidget {
  final PinSkin skin;
  final bool isUnlocked;
  final VoidCallback? onTap;
  final bool isEquipped;

  const PinSkinCard({
    Key? key,
    required this.skin,
    required this.isUnlocked,
    this.onTap,
    this.isEquipped = false,
  }) : super(key: key);

  @override
  State<PinSkinCard> createState() => _PinSkinCardState();
}

class _PinSkinCardState extends State<PinSkinCard> with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.01,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeOutCubic,
    ));
    
    _elevationAnimation = Tween<double>(
      begin: 0,
      end: 6,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeOutCubic,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  void _showLockedInfo(BuildContext context) {
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.lock_outline, color: Colors.white.withOpacity(0.9), size: 18),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                'Unlock ${widget.skin.name}!',
                style: const TextStyle(fontSize: 14),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Theme.of(context).colorScheme.error,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(12),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final actualIsEquipped = widget.isEquipped || widget.skin.isEquipped;
    final isDark = theme.brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    
    // Responsive sizing based on screen width
    final isSmallScreen = screenWidth < 360;
    final cardPadding = isSmallScreen ? 6.0 : 8.0;
    final headerHeight = isSmallScreen ? 14.0 : 16.0;
    final infoHeight = isSmallScreen ? 20.0 : 24.0;
    final spacingSmall = isSmallScreen ? 4.0 : 6.0;
    
    return AnimatedBuilder(
      animation: _hoverController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: actualIsEquipped 
                    ? theme.colorScheme.primary.withOpacity(0.2)
                    : Colors.black.withOpacity(isDark ? 0.2 : 0.08),
                  blurRadius: _elevationAnimation.value,
                  offset: Offset(0, _elevationAnimation.value / 3),
                  spreadRadius: actualIsEquipped ? 0.5 : 0,
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(16),
              child: InkWell(
                onTap: widget.isUnlocked ? widget.onTap : () => _showLockedInfo(context),
                onHover: (hovered) {
                  setState(() => _isHovered = hovered);
                  if (hovered) {
                    _hoverController.forward();
                  } else {
                    _hoverController.reverse();
                  }
                },
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: actualIsEquipped
                        ? [
                            theme.colorScheme.primary.withOpacity(0.12),
                            theme.colorScheme.secondary.withOpacity(0.08),
                          ]
                        : widget.isUnlocked
                          ? [
                              theme.colorScheme.surface.withOpacity(0.95),
                              theme.colorScheme.surface.withOpacity(0.85),
                            ]
                          : [
                              theme.colorScheme.surface.withOpacity(0.5),
                              theme.colorScheme.surface.withOpacity(0.3),
                            ],
                    ),
                    border: Border.all(
                      color: actualIsEquipped
                        ? theme.colorScheme.primary.withOpacity(0.25)
                        : widget.isUnlocked
                          ? theme.colorScheme.outline.withOpacity(0.08)
                          : theme.colorScheme.outline.withOpacity(0.04),
                      width: actualIsEquipped ? 1.5 : 0.5,
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Main content with fixed layout to prevent overflow
                      Padding(
                        padding: EdgeInsets.all(cardPadding),
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minWidth: isSmallScreen ? 80 : 100,
                            minHeight: isSmallScreen ? 120 : 140,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Status badges with fixed height
                              SizedBox(
                                height: headerHeight,
                                child: PinSkinStatusBadge(
                                  isEquipped: actualIsEquipped,
                                  isUnlocked: widget.isUnlocked,
                                  isPremium: widget.skin.isPremium,
                                  isSmallScreen: isSmallScreen,
                                ),
                              ),
                              
                              SizedBox(height: spacingSmall),
                              
                              // Pin preview with flexible height
                              Expanded(
                                child: AspectRatio(
                                  aspectRatio: 1.0,
                                  child: PinSkinPreview(
                                    skin: widget.skin,
                                    isUnlocked: widget.isUnlocked,
                                    isHovered: _isHovered,
                                    isSmallScreen: isSmallScreen,
                                  ),
                                ),
                              ),
                              
                              SizedBox(height: spacingSmall),
                              
                              // Skin info with fixed height
                              SizedBox(
                                height: infoHeight,
                                child: PinSkinInfo(
                                  skin: widget.skin,
                                  isUnlocked: widget.isUnlocked,
                                  isEquipped: actualIsEquipped,
                                  isSmallScreen: isSmallScreen,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      // Hover glow effect
                      if (_isHovered && widget.isUnlocked)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  theme.colorScheme.primary.withOpacity(0.08),
                                  theme.colorScheme.secondary.withOpacity(0.04),
                                ],
                              ),
                            ),
                          ),
                        ),
                      
                      // Music pulse animation for equipped items
                      if (actualIsEquipped)
                        Positioned(
                          top: 6,
                          right: 6,
                          child: Container(
                            width: isSmallScreen ? 5 : 6,
                            height: isSmallScreen ? 5 : 6,
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary,
                              shape: BoxShape.circle,
                            ),
                          ).animate(onPlay: (controller) => controller.repeat())
                           .scale(
                             begin: const Offset(1, 1), 
                             end: const Offset(1.3, 1.3),
                             duration: 600.ms,
                           )
                           .then()
                           .scale(
                             begin: const Offset(1.3, 1.3), 
                             end: const Offset(1, 1),
                             duration: 600.ms,
                           ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
} 