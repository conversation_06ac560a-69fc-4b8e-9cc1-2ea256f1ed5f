import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../providers/gamification_provider.dart';
import '../components/challenges_empty_state.dart';
import '../achievement_grid_item.dart';
import '../achievement_card.dart';
import '../pin_skin_card.dart';
import '../stats_overview.dart';

class CompletedChallengesTab extends StatelessWidget {
  final GamificationProvider gamificationProvider;

  const CompletedChallengesTab({
    Key? key,
    required this.gamificationProvider,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final completedAchievements = gamificationProvider.completedAchievements;
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isWideScreen = screenWidth > 600;
    
    if (completedAchievements.isEmpty) {
      return const ChallengesEmptyState(
        message: 'No completed challenges yet!\nStart exploring to unlock achievements.',
        icon: Icons.check_circle_outline,
      );
    }

    // Group achievements by category based on their iconData
    final groupedAchievements = {
      'Artist': completedAchievements.where((a) => a.iconData == Icons.mic).toList(),
      'Genre': completedAchievements.where((a) => a.iconData == Icons.music_note).toList(),
      'Location': completedAchievements.where((a) => a.iconData == Icons.location_on).toList(),
      'Social': completedAchievements.where((a) => a.iconData == Icons.favorite).toList(),
    };

    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        // Stats Overview
        SliverPadding(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          sliver: SliverToBoxAdapter(
            child: StatsOverview(
              gamificationProvider: gamificationProvider,
            ).animate()
              .fadeIn(duration: 400.ms)
              .slideY(begin: 0.2, end: 0, duration: 400.ms, curve: Curves.easeOutQuad),
          ),
        ),

        // Category Headers
        ...groupedAchievements.entries.map((entry) {
          if (entry.value.isEmpty) return const SliverToBoxAdapter(child: SizedBox.shrink());
          
          return SliverMainAxisGroup(
            slivers: [
              // Category Header
              SliverPadding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                sliver: SliverToBoxAdapter(
                  child: _buildCategoryHeader(
                    context,
                    entry.key,
                    entry.value.length,
                    _getCategoryColor(entry.key, theme),
                    _getCategoryIcon(entry.key),
                  ).animate()
                    .fadeIn(duration: 400.ms)
                    .slideX(begin: -0.2, end: 0, duration: 400.ms),
                ),
              ),

              // Category Grid
              SliverPadding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                sliver: SliverGrid(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: isWideScreen ? 3 : 2,
                    childAspectRatio: 0.85,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final achievement = entry.value[index];
                      return AchievementGridItem(
                        achievement: achievement,
                        isCompleted: true,
                        showProgress: false,
                        onTap: () => _showRewardDetails(context, achievement),
                      ).animate(delay: (50 * index).ms)
                        .fadeIn(duration: 400.ms)
                        .slideY(begin: 0.2, end: 0, duration: 400.ms);
                    },
                    childCount: entry.value.length,
                  ),
                ),
              ),
            ],
          );
        }),
      ],
    );
  }

  Widget _buildCategoryHeader(
    BuildContext context,
    String category,
    int count,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Text(
            category,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 15,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '$count completed',
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category, ThemeData theme) {
    switch (category) {
      case 'Artist':
        return Colors.purple;
      case 'Genre':
        return Colors.blue;
      case 'Location':
        return Colors.green;
      case 'Social':
        return Colors.red;
      default:
        return theme.colorScheme.primary;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Artist':
        return Icons.mic;
      case 'Genre':
        return Icons.music_note;
      case 'Location':
        return Icons.location_on;
      case 'Social':
        return Icons.favorite;
      default:
        return Icons.emoji_events;
    }
  }

  void _showRewardDetails(BuildContext context, achievement) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          final theme = Theme.of(context);
          return Container(
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.onSurface.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                
                // Content
                Expanded(
                  child: ListView(
                    controller: scrollController,
                    padding: const EdgeInsets.all(16),
                    children: [
                      AchievementCard(
                        achievement: achievement,
                        showCompletionDate: true,
                      ).animate()
                        .fadeIn(duration: 300.ms)
                        .slideY(begin: 0.2, end: 0),
                        
                      if (achievement.rewardSkin != null) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Unlocked Reward',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ).animate()
                          .fadeIn(delay: 100.ms, duration: 300.ms)
                          .slideX(begin: -0.2, end: 0),
                          
                        const SizedBox(height: 12),
                        PinSkinCard(
                          skin: achievement.rewardSkin!,
                          isUnlocked: true,
                          onTap: () {},
                        ).animate()
                          .fadeIn(delay: 200.ms, duration: 300.ms)
                          .scale(begin: const Offset(0.95, 0.95)),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
} 