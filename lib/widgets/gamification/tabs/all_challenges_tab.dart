import 'package:flutter/material.dart';
import 'dart:math';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:confetti/confetti.dart';
import 'package:rive/rive.dart';
import '../../../providers/gamification_provider.dart';
import '../../../screens/gamification/category_detail_screen.dart';
import '../components/category_section.dart';
import '../components/challenges_empty_state.dart';
import '../stats_overview.dart';

class AllChallengesTab extends StatefulWidget {
  final GamificationProvider gamificationProvider;

  const AllChallengesTab({
    Key? key,
    required this.gamificationProvider,
  }) : super(key: key);

  @override
  State<AllChallengesTab> createState() => _AllChallengesTabState();
}

class _AllChallengesTabState extends State<AllChallengesTab> with SingleTickerProviderStateMixin {
  late ConfettiController _confettiController;
  late AnimationController _animationController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(duration: const Duration(seconds: 1));
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.gamificationProvider.isLoadingAchievements) {
      return const Center(
        child: SizedBox(
          width: 200,
          height: 200,
          child: RiveAnimation.asset(
            'assets/animations/loading.riv',
            fit: BoxFit.contain,
          ),
        ),
      );
    }

    final achievements = widget.gamificationProvider.achievements;
    if (achievements.isEmpty) {
      return const ChallengesEmptyState(
        message: 'No challenges available yet!',
        icon: Icons.emoji_events_outlined,
      );
    }

    final screenWidth = MediaQuery.of(context).size.width;
    final isWideScreen = screenWidth > 600;

    final categories = [
      (
        title: 'Artist',
        icon: Icons.mic,
        achievements: widget.gamificationProvider.artistAchievements,
        color: Colors.purple,
      ),
      (
        title: 'Genre',
        icon: Icons.music_note,
        achievements: widget.gamificationProvider.genreAchievements,
        color: Colors.blue,
      ),
      (
        title: 'Location',
        icon: Icons.location_on,
        achievements: widget.gamificationProvider.locationAchievements,
        color: Colors.green,
      ),
      (
        title: 'Social',
        icon: Icons.favorite,
        achievements: widget.gamificationProvider.socialAchievements,
        color: Colors.red,
      ),
    ];

    return Theme(
      data: Theme.of(context).copyWith(
        scaffoldBackgroundColor: Colors.transparent,
      ),
      child: Stack(
        children: [
          RefreshIndicator(
            onRefresh: () => widget.gamificationProvider.loadAchievements(),
            child: CustomScrollView(
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                // Stats Overview with minimal padding
                SliverPadding(
                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                  sliver: SliverToBoxAdapter(
                    child: StatsOverview(
                      gamificationProvider: widget.gamificationProvider,
                    ).animate()
                      .fadeIn(duration: 400.ms)
                      .slideY(begin: 0.2, end: 0, duration: 400.ms, curve: Curves.easeOutQuad),
                  ),
                ),
                // Categories Grid
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  sliver: SliverGrid(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: isWideScreen ? 3 : 2,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      mainAxisExtent: 280,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        if (index >= categories.length) return null;
                        final category = categories[index];
                        return GestureDetector(
                          onTap: () => _navigateToCategoryDetail(
                            context,
                            '${category.title} Challenges',
                            category.icon,
                            category.achievements,
                            category.color,
                          ),
                          child: CategorySection(
                            title: category.title,
                            icon: category.icon,
                            achievements: category.achievements,
                            color: category.color,
                          ),
                        ).animate(delay: (100 * index).ms)
                          .fadeIn(duration: 400.ms)
                          .slideY(begin: 0.2, end: 0, duration: 400.ms, curve: Curves.easeOutQuad);
                      },
                      childCount: categories.length,
                    ),
                  ),
                ),
                // Bottom padding
                const SliverPadding(
                  padding: EdgeInsets.only(bottom: 16),
                ),
              ],
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirection: pi / 2,
              maxBlastForce: 5,
              minBlastForce: 2,
              emissionFrequency: 0.05,
              numberOfParticles: 20,
              gravity: 0.1,
              shouldLoop: false,
              colors: const [
                Colors.green,
                Colors.blue,
                Colors.pink,
                Colors.orange,
                Colors.purple,
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToCategoryDetail(
    BuildContext context,
    String title,
    IconData icon,
    List<dynamic> achievements,
    Color color,
  ) {
    _confettiController.play();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CategoryDetailScreen(
          title: title,
          icon: icon,
          achievements: achievements,
          color: color,
        ),
      ),
    );
  }
} 