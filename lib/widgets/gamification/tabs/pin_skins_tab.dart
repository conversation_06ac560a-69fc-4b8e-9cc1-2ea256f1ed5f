import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../providers/gamification_provider.dart';
import '../pin_skin_card.dart';
import '../components/challenges_empty_state.dart';
import '../../../widgets/common/pin_preview_widget.dart';
import '../../../widgets/common/equipped_pin_preview.dart';
import '../../../models/pin_skin.dart';

class PinSkinsTab extends StatefulWidget {
  final GamificationProvider gamificationProvider;

  const PinSkinsTab({
    Key? key,
    required this.gamificationProvider,
  }) : super(key: key);

  @override
  State<PinSkinsTab> createState() => _PinSkinsTabState();
}

class _PinSkinsTabState extends State<PinSkinsTab> {
  String _currentFilter = 'All';
  final List<String> _filterOptions = ['All', 'Unlocked', 'Locked'];
  final String defaultPinAsset = 'assets/images/pins/default_pin.png';
  
  @override
  Widget build(BuildContext context) {
    final unlockedSkins = widget.gamificationProvider.unlockedSkins;
    final allSkins = widget.gamificationProvider.pinSkins;
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;
    
    // Get currently equipped skin
    final equippedSkin = unlockedSkins.isNotEmpty 
      ? unlockedSkins.firstWhere(
          (skin) => skin.isEquipped,
          orElse: () => unlockedSkins.first,
        )
      : null;
    
    if (widget.gamificationProvider.isLoadingSkins) {
      return _buildLoadingState(theme);
    }

    if (allSkins.isEmpty) {
      return const ChallengesEmptyState(
        message: 'No pin skins available yet!\nComplete challenges to unlock exclusive skins.',
        icon: Icons.palette_outlined,
      );
    }

    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        // Equipped Pin Card
        if (equippedSkin != null) ...[
          SliverToBoxAdapter(
            child: _buildEquippedCard(context, _useDefaultSkin(equippedSkin)),
          ),
        ],

        // Filter options
        SliverToBoxAdapter(
          child: _buildFilterHeader(context),
        ),
        
        // Skins Grid
        SliverPadding(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: _getGridColumns(screenWidth),
              childAspectRatio: 0.65, // Taller aspect ratio for pin stems
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final filteredSkins = _getFilteredSkins();
                if (index < filteredSkins.length) {
                  final skin = filteredSkins[index];
                  return _buildSkinCard(context, _useDefaultSkin(skin));
                }
                return null;
              },
              childCount: _getFilteredSkins().length,
            ),
          ),
        ),
        
        // Bottom spacing
        const SliverToBoxAdapter(
          child: SizedBox(height: 24),
        ),
      ],
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'Loading pin collection...',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Equipped Card
  Widget _buildEquippedCard(BuildContext context, dynamic equippedSkin) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withOpacity(0.1),
              theme.colorScheme.primary.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              offset: const Offset(0, 4),
              blurRadius: 12,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(24),
          child: InkWell(
            onTap: () => _equipSkin(context, equippedSkin),
            borderRadius: BorderRadius.circular(24),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Equipped pin preview
                  SizedBox(
                    width: 80,
                    height: 110,
                    child: EquippedPinPreview(
                      skin: equippedSkin,
                      size: 80,
                      onTap: () => _equipSkin(context, equippedSkin),
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Info column
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Equipped badge
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withOpacity(0.15),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: theme.colorScheme.primary.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.check_circle_rounded,
                                size: 14,
                                color: theme.colorScheme.primary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'EQUIPPED',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Name
                        Text(
                          equippedSkin.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                        
                        const SizedBox(height: 4),
                        
                        // Description
                        Text(
                          equippedSkin.description ?? 'Your currently equipped pin skin',
                          style: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                            fontSize: 12,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        children: [
          // Section Title
          Icon(
            Icons.palette_outlined,
            size: 18,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'Pin Skins',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const Spacer(),
          // Filter Chip
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
              ),
            ),
            child: DropdownButton<String>(
              value: _currentFilter,
              icon: const Icon(Icons.arrow_drop_down, size: 20),
              elevation: 1,
              underline: const SizedBox(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _currentFilter = newValue;
                  });
                }
              },
              borderRadius: BorderRadius.circular(12),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
              items: _filterOptions.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkinCard(BuildContext context, dynamic skin) {
    final theme = Theme.of(context);
    final isUnlocked = skin.isUnlocked ?? false;
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: () => isUnlocked 
            ? _equipSkin(context, skin)
            : _showUnlockRequirement(context, skin),
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Pin Image Area - Using PinPreviewWidget
              Expanded(
                flex: 3,
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Background gradient
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            isUnlocked 
                              ? theme.colorScheme.primary.withOpacity(0.1)
                              : theme.colorScheme.error.withOpacity(0.05),
                            isUnlocked 
                              ? theme.colorScheme.primary.withOpacity(0.05)
                              : theme.colorScheme.error.withOpacity(0.02),
                          ],
                        ),
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(16),
                        ),
                      ),
                    ),
                    
                    // Pin preview widget
                    Center(
                      child: PinPreviewWidget(
                        skin: skin,
                        size: 80,
                        animate: isUnlocked,
                        isSelected: skin.isEquipped,
                      ),
                    ),
                    
                    // Lock icon for locked skins
                    if (!isUnlocked)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.error.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.lock_outline,
                            size: 16,
                            color: theme.colorScheme.error,
                          ),
                        ),
                      ),
                    
                    // Premium badge
                    if (skin.isPremium ?? false)
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.amber.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.amber,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.stars_rounded,
                                size: 12,
                                color: Colors.amber[800],
                              ),
                              const SizedBox(width: 2),
                              Text(
                                'PREMIUM',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.amber[800],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              
              // Info section
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name
                      Text(
                        skin.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // Description
                      Expanded(
                        child: Text(
                          skin.description ?? (isUnlocked ? 'Available to equip' : 'Complete challenges to unlock'),
                          style: TextStyle(
                            fontSize: 12,
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      
                      // Status button
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: isUnlocked
                            ? theme.colorScheme.primary.withOpacity(0.1)
                            : theme.colorScheme.error.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          isUnlocked
                            ? skin.isEquipped ? 'EQUIPPED' : 'EQUIP'
                            : 'LOCKED',
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                            color: isUnlocked
                              ? theme.colorScheme.primary
                              : theme.colorScheme.error,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int _getGridColumns(double screenWidth) {
    if (screenWidth < 320) return 1;  // Very small screens
    if (screenWidth < 600) return 2;  // Mobile
    return 3;  // Tablet and larger
  }

  List<dynamic> _getFilteredSkins() {
    switch (_currentFilter) {
      case 'Unlocked':
        return widget.gamificationProvider.unlockedSkins;
      case 'Locked':
        return widget.gamificationProvider.pinSkins.where((skin) => !skin.isUnlocked).toList();
      case 'All':
      default:
        return widget.gamificationProvider.pinSkins;
    }
  }

  void _equipSkin(BuildContext context, dynamic skin) {
    if (!skin.isUnlocked) {
      _showUnlockRequirement(context, skin);
      return;
    }
    
    widget.gamificationProvider.equipSkin(skin);
    
    // Show success animation with haptic feedback
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(Icons.check_circle, color: Colors.white, size: 16),
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                '${skin.name} equipped!',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Theme.of(context).colorScheme.primary,
        duration: const Duration(seconds: 2),
        margin: const EdgeInsets.all(12),
      ),
    );
  }

  void _showUnlockRequirement(BuildContext context, dynamic skin) {
    HapticFeedback.lightImpact();
    
    // Mock requirements since PinSkin model doesn't have requirements property
    final mockRequirements = [
      {'description': 'Complete 5 music challenges', 'isCompleted': false},
      {'description': 'Discover 10 new music spots', 'isCompleted': true},
      {'description': 'Share 3 music pins', 'isCompleted': false},
    ];
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.45,
        maxChildSize: 0.6,
        minChildSize: 0.3,
        builder: (context, controller) {
          final theme = Theme.of(context);
          return Container(
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 16,
                  offset: const Offset(0, -4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  width: 40,
                  height: 3,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                
                Expanded(
                  child: ListView(
                    controller: controller,
                    padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  theme.colorScheme.error,
                                  theme.colorScheme.error.withOpacity(0.8),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.lock,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Unlock ${skin.name}',
                                  style: theme.textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 18,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  'Complete these music challenges',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      
                      // Compact requirements list
                      ...mockRequirements.map((req) => Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: req['isCompleted'] as bool
                            ? theme.colorScheme.primary.withOpacity(0.06)
                            : theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: req['isCompleted'] as bool
                              ? theme.colorScheme.primary.withOpacity(0.2)
                              : theme.colorScheme.outline.withOpacity(0.1),
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: req['isCompleted'] as bool
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.onSurface.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                req['isCompleted'] as bool ? Icons.check : Icons.radio_button_unchecked,
                                color: req['isCompleted'] as bool
                                  ? Colors.white
                                  : theme.colorScheme.onSurface.withOpacity(0.5),
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                req['description'] as String,
                                style: TextStyle(
                                  color: req['isCompleted'] as bool
                                    ? theme.colorScheme.onSurface 
                                    : theme.colorScheme.onSurface.withOpacity(0.7),
                                  decoration: req['isCompleted'] as bool ? TextDecoration.lineThrough : null,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Helper method to use default skin image
  dynamic _useDefaultSkin(dynamic originalSkin) {
    // If the skin is already a PinSkin object, create a copy with the default image
    if (originalSkin is PinSkin) {
      return PinSkin(
        id: originalSkin.id,
        name: originalSkin.name,
        image: defaultPinAsset,
        description: originalSkin.description,
        createdAt: originalSkin.createdAt,
        isUnlocked: originalSkin.isUnlocked,
        isEquipped: originalSkin.isEquipped,
        isPremium: originalSkin.isPremium,
      );
    }
    
    // If it's a dynamic object, try to set the image property
    try {
      // Create a new object with the same properties but different image
      final modifiedSkin = originalSkin;
      // Try to set the image property if available
      modifiedSkin.image = defaultPinAsset;
      return modifiedSkin;
    } catch (e) {
      // If we can't modify it, return as is
      return originalSkin;
    }
  }
} 