import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../config/themes.dart';

/// A versatile shimmer loading widget for various skeleton patterns
class ShimmerLoading extends StatefulWidget {
  final Widget child;
  final bool isLoading;
  final Color? baseColor;
  final Color? highlightColor;
  final Duration period;
  final double? width;
  final double? height;

  const ShimmerLoading({
    Key? key,
    required this.child,
    this.isLoading = true,
    this.baseColor,
    this.highlightColor,
    this.period = const Duration(milliseconds: 2000),
    this.width,
    this.height,
  }) : super(key: key);

  @override
  State<ShimmerLoading> createState() => _ShimmerLoadingState();
}

class _ShimmerLoadingState extends State<ShimmerLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.period,
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.5, end: 1.5).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOutCubic),
    );
    
    if (widget.isLoading) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(ShimmerLoading oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading && !_animationController.isAnimating) {
      _animationController.repeat();
    } else if (!widget.isLoading && _animationController.isAnimating) {
      _animationController.stop();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // More sophisticated color scheme
    final baseColor = widget.baseColor ?? 
        (isDark 
            ? const Color(0xFF2A2A2A)  // Darker, less harsh gray for dark mode
            : const Color(0xFFF5F5F5)); // Softer light gray for light mode
            
    final highlightColor = widget.highlightColor ?? 
        (isDark 
            ? const Color(0xFF404040)  // Subtle highlight for dark mode
            : const Color(0xFFFFFFFF)); // Pure white highlight for light mode
            
    final accentColor = isDark 
        ? const Color(0xFF505050)  // Additional accent for smoothness
        : const Color(0xFFF8F8F8);

    if (!widget.isLoading) {
      return widget.child;
    }

    Widget shimmerChild = AnimatedBuilder(
      animation: _animation,
      builder: (context, child) => ShaderMask(
        blendMode: BlendMode.srcATop,
        shaderCallback: (bounds) => LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            baseColor,
            accentColor,
            highlightColor,
            accentColor,
            baseColor,
          ],
          stops: [
            0.0,
            0.2 + _animation.value * 0.3,
            0.5 + _animation.value * 0.3,
            0.8 + _animation.value * 0.3,
            1.0,
          ],
          tileMode: TileMode.clamp,
        ).createShader(bounds),
        child: widget.child,
      ),
    );

    // Wrap in SizedBox if width or height are provided for backward compatibility
    if (widget.width != null || widget.height != null) {
      shimmerChild = SizedBox(
        width: widget.width,
        height: widget.height,
        child: shimmerChild,
      );
    }

    return shimmerChild;
  }
}

/// Skeleton for bop drop cards
class BopDropSkeleton extends StatelessWidget {
  final bool showEngagementStats;
  final bool showExpandedDetails;
  
  const BopDropSkeleton({
    Key? key,
    this.showEngagementStats = false,
    this.showExpandedDetails = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Modern, subtle colors
    final skeletonColor = isDark 
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFF8F8F8);
    final cardColor = isDark 
        ? const Color(0xFF1E1E1E)
        : Colors.white;
    final borderColor = isDark 
        ? const Color(0xFF333333)
        : const Color(0xFFF0F0F0);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: borderColor, width: 1),
        boxShadow: isDark ? null : [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Main bop drop row
          Row(
            children: [
              // Artwork skeleton
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: skeletonColor,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Track info skeleton
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 18,
                      decoration: BoxDecoration(
                        color: skeletonColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      height: 14,
                      width: 150,
                      decoration: BoxDecoration(
                        color: skeletonColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          height: 12,
                          width: 80,
                          decoration: BoxDecoration(
                            color: skeletonColor,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Container(
                          height: 12,
                          width: 40,
                          decoration: BoxDecoration(
                            color: skeletonColor,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Stats skeleton (if engagement stats are shown)
              if (showEngagementStats) ...[
                const SizedBox(width: 16),
                Column(
                  children: [
                    Container(
                      width: 50,
                      height: 24,
                      decoration: BoxDecoration(
                        color: skeletonColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: 50,
                      height: 24,
                      decoration: BoxDecoration(
                        color: skeletonColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          
          // Expanded engagement details skeleton
          if (showExpandedDetails) ...[
            const SizedBox(height: 16),
            Container(
              height: 1,
              color: borderColor,
            ),
            const SizedBox(height: 16),
            
            // Engagement stats row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(3, (index) => Column(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: skeletonColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: 30,
                    height: 16,
                    decoration: BoxDecoration(
                      color: skeletonColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    width: 40,
                    height: 12,
                    decoration: BoxDecoration(
                      color: skeletonColor,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ],
              )),
            ),
            
            const SizedBox(height: 20),
            
            // User engagement list skeleton
            ...List.generate(3, (index) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: skeletonColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 14,
                          width: 120,
                          decoration: BoxDecoration(
                            color: skeletonColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          height: 12,
                          width: 60,
                          decoration: BoxDecoration(
                            color: skeletonColor,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: skeletonColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }
}

/// Skeleton for bottom sheet header
class BottomSheetHeaderSkeleton extends StatelessWidget {
  const BottomSheetHeaderSkeleton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Modern, subtle colors
    final skeletonColor = isDark 
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFF8F8F8);
    
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Avatar skeleton
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: skeletonColor,
              shape: BoxShape.circle,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // User info skeleton
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 20,
                  width: 120,
                  decoration: BoxDecoration(
                    color: skeletonColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 14,
                  width: 80,
                  decoration: BoxDecoration(
                    color: skeletonColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
          
          // Close button placeholder
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: skeletonColor,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }
}

/// Complete skeleton for My Bop Drops bottom sheet
class MyBopDropsBottomSheetSkeleton extends StatelessWidget {
  const MyBopDropsBottomSheetSkeleton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerLoading(
      child: Column(
        children: [
          // Header skeleton
          const BottomSheetHeaderSkeleton(),
          
          // Content skeleton
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              children: List.generate(5, (index) => const BopDropSkeleton(
                showEngagementStats: true,
                showExpandedDetails: false,
              )),
            ),
          ),
        ],
      ),
    );
  }
}

/// Complete skeleton for Friend Bop Drops bottom sheet
class FriendBopDropsBottomSheetSkeleton extends StatelessWidget {
  const FriendBopDropsBottomSheetSkeleton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerLoading(
      child: Column(
        children: [
          // Header skeleton
          const BottomSheetHeaderSkeleton(),
          
          // Content skeleton
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              children: List.generate(6, (index) => const BopDropSkeleton(
                showEngagementStats: false,
                showExpandedDetails: false,
              )),
            ),
          ),
        ],
      ),
    );
  }
} 