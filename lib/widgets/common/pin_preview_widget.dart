import 'dart:math' as math;
import 'dart:math' show Random;
import 'dart:ui';
import 'package:flutter/material.dart';
import '../../models/pin_skin.dart';

class PinPreviewWidget extends StatefulWidget {
  final PinSkin skin;
  final double size;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool showShadow;
  final bool animate;

  const PinPreviewWidget({
    Key? key,
    required this.skin,
    this.size = 120,
    this.isSelected = false,
    this.onTap,
    this.showShadow = true,
    this.animate = true,
  }) : super(key: key);

  @override
  State<PinPreviewWidget> createState() => _PinPreviewWidgetState();
}

class _PinPreviewWidgetState extends State<PinPreviewWidget> with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _floatController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _floatAnimation;
  bool _isHovering = false;
  final List<_SparkleParticle> _sparkles = [];
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    
    // Main rotation animation
    _rotationController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    );

    _rotationAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0, end: math.pi * 2)
            .chain(CurveTween(curve: Curves.easeInOutSine)),
        weight: 1,
      ),
    ]).animate(_rotationController);

    // Floating animation
    _floatController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _floatAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0, end: 6)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 6, end: 0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 1,
      ),
    ]).animate(_floatController);

    if (widget.animate) {
      _rotationController.repeat();
      _floatController.repeat();
      _generateSparkles();
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _floatController.dispose();
    super.dispose();
  }

  void _generateSparkles() {
    if (!mounted) return;
    
    Future.delayed(Duration(milliseconds: _random.nextInt(1000) + 500), () {
      if (!mounted) return;
      
      setState(() {
        if (_sparkles.length < 5) {
          _sparkles.add(_SparkleParticle(
            position: Offset(
              _random.nextDouble() * widget.size,
              _random.nextDouble() * widget.size,
            ),
            size: _random.nextDouble() * 4 + 2,
            duration: Duration(milliseconds: _random.nextInt(1000) + 1000),
          ));
        }
      });
      
      _generateSparkles();
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          width: widget.size,
          height: widget.size * 1.5,
          child: Stack(
            children: [
              // Main pin with animations
              AnimatedBuilder(
                animation: Listenable.merge([
                  _rotationController,
                  _floatController,
                ]),
                builder: (context, child) {
                  // Calculate rotation based on whether it's animated and hovering
                  double rotationY = 0.0;
                  if (widget.animate) {
                    // Full rotation only for selected pins or when hovering
                    if (widget.isSelected || _isHovering) {
                      rotationY = _rotationAnimation.value;
                    } else {
                      // Subtle oscillation for non-selected pins
                      rotationY = math.sin(_rotationAnimation.value) * 0.2;
                    }
                  } else if (_isHovering) {
                    rotationY = math.pi / 6;
                  }

                  return Transform(
                    transform: Matrix4.identity()
                      ..setEntry(3, 2, 0.001) // perspective
                      ..translate(
                        0.0,
                        _isHovering ? -10.0 : -_floatAnimation.value,
                        0.0,
                      )
                      ..rotateY(rotationY)
                      ..rotateX(_isHovering ? -math.pi / 12 : math.sin(_floatAnimation.value / 3) * 0.1),
                    alignment: Alignment.center,
                    child: Stack(
                      alignment: Alignment.topCenter,
                      children: [
                        // Pin Stem with enhanced 3D effect
                        Positioned(
                          top: widget.size * 0.4,
                          child: Transform(
                            transform: Matrix4.identity()
                              ..setEntry(3, 2, 0.002)
                              ..rotateX(-math.pi / 6),
                            alignment: Alignment.topCenter,
                            child: Container(
                              width: widget.size * 0.06,
                              height: widget.size * 0.8,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    const Color(0xFFEEEEEE), // Top metallic
                                    const Color(0xFFD8D8D8), // Upper metallic
                                    const Color(0xFFC9C9C9), // Mid metallic
                                    const Color(0xFFB0B0B0), // Lower metallic
                                    const Color(0xFFA0A0A0), // Bottom metallic
                                  ],
                                  stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
                                ),
                                borderRadius: BorderRadius.circular(widget.size * 0.015),
                              ),
                              child: Stack(
                                children: [
                                  // Base metal texture
                                  Positioned.fill(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(widget.size * 0.015),
                                        gradient: LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: [
                                            const Color(0xFFDDDDDD),
                                            const Color(0xFFCCCCCC),
                                            const Color(0xFFE5E5E5),
                                            const Color(0xFFCCCCCC),
                                          ],
                                          stops: const [0.0, 0.3, 0.6, 1.0],
                                        ),
                                      ),
                                    ),
                                  ),
                                  
                                  // Central highlight line (simulates light reflection)
                                  Positioned(
                                    left: widget.size * 0.03 - (widget.size * 0.01 / 2),
                                    top: 0,
                                    bottom: 0,
                                    width: widget.size * 0.01,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          colors: [
                                            Colors.white.withOpacity(0.9),
                                            Colors.white.withOpacity(0.7),
                                            Colors.white.withOpacity(0.2),
                                            Colors.white.withOpacity(0.0),
                                          ],
                                          stops: const [0.0, 0.2, 0.5, 0.8],
                                        ),
                                      ),
                                    ),
                                  ),
                                  
                                  // Diagonal highlight (simulates rounded metal)
                                  Positioned.fill(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(widget.size * 0.015),
                                      child: CustomPaint(
                                        painter: DiagonalHighlightPainter(
                                          color: Colors.white.withOpacity(0.4),
                                          strokeWidth: widget.size * 0.02,
                                        ),
                                      ),
                                    ),
                                  ),
                                  
                                  // Edge highlight to add dimension
                                  Positioned(
                                    left: 0,
                                    top: 0,
                                    bottom: 0,
                                    width: widget.size * 0.01,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: [
                                            Colors.white.withOpacity(0.7),
                                            Colors.white.withOpacity(0.0),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(widget.size * 0.015),
                                          bottomLeft: Radius.circular(widget.size * 0.015),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        
                        // Pin Head with enhanced effects
                        Positioned(
                          top: widget.size * 0.1,
                          child: Container(
                            width: widget.size * 0.8,
                            height: widget.size * 0.8,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              image: DecorationImage(
                                image: widget.skin.image.startsWith('http')
                                    ? NetworkImage(widget.skin.image)
                                    : AssetImage(widget.skin.image) as ImageProvider,
                                fit: BoxFit.cover,
                              ),
                              boxShadow: widget.showShadow ? [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.4),
                                  blurRadius: 12,
                                  offset: const Offset(4, 6),
                                ),
                                BoxShadow(
                                  color: Colors.white.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(-2, -2),
                                ),
                              ] : null,
                            ),
                            child: Stack(
                              children: [
                                // Hover glow
                                if (_isHovering)
                                  Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: RadialGradient(
                                        colors: [
                                          Colors.white.withOpacity(0.2),
                                          Colors.white.withOpacity(0.0),
                                        ],
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                        
                        // Selection Indicator with glow
                        if (widget.isSelected)
                          Positioned.fill(
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Theme.of(context).colorScheme.primary,
                                  width: 3,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                                    blurRadius: 8,
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  );
                },
              ),
              
              // Sparkle particles
              if (widget.animate && widget.skin.isPremium)
                ...List.generate(_sparkles.length, (index) {
                  final sparkle = _sparkles[index];
                  return Positioned(
                    left: sparkle.position.dx,
                    top: sparkle.position.dy,
                    child: TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0, end: 1),
                      duration: sparkle.duration,
                      onEnd: () {
                        setState(() {
                          _sparkles.remove(sparkle);
                        });
                      },
                      builder: (context, value, child) {
                        return Opacity(
                          opacity: math.sin(value * math.pi),
                          child: Transform.rotate(
                            angle: value * math.pi * 2,
                            child: Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: sparkle.size,
                            ),
                          ),
                        );
                      },
                    ),
                  );
                }),
              
              // Premium badge with enhanced effects
              if (widget.skin.isPremium)
                Positioned(
                  top: 0,
                  right: 0,
                  child: TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0, end: 1),
                    duration: const Duration(milliseconds: 500),
                    builder: (context, value, child) {
                      return Transform.scale(
                        scale: 0.8 + (value * 0.2),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.amber,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.amber.withOpacity(0.4),
                                blurRadius: 8,
                                spreadRadius: value * 2,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.star,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      );
                    },
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class _SparkleParticle {
  final Offset position;
  final double size;
  final Duration duration;

  _SparkleParticle({
    required this.position,
    required this.size,
    required this.duration,
  });
}

// Custom painter for diagonal highlight on pin stem
class DiagonalHighlightPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;

  DiagonalHighlightPainter({
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    // Draw top-right to bottom-left diagonal highlight
    canvas.drawLine(
      Offset(size.width * 0.9, size.height * 0.1),
      Offset(size.width * 0.1, size.height * 0.9),
      paint,
    );
  }

  @override
  bool shouldRepaint(DiagonalHighlightPainter oldDelegate) {
    return color != oldDelegate.color || strokeWidth != oldDelegate.strokeWidth;
  }
} 