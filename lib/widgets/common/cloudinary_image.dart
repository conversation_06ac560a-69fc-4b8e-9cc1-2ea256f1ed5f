import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../services/cloudinary_service.dart';

/// A widget for displaying Cloudinary images with automatic optimization
/// and caching. This widget generates optimized URLs based on the container
/// size and device pixel ratio.
class CloudinaryImage extends StatelessWidget {
  final String publicId;
  final double? width;
  final double? height;
  final BoxFit fit;
  final int? quality;
  final String? format;
  final String? crop;
  final List<String>? effects;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;

  const CloudinaryImage({
    Key? key,
    required this.publicId,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.quality,
    this.format = 'webp',
    this.crop = 'fill',
    this.effects,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cloudinaryService = CloudinaryService();
    
    // Calculate optimal dimensions based on device pixel ratio
    final pixelRatio = MediaQuery.of(context).devicePixelRatio;
    final optimalWidth = width != null ? (width! * pixelRatio).round() : null;
    final optimalHeight = height != null ? (height! * pixelRatio).round() : null;
    
    // Generate optimized URL
    final imageUrl = cloudinaryService.generateImageUrl(
      publicId: publicId,
      width: optimalWidth,
      height: optimalHeight,
      quality: quality ?? 85,
      format: format,
      crop: crop,
      effects: effects,
    );

    if (kDebugMode) {
      print('🌤️ [CloudinaryImage] Generating image:');
      print('   Public ID: $publicId');
      print('   Generated URL: $imageUrl');
      print('   Dimensions: ${width}x$height (optimal: ${optimalWidth}x$optimalHeight)');
    }

    Widget imageWidget = CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) => placeholder ?? _defaultPlaceholder(),
      errorWidget: (context, url, error) {
        if (kDebugMode) {
          print('❌ [CloudinaryImage] Error loading image: $error');
          print('   URL: $url');
        }
        return errorWidget ?? _defaultErrorWidget();
      },
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 100),
    );

    // Apply border radius if provided
    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  Widget _defaultPlaceholder() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: borderRadius,
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _defaultErrorWidget() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: borderRadius,
      ),
      child: Icon(
        Icons.error_outline,
        color: Colors.grey[600],
        size: 32,
      ),
    );
  }
}

/// A specialized widget for circular profile images
class CloudinaryProfileImage extends StatelessWidget {
  final String publicId;
  final double size;
  final int? quality;
  final Widget? placeholder;
  final Widget? errorWidget;

  const CloudinaryProfileImage({
    Key? key,
    required this.publicId,
    this.size = 80,
    this.quality = 90,
    this.placeholder,
    this.errorWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CloudinaryImage(
      publicId: publicId,
      width: size,
      height: size,
      quality: quality,
      crop: 'fill',
      format: 'webp',
      effects: const ['g_face'], // Focus on face for profile images
      borderRadius: BorderRadius.circular(size / 2),
      placeholder: placeholder ?? _defaultProfilePlaceholder(),
      errorWidget: errorWidget ?? _defaultProfileErrorWidget(),
    );
  }

  Widget _defaultProfilePlaceholder() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.person,
        color: Colors.grey,
      ),
    );
  }

  Widget _defaultProfileErrorWidget() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.person,
        color: Colors.grey,
      ),
    );
  }
}

/// A widget for collection cover images with specific aspect ratio
class CloudinaryCollectionCover extends StatelessWidget {
  final String publicId;
  final double width;
  final double aspectRatio;
  final int? quality;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;

  const CloudinaryCollectionCover({
    Key? key,
    required this.publicId,
    required this.width,
    this.aspectRatio = 1.0, // Square by default
    this.quality = 85,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final height = width / aspectRatio;

    return CloudinaryImage(
      publicId: publicId,
      width: width,
      height: height,
      quality: quality,
      crop: 'fill',
      format: 'webp',
      borderRadius: borderRadius ?? BorderRadius.circular(12),
      placeholder: placeholder ?? _defaultCollectionPlaceholder(),
      errorWidget: errorWidget ?? _defaultCollectionErrorWidget(),
    );
  }

  Widget _defaultCollectionPlaceholder() {
    final height = width / aspectRatio;
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.purple.withOpacity(0.3),
            Colors.blue.withOpacity(0.3),
          ],
        ),
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      child: const Center(
        child: Icon(
          Icons.collections_outlined,
          color: Colors.white,
          size: 48,
        ),
      ),
    );
  }

  Widget _defaultCollectionErrorWidget() {
    final height = width / aspectRatio;
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      child: const Center(
        child: Icon(
          Icons.collections_outlined,
          color: Colors.grey,
          size: 48,
        ),
      ),
    );
  }
} 