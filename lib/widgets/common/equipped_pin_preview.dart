import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../models/pin_skin.dart';

class EquippedPinPreview extends StatefulWidget {
  final PinSkin skin;
  final double size;
  final VoidCallback? onTap;

  const EquippedPinPreview({
    Key? key,
    required this.skin,
    this.size = 120,
    this.onTap,
  }) : super(key: key);

  @override
  State<EquippedPinPreview> createState() => _EquippedPinPreviewState();
}

class _EquippedPinPreviewState extends State<EquippedPinPreview> with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _floatController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _floatAnimation;
  bool _isHovering = false;

  @override
  void initState() {
    super.initState();
    
    // Smooth, continuous rotation animation
    _rotationController = AnimationController(
      duration: const Duration(seconds: 8), // Slower, more majestic rotation
      vsync: this,
    );

    _rotationAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0, end: math.pi * 2)
            .chain(CurveTween(curve: Curves.easeInOutSine)),
        weight: 1,
      ),
    ]).animate(_rotationController);

    // Gentle floating animation
    _floatController = AnimationController(
      duration: const Duration(seconds: 3), // Slower float for more elegance
      vsync: this,
    );

    _floatAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0, end: 4) // Smaller float range
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 4, end: 0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 1,
      ),
    ]).animate(_floatController);

    // Start animations
    _rotationController.repeat();
    _floatController.repeat();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _floatController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: SizedBox(
          width: widget.size,
          height: widget.size * 1.5,
          child: AnimatedBuilder(
            animation: Listenable.merge([_rotationController, _floatController]),
            builder: (context, child) {
              return Transform(
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001) // perspective
                  ..translate(
                    0.0,
                    _isHovering ? -8.0 : -_floatAnimation.value,
                    0.0,
                  )
                  ..rotateY(_rotationAnimation.value)
                  ..rotateX(_isHovering ? -math.pi / 24 : math.sin(_floatAnimation.value / 4) * 0.05),
                alignment: Alignment.center,
                child: Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    // Pin Stem with realistic 3D effect
                    Positioned(
                      top: widget.size * 0.4,
                      child: Transform(
                        transform: Matrix4.identity()
                          ..setEntry(3, 2, 0.002)
                          ..rotateX(-math.pi / 6),
                        alignment: Alignment.topCenter,
                        child: Container(
                          width: widget.size * 0.06,
                          height: widget.size * 0.8,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                const Color(0xFFEEEEEE), // Top metallic
                                const Color(0xFFD8D8D8), // Upper metallic
                                const Color(0xFFC9C9C9), // Mid metallic
                                const Color(0xFFB0B0B0), // Lower metallic
                                const Color(0xFFA0A0A0), // Bottom metallic
                              ],
                              stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
                            ),
                            borderRadius: BorderRadius.circular(widget.size * 0.015),
                          ),
                          child: Stack(
                            children: [
                              // Base metal texture
                              Positioned.fill(
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(widget.size * 0.015),
                                    gradient: LinearGradient(
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                      colors: [
                                        const Color(0xFFDDDDDD),
                                        const Color(0xFFCCCCCC),
                                        const Color(0xFFE5E5E5),
                                        const Color(0xFFCCCCCC),
                                      ],
                                      stops: const [0.0, 0.3, 0.6, 1.0],
                                    ),
                                  ),
                                ),
                              ),
                              
                              // Central highlight line (simulates light reflection)
                              Positioned(
                                left: widget.size * 0.03 - (widget.size * 0.01 / 2),
                                top: 0,
                                bottom: 0,
                                width: widget.size * 0.01,
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        Colors.white.withOpacity(0.9),
                                        Colors.white.withOpacity(0.7),
                                        Colors.white.withOpacity(0.2),
                                        Colors.white.withOpacity(0.0),
                                      ],
                                      stops: const [0.0, 0.2, 0.5, 0.8],
                                    ),
                                  ),
                                ),
                              ),
                              
                              // Diagonal highlight (simulates rounded metal)
                              Positioned.fill(
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(widget.size * 0.015),
                                  child: CustomPaint(
                                    painter: DiagonalHighlightPainter(
                                      color: Colors.white.withOpacity(0.4),
                                      strokeWidth: widget.size * 0.02,
                                    ),
                                  ),
                                ),
                              ),
                              
                              // Edge highlight to add dimension
                              Positioned(
                                left: 0,
                                top: 0,
                                bottom: 0,
                                width: widget.size * 0.01,
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                      colors: [
                                        Colors.white.withOpacity(0.7),
                                        Colors.white.withOpacity(0.0),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(widget.size * 0.015),
                                      bottomLeft: Radius.circular(widget.size * 0.015),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    
                    // Pin Head with enhanced lighting
                    Positioned(
                      top: widget.size * 0.1,
                      child: Container(
                        width: widget.size * 0.8,
                        height: widget.size * 0.8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          image: DecorationImage(
                            image: widget.skin.image.startsWith('http')
                                ? NetworkImage(widget.skin.image)
                                : AssetImage(widget.skin.image) as ImageProvider,
                            fit: BoxFit.cover,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.4),
                              blurRadius: 12,
                              offset: const Offset(4, 6),
                            ),
                            BoxShadow(
                              color: Colors.white.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(-2, -2),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    // Dynamic lighting effect
                    Positioned(
                      top: widget.size * 0.1,
                      child: Container(
                        width: widget.size * 0.8,
                        height: widget.size * 0.8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: SweepGradient(
                            colors: [
                              Colors.white.withOpacity(0.1),
                              Colors.white.withOpacity(0.0),
                              Colors.black.withOpacity(0.1),
                              Colors.black.withOpacity(0.0),
                            ],
                            stops: [0.0, 0.25, 0.5, 0.75],
                            transform: GradientRotation(_rotationAnimation.value),
                          ),
                        ),
                      ),
                    ),
                    
                    // Hover effect
                    if (_isHovering)
                      Positioned(
                        top: widget.size * 0.1,
                        child: Container(
                          width: widget.size * 0.8,
                          height: widget.size * 0.8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                Colors.white.withOpacity(0.2),
                                Colors.white.withOpacity(0.0),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

// Custom painter for diagonal highlight on pin stem
class DiagonalHighlightPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;

  DiagonalHighlightPainter({
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    // Draw top-right to bottom-left diagonal highlight
    canvas.drawLine(
      Offset(size.width * 0.9, size.height * 0.1),
      Offset(size.width * 0.1, size.height * 0.9),
      paint,
    );
  }

  @override
  bool shouldRepaint(DiagonalHighlightPainter oldDelegate) {
    return color != oldDelegate.color || strokeWidth != oldDelegate.strokeWidth;
  }
} 