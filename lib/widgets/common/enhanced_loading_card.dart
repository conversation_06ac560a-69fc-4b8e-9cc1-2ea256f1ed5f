import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../theme/app_colors.dart';

/// Enhanced loading card with shimmer effect and better visual feedback
class EnhancedLoadingCard extends StatelessWidget {
  final double width;
  final double height;
  final String? loadingText;
  final bool showShimmer;
  final BorderRadius? borderRadius;

  const EnhancedLoadingCard({
    super.key,
    this.width = 150,
    this.height = 180,
    this.loadingText,
    this.showShimmer = true,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final borderRad = borderRadius ?? BorderRadius.circular(12);

    return Container(
      width: width,
      height: height,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: borderRad,
        color: Theme.of(context).cardColor.withOpacity(0.1),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // Shimmer background
          if (showShimmer)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: borderRad,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: isDarkMode
                        ? [
                            Colors.grey[800]!.withOpacity(0.3),
                            Colors.grey[700]!.withOpacity(0.5),
                            Colors.grey[800]!.withOpacity(0.3),
                          ]
                        : [
                            Colors.grey[200]!.withOpacity(0.3),
                            Colors.grey[100]!.withOpacity(0.5),
                            Colors.grey[200]!.withOpacity(0.3),
                          ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                ),
              )
                  .animate(onPlay: (controller) => controller.repeat())
                  .shimmer(
                    duration: 1500.milliseconds,
                    color: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.white.withOpacity(0.3),
                  ),
            ),

          // Content
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Animated loading indicator
                SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.primary,
                    ),
                  ),
                )
                    .animate(onPlay: (controller) => controller.repeat())
                    .rotate(duration: 1000.milliseconds),

                if (loadingText != null) ...[
                  const SizedBox(height: 12),
                  Text(
                    loadingText!,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  )
                      .animate(onPlay: (controller) => controller.repeat())
                      .fadeIn(duration: 800.milliseconds)
                      .then()
                      .fadeOut(duration: 800.milliseconds),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Skeleton loading card that mimics the actual content structure
class SkeletonLoadingCard extends StatelessWidget {
  final double width;
  final double height;
  final bool showImage;
  final bool showText;
  final int textLines;

  const SkeletonLoadingCard({
    super.key,
    this.width = 150,
    this.height = 180,
    this.showImage = true,
    this.showText = true,
    this.textLines = 2,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final shimmerColor = isDarkMode
        ? Colors.grey[700]!.withOpacity(0.3)
        : Colors.grey[300]!.withOpacity(0.3);

    return Container(
      width: width,
      height: height,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Theme.of(context).cardColor.withOpacity(0.1),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image skeleton
          if (showImage)
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: shimmerColor,
                ),
              )
                  .animate(onPlay: (controller) => controller.repeat())
                  .shimmer(
                    duration: 1500.milliseconds,
                    color: Colors.white.withOpacity(0.2),
                  ),
            ),

          // Text skeleton
          if (showText)
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: List.generate(textLines, (index) {
                    final width = index == 0 ? 0.8 : 0.6;
                    return Container(
                      width: (this.width - 16) * width,
                      height: 12,
                      margin: const EdgeInsets.only(bottom: 4),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: shimmerColor,
                      ),
                    )
                        .animate(
                          delay: (index * 200).milliseconds,
                          onPlay: (controller) => controller.repeat(),
                        )
                        .shimmer(
                          duration: 1500.milliseconds,
                          color: Colors.white.withOpacity(0.2),
                        );
                  }),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Loading state indicator for lists
class ListLoadingIndicator extends StatelessWidget {
  final String message;
  final bool showRetry;
  final VoidCallback? onRetry;

  const ListLoadingIndicator({
    super.key,
    this.message = 'Loading more...',
    this.showRetry = false,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!showRetry) ...[
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppColors.primary,
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],
          Text(
            showRetry ? 'Failed to load more content' : message,
            style: TextStyle(
              fontSize: 14,
              color: showRetry
                  ? Colors.red
                  : Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          if (showRetry && onRetry != null) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: onRetry,
              child: const Text('Retry'),
            ),
          ],
        ],
      ),
    );
  }
}

/// Empty state widget with helpful messaging
class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String message;
  final String? actionText;
  final VoidCallback? onAction;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.message,
    this.actionText,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
          )
              .animate()
              .scale(
                begin: const Offset(0.5, 0.5),
                end: const Offset(1, 1),
                duration: 300.milliseconds,
              )
              .fadeIn(duration: 300.milliseconds),

          const SizedBox(height: 16),

          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).textTheme.headlineSmall?.color,
            ),
            textAlign: TextAlign.center,
          )
              .animate(delay: 100.milliseconds)
              .slideY(
                begin: 0.3,
                end: 0,
                duration: 300.milliseconds,
              )
              .fadeIn(duration: 300.milliseconds),

          const SizedBox(height: 8),

          Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.color
                  ?.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          )
              .animate(delay: 200.milliseconds)
              .slideY(
                begin: 0.3,
                end: 0,
                duration: 300.milliseconds,
              )
              .fadeIn(duration: 300.milliseconds),

          if (actionText != null && onAction != null) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onAction,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: Text(actionText!),
            )
                .animate(delay: 300.milliseconds)
                .scale(
                  begin: const Offset(0.8, 0.8),
                  end: const Offset(1, 1),
                  duration: 300.milliseconds,
                )
                .fadeIn(duration: 300.milliseconds),
          ],
        ],
      ),
    );
  }
}