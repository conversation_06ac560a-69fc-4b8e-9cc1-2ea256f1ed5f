import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../config/constants.dart';

class CachedAvatar extends StatelessWidget {
  final String? imageUrl;
  final double radius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BoxFit? fit;

  const CachedAvatar({
    Key? key,
    this.imageUrl,
    required this.radius,
    this.placeholder,
    this.errorWidget,
    this.fit = BoxFit.cover,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Default fallback avatar
    Widget fallbackAvatar = CircleAvatar(
      radius: radius,
      backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
      child: Icon(
        Icons.person_rounded,
        size: radius * 1.2,
        color: theme.colorScheme.primary.withOpacity(0.7),
      ),
    );

    // If no URL provided, return fallback
    if (imageUrl == null || imageUrl!.isEmpty) {
      return fallbackAvatar;
    }

    // Convert relative URLs to full URLs
    String fullImageUrl = _buildFullImageUrl(imageUrl!);
    
    // Validate the final URL
    if (Uri.tryParse(fullImageUrl) == null) {
      debugPrint('CachedAvatar: Invalid URL after processing: $fullImageUrl (original: $imageUrl)');
      return fallbackAvatar;
    }

    return ClipOval(
      child: CachedNetworkImage(
        imageUrl: fullImageUrl,
        width: radius * 2,
        height: radius * 2,
        fit: fit ?? BoxFit.cover,
        placeholder: (context, url) => placeholder ?? SizedBox(
          width: radius * 2,
          height: radius * 2,
          child: Center(
            child: SizedBox(
              width: radius,
              height: radius,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            ),
          ),
        ),
        errorWidget: (context, url, error) {
          debugPrint('CachedAvatar error loading $url: $error');
          return errorWidget ?? fallbackAvatar;
        },
        fadeInDuration: const Duration(milliseconds: 300),
        fadeOutDuration: const Duration(milliseconds: 300),
        memCacheWidth: (radius * 2 * MediaQuery.of(context).devicePixelRatio).toInt(),
        errorListener: (error) {
          debugPrint('CachedAvatar error loading $fullImageUrl: $error (original: $imageUrl)');
        },
      ),
    );
  }

  /// Converts relative URLs to full URLs using the base API URL
  String _buildFullImageUrl(String imageUrl) {
    // If it's already a full URL (starts with http/https), return as is
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }
    
    // If it's a relative URL (starts with /), prepend the base URL
    if (imageUrl.startsWith('/')) {
      // Remove /api from baseApiUrl since media files are typically served from root
      String baseUrl = AppConstants.baseApiUrl;
      if (baseUrl.endsWith('/api')) {
        baseUrl = baseUrl.substring(0, baseUrl.length - 4);
      }
      return '$baseUrl$imageUrl';
    }
    
    // If it doesn't start with /, prepend both base URL and /
    String baseUrl = AppConstants.baseApiUrl;
    if (baseUrl.endsWith('/api')) {
      baseUrl = baseUrl.substring(0, baseUrl.length - 4);
    }
    return '$baseUrl/$imageUrl';
  }
} 