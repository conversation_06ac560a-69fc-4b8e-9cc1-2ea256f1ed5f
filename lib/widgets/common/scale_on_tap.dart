import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// A reusable wrapper that provides a subtle scale animation (and optional
/// haptic feedback) whenever its child is tapped.
///
/// The widget uses the `flutter_animate` package to animate a quick scale down
/// on tap-down and scale back to normal on tap-up/cancel. It is intentionally
/// lightweight so it can be wrapped around any tappable widget (e.g. `Card`,
/// `ListTile`, custom buttons, etc.) without having to modify their internal
/// structure.
class ScaleOnTap extends StatefulWidget {
  /// The widget below this wrapper.
  final Widget child;

  /// Callback invoked when the tap gesture completes.
  final VoidCallback onTap;

  /// Whether to trigger a light haptic feedback vibration when the tap
  /// completes. Defaults to `true`.
  final bool haptic;

  /// Creates a [ScaleOnTap] wrapper.
  const ScaleOnTap({
    Key? key,
    required this.child,
    required this.onTap,
    this.haptic = true,
  }) : super(key: key);

  @override
  State<ScaleOnTap> createState() => _ScaleOnTapState();
}

class _ScaleOnTapState extends State<ScaleOnTap> {
  bool _pressed = false;

  void _handleTapDown(TapDownDetails details) {
    setState(() => _pressed = true);
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.haptic) {
      HapticFeedback.lightImpact();
    }
    widget.onTap();
    // Delay resetting the scale slightly so the user feels the press state.
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() => _pressed = false);
      }
    });
  }

  void _handleTapCancel() {
    setState(() => _pressed = false);
  }

  @override
  Widget build(BuildContext context) {
    // The `animate` extension from flutter_animate handles the scale effect.
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: widget.child
          .animate(target: _pressed ? 1 : 0)
          .scale(
            begin: const Offset(1, 1),
            end: const Offset(0.93, 0.93),
            duration: 100.ms,
            curve: Curves.easeOut,
          ),
    );
  }
} 