import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

/// A widget to display error states with an option to retry
class Error<PERSON>iew extends StatelessWidget {
  final String? message;
  final String? title;
  final VoidCallback? onRetry;
  final bool useAnimation;
  final double? width;
  final double? height;
  
  const ErrorView({
    Key? key,
    this.message,
    this.title,
    this.onRetry,
    this.useAnimation = true,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Center(
      child: Container(
        width: width,
        height: height,
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (useAnimation) ...[
              // Uses Lottie animation for error state
              // Replace with your own animation asset
              Lottie.asset(
                'assets/animations/error.json',
                width: 150,
                height: 150,
                repeat: true,
                frameRate: FrameRate.max,
              ),
              const SizedBox(height: 24),
            ] else ...[
              Icon(
                Icons.error_outline_rounded,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
            ],
            
            if (title != null) ...[
              Text(
                title!,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
            ],
            
            if (message != null) ...[
              Text(
                message!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
            ],
            
            if (onRetry != null)
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// A compact error view for in-line display
class CompactErrorView extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;
  
  const CompactErrorView({
    Key? key,
    this.message,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.errorContainer,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline_rounded,
            color: theme.colorScheme.error,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message ?? 'Something went wrong',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onErrorContainer,
              ),
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(width: 12),
            IconButton(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh_rounded),
              tooltip: 'Retry',
              color: theme.colorScheme.primary,
            ),
          ]
        ],
      ),
    );
  }
}

/// An error placeholder for images
class ImageErrorView extends StatelessWidget {
  final VoidCallback? onRetry;
  final double? width;
  final double? height;
  
  const ImageErrorView({
    Key? key,
    this.onRetry,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image_rounded,
            size: 32,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 8),
            IconButton(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh_rounded, size: 20),
              style: IconButton.styleFrom(
                padding: const EdgeInsets.all(8),
                backgroundColor: theme.colorScheme.surface,
              ),
            ),
          ],
        ],
      ),
    );
  }
} 