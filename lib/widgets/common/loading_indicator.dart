import 'package:flutter/material.dart';

class LoadingIndicator extends StatelessWidget {
  final String? message;
  final Color color;
  final double size;

  const LoadingIndicator({
    Key? key,
    this.message,
    this.color = Colors.blue,
    this.size = 24.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CircularProgressIndicator(
          color: color,
          strokeWidth: 3.0,
        ),
        if (message != null)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Text(
              message!,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: color,
                fontSize: 16.0,
              ),
            ),
          ),
      ],
    );
  }
} 