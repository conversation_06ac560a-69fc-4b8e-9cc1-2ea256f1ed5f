import 'package:flutter/material.dart';
import 'dart:math' as math;

class Pin3DWidget extends StatefulWidget {
  final String skinImageUrl;
  final double size;
  final bool enableAutoRotation;
  final bool enableInteraction;
  final Duration rotationDuration;
  final Color pinColor;
  final double needleHeight;
  final double pinHeadSize;

  const Pin3DWidget({
    Key? key,
    required this.skinImageUrl,
    this.size = 100.0,
    this.enableAutoRotation = true,
    this.enableInteraction = false,
    this.rotationDuration = const Duration(seconds: 8),
    this.pinColor = Colors.grey,
    this.needleHeight = 40.0,
    this.pinHeadSize = 30.0,
  }) : super(key: key);

  @override
  State<Pin3DWidget> createState() => _Pin3DWidgetState();
}

class _Pin3DWidgetState extends State<Pin3DWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _rotationController;
  double _userRotationX = 0.0;
  double _userRotationY = 0.0;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: widget.rotationDuration,
      vsync: this,
    );

    if (widget.enableAutoRotation) {
      _rotationController.repeat();
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!widget.enableInteraction) return;

    setState(() {
      _userRotationY += details.delta.dx * 0.01;
      _userRotationX -= details.delta.dy * 0.01;
      _userRotationX = _userRotationX.clamp(-math.pi / 3, math.pi / 3);
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanUpdate: _onPanUpdate,
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: AnimatedBuilder(
          animation: _rotationController,
          builder: (context, child) {
            final autoRotationY = widget.enableAutoRotation 
                ? _rotationController.value * 2 * math.pi 
                : 0.0;
            
            final totalRotationY = autoRotationY + _userRotationY;
            
            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001) // Perspective
                ..rotateX(_userRotationX)
                ..rotateY(totalRotationY),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Shadow
                  Positioned(
                    bottom: 5,
                    child: Transform.rotate(
                      angle: totalRotationY,
                      child: Container(
                        width: widget.pinHeadSize + 4,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                  
                  // 3D Pin using CustomPaint
                  CustomPaint(
                    painter: Pin3DPainter(
                      skinImageUrl: widget.skinImageUrl,
                      pinColor: widget.pinColor,
                      needleHeight: widget.needleHeight,
                      pinHeadSize: widget.pinHeadSize,
                      rotationY: totalRotationY,
                    ),
                    size: Size(widget.size, widget.size),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

class Pin3DPainter extends CustomPainter {
  final String skinImageUrl;
  final Color pinColor;
  final double needleHeight;
  final double pinHeadSize;
  final double rotationY;

  Pin3DPainter({
    required this.skinImageUrl,
    required this.pinColor,
    required this.needleHeight,
    required this.pinHeadSize,
    required this.rotationY,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = pinColor
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    
    // Calculate 3D positions based on rotation
    final cosY = math.cos(rotationY);
    final sinY = math.sin(rotationY);
    
    // Draw pin needle (stick part)
    final needleTop = Offset(center.dx, center.dy - needleHeight / 2);
    final needleBottom = Offset(center.dx, center.dy + needleHeight / 2);
    
    // Needle gets thinner based on viewing angle
    final needleWidth = 3.0 * cosY.abs();
    
    if (cosY > 0) {
      // Front face of needle
      paint.color = pinColor.withOpacity(0.9);
    } else {
      // Back face of needle (darker)
      paint.color = pinColor.withOpacity(0.6);
    }
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset(center.dx, center.dy),
          width: needleWidth,
          height: needleHeight,
        ),
        const Radius.circular(1.5),
      ),
      paint,
    );
    
    // Draw pin head (the round part with the skin texture)
    final headCenter = Offset(center.dx, center.dy - needleHeight / 2 - pinHeadSize / 2);
    
    // Draw pin head base
    paint.color = Colors.grey[300]!;
    canvas.drawCircle(headCenter, pinHeadSize / 2, paint);
    
    // Draw skin texture on pin head
    _drawSkinTexture(canvas, headCenter, pinHeadSize / 2);
    
    // Add pin head rim/border
    paint
      ..color = Colors.grey[600]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    canvas.drawCircle(headCenter, pinHeadSize / 2, paint);
    
    // Add highlight on pin head
    final highlightPaint = Paint()
      ..color = Colors.white.withOpacity(0.4)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(headCenter.dx - pinHeadSize / 6, headCenter.dy - pinHeadSize / 6),
      pinHeadSize / 8,
      highlightPaint,
    );
  }

  void _drawSkinTexture(Canvas canvas, Offset center, double radius) {
    final texturePaint = Paint()
      ..color = _getSkinColor()
      ..style = PaintingStyle.fill;
    
    // Draw texture pattern
    canvas.save();
    canvas.clipPath(Path()..addOval(Rect.fromCircle(center: center, radius: radius)));
    
    // Create a pattern based on skin name/URL (not trying to load actual image)
    final skinName = skinImageUrl.toLowerCase();
    
    if (skinName.contains('neon') || skinName.contains('purple')) {
      // Neon glow effect
      final glowPaint = Paint()
        ..color = Colors.cyan.withOpacity(0.6)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, radius * 0.8, glowPaint);
      
      texturePaint.color = Colors.cyan[300]!;
      canvas.drawCircle(center, radius * 0.6, texturePaint);
      
      // Add glow lines
      final linePaint = Paint()
        ..color = Colors.white.withOpacity(0.8)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;
      
      for (int i = 0; i < 4; i++) {
        final angle = (i * math.pi) / 2;
        final startX = center.dx + math.cos(angle) * radius * 0.2;
        final startY = center.dy + math.sin(angle) * radius * 0.2;
        final endX = center.dx + math.cos(angle) * radius * 0.7;
        final endY = center.dy + math.sin(angle) * radius * 0.7;
        
        canvas.drawLine(Offset(startX, startY), Offset(endX, endY), linePaint);
      }
    } else if (skinName.contains('crystal') || skinName.contains('blue')) {
      // Crystal facet effect
      texturePaint.color = Colors.blue[200]!;
      canvas.drawCircle(center, radius, texturePaint);
      
      // Draw crystal facets
      final facetPaint = Paint()
        ..color = Colors.white.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;
      
      for (int i = 0; i < 6; i++) {
        final angle = (i * math.pi * 2) / 6;
        final startX = center.dx + math.cos(angle) * radius * 0.3;
        final startY = center.dy + math.sin(angle) * radius * 0.3;
        final endX = center.dx + math.cos(angle) * radius * 0.8;
        final endY = center.dy + math.sin(angle) * radius * 0.8;
        
        canvas.drawLine(Offset(startX, startY), Offset(endX, endY), facetPaint);
      }
    } else if (skinName.contains('retro') || skinName.contains('orange')) {
      // Retro pattern
      texturePaint.color = Colors.orange[300]!;
      canvas.drawCircle(center, radius, texturePaint);
      
      // Draw retro stripes
      final stripePaint = Paint()
        ..color = Colors.yellow.withOpacity(0.6)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      
      for (int i = 0; i < 3; i++) {
        canvas.drawCircle(center, radius * (0.3 + i * 0.2), stripePaint);
      }
    } else if (skinName.contains('gold') || skinName.contains('golden')) {
      // Golden pattern
      texturePaint.color = Colors.amber[300]!;
      canvas.drawCircle(center, radius, texturePaint);
      
      // Draw golden sparkles
      final sparklePaint = Paint()
        ..color = Colors.yellow[200]!
        ..style = PaintingStyle.fill;
      
      for (int i = 0; i < 8; i++) {
        final angle = (i * math.pi * 2) / 8;
        final sparkleX = center.dx + math.cos(angle) * radius * 0.6;
        final sparkleY = center.dy + math.sin(angle) * radius * 0.6;
        
        canvas.drawCircle(Offset(sparkleX, sparkleY), 1.5, sparklePaint);
      }
    } else {
      // Default texture
      texturePaint.color = Colors.blue[400]!;
      canvas.drawCircle(center, radius, texturePaint);
      
      // Simple dot pattern
      final dotPaint = Paint()
        ..color = Colors.white.withOpacity(0.4)
        ..style = PaintingStyle.fill;
      
      for (int i = 0; i < 6; i++) {
        final angle = (i * math.pi * 2) / 6;
        final dotX = center.dx + math.cos(angle) * radius * 0.5;
        final dotY = center.dy + math.sin(angle) * radius * 0.5;
        
        canvas.drawCircle(Offset(dotX, dotY), 2, dotPaint);
      }
    }
    
    canvas.restore();
  }

  Color _getSkinColor() {
    final skinName = skinImageUrl.toLowerCase();
    if (skinName.contains('neon') || skinName.contains('purple')) return Colors.cyan;
    if (skinName.contains('crystal') || skinName.contains('blue')) return Colors.blue;
    if (skinName.contains('retro') || skinName.contains('orange')) return Colors.orange;
    if (skinName.contains('gold') || skinName.contains('golden')) return Colors.amber;
    return Colors.blue;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
} 