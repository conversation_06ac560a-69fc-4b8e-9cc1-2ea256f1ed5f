import 'dart:math' as math;
import 'dart:math' show Random;
import 'dart:ui';
import 'package:flutter/material.dart';
import '../../config/constants.dart';
import '../../config/themes.dart';
import '../../models/pin_skin.dart';

class MapPinWidget extends StatefulWidget {
  final Map<String, dynamic> pinData;
  final Color? customColor;
  final bool isSelected;
  final bool animate;
  final double size;
  
  const MapPinWidget({
    Key? key,
    required this.pinData,
    this.customColor,
    this.isSelected = false,
    this.animate = true,
    this.size = 40,
  }) : super(key: key);

  @override
  State<MapPinWidget> createState() => _MapPinWidgetState();
}

class _MapPinWidgetState extends State<MapPinWidget> with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _floatController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _floatAnimation;
  bool _isHovering = false;
  final List<_SparkleParticle> _sparkles = [];
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    
    // Main rotation animation
    _rotationController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    );

    _rotationAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0, end: math.pi * 2)
            .chain(CurveTween(curve: Curves.easeInOutSine)),
        weight: 1,
      ),
    ]).animate(_rotationController);

    // Floating animation
    _floatController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _floatAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0, end: 6)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 6, end: 0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 1,
      ),
    ]).animate(_floatController);

    if (widget.animate) {
      _rotationController.repeat();
      _floatController.repeat();
      _generateSparkles();
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _floatController.dispose();
    super.dispose();
  }

  void _generateSparkles() {
    if (!mounted) return;
    
    Future.delayed(Duration(milliseconds: _random.nextInt(1000) + 500), () {
      if (!mounted) return;
      
      setState(() {
        if (_sparkles.length < 5) {
          _sparkles.add(_SparkleParticle(
            position: Offset(
              _random.nextDouble() * widget.size,
              _random.nextDouble() * widget.size,
            ),
            size: _random.nextDouble() * 4 + 2,
            duration: Duration(milliseconds: _random.nextInt(1000) + 1000),
          ));
        }
      });
      
      _generateSparkles();
    });
  }

  bool get _isPremium {
    final String rarity = widget.pinData['rarity']?.toLowerCase() ?? 'common';
    return rarity == 'legendary' || rarity == 'epic';
  }

  String get _pinImage {
    final String rarity = widget.pinData['rarity']?.toLowerCase() ?? 'common';
    return 'assets/images/pins/default_pin.png';
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: Container(
        width: widget.size,
        height: widget.size * 1.5,
        child: Stack(
          children: [
            // Main pin with animations
            AnimatedBuilder(
              animation: Listenable.merge([
                _rotationController,
                _floatController,
              ]),
              builder: (context, child) {
                // Calculate rotation based on whether it's animated and hovering
                double rotationY = 0.0;
                if (widget.animate) {
                  // Full rotation only for selected pins or when hovering
                  if (widget.isSelected || _isHovering) {
                    rotationY = _rotationAnimation.value;
                  } else {
                    // Subtle oscillation for non-selected pins
                    rotationY = math.sin(_rotationAnimation.value) * 0.2;
                  }
                } else if (_isHovering) {
                  rotationY = math.pi / 6;
                }

                return Transform(
                  transform: Matrix4.identity()
                    ..setEntry(3, 2, 0.001) // perspective
                    ..translate(
                      0.0,
                      _isHovering ? -10.0 : -_floatAnimation.value,
                      0.0,
                    )
                    ..rotateY(rotationY)
                    ..rotateX(_isHovering ? -math.pi / 12 : math.sin(_floatAnimation.value / 3) * 0.1),
                  alignment: Alignment.center,
                  child: Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      // Pin Stem with enhanced 3D effect
                      Positioned(
                        top: widget.size * 0.4,
                        child: Transform(
                          transform: Matrix4.identity()
                            ..setEntry(3, 2, 0.002)
                            ..rotateX(-math.pi / 6),
                          alignment: Alignment.topCenter,
                          child: Container(
                            width: widget.size * 0.06,
                            height: widget.size * 0.8,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  const Color(0xFFEEEEEE), // Top metallic
                                  const Color(0xFFD8D8D8), // Upper metallic
                                  const Color(0xFFC9C9C9), // Mid metallic
                                  const Color(0xFFB0B0B0), // Lower metallic
                                  const Color(0xFFA0A0A0), // Bottom metallic
                                ],
                                stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
                              ),
                              borderRadius: BorderRadius.circular(widget.size * 0.015),
                            ),
                            child: Stack(
                              children: [
                                // Base metal texture
                                Positioned.fill(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(widget.size * 0.015),
                                      gradient: LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: [
                                          const Color(0xFFDDDDDD),
                                          const Color(0xFFCCCCCC),
                                          const Color(0xFFE5E5E5),
                                          const Color(0xFFCCCCCC),
                                        ],
                                        stops: const [0.0, 0.3, 0.6, 1.0],
                                      ),
                                    ),
                                  ),
                                ),
                                
                                // Central highlight line
                                Positioned(
                                  left: widget.size * 0.03 - (widget.size * 0.01 / 2),
                                  top: 0,
                                  bottom: 0,
                                  width: widget.size * 0.01,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          Colors.white.withOpacity(0.9),
                                          Colors.white.withOpacity(0.7),
                                          Colors.white.withOpacity(0.2),
                                          Colors.white.withOpacity(0.0),
                                        ],
                                        stops: const [0.0, 0.2, 0.5, 0.8],
                                      ),
                                    ),
                                  ),
                                ),
                                
                                // Diagonal highlight
                                Positioned.fill(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(widget.size * 0.015),
                                    child: CustomPaint(
                                      painter: DiagonalHighlightPainter(
                                        color: Colors.white.withOpacity(0.4),
                                        strokeWidth: widget.size * 0.02,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      
                      // Pin Head with enhanced effects
                      Positioned(
                        top: widget.size * 0.1,
                        child: Container(
                          width: widget.size * 0.8,
                          height: widget.size * 0.8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            image: DecorationImage(
                              image: AssetImage(_pinImage),
                              fit: BoxFit.cover,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.4),
                                blurRadius: 12,
                                offset: const Offset(4, 6),
                              ),
                              BoxShadow(
                                color: Colors.white.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(-2, -2),
                              ),
                            ],
                          ),
                          child: Stack(
                            children: [
                              // Hover glow
                              if (_isHovering)
                                Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: RadialGradient(
                                      colors: [
                                        Colors.white.withOpacity(0.2),
                                        Colors.white.withOpacity(0.0),
                                      ],
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                      
                      // Selection Indicator with glow
                      if (widget.isSelected)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Theme.of(context).colorScheme.primary,
                                width: 3,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                                  blurRadius: 8,
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
            
            // Sparkle particles
            if (widget.animate && _isPremium)
              ...List.generate(_sparkles.length, (index) {
                final sparkle = _sparkles[index];
                return Positioned(
                  left: sparkle.position.dx,
                  top: sparkle.position.dy,
                  child: TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0, end: 1),
                    duration: sparkle.duration,
                    onEnd: () {
                      setState(() {
                        _sparkles.remove(sparkle);
                      });
                    },
                    builder: (context, value, child) {
                      return Opacity(
                        opacity: math.sin(value * math.pi),
                        child: Transform.rotate(
                          angle: value * math.pi * 2,
                          child: Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: sparkle.size,
                          ),
                        ),
                      );
                    },
                  ),
                );
              }),
            
            // Premium badge with enhanced effects
            if (_isPremium)
              Positioned(
                top: 0,
                right: 0,
                child: TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0, end: 1),
                  duration: const Duration(milliseconds: 500),
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: 0.8 + (value * 0.2),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.amber.withOpacity(0.4),
                              blurRadius: 8,
                              spreadRadius: value * 2,
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.star,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _SparkleParticle {
  final Offset position;
  final double size;
  final Duration duration;

  _SparkleParticle({
    required this.position,
    required this.size,
    required this.duration,
  });
}

// Custom painter for diagonal highlight on pin stem
class DiagonalHighlightPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;

  DiagonalHighlightPainter({
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      Offset(size.width * 0.9, size.height * 0.1),
      Offset(size.width * 0.1, size.height * 0.9),
      paint,
    );
  }

  @override
  bool shouldRepaint(DiagonalHighlightPainter oldDelegate) {
    return color != oldDelegate.color || strokeWidth != oldDelegate.strokeWidth;
  }
}

class MapPinPainter extends CustomPainter {
  final Color pinColor;
  final String rarity;
  
  MapPinPainter({
    required this.pinColor,
    required this.rarity,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final double pinHeadRadius = size.width * 0.3;
    final double pinShaftWidth = size.width * 0.05;
    final double pinShaftHeight = size.height * 0.25;
    
    final double centerX = size.width / 2;
    final double pinHeadCenterY = size.height * 0.25;

    // Draw premium effects based on rarity
    if (rarity != 'common') {
      // Outer glow
      final glowPaint = Paint()
        ..color = pinColor.withOpacity(0.3)
        ..maskFilter = MaskFilter.blur(BlurStyle.outer, _getGlowRadius());
      
      canvas.drawCircle(
        Offset(centerX, pinHeadCenterY),
        pinHeadRadius * 1.5,
        glowPaint,
      );

      // Animated sparkles for legendary pins
      if (rarity == 'legendary') {
        _drawSparkles(canvas, Offset(centerX, pinHeadCenterY), pinHeadRadius);
      }
    }

    // Enhanced shadow for 3D effect
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.4)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
    
    canvas.drawCircle(
      Offset(centerX + 2, pinHeadCenterY + 2), 
      pinHeadRadius, 
      shadowPaint,
    );

    // Premium gradient for pin head
    final headRect = Rect.fromCircle(
      center: Offset(centerX, pinHeadCenterY),
      radius: pinHeadRadius,
    );
    
    final gradientPaint = Paint()
      ..shader = _createPremiumGradient(headRect)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(centerX, pinHeadCenterY), 
      pinHeadRadius, 
      gradientPaint,
    );

    // Metallic shine effect
    final shinePaint = Paint()
      ..color = Colors.white.withOpacity(0.7)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);
    
    canvas.drawCircle(
      Offset(centerX - pinHeadRadius * 0.3, pinHeadCenterY - pinHeadRadius * 0.3),
      pinHeadRadius * 0.3,
      shinePaint,
    );

    // Enhanced pin shaft with gradient
    final shaftRect = Rect.fromLTWH(
      centerX - pinShaftWidth / 2,
      pinHeadCenterY + pinHeadRadius - pinShaftWidth / 2,
      pinShaftWidth,
      pinShaftHeight,
    );
    
    final shaftPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          pinColor,
          pinColor.withOpacity(0.7),
        ],
      ).createShader(shaftRect);
    
    canvas.drawRect(shaftRect, shaftPaint);

    // Enhanced shadow/base effect
    final basePaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);
    
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(centerX, pinHeadCenterY + pinHeadRadius + pinShaftHeight),
        width: pinShaftWidth * 3,
        height: pinShaftWidth * 1.5,
      ),
      basePaint,
    );

    // Draw premium music icon
    _drawPremiumMusicIcon(canvas, Offset(centerX, pinHeadCenterY), pinHeadRadius * 0.6);
  }

  void _drawPremiumMusicIcon(Canvas canvas, Offset center, double size) {
    final iconPaint = Paint()
      ..color = Colors.white.withOpacity(0.9)
      ..style = PaintingStyle.fill
      ..strokeWidth = size * 0.15
      ..strokeCap = StrokeCap.round;

    // Enhanced music note with better proportions
    final noteHeadCenter = Offset(
      center.dx - size * 0.15,
      center.dy + size * 0.25,
    );

    // Note head with gradient
    final noteHeadGradient = RadialGradient(
      colors: [Colors.white, Colors.white.withOpacity(0.7)],
      center: Alignment.topLeft,
    ).createShader(Rect.fromCircle(
      center: noteHeadCenter,
      radius: size * 0.25,
    ));

    final noteHeadPaint = Paint()
      ..shader = noteHeadGradient;

    canvas.drawCircle(noteHeadCenter, size * 0.25, noteHeadPaint);

    // Stem with thickness variation
    final stemPath = Path()
      ..moveTo(noteHeadCenter.dx + size * 0.2, noteHeadCenter.dy)
      ..lineTo(noteHeadCenter.dx + size * 0.2, center.dy - size * 0.4);

    canvas.drawPath(stemPath, iconPaint);

    // Enhanced flag with curve
    final flagPath = Path()
      ..moveTo(noteHeadCenter.dx + size * 0.2, center.dy - size * 0.4)
      ..quadraticBezierTo(
        noteHeadCenter.dx + size * 0.6,
        center.dy - size * 0.3,
        noteHeadCenter.dx + size * 0.4,
        center.dy - size * 0.1,
      );

    canvas.drawPath(flagPath, iconPaint);
  }

  void _drawSparkles(Canvas canvas, Offset center, double radius) {
    final sparklePoints = 5;
    final sparkleRadius = radius * 0.8;
    final sparkleInnerRadius = sparkleRadius * 0.4;
    final sparkleRotation = DateTime.now().millisecondsSinceEpoch / 1000;

    final sparklePaint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    for (var i = 0; i < sparklePoints; i++) {
      final angle = (i * 2 * math.pi / sparklePoints) + sparkleRotation;
      final x = center.dx + sparkleRadius * math.cos(angle);
      final y = center.dy + sparkleRadius * math.sin(angle);
      final innerX = center.dx + sparkleInnerRadius * math.cos(angle + math.pi / sparklePoints);
      final innerY = center.dy + sparkleInnerRadius * math.sin(angle + math.pi / sparklePoints);

      canvas.drawLine(
        Offset(innerX, innerY),
        Offset(x, y),
        sparklePaint,
      );
    }
  }

  Shader _createPremiumGradient(Rect bounds) {
    switch (rarity) {
      case 'legendary':
        return RadialGradient(
          colors: [
            Color(0xFFFFD700), // Gold
            Color(0xFFFFA500), // Orange
            Color(0xFFFFD700), // Gold
          ],
          stops: [0.0, 0.5, 1.0],
          center: Alignment(-0.3, -0.3),
        ).createShader(bounds);
      case 'epic':
        return RadialGradient(
          colors: [
            Color(0xFFE066FF), // Light purple
            Color(0xFF9C27B0), // Dark purple
          ],
          stops: [0.2, 1.0],
          center: Alignment(-0.3, -0.3),
        ).createShader(bounds);
      case 'rare':
        return RadialGradient(
          colors: [
            Color(0xFF00E5FF), // Light blue
            Color(0xFF0288D1), // Dark blue
          ],
          stops: [0.2, 1.0],
          center: Alignment(-0.3, -0.3),
        ).createShader(bounds);
      case 'uncommon':
        return RadialGradient(
          colors: [
            Color(0xFF00E676), // Light green
            Color(0xFF2E7D32), // Dark green
          ],
          stops: [0.2, 1.0],
          center: Alignment(-0.3, -0.3),
        ).createShader(bounds);
      default:
        return RadialGradient(
          colors: [
            Color(0xFF90CAF9), // Light blue
            Color(0xFF1976D2), // Dark blue
          ],
          stops: [0.2, 1.0],
          center: Alignment(-0.3, -0.3),
        ).createShader(bounds);
    }
  }

  double _getGlowRadius() {
    switch (rarity) {
      case 'legendary':
        return 8.0;
      case 'epic':
        return 6.0;
      case 'rare':
        return 4.0;
      case 'uncommon':
        return 3.0;
      default:
        return 0.0;
    }
  }

  @override
  bool shouldRepaint(MapPinPainter oldDelegate) {
    return oldDelegate.pinColor != pinColor || oldDelegate.rarity != rarity;
  }
} 