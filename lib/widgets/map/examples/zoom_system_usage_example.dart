import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';

import '../core/map_zoom_coordinator.dart';
import '../core/zoom_system_integration.dart';
import '../core/map_constants.dart';
import '../../../providers/map_settings_provider.dart';

/// Example widget showing how to integrate the new zoom system
/// with the existing FlutterMapWidget.
///
/// This serves as a reference implementation showing how to replace
/// the scattered zoom management with the consolidated system.
class ZoomSystemExample extends StatefulWidget {
  final Function(Map<String, dynamic>) onPinTap;

  const ZoomSystemExample({
    Key? key,
    required this.onPinTap,
  }) : super(key: key);

  @override
  ZoomSystemExampleState createState() => ZoomSystemExampleState();
}

class ZoomSystemExampleState extends State<ZoomSystemExample> with TickerProviderStateMixin {
  // Core controllers
  final MapController _mapController = MapController();
  
  // New consolidated zoom system
  late MapZoomCoordinator _zoomCoordinator;
  
  // For backward compatibility with existing code
  late ZoomBackwardCompatibility _zoomCompatLayer;
  
  // For the 2.5D effect
  late AnimationController _tiltAnimationController;
  late Animation<double> _tiltAnimation;
  
  // State tracking
  bool _isZooming = false;
  double _currentZoom = MapConstants.defaultZoom;
  LatLngBounds _visibleBounds = MapConstants.defaultBounds;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize tilt animation controller
    _tiltAnimationController = AnimationController(
      duration: MapConstants.tiltAnimationDuration,
      vsync: this,
    );
    
    // Set up initial tilt animation
    _tiltAnimation = Tween<double>(
      begin: 0.0,
      end: MapConstants.defaultTiltAngle,
    ).animate(CurvedAnimation(
      parent: _tiltAnimationController,
      curve: Curves.easeOutCubic,
    ));
    
    // Schedule initialization after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeZoomSystem();
      }
    });
  }
  
  /// Initialize the new zoom system once the widget is built
  void _initializeZoomSystem() {
    // Get the map settings provider
    final mapSettings = Provider.of<MapSettingsProvider>(context, listen: false);
    
    // Initialize the new zoom system with our widget
    _zoomCoordinator = ZoomSystemIntegration.initializeInWidget(
      tickerProvider: this,
      mapController: _mapController,
      initialZoom: _currentZoom,
      setState: setState,
      tiltAnimController: _tiltAnimationController,
      tiltAnimation: _tiltAnimation,
      mapSettings: mapSettings,
    );
    
    // Set up the compatibility layer for existing code
    _zoomCompatLayer = ZoomSystemIntegration.createCompatibilityLayer(_zoomCoordinator);
    
    // Listen for zoom changes to update our state
    _zoomCoordinator.addZoomListener((zoom) {
      setState(() {
        _currentZoom = zoom;
      });
    });
    
    // Listen for zooming state changes
    _zoomCoordinator.addZoomingStateListener((isZooming) {
      setState(() {
        _isZooming = isZooming;
      });
    });
    
    // Start with 3D effect by default
    _tiltAnimationController.forward();
  }
  
  /// Called when map settings change
  void _onMapSettingsChanged() {
    // Update zoom system from settings
    final mapSettings = Provider.of<MapSettingsProvider>(context, listen: false);
    ZoomSystemIntegration.updateFromSettings(_zoomCoordinator, mapSettings);
  }
  
  @override
  void dispose() {
    _tiltAnimationController.dispose();
    _zoomCoordinator.dispose();
    _mapController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // Get the map settings
    final mapSettings = Provider.of<MapSettingsProvider>(context);
    
    return Scaffold(
      body: Stack(
        children: [
          // Main map widget using the tilt animation from our new system
          _buildMapWidget(mapSettings),
          
          // Map controls
          _buildMapControls(),
        ],
      ),
    );
  }
  
  /// Build the main map widget
  Widget _buildMapWidget(MapSettingsProvider mapSettings) {
    return AnimatedBuilder(
      animation: _tiltAnimation,
      builder: (context, child) {
        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.0006) // Perspective component
            ..rotateX(_tiltAnimation.value), // Apply tilt from animation
          child: FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: LatLng(MapConstants.defaultLatitude, MapConstants.defaultLongitude),
              initialZoom: _currentZoom,
              minZoom: MapConstants.minZoom,
              maxZoom: MapConstants.maxZoom,
              onMapEvent: (event) {
                // Our zoom coordinator handles events automatically
                // The events are used to update visible bounds
                if (event is MapEventMoveEnd) {
                  setState(() {
                    _visibleBounds = _mapController.camera.visibleBounds;
                  });
                }
              },
            ),
            children: [
              // Base map layer
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.example.bopmaps',
              ),
              
              // Here you would add other layers specific to your app
              // For example, building layers, markers, etc.
              
              // Example: simplified or detailed layers based on zoom state
              if (_zoomCoordinator.shouldShow3DBuildings() && !_isZooming) {
                // Show detailed 3D buildings when not zooming
                // This would be your 3D buildings layer
              } else {
                // Show simplified buildings during zoom
                // This would be your simplified layer
              }
            ],
          ),
        );
      },
    );
  }
  
  /// Build the map controls
  Widget _buildMapControls() {
    return Positioned(
      right: 16,
      bottom: 100,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Zoom in button
          FloatingActionButton(
            mini: true,
            heroTag: 'zoomIn',
            onPressed: _zoomIn,
            child: const Icon(Icons.add),
          ),
          const SizedBox(height: 8),
          
          // Zoom out button
          FloatingActionButton(
            mini: true,
            heroTag: 'zoomOut',
            onPressed: _zoomOut,
            child: const Icon(Icons.remove),
          ),
          const SizedBox(height: 8),
          
          // Toggle 2D/3D mode
          FloatingActionButton(
            mini: true,
            heroTag: 'toggle3D',
            onPressed: _toggle3DMode,
            child: Icon(_zoomCoordinator.shouldShow3DBuildings() 
                ? Icons.view_in_ar 
                : Icons.map),
          ),
        ],
      ),
    );
  }
  
  // Zoom in with the new system
  void _zoomIn() {
    _zoomCoordinator.zoomIn();
  }
  
  // Zoom out with the new system
  void _zoomOut() {
    _zoomCoordinator.zoomOut();
  }
  
  // Toggle 3D mode with the new system
  void _toggle3DMode() {
    _zoomCoordinator.toggle2D3DMode();
  }
} 