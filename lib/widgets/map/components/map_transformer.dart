import 'package:flutter/material.dart';

// Helper class for handling map transformations (2.5D effects)
class MapTransformer {
  
  // Build the 2.5D perspective transformation matrix
  static Matrix4 build25DMatrix(double tilt) {
    // Start with an identity matrix
    final matrix = Matrix4.identity();
    
    // Apply perspective
    const perspective = 0.001;
    final perspectiveMatrix = Matrix4.identity()
      ..setEntry(3, 2, perspective);
    matrix.multiply(perspectiveMatrix);
    
    // Apply rotation around X axis for tilt effect
    matrix.rotateX(tilt);
    
    return matrix;
  }
  
  // Create a modified container to account for the tilt effect
  static Widget buildTiltedContainer({
    required Widget child,
    required double tiltValue,
    required BoxConstraints constraints,
  }) {
    return Container(
      // Make container larger to accommodate for the tilt effect
      width: constraints.maxWidth * (1 + tiltValue * 0.3),
      height: constraints.maxHeight * (1 + tiltValue * 0.3),
      alignment: Alignment.center,
      child: ClipRRect(
        child: OverflowBox(
          alignment: Alignment.center,
          maxWidth: constraints.maxWidth * (1 + tiltValue * 0.3),
          maxHeight: constraints.maxHeight * (1 + tiltValue * 0.3),
          child: SizedBox(
            width: constraints.maxWidth,
            height: constraints.maxHeight,
            child: child,
          ),
        ),
      ),
    );
  }
} 