import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map_marker_popup/flutter_map_marker_popup.dart';

import '../flutter_map_widget.dart';
import 'custom_marker.dart';

// Popup builder for the map pins
class MapPopupBuilder extends StatelessWidget {
  final Marker marker;
  
  const MapPopupBuilder({
    Key? key,
    required this.marker,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    if (marker is CustomMarker) {
      final customMarker = marker as CustomMarker;
      return _buildPinPopup(context, customMarker.pinData);
    }
    return const SizedBox.shrink();
  }
  
  Widget _buildPinPopup(BuildContext context, Map<String, dynamic> pinData) {
    // Extract pin information
    final String title = pinData['title'] as String? ?? 'Unknown Location';
    final String description = pinData['description'] as String? ?? '';
    
    return Card(
      elevation: 6,
      margin: const EdgeInsets.all(0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 250, minWidth: 150),
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            if (description.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                description,
                style: const TextStyle(fontSize: 14),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () {
                  // Get the FlutterMapWidgetState to access the onPinTap callback
                  final mapWidgetState = context.findAncestorStateOfType<FlutterMapWidgetState>();
                  if (mapWidgetState != null) {
                    mapWidgetState.widget.onPinTap(pinData);
                  }
                },
                child: const Text('View Details'),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 