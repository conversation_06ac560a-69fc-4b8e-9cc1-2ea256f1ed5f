import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';

import '../map_styles.dart';
import '../map_layers/osm_buildings_layer.dart';
import '../map_layers/osm_roads_layer.dart';
import '../map_layers/osm_water_features_layer.dart';
import '../map_layers/parks/osm_parks_layer.dart';
import '../map_layers/pedestrian/osm_pedestrian_layer.dart';
import '../map_layers/recreation/osm_recreation_layer.dart';
import '../map_layers/parking/osm_parking_layer.dart';
import '../map_layers/terrain_layer.dart';
import '../map_layers/buildings_layer.dart';
import '../map_layers/trees_layer.dart';
import '../map_layers/specialized_green/specialized_green_layer.dart';
import '../map_layers/specialized_green/specialized_green_widget.dart';
import '../map_caching/optimized_tile_provider.dart';
import '../../../providers/tile_cache_provider.dart';

// Helper class to build map layers
class MapLayersBuilder {
  
  // Build the base tile layer with optimized caching
  static TileLayer buildBaseTileLayer({
    required String urlTemplate,
    required double tiltAnimationValue,
    required void Function(Object, StackTrace) errorTileCallback,
    required Color backgroundColor,
    required BuildContext context, // Add context parameter for provider access
  }) {
    // Get TileCacheProvider from context
    final tileCacheProvider = Provider.of<TileCacheProvider>(context, listen: false);
    
    // Create tile layer with optimized provider
    final tileLayer = TileLayer(
      urlTemplate: urlTemplate,
      userAgentPackageName: 'com.example.bopmaps',
      backgroundColor: backgroundColor,
      retinaMode: true,
      tileBuilder: (context, child, tile) {
        return Opacity(
          opacity: 0.8 + (0.2 * tiltAnimationValue),
          child: child,
        );
      },
      errorTileCallback: (tile, error, stackTrace) {
        errorTileCallback(error, stackTrace ?? StackTrace.current);
      },
      errorImage: const NetworkImage('https://tile.openstreetmap.org/1/1/1.png'),
    );
    
    // Create an optimized tile provider that uses our cache service
    final optimizedProvider = OptimizedTileProvider(
      urlTemplate: urlTemplate,
      enablePreloading: true,
      tileCacheService: tileCacheProvider.service,
      headers: const {
        'User-Agent': 'BOPMaps Flutter App',
        'Accept': 'image/png,image/*;q=0.9,*/*;q=0.8',
      },
    );
    
    // Use reflection to set the private tileProvider field
    try {
      // Using a workaround to inject our provider
      final layer = TileLayer(
        urlTemplate: urlTemplate,
        userAgentPackageName: 'com.example.bopmaps',
        backgroundColor: backgroundColor,
        retinaMode: true,
        tileProvider: optimizedProvider,
        tileBuilder: (context, child, tile) {
          return Opacity(
            opacity: 0.8 + (0.2 * tiltAnimationValue),
            child: child,
          );
        },
        errorTileCallback: (tile, error, stackTrace) {
          errorTileCallback(error, stackTrace ?? StackTrace.current);
        },
        errorImage: const NetworkImage('https://tile.openstreetmap.org/1/1/1.png'),
      );
      return layer;
    } catch (e) {
      // If reflection fails, fall back to the default implementation
      debugPrint('Failed to inject optimized tile provider: $e');
      return tileLayer;
    }
  }
  
  // Build fallback tile layer with dark overlay and optimized caching
  static TileLayer buildFallbackTileLayer({
    required String urlTemplate,
    required double tiltAnimationValue,
    required void Function(Object, StackTrace) errorTileCallback,
    required Color backgroundColor,
    List<String>? subdomains,
    required BuildContext context,
  }) {
    // Get TileCacheProvider from context
    final tileCacheProvider = Provider.of<TileCacheProvider>(context, listen: false);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Create an optimized tile provider that uses our cache service
    final optimizedProvider = OptimizedTileProvider(
      urlTemplate: urlTemplate,
      enablePreloading: true,
      tileCacheService: tileCacheProvider.service,
      headers: const {
        'User-Agent': 'BOPMaps Flutter App',
        'Accept': 'image/png,image/*;q=0.9,*/*;q=0.8',
      },
    );
    
    // Create tile layer with optimized provider
    final tileLayer = TileLayer(
      urlTemplate: urlTemplate,
      userAgentPackageName: 'com.example.bopmaps',
      backgroundColor: backgroundColor,
      subdomains: subdomains ?? const ['a', 'b', 'c'],
      retinaMode: true,
      // CRITICAL FIX: Optimize tile loading during panning
      tileProvider: optimizedProvider, // Use our custom optimized provider
      // CRITICAL FIX: Keep more tiles in memory to reduce flicker during panning
      keepBuffer: 8, // Increased buffer size
      // CRITICAL FIX: Optimize tile loading
      tileBuilder: (context, child, tile) {
        return Stack(
          children: [
            // CRITICAL FIX: Use RepaintBoundary to optimize rendering during pan
            RepaintBoundary(
              child: Opacity(
                opacity: isDarkMode ? (0.8 + (0.2 * tiltAnimationValue)) : (0.9 + (0.1 * tiltAnimationValue)),
                child: child,
              ),
            ),
            // CRITICAL FIX: Only add overlay when needed
            if (isDarkMode || tiltAnimationValue > 0.1)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(
                    isDarkMode ? 0.4 : (0.2 * tiltAnimationValue),
                  ),
                ),
              ),
          ],
        );
      },
      errorTileCallback: (tile, error, stackTrace) {
        errorTileCallback(error, stackTrace ?? StackTrace.current);
      },
      errorImage: const NetworkImage('https://tile.openstreetmap.org/1/1/1.png'),
    );
    
    // Use reflection to set the private tileProvider field
    try {
      // Using a workaround to inject our provider
      final layer = TileLayer(
        urlTemplate: urlTemplate,
        userAgentPackageName: 'com.example.bopmaps',
        backgroundColor: backgroundColor,
        subdomains: subdomains ?? const ['a', 'b', 'c'],
        retinaMode: true,
        tileProvider: optimizedProvider,
        tileBuilder: (context, child, tile) {
          // Add a dark overlay to maintain consistent visual style
          return Stack(
            children: [
              Opacity(
                opacity: isDarkMode ? (0.8 + (0.2 * tiltAnimationValue)) : (0.9 + (0.1 * tiltAnimationValue)),
                child: child,
              ),
              // Dark overlay to make standard OSM tiles look more like dark mode
              // Reduced opacity from 0.6 to 0.4 to make structures more visible
              if (isDarkMode) Container(
                color: Colors.black.withOpacity(0.4),
              ),
            ],
          );
        },
        errorTileCallback: (tile, error, stackTrace) {
          errorTileCallback(error, stackTrace ?? StackTrace.current);
        },
      );
      return layer;
    } catch (e) {
      // If reflection fails, fall back to the default implementation
      debugPrint('Failed to inject optimized tile provider: $e');
      return tileLayer;
    }
  }
  
  // Build OSM data layers
  static List<Widget> buildOSMDataLayers({
    required double tiltValue,
    required double zoomLevel,
    required bool isMapMoving,
    required LatLngBounds visibleBounds,
    int detailLevel = 3,
    MapCamera? mapCamera,
    bool emergencyPerformanceMode = false,
    bool use3DBuildings = true,
  }) {
    // Make sure this flag is correctly passed from the controller wrapper
    debugPrint('Building OSM layers - Moving: $isMapMoving, Zoom: $zoomLevel, DetailLevel: $detailLevel');
    debugPrint('Bounds: ${visibleBounds.southWest} to ${visibleBounds.northEast}');
    
    // In emergency mode, reduce detail level to improve performance
    if (emergencyPerformanceMode) {
      debugPrint('⚠️ Using reduced detail in emergency performance mode');
      detailLevel = 1; // Use minimal detail level
    }
    
    // Modify enhancedDetail flags based on emergency mode
    bool enhancedDetail = detailLevel >= 2 && !emergencyPerformanceMode;
    
    return [
      // Water features layer - should be rendered first (beneath everything)
      OSMWaterFeaturesLayer(
        tiltFactor: tiltValue,
        zoomLevel: zoomLevel,
        isMapMoving: isMapMoving,
        visibleBounds: visibleBounds,
        waterColor: MapStyles.primaryColor.withOpacity(0.5), // Use primary color for music theme
        mapCamera: mapCamera,
      ),
      
      // Specialized Green Layer - render before buildings for proper layering
      if (!emergencyPerformanceMode)
        SpecializedGreenWidget(
          tiltFactor: tiltValue,
          zoomLevel: zoomLevel,
          visibleBounds: visibleBounds,
          isMapMoving: isMapMoving,
          theme: 'vibrant',
          enhancedDetail: enhancedDetail,
          mapCamera: mapCamera,
        ),
      
      // Parks and vegetation layer - rendered before roads and buildings
      OSMParksLayer(
        tiltFactor: tiltValue,
        zoomLevel: zoomLevel,
        isMapMoving: isMapMoving,
        visibleBounds: visibleBounds,
        theme: 'vibrant',
        season: 'default',
        enhancedDetail: enhancedDetail,
        mapCamera: mapCamera,
      ),
      
      // Real OSM Roads Layer with 2.5D effects
      OSMRoadsLayer(
        tiltFactor: tiltValue,
        zoomLevel: zoomLevel,
        isMapMoving: isMapMoving,
        visibleBounds: visibleBounds,
        detailLevel: detailLevel,
        mapCamera: mapCamera,
      ),

      // Pedestrian Layer - Add after roads but before buildings for proper z-ordering
      OSMPedestrianLayer(
        tiltFactor: tiltValue,
        zoomLevel: zoomLevel,
        isMapMoving: isMapMoving,
        visibleBounds: visibleBounds,
        theme: 'vibrant',
        enhancedDetail: enhancedDetail,
        mapCamera: mapCamera,
      ),
      
      // Parking Layer - Add after pedestrian areas but before buildings
      OSMParkingLayer(
        tiltFactor: tiltValue,
        zoomLevel: zoomLevel,
        isMapMoving: isMapMoving,
        visibleBounds: visibleBounds,
        theme: 'vibrant',
        enhancedDetail: enhancedDetail,
        mapCamera: mapCamera,
      ),
      
      // Real OSM Buildings Layer with 2.5D effects - IMPORTANT: Always render buildings
      // regardless of use3DBuildings setting. The tilt factor will control if they appear 3D or flat
      OSMBuildingsLayer(
        tiltFactor: use3DBuildings ? tiltValue : 0.0, // Use tilt of 0 when in 2D mode
        zoomLevel: zoomLevel,
        isMapMoving: isMapMoving,
        visibleBounds: visibleBounds,
        detailLevel: detailLevel,
        mapCamera: mapCamera,
      ),
      
      // In emergency performance mode, don't render recreation layer at high zoom
      if (!(emergencyPerformanceMode && zoomLevel > 17.5))
        OSMRecreationLayer(
          tiltFactor: tiltValue,
          zoomLevel: zoomLevel,
          isMapMoving: isMapMoving,
          visibleBounds: visibleBounds,
          theme: 'vibrant',
          enhancedDetail: enhancedDetail,
          mapCamera: mapCamera,
        ),
    ];
  }
  
  // Build decorative placeholder layers
  static List<Widget> buildDecorativeLayers({
    required double tiltValue,
    MapCamera? mapCamera,
    bool use3DBuildings = true,
  }) {
    return [
      // Decorative placeholder layers when not using real OSM data
      TerrainLayer(
        tiltFactor: tiltValue,
        mapCamera: mapCamera,
      ),
      TreesLayer(
        tiltFactor: tiltValue,
        mapCamera: mapCamera,
      ),
      // Always show buildings layer, just with different tilt based on use3DBuildings
      BuildingsLayer(
        tiltFactor: use3DBuildings ? tiltValue : 0.0, // Use tilt of 0 when in 2D mode
        mapCamera: mapCamera,
      ),
    ];
  }
} 