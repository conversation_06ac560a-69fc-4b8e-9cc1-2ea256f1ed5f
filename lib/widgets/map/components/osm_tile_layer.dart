import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';

import '../../../constants/map_constants.dart';
import '../../../lib/providers/map_settings_provider.dart';
import '../core/map_controller_wrapper.dart';

/// A highly optimized OSM tile layer with frame-locked rendering
/// to ensure perfect synchronization with the base map.
class OSMTileLayer extends StatefulWidget {
  final String urlTemplate;
  final List<String>? subdomains;
  final double opacity;
  final bool showInBackground;
  final Widget? loadingWidget;

  const OSMTileLayer({
    super.key,
    required this.urlTemplate,
    this.subdomains,
    this.opacity = 1.0,
    this.showInBackground = false,
    this.loadingWidget,
  });

  @override
  State<OSMTileLayer> createState() => _OSMTileLayerState();
}

class _OSMTileLayerState extends State<OSMTileLayer> with SingleTickerProviderStateMixin {
  // Internal tile controller for this layer
  late TileLayer _tileLayer;
  
  // UBER-STYLE OPTIMIZATION: Frame-synchronization variables
  int _lastFrameTime = 0;
  double _frameDelta = 0;
  bool _needsRepaint = false;
  
  // PERFORMANCE OPTIMIZATION: Cached calculations
  final Map<String, Tile> _tileCache = {};
  List<Tile> _visibleTiles = [];
  Rect? _lastViewport;
  
  // Animation controller for smooth transitions
  late AnimationController _opacityController;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller for smooth opacity transitions
    _opacityController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
      value: widget.opacity,
    );
    
    // Initialize the tile layer
    _initTileLayer();
    
    // UBER-STYLE OPTIMIZATION: Start frame callback for synchronized rendering
    SchedulerBinding.instance.addPostFrameCallback(_onPostFrame);
  }
  
  @override
  void didUpdateWidget(OSMTileLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update opacity with animation if changed
    if (oldWidget.opacity != widget.opacity) {
      _opacityController.animateTo(widget.opacity);
    }
    
    // Reinitialize tile layer if URL template or subdomains changed
    if (oldWidget.urlTemplate != widget.urlTemplate ||
        oldWidget.subdomains != widget.subdomains) {
      _initTileLayer();
      _needsRepaint = true;
    }
  }
  
  void _initTileLayer() {
    _tileLayer = TileLayer(
      urlTemplate: widget.urlTemplate,
      subdomains: widget.subdomains,
      tileProvider: NetworkTileProvider(),
      tileSize: 256,
      maxZoom: 19,
      minZoom: 3,
      backgroundColor: Colors.transparent,
      // PERFORMANCE OPTIMIZATION: Keep tile buffer minimal
      keepBuffer: 2,
      // PERFORMANCE OPTIMIZATION: Immediate reset to prevent stale tiles
      reset: () {
        _tileCache.clear();
        _visibleTiles = [];
        _needsRepaint = true;
        setState(() {});
      },
    );
  }
  
  void _onPostFrame(Duration timestamp) {
    // Calculate frame delta for smooth animations
    final currentFrameTime = timestamp.inMilliseconds;
    if (_lastFrameTime > 0) {
      _frameDelta = (currentFrameTime - _lastFrameTime) / 1000;
    }
    _lastFrameTime = currentFrameTime;
    
    // Schedule next frame
    if (mounted) {
      SchedulerBinding.instance.addPostFrameCallback(_onPostFrame);
      
      // Trigger repaint if needed
      if (_needsRepaint) {
        _needsRepaint = false;
        setState(() {});
      }
    }
  }
  
  @override
  void dispose() {
    _opacityController.dispose();
    _tileCache.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MapSettingsProvider>(
      builder: (context, mapSettings, _) {
        // Skip rendering if layer is disabled in settings
        if (!mapSettings.showOSMLayers) {
          return const SizedBox.shrink();
        }
        
        return Consumer<MapControllerWrapper>(
          builder: (context, mapController, _) {
            // UBER-STYLE OPTIMIZATION: Use RepaintBoundary to isolate tile repaints
            return RepaintBoundary(
              child: AnimatedBuilder(
                animation: _opacityController,
                builder: (context, _) {
                  // UBER-STYLE OPTIMIZATION: Use CustomPaint for frame-locked rendering
                  return Opacity(
                    opacity: _opacityController.value,
                    child: widget.showInBackground 
                      ? _buildBackgroundTileLayer(mapController)
                      : _buildForegroundTileLayer(mapController),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
  
  Widget _buildBackgroundTileLayer(MapControllerWrapper mapController) {
    return CustomPaint(
      painter: _OSMTileLayerPainter(
        mapController: mapController,
        tileLayer: _tileLayer,
        tileCache: _tileCache,
        onNeedsRepaint: () {
          _needsRepaint = true;
        },
        isMoving: mapController.isMoving,
        frameDelta: _frameDelta,
        visibleTiles: _visibleTiles,
        onVisibleTilesChanged: (tiles) {
          _visibleTiles = tiles;
        },
        inBackground: true,
      ),
      child: widget.loadingWidget,
    );
  }
  
  Widget _buildForegroundTileLayer(MapControllerWrapper mapController) {
    return CustomPaint(
      painter: _OSMTileLayerPainter(
        mapController: mapController,
        tileLayer: _tileLayer,
        tileCache: _tileCache,
        onNeedsRepaint: () {
          _needsRepaint = true;
        },
        isMoving: mapController.isMoving,
        frameDelta: _frameDelta,
        visibleTiles: _visibleTiles,
        onVisibleTilesChanged: (tiles) {
          _visibleTiles = tiles;
        },
        inBackground: false,
      ),
      child: widget.loadingWidget,
    );
  }
}

/// UBER-STYLE OPTIMIZATION: High-performance tile layer painter
/// that renders tiles directly to the canvas with perfect frame synchronization
class _OSMTileLayerPainter extends CustomPainter {
  final MapControllerWrapper mapController;
  final TileLayer tileLayer;
  final Map<String, Tile> tileCache;
  final VoidCallback onNeedsRepaint;
  final bool isMoving;
  final double frameDelta;
  final List<Tile> visibleTiles;
  final Function(List<Tile>) onVisibleTilesChanged;
  final bool inBackground;
  
  // Viewport calculation caching
  Rect? _calculatedViewport;
  
  _OSMTileLayerPainter({
    required this.mapController,
    required this.tileLayer,
    required this.tileCache,
    required this.onNeedsRepaint,
    required this.isMoving,
    required this.frameDelta,
    required this.visibleTiles,
    required this.onVisibleTilesChanged,
    required this.inBackground,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Skip painting if controller not ready
    if (mapController.controller == null) return;
    
    final camera = mapController.controller!.camera;
    final zoom = camera.zoom;
    final zoomInt = zoom.floor();
    
    // Calculate viewport if needed
    if (_calculatedViewport == null || isMoving) {
      _calculatedViewport = Rect.fromLTWH(0, 0, size.width, size.height);
      
      // UBER-STYLE OPTIMIZATION: Calculate visible tiles
      try {
        final newVisibleTiles = _calculateVisibleTiles(
          camera, 
          _calculatedViewport!, 
          zoomInt
        );
        
        // Only update if different to prevent unnecessary rebuilds
        if (_tilesAreDifferent(visibleTiles, newVisibleTiles)) {
          onVisibleTilesChanged(newVisibleTiles);
        }
      } catch (e) {
        debugPrint('Error calculating visible tiles: $e');
      }
    }
    
    // UBER-STYLE OPTIMIZATION: Use high-performance rendering while moving
    final renderMode = isMoving ? _RenderMode.highPerformance : _RenderMode.highQuality;
    
    // Draw tiles
    _drawVisibleTiles(canvas, camera, renderMode);
  }
  
  void _drawVisibleTiles(Canvas canvas, MapCamera camera, _RenderMode renderMode) {
    final destinationPaint = Paint()
      ..filterQuality = renderMode == _RenderMode.highQuality 
          ? FilterQuality.medium 
          : FilterQuality.low;
    
    // Draw current zoom level tiles first
    for (final tile in visibleTiles) {
      if (tile.image != null) {
        // UBER-STYLE OPTIMIZATION: Calculate position with exact precision
        final tilePos = _getTilePos(tile, camera);
        canvas.drawImageRect(
          tile.image!,
          Rect.fromLTWH(0, 0, tile.image!.width.toDouble(), tile.image!.height.toDouble()),
          tilePos,
          destinationPaint,
        );
      }
    }
  }
  
  Rect _getTilePos(Tile tile, MapCamera camera) {
    // UBER-STYLE OPTIMIZATION: Precise positioning algorithm based on MapLibre's implementation
    final scale = tile.coordsKey.z != camera.zoom.floor()
        ? pow(2, camera.zoom - tile.coordsKey.z).toDouble()
        : 1.0;
    
    final centerPoint = camera.project(camera.center);
    final tileSize = tileLayer.tileSize * scale;
    
    // Calculate tile origin point
    final tilePoint = Point(
      tile.coordsKey.x * tileSize,
      tile.coordsKey.y * tileSize,
    );
    
    // Convert to screen coordinates
    final left = tilePoint.x - centerPoint.x + camera.nonRotatedSize.x / 2;
    final top = tilePoint.y - centerPoint.y + camera.nonRotatedSize.y / 2;
    
    return Rect.fromLTWH(
      left.toDouble(),
      top.toDouble(),
      tileSize.toDouble(),
      tileSize.toDouble(),
    );
  }
  
  List<Tile> _calculateVisibleTiles(MapCamera camera, Rect viewport, int zoom) {
    final tileRange = _calculateTileRange(camera, viewport, zoom);
    final tiles = <Tile>[];
    
    for (var x = tileRange.min.x; x <= tileRange.max.x; x++) {
      for (var y = tileRange.min.y; y <= tileRange.max.y; y++) {
        // Skip tiles outside valid range
        if (y < 0 || y >= (1 << zoom)) continue;
        
        // Normalize x coordinate (wrap around for longitude)
        final wrappedX = x % (1 << zoom);
        
        // Create tile key
        final coords = TileCoords(wrappedX, y, zoom);
        final tileKey = coords.key;
        
        // Use cached tile or create a new one
        if (tileCache.containsKey(tileKey)) {
          tiles.add(tileCache[tileKey]!);
        } else {
          final tile = Tile(
            coordsKey: coords,
            coords: coords,
            tilePos: Point(x, y),
          );
          tileCache[tileKey] = tile;
          tiles.add(tile);
          
          // Load tile image asynchronously
          _loadTileImage(tile);
        }
      }
    }
    
    return tiles;
  }
  
  void _loadTileImage(Tile tile) {
    // Create image provider
    final url = _createTileUrl(tile);
    final provider = NetworkImage(url);
    
    // Load image
    provider.resolve(const ImageConfiguration()).addListener(
      ImageStreamListener((info, _) {
        tile.image = info.image;
        onNeedsRepaint();
      }),
    );
  }
  
  String _createTileUrl(Tile tile) {
    final coords = tile.coords!;
    var url = tileLayer.urlTemplate;
    
    // Replace variables in URL template
    url = url.replaceAll('{z}', coords.z.toString());
    url = url.replaceAll('{x}', coords.x.toString());
    url = url.replaceAll('{y}', coords.y.toString());
    
    // Handle subdomains
    if (tileLayer.subdomains != null && tileLayer.subdomains!.isNotEmpty) {
      final subdomain = tileLayer.subdomains![
        (coords.x + coords.y) % tileLayer.subdomains!.length
      ];
      url = url.replaceAll('{s}', subdomain);
    }
    
    return url;
  }
  
  TileRange _calculateTileRange(MapCamera camera, Rect viewport, int zoom) {
    final centerPoint = camera.project(camera.center);
    final tileSize = tileLayer.tileSize.toDouble();
    
    // Calculate pixel bounds
    final pixelBounds = Rect.fromLTRB(
      centerPoint.x - (viewport.width / 2),
      centerPoint.y - (viewport.height / 2),
      centerPoint.x + (viewport.width / 2),
      centerPoint.y + (viewport.height / 2),
    );
    
    // Convert to tile coordinates
    final min = Point(
      (pixelBounds.left / tileSize).floor(),
      (pixelBounds.top / tileSize).floor(),
    );
    final max = Point(
      (pixelBounds.right / tileSize).ceil(),
      (pixelBounds.bottom / tileSize).ceil(),
    );
    
    // Add some extra buffer for smooth scrolling
    final buffer = isMoving ? 1 : 2;
    
    return TileRange(
      Point(min.x - buffer, min.y - buffer),
      Point(max.x + buffer, max.y + buffer),
    );
  }
  
  bool _tilesAreDifferent(List<Tile> tilesA, List<Tile> tilesB) {
    if (tilesA.length != tilesB.length) return true;
    
    final setA = tilesA.map((t) => t.coordsKey.key).toSet();
    final setB = tilesB.map((t) => t.coordsKey.key).toSet();
    
    return !setEquals(setA, setB);
  }
  
  @override
  bool shouldRepaint(_OSMTileLayerPainter oldDelegate) {
    return isMoving != oldDelegate.isMoving ||
        frameDelta != oldDelegate.frameDelta ||
        visibleTiles.length != oldDelegate.visibleTiles.length;
  }
}

/// UBER-STYLE OPTIMIZATION: Tile coordinates class
class TileCoords {
  final int x;
  final int y;
  final int z;
  
  TileCoords(this.x, this.y, this.z);
  
  String get key => '$z:$x:$y';
}

/// UBER-STYLE OPTIMIZATION: Tile range class
class TileRange {
  final Point<int> min;
  final Point<int> max;
  
  TileRange(this.min, this.max);
}

/// UBER-STYLE OPTIMIZATION: Tile class
class Tile {
  final TileCoords coordsKey;
  final TileCoords? coords;
  final Point<int> tilePos;
  ui.Image? image;
  
  Tile({
    required this.coordsKey,
    required this.coords,
    required this.tilePos,
    this.image,
  });
}

/// UBER-STYLE OPTIMIZATION: Rendering modes
enum _RenderMode {
  highQuality,
  highPerformance,
} 