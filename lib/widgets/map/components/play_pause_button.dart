import 'package:flutter/material.dart';

class PlayPauseButton extends StatelessWidget {
  final bool isPlaying;
  final Function(bool) onPlayPauseToggle;
  final Color backgroundColor;
  final Color foregroundColor;
  final double elevation;
  final double size;
  final String? heroTag;
  final String? albumArtUrl;

  const PlayPauseButton({
    Key? key,
    required this.isPlaying,
    required this.onPlayPauseToggle,
    this.backgroundColor = Colors.white,
    this.foregroundColor = Colors.black,
    this.elevation = 2,
    this.size = 24,
    this.heroTag,
    this.albumArtUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: FloatingActionButton(
        heroTag: heroTag,
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        elevation: elevation,
        onPressed: () => onPlayPauseToggle(!isPlaying),
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (albumArtUrl != null && albumArtUrl!.isNotEmpty)
              ClipOval(
                child: Image.network(
                  albumArtUrl ?? 'https://via.placeholder.com/56',
                  width: 56,
                  height: 56,
                  fit: BoxFit.cover,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                      child: Center(
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded / 
                                  loadingProgress.expectedTotalBytes!
                              : null,
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) => Container(
                    color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                    child: Icon(
                      Icons.music_note,
                      color: isDarkMode ? Colors.grey[600] : Colors.grey[500],
                      size: 24,
                    ),
                  ),
                ),
              ),
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: backgroundColor.withOpacity(albumArtUrl != null ? 0.7 : 1.0),
              ),
              child: Icon(
                isPlaying ? Icons.pause : Icons.play_arrow,
                size: size,
              ),
            ),
          ],
        ),
      ),
    );
  }
} 