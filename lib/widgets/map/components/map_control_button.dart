import 'package:flutter/material.dart';

// Map control button widget
class MapControlButton extends StatelessWidget {
  final IconData icon;
  final String tooltip;
  final VoidCallback onPressed;
  final bool isActive;
  final bool isTransparent;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? iconSize;
  
  const MapControlButton({
    Key? key,
    required this.icon,
    required this.tooltip,
    required this.onPressed,
    this.isActive = true,
    this.isTransparent = false,
    this.backgroundColor,
    this.foregroundColor,
    this.iconSize,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Use provided colors or fall back to theme colors
    final effectiveBackgroundColor = backgroundColor ?? theme.colorScheme.surface;
    final effectiveForegroundColor = foregroundColor ?? theme.colorScheme.primary;
    
    return Container(
      decoration: isTransparent ? null : BoxDecoration(
        shape: BoxShape.circle,
        color: effectiveBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: Tooltip(
          message: tooltip,
          child: InkWell(
            onTap: onPressed,
            customBorder: const CircleBorder(),
            child: Container(
              width: 40,
              height: 40,
              alignment: Alignment.center,
              child: Icon(
                icon,
                size: iconSize ?? (isTransparent ? 24 : 20),
                color: effectiveForegroundColor,
              ),
            ),
          ),
        ),
      ),
    );
  }
} 