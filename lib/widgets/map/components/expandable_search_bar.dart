import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ExpandableSearchBar extends StatefulWidget {
  final Function(String) onSearch;
  final String hintText;
  final double iconSize;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool autofocus;
  final VoidCallback? onExpand;
  final VoidCallback? onCollapse;
  
  const ExpandableSearchBar({
    Key? key,
    required this.onSearch,
    this.hintText = 'Search profiles...',
    this.iconSize = 24,
    this.backgroundColor,
    this.foregroundColor,
    this.autofocus = false,
    this.onExpand,
    this.onCollapse,
  }) : super(key: key);

  @override
  State<ExpandableSearchBar> createState() => _ExpandableSearchBarState();
}

class _ExpandableSearchBarState extends State<ExpandableSearchBar> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isExpanded = false;
  bool _isHovering = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 350),
    );
    
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutQuint,
      reverseCurve: Curves.easeInQuint,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.97,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.8, curve: Curves.easeOutCirc),
    ));
    
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus && _textController.text.isEmpty) {
        _collapseSearchBar();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor = widget.backgroundColor ?? theme.colorScheme.surface;
    final effectiveForegroundColor = widget.foregroundColor ?? theme.colorScheme.onSurface;
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 400;
    
    // More conservative max width
    final maxWidth = isSmallScreen 
        ? size.width * 0.65
        : size.width < 600 
            ? size.width * 0.5
            : size.width * 0.35;
    
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: Focus(
        onKeyEvent: (FocusNode node, KeyEvent event) {
          if (event is KeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.escape) {
              if (_isExpanded) {
                _collapseSearchBar();
                return KeyEventResult.handled;
              }
            } else if (event.logicalKey == LogicalKeyboardKey.keyF && 
                      (HardwareKeyboard.instance.isControlPressed || 
                       HardwareKeyboard.instance.isMetaPressed)) {
              _expandSearchBar();
              return KeyEventResult.handled;
            }
          }
          return KeyEventResult.ignored;
        },
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            final currentWidth = _animation.value * maxWidth;
            
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: _isExpanded 
                      ? effectiveBackgroundColor.withOpacity(0.15)
                      : (_isHovering 
                          ? effectiveBackgroundColor.withOpacity(0.08) 
                          : Colors.transparent),
                  border: Border.all(
                    color: _isExpanded
                        ? Colors.white.withOpacity(0.12)
                        : Colors.transparent,
                    width: 0.5,
                  ),
                  boxShadow: _isExpanded ? [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      spreadRadius: -5,
                      offset: const Offset(0, 5),
                    ),
                  ] : null,
                ),
                width: _isExpanded ? currentWidth + 40 : 40,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: _isExpanded ? 30 : 20,
                      sigmaY: _isExpanded ? 30 : 20,
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Search Icon Button
                        Positioned(
                          left: 0,
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _isExpanded ? _collapseSearchBar : _expandSearchBar,
                              customBorder: const CircleBorder(),
                              child: Container(
                                width: 40,
                                height: 40,
                                alignment: Alignment.center,
                                child: AnimatedRotation(
                                  duration: const Duration(milliseconds: 350),
                                  turns: _isExpanded ? 0.125 : 0,
                                  child: Icon(
                                    _isExpanded ? Icons.arrow_back_ios_new : Icons.search,
                                    color: effectiveForegroundColor,
                                    size: widget.iconSize,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        
                        // Search TextField
                        if (_isExpanded)
                          Positioned.fill(
                            left: 40,
                            right: 40,
                            child: FadeTransition(
                              opacity: _fadeAnimation,
                              child: TextField(
                                controller: _textController,
                                focusNode: _focusNode,
                                cursorColor: effectiveForegroundColor,
                                cursorWidth: 1,
                                cursorRadius: const Radius.circular(1),
                                style: TextStyle(
                                  color: effectiveForegroundColor,
                                  fontSize: 15,
                                  height: 1.2,
                                  fontWeight: FontWeight.w400,
                                  letterSpacing: 0.15,
                                ),
                                decoration: InputDecoration(
                                  hintText: widget.hintText,
                                  hintStyle: TextStyle(
                                    color: effectiveForegroundColor.withOpacity(0.5),
                                    fontSize: 15,
                                    height: 1.2,
                                    fontWeight: FontWeight.w400,
                                    letterSpacing: 0.15,
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 10,
                                  ),
                                  isDense: true,
                                ),
                                onChanged: widget.onSearch,
                                textInputAction: TextInputAction.search,
                                onSubmitted: (value) {
                                  if (value.isEmpty) {
                                    _collapseSearchBar();
                                  } else {
                                    widget.onSearch(value);
                                  }
                                },
                              ),
                            ),
                          ),
                        
                        // Right side actions
                        if (_isExpanded)
                          Positioned(
                            right: 0,
                            child: FadeTransition(
                              opacity: _fadeAnimation,
                              child: Row(
                                children: [
                                  if (_textController.text.isNotEmpty)
                                    Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        onTap: _clearSearch,
                                        customBorder: const CircleBorder(),
                                        child: Container(
                                          width: 40,
                                          height: 40,
                                          alignment: Alignment.center,
                                          child: Icon(
                                            Icons.close,
                                            color: effectiveForegroundColor.withOpacity(0.6),
                                            size: 18,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  void _expandSearchBar() {
    if (!_isExpanded) {
      HapticFeedback.lightImpact();
      setState(() => _isExpanded = true);
      _animationController.forward();
      _focusNode.requestFocus();
      if (widget.onExpand != null) widget.onExpand!();
    }
  }

  void _collapseSearchBar() {
    if (_isExpanded) {
      setState(() => _isExpanded = false);
      _animationController.reverse();
      _focusNode.unfocus();
      if (widget.onCollapse != null) widget.onCollapse!();
    }
  }

  void _clearSearch() {
    HapticFeedback.lightImpact();
    _textController.clear();
    widget.onSearch('');
    _focusNode.requestFocus();
  }
} 