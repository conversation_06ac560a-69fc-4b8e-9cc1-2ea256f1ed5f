import 'package:flutter/material.dart';

import 'map_control_button.dart';

// Widget that contains the map control buttons
class MapControls extends StatelessWidget {
  final VoidCallback onTiltToggle;
  final VoidCallback onZoomIn;
  final VoidCallback onZoomOut;
  final VoidCallback onLocationButtonPressed;
  final bool isLocationTracking;
  final double currentZoom;
  
  const MapControls({
    Key? key,
    required this.onTiltToggle,
    required this.onZoomIn,
    required this.onZoomOut,
    required this.onLocationButtonPressed,
    required this.isLocationTracking,
    required this.currentZoom,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Tilt control
        MapControlButton(
          icon: Icons.layers,
          tooltip: '3D Toggle',
          onPressed: onTiltToggle,
        ),
        const SizedBox(height: 8),
        
        // Zoom in
        MapControlButton(
          icon: Icons.add,
          tooltip: 'Zoom In',
          onPressed: onZoomIn,
        ),
        const SizedBox(height: 8),
        
        // Zoom out
        MapControlButton(
          icon: Icons.remove,
          tooltip: 'Zoom Out',
          onPressed: onZoomOut,
        ),
        const SizedBox(height: 8),
        
        // Toggle location tracking
        MapControlButton(
          icon: isLocationTracking
            ? Icons.my_location
            : Icons.location_searching,
          tooltip: isLocationTracking
            ? 'Tracking On'
            : 'Locate Me',
          onPressed: onLocationButtonPressed,
        ),
      ],
    );
  }
} 