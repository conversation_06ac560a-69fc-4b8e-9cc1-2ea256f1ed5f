import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../providers/spotify_provider.dart';
import 'map_control_button.dart';

class TrackNavigationButtons extends StatelessWidget {
  const TrackNavigationButtons({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        MapControlButton(
          icon: Icons.skip_previous_rounded,
          tooltip: 'Previous Track',
          onPressed: () async {
            HapticFeedback.mediumImpact();
            await spotifyProvider.skipPrevious();
          },
          backgroundColor: theme.colorScheme.surface,
          foregroundColor: theme.colorScheme.primary,
          iconSize: 28,
        ),
        const SizedBox(width: 8),
        MapControlButton(
          icon: Icons.skip_next_rounded,
          tooltip: 'Next Track',
          onPressed: () async {
            HapticFeedback.mediumImpact();
            await spotifyProvider.skipNext();
          },
          backgroundColor: theme.colorScheme.surface,
          foregroundColor: theme.colorScheme.primary,
          iconSize: 28,
        ),
      ],
    );
  }
} 