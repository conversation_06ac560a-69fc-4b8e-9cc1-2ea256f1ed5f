import 'package:flutter/material.dart';
import '../../../config/themes.dart';

class MapNoteBar extends StatelessWidget {
  final String note;
  final VoidCallback? onTap;
  // Maximum characters allowed to ensure visibility
  static const int maxCharacters = 100;

  const MapNoteBar({
    Key? key,
    required this.note,
    this.onTap,
  }) : super(key: key);

  String get _truncatedNote {
    // Remove any existing quotes to prevent duplicates
    final cleanNote = note.trim().replaceAll('"', '');
    
    if (cleanNote.length <= maxCharacters - 2) { // Account for quotes
      return '"$cleanNote';  // Only add opening quote, closing quote added separately
    }
    return '"${cleanNote.substring(0, maxCharacters - 6)}...'; // Only add opening quote
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDarkMode 
      ? Colors.black.withOpacity(0.8)
      : Colors.white.withOpacity(0.9);
    final textColor = isDarkMode ? Colors.white : Colors.black;

    // Calculate exact width based on music controls:
    // 3 buttons at 40px each + 2 spacing gaps at 8px each = 136px total
    final barWidth = (40.0 * 3) + (8.0 * 2); // 136px total

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: barWidth,
        constraints: const BoxConstraints(
          minHeight: 64,
          maxHeight: 64,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8), // Reduced padding
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
              spreadRadius: 1,
            ),
          ],
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Center(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: barWidth - 16, // Account for horizontal padding
                  maxHeight: constraints.maxHeight - 16, // Account for vertical padding
                ),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: barWidth - 16,
                    ),
                    child: RichText(
                      textAlign: TextAlign.center,
                      maxLines: 3,
                      text: TextSpan(
                        style: TextStyle(
                          color: textColor,
                          fontSize: 11, // Smaller default font size
                          fontWeight: FontWeight.w500,
                          height: 1.1, // Tighter line height
                        ),
                        children: [
                          TextSpan(text: _truncatedNote),
                          TextSpan(
                            text: '"',
                            style: TextStyle(
                              color: textColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
} 