import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../models/music_track.dart';
import '../../../providers/spotify_provider.dart';
import '../../../providers/apple_music_provider.dart';
import '../../../providers/now_playing_provider.dart';
import '../../../config/themes.dart';

class MapNowPlayingBar extends StatelessWidget {
  final VoidCallback? onTap;

  const MapNowPlayingBar({
    Key? key,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context);
    final nowPlayingProvider = Provider.of<NowPlayingProvider>(context);
    final mediaQuery = MediaQuery.of(context);
    
    // Get track from either service, prioritizing the one that's actually playing
    final spotifyTrack = spotifyProvider.currentTrack;
    final appleMusicTrack = appleMusicProvider.currentTrack;
    final spotifyIsPlaying = spotifyProvider.isPlaying;
    final appleMusicIsPlaying = appleMusicProvider.isPlaying;
    
    // Prioritize the track from the service that's actually playing
    final track = (appleMusicIsPlaying && appleMusicTrack != null) 
        ? appleMusicTrack 
        : (spotifyIsPlaying && spotifyTrack != null) 
            ? spotifyTrack 
            : (appleMusicTrack ?? spotifyTrack); // Fall back to any available track
    
    final hasActivePlayback = spotifyProvider.hasActivePlayback || appleMusicProvider.hasActivePlayback;
    
    // Only show if we have active playback
    if (!hasActivePlayback || track == null) {
      return const SizedBox.shrink();
    }

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDarkMode 
      ? Colors.black.withOpacity(0.8)
      : Colors.white.withOpacity(0.9);
    final textColor = isDarkMode ? Colors.white : Colors.black;

    // Calculate responsive width based on screen size
    final screenWidth = mediaQuery.size.width;
    final barWidth = screenWidth < 360 ? 180.0 : 220.0;

    return GestureDetector(
      onTap: () {
        if (hasActivePlayback) {
          nowPlayingProvider.toggleExpanded();
          onTap?.call();
        }
      },
      child: Container(
        width: barWidth,
        constraints: const BoxConstraints(
          minHeight: 72,
          maxHeight: 80,
        ),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
              spreadRadius: 1,
            ),
          ],
        ),
        child: Row(
          children: [
            // Album Art
            AspectRatio(
              aspectRatio: 1,
              child: ClipRRect(
                borderRadius: const BorderRadius.horizontal(
                  left: Radius.circular(16),
                ),
                child: Builder(
                  builder: (context) {
                    // Convert Spotify URI to HTTP URL if needed
                    String imageUrl = track.albumArt ?? track.albumArtUrl ?? 'https://via.placeholder.com/64';
                    if (imageUrl.startsWith('spotify:image:')) {
                      final imageId = imageUrl.split(':').last;
                      imageUrl = 'https://i.scdn.co/image/$imageId';
                    }

                    return Image.network(
                      imageUrl,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                          child: Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded / 
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                            ),
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        print('Error loading album art: $error');
                        return Container(
                          color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                          child: Icon(
                            Icons.music_note,
                            color: isDarkMode ? Colors.grey[600] : Colors.grey[500],
                            size: 32,
                          ),
                        );
                      },
                    );
                  }
                ),
              ),
            ),
            
            // Track Info
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Track Title
                    Text(
                      track.title,
                      style: TextStyle(
                        color: textColor,
                        fontSize: screenWidth < 360 ? 12 : 13,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    // Artist
                    Text(
                      track.artist,
                      style: TextStyle(
                        color: textColor.withOpacity(0.7),
                        fontSize: screenWidth < 360 ? 10 : 11,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 