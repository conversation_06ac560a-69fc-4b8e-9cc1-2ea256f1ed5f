import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../config/themes.dart';
import '../../../services/api/pin_engagement_service.dart';
import '../../../services/api_service.dart';
import '../../../services/auth_service.dart';
import '../../../providers/gamification_provider.dart';

/// MapTrackDetailsBar with ultra-fast API integration
/// 
/// ⚡ PERFORMANCE OPTIMIZED:
/// - Uses /api/pins/{id}/vote_info/ for lightning-fast vote data loading
/// - Single API call instead of multiple requests
/// - Real-time vote updates with haptic feedback
/// - Graceful fallback to initial values on API failure
/// - Profile picture loaded from API response (pin_info.owner.profile_pic)
/// - Always shows most up-to-date user data from backend
/// 
/// 🔄 DYNAMIC PIN SWITCHING:
/// - Automatically reloads vote data when pinId changes
/// - <PERSON>les rapid pin switching with proper state management
/// - Shows loading indicators during pin transitions
/// - No stale data between different pins
class MapTrackDetailsBar extends StatefulWidget {
  final int pinId;  // Required: the pin ID for API calls
  final String username;
  final String? userImage;
  final int initialUpvotes;
  final int initialDownvotes;
  final VoidCallback? onComment;
  final VoidCallback? onTap;
  final bool isCompact;
  final bool transparent; // New parameter for glassmorphic usage

  const MapTrackDetailsBar({
    super.key,
    required this.pinId,  // NEW: Required pin ID
    required this.username,
    this.userImage,
    this.initialUpvotes = 0,
    this.initialDownvotes = 0,
    this.onComment,
    this.onTap,
    this.isCompact = false,
    this.transparent = false, // Defaults to false for backwards compatibility
  });

  @override
  State<MapTrackDetailsBar> createState() => _MapTrackDetailsBarState();
}

class _MapTrackDetailsBarState extends State<MapTrackDetailsBar> {
  late int _upvotes;
  late int _downvotes;
  bool? _isUpvoted;  // null = no vote, true = upvoted, false = downvoted
  bool _isLoading = false;
  PinEngagementService? _engagementService;
  
  // ⚡ NEW: Store profile picture from API response
  String? _userProfilePic;
  String? _username;

  @override
  void initState() {
    super.initState();
    _upvotes = widget.initialUpvotes;
    _downvotes = widget.initialDownvotes;
    // Initialize with constructor values as fallback
    _userProfilePic = widget.userImage;
    _username = widget.username;
    _initializeServices();
    _loadVoteData();
  }

  @override
  void didUpdateWidget(MapTrackDetailsBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // ⚡ CRITICAL: Reload data when switching to a different pin!
    if (oldWidget.pinId != widget.pinId) {
      print('🔄 MapTrackDetailsBar: Pin changed from ${oldWidget.pinId} to ${widget.pinId} - reloading vote data');
      
      // Reset to new widget's initial values and show loading
      setState(() {
        _upvotes = widget.initialUpvotes;
        _downvotes = widget.initialDownvotes;
        _userProfilePic = widget.userImage;
        _username = widget.username;
        _isUpvoted = null;  // Reset vote state
        _isLoading = true;  // Show loading state during pin switch
      });
      
      // Load vote data for the new pin
      _loadVoteData();
    }
  }

  void _initializeServices() {
    final apiService = context.read<ApiService>();
    final authService = context.read<AuthService>();
    _engagementService = PinEngagementService(apiService, authService);
  }

  /// Load initial vote data from API using ultra-fast endpoint
  Future<void> _loadVoteData() async {
    if (_engagementService == null) return;
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // ⚡ Use ultra-fast vote info endpoint for maximum performance
      final voteStats = await _engagementService!.getQuickVoteInfo(widget.pinId);
      
      stopwatch.stop();
      print('⚡ MapTrackDetailsBar: Vote data loaded in ${stopwatch.elapsedMilliseconds}ms (ultra-fast endpoint)');
      
      if (mounted) {
        setState(() {
          _upvotes = voteStats.upvotes;
          _downvotes = voteStats.downvotes;
          _isUpvoted = voteStats.isUpvoted ? true : 
                      voteStats.isDownvoted ? false : null;
          
          // ⚡ USE PROFILE PICTURE FROM API RESPONSE!
          _userProfilePic = voteStats.pinInfo?['owner']?['profile_pic'] ?? widget.userImage;
          _username = voteStats.ownerUsername ?? widget.username;
          
          // ⚡ CLEAR LOADING STATE - important for pin switching
          _isLoading = false;
          
          if (voteStats.pinInfo?['owner']?['profile_pic'] != null) {
            print('⚡ MapTrackDetailsBar: Updated profile picture from API response');
          }
        });
      }
    } catch (e) {
      stopwatch.stop();
      if (mounted) {
        setState(() {
          // ⚡ CLEAR LOADING STATE even on error - prevents stuck loading indicators
          _isLoading = false;
        });
        print('⚠️ Failed to load vote data after ${stopwatch.elapsedMilliseconds}ms: $e');
        // Keep using the initial values passed to the widget
      }
    }
  }

  /// Handle upvote button press with ultra-fast API call
  Future<void> _handleUpvote() async {
    if (_isLoading || _engagementService == null) return;
    
    // Provide haptic feedback
    HapticFeedback.selectionClick();
    
    final stopwatch = Stopwatch()..start();
    
    setState(() {
      _isLoading = true;
    });

    try {
      // ⚡ SIMPLIFIED: The vote_post endpoint handles toggle logic automatically!
      // If user already upvoted, sending upvote again will remove it
      // If user downvoted, sending upvote will switch to upvote
      // If user hasn't voted, sending upvote will create upvote
      final newStats = await _engagementService!.upvotePin(widget.pinId);
      
      stopwatch.stop();
      print('⚡ MapTrackDetailsBar: Upvote completed in ${stopwatch.elapsedMilliseconds}ms (ultra-fast atomic)');
      
      if (mounted) {
        setState(() {
          _upvotes = newStats.upvotes;
          _downvotes = newStats.downvotes;
          _isUpvoted = newStats.isUpvoted ? true : 
                      newStats.isDownvoted ? false : null;
          _isLoading = false;
          
          // ⚡ UPDATE PROFILE PICTURE FROM VOTE RESPONSE (keeps data fresh)
          if (newStats.pinInfo?['owner']?['profile_pic'] != null) {
            _userProfilePic = newStats.pinInfo!['owner']['profile_pic'];
          }
          if (newStats.ownerUsername != null) {
            _username = newStats.ownerUsername;
        }
        });
        
        // 🎯 TRACK GAMIFICATION: Vote given for social challenges
        try {
          final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
          await gamificationProvider.handleVoteGiven(widget.pinId, 1); // 1 = upvote
          print('🎯 Gamification: Upvote tracked successfully');
        } catch (e) {
          print('⚠️ Gamification tracking failed for upvote: $e');
        }
      }
    } catch (e) {
      stopwatch.stop();
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        print('❌ Upvote failed after ${stopwatch.elapsedMilliseconds}ms: $e');
        
        // Show error feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to vote: $e'),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Handle downvote button press with ultra-fast API call
  Future<void> _handleDownvote() async {
    if (_isLoading || _engagementService == null) return;
    
    // Provide haptic feedback
    HapticFeedback.selectionClick();
    
    final stopwatch = Stopwatch()..start();
    
    setState(() {
      _isLoading = true;
    });

    try {
      // ⚡ SIMPLIFIED: The vote_post endpoint handles toggle logic automatically!
      // If user already downvoted, sending downvote again will remove it
      // If user upvoted, sending downvote will switch to downvote
      // If user hasn't voted, sending downvote will create downvote
      final newStats = await _engagementService!.downvotePin(widget.pinId);
      
      stopwatch.stop();
      print('⚡ MapTrackDetailsBar: Downvote completed in ${stopwatch.elapsedMilliseconds}ms (ultra-fast atomic)');
      
      if (mounted) {
        setState(() {
          _upvotes = newStats.upvotes;
          _downvotes = newStats.downvotes;
          _isUpvoted = newStats.isUpvoted ? true : 
                      newStats.isDownvoted ? false : null;
          _isLoading = false;
          
          // ⚡ UPDATE PROFILE PICTURE FROM VOTE RESPONSE (keeps data fresh)
          if (newStats.pinInfo?['owner']?['profile_pic'] != null) {
            _userProfilePic = newStats.pinInfo!['owner']['profile_pic'];
          }
          if (newStats.ownerUsername != null) {
            _username = newStats.ownerUsername;
          }
        });
        
        // 🎯 TRACK GAMIFICATION: Vote given for social challenges
        try {
          final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
          await gamificationProvider.handleVoteGiven(widget.pinId, -1); // -1 = downvote
          print('🎯 Gamification: Downvote tracked successfully');
        } catch (e) {
          print('⚠️ Gamification tracking failed for downvote: $e');
        }
      }
    } catch (e) {
      stopwatch.stop();
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        print('❌ Downvote failed after ${stopwatch.elapsedMilliseconds}ms: $e');
        
        // Show error feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to vote: $e'),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final backgroundColor = widget.transparent 
        ? Colors.transparent 
        : theme.colorScheme.surface.withOpacity(0.9);
    final textColor = widget.transparent 
        ? Colors.white.withOpacity(0.9) 
        : theme.colorScheme.onSurface;

    // Calculate responsive width based on screen size
    final screenWidth = MediaQuery.of(context).size.width;
    final barWidth = screenWidth < 360 ? 160.0 : 180.0; // Smaller for compact design

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: barWidth,
        height: 56, // Smaller height for compact design
        decoration: widget.transparent ? null : BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
              spreadRadius: 1,
            ),
          ],
        ),
        child: Row(
          children: [
            // User Avatar
            Padding(
              padding: EdgeInsets.all(widget.transparent ? 6.0 : 8.0),
              child: _buildUserAvatar(theme),
            ),
            
            // Username and Vote Controls
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(right: 6),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Username
                    Text(
                      _username ?? widget.username,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: textColor,
                        fontWeight: FontWeight.w600,
                        fontSize: widget.transparent ? 13 : null,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: widget.transparent ? 3 : 4),
                    // Vote Controls
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Upvote Button
                        _buildVoteButton(
                          context: context,
                          icon: Icons.arrow_upward_rounded,
                          count: _upvotes,
                          isSelected: _isUpvoted == true,
                          onPressed: _handleUpvote,
                          color: Colors.green,
                          theme: theme,
                        ),
                        SizedBox(width: widget.transparent ? 6 : 8),
                        // Downvote Button
                        _buildVoteButton(
                          context: context,
                          icon: Icons.arrow_downward_rounded,
                          count: _downvotes,
                          isSelected: _isUpvoted == false,
                          onPressed: _handleDownvote,
                          color: Colors.red,
                          theme: theme,
                        ),
                        SizedBox(width: widget.transparent ? 6 : 8),
                        // Comment Button
                        _buildCommentButton(
                          context: context,
                          theme: theme,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserAvatar(ThemeData theme) {
    final avatarSize = widget.transparent ? 32.0 : 40.0;
    final iconSize = widget.transparent ? 20.0 : 24.0;
    
    return Container(
      width: avatarSize,
      height: avatarSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: widget.transparent 
            ? Colors.white.withOpacity(0.2)
            : theme.colorScheme.surfaceVariant,
        border: widget.transparent ? Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ) : null,
      ),
      child: ClipOval(
        child: (_userProfilePic ?? widget.userImage) != null
            ? Image.network(
                _userProfilePic ?? widget.userImage!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Icon(
                  Icons.person,
                  color: widget.transparent 
                      ? Colors.white.withOpacity(0.8)
                      : theme.colorScheme.onSurfaceVariant,
                  size: iconSize,
                ),
              )
            : Icon(
                Icons.person,
                color: widget.transparent 
                    ? Colors.white.withOpacity(0.8)
                    : theme.colorScheme.onSurfaceVariant,
                size: iconSize,
              ),
      ),
    );
  }

  Widget _buildVoteButton({
    required BuildContext context,
    required IconData icon,
    required int count,
    required bool isSelected,
    required Future<void> Function() onPressed,  // Changed to async function
    required Color color,
    required ThemeData theme,
  }) {
    final buttonColor = widget.transparent
        ? (isSelected ? color : Colors.white.withOpacity(0.8))
        : (isSelected ? color : theme.colorScheme.onSurface.withOpacity(0.7));
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _isLoading ? null : () async {
          await onPressed();
        },
        borderRadius: BorderRadius.circular(10),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
          decoration: BoxDecoration(
            color: widget.transparent
                ? (isSelected ? color.withOpacity(0.25) : Colors.white.withOpacity(0.15))
                : (isSelected ? color.withOpacity(0.15) : buttonColor.withOpacity(0.1)),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: widget.transparent
                  ? Colors.white.withOpacity(0.3)
                  : buttonColor.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _isLoading
                  ? SizedBox(
                      width: 14,
                      height: 14,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(buttonColor),
                      ),
                    )
                  : Icon(
                icon,
                size: 14,
                color: buttonColor,
              ),
              const SizedBox(width: 3),
              Text(
                count.toString(),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: buttonColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentButton({
    required BuildContext context,
    required ThemeData theme,
  }) {
    final color = widget.transparent 
        ? Colors.white.withOpacity(0.8)
        : theme.colorScheme.primary;
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: widget.onComment,
        borderRadius: BorderRadius.circular(10),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
          decoration: BoxDecoration(
            color: widget.transparent
                ? Colors.white.withOpacity(0.15)
                : color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: widget.transparent
                  ? Colors.white.withOpacity(0.3)
                  : color.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Icon(
            Icons.chat_bubble_outline_rounded,
            size: 14,
            color: color,
          ),
        ),
      ),
    );
  }
} 