import 'package:flutter/material.dart';

// This is a widget for handling map loading errors and reloading
class MapErrorHandler extends StatelessWidget {
  final bool mapLoadFailed;
  final bool hasNetworkError;
  final bool initialLoadAttempted;
  final String? errorMessage;
  final VoidCallback onRetry;

  const MapErrorHandler({
    Key? key,
    required this.mapLoadFailed,
    required this.hasNetworkError,
    required this.initialLoadAttempted,
    this.errorMessage,
    required this.onRetry,
  }) : super(key: key);
  
  // Static method to handle tile errors
  static void handleTileError(BuildContext context, Object error, StackTrace stackTrace) {
    debugPrint('Tile Error: $error\n$stackTrace');
  }

  @override
  Widget build(BuildContext context) {
    // Return empty container to hide loading indicators
    if (!initialLoadAttempted) {
      return Container();
    }

    if (mapLoadFailed) {
      return Container(
        color: Colors.black87,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.map_outlined,
                color: Colors.white70,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(
                errorMessage ?? 'Unable to load map',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: Colors.blue[800],
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // No errors, return an empty container
    return Container();
  }
} 