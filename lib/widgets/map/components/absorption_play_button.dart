import 'package:flutter/material.dart';

class AbsorptionPlayButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isAbsorbing;
  final bool isPaused;

  const AbsorptionPlayButton({
    Key? key,
    required this.onPressed,
    required this.isAbsorbing,
    this.isPaused = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: onPressed,
      backgroundColor: Theme.of(context).primaryColor,
      child: _buildButtonContent(),
    );
  }

  Widget _buildButtonContent() {
    if (isAbsorbing) {
      if (isPaused) {
        return const Icon(Icons.play_arrow);
      }
      return const CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
      );
    }
    return const Icon(Icons.play_arrow);
  }
} 