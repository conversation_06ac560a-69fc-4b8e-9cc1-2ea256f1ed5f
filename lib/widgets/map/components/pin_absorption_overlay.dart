import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:latlong2/latlong.dart';
import '../../../config/themes.dart';

/**
 * ABSORPTION ANIMATION OVERLAY
 * 
 * A full-screen overlay that displays particle animations when absorbing music pins.
 * The animation shows particles flowing from pin locations to the user's position.
 * 
 * VISUAL EFFECTS:
 * 1. Particles
 *    - Size: 24.0 pixels base size
 *    - Count: 12 particles per pin
 *    - Glow effect with 15px blur
 *    - Trails with 6px width
 *    - Color-coded by pin rarity
 * 
 * 2. Animation
 *    - Duration: 1500ms
 *    - Staggered starts (0.3s max delay)
 *    - Support for pause/resume
 *    - Haptic feedback during absorption
 * 
 * 3. Effects
 *    - Particle trails with glow
 *    - Semi-transparent background overlay
 *    - Support for map tilt perspective
 *    - Smooth particle fading
 * 
 * FEATURES:
 * - Handles multiple pins simultaneously
 * - Supports 2.5D perspective with map tilt
 * - Provides haptic feedback
 * - Manages animation state
 * - Cleanup on completion
 * 
 * USAGE:
 * ```dart
 * PinAbsorptionOverlay(
 *   userLocation: userLatLng,
 *   pins: nearbyPins,
 *   radius: 1000.0,
 *   mapTilt: mapTilt,
 *   isPaused: false,
 *   onAnimationComplete: () => handleComplete(),
 * )
 * ```
 */

// Constants for animation configuration
class AbsorptionConfig {
  static const Duration animationDuration = Duration(milliseconds: 1500);
  static const int particlesPerPin = 12;
  static const double particleBaseSize = 24.0;
  static const double particleTrailOpacity = 0.8;
  static const double particleTrailWidth = 6.0;
  static const double maxDelaySeconds = 0.3;
  static const Duration hapticInterval = Duration(milliseconds: 100);
}

// Colors for different pin rarities with increased brightness
class PinRarityColors {
  static const legendary = Color(0xFFFFE57F); // Brighter gold
  static const epic = Color(0xFFE040FB);      // Brighter purple
  static const rare = Color(0xFF448AFF);      // Brighter blue
  static const uncommon = Color(0xFF69F0AE);  // Brighter green
  static const common = Color(0xFFE0E0E0);    // Brighter grey
}

class PinAbsorptionOverlay extends StatefulWidget {
  final LatLng userLocation;
  final List<Map<String, dynamic>> pins;
  final double radius;
  final VoidCallback onAnimationComplete;
  final double mapTilt; // Add tilt parameter for 2.5D effect
  final bool isPaused; // Add pause state

  const PinAbsorptionOverlay({
    Key? key,
    required this.userLocation,
    required this.pins,
    required this.radius,
    required this.onAnimationComplete,
    this.mapTilt = 0.0, // Default to flat view
    this.isPaused = false, // Default to not paused
  }) : super(key: key);

  @override
  State<PinAbsorptionOverlay> createState() => _PinAbsorptionOverlayState();
}

class _PinAbsorptionOverlayState extends State<PinAbsorptionOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  List<_AbsorptionParticle> _particles = [];
  final Random _random = Random();
  DateTime? _lastHapticFeedback;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: AbsorptionConfig.animationDuration,
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          HapticFeedback.mediumImpact();
          widget.onAnimationComplete();
        }
      });

    _initializeParticles();
    if (!widget.isPaused) {
      _controller.forward();
      HapticFeedback.lightImpact();
    }
  }

  @override
  void didUpdateWidget(PinAbsorptionOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isPaused != oldWidget.isPaused) {
      if (widget.isPaused) {
        _controller.stop();
      } else {
        _controller.forward();
        HapticFeedback.lightImpact();
      }
    }
  }

  void _initializeParticles() {
    _particles = [];
    for (final pin in widget.pins) {
      final pinLat = pin['latitude'] as double;
      final pinLng = pin['longitude'] as double;
      
      for (int i = 0; i < AbsorptionConfig.particlesPerPin; i++) {
        _particles.add(_AbsorptionParticle(
          startLat: pinLat,
          startLng: pinLng,
          endLat: widget.userLocation.latitude,
          endLng: widget.userLocation.longitude,
          color: _getPinColor(pin),
          delay: _random.nextDouble() * AbsorptionConfig.maxDelaySeconds,
          tilt: widget.mapTilt,
        ));
      }
    }
  }

  Color _getPinColor(Map<String, dynamic> pin) {
    final rarity = pin['rarity']?.toString().toLowerCase() ?? 'common';
    switch (rarity) {
      case 'legendary':
        return PinRarityColors.legendary;
      case 'epic':
        return PinRarityColors.epic;
      case 'rare':
        return PinRarityColors.rare;
      case 'uncommon':
        return PinRarityColors.uncommon;
      default:
        return PinRarityColors.common;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          final now = DateTime.now();
          if (_lastHapticFeedback == null ||
              now.difference(_lastHapticFeedback!) > AbsorptionConfig.hapticInterval) {
            _lastHapticFeedback = now;
            HapticFeedback.selectionClick();
          }

          return Stack(
            children: [
              // Background blur/dimming effect
              Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.1),
                ),
              ),
              // Particle animation
              CustomPaint(
                size: Size.infinite,
                painter: _AbsorptionPainter(
                  particles: _particles,
                  progress: _controller.value,
                  tilt: widget.mapTilt,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class _AbsorptionParticle {
  final double startLat;
  final double startLng;
  final double endLat;
  final double endLng;
  final Color color;
  final double delay;
  final double tilt;

  _AbsorptionParticle({
    required this.startLat,
    required this.startLng,
    required this.endLat,
    required this.endLng,
    required this.color,
    required this.delay,
    required this.tilt,
  });
}

class _AbsorptionPainter extends CustomPainter {
  final List<_AbsorptionParticle> particles;
  final double progress;
  final double tilt;

  _AbsorptionPainter({
    required this.particles,
    required this.progress,
    required this.tilt,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Add a semi-transparent overlay to make particles pop
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = Colors.black.withOpacity(0.3),
    );

    for (final particle in particles) {
      final particleProgress = (progress - particle.delay).clamp(0.0, 1.0);
      if (particleProgress <= 0) continue;

      final currentLat = _lerp(particle.startLat, particle.endLat, particleProgress);
      final currentLng = _lerp(particle.startLng, particle.endLng, particleProgress);
      
      // Apply 2.5D transformation
      final Point screenPoint = _latLngToScreen(
        currentLat,
        currentLng,
        size,
        tilt,
      );

      // Draw trail with glow effect
      if (particleProgress > 0.1) {
        final startPoint = _latLngToScreen(
          particle.startLat,
          particle.startLng,
          size,
          tilt,
        );
        
        // Draw outer glow
        final glowPaint = Paint()
          ..color = particle.color.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = AbsorptionConfig.particleTrailWidth * 3
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

        final path = Path()
          ..moveTo(startPoint.x, startPoint.y)
          ..lineTo(screenPoint.x, screenPoint.y);
        
        canvas.drawPath(path, glowPaint);

        // Draw main trail
        final trailPaint = Paint()
          ..color = particle.color.withOpacity(AbsorptionConfig.particleTrailOpacity)
          ..style = PaintingStyle.stroke
          ..strokeWidth = AbsorptionConfig.particleTrailWidth;
        
        canvas.drawPath(path, trailPaint);
      }

      // Draw particle with glow effect
      final glowPaint = Paint()
        ..color = particle.color.withOpacity(0.5)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);

      final radius = AbsorptionConfig.particleBaseSize * (1.0 - particleProgress * 0.5);
      canvas.drawCircle(Offset(screenPoint.x, screenPoint.y), radius * 1.5, glowPaint);

      // Draw main particle
      final paint = Paint()
        ..color = particle.color.withOpacity(1.0 - particleProgress * 0.5)
        ..style = PaintingStyle.fill;

      canvas.drawCircle(Offset(screenPoint.x, screenPoint.y), radius, paint);
    }
  }

  Point _latLngToScreen(double lat, double lng, Size size, double tilt) {
    // First convert lat/lng to mercator projection
    final latRad = lat * math.pi / 180;
    final mercN = math.log(math.tan((math.pi / 4) + (latRad / 2)));
    
    // Get x/y coordinates (0-1)
    final x = (lng + 180) / 360;
    final y = (1 - mercN / (2 * math.pi)) / 2;
    
    // Apply screen transformation and perspective
    final screenX = x * size.width;
    final screenY = y * size.height;
    
    // Apply tilt effect
    final tiltOffset = y * tilt * size.height * 0.3; // Increased tilt effect
    
    return Point(
      screenX,
      screenY - tiltOffset,
    );
  }

  double _lerp(double start, double end, double progress) {
    return start + (end - start) * progress;
  }

  @override
  bool shouldRepaint(_AbsorptionPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.tilt != tilt;
  }
}

class Point {
  final double x;
  final double y;

  Point(this.x, this.y);
} 