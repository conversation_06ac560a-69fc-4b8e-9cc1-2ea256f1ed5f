import 'package:flutter/material.dart';

// Widget for displaying various loading indicators on the map
class MapLoadingIndicator extends StatelessWidget {
  final bool isMapMoving;
  final int buildingDetailLevel;
  final bool isGettingUserLocation;
  final bool isProviderLoading;
  final bool initialLoadAttempted;

  const MapLoadingIndicator({
    Key? key,
    required this.isMapMoving,
    required this.buildingDetailLevel,
    required this.isGettingUserLocation,
    required this.isProviderLoading,
    required this.initialLoadAttempted,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Return empty container to hide all loading indicators
    return Container();
  }
} 