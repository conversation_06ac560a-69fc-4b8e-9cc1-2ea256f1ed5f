import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

// Custom marker class that includes pin data
class CustomMarker extends Marker {
  final Map<String, dynamic> pinData;
  
  CustomMarker({
    required LatLng point,
    required this.pinData,
    required Widget Function(BuildContext) builder,
    double width = 40.0,  // Increased width
    double height = 45.0, // Increased height to accommodate pin shape
    Alignment? alignment,
  }) : super(
         point: point,
         child: Builder(builder: builder),
         width: width,
         height: height,
         alignment: alignment ?? Alignment.topCenter,
       );
}