import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

/// A widget that displays the user's location on the map with a pulsing animation
/// and directional indicator, similar to Uber's user location marker
class UserLocationMarker extends StatelessWidget {
  final double? heading;
  final Color primaryColor;
  final bool showPulse;
  final bool showHeading;
  final double size;

  const UserLocationMarker({
    Key? key,
    this.heading,
    Color? primaryColor,
    this.showPulse = true,
    this.showHeading = true,
    this.size = 24,
  }) : primaryColor = primaryColor ?? Colors.blue, // Default to blue if no color provided
      super(key: key);

  @override
  Widget build(BuildContext context) {
    // Ensure marker color is visible
    final markerColor = primaryColor.computeLuminance() < 0.1 ? Colors.blue : primaryColor;
    
    return Stack(
      children: [
        // Pulse animation
        if (showPulse)
          TweenAnimationBuilder<double>(
            tween: Tween(begin: 0.0, end: 1.0),
            duration: const Duration(seconds: 2),
            curve: Curves.easeInOut,
            builder: (context, value, child) {
              return Opacity(
                opacity: (1 - value) * 0.5,
                child: Transform.scale(
                  scale: 1 + (value * 0.5),
                  child: Container(
                    width: size * 1.5,
                    height: size * 1.5,
                    decoration: BoxDecoration(
                      color: markerColor.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              );
            },
          ),

        // Main marker
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: markerColor,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 4,
                spreadRadius: 1,
              )
            ],
          ),
          // Add directional indicator if heading is available
          child: showHeading && heading != null
              ? Transform.rotate(
                  angle: (heading! * math.pi / 180),
                  child: const Icon(
                    Icons.navigation,
                    color: Colors.white,
                    size: 14,
                  ),
                )
              : null,
        ),

        // Elevation shadow
        Positioned(
          bottom: -2,
          left: size * 0.125,
          child: Container(
            width: size * 0.75,
            height: 2,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(3),
              color: Colors.black.withOpacity(0.2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 3,
                  spreadRadius: 0,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
} 