import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';

import 'core/optimized_map_controller.dart';
import 'core/optimized_building_renderer.dart';
import '../providers/map_settings_provider.dart';
import '../map_layers/osm_data_processor.dart';

/// A map widget that uses advanced optimizations for smooth performance
class OptimizedMapWidget extends StatefulWidget {
  final LatLng initialCenter;
  final double initialZoom;
  final bool initialUse3D;
  final Function(Map<String, dynamic>)? onPerformanceUpdate;

  const OptimizedMapWidget({
    Key? key,
    required this.initialCenter,
    this.initialZoom = 13.0,
    this.initialUse3D = true,
    this.onPerformanceUpdate,
  }) : super(key: key);

  @override
  State<OptimizedMapWidget> createState() => _OptimizedMapWidgetState();
}

class _OptimizedMapWidgetState extends State<OptimizedMapWidget>
    with TickerProviderStateMixin {
  // Controllers
  late OptimizedMapController _mapController;
  late OptimizedBuildingRenderer _buildingRenderer;

  // State
  Size _viewportSize = Size.zero;
  Matrix4 _viewProjection = Matrix4.identity();
  bool _isFirstFrame = true;

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    final dataProcessor = OSMDataProcessor();
    
    _mapController = OptimizedMapController(
      vsync: this,
      dataProcessor: dataProcessor,
    );

    _buildingRenderer = OptimizedBuildingRenderer();

    // Set up performance monitoring
    _startPerformanceMonitoring();
  }

  /// Start monitoring performance metrics
  void _startPerformanceMonitoring() {
    // Update metrics every second
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;

      // Gather metrics from all systems
      final metrics = _gatherPerformanceMetrics();
      
      // Notify listener
      widget.onPerformanceUpdate?.call(metrics);
      
      // Schedule next update
      _startPerformanceMonitoring();
    });
  }

  /// Gather performance metrics from all systems
  Map<String, dynamic> _gatherPerformanceMetrics() {
    final mapMetrics = _mapController.getPerformanceMetrics();
    final rendererMetrics = _buildingRenderer.getPerformanceMetrics();

    return {
      ...mapMetrics,
      ...rendererMetrics,
      'viewportSize': '${_viewportSize.width.round()}x${_viewportSize.height.round()}',
    };
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Update viewport size
        _viewportSize = Size(constraints.maxWidth, constraints.maxHeight);
        
        return FlutterMap(
          mapController: _mapController,
          options: MapOptions(
            center: widget.initialCenter,
            zoom: widget.initialZoom,
            minZoom: 4,
            maxZoom: 18,
            interactiveFlags: InteractiveFlag.all,
            onMapReady: _onMapReady,
            onPositionChanged: _onPositionChanged,
          ),
          children: [
            // Base tile layer
            TileLayer(
              urlTemplate: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
              subdomains: const ['a', 'b', 'c'],
              maxZoom: 19,
              tileProvider: _createOptimizedTileProvider(),
            ),
            
            // Building layer
            _BuildingLayer(
              controller: _mapController,
              renderer: _buildingRenderer,
              viewportSize: _viewportSize,
              viewProjection: _viewProjection,
            ),
            
            // Controls
            _MapControls(
              controller: _mapController,
              onViewportUpdate: _updateViewport,
            ),
          ],
        );
      },
    );
  }

  /// Create an optimized tile provider
  TileProvider _createOptimizedTileProvider() {
    return NetworkTileProvider(
      headers: const {
        'User-Agent': 'OptimizedMapWidget/1.0',
      },
      maxConcurrentLoads: 6,
      retryCount: 3,
    );
  }

  /// Handle map ready event
  void _onMapReady() {
    if (_isFirstFrame) {
      _isFirstFrame = false;
      
      // Initialize 3D mode if requested
      if (widget.initialUse3D) {
        final mapSettings = context.read<MapSettingsProvider>();
        mapSettings.setUse3DBuildings(true);
      }
    }
  }

  /// Handle position changes
  void _onPositionChanged(MapPosition position, bool hasGesture) {
    // Update view projection matrix
    _updateViewProjection(position);
    
    // Update map controller
    _mapController.updateViewport(_viewportSize, _viewProjection);
  }

  /// Update the view projection matrix
  void _updateViewProjection(MapPosition position) {
    // Calculate new view projection matrix
    final center = position.center!;
    final zoom = position.zoom!;
    
    // Create view matrix
    final view = Matrix4.identity()
      ..translate(0.0, 0.0, -500.0) // Camera distance
      ..rotateX(-45.0 * (pi / 180.0)); // Camera angle
    
    // Create projection matrix
    final projection = Matrix4.perspective(
      45.0 * (pi / 180.0), // FOV
      _viewportSize.width / _viewportSize.height, // Aspect ratio
      0.1, // Near plane
      1000.0, // Far plane
    );
    
    // Combine matrices
    _viewProjection = projection * view;
  }

  /// Update viewport parameters
  void _updateViewport(Size size, Matrix4 projection) {
    setState(() {
      _viewportSize = size;
      _viewProjection = projection;
    });
  }

  @override
  void dispose() {
    _mapController.dispose();
    _buildingRenderer.dispose();
    super.dispose();
  }
}

/// Widget for rendering buildings
class _BuildingLayer extends StatelessWidget {
  final OptimizedMapController controller;
  final OptimizedBuildingRenderer renderer;
  final Size viewportSize;
  final Matrix4 viewProjection;

  const _BuildingLayer({
    Key? key,
    required this.controller,
    required this.renderer,
    required this.viewportSize,
    required this.viewProjection,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<MapSettingsProvider>(
      builder: (context, settings, child) {
        return CustomPaint(
          painter: _BuildingPainter(
            controller: controller,
            renderer: renderer,
            viewportSize: viewportSize,
            viewProjection: viewProjection,
            use3D: settings.use3DBuildings,
          ),
        );
      },
    );
  }
}

/// Custom painter for buildings
class _BuildingPainter extends CustomPainter {
  final OptimizedMapController controller;
  final OptimizedBuildingRenderer renderer;
  final Size viewportSize;
  final Matrix4 viewProjection;
  final bool use3D;

  _BuildingPainter({
    required this.controller,
    required this.renderer,
    required this.viewportSize,
    required this.viewProjection,
    required this.use3D,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Skip if 3D is disabled
    if (!use3D) return;
    
    // Update renderer viewport
    renderer.updateViewport(viewportSize, viewProjection);
    
    // Paint buildings
    renderer.paint(canvas, Offset.zero);
  }

  @override
  bool shouldRepaint(_BuildingPainter oldDelegate) {
    return viewportSize != oldDelegate.viewportSize ||
           viewProjection != oldDelegate.viewProjection ||
           use3D != oldDelegate.use3D;
  }
}

/// Widget for map controls
class _MapControls extends StatelessWidget {
  final OptimizedMapController controller;
  final Function(Size, Matrix4) onViewportUpdate;

  const _MapControls({
    Key? key,
    required this.controller,
    required this.onViewportUpdate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 16,
      bottom: 16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Zoom controls
          FloatingActionButton(
            heroTag: 'zoomIn',
            onPressed: controller.zoomIn,
            child: const Icon(Icons.add),
          ),
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: 'zoomOut',
            onPressed: controller.zoomOut,
            child: const Icon(Icons.remove),
          ),
          const SizedBox(height: 8),
          // 3D toggle
          Consumer<MapSettingsProvider>(
            builder: (context, settings, child) {
              return FloatingActionButton(
                heroTag: '3dToggle',
                onPressed: () => settings.toggle3DBuildings(),
                child: Icon(
                  settings.use3DBuildings ? Icons.view_in_ar : Icons.map,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
} 