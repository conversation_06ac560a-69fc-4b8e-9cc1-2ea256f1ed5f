import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';

import '../../../services/map/tile_cache_service.dart';
import '../../../models/map/tile.dart';

/// An optimized tile provider that implements multi-level caching and
/// preloading for better map performance at different zoom levels.
class OptimizedTileProvider extends TileProvider {
  final String urlTemplate;
  final Map<String, String> headers;
  final bool enablePreloading;
  
  // New TileCacheService integration (optional)
  final TileCacheService? _tileCacheService;
  
  // Tile cache in memory for fast access
  final Map<String, Uint8List> _memoryCache = {};
  
  // Keep track of which tiles are currently being fetched to avoid duplicates
  final Set<String> _tilesInProgress = {};
  
  // Fallback tile for when loading fails
  Uint8List? _fallbackTile;
  
  // Server rotation for OpenStreetMap (a,b,c subdomains)
  int _serverIndex = 0;
  final List<String> _subdomains = ['a', 'b', 'c'];
  
  // Connection management
  final http.Client _httpClient = http.Client();
  int _activeConnections = 0;
  final int _maxConcurrentConnections = 5; // Reduced from 8 to avoid connection issues
  final _pendingRequests = <Function>[];

  // File cache to save tiles locally
  final Map<String, String> _fileCache = {};
  bool _diskCacheInitialized = false;
  String _tileCacheDir = '';

  // Request throttling
  final Map<String, DateTime> _lastHostRequest = {};
  final int _minRequestInterval = 300; // Increased from 100 to respect rate limits
  
  OptimizedTileProvider({
    required this.urlTemplate,
    Map<String, String>? headers,
    this.enablePreloading = true,
    TileCacheService? tileCacheService,
  }) : 
    headers = headers ?? {},
    _tileCacheService = tileCacheService {
    _initialize();
  }
  
  // Initialize the provider
  Future<void> _initialize() async {
    // Create a simple fallback tile
    _createSimpleFallbackTile();
    
    // Initialize the file cache
    _initializeFileCache();
  }
  
  // Initialize file cache
  Future<void> _initializeFileCache() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _tileCacheDir = '${appDir.path}/tile_cache';
      
      // Create the cache directory if it doesn't exist
      final dir = Directory(_tileCacheDir);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }
      
      _diskCacheInitialized = true;
      debugPrint('Tile file cache initialized at $_tileCacheDir');
    } catch (e) {
      debugPrint('Error initializing file cache: $e');
    }
  }
  
  // Create a simple fallback tile
  void _createSimpleFallbackTile() {
    // Create a simple blank white tile as backup
    _fallbackTile = Uint8List.fromList(List.filled(256 * 256 * 4, 255));
  }
  
  @override
  ImageProvider getImage(TileCoordinates coordinates, TileLayer options) {
    final tileKey = _generateTileKey(coordinates);
    
    // Check if the tile is already in memory
    if (_memoryCache.containsKey(tileKey)) {
      return MemoryImage(_memoryCache[tileKey]!);
    }
    
    // If the tile is already being loaded, don't start another load
    if (_tilesInProgress.contains(tileKey)) {
      return MemoryImage(_fallbackTile ?? Uint8List.fromList(List.filled(256 * 256 * 4, 255)));
    }
    
    // Mark this tile as in progress
    _tilesInProgress.add(tileKey);
    
    // Queue the request if we have too many active connections
    if (_activeConnections >= _maxConcurrentConnections) {
      _queueRequest(() => _loadAndProcessTile(coordinates, tileKey));
    } else {
      _loadAndProcessTile(coordinates, tileKey);
    }
    
    // Return a fallback tile while loading
    return MemoryImage(_fallbackTile ?? Uint8List.fromList(List.filled(256 * 256 * 4, 255)));
  }
  
  // Queue a request for later processing
  void _queueRequest(Function requestFn) {
    _pendingRequests.add(requestFn);
  }
  
  // Process the next request in queue
  void _processNextRequest() {
    if (_pendingRequests.isEmpty || _activeConnections >= _maxConcurrentConnections) {
      return;
    }
    
    final nextRequest = _pendingRequests.removeAt(0);
    nextRequest();
  }
  
  // Load and process a tile
  Future<void> _loadAndProcessTile(TileCoordinates coordinates, String tileKey) async {
        _activeConnections++;
        
    try {
      final data = await _loadTile(coordinates);
      if (data != null) {
        _memoryCache[tileKey] = data;
      }
    } catch (e) {
      debugPrint('Error loading tile: $e');
    } finally {
      _tilesInProgress.remove(tileKey);
      _activeConnections--;
      
      // Process next request if any
      _processNextRequest();
    }
  }
  
  // Check if we need to rate limit requests to this host
  bool _shouldThrottleHost(String host) {
    final lastRequest = _lastHostRequest[host];
    if (lastRequest != null) {
      final timeSinceLastRequest = DateTime.now().difference(lastRequest).inMilliseconds;
      return timeSinceLastRequest < _minRequestInterval;
    }
    return false;
  }
  
  // Update the last request time
  void _updateLastRequestTime(String host) {
    _lastHostRequest[host] = DateTime.now();
  }
  
  // Extract hostname from URL
  String _extractHostname(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      final parts = url.split('/');
      return parts.length > 2 ? parts[2] : url;
    }
  }

  // Check if a tile is in the file cache
  Future<Uint8List?> _checkFileCacheForTile(String tileKey) async {
    if (!_diskCacheInitialized) return null;
    
    try {
      final filePath = '$_tileCacheDir/$tileKey.png';
      final file = File(filePath);
      
      if (await file.exists()) {
        final data = await file.readAsBytes();
        if (data.isNotEmpty) {
          return data;
        }
      }
    } catch (e) {
      debugPrint('Error checking file cache: $e');
    }
    
    return null;
  }
  
  // Save a tile to the file cache
  Future<void> _saveTileToFileCache(String tileKey, Uint8List data) async {
    if (!_diskCacheInitialized) return;
    
    try {
      final filePath = '$_tileCacheDir/$tileKey.png';
      final file = File(filePath);
      await file.writeAsBytes(data);
      _fileCache[tileKey] = filePath;
    } catch (e) {
      debugPrint('Error saving to file cache: $e');
    }
  }
  
  // Load a tile from cache or network
  Future<Uint8List?> _loadTile(TileCoordinates coordinates) async {
    final tileKey = _generateTileKey(coordinates);
    
    // 1. Check memory cache first (already checked in getImage but double-check here)
    if (_memoryCache.containsKey(tileKey)) {
      return _memoryCache[tileKey];
    }
    
    // 2. Check file cache next
    final cachedData = await _checkFileCacheForTile(tileKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    // 3. Try backend TileCacheService if available
    if (_tileCacheService != null) {
      try {
        final mapTile = MapTile(
          coordinates.z.toInt(), 
          coordinates.x.toInt(), 
          coordinates.y.toInt()
        );
        
        final tileData = await _tileCacheService?.getCachedTile(mapTile);
        if (tileData != null) {
          final data = Uint8List.fromList(tileData);
          
          // Save to file cache for future use
          await _saveTileToFileCache(tileKey, data);
          
          return data;
        }
      } catch (e) {
        debugPrint('TileCacheService error (non-critical): $e');
        // Continue to network fetch
      }
    }
    
    // 4. If all caches miss, load from network
    try {
      final url = _getTileUrl(coordinates);
    final host = _extractHostname(url);
    
      // Check if we need to throttle requests to this host
      if (_shouldThrottleHost(host)) {
      await Future.delayed(Duration(milliseconds: _minRequestInterval));
    }
    
      // Update the last request time
    _updateLastRequestTime(host);
    
      final response = await _httpClient.get(Uri.parse(url), headers: headers)
          .timeout(const Duration(seconds: 5));
      
      if (response.statusCode == 200) {
        final data = response.bodyBytes;
        
        // Save to file cache for future use
        await _saveTileToFileCache(tileKey, data);
        
        // Store in TileCacheService if available (non-blocking)
        if (_tileCacheService != null) {
          try {
            final mapTile = MapTile(
              coordinates.z.toInt(), 
              coordinates.x.toInt(), 
              coordinates.y.toInt()
            );
            
            // Don't await this, let it happen in the background
            _tileCacheService?.storeTile(mapTile, data);
          } catch (e) {
            // Non-critical error, just log
            debugPrint('Error storing tile in backend cache: $e');
          }
        }
        
        return data;
      }
    } catch (e) {
      debugPrint('Network error loading tile: $e');
    }
    
    // 5. Return fallback if all else fails
    return _fallbackTile;
  }
  
  // Get the URL for a tile
  String _getTileUrl(TileCoordinates coordinates) {
    var url = urlTemplate
          .replaceAll('{z}', coordinates.z.toString())
          .replaceAll('{x}', coordinates.x.toString())
      .replaceAll('{y}', coordinates.y.toString());
      
    // Rotate servers if this is an OSM URL
    if (url.contains('{s}')) {
      url = url.replaceAll('{s}', _getNextSubdomain());
    }
    
    // Remove the {r} part if present (not all providers support retina)
    url = url.replaceAll('{r}', '');
    
    return url;
  }
  
  // Get the next server subdomain in rotation
  String _getNextSubdomain() {
    final subdomain = _subdomains[_serverIndex];
    _serverIndex = (_serverIndex + 1) % _subdomains.length;
    return subdomain;
  }
  
  // Generate a consistent key for a tile
  String _generateTileKey(TileCoordinates coordinates) {
    return 'tile_${coordinates.z}_${coordinates.x}_${coordinates.y}';
  }
  
  // Clear cache
  void clearCache() {
    _memoryCache.clear();
  }
  
  // For visible bounds prefetching
  Future<void> prefetchVisibleRegion(LatLngBounds bounds, int zoom) async {
    if (!enablePreloading) return;
    
    // Convert bounds to tile coordinates
    final sw = _latLngToTileCoords(bounds.southWest, zoom);
    final ne = _latLngToTileCoords(bounds.northEast, zoom);
    
    // Limit the number of tiles to prefetch to avoid overloading
    final tilesX = ne.x - sw.x + 1;
    final tilesY = ne.y - sw.y + 1;
    
    // If too many tiles, skip prefetching
    if (tilesX * tilesY > 25) { // Reduced from 36 to be more conservative
      debugPrint('Skipping prefetch - too many tiles: $tilesX x $tilesY');
      return;
    }
    
    // Queue up prefetching
    for (int x = sw.x; x <= ne.x; x++) {
      for (int y = sw.y; y <= ne.y; y++) {
        final coords = TileCoordinates(x, y, zoom);
        final tileKey = _generateTileKey(coords);
        
        // Skip if already cached or in progress
        if (_memoryCache.containsKey(tileKey) || _tilesInProgress.contains(tileKey)) {
          continue;
        }
        
        // Queue for prefetch
        if (_activeConnections >= _maxConcurrentConnections) {
          _queueRequest(() => _loadAndProcessTile(coords, tileKey));
      } else {
          _loadAndProcessTile(coords, tileKey);
        }
      }
    }
  }
  
  // Convert LatLng to tile coordinates
  TileCoordinates _latLngToTileCoords(LatLng latLng, int zoom) {
    final lat = latLng.latitude;
    final lng = latLng.longitude;
    
    final x = ((lng + 180) / 360 * math.pow(2, zoom)).floor();
    final y = ((1 - math.log(math.tan(lat * math.pi / 180) + 1 / math.cos(lat * math.pi / 180)) / math.pi) / 2 * math.pow(2, zoom)).floor();
    
    return TileCoordinates(x, y, zoom);
  }
  
  @override
  void dispose() {
    _httpClient.close();
    super.dispose();
  }
} 