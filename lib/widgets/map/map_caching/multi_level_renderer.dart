import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

import 'zoom_level_manager.dart';
import '../map_layers/osm_buildings_layer.dart';
import '../map_layers/osm_roads_layer.dart';
import '../map_layers/osm_water_features_layer.dart';
import '../map_layers/osm_points_of_interest_layer.dart';
import '../map_layers/parks/osm_parks_layer.dart';
import '../map_layers/pedestrian/osm_pedestrian_layer.dart';
import '../map_layers/specialized_green/specialized_green_layer.dart';

/// A specialized renderer that efficiently manages different visual representations
/// across the 5 distinct zoom levels of the map.
class MultiLevelRenderer extends StatefulWidget {
  final LatLngBounds visibleBounds;
  final double zoomLevel;
  final bool isMapMoving;
  final double tiltFactor;
  final String theme;
  final MapCamera? mapCamera;
  final bool use3DBuildings; // Add parameter for 3D buildings setting
  
  const MultiLevelRenderer({
    Key? key,
    required this.visibleBounds,
    required this.zoomLevel,
    required this.isMapMoving,
    required this.tiltFactor,
    this.theme = 'vibrant',
    this.mapCamera,
    this.use3DBuildings = true, // Default to true for backward compatibility
  }) : super(key: key);

  @override
  State<MultiLevelRenderer> createState() => _MultiLevelRendererState();
}

class _MultiLevelRendererState extends State<MultiLevelRenderer> with SingleTickerProviderStateMixin {
  // Zoom level manager
  final ZoomLevelManager _zoomManager = ZoomLevelManager();
  
  // Animation controller for smooth transitions
  late AnimationController _animationController;
  Animation<double>? _fadeAnimation;
  
  // Keep track of rendered layers and their visibility
  int _activeZoomLevel = 3; // Default to regional view
  Map<String, dynamic> _renderParams = {};
  
  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller for smooth transitions
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    
    // Initial update
    _updateActiveZoomLevel();
  }
  
  @override
  void didUpdateWidget(MultiLevelRenderer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update if zoom changes
    if (widget.zoomLevel != oldWidget.zoomLevel) {
      _updateActiveZoomLevel();
    }
    
    // Update bounds in the zoom manager
    _zoomManager.updateBounds(widget.visibleBounds);
    
    // Trigger preloading for the next zoom level if we're not moving
    if (!widget.isMapMoving && oldWidget.isMapMoving) {
      _zoomManager.preloadNextZoomLevel();
    }
    
    // Update if 3D buildings setting changes
    if (widget.use3DBuildings != oldWidget.use3DBuildings) {
      setState(() {
        // Force rebuild with new 3D settings
      });
    }
  }
  
  void _updateActiveZoomLevel() {
    // Update zoom manager
    _zoomManager.updateZoomLevel(widget.zoomLevel);
    
    // Get the new zoom level
    final newZoomLevel = _zoomManager.currentZoomLevel;
    
    // Skip if same level
    if (newZoomLevel == _activeZoomLevel && _renderParams.isNotEmpty) {
      return;
    }
    
    // Get new rendering parameters
    final newParams = _zoomManager.getOptimizedRenderingParameters();
    
    // Animate transition if it's a significant change
    final bool needsAnimation = _activeZoomLevel != newZoomLevel && _renderParams.isNotEmpty;
    
    setState(() {
      _activeZoomLevel = newZoomLevel;
      _renderParams = newParams;
      
      if (needsAnimation) {
        // Reset and play animation
        _animationController.reset();
        _animationController.forward();
      } else {
        // Skip animation for initial load
        _animationController.value = 1.0;
      }
    });
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Apply different rendering strategies based on zoom level
        _buildZoomLevelLayers(),
      ],
    );
  }
  
  Widget _buildZoomLevelLayers() {
    // Calculate an effective tilt factor that increases with zoom
    final double zoomEnhancedTilt = _calculateZoomEnhancedTilt();
    
    // Show different layer combinations based on active zoom level
    return AnimatedBuilder(
      animation: _fadeAnimation!,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation!.value,
          child: Stack(
            children: [
              // Water Features Layer (always at bottom)
              if (_renderParams['showWater'] == true)
                OSMWaterFeaturesLayer(
                  tiltFactor: zoomEnhancedTilt,
                  zoomLevel: widget.zoomLevel,
                  isMapMoving: widget.isMapMoving,
                  visibleBounds: widget.visibleBounds,
                  detailLevel: _getDetailLevel(_renderParams['detailLevel']),
                  mapCamera: widget.mapCamera,
                ),
              
              // Specialized Green Layer (render before parks for proper layering)
              if (_renderParams['showSpecializedGreen'] == true && widget.zoomLevel >= 12)
                SpecializedGreenLayer(
                  season: _getSeason(),
                  enhancedDetail: _renderParams['detailLevel'] == 'high' || _renderParams['detailLevel'] == 'medium',
                  tiltFactor: zoomEnhancedTilt,
                  theme: widget.theme,
                  zoomLevel: widget.zoomLevel,
                  visibleBounds: widget.visibleBounds,
                  isMapMoving: widget.isMapMoving,
                  mapCamera: widget.mapCamera,
                ),
              
              // Parks Layer
              if (_renderParams['showParks'] == true)
                OSMParksLayer(
                  tiltFactor: zoomEnhancedTilt,
                  zoomLevel: widget.zoomLevel,
                  isMapMoving: widget.isMapMoving,
                  visibleBounds: widget.visibleBounds,
                  theme: widget.theme,
                  season: _getSeason(),
                  enhancedDetail: _renderParams['detailLevel'] == 'high' || _renderParams['detailLevel'] == 'medium',
                  detailLevel: _getDetailLevel(_renderParams['detailLevel']),
                  mapCamera: widget.mapCamera,
                ),
              
              // Roads Layer
              if (_renderParams['showRoads'] == true)
                OSMRoadsLayer(
                  tiltFactor: zoomEnhancedTilt,
                  zoomLevel: widget.zoomLevel,
                  isMapMoving: widget.isMapMoving,
                  visibleBounds: widget.visibleBounds,
                  detailLevel: _getDetailLevel(_renderParams['detailLevel']),
                  mapCamera: widget.mapCamera,
                ),
                
              // Pedestrian Layer - Add alongside similar layers with proper z-index
              if (_renderParams['showPedestrian'] == true || widget.zoomLevel >= 16)
                OSMPedestrianLayer(
                  tiltFactor: zoomEnhancedTilt,
                  zoomLevel: widget.zoomLevel,
                  isMapMoving: widget.isMapMoving,
                  visibleBounds: widget.visibleBounds,
                  theme: widget.theme,
                  enhancedDetail: _renderParams['detailLevel'] == 'high' || _renderParams['detailLevel'] == 'medium',
                  mapCamera: widget.mapCamera,
                ),
              
              // Buildings Layer - Only show if both render parameter and widget setting allow it
              if (_renderParams['showBuildings'] == true && widget.use3DBuildings)
                OSMBuildingsLayer(
                  tiltFactor: zoomEnhancedTilt,
                  zoomLevel: widget.zoomLevel,
                  isMapMoving: widget.isMapMoving,
                  visibleBounds: widget.visibleBounds,
                  enhancedDetail: _renderParams['detailLevel'] == 'high',
                  detailLevel: _getDetailLevel(_renderParams['detailLevel']),
                  mapCamera: widget.mapCamera,
                ),
              
              // Points of Interest Layer
              if (_renderParams['showPOIs'] == true && widget.zoomLevel > 14)
                OSMPointsOfInterestLayer(
                  tiltFactor: zoomEnhancedTilt,
                  zoomLevel: widget.zoomLevel,
                  isMapMoving: widget.isMapMoving,
                  visibleBounds: widget.visibleBounds,
                  mapCamera: widget.mapCamera,
                ),
            ],
          ),
        );
      },
    );
  }
  
  // Convert string detail level to numeric value
  double _getDetailLevel(String detailLevel) {
    switch (detailLevel) {
      case 'ultra-low': return 0.2;
      case 'very-low': return 0.4;
      case 'low': return 0.6;
      case 'medium': return 0.8;
      case 'high': return 1.0;
      default: return 0.8;
    }
  }

  // Add a helper method to determine the season based on date or settings
  String _getSeason() {
    // Default implementation - could be expanded to use date or user preferences
    return 'default';
  }

  // Calculate a tilt factor that enhances with zoom level
  double _calculateZoomEnhancedTilt() {
    // Only apply zoom enhancement if 3D rendering is enabled AND 3D buildings setting is on
    if (_renderParams['render3D'] != true || !widget.use3DBuildings) return 0.0;
    
    // Base tilt from widget property
    final double baseTilt = widget.tiltFactor;
    
    // Enhance tilt based on zoom level for more dramatic 3D effect at high zooms
    if (widget.zoomLevel <= 14) return baseTilt;
    if (widget.zoomLevel >= 20) return baseTilt * 1.5; // 50% increase at maximum zoom
    
    // Linear interpolation between zoom 14 and 20
    final double zoomFactor = (widget.zoomLevel - 14) / 6;
    return baseTilt * (1.0 + zoomFactor * 0.5);
  }
} 