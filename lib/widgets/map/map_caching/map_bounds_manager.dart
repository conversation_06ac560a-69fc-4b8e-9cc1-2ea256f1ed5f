import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

/// Manages the map bounds and controls when to fetch OSM data based on user movement
class MapBoundsManager {
  // Initial view area bounds
  final LatLngBounds _initialBounds;
  
  // Current expanded bounds that we've fetched data for
  LatLngBounds _expandedBounds;
  
  // User location tracking
  LatLng? _userLocation;
  bool _hasUserLocation = false;
  
  // Minimum movement threshold to trigger expansion (in degrees)
  static const double _movementThreshold = 0.005; // ~500m
  
  // Maximum expansion factor from initial bounds
  static const double _maxExpansionFactor = 2.0; // Reduced from 4.0 to be more restrictive
  
  // Constructor
  MapBoundsManager(LatLngBounds initialBounds) 
    : _initialBounds = initialBounds,
      _expandedBounds = initialBounds {
    debugPrint('MapBoundsManager initialized with bounds: ${initialBounds.toString()}');
  }
  
  /// Update user location - this will influence bounds calculations
  void updateUserLocation(LatLng? location) {
    _userLocation = location;
    _hasUserLocation = location != null;
    
    if (_hasUserLocation) {
      debugPrint('Updated user location: ${location.toString()}');
    }
  }
  
  /// Check if we should fetch data for the current visible bounds
  bool shouldFetchForBounds(LatLngBounds visibleBounds) {
    // Always allow fetching within initial bounds
    if (_isWithinBounds(visibleBounds, _initialBounds)) {
      return true;
    }
    
    // Get centers for comparison
    final visibleCenter = _getBoundsCenter(visibleBounds);
    final initialCenter = _getBoundsCenter(_initialBounds);
    
    // Calculate distance from initial center
    final distanceFromInitial = _calculateDistance(visibleCenter, initialCenter);
    
    // If user location is available, check if visible bounds include user location
    if (_hasUserLocation && _userLocation != null) {
      // If user is within visible bounds, be more lenient with distance restrictions
      if (_isPointInBounds(_userLocation!, visibleBounds)) {
        // Allow up to 6x movement threshold when user is in view
        if (distanceFromInitial <= _movementThreshold * 6) {
          debugPrint('Allowing fetch: User location in view');
          return true;
        }
      }
    }
    
    // Standard distance check
    if (distanceFromInitial > _movementThreshold * 4) {
      debugPrint('Too far from initial center: $distanceFromInitial degrees');
      return false;
    }
    
    // Check if we've moved significantly from the expanded bounds
    if (_hasSignificantMovement(visibleBounds, _expandedBounds)) {
      // Calculate potential new expanded bounds
      final newExpandedBounds = _calculateExpandedBounds(visibleBounds);
      
      // If user location is available, adjust expansion limits
      if (_hasUserLocation && _userLocation != null && 
          _isPointInBounds(_userLocation!, newExpandedBounds)) {
        // Allow slightly more expansion when moving towards user location
        final adjustedLimits = _isWithinExpansionLimits(newExpandedBounds, userLocationAdjustment: 1.2);
        if (adjustedLimits) {
          _expandedBounds = newExpandedBounds;
          debugPrint('Expanded bounds updated (user location adjusted): ${newExpandedBounds.toString()}');
          return true;
        }
      } else {
        // Standard expansion check
        if (_isWithinExpansionLimits(newExpandedBounds)) {
          _expandedBounds = newExpandedBounds;
          debugPrint('Expanded bounds updated to: ${newExpandedBounds.toString()}');
          return true;
        }
      }
      debugPrint('Expansion would exceed limits');
      return false;
    }
    
    // If we're within expanded bounds, allow fetch
    return _isWithinBounds(visibleBounds, _expandedBounds);
  }
  
  /// Get the current bounds to use for fetching
  LatLngBounds getBoundsForFetching(LatLngBounds visibleBounds) {
    // If within initial bounds, use those
    if (_isWithinBounds(visibleBounds, _initialBounds)) {
      return _initialBounds;
    }
    
    // If user location is available and within visible bounds,
    // ensure the returned bounds include the user location
    if (_hasUserLocation && _userLocation != null && 
        _isPointInBounds(_userLocation!, visibleBounds)) {
      final userAdjustedBounds = _getIntersectionWithUserLocation(
        visibleBounds, 
        _expandedBounds,
        _userLocation!
      );
      debugPrint('Using user-adjusted bounded fetch area: ${userAdjustedBounds.toString()}');
      return userAdjustedBounds;
    }
    
    // Otherwise use standard intersection
    final intersection = _getIntersection(visibleBounds, _expandedBounds);
    debugPrint('Using bounded fetch area: ${intersection.toString()}');
    return intersection;
  }
  
  /// Get intersection of bounds while ensuring user location is included if nearby
  LatLngBounds _getIntersectionWithUserLocation(
    LatLngBounds bounds1, 
    LatLngBounds bounds2,
    LatLng userLocation
  ) {
    // Get standard intersection first
    final intersection = _getIntersection(bounds1, bounds2);
    
    // If user location is already within intersection, return as is
    if (_isPointInBounds(userLocation, intersection)) {
      return intersection;
    }
    
    // If user location is close to intersection, expand slightly to include it
    final intersectionCenter = _getBoundsCenter(intersection);
    final distanceToUser = _calculateDistance(intersectionCenter, userLocation);
    
    if (distanceToUser <= _movementThreshold * 2) {
      // Expand bounds minimally to include user location
      return LatLngBounds(
        LatLng(
          math.min(intersection.south, userLocation.latitude - _movementThreshold * 0.1),
          math.min(intersection.west, userLocation.longitude - _movementThreshold * 0.1)
        ),
        LatLng(
          math.max(intersection.north, userLocation.latitude + _movementThreshold * 0.1),
          math.max(intersection.east, userLocation.longitude + _movementThreshold * 0.1)
        )
      );
    }
    
    return intersection;
  }
  
  /// Check if expansion is within allowed limits
  bool _isWithinExpansionLimits(LatLngBounds expandedBounds, {double userLocationAdjustment = 1.0}) {
    final initialArea = _calculateBoundsArea(_initialBounds);
    final expandedArea = _calculateBoundsArea(expandedBounds);
    
    final expansionFactor = expandedArea / initialArea;
    final adjustedMaxExpansion = _maxExpansionFactor * userLocationAdjustment;
    
    debugPrint('Expansion factor: $expansionFactor (max: $adjustedMaxExpansion)');
    return expansionFactor <= adjustedMaxExpansion;
  }
  
  /// Calculate if there's been significant movement from current bounds
  bool _hasSignificantMovement(LatLngBounds newBounds, LatLngBounds currentBounds) {
    final currentCenter = _getBoundsCenter(currentBounds);
    final newCenter = _getBoundsCenter(newBounds);
    
    final distance = _calculateDistance(currentCenter, newCenter);
    return distance > _movementThreshold;
  }
  
  /// Calculate expanded bounds based on movement direction
  LatLngBounds _calculateExpandedBounds(LatLngBounds visibleBounds) {
    // Calculate movement direction from initial bounds center
    final initialCenter = _getBoundsCenter(_initialBounds);
    final newCenter = _getBoundsCenter(visibleBounds);
    
    // Calculate expansion factors
    final latDiff = newCenter.latitude - initialCenter.latitude;
    final lonDiff = newCenter.longitude - initialCenter.longitude;
    
    // Expand more in the direction of movement, but with stricter limits
    return LatLngBounds(
      LatLng(
        _expandedBounds.south + (latDiff < 0 ? latDiff * 0.3 : 0),
        _expandedBounds.west + (lonDiff < 0 ? lonDiff * 0.3 : 0)
      ),
      LatLng(
        _expandedBounds.north + (latDiff > 0 ? latDiff * 0.3 : 0),
        _expandedBounds.east + (lonDiff > 0 ? lonDiff * 0.3 : 0)
      )
    );
  }
  
  /// Calculate approximate area of bounds
  double _calculateBoundsArea(LatLngBounds bounds) {
    return (bounds.north - bounds.south) * (bounds.east - bounds.west);
  }
  
  /// Check if bounds are completely within other bounds
  bool _isWithinBounds(LatLngBounds inner, LatLngBounds outer) {
    return inner.south >= outer.south &&
           inner.north <= outer.north &&
           inner.west >= outer.west &&
           inner.east <= outer.east;
  }
  
  /// Get intersection of two bounds
  LatLngBounds _getIntersection(LatLngBounds bounds1, LatLngBounds bounds2) {
    return LatLngBounds(
      LatLng(
        math.max(bounds1.south, bounds2.south),
        math.max(bounds1.west, bounds2.west)
      ),
      LatLng(
        math.min(bounds1.north, bounds2.north),
        math.min(bounds1.east, bounds2.east)
      )
    );
  }
  
  /// Get center point of bounds
  LatLng _getBoundsCenter(LatLngBounds bounds) {
    return LatLng(
      (bounds.north + bounds.south) / 2,
      (bounds.east + bounds.west) / 2
    );
  }
  
  /// Check if point is within bounds
  bool _isPointInBounds(LatLng point, LatLngBounds bounds) {
    return point.latitude >= bounds.south &&
           point.latitude <= bounds.north &&
           point.longitude >= bounds.west &&
           point.longitude <= bounds.east;
  }
  
  /// Calculate approximate distance between two points
  double _calculateDistance(LatLng p1, LatLng p2) {
    return math.sqrt(
      math.pow(p1.latitude - p2.latitude, 2) +
      math.pow(p1.longitude - p2.longitude, 2)
    );
  }
} 