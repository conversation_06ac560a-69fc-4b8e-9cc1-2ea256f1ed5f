import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map/flutter_map.dart';
import 'map_cache_coordinator.dart';
import 'persistent_map_cache.dart';

/// Centralized throttling manager for OpenStreetMap API requests
/// This class coordinates requests across all map layers to prevent
/// exceeding API rate limits and implements emergency handling strategies
class OSMThrottleManager {
  // Singleton instance
  static final OSMThrottleManager _instance = OSMThrottleManager._internal();
  factory OSMThrottleManager() => _instance;
  OSMThrottleManager._internal();
  
  // Layer types for request tracking
  static const String LAYER_BUILDINGS = 'buildings';
  static const String LAYER_ROADS = 'roads';
  static const String LAYER_RECREATION = 'recreation';
  static const String LAYER_PARKING = 'parking';
  static const String LAYER_PEDESTRIAN = 'pedestrian';
  static const String LAYER_SPECIALIZED_GREEN = 'specialized_green';
  
  // State tracking
  bool _isInEmergencyMode = false;
  bool _isInCooldownMode = false;
  final List<_OSMRequest> _recentRequests = [];
  
  // Minimum time between requests for each layer (in seconds)
  final Map<String, int> _minRequestIntervals = {
    LAYER_BUILDINGS: 20,
    LAYER_ROADS: 20,
    LAYER_RECREATION: 20,
    LAYER_PARKING: 20,
    LAYER_PEDESTRIAN: 20,
    LAYER_SPECIALIZED_GREEN: 20,
  };
  
  // Track last request time for each layer
  final Map<String, DateTime> _lastRequestTimes = {};
  
  // Track consecutive errors
  int _consecutiveErrors = 0;
  int _currentBackoff = 20; // Start with 20 second backoff
  
  // Track successful requests to potentially reduce backoff
  int _consecutiveSuccesses = 0;
  
  // Configuration
  final Duration _emergencyCooldown = const Duration(seconds: 60);
  final Duration _standardCooldown = const Duration(seconds: 25);
  final int _maxRequestsPer60Seconds = 8;
  final int _errorThreshold = 3;
  
  // Per-layer minimum wait times
  final Map<String, Duration> _layerWaitTimes = {
    LAYER_BUILDINGS: const Duration(seconds: 20),
    LAYER_ROADS: const Duration(seconds: 18),
    LAYER_RECREATION: const Duration(seconds: 15),
    LAYER_PARKING: const Duration(seconds: 18),
    LAYER_PEDESTRIAN: const Duration(seconds: 20),
    LAYER_SPECIALIZED_GREEN: const Duration(seconds: 15),
  };
  
  // Layer priorities (higher number = higher priority)
  final Map<String, int> _layerPriorities = {
    LAYER_BUILDINGS: 5,  // Most important
    LAYER_ROADS: 4,
    LAYER_RECREATION: 2,
    LAYER_PARKING: 3,
    LAYER_PEDESTRIAN: 1, // Least important
    LAYER_SPECIALIZED_GREEN: 3, // Medium-high priority
  };
  
  // Event tracking
  final List<String> _eventLog = [];
  
  /// Check if a layer can make a request now
  /// Returns a Future that completes when the request can proceed
  /// If the layer can proceed immediately, the Future completes immediately
  Future<bool> canMakeRequest(String layerType, LatLngBounds bounds) async {
    _cleanupOldRequests();
    
    final adjustedLayerType = _adjustLayerType(layerType);
    _logEvent('Request attempt: $adjustedLayerType');
    
    if (_isInEmergencyMode) {
      _logEvent('⚠️ In emergency mode - request blocked for $adjustedLayerType');
      return false;
    }
    
    if (_isInCooldownMode) {
      _logEvent('🔄 In cooldown mode - request blocked for $adjustedLayerType');
      return false;
    }
    
    if (_recentRequests.length >= _maxRequestsPer60Seconds) {
      _logEvent('🛑 Global rate limit reached (${_recentRequests.length}/${_maxRequestsPer60Seconds})');
      _enterCooldownMode();
      return false;
    }
    
    final lastRequest = _lastRequestTimes[adjustedLayerType];
    if (lastRequest != null) {
      final elapsed = DateTime.now().difference(lastRequest).inSeconds;
      final minInterval = math.max(_minRequestIntervals[adjustedLayerType] ?? 20, _currentBackoff);
      
      if (elapsed < minInterval) {
        _logEvent('⏱️ Layer $adjustedLayerType requested too soon (elapsed: ${elapsed}s, min: ${minInterval}s)');
        
        // If we're close to the wait time being over, just wait it out
        final remainingWait = minInterval - elapsed;
        if (remainingWait <= 5) {
          await Future.delayed(Duration(seconds: remainingWait));
          return true;
        }
        
        return false;
      }
    }
    
    // Check if another layer has more priority and recently requested
    final priority = _layerPriorities[adjustedLayerType] ?? 0;
    for (final entry in _lastRequestTimes.entries) {
      final otherLayerType = entry.key;
      final otherLayerTime = entry.value;
      final otherPriority = _layerPriorities[otherLayerType] ?? 0;
      
      // If the other layer has higher priority and recently made a request
      if (otherPriority > priority && 
          DateTime.now().difference(otherLayerTime).inSeconds < 5) {
        _logEvent('🔽 Layer $adjustedLayerType yielding to higher priority layer $otherLayerType');
        return false;
      }
    }
    
    // All checks passed, this layer can make a request
    return true;
  }
  
  /// Record a successful API request
  void recordRequest(String layerType, LatLngBounds bounds) {
    final adjustedLayerType = _adjustLayerType(layerType);
    
    // Record the request
    _lastRequestTimes[adjustedLayerType] = DateTime.now();
    _recentRequests.add(_OSMRequest(
      layerType: adjustedLayerType, 
      timestamp: DateTime.now(),
      bounds: bounds
    ));
    
    _logEvent('✅ Request recorded: $adjustedLayerType');
    
    // Reset error counter on successful request
    if (_consecutiveErrors > 0) {
      _consecutiveErrors = 0;
      _consecutiveSuccesses++;
      _logEvent('🔄 Error counter reset');
      
      // Reduce backoff after several successes
      if (_consecutiveSuccesses >= 5 && _currentBackoff > 20) {
        _currentBackoff = math.max(20, _currentBackoff ~/ 2);
        debugPrint('Reducing backoff to $_currentBackoff seconds after $_consecutiveSuccesses successes');
      }
    }
  }
  
  /// Record an error response from the API
  void recordError(String layerType, String errorMessage) {
    final adjustedLayerType = _adjustLayerType(layerType);
    
    _consecutiveErrors++;
    _consecutiveSuccesses = 0;
    _logEvent('❌ Error in $adjustedLayerType: $errorMessage (consecutive: $_consecutiveErrors)');
    
    // Implement exponential backoff
    if (_consecutiveErrors > 3) {
      _currentBackoff = math.min(300, _currentBackoff * 2); // Cap at 5 minutes
      debugPrint('Increasing backoff to $_currentBackoff seconds after $_consecutiveErrors errors');
    }
    
    // Check if we've reached the error threshold
    if (_consecutiveErrors >= _errorThreshold) {
      _enterEmergencyMode();
    }
  }
  
  /// Check if we're in emergency mode
  bool get isInEmergencyMode => _isInEmergencyMode;
  
  /// Get the recent event log
  List<String> getEventLog() {
    return List.from(_eventLog);
  }
  
  /// Enter emergency mode - blocks all requests temporarily and clears some cache
  void _enterEmergencyMode() {
    if (_isInEmergencyMode) return; // Already in emergency mode
    
    _isInEmergencyMode = true;
    _logEvent('⚠️⚠️⚠️ EMERGENCY MODE ACTIVATED - API may be throttling');
    
    // Log detailed recent requests
    _logEvent('Recent requests: ${_recentRequests.map((r) => r.layerType).join(', ')}');
    
    // Clear some cache for emergency recovery
    _clearEmergencyCache();
    
    // Set cooldown timer
    Future.delayed(_emergencyCooldown, () {
      _isInEmergencyMode = false;
      _consecutiveErrors = 0;
      _consecutiveSuccesses = 0;
      _recentRequests.clear();
      _logEvent('✅ Emergency mode deactivated');
    });
  }
  
  /// Enter cooldown mode - temporarily reduce request rate
  void _enterCooldownMode() {
    if (_isInCooldownMode) return; // Already in cooldown mode
    
    _isInCooldownMode = true;
    _logEvent('🔄 Cooldown mode activated - reducing request rate');
    
    // Set cooldown timer
    Future.delayed(_standardCooldown, () {
      _isInCooldownMode = false;
      _logEvent('✅ Cooldown mode deactivated');
    });
  }
  
  /// Clear emergency cache to recover from throttling
  void _clearEmergencyCache() {
    _logEvent('🧹 Performing emergency cache pruning');
    
    // Determine which data type to clear based on recent requests
    final requestCounts = <String, int>{};
    
    for (final request in _recentRequests) {
      requestCounts[request.layerType] = (requestCounts[request.layerType] ?? 0) + 1;
    }
    
    // Find the layer with the most requests
    String? mostRequestedLayer;
    int maxRequests = 0;
    
    requestCounts.forEach((layer, count) {
      if (count > maxRequests) {
        maxRequests = count;
        mostRequestedLayer = layer;
      }
    });
    
    // Clear cache for the most requested layer
    if (mostRequestedLayer != null) {
      _logEvent('🧹 Clearing cache for most requested layer: $mostRequestedLayer');
      
      // Determine the MapDataType to clear
      MapDataType? typeToClean;
      
      switch (mostRequestedLayer) {
        case LAYER_BUILDINGS:
          typeToClean = MapDataType.buildings;
          break;
        case LAYER_ROADS:
          typeToClean = MapDataType.roads;
          break;
        case LAYER_RECREATION:
        case LAYER_PARKING:
        case LAYER_PEDESTRIAN:
          typeToClean = MapDataType.poi;
          break;
        case LAYER_SPECIALIZED_GREEN:
          typeToClean = MapDataType.specializedGreen;
          break;
        default:
          // No specific type to clean
          break;
      }
      
      if (typeToClean != null) {
        // Clear cache for this type
        unawaited(MapCacheCoordinator().clearType(typeToClean));
      }
    }
  }
  
  /// Adjust layer type to standard values
  String _adjustLayerType(String layerType) {
    // Normalize to standard layer types
    final lowered = layerType.toLowerCase();
    
    if (lowered.contains('build')) return LAYER_BUILDINGS;
    if (lowered.contains('road')) return LAYER_ROADS;
    if (lowered.contains('rec')) return LAYER_RECREATION;
    if (lowered.contains('park') && !lowered.contains('recreation')) return LAYER_PARKING;
    if (lowered.contains('ped') || lowered.contains('foot')) return LAYER_PEDESTRIAN;
    
    // Default case
    return layerType;
  }
  
  /// Remove old requests from tracking
  void _cleanupOldRequests() {
    final cutoff = DateTime.now().subtract(const Duration(seconds: 60));
    _recentRequests.removeWhere((request) => request.timestamp.isBefore(cutoff));
  }
  
  /// Log an event with timestamp
  void _logEvent(String message) {
    final timestamp = DateTime.now().toIso8601String().substring(11, 19); // HH:MM:SS
    _eventLog.add('[$timestamp] $message');
    
    // Keep log size reasonable
    if (_eventLog.length > 100) {
      _eventLog.removeRange(0, 50);
    }
    
    // Print to debug console
    debugPrint('OSMThrottle: $message');
  }
  
  /// Get statistics about the throttling
  Map<String, dynamic> getStats() {
    return {
      'emergency_mode': _isInEmergencyMode,
      'cooldown_mode': _isInCooldownMode,
      'consecutive_errors': _consecutiveErrors,
      'recent_requests': _recentRequests.length,
      'layer_last_requests': _lastRequestTimes.map((k, v) => 
        MapEntry(k, DateTime.now().difference(v).inSeconds)),
    };
  }
}

/// Represents a single OSM API request for tracking
class _OSMRequest {
  final String layerType;
  final DateTime timestamp;
  final LatLngBounds bounds;
  
  _OSMRequest({
    required this.layerType,
    required this.timestamp,
    required this.bounds,
  });
}

// Helper to run futures without awaiting
// This is used for background operations like cache cleanup
void unawaited(Future<dynamic> future) {
  // Intentionally ignores the future's completion
} 