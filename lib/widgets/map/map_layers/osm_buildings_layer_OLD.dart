import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

import 'osm_data_processor.dart';
import '../../../services/map_cache_manager.dart';
import '../map_caching/map_cache_extension.dart';
import '../map_caching/zoom_level_manager.dart';
import '../map_caching/map_cache_coordinator.dart';

// Extension to add normalize method to Offset
extension OffsetExtensions on Offset {
  Offset normalize() {
    final double magnitude = distance;
    if (magnitude == 0) return Offset.zero;
    return Offset(dx / magnitude, dy / magnitude);
  }
}

/// A custom layer to render OpenStreetMap buildings in 2.5D
class OSMBuildingsLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final bool isMapMoving;
  final String theme; // Added theme parameter for customization
  
  const OSMBuildingsLayer({
    Key? key,
    this.tiltFactor = 1.0,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.theme = 'vibrant', // Default to vibrant theme
  }) : super(key: key);

  @override
  State<OSMBuildingsLayer> createState() => _OSMBuildingsLayerState();
}

class _OSMBuildingsLayerState extends State<OSMBuildingsLayer> {
  final OSMDataProcessor _dataProcessor = OSMDataProcessor();
  final MapCacheManager _cacheManager = MapCacheManager();
  final ZoomLevelManager _zoomManager = ZoomLevelManager();
  
  List<Map<String, dynamic>> _buildings = [];
  bool _isLoading = true;
  bool _needsRefresh = true;
  String _lastBoundsKey = "";
  String _currentTheme = 'vibrant';
  
  // Track last fetch params to avoid unnecessary fetches
  LatLngBounds? _lastFetchedBounds;
  double _lastFetchedZoom = 0;
  bool _didInitialFetch = false;
  
  // Track current zoom level bucket for optimized rendering
  int _currentZoomBucket = 3;
  
  // Keep track of the last request time for rate limiting
  DateTime _lastRequestTime = DateTime.now().subtract(const Duration(seconds: 30));
  
  // Network error state
  bool _hasNetworkError = false;
  
  // Building color palettes for different themes
  final Map<String, Map<String, Map<String, Color>>> _buildingColorPalette = {
    'vibrant': {
      'commercial': {
        'wall': const Color(0xFF7986CB), 
        'roof': const Color(0xFF5C6BC0)
      },
      'residential': {
        'wall': const Color(0xFFFFB74D), 
        'roof': const Color(0xFFFF9800)
      },
      'office': {
        'wall': const Color(0xFF4FC3F7), 
        'roof': const Color(0xFF29B6F6)
      },
      'industrial': {
        'wall': const Color(0xFF90A4AE), 
        'roof': const Color(0xFF78909C)
      },
      'education': {
        'wall': const Color(0xFF81C784), 
        'roof': const Color(0xFF66BB6A)
      },
      'healthcare': {
        'wall': const Color(0xFFE57373), 
        'roof': const Color(0xFFEF5350)
      },
      'public': {
        'wall': const Color(0xFFBA68C8), 
        'roof': const Color(0xFFAB47BC)
      },
      'historic': {
        'wall': const Color(0xFFD4B178), 
        'roof': const Color(0xFFC19A57)
      },
      'default': {
        'wall': const Color(0xFFBDBDBD), 
        'roof': const Color(0xFF9E9E9E)
      },
    },
    'dark': {
      'commercial': {
        'wall': const Color(0xFF5C6BC0).withAlpha(220), 
        'roof': const Color(0xFF3F51B5).withAlpha(220)
      },
      'residential': {
        'wall': const Color(0xFFFF9800).withAlpha(220), 
        'roof': const Color(0xFFF57C00).withAlpha(220)
      },
      'office': {
        'wall': const Color(0xFF0288D1).withAlpha(220), 
        'roof': const Color(0xFF0277BD).withAlpha(220)
      },
      'industrial': {
        'wall': const Color(0xFF546E7A).withAlpha(220), 
        'roof': const Color(0xFF455A64).withAlpha(220)
      },
      'education': {
        'wall': const Color(0xFF388E3C).withAlpha(220), 
        'roof': const Color(0xFF2E7D32).withAlpha(220)
      },
      'healthcare': {
        'wall': const Color(0xFFD32F2F).withAlpha(220), 
        'roof': const Color(0xFFC62828).withAlpha(220)
      },
      'public': {
        'wall': const Color(0xFF8E24AA).withAlpha(220), 
        'roof': const Color(0xFF7B1FA2).withAlpha(220)
      },
      'historic': {
        'wall': const Color(0xFFAA8E57).withAlpha(220), 
        'roof': const Color(0xFF8D6E3A).withAlpha(220)
      },
      'default': {
        'wall': const Color(0xFF616161).withAlpha(220), 
        'roof': const Color(0xFF424242).withAlpha(220)
      },
    },
    // Monochrome Uber-like theme
    'monochrome': {
      'commercial': {
        'wall': const Color(0xFF374151), 
        'roof': const Color(0xFF1F2937)
      },
      'residential': {
        'wall': const Color(0xFF4B5563), 
        'roof': const Color(0xFF374151)
      },
      'office': {
        'wall': const Color(0xFF1F2937), 
        'roof': const Color(0xFF111827)
      },
      'industrial': {
        'wall': const Color(0xFF6B7280), 
        'roof': const Color(0xFF4B5563)
      },
      'education': {
        'wall': const Color(0xFF374151), 
        'roof': const Color(0xFF1F2937)
      },
      'healthcare': {
        'wall': const Color(0xFF4B5563), 
        'roof': const Color(0xFF374151)
      },
      'public': {
        'wall': const Color(0xFF6B7280), 
        'roof': const Color(0xFF4B5563)
      },
      'historic': {
        'wall': const Color(0xFF1F2937), 
        'roof': const Color(0xFF111827)
      },
      'default': {
        'wall': const Color(0xFF4B5563), 
        'roof': const Color(0xFF374151)
      },
    },
  };
  
  // Rooftop colors for different themes
  final Map<String, Map<String, Color>> _rooftopColors = {
    'vibrant': {
      'default': const Color(0xFFCFCFCF),   // Lighter gray for roofs
      'commercial': const Color(0xFFCE93D8), // Darker purple for commercial
      'residential': const Color(0xFFFFAB91), // Darker peach for residential
      'office': const Color(0xFF80CBC4),    // Darker teal for office
      'industrial': const Color(0xFFFFCA28), // Darker amber for industrial
      'retail': const Color(0xFFF48FB1),    // Darker pink for retail
      'public': const Color(0xFF64B5F6),    // Darker blue for public
      'education': const Color(0xFFAED581), // Darker green for education
      'healthcare': const Color(0xFFEC407A), // Bright pink for healthcare
      'historic': const Color(0xFFDCE775),  // Darker lime for historic
    },
    'dark': {
      'default': const Color(0xFF616161),   // Dark gray for roofs
      'commercial': const Color(0xFF6A1B9A).withOpacity(0.9),  // Very dark purple
      'residential': const Color(0xFFE64A19).withOpacity(0.9),  // Dark orange
      'office': const Color(0xFF00897B).withOpacity(0.9),     // Dark teal
      'industrial': const Color(0xFFFF8F00).withOpacity(0.9),  // Dark amber
      'retail': const Color(0xFFC2185B).withOpacity(0.9),     // Dark pink
      'public': const Color(0xFF1565C0).withOpacity(0.9),     // Dark blue
      'education': const Color(0xFF558B2F).withOpacity(0.9),  // Dark green
      'healthcare': const Color(0xFFC2185B).withOpacity(0.9),  // Dark pink
      'historic': const Color(0xFFAFB42B).withOpacity(0.9),   // Dark lime
    },
  };
  
  @override
  void initState() {
    super.initState();
    _currentTheme = widget.theme;
    _currentZoomBucket = _getZoomBucket(widget.zoomLevel);
    _fetchBuildings();
  }
  
  // Add a method to reset network errors and retry
  void retryAfterNetworkError() {
    if (_hasNetworkError) {
      setState(() {
        _hasNetworkError = false;
        _isLoading = true;
      });
      
      // Reset the error state in the data processor
      _dataProcessor.resetErrorState();
      
      // Try fetching again
      _fetchBuildings();
    }
  }
  
  // Get the zoom bucket (1-5) for current zoom level
  int _getZoomBucket(double zoom) {
    if (zoom < 8) return 1;
    if (zoom < 11) return 2;
    if (zoom < 14) return 3;
    if (zoom < 17) return 4;
    return 5;
  }
  
  @override
  void didUpdateWidget(OSMBuildingsLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update current theme if changed
    if (widget.theme != oldWidget.theme) {
      _currentTheme = widget.theme;
    }
    
    // Update zoom bucket if needed
    final newZoomBucket = _getZoomBucket(widget.zoomLevel);
    if (newZoomBucket != _currentZoomBucket) {
      _currentZoomBucket = newZoomBucket;
      _needsRefresh = true;
    }
    
    // Check if bounds have changed significantly
    bool boundsChanged = false;
    if (widget.visibleBounds != oldWidget.visibleBounds) {
      final newBoundsKey = _getBoundsKey();
      boundsChanged = newBoundsKey != _lastBoundsKey;
    }
    
    // Track if map has stopped moving
    final wasMoving = oldWidget.isMapMoving;
    final isMovingNow = widget.isMapMoving;
    final stoppedMoving = wasMoving && !isMovingNow;
    
    // Fetch new data if:
    // 1. Bounds changed significantly and we're not moving, or
    // 2. Map was moving and has now stopped and we've marked for refresh, or
    // 3. Zoom bucket changed
    if ((boundsChanged && !isMovingNow) || 
        (stoppedMoving && _needsRefresh) || 
        (newZoomBucket != _currentZoomBucket)) {
      
      // Schedule fetch after a short delay if we just stopped moving
      // This prevents too many fetches during fast interactions
      if (stoppedMoving) {
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted && _needsRefresh) {
            _fetchBuildings();
          }
        });
      } else {
        _fetchBuildings();
      }
    }
  }
  
  // Get a key to identify current map bounds, with reduced precision for fewer unnecessary refreshes
  String _getBoundsKey() {
    final sw = widget.visibleBounds.southWest;
    final ne = widget.visibleBounds.northEast;
    
    // Reduce precision for bounds (3 decimal places ≈ 100m accuracy)
    final key = 'bounds_${sw.latitude.toStringAsFixed(4)}_${sw.longitude.toStringAsFixed(4)}_${ne.latitude.toStringAsFixed(4)}_${ne.longitude.toStringAsFixed(4)}_${widget.zoomLevel.toStringAsFixed(1)}';
    return key;
  }
  
  // Check if bounds are similar enough to avoid unnecessary fetches
  bool _areBoundsSimilar(LatLngBounds bounds1, LatLngBounds bounds2) {
    // Calculate center points
    final LatLng center1 = LatLng(
      (bounds1.north + bounds1.south) / 2,
      (bounds1.east + bounds1.west) / 2,
    );
    final LatLng center2 = LatLng(
      (bounds2.north + bounds2.south) / 2,
      (bounds2.east + bounds2.west) / 2,
    );
    
    // Calculate distance between centers (in degrees)
    final double latDiff = (center1.latitude - center2.latitude).abs();
    final double lngDiff = (center1.longitude - center2.longitude).abs();
    
    // Calculate size of bounds (in degrees)
    final double bounds1Height = bounds1.north - bounds1.south;
    final double bounds1Width = bounds1.east - bounds1.west;
    
    // If centers differ by more than 30% of the bounds size, consider them different
    return latDiff < (bounds1Height * 0.3) && lngDiff < (bounds1Width * 0.3);
  }
  
  // Delay fetch to prevent excessive API calls during continuous panning/zooming
  void _delayedFetch() {
    // Adaptive delay based on zoom level - longer delay for higher zoom levels
    // as they require more detailed data and processing
    int delay = 300; // Base delay in milliseconds
    if (_currentZoomBucket >= 4) {
      delay = 500; // Longer delay for high zoom levels
    }
    
    Future.delayed(Duration(milliseconds: delay), () {
      if (mounted && _needsRefresh) {
        _fetchBuildings();
      }
    });
  }
  
  void _fetchBuildings() async {
    // Skip if we're at global view level where buildings aren't needed
    if (_currentZoomBucket <= 2 && widget.zoomLevel < 10) {
      setState(() {
        _buildings = [];
        _isLoading = false;
        _needsRefresh = false;
        _didInitialFetch = true;
        _hasNetworkError = false;
      });
      return;
    }
    
    setState(() {
      _isLoading = !_didInitialFetch; // Only show loading on first fetch
    });
    
    // Update bounds key
    _lastBoundsKey = _getBoundsKey();
    
    // Check if bounds or zoom has significantly changed
    bool shouldSkipFetch = false;
    if (_lastFetchedBounds != null && _lastFetchedZoom > 0) {
      final boundsDistance = _calculateBoundsDistance(widget.visibleBounds, _lastFetchedBounds!);
      final zoomDifference = (widget.zoomLevel - _lastFetchedZoom).abs();
      
      // If we're moving the map and have cached data, delay the fetch
      if (widget.isMapMoving && _buildings.isNotEmpty) {
        shouldSkipFetch = true;
      }
      // If bounds haven't changed much and zoom level is similar, use cached data
      else if (boundsDistance < 0.05 && zoomDifference < 1.0 && _buildings.isNotEmpty) {
        shouldSkipFetch = true;
      }
    }
    
    if (shouldSkipFetch) {
      setState(() {
        _isLoading = false;
        _needsRefresh = true; // Mark for refresh when map movement stops
      });
      return;
    }
    
    // Generate cache key for the MapCacheCoordinator
    final cacheKey = 'buildings_${widget.visibleBounds.southWest.latitude.toStringAsFixed(4)}_${widget.visibleBounds.southWest.longitude.toStringAsFixed(4)}_${widget.visibleBounds.northEast.latitude.toStringAsFixed(4)}_${widget.visibleBounds.northEast.longitude.toStringAsFixed(4)}_${_currentZoomBucket}';
    
    try {
      // Use MapCacheCoordinator to get data from cache or fetch from network
      final buildingsData = await MapCacheCoordinator().getData(
        type: MapDataType.buildings,
        key: cacheKey,
        southwest: widget.visibleBounds.southWest,
        northeast: widget.visibleBounds.northEast,
        zoomLevel: widget.zoomLevel,
        fetchIfMissing: () async {
          // Adapt detail level based on zoom bucket
          final detailLevel = _getDetailLevel(_currentZoomBucket);
          
          // Calculate a safe data request region based on Overpass API limits
          final safeRequestBounds = _calculateSafeRequestBounds(
            widget.visibleBounds,
            detailLevel,
            widget.zoomLevel
          );
          
          // Fetch building data with appropriate detail level
          return await _dataProcessor.fetchBuildingData(
            safeRequestBounds.southWest,
            safeRequestBounds.northEast,
            detailLevel: detailLevel
          );
        }
      );
    
      if (mounted) {
        setState(() {
          _buildings = buildingsData ?? [];
          _isLoading = false;
          _needsRefresh = false;
          _lastFetchedBounds = widget.visibleBounds;
          _lastFetchedZoom = widget.zoomLevel;
          _didInitialFetch = true;
          _hasNetworkError = false;
        });
      }
    } catch (e) {
      debugPrint('Error in OSMBuildingsLayer: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasNetworkError = true;
          // Keep existing buildings if we have them
          _didInitialFetch = true;
        });
      }
    }
  }
  
  // Calculate a distance metric between two bounds
  double _calculateBoundsDistance(LatLngBounds bounds1, LatLngBounds bounds2) {
    // Simple Euclidean distance between centers
    final center1 = LatLng(
      (bounds1.northEast.latitude + bounds1.southWest.latitude) / 2,
      (bounds1.northEast.longitude + bounds1.southWest.longitude) / 2
    );
    
    final center2 = LatLng(
      (bounds2.northEast.latitude + bounds2.southWest.latitude) / 2,
      (bounds2.northEast.longitude + bounds2.southWest.longitude) / 2
    );
    
    // Approximate using flat-earth model for small distances
    return math.sqrt(
      math.pow(center1.latitude - center2.latitude, 2) +
      math.pow(center1.longitude - center2.longitude, 2)
    );
  }
  
  // Calculate a safe request bounds that won't exceed Overpass API limits
  LatLngBounds _calculateSafeRequestBounds(LatLngBounds visibleBounds, double detailLevel, double zoomLevel) {
    final double latDelta = visibleBounds.northEast.latitude - visibleBounds.southWest.latitude;
    final double lonDelta = visibleBounds.northEast.longitude - visibleBounds.southWest.longitude;
    
    // Calculate center of visible bounds
    final LatLng center = LatLng(
      visibleBounds.southWest.latitude + latDelta * 0.5,
      visibleBounds.southWest.longitude + lonDelta * 0.5
    );
    
    // Adjust maximum query area based on zoom level
    // Higher zoom levels can query smaller areas with higher detail
    double maxAreaSize = 0.04; // Default max area (~ 4km)
    
    if (zoomLevel < 12) {
      // At low zoom levels, use lower detail but larger areas
      maxAreaSize = 0.1 * math.min(1.0, detailLevel);
    } else if (zoomLevel < 15) {
      // Medium zoom levels
      maxAreaSize = 0.06 * math.min(1.0, detailLevel);
    } else {
      // High zoom levels, use smaller areas for more detail
      maxAreaSize = 0.04 * math.min(1.0, detailLevel);
    }
    
    // If current area is too large, focus on a smaller region
    if (latDelta > maxAreaSize || lonDelta > maxAreaSize) {
      final double halfSize = maxAreaSize / 2;
      
      return LatLngBounds(
        LatLng(
          center.latitude - halfSize,
          center.longitude - halfSize
        ),
        LatLng(
          center.latitude + halfSize,
          center.longitude + halfSize
        )
      );
    }
    
    // Otherwise use the original bounds
    return visibleBounds;
  }
  
  // Get appropriate detail level for current zoom bucket
  double _getDetailLevel(int zoomBucket) {
    switch (zoomBucket) {
      case 1: return 0.2; // World - minimal detail
      case 2: return 0.4; // Continental - low detail
      case 3: return 0.6; // Regional - medium detail
      case 4: return 0.8; // Local - high detail
      case 5: return 1.0; // Fully zoomed - full detail
      default: return 0.6;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine effective theme (adapt to system theme or use explicit theme)
    final effectiveTheme = widget.theme == 'auto' 
        ? MediaQuery.of(context).platformBrightness == Brightness.dark ? 'dark' : 'vibrant'
        : widget.theme;
    
    // Optimize rendering based on zoom bucket
    final bool showDetailedBuildings = _currentZoomBucket >= 4;
    final bool showSimpleBuildings = _currentZoomBucket >= 3;
    
    // Don't render buildings at world or continental level unless zoomed in
    if (_currentZoomBucket <= 2 && widget.zoomLevel < 10) {
      return const SizedBox.shrink();
    }
    
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_isLoading && !_didInitialFetch) {
          // Show loading indicator on first load
          return Center(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
          );
        }
        
        // Show error overlay with retry button if there was a network error
        if (_hasNetworkError) {
          return Stack(
            children: [
              // Show existing buildings if we have them
              if (_buildings.isNotEmpty)
                CustomPaint(
                  size: Size(
                    constraints.maxWidth,
                    constraints.maxHeight,
                  ),
                  painter: OSMBuildingsPainter(
                    buildings: _buildings,
                    tiltFactor: widget.tiltFactor,
                    zoomLevel: widget.zoomLevel,
                    visibleBounds: widget.visibleBounds,
                    theme: effectiveTheme,
                    zoomBucket: _currentZoomBucket,
                    showDetails: showDetailedBuildings,
                  ),
                ),
              
              // Overlay with retry button
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'Network error loading map data',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: retryAfterNetworkError,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        }

        if (_buildings.isEmpty && _didInitialFetch) {
          // No buildings found but we did search
          return const SizedBox.shrink();
        }
    
        return CustomPaint(
          size: Size(
            constraints.maxWidth,
            constraints.maxHeight,
          ),
          painter: OSMBuildingsPainter(
            buildings: _buildings,
            tiltFactor: widget.tiltFactor,
            zoomLevel: widget.zoomLevel,
            visibleBounds: widget.visibleBounds,
            theme: effectiveTheme,
            zoomBucket: _currentZoomBucket,
            showDetails: showDetailedBuildings,
          ),
          // Add overlay for interactive building selection if needed
          child: widget.zoomLevel >= 17.0 ? GestureDetector(
            onTapDown: _handleTapDown,
            child: Container(color: Colors.transparent),
          ) : null,
        );
      },
    );
  }
  
  // Handle taps on buildings for future interactive features
  void _handleTapDown(TapDownDetails details) {
    // Get tap position
    final tapPosition = details.localPosition;
    
    // Find building under tap
    // This would be implemented to detect which building was tapped
    // for implementing selection, info display, etc.
    
    // For future implementation - can use hit testing with paths
  }
}

/// Custom painter to render buildings in 2.5D
class OSMBuildingsPainter extends CustomPainter {
  final List<Map<String, dynamic>> buildings;
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final int zoomBucket;
  final bool showDetails;
  final math.Random _random = math.Random(42); // Add random generator with fixed seed
  
  OSMBuildingsPainter({
    required this.buildings,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    required this.theme,
    required this.zoomBucket,
    required this.showDetails,
  });
  
  // Building color palettes for different themes
  final Map<String, Map<String, Map<String, Color>>> _buildingColorPalette = {
    'vibrant': {
      'commercial': {
        'wall': const Color(0xFF7986CB), 
        'roof': const Color(0xFF5C6BC0)
      },
      'residential': {
        'wall': const Color(0xFFFFB74D), 
        'roof': const Color(0xFFFF9800)
      },
      'office': {
        'wall': const Color(0xFF4FC3F7), 
        'roof': const Color(0xFF29B6F6)
      },
      'industrial': {
        'wall': const Color(0xFF90A4AE), 
        'roof': const Color(0xFF78909C)
      },
      'education': {
        'wall': const Color(0xFF81C784), 
        'roof': const Color(0xFF66BB6A)
      },
      'healthcare': {
        'wall': const Color(0xFFE57373), 
        'roof': const Color(0xFFEF5350)
      },
      'public': {
        'wall': const Color(0xFFBA68C8), 
        'roof': const Color(0xFFAB47BC)
      },
      'historic': {
        'wall': const Color(0xFFD4B178), 
        'roof': const Color(0xFFC19A57)
      },
      'default': {
        'wall': const Color(0xFFBDBDBD), 
        'roof': const Color(0xFF9E9E9E)
      },
    },
    // Add other themes here
  };

  @override
  void paint(Canvas canvas, Size size) {
    // Implement building rendering here
  }

  @override
  bool shouldRepaint(OSMBuildingsPainter oldDelegate) {
    return oldDelegate.buildings != buildings ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.visibleBounds != visibleBounds ||
           oldDelegate.theme != theme ||
           oldDelegate.zoomBucket != zoomBucket ||
           oldDelegate.showDetails != showDetails;
  }
} 