import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:async';

import './specialized_green_layer.dart';
import './data/specialized_green_data_provider.dart';

class SpecializedGreenWidget extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final bool isMapMoving;
  final String theme;
  final bool enhancedDetail;
  final MapCamera? mapCamera;

  const SpecializedGreenWidget({
    Key? key,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.theme = 'vibrant',
    this.enhancedDetail = true,
    this.mapCamera,
  }) : super(key: key);

  @override
  State<SpecializedGreenWidget> createState() => _SpecializedGreenWidgetState();
}

class _SpecializedGreenWidgetState extends State<SpecializedGreenWidget> {
  final SpecializedGreenDataProvider _dataProvider = SpecializedGreenDataProvider();
  Timer? _seasonTimer;
  String _season = 'default';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _updateSeason();
    _startSeasonalUpdates();
    _loadData();
  }

  @override
  void didUpdateWidget(SpecializedGreenWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.zoomLevel != widget.zoomLevel ||
        oldWidget.visibleBounds != widget.visibleBounds ||
        oldWidget.isMapMoving != widget.isMapMoving) {
      _loadData();
    }
  }

  /// Start timer for seasonal updates
  void _startSeasonalUpdates() {
    // Update season every 6 hours to match day/night cycle
    _seasonTimer = Timer.periodic(const Duration(hours: 6), (_) {
      _updateSeason();
    });
  }

  /// Update the current season based on date
  void _updateSeason() {
    final now = DateTime.now();
    final month = now.month;
    
    setState(() {
      // Simple season determination based on month
      if (month >= 3 && month <= 5) {
        _season = 'spring';
      } else if (month >= 6 && month <= 8) {
        _season = 'summer';
      } else if (month >= 9 && month <= 11) {
        _season = 'autumn';
      } else {
        _season = 'winter';
      }
    });
  }

  Future<void> _loadData() async {
    if (widget.zoomLevel < 12) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    await _dataProvider.loadData(widget.visibleBounds, widget.zoomLevel);
    
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _seasonTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox.shrink();
    }

    return CustomPaint(
      painter: SpecializedGreenLayer(
        season: _season,
        zoomLevel: widget.zoomLevel,
        visibleBounds: widget.visibleBounds,
        isMapMoving: widget.isMapMoving,
        enhancedDetail: widget.enhancedDetail,
        tiltFactor: widget.tiltFactor,
        theme: widget.theme,
        mapCamera: widget.mapCamera,
      ),
    );
  }
} 