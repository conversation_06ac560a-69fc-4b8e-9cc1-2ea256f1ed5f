import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

import '../models/specialized_green_feature.dart';
import '../utils/specialized_green_colors.dart';
import '../utils/specialized_green_geometry.dart';

/// Specialized renderer for nursery and greenhouse areas
class NurseryRenderer {
  final double zoom;
  final String season;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  
  final SpecializedGreenColors _colors = SpecializedGreenColors();
  final SpecializedGreenGeometry _geometry = SpecializedGreenGeometry();
  
  NurseryRenderer({
    required this.zoom,
    required this.season,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  });

  /// Render a nursery feature
  void render(Canvas canvas, Size size, SpecializedGreenFeature feature) {
    canvas.save();
    
    try {
      // Get feature colors
      final colors = _colors.getFeatureColors('nursery', theme, season);
      
      // Create base path
      final path = _geometry.createPath(feature.points, innerRings: feature.innerRings);
      final bounds = path.getBounds();
      
      // Draw base ground
      _renderBaseGround(canvas, path, bounds, colors);
      
      // Draw plant rows and beds
      _renderPlantBeds(canvas, feature, bounds, colors);
      
      // Draw structures if present
      if (feature.hasStructures == true) {
        _renderStructures(canvas, feature, bounds, colors);
      }
      
      // Draw detail elements if zoomed in enough
      if (zoom >= 16 && enhancedDetail) {
        _renderDetailElements(canvas, feature, bounds, colors);
      }
      
      // Draw boundary
      _renderBoundary(canvas, path, colors);
      
    } finally {
      canvas.restore();
    }
  }

  /// Render the base ground of the nursery
  void _renderBaseGround(Canvas canvas, Path path, Rect bounds, Map<String, Color> colors) {
    // Create ground gradient with slight texture
    final groundPaint = _colors.createGradientPaint(
      bounds,
      [
        colors['base']!,
        _colors.adjustBrightness(colors['base']!, 0.95),
        _colors.adjustBrightness(colors['base']!, 1.05),
      ],
      [0.0, 0.5, 1.0],
      isRadial: false,
    );
    
    // Add soil texture
    final textureShader = ui.Gradient.linear(
      bounds.topLeft,
      bounds.bottomRight,
      [
        Colors.black.withOpacity(0.05),
        Colors.black.withOpacity(0.02),
        Colors.black.withOpacity(0.04),
      ],
      [0.0, 0.5, 1.0],
    );
    
    // Draw base ground
    canvas.drawPath(path, groundPaint);
    canvas.drawPath(
      path,
      Paint()
        ..shader = textureShader
        ..blendMode = BlendMode.multiply,
    );
  }

  /// Render plant beds and rows
  void _renderPlantBeds(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate grid spacing based on zoom
    final spacing = math.max(10.0 - zoom / 2, 3.0);
    
    // Generate grid points for plant beds
    final points = _geometry.generateGrid(
      feature.points,
      spacing,
      rotation: random.nextDouble() * math.pi / 6 - math.pi / 12,
    );
    
    // Draw plant beds
    for (final point in points) {
      final size = 1.5 + random.nextDouble() * 1.0;
      final variation = _colors.addVariation(colors['plant']!);
      
      _drawPlantBed(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single plant bed
  void _drawPlantBed(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw organized plant arrangement
    final path = Path();
    
    // Create plant bed shape
    path.addRect(
      Rect.fromCenter(
        center: Offset.zero,
        width: size * 2,
        height: size * 0.8,
      ),
    );
    
    // Add plant details
    for (int i = 0; i < 3; i++) {
      final offset = Offset(
        (i - 1) * size * 0.8,
        0,
      );
      
      path.addOval(
        Rect.fromCenter(
          center: offset,
          width: size * 0.6,
          height: size * 0.6,
        ),
      );
    }
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render greenhouse and other structures
  void _renderStructures(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 1);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate structure density
    final density = math.min(area / 15000, 50) * (zoom / 16);
    final count = math.max((density * 3).round(), 2);
    
    // Generate points for structures
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw structures
    for (final point in points) {
      final size = 4.0 + random.nextDouble() * 3.0;
      final variation = _colors.addVariation(colors['structure']!);
      
      _drawStructure(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi / 6 - math.pi / 12,
      );
    }
  }

  /// Draw a single structure (greenhouse or shed)
  void _drawStructure(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw greenhouse structure
    final path = Path();
    
    // Base rectangle
    path.addRect(
      Rect.fromCenter(
        center: Offset.zero,
        width: size * 2,
        height: size,
      ),
    );
    
    // Roof
    path.moveTo(-size, -size / 2);
    path.lineTo(0, -size);
    path.lineTo(size, -size / 2);
    path.close();
    
    // Draw structure with shadow
    canvas.drawPath(
      path.shift(Offset(1, 1)),
      Paint()
        ..color = Colors.black.withOpacity(0.2)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 1),
    );
    
    canvas.drawPath(path, paint);
    
    // Add glass effect
    final glassPath = Path();
    for (int i = -1; i <= 1; i++) {
      glassPath.addRect(
        Rect.fromCenter(
          center: Offset(i * size * 0.5, 0),
          width: size * 0.3,
          height: size * 0.6,
        ),
      );
    }
    
    canvas.drawPath(
      glassPath,
      Paint()
        ..color = Colors.white.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.5,
    );
    
    canvas.restore();
  }

  /// Render detail elements
  void _renderDetailElements(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 2);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate detail density
    final density = math.min(area / 20000, 40) * (zoom / 16);
    final count = math.max((density * 4).round(), 4);
    
    // Generate points for details
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw detail elements
    for (final point in points) {
      final size = 1.0 + random.nextDouble() * 1.5;
      final variation = _colors.addVariation(colors['detail']!);
      
      _drawDetailElement(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single detail element
  void _drawDetailElement(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw gardening equipment or supplies
    final path = Path();
    
    // Create simple tool or pot shape
    path.addRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset.zero,
          width: size,
          height: size * 1.5,
        ),
        Radius.circular(size * 0.2),
      ),
    );
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render the boundary of the nursery
  void _renderBoundary(Canvas canvas, Path path, Map<String, Color> colors) {
    if (zoom >= 14) {
      canvas.drawPath(
        path,
        Paint()
          ..color = colors['detail']!.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5,
      );
    }
  }
} 