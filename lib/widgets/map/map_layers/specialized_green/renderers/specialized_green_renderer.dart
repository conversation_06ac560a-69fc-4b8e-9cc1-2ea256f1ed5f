import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

import '../models/specialized_green_feature.dart';

/// Renders specialized green features with optimizations for performance
class SpecializedGreenRenderer extends CustomPainter {
  final List<SpecializedGreenFeature> features;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final int zoomBucket;
  final MapCamera? mapCamera;
  final bool showDetails;

  // Performance settings
  static const int _maxFeaturesPerFrame = 500;
  static const double _minFeatureSize = 4.0;
  static const double _maxFeatureSize = 1000.0;

  // Rendering state
  final Paint _fillPaint = Paint()..style = PaintingStyle.fill;
  final Paint _strokePaint = Paint()..style = PaintingStyle.stroke;
  final Path _path = Path();
  
  // Performance tracking
  static int _lastFrameFeatureCount = 0;
  static Duration _lastFrameRenderTime = Duration.zero;

  SpecializedGreenRenderer({
    required this.features,
    required this.zoomLevel,
    required this.visibleBounds,
    required this.theme,
    required this.zoomBucket,
    this.mapCamera,
    this.showDetails = true,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final stopwatch = Stopwatch()..start();
    
    // Skip if no features or invalid state
    if (features.isEmpty || size.isEmpty) return;
    
    // Calculate view metrics
    final viewMetrics = _calculateViewMetrics(size);
    
    // Sort features by importance and filter visible ones
    final visibleFeatures = _getVisibleFeatures(viewMetrics);
    
    // Render features
    _renderFeatures(canvas, size, visibleFeatures, viewMetrics);
    
    // Update performance metrics
    _lastFrameFeatureCount = visibleFeatures.length;
    _lastFrameRenderTime = stopwatch.elapsed;
  }

  /// Calculate view metrics for optimized rendering
  _ViewMetrics _calculateViewMetrics(Size size) {
    // Calculate projection parameters
    final scale = math.pow(2.0, zoomLevel) * 256.0 / 360.0;
    final centerLat = (visibleBounds.north + visibleBounds.south) / 2;
    final centerLng = (visibleBounds.east + visibleBounds.west) / 2;
    
    // Calculate pixel coordinates
    final centerPoint = _latLngToPoint(LatLng(centerLat, centerLng), scale);
    final viewportSize = Size(
      (visibleBounds.east - visibleBounds.west) * scale,
      (visibleBounds.north - visibleBounds.south) * scale
    );
    
    return _ViewMetrics(
      scale: scale,
      centerPoint: centerPoint,
      viewportSize: viewportSize,
      detailLevel: _getDetailLevel(),
    );
  }

  /// Get visible and important features
  List<SpecializedGreenFeature> _getVisibleFeatures(_ViewMetrics metrics) {
    // Filter features that are potentially visible
    final candidates = features.where((feature) {
      // Skip if detail level doesn't match
      if (!feature.shouldRender(metrics.detailLevel)) return false;
      
      // Calculate screen bounds
      final bounds = _calculateFeatureBounds(feature, metrics);
      if (bounds == null) return false;
      
      // Check if feature is too small or too large
      final screenSize = bounds.width * bounds.height;
      if (screenSize < _minFeatureSize || screenSize > _maxFeatureSize) {
        return false;
      }
      
      return true;
    }).toList();
    
    // Sort by importance and limit count
    candidates.sort((a, b) {
      // Water features first
      if (a.hasWater != b.hasWater) {
        return a.hasWater ? -1 : 1;
      }
      
      // Then by size
      final aSize = a.size ?? 0.0;
      final bSize = b.size ?? 0.0;
      return bSize.compareTo(aSize);
    });
    
    return candidates.take(_maxFeaturesPerFrame).toList();
  }

  /// Render the features efficiently
  void _renderFeatures(
    Canvas canvas,
    Size size,
    List<SpecializedGreenFeature> visibleFeatures,
    _ViewMetrics metrics,
  ) {
    // Group features by type for efficient rendering
    final featuresByType = <SpecializedGreenType, List<SpecializedGreenFeature>>{};
    for (final feature in visibleFeatures) {
      featuresByType.putIfAbsent(feature.type, () => []).add(feature);
    }
    
    // Render each type group
    for (final type in featuresByType.keys) {
      final features = featuresByType[type]!;
      final baseColor = features.first.getColor(theme);
      
      // Setup paints
      _fillPaint
        ..color = baseColor
        ..style = PaintingStyle.fill;
      
      _strokePaint
        ..color = baseColor.withOpacity(0.8)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;
      
      // Render features of this type
      for (final feature in features) {
        _renderFeature(canvas, feature, metrics);
      }
    }
  }

  /// Render a single feature
  void _renderFeature(
    Canvas canvas,
    SpecializedGreenFeature feature,
    _ViewMetrics metrics,
  ) {
    // Convert points to screen coordinates
    final screenPoints = feature.points.map((p) {
      final point = _latLngToPoint(
        LatLng(p.dy, p.dx),
        metrics.scale
      );
      return Offset(point.x, point.y);
    }).toList();
    
    if (screenPoints.length < 3) return;
    
    // Build path
    _path.reset();
    _path.moveTo(screenPoints[0].dx, screenPoints[0].dy);
    for (int i = 1; i < screenPoints.length; i++) {
      _path.lineTo(screenPoints[i].dx, screenPoints[i].dy);
    }
    _path.close();
    
    // Add inner rings if any
    if (feature.innerRings != null) {
      for (final ring in feature.innerRings!) {
        final ringPoints = ring.map((p) {
          final point = _latLngToPoint(
            LatLng(p.dy, p.dx),
            metrics.scale
          );
          return Offset(point.x, point.y);
        }).toList();
        
        if (ringPoints.length < 3) continue;
        
        _path.moveTo(ringPoints[0].dx, ringPoints[0].dy);
        for (int i = 1; i < ringPoints.length; i++) {
          _path.lineTo(ringPoints[i].dx, ringPoints[i].dy);
        }
        _path.close();
      }
    }
    
    // Apply feature-specific styling
    if (feature.hasWater) {
      _fillPaint.color = _fillPaint.color.withOpacity(0.7);
    }
    
    // Draw feature
    canvas.drawPath(_path, _fillPaint);
    if (showDetails) {
      canvas.drawPath(_path, _strokePaint);
    }
  }

  /// Convert LatLng to screen point
  ui.Offset _latLngToPoint(LatLng latLng, double scale) {
    final x = (latLng.longitude + 180.0) * scale;
    final y = (1.0 - math.log(math.tan(latLng.latitude * math.pi / 180.0) + 
      1.0 / math.cos(latLng.latitude * math.pi / 180.0)) / math.pi) * scale * 256.0;
    return Offset(x, y);
  }

  /// Calculate screen bounds for a feature
  Rect? _calculateFeatureBounds(SpecializedGreenFeature feature, _ViewMetrics metrics) {
    if (feature.points.isEmpty) return null;
    
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    for (final point in feature.points) {
      final screenPoint = _latLngToPoint(
        LatLng(point.dy, point.dx),
        metrics.scale
      );
      
      minX = math.min(minX, screenPoint.dx);
      maxX = math.max(maxX, screenPoint.dx);
      minY = math.min(minY, screenPoint.dy);
      maxY = math.max(maxY, screenPoint.dy);
    }
    
    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }

  /// Get detail level based on zoom bucket
  double _getDetailLevel() {
    switch (zoomBucket) {
      case 1: return 0.2; // World view
      case 2: return 0.4; // Continental view
      case 3: return 0.6; // Regional view
      case 4: return 0.8; // Local view
      case 5: return 1.0; // Fully zoomed
      default: return 0.6;
    }
  }

  /// Get current performance metrics
  static Map<String, dynamic> getPerformanceMetrics() {
    return {
      'featuresRendered': _lastFrameFeatureCount,
      'renderTime': _lastFrameRenderTime.inMilliseconds,
    };
  }

  @override
  bool shouldRepaint(SpecializedGreenRenderer oldDelegate) {
    return features != oldDelegate.features ||
           zoomLevel != oldDelegate.zoomLevel ||
           visibleBounds != oldDelegate.visibleBounds ||
           theme != oldDelegate.theme ||
           zoomBucket != oldDelegate.zoomBucket ||
           showDetails != oldDelegate.showDetails;
  }
}

/// Helper class for view metrics
class _ViewMetrics {
  final double scale;
  final ui.Offset centerPoint;
  final Size viewportSize;
  final double detailLevel;

  _ViewMetrics({
    required this.scale,
    required this.centerPoint,
    required this.viewportSize,
    required this.detailLevel,
  });
} 