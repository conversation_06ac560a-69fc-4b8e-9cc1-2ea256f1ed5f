import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

import '../models/specialized_green_feature.dart';
import '../utils/specialized_green_colors.dart';
import '../utils/specialized_green_geometry.dart';

/// Specialized renderer for recreational areas like parks, playgrounds, and sports fields
class RecreationalRenderer {
  final double zoom;
  final String season;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  
  final SpecializedGreenColors _colors = SpecializedGreenColors();
  final SpecializedGreenGeometry _geometry = SpecializedGreenGeometry();
  
  RecreationalRenderer({
    required this.zoom,
    required this.season,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  });

  /// Render a recreational feature
  void render(Canvas canvas, Size size, SpecializedGreenFeature feature) {
    canvas.save();
    
    try {
      // Get feature colors
      final colors = _colors.getFeatureColors('recreational', theme, season);
      
      // Create base path
      final path = _geometry.createPath(feature.points, innerRings: feature.innerRings);
      final bounds = path.getBounds();
      
      // Draw base ground
      _renderBaseGround(canvas, path, bounds, colors);
      
      // Draw paths and walkways
      _renderPaths(canvas, feature, bounds, colors);
      
      // Draw sports fields if present
      if (feature.hasSportsFields == true) {
        _renderSportsFields(canvas, feature, bounds, colors);
      }
      
      // Draw playground equipment if present
      if (feature.hasPlayground == true) {
        _renderPlayground(canvas, feature, bounds, colors);
      }
      
      // Draw vegetation
      _renderVegetation(canvas, feature, bounds, colors);
      
      // Draw detail elements if zoomed in enough
      if (zoom >= 16 && enhancedDetail) {
        _renderDetailElements(canvas, feature, bounds, colors);
      }
      
      // Draw boundary
      _renderBoundary(canvas, path, colors);
      
    } finally {
      canvas.restore();
    }
  }

  /// Render the base ground of the recreational area
  void _renderBaseGround(Canvas canvas, Path path, Rect bounds, Map<String, Color> colors) {
    // Create ground gradient with grass texture
    final groundPaint = _colors.createGradientPaint(
      bounds,
      [
        colors['base']!,
        _colors.adjustBrightness(colors['base']!, 0.97),
        _colors.adjustBrightness(colors['base']!, 1.03),
      ],
      [0.0, 0.5, 1.0],
      isRadial: false,
    );
    
    // Add grass texture
    final textureShader = ui.Gradient.linear(
      bounds.topLeft,
      bounds.bottomRight,
      [
        Colors.black.withOpacity(0.03),
        Colors.black.withOpacity(0.01),
        Colors.black.withOpacity(0.02),
      ],
      [0.0, 0.5, 1.0],
    );
    
    // Draw base ground
    canvas.drawPath(path, groundPaint);
    canvas.drawPath(
      path,
      Paint()
        ..shader = textureShader
        ..blendMode = BlendMode.multiply,
    );
  }

  /// Render paths and walkways
  void _renderPaths(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode);
    final area = _geometry.calculateArea(feature.points);
    final center = _geometry.calculateCentroid(feature.points);
    
    // Calculate main path points
    final mainPathPoints = <Offset>[];
    
    // Create curved paths
    for (int i = 0; i < 6; i++) {
      final angle = (i * math.pi / 3) + random.nextDouble() * 0.3;
      final length = math.sqrt(area) * 0.3;
      
      mainPathPoints.add(Offset(
        center.dx + math.cos(angle) * length,
        center.dy + math.sin(angle) * length,
      ));
    }
    
    // Draw main paths with curves
    final mainPathPaint = Paint()
      ..color = colors['path']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    final path = Path()..moveTo(mainPathPoints[0].dx, mainPathPoints[0].dy);
    
    for (int i = 1; i <= mainPathPoints.length; i++) {
      final current = mainPathPoints[i % mainPathPoints.length];
      final previous = mainPathPoints[i - 1];
      final control = Offset(
        (previous.dx + current.dx) / 2 + random.nextDouble() * 10 - 5,
        (previous.dy + current.dy) / 2 + random.nextDouble() * 10 - 5,
      );
      
      path.quadraticBezierTo(
        control.dx,
        control.dy,
        current.dx,
        current.dy,
      );
    }
    
    canvas.drawPath(path, mainPathPaint);
  }

  /// Render sports fields
  void _renderSportsFields(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 1);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate field size based on area
    final fieldSize = math.min(math.sqrt(area) * 0.3, 30.0);
    final center = _geometry.calculateCentroid(feature.points);
    
    // Draw main field
    _drawSportsField(
      canvas,
      center,
      fieldSize,
      colors['field']!,
      random.nextDouble() * math.pi / 6 - math.pi / 12,
    );
    
    // Draw additional smaller fields if area permits
    if (area > 10000) {
      final smallFieldPoints = _geometry.generateRandomPoints(
        feature.points,
        2,
        exclusionZones: feature.innerRings,
      );
      
      for (final point in smallFieldPoints) {
        _drawSportsField(
          canvas,
          point,
          fieldSize * 0.6,
          colors['field']!,
          random.nextDouble() * math.pi / 6 - math.pi / 12,
        );
      }
    }
  }

  /// Draw a single sports field
  void _drawSportsField(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    // Draw field outline
    final fieldRect = Rect.fromCenter(
      center: Offset.zero,
      width: size * 2,
      height: size,
    );
    
    canvas.drawRect(fieldRect, paint);
    
    // Draw center line and circle
    canvas.drawLine(
      Offset(0, -size / 2),
      Offset(0, size / 2),
      paint,
    );
    
    canvas.drawCircle(
      Offset.zero,
      size * 0.15,
      paint,
    );
    
    // Draw goal areas
    final goalAreaWidth = size * 0.4;
    final goalAreaHeight = size * 0.3;
    
    for (final xOffset in [-size + goalAreaWidth / 2, size - goalAreaWidth / 2]) {
      canvas.drawRect(
        Rect.fromCenter(
          center: Offset(xOffset, 0),
          width: goalAreaWidth,
          height: goalAreaHeight,
        ),
        paint,
      );
    }
    
    canvas.restore();
  }

  /// Render playground equipment
  void _renderPlayground(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 2);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate playground density
    final density = math.min(area / 10000, 70) * (zoom / 16);
    final count = math.max((density * 5).round(), 5);
    
    // Generate points for playground equipment
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw playground equipment
    for (final point in points) {
      final size = 2.0 + random.nextDouble() * 2.0;
      final variation = _colors.addVariation(colors['equipment']!);
      
      _drawPlaygroundEquipment(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single piece of playground equipment
  void _drawPlaygroundEquipment(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw playground structure
    final path = Path();
    
    // Create simple playground structure shape
    path.addRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset.zero,
          width: size * 1.5,
          height: size,
        ),
        Radius.circular(size * 0.2),
      ),
    );
    
    // Add top structure
    path.moveTo(-size * 0.5, -size * 0.5);
    path.lineTo(0, -size);
    path.lineTo(size * 0.5, -size * 0.5);
    path.close();
    
    // Draw shadow
    canvas.drawPath(
      path.shift(Offset(0.5, 0.5)),
      Paint()
        ..color = Colors.black.withOpacity(0.2)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 1),
    );
    
    canvas.drawPath(path, paint);
    
    canvas.restore();
  }

  /// Render vegetation
  void _renderVegetation(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 3);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate vegetation density
    final density = math.min(area / 12000, 80) * (zoom / 16);
    final count = math.max((density * 6).round(), 6);
    
    // Generate points for vegetation
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw vegetation
    for (final point in points) {
      final size = 2.0 + random.nextDouble() * 2.0;
      final variation = _colors.addVariation(colors['vegetation']!);
      
      _drawVegetation(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single vegetation element
  void _drawVegetation(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw tree or bush shape
    final path = Path();
    
    // Create foliage
    for (int i = 0; i < 5; i++) {
      final angle = (i * math.pi * 2 / 5);
      path.addOval(
        Rect.fromCenter(
          center: Offset(
            math.cos(angle) * size * 0.3,
            math.sin(angle) * size * 0.3,
          ),
          width: size,
          height: size,
        ),
      );
    }
    
    // Add central foliage
    path.addOval(
      Rect.fromCenter(
        center: Offset.zero,
        width: size * 1.2,
        height: size * 1.2,
      ),
    );
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render detail elements
  void _renderDetailElements(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 4);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate detail density
    final density = math.min(area / 15000, 60) * (zoom / 16);
    final count = math.max((density * 5).round(), 5);
    
    // Generate points for details
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw detail elements
    for (final point in points) {
      final size = 1.0 + random.nextDouble() * 1.5;
      final variation = _colors.addVariation(colors['detail']!);
      
      _drawDetailElement(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single detail element
  void _drawDetailElement(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw park furniture (bench, table, etc.)
    final path = Path();
    
    // Create simple bench shape
    path.addRect(
      Rect.fromCenter(
        center: Offset(0, size * 0.3),
        width: size * 2,
        height: size * 0.4,
      ),
    );
    
    // Add legs
    path.addRect(
      Rect.fromCenter(
        center: Offset(-size * 0.7, size * 0.6),
        width: size * 0.3,
        height: size * 0.4,
      ),
    );
    
    path.addRect(
      Rect.fromCenter(
        center: Offset(size * 0.7, size * 0.6),
        width: size * 0.3,
        height: size * 0.4,
      ),
    );
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render the boundary of the recreational area
  void _renderBoundary(Canvas canvas, Path path, Map<String, Color> colors) {
    if (zoom >= 14) {
      canvas.drawPath(
        path,
        Paint()
          ..color = colors['detail']!.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5,
      );
    }
  }
} 