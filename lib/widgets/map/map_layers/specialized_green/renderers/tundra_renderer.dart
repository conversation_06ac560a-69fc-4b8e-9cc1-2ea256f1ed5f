import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

import '../models/specialized_green_feature.dart';
import '../utils/specialized_green_colors.dart';
import '../utils/specialized_green_geometry.dart';

/// Specialized renderer for tundra and arctic vegetation areas
class TundraRenderer {
  final double zoom;
  final String season;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  
  final SpecializedGreenColors _colors = SpecializedGreenColors();
  final SpecializedGreenGeometry _geometry = SpecializedGreenGeometry();
  
  TundraRenderer({
    required this.zoom,
    required this.season,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  });

  /// Render a tundra feature
  void render(Canvas canvas, Size size, SpecializedGreenFeature feature) {
    canvas.save();
    
    try {
      // Get feature colors
      final colors = _colors.getFeatureColors('tundra', theme, season);
      
      // Create base path
      final path = _geometry.createPath(feature.points, innerRings: feature.innerRings);
      final bounds = path.getBounds();
      
      // Draw base terrain
      _renderBaseTerrain(canvas, path, bounds, colors);
      
      // Draw rock outcrops
      _renderRockOutcrops(canvas, feature, bounds, colors);
      
      // Draw tundra vegetation
      _renderTundraVegetation(canvas, feature, bounds, colors);
      
      // Draw lichen patches if zoomed in enough
      if (zoom >= 16 && enhancedDetail) {
        _renderLichenPatches(canvas, feature, bounds, colors);
      }
      
      // Draw snow patches in winter
      if (season == 'winter') {
        _renderSnowPatches(canvas, feature, bounds);
      }
      
      // Draw boundary
      _renderBoundary(canvas, path, colors);
      
    } finally {
      canvas.restore();
    }
  }

  /// Render the base terrain of the tundra
  void _renderBaseTerrain(Canvas canvas, Path path, Rect bounds, Map<String, Color> colors) {
    // Create terrain gradient with permafrost texture
    final terrainPaint = _colors.createGradientPaint(
      bounds,
      [
        colors['base']!,
        _colors.adjustBrightness(colors['base']!, 0.97),
        _colors.adjustBrightness(colors['base']!, 1.03),
      ],
      [0.0, 0.4, 1.0],
      isRadial: false,
    );
    
    // Add permafrost texture
    final textureShader = ui.Gradient.linear(
      bounds.topLeft,
      bounds.bottomRight,
      [
        Colors.white.withOpacity(0.05),
        Colors.white.withOpacity(0.0),
        Colors.white.withOpacity(0.03),
        Colors.white.withOpacity(0.0),
      ],
      [0.0, 0.3, 0.7, 1.0],
    );
    
    // Draw base terrain
    canvas.drawPath(path, terrainPaint);
    canvas.drawPath(
      path,
      Paint()
        ..shader = textureShader
        ..blendMode = BlendMode.screen,
    );
  }

  /// Render rock outcrops
  void _renderRockOutcrops(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate rock density based on zoom and area
    final density = math.min(area / 10000, 90) * (zoom / 16);
    final count = math.max((density * 7).round(), 7);
    
    // Generate points for rocks
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw rock outcrops
    for (final point in points) {
      final size = 2.5 + random.nextDouble() * 3.5;
      final variation = _colors.addVariation(colors['rock']!);
      
      _drawRockOutcrop(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single rock outcrop
  void _drawRockOutcrop(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw angular rock shape
    final path = Path();
    final random = math.Random(color.value);
    
    // Create irregular polygon for rock
    final points = <Offset>[];
    final numPoints = 6 + random.nextInt(3);
    
    for (int i = 0; i < numPoints; i++) {
      final angle = (i * math.pi * 2 / numPoints) + (random.nextDouble() * 0.3);
      final radius = size * (0.8 + random.nextDouble() * 0.4);
      points.add(Offset(
        math.cos(angle) * radius,
        math.sin(angle) * radius,
      ));
    }
    
    // Create rock path
    path.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    path.close();
    
    // Add some texture/highlights
    final highlight = _colors.adjustBrightness(color, 1.2);
    final shadow = _colors.adjustBrightness(color, 0.8);
    
    // Draw base rock
    canvas.drawPath(path, paint);
    
    // Add highlights and shadows
    for (int i = 0; i < 3; i++) {
      final highlightPath = Path();
      final shadowPath = Path();
      
      final startAngle = random.nextDouble() * math.pi * 2;
      final sweepAngle = math.pi / 6 + random.nextDouble() * math.pi / 6;
      
      highlightPath.addArc(
        Rect.fromCircle(center: Offset.zero, radius: size),
        startAngle,
        sweepAngle,
      );
      
      shadowPath.addArc(
        Rect.fromCircle(center: Offset.zero, radius: size),
        startAngle + math.pi,
        sweepAngle,
      );
      
      canvas.drawPath(
        highlightPath,
        Paint()
          ..color = highlight.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0,
      );
      
      canvas.drawPath(
        shadowPath,
        Paint()
          ..color = shadow.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0,
      );
    }
    
    canvas.restore();
  }

  /// Render tundra vegetation
  void _renderTundraVegetation(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 1);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate vegetation density based on season and zoom
    double densityMultiplier = 1.0;
    if (season == 'winter') densityMultiplier = 0.6;
    if (season == 'summer') densityMultiplier = 1.2;
    
    final density = math.min(area / 12000, 100) * (zoom / 16) * densityMultiplier;
    final count = math.max((density * 8).round(), 8);
    
    // Generate points for vegetation
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw vegetation patches
    for (final point in points) {
      final size = 1.5 + random.nextDouble() * 2.0;
      final variation = _colors.addVariation(colors['vegetation']!);
      
      _drawTundraVegetation(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single tundra vegetation patch
  void _drawTundraVegetation(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw low-growing vegetation cluster
    final path = Path();
    
    // Create small, dense cluster of leaves/stems
    for (int i = 0; i < 6; i++) {
      final angle = (i * math.pi / 3);
      path.addOval(
        Rect.fromCenter(
          center: Offset(
            math.cos(angle) * size * 0.3,
            math.sin(angle) * size * 0.3,
          ),
          width: size * 0.8,
          height: size * 0.6,
        ),
      );
    }
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render lichen patches
  void _renderLichenPatches(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 2);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate lichen density
    final density = math.min(area / 15000, 70) * (zoom / 16);
    final count = math.max((density * 6).round(), 6);
    
    // Generate points for lichen
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw lichen patches
    for (final point in points) {
      final size = 1.0 + random.nextDouble() * 1.5;
      final variation = _colors.addVariation(colors['highlight']!);
      
      _drawLichenPatch(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single lichen patch
  void _drawLichenPatch(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color.withOpacity(0.7)
      ..style = PaintingStyle.fill;
    
    // Draw branching lichen pattern
    final path = Path();
    final random = math.Random(color.value);
    
    // Create branching structure
    void addBranch(Offset start, double angle, double length, int depth) {
      if (depth <= 0) return;
      
      final end = Offset(
        start.dx + math.cos(angle) * length,
        start.dy + math.sin(angle) * length,
      );
      
      path.moveTo(start.dx, start.dy);
      path.lineTo(end.dx, end.dy);
      
      // Add side branches
      final branchAngle = math.pi / 4 + random.nextDouble() * math.pi / 4;
      addBranch(end, angle + branchAngle, length * 0.7, depth - 1);
      addBranch(end, angle - branchAngle, length * 0.7, depth - 1);
    }
    
    // Create several main branches
    for (int i = 0; i < 4; i++) {
      final angle = (i * math.pi / 2) + random.nextDouble() * 0.5;
      addBranch(Offset.zero, angle, size, 3);
    }
    
    // Draw with some thickness
    canvas.drawPath(
      path,
      Paint()
        ..color = color.withOpacity(0.7)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.5,
    );
    
    canvas.restore();
  }

  /// Render snow patches in winter
  void _renderSnowPatches(Canvas canvas, SpecializedGreenFeature feature, Rect bounds) {
    final random = math.Random(feature.id.hashCode + 3);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate snow patch density
    final density = math.min(area / 10000, 80) * (zoom / 16);
    final count = math.max((density * 5).round(), 5);
    
    // Generate points for snow patches
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw snow patches
    for (final point in points) {
      final size = 2.0 + random.nextDouble() * 3.0;
      
      _drawSnowPatch(
        canvas,
        point,
        size,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single snow patch
  void _drawSnowPatch(
    Canvas canvas,
    Offset center,
    double size,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.7)
      ..style = PaintingStyle.fill;
    
    // Create irregular snow patch shape
    final path = Path();
    final random = math.Random(center.hashCode);
    
    final points = <Offset>[];
    final numPoints = 8 + random.nextInt(4);
    
    for (int i = 0; i < numPoints; i++) {
      final angle = (i * math.pi * 2 / numPoints) + (random.nextDouble() * 0.4);
      final radius = size * (0.7 + random.nextDouble() * 0.6);
      points.add(Offset(
        math.cos(angle) * radius,
        math.sin(angle) * radius,
      ));
    }
    
    // Create smooth snow patch
    path.moveTo(points[0].dx, points[0].dy);
    for (int i = 0; i < points.length; i++) {
      final next = points[(i + 1) % points.length];
      final control = Offset(
        (points[i].dx + next.dx) / 2,
        (points[i].dy + next.dy) / 2,
      );
      path.quadraticBezierTo(control.dx, control.dy, next.dx, next.dy);
    }
    path.close();
    
    // Draw with soft edges
    canvas.drawPath(
      path,
      paint..maskFilter = MaskFilter.blur(BlurStyle.normal, 2),
    );
    
    canvas.restore();
  }

  /// Render the boundary of the tundra
  void _renderBoundary(Canvas canvas, Path path, Map<String, Color> colors) {
    if (zoom >= 14) {
      canvas.drawPath(
        path,
        Paint()
          ..color = colors['detail']!.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5,
      );
    }
  }
} 