import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

import '../models/specialized_green_feature.dart';
import '../utils/specialized_green_colors.dart';
import '../utils/specialized_green_geometry.dart';

/// Specialized renderer for cemetery and graveyard areas
class CemeteryRenderer {
  final double zoom;
  final String season;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  
  final SpecializedGreenColors _colors = SpecializedGreenColors();
  final SpecializedGreenGeometry _geometry = SpecializedGreenGeometry();
  
  CemeteryRenderer({
    required this.zoom,
    required this.season,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  });

  /// Render a cemetery feature
  void render(Canvas canvas, Size size, SpecializedGreenFeature feature) {
    canvas.save();
    
    try {
      // Get feature colors
      final colors = _colors.getFeatureColors('cemetery', theme, season);
      
      // Create base path
      final path = _geometry.createPath(feature.points, innerRings: feature.innerRings);
      final bounds = path.getBounds();
      
      // Draw base ground
      _renderBaseGround(canvas, path, bounds, colors);
      
      // Draw paths
      _renderPaths(canvas, feature, bounds, colors);
      
      // Draw monuments and gravestones
      if (feature.hasMonuments == true) {
        _renderMonuments(canvas, feature, bounds, colors);
      }
      
      // Draw vegetation
      _renderVegetation(canvas, feature, bounds, colors);
      
      // Draw detail elements if zoomed in enough
      if (zoom >= 16 && enhancedDetail) {
        _renderDetailElements(canvas, feature, bounds, colors);
      }
      
      // Draw boundary
      _renderBoundary(canvas, path, colors);
      
    } finally {
      canvas.restore();
    }
  }

  /// Render the base ground of the cemetery
  void _renderBaseGround(Canvas canvas, Path path, Rect bounds, Map<String, Color> colors) {
    // Create ground gradient with grass texture
    final groundPaint = _colors.createGradientPaint(
      bounds,
      [
        colors['base']!,
        _colors.adjustBrightness(colors['base']!, 0.97),
        _colors.adjustBrightness(colors['base']!, 1.03),
      ],
      [0.0, 0.5, 1.0],
      isRadial: false,
    );
    
    // Add grass texture
    final textureShader = ui.Gradient.linear(
      bounds.topLeft,
      bounds.bottomRight,
      [
        Colors.black.withOpacity(0.03),
        Colors.black.withOpacity(0.01),
        Colors.black.withOpacity(0.02),
      ],
      [0.0, 0.5, 1.0],
    );
    
    // Draw base ground
    canvas.drawPath(path, groundPaint);
    canvas.drawPath(
      path,
      Paint()
        ..shader = textureShader
        ..blendMode = BlendMode.multiply,
    );
  }

  /// Render paths through the cemetery
  void _renderPaths(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate main path points
    final mainPathPoints = <Offset>[];
    final center = _geometry.calculateCentroid(feature.points);
    
    // Create main paths
    for (int i = 0; i < 4; i++) {
      final angle = (i * math.pi / 2) + random.nextDouble() * 0.3;
      final length = math.sqrt(area) * 0.4;
      
      mainPathPoints.add(Offset(
        center.dx + math.cos(angle) * length,
        center.dy + math.sin(angle) * length,
      ));
    }
    
    // Draw main paths
    final mainPathPaint = Paint()
      ..color = colors['path']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    for (int i = 0; i < mainPathPoints.length; i++) {
      final start = mainPathPoints[i];
      final end = mainPathPoints[(i + 1) % mainPathPoints.length];
      
      canvas.drawLine(start, end, mainPathPaint);
    }
    
    // Draw secondary paths if zoomed in enough
    if (zoom >= 15) {
      final secondaryPathPaint = Paint()
        ..color = colors['path']!.withOpacity(0.7)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;
      
      for (int i = 0; i < mainPathPoints.length; i++) {
        final start = mainPathPoints[i];
        final mid = Offset(
          (start.dx + center.dx) / 2,
          (start.dy + center.dy) / 2,
        );
        
        canvas.drawLine(start, mid, secondaryPathPaint);
      }
    }
  }

  /// Render monuments and gravestones
  void _renderMonuments(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 1);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate monument density based on zoom and area
    final density = math.min(area / 8000, 150) * (zoom / 16);
    final count = math.max((density * 10).round(), 10);
    
    // Generate points for monuments
    final points = _geometry.generateGrid(
      feature.points,
      5.0,
      rotation: random.nextDouble() * math.pi / 6 - math.pi / 12,
    );
    
    // Draw monuments
    for (final point in points) {
      final size = 1.5 + random.nextDouble() * 1.0;
      final variation = _colors.addVariation(colors['monument']!);
      
      _drawMonument(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi / 6 - math.pi / 12,
      );
    }
  }

  /// Draw a single monument or gravestone
  void _drawMonument(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw monument shape
    final path = Path();
    
    // Base
    path.addRect(
      Rect.fromCenter(
        center: Offset(0, size * 0.4),
        width: size * 1.2,
        height: size * 0.4,
      ),
    );
    
    // Main stone
    path.addRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset.zero,
          width: size,
          height: size * 1.6,
        ),
        Radius.circular(size * 0.1),
      ),
    );
    
    // Draw shadow
    canvas.drawPath(
      path.shift(Offset(0.5, 0.5)),
      Paint()
        ..color = Colors.black.withOpacity(0.2)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 1),
    );
    
    // Draw monument
    canvas.drawPath(path, paint);
    
    // Add detail lines
    if (zoom >= 16) {
      final detailPaint = Paint()
        ..color = _colors.adjustBrightness(color, 0.8)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.3;
      
      canvas.drawLine(
        Offset(-size * 0.3, -size * 0.4),
        Offset(size * 0.3, -size * 0.4),
        detailPaint,
      );
      
      canvas.drawLine(
        Offset(-size * 0.3, -size * 0.2),
        Offset(size * 0.3, -size * 0.2),
        detailPaint,
      );
    }
    
    canvas.restore();
  }

  /// Render vegetation
  void _renderVegetation(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 2);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate vegetation density based on zoom and area
    final density = math.min(area / 12000, 80) * (zoom / 16);
    final count = math.max((density * 6).round(), 6);
    
    // Generate points for vegetation
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw vegetation
    for (final point in points) {
      final size = 2.0 + random.nextDouble() * 2.0;
      final variation = _colors.addVariation(colors['base']!);
      
      _drawVegetation(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single vegetation element
  void _drawVegetation(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw tree or bush shape
    final path = Path();
    
    // Create foliage
    for (int i = 0; i < 5; i++) {
      final angle = (i * math.pi * 2 / 5);
      path.addOval(
        Rect.fromCenter(
          center: Offset(
            math.cos(angle) * size * 0.3,
            math.sin(angle) * size * 0.3,
          ),
          width: size,
          height: size,
        ),
      );
    }
    
    // Add central foliage
    path.addOval(
      Rect.fromCenter(
        center: Offset.zero,
        width: size * 1.2,
        height: size * 1.2,
      ),
    );
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render detail elements
  void _renderDetailElements(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 3);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate detail density
    final density = math.min(area / 15000, 60) * (zoom / 16);
    final count = math.max((density * 5).round(), 5);
    
    // Generate points for details
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw detail elements
    for (final point in points) {
      final size = 1.0 + random.nextDouble() * 1.5;
      final variation = _colors.addVariation(colors['detail']!);
      
      _drawDetailElement(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single detail element
  void _drawDetailElement(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw decorative element (flower, vase, etc.)
    final path = Path();
    
    // Create simple vase shape
    path.moveTo(-size * 0.3, size * 0.5);
    path.lineTo(-size * 0.2, -size * 0.5);
    path.lineTo(size * 0.2, -size * 0.5);
    path.lineTo(size * 0.3, size * 0.5);
    path.close();
    
    canvas.drawPath(path, paint);
    
    // Add flower if in spring or summer
    if (season == 'spring' || season == 'summer') {
      final flowerPaint = Paint()
        ..color = Colors.white.withOpacity(0.8)
        ..style = PaintingStyle.fill;
      
      for (int i = 0; i < 5; i++) {
        final angle = (i * math.pi * 2 / 5);
        canvas.drawCircle(
          Offset(
            math.cos(angle) * size * 0.2,
            -size * 0.7 + math.sin(angle) * size * 0.2,
          ),
          size * 0.15,
          flowerPaint,
        );
      }
    }
    
    canvas.restore();
  }

  /// Render the boundary of the cemetery
  void _renderBoundary(Canvas canvas, Path path, Map<String, Color> colors) {
    if (zoom >= 14) {
      canvas.drawPath(
        path,
        Paint()
          ..color = colors['detail']!.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5,
      );
    }
  }
} 