import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

import '../models/specialized_green_feature.dart';
import '../utils/specialized_green_colors.dart';
import '../utils/specialized_green_geometry.dart';

/// Specialized renderer for wetland areas
class WetlandRenderer {
  final double zoom;
  final String season;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  
  final SpecializedGreenColors _colors = SpecializedGreenColors();
  final SpecializedGreenGeometry _geometry = SpecializedGreenGeometry();
  
  WetlandRenderer({
    required this.zoom,
    required this.season,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  });

  /// Render a wetland feature
  void render(Canvas canvas, Size size, SpecializedGreenFeature feature) {
    canvas.save();
    
    try {
      // Get feature colors
      final colors = _colors.getFeatureColors('wetland', theme, season);
      
      // Create base path
      final path = _geometry.createPath(feature.points, innerRings: feature.innerRings);
      final bounds = path.getBounds();
      
      // Draw water base
      if (feature.hasWater == true) {
        _renderWaterBase(canvas, path, bounds, colors);
      }
      
      // Draw vegetation
      _renderVegetation(canvas, feature, bounds, colors);
      
      // Draw water ripples if applicable
      if (feature.hasWater == true && zoom >= 15) {
        _renderWaterRipples(canvas, feature, bounds, colors);
      }
      
      // Draw detail elements if zoomed in enough
      if (zoom >= 16 && enhancedDetail) {
        _renderDetailElements(canvas, feature, bounds, colors);
      }
      
      // Draw boundary
      _renderBoundary(canvas, path, colors);
      
    } finally {
      canvas.restore();
    }
  }

  /// Render the water base of the wetland
  void _renderWaterBase(Canvas canvas, Path path, Rect bounds, Map<String, Color> colors) {
    // Create water gradient
    final waterPaint = _colors.createGradientPaint(
      bounds,
      [
        colors['water']!,
        _colors.adjustBrightness(colors['water']!, 1.2),
      ],
      [0.0, 1.0],
      isRadial: false,
    );
    
    // Add some noise to the water
    final shader = ui.Gradient.linear(
      bounds.topLeft,
      bounds.bottomRight,
      [
        Colors.white.withOpacity(0.1),
        Colors.white.withOpacity(0.0),
        Colors.white.withOpacity(0.05),
      ],
      [0.0, 0.5, 1.0],
    );
    
    // Draw water base
    canvas.drawPath(path, waterPaint);
    canvas.drawPath(
      path,
      Paint()
        ..shader = shader
        ..blendMode = BlendMode.screen,
    );
  }

  /// Render vegetation in the wetland
  void _renderVegetation(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate vegetation density based on zoom and area
    final density = math.min(area / 10000, 100) * (zoom / 16);
    final count = math.max((density * 10).round(), 10);
    
    // Generate random points for vegetation
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw vegetation elements
    for (final point in points) {
      final size = 2.0 + random.nextDouble() * 3.0;
      final variation = _colors.addVariation(colors['vegetation']!);
      
      _drawVegetationElement(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single vegetation element
  void _drawVegetationElement(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw reed or grass-like shape
    final path = Path();
    path.moveTo(-size / 4, size);
    path.quadraticBezierTo(0, -size * 2, size / 4, size);
    path.close();
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render water ripples
  void _renderWaterRipples(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 1);
    final rippleCount = math.min((bounds.width * bounds.height / 1000).round(), 20);
    
    final paint = Paint()
      ..color = colors['water']!.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    for (int i = 0; i < rippleCount; i++) {
      final center = Offset(
        bounds.left + random.nextDouble() * bounds.width,
        bounds.top + random.nextDouble() * bounds.height,
      );
      
      // Check if center point is inside the polygon
      if (!_geometry.isPointInPolygon(center, feature.points)) continue;
      
      final radius = 2.0 + random.nextDouble() * 4.0;
      canvas.drawCircle(center, radius, paint);
    }
  }

  /// Render detail elements
  void _renderDetailElements(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 2);
    final detailCount = math.min((bounds.width * bounds.height / 2000).round(), 30);
    
    final points = _geometry.generateRandomPoints(
      feature.points,
      detailCount,
      exclusionZones: feature.innerRings,
    );
    
    for (final point in points) {
      final size = 1.0 + random.nextDouble() * 2.0;
      final variation = _colors.addVariation(colors['detail']!);
      
      // Draw small plant clusters or water plants
      _drawDetailElement(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single detail element
  void _drawDetailElement(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw a small cluster of leaves or water plants
    final path = Path();
    for (int i = 0; i < 3; i++) {
      final angle = (i * math.pi * 2 / 3);
      path.addOval(
        Rect.fromCenter(
          center: Offset(math.cos(angle) * size, math.sin(angle) * size),
          width: size,
          height: size * 2,
        ),
      );
    }
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render the boundary of the wetland
  void _renderBoundary(Canvas canvas, Path path, Map<String, Color> colors) {
    if (zoom >= 14) {
      canvas.drawPath(
        path,
        Paint()
          ..color = colors['detail']!.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5,
      );
    }
  }
} 