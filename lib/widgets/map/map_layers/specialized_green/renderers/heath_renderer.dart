import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

import '../models/specialized_green_feature.dart';
import '../utils/specialized_green_colors.dart';
import '../utils/specialized_green_geometry.dart';

/// Specialized renderer for heath and moorland areas
class HeathRenderer {
  final double zoom;
  final String season;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  
  final SpecializedGreenColors _colors = SpecializedGreenColors();
  final SpecializedGreenGeometry _geometry = SpecializedGreenGeometry();
  
  HeathRenderer({
    required this.zoom,
    required this.season,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  });

  /// Render a heath feature
  void render(Canvas canvas, Size size, SpecializedGreenFeature feature) {
    canvas.save();
    
    try {
      // Get feature colors
      final colors = _colors.getFeatureColors('heath', theme, season);
      
      // Create base path
      final path = _geometry.createPath(feature.points, innerRings: feature.innerRings);
      final bounds = path.getBounds();
      
      // Draw base terrain
      _renderBaseTerrain(canvas, path, bounds, colors);
      
      // Draw heather patches
      _renderHeatherPatches(canvas, feature, bounds, colors);
      
      // Draw flowering plants if in season
      if (season == 'summer' || season == 'spring') {
        _renderFloweringPlants(canvas, feature, bounds, colors);
      }
      
      // Draw detail elements if zoomed in enough
      if (zoom >= 16 && enhancedDetail) {
        _renderDetailElements(canvas, feature, bounds, colors);
      }
      
      // Draw boundary
      _renderBoundary(canvas, path, colors);
      
    } finally {
      canvas.restore();
    }
  }

  /// Render the base terrain of the heath
  void _renderBaseTerrain(Canvas canvas, Path path, Rect bounds, Map<String, Color> colors) {
    // Create terrain gradient with slight noise
    final terrainPaint = _colors.createGradientPaint(
      bounds,
      [
        colors['base']!,
        _colors.adjustBrightness(colors['base']!, 0.95),
        _colors.adjustBrightness(colors['base']!, 1.05),
      ],
      [0.0, 0.5, 1.0],
      isRadial: false,
    );
    
    // Add terrain texture
    final noiseShader = ui.Gradient.linear(
      bounds.topLeft,
      bounds.bottomRight,
      [
        Colors.black.withOpacity(0.05),
        Colors.black.withOpacity(0.0),
        Colors.black.withOpacity(0.03),
      ],
      [0.0, 0.5, 1.0],
    );
    
    // Draw base terrain
    canvas.drawPath(path, terrainPaint);
    canvas.drawPath(
      path,
      Paint()
        ..shader = noiseShader
        ..blendMode = BlendMode.multiply,
    );
  }

  /// Render heather patches
  void _renderHeatherPatches(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate patch density based on zoom and area
    final density = math.min(area / 8000, 120) * (zoom / 16);
    final count = math.max((density * 8).round(), 8);
    
    // Generate points for heather patches
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw heather patches
    for (final point in points) {
      final size = 3.0 + random.nextDouble() * 4.0;
      final variation = _colors.addVariation(colors['vegetation']!);
      
      _drawHeatherPatch(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single heather patch
  void _drawHeatherPatch(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw a cluster of small heather bushes
    final path = Path();
    for (int i = 0; i < 5; i++) {
      final angle = (i * math.pi * 2 / 5);
      final offset = Offset(
        math.cos(angle) * size * 0.5,
        math.sin(angle) * size * 0.5,
      );
      
      path.addOval(
        Rect.fromCenter(
          center: offset,
          width: size,
          height: size * 1.2,
        ),
      );
    }
    
    // Add central bush
    path.addOval(
      Rect.fromCenter(
        center: Offset.zero,
        width: size * 1.2,
        height: size * 1.4,
      ),
    );
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render flowering plants
  void _renderFloweringPlants(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 1);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate flower density based on season and zoom
    final density = math.min(area / 12000, 80) * (zoom / 16);
    final count = math.max((density * 6).round(), 6);
    
    // Generate points for flowers
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw flowers
    for (final point in points) {
      final size = 1.0 + random.nextDouble() * 1.5;
      final variation = _colors.addVariation(colors['flower']!);
      
      _drawFlower(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single flower
  void _drawFlower(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw flower petals
    final path = Path();
    for (int i = 0; i < 5; i++) {
      final angle = (i * math.pi * 2 / 5);
      path.addOval(
        Rect.fromCenter(
          center: Offset(
            math.cos(angle) * size * 0.5,
            math.sin(angle) * size * 0.5,
          ),
          width: size * 0.8,
          height: size * 0.4,
        ),
      );
    }
    
    // Draw flower center
    path.addOval(
      Rect.fromCenter(
        center: Offset.zero,
        width: size * 0.6,
        height: size * 0.6,
      ),
    );
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render detail elements
  void _renderDetailElements(
    Canvas canvas,
    SpecializedGreenFeature feature,
    Rect bounds,
    Map<String, Color> colors,
  ) {
    final random = math.Random(feature.id.hashCode + 2);
    final area = _geometry.calculateArea(feature.points);
    
    // Calculate detail density
    final density = math.min(area / 15000, 60) * (zoom / 16);
    final count = math.max((density * 5).round(), 5);
    
    // Generate points for details
    final points = _geometry.generateRandomPoints(
      feature.points,
      count,
      exclusionZones: feature.innerRings,
    );
    
    // Draw detail elements
    for (final point in points) {
      final size = 0.8 + random.nextDouble() * 1.2;
      final variation = _colors.addVariation(colors['detail']!);
      
      _drawDetailElement(
        canvas,
        point,
        size,
        variation,
        random.nextDouble() * math.pi,
      );
    }
  }

  /// Draw a single detail element
  void _drawDetailElement(
    Canvas canvas,
    Offset center,
    double size,
    Color color,
    double rotation,
  ) {
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    // Draw small grass or moss clump
    final path = Path();
    for (int i = 0; i < 4; i++) {
      final angle = (i * math.pi / 2);
      path.moveTo(0, 0);
      path.quadraticBezierTo(
        math.cos(angle) * size * 0.5,
        math.sin(angle) * size * 0.5,
        math.cos(angle) * size,
        math.sin(angle) * size,
      );
    }
    
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  /// Render the boundary of the heath
  void _renderBoundary(Canvas canvas, Path path, Map<String, Color> colors) {
    if (zoom >= 14) {
      canvas.drawPath(
        path,
        Paint()
          ..color = colors['detail']!.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5,
      );
    }
  }
} 