import 'package:flutter/material.dart';
import 'dart:ui' as ui;

import '../models/specialized_green_feature.dart';
import '../renderers/wetland_renderer.dart';
import '../renderers/heath_renderer.dart';
import '../renderers/tundra_renderer.dart';
import '../renderers/nursery_renderer.dart';
import '../renderers/cemetery_renderer.dart';
import '../renderers/recreational_renderer.dart';
import '../renderers/farmland_renderer.dart';
import '../utils/specialized_green_colors.dart';
import '../utils/specialized_green_geometry.dart';

/// Main painter for specialized green spaces that coordinates all the specialized renderers
class SpecializedGreenPainter extends CustomPainter {
  final List<SpecializedGreenFeature> features;
  final double zoom;
  final String season;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  
  // Specialized renderers
  late final WetlandRenderer _wetlandRenderer;
  late final HeathRenderer _heathRenderer;
  late final TundraRenderer _tundraRenderer;
  late final NurseryRenderer _nurseryRenderer;
  late final CemeteryRenderer _cemeteryRenderer;
  late final RecreationalRenderer _recreationalRenderer;
  late final FarmlandRenderer _farmlandRenderer;
  
  // Utility classes
  late final SpecializedGreenColors _colorsHelper;
  late final SpecializedGreenGeometry _geometryHelper;
  
  SpecializedGreenPainter({
    required this.features,
    required this.zoom,
    required this.season,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  }) {
    // Initialize renderers
    _wetlandRenderer = WetlandRenderer(
      zoom: zoom,
      season: season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _heathRenderer = HeathRenderer(
      zoom: zoom,
      season: season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _tundraRenderer = TundraRenderer(
      zoom: zoom,
      season: season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _nurseryRenderer = NurseryRenderer(
      zoom: zoom,
      season: season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _cemeteryRenderer = CemeteryRenderer(
      zoom: zoom,
      season: season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _recreationalRenderer = RecreationalRenderer(
      zoom: zoom,
      season: season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _farmlandRenderer = FarmlandRenderer(
      zoom: zoom,
      season: season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    // Initialize utility classes
    _colorsHelper = SpecializedGreenColors();
    _geometryHelper = SpecializedGreenGeometry();
  }

  @override
  void paint(Canvas canvas, Size size) {
    if (features.isEmpty) return;
    
    // Apply global canvas transformations if needed
    _applyGlobalTransforms(canvas, size);
    
    // Sort features by type and render each type with its specialized renderer
    for (final feature in features) {
      try {
        _renderFeature(canvas, size, feature);
      } catch (e) {
        print('Error rendering feature ${feature.id}: $e');
        continue;
      }
    }
    
    // Restore canvas state
    canvas.restore();
  }

  /// Apply any global canvas transformations needed
  void _applyGlobalTransforms(Canvas canvas, Size size) {
    canvas.save();
    
    // Apply global scaling if needed
    if (zoom < 14) {
      final scale = (zoom / 14).clamp(0.5, 1.0);
      canvas.scale(scale, scale);
    }
    
    // Apply tilt transformation for 2.5D effect
    if (tiltFactor > 0) {
      final matrix = Matrix4.identity()
        ..setEntry(3, 2, 0.001 * tiltFactor)
        ..rotateX(-0.2 * tiltFactor);
      canvas.transform(matrix.storage);
    }
  }

  /// Render a single feature using the appropriate specialized renderer
  void _renderFeature(Canvas canvas, Size size, SpecializedGreenFeature feature) {
    canvas.save();
    
    switch (feature.type) {
      case SpecializedGreenType.wetland:
      case SpecializedGreenType.bog:
        _wetlandRenderer.render(canvas, size, feature);
        break;
        
      case SpecializedGreenType.heath:
        _heathRenderer.render(canvas, size, feature);
        break;
        
      case SpecializedGreenType.tundra:
        _tundraRenderer.render(canvas, size, feature);
        break;
        
      case SpecializedGreenType.nursery:
      case SpecializedGreenType.greenhouse:
        _nurseryRenderer.render(canvas, size, feature);
        break;
        
      case SpecializedGreenType.cemetery:
        _cemeteryRenderer.render(canvas, size, feature);
        break;
        
      case SpecializedGreenType.dogPark:
      case SpecializedGreenType.villageGreen:
      case SpecializedGreenType.allotment:
      case SpecializedGreenType.recreationGround:
      case SpecializedGreenType.natureReserve:
        _recreationalRenderer.render(canvas, size, feature);
        break;

      case SpecializedGreenType.farmland:
      case SpecializedGreenType.orchard:
      case SpecializedGreenType.vineyard:
      case SpecializedGreenType.aquaculture:
        _farmlandRenderer.render(canvas, size, feature);
        break;
    }
    
    canvas.restore();
  }

  @override
  bool shouldRepaint(SpecializedGreenPainter oldDelegate) {
    return zoom != oldDelegate.zoom ||
           season != oldDelegate.season ||
           theme != oldDelegate.theme ||
           enhancedDetail != oldDelegate.enhancedDetail ||
           tiltFactor != oldDelegate.tiltFactor ||
           features != oldDelegate.features;
  }
} 