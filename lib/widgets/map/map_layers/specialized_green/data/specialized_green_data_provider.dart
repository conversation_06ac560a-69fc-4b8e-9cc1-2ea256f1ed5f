import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:math' as math;
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../models/specialized_green_feature.dart';
import '../../map_bounds.dart';
import '../../map_caching/map_cache_coordinator.dart';
import '../../../map_caching/osm_throttle_manager.dart';

/// Provides data for specialized green spaces by fetching and processing OSM data
class SpecializedGreenDataProvider {
  final OSMThrottleManager _throttleManager = OSMThrottleManager();
  List<SpecializedGreenFeature> _features = [];
  bool _hasData = false;
  bool _isLoading = false;
  bool _hasError = false;
  bool _needsRefresh = false;
  LatLngBounds? _lastFetchedBounds;
  double? _lastFetchedZoom;
  bool _didInitialFetch = false;
  
  // Track current zoom level bucket for optimized rendering
  int _currentZoomBucket = 3;
  
  // Callback for error handling
  final Function(String)? onError;
  
  // Current parameters
  double _zoomLevel = 0;
  LatLngBounds? _visibleBounds;
  bool _isMapMoving = false;
  
  final List<String> _overpassEndpoints = [
    'https://overpass-api.de/api/interpreter',
    'https://maps.mail.ru/osm/interpreter',
    'https://overpass.kumi.systems/api/interpreter'
  ];
  int _currentEndpointIndex = 0;

  SpecializedGreenDataProvider({
    this.onError,
    double initialZoom = 15,
    LatLngBounds? initialBounds,
    bool isMapMoving = false,
  }) {
    _zoomLevel = initialZoom;
    _visibleBounds = initialBounds;
    _isMapMoving = isMapMoving;
    _updateZoomBucket();
  }

  /// Get the current features
  List<SpecializedGreenFeature> get features => _features;
  
  /// Check if data is available
  bool get hasData => _hasData;
  
  /// Check if currently loading
  bool get isLoading => _isLoading;
  
  /// Check if there was an error
  bool get hasError => _hasError;
  
  /// Check if initial fetch was done
  bool get hasInitialData => _didInitialFetch;

  /// Update parameters and check if new data is needed
  void updateParameters({
    required double zoomLevel,
    required LatLngBounds visibleBounds,
    required bool isMapMoving,
  }) {
    final oldZoomBucket = _currentZoomBucket;
    
    _zoomLevel = zoomLevel;
    _visibleBounds = visibleBounds;
    _isMapMoving = isMapMoving;
    
    _updateZoomBucket();
    
    // If zoom bucket changed significantly, force a refresh
    if ((_currentZoomBucket - oldZoomBucket).abs() >= 2) {
      _needsRefresh = true;
    }
  }

  /// Update the current zoom bucket
  void _updateZoomBucket() {
    if (_zoomLevel < 6) _currentZoomBucket = 1;      // World view
    else if (_zoomLevel < 9) _currentZoomBucket = 2; // Continental view
    else if (_zoomLevel < 12) _currentZoomBucket = 3; // Country view
    else if (_zoomLevel < 15) _currentZoomBucket = 4; // Region view
    else _currentZoomBucket = 5;                      // Local view
  }

  /// Check if new data should be fetched
  bool shouldFetchNewData({
    required double oldZoomLevel,
    required LatLngBounds oldBounds,
    required bool oldIsMoving,
    required double newZoomLevel,
    required LatLngBounds newBounds,
    required bool newIsMoving,
  }) {
    // Always fetch if we need a refresh
    if (_needsRefresh) return true;
    
    // Skip if we're at too low zoom
    if (newZoomLevel < 7) return false;
    
    // Calculate how much the view has changed
    final zoomDiff = (oldZoomLevel - newZoomLevel).abs();
    final centerDiff = _calculateBoundsDistance(oldBounds, newBounds);
    
    // Fetch if significant changes occurred
    if (zoomDiff >= 1.0 || centerDiff >= 0.1) return true;
    
    // If map stopped moving, fetch to ensure data is up to date
    if (oldIsMoving && !newIsMoving) return true;
    
    return false;
  }

  /// Load data for the given bounds and zoom level
  Future<void> loadData(LatLngBounds bounds, double zoom) async {
    // Skip if at very low zoom level
    if (zoom < 7) {
      _features = [];
      _isLoading = false;
      _needsRefresh = false;
      _didInitialFetch = true;
      _hasError = false;
      return;
    }

    if (_isLoading) {
      debugPrint('Already loading specialized green data, skipping');
      return;
    }

    _isLoading = true;

    try {
      // Check if we can make a request
      final canRequest = await _throttleManager.canMakeRequest(
        'specialized_green',
        bounds
      );

      if (!canRequest) {
        debugPrint('Specialized green request throttled');
        _isLoading = false;
        _needsRefresh = true;
        return;
      }

      // Generate cache key
      final cacheKey = _getBoundsKey(bounds, zoom);
      debugPrint('Cache key: $cacheKey');

      // Get data from cache or fetch from network
      final data = await MapCacheCoordinator().getData(
        type: MapDataType.specializedGreen,
        key: cacheKey,
        southwest: bounds.southWest,
        northeast: bounds.northEast,
        zoomLevel: zoom,
        fetchIfMissing: () async {
          debugPrint('Fetching specialized green data from Overpass API');
          
          // Calculate safe request bounds
          final safeRequestBounds = _calculateSafeRequestBounds(bounds, zoom);
          
          // Build and execute Overpass query
          final query = _buildOverpassQuery(safeRequestBounds);
          
          // Try each endpoint with retries
          List<Map<String, dynamic>>? features;
          String? lastError;
          
          for (int endpointIndex = 0; endpointIndex < _overpassEndpoints.length; endpointIndex++) {
            final endpoint = _overpassEndpoints[(_currentEndpointIndex + endpointIndex) % _overpassEndpoints.length];
            
            // Try up to 3 times per endpoint
            for (int attempt = 0; attempt < 3; attempt++) {
              try {
                debugPrint('Sending request to $endpoint (attempt ${attempt + 1})');
                final response = await http.post(
                  Uri.parse(endpoint),
                  body: query,
                  headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'application/json',
                    'User-Agent': 'BOPMaps/1.0'
                  },
                ).timeout(const Duration(seconds: 25));

                if (response.statusCode == 200) {
                  debugPrint('Successfully received response from Overpass API');
                  final jsonData = json.decode(response.body);
                  final elements = jsonData['elements'] as List<dynamic>;
                  debugPrint('Received ${elements.length} elements');
                  
                  // Process elements into features with detail level filtering
                  features = [];
                  for (final element in elements) {
                    final feature = _processElement(element, _getDetailLevel(_currentZoomBucket));
                    if (feature != null) {
                      features.add(feature.toJson());
                    }
                  }
                  
                  // Update current endpoint index to the successful one
                  _currentEndpointIndex = (_currentEndpointIndex + endpointIndex) % _overpassEndpoints.length;
                  debugPrint('Processed ${features.length} valid features');
                  
                  // Apply feature limits based on zoom bucket
                  features = _limitFeaturesByZoomBucket(features, _currentZoomBucket);
                  return features;
                } else {
                  lastError = 'HTTP ${response.statusCode}: ${response.body}';
                  debugPrint('Error from endpoint $endpoint: $lastError');
                  
                  // Only wait between attempts if not the last attempt
                  if (attempt < 2) {
                    await Future.delayed(Duration(seconds: math.pow(2, attempt).toInt()));
                  }
                }
              } catch (e) {
                lastError = e.toString();
                debugPrint('Error with endpoint $endpoint: $lastError');
                
                // Only wait between attempts if not the last attempt
                if (attempt < 2) {
                  await Future.delayed(Duration(seconds: math.pow(2, attempt).toInt()));
                }
              }
            }
          }
          
          debugPrint('All endpoints failed. Last error: $lastError');
          _throttleManager.recordError('specialized_green', lastError ?? 'Unknown error');
          throw Exception('Failed to fetch data from all endpoints: $lastError');
        }
      );

      final processedFeatures = data?.map((d) => SpecializedGreenFeature.fromJson(d)).toList() ?? [];
      
      // Sort features by importance
      _features = _sortFeaturesByImportance(processedFeatures);
      _hasData = true;
      _isLoading = false;
      _needsRefresh = false;
      _lastFetchedBounds = bounds;
      _lastFetchedZoom = zoom;
      _didInitialFetch = true;
      _hasError = false;

      // Record successful request
      _throttleManager.recordRequest('specialized_green', bounds);
      
      debugPrint('Specialized green data loaded: ${_features.length} features');
    } catch (e) {
      debugPrint('Error loading specialized green data: $e');
      _features = [];
      _hasData = false;
      _isLoading = false;
      _hasError = true;
      _didInitialFetch = true;
      onError?.call(e.toString());
      _throttleManager.recordError('specialized_green', e.toString());
    }
  }

  /// Build Overpass query for specialized green spaces
  String _buildOverpassQuery(LatLngBounds bounds) {
    final bbox = '${bounds.south},${bounds.west},${bounds.north},${bounds.east}';
    debugPrint('Building Overpass query for bbox: $bbox');
    
    return '''[out:json][timeout:25];
(
  // Wetlands and natural areas
  way["natural"~"wetland|bog|heath|tundra|water"](${bbox});
  relation["natural"~"wetland|bog|heath|tundra|water"](${bbox});
  
  // Plant nurseries and greenhouses
  way["landuse"~"greenhouse_horticulture|plant_nursery"](${bbox});
  relation["landuse"~"greenhouse_horticulture|plant_nursery"](${bbox});
  
  // Cemeteries
  way["landuse"~"cemetery|graveyard"](${bbox});
  relation["landuse"~"cemetery|graveyard"](${bbox});
  
  // Recreational areas
  way["leisure"~"recreation_ground|park|playground|sports_centre"](${bbox});
  relation["leisure"~"recreation_ground|park|playground|sports_centre"](${bbox});
  
  // Farmland and agricultural areas - EXPANDED
  way["landuse"~"farmland|farm|orchard|vineyard|aquaculture|allotments|greenhouse|agriculture"](${bbox});
  relation["landuse"~"farmland|farm|orchard|vineyard|aquaculture|allotments|greenhouse|agriculture"](${bbox});
  way["landuse"="meadow"]["meadow"="agricultural"](${bbox});
  relation["landuse"="meadow"]["meadow"="agricultural"](${bbox});
  way["crop"](${bbox});
  relation["crop"](${bbox});
  way["produce"](${bbox});
  relation["produce"](${bbox});
  way["agriculture"](${bbox});
  relation["agriculture"](${bbox});
  way["farming"](${bbox});
  relation["farming"](${bbox});
  
  // Water bodies and waterways
  way["water"](${bbox});
  relation["water"](${bbox});
  way["waterway"](${bbox});
  relation["waterway"](${bbox});
);
out body geom;''';
  }

  /// Calculate safe request bounds that won't exceed API limits
  LatLngBounds _calculateSafeRequestBounds(LatLngBounds bounds, double zoom) {
    final double latDelta = bounds.north - bounds.south;
    final double lonDelta = bounds.east - bounds.west;
    
    // Maximum allowed area size based on zoom
    double maxAreaSize = 0.04; // Default ~4km at equator
    if (zoom < 12) maxAreaSize = 0.1;
    else if (zoom < 14) maxAreaSize = 0.06;
    
    if (latDelta > maxAreaSize || lonDelta > maxAreaSize) {
      final center = LatLng(
        (bounds.north + bounds.south) / 2,
        (bounds.east + bounds.west) / 2
      );
      
      final halfSize = maxAreaSize / 2;
      return LatLngBounds(
        LatLng(center.latitude - halfSize, center.longitude - halfSize),
        LatLng(center.latitude + halfSize, center.longitude + halfSize)
      );
    }
    
    return bounds;
  }

  /// Get detail level based on zoom bucket
  double _getDetailLevel(int zoomBucket) {
    switch (zoomBucket) {
      case 1: return 0.2; // World view - minimal detail
      case 2: return 0.4; // Continental view - low detail
      case 3: return 0.6; // Regional view - medium detail
      case 4: return 0.8; // Local view - high detail
      case 5: return 1.0; // Fully zoomed - full detail
      default: return 0.6;
    }
  }

  /// Limit features based on zoom bucket to prevent overwhelming the renderer
  List<Map<String, dynamic>> _limitFeaturesByZoomBucket(List<Map<String, dynamic>> features, int zoomBucket) {
    if (features.isEmpty) return features;
    
    int maxFeatures;
    switch (zoomBucket) {
      case 1: maxFeatures = 30;  // World view
      case 2: maxFeatures = 50;  // Continental view
      case 3: maxFeatures = 100; // Regional view
      case 4: maxFeatures = 200; // Local view
      case 5: maxFeatures = 500; // Fully zoomed
      default: maxFeatures = 100;
    }
    
    if (features.length > maxFeatures) {
      features.sort((a, b) {
        final aSize = a['size'] as double? ?? 0.0;
        final bSize = b['size'] as double? ?? 0.0;
        return bSize.compareTo(aSize); // Larger features first
      });
      return features.take(maxFeatures).toList();
    }
    
    return features;
  }

  /// Get a key to identify current map bounds
  String _getBoundsKey(LatLngBounds bounds, double zoom) {
    return 'specialized_green_${bounds.south.toStringAsFixed(4)}_${bounds.west.toStringAsFixed(4)}_${bounds.north.toStringAsFixed(4)}_${bounds.east.toStringAsFixed(4)}_${zoom.toStringAsFixed(1)}';
  }

  /// Calculate distance between two bounds centers
  double _calculateBoundsDistance(LatLngBounds a, LatLngBounds b) {
    final centerA = LatLng(
      (a.north + a.south) / 2,
      (a.east + a.west) / 2
    );
    final centerB = LatLng(
      (b.north + b.south) / 2,
      (b.east + b.west) / 2
    );
    
    return math.sqrt(
      math.pow(centerA.latitude - centerB.latitude, 2) +
      math.pow(centerA.longitude - centerB.longitude, 2)
    );
  }

  /// Process a single element from the OSM data
  SpecializedGreenFeature? _processElement(Map<String, dynamic> element, double detailLevel) {
    try {
      // Extract basic properties
      final id = element['id'].toString();
      final tags = element['tags'] as Map<String, dynamic>? ?? {};
      
      // Determine feature type
      SpecializedGreenType? type = _determineFeatureType(tags);
      if (type == null) return null;
      
      // Extract geometry
      List<Offset> points = [];
      List<List<Offset>>? innerRings;
      
      if (element['type'] == 'way' || element['type'] == 'relation') {
        final geometry = _extractGeometry(element);
        if (geometry == null) return null;
        
        points = geometry.points;
        innerRings = geometry.innerRings;
      }
      
      if (points.isEmpty) return null;
      
      // Calculate feature size for importance sorting
      final size = _calculateFeatureSize(points);
      
      // Create feature with additional properties
      return SpecializedGreenFeature(
        id: id,
        type: type,
        points: points,
        innerRings: innerRings,
        properties: _extractProperties(tags),
        tags: tags,
        hasWater: _hasWater(tags),
        hasStructures: _hasStructures(tags),
        hasMonuments: _hasMonuments(tags),
        hasSportsFields: _hasSportsFields(tags),
        hasPlayground: _hasPlayground(tags),
        size: size,
        detailLevel: detailLevel,
      );
    } catch (e) {
      debugPrint('Error processing element: $e');
      return null;
    }
  }

  /// Calculate the size of a feature for importance sorting
  double _calculateFeatureSize(List<Offset> points) {
    if (points.length < 3) return 0.0;
    
    double minX = points[0].dx;
    double maxX = points[0].dx;
    double minY = points[0].dy;
    double maxY = points[0].dy;
    
    for (final point in points) {
      minX = math.min(minX, point.dx);
      maxX = math.max(maxX, point.dx);
      minY = math.min(minY, point.dy);
      maxY = math.max(maxY, point.dy);
    }
    
    return (maxX - minX) * (maxY - minY);
  }

  /// Sort features by importance
  List<SpecializedGreenFeature> _sortFeaturesByImportance(List<SpecializedGreenFeature> features) {
    return features..sort((a, b) {
      // Water features get high priority
      if (a.hasWater && !b.hasWater) return -1;
      if (!a.hasWater && b.hasWater) return 1;
      
      // Then sort by size
      return (b.size ?? 0.0).compareTo(a.size ?? 0.0);
    });
  }

  /// Reset error state
  void resetErrorState() {
    _hasError = false;
  }

  /// Get current zoom bucket
  int getCurrentZoomBucket() => _currentZoomBucket;

  /// Clean up resources
  void dispose() {
    // Nothing to dispose currently
  }

  /// Determine the type of green space from OSM tags
  SpecializedGreenType? _determineFeatureType(Map<String, dynamic> tags) {
    // Natural features
    if (tags['natural'] == 'wetland') return SpecializedGreenType.wetland;
    if (tags['natural'] == 'bog') return SpecializedGreenType.bog;
    if (tags['natural'] == 'heath') return SpecializedGreenType.heath;
    if (tags['natural'] == 'tundra') return SpecializedGreenType.tundra;
    
    // Agricultural and farming - EXPANDED
    if (tags['landuse'] == 'farmland' || 
        tags['landuse'] == 'farm' ||
        tags['agriculture'] == 'yes' ||
        tags['farming'] == 'yes') {
      return SpecializedGreenType.farmland;
    }
    
    if (tags['landuse'] == 'orchard' || tags['produce'] == 'orchard') {
      return SpecializedGreenType.orchard;
    }
    
    if (tags['landuse'] == 'vineyard' || tags['produce'] == 'grape') {
      return SpecializedGreenType.vineyard;
    }
    
    if (tags['landuse'] == 'aquaculture' || 
        tags['produce'] == 'fish' ||
        tags['aquaculture'] == 'yes') {
      return SpecializedGreenType.aquaculture;
    }
    
    // Horticultural
    if (tags['landuse'] == 'greenhouse_horticulture' || 
        tags['landuse'] == 'plant_nursery' ||
        tags['landuse'] == 'greenhouse') {
      return SpecializedGreenType.nursery;
    }
    
    // Recreational and community
    if (tags['landuse'] == 'cemetery') return SpecializedGreenType.cemetery;
    if (tags['leisure'] == 'park' || 
        tags['leisure'] == 'playground' ||
        tags['leisure'] == 'sports_centre' ||
        tags['leisure'] == 'recreation_ground') {
      return SpecializedGreenType.recreationGround;
    }
    
    // Default to farmland if agricultural tags are present
    if (tags['crop'] != null || 
        tags['meadow'] == 'agricultural' ||
        tags['produce'] != null) {
      return SpecializedGreenType.farmland;
    }
    
    return null;
  }

  /// Extract geometry from an OSM element
  _Geometry? _extractGeometry(Map<String, dynamic> element) {
    try {
      if (element['type'] == 'way') {
        // Process way geometry
        final nodes = element['nodes'] as List<dynamic>;
        final points = nodes.map((node) {
          return Offset(
            _lon2x(node['lon'] as double),
            _lat2y(node['lat'] as double),
          );
        }).toList();
        
        return _Geometry(points: points);
      } else if (element['type'] == 'relation') {
        // Process relation geometry (multipolygon)
        final members = element['members'] as List<dynamic>;
        final outerRings = <List<Offset>>[];
        final innerRings = <List<Offset>>[];
        
        for (final member in members) {
          if (member['type'] == 'way') {
            final role = member['role'] as String;
            final nodes = member['nodes'] as List<dynamic>;
            final points = nodes.map((node) {
              return Offset(
                _lon2x(node['lon'] as double),
                _lat2y(node['lat'] as double),
              );
            }).toList();
            
            if (role == 'outer') {
              outerRings.add(points);
            } else if (role == 'inner') {
              innerRings.add(points);
            }
          }
        }
        
        if (outerRings.isNotEmpty) {
          return _Geometry(
            points: outerRings.first,
            innerRings: innerRings,
          );
        }
      }
    } catch (e) {
      debugPrint('Error extracting geometry: $e');
    }
    return null;
  }

  /// Extract properties from tags
  Map<String, dynamic> _extractProperties(Map<String, dynamic> tags) {
    final properties = <String, dynamic>{};
    
    // Extract common properties
    properties['name'] = tags['name'];
    properties['description'] = tags['description'];
    
    // Extract specific properties based on type
    if (tags['natural'] == 'wetland') {
      properties['wetland_type'] = tags['wetland'];
    } else if (tags['landuse'] == 'cemetery') {
      properties['religion'] = tags['religion'];
      properties['denomination'] = tags['denomination'];
    } else if (tags['leisure'] == 'sports_centre') {
      properties['sport'] = tags['sport'];
    } else if (tags['landuse'] == 'farmland' || tags['landuse'] == 'farm') {
      properties['crop_type'] = tags['crop'];
      properties['irrigation'] = tags['irrigation'];
      properties['fertilizer'] = tags['fertilizer'];
    } else if (tags['landuse'] == 'orchard') {
      properties['trees'] = tags['trees'];
      properties['produce'] = tags['produce'];
    } else if (tags['landuse'] == 'vineyard') {
      properties['grape_variety'] = tags['grape_variety'];
      properties['wine_region'] = tags['wine_region'];
    } else if (tags['landuse'] == 'aquaculture') {
      properties['produce'] = tags['produce'];
      properties['species'] = tags['species'];
    }
    
    return properties;
  }

  /// Check if feature has water elements
  bool _hasWater(Map<String, dynamic> tags) {
    return tags['natural'] == 'wetland' ||
           tags['water'] != null ||
           tags['waterway'] != null;
  }

  /// Check if feature has structures
  bool _hasStructures(Map<String, dynamic> tags) {
    return tags['building'] != null ||
           tags['landuse'] == 'greenhouse_horticulture' ||
           tags['amenity'] == 'shelter';
  }

  /// Check if feature has monuments
  bool _hasMonuments(Map<String, dynamic> tags) {
    return tags['landuse'] == 'cemetery' ||
           tags['historic'] == 'memorial' ||
           tags['memorial'] != null;
  }

  /// Check if feature has sports fields
  bool _hasSportsFields(Map<String, dynamic> tags) {
    return tags['leisure'] == 'sports_centre' ||
           tags['leisure'] == 'pitch' ||
           tags['sport'] != null;
  }

  /// Check if feature has playground equipment
  bool _hasPlayground(Map<String, dynamic> tags) {
    return tags['leisure'] == 'playground' ||
           tags['playground'] != null;
  }

  /// Convert longitude to x coordinate
  double _lon2x(double lon) {
    return (lon + 180) / 360;
  }

  /// Convert latitude to y coordinate
  double _lat2y(double lat) {
    final latRad = lat * math.pi / 180;
    return (1 - math.log(math.tan(latRad) + 1 / math.cos(latRad)) / math.pi) / 2;
  }
}

/// Helper class for geometry data
class _Geometry {
  final List<Offset> points;
  final List<List<Offset>>? innerRings;
  
  _Geometry({
    required this.points,
    this.innerRings,
  });
} 