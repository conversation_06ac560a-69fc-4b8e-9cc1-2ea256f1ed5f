import 'dart:ui';
import 'package:flutter/material.dart';

/// Types of specialized green spaces
enum SpecializedGreenType {
  wetland,
  bog,
  heath,
  tundra,
  nursery,
  cemetery,
  recreationGround,
  farmland,
  orchard,
  vineyard,
  aquaculture,
  water,
}

/// Model class for specialized green features
class SpecializedGreenFeature {
  final String id;
  final SpecializedGreenType type;
  final List<Offset> points;
  final List<List<Offset>>? innerRings;
  final Map<String, dynamic> properties;
  final Map<String, dynamic> tags;
  final bool hasWater;
  final bool hasStructures;
  final bool hasMonuments;
  final bool hasSportsFields;
  final bool hasPlayground;
  final double? size;  // Size of the feature for importance sorting
  final double detailLevel;  // Detail level for rendering optimization

  SpecializedGreenFeature({
    required this.id,
    required this.type,
    required this.points,
    this.innerRings,
    required this.properties,
    required this.tags,
    required this.hasWater,
    required this.hasStructures,
    required this.hasMonuments,
    required this.hasSportsFields,
    required this.hasPlayground,
    this.size,
    this.detailLevel = 1.0,
  });

  /// Convert feature to JSON for caching
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'points': points.map((p) => {'dx': p.dx, 'dy': p.dy}).toList(),
      'innerRings': innerRings?.map(
        (ring) => ring.map((p) => {'dx': p.dx, 'dy': p.dy}).toList()
      ).toList(),
      'properties': properties,
      'tags': tags,
      'hasWater': hasWater,
      'hasStructures': hasStructures,
      'hasMonuments': hasMonuments,
      'hasSportsFields': hasSportsFields,
      'hasPlayground': hasPlayground,
      'size': size,
      'detailLevel': detailLevel,
    };
  }

  /// Create feature from JSON data
  static SpecializedGreenFeature fromJson(Map<String, dynamic> json) {
    return SpecializedGreenFeature(
      id: json['id'] as String,
      type: SpecializedGreenType.values.firstWhere(
        (t) => t.toString() == json['type'],
        orElse: () => SpecializedGreenType.farmland
      ),
      points: (json['points'] as List<dynamic>).map((p) => 
        Offset(p['dx'] as double, p['dy'] as double)
      ).toList(),
      innerRings: (json['innerRings'] as List<dynamic>?)?.map((ring) =>
        (ring as List<dynamic>).map((p) =>
          Offset(p['dx'] as double, p['dy'] as double)
        ).toList()
      ).toList(),
      properties: json['properties'] as Map<String, dynamic>,
      tags: json['tags'] as Map<String, dynamic>,
      hasWater: json['hasWater'] as bool,
      hasStructures: json['hasStructures'] as bool,
      hasMonuments: json['hasMonuments'] as bool,
      hasSportsFields: json['hasSportsFields'] as bool,
      hasPlayground: json['hasPlayground'] as bool,
      size: json['size'] as double?,
      detailLevel: json['detailLevel'] as double? ?? 1.0,
    );
  }

  /// Get the color for this feature type
  Color getColor(String theme) {
    final isDark = theme == 'dark';
    
    switch (type) {
      case SpecializedGreenType.wetland:
        return isDark ? const Color(0xFF1A4D4D) : const Color(0xFF66B2B2);
      case SpecializedGreenType.bog:
        return isDark ? const Color(0xFF2D4D40) : const Color(0xFF8CB299);
      case SpecializedGreenType.heath:
        return isDark ? const Color(0xFF4D3D33) : const Color(0xFFB29980);
      case SpecializedGreenType.tundra:
        return isDark ? const Color(0xFF4D4D40) : const Color(0xFFB2B299);
      case SpecializedGreenType.nursery:
        return isDark ? const Color(0xFF336600) : const Color(0xFF99CC66);
      case SpecializedGreenType.cemetery:
        return isDark ? const Color(0xFF1A3300) : const Color(0xFF669933);
      case SpecializedGreenType.recreationGround:
        return isDark ? const Color(0xFF2D4D2D) : const Color(0xFF8CB28C);
      case SpecializedGreenType.farmland:
        return isDark ? const Color(0xFF4D4D1A) : const Color(0xFFB2B266);
      case SpecializedGreenType.orchard:
        return isDark ? const Color(0xFF334D1A) : const Color(0xFF99B266);
      case SpecializedGreenType.vineyard:
        return isDark ? const Color(0xFF4D1A4D) : const Color(0xFFB266B2);
      case SpecializedGreenType.aquaculture:
        return isDark ? const Color(0xFF1A334D) : const Color(0xFF6699B2);
      case SpecializedGreenType.water:
        return isDark ? const Color(0xFF1A3366) : const Color(0xFF6699CC);
    }
  }

  /// Get the pattern for this feature type
  Paint getPattern(String theme, {double scale = 1.0}) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = getColor(theme);

    // Apply patterns based on type
    if (hasWater) {
      // Water pattern
      paint.color = paint.color.withOpacity(0.7);
    }
    
    if (hasStructures) {
      // Structure pattern
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = 1.0 * scale;
    }

    return paint;
  }

  /// Get the border paint for this feature
  Paint getBorderPaint(String theme, {double scale = 1.0}) {
    return Paint()
      ..style = PaintingStyle.stroke
      ..color = getColor(theme).withOpacity(0.8)
      ..strokeWidth = 1.0 * scale;
  }

  /// Check if this feature should be rendered at current detail level
  bool shouldRender(double currentDetailLevel) {
    return detailLevel <= currentDetailLevel;
  }
}