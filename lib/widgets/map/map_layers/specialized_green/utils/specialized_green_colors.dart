import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Utility class for managing colors of specialized green spaces
class SpecializedGreenColors {
  // Color palettes for different themes and seasons
  Map<String, Map<String, Map<String, Map<String, Color>>>> get _colorPalettes => {
    'vibrant': {
      'default': {
        'farmland_cropland': {
          ...croplandColors,
        },
        'farmland_pasture': {
          ...pastureColors,
        },
        'farmland_orchard': {
          ...orchardColors,
        },
        'farmland_vineyard': {
          ...vineyardColors,
        },
        'farmland_rice': {
          ...riceFieldColors,
        },
        'farmland_aquaculture': {
          ...aquacultureColors,
        },
        'wetland': {
          'water': Color(0xFF4FC3F7),
          'vegetation': Color(0xFF81C784),
          'detail': Color(0xFF66BB6A),
          'highlight': Color(0xFFA5D6A7),
        },
        'heath': {
          'base': Color(0xFFAED581),
          'vegetation': Color(0xFF8BC34A),
          'flower': Color(0xFFE57373),
          'highlight': Color(0xFFDCEDC8),
        },
        'tundra': {
          'base': Color(0xFFE0E0E0),
          'vegetation': Color(0xFF9CCC65),
          'rock': Color(0xFF90A4AE),
          'highlight': Color(0xFFEEEEEE),
        },
        'nursery': {
          'base': Color(0xFF8BC34A),
          'plant': Color(0xFF66BB6A),
          'structure': Color(0xFFBDBDBD),
          'detail': Color(0xFF81C784),
        },
        'cemetery': {
          'base': Color(0xFF9CCC65),
          'path': Color(0xFFBDBDBD),
          'monument': Color(0xFF90A4AE),
          'detail': Color(0xFF78909C),
        },
        'recreational': {
          'base': Color(0xFF81C784),
          'path': Color(0xFFBDBDBD),
          'field': Color(0xFF66BB6A),
          'equipment': Color(0xFF90A4AE),
          'vegetation': Color(0xFF9CCC65),
          'detail': Color(0xFF78909C),
        },
      },
      'spring': {
        'wetland': {
          'water': Color(0xFF4FC3F7),
          'vegetation': Color(0xFF81C784),
          'detail': Color(0xFF66BB6A),
          'highlight': Color(0xFFA5D6A7),
        },
        'heath': {
          'base': Color(0xFFAED581),
          'vegetation': Color(0xFF8BC34A),
          'flower': Color(0xFFE57373),
          'highlight': Color(0xFFDCEDC8),
        },
        'tundra': {
          'base': Color(0xFFE0E0E0),
          'vegetation': Color(0xFF9CCC65),
          'rock': Color(0xFF90A4AE),
          'highlight': Color(0xFFEEEEEE),
        },
        'nursery': {
          'base': Color(0xFF8BC34A),
          'plant': Color(0xFF66BB6A),
          'structure': Color(0xFFBDBDBD),
          'detail': Color(0xFF81C784),
        },
        'cemetery': {
          'base': Color(0xFF9CCC65),
          'path': Color(0xFFBDBDBD),
          'monument': Color(0xFF90A4AE),
          'detail': Color(0xFF78909C),
        },
        'recreational': {
          'base': Color(0xFF81C784),
          'path': Color(0xFFBDBDBD),
          'field': Color(0xFF66BB6A),
          'equipment': Color(0xFF90A4AE),
          'vegetation': Color(0xFF9CCC65),
          'detail': Color(0xFF78909C),
        },
        'farmland_cropland': {
          'base': const Color(0xFFE6EE9C),       // Spring soil
          'crop': const Color(0xFF8BC34A),       // Young crops
          'highlight': const Color(0xFFDCE775),   // Spring highlight
          'boundary': const Color(0xFF9E9D24),    // Field boundary
          'spring': const Color(0xFF7CB342),      // Early growth
        },
        'farmland_pasture': {
          'base': const Color(0xFFDCE775),       // Spring grass base
          'grass': const Color(0xFF7CB342),       // Fresh grass
          'highlight': const Color(0xFFC5E1A5),   // Spring highlight
          'boundary': const Color(0xFF558B2F),    // Field boundary
          'spring': const Color(0xFF8BC34A),      // New growth
        },
        'farmland_orchard': {
          'base': const Color(0xFFE6EE9C),       // Spring ground
          'tree': const Color(0xFF8BC34A),       // Spring foliage
          'highlight': const Color(0xFFDCE775),   // Blossom highlight
          'boundary': const Color(0xFF689F38),    // Orchard boundary
          'spring': const Color(0xFFF8BBD0),      // Spring blossom
        },
        'farmland_vineyard': {
          'base': const Color(0xFFDCE775),       // Spring ground
          'vine': const Color(0xFF8BC34A),        // Spring vines
          'highlight': const Color(0xFFC5E1A5),   // Spring highlight
          'boundary': const Color(0xFF558B2F),    // Vineyard boundary
          'spring': const Color(0xFF7CB342),      // New growth
        },
        'farmland_rice': {
          'base': const Color(0xFF4FC3F7),       // Spring water
          'plant': const Color(0xFF81C784),       // Young rice
          'highlight': const Color(0xFF81D4FA),   // Water highlight
          'boundary': const Color(0xFF26A69A),    // Field boundary
          'spring': const Color(0xFF4DB6AC),      // Early growth
        },
        'farmland_aquaculture': {
          'base': const Color(0xFF4FC3F7),       // Spring water
          'water': const Color(0xFF29B6F6),       // Clear water
          'highlight': const Color(0xFF81D4FA),   // Water highlight
          'structure': const Color(0xFF90A4AE),   // Pond structure
          'boundary': const Color(0xFF0288D1),    // Pond boundary
        },
      },
      'summer': {
        'wetland': {
          'water': Color(0xFF29B6F6),
          'vegetation': Color(0xFF66BB6A),
          'detail': Color(0xFF4CAF50),
          'highlight': Color(0xFF81C784),
        },
        'heath': {
          'base': Color(0xFF9CCC65),
          'vegetation': Color(0xFF7CB342),
          'flower': Color(0xFFEF5350),
          'highlight': Color(0xFFC5E1A5),
        },
        'tundra': {
          'base': Color(0xFFE0E0E0),
          'vegetation': Color(0xFF8BC34A),
          'rock': Color(0xFF78909C),
          'highlight': Color(0xFFEEEEEE),
        },
        'nursery': {
          'base': Color(0xFF7CB342),
          'plant': Color(0xFF4CAF50),
          'structure': Color(0xFFBDBDBD),
          'detail': Color(0xFF66BB6A),
        },
        'cemetery': {
          'base': Color(0xFF8BC34A),
          'path': Color(0xFFBDBDBD),
          'monument': Color(0xFF78909C),
          'detail': Color(0xFF607D8B),
        },
        'recreational': {
          'base': Color(0xFF66BB6A),
          'path': Color(0xFFBDBDBD),
          'field': Color(0xFF4CAF50),
          'equipment': Color(0xFF78909C),
          'vegetation': Color(0xFF8BC34A),
          'detail': Color(0xFF607D8B),
        },
        'farmland_cropland': {
          'base': const Color(0xFFD4E157),       // Summer soil
          'crop': const Color(0xFF7CB342),       // Mature crops
          'highlight': const Color(0xFFC5E1A5),   // Summer highlight
          'boundary': const Color(0xFF827717),    // Field boundary
          'summer': const Color(0xFF558B2F),      // Full growth
        },
        'farmland_pasture': {
          'base': const Color(0xFFAED581),       // Summer grass base
          'grass': const Color(0xFF689F38),       // Mature grass
          'highlight': const Color(0xFF9CCC65),   // Summer highlight
          'boundary': const Color(0xFF33691E),    // Field boundary
          'summer': const Color(0xFF558B2F),      // Peak growth
        },
        'farmland_orchard': {
          'base': const Color(0xFFD4E157),       // Summer ground
          'tree': const Color(0xFF558B2F),       // Full foliage
          'highlight': const Color(0xFF9CCC65),   // Summer highlight
          'boundary': const Color(0xFF33691E),    // Orchard boundary
          'summer': const Color(0xFF2E7D32),      // Dense foliage
        },
        'farmland_vineyard': {
          'base': const Color(0xFFD4E157),       // Summer ground
          'vine': const Color(0xFF558B2F),        // Mature vines
          'highlight': const Color(0xFF9CCC65),   // Summer highlight
          'boundary': const Color(0xFF33691E),    // Vineyard boundary
          'summer': const Color(0xFF2E7D32),      // Full growth
        },
        'farmland_rice': {
          'base': const Color(0xFF29B6F6),       // Summer water
          'plant': const Color(0xFF66BB6A),       // Mature rice
          'highlight': const Color(0xFF81D4FA),   // Water highlight
          'boundary': const Color(0xFF00897B),    // Field boundary
          'summer': const Color(0xFF43A047),      // Full growth
        },
        'farmland_aquaculture': {
          'base': const Color(0xFF29B6F6),       // Summer water
          'water': const Color(0xFF0288D1),       // Deep water
          'highlight': const Color(0xFF4FC3F7),   // Water highlight
          'structure': const Color(0xFF78909C),   // Pond structure
          'boundary': const Color(0xFF0277BD),    // Pond boundary
        },
      },
      'autumn': {
        'wetland': {
          'water': Color(0xFF4FC3F7),
          'vegetation': Color(0xFFAED581),
          'detail': Color(0xFF8BC34A),
          'highlight': Color(0xFFC5E1A5),
        },
        'heath': {
          'base': Color(0xFFCDDC39),
          'vegetation': Color(0xFFAFB42B),
          'flower': Color(0xFFF44336),
          'highlight': Color(0xFFDCE775),
        },
        'tundra': {
          'base': Color(0xFFE0E0E0),
          'vegetation': Color(0xFFCDDC39),
          'rock': Color(0xFF90A4AE),
          'highlight': Color(0xFFEEEEEE),
        },
        'nursery': {
          'base': Color(0xFFAFB42B),
          'plant': Color(0xFF8BC34A),
          'structure': Color(0xFFBDBDBD),
          'detail': Color(0xFF9CCC65),
        },
        'cemetery': {
          'base': Color(0xFFCDDC39),
          'path': Color(0xFFBDBDBD),
          'monument': Color(0xFF90A4AE),
          'detail': Color(0xFF78909C),
        },
        'recreational': {
          'base': Color(0xFF9CCC65),
          'path': Color(0xFFBDBDBD),
          'field': Color(0xFF8BC34A),
          'equipment': Color(0xFF90A4AE),
          'vegetation': Color(0xFFCDDC39),
          'detail': Color(0xFF78909C),
        },
        'farmland_cropland': {
          'base': const Color(0xFFF0E68C),       // Autumn soil
          'crop': const Color(0xFFCDDC39),       // Harvest ready
          'highlight': const Color(0xFFE6EE9C),   // Autumn highlight
          'boundary': const Color(0xFF827717),    // Field boundary
          'autumn': const Color(0xFFFFEB3B),      // Harvest color
        },
        'farmland_pasture': {
          'base': const Color(0xFFDCE775),       // Autumn grass base
          'grass': const Color(0xFF9CCC65),       // Autumn grass
          'highlight': const Color(0xFFC5E1A5),   // Autumn highlight
          'boundary': const Color(0xFF558B2F),    // Field boundary
          'autumn': const Color(0xFFD4E157),      // Late season
        },
        'farmland_orchard': {
          'base': const Color(0xFFF0E68C),       // Autumn ground
          'tree': const Color(0xFFFFA726),       // Fall colors
          'highlight': const Color(0xFFFFCC80),   // Autumn highlight
          'boundary': const Color(0xFFF57C00),    // Orchard boundary
          'autumn': const Color(0xFFFF9800),      // Peak fall
        },
        'farmland_vineyard': {
          'base': const Color(0xFFF0E68C),       // Autumn ground
          'vine': const Color(0xFF8D6E63),        // Autumn vines
          'highlight': const Color(0xFFBCAAA4),   // Autumn highlight
          'boundary': const Color(0xFF5D4037),    // Vineyard boundary
          'autumn': const Color(0xFF795548),      // Harvest ready
        },
        'farmland_rice': {
          'base': const Color(0xFF81D4FA),       // Autumn water
          'plant': const Color(0xFFFFEB3B),       // Golden rice
          'highlight': const Color(0xFFFFF176),   // Harvest highlight
          'boundary': const Color(0xFF00897B),    // Field boundary
          'autumn': const Color(0xFFFDD835),      // Ready to harvest
        },
        'farmland_aquaculture': {
          'base': const Color(0xFF4FC3F7),       // Autumn water
          'water': const Color(0xFF0288D1),       // Deep water
          'highlight': const Color(0xFF81D4FA),   // Water highlight
          'structure': const Color(0xFF78909C),   // Pond structure
          'boundary': const Color(0xFF0277BD),    // Pond boundary
        },
      },
      'winter': {
        'wetland': {
          'water': Color(0xFF81D4FA),
          'vegetation': Color(0xFFC5E1A5),
          'detail': Color(0xFFAED581),
          'highlight': Color(0xFFDCEDC8),
        },
        'heath': {
          'base': Color(0xFFDCE775),
          'vegetation': Color(0xFFD4E157),
          'flower': Color(0xFFEF9A9A),
          'highlight': Color(0xFFE6EE9C),
        },
        'tundra': {
          'base': Color(0xFFF5F5F5),
          'vegetation': Color(0xFFDCE775),
          'rock': Color(0xFFB0BEC5),
          'highlight': Color(0xFFFFFFFF),
        },
        'nursery': {
          'base': Color(0xFFD4E157),
          'plant': Color(0xFFAED581),
          'structure': Color(0xFFE0E0E0),
          'detail': Color(0xFFC5E1A5),
        },
        'cemetery': {
          'base': Color(0xFFDCE775),
          'path': Color(0xFFE0E0E0),
          'monument': Color(0xFFB0BEC5),
          'detail': Color(0xFF90A4AE),
        },
        'recreational': {
          'base': Color(0xFFC5E1A5),
          'path': Color(0xFFE0E0E0),
          'field': Color(0xFFAED581),
          'equipment': Color(0xFFB0BEC5),
          'vegetation': Color(0xFFDCE775),
          'detail': Color(0xFF90A4AE),
        },
        'farmland_cropland': {
          'base': const Color(0xFFE0E0E0),       // Winter soil
          'crop': const Color(0xFFBDBDBD),       // Dormant crops
          'highlight': const Color(0xFFEEEEEE),   // Winter highlight
          'boundary': const Color(0xFF616161),    // Field boundary
          'winter': const Color(0xFF9E9E9E),      // Winter state
        },
        'farmland_pasture': {
          'base': const Color(0xFFE0E0E0),       // Winter grass base
          'grass': const Color(0xFFBDBDBD),       // Winter grass
          'highlight': const Color(0xFFEEEEEE),   // Winter highlight
          'boundary': const Color(0xFF616161),    // Field boundary
          'winter': const Color(0xFF9E9E9E),      // Dormant state
        },
        'farmland_orchard': {
          'base': const Color(0xFFE0E0E0),       // Winter ground
          'tree': const Color(0xFF795548),       // Bare trees
          'highlight': const Color(0xFFBCAAA4),   // Winter highlight
          'boundary': const Color(0xFF5D4037),    // Orchard boundary
          'winter': const Color(0xFF8D6E63),      // Dormant state
        },
        'farmland_vineyard': {
          'base': const Color(0xFFE0E0E0),       // Winter ground
          'vine': const Color(0xFF795548),        // Dormant vines
          'highlight': const Color(0xFFBCAAA4),   // Winter highlight
          'boundary': const Color(0xFF5D4037),    // Vineyard boundary
          'winter': const Color(0xFF8D6E63),      // Winter state
        },
        'farmland_rice': {
          'base': const Color(0xFFB3E5FC),       // Winter water
          'plant': const Color(0xFFBDBDBD),       // Dormant rice
          'highlight': const Color(0xFFE1F5FE),   // Ice highlight
          'boundary': const Color(0xFF80DEEA),    // Field boundary
          'winter': const Color(0xFF9E9E9E),      // Winter state
        },
        'farmland_aquaculture': {
          'base': const Color(0xFFB3E5FC),       // Winter water
          'water': const Color(0xFF03A9F4),       // Cold water
          'highlight': const Color(0xFFE1F5FE),   // Ice highlight
          'structure': const Color(0xFF90A4AE),   // Pond structure
          'boundary': const Color(0xFF0288D1),    // Pond boundary
        },
      },
    },
    'muted': {
      'default': {
        'farmland_cropland': {
          ...croplandColors,
        },
        'farmland_pasture': {
          ...pastureColors,
        },
        'farmland_orchard': {
          ...orchardColors,
        },
        'farmland_vineyard': {
          ...vineyardColors,
        },
        'farmland_rice': {
          ...riceFieldColors,
        },
        'farmland_aquaculture': {
          ...aquacultureColors,
        },
      },
    },
  };

  /// Get colors for a specific feature type
  Map<String, Color> getFeatureColors(
    String featureType,
    String theme,
    String season,
  ) {
    // Default to 'vibrant' theme and 'default' season if not found
    final themeColors = _colorPalettes[theme] ?? _colorPalettes['vibrant']!;
    final seasonColors = themeColors[season] ?? themeColors['default']!;
    return seasonColors[featureType] ?? {};
  }

  /// Create a gradient paint for the given bounds and colors
  Paint createGradientPaint(
    Rect bounds,
    List<Color> colors,
    List<double> stops,
    {bool isRadial = false}
  ) {
    if (isRadial) {
      return Paint()
        ..shader = RadialGradient(
          colors: colors,
          stops: stops,
        ).createShader(bounds);
    } else {
      return Paint()
        ..shader = LinearGradient(
          colors: colors,
          stops: stops,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ).createShader(bounds);
    }
  }

  /// Add random variation to a color
  Color addVariation(Color color, {double amount = 0.1}) {
    final random = math.Random();
    final variation = amount * (random.nextDouble() * 2 - 1);
    
    return Color.fromARGB(
      color.alpha,
      (color.red + color.red * variation).round().clamp(0, 255),
      (color.green + color.green * variation).round().clamp(0, 255),
      (color.blue + color.blue * variation).round().clamp(0, 255),
    );
  }

  /// Adjust brightness of a color
  Color adjustBrightness(Color color, double factor) {
    return Color.fromARGB(
      color.alpha,
      (color.red * factor).round().clamp(0, 255),
      (color.green * factor).round().clamp(0, 255),
      (color.blue * factor).round().clamp(0, 255),
    );
  }

  /// Create a color transition between two colors
  Color interpolateColor(Color a, Color b, double t) {
    return Color.fromARGB(
      lerpInt(a.alpha, b.alpha, t),
      lerpInt(a.red, b.red, t),
      lerpInt(a.green, b.green, t),
      lerpInt(a.blue, b.blue, t),
    );
  }

  /// Helper function to interpolate between two integers
  int lerpInt(int a, int b, double t) {
    return (a + (b - a) * t).round().clamp(0, 255);
  }

  // Cropland colors
  static final Map<String, Color> croplandColors = <String, Color>{
    'base': const Color(0xFFD7CCA1),      // Base soil color
    'crop': const Color(0xFF90A4AE),      // Crop color
    'highlight': const Color(0xFFB0BEC5),  // Highlight color
    'boundary': const Color(0xFF6D4C41),   // Field boundary
    'spring': const Color(0xFF78909C),     // Spring growth
    'summer': const Color(0xFF607D8B),     // Summer crops
    'autumn': const Color(0xFFBDBDBD),     // Autumn harvest
  };

  // Pasture colors
  static final Map<String, Color> pastureColors = <String, Color>{
    'base': const Color(0xFFB0BEC5),      // Base grass color
    'grass': const Color(0xFF78909C),     // Grass color
    'highlight': const Color(0xFF90A4AE),  // Highlight color
    'boundary': const Color(0xFF455A64),   // Pasture boundary
    'spring': const Color(0xFF607D8B),     // Spring grass
    'summer': const Color(0xFF546E7A),     // Summer grass
    'autumn': const Color(0xFF90A4AE),     // Autumn grass
  };

  // Orchard colors
  static final Map<String, Color> orchardColors = <String, Color>{
    'base': const Color(0xFFCFD8DC),      // Base ground color
    'tree': const Color(0xFF607D8B),      // Tree color
    'highlight': const Color(0xFF90A4AE),  // Highlight color
    'boundary': const Color(0xFF37474F),   // Orchard boundary
    'spring': const Color(0xFF78909C),     // Spring bloom
    'summer': const Color(0xFF546E7A),     // Summer foliage
    'autumn': const Color(0xFF8D6E63),     // Autumn colors
  };

  // Vineyard colors
  static final Map<String, Color> vineyardColors = <String, Color>{
    'base': const Color(0xFFE0E0E0),      // Base soil color
    'vine': const Color(0xFF455A64),      // Vine color
    'highlight': const Color(0xFF607D8B),  // Highlight color
    'boundary': const Color(0xFF37474F),   // Vineyard boundary
    'spring': const Color(0xFF607D8B),     // Spring growth
    'summer': const Color(0xFF455A64),     // Summer vines
    'autumn': const Color(0xFF6D4C41),     // Autumn vines
  };

  // Rice field colors
  static final Map<String, Color> riceFieldColors = <String, Color>{
    'base': const Color(0xFF90A4AE),      // Water base color
    'water': const Color(0xFF78909C),     // Water color
    'plant': const Color(0xFF80CBC4),     // Rice plant color
    'boundary': const Color(0xFF455A64),   // Paddy boundary
    'spring': const Color(0xFF78909C),     // Spring water
    'summer': const Color(0xFF4DB6AC),     // Summer growth
    'autumn': const Color(0xFFBCAAA4),     // Harvest ready
  };

  // Aquaculture colors
  static final Map<String, Color> aquacultureColors = <String, Color>{
    'base': const Color(0xFF90A4AE),      // Water base color
    'water': const Color(0xFF78909C),     // Water color
    'structure': const Color(0xFF455A64),  // Pond structure color
    'boundary': const Color(0xFF37474F),   // Pond boundary
    'highlight': const Color(0xFF607D8B),  // Water highlight
    'detail': const Color(0xFF546E7A),     // Detail elements
  };

  /// Get color for wetland features
  static Color getWetlandColor({bool isRadial = false}) {
    return isRadial ? Colors.blue[100]! : Colors.blue[200]!;
  }

  /// Get color for heath features
  static Color getHeathColor({bool isRadial = false}) {
    return isRadial ? Colors.brown[200]! : Colors.brown[300]!;
  }

  /// Get color for tundra features
  static Color getTundraColor({bool isRadial = false}) {
    return isRadial ? Colors.grey[300]! : Colors.grey[400]!;
  }

  /// Get color for nursery features
  static Color getNurseryColor({bool isRadial = false}) {
    return isRadial ? Colors.lightGreen[200]! : Colors.lightGreen[300]!;
  }

  /// Get color for cemetery features
  static Color getCemeteryColor({bool isRadial = false}) {
    return isRadial ? Colors.blueGrey[100]! : Colors.blueGrey[200]!;
  }

  /// Get color for recreational features
  static Color getRecreationalColor({bool isRadial = false}) {
    return isRadial ? Colors.green[200]! : Colors.green[300]!;
  }

  /// Get color for farmland features
  static Color getFarmlandColor({bool isRadial = false}) {
    return isRadial ? Colors.lime[200]! : Colors.lime[300]!;
  }

  /// Get color for orchard features
  static Color getOrchardColor({bool isRadial = false}) {
    return isRadial ? Colors.lightGreen[300]! : Colors.lightGreen[400]!;
  }

  /// Get color for vineyard features
  static Color getVineyardColor({bool isRadial = false}) {
    return isRadial ? Colors.purple[200]! : Colors.purple[300]!;
  }

  /// Get color for aquaculture features
  static Color getAquacultureColor({bool isRadial = false}) {
    return isRadial ? Colors.cyan[200]! : Colors.cyan[300]!;
  }
} 