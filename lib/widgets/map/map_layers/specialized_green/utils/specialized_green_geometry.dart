import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Utility class for geometry operations on specialized green spaces
class SpecializedGreenGeometry {
  /// Create a path from a list of points
  Path createPath(List<Offset> points, {List<List<Offset>>? innerRings}) {
    if (points.length < 3) return Path();
    
    final path = Path()..moveTo(points[0].dx, points[0].dy);
    
    // Add outer boundary
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    path.close();
    
    // Add inner rings (holes) if any
    if (innerRings != null) {
      for (final ring in innerRings) {
        if (ring.length < 3) continue;
        
        path.moveTo(ring[0].dx, ring[0].dy);
        for (int i = 1; i < ring.length; i++) {
          path.lineTo(ring[i].dx, ring[i].dy);
        }
        path.close();
      }
    }
    
    return path;
  }

  /// Create a smoothed path from a list of points
  Path createSmoothedPath(List<Offset> points, {double tension = 0.5}) {
    if (points.length < 3) return createPath(points);
    
    final path = Path()..moveTo(points[0].dx, points[0].dy);
    
    for (int i = 1; i < points.length - 2; i++) {
      final p0 = points[i - 1];
      final p1 = points[i];
      final p2 = points[i + 1];
      final p3 = points[i + 2];
      
      // Calculate control points
      final c1 = Offset(
        p1.dx + (p2.dx - p0.dx) * tension / 3,
        p1.dy + (p2.dy - p0.dy) * tension / 3,
      );
      
      final c2 = Offset(
        p2.dx - (p3.dx - p1.dx) * tension / 3,
        p2.dy - (p3.dy - p1.dy) * tension / 3,
      );
      
      path.cubicTo(c1.dx, c1.dy, c2.dx, c2.dy, p2.dx, p2.dy);
    }
    
    // Close the path
    path.close();
    return path;
  }

  /// Calculate the centroid of a polygon
  Offset calculateCentroid(List<Offset> points) {
    if (points.isEmpty) return Offset.zero;
    
    double sumX = 0;
    double sumY = 0;
    
    for (final point in points) {
      sumX += point.dx;
      sumY += point.dy;
    }
    
    return Offset(sumX / points.length, sumY / points.length);
  }

  /// Calculate the area of a polygon
  double calculateArea(List<Offset> points) {
    if (points.length < 3) return 0;
    
    double area = 0;
    for (int i = 0; i < points.length; i++) {
      final j = (i + 1) % points.length;
      area += points[i].dx * points[j].dy;
      area -= points[j].dx * points[i].dy;
    }
    
    return area.abs() / 2;
  }

  /// Generate random points within a polygon
  List<Offset> generateRandomPoints(
    List<Offset> boundaryPoints,
    int count,
    {List<List<Offset>>? exclusionZones}
  ) {
    final random = math.Random(42); // Fixed seed for consistency
    final bounds = _calculateBounds(boundaryPoints);
    final points = <Offset>[];
    
    while (points.length < count) {
      final x = bounds.left + random.nextDouble() * bounds.width;
      final y = bounds.top + random.nextDouble() * bounds.height;
      final point = Offset(x, y);
      
      if (isPointInPolygon(point, boundaryPoints) &&
          !_isPointInExclusionZones(point, exclusionZones)) {
        points.add(point);
      }
    }
    
    return points;
  }

  /// Calculate the bounds of a polygon
  Rect _calculateBounds(List<Offset> points) {
    if (points.isEmpty) return Rect.zero;
    
    double minX = points[0].dx;
    double minY = points[0].dy;
    double maxX = points[0].dx;
    double maxY = points[0].dy;
    
    for (final point in points) {
      minX = math.min(minX, point.dx);
      minY = math.min(minY, point.dy);
      maxX = math.max(maxX, point.dx);
      maxY = math.max(maxY, point.dy);
    }
    
    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }

  /// Check if a point is inside a polygon using the ray casting algorithm
  bool isPointInPolygon(Offset point, List<Offset> polygon) {
    bool inside = false;
    int j = polygon.length - 1;
    
    for (int i = 0; i < polygon.length; i++) {
      if ((polygon[i].dy > point.dy) != (polygon[j].dy > point.dy) &&
          point.dx < (polygon[j].dx - polygon[i].dx) * (point.dy - polygon[i].dy) /
                  (polygon[j].dy - polygon[i].dy) +
              polygon[i].dx) {
        inside = !inside;
      }
      j = i;
    }
    
    return inside;
  }

  /// Check if a point is inside any exclusion zone
  bool _isPointInExclusionZones(Offset point, List<List<Offset>>? exclusionZones) {
    if (exclusionZones == null) return false;
    
    for (final zone in exclusionZones) {
      if (isPointInPolygon(point, zone)) return true;
    }
    
    return false;
  }

  /// Generate a grid of points within a polygon
  List<Offset> generateGrid(
    List<Offset> boundaryPoints,
    double spacing,
    {double rotation = 0.0}
  ) {
    final bounds = _calculateBounds(boundaryPoints);
    final points = <Offset>[];
    final center = bounds.center;
    
    // Create rotation matrix
    final double cos = math.cos(rotation);
    final double sin = math.sin(rotation);
    
    for (double x = bounds.left; x <= bounds.right; x += spacing) {
      for (double y = bounds.top; y <= bounds.bottom; y += spacing) {
        // Apply rotation around center
        final dx = x - center.dx;
        final dy = y - center.dy;
        final rotatedX = center.dx + (dx * cos - dy * sin);
        final rotatedY = center.dy + (dx * sin + dy * cos);
        final point = Offset(rotatedX, rotatedY);
        
        if (isPointInPolygon(point, boundaryPoints)) {
          points.add(point);
        }
      }
    }
    
    return points;
  }

  /// Generate points along a path with even spacing
  List<Offset> generatePathPoints(Path path, double spacing) {
    final points = <Offset>[];
    final metrics = path.computeMetrics();
    
    for (final metric in metrics) {
      double distance = 0;
      while (distance < metric.length) {
        final tangent = metric.getTangentForOffset(distance);
        if (tangent != null) {
          points.add(tangent.position);
        }
        distance += spacing;
      }
    }
    
    return points;
  }

  /// Create a parallel offset path
  Path createParallelPath(List<Offset> points, double offset) {
    if (points.length < 2) return Path();
    
    final offsetPoints = <Offset>[];
    
    for (int i = 0; i < points.length; i++) {
      final prev = points[(i - 1 + points.length) % points.length];
      final curr = points[i];
      final next = points[(i + 1) % points.length];
      
      // Calculate perpendicular vectors
      final toPrev = (prev - curr);
      final toNext = (next - curr);
      
      // Normalize vectors
      final toPrevNorm = Offset(
        -toPrev.dy / toPrev.distance,
        toPrev.dx / toPrev.distance,
      );
      final toNextNorm = Offset(
        -toNext.dy / toNext.distance,
        toNext.dx / toNext.distance,
      );
      
      // Average the normals
      final avgNorm = (toPrevNorm + toNextNorm) / 2;
      final normLength = math.sqrt(avgNorm.dx * avgNorm.dx + avgNorm.dy * avgNorm.dy);
      
      // Calculate offset point
      offsetPoints.add(Offset(
        curr.dx + (avgNorm.dx / normLength) * offset,
        curr.dy + (avgNorm.dy / normLength) * offset,
      ));
    }
    
    return createPath(offsetPoints);
  }
} 