import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path;
import 'dart:ui';

import './data/specialized_green_data_provider.dart';
import './models/specialized_green_feature.dart';
import './renderers/wetland_renderer.dart';
import './renderers/heath_renderer.dart';
import './renderers/tundra_renderer.dart';
import './renderers/nursery_renderer.dart';
import './renderers/cemetery_renderer.dart';
import './renderers/recreational_renderer.dart';
import './renderers/farmland_renderer.dart';

/// A specialized layer for rendering unique green spaces like wetlands, heath, tundra, etc.
/// This layer handles the complex rendering of natural and specialized green areas
/// that require unique visual treatments and seasonal variations.
class SpecializedGreenLayer extends CustomPainter {
  final SpecializedGreenDataProvider _dataProvider = SpecializedGreenDataProvider();
  
  // Add _season field
  final String _season;
  
  // Renderers for different types of green spaces
  late final WetlandRenderer _wetlandRenderer;
  late final HeathRenderer _heathRenderer;
  late final TundraRenderer _tundraRenderer;
  late final NurseryRenderer _nurseryRenderer;
  late final CemeteryRenderer _cemeteryRenderer;
  late final RecreationalRenderer _recreationalRenderer;
  late final FarmlandRenderer _farmlandRenderer;
  
  // Layer configuration
  final bool enhancedDetail;
  final double tiltFactor;
  final String theme;
  final double zoomLevel;
  final bool isMapMoving;
  final LatLngBounds visibleBounds;
  final MapCamera? mapCamera;

  SpecializedGreenLayer({
    required String season,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
    this.theme = 'vibrant',
    this.mapCamera,
  }) : _season = season {
    _initializeRenderers();
  }

  /// Initialize all renderers with current settings
  void _initializeRenderers() {
    _wetlandRenderer = WetlandRenderer(
      zoom: zoomLevel,
      season: _season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _heathRenderer = HeathRenderer(
      zoom: zoomLevel,
      season: _season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _tundraRenderer = TundraRenderer(
      zoom: zoomLevel,
      season: _season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _nurseryRenderer = NurseryRenderer(
      zoom: zoomLevel,
      season: _season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _cemeteryRenderer = CemeteryRenderer(
      zoom: zoomLevel,
      season: _season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _recreationalRenderer = RecreationalRenderer(
      zoom: zoomLevel,
      season: _season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _farmlandRenderer = FarmlandRenderer(
      zoom: zoomLevel,
      season: _season,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
  }

  @override
  void paint(Canvas canvas, Size size) {
    // Skip rendering if zoom is too low for meaningful display
    if (zoomLevel < 7) return;

    // Proceed with rendering if we have features
    if (_dataProvider.features.isEmpty) return;
    
    // Sort features by type for efficient rendering
    final features = List<SpecializedGreenFeature>.from(_dataProvider.features);
    features.sort((a, b) => a.type.index.compareTo(b.type.index));
    
    // Render features by type
    for (final feature in features) {
      try {
        switch (feature.type) {
          case SpecializedGreenType.wetland:
            _wetlandRenderer.render(canvas, size, feature);
            break;
          case SpecializedGreenType.heath:
            _heathRenderer.render(canvas, size, feature);
            break;
          case SpecializedGreenType.tundra:
            _tundraRenderer.render(canvas, size, feature);
            break;
          case SpecializedGreenType.nursery:
            _nurseryRenderer.render(canvas, size, feature);
            break;
          case SpecializedGreenType.cemetery:
            _cemeteryRenderer.render(canvas, size, feature);
            break;
          case SpecializedGreenType.recreationGround:
            _recreationalRenderer.render(canvas, size, feature);
            break;
          case SpecializedGreenType.farmland:
          case SpecializedGreenType.orchard:
          case SpecializedGreenType.vineyard:
          case SpecializedGreenType.aquaculture:
            _farmlandRenderer.render(canvas, size, feature);
            break;
          default:
            // Skip unknown types
            continue;
        }
      } catch (e) {
        print('Error rendering feature ${feature.id}: $e');
        continue;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    final old = oldDelegate as SpecializedGreenLayer;
    return old._season != _season ||
           old.theme != theme ||
           old.enhancedDetail != enhancedDetail ||
           old.tiltFactor != tiltFactor;
  }
} 