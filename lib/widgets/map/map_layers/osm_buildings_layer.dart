import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

import 'osm_data_processor.dart';
import '../../../services/map_cache_manager.dart';
import '../map_caching/map_cache_extension.dart';
import '../map_caching/zoom_level_manager.dart';
import '../map_caching/map_cache_coordinator.dart';
import 'osm_buildings_data_provider.dart';
import 'buildings/osm_buildings_painter.dart';

// Extension to add normalize method to Offset
extension OffsetExtensions on Offset {
  Offset normalize() {
    final double magnitude = distance;
    if (magnitude == 0) return Offset.zero;
    return Offset(dx / magnitude, dy / magnitude);
  }
}

/// A custom layer to render OpenStreetMap buildings in 2.5D with cleaner separation of concerns
class OSMBuildingsLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final bool isMapMoving;
  final String theme;
  final bool enhancedDetail;
  final int? detailLevel;
  final MapCamera? mapCamera;
  
  const OSMBuildingsLayer({
    Key? key,
    this.tiltFactor = 1.0,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.theme = 'vibrant',
    this.enhancedDetail = true,
    this.detailLevel = 3,
    this.mapCamera,
  }) : super(key: key);

  @override
  State<OSMBuildingsLayer> createState() => _OSMBuildingsLayerState();
}

class _OSMBuildingsLayerState extends State<OSMBuildingsLayer> {
  // Data provider handles all data fetching and caching logic
  late final OSMBuildingsDataProvider _dataProvider;
  
  // Track local state
  String _currentTheme = 'vibrant';
  bool _hasNetworkError = false;
  
  @override
  void initState() {
    super.initState();
    _currentTheme = widget.theme;
    
    // Initialize data provider
    _dataProvider = OSMBuildingsDataProvider(
      initialZoomLevel: widget.zoomLevel,
      initialBounds: widget.visibleBounds,
      onError: _handleDataError,
      initialDetailLevel: widget.detailLevel, // Pass detail level to data provider
    );
    
    // Initial data fetch
    _fetchBuildingsData();
  }
  
  // Handle network/data errors
  void _handleDataError(String errorMessage) {
    if (mounted) {
      setState(() {
        _hasNetworkError = true;
      });
    }
    debugPrint('OSMBuildingsLayer error: $errorMessage');
  }
  
  @override
  void didUpdateWidget(OSMBuildingsLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update current theme if changed
    if (widget.theme != oldWidget.theme) {
      _currentTheme = widget.theme;
    }
    
    // Notify data provider of changes
    _dataProvider.updateParameters(
      zoomLevel: widget.zoomLevel,
      visibleBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving
    );
    
    // Determine if we need to fetch new data
    bool shouldFetch = _dataProvider.shouldFetchNewData(
      oldZoomLevel: oldWidget.zoomLevel,
      oldBounds: oldWidget.visibleBounds,
      oldIsMoving: oldWidget.isMapMoving,
      newZoomLevel: widget.zoomLevel,
      newBounds: widget.visibleBounds,
      newIsMoving: widget.isMapMoving,
    );
    
    if (shouldFetch) {
      _fetchBuildingsData();
    }
  }
  
  void _fetchBuildingsData() async {
    // Use the data provider to fetch data
    await _dataProvider.fetchBuildingsData();
    
    // If there was a successful fetch, clear any previous error state
    if (_hasNetworkError && !_dataProvider.hasError) {
      setState(() {
        _hasNetworkError = false;
      });
    }
  }
  
  // Retry after network error
  void _retryAfterNetworkError() {
    if (_hasNetworkError) {
      setState(() {
        _hasNetworkError = false;
      });
      
      _dataProvider.resetErrorState();
      _fetchBuildingsData();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine effective theme (adapt to system theme or use explicit theme)
    final effectiveTheme = widget.theme == 'auto' 
        ? MediaQuery.of(context).platformBrightness == Brightness.dark ? 'dark' : 'vibrant'
        : widget.theme;
    
    // Get current zoom bucket for rendering optimizations
    final zoomBucket = _dataProvider.getCurrentZoomBucket();
    
    // Don't render buildings at world or continental level unless zoomed in
    if (zoomBucket <= 2 && widget.zoomLevel < 10) {
      return const SizedBox.shrink();
    }
    
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_dataProvider.isLoading && !_dataProvider.hasInitialData) {
          // Show loading indicator on first load
          return Center(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
          );
        }
        
        // Show error overlay with retry button if there was a network error
        if (_hasNetworkError) {
          return Stack(
            children: [
              // Show existing buildings if we have them
              if (_dataProvider.buildings.isNotEmpty)
                CustomPaint(
                  size: Size(
                    constraints.maxWidth,
                    constraints.maxHeight,
                  ),
                  painter: OSMBuildingsPainter(
                    buildings: _dataProvider.buildings,
                    tiltFactor: widget.tiltFactor,
                    zoomLevel: widget.zoomLevel,
                    visibleBounds: widget.visibleBounds,
                    theme: effectiveTheme,
                    zoomBucket: zoomBucket,
                    showDetails: zoomBucket >= 4,
                    mapCamera: widget.mapCamera,
                  ),
                ),
              
              // Overlay with retry button
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'Network error loading map data',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _retryAfterNetworkError,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        }

        if (_dataProvider.buildings.isEmpty && _dataProvider.hasInitialData) {
          // No buildings found but we did search
          return const SizedBox.shrink();
        }
    
        return CustomPaint(
          painter: OSMBuildingsPainter(
            buildings: _dataProvider.buildings,
            tiltFactor: widget.tiltFactor,
            zoomLevel: widget.zoomLevel,
            visibleBounds: widget.visibleBounds,
            theme: _currentTheme,
            zoomBucket: zoomBucket,
            showDetails: zoomBucket >= 4,
            mapCamera: widget.mapCamera,
          ),
          size: Size.infinite,
        );
      },
    );
  }
  
  // Handle taps on buildings for future interactive features
  void _handleTapDown(TapDownDetails details) {
    // Get tap position
    final tapPosition = details.localPosition;
    
    // Find building under tap
    // This would be implemented to detect which building was tapped
    // for implementing selection, info display, etc.
    
    // For future implementation - can use hit testing with paths
  }
  
  @override
  void dispose() {
    // Clean up the data provider
    _dataProvider.dispose();
    super.dispose();
  }
} 