import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

import '../osm_data_processor.dart';
import '../../../services/map_cache_manager.dart';
import '../../map_caching/map_cache_extension.dart';
import '../../map_caching/zoom_level_manager.dart';
import '../../map_caching/map_cache_coordinator.dart';
import '../../map_caching/osm_throttle_manager.dart';
import '../osm_base_data_provider.dart';

/// Data provider class that handles building data fetching and caching
/// This class is responsible for all the data-related logic, separating it from UI rendering
class OSMB<PERSON>ingsDataProvider extends OSMBaseDataProvider {
  // Data processing and caching dependencies
  final OSMDataProcessor _dataProcessor = OSMDataProcessor();
  final MapCacheManager _cacheManager = MapCacheManager();
  final ZoomLevelManager _zoomManager = ZoomLevelManager();
  final OSMThrottleManager _throttleManager = OSMThrottleManager();
  
  // Building data
  List<Map<String, dynamic>> _buildings = [];
  
  // Track current zoom level bucket for optimized rendering
  int _currentZoomBucket = 3;
  
  // Keep track of the last request time for rate limiting
  DateTime _lastRequestTime = DateTime.now().subtract(const Duration(seconds: 30));
  
  /// Constructor that takes initial parameters and optional error callback
  OSMBuildingsDataProvider({
    required double initialZoomLevel,
    required LatLngBounds initialBounds,
    bool isMapMoving = false,
    Function(String)? onError,
  }) : super(
    initialBounds: initialBounds,
    initialZoom: initialZoomLevel,
    onError: onError,
  ) {
    _updateZoomBucket();
  }
  
  /// Get the current buildings data
  List<Map<String, dynamic>> get buildings => _buildings;
  
  /// Update parameters and check if we need to fetch new data
  bool shouldFetchNewData({
    required LatLngBounds newBounds,
    required double newZoom,
    required bool isMoving,
  }) {
    // Update zoom bucket if needed
    final oldZoom = zoomLevel;
    
    // Let base class handle bounds checking
    final shouldUpdate = shouldUpdateData(
      newBounds: newBounds,
      newZoom: newZoom,
      isMoving: isMoving,
    );
    
    // Check if zoom bucket changed
    if (oldZoom != newZoom) {
      _updateZoomBucket();
    }
    
    return shouldUpdate;
  }
  
  /// Main method for fetching building data
  Future<void> fetchBuildingData() async {
    // Only show loading on first fetch
    isLoading = !didInitialFetch;
    
    // Get the appropriate bounds for fetching
    final fetchBounds = getBoundsForFetching();
    
    // Generate cache key
    final cacheKey = 'buildings_${fetchBounds.southWest.latitude.toStringAsFixed(4)}_${fetchBounds.southWest.longitude.toStringAsFixed(4)}_${fetchBounds.northEast.latitude.toStringAsFixed(4)}_${fetchBounds.northEast.longitude.toStringAsFixed(4)}_${_currentZoomBucket}';
    
    try {
      // Check if we can make a request
      final canRequest = await throttleManager.canMakeRequest(
        'buildings',
        fetchBounds
      );
      
      if (!canRequest) {
        debugPrint('Buildings request throttled');
        isLoading = false;
        needsRefresh = true;
        return;
      }
      
      // Use MapCacheCoordinator to get data from cache or fetch from network
      final buildingsData = await MapCacheCoordinator().getData(
        type: MapDataType.buildings,
        key: cacheKey,
        southwest: fetchBounds.southWest,
        northeast: fetchBounds.northEast,
        zoomLevel: zoomLevel,
        fetchIfMissing: () async {
          // Adapt detail level based on zoom bucket
          final detailLevel = _getDetailLevel(_currentZoomBucket);
          
          // Calculate a safe data request region based on Overpass API limits
          final safeRequestBounds = _calculateSafeRequestBounds(
            fetchBounds,
            detailLevel,
            zoomLevel
          );
          
          // Record this request in the throttle manager
          throttleManager.recordRequest('buildings', fetchBounds);
          
          // Fetch building data with appropriate detail level
          return await _dataProcessor.fetchBuildingData(
            safeRequestBounds.southWest,
            safeRequestBounds.northEast,
            detailLevel: detailLevel
          );
        }
      );
      
      _buildings = buildingsData ?? [];
      isLoading = false;
      needsRefresh = false;
      didInitialFetch = true;
      hasError = false;
      _lastRequestTime = DateTime.now();
    } catch (e) {
      debugPrint('Error in OSMBuildingsDataProvider: $e');
      isLoading = false;
      hasError = true;
      didInitialFetch = true;
      
      // Notify listeners about the error
      onError?.call(e.toString());
    }
  }
  
  /// Update the current zoom bucket for optimized rendering
  void _updateZoomBucket() {
    _currentZoomBucket = _getZoomBucket(zoomLevel);
  }
  
  /// Get zoom bucket (1-5) based on zoom level
  int _getZoomBucket(double zoom) {
    if (zoom <= 14) return 1;
    if (zoom <= 15) return 2;
    if (zoom <= 16) return 3;
    if (zoom <= 17) return 4;
    return 5;
  }
  
  /// Get detail level (0.2-1.0) based on zoom bucket
  double _getDetailLevel(int zoomBucket) {
    switch (zoomBucket) {
      case 1: return 0.2;
      case 2: return 0.4;
      case 3: return 0.6;
      case 4: return 0.8;
      case 5: return 1.0;
      default: return 0.6;
    }
  }
  
  /// Calculate a safe request bounds that won't exceed Overpass API limits
  LatLngBounds _calculateSafeRequestBounds(LatLngBounds bounds, double detailLevel, double zoom) {
    final double latDelta = bounds.northEast.latitude - bounds.southWest.latitude;
    final double lonDelta = bounds.northEast.longitude - bounds.southWest.longitude;
    
    // Get user location from bounds manager
    final userLocation = boundsManager.userLocation;
    
    // Calculate center point - prioritize user location if available
    final LatLng center = userLocation ?? LatLng(
      (bounds.northEast.latitude + bounds.southWest.latitude) / 2,
      (bounds.northEast.longitude + bounds.southWest.longitude) / 2
    );
    
    // Adjust request size based on zoom level and detail level
    double requestFactor = 1.2; // Base factor
    
    // At higher zoom levels and higher detail, request larger areas
    if (zoom > 17) {
      requestFactor = 1.5 / detailLevel;
    } else if (zoom > 15) {
      requestFactor = 1.8 / detailLevel;
    } else if (zoom > 13) {
      requestFactor = 2.0 / detailLevel;
    } else {
      requestFactor = 2.2 / detailLevel;
    }
    
    // Cap to reasonable values while allowing more coverage
    requestFactor = math.max(1.2, math.min(requestFactor, 2.5));
    
    // Calculate new bounds with adjusted size
    final double newLatDelta = latDelta * requestFactor;
    final double newLonDelta = lonDelta * requestFactor;
    
    // Create bounds centered on priority point (user location or view center)
    return LatLngBounds(
      LatLng(
        center.latitude - (newLatDelta / 2),
        center.longitude - (newLonDelta / 2)
      ),
      LatLng(
        center.latitude + (newLatDelta / 2),
        center.longitude + (newLonDelta / 2)
      )
    );
  }
  
  /// Process and filter building data before display
  List<Map<String, dynamic>> _processBuildings(List<Map<String, dynamic>> buildings, double detailLevel) {
    if (buildings.isEmpty) return [];
    
    // Get user location
    final userLocation = boundsManager.userLocation;
    
    // Sort buildings by priority (distance to user if available, then by height)
    buildings.sort((a, b) {
      if (userLocation != null) {
        // Calculate distances to user location
        final aLat = a['center_lat'] as double;
        final aLon = a['center_lon'] as double;
        final bLat = b['center_lat'] as double;
        final bLon = b['center_lon'] as double;
        
        final distA = _calculateDistance(userLocation.latitude, userLocation.longitude, aLat, aLon);
        final distB = _calculateDistance(userLocation.latitude, userLocation.longitude, bLat, bLon);
        
        // Prioritize buildings closer to user
        if ((distA - distB).abs() > 0.001) { // Only use distance if difference is significant
          return distA.compareTo(distB);
        }
      }
      
      // Fall back to height-based sorting
      final heightA = a['height'] as double? ?? 0.0;
      final heightB = b['height'] as double? ?? 0.0;
      return heightB.compareTo(heightA);
    });
    
    // Calculate how many buildings to keep based on detail level and user proximity
    int maxBuildings = 300; // Base number
    if (detailLevel > 0.8) {
      maxBuildings = 500;
    } else if (detailLevel > 0.6) {
      maxBuildings = 400;
    }
    
    // Always keep buildings very close to user
    if (userLocation != null) {
      final nearbyBuildings = buildings.where((b) {
        final dist = _calculateDistance(
          userLocation.latitude, 
          userLocation.longitude,
          b['center_lat'] as double,
          b['center_lon'] as double
        );
        return dist < 0.002; // Roughly 200m
      }).toList();
      
      // Keep all nearby buildings plus others up to maxBuildings
      final otherBuildings = buildings.where((b) => !nearbyBuildings.contains(b))
          .take(maxBuildings - nearbyBuildings.length)
          .toList();
      
      return [...nearbyBuildings, ...otherBuildings];
    }
    
    // If no user location, just take top buildings by height
    return buildings.take(maxBuildings).toList();
  }
  
  /// Calculate distance between two points in kilometers
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    final dLat = _toRadians(lat2 - lat1);
    final dLon = _toRadians(lon2 - lon1);
    
    final a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_toRadians(lat1)) * math.cos(_toRadians(lat2)) *
        math.sin(dLon / 2) * math.sin(dLon / 2);
    
    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }
  
  /// Convert degrees to radians
  double _toRadians(double degrees) {
    return degrees * math.pi / 180;
  }
  
  /// Clean up resources
  void dispose() {
    // Nothing to dispose of at the moment, but here for future use
  }
} 