import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path
import 'dart:math';

/// Type definition for map projection function
typedef MapProjection = Offset Function(LatLng);

// Extension to add normalize method to Offset
extension OffsetExtensions on Offset {
  Offset normalize() {
    final double magnitude = distance;
    if (magnitude == 0) return Offset.zero;
    return Offset(dx / magnitude, dy / magnitude);
  }
}

/// Custom painter to render OpenStreetMap buildings in 2.5D
class OSMBuildingsPainter extends CustomPainter {
  final List<Map<String, dynamic>> buildings;
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final int zoomBucket;
  final bool showDetails;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  final MapCamera? mapCamera;
  
  // Shadow paint for buildings with cool-toned shadow
  final Paint _shadowPaint = Paint()
    ..color = const Color(0xFF1C1F2A).withOpacity(0.3)
    ..style = PaintingStyle.fill
    ..isAntiAlias = true;
    
  // Building shadow offset (simulates sun from northwest)
  final Offset _shadowOffset = const Offset(3.0, 3.0);
  
  // Edge highlighting for building outlines with softer color
  final Paint _edgePaint = Paint()
    ..color = const Color(0xFF2A3441).withOpacity(0.35)
    ..style = PaintingStyle.stroke
    ..strokeWidth = 0.5
    ..isAntiAlias = true;
  
  OSMBuildingsPainter({
    required this.buildings,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    required this.theme,
    required this.zoomBucket,
    required this.showDetails,
    this.mapCamera,
  });

  // Building color palettes for different themes
  final Map<String, Map<String, Map<String, Color>>> _buildingColorPalette = {
    'vibrant': {
      'commercial': {
        'wall': const Color(0xFF8B9FEA), 
        'roof': const Color(0xFF6E7BDD)
      },
      'residential': {
        'wall': const Color(0xFFFFC8A0), 
        'roof': const Color(0xFFFF936F)
      },
      'office': {
        'wall': const Color(0xFF5DD5E8), 
        'roof': const Color(0xFF3AB3F0)
      },
      'industrial': {
        'wall': const Color(0xFFB0C4D0), 
        'roof': const Color(0xFF8AA5B5)
      },
      'education': {
        'wall': const Color(0xFF9D2235), 
        'roof': const Color(0xFF7A1A29)
      },
      'healthcare': {
        'wall': const Color(0xFFFF9A9A), 
        'roof': const Color(0xFFFF7272)
      },
      'public': {
        'wall': const Color(0xFFD78FE8), 
        'roof': const Color(0xFFBA67D1)
      },
      'historic': {
        'wall': const Color(0xFFEAD190), 
        'roof': const Color(0xFFD4B86B)
      },
      'default': {
        'wall': const Color(0xFFD9E1F0), 
        'roof': const Color(0xFFBBC5D8)
      },
    },
    'dark': {
      'commercial': {
        'wall': const Color(0xFF4A5BD8).withAlpha(220), 
        'roof': const Color(0xFF3040C0).withAlpha(220)
      },
      'residential': {
        'wall': const Color(0xFFFF8C00).withAlpha(220), 
        'roof': const Color(0xFFE67300).withAlpha(220)
      },
      'office': {
        'wall': const Color(0xFF0099E0).withAlpha(220), 
        'roof': const Color(0xFF0077B3).withAlpha(220)
      },
      'industrial': {
        'wall': const Color(0xFF607D8B).withAlpha(220), 
        'roof': const Color(0xFF455A64).withAlpha(220)
      },
      'education': {
        'wall': const Color(0xFF9D2235).withAlpha(220), 
        'roof': const Color(0xFF7A1A29).withAlpha(220)
      },
      'healthcare': {
        'wall': const Color(0xFFE53935).withAlpha(220), 
        'roof': const Color(0xFFC62828).withAlpha(220)
      },
      'public': {
        'wall': const Color(0xFF9C27B0).withAlpha(220), 
        'roof': const Color(0xFF7B1FA2).withAlpha(220)
      },
      'historic': {
        'wall': const Color(0xFFBF9B58).withAlpha(220), 
        'roof': const Color(0xFF9F7A35).withAlpha(220)
      },
      'default': {
        'wall': const Color(0xFF78839C).withAlpha(220), 
        'roof': const Color(0xFF546277).withAlpha(220)
      },
    },
    'monochrome': {
      'commercial': {
        'wall': const Color(0xFF3D4A5C), 
        'roof': const Color(0xFF2A3441)
      },
      'residential': {
        'wall': const Color(0xFF4F5A6A), 
        'roof': const Color(0xFF3A4351)
      },
      'office': {
        'wall': const Color(0xFF2A3441), 
        'roof': const Color(0xFF1A2130)
      },
      'industrial': {
        'wall': const Color(0xFF6D7A8C), 
        'roof': const Color(0xFF505A68)
      },
      'education': {
        'wall': const Color(0xFF9D2235), 
        'roof': const Color(0xFF7A1A29)
      },
      'healthcare': {
        'wall': const Color(0xFF4F5A6A), 
        'roof': const Color(0xFF3A4351)
      },
      'public': {
        'wall': const Color(0xFF6D7A8C), 
        'roof': const Color(0xFF505A68)
      },
      'historic': {
        'wall': const Color(0xFF2A3441), 
        'roof': const Color(0xFF1A2130)
      },
      'default': {
        'wall': const Color(0xFF4F5A6A), 
        'roof': const Color(0xFF3A4351)
      },
    },
    'pastelPop': {
      'commercial': {
        'wall': const Color(0xFFB5C7FF), 
        'roof': const Color(0xFF8FA8FF)
      },
      'residential': {
        'wall': const Color(0xFFFFD6CC), 
        'roof': const Color(0xFFFFB5A5)
      },
      'office': {
        'wall': const Color(0xFFA5F2F3), 
        'roof': const Color(0xFF7DE8EA)
      },
      'industrial': {
        'wall': const Color(0xFFD8E2E9), 
        'roof': const Color(0xFFB8C9D4)
      },
      'education': {
        'wall': const Color(0xFFAA3A4C), 
        'roof': const Color(0xFF882D3D)
      },
      'healthcare': {
        'wall': const Color(0xFFFFC2C2), 
        'roof': const Color(0xFFFFAAAA)
      },
      'public': {
        'wall': const Color(0xFFE8C5F0), 
        'roof': const Color(0xFFD9A6E8)
      },
      'historic': {
        'wall': const Color(0xFFF0E6C5), 
        'roof': const Color(0xFFE8D6A6)
      },
      'default': {
        'wall': const Color(0xFFE6EBF5), 
        'roof': const Color(0xFFCCD6E8)
      },
    },
    'noir': {
      'commercial': {
        'wall': const Color(0xFF1A2C4D), 
        'roof': const Color(0xFF0E1A30)
      },
      'residential': {
        'wall': const Color(0xFF2D2D33), 
        'roof': const Color(0xFF1A1A1F)
      },
      'office': {
        'wall': const Color(0xFF1F3347), 
        'roof': const Color(0xFF0F1A24)
      },
      'industrial': {
        'wall': const Color(0xFF3A3A40), 
        'roof': const Color(0xFF26262B)
      },
      'education': {
        'wall': const Color(0xFF9D2235), 
        'roof': const Color(0xFF7A1A29)
      },
      'healthcare': {
        'wall': const Color(0xFF4D1A1A), 
        'roof': const Color(0xFF300E0E)
      },
      'public': {
        'wall': const Color(0xFF2D1A3D), 
        'roof': const Color(0xFF1A0E24)
      },
      'historic': {
        'wall': const Color(0xFF4D3D1A), 
        'roof': const Color(0xFF30240E)
      },
      'default': {
        'wall': const Color(0xFF2D2D33), 
        'roof': const Color(0xFF1A1A1F)
      },
    },
    'futuristic': {
      'commercial': {
        'wall': const Color(0xFF303540), 
        'roof': const Color(0xFF00CCFF)
      },
      'residential': {
        'wall': const Color(0xFF404550), 
        'roof': const Color(0xFFFF00AA)
      },
      'office': {
        'wall': const Color(0xFF252A35), 
        'roof': const Color(0xFF00FFCC)
      },
      'industrial': {
        'wall': const Color(0xFF505560), 
        'roof': const Color(0xFFFFCC00)
      },
      'education': {
        'wall': const Color(0xFF9D2235), 
        'roof': const Color(0xFFBF2A42)
      },
      'healthcare': {
        'wall': const Color(0xFF404550), 
        'roof': const Color(0xFFFF3366)
      },
      'public': {
        'wall': const Color(0xFF303540), 
        'roof': const Color(0xFFAA00FF)
      },
      'historic': {
        'wall': const Color(0xFF404550), 
        'roof': const Color(0xFFFFAA00)
      },
      'default': {
        'wall': const Color(0xFF353A45), 
        'roof': const Color(0xFF66CCFF)
      },
    },
  };
  
  // Rooftop colors for different themes
  final Map<String, Map<String, Color>> _rooftopColors = {
    'vibrant': {
      'default': const Color(0xFFE8F0FF),   // Lighter silvery blue for roofs
      'commercial': const Color(0xFFDFB5FF), // Vibrant lavender for commercial
      'residential': const Color(0xFFFFD6B8), // Warm peach for residential
      'office': const Color(0xFFA5F2F9),    // Bright teal for office
      'industrial': const Color(0xFFFFE07F), // Rich amber for industrial
      'retail': const Color(0xFFFFBED6),    // Soft pink for retail
      'public': const Color(0xFFA5E4FF),    // Sky blue for public
      'education': const Color(0xFFBF2A42), // Rich maroon for education
      'healthcare': const Color(0xFFFF8CAD), // Hot pink for healthcare
      'historic': const Color(0xFFFFEEAF),  // Golden yellow for historic
    },
    'dark': {
      'default': const Color(0xFF546277),   // Deep slate for roofs
      'commercial': const Color(0xFF7B1FA2).withOpacity(0.9),  // Rich purple
      'residential': const Color(0xFFE65100).withOpacity(0.9),  // Burnt orange
      'office': const Color(0xFF00ACC1).withOpacity(0.9),     // Cyan
      'industrial': const Color(0xFFFFA000).withOpacity(0.9),  // Golden amber
      'retail': const Color(0xFFD81B60).withOpacity(0.9),     // Magenta
      'public': const Color(0xFF1976D2).withOpacity(0.9),     // Royal blue
      'education': const Color(0xFFBF2A42).withOpacity(0.9),  // Rich maroon for education
      'healthcare': const Color(0xFFD50000).withOpacity(0.9),  // Bright red
      'historic': const Color(0xFFC0A030).withOpacity(0.9),   // Antique gold
    },
    'pastelPop': {
      'default': const Color(0xFFE6F0FF),   // Soft blue for roofs
      'commercial': const Color(0xFFD6C2FF), // Pastel purple for commercial
      'residential': const Color(0xFFFFE6D9), // Soft peach for residential
      'office': const Color(0xFFBFF9FA),    // Light cyan for office
      'industrial': const Color(0xFFFFF0C2), // Pale amber for industrial
      'retail': const Color(0xFFFFD9E6),    // Baby pink for retail
      'public': const Color(0xFFBFE6FF),    // Powder blue for public
      'education': const Color(0xFFBF2A42), // Rich maroon for education
      'healthcare': const Color(0xFFFFCCDA), // Blush pink for healthcare
      'historic': const Color(0xFFFFF5D9),  // Cream for historic
    },
    'noir': {
      'default': const Color(0xFF3D4655),   // Deep slate for roofs
      'commercial': const Color(0xFF2E1A47), // Deep purple
      'residential': const Color(0xFF472A1A), // Deep brown
      'office': const Color(0xFF1A3D47),    // Deep teal
      'industrial': const Color(0xFF474A1A), // Deep olive
      'retail': const Color(0xFF471A2E),    // Deep burgundy
      'public': const Color(0xFF1A2E47),    // Deep navy
      'education': const Color(0xFF9D2235), // Rich maroon for education
      'healthcare': const Color(0xFF471A1A), // Deep crimson
      'historic': const Color(0xFF473A1A),  // Deep bronze
    },
    'futuristic': {
      'default': const Color(0xFF66CCFF),   // Neon blue for roofs
      'commercial': const Color(0xFFAA00FF), // Neon purple for commercial
      'residential': const Color(0xFFFF00AA), // Neon pink for residential
      'office': const Color(0xFF00FFCC),    // Neon teal for office
      'industrial': const Color(0xFFFFCC00), // Neon amber for industrial
      'retail': const Color(0xFFFF3366),    // Neon red for retail
      'public': const Color(0xFF00AAFF),    // Neon blue for public
      'education': const Color(0xFFBF2A42), // Rich maroon for education
      'healthcare': const Color(0xFFFF3366), // Neon red for healthcare
      'historic': const Color(0xFFFFAA00),  // Neon orange for historic
    },
  };

  @override
  void paint(Canvas canvas, Size size) {
    // Skip if no buildings or at too low zoom level
    if (buildings.isEmpty || zoomLevel < 10) {
      return;
    }
    
    // Memory optimization: Pre-calculate theme colors once
    final String themeKey = _buildingColorPalette.containsKey(theme) ? theme : 'vibrant';
    final Map<String, Map<String, Color>> colors = _buildingColorPalette[themeKey]!;
    
    // Performance optimization: Only sort buildings at certain zoom levels
    final List<Map<String, dynamic>> buildingsToRender;
    if (zoomLevel >= 15) {
      buildingsToRender = _sortBuildingsByImportance(buildings);
    } else {
      // At lower zooms, limit the number of buildings and skip sorting
      final int maxBuildings = zoomLevel < 13 ? 100 : 300;
      buildingsToRender = buildings.length > maxBuildings 
        ? buildings.sublist(0, maxBuildings) 
        : buildings;
    }
    
    // Calculate the projection from geographic to screen coordinates
    final projection = _getMapProjection(size);
    
    // Create light source direction (for 3D lighting effects)
    final lightDir = Offset(1.0, 1.0).normalize();
    
    // Memory optimization: Reuse paint objects
    final Paint wallPaint = Paint()
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final Paint roofPaint = Paint()
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw buildings
    for (final building in buildingsToRender) {
      try {
        _drawBuilding(
          canvas, 
          building, 
          colors, 
          tiltFactor,
          projection,
          lightDir,
          size,
          wallPaint,
          roofPaint,
        );
      } catch (e) {
        // Skip rendering this building if there's an error
        debugPrint('Error rendering building: $e');
      }
    }
  }
  
  // Sort buildings by importance and size
  List<Map<String, dynamic>> _sortBuildingsByImportance(List<Map<String, dynamic>> buildings) {
    // Create a copy of the buildings list to avoid modifying the original
    final sortedBuildings = List<Map<String, dynamic>>.from(buildings);
    
    // Sort buildings - more important buildings first
    sortedBuildings.sort((a, b) {
      // Get heights (default to 10 if not available)
      final double heightA = (a['height'] as num?)?.toDouble() ?? 10.0;
      final double heightB = (b['height'] as num?)?.toDouble() ?? 10.0;
      
      // Check if either building has tags indicating importance
      final bool isImportantA = _isImportantBuilding(a['tags'] as Map<String, dynamic>?);
      final bool isImportantB = _isImportantBuilding(b['tags'] as Map<String, dynamic>?);
      
      if (isImportantA && !isImportantB) return -1;
      if (!isImportantA && isImportantB) return 1;
      
      // If equal importance, sort by height (tallest first)
      return heightB.compareTo(heightA);
    });
    
    return sortedBuildings;
  }
  
  // Determine if a building is important based on tags
  bool _isImportantBuilding(Map<String, dynamic>? tags) {
    if (tags == null) return false;
    
    // Check for important amenities
    if (tags.containsKey('amenity')) {
      final String amenity = tags['amenity'].toString();
      if (['school', 'university', 'hospital', 'government', 'townhall'].contains(amenity)) {
        return true;
      }
    }
    
    // Check for important building types
    if (tags.containsKey('building')) {
      final String building = tags['building'].toString();
      if (['civic', 'government', 'public', 'office', 'commercial', 'hospital'].contains(building)) {
        return true;
      }
    }
    
    return false;
  }
  
  // Calculate the center point of a building from its points
  LatLng _calculateBuildingCenter(List points) {
    if (points.isEmpty) return LatLng(0, 0);
    
    double sumLat = 0;
    double sumLng = 0;
    
    for (final point in points) {
      sumLat += point.latitude;
      sumLng += point.longitude;
    }
    
    return LatLng(sumLat / points.length, sumLng / points.length);
  }
  
  // Project a geographic point to screen coordinates
  Offset _projectPoint(double lat, double lng, MapProjection projection, Size size) {
    // UBER FIX: Optimized projection during map movement
    if (mapCamera != null) {
      try {
        // Use MapCamera directly for precise coordinate transformation during panning
        final point = mapCamera!.latLngToScreenPoint(LatLng(lat, lng));
        return Offset(point.x.toDouble(), point.y.toDouble());
      } catch (e) {
        // Silently fall back to manual projection (no logging to improve performance)
      }
    }
    
    // Fallback to manual projection only if MapCamera is unavailable or failed
    return projection(LatLng(lat, lng));
  }
  
  // Create a function to project from geographic to screen coordinates
  MapProjection _getMapProjection(Size size) {
    // UBER FIX: Optimized projection function
    if (mapCamera != null) {
      return (LatLng latlng) {
        try {
          // Direct access to camera for fast coordinate transformation
          final point = mapCamera!.latLngToScreenPoint(latlng);
          return Offset(point.x.toDouble(), point.y.toDouble());
        } catch (_) {
          // Silent fallback to manual projection (no logging during panning)
          return _manualProject(latlng, size);
        }
      };
    }
    
    // Fallback to manual projection
    return (LatLng latlng) => _manualProject(latlng, size);
  }
  
  /// Manual projection from geographic to screen coordinates
  Offset _manualProject(LatLng latlng, Size size) {
    final sw = visibleBounds.southWest;
    final ne = visibleBounds.northEast;
    
    // Calculate x position based on longitude
    final double x = size.width * (latlng.longitude - sw.longitude) / (ne.longitude - sw.longitude);
    
    // Calculate y position based on latitude
    final double y = size.height * (1.0 - (latlng.latitude - sw.latitude) / (ne.latitude - sw.latitude));
    
    return Offset(x, y);
  }
  
  // Draw a single building
  void _drawBuilding(
    Canvas canvas, 
    Map<String, dynamic> building, 
    Map<String, Map<String, Color>> colors, 
    double tiltFactor,
    MapProjection projection,
    Offset lightDir,
    Size size,
    Paint wallPaint,
    Paint roofPaint,
  ) {
    final List points = building['points'] as List;
    if (points.length < 3) return; // Need at least 3 points for a polygon
    
    // Memory optimization: Skip buildings outside visible bounds at higher zooms
    if (zoomLevel >= 15) {
      final LatLng center = _calculateBuildingCenter(points);
      if (!visibleBounds.contains(center)) return;
    }
    
    // Extract building properties
    final double height = (building['height'] as num?)?.toDouble() ?? 10.0;
    final Map<String, dynamic>? tags = building['tags'] as Map<String, dynamic>?;
    
    // Get enhanced tilt factor that increases with zoom level
    final double zoomEnhancedTiltFactor = tiltFactor * _getZoomTiltEnhancement();
    
    // Adjust height based on zoom level for nicer visuals at various zoom levels
    final double effectiveHeight = height * 0.2 * zoomEnhancedTiltFactor * _getEffectiveHeightMultiplier();
    
    // Determine building type from tags
    final String buildingType = _determineBuildingType(tags);
    
    // Apply enhanced visual styles
    final Color enhancedWallColor = _getEnhancedBuildingColor(buildingType, 'wall');
    final Color enhancedRoofColor = _getEnhancedBuildingColor(buildingType, 'roof');
    
    // Convert geographic coordinates to screen coordinates
    final List<Offset> screenPoints = points.map((p) => _projectPoint(p.latitude, p.longitude, projection, size)).toList();
    
    // Performance optimization: Skip very small buildings
    if (_isBuildingTooSmall(screenPoints, buildingType)) return;
    
    // Create path for the building footprint
    final Path basePath = Path()..addPolygon(screenPoints, true);
    
    // Create path for the building roof (shifted up by the building height)
    final Path roofPath = Path();
    for (int i = 0; i < screenPoints.length; i++) {
      final Offset currentPoint = screenPoints[i];
      if (i == 0) {
        roofPath.moveTo(currentPoint.dx, currentPoint.dy - effectiveHeight);
      } else {
        roofPath.lineTo(currentPoint.dx, currentPoint.dy - effectiveHeight);
      }
    }
    roofPath.close();
    
    // Enhanced shadow effect based on zoom level with cool-toned shadow
    final double shadowOpacity = math.min(0.5, 0.35 + (zoomLevel - 14) * 0.03);
    final Paint enhancedShadowPaint = Paint()
      ..color = const Color(0xFF1C1F2A).withOpacity(shadowOpacity)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0) // Add blur for softer shadows
      ..isAntiAlias = true;
    
    // Calculate shadow offset that increases with zoom for more dramatic effect
    final double shadowMultiplier = math.min(1.8, 1.2 + (zoomLevel - 15) * 0.12);
    final Offset enhancedShadowOffset = Offset(
      _shadowOffset.dx * shadowMultiplier, 
      _shadowOffset.dy * shadowMultiplier
    );
    
    // Draw shadow with enhanced offset and blur
    final Path shadowPath = basePath.shift(enhancedShadowOffset);
    canvas.drawPath(shadowPath, enhancedShadowPaint);
    
    // Draw walls with enhanced colors
    for (int i = 0; i < screenPoints.length; i++) {
      final int nextIdx = (i + 1) % screenPoints.length;
      final Offset current = screenPoints[i];
      final Offset next = screenPoints[nextIdx];
      
      // Create wall path
      final Path wallPath = Path()
        ..moveTo(current.dx, current.dy)
        ..lineTo(next.dx, next.dy)
        ..lineTo(next.dx, next.dy - effectiveHeight)
        ..lineTo(current.dx, current.dy - effectiveHeight)
        ..close();
        
      // Wall paint with enhanced visual effects - gradient from base to top with tilted light source
      final Paint wallPaint = Paint()
        ..shader = LinearGradient(
          begin: const Alignment(-0.3, 1.0), // Tilted light source
          end: const Alignment(0.3, 0.0),
          colors: [
            enhancedWallColor,
            Color.lerp(enhancedWallColor, enhancedRoofColor, 0.3) ?? enhancedWallColor,
          ],
          stops: const [0.7, 1.0],
        ).createShader(Rect.fromLTRB(
          current.dx, 
          current.dy, 
          next.dx, 
          current.dy - effectiveHeight
        ))
        ..style = PaintingStyle.fill
        ..isAntiAlias = true;
      
      // Calculate wall direction for determining edge highlights
      final Offset wallDir = (next - current).normalize();
      final bool isWallFacingViewer = wallDir.dy >= 0;
      
      // Adjust edge width based on zoom and whether wall faces viewer
      final double edgeWidth = isWallFacingViewer 
          ? math.min(1.0, 0.5 + (zoomLevel - 16) * 0.1) 
          : math.min(0.7, 0.3 + (zoomLevel - 16) * 0.08);
          
      // Edge paint with enhanced style - dynamic color based on wall color
      final Paint edgePaint = Paint()
        ..color = isWallFacingViewer 
            ? HSLColor.fromColor(enhancedWallColor).withLightness(0.2).toColor().withOpacity(0.4)
            : HSLColor.fromColor(enhancedWallColor).withLightness(0.15).toColor().withOpacity(0.25)
        ..style = PaintingStyle.stroke
        ..strokeWidth = edgeWidth
        ..isAntiAlias = true;
      
      canvas.drawPath(wallPath, wallPaint);
      canvas.drawPath(wallPath, edgePaint);
    }
    
    // Roof paint with enhanced visual effects - radial gradient with offset for light direction
    final Paint roofPaint = Paint()
      ..shader = RadialGradient(
        center: const Alignment(-0.3, -0.3), // Offset gradient for more dramatic light direction
        radius: 1.4,
        colors: [
          Color.lerp(enhancedRoofColor, Colors.white, 0.25) ?? enhancedRoofColor,
          enhancedRoofColor,
        ],
      ).createShader(roofPath.getBounds())
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw roof with enhanced visuals
    canvas.drawPath(roofPath, roofPaint);
    
    // Draw roof edge with color derived from roof color for better cohesion
    final Paint roofEdgePaint = Paint()
      ..color = HSLColor.fromColor(enhancedRoofColor).withLightness(0.25).toColor().withOpacity(0.35)
      ..style = PaintingStyle.stroke
      ..strokeWidth = math.min(0.8, 0.4 + (zoomLevel - 16) * 0.08)
      ..isAntiAlias = true;
    
    canvas.drawPath(roofPath, roofEdgePaint);
    
    // Draw architectural details without windows
    if (zoomLevel >= 17 && effectiveHeight > 15) {
      _drawModernArchitecturalDetails(canvas, screenPoints, effectiveHeight, buildingType);
    }
  }
  
  // Determine building type from tags
  String _determineBuildingType(Map<String, dynamic>? tags) {
    if (tags == null) return 'default';
    
    // Check for amenity tag
    if (tags.containsKey('amenity')) {
      final String amenity = tags['amenity'].toString();
      if (['school', 'university', 'college', 'library'].contains(amenity)) {
        return 'education';
      } else if (['hospital', 'clinic', 'doctors'].contains(amenity)) {
        return 'healthcare';
      } else if (['shop', 'mall', 'supermarket', 'store'].contains(amenity)) {
        return 'commercial';
      } else if (['office', 'bank', 'company'].contains(amenity)) {
        return 'office';
      } else if (['government', 'townhall', 'public_building'].contains(amenity)) {
        return 'public';
      }
    }
    
    // Check building tag
    if (tags.containsKey('building')) {
      final String buildingTag = tags['building'].toString();
      if (['commercial', 'retail', 'shop'].contains(buildingTag)) {
        return 'commercial';
      } else if (['residential', 'house', 'apartment', 'apartments'].contains(buildingTag)) {
        return 'residential';
      } else if (['office'].contains(buildingTag)) {
        return 'office';
      } else if (['industrial', 'factory', 'warehouse'].contains(buildingTag)) {
        return 'industrial';
      } else if (['school', 'university', 'college'].contains(buildingTag)) {
        return 'education';
      } else if (['hospital', 'healthcare'].contains(buildingTag)) {
        return 'healthcare';
      } else if (['public', 'government', 'civic'].contains(buildingTag)) {
        return 'public';
      } else if (['historic', 'monument', 'heritage'].contains(buildingTag)) {
        return 'historic';
      }
    }
    
    // Default type
    return 'default';
  }
  
  // Get enhanced vibrant colors for buildings that look sleek and modern
  Color _getEnhancedBuildingColor(String buildingType, String part) {
    // Use theme if available, otherwise fallback to vibrant
    final String effectiveTheme = _buildingColorPalette.containsKey(theme) ? theme : 'vibrant';
    
    // Get type-specific colors
    final Map<String, Color>? typeColors = _buildingColorPalette[effectiveTheme]![buildingType];
    final Color baseColor = (typeColors != null) 
        ? typeColors[part]! 
        : _buildingColorPalette[effectiveTheme]!['default']![part]!;
    
    // Apply enhanced color adjustments based on part and zoom level
    if (part == 'wall') {
      // Make walls more vibrant and slightly transparent at higher zooms
      final double alpha = math.min(0.95, 0.75 + (zoomLevel - 14) * 0.04);
      
      // Create a more harmonious color transformation
      final HSLColor hslColor = HSLColor.fromColor(baseColor);
      final Color enhancedColor = hslColor
          .withSaturation(math.min(1.0, hslColor.saturation * 1.15))
          .withLightness(math.min(0.85, hslColor.lightness * 1.1))
          .toColor()
          .withOpacity(alpha);
      
      return _addColorVariation(enhancedColor, variationAmount: 15);
    } else {
      // Make roofs slightly darker and less transparent
      final double alpha = math.min(0.98, 0.85 + (zoomLevel - 14) * 0.03);
      
      // Create a more harmonious color transformation
      final HSLColor hslColor = HSLColor.fromColor(baseColor);
      final Color enhancedColor = hslColor
          .withSaturation(math.min(1.0, hslColor.saturation * 1.1))
          .withLightness(math.max(0.2, hslColor.lightness * 0.9))
          .toColor()
          .withOpacity(alpha);
      
      return _addColorVariation(enhancedColor, variationAmount: 12);
    }
  }
  
  // Add controlled color variation for more natural looking buildings
  Color _addColorVariation(Color baseColor, {int variationAmount = 20}) {
    // Only add variation for very high zoom levels
    if (zoomLevel < 17) return baseColor;
    
    // Add small random variation to color components
    final int variation = math.min(variationAmount, 10); // Reduced maximum variation
    
    // Use a fixed seed based on the building's position to ensure consistent variation
    final int seed = (baseColor.value + variation).hashCode;
    final math.Random fixedRandom = math.Random(seed);
    
    final int r = (baseColor.red + (fixedRandom.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int g = (baseColor.green + (fixedRandom.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int b = (baseColor.blue + (fixedRandom.nextInt(variation) - (variation~/2))).clamp(0, 255);
    
    return Color.fromRGBO(r, g, b, baseColor.opacity);
  }
  
  // Draw modern architectural details without windows
  void _drawModernArchitecturalDetails(Canvas canvas, List<Offset> screenPoints, double effectiveHeight, String buildingType) {
    // For each wall, add subtle architectural details
    for (int i = 0; i < screenPoints.length; i++) {
      final int nextIdx = (i + 1) % screenPoints.length;
      final Offset current = screenPoints[i];
      final Offset next = screenPoints[nextIdx];
      
      // Skip very short walls
      final double wallLength = (current - next).distance;
      if (wallLength < 15) continue;
      
      // Calculate wall direction
      final Offset wallDirection = (next - current).normalize();
      
      // Only draw details on walls facing toward the bottom of the screen
      if (wallDirection.dy < 0) continue;
      
      // Draw corner highlights to enhance the 3D effect
      _drawSleekCornerDetail(canvas, current, next, wallDirection, effectiveHeight, buildingType);
    }
  }
  
  // Draw sleek corner highlights for modern architectural look
  void _drawSleekCornerDetail(Canvas canvas, Offset start, Offset end, Offset wallDirection, double effectiveHeight, String buildingType) {
    // Calculate perpendicular to wall for 3D effect
    final Offset perpendicular = Offset(-wallDirection.dy, wallDirection.dx);
    
    // Get appropriate accent color based on building type
    final Color baseWallColor = _getEnhancedBuildingColor(buildingType, 'wall');
    
    // Create a lighter accent color
    final Color accentColor = HSLColor.fromColor(baseWallColor)
        .withLightness(math.min(1.0, HSLColor.fromColor(baseWallColor).lightness * 1.4))
        .toColor()
        .withOpacity(0.7);
    
    // Create edge highlight paint
    final Paint highlightPaint = Paint()
        ..color = accentColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = math.min(2.0, 1.0 + (zoomLevel - 16) * 0.2)
        ..isAntiAlias = true;
    
    // Draw subtle vertical highlight lines near corners
    final double cornerInset = math.max(5.0, effectiveHeight * 0.08);
    
    // Draw highlight at start corner
    final Offset startHighlightBase = Offset(
      start.dx + wallDirection.dx * cornerInset,
      start.dy + wallDirection.dy * cornerInset
    );
    
    // Draw highlight at end corner
    final Offset endHighlightBase = Offset(
      end.dx - wallDirection.dx * cornerInset,
      end.dy - wallDirection.dy * cornerInset
    );
    
    // Draw vertical highlight lines with perspective
    _drawVerticalHighlight(canvas, startHighlightBase, effectiveHeight, perpendicular, highlightPaint);
    _drawVerticalHighlight(canvas, endHighlightBase, effectiveHeight, perpendicular, highlightPaint);
    
    // For taller/larger buildings, add a horizontal accent line
    if (effectiveHeight > 30) {
      final double horizontalLineHeight = effectiveHeight * 0.7;
      
      final Offset lineStart = Offset(
        start.dx + wallDirection.dx * cornerInset * 0.5,
        start.dy + wallDirection.dy * cornerInset * 0.5 - horizontalLineHeight
      );
      
      final Offset lineEnd = Offset(
        end.dx - wallDirection.dx * cornerInset * 0.5,
        end.dy - wallDirection.dy * cornerInset * 0.5 - horizontalLineHeight
      );
      
      // Adjust for perspective
      final double depthFactor = 0.15 * (horizontalLineHeight / effectiveHeight) * (1.0 + tiltFactor);
      final Offset perspLineStart = Offset(
        lineStart.dx - (perpendicular.dx * depthFactor),
        lineStart.dy - (perpendicular.dy * depthFactor)
      );
      
      final Offset perspLineEnd = Offset(
        lineEnd.dx - (perpendicular.dx * depthFactor),
        lineEnd.dy - (perpendicular.dy * depthFactor)
      );
      
      // Draw horizontal accent line
      canvas.drawLine(perspLineStart, perspLineEnd, highlightPaint..strokeWidth *= 0.7);
    }
  }
  
  // Draw a vertical highlight line with proper perspective
  void _drawVerticalHighlight(Canvas canvas, Offset base, double height, Offset perpendicular, Paint paint) {
    final double topOffset = 0.15 * height * (1.0 + tiltFactor);
    
    final Path highlightPath = Path();
    highlightPath.moveTo(base.dx, base.dy);
    highlightPath.lineTo(
      base.dx - (perpendicular.dx * topOffset / height),
      base.dy - height - (perpendicular.dy * topOffset / height)
    );
    
    canvas.drawPath(highlightPath, paint);
  }

  // Get height multiplier based on zoom level
  double _getEffectiveHeightMultiplier() {
    // Enhanced height multiplier that increases with zoom level
    // This makes buildings appear more 3D as you zoom in
    if (zoomLevel < 14) return 0.5;  // Lower zoom: shorter buildings for better overview
    if (zoomLevel < 16) return 1.0;  // Medium zoom: normal height
    if (zoomLevel < 18) return 1.7;  // High zoom: taller buildings 
    if (zoomLevel < 20) return 2.3;  // Very high zoom: maximum height effect for dramatic close-ups
    return 3.0;  // Extreme zoom: even more dramatic 3D effect
  }
  
  // Calculate enhanced tilt factor based on zoom level
  double _getZoomTiltEnhancement() {
    // Start with normal tilt at zoom 14, then increase for higher zooms
    if (zoomLevel <= 14) return 1.0;
    if (zoomLevel >= 20) return 3.0; // Maximum 3x enhancement at max zoom
    
    // Non-linear interpolation for smoother transition - stronger enhancement at higher zooms
    return 1.0 + math.pow((zoomLevel - 14) / 6, 1.2) * 2.0;
  }

  @override
  bool shouldRepaint(OSMBuildingsPainter oldDelegate) {
    // UBER FIX: More robust camera change detection for smooth panning
    if (mapCamera != null && oldDelegate.mapCamera != null) {
      // Compare camera center positions to detect any movement, not just reference equality
      try {
        // Check for new timestamp property added by the _safeGetMapCamera method
        final oldTimestamp = (oldDelegate.mapCamera as dynamic)._lastUpdateTimestamp;
        final newTimestamp = (mapCamera as dynamic)._lastUpdateTimestamp;
        
        if (oldTimestamp != newTimestamp) {
          return true; // Camera has been updated with a new timestamp
        }
        
        // Fallback to position comparison if timestamps aren't available
        final oldCenter = oldDelegate.mapCamera!.center;
        final newCenter = mapCamera!.center;
        
        if (oldCenter.latitude != newCenter.latitude || 
            oldCenter.longitude != newCenter.longitude ||
            oldDelegate.mapCamera!.zoom != mapCamera!.zoom ||
            oldDelegate.mapCamera!.rotation != mapCamera!.rotation) {
          return true; // Camera has moved
        }
      } catch (e) {
        // If we can't compare the cameras directly, assume they're different
        return oldDelegate.mapCamera != mapCamera;
      }
    } else if (oldDelegate.mapCamera != mapCamera) {
      return true; // One of the cameras is null
    }
    
    // Check other properties as before
    return oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.visibleBounds != visibleBounds ||
           oldDelegate.theme != theme ||
           oldDelegate.zoomBucket != zoomBucket ||
           oldDelegate.showDetails != showDetails ||
           oldDelegate.buildings != buildings;
  }

  // Helper method to check if a building is too small to be visible
  bool _isBuildingTooSmall(List<Offset> points, String buildingType) {
    if (points.length < 2) return true;
    
    // Calculate approximate screen area
    double minX = points[0].dx, maxX = points[0].dx;
    double minY = points[0].dy, maxY = points[0].dy;
    
    for (final point in points) {
      minX = math.min(minX, point.dx);
      maxX = math.max(maxX, point.dx);
      minY = math.min(minY, point.dy);
      maxY = math.max(maxY, point.dy);
    }
    
    final double area = (maxX - minX) * (maxY - minY);
    
    // Never skip important buildings regardless of size
    if (buildingType == 'education' || 
        buildingType == 'healthcare' || 
        buildingType == 'public' || 
        buildingType == 'historic') {
      return false;
    }
    
    // Very conservative size thresholds - only skip extremely small buildings
    if (zoomLevel >= 17) return false; // At high zoom, show all buildings
    if (zoomLevel >= 15) return area < 5; // Only skip truly tiny buildings
    return area < 8; // At lower zooms, still very conservative
  }
} 