// Project a geographic point to screen coordinates
Offset _projectPoint(double lat, double lng, Size size) {
  // CRITICAL FIX: Always use MapCamera when available for accurate projection during map movement
  if (mapCamera != null) {
    final screenPoint = mapCamera!.latLngToScreenPoint(LatLng(lat, lng));
    if (screenPoint != null) {
      return Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
    }
  }
  
  // Fall back to simple linear projection if no camera or camera projection fails
  final sw = visibleBounds.southWest;
  final ne = visibleBounds.northEast;
  
  // Calculate x position based on longitude
  final double x = size.width * (lng - sw.longitude) / (ne.longitude - sw.longitude);
  
  // Calculate y position based on latitude
  final double y = size.height * (1.0 - (lat - sw.latitude) / (ne.latitude - sw.latitude));
  
  return Offset(x, y);
}

@override
bool shouldRepaint(OSMWaterFeaturesPainter oldDelegate) {
  // IMPORTANT FIX: Always repaint when mapCamera changes to ensure smooth movement
  return oldDelegate.zoomLevel != zoomLevel ||
         oldDelegate.tiltFactor != tiltFactor ||
         oldDelegate.visibleBounds != visibleBounds ||
         oldDelegate.waterColor != waterColor ||
         oldDelegate.mapCamera != mapCamera ||
         oldDelegate.waterFeatures != waterFeatures;
} 