import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;
import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import '../../../utils/bounding_box_utilities.dart';
import './osm_water_painter.dart';

/// A layer that displays OpenStreetMap water features with enhanced 2.5D styling
class OSMWaterLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final Color waterColor;
  final Color waterOutlineColor;
  final String theme;
  final bool showDetails;
  
  const OSMWaterLayer({
    Key? key,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    this.waterColor = const Color(0xFF1976D2), // Default blue
    this.waterOutlineColor = const Color(0xFF0D47A1), // Default darker blue
    this.theme = 'vibrant',
    this.showDetails = true,
  }) : super(key: key);

  @override
  State<OSMWaterLayer> createState() => _OSMWaterLayerState();
}

class _OSMWaterLayerState extends State<OSMWaterLayer> with TickerProviderStateMixin {
  List<Map<String, dynamic>> _waterFeatures = [];
  bool _isLoading = true;
  LatLngBounds? _currentBounds;
  
  // Animation controllers for different water effects
  late final AnimationController _rippleAnimation;
  late final AnimationController _flowAnimation;
  late final AnimationController _reflectionAnimation;
  
  // Combined animation value
  double get _combinedAnimationValue => _rippleAnimation.value;
  
  // Calculate zoom bucket for detail levels
  int get _zoomBucket {
    if (widget.zoomLevel >= 18) return 5;
    if (widget.zoomLevel >= 16) return 4;
    if (widget.zoomLevel >= 14) return 3;
    if (widget.zoomLevel >= 12) return 2;
    if (widget.zoomLevel >= 10) return 1;
    return 0;
  }
  
  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers with different durations for varied effects
    _rippleAnimation = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    )..repeat();
    
    _flowAnimation = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    )..repeat();
    
    _reflectionAnimation = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 12),
    )..repeat();
    
    // Initial load
    _loadWaterData();
  }
  
  @override
  void didUpdateWidget(OSMWaterLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Check if map bounds have changed significantly to reload data
    if (_currentBounds == null ||
        oldWidget.zoomLevel != widget.zoomLevel ||
        _calculateBoundsDistance(_currentBounds!, widget.visibleBounds) > 0.05) {
      _loadWaterData();
    }
  }
  
  @override
  void dispose() {
    _rippleAnimation.dispose();
    _flowAnimation.dispose();
    _reflectionAnimation.dispose();
    super.dispose();
  }
  
  // Load water feature data from OpenStreetMap Overpass API
  Future<void> _loadWaterData() async {
    if (widget.zoomLevel < 10) {
      // Skip loading data at very low zoom levels
      if (mounted) {
        setState(() {
          _waterFeatures = [];
          _isLoading = false;
        });
      }
      return;
    }
    
    // Set current bounds and loading state
    _currentBounds = widget.visibleBounds;
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }
    
    try {
      // Implementation of Overpass API query to fetch water data
      // ... existing code ...
      
    } catch (e) {
      debugPrint('Error fetching water feature data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          // Keep existing water features if we have them
        });
      }
    }
  }
  
  // Calculate a distance metric between two bounds
  double _calculateBoundsDistance(LatLngBounds bounds1, LatLngBounds bounds2) {
    // Simple Euclidean distance between centers
    final center1 = LatLng(
      (bounds1.northEast.latitude + bounds1.southWest.latitude) / 2,
      (bounds1.northEast.longitude + bounds1.southWest.longitude) / 2
    );
    
    final center2 = LatLng(
      (bounds2.northEast.latitude + bounds2.southWest.latitude) / 2,
      (bounds2.northEast.longitude + bounds2.southWest.longitude) / 2
    );
    
    // Approximate using flat-earth model for small distances
    return math.sqrt(
      math.pow(center1.latitude - center2.latitude, 2) +
      math.pow(center1.longitude - center2.longitude, 2)
    );
  }

  @override
  Widget build(BuildContext context) {
    // Return empty container while loading or if water features list is empty
    if (_isLoading || _waterFeatures.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // Combine multiple animations for more complex water effects
    return AnimatedBuilder(
      animation: Listenable.merge([_rippleAnimation, _flowAnimation, _reflectionAnimation]),
      builder: (context, child) {
        return CustomPaint(
          painter: OSMWaterPainter(
            waterFeatures: _waterFeatures,
            tiltFactor: widget.tiltFactor,
            zoomLevel: widget.zoomLevel,
            visibleBounds: widget.visibleBounds,
            theme: widget.theme,
            zoomBucket: _zoomBucket,
            showDetails: widget.showDetails,
            animationValue: _combinedAnimationValue,
            waterColor: widget.waterColor,
            waterOutlineColor: widget.waterOutlineColor,
          ),
          size: Size.infinite,
        );
      },
    );
  }
} 