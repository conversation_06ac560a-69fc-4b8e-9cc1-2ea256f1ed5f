import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

// Extension to add normalize method to Offset
extension OffsetExtensions on Offset {
  Offset normalize() {
    final double magnitude = distance;
    if (magnitude == 0) return Offset.zero;
    return Offset(dx / magnitude, dy / magnitude);
  }
}

/// Custom painter to render OpenStreetMap water features with animations and styling
class OSMWaterPainter extends CustomPainter {
  final List<Map<String, dynamic>> waterFeatures;
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final int zoomBucket;
  final bool showDetails;
  final double animationValue; // 0.0 to 1.0 for ripple animation
  final Color waterColor;
  final Color waterOutlineColor;
  final math.Random _random = math.Random(42); // Add fixed seed random for consistent rendering
  final MapCamera? mapCamera;
  
  OSMWaterPainter({
    required this.waterFeatures,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    required this.theme,
    required this.zoomBucket,
    required this.showDetails,
    required this.animationValue,
    this.waterColor = const Color(0xFF1976D2), // Default blue
    this.waterOutlineColor = const Color(0xFF0D47A1), // Default darker blue
    this.mapCamera,
  });

  // Water feature color palettes for different themes
  final Map<String, Map<String, Color>> _waterTypeColors = {
    'vibrant': {
      'river': const Color(0xFF1976D2).withOpacity(0.75),    // Rich blue for rivers
      'stream': const Color(0xFF4FC3F7).withOpacity(0.65),   // Light blue for streams
      'canal': const Color(0xFF0288D1).withOpacity(0.7),     // Medium blue for canals
      'lake': const Color(0xFF0277BD).withOpacity(0.65),     // Deep blue for lakes
      'reservoir': const Color(0xFF01579B).withOpacity(0.7), // Deeper blue for reservoirs
      'pond': const Color(0xFF4DD0E1).withOpacity(0.65),     // Cyan-blue for ponds
      'water': const Color(0xFF0288D1).withOpacity(0.7),     // Generic water
      'coastline': const Color(0xFF039BE5).withOpacity(0.65), // Bright blue for coastlines
      'drain': const Color(0xFF80DEEA).withOpacity(0.5),     // Very light cyan for drains
      'ditch': const Color(0xFF80DEEA).withOpacity(0.45),    // Very light cyan for ditches
    },
    'dark': {
      'river': const Color(0xFF0D47A1).withOpacity(0.65),   // Dark blue for rivers
      'stream': const Color(0xFF1565C0).withOpacity(0.6),   // Dark medium blue for streams
      'canal': const Color(0xFF0D47A1).withOpacity(0.65),   // Dark blue for canals
      'lake': const Color(0xFF1A237E).withOpacity(0.6),     // Deep blue-purple for lakes
      'reservoir': const Color(0xFF1A237E).withOpacity(0.65), // Deeper blue-purple for reservoirs
      'pond': const Color(0xFF0097A7).withOpacity(0.6),     // Dark cyan for ponds
      'water': const Color(0xFF0D47A1).withOpacity(0.65),   // Dark generic water
      'coastline': const Color(0xFF0277BD).withOpacity(0.6), // Dark bright blue for coastlines
      'drain': const Color(0xFF00838F).withOpacity(0.45),   // Dark cyan for drains
      'ditch': const Color(0xFF00838F).withOpacity(0.4),    // Dark cyan for ditches
    },
    'tropical': {
      'river': const Color(0xFF00BCD4).withOpacity(0.7),    // Turquoise water
      'stream': const Color(0xFF26C6DA).withOpacity(0.65),  // Light turquoise for streams
      'canal': const Color(0xFF00ACC1).withOpacity(0.7),    // Medium turquoise for canals
      'lake': const Color(0xFF00ACC1).withOpacity(0.65),    // Deeper turquoise for lakes
      'reservoir': const Color(0xFF0097A7).withOpacity(0.7), // Deeper turquoise for reservoirs
      'pond': const Color(0xFF4DD0E1).withOpacity(0.65),    // Light turquoise for ponds
      'water': const Color(0xFF00BCD4).withOpacity(0.7),    // Generic turquoise water
      'coastline': const Color(0xFF26C6DA).withOpacity(0.65), // Bright turquoise for coastlines
      'drain': const Color(0xFF80DEEA).withOpacity(0.5),    // Very light turquoise for drains
      'ditch': const Color(0xFF80DEEA).withOpacity(0.45),   // Very light turquoise for ditches
    },
    'monochrome': {
      'river': const Color(0xFFCFD8DC).withOpacity(0.65),   // Light gray for rivers
      'stream': const Color(0xFFECEFF1).withOpacity(0.6),   // Very light gray for streams
      'canal': const Color(0xFFCFD8DC).withOpacity(0.65),   // Light gray for canals
      'lake': const Color(0xFFB0BEC5).withOpacity(0.6),     // Medium gray for lakes
      'reservoir': const Color(0xFF90A4AE).withOpacity(0.65), // Dark gray for reservoirs
      'pond': const Color(0xFFCFD8DC).withOpacity(0.6),     // Light gray for ponds
      'water': const Color(0xFFB0BEC5).withOpacity(0.6),    // Medium gray for generic water
      'coastline': const Color(0xFFCFD8DC).withOpacity(0.6), // Light gray for coastlines
      'drain': const Color(0xFFECEFF1).withOpacity(0.45),   // Very light gray for drains
      'ditch': const Color(0xFFECEFF1).withOpacity(0.4),    // Very light gray for ditches
    },
  };

  @override
  void paint(Canvas canvas, Size size) {
    if (waterFeatures.isEmpty) return;
    
    // Sort features to ensure proper drawing order 
    // (larger features first, then smaller features on top)
    final sortedWaterFeatures = List<Map<String, dynamic>>.from(waterFeatures);
    sortedWaterFeatures.sort((a, b) {
      // Sort by water feature type and size
      final String typeA = _getWaterType(a);
      final String typeB = _getWaterType(b);
      
      // Get importance rank for sorting
      final int rankA = _getWaterImportanceRank(typeA);
      final int rankB = _getWaterImportanceRank(typeB);
      
      if (rankA != rankB) {
        // Primary sort by importance rank
        return rankB.compareTo(rankA); // Higher rank first
      } else {
        // Secondary sort by area
        final double areaA = _estimateFeatureArea(a['points'] as List);
        final double areaB = _estimateFeatureArea(b['points'] as List);
        return areaB.compareTo(areaA); // Larger area first
      }
    });
    
    // Draw each water feature
    for (final feature in sortedWaterFeatures) {
      _drawWaterFeature(canvas, size, feature);
    }
  }
  
  // Get water feature type from tags
  String _getWaterType(Map<String, dynamic> feature) {
    final Map<String, dynamic>? tags = feature['tags'] as Map<String, dynamic>?;
    if (tags == null) return 'water';
    
    if (tags.containsKey('waterway')) {
      return tags['waterway'] as String;
    } else if (tags.containsKey('natural') && tags['natural'] == 'water') {
      if (tags.containsKey('water')) {
        return tags['water'] as String;
      }
      return 'water';
    } else if (tags.containsKey('natural') && tags['natural'] == 'coastline') {
      return 'coastline';
    }
    
    return 'water';
  }
  
  // Determine importance rank for sorting water features
  int _getWaterImportanceRank(String waterType) {
    switch (waterType) {
      case 'coastline':
        return 10;
      case 'lake':
      case 'reservoir':
        return 9;
      case 'river':
        return 8;
      case 'canal':
        return 7;
      case 'stream':
        return 6;
      case 'pond':
        return 5;
      case 'water':
        return 4;
      case 'drain':
        return 3;
      case 'ditch':
        return 2;
      default:
        return 1;
    }
  }
  
  // Estimate area of a feature for sorting
  double _estimateFeatureArea(List points) {
    if (points.length < 3) return 0.0;
    
    // Simple polygon area estimation using Shoelace formula
    double area = 0.0;
    for (int i = 0; i < points.length; i++) {
      final LatLng current = points[i] as LatLng;
      final LatLng next = points[(i + 1) % points.length] as LatLng;
      
      area += (current.longitude * next.latitude) - (current.latitude * next.longitude);
    }
    
    return area.abs() / 2.0;
  }
  
  // Project a geographic point to screen coordinates
  Offset _projectPoint(LatLng point, Size size) {
    // If we have a mapCamera, use its projection for accurate positioning
    if (mapCamera != null) {
      try {
        final pixelPoint = mapCamera!.latLngToScreenPoint(point);
        return Offset(pixelPoint.x.toDouble(), pixelPoint.y.toDouble());
      } catch (e) {
        debugPrint('Error projecting point with camera: $e');
        // Fall back to linear projection if camera projection fails
      }
    }
    
    // Fall back to simple linear projection if no camera or camera projection fails
    final sw = visibleBounds.southWest;
    final ne = visibleBounds.northEast;
    
    // Calculate x position based on longitude
    final double x = size.width * (point.longitude - sw.longitude) / (ne.longitude - sw.longitude);
    
    // Calculate y position based on latitude
    final double y = size.height * (1.0 - (point.latitude - sw.latitude) / (ne.latitude - sw.latitude));
    
    return Offset(x, y);
  }
  
  // Draw a single water feature
  void _drawWaterFeature(Canvas canvas, Size size, Map<String, dynamic> feature) {
    final List points = feature['points'] as List;
    if (points.isEmpty) return;
    
    // Extract water feature properties
    final Map<String, dynamic>? tags = feature['tags'] as Map<String, dynamic>?;
    final String waterType = _getWaterType(feature);
    final bool isPolygon = points.length > 2 && 
                           waterType != 'river' && 
                           waterType != 'stream' &&
                           waterType != 'canal' &&
                           waterType != 'drain' &&
                           waterType != 'ditch';
    
    // Get water color based on water type and theme
    final Color waterColor = _getWaterColor(waterType);
    
    // Convert geographic coordinates to screen coordinates
    final List<Offset> screenPoints = points.map((p) => _projectPoint(p as LatLng, size)).toList();
    
    if (isPolygon) {
      // Create path for the water polygon
      final Path waterPath = Path()..addPolygon(screenPoints, true);
      
      // Draw water feature with enhanced 2.5D effects
      _drawWaterPolygon(canvas, waterPath, waterColor, waterType);
    } else {
      // Linear water features (rivers, streams, etc.)
      _drawWaterLine(canvas, screenPoints, waterColor, waterType);
    }
    
    // Draw water names for large features at high zoom
    if (showDetails && zoomLevel >= 16 && tags != null && tags.containsKey('name')) {
      if (waterType == 'lake' || waterType == 'pond' || waterType == 'reservoir') {
        // Skip drawing water feature names to remove labels
        // _drawWaterFeatureName(canvas, screenPoints, tags['name'] as String, waterType);
      } else {
        // Skip drawing water line names to remove labels
        // _drawWaterLineName(canvas, screenPoints, tags['name'] as String, waterType);
      }
    }
  }
  
  // Draw a water polygon with enhanced 2.5D effects
  void _drawWaterPolygon(Canvas canvas, Path waterPath, Color waterColor, String waterType) {
    // Skip drawing if the path is empty or invalid
    if (waterPath.getBounds().isEmpty) return;
    
    // Calculate elevation effect based on water type and tilt factor
    final double elevationEffect = _calculateWaterElevation(waterType);
    
    // Calculate shadow intensity based on tilt and water type
    double shadowOpacity = math.min(0.3, tiltFactor * 0.6);
    
    // Shadow paint for water features
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(shadowOpacity)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0) // Add blur for softer shadows
      ..isAntiAlias = true;
    
    // Calculate shadow offset based on tilt factor
    final shadowMultiplier = math.min(1.2, 1.0 + (tiltFactor * 0.2));
    final Offset shadowOffset = Offset(2.0 * shadowMultiplier, 2.0 * shadowMultiplier);
    
    // Draw shadow for water polygons when in 2.5D mode
    if (tiltFactor > 0.1 && elevationEffect > 0.05) {
      final Path shadowPath = waterPath.shift(shadowOffset);
      canvas.drawPath(shadowPath, shadowPaint);
    }
    
    // Create gradient colors based on water depth effect
    final Color baseWaterColor = _addColorVariation(waterColor, variationAmount: 8);
    final Color shallowWaterColor = Color.lerp(baseWaterColor, Colors.white, 0.15) ?? baseWaterColor;
    final Color deepWaterColor = Color.lerp(baseWaterColor, Colors.black, 0.2) ?? baseWaterColor;
    
    // Create gradient for water depth effect
    final Rect bounds = waterPath.getBounds();
    
    // Choose gradient type based on water feature
    Gradient waterGradient;
    
    if (waterType == 'lake' || waterType == 'pond') {
      // Radial gradient for lakes and ponds (deeper in the middle)
      waterGradient = RadialGradient(
        center: Alignment.center,
        radius: 1.0,
        colors: [
          deepWaterColor,
          baseWaterColor,
          Color.lerp(baseWaterColor, shallowWaterColor, 0.5) ?? baseWaterColor,
        ],
        stops: const [0.0, 0.6, 1.0],
      );
    } else {
      // Linear gradient for other water bodies
      waterGradient = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          shallowWaterColor,
          baseWaterColor,
          deepWaterColor,
        ],
        stops: const [0.0, 0.5, 1.0],
      );
    }
    
    // Base paint for the water with gradient
    final Paint waterPaint = Paint()
      ..shader = waterGradient.createShader(bounds)
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw base water
    canvas.drawPath(waterPath, waterPaint);
    
    // Add enhanced ripple effect for lakes, ponds, etc. at higher zoom levels
    if (zoomLevel >= 14 && 
        (waterType == 'lake' || waterType == 'pond' || waterType == 'reservoir')) {
      _addEnhancedRippleEffect(canvas, waterPath, baseWaterColor);
    }
    
    // Add shoreline effect - subtle lighter edge
    final Paint shorelinePaint = Paint()
      ..color = shallowWaterColor.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..isAntiAlias = true;
    
    canvas.drawPath(waterPath, shorelinePaint);
    
    // Add subtle reflection highlights at high zoom levels
    if (zoomLevel >= 16 && tiltFactor > 0.2 && 
        (waterType == 'lake' || waterType == 'pond' || waterType == 'reservoir')) {
      _addWaterReflectionHighlights(canvas, waterPath, baseWaterColor);
    }
  }
  
  // Calculate elevation effect for water features
  double _calculateWaterElevation(String waterType) {
    // Base elevation effect scales with tilt factor
    double baseElevation = math.max(0.0, tiltFactor - 0.1) * 1.5;
    
    // Enhanced elevation effect with zoom level
    double zoomMultiplier = _getZoomElevationFactor();
    
    // Water bodies are generally at base level (negative value means below surroundings)
    double depthMultiplier;
    switch (waterType) {
      case 'river':
      case 'canal':
        depthMultiplier = -0.1; // Slightly below ground
        break;
      case 'lake':
      case 'reservoir':
        depthMultiplier = -0.05 * zoomMultiplier; // Enhanced depth at higher zoom
        break;
      default:
        depthMultiplier = 0.0; // At ground level
    }
    
    return baseElevation * depthMultiplier;
  }
  
  // Get zoom-dependent elevation enhancement factor
  double _getZoomElevationFactor() {
    // Start enhancing 3D effect at zoom 14
    if (zoomLevel <= 14) return 1.0;
    if (zoomLevel >= 20) return 2.5; // Maximum 2.5x enhancement at zoom 20
    
    // Linear interpolation between zoom 14 and 20
    return 1.0 + (zoomLevel - 14) / 2.4;
  }
  
  // Add color variation to water
  Color _addColorVariation(Color baseColor, {int variationAmount = 10}) {
    // Only add variation for higher zoom levels
    if (zoomLevel < 14) return baseColor;
    
    // Add small random variation to color components
    final int variation = math.min(variationAmount, 15); // Limit maximum variation
    final int r = (baseColor.red + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int g = (baseColor.green + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int b = (baseColor.blue + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    
    return Color.fromRGBO(r, g, b, baseColor.opacity);
  }
  
  // Add enhanced ripple effect to water polygons
  void _addEnhancedRippleEffect(Canvas canvas, Path waterPath, Color waterColor) {
    // Only add ripple effect if animationValue is provided and valid
    if (animationValue < 0.0) return;
    
    // Create lighter color for ripple
    final Color rippleColor = Color.lerp(waterColor, Colors.white, 0.2) ?? waterColor;
    
    // Save canvas state to clip ripples within water boundary
    canvas.save();
    canvas.clipPath(waterPath);
    
    // Get bounds of the water feature
    final Rect bounds = waterPath.getBounds();
    final double centerX = bounds.center.dx;
    final double centerY = bounds.center.dy;
    
    // Scale ripple intensity with zoom level
    final int baseRippleCount = 4;
    final int rippleCount = zoomLevel >= 17 ? baseRippleCount + 1 : baseRippleCount;
    
    // Enhanced ripple opacity based on zoom level
    final double baseOpacity = math.min(0.4, 0.25 + (zoomLevel - 14) * 0.025);
    
    // Multiple ripple rings with different phases and sizes
    for (int i = 0; i < rippleCount; i++) {
      // Calculate phase offset for each ripple (0 to 1)
      final double phaseOffset = i / rippleCount;
      
      // Calculate current radius for this ripple
      final double minRadius = bounds.width / 10;
      final double maxRadius = bounds.width / 3;
      final double radiusRange = maxRadius - minRadius;
      
      // Animate radius based on animation value and phase offset
      final double animPhase = (animationValue + phaseOffset) % 1.0;
      
      final double currentRadius = minRadius + (radiusRange * animPhase);
      
      // Ripple opacity fades as it expands
      final double rippleOpacity = math.max(0.0, baseOpacity - animPhase * 0.4);
      
      // Skip if ripple isn't visible
      if (rippleOpacity <= 0.02) continue;
      
      // Create ripple paint with animated opacity
      final Paint ripplePaint = Paint()
        ..color = rippleColor.withOpacity(rippleOpacity)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0 + (1.0 - animPhase) * 1.5 // Thicker when starting, thinner as it expands
        ..isAntiAlias = true;
      
      // Add slight randomness to ripple center position
      final double offsetX = (i % 2 == 0) ? 
          centerX + bounds.width * 0.1 * math.sin(animPhase * math.pi * 2) : 
          centerX - bounds.width * 0.05 * math.cos(animPhase * math.pi * 2);
      
      final double offsetY = (i % 2 == 0) ? 
          centerY - bounds.height * 0.07 * math.cos(animPhase * math.pi * 2) : 
          centerY + bounds.height * 0.08 * math.sin(animPhase * math.pi * 2);
      
      // Draw ripple circle
      canvas.drawCircle(Offset(offsetX, offsetY), currentRadius, ripplePaint);
    }
    
    // Restore canvas to original state
    canvas.restore();
  }
  
  // Add water reflection highlights
  void _addWaterReflectionHighlights(Canvas canvas, Path waterPath, Color waterColor) {
    // Only add reflections when tilted and at higher zoom levels
    if (tiltFactor < 0.2 || zoomLevel < 16) return;
    
    // Save canvas state to clip highlights within water boundary
    canvas.save();
    canvas.clipPath(waterPath);
    
    // Get bounds of the water feature
    final Rect bounds = waterPath.getBounds();
    
    // Create reflection highlights with randomized positions
    final int numHighlights = math.max(3, (bounds.width / 100).round());
    final Color highlightColor = Colors.white;
    
    for (int i = 0; i < numHighlights; i++) {
      // Calculate position within the water polygon
      final double x = bounds.left + bounds.width * (0.2 + 0.6 * _random.nextDouble());
      final double y = bounds.top + bounds.height * (0.2 + 0.6 * _random.nextDouble());
      
      // Highlight size varies
      final double size = 5.0 + 15.0 * _random.nextDouble();
      
      // Opacity varies based on size and animation
      double baseOpacity = 0.1 + 0.2 * _random.nextDouble();
      if (animationValue >= 0) {
        // Add subtle pulsing effect - ensure animationValue is valid
        baseOpacity *= 0.5 + 0.5 * math.sin(((animationValue) * math.pi * 2) + (i * 0.5));
      }
      
      // Create highlight paint
      final Paint highlightPaint = Paint()
        ..shader = RadialGradient(
          colors: [
            highlightColor.withOpacity(baseOpacity),
            highlightColor.withOpacity(0.0),
          ],
        ).createShader(Rect.fromCircle(center: Offset(x, y), radius: size))
        ..style = PaintingStyle.fill
        ..isAntiAlias = true;
      
      // Draw highlight
      canvas.drawCircle(Offset(x, y), size, highlightPaint);
    }
    
    // Restore canvas to original state
    canvas.restore();
  }
  
  // Draw a water line (river, stream, etc.) with enhanced effects
  void _drawWaterLine(Canvas canvas, List<Offset> points, Color waterColor, String waterType) {
    if (points.length < 2) return;
    
    // Get line width based on water type and zoom level
    final double lineWidth = _getWaterLineWidth(waterType);
    
    // Create base path for the water line
    final Path linePath = Path();
    linePath.moveTo(points.first.dx, points.first.dy);
    for (int i = 1; i < points.length; i++) {
      linePath.lineTo(points[i].dx, points[i].dy);
    }
    
    // Enhanced water line rendering
    if (lineWidth > 2.5 && zoomLevel >= 14) {
      // For wider water lines, create an actual polygon path with width
      _drawEnhancedWaterLine(canvas, points, waterColor, waterType, lineWidth);
    } else {
      // For narrower lines at lower zoom levels, use simple line style
      _drawSimpleWaterLine(canvas, linePath, waterColor, waterType, lineWidth);
    }
  }
  
  // Draw a simple water line with basic styling
  void _drawSimpleWaterLine(Canvas canvas, Path linePath, Color waterColor, String waterType, double lineWidth) {
    // Line paint for the water
    final Paint linePaint = Paint()
      ..color = waterColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = lineWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    // Draw water line
    canvas.drawPath(linePath, linePaint);
    
    // Add flow animation for rivers and canals at higher zoom levels
    if (zoomLevel >= 15 && (waterType == 'river' || waterType == 'canal')) {
      _addEnhancedFlowEffect(canvas, linePath, linePath, waterColor, lineWidth);
    }
  }
  
  // Draw an enhanced water line with banks and flow
  void _drawEnhancedWaterLine(Canvas canvas, List<Offset> points, Color waterColor, String waterType, double lineWidth) {
    if (points.length < 2) return;
    
    // Create a path for the water line with width (like a polygon)
    final Path widePath = Path();
    
    // Create points for both banks of the river/stream
    List<Offset> leftBank = [];
    List<Offset> rightBank = [];
    
    // Generate points for both banks
    for (int i = 0; i < points.length; i++) {
      // Get current point and vectors to adjacent points
      Offset current = points[i];
      Offset? prev = i > 0 ? points[i - 1] : null;
      Offset? next = i < points.length - 1 ? points[i + 1] : null;
      
      // Calculate bisector vector (perpendicular to flow direction)
      Offset bisector;
      
      if (prev == null) {
        // First point - use direction to next point
        Offset dir = (next! - current).normalize();
        bisector = Offset(-dir.dy, dir.dx); // 90 degree rotation
      } else if (next == null) {
        // Last point - use direction from previous point
        Offset dir = (current - prev).normalize();
        bisector = Offset(-dir.dy, dir.dx); // 90 degree rotation
      } else {
        // Middle point - use average of directions to create smooth corners
        Offset dir1 = (current - prev).normalize();
        Offset dir2 = (next - current).normalize();
        
        // Calculate bisector (perpendicular to average direction)
        Offset avgDir = Offset(
          (dir1.dx + dir2.dx) / 2,
          (dir1.dy + dir2.dy) / 2
        ).normalize();
        
        bisector = Offset(-avgDir.dy, avgDir.dx);
        
        // Adjust bisector length at sharp corners to avoid spikes
        final double dot = dir1.dx * dir2.dx + dir1.dy * dir2.dy;
        if (dot < 0.7) { // Angle is more than ~45 degrees
          // Increase bisector length for sharper corners
          double lengthFactor = 1.0 / math.max(0.3, math.sqrt((1.0 + dot) / 2.0));
          bisector = Offset(bisector.dx * lengthFactor, bisector.dy * lengthFactor);
        }
      }
      
      // Calculate points on both banks of the water
      double halfWidth = lineWidth / 2.0;
      
      // Make width variation for natural look
      if (i > 0 && i < points.length - 1 && waterType == 'river') {
        // Add subtle width variation along the river for natural look
        final double variation = 0.8 + 0.4 * math.sin(i * 0.5);
        halfWidth *= variation;
      }
      
      leftBank.add(Offset(
        current.dx + bisector.dx * halfWidth,
        current.dy + bisector.dy * halfWidth
      ));
      
      rightBank.add(Offset(
        current.dx - bisector.dx * halfWidth,
        current.dy - bisector.dy * halfWidth
      ));
    }
    
    // Build the path - start from left bank, go forward, then right bank backward
    if (leftBank.isNotEmpty) {
      widePath.moveTo(leftBank.first.dx, leftBank.first.dy);
      
      // Add left bank points
      for (int i = 1; i < leftBank.length; i++) {
        widePath.lineTo(leftBank[i].dx, leftBank[i].dy);
      }
      
      // Add right bank points in reverse order
      for (int i = rightBank.length - 1; i >= 0; i--) {
        widePath.lineTo(rightBank[i].dx, rightBank[i].dy);
      }
      
      // Close the path
      widePath.close();
    }
    
    // Create gradient for water depth effect
    final Rect bounds = widePath.getBounds();
    final Color baseWaterColor = _addColorVariation(waterColor, variationAmount: 8);
    final Color shallowWaterColor = Color.lerp(baseWaterColor, Colors.white, 0.15) ?? baseWaterColor;
    final Color deepWaterColor = Color.lerp(baseWaterColor, Colors.black, 0.15) ?? baseWaterColor;
    
    // Create gradient for water flow
    final Gradient waterGradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        baseWaterColor,
        deepWaterColor,
        baseWaterColor,
        shallowWaterColor,
        baseWaterColor,
      ],
      stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
    );
    
    // Water fill paint with gradient
    final Paint waterPaint = Paint()
      ..shader = waterGradient.createShader(bounds)
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw the water body
    canvas.drawPath(widePath, waterPaint);
    
    // Add bank detail for rivers at higher zoom levels
    if (zoomLevel >= 16 && waterType == 'river') {
      // Create a subtle bank line
      final Paint bankPaint = Paint()
        ..color = shallowWaterColor.withOpacity(0.5)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.8
        ..isAntiAlias = true;
      
      canvas.drawPath(widePath, bankPaint);
    }
    
    // Add flow animation for rivers and canals
    if (animationValue >= 0 && (waterType == 'river' || waterType == 'canal')) {
      // Create a simple line path down the center for flow animation
      final Path centerPath = Path();
      centerPath.moveTo(points.first.dx, points.first.dy);
      for (int i = 1; i < points.length; i++) {
        centerPath.lineTo(points[i].dx, points[i].dy);
      }
      
      _addEnhancedFlowEffect(canvas, centerPath, widePath, waterColor, lineWidth);
    }
  }
  
  // Add enhanced flow effect to water lines
  void _addEnhancedFlowEffect(Canvas canvas, Path centerPath, Path waterPath, Color waterColor, double lineWidth) {
    // Only add flow effect if animationValue is provided
    if (animationValue < 0.0) return;
    
    // Create lighter color for flow
    final Color flowColor = Color.lerp(waterColor, Colors.white, 0.4) ?? waterColor;
    
    // Clip to water path to keep flow within banks
    canvas.save();
    canvas.clipPath(waterPath);
    
    // Use the path metrics to draw flow particles along the path
    final PathMetrics metrics = centerPath.computeMetrics();
    
    for (final metric in metrics) {
      // Calculate number of flow particles based on path length
      final int numParticles = math.max(5, (metric.length / 40).round());
      if (numParticles <= 0) continue;
      
      for (int i = 0; i < numParticles; i++) {
        // Calculate position along path based on animation, ensuring we handle animationValue safely
        final double progress = (i / numParticles + animationValue) % 1.0;
        
        // Get the position and tangent at this point
        final Tangent? tangent = metric.getTangentForOffset(metric.length * progress);
        if (tangent == null) continue;
        
        // Flow particle opacity varies with animation
        final double opacity = math.sin(progress * math.pi) * 0.8;
        
        // Skip if particle isn't visible
        if (opacity <= 0.05) continue;
        
        // Size varies with animation phase
        final double size = (lineWidth * 0.3) * (0.5 + 0.5 * math.sin(progress * math.pi));
        
        // Position with slight randomness
        final Offset pos = tangent.position;
        final Offset dir = tangent.vector.normalize();
        final Offset perpDir = Offset(-dir.dy, dir.dx); // Perpendicular to flow
        
        // Add slight random perpendicular offset for more natural flow
        final double perpOffset = (progress * 7) % 3 - 1.5; // Range from -1.5 to 1.5
        final Offset adjustedPos = Offset(
          pos.dx + perpDir.dx * perpOffset * 0.8,
          pos.dy + perpDir.dy * perpOffset * 0.8
        );
        
        // Draw enhanced flow particle
        final Paint particlePaint = Paint()
          ..shader = RadialGradient(
            colors: [
              flowColor.withOpacity(opacity),
              flowColor.withOpacity(0),
            ],
          ).createShader(Rect.fromCircle(center: adjustedPos, radius: size * 2))
          ..style = PaintingStyle.fill
          ..isAntiAlias = true;
        
        // Draw a circle for each particle
        canvas.drawCircle(adjustedPos, size, particlePaint);
        
        // For some particles, add a trailing effect
        if (i % 3 == 0 && opacity > 0.3) {
          // Draw a short trailing line in the direction of flow
          final Paint trailPaint = Paint()
            ..color = flowColor.withOpacity(opacity * 0.5)
            ..style = PaintingStyle.stroke
            ..strokeWidth = size * 0.7
            ..strokeCap = StrokeCap.round
            ..isAntiAlias = true;
          
        canvas.drawLine(
            adjustedPos,
          Offset(
              adjustedPos.dx - dir.dx * size * 2.5,
              adjustedPos.dy - dir.dy * size * 2.5,
          ),
            trailPaint
        );
      }
    }
    }
    
    canvas.restore();
  }
  
  // Draw a name for a water polygon
  void _drawWaterFeatureName(Canvas canvas, List<Offset> points, String name, String waterType) {
    if (points.length < 3) return;
    
    // Calculate centroid of the polygon for text placement
    double sumX = 0, sumY = 0;
    for (final point in points) {
      sumX += point.dx;
      sumY += point.dy;
    }
    
    Offset center = Offset(sumX / points.length, sumY / points.length);
    
    // Create text painter
    final textStyle = TextStyle(
      color: theme == 'dark' ? Colors.white.withOpacity(0.9) : Colors.white.withOpacity(0.9),
      fontSize: 12,
      fontWeight: FontWeight.w500,
      shadows: [
        Shadow(
          offset: const Offset(1.0, 1.0),
          blurRadius: 2.0,
          color: Colors.black.withOpacity(0.7),
        ),
      ],
    );
    
    final textSpan = TextSpan(
      text: name,
      style: textStyle,
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    // Draw text centered on the polygon
    textPainter.paint(
      canvas, 
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }
  
  // Draw a name for a water line (river, etc.)
  void _drawWaterLineName(Canvas canvas, List<Offset> points, String name, String waterType) {
    if (points.length < 2) return;
    
    // Find a suitable segment in the middle of the line to place the text
    int midIndex = points.length ~/ 2;
    if (midIndex >= points.length - 1) midIndex = points.length - 2;
    
    Offset start = points[midIndex];
    Offset end = points[midIndex + 1];
    
    // Calculate angle for text rotation
    double angle = math.atan2(end.dy - start.dy, end.dx - start.dx);
    
    // Flip text if angle would make it upside down
    if (angle > math.pi / 2 || angle < -math.pi / 2) {
      angle += math.pi;
      Offset temp = start;
      start = end;
      end = temp;
    }
    
    // Create text painter
    final textStyle = TextStyle(
      color: Colors.white,
      fontSize: 10,
      fontWeight: FontWeight.w500,
      fontStyle: FontStyle.italic,
      shadows: [
        Shadow(
          offset: const Offset(1.0, 1.0),
          blurRadius: 1.5,
          color: Colors.black.withOpacity(0.7),
        ),
      ],
    );
    
    final textSpan = TextSpan(
      text: name,
      style: textStyle,
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    // Calculate text position
    Offset center = Offset(
      (start.dx + end.dx) / 2,
      (start.dy + end.dy) / 2,
    );
    
    // Save canvas state before rotating
    canvas.save();
    
    // Translate to the center point
    canvas.translate(center.dx, center.dy);
    
    // Rotate canvas
    canvas.rotate(angle);
    
    // Draw text centered on the road
    textPainter.paint(
      canvas, 
      Offset(-textPainter.width / 2, -textPainter.height / 2),
    );
    
    // Restore canvas to original state
    canvas.restore();
  }
  
  // Get appropriate color for a water feature
  Color _getWaterColor(String waterType) {
    // If we have a custom color provided, use it as a base
    if (waterColor != const Color(0xFF1976D2)) {
      // Adjust opacity and shading based on water type
      switch (waterType) {
        case 'river':
          return waterColor.withOpacity(0.75);
        case 'stream':
          return Color.lerp(waterColor, Colors.white, 0.3)!.withOpacity(0.65);
        case 'lake':
          return Color.lerp(waterColor, Colors.black, 0.1)!.withOpacity(0.7);
        default:
          return waterColor.withOpacity(0.7);
      }
    }
    
    // Otherwise use theme-based colors
    final String effectiveTheme = _waterTypeColors.containsKey(theme) ? theme : 'vibrant';
    
    // Get the color for this water type, or fall back to default if not found
    return _waterTypeColors[effectiveTheme]![waterType] ?? 
           _waterTypeColors[effectiveTheme]!['water']!;
  }
  
  // Get appropriate width for a water line
  double _getWaterLineWidth(String waterType) {
    // Base widths for different water types
    double baseWidth;
    switch (waterType) {
      case 'river':
        baseWidth = 5.0;
        break;
      case 'canal':
        baseWidth = 4.0;
        break;
      case 'stream':
        baseWidth = 3.0;
        break;
      case 'drain':
        baseWidth = 2.0;
        break;
      case 'ditch':
        baseWidth = 1.5;
        break;
      default:
        baseWidth = 2.0;
    }
    
    // Scale width based on zoom level
    double zoomScale;
    if (zoomLevel < 10) {
      zoomScale = 0.4;
    } else if (zoomLevel < 12) {
      zoomScale = 0.6;
    } else if (zoomLevel < 14) {
      zoomScale = 0.8;
    } else if (zoomLevel < 16) {
      zoomScale = 1.0;
    } else if (zoomLevel < 18) {
      zoomScale = 1.2;
    } else {
      zoomScale = 1.4;
    }
    
    return baseWidth * zoomScale;
  }

  @override
  bool shouldRepaint(OSMWaterPainter oldDelegate) {
    return oldDelegate.waterFeatures != waterFeatures ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.visibleBounds != visibleBounds ||
           oldDelegate.theme != theme ||
           oldDelegate.zoomBucket != zoomBucket ||
           oldDelegate.showDetails != showDetails ||
           oldDelegate.animationValue != animationValue ||
           oldDelegate.waterColor != waterColor ||
           oldDelegate.waterOutlineColor != waterOutlineColor ||
           oldDelegate.mapCamera != mapCamera;
  }
} 