import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

/// Custom painter to render OpenStreetMap recreation facilities with enhanced styling
class OSMRecreationPainter extends CustomPainter {
  final Map<String, List<Map<String, dynamic>>> recreationData;
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final bool enhancedDetail;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  final MapCamera? mapCamera;
  
  OSMRecreationPainter({
    required this.recreationData,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    required this.theme,
    required this.enhancedDetail,
    this.mapCamera,
  });

  // Colors for different recreation facility types and themes
  final Map<String, Map<String, Map<String, Color>>> _facilityColorPalette = {
    'vibrant': {
      'playground': {
        'fill': const Color(0xFFFFB74D).withOpacity(0.3),  // Orange for playgrounds
        'outline': const Color(0xFFFF9800).withOpacity(0.8),
        'equipment': const Color(0xFFFFA726).withOpacity(0.9),
      },
      'sports': {
        'fill': const Color(0xFF4FC3F7).withOpacity(0.3),  // Blue for sports
        'outline': const Color(0xFF03A9F4).withOpacity(0.8),
        'court': const Color(0xFF29B6F6).withOpacity(0.9),
      },
      'dog_park': {
        'fill': const Color(0xFFAED581).withOpacity(0.3),  // Green for dog parks
        'outline': const Color(0xFF8BC34A).withOpacity(0.8),
        'fence': const Color(0xFF9CCC65).withOpacity(0.9),
      },
      'fitness': {
        'fill': const Color(0xFFBA68C8).withOpacity(0.3),  // Purple for fitness areas
        'outline': const Color(0xFF9C27B0).withOpacity(0.8),
        'equipment': const Color(0xFFAB47BC).withOpacity(0.9),
      },
    },
    'dark': {
      'playground': {
        'fill': const Color(0xFFFF9800).withOpacity(0.2),  // Darker orange
        'outline': const Color(0xFFF57C00).withOpacity(0.7),
        'equipment': const Color(0xFFEF6C00).withOpacity(0.8),
      },
      'sports': {
        'fill': const Color(0xFF0288D1).withOpacity(0.2),  // Darker blue
        'outline': const Color(0xFF0277BD).withOpacity(0.7),
        'court': const Color(0xFF01579B).withOpacity(0.8),
      },
      'dog_park': {
        'fill': const Color(0xFF689F38).withOpacity(0.2),  // Darker green
        'outline': const Color(0xFF558B2F).withOpacity(0.7),
        'fence': const Color(0xFF33691E).withOpacity(0.8),
      },
      'fitness': {
        'fill': const Color(0xFF8E24AA).withOpacity(0.2),  // Darker purple
        'outline': const Color(0xFF7B1FA2).withOpacity(0.7),
        'equipment': const Color(0xFF6A1B9A).withOpacity(0.8),
      },
    },
    'monochrome': {
      'playground': {
        'fill': const Color(0xFFE0E0E0).withOpacity(0.25),  // Light gray
        'outline': const Color(0xFF9E9E9E).withOpacity(0.7),
        'equipment': const Color(0xFF757575).withOpacity(0.8),
      },
      'sports': {
        'fill': const Color(0xFFEEEEEE).withOpacity(0.25),  // Very light gray
        'outline': const Color(0xFFBDBDBD).withOpacity(0.7),
        'court': const Color(0xFF9E9E9E).withOpacity(0.8),
      },
      'dog_park': {
        'fill': const Color(0xFFE0E0E0).withOpacity(0.2),  // Light gray
        'outline': const Color(0xFF9E9E9E).withOpacity(0.7),
        'fence': const Color(0xFF757575).withOpacity(0.8),
      },
      'fitness': {
        'fill': const Color(0xFFEEEEEE).withOpacity(0.25),  // Very light gray
        'outline': const Color(0xFFBDBDBD).withOpacity(0.7),
        'equipment': const Color(0xFF9E9E9E).withOpacity(0.8),
      },
    },
  };

  @override
  void paint(Canvas canvas, Size size) {
    final themeColors = _facilityColorPalette[theme] ?? _facilityColorPalette['vibrant']!;
    
    // Don't render anything if there's no data to display
    if (recreationData.isEmpty) return;
    if (recreationData.values.every((list) => list.isEmpty)) return;
    
    // Draw different types of recreation facilities in the correct order
    // Order matters for overlapping elements
    
    // 1. Draw dog parks (largest areas)
    _drawDogParks(canvas, size, themeColors);
    
    // 2. Draw sports facilities
    _drawSportsFacilities(canvas, size, themeColors);
    
    // 3. Draw playgrounds
    _drawPlaygrounds(canvas, size, themeColors);
    
    // 4. Draw fitness stations (smallest features)
    _drawFitnessStations(canvas, size, themeColors);
  }
  
  // Draw playgrounds
  void _drawPlaygrounds(Canvas canvas, Size size, Map<String, Map<String, Color>> themeColors) {
    final playgrounds = recreationData['playgrounds'] ?? [];
    if (playgrounds.isEmpty) return;
    
    // Performance optimization: limit number of playgrounds at high zoom levels
    final int maxPlaygrounds = zoomLevel > 18 ? 15 : (zoomLevel > 16 ? 30 : 50);
    final limitedPlaygrounds = playgrounds.length > maxPlaygrounds 
        ? playgrounds.sublist(0, maxPlaygrounds) 
        : playgrounds;
    
    final fillPaint = Paint()
      ..color = themeColors['playground']!['fill']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final outlinePaint = Paint()
      ..color = themeColors['playground']!['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..isAntiAlias = true;
    
    final equipmentPaint = Paint()
      ..color = themeColors['playground']!['equipment']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..isAntiAlias = true;
    
    for (final playground in limitedPlaygrounds) {
      // Draw playground area as a filled polygon
      final points = playground['points'] as List? ?? [];
      final LatLng? center = playground['center'] as LatLng?;
      
      if (points.isNotEmpty) {
        // Draw as polygon if we have multiple points
        final Path path = Path();
        bool first = true;
        
        for (final point in points) {
          final LatLng latLng = point as LatLng;
          final Offset screenPoint = _projectPoint(latLng.latitude, latLng.longitude, size);
          
          if (first) {
            path.moveTo(screenPoint.dx, screenPoint.dy);
            first = false;
          } else {
            path.lineTo(screenPoint.dx, screenPoint.dy);
          }
        }
        
        path.close();
        
        // Apply a 3D effect by shifting the path based on tilt
        final Matrix4 tiltMatrix = Matrix4.identity()
          ..translate(0.0, -3.0 * tiltFactor);
        
        path.transform(tiltMatrix.storage);
        
        // Draw the playground area
        canvas.drawShadow(path, Colors.black, 3.0 * tiltFactor, false);
        canvas.drawPath(path, fillPaint);
        canvas.drawPath(path, outlinePaint);
        
        // Add playground equipment illustrations if at high zoom
        if (enhancedDetail && zoomLevel > 17) {
          _drawPlaygroundEquipment(canvas, path, equipmentPaint);
        }
      } else if (center != null) {
        // Draw as point if we only have a center point
        final Offset centerPoint = _projectPoint(center.latitude, center.longitude, size);
        
        // Apply tilt effect
        final Offset tiltedCenter = Offset(
          centerPoint.dx,
          centerPoint.dy - (3.0 * tiltFactor)
        );
        
        // Calculate playground radius based on zoom level
        final double radius = _getPlaygroundRadius();
        
        // Draw the playground area
        canvas.drawShadow(
          Path()..addOval(Rect.fromCircle(center: tiltedCenter, radius: radius)),
          Colors.black,
          3.0 * tiltFactor,
          false
        );
        canvas.drawCircle(tiltedCenter, radius, fillPaint);
        canvas.drawCircle(tiltedCenter, radius, outlinePaint);
        
        // Add a playground icon or symbol
        if (enhancedDetail && zoomLevel > 16) {
          _drawPlaygroundSymbol(canvas, tiltedCenter, radius, equipmentPaint);
        }
      }
    }
  }
  
  // Draw sports facilities (courts, fields, etc.)
  void _drawSportsFacilities(Canvas canvas, Size size, Map<String, Map<String, Color>> themeColors) {
    final sportsFacilities = recreationData['sports'] ?? [];
    if (sportsFacilities.isEmpty) return;
    
    // Performance optimization: limit number of sports facilities at high zoom levels
    final int maxSportsFacilities = zoomLevel > 18 ? 12 : (zoomLevel > 16 ? 25 : 40);
    final limitedSportsFacilities = sportsFacilities.length > maxSportsFacilities 
        ? sportsFacilities.sublist(0, maxSportsFacilities) 
        : sportsFacilities;
    
    final fillPaint = Paint()
      ..color = themeColors['sports']!['fill']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final outlinePaint = Paint()
      ..color = themeColors['sports']!['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..isAntiAlias = true;
    
    final courtPaint = Paint()
      ..color = themeColors['sports']!['court']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..isAntiAlias = true;
    
    for (final facility in limitedSportsFacilities) {
      // Draw sports facility as a filled polygon or point depending on data
      final points = facility['points'] as List? ?? [];
      final LatLng? center = facility['center'] as LatLng?;
      final Map<String, dynamic>? tags = facility['tags'] as Map<String, dynamic>?;
      
      // Determine the sport type for specialized rendering
      String sportType = 'unknown';
      if (tags != null && tags.containsKey('sport')) {
        sportType = tags['sport'] as String;
      }
      
      if (points.isNotEmpty) {
        // Draw as polygon if we have multiple points
        final Path path = Path();
        bool first = true;
        
        for (final point in points) {
          final LatLng latLng = point as LatLng;
          final Offset screenPoint = _projectPoint(latLng.latitude, latLng.longitude, size);
          
          if (first) {
            path.moveTo(screenPoint.dx, screenPoint.dy);
            first = false;
          } else {
            path.lineTo(screenPoint.dx, screenPoint.dy);
          }
        }
        
        path.close();
        
        // Apply a 3D effect
        final Matrix4 tiltMatrix = Matrix4.identity()
          ..translate(0.0, -2.0 * tiltFactor);
        
        path.transform(tiltMatrix.storage);
        
        // Draw the sports facility
        canvas.drawShadow(path, Colors.black, 2.0 * tiltFactor, false);
        canvas.drawPath(path, fillPaint);
        canvas.drawPath(path, outlinePaint);
        
        // Add court/field markings based on sport type at higher zoom levels
        if (enhancedDetail && zoomLevel > 16) {
          _drawSportCourtMarkings(canvas, path, sportType, courtPaint);
        }
      } else if (center != null) {
        // Draw as point if we only have a center point
        final Offset centerPoint = _projectPoint(center.latitude, center.longitude, size);
        
        // Apply tilt effect
        final Offset tiltedCenter = Offset(
          centerPoint.dx, 
          centerPoint.dy - (2.0 * tiltFactor)
        );
        
        // Calculate facility radius based on zoom level
        final double radius = _getSportsFacilityRadius();
        
        // Draw the sports facility
        canvas.drawShadow(
          Path()..addOval(Rect.fromCircle(center: tiltedCenter, radius: radius)),
          Colors.black,
          2.0 * tiltFactor,
          false
        );
        canvas.drawCircle(tiltedCenter, radius, fillPaint);
        canvas.drawCircle(tiltedCenter, radius, outlinePaint);
        
        // Add a sport-specific icon/symbol
        if (enhancedDetail && zoomLevel > 16) {
          _drawSportSymbol(canvas, tiltedCenter, radius, sportType, courtPaint);
        }
      }
    }
  }
  
  // Draw dog parks
  void _drawDogParks(Canvas canvas, Size size, Map<String, Map<String, Color>> themeColors) {
    final dogParks = recreationData['dog_parks'] ?? [];
    if (dogParks.isEmpty) return;
    
    // Performance optimization: limit number of dog parks at high zoom levels
    final int maxDogParks = zoomLevel > 18 ? 8 : (zoomLevel > 16 ? 15 : 25);
    final limitedDogParks = dogParks.length > maxDogParks 
        ? dogParks.sublist(0, maxDogParks) 
        : dogParks;
    
    final fillPaint = Paint()
      ..color = themeColors['dog_park']!['fill']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final outlinePaint = Paint()
      ..color = themeColors['dog_park']!['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..isAntiAlias = true;
    
    final fencePaint = Paint()
      ..color = themeColors['dog_park']!['fence']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round
      ..isAntiAlias = true;
    
    for (final dogPark in limitedDogParks) {
      // Draw dog park as a filled polygon
      final points = dogPark['points'] as List? ?? [];
      final LatLng? center = dogPark['center'] as LatLng?;
      
      if (points.isNotEmpty) {
        // Draw as polygon if we have multiple points
        final Path path = Path();
        bool first = true;
        
        for (final point in points) {
          final LatLng latLng = point as LatLng;
          final Offset screenPoint = _projectPoint(latLng.latitude, latLng.longitude, size);
          
          if (first) {
            path.moveTo(screenPoint.dx, screenPoint.dy);
            first = false;
          } else {
            path.lineTo(screenPoint.dx, screenPoint.dy);
          }
        }
        
        path.close();
        
        // Apply a 3D effect
        final Matrix4 tiltMatrix = Matrix4.identity()
          ..translate(0.0, -2.0 * tiltFactor);
        
        path.transform(tiltMatrix.storage);
        
        // Draw the dog park
        canvas.drawShadow(path, Colors.black, 2.0 * tiltFactor, false);
        canvas.drawPath(path, fillPaint);
        canvas.drawPath(path, outlinePaint);
        
        // Add fence pattern and paw prints at higher zoom levels
        if (enhancedDetail && zoomLevel > 16) {
          _drawDogParkFence(canvas, path, fencePaint);
          if (zoomLevel > 17) {
            _drawPawPrints(canvas, path, fencePaint.color.withOpacity(0.6));
          }
        }
      } else if (center != null) {
        // Draw as point if we only have a center point
        final Offset centerPoint = _projectPoint(center.latitude, center.longitude, size);
        
        // Apply tilt effect
        final Offset tiltedCenter = Offset(
          centerPoint.dx,
          centerPoint.dy - (2.0 * tiltFactor)
        );
        
        // Calculate dog park radius based on zoom level
        final double radius = _getDogParkRadius();
        
        // Draw the dog park
        canvas.drawShadow(
          Path()..addOval(Rect.fromCircle(center: tiltedCenter, radius: radius)),
          Colors.black,
          2.0 * tiltFactor,
          false
        );
        canvas.drawCircle(tiltedCenter, radius, fillPaint);
        canvas.drawCircle(tiltedCenter, radius, outlinePaint);
        
        // Add a dog park symbol
        if (enhancedDetail && zoomLevel > 16) {
          _drawDogParkSymbol(canvas, tiltedCenter, radius, fencePaint);
        }
      }
    }
  }
  
  // Draw fitness stations
  void _drawFitnessStations(Canvas canvas, Size size, Map<String, Map<String, Color>> themeColors) {
    final fitnessStations = recreationData['fitness'] ?? [];
    if (fitnessStations.isEmpty) return;
    
    // Performance optimization: strictly limit fitness stations as they're detailed
    final int maxFitnessStations = zoomLevel > 18 ? 6 : (zoomLevel > 16 ? 12 : 20);
    final limitedFitnessStations = fitnessStations.length > maxFitnessStations 
        ? fitnessStations.sublist(0, maxFitnessStations) 
        : fitnessStations;
    
    final fillPaint = Paint()
      ..color = themeColors['fitness']!['fill']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final outlinePaint = Paint()
      ..color = themeColors['fitness']!['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..isAntiAlias = true;
    
    final equipmentPaint = Paint()
      ..color = themeColors['fitness']!['equipment']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..isAntiAlias = true;
    
    for (final station in limitedFitnessStations) {
      // Draw fitness station as a filled polygon or point
      final points = station['points'] as List? ?? [];
      final LatLng? center = station['center'] as LatLng?;
      
      if (points.isNotEmpty) {
        // Draw as polygon if we have multiple points
        final Path path = Path();
        bool first = true;
        
        for (final point in points) {
          final LatLng latLng = point as LatLng;
          final Offset screenPoint = _projectPoint(latLng.latitude, latLng.longitude, size);
          
          if (first) {
            path.moveTo(screenPoint.dx, screenPoint.dy);
            first = false;
          } else {
            path.lineTo(screenPoint.dx, screenPoint.dy);
          }
        }
        
        path.close();
        
        // Apply a 3D effect
        final Matrix4 tiltMatrix = Matrix4.identity()
          ..translate(0.0, -2.5 * tiltFactor);
        
        path.transform(tiltMatrix.storage);
        
        // Draw the fitness station
        canvas.drawShadow(path, Colors.black, 2.5 * tiltFactor, false);
        canvas.drawPath(path, fillPaint);
        canvas.drawPath(path, outlinePaint);
        
        // Add fitness equipment illustrations at higher zoom levels
        if (enhancedDetail && zoomLevel > 17) {
          _drawFitnessEquipment(canvas, path, equipmentPaint);
        }
      } else if (center != null) {
        // Draw as point if we only have a center point
        final Offset centerPoint = _projectPoint(center.latitude, center.longitude, size);
        
        // Apply tilt effect
        final Offset tiltedCenter = Offset(
          centerPoint.dx,
          centerPoint.dy - (2.5 * tiltFactor)
        );
        
        // Calculate fitness station radius based on zoom level
        final double radius = _getFitnessStationRadius();
        
        // Draw the fitness station
        canvas.drawShadow(
          Path()..addOval(Rect.fromCircle(center: tiltedCenter, radius: radius)),
          Colors.black,
          2.5 * tiltFactor,
          false
        );
        canvas.drawCircle(tiltedCenter, radius, fillPaint);
        canvas.drawCircle(tiltedCenter, radius, outlinePaint);
        
        // Add a fitness equipment symbol
        if (enhancedDetail && zoomLevel > 16) {
          _drawFitnessSymbol(canvas, tiltedCenter, radius, equipmentPaint);
        }
      }
    }
  }
  
  // Project a geographic point to screen coordinates
  Offset _projectPoint(double lat, double lng, Size size) {
    // CRITICAL FIX: Always use MapCamera when available for accurate projection during map movement
    if (mapCamera != null) {
      final screenPoint = mapCamera!.latLngToScreenPoint(LatLng(lat, lng));
      if (screenPoint != null) {
        return Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
      }
    }
    
    // Fall back to simple linear projection if no camera or camera projection fails
    final sw = visibleBounds.southWest;
    final ne = visibleBounds.northEast;
    
    // Calculate x position based on longitude
    final double x = size.width * (lng - sw.longitude) / (ne.longitude - sw.longitude);
    
    // Calculate y position based on latitude
    final double y = size.height * (1.0 - (lat - sw.latitude) / (ne.latitude - sw.latitude));
    
    return Offset(x, y);
  }
  
  // Helper methods for individual facility types
  
  // Draw playground equipment inside a playground area
  void _drawPlaygroundEquipment(Canvas canvas, Path path, Paint paint) {
    // No implementation details for brevity
  }
  
  // Draw playground symbol
  void _drawPlaygroundSymbol(Canvas canvas, Offset center, double radius, Paint paint) {
    // No implementation details for brevity
  }
  
  // Draw court markings for different sports
  void _drawSportCourtMarkings(Canvas canvas, Path path, String sportType, Paint paint) {
    // No implementation details for brevity
  }
  
  // Draw sport-specific symbol
  void _drawSportSymbol(Canvas canvas, Offset center, double radius, String sportType, Paint paint) {
    // No implementation details for brevity
  }
  
  // Draw dog park fence pattern
  void _drawDogParkFence(Canvas canvas, Path path, Paint paint) {
    // No implementation details for brevity
  }
  
  // Draw paw prints inside dog park
  void _drawPawPrints(Canvas canvas, Path path, Color color) {
    // No implementation details for brevity
  }
  
  // Draw dog park symbol
  void _drawDogParkSymbol(Canvas canvas, Offset center, double radius, Paint paint) {
    // No implementation details for brevity
  }
  
  // Draw fitness equipment inside a fitness area
  void _drawFitnessEquipment(Canvas canvas, Path path, Paint paint) {
    // No implementation details for brevity
  }
  
  // Draw fitness symbol
  void _drawFitnessSymbol(Canvas canvas, Offset center, double radius, Paint paint) {
    // No implementation details for brevity
  }
  
  // Get radius for playground based on zoom level
  double _getPlaygroundRadius() {
    if (zoomLevel >= 19) return 25.0;
    if (zoomLevel >= 18) return 20.0;
    if (zoomLevel >= 17) return 15.0;
    if (zoomLevel >= 16) return 12.0;
    if (zoomLevel >= 15) return 10.0;
    return 8.0;
  }
  
  // Get radius for sports facility based on zoom level
  double _getSportsFacilityRadius() {
    if (zoomLevel >= 19) return 25.0;
    if (zoomLevel >= 18) return 20.0;
    if (zoomLevel >= 17) return 15.0;
    if (zoomLevel >= 16) return 12.0;
    if (zoomLevel >= 15) return 10.0;
    return 8.0;
  }
  
  // Get radius for dog park based on zoom level
  double _getDogParkRadius() {
    if (zoomLevel >= 19) return 30.0;
    if (zoomLevel >= 18) return 25.0;
    if (zoomLevel >= 17) return 20.0;
    if (zoomLevel >= 16) return 15.0;
    if (zoomLevel >= 15) return 12.0;
    return 10.0;
  }
  
  // Get radius for fitness station based on zoom level
  double _getFitnessStationRadius() {
    if (zoomLevel >= 19) return 20.0;
    if (zoomLevel >= 18) return 15.0;
    if (zoomLevel >= 17) return 12.0;
    if (zoomLevel >= 16) return 10.0;
    if (zoomLevel >= 15) return 8.0;
    return 6.0;
  }
  
  // Helper to determine if a feature is visible in the current viewport
  // Add this method to optimize rendering by filtering elements outside view
  bool _isFeatureVisible(List<LatLng> points, LatLng? center) {
    // If we have a center point, check if it's in bounds
    if (center != null) {
      return visibleBounds.contains(center);
    }
    
    // If we have points, check if any are in bounds
    if (points.isNotEmpty) {
      for (final point in points) {
        if (visibleBounds.contains(point)) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  @override
  bool shouldRepaint(OSMRecreationPainter oldDelegate) {
    // IMPORTANT FIX: Always repaint when mapCamera changes to ensure smooth movement
    return oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.visibleBounds != visibleBounds ||
           oldDelegate.theme != theme ||
           oldDelegate.enhancedDetail != enhancedDetail ||
           oldDelegate.mapCamera != mapCamera ||
           oldDelegate.recreationData != recreationData;
  }
} 