import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

import '../osm_data_processor.dart';
import '../../../../services/map_cache_manager.dart';
import '../../map_caching/zoom_level_manager.dart';
import '../../map_caching/map_cache_extension.dart';
import '../../map_caching/map_cache_coordinator.dart';
import '../../map_caching/osm_throttle_manager.dart';

/// Data provider class that handles recreation facilities data fetching and caching
/// This class is responsible for all the data-related logic, separating it from UI rendering
class OSMRecreationDataProvider {
  // Data processing and caching dependencies
  final OSMDataProcessor _dataProcessor = OSMDataProcessor();
  final MapCacheManager _cacheManager = MapCacheManager();
  final ZoomLevelManager _zoomManager = ZoomLevelManager();
  final OSMThrottleManager _throttleManager = OSMThrottleManager();
  
  // Callback for error handling
  final Function(String errorMessage)? onError;
  
  // State variables
  Map<String, List<Map<String, dynamic>>> _recreationData = {
    'playgrounds': [],
    'sports': [],
    'dog_parks': [],
    'fitness': []
  };
  bool _isLoading = true;
  bool _needsRefresh = true;
  String _lastBoundsKey = "";
  bool _hasError = false;
  bool _didInitialFetch = false;
  bool _emergencyPerformanceMode = false; // Add emergency mode for performance issues
  DateTime _lastFreezeDetected = DateTime.now().subtract(const Duration(minutes: 5));
  
  // Track last fetch params to avoid unnecessary fetches
  LatLngBounds? _lastFetchedBounds;
  double _lastFetchedZoom = 0;
  
  // Current parameters
  double _zoomLevel;
  LatLngBounds _visibleBounds;
  bool _isMapMoving;
  
  // Track current zoom level bucket for optimized rendering
  int _currentZoomBucket = 3;
  
  // Keep track of the last request time for rate limiting
  DateTime _lastRequestTime = DateTime.now().subtract(const Duration(seconds: 30));
  
  // Track consecutive rapid requests
  int _consecutiveRapidRequests = 0;
  
  /// Constructor that takes initial parameters and optional error callback
  OSMRecreationDataProvider({
    required double initialZoomLevel,
    required LatLngBounds initialBounds,
    bool isMapMoving = false,
    this.onError,
  }) : 
    _zoomLevel = initialZoomLevel,
    _visibleBounds = initialBounds,
    _isMapMoving = isMapMoving {
    // Initialize zoom bucket
    _currentZoomBucket = _getZoomBucket(initialZoomLevel);
  }

  /// Check if the provider has initial data
  bool get hasInitialData => _didInitialFetch && !_isLoading;
  
  /// Check if there's an error
  bool get hasError => _hasError;
  
  /// Get the recreation data
  Map<String, List<Map<String, dynamic>>> get recreationData => _recreationData;
  
  /// Get current zoom bucket
  int getCurrentZoomBucket() => _currentZoomBucket;

  /// Convert zoom level to bucket for detail control
  int _getZoomBucket(double zoomLevel) {
    if (zoomLevel < 6) return 0;  // Global view
    if (zoomLevel < 9) return 1;  // Continental view
    if (zoomLevel < 12) return 2; // Country view
    if (zoomLevel < 15) return 3; // Region/city view
    if (zoomLevel < 18) return 4; // Neighborhood view
    return 5;                     // Street view
  }
  
  /// Update the parameters when map changes
  void updateParameters({
    required double zoomLevel,
    required LatLngBounds visibleBounds,
    required bool isMapMoving,
  }) {
    // Update current parameters
    _zoomLevel = zoomLevel;
    _visibleBounds = visibleBounds;
    _isMapMoving = isMapMoving;
    
    // Update zoom bucket if needed
    final newZoomBucket = _getZoomBucket(zoomLevel);
    if (newZoomBucket != _currentZoomBucket) {
      _currentZoomBucket = newZoomBucket;
      _needsRefresh = true;
    }
  }
  
  /// Check if we need to fetch new data
  bool shouldFetchNewData({
    required double oldZoomLevel,
    required LatLngBounds oldBounds,
    required bool oldIsMoving,
    required double newZoomLevel,
    required LatLngBounds newBounds,
    required bool newIsMoving,
  }) {
    // If we're in emergency performance mode, be more conservative about fetching
    if (_emergencyPerformanceMode) {
      final now = DateTime.now();
      // Exit emergency mode after 1 minute
      if (now.difference(_lastFreezeDetected) > const Duration(minutes: 1)) {
        debugPrint('OSMThrottle: ✅ Emergency mode deactivated');
        _emergencyPerformanceMode = false;
      } else {
        // In emergency mode, only fetch if zoom level changes significantly
        final bool significantZoomChange = (newZoomLevel - oldZoomLevel).abs() > 1.0;
        
        // If we're not moving and there's no significant zoom change, skip fetching
        if (!newIsMoving && !significantZoomChange) {
          return false;
        }
      }
    }
    
    // Check if zoom bucket has changed
    final oldZoomBucket = _getZoomBucket(oldZoomLevel);
    final newZoomBucket = _getZoomBucket(newZoomLevel);
    final zoomBucketChanged = oldZoomBucket != newZoomBucket;
    
    // Check if bounds have changed significantly
    bool boundsChanged = false;
    if (oldBounds != newBounds) {
      final newBoundsKey = _getBoundsKey(newBounds, newZoomLevel);
      boundsChanged = newBoundsKey != _lastBoundsKey;
    }
    
    // Track if map has stopped moving
    final wasMoving = oldIsMoving;
    final isMovingNow = newIsMoving;
    final stoppedMoving = wasMoving && !isMovingNow;
    
    // Decide if we should fetch new data
    if ((boundsChanged && !isMovingNow) || 
        (stoppedMoving && _needsRefresh) || 
        zoomBucketChanged) {
      
      // If we just stopped moving, we can delay the fetch slightly
      if (stoppedMoving) {
        _needsRefresh = true;
        return false; // We'll fetch in a moment when the map settles
      } else {
        return true; // Fetch immediately
      }
    }
    
    return false;
  }
  
  /// Get a key to identify current map bounds, with reduced precision for fewer unnecessary refreshes
  String _getBoundsKey(LatLngBounds bounds, double zoom) {
    final sw = bounds.southWest;
    final ne = bounds.northEast;
    
    // Reduce precision for bounds (4 decimal places ≈ 10m accuracy)
    return 'bounds_${sw.latitude.toStringAsFixed(4)}_${sw.longitude.toStringAsFixed(4)}_${ne.latitude.toStringAsFixed(4)}_${ne.longitude.toStringAsFixed(4)}_${zoom.toStringAsFixed(1)}';
  }
  
  /// Calculate a distance metric between two bounds
  double _calculateBoundsDistance(LatLngBounds bounds1, LatLngBounds bounds2) {
    // Simple Euclidean distance between centers
    final center1 = LatLng(
      (bounds1.northEast.latitude + bounds1.southWest.latitude) / 2,
      (bounds1.northEast.longitude + bounds1.southWest.longitude) / 2
    );
    
    final center2 = LatLng(
      (bounds2.northEast.latitude + bounds2.southWest.latitude) / 2,
      (bounds2.northEast.longitude + bounds2.southWest.longitude) / 2
    );
    
    // Approximate using flat-earth model for small distances
    return math.sqrt(
      math.pow(center1.latitude - center2.latitude, 2) +
      math.pow(center1.longitude - center2.longitude, 2)
    );
  }
  
  /// Calculate a safe request bounds that won't exceed Overpass API limits
  LatLngBounds _calculateSafeRequestBounds(LatLngBounds visibleBounds, double detailLevel, double zoomLevel) {
    final double latDelta = visibleBounds.northEast.latitude - visibleBounds.southWest.latitude;
    final double lonDelta = visibleBounds.northEast.longitude - visibleBounds.southWest.longitude;
    
    // Calculate center of visible bounds
    final LatLng center = LatLng(
      (visibleBounds.northEast.latitude + visibleBounds.southWest.latitude) / 2,
      (visibleBounds.northEast.longitude + visibleBounds.southWest.longitude) / 2
    );
    
    // Adjust request size based on zoom level and detail level
    double requestFactor = 1.0;
    
    // At higher zoom levels and higher detail, request smaller areas
    if (zoomLevel > 17) {
      requestFactor = 0.8 / detailLevel;
    } else if (zoomLevel > 15) {
      requestFactor = 1.0 / detailLevel;
    } else if (zoomLevel > 13) {
      requestFactor = 1.2 / detailLevel;
    } else {
      requestFactor = 1.5 / detailLevel;
    }
    
    // Cap to reasonable values to avoid exceeding API limits
    requestFactor = math.max(0.5, math.min(requestFactor, 1.5));
    
    // Calculate new bounds with adjusted size
    return LatLngBounds(
      LatLng(
        center.latitude - (latDelta * requestFactor / 2),
        center.longitude - (lonDelta * requestFactor / 2)
      ),
      LatLng(
        center.latitude + (latDelta * requestFactor / 2),
        center.longitude + (lonDelta * requestFactor / 2)
      )
    );
  }
  
  /// Get detail level based on zoom bucket
  double _getDetailLevel(int zoomBucket) {
    switch (zoomBucket) {
      case 0: return 0.1; // Global view - minimal detail
      case 1: return 0.2; // Continental view - very low detail
      case 2: return 0.4; // Country view - low detail
      case 3: return 0.7; // Region/city view - medium detail
      case 4: return 1.0; // Neighborhood view - high detail
      case 5: return 1.5; // Street view - very high detail
      default: return 1.0;
    }
  }
  
  /// Reset error state to try again
  void resetErrorState() {
    _hasError = false;
    _dataProcessor.resetErrorState();
  }
  
  /// Fetch recreation facilities data from OpenStreetMap
  Future<void> fetchRecreationData() async {
    if (_isLoading) return; // Don't fetch if already loading
    
    // Update loading state directly without setState
    _isLoading = true;
    
    // Check if too many requests in a short time - might indicate a performance issue
    final now = DateTime.now();
    final timeSinceLastRequest = now.difference(_lastRequestTime);
    if (timeSinceLastRequest.inMilliseconds < 200) {
      _consecutiveRapidRequests++;
      
      // If we've had several rapid requests, enable emergency performance mode
      if (_consecutiveRapidRequests > 3) {
        debugPrint('OSMThrottle: ⚠️ Emergency performance mode activated');
        _emergencyPerformanceMode = true;
        _lastFreezeDetected = now;
        _consecutiveRapidRequests = 0;
        
        // Return existing data without fetching to reduce load
        _isLoading = false;
        return;
      }
    } else {
      // Reset counter if requests aren't rapid
      _consecutiveRapidRequests = 0;
    }
    
    // Skip if at a very low zoom level - increase threshold to be more conservative
    if (_currentZoomBucket <= 2 || _zoomLevel < 14) {
      _recreationData = {
        'playgrounds': [],
        'sports': [],
        'dog_parks': [],
        'fitness': []
      };
      _isLoading = !_didInitialFetch; // Only show loading on first fetch
      _needsRefresh = false;
      _didInitialFetch = true;
      _hasError = false;
      return;
    }
    
    _isLoading = !_didInitialFetch; // Only show loading on first fetch
    
    // Update bounds key
    _lastBoundsKey = _getBoundsKey(_visibleBounds, _zoomLevel);
    
    // Get the map cache coordinator
    final cacheCoordinator = MapCacheCoordinator();
    
    // Check if we should use the new aura loading pattern
    final userBasedLoading = cacheCoordinator.getNextLoadingLocations(
      MapDataType.poi, 
      LatLng(
        (_visibleBounds.northEast.latitude + _visibleBounds.southWest.latitude) / 2,
        (_visibleBounds.northEast.longitude + _visibleBounds.southWest.longitude) / 2,
      ),
      _zoomLevel
    );
    
    // If we have user-based loading points, prioritize those
    if (userBasedLoading.isNotEmpty) {
      debugPrint('Recreation: Using user-centric aura loading with ${userBasedLoading.length} points');
      
      // Load data for each aura point, starting from closest to user
      bool anyFetched = false;
      
      for (final center in userBasedLoading) {
        // Skip if we're moving the map - we'll fetch when it stops
        if (_isMapMoving) {
          debugPrint('Recreation: Skipping aura fetch because map is moving');
          _isLoading = false;
          _needsRefresh = true;
          return;
        }
        
        // Calculate radius for this point (smaller at higher zoom levels)
        final double radius = _zoomLevel > 17 ? 0.005 : (_zoomLevel > 15 ? 0.01 : 0.02);
        
        // Create bounds around this point
        final areaCenter = center;
        final areaBounds = LatLngBounds(
          LatLng(center.latitude - radius, center.longitude - radius),
          LatLng(center.latitude + radius, center.longitude + radius),
        );
        
        // Generate cache key for this area
        final areaKey = 'recreation_aura_${areaCenter.latitude.toStringAsFixed(5)}_${areaCenter.longitude.toStringAsFixed(5)}_${_currentZoomBucket}';
        
        // Check if we can make a request through the throttle manager
        final canRequest = await _throttleManager.canMakeRequest(
          'recreation', 
          areaBounds
        );
        
        if (!canRequest) {
          debugPrint('Recreation aura request throttled by central manager');
          continue; // Try the next point
        }
        
        // Fetch data for this area
        try {
          final areaData = await MapCacheCoordinator().getData(
            type: MapDataType.poi,
            key: areaKey,
            southwest: areaBounds.southWest,
            northeast: areaBounds.northEast,
            zoomLevel: _zoomLevel,
            fetchIfMissing: () async {
              // Adapt detail level based on zoom bucket
              final detailLevel = _getDetailLevel(_currentZoomBucket);
              
              // Record this request in the throttle manager
              _throttleManager.recordRequest('recreation', areaBounds);
              
              // Fetch the data
              final Map<String, dynamic> result = await _fetchRecreationFeatures(
                areaBounds.southWest,
                areaBounds.northEast
              );
              
              // Record this as a completed loading zone
              cacheCoordinator.recordLoadedZone(
                MapDataType.poi, 
                areaCenter, 
                radius, 
                _zoomLevel.round()
              );
              
              return result;
            }
          );
          
          if (areaData != null) {
            // Merge this data with our existing data
            _mergeRecreationData(areaData);
            anyFetched = true;
          }
        } catch (e) {
          debugPrint('Error in aura fetch for recreation: $e');
          _throttleManager.recordError('recreation', e.toString());
        }
        
        // Add a delay between area fetches
        await Future.delayed(const Duration(milliseconds: 300));
      }
      
      // If we fetched any data, update the state
      if (anyFetched) {
        _isLoading = false;
        _needsRefresh = false;
        _didInitialFetch = true;
        _hasError = false;
        return;
      }
    }
    
    // Fall back to traditional fetch if aura loading didn't work
    // Check if bounds or zoom has significantly changed
    bool shouldSkipFetch = false;
    if (_lastFetchedBounds != null && _lastFetchedZoom > 0) {
      final boundsDistance = _calculateBoundsDistance(_visibleBounds, _lastFetchedBounds!);
      final zoomDifference = (_zoomLevel - _lastFetchedZoom).abs();
      
      // More aggressive caching - increase the threshold for when to skip fetches
      // If we're moving the map and have cached data, delay the fetch
      if (_isMapMoving && (_recreationData['playgrounds']!.isNotEmpty || _recreationData['sports']!.isNotEmpty)) {
        shouldSkipFetch = true;
      }
      // If bounds haven't changed much and zoom level is similar, use cached data
      else if (boundsDistance < 0.08 && zoomDifference < 1.2 && 
              (_recreationData['playgrounds']!.isNotEmpty || _recreationData['sports']!.isNotEmpty)) {
        shouldSkipFetch = true;
      }
    }
    
    if (shouldSkipFetch) {
      _isLoading = false;
      _needsRefresh = true; // Mark for refresh when map movement stops
      return;
    }
    
    // Add randomization to request timing to avoid synchronized requests across layers
    final randomDelayMs = math.Random().nextInt(1000);
    if (randomDelayMs > 0) {
      await Future.delayed(Duration(milliseconds: randomDelayMs));
    }
    
    // Generate cache key for the MapCacheCoordinator
    final cacheKey = 'recreation_${_visibleBounds.southWest.latitude.toStringAsFixed(4)}_${_visibleBounds.southWest.longitude.toStringAsFixed(4)}_${_visibleBounds.northEast.latitude.toStringAsFixed(4)}_${_visibleBounds.northEast.longitude.toStringAsFixed(4)}_${_currentZoomBucket}';
    
    try {
      // Use the centralized throttle manager to check if we can make a request
      final canRequest = await _throttleManager.canMakeRequest(
        'recreation', 
        _visibleBounds
      );
      
      if (!canRequest) {
        debugPrint('Recreation request throttled by central manager');
        // If we're not allowed to make a request now, use existing data
        _isLoading = false;
        _needsRefresh = true; // Try again later
        return;
      }
      
      // Use MapCacheCoordinator to get data from cache or fetch from network
      final recreationData = await MapCacheCoordinator().getData(
        type: MapDataType.poi, // Reuse the POI type for recreation data
        key: cacheKey,
        southwest: _visibleBounds.southWest,
        northeast: _visibleBounds.northEast,
        zoomLevel: _zoomLevel,
        fetchIfMissing: () async {
          // Adapt detail level based on zoom bucket
          final detailLevel = _getDetailLevel(_currentZoomBucket);
          
          // Calculate a safe data request region based on Overpass API limits
          final safeRequestBounds = _calculateSafeRequestBounds(
            _visibleBounds,
            detailLevel,
            _zoomLevel
          );
          
          // Record this request in the throttle manager
          _throttleManager.recordRequest('recreation', _visibleBounds);
          
          // Create the Overpass queries for different recreation facilities
          final Map<String, dynamic> result = await _fetchRecreationFeatures(
            safeRequestBounds.southWest,
            safeRequestBounds.northEast
          );
          
          // Record this as a completed loading zone for the view center
          final viewCenter = LatLng(
            (_visibleBounds.northEast.latitude + _visibleBounds.southWest.latitude) / 2,
            (_visibleBounds.northEast.longitude + _visibleBounds.southWest.longitude) / 2
          );
          
          cacheCoordinator.recordLoadedZone(
            MapDataType.poi, 
            viewCenter, 
            0.02,  // Approximate radius for the zone
            _zoomLevel.round()
          );
          
          return result;
        }
      );
      
      if (recreationData != null) {
        _recreationData = Map<String, List<Map<String, dynamic>>>.from(recreationData);
      } else {
        _recreationData = {
          'playgrounds': [],
          'sports': [],
          'dog_parks': [],
          'fitness': []
        };
      }
      
      _isLoading = false;
      _needsRefresh = false;
      _lastFetchedBounds = _visibleBounds;
      _lastFetchedZoom = _zoomLevel;
      _didInitialFetch = true;
      _hasError = false;
      _lastRequestTime = DateTime.now(); // Update last request time for throttling
    } catch (e) {
      debugPrint('Error in OSMRecreationDataProvider: $e');
      _isLoading = false;
      _hasError = true;
      _didInitialFetch = true; // We did try to fetch
      
      // Record error in throttle manager
      _throttleManager.recordError('recreation', e.toString());
      
      // Notify listeners about the error
      onError?.call(e.toString());
    }
  }
  
  /// Fetch different types of recreation facilities using the OSMDataProcessor
  Future<Map<String, dynamic>> _fetchRecreationFeatures(LatLng southwest, LatLng northeast) async {
    final Map<String, List<Map<String, dynamic>>> result = {
      'playgrounds': [],
      'sports': [],
      'dog_parks': [],
      'fitness': []
    };
    
    try {
      // Determine what level of detail to fetch based on zoom level
      final bool fetchMinimal = _zoomLevel < 16;
      final bool skipEntirelyAtLowZoom = _zoomLevel < 14;
      final bool fetchDetailed = _zoomLevel >= 17 && !_emergencyPerformanceMode;
      
      // At very low zoom levels, only fetch the most important facilities
      if (skipEntirelyAtLowZoom) {
        debugPrint('Zoom level too low for recreation data, returning empty result');
        // Just return empty results, as these facilities would be too small to see anyway
        return result;
      }
      
      // In emergency mode, fetch less data overall
      if (_emergencyPerformanceMode) {
        debugPrint('Recreation data: In emergency performance mode, fetching minimal data');
        // Limit area and only fetch most important facilities
        String simplifiedQuery = '[leisure=playground][area>2000], ' +
                               '[leisure=pitch][area>8000], ' +
                               '[leisure=sports_centre][area>5000]';
        
        final combinedResults = await _dataProcessor.fetchCustomData(
          southwest,
          northeast,
          simplifiedQuery,
          'way'  // Only fetch 'way' objects, not 'node' to reduce data
        );
        
        // Sort the results into their respective categories
        for (var feature in combinedResults) {
          String tags = feature['tags'].toString().toLowerCase();
          if (tags.contains('playground')) {
            result['playgrounds']!.add(feature);
          } else if (tags.contains('pitch') || tags.contains('sports_centre') || tags.contains('sport')) {
            result['sports']!.add(feature);
          }
        }
        
        debugPrint('Received ${result['playgrounds']!.length} playground elements and ${result['sports']!.length} sports elements');
        return result;
      }
      
      // Combine queries where possible to reduce number of API requests
      if (fetchMinimal) {
        // At lower zoom levels, only fetch the most important recreation facilities
        // and combine them into a single query to reduce API calls
        String combinedQuery = '[leisure=playground][area>1000], ' +
                               '[leisure=pitch][area>5000], ' +
                               '[leisure=sports_centre][area>2000]';
        
        final combinedResults = await _dataProcessor.fetchCustomData(
          southwest,
          northeast,
          combinedQuery,
          'way,node'
        );
        
        // Sort the results into their respective categories
        for (var feature in combinedResults) {
          String tags = feature['tags'].toString().toLowerCase();
          if (tags.contains('playground')) {
            result['playgrounds']!.add(feature);
          } else if (tags.contains('pitch') || tags.contains('sports_centre') || tags.contains('sport')) {
            result['sports']!.add(feature);
          }
        }
        
        debugPrint('Received ${result['playgrounds']!.length} playground elements and ${result['sports']!.length} sports elements');
      } else {
        // At higher zoom levels, fetch more detailed data with separate queries
        
        // Fetch playgrounds
        result['playgrounds'] = await _dataProcessor.fetchCustomData(
          southwest,
          northeast,
          '[leisure=playground]',
          'way,node'
        );
        debugPrint('Received ${result['playgrounds']!.length} playground elements');
        
        // Add a delay between requests
        await Future.delayed(const Duration(milliseconds: 400));
        
        // Fetch sports facilities
        String sportsQuery = '[leisure=pitch], [leisure=sports_centre], [sport]';
        
        result['sports'] = await _dataProcessor.fetchCustomData(
          southwest,
          northeast,
          sportsQuery,
          'way,node'
        );
        debugPrint('Received ${result['sports']!.length} sports facility elements');
        
        // Only fetch dog parks and fitness stations at higher zoom levels
        // Add another delay
        await Future.delayed(const Duration(milliseconds: 500));
        
        // Fetch dog parks
        result['dog_parks'] = await _dataProcessor.fetchCustomData(
          southwest,
          northeast,
          '[leisure=dog_park]',
          'way,node'
        );
        debugPrint('Received ${result['dog_parks']!.length} dog park elements');
        
        // Only fetch fitness stations at detailed zoom levels as they're small
        if (fetchDetailed) {
          // Add another delay - longer to prevent API overload
          await Future.delayed(const Duration(milliseconds: 600));
          
          // Fetch fitness stations and outdoor gyms
          result['fitness'] = await _dataProcessor.fetchCustomData(
            southwest,
            northeast,
            '[leisure=fitness_station], [leisure=fitness_centre][building!=yes], [amenity=outdoor_gym]',
            'way,node'
          );
          debugPrint('Received ${result['fitness']!.length} fitness facility elements');
        }
      }
      
      // Print total count
      int totalCount = result['playgrounds']!.length + result['sports']!.length + 
                      result['dog_parks']!.length + result['fitness']!.length;
      debugPrint('Total recreation elements: $totalCount');
      
      return result;
    } catch (e) {
      // Record error in throttle manager
      _throttleManager.recordError('recreation', e.toString());
      debugPrint('Error fetching recreation facilities: $e');
      throw e;
    }
  }
  
  /// Merge newly fetched recreation data with existing data, avoiding duplicates
  void _mergeRecreationData(Map<String, dynamic> newData) {
    // Cast the new data to the right type
    final typedNewData = Map<String, List<Map<String, dynamic>>>.from(newData);
    
    // Track how many new items we added
    int newItemsAdded = 0;
    
    // Merge each category
    for (final category in typedNewData.keys) {
      // Skip if the category doesn't exist in our data
      if (!_recreationData.containsKey(category)) continue;
      
      // Get existing IDs to avoid duplicates
      final existingIds = _recreationData[category]!.map((item) => 
          item['id']?.toString() ?? '${item['lat']}-${item['lon']}').toSet();
      
      // Add only new items
      for (final item in typedNewData[category]!) {
        final itemId = item['id']?.toString() ?? '${item['lat']}-${item['lon']}';
        if (!existingIds.contains(itemId)) {
          _recreationData[category]!.add(item);
          newItemsAdded++;
        }
      }
    }
    
    if (newItemsAdded > 0) {
      debugPrint('Added $newItemsAdded new recreation items from aura fetch');
    }
  }
} 