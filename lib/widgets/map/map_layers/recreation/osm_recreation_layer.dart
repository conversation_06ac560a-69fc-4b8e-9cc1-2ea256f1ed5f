import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:async';

import 'osm_recreation_data_provider.dart';
import 'osm_recreation_painter.dart';

/// A Flutter Map layer for rendering OpenStreetMap recreation facilities
/// including playgrounds, sports courts/fields, dog parks, and fitness stations
class OSMRecreationLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final bool isMapMoving;
  final String theme;
  final bool enhancedDetail;
  final ValueChanged<OSMRecreationDataProvider>? onDataProviderCreated;
  final MapCamera? mapCamera;

  const OSMRecreationLayer({
    Key? key,
    this.tiltFactor = 1.0,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.theme = 'vibrant',
    this.enhancedDetail = true,
    this.onDataProviderCreated,
    this.mapCamera,
  }) : super(key: key);

  @override
  State<OSMRecreationLayer> createState() => _OSMRecreationLayerState();
}

class _OSMRecreationLayerState extends State<OSMRecreationLayer> {
  // Use non-late initialization with nullable to avoid LateInitializationError
  OSMRecreationDataProvider? _dataProviderInstance;
  OSMRecreationDataProvider get _dataProvider => _dataProviderInstance!;
  
  bool _isLoading = true;
  bool _hasNetworkError = false;
  Map<String, List<Map<String, dynamic>>> _recreationData = {};
  String _currentTheme = 'vibrant';
  
  @override
  void initState() {
    super.initState();
    
    _currentTheme = widget.theme;
    
    // Initialize the data provider
    _dataProviderInstance = OSMRecreationDataProvider(
      initialZoomLevel: widget.zoomLevel,
      initialBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving,
      onError: _handleDataError,
    );
    
    if (widget.onDataProviderCreated != null) {
      widget.onDataProviderCreated!(_dataProvider);
    }
    
    // Initial data load
    _fetchRecreationData();
  }
  
  // Handle network/data errors
  void _handleDataError(String errorMessage) {
    if (mounted) {
      setState(() {
        _hasNetworkError = true;
      });
    }
    debugPrint('OSMRecreationLayer error: $errorMessage');
  }

  @override
  void didUpdateWidget(OSMRecreationLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Only proceed if data provider is initialized
    if (_dataProviderInstance == null) return;
    
    // Debug print to track parameter changes
    if (oldWidget.isMapMoving != widget.isMapMoving || 
        oldWidget.zoomLevel != widget.zoomLevel ||
        oldWidget.visibleBounds != widget.visibleBounds) {
      debugPrint('Recreation layer updated - Moving changed: ${oldWidget.isMapMoving} -> ${widget.isMapMoving}');
      debugPrint('Zoom changed: ${oldWidget.zoomLevel.toStringAsFixed(2)} -> ${widget.zoomLevel.toStringAsFixed(2)}');
      debugPrint('Bounds changed: ${oldWidget.visibleBounds != widget.visibleBounds}');
    }
    
    // Update current theme if changed
    if (widget.theme != oldWidget.theme) {
      _currentTheme = widget.theme;
    }
    
    // Notify data provider of changes
    _dataProvider.updateParameters(
      zoomLevel: widget.zoomLevel,
      visibleBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving,
    );
    
    // Determine if we need to fetch new data
    bool shouldFetch = _dataProvider.shouldFetchNewData(
      oldZoomLevel: oldWidget.zoomLevel,
      oldBounds: oldWidget.visibleBounds,
      oldIsMoving: oldWidget.isMapMoving,
      newZoomLevel: widget.zoomLevel,
      newBounds: widget.visibleBounds,
      newIsMoving: widget.isMapMoving,
    );
    
    if (shouldFetch) {
      debugPrint('Recreation layer fetching new data');
      _fetchRecreationData();
    }
  }

  void _fetchRecreationData() async {
    // Only proceed if data provider is initialized
    if (_dataProviderInstance == null) return;
    
    setState(() {
      _isLoading = true;
    });
    
    // Use the data provider to fetch data
    await _dataProvider.fetchRecreationData();
    
    if (mounted) {
      setState(() {
        _recreationData = _dataProvider.recreationData;
        _isLoading = false;
        
        // If there was a successful fetch, clear any previous error state
        if (_hasNetworkError && !_dataProvider.hasError) {
          _hasNetworkError = false;
        }
      });
    }
  }
  
  // Retry after network error
  void _retryAfterNetworkError() {
    if (_hasNetworkError && _dataProviderInstance != null) {
      setState(() {
        _hasNetworkError = false;
      });
      
      _dataProvider.resetErrorState();
      _fetchRecreationData();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Add debug print to track layer rebuilds
    debugPrint('Building recreation layer - Moving: ${widget.isMapMoving}, Zoom: ${widget.zoomLevel.toStringAsFixed(2)}');
    debugPrint('Bounds: ${widget.visibleBounds.southWest} to ${widget.visibleBounds.northEast}');
    
    // Don't render recreation facilities at low zoom levels
    if (widget.zoomLevel < 14) {
      return const SizedBox.shrink();
    }
    
    // If data is not yet loaded or provider is not initialized, show a placeholder
    if (_dataProviderInstance == null || (_isLoading && !_dataProvider.hasInitialData)) {
      return const SizedBox.shrink();
    }
    
    // Show error overlay with retry button if there was a network error
    if (_hasNetworkError) {
      return GestureDetector(
        onTap: _retryAfterNetworkError,
        child: Container(
          color: Colors.transparent,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'Tap to retry loading recreation facilities',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ),
      );
    }
    
    // Create the custom painter that will render the recreation facilities
    return CustomPaint(
      painter: OSMRecreationPainter(
        recreationData: _recreationData,
        zoomLevel: widget.zoomLevel,
        visibleBounds: widget.visibleBounds,
        theme: _currentTheme,
        enhancedDetail: widget.enhancedDetail,
        tiltFactor: widget.tiltFactor,
        mapCamera: widget.mapCamera,
      ),
      size: Size.infinite,
    );
  }
  
  @override
  void dispose() {
    // Clean up any resources
    _dataProviderInstance = null;
    super.dispose();
  }
} 