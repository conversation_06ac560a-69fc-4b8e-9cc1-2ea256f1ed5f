import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

import 'renderers/footway_renderer.dart';
import 'renderers/cycleway_renderer.dart';
import 'renderers/crossing_renderer.dart';
import 'renderers/pedestrian_area_renderer.dart';
import 'renderers/steps_renderer.dart';
import 'renderers/track_renderer.dart';
import 'utils/pedestrian_colors.dart';
import 'utils/pedestrian_geometry_utils.dart';
import 'utils/shadow_renderer.dart';

/// Improved renderer for OpenStreetMap pedestrian elements with specialized renderers
/// Coordinates specialized renderers for different pedestrian element types
class OSMPedestrianRenderer extends CustomPainter {
  final Map<String, List<Map<String, dynamic>>> pedestrianData;
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final bool enhancedDetail;
  final MapCamera? mapCamera;
  
  // Helper utilities
  final PedestrianGeometryUtils _geometryUtils = PedestrianGeometryUtils();
  final PedestrianColors _colorsHelper = PedestrianColors();
  
  // Specialized renderers
  late final FootwayRenderer _footwayRenderer;
  late final CyclewayRenderer _cyclewayRenderer;
  late final CrossingRenderer _crossingRenderer;
  late final PedestrianAreaRenderer _pedestrianAreaRenderer;
  late final StepsRenderer _stepsRenderer;
  late final TrackRenderer _trackRenderer;
  late final ShadowRenderer _shadowRenderer;
  
  OSMPedestrianRenderer({
    required this.pedestrianData,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    required this.theme,
    required this.enhancedDetail,
    this.mapCamera,
  }) {
    // Initialize all specialized renderers
    _footwayRenderer = FootwayRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _cyclewayRenderer = CyclewayRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _crossingRenderer = CrossingRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _pedestrianAreaRenderer = PedestrianAreaRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _stepsRenderer = StepsRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _trackRenderer = TrackRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _shadowRenderer = ShadowRenderer(
      zoomLevel: zoomLevel,
      tiltFactor: tiltFactor,
    );
  }

  @override
  void paint(Canvas canvas, Size size) {
    // Don't render anything if there's no data to display
    if (pedestrianData.isEmpty) return;
    if (pedestrianData.values.every((list) => list.isEmpty)) return;
    
    // Get appropriate color palette for current theme
    final themeColors = _colorsHelper.getColorPalette(theme);
    
    // Order of drawing is important for visual layering
    
    // 1. First draw pedestrian zones (areas) with elevation
    _renderPedestrianAreas(canvas, size, themeColors);
    
    // 2. Draw different types of paths with appropriate elevations
    
    // Tracks (hiking paths, walking trails)
    _renderTracks(canvas, size, themeColors);
    
    // General paths
    _renderPaths(canvas, size, themeColors);
    
    // Footways (walking paths)
    _renderFootways(canvas, size, themeColors);
    
    // Cycleways 
    _renderCycleways(canvas, size, themeColors);
    
    // Steps with special rendering
    _renderSteps(canvas, size, themeColors);
    
    // 3. Draw special elements on top
    
    // Crossings with zebra patterns
    _renderCrossings(canvas, size, themeColors);
  }
  
  /// Render pedestrian areas (plazas, squares, pedestrian zones)
  void _renderPedestrianAreas(
    Canvas canvas, 
    Size size, 
    Map<String, Map<String, Color>> themeColors
  ) {
    final List<Map<String, dynamic>> areas = pedestrianData['pedestrian_areas'] ?? [];
    if (areas.isEmpty) return;
    
    final colors = themeColors['pedestrian'] ?? themeColors['pedestrian']!;
    
    // Process and enhance area data
    for (final area in areas) {
      // Add bounds for coordinate conversion
      area['bounds'] = visibleBounds;
      
      // Render area using specialized renderer
      _pedestrianAreaRenderer.renderPedestrianArea(canvas, size, area, colors);
    }
  }
  
  /// Render footways (sidewalks, etc.)
  void _renderFootways(
    Canvas canvas, 
    Size size, 
    Map<String, Map<String, Color>> themeColors
  ) {
    final List<Map<String, dynamic>> footways = pedestrianData['footways'] ?? [];
    if (footways.isEmpty) return;
    
    final colors = themeColors['footway'] ?? themeColors['pedestrian']!;
    
    // Process and enhance footway data
    for (final footway in footways) {
      // Add bounds for coordinate conversion
      footway['bounds'] = visibleBounds;
      
      // Render footway using specialized renderer
      _footwayRenderer.renderFootway(canvas, size, footway, colors);
    }
  }
  
  /// Render cycleways
  void _renderCycleways(
    Canvas canvas, 
    Size size, 
    Map<String, Map<String, Color>> themeColors
  ) {
    final List<Map<String, dynamic>> cycleways = pedestrianData['cycleways'] ?? [];
    if (cycleways.isEmpty) return;
    
    final colors = themeColors['cycleway'] ?? themeColors['pedestrian']!;
    
    // Process and enhance cycleway data
    for (final cycleway in cycleways) {
      // Add bounds for coordinate conversion
      cycleway['bounds'] = visibleBounds;
      
      // Render cycleway using specialized renderer
      _cyclewayRenderer.renderCycleway(canvas, size, cycleway, colors);
    }
  }
  
  /// Render general paths
  void _renderPaths(
    Canvas canvas, 
    Size size, 
    Map<String, Map<String, Color>> themeColors
  ) {
    final List<Map<String, dynamic>> paths = pedestrianData['paths'] ?? [];
    if (paths.isEmpty) return;
    
    final colors = themeColors['path'] ?? themeColors['pedestrian']!;
    
    // Process and enhance path data
    for (final path in paths) {
      // Add bounds for coordinate conversion
      path['bounds'] = visibleBounds;
      
      // Render path using footway renderer (simplified)
      _footwayRenderer.renderFootway(canvas, size, path, colors);
    }
  }
  
  /// Render tracks (hiking paths, walking trails)
  void _renderTracks(
    Canvas canvas, 
    Size size, 
    Map<String, Map<String, Color>> themeColors
  ) {
    final List<Map<String, dynamic>> tracks = pedestrianData['tracks'] ?? [];
    if (tracks.isEmpty) return;
    
    final colors = themeColors['track'] ?? themeColors['pedestrian']!;
    
    // Process and enhance track data
    for (final track in tracks) {
      // Add bounds for coordinate conversion
      track['bounds'] = visibleBounds;
      
      // Render track using specialized renderer
      _trackRenderer.renderTrack(canvas, size, track, colors);
    }
  }
  
  /// Render steps
  void _renderSteps(
    Canvas canvas, 
    Size size, 
    Map<String, Map<String, Color>> themeColors
  ) {
    final List<Map<String, dynamic>> steps = pedestrianData['steps'] ?? [];
    if (steps.isEmpty) return;
    
    final colors = themeColors['steps'] ?? themeColors['pedestrian']!;
    
    // Process and enhance step data
    for (final step in steps) {
      // Add bounds for coordinate conversion
      step['bounds'] = visibleBounds;
      
      // Render steps using specialized renderer
      _stepsRenderer.renderSteps(canvas, size, step, colors);
    }
  }
  
  /// Render crossings
  void _renderCrossings(
    Canvas canvas, 
    Size size, 
    Map<String, Map<String, Color>> themeColors
  ) {
    final List<Map<String, dynamic>> crossings = pedestrianData['crossings'] ?? [];
    if (crossings.isEmpty) return;
    
    final colors = themeColors['crossing'] ?? themeColors['pedestrian']!;
    
    // Process and enhance crossing data
    for (final crossing in crossings) {
      // Add bounds for coordinate conversion
      crossing['bounds'] = visibleBounds;
      
      // Render crossing using specialized renderer
      _crossingRenderer.renderCrossing(canvas, size, crossing, colors);
    }
  }
  
  @override
  bool shouldRepaint(OSMPedestrianRenderer oldDelegate) {
    return oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.visibleBounds != visibleBounds ||
           oldDelegate.theme != theme ||
           oldDelegate.mapCamera != mapCamera ||
           oldDelegate.pedestrianData != pedestrianData;
  }
} 