import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'package:flutter_map/flutter_map.dart';

import '../utils/pedestrian_colors.dart';
import '../utils/pedestrian_geometry_utils.dart';
import '../utils/shadow_renderer.dart';

/// Specialized renderer for pedestrian areas (plazas, squares, pedestrian zones, etc.)
/// Provides enhanced rendering with textures and 2.5D effects
class PedestrianAreaRenderer {
  final PedestrianGeometryUtils _geometryUtils = PedestrianGeometryUtils();
  final PedestrianColors _colorsHelper = PedestrianColors();
  final double zoomLevel;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  late final ShadowRenderer _shadowRenderer;
  
  PedestrianAreaRenderer({
    required this.zoomLevel,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  }) {
    _shadowRenderer = ShadowRenderer(
      zoomLevel: zoomLevel,
      tiltFactor: tiltFactor,
    );
  }
  
  /// Main render method for pedestrian areas
  void renderPedestrianArea(
    Canvas canvas, 
    Size size, 
    Map<String, dynamic> area, 
    Map<String, Color> colors
  ) {
    final List<LatLng> points = area['points'] as List<LatLng>? ?? [];
    
    if (points.isEmpty) return;
    
    // Convert geographic coordinates to screen coordinates
    final List<Offset> screenPoints = _geometryUtils.convertToScreenPoints(
      points, 
      area['bounds'] as LatLngBounds, 
      size
    );
    
    // Get area information for styling
    final String? areaType = area['area_type'] as String?;
    final String? surface = area['surface'] as String?;
    
    // Draw the pedestrian area with appropriate styling
    _drawPedestrianArea(canvas, screenPoints, colors, areaType, surface);
  }
  
  /// Draw the main pedestrian area with appropriate styling
  void _drawPedestrianArea(
    Canvas canvas, 
    List<Offset> points, 
    Map<String, Color> colors, 
    String? areaType, 
    String? surface
  ) {
    // Prepare area paints
    final Paint fillPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final Paint outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..isAntiAlias = true;
    
    // Create path for area
    final Path areaPath = Path();
    if (points.isEmpty) return;
    
    areaPath.addPolygon(points, true);
    
    // Apply 2.5D effect with shadow for the area
    if (tiltFactor > 0.1) {
      final double elevation = 0.08 * tiltFactor; // Slightly higher elevation for areas
      _shadowRenderer.drawAreaShadow(
        canvas, 
        points, 
        Colors.black.withOpacity(0.3), 
        elevation
      );
    }
    
    // Draw the area fill
    canvas.drawPath(areaPath, fillPaint);
    
    // Draw the outline
    canvas.drawPath(areaPath, outlinePaint);
    
    // Add texture pattern for areas when zoomed in
    if (enhancedDetail && zoomLevel > 16) {
      _addAreaTexture(canvas, points, colors['pattern']!, areaType, surface);
    }
  }
  
  /// Add texture pattern to area based on type and surface
  void _addAreaTexture(
    Canvas canvas, 
    List<Offset> points, 
    Color patternColor, 
    String? areaType, 
    String? surface
  ) {
    if (points.length < 3) return;
    
    // Get area bounds
    final Rect bounds = _geometryUtils.getBounds(points);
    
    // Skip if area is too small to see details
    if (bounds.width < 10 || bounds.height < 10) return;
    
    // Create a clipping path to contain the pattern
    final Path areaPath = Path();
    areaPath.addPolygon(points, true);
    
    canvas.save();
    canvas.clipPath(areaPath);
    
    // Determine pattern type and spacing based on area type and surface
    double spacing = 16.0;
    bool drawGridLines = true;
    bool drawDiagonalLines = false;
    bool drawDotsPattern = false;
    
    // Adjust pattern based on area type
    if (areaType != null) {
      switch (areaType.toLowerCase()) {
        case 'square':
        case 'plaza':
          spacing = 16.0;
          drawGridLines = true;
          break;
        case 'pedestrian_street':
        case 'pedestrian_zone':
          spacing = 14.0;
          drawGridLines = true;
          drawDotsPattern = true;
          break;
        default:
          // Default pattern
          spacing = 16.0;
          drawGridLines = true;
      }
    }
    
    // Further adjust pattern based on surface type
    if (surface != null) {
      switch (surface.toLowerCase()) {
        case 'paving_stones':
        case 'paved':
          spacing = 12.0;
          drawGridLines = true;
          break;
        case 'concrete':
          spacing = 20.0;
          drawGridLines = true;
          break;
        case 'cobblestone':
          spacing = 8.0;
          drawDotsPattern = true;
          drawGridLines = false;
          break;
        case 'asphalt':
          drawGridLines = false;
          drawDiagonalLines = true;
          spacing = 24.0;
          break;
        default:
          // Keep defaults
          break;
      }
    }
    
    // Adjust spacing based on zoom level
    if (zoomLevel >= 19) {
      spacing *= 0.7; // More detail at high zoom
    } else if (zoomLevel >= 18) {
      spacing *= 0.85;
    }
    
    final Paint patternPaint = Paint()
      ..color = patternColor.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.8
      ..isAntiAlias = true;
    
    final Paint dotsPaint = Paint()
      ..color = patternColor.withOpacity(0.6)
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw grid lines pattern
    if (drawGridLines) {
      // Draw horizontal lines
      for (double y = bounds.top; y <= bounds.bottom; y += spacing) {
        canvas.drawLine(Offset(bounds.left, y), Offset(bounds.right, y), patternPaint);
      }
      
      // Draw vertical lines
      for (double x = bounds.left; x <= bounds.right; x += spacing) {
        canvas.drawLine(Offset(x, bounds.top), Offset(x, bounds.bottom), patternPaint);
      }
    }
    
    // Draw diagonal lines pattern
    if (drawDiagonalLines) {
      final double diagonalSpacing = spacing * 1.2;
      final double maxDim = math.max(bounds.width, bounds.height) * 2;
      
      // Draw diagonal lines in both directions
      for (double offset = -maxDim; offset <= maxDim; offset += diagonalSpacing) {
        // First direction
        canvas.drawLine(
          Offset(bounds.left + offset, bounds.top),
          Offset(bounds.left + offset + maxDim, bounds.top + maxDim),
          patternPaint
        );
        
        // Second direction
        canvas.drawLine(
          Offset(bounds.right - offset, bounds.top),
          Offset(bounds.right - offset - maxDim, bounds.top + maxDim),
          patternPaint
        );
      }
    }
    
    // Draw dots pattern
    if (drawDotsPattern) {
      final double dotSize = zoomLevel >= 18 ? 1.5 : 1.0;
      
      for (double y = bounds.top + spacing / 2; y <= bounds.bottom; y += spacing) {
        for (double x = bounds.left + spacing / 2; x <= bounds.right; x += spacing) {
          // Add a bit of randomness to dot positions
          final double offsetX = _random.nextDouble() * spacing * 0.2;
          final double offsetY = _random.nextDouble() * spacing * 0.2;
          
          canvas.drawCircle(
            Offset(x + offsetX, y + offsetY),
            dotSize,
            dotsPaint
          );
        }
      }
    }
    
    // Restore canvas state
    canvas.restore();
    
    // Add street furniture at high zoom levels
    if (enhancedDetail && zoomLevel >= 18) {
      _addStreetFurniture(canvas, points, bounds, patternColor);
    }
  }
  
  /// Add street furniture elements like benches to pedestrian areas
  void _addStreetFurniture(
    Canvas canvas, 
    List<Offset> points, 
    Rect bounds, 
    Color furnitureColor
  ) {
    if (bounds.width < 30 || bounds.height < 30) return;
    
    final Paint furniturePaint = Paint()
      ..color = furnitureColor
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final Paint outlinePaint = Paint()
      ..color = Colors.black.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..isAntiAlias = true;
    
    // Calculate how many furniture items to add based on area size
    final double area = bounds.width * bounds.height;
    final int furnitureCount = math.min(5, math.max(1, (area / 10000).floor()));
    
    // Add benches at semi-random locations
    for (int i = 0; i < furnitureCount; i++) {
      // Use deterministic positions based on fixed random seed
      final double x = bounds.left + bounds.width * (0.2 + 0.6 * _random.nextDouble());
      final double y = bounds.top + bounds.height * (0.2 + 0.6 * _random.nextDouble());
      
      final Offset position = Offset(x, y);
      
      // Check if position is inside the area
      if (_isPointInPolygon(position, points)) {
        _drawBench(canvas, position, _random.nextDouble() * math.pi, furniturePaint, outlinePaint);
      }
    }
  }
  
  /// Check if a point is inside a polygon
  bool _isPointInPolygon(Offset point, List<Offset> polygon) {
    if (polygon.length < 3) return false;
    
    bool isInside = false;
    for (int i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      final Offset vertI = polygon[i];
      final Offset vertJ = polygon[j];
      
      if (((vertI.dy > point.dy) != (vertJ.dy > point.dy)) &&
          (point.dx < (vertJ.dx - vertI.dx) * (point.dy - vertI.dy) / 
          (vertJ.dy - vertI.dy) + vertI.dx)) {
        isInside = !isInside;
      }
    }
    
    return isInside;
  }
  
  /// Draw a bench at given position with given angle
  void _drawBench(Canvas canvas, Offset position, double angle, Paint paint, Paint outlinePaint) {
    final double benchLength = 8.0;
    final double benchWidth = 3.0;
    
    // Save canvas state to restore after rotation
    canvas.save();
    
    // Translate and rotate canvas
    canvas.translate(position.dx, position.dy);
    canvas.rotate(angle);
    
    // Draw bench
    final Rect benchRect = Rect.fromCenter(
      center: Offset.zero,
      width: benchLength,
      height: benchWidth
    );
    
    canvas.drawRect(benchRect, paint);
    canvas.drawRect(benchRect, outlinePaint);
    
    // Restore canvas
    canvas.restore();
  }
} 