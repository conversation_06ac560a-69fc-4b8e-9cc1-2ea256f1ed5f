import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:latlong2/latlong.dart' hide Path;
import 'package:flutter_map/flutter_map.dart';

import '../utils/pedestrian_colors.dart';
import '../utils/pedestrian_geometry_utils.dart';
import '../utils/shadow_renderer.dart';

/// Specialized renderer for pedestrian crossings (zebra crossings, etc.)
/// Provides enhanced rendering with appropriate striped patterns
class CrossingRenderer {
  final PedestrianGeometryUtils _geometryUtils = PedestrianGeometryUtils();
  final PedestrianColors _colorsHelper = PedestrianColors();
  final double zoomLevel;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  late final ShadowRenderer _shadowRenderer;
  
  CrossingRenderer({
    required this.zoomLevel,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  }) {
    _shadowRenderer = ShadowRenderer(
      zoomLevel: zoomLevel,
      tiltFactor: tiltFactor,
    );
  }
  
  /// Get the path width based on zoom level
  double getPathWidth() {
    // Wider than footways to emphasize crossings
    double baseWidth = 3.5;
    
    // Scale width based on zoom level
    if (zoomLevel >= 19) return baseWidth * 3.0;
    if (zoomLevel >= 18) return baseWidth * 2.5;
    if (zoomLevel >= 17) return baseWidth * 2.0;
    if (zoomLevel >= 16) return baseWidth * 1.6;
    if (zoomLevel >= 15) return baseWidth * 1.2;
    return baseWidth * 1.0;
  }
  
  /// Main render method for crossings
  void renderCrossing(
    Canvas canvas, 
    Size size, 
    Map<String, dynamic> crossing, 
    Map<String, Color> colors
  ) {
    final List<LatLng> points = crossing['points'] as List<LatLng>? ?? [];
    
    if (points.length < 2) return;
    
    // Convert geographic coordinates to screen coordinates
    final List<Offset> screenPoints = _geometryUtils.convertToScreenPoints(
      points, 
      crossing['bounds'] as LatLngBounds, 
      size
    );
    
    // Get the crossing type for styling
    final String? crossingType = crossing['crossing_type'] as String?;
    
    // Draw the crossing with appropriate styling
    _drawCrossing(canvas, screenPoints, colors, crossingType);
  }
  
  /// Draw the main crossing with appropriate styling
  void _drawCrossing(
    Canvas canvas, 
    List<Offset> points, 
    Map<String, Color> colors, 
    String? crossingType
  ) {
    // Get path width based on zoom
    final double pathWidth = getPathWidth();
    
    // Prepare path paints
    final Paint outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth + 1.5
      ..strokeCap = StrokeCap.butt
      ..strokeJoin = StrokeJoin.miter
      ..isAntiAlias = true;
    
    final Paint pathPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth
      ..strokeCap = StrokeCap.butt
      ..strokeJoin = StrokeJoin.miter
      ..isAntiAlias = true;
    
    // Create path for drawing
    final Path mainPath = Path();
    if (points.isEmpty) return;
    
    mainPath.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      mainPath.lineTo(points[i].dx, points[i].dy);
    }
    
    // Apply 2.5D effect with shadow
    if (tiltFactor > 0.1) {
      final double elevation = 0.05 * tiltFactor; // Subtle elevation for crossings
      _shadowRenderer.drawPathShadow(
        canvas, 
        mainPath, 
        Colors.black.withOpacity(0.4), 
        elevation
      );
    }
    
    // Draw path outline first (for better visibility)
    canvas.drawPath(mainPath, outlinePaint);
    
    // Then draw the main path
    canvas.drawPath(mainPath, pathPaint);
    
    // Draw zebra stripes for the crossing
    _drawZebraPattern(
      canvas, 
      points, 
      pathWidth, 
      colors['pattern']!, 
      crossingType
    );
  }
  
  /// Draw zebra crossing pattern
  void _drawZebraPattern(
    Canvas canvas, 
    List<Offset> points, 
    double pathWidth, 
    Color stripeColor, 
    String? crossingType
  ) {
    if (points.length < 2) return;
    
    // Skip pattern if zoom is too low for detail
    if (zoomLevel < 15) return;
    
    // Determine the crossing path direction
    final Offset startPoint = points.first;
    final Offset endPoint = points.last;
    
    // Calculate the crossing direction vector
    final Offset pathVector = Offset(
      endPoint.dx - startPoint.dx,
      endPoint.dy - startPoint.dy
    );
    
    // Normalize for unit vector
    final double pathLength = math.sqrt(
      pathVector.dx * pathVector.dx + pathVector.dy * pathVector.dy
    );
    
    if (pathLength <= 0) return;
    
    final Offset pathDirection = Offset(
      pathVector.dx / pathLength,
      pathVector.dy / pathLength
    );
    
    // Perpendicular direction for stripes
    final Offset stripeDirection = Offset(
      -pathDirection.dy,
      pathDirection.dx
    );
    
    // Determine stripe dimensions based on crossing type and zoom
    double stripeWidth = zoomLevel >= 18 ? 6.0 : 4.0;
    double stripeSpacing = zoomLevel >= 18 ? 8.0 : 6.0;
    
    // Adjust for crossing type
    if (crossingType == 'zebra') {
      stripeWidth = zoomLevel >= 18 ? 8.0 : 6.0;
      stripeSpacing = zoomLevel >= 18 ? 10.0 : 8.0;
    } else if (crossingType == 'marked') {
      stripeWidth = zoomLevel >= 18 ? 6.0 : 4.0;
      stripeSpacing = zoomLevel >= 18 ? 8.0 : 6.0;
    }
    
    // Stripe paint
    final Paint stripePaint = Paint()
      ..color = stripeColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = zoomLevel >= 18 ? pathWidth * 0.4 : pathWidth * 0.3
      ..strokeCap = StrokeCap.butt
      ..isAntiAlias = true;
    
    // Calculate stripe count based on path length
    final int stripeCount = math.max(
      3, 
      (pathLength / (stripeWidth + stripeSpacing)).floor()
    );
    
    // Draw the stripes across the crossing
    for (int i = 0; i < stripeCount; i++) {
      final double position = i * (stripeWidth + stripeSpacing) + stripeSpacing / 2;
      
      if (position >= pathLength) break;
      
      // Position along the path
      final Offset stripeCenter = Offset(
        startPoint.dx + pathDirection.dx * position,
        startPoint.dy + pathDirection.dy * position
      );
      
      // Calculate stripe endpoints
      final Offset stripeStart = Offset(
        stripeCenter.dx - stripeDirection.dx * pathWidth * 0.8,
        stripeCenter.dy - stripeDirection.dy * pathWidth * 0.8
      );
      
      final Offset stripeEnd = Offset(
        stripeCenter.dx + stripeDirection.dx * pathWidth * 0.8,
        stripeCenter.dy + stripeDirection.dy * pathWidth * 0.8
      );
      
      // Draw the stripe
      canvas.drawLine(stripeStart, stripeEnd, stripePaint);
    }
    
    // Add crossing signals at high zoom levels
    if (enhancedDetail && zoomLevel >= 18) {
      _addCrossingSignals(canvas, points, stripeColor);
    }
  }
  
  /// Add crossing signals at ends of crossing
  void _addCrossingSignals(
    Canvas canvas, 
    List<Offset> points, 
    Color signalColor
  ) {
    if (points.length < 2) return;
    
    // Signal paint
    final Paint signalPaint = Paint()
      ..color = signalColor
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final Paint signalOutlinePaint = Paint()
      ..color = Colors.black.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..isAntiAlias = true;
    
    // Draw signal at start of crossing
    final double signalRadius = zoomLevel >= 19 ? 5.0 : 4.0;
    
    canvas.drawCircle(points.first, signalRadius, signalPaint);
    canvas.drawCircle(points.first, signalRadius, signalOutlinePaint);
    
    // Draw signal at end of crossing
    canvas.drawCircle(points.last, signalRadius, signalPaint);
    canvas.drawCircle(points.last, signalRadius, signalOutlinePaint);
  }
} 