import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'package:flutter_map/flutter_map.dart';

import '../utils/pedestrian_colors.dart';
import '../utils/pedestrian_geometry_utils.dart';
import '../utils/shadow_renderer.dart';

/// Specialized renderer for cycleways (bicycle paths, lanes, etc.)
/// Provides enhanced rendering with bicycle symbols and appropriate styling
class CyclewayRenderer {
  final PedestrianGeometryUtils _geometryUtils = PedestrianGeometryUtils();
  final PedestrianColors _colorsHelper = PedestrianColors();
  final double zoomLevel;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  late final ShadowRenderer _shadowRenderer;
  
  CyclewayRenderer({
    required this.zoomLevel,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  }) {
    _shadowRenderer = ShadowRenderer(
      zoomLevel: zoomLevel,
      tiltFactor: tiltFactor,
    );
  }
  
  /// Get the path width based on zoom level
  double getPathWidth() {
    // Make cycle paths slightly wider than footways
    double baseWidth = 2.8;
    
    // Scale width based on zoom level
    if (zoomLevel >= 19) return baseWidth * 3.0;
    if (zoomLevel >= 18) return baseWidth * 2.5;
    if (zoomLevel >= 17) return baseWidth * 2.0;
    if (zoomLevel >= 16) return baseWidth * 1.6;
    if (zoomLevel >= 15) return baseWidth * 1.2;
    return baseWidth * 1.0;
  }
  
  /// Render a cycleway with the provided colors - compatibility method for OSMPedestrianRenderer
  void renderCycleway(
    Canvas canvas, 
    Size size, 
    Map<String, dynamic> cycleway, 
    Map<String, Color> colors
  ) {
    final List<LatLng> points = cycleway['points'] as List<LatLng>? ?? [];
    
    if (points.length < 2) return;
    
    // Convert geographic coordinates to screen coordinates
    final List<Offset> screenPoints = _geometryUtils.convertToScreenPoints(
      points, 
      cycleway['bounds'] as LatLngBounds, 
      size
    );
    
    // Get cycleway information for styling
    final String? cyclewayType = cycleway['cycleway_type'] as String?;
    final String? surface = cycleway['surface'] as String?;
    
    // Draw the cycleway with appropriate styling
    _drawCycleway(canvas, screenPoints, colors, cyclewayType, surface);
  }
  
  /// Draw the main cycleway with appropriate styling
  void _drawCycleway(
    Canvas canvas, 
    List<Offset> points, 
    Map<String, Color> colors, 
    String? cyclewayType, 
    String? surface
  ) {
    if (points.length < 2) return;
    
    // Get path width based on zoom level and cycleway type
    double pathWidth = getPathWidth();
    
    // Adjust width based on cycleway type
    if (cyclewayType != null) {
      switch (cyclewayType.toLowerCase()) {
        case 'track':
        case 'separate':
          // Dedicated cycle tracks are wider
          pathWidth *= 1.2;
          break;
        case 'lane':
          // Standard width for cycle lanes
          break;
        case 'shared':
        case 'shared_lane':
          // Shared lanes are narrower
          pathWidth *= 0.9;
          break;
        default:
          // Default to standard width
          break;
      }
    }
    
    // Prepare path paints
    final Paint outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth + 1.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    final Paint pathPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    // Create path for drawing
    final Path mainPath = Path();
    mainPath.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      mainPath.lineTo(points[i].dx, points[i].dy);
    }
    
    // Apply 2.5D effect with shadow
    if (tiltFactor > 0.1) {
      final double elevation = 0.06 * tiltFactor; // Medium elevation for cycleways
      _shadowRenderer.drawPathShadow(
        canvas, 
        mainPath, 
        Colors.black.withOpacity(0.4), 
        elevation
      );
    }
    
    // Draw path outline first (for better visibility)
    canvas.drawPath(mainPath, outlinePaint);
    
    // Then draw the main path
    canvas.drawPath(mainPath, pathPaint);
    
    // Add cycleway-specific details
    if (enhancedDetail) {
      // Add centerline for dedicated cycle tracks
      if (cyclewayType == 'track' || cyclewayType == 'separate') {
        _addCenterline(canvas, points, colors['pattern']!, pathWidth);
      }
      
      // Add bicycle symbols along the path
      if (zoomLevel >= 16) {
        _addBicycleSymbols(canvas, points, colors['pattern']!);
      }
    }
  }
  
  /// Add a dashed centerline for two-way cycle tracks
  void _addCenterline(
    Canvas canvas, 
    List<Offset> points, 
    Color color, 
    double pathWidth
  ) {
    if (points.length < 2 || zoomLevel < 16) return;
    
    final Paint centerlinePaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..isAntiAlias = true;
    
    // Draw a dashed line along the center of the path
    _drawDashedPath(
      canvas, 
      points, 
      4.0, // dash width
      8.0, // dash spacing
      centerlinePaint
    );
  }
  
  /// Draw a dashed pattern along a path
  void _drawDashedPath(
    Canvas canvas, 
    List<Offset> points, 
    double dashWidth, 
    double dashSpacing, 
    Paint paint, 
    {bool randomizeInterval = false}
  ) {
    if (points.length < 2) return;
    
    double distanceRemaining = 0;
    bool drawDash = true;
    
    for (int i = 0; i < points.length - 1; i++) {
      final Offset start = points[i];
      final Offset end = points[i + 1];
      
      final double segmentLength = _geometryUtils.distance(start, end);
      final Offset direction = _geometryUtils.normalizeOffset(Offset(
        end.dx - start.dx,
        end.dy - start.dy
      ));
      
      double distanceCovered = 0;
      
      while (distanceCovered < segmentLength) {
        final double intervalLength = randomizeInterval 
            ? (drawDash ? dashWidth : dashSpacing) * (0.8 + _random.nextDouble() * 0.4)
            : (drawDash ? dashWidth : dashSpacing);
        
        double currentStepLength;
        
        if (distanceRemaining > 0) {
          currentStepLength = distanceRemaining;
          distanceRemaining = 0;
        } else {
          currentStepLength = intervalLength;
        }
        
        if (distanceCovered + currentStepLength > segmentLength) {
          distanceRemaining = currentStepLength - (segmentLength - distanceCovered);
          currentStepLength = segmentLength - distanceCovered;
        }
        
        if (drawDash) {
          final Offset dashStart = Offset(
            start.dx + direction.dx * distanceCovered,
            start.dy + direction.dy * distanceCovered
          );
          final Offset dashEnd = Offset(
            start.dx + direction.dx * (distanceCovered + currentStepLength),
            start.dy + direction.dy * (distanceCovered + currentStepLength)
          );
          
          canvas.drawLine(dashStart, dashEnd, paint);
        }
        
        distanceCovered += currentStepLength;
        drawDash = !drawDash;
      }
      
      drawDash = !drawDash;
    }
  }
  
  /// Add bicycle symbols along the path
  void _addBicycleSymbols(
    Canvas canvas, 
    List<Offset> points, 
    Color symbolColor
  ) {
    if (points.length < 2 || zoomLevel < 16) return;
    
    // Calculate total path length
    double totalLength = 0;
    for (int i = 0; i < points.length - 1; i++) {
      totalLength += _geometryUtils.distance(points[i], points[i + 1]);
    }
    
    // Determine spacing based on path length
    final double symbolSize = zoomLevel >= 18 ? 7.0 : 5.0;
    final double minSpacing = symbolSize * 10;
    final int symbolCount = math.max(1, (totalLength / minSpacing).floor());
    final double spacing = totalLength / symbolCount;
    
    final Paint symbolPaint = Paint()
      ..color = symbolColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round
      ..isAntiAlias = true;
    
    double distanceTraveled = spacing / 2;  // Start halfway through first segment
    
    // Walk along the path
    int currentSegment = 0;
    double currentSegmentProgress = 0;
    double currentSegmentLength = (points.length > 1) ? 
        _geometryUtils.distance(points[0], points[1]) : 0;
    
    while (distanceTraveled <= totalLength && currentSegment < points.length - 1) {
      // Check if we've moved to the next segment
      while (distanceTraveled > currentSegmentProgress + currentSegmentLength && 
             currentSegment < points.length - 2) {
        currentSegmentProgress += currentSegmentLength;
        currentSegment++;
        currentSegmentLength = _geometryUtils.distance(
          points[currentSegment], points[currentSegment + 1]);
      }
      
      // Calculate position along current segment
      final double t = (distanceTraveled - currentSegmentProgress) / currentSegmentLength;
      final Offset p1 = points[currentSegment];
      final Offset p2 = points[currentSegment + 1];
      
      final Offset symbolCenter = Offset(
        p1.dx + (p2.dx - p1.dx) * t,
        p1.dy + (p2.dy - p1.dy) * t
      );
      
      // Calculate direction vector
      final Offset direction = _geometryUtils.normalizeOffset(Offset(
        p2.dx - p1.dx, p2.dy - p1.dy));
      final Offset perpendicular = Offset(-direction.dy, direction.dx);
      
      // Draw bicycle symbol
      _drawBicycleSymbol(canvas, symbolCenter, symbolSize, direction, perpendicular, symbolPaint);
      
      // Move to next symbol position
      distanceTraveled += spacing;
    }
  }
  
  /// Draw a bicycle symbol
  void _drawBicycleSymbol(
    Canvas canvas, 
    Offset center, 
    double size, 
    Offset direction, 
    Offset perpendicular, 
    Paint paint
  ) {
    // Use a more detailed bicycle symbol at higher zoom levels
    if (zoomLevel >= 18) {
      _drawDetailedBicycleSymbol(canvas, center, size, direction, perpendicular, paint);
    } else {
      _drawSimpleBicycleSymbol(canvas, center, size, direction, perpendicular, paint);
    }
  }
  
  /// Draw a simplified bicycle symbol (for lower zoom levels)
  void _drawSimpleBicycleSymbol(
    Canvas canvas, 
    Offset center, 
    double size, 
    Offset direction, 
    Offset perpendicular, 
    Paint paint
  ) {
    final double wheelRadius = size * 0.4;
    
    // Wheel positions
    final Offset wheel1Center = center - direction * (size * 0.5);
    final Offset wheel2Center = center + direction * (size * 0.5);
    
    // Draw wheels
    canvas.drawCircle(wheel1Center, wheelRadius, paint);
    canvas.drawCircle(wheel2Center, wheelRadius, paint);
    
    // Draw frame connecting wheels
    canvas.drawLine(wheel1Center, wheel2Center, paint);
    
    // Draw handlebar
    final Offset handlebarTop = wheel2Center + perpendicular * (size * 0.3);
    canvas.drawLine(wheel2Center, handlebarTop, paint);
    
    // Draw seat
    final Offset seatTop = wheel1Center + perpendicular * (size * 0.2);
    canvas.drawLine(wheel1Center, seatTop, paint);
  }
  
  /// Draw a more detailed bicycle symbol (for higher zoom levels)
  void _drawDetailedBicycleSymbol(
    Canvas canvas, 
    Offset center, 
    double size, 
    Offset direction, 
    Offset perpendicular, 
    Paint paint
  ) {
    final double wheelRadius = size * 0.35;
    
    // Wheel positions
    final Offset wheel1Center = center - direction * (size * 0.5);
    final Offset wheel2Center = center + direction * (size * 0.5);
    
    // Draw wheels
    canvas.drawCircle(wheel1Center, wheelRadius, paint);
    canvas.drawCircle(wheel2Center, wheelRadius, paint);
    
    // Calculate triangle points for the bicycle frame
    final Offset frameCenter = center + perpendicular * (size * 0.1);
    final Offset seatPost = wheel1Center + perpendicular * (size * 0.4);
    final Offset handlePost = wheel2Center + perpendicular * (size * 0.4);
    
    // Draw the frame as a triangle
    final Path framePath = Path();
    framePath.moveTo(frameCenter.dx, frameCenter.dy);
    framePath.lineTo(seatPost.dx, seatPost.dy);
    framePath.lineTo(wheel1Center.dx, wheel1Center.dy);
    framePath.close();
    
    canvas.drawPath(framePath, paint);
    
    // Draw handlebar
    canvas.drawLine(frameCenter, handlePost, paint);
    canvas.drawLine(handlePost, wheel2Center, paint);
    
    // Draw handlebars
    final Offset handlebarLeft = handlePost + perpendicular * (size * 0.2);
    final Offset handlebarRight = handlePost - perpendicular * (size * 0.2);
    canvas.drawLine(handlePost, handlebarLeft, paint);
    canvas.drawLine(handlePost, handlebarRight, paint);
    
    // Draw seat
    final Offset seat = seatPost + perpendicular * (size * 0.1);
    canvas.drawLine(seatPost, seat - direction * (size * 0.1), paint);
    canvas.drawLine(seatPost, seat + direction * (size * 0.1), paint);
  }
} 