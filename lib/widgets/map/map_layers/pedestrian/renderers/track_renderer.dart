import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'package:flutter_map/flutter_map.dart';

import '../utils/pedestrian_colors.dart';
import '../utils/pedestrian_geometry_utils.dart';
import '../utils/shadow_renderer.dart';

/// Specialized renderer for tracks (hiking paths, walking trails, etc.)
/// Provides enhanced rendering with appropriate styling and patterns
class TrackRenderer {
  final PedestrianGeometryUtils _geometryUtils = PedestrianGeometryUtils();
  final PedestrianColors _colorsHelper = PedestrianColors();
  final double zoomLevel;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  late final ShadowRenderer _shadowRenderer;
  
  TrackRenderer({
    required this.zoomLevel,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  }) {
    _shadowRenderer = ShadowRenderer(
      zoomLevel: zoomLevel,
      tiltFactor: tiltFactor,
    );
  }
  
  /// Get the path width based on zoom level
  double getPathWidth() {
    // Tracks are typically narrower than footways
    double baseWidth = 1.8;
    
    // Scale width based on zoom level
    if (zoomLevel >= 19) return baseWidth * 3.0;
    if (zoomLevel >= 18) return baseWidth * 2.5;
    if (zoomLevel >= 17) return baseWidth * 2.0;
    if (zoomLevel >= 16) return baseWidth * 1.6;
    if (zoomLevel >= 15) return baseWidth * 1.2;
    return baseWidth * 1.0;
  }
  
  /// Main render method for tracks
  void renderTrack(
    Canvas canvas, 
    Size size, 
    Map<String, dynamic> track, 
    Map<String, Color> colors
  ) {
    final List<LatLng> points = track['points'] as List<LatLng>? ?? [];
    
    if (points.length < 2) return;
    
    // Convert geographic coordinates to screen coordinates
    final List<Offset> screenPoints = _geometryUtils.convertToScreenPoints(
      points, 
      track['bounds'] as LatLngBounds, 
      size
    );
    
    // Get track information for styling
    final String? trackType = track['track_type'] as String?;
    final String? surface = track['surface'] as String?;
    
    // Draw the track with appropriate styling
    _drawTrack(canvas, screenPoints, colors, trackType, surface);
  }
  
  /// Draw the main track with appropriate styling
  void _drawTrack(
    Canvas canvas, 
    List<Offset> points, 
    Map<String, Color> colors, 
    String? trackType, 
    String? surface
  ) {
    if (points.length < 2) return;
    
    // Get path width based on zoom and track type
    double pathWidth = getPathWidth();
    
    // Adjust width based on track type
    if (trackType != null) {
      switch (trackType.toLowerCase()) {
        case 'grade1':
        case 'grade2':
          // Better quality tracks are slightly wider
          pathWidth *= 1.2;
          break;
        case 'grade3':
          // Standard width
          break;
        case 'grade4':
        case 'grade5':
          // Lower grade tracks are narrower
          pathWidth *= 0.8;
          break;
        default:
          // Default to standard width
          break;
      }
    }
    
    // Prepare path paints
    final Paint outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth + 1.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    final Paint pathPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    // Create path for drawing
    final Path mainPath = Path();
    mainPath.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      mainPath.lineTo(points[i].dx, points[i].dy);
    }
    
    // Apply 2.5D effect with shadow
    if (tiltFactor > 0.1) {
      final double elevation = 0.04 * tiltFactor; // Subtle elevation for tracks
      _shadowRenderer.drawPathShadow(
        canvas, 
        mainPath, 
        Colors.black.withOpacity(0.3), 
        elevation
      );
    }
    
    // Draw path outline first (for better visibility)
    canvas.drawPath(mainPath, outlinePaint);
    
    // Then draw the main path
    canvas.drawPath(mainPath, pathPaint);
    
    // Add track-specific details based on type and surface
    _addTrackDetails(canvas, points, pathWidth, colors, trackType, surface);
  }
  
  /// Add track-specific details based on type and surface
  void _addTrackDetails(
    Canvas canvas, 
    List<Offset> points, 
    double pathWidth, 
    Map<String, Color> colors, 
    String? trackType, 
    String? surface
  ) {
    if (points.length < 2 || !enhancedDetail) return;
    
    // Pattern paint
    final Paint patternPaint = Paint()
      ..color = colors['pattern']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth * 0.4
      ..strokeCap = StrokeCap.round
      ..isAntiAlias = true;
    
    // Different track types get different patterns
    bool addDashPattern = true;
    bool addMarkers = false;
    double dashWidth = 3.0;
    double dashSpacing = 8.0;
    bool randomizeDashes = false;
    
    // Adjust pattern based on track type
    if (trackType != null) {
      switch (trackType.toLowerCase()) {
        case 'grade1':
        case 'grade2':
          // Better quality tracks get a more regular pattern
          dashWidth = 4.0;
          dashSpacing = 10.0;
          randomizeDashes = false;
          break;
        case 'grade3':
          // Standard pattern
          dashWidth = 3.0;
          dashSpacing = 8.0;
          randomizeDashes = true;
          break;
        case 'grade4':
        case 'grade5':
          // Lower grade tracks get a more irregular pattern
          dashWidth = 2.0;
          dashSpacing = 6.0;
          randomizeDashes = true;
          break;
        case 'path':
          // Simple paths get markers instead of dashes
          addDashPattern = false;
          addMarkers = true;
          break;
        default:
          // Default pattern
          break;
      }
    }
    
    // Further adjust based on surface
    if (surface != null) {
      switch (surface.toLowerCase()) {
        case 'paved':
        case 'asphalt':
        case 'concrete':
          // Paved surfaces get more regular patterns
          randomizeDashes = false;
          dashWidth = 4.0;
          dashSpacing = 12.0;
          break;
        case 'gravel':
          // Semi-regular pattern
          randomizeDashes = true;
          dashWidth = 3.0;
          dashSpacing = 9.0;
          break;
        case 'dirt':
        case 'earth':
        case 'grass':
          // Natural surfaces get more irregular patterns
          randomizeDashes = true;
          dashWidth = 2.0;
          dashSpacing = 6.0;
          break;
        default:
          // Keep default pattern
          break;
      }
    }
    
    // Only add patterns at sufficient zoom levels
    if (zoomLevel >= 16) {
      if (addDashPattern) {
        _drawDashedPath(
          canvas, 
          points, 
          dashWidth, 
          dashSpacing, 
          patternPaint,
          randomizeInterval: randomizeDashes
        );
      }
      
      if (addMarkers && zoomLevel >= 17) {
        _addTrackMarkers(canvas, points, colors);
      }
    }
    
    // Add trail sign at high zoom levels
    if (zoomLevel >= 18) {
      _addTrailSign(canvas, points, colors);
    }
  }
  
  /// Draw a dashed pattern along a path
  void _drawDashedPath(
    Canvas canvas, 
    List<Offset> points, 
    double dashWidth, 
    double dashSpacing, 
    Paint paint, 
    {bool randomizeInterval = false}
  ) {
    if (points.length < 2) return;
    
    double distanceRemaining = 0;
    bool drawDash = true;
    
    for (int i = 0; i < points.length - 1; i++) {
      final Offset start = points[i];
      final Offset end = points[i + 1];
      
      final double segmentLength = _geometryUtils.distance(start, end);
      final Offset direction = _geometryUtils.normalizeOffset(Offset(
        end.dx - start.dx,
        end.dy - start.dy
      ));
      
      double distanceCovered = 0;
      
      while (distanceCovered < segmentLength) {
        final double intervalLength = randomizeInterval 
            ? (drawDash ? dashWidth : dashSpacing) * (0.7 + _random.nextDouble() * 0.6)
            : (drawDash ? dashWidth : dashSpacing);
        
        double currentStepLength;
        
        if (distanceRemaining > 0) {
          currentStepLength = distanceRemaining;
          distanceRemaining = 0;
        } else {
          currentStepLength = intervalLength;
        }
        
        if (distanceCovered + currentStepLength > segmentLength) {
          distanceRemaining = currentStepLength - (segmentLength - distanceCovered);
          currentStepLength = segmentLength - distanceCovered;
        }
        
        if (drawDash) {
          final Offset dashStart = Offset(
            start.dx + direction.dx * distanceCovered,
            start.dy + direction.dy * distanceCovered
          );
          final Offset dashEnd = Offset(
            start.dx + direction.dx * (distanceCovered + currentStepLength),
            start.dy + direction.dy * (distanceCovered + currentStepLength)
          );
          
          canvas.drawLine(dashStart, dashEnd, paint);
        }
        
        distanceCovered += currentStepLength;
        drawDash = !drawDash;
      }
      
      drawDash = !drawDash;
    }
  }
  
  /// Add trail markers (e.g., small dots) along the path
  void _addTrackMarkers(
    Canvas canvas, 
    List<Offset> points, 
    Map<String, Color> colors
  ) {
    if (points.length < 2) return;
    
    final Paint markerPaint = Paint()
      ..color = colors['highlight']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final double totalLength = _geometryUtils.calculatePathLength(points);
    final int markerCount = math.max(2, (totalLength / 50).floor());
    final double markerSpacing = totalLength / markerCount;
    
    for (int i = 0; i < markerCount; i++) {
      final double distance = i * markerSpacing;
      
      // Skip if beyond path length
      if (distance > totalLength) continue;
      
      final Offset position = _geometryUtils.interpolatePointAtDistance(points, distance);
      
      // Draw marker dot
      final double markerSize = zoomLevel >= 18 ? 2.5 : 1.5;
      canvas.drawCircle(position, markerSize, markerPaint);
    }
  }
  
  /// Add a trail sign at the start or a significant point along the track
  void _addTrailSign(
    Canvas canvas, 
    List<Offset> points, 
    Map<String, Color> colors
  ) {
    if (points.length < 2 || zoomLevel < 18) return;
    
    // Choose sign placement - either start of track or at a third of the way in
    final Offset signPosition = (_random.nextBool() || points.length < 5) 
        ? points.first 
        : points[points.length ~/ 3];
    
    final Paint signPaint = Paint()
      ..color = colors['highlight']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final Paint signOutlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..isAntiAlias = true;
    
    // Draw a simple trail sign
    final double signSize = zoomLevel >= 19 ? 6.0 : 4.0;
    
    canvas.drawRect(
      Rect.fromCenter(
        center: signPosition,
        width: signSize,
        height: signSize
      ),
      signPaint
    );
    
    canvas.drawRect(
      Rect.fromCenter(
        center: signPosition,
        width: signSize,
        height: signSize
      ),
      signOutlinePaint
    );
  }
} 