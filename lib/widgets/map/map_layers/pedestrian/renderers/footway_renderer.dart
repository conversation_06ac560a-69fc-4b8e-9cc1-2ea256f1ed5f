import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import '../utils/pedestrian_colors.dart';
import '../utils/rendering_utils.dart';

/// A specialized renderer for OpenStreetMap footway elements
class FootwayRenderer {
  /// The map zoom level for rendering calculations
  final double zoomLevel;
  
  /// The current theme for color selection
  final String theme;
  
  /// Whether to render with enhanced detail
  final bool enhancedDetail;
  
  /// Factor to simulate 3D perspective (0-1)
  final double tiltFactor;
  
  /// Utility for color management
  final PedestrianColors _colors = PedestrianColors();
  
  /// Creates a new footway renderer
  FootwayRenderer({
    required this.zoomLevel,
    required this.theme,
    this.enhancedDetail = false,
    this.tiltFactor = 0.0,
  });
  
  /// Render a footway with the provided colors - compatibility method for OSMPedestrianRenderer
  void renderFootway(Canvas canvas, Size size, Map<String, dynamic> footway, Map<String, Color> colors) {
    // Extract properties from the footway
    final String? surface = footway['surface'] as String?;
    
    // Convert to the format expected by the render method
    Map<String, dynamic> formattedFootway = {
      'geometry': {
        'type': 'LineString',
        'coordinates': footway['points'] != null ? _convertLatLngToCoordinates(footway['points'], footway['bounds'], size) : []
      },
      'properties': {
        'surface': surface ?? 'paved',
        // Copy other properties if available
        'width': footway['width'],
        'tactile_paving': footway['tactile_paving'],
        'lit': footway['lit'],
      }
    };
    
    // Use base rendering logic
    render(canvas, size, [formattedFootway]);
  }
  
  /// Convert LatLng points to coordinate format expected by the renderer
  List<List<double>> _convertLatLngToCoordinates(List<dynamic> points, LatLngBounds bounds, Size size) {
    List<List<double>> coordinates = [];
    
    for (var point in points) {
      if (point is LatLng) {
        // Project the geographic point to screen coordinates
        final percentX = (point.longitude - bounds.west) / (bounds.east - bounds.west);
        final percentY = (point.latitude - bounds.north) / (bounds.south - bounds.north);
        
        final screenX = size.width * percentX;
        final screenY = size.height * percentY;
        
        coordinates.add([screenX, screenY]);
      }
    }
    
    return coordinates;
  }
  
  /// Render footways on the canvas
  void render(Canvas canvas, Size size, List<dynamic> footways) {
    if (footways.isEmpty) return;
    
    // Group footways by surface type for batched rendering
    final Map<String, List<dynamic>> footwaysByType = {};
    
    for (final footway in footways) {
      final String surface = footway['properties']['surface'] ?? 'paved';
      if (!footwaysByType.containsKey(surface)) {
        footwaysByType[surface] = [];
      }
      footwaysByType[surface]!.add(footway);
    }
    
    // Render each surface type batch
    footwaysByType.forEach((surface, footwayList) {
      _renderFootwayBatch(canvas, size, footwayList, surface);
    });
    
    // Render special footway features (if enhanced detail is enabled)
    if (enhancedDetail) {
      _renderEnhancedFootwayFeatures(canvas, size, footways);
    }
  }
  
  /// Render a batch of footways with the same surface type
  void _renderFootwayBatch(Canvas canvas, Size size, List<dynamic> footways, String surface) {
    // Base line width based on zoom level
    final double baseLineWidth = RenderingUtils.getScaledLineWidth(zoomLevel, 2.0, 4.0);
    
    // Get colors for footways
    final Color primaryColor = _colors.getSurfaceColor(theme, 'footway', surface);
    final Color outlineColor = _colors.getColor(theme, 'footway', 'outline');
    
    // Prepare path paint objects
    final Paint outlinePaint = Paint()
      ..color = outlineColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = baseLineWidth + 1.5
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;
    
    final Paint fillPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = baseLineWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;
    
    // Apply pattern to certain surface types
    if (enhancedDetail && ['unpaved', 'gravel', 'dirt', 'ground', 'sand'].contains(surface)) {
      final patternColor = _colors.darken(primaryColor, 10);
      final double dashWidth = zoomLevel > 16 ? 3.0 : 2.0;
      final double dashSpace = zoomLevel > 16 ? 4.0 : 3.0;
      fillPaint.shader = _createDashPatternShader(patternColor, dashWidth, dashSpace);
    }
    
    // Apply shadow if tilt factor is significant
    if (tiltFactor > 0.05) {
      outlinePaint.maskFilter = MaskFilter.blur(BlurStyle.normal, tiltFactor * 0.8);
    }
    
    // Render each footway path
    for (final footway in footways) {
      final path = _createPathFromCoordinates(footway);
      
      // Draw outline first, then fill
      canvas.drawPath(path, outlinePaint);
      canvas.drawPath(path, fillPaint);
    }
  }
  
  /// Create a Path object from feature coordinates
  Path _createPathFromCoordinates(dynamic feature) {
    final path = Path();
    final coordinates = feature['geometry']['coordinates'];
    
    if (coordinates == null || coordinates.isEmpty) return path;
    
    // Different handling based on geometry type
    final String geometryType = feature['geometry']['type'];
    
    if (geometryType == 'LineString') {
      _addLineStringToPath(path, coordinates);
    } 
    else if (geometryType == 'MultiLineString') {
      for (final lineString in coordinates) {
        _addLineStringToPath(path, lineString);
      }
    }
    
    return path;
  }
  
  /// Add a LineString's coordinates to a path
  void _addLineStringToPath(Path path, List<dynamic> coordinates) {
    if (coordinates.isEmpty) return;
    
    final firstPoint = coordinates[0];
    path.moveTo(firstPoint[0].toDouble(), firstPoint[1].toDouble());
    
    for (int i = 1; i < coordinates.length; i++) {
      final point = coordinates[i];
      path.lineTo(point[0].toDouble(), point[1].toDouble());
    }
  }
  
  /// Render enhanced features for footways (only if enhanced detail is enabled)
  void _renderEnhancedFootwayFeatures(Canvas canvas, Size size, List<dynamic> footways) {
    for (final footway in footways) {
      final properties = footway['properties'];
      
      // Handle specific footway types or attributes
      if (properties.containsKey('width') && zoomLevel > 17) {
        _renderFootwayWithWidth(canvas, footway, properties['width']);
      }
      
      // Render tactile paving indicators
      if (properties.containsKey('tactile_paving') && 
          properties['tactile_paving'] == 'yes' && 
          zoomLevel > 18) {
        _renderTactilePaving(canvas, footway);
      }
      
      // Render lit areas
      if (properties.containsKey('lit') && 
          properties['lit'] == 'yes' && 
          zoomLevel > 17) {
        _renderLitFootway(canvas, footway);
      }
    }
  }
  
  /// Render a footway with specific width
  void _renderFootwayWithWidth(Canvas canvas, dynamic footway, dynamic width) {
    // Parse width (may be a string like "2.5" or a number)
    double actualWidth = 1.0;
    try {
      actualWidth = double.parse(width.toString());
    } catch (_) {
      // Default to 1.0 if parsing fails
    }
    
    // Scale width based on zoom
    final scaledWidth = actualWidth * RenderingUtils.getScaleFactor(zoomLevel);
    
    final path = _createPathFromCoordinates(footway);
    final Paint widthPaint = Paint()
      ..color = _colors.getColor(theme, 'footway', 'primary')
      ..style = PaintingStyle.stroke
      ..strokeWidth = scaledWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;
    
    canvas.drawPath(path, widthPaint);
  }
  
  /// Render tactile paving indicators on a footway
  void _renderTactilePaving(Canvas canvas, dynamic footway) {
    final path = _createPathFromCoordinates(footway);
    
    // Create dotted pattern for tactile paving
    final Paint tactilePaint = Paint()
      ..color = _colors.getColor(theme, 'footway', 'accent')
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    // Create a dash pattern
    final dashPattern = path.computeMetrics();
    final dotSpacing = 8.0;
    
    for (final metric in dashPattern) {
      for (double distance = 0; distance < metric.length; distance += dotSpacing) {
        try {
          final position = metric.getTangentForOffset(distance)?.position;
          if (position != null) {
            canvas.drawCircle(position, 1.0, tactilePaint);
          }
        } catch (e) {
          // Skip errors in path calculation
        }
      }
    }
  }
  
  /// Render lighting effects for lit footways
  void _renderLitFootway(Canvas canvas, dynamic footway) {
    final path = _createPathFromCoordinates(footway);
    
    // Subtle glow effect for lit paths
    final Paint glowPaint = Paint()
      ..color = _colors.getColor(theme, 'footway', 'accent').withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 5.0
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 4.0);
    
    canvas.drawPath(path, glowPaint);
  }
  
  /// Create a dash pattern shader for textured paths
  ui.Shader _createDashPatternShader(Color patternColor, double dashWidth, double dashSpace) {
    final dashCanvas = ui.PictureRecorder();
    final dashPainter = Canvas(dashCanvas);
    
    final patternWidth = dashWidth + dashSpace;
    final rect = Rect.fromLTWH(0, 0, patternWidth, 1);
    
    final Paint dashPaint = Paint()
      ..color = patternColor
      ..style = PaintingStyle.fill;
    
    dashPainter.drawRect(Rect.fromLTWH(0, 0, dashWidth, 1), dashPaint);
    
    final picture = dashCanvas.endRecording();
    final futureImage = picture.toImage(patternWidth.ceil(), 1);
    
    // This is a simplification - in a real implementation, you'd handle the Future
    // But for now we'll return a simple linear gradient as a fallback
    return LinearGradient(
      colors: [patternColor, patternColor.withOpacity(0.5)],
      stops: [dashWidth / patternWidth, dashWidth / patternWidth],
      tileMode: TileMode.repeated,
    ).createShader(rect);
  }
} 