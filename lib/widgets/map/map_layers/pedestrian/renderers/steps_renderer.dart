import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'package:flutter_map/flutter_map.dart';

import '../utils/pedestrian_colors.dart';
import '../utils/pedestrian_geometry_utils.dart';
import '../utils/shadow_renderer.dart';

/// Specialized renderer for steps (staircases, etc.)
/// Provides enhanced rendering with step patterns and 2.5D effects
class StepsRenderer {
  final PedestrianGeometryUtils _geometryUtils = PedestrianGeometryUtils();
  final PedestrianColors _colorsHelper = PedestrianColors();
  final double zoomLevel;
  final String theme;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  late final ShadowRenderer _shadowRenderer;
  
  StepsRenderer({
    required this.zoomLevel,
    required this.theme,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  }) {
    _shadowRenderer = ShadowRenderer(
      zoomLevel: zoomLevel,
      tiltFactor: tiltFactor,
    );
  }
  
  /// Get the path width based on zoom level
  double getPathWidth() {
    // Make steps stand out with wider paths
    double baseWidth = 3.0;
    
    // Scale width based on zoom level
    if (zoomLevel >= 19) return baseWidth * 3.0;
    if (zoomLevel >= 18) return baseWidth * 2.5;
    if (zoomLevel >= 17) return baseWidth * 2.0;
    if (zoomLevel >= 16) return baseWidth * 1.6;
    if (zoomLevel >= 15) return baseWidth * 1.2;
    return baseWidth * 1.0;
  }
  
  /// Main render method for steps
  void renderSteps(
    Canvas canvas, 
    Size size, 
    Map<String, dynamic> steps, 
    Map<String, Color> colors
  ) {
    final List<LatLng> points = steps['points'] as List<LatLng>? ?? [];
    
    if (points.length < 2) return;
    
    // Convert geographic coordinates to screen coordinates
    final List<Offset> screenPoints = _geometryUtils.convertToScreenPoints(
      points, 
      steps['bounds'] as LatLngBounds, 
      size
    );
    
    // Get step information
    final int? stepCount = steps['step_count'] as int?;
    final String? material = steps['material'] as String?;
    
    // Draw the steps with appropriate styling
    _drawSteps(canvas, screenPoints, colors, stepCount, material);
  }
  
  /// Draw steps with appropriate styling
  void _drawSteps(
    Canvas canvas, 
    List<Offset> points, 
    Map<String, Color> colors, 
    int? stepCount, 
    String? material
  ) {
    if (points.length < 2) return;
    
    // Get path width based on zoom
    final double pathWidth = getPathWidth();
    
    // Prepare path paints
    final Paint pathPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    final Paint outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth + 1.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    final Paint highlightPaint = Paint()
      ..color = colors['highlight']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth * 0.5
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    // Create path for drawing
    final Path mainPath = Path();
    mainPath.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      mainPath.lineTo(points[i].dx, points[i].dy);
    }
    
    // Apply 2.5D effect with shadow
    if (tiltFactor > 0.1) {
      // Steps have more pronounced shadows for depth
      final double elevation = 0.12 * tiltFactor;
      _shadowRenderer.drawPathShadow(
        canvas, 
        mainPath, 
        Colors.black.withOpacity(0.5), 
        elevation
      );
    }
    
    // Draw path outline first (for better visibility)
    canvas.drawPath(mainPath, outlinePaint);
    
    // Then draw the main path
    canvas.drawPath(mainPath, pathPaint);
    
    // Draw step markers perpendicular to the path
    _drawStepMarkers(canvas, points, pathWidth, colors);
    
    // Add handrails at higher zoom levels
    if (enhancedDetail && zoomLevel >= 17) {
      _drawHandrails(canvas, points, pathWidth, colors['highlight']!);
    }
  }
  
  /// Draw step markers perpendicular to the path
  void _drawStepMarkers(
    Canvas canvas, 
    List<Offset> points, 
    double pathWidth, 
    Map<String, Color> colors
  ) {
    if (points.length < 2) return;
    
    final Paint markerPaint = Paint()
      ..color = colors['pattern']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round
      ..isAntiAlias = true;
    
    // Calculate path length to determine step spacing
    final double totalLength = _geometryUtils.calculatePathLength(points);
    
    // Determine number of steps based on path length
    int stepCount = math.max(3, (totalLength / 10).floor());
    
    // Maximum number of steps to avoid overcrowding
    if (zoomLevel >= 19) {
      stepCount = math.min(stepCount, 30);
    } else if (zoomLevel >= 18) {
      stepCount = math.min(stepCount, 20);
    } else {
      stepCount = math.min(stepCount, 15);
    }
    
    final double stepSpacing = totalLength / stepCount;
    
    // Draw step markers along the path
    for (int i = 1; i < stepCount; i++) {
      final double distance = i * stepSpacing;
      
      // Get point along the path at this distance
      final Offset position = _geometryUtils.interpolatePointAtDistance(points, distance);
      
      // Calculate direction at this point
      // For a more accurate direction, get two points surrounding the current position
      final double distanceForward = math.min(distance + 2, totalLength);
      final double distanceBackward = math.max(distance - 2, 0);
      
      final Offset pointForward = _geometryUtils.interpolatePointAtDistance(points, distanceForward);
      final Offset pointBackward = _geometryUtils.interpolatePointAtDistance(points, distanceBackward);
      
      // Path direction at this point
      final Offset direction = _geometryUtils.normalizeOffset(Offset(
        pointForward.dx - pointBackward.dx,
        pointForward.dy - pointBackward.dy
      ));
      
      // Perpendicular direction for step marker
      final Offset perpendicular = Offset(-direction.dy, direction.dx);
      
      // Calculate step marker endpoints
      final Offset markerStart = Offset(
        position.dx - perpendicular.dx * pathWidth * 0.8,
        position.dy - perpendicular.dy * pathWidth * 0.8
      );
      
      final Offset markerEnd = Offset(
        position.dx + perpendicular.dx * pathWidth * 0.8,
        position.dy + perpendicular.dy * pathWidth * 0.8
      );
      
      // Draw the step marker
      canvas.drawLine(markerStart, markerEnd, markerPaint);
    }
  }
  
  /// Draw handrails alongside steps
  void _drawHandrails(
    Canvas canvas, 
    List<Offset> points, 
    double pathWidth, 
    Color color
  ) {
    if (points.length < 2 || zoomLevel < 17) return;
    
    final Paint railPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..isAntiAlias = true;
    
    // Offset distance from center of steps
    final double railOffset = pathWidth * 0.9;
    
    // Create paths for left and right handrails
    final List<Offset> leftRailPoints = [];
    final List<Offset> rightRailPoints = [];
    
    for (int i = 0; i < points.length - 1; i++) {
      final Offset current = points[i];
      final Offset next = points[i + 1];
      
      // Calculate direction vector
      final Offset direction = _geometryUtils.normalizeOffset(Offset(
        next.dx - current.dx,
        next.dy - current.dy
      ));
      
      // Calculate perpendicular vector
      final Offset perpendicular = Offset(-direction.dy, direction.dx);
      
      // Calculate rail positions
      final Offset leftRail = Offset(
        current.dx - perpendicular.dx * railOffset,
        current.dy - perpendicular.dy * railOffset
      );
      
      final Offset rightRail = Offset(
        current.dx + perpendicular.dx * railOffset,
        current.dy + perpendicular.dy * railOffset
      );
      
      // Add to rail point collections
      leftRailPoints.add(leftRail);
      rightRailPoints.add(rightRail);
    }
    
    // Add final points
    final Offset lastPoint = points.last;
    final Offset secondLastPoint = points[points.length - 2];
    
    final Offset direction = _geometryUtils.normalizeOffset(Offset(
      lastPoint.dx - secondLastPoint.dx,
      lastPoint.dy - secondLastPoint.dy
    ));
    
    final Offset perpendicular = Offset(-direction.dy, direction.dx);
    
    leftRailPoints.add(Offset(
      lastPoint.dx - perpendicular.dx * railOffset,
      lastPoint.dy - perpendicular.dy * railOffset
    ));
    
    rightRailPoints.add(Offset(
      lastPoint.dx + perpendicular.dx * railOffset,
      lastPoint.dy + perpendicular.dy * railOffset
    ));
    
    // Draw the rails
    if (leftRailPoints.length >= 2) {
      final Path leftRailPath = Path();
      leftRailPath.moveTo(leftRailPoints[0].dx, leftRailPoints[0].dy);
      
      for (int i = 1; i < leftRailPoints.length; i++) {
        leftRailPath.lineTo(leftRailPoints[i].dx, leftRailPoints[i].dy);
      }
      
      canvas.drawPath(leftRailPath, railPaint);
    }
    
    if (rightRailPoints.length >= 2) {
      final Path rightRailPath = Path();
      rightRailPath.moveTo(rightRailPoints[0].dx, rightRailPoints[0].dy);
      
      for (int i = 1; i < rightRailPoints.length; i++) {
        rightRailPath.lineTo(rightRailPoints[i].dx, rightRailPoints[i].dy);
      }
      
      canvas.drawPath(rightRailPath, railPaint);
    }
    
    // Add posts at regular intervals
    if (zoomLevel >= 18) {
      final double totalLength = _geometryUtils.calculatePathLength(points);
      final int postCount = math.max(2, (totalLength / 20).floor());
      final double postSpacing = totalLength / postCount;
      
      for (int i = 0; i <= postCount; i++) {
        final double distance = i * postSpacing;
        
        // Skip if beyond path length
        if (distance > totalLength) continue;
        
        final Offset position = _geometryUtils.interpolatePointAtDistance(points, distance);
        
        // Calculate direction at this point
        final double distanceForward = math.min(distance + 5, totalLength);
        final double distanceBackward = math.max(distance - 5, 0);
        
        final Offset pointForward = _geometryUtils.interpolatePointAtDistance(points, distanceForward);
        final Offset pointBackward = _geometryUtils.interpolatePointAtDistance(points, distanceBackward);
        
        final Offset direction = _geometryUtils.normalizeOffset(Offset(
          pointForward.dx - pointBackward.dx,
          pointForward.dy - pointBackward.dy
        ));
        
        final Offset perpendicular = Offset(-direction.dy, direction.dx);
        
        // Left post
        final Offset leftPost = Offset(
          position.dx - perpendicular.dx * railOffset,
          position.dy - perpendicular.dy * railOffset
        );
        
        // Right post
        final Offset rightPost = Offset(
          position.dx + perpendicular.dx * railOffset,
          position.dy + perpendicular.dy * railOffset
        );
        
        // Draw small circles for posts
        canvas.drawCircle(leftPost, 1.0, railPaint);
        canvas.drawCircle(rightPost, 1.0, railPaint);
      }
    }
  }
} 