import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

// Extension to add normalize method to Offset
extension OffsetExtensions on Offset {
  Offset normalize() {
    final double magnitude = distance;
    if (magnitude == 0) return Offset.zero;
    return Offset(dx / magnitude, dy / magnitude);
  }
}

/// Custom painter for rendering OpenStreetMap pedestrian elements with 2.5D effects
class OSMPedestrianPainter extends CustomPainter {
  final Map<String, List<Map<String, dynamic>>> pedestrianData;
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final bool enhancedDetail;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  final MapCamera? mapCamera;
  
  OSMPedestrianPainter({
    required this.pedestrianData,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    required this.theme,
    required this.enhancedDetail,
    this.mapCamera,
  });

  // Color schemes for different pedestrian elements in different themes
  final Map<String, Map<String, Map<String, Color>>> _pathColorPalette = {
    'vibrant': {
      'pedestrian': {
        'fill': const Color(0xFFAED581).withOpacity(0.35),
        'outline': const Color(0xFF7CB342).withOpacity(0.8),
        'pattern': const Color(0xFF558B2F).withOpacity(0.7),
        'highlight': const Color(0xFF558B2F),
      },
      'footway': {
        'fill': const Color(0xFFFFCC80).withOpacity(0.35),
        'outline': const Color(0xFFFF9800).withOpacity(0.8),
        'pattern': const Color(0xFFE65100).withOpacity(0.7),
        'highlight': const Color(0xFFE65100),
      },
      'cycleway': {
        'fill': const Color(0xFF80DEEA).withOpacity(0.35),
        'outline': const Color(0xFF00ACC1).withOpacity(0.8),
        'pattern': const Color(0xFF006064).withOpacity(0.7),
        'highlight': const Color(0xFF006064),
      },
      'path': {
        'fill': const Color(0xFFE6EE9C).withOpacity(0.35),
        'outline': const Color(0xFFCDDC39).withOpacity(0.8),
        'pattern': const Color(0xFF827717).withOpacity(0.7),
        'highlight': const Color(0xFF827717),
      },
      'steps': {
        'fill': const Color(0xFFFFD54F).withOpacity(0.35),
        'outline': const Color(0xFFFFB300).withOpacity(0.8),
        'pattern': const Color(0xFFF57F17).withOpacity(0.7),
        'highlight': const Color(0xFFF57F17),
      },
      'crossing': {
        'fill': const Color(0xFFB0BEC5).withOpacity(0.5),
        'outline': const Color(0xFF78909C).withOpacity(0.8),
        'pattern': const Color(0xFF263238).withOpacity(0.7),
        'highlight': const Color(0xFF263238),
      },
      'track': {
        'fill': const Color(0xFFBCAAA4).withOpacity(0.35),
        'outline': const Color(0xFF8D6E63).withOpacity(0.8),
        'pattern': const Color(0xFF4E342E).withOpacity(0.7),
        'highlight': const Color(0xFF4E342E),
      },
    },
    'dark': {
      'pedestrian': {
        'fill': const Color(0xFF689F38).withOpacity(0.35),
        'outline': const Color(0xFF558B2F).withOpacity(0.8),
        'pattern': const Color(0xFF33691E).withOpacity(0.7),
        'highlight': const Color(0xFF33691E),
      },
      'footway': {
        'fill': const Color(0xFFE65100).withOpacity(0.35),
        'outline': const Color(0xFFEF6C00).withOpacity(0.8),
        'pattern': const Color(0xFFBF360C).withOpacity(0.7),
        'highlight': const Color(0xFFBF360C),
      },
      'cycleway': {
        'fill': const Color(0xFF006064).withOpacity(0.35),
        'outline': const Color(0xFF00838F).withOpacity(0.8),
        'pattern': const Color(0xFF01579B).withOpacity(0.7),
        'highlight': const Color(0xFF01579B),
      },
      'path': {
        'fill': const Color(0xFF827717).withOpacity(0.35),
        'outline': const Color(0xFF9E9D24).withOpacity(0.8),
        'pattern': const Color(0xFF558B2F).withOpacity(0.7),
        'highlight': const Color(0xFF558B2F),
      },
      'steps': {
        'fill': const Color(0xFFF57F17).withOpacity(0.35),
        'outline': const Color(0xFFF9A825).withOpacity(0.8),
        'pattern': const Color(0xFFEF6C00).withOpacity(0.7),
        'highlight': const Color(0xFFEF6C00),
      },
      'crossing': {
        'fill': const Color(0xFF546E7A).withOpacity(0.5),
        'outline': const Color(0xFF455A64).withOpacity(0.8),
        'pattern': const Color(0xFF263238).withOpacity(0.7),
        'highlight': const Color(0xFF263238),
      },
      'track': {
        'fill': const Color(0xFF5D4037).withOpacity(0.35),
        'outline': const Color(0xFF4E342E).withOpacity(0.8),
        'pattern': const Color(0xFF3E2723).withOpacity(0.7),
        'highlight': const Color(0xFF3E2723),
      },
    },
    'monochrome': {
      'pedestrian': {
        'fill': const Color(0xFF9E9E9E).withOpacity(0.35),
        'outline': const Color(0xFF757575).withOpacity(0.7),
        'pattern': const Color(0xFF424242).withOpacity(0.7),
        'highlight': const Color(0xFF424242),
      },
      'footway': {
        'fill': const Color(0xFFBDBDBD).withOpacity(0.35),
        'outline': const Color(0xFF9E9E9E).withOpacity(0.7),
        'pattern': const Color(0xFF757575).withOpacity(0.7),
        'highlight': const Color(0xFF757575),
      },
      'cycleway': {
        'fill': const Color(0xFFE0E0E0).withOpacity(0.35),
        'outline': const Color(0xFFBDBDBD).withOpacity(0.7),
        'pattern': const Color(0xFF9E9E9E).withOpacity(0.7),
        'highlight': const Color(0xFF9E9E9E),
      },
      'path': {
        'fill': const Color(0xFFEEEEEE).withOpacity(0.35),
        'outline': const Color(0xFFE0E0E0).withOpacity(0.7),
        'pattern': const Color(0xFFBDBDBD).withOpacity(0.7),
        'highlight': const Color(0xFFBDBDBD),
      },
      'steps': {
        'fill': const Color(0xFFE0E0E0).withOpacity(0.35),
        'outline': const Color(0xFFBDBDBD).withOpacity(0.7),
        'pattern': const Color(0xFF9E9E9E).withOpacity(0.7),
        'highlight': const Color(0xFF9E9E9E),
      },
      'crossing': {
        'fill': const Color(0xFFEEEEEE).withOpacity(0.5),
        'outline': const Color(0xFFE0E0E0).withOpacity(0.7),
        'pattern': const Color(0xFFBDBDBD).withOpacity(0.7),
        'highlight': const Color(0xFFBDBDBD),
      },
      'track': {
        'fill': const Color(0xFFBDBDBD).withOpacity(0.35),
        'outline': const Color(0xFF9E9E9E).withOpacity(0.7),
        'pattern': const Color(0xFF757575).withOpacity(0.7),
        'highlight': const Color(0xFF757575),
      },
    },
  };

  // Get the appropriate color theme
  Map<String, Map<String, Color>> get themeColors => _pathColorPalette[theme] ?? _pathColorPalette['vibrant']!;

  // Method to get path width based on zoom level and path type
  double _getPathWidthForZoomLevel(String pathType) {
    double baseWidth;
    
    switch (pathType) {
      case 'footway':
        baseWidth = 2.0;
        break;
      case 'cycleway':
        baseWidth = 2.5;
        break;
      case 'path':
        baseWidth = 1.8;
        break;
      case 'steps':
        baseWidth = 2.2;
        break;
      case 'crossing':
        baseWidth = 3.0;
        break;
      case 'track':
        baseWidth = 1.5;
        break;
      default:
        baseWidth = 2.0;
    }
    
    // Scale width based on zoom level
    if (zoomLevel >= 19) return baseWidth * 2.8;
    if (zoomLevel >= 18) return baseWidth * 2.2;
    if (zoomLevel >= 17) return baseWidth * 1.8;
    if (zoomLevel >= 16) return baseWidth * 1.4;
    if (zoomLevel >= 15) return baseWidth * 1.0;
    return baseWidth * 0.8;
  }

  // Helper to get stroke width for different path types
  double _getStrokeWidth(String pathType) {
    return _getPathWidthForZoomLevel(pathType);
  }

  // Draw a dashed path
  void _drawDashedPath(Canvas canvas, List<Offset> points, double dashWidth, double dashSpacing, Color color, {bool applyRandomness = true, bool randomizeInterval = false}) {
    if (points.length < 2) return;
    
    final dashPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = dashWidth
      ..strokeCap = StrokeCap.round;
    
    final Path mainPath = Path();
    mainPath.moveTo(points[0].dx, points[0].dy);
    
    for (int i = 1; i < points.length; i++) {
      mainPath.lineTo(points[i].dx, points[i].dy);
    }
    
    final PathMetrics pathMetrics = mainPath.computeMetrics();
    final Path dashPath = Path();
    
    for (PathMetric metric in pathMetrics) {
      double distance = 0.0;
      bool draw = true;
      
      while (distance < metric.length) {
        final double length = draw 
            ? applyRandomness || randomizeInterval
              ? dashWidth * (0.7 + _random.nextDouble() * 0.6)
              : dashWidth
            : applyRandomness || randomizeInterval
              ? dashSpacing * (0.7 + _random.nextDouble() * 0.6)
              : dashSpacing;
        
        if (draw && distance + length < metric.length) {
          final extractPath = metric.extractPath(distance, distance + length);
          dashPath.addPath(extractPath, Offset.zero);
        }
        
        distance += length;
        draw = !draw;
      }
    }
    
    canvas.drawPath(dashPath, dashPaint);
  }

  // Draw zebra crossing pattern
  void _drawZebraCrossing(Canvas canvas, List<Offset> points, double pathWidth, Map<String, Color> colors) {
    if (points.length < 2) return;
    
    // Create the main path
    final Path crossingPath = Path();
    crossingPath.moveTo(points[0].dx, points[0].dy);
    
    for (int i = 1; i < points.length; i++) {
      crossingPath.lineTo(points[i].dx, points[i].dy);
    }
    
    // Get the crossing direction
    final Offset start = points.first;
    final Offset end = points.last;
    final Offset direction = (end - start).normalize();
    
    // Perpendicular direction for zebra stripes
    final Offset perpendicular = Offset(-direction.dy, direction.dx);
    
    // Draw the background of the crossing
    final backgroundPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth * 1.5
      ..strokeCap = StrokeCap.round;
    
    canvas.drawPath(crossingPath, backgroundPaint);
    
    // Draw the outline
    final outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth * 1.6
      ..strokeJoin = StrokeJoin.round
      ..strokeCap = StrokeCap.round;
    
    // Calculate the stripes
    double stripeWidth = pathWidth * 0.5;
    double totalLength = (end - start).distance;
    double stripeSpacing = pathWidth * 1.0;
    int stripeCount = (totalLength / (stripeWidth + stripeSpacing)).floor();
    
    if (stripeCount > 0) {
      // Draw zebra stripes
      final stripePaint = Paint()
        ..color = colors['pattern']!
        ..style = PaintingStyle.stroke
        ..strokeWidth = stripeWidth
        ..strokeCap = StrokeCap.round;
      
      for (int i = 0; i < stripeCount; i++) {
        double t = i * (stripeWidth + stripeSpacing) / totalLength;
        Offset stripeCenter = Offset(
          start.dx + (end.dx - start.dx) * t,
          start.dy + (end.dy - start.dy) * t
        );
        
        double stripeLength = pathWidth * 1.2;
        Offset stripeStart = Offset(
          stripeCenter.dx - perpendicular.dx * stripeLength / 2,
          stripeCenter.dy - perpendicular.dy * stripeLength / 2
        );
        Offset stripeEnd = Offset(
          stripeCenter.dx + perpendicular.dx * stripeLength / 2,
          stripeCenter.dy + perpendicular.dy * stripeLength / 2
        );
        
        canvas.drawLine(stripeStart, stripeEnd, stripePaint);
      }
    }
  }

  // Project a geographic point to screen coordinates
  Offset _projectPoint(double lat, double lng, Size size) {
    // CRITICAL FIX: Always use MapCamera when available for projection during map movement
    if (mapCamera != null) {
      final screenPoint = mapCamera!.latLngToScreenPoint(LatLng(lat, lng));
      if (screenPoint != null) {
        return Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
      }
    }
    
    // Fall back to simple linear projection if no camera or camera projection fails
    final sw = visibleBounds.southWest;
    final ne = visibleBounds.northEast;
    
    // Calculate x position based on longitude
    final double x = size.width * (lng - sw.longitude) / (ne.longitude - sw.longitude);
    
    // Calculate y position based on latitude
    final double y = size.height * (1.0 - (lat - sw.latitude) / (ne.latitude - sw.latitude));
    
    return Offset(x, y);
  }

  @override
  void paint(Canvas canvas, Size size) {
    final themeColors = _pathColorPalette[theme] ?? _pathColorPalette['vibrant']!;
    
    // Don't render anything if there's no data to display
    if (pedestrianData.isEmpty) return;
    if (pedestrianData.values.every((list) => list.isEmpty)) return;
    
    // Order of drawing is important for visual layers
    
    // 1. First draw pedestrian zones (areas) with elevation
    _drawPedestrianAreas(canvas, size, pedestrianData['pedestrian_zones'] ?? [], 
                         themeColors['pedestrian'] ?? themeColors['pedestrian']!);
    
    // 2. Draw different types of paths with appropriate elevations
    
    // Footways (walking paths)
    _drawPedestrianPaths(canvas, size, pedestrianData['footways'] ?? [], 
                        themeColors['footway'] ?? themeColors['pedestrian']!, 'footway');
    
    // Cycleways 
    _drawPedestrianPaths(canvas, size, pedestrianData['cycleways'] ?? [], 
                        themeColors['cycleway'] ?? themeColors['pedestrian']!, 'cycleway');
    
    // General paths
    _drawPedestrianPaths(canvas, size, pedestrianData['paths'] ?? [], 
                        themeColors['path'] ?? themeColors['pedestrian']!, 'path');
    
    // Tracks (hiking paths, walking trails)
    _drawTracks(canvas, size, pedestrianData['tracks'] ?? [], 
               themeColors['track'] ?? themeColors['pedestrian']!);
    
    // Steps with special rendering
    _drawSteps(canvas, size, pedestrianData['steps'] ?? [], 
              themeColors['steps'] ?? themeColors['pedestrian']!);
    
    // 3. Draw special elements on top
    
    // Crossings with zebra patterns
    _drawCrossings(canvas, size, pedestrianData['crossings'] ?? [], 
                  themeColors['crossing'] ?? themeColors['pedestrian']!);
  }
  
  // Draw pedestrian areas like plazas, squares, and pedestrian zones
  void _drawPedestrianAreas(Canvas canvas, Size size, List<Map<String, dynamic>> areas, 
                          Map<String, Color> colors) {
    if (areas.isEmpty) return;
    
    final fillPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..isAntiAlias = true;
      
    for (final area in areas) {
      final List<LatLng> points = area['points'] as List<LatLng>? ?? [];
      
      if (points.isEmpty) continue;
      
      // Convert geographic coordinates to screen coordinates
      final List<Offset> screenPoints = points.map((point) => 
        _projectPoint(point.latitude, point.longitude, size)).toList();
      
      // Apply slight elevation for 2.5D effect on pedestrian areas
      final double elevation = 0.08 * tiltFactor;
      
      // Draw shadow for 2.5D effect
      if (tiltFactor > 0.1) {
        _drawShadow(canvas, screenPoints, colors['fill']!.withOpacity(0.3), elevation);
      }
      
      // Draw the pedestrian area
      final Path path = Path();
      path.addPolygon(screenPoints, true);
      
      canvas.drawPath(path, fillPaint);
      canvas.drawPath(path, outlinePaint);
      
      // Add texture pattern for pedestrian areas when zoomed in
      if (enhancedDetail && zoomLevel > 17) {
        _drawPedestrianAreaPattern(canvas, screenPoints, colors['pattern']!);
      }
    }
  }
  
  // Draw pedestrian paths like footways, sidewalks, etc.
  void _drawPedestrianPaths(Canvas canvas, Size size, List<Map<String, dynamic>> paths, 
                          Map<String, Color> colors, String pathType) {
    if (paths.isEmpty) return;
    
    final linePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = _getStrokeWidth(pathType)
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    for (final path in paths) {
      final List<LatLng> points = path['points'] as List<LatLng>? ?? [];
      
      if (points.length < 2) continue;
      
      // Convert geographic coordinates to screen coordinates
      final List<Offset> screenPoints = points.map((point) => 
        _projectPoint(point.latitude, point.longitude, size)).toList();
      
      // Apply slight elevation for 2.5D effect on paths
      final double elevation = 0.05 * tiltFactor;
      
      // Draw shadow for 2.5D effect on the path
      if (tiltFactor > 0.1) {
        final Path shadowPath = Path();
        shadowPath.moveTo(screenPoints[0].dx, screenPoints[0].dy);
        
        for (int i = 1; i < screenPoints.length; i++) {
          shadowPath.lineTo(screenPoints[i].dx, screenPoints[i].dy);
        }
        
        final Paint shadowPaint = Paint()
          ..color = Colors.black.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = _getStrokeWidth(pathType)
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, elevation * 3);
        
        canvas.drawPath(
          shadowPath.shift(Offset(elevation * 2, elevation * 2)), 
          shadowPaint
        );
      }
      
      // Draw the main path
      final Path mainPath = Path();
      mainPath.moveTo(screenPoints[0].dx, screenPoints[0].dy);
      
      for (int i = 1; i < screenPoints.length; i++) {
        mainPath.lineTo(screenPoints[i].dx, screenPoints[i].dy);
      }
      
      canvas.drawPath(mainPath, linePaint);
      
      // Add path-specific styling based on type
      if (pathType == 'footway') {
        // Draw dotted pattern for footway
        _drawDashedPath(
          canvas, 
          screenPoints, 
          2.0,  // dashWidth 
          4.0,  // dashSpacing
          colors['pattern']!, 
          applyRandomness: false
        );
      } else if (pathType == 'cycleway') {
        // Draw cycleway symbols at intervals
        _drawCyclewaySymbols(canvas, screenPoints, colors['pattern']!);
      } else if (pathType == 'path') {
        // Draw general path pattern
        _drawDashedPath(
          canvas, 
          screenPoints, 
          1.5,  // dashWidth
          3.0,  // dashSpacing
          colors['pattern']!, 
          applyRandomness: false
        );
      }
    }
  }
  
  // Draw steps with special rendering to show individual steps
  void _drawSteps(Canvas canvas, Size size, List<Map<String, dynamic>> steps, 
                Map<String, Color> colors) {
    if (steps.isEmpty) return;
    
    // Get the appropriate width based on zoom level
    final pathWidth = _getPathWidthForZoomLevel('steps');
    
    final pathPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    final outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth + 1.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    final highlightPaint = Paint()
      ..color = colors['highlight']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth * 0.5
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    for (final step in steps) {
      // Draw each step with a subtle 3D effect
      final points = step['points'] as List? ?? [];
      if (points.length < 2) continue;
      
      final Path outlinePath = Path();
      final Path mainPath = Path();
      final Path highlightPath = Path();
      
      bool first = true;
      List<Offset> screenPoints = [];
      
      // Convert geographic points to screen coordinates
      for (final point in points) {
        final Offset basePoint = _projectPoint(point.latitude, point.longitude, size);
        
        // Apply a subtle elevation effect based on tilt
        final Offset elevatedPoint = Offset(
          basePoint.dx,
          basePoint.dy - (1.0 * tiltFactor)
        );
        
        screenPoints.add(elevatedPoint);
        
        if (first) {
          outlinePath.moveTo(elevatedPoint.dx, elevatedPoint.dy);
          mainPath.moveTo(elevatedPoint.dx, elevatedPoint.dy);
          highlightPath.moveTo(elevatedPoint.dx, elevatedPoint.dy - (pathWidth * 0.2));
          first = false;
        } else {
          outlinePath.lineTo(elevatedPoint.dx, elevatedPoint.dy);
          mainPath.lineTo(elevatedPoint.dx, elevatedPoint.dy);
          highlightPath.lineTo(elevatedPoint.dx, elevatedPoint.dy - (pathWidth * 0.2));
        }
      }
      
      // Draw the paths with a layered effect for depth
      canvas.drawPath(outlinePath, outlinePaint);
      canvas.drawPath(mainPath, pathPaint);
      
      // Only add highlight at higher zoom levels
      if (enhancedDetail && zoomLevel > 16) {
        canvas.drawPath(highlightPath, highlightPaint);
      }
      
      // Add dashed effect for steps
      if (enhancedDetail && zoomLevel > 17) {
        _drawDashedPath(
          canvas, 
          screenPoints, 
          pathWidth * 0.3, 
          pathWidth * 1.5, 
          colors['highlight']!.withOpacity(0.5)
        );
      }
    }
  }
  
  // Draw tracks (walking trails, hiking paths)
  void _drawTracks(Canvas canvas, Size size, List<Map<String, dynamic>> tracks, 
                Map<String, Color> colors) {
    if (tracks.isEmpty) return;
    
    // Get the appropriate width based on zoom level
    final pathWidth = _getPathWidthForZoomLevel('track');
    
    final pathPaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    final outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth + 1.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;
    
    for (final track in tracks) {
      // Draw each track with a natural, organic look
      final points = track['points'] as List? ?? [];
      if (points.length < 2) continue;
      
      final Path outlinePath = Path();
      final Path mainPath = Path();
      
      bool first = true;
      List<Offset> screenPoints = [];
      
      // Convert geographic points to screen coordinates
      for (final point in points) {
        final Offset basePoint = _projectPoint(point.latitude, point.longitude, size);
        
        // Apply a subtle elevation effect based on tilt
        final Offset elevatedPoint = Offset(
          basePoint.dx,
          basePoint.dy - (0.5 * tiltFactor)
        );
        
        screenPoints.add(elevatedPoint);
        
        if (first) {
          outlinePath.moveTo(elevatedPoint.dx, elevatedPoint.dy);
          mainPath.moveTo(elevatedPoint.dx, elevatedPoint.dy);
          first = false;
        } else {
          outlinePath.lineTo(elevatedPoint.dx, elevatedPoint.dy);
          mainPath.lineTo(elevatedPoint.dx, elevatedPoint.dy);
        }
      }
      
      // Draw the paths with a layered effect for depth
      canvas.drawPath(outlinePath, outlinePaint);
      canvas.drawPath(mainPath, pathPaint);
      
      // Add a nature-inspired dashed effect for trails
      if (enhancedDetail && zoomLevel > 16) {
        _drawDashedPath(
          canvas, 
          screenPoints, 
          pathWidth * 0.25, 
          pathWidth * 3.0, 
          colors['highlight']!, 
          randomizeInterval: true
        );
      }
    }
  }
  
  // Draw crossings (pedestrian crossings)
  void _drawCrossings(Canvas canvas, Size size, List<Map<String, dynamic>> crossings, 
                  Map<String, Color> colors) {
    if (crossings.isEmpty) return;
    
    // Get the appropriate width based on zoom level
    final pathWidth = _getPathWidthForZoomLevel('crossing');
    
    final pathPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth
      ..strokeCap = StrokeCap.butt
      ..strokeJoin = StrokeJoin.miter
      ..isAntiAlias = true;
    
    final outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth + 1.5
      ..strokeCap = StrokeCap.butt
      ..strokeJoin = StrokeJoin.miter
      ..isAntiAlias = true;
    
    for (final crossing in crossings) {
      // Draw each crossing with a zebra crossing pattern if at high zoom
      final points = crossing['points'] as List? ?? [];
      if (points.length < 2) continue;
      
      final Path outlinePath = Path();
      final Path mainPath = Path();
      
      bool first = true;
      List<Offset> screenPoints = [];
      
      // Convert geographic points to screen coordinates
      for (final point in points) {
        final Offset basePoint = _projectPoint(point.latitude, point.longitude, size);
        
        // Apply a subtle elevation effect based on tilt
        final Offset elevatedPoint = Offset(
          basePoint.dx,
          basePoint.dy - (1.5 * tiltFactor)
        );
        
        screenPoints.add(elevatedPoint);
        
        if (first) {
          outlinePath.moveTo(elevatedPoint.dx, elevatedPoint.dy);
          mainPath.moveTo(elevatedPoint.dx, elevatedPoint.dy);
          first = false;
        } else {
          outlinePath.lineTo(elevatedPoint.dx, elevatedPoint.dy);
          mainPath.lineTo(elevatedPoint.dx, elevatedPoint.dy);
        }
      }
      
      // Draw the crosswalk
      canvas.drawPath(outlinePath, outlinePaint);
      canvas.drawPath(mainPath, pathPaint);
      
      // Add zebra crossing pattern at higher zoom levels
      if (zoomLevel > 17 && screenPoints.length >= 2) {
        _drawZebraCrossing(canvas, screenPoints, pathWidth, colors);
      }
    }
  }
  
  // Add texture to pedestrian areas
  void _addAreaTexture(Canvas canvas, Path path, Color color) {
    final texturePaint = Paint()
      ..color = color.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    // Create a clipPath to constrain drawing within the area
    canvas.save();
    canvas.clipPath(path);
    
    // Draw a grid pattern or dots pattern
    final bounds = path.getBounds();
    
    // Grid pattern
    for (double x = bounds.left; x < bounds.right; x += 10.0) {
      final linePath = Path()
        ..moveTo(x, bounds.top)
        ..lineTo(x, bounds.bottom);
      canvas.drawPath(linePath, texturePaint);
    }
    
    for (double y = bounds.top; y < bounds.bottom; y += 10.0) {
      final linePath = Path()
        ..moveTo(bounds.left, y)
        ..lineTo(bounds.right, y);
      canvas.drawPath(linePath, texturePaint);
    }
    
    canvas.restore();
  }
  
  // Helper method to draw shadows for 2.5D effect
  void _drawShadow(Canvas canvas, List<Offset> points, Color shadowColor, double elevation) {
    // Create shadow path
    final Path shadowPath = Path();
    if (points.isEmpty) return;
    
    shadowPath.moveTo(points[0].dx, points[0].dy);
    
    for (int i = 1; i < points.length; i++) {
      shadowPath.lineTo(points[i].dx, points[i].dy);
    }
    
    shadowPath.close();
    
    // Create a shadow paint with blur
    final Paint shadowPaint = Paint()
      ..color = shadowColor
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, elevation * 3);
    
    // Draw the shadow slightly offset
    canvas.drawPath(shadowPath.shift(Offset(elevation * 2, elevation * 2)), shadowPaint);
  }
  
  // Helper method to draw pattern in pedestrian areas
  void _drawPedestrianAreaPattern(Canvas canvas, List<Offset> points, Color patternColor) {
    if (points.length < 3) return;
    
    // Create a path for the area
    final Path areaPath = Path();
    areaPath.addPolygon(points, true);
    
    // Get the bounds of the area
    final Rect bounds = areaPath.getBounds();
    
    // Create a clipping path to contain the pattern
    canvas.save();
    canvas.clipPath(areaPath);
    
    // Create a grid pattern
    final double spacing = 12.0;
    final Paint patternPaint = Paint()
      ..color = patternColor.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.8;
    
    // Draw horizontal lines
    for (double y = bounds.top; y <= bounds.bottom; y += spacing) {
      canvas.drawLine(Offset(bounds.left, y), Offset(bounds.right, y), patternPaint);
    }
    
    // Draw vertical lines
    for (double x = bounds.left; x <= bounds.right; x += spacing) {
      canvas.drawLine(Offset(x, bounds.top), Offset(x, bounds.bottom), patternPaint);
    }
    
    // Restore the canvas state
    canvas.restore();
  }
  
  // Draw cycleway symbols along a path
  void _drawCyclewaySymbols(Canvas canvas, List<Offset> points, Color symbolColor) {
    if (points.length < 2) return;
    
    // Only draw symbols at high enough zoom levels
    if (zoomLevel < 16) return;
    
    // Calculate total path length
    double totalLength = 0;
    for (int i = 0; i < points.length - 1; i++) {
      totalLength += (points[i + 1] - points[i]).distance;
    }
    
    // Determine spacing based on path length
    final double symbolSize = zoomLevel >= 18 ? 6.0 : 4.0;
    final double minSpacing = symbolSize * 8;
    final int symbolCount = math.max(1, (totalLength / minSpacing).floor());
    final double spacing = totalLength / symbolCount;
    
    // Drawing the bicycle symbols
    final Paint symbolPaint = Paint()
      ..color = symbolColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round
      ..isAntiAlias = true;
    
    double distanceTraveled = spacing / 2;  // Start halfway through first segment
    
    // Walk along the path
    int currentSegment = 0;
    double currentSegmentProgress = 0;
    double currentSegmentLength = (points[1] - points[0]).distance;
    
    while (distanceTraveled <= totalLength && currentSegment < points.length - 1) {
      // Check if we've moved to the next segment
      while (distanceTraveled > currentSegmentProgress + currentSegmentLength && 
             currentSegment < points.length - 2) {
        currentSegmentProgress += currentSegmentLength;
        currentSegment++;
        currentSegmentLength = (points[currentSegment + 1] - points[currentSegment]).distance;
      }
      
      // Calculate position along current segment
      final double t = (distanceTraveled - currentSegmentProgress) / currentSegmentLength;
      final Offset p1 = points[currentSegment];
      final Offset p2 = points[currentSegment + 1];
      
      final Offset symbolCenter = Offset(
        p1.dx + (p2.dx - p1.dx) * t,
        p1.dy + (p2.dy - p1.dy) * t
      );
      
      // Calculate direction vector
      final Offset direction = (p2 - p1).normalize();
      final Offset perpendicular = Offset(-direction.dy, direction.dx);
      
      // Draw bicycle symbol (simplified)
      _drawBicycleSymbol(canvas, symbolCenter, symbolSize, direction, perpendicular, symbolPaint);
      
      // Move to next symbol position
      distanceTraveled += spacing;
    }
  }
  
  // Draw a simplified bicycle symbol
  void _drawBicycleSymbol(Canvas canvas, Offset center, double size, 
                         Offset direction, Offset perpendicular, Paint paint) {
    // Draw two wheels
    final double wheelRadius = size * 0.4;
    final Offset wheel1Center = center - direction * (size * 0.5);
    final Offset wheel2Center = center + direction * (size * 0.5);
    
    canvas.drawCircle(wheel1Center, wheelRadius, paint);
    canvas.drawCircle(wheel2Center, wheelRadius, paint);
    
    // Draw frame connecting wheels
    canvas.drawLine(wheel1Center, wheel2Center, paint);
    
    // Draw handlebar
    final Offset handlebarTop = wheel2Center + perpendicular * (size * 0.3);
    canvas.drawLine(wheel2Center, handlebarTop, paint);
    
    // Draw seat
    final Offset seatTop = wheel1Center + perpendicular * (size * 0.2);
    canvas.drawLine(wheel1Center, seatTop, paint);
  }
  
  @override
  bool shouldRepaint(OSMPedestrianPainter oldDelegate) {
    // IMPORTANT FIX: Always repaint when mapCamera changes to ensure smooth movement
    return oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.visibleBounds != visibleBounds ||
           oldDelegate.theme != theme ||
           oldDelegate.mapCamera != mapCamera ||
           oldDelegate.pedestrianData != pedestrianData;
  }
} 