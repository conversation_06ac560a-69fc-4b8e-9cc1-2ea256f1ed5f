import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

import '../osm_data_processor.dart';
import '../../../../services/map_cache_manager.dart';
import '../../map_caching/zoom_level_manager.dart';
import '../../map_caching/map_cache_extension.dart';
import '../../map_caching/map_cache_coordinator.dart';
import '../../map_caching/osm_throttle_manager.dart';

/// Data provider class that handles pedestrian infrastructure data fetching and caching
/// This class is responsible for all the data-related logic, separating it from UI rendering
class OSMPedestrianDataProvider {
  // Data processing and caching dependencies
  final OSMDataProcessor _dataProcessor = OSMDataProcessor();
  final MapCacheManager _cacheManager = MapCacheManager();
  final ZoomLevelManager _zoomManager = ZoomLevelManager();
  final OSMThrottleManager _throttleManager = OSMThrottleManager();
  
  // Callback for error handling
  final Function(String errorMessage)? onError;
  
  // State variables
  Map<String, List<Map<String, dynamic>>> _pedestrianData = {
    'footways': [],
    'crossings': [],
    'pedestrian_areas': [],
    'tracks': []
  };
  bool _isLoading = true;
  bool _needsRefresh = true;
  String _lastBoundsKey = "";
  bool _hasError = false;
  bool _didInitialFetch = false;
  
  // Track last fetch params to avoid unnecessary fetches
  LatLngBounds? _lastFetchedBounds;
  double _lastFetchedZoom = 0;
  
  // Current parameters
  double _zoomLevel;
  LatLngBounds _visibleBounds;
  bool _isMapMoving;
  
  // Track current zoom level bucket for optimized rendering
  int _currentZoomBucket = 3;
  
  // Keep track of the last request time for rate limiting
  DateTime _lastRequestTime = DateTime.now().subtract(const Duration(seconds: 30));
  
  /// Constructor that takes initial parameters and optional error callback
  OSMPedestrianDataProvider({
    required double initialZoomLevel,
    required LatLngBounds initialBounds,
    bool isMapMoving = false,
    this.onError,
  }) : 
    _zoomLevel = initialZoomLevel,
    _visibleBounds = initialBounds,
    _isMapMoving = isMapMoving {
    // Initialize zoom bucket
    _currentZoomBucket = _getZoomBucket(initialZoomLevel);
  }

  /// Check if the provider has initial data
  bool get hasInitialData => _didInitialFetch && !_isLoading;
  
  /// Check if there's an error
  bool get hasError => _hasError;
  
  /// Get the pedestrian data
  Map<String, List<Map<String, dynamic>>> get pedestrianData => _pedestrianData;
  
  /// Get current zoom bucket
  int getCurrentZoomBucket() => _currentZoomBucket;

  /// Convert zoom level to bucket for detail control
  int _getZoomBucket(double zoomLevel) {
    if (zoomLevel < 6) return 0;  // Global view
    if (zoomLevel < 9) return 1;  // Continental view
    if (zoomLevel < 12) return 2; // Country view
    if (zoomLevel < 15) return 3; // Region/city view
    if (zoomLevel < 18) return 4; // Neighborhood view
    return 5;                     // Street view
  }
  
  /// Update the parameters when map changes
  void updateParameters({
    required double zoomLevel,
    required LatLngBounds visibleBounds,
    required bool isMapMoving,
  }) {
    // Update current parameters
    _zoomLevel = zoomLevel;
    _visibleBounds = visibleBounds;
    _isMapMoving = isMapMoving;
    
    // Update zoom bucket if needed
    final newZoomBucket = _getZoomBucket(zoomLevel);
    if (newZoomBucket != _currentZoomBucket) {
      _currentZoomBucket = newZoomBucket;
      _needsRefresh = true;
    }
  }
  
  /// Determine if we need to fetch new data based on parameter changes
  bool shouldFetchNewData({
    required double oldZoomLevel,
    required LatLngBounds oldBounds,
    required bool oldIsMoving,
    required double newZoomLevel,
    required LatLngBounds newBounds,
    required bool newIsMoving,
  }) {
    // Check if zoom bucket has changed
    final oldZoomBucket = _getZoomBucket(oldZoomLevel);
    final newZoomBucket = _getZoomBucket(newZoomLevel);
    final zoomBucketChanged = oldZoomBucket != newZoomBucket;
    
    // Check if bounds have changed significantly
    bool boundsChanged = false;
    if (oldBounds != newBounds) {
      final newBoundsKey = _getBoundsKey(newBounds, newZoomLevel);
      boundsChanged = newBoundsKey != _lastBoundsKey;
    }
    
    // Track if map has stopped moving
    final wasMoving = oldIsMoving;
    final isMovingNow = newIsMoving;
    final stoppedMoving = wasMoving && !isMovingNow;
    
    // Decide if we should fetch new data
    if ((boundsChanged && !isMovingNow) || 
        (stoppedMoving && _needsRefresh) || 
        zoomBucketChanged) {
      
      // If we just stopped moving, we can delay the fetch slightly
      if (stoppedMoving) {
        _needsRefresh = true;
        return false; // We'll fetch in a moment when the map settles
      } else {
        return true; // Fetch immediately
      }
    }
    
    return false;
  }
  
  /// Get a key to identify current map bounds, with reduced precision for fewer unnecessary refreshes
  String _getBoundsKey(LatLngBounds bounds, double zoom) {
    final sw = bounds.southWest;
    final ne = bounds.northEast;
    
    // Reduce precision for bounds (4 decimal places ≈ 10m accuracy)
    return 'bounds_${sw.latitude.toStringAsFixed(4)}_${sw.longitude.toStringAsFixed(4)}_${ne.latitude.toStringAsFixed(4)}_${ne.longitude.toStringAsFixed(4)}_${zoom.toStringAsFixed(1)}';
  }
  
  /// Calculate a distance metric between two bounds
  double _calculateBoundsDistance(LatLngBounds bounds1, LatLngBounds bounds2) {
    // Simple Euclidean distance between centers
    final center1 = LatLng(
      (bounds1.northEast.latitude + bounds1.southWest.latitude) / 2,
      (bounds1.northEast.longitude + bounds1.southWest.longitude) / 2
    );
    
    final center2 = LatLng(
      (bounds2.northEast.latitude + bounds2.southWest.latitude) / 2,
      (bounds2.northEast.longitude + bounds2.southWest.longitude) / 2
    );
    
    // Approximate using flat-earth model for small distances
    return math.sqrt(
      math.pow(center1.latitude - center2.latitude, 2) +
      math.pow(center1.longitude - center2.longitude, 2)
    );
  }
  
  /// Calculate a safe request bounds that won't exceed Overpass API limits
  LatLngBounds _calculateSafeRequestBounds(LatLngBounds visibleBounds, double detailLevel, double zoomLevel) {
    final double latDelta = visibleBounds.northEast.latitude - visibleBounds.southWest.latitude;
    final double lonDelta = visibleBounds.northEast.longitude - visibleBounds.southWest.longitude;
    
    // Calculate center of visible bounds
    final LatLng center = LatLng(
      (visibleBounds.northEast.latitude + visibleBounds.southWest.latitude) / 2,
      (visibleBounds.northEast.longitude + visibleBounds.southWest.longitude) / 2
    );
    
    // Adjust request size based on zoom level and detail level
    double requestFactor = 1.0;
    
    // At higher zoom levels and higher detail, request smaller areas
    if (zoomLevel > 17) {
      requestFactor = 0.8 / detailLevel;
    } else if (zoomLevel > 15) {
      requestFactor = 1.0 / detailLevel;
    } else if (zoomLevel > 13) {
      requestFactor = 1.2 / detailLevel;
    } else {
      requestFactor = 1.5 / detailLevel;
    }
    
    // Cap to reasonable values to avoid exceeding API limits
    requestFactor = math.max(0.5, math.min(requestFactor, 1.5));
    
    // Calculate new bounds with adjusted size
    return LatLngBounds(
      LatLng(
        center.latitude - (latDelta * requestFactor / 2),
        center.longitude - (lonDelta * requestFactor / 2)
      ),
      LatLng(
        center.latitude + (latDelta * requestFactor / 2),
        center.longitude + (lonDelta * requestFactor / 2)
      )
    );
  }
  
  /// Get detail level based on zoom bucket
  double _getDetailLevel(int zoomBucket) {
    switch (zoomBucket) {
      case 0: return 0.1; // Global view - minimal detail
      case 1: return 0.2; // Continental view - very low detail
      case 2: return 0.4; // Country view - low detail
      case 3: return 0.7; // Region/city view - medium detail
      case 4: return 1.0; // Neighborhood view - high detail
      case 5: return 1.5; // Street view - very high detail
      default: return 1.0;
    }
  }
  
  /// Reset error state to try again
  void resetErrorState() {
    _hasError = false;
    _dataProcessor.resetErrorState();
  }
  
  /// Fetch pedestrian infrastructure data from OpenStreetMap
  Future<void> fetchPedestrianData() async {
    // LOWERED zoom threshold to show pedestrian paths at lower zoom levels
    if (_currentZoomBucket <= 1 || _zoomLevel < 13) {
      _pedestrianData = {
        'footways': [],
        'crossings': [],
        'pedestrian_areas': [],
        'tracks': []
      };
      _isLoading = false;
      _needsRefresh = false;
      _didInitialFetch = true;
      _hasError = false;
      return;
    }
    
    _isLoading = !_didInitialFetch; // Only show loading on first fetch
    
    // Update bounds key
    _lastBoundsKey = _getBoundsKey(_visibleBounds, _zoomLevel);
    
    // Check if bounds or zoom has significantly changed
    bool shouldSkipFetch = false;
    if (_lastFetchedBounds != null && _lastFetchedZoom > 0) {
      final boundsDistance = _calculateBoundsDistance(_visibleBounds, _lastFetchedBounds!);
      final zoomDifference = (_zoomLevel - _lastFetchedZoom).abs();
      
      // REDUCED caching thresholds to fetch more frequently
      if (_isMapMoving && _pedestrianData['footways']!.isNotEmpty) {
        shouldSkipFetch = true;
      }
      // Reduced threshold for more frequent fetching
      else if (boundsDistance < 0.05 && zoomDifference < 0.8 && _pedestrianData['footways']!.isNotEmpty) {
        shouldSkipFetch = true;
      }
    }
    
    if (shouldSkipFetch) {
      _isLoading = false;
      _needsRefresh = true; // Mark for refresh when map movement stops
      return;
    }
    
    // Add randomization to request timing to avoid synchronized requests across layers
    final randomDelayMs = math.Random().nextInt(1500);
    if (randomDelayMs > 0) {
      await Future.delayed(Duration(milliseconds: randomDelayMs));
    }
    
    // Generate cache key for the MapCacheCoordinator
    final cacheKey = 'pedestrian_${_visibleBounds.southWest.latitude.toStringAsFixed(4)}_${_visibleBounds.southWest.longitude.toStringAsFixed(4)}_${_visibleBounds.northEast.latitude.toStringAsFixed(4)}_${_visibleBounds.northEast.longitude.toStringAsFixed(4)}_${_currentZoomBucket}';
    
    try {
      // Use the centralized throttle manager to check if we can make a request
      final canRequest = await _throttleManager.canMakeRequest(
        'pedestrian', 
        _visibleBounds
      );
      
      if (!canRequest) {
        debugPrint('Pedestrian request throttled by central manager');
        // If we're not allowed to make a request now, use existing data
        _isLoading = false;
        _needsRefresh = true; // Try again later
        return;
      }
      
      // Use MapCacheCoordinator to get data from cache or fetch from network
      final pedestrianData = await MapCacheCoordinator().getData(
        type: MapDataType.poi, // Reuse the POI type for pedestrian data
        key: cacheKey,
        southwest: _visibleBounds.southWest,
        northeast: _visibleBounds.northEast,
        zoomLevel: _zoomLevel,
        fetchIfMissing: () async {
          // Adapt detail level based on zoom bucket
          final detailLevel = _getDetailLevel(_currentZoomBucket);
          
          // Calculate a safe data request region based on Overpass API limits
          final safeRequestBounds = _calculateSafeRequestBounds(
            _visibleBounds,
            detailLevel,
            _zoomLevel
          );
          
          // Record this request in the throttle manager
          _throttleManager.recordRequest('pedestrian', _visibleBounds);
          
          // Create the Overpass queries for different pedestrian infrastructure
          final Map<String, dynamic> result = await _fetchPedestrianFeatures(
            safeRequestBounds.southWest,
            safeRequestBounds.northEast
          );
          
          return result;
        }
      );
      
      if (pedestrianData != null) {
        _pedestrianData = Map<String, List<Map<String, dynamic>>>.from(pedestrianData);
      } else {
        _pedestrianData = {
          'footways': [],
          'crossings': [],
          'pedestrian_areas': [],
          'tracks': []
        };
      }
      
      _isLoading = false;
      _needsRefresh = false;
      _lastFetchedBounds = _visibleBounds;
      _lastFetchedZoom = _zoomLevel;
      _didInitialFetch = true;
      _hasError = false;
      _lastRequestTime = DateTime.now(); // Update last request time for throttling
    } catch (e) {
      debugPrint('Error in OSMPedestrianDataProvider: $e');
      _isLoading = false;
      _hasError = true;
      _didInitialFetch = true; // We did try to fetch
      
      // Record error in throttle manager
      _throttleManager.recordError('pedestrian', e.toString());
      
      // Notify listeners about the error
      onError?.call(e.toString());
    }
  }
  
  /// Fetch different types of pedestrian infrastructure using the OSMDataProcessor
  Future<Map<String, dynamic>> _fetchPedestrianFeatures(LatLng southwest, LatLng northeast) async {
    final Map<String, List<Map<String, dynamic>>> result = {
      'footways': [],
      'crossings': [],
      'pedestrian_areas': [],
      'tracks': []
    };
    
    try {
      // LOWERED thresholds to show pedestrian paths at lower zoom levels
      final bool fetchMinimal = _zoomLevel < 15;
      final bool skipEntirelyAtLowZoom = _zoomLevel < 13; // Reduced from 14
      final bool fetchDetailed = _zoomLevel >= 16; // Reduced from 17
      
      // Skip fetching at very low zoom levels where data wouldn't be visible anyway
      if (skipEntirelyAtLowZoom) {
        debugPrint('Zoom level too low for pedestrian data, returning empty result');
        return result;
      }
      
      // Combine queries where possible to reduce number of API requests
      if (fetchMinimal) {
        // IMPROVED query to include more pedestrian path types
        String combinedQuery = '[highway=pedestrian], [area:highway=pedestrian], ' +
                               '[highway=footway], [highway=path][foot=designated], ' +
                               '[highway=path][foot=yes], [highway=track][foot=designated]';
        
        final combinedResults = await _dataProcessor.fetchCustomData(
          southwest,
          northeast,
          combinedQuery,
          'way,relation' // Added relation type to catch more complex structures
        );
        
        // Sort the results into their respective categories
        for (var feature in combinedResults) {
          String tags = feature['tags'].toString().toLowerCase();
          if (tags.contains('pedestrian')) {
            result['pedestrian_areas']!.add(feature);
          } else if (tags.contains('path') && (tags.contains('designated') || tags.contains('yes'))) {
            result['tracks']!.add(feature);
          } else if (tags.contains('track') && tags.contains('designated')) {
            result['tracks']!.add(feature);
          } else {
            result['footways']!.add(feature);
          }
        }
        
        debugPrint('Received ${combinedResults.length} combined pedestrian elements');
      } else {
        // At higher zoom levels, fetch more detailed data with separate queries
      
        // IMPROVED footways query to include more types
        String footwayQuery = '[highway=footway][footway!=crossing], ' +
                             '[highway=path][foot=designated], ' +
                             '[highway=path][foot=yes]';
        
        result['footways'] = await _dataProcessor.fetchCustomData(
          southwest,
          northeast,
          footwayQuery,
          'way,relation' // Added relation type
        );
        debugPrint('Received ${result['footways']!.length} footway elements');
        
        // Add a longer delay between requests
        await Future.delayed(const Duration(milliseconds: 500));
        
        // Fetch pedestrian areas (plazas, pedestrian zones) - always important
        result['pedestrian_areas'] = await _dataProcessor.fetchCustomData(
          southwest,
          northeast,
          '[highway=pedestrian], [area:highway=pedestrian]',
          'way'
        );
        debugPrint('Received ${result['pedestrian_areas']!.length} pedestrian area elements');
        
        // Only fetch more detailed features at higher zoom levels
        if (!fetchMinimal) {
          // Add a longer delay between requests
          await Future.delayed(const Duration(milliseconds: 600));
          
          // Fetch tracks and trails (walking trails, etc.)
          result['tracks'] = await _dataProcessor.fetchCustomData(
            southwest,
            northeast,
            '[highway=track][foot=yes], [highway=track][foot=designated], [highway=path][foot=yes]',
            'way'
          );
          debugPrint('Received ${result['tracks']!.length} track/trail elements');
          
          // Only fetch detailed crossings at the highest zoom levels
          if (fetchDetailed) {
            // Add an even longer delay for crossings
            await Future.delayed(const Duration(milliseconds: 700));
            
            // Fetch crossings
            result['crossings'] = await _dataProcessor.fetchCustomData(
              southwest,
              northeast,
              '[highway=footway][footway=crossing], [highway=crossing]',
              'way,node'
            );
            debugPrint('Received ${result['crossings']!.length} crossing elements');
          }
        }
      }
      
      // Print total count
      int totalCount = result['footways']!.length + result['crossings']!.length + 
                      result['pedestrian_areas']!.length + result['tracks']!.length;
      debugPrint('Total pedestrian elements: $totalCount');
      
      return result;
    } catch (e) {
      // Record error in throttle manager
      _throttleManager.recordError('pedestrian', e.toString());
      debugPrint('Error fetching pedestrian infrastructure: $e');
      throw e;
    }
  }
} 