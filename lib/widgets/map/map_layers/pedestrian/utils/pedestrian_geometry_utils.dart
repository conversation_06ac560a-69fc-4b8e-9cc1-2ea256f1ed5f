import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path;

/// Utility class for pedestrian infrastructure geometry operations
/// Provides methods for coordinate conversion, geometry manipulation, and path calculations
class PedestrianGeometryUtils {
  // Project a geographic point to screen coordinates
  Offset projectPoint(double latitude, double longitude, LatLngBounds bounds, Size size) {
    // Calculate the position on the screen
    final percentX = (longitude - bounds.west) / (bounds.east - bounds.west);
    final percentY = (latitude - bounds.north) / (bounds.south - bounds.north);
    
    return Offset(
      size.width * percentX,
      size.height * percentY
    );
  }
  
  // Convert geographic points to screen coordinates
  List<Offset> convertToScreenPoints(List<LatLng> points, LatLngBounds bounds, Size size) {
    return points.map((point) => 
      projectPoint(point.latitude, point.longitude, bounds, size)).toList();
  }
  
  // Calculate distance between two points
  double distance(Offset a, Offset b) {
    return math.sqrt(math.pow(a.dx - b.dx, 2) + math.pow(a.dy - b.dy, 2));
  }
  
  // Normalize an offset (create a unit vector with the same direction)
  Offset normalizeOffset(Offset offset) {
    final double length = math.sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
    if (length == 0) return Offset.zero;
    return Offset(offset.dx / length, offset.dy / length);
  }
  
  // Calculate angle between two points
  double angleBetween(Offset a, Offset b) {
    return math.atan2(b.dy - a.dy, b.dx - a.dx);
  }
  
  // Simplify a path using Douglas-Peucker algorithm
  List<Offset> simplifyPath(List<Offset> points, double tolerance) {
    if (points.length <= 2) return List.from(points);
    
    // Find the point with the maximum distance
    double maxDistance = 0;
    int index = 0;
    
    for (int i = 1; i < points.length - 1; i++) {
      double distance = perpendicularDistance(
        points[i], points.first, points.last);
      
      if (distance > maxDistance) {
        maxDistance = distance;
        index = i;
      }
    }
    
    // If max distance is greater than tolerance, recursively simplify
    if (maxDistance > tolerance) {
      // Recursive call
      final List<Offset> firstPart = simplifyPath(
        points.sublist(0, index + 1), tolerance);
      final List<Offset> secondPart = simplifyPath(
        points.sublist(index), tolerance);
      
      // Concatenate the two parts (avoiding duplicating the connecting point)
      return [...firstPart.sublist(0, firstPart.length - 1), ...secondPart];
    } else {
      // Below tolerance, return just the endpoints
      return [points.first, points.last];
    }
  }
  
  // Calculate perpendicular distance from a point to a line
  double perpendicularDistance(Offset point, Offset lineStart, Offset lineEnd) {
    if (lineStart == lineEnd) {
      return distance(point, lineStart);
    }
    
    // Calculate the area of the triangle * 2
    final double area = ((lineEnd.dx - lineStart.dx) * (lineStart.dy - point.dy) -
                         (lineStart.dx - point.dx) * (lineEnd.dy - lineStart.dy)).abs();
    
    // Base of the triangle (distance between line endpoints)
    final double base = distance(lineStart, lineEnd);
    
    // Height of the triangle (perpendicular distance)
    return area / base;
  }
  
  // Apply a smoothing algorithm to a polygon
  List<Offset> smoothPolygon(List<Offset> points, {double tension = 0.5}) {
    if (points.length < 3) return points;
    
    final List<Offset> result = [];
    final int len = points.length;
    
    for (int i = 0; i < len; i++) {
      final Offset p0 = points[i];
      final Offset p1 = points[(i + 1) % len];
      final Offset p2 = points[(i + 2) % len];
      
      // Add the current point
      result.add(p0);
      
      // Add two control points between p0 and p1
      result.add(Offset(
        p0.dx + (p1.dx - p0.dx) * tension,
        p0.dy + (p1.dy - p0.dy) * tension
      ));
      
      result.add(Offset(
        p1.dx - (p2.dx - p0.dx) * tension / 2,
        p1.dy - (p2.dy - p0.dy) * tension / 2
      ));
    }
    
    return result;
  }
  
  // Calculate the bounds of a list of points
  Rect getBounds(List<Offset> points) {
    if (points.isEmpty) return Rect.zero;
    
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;
    
    for (final point in points) {
      minX = math.min(minX, point.dx);
      minY = math.min(minY, point.dy);
      maxX = math.max(maxX, point.dx);
      maxY = math.max(maxY, point.dy);
    }
    
    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }
  
  // Calculate the center of a list of points
  Offset getCenter(List<Offset> points) {
    if (points.isEmpty) return Offset.zero;
    
    final Rect bounds = getBounds(points);
    return bounds.center;
  }
  
  // Interpolate a point along a path at a specific distance
  Offset interpolatePointAtDistance(List<Offset> points, double targetDistance) {
    if (points.isEmpty) return Offset.zero;
    if (points.length == 1) return points[0];
    
    double accumulatedDistance = 0;
    
    for (int i = 0; i < points.length - 1; i++) {
      final currentSegmentLength = distance(points[i], points[i + 1]);
      
      if (accumulatedDistance + currentSegmentLength >= targetDistance) {
        // Calculate how far along this segment the target point is
        final double segmentProgressFraction = 
            (targetDistance - accumulatedDistance) / currentSegmentLength;
        
        // Interpolate between the two points
        return Offset(
          points[i].dx + (points[i + 1].dx - points[i].dx) * segmentProgressFraction,
          points[i].dy + (points[i + 1].dy - points[i].dy) * segmentProgressFraction
        );
      }
      
      accumulatedDistance += currentSegmentLength;
    }
    
    // If we get here, the target distance is beyond the path length
    return points.last;
  }
  
  // Calculate total path length
  double calculatePathLength(List<Offset> points) {
    double length = 0;
    for (int i = 0; i < points.length - 1; i++) {
      length += distance(points[i], points[i + 1]);
    }
    return length;
  }
} 