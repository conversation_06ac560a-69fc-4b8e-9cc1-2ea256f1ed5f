import 'package:flutter/material.dart';

/// Utility class for managing colors for pedestrian elements
/// Supports different themes and provides color mappings for various pedestrian elements
class PedestrianColors {
  // Default color palettes for different themes
  static const Map<String, Map<String, Map<String, Color>>> _themePalettes = {
    'light': {
      'pedestrian': {
        'primary': Color(0xFFE6E6E6),        // Light gray for pedestrian areas
        'outline': Color(0xFFCCCCCC),        // Outline for pedestrian areas
        'surface': Color(0xFFE6E6E6),        // Surface color for areas
        'accent': Color(0xFFAAAAAA),         // Accent color for pedestrian features
        'furniture': Color(0xFF888888),      // Color for street furniture
        'text': Color(0xFF555555),           // Text color
        'shadow': Color(0x40000000),         // Shadow color with opacity
      },
      'footway': {
        'primary': Color(0xFFE5E5E5),        // Main color for footways
        'outline': Color(0xFFCCCCCC),        // Outline color
        'surface': Color(0xFFEAEAEA),        // Surface color
        'accent': Color(0xFFAAAAAA),         // Accent lines or markers
        'pattern': Color(0xFFD5D5D5),        // Pattern color (dashed lines, etc.)
        'text': Color(0xFF555555),           // Text/label color
        'shadow': Color(0x40000000),         // Shadow color with opacity
      },
      'cycleway': {
        'primary': Color(0xFFE0F0E0),        // Light green for cycle paths
        'outline': Color(0xFFC0D0C0),        // Outline color
        'surface': Color(0xFFE5F5E5),        // Surface color
        'accent': Color(0xFF90C090),         // Accent lines or markers
        'pattern': Color(0xFFB0E0B0),        // Pattern color
        'text': Color(0xFF447744),           // Text/label color
        'shadow': Color(0x40000000),         // Shadow color with opacity
      },
      'track': {
        'primary': Color(0xFFE9D9C9),        // Brown-ish color for tracks/trails
        'outline': Color(0xFFD9C9B9),        // Outline color
        'surface': Color(0xFFEEDECE),        // Surface color
        'accent': Color(0xFFB19981),         // Accent lines or markers
        'pattern': Color(0xFFD9C9B9),        // Pattern color
        'text': Color(0xFF6D5F4B),           // Text/label color
        'shadow': Color(0x40000000),         // Shadow color with opacity
      },
      'crossing': {
        'primary': Color(0xFFF0F0F0),        // White for crossings
        'outline': Color(0xFFCCCCCC),        // Gray outline
        'surface': Color(0xFFFFFFFF),        // Surface (stripes)
        'accent': Color(0xFF999999),         // Accent color
        'signal': Color(0xFFFF8800),         // Signal color (orange)
        'text': Color(0xFF555555),           // Text color
        'shadow': Color(0x40000000),         // Shadow color with opacity
      },
      'steps': {
        'primary': Color(0xFFE0E0E0),        // Main color for steps
        'outline': Color(0xFFC0C0C0),        // Outline color
        'surface': Color(0xFFEBEBEB),        // Surface color
        'accent': Color(0xFFB0B0B0),         // Step lines 
        'handrail': Color(0xFF888888),       // Handrail color
        'text': Color(0xFF555555),           // Text color
        'shadow': Color(0x40000000),         // Shadow color with opacity
      },
      'path': {
        'primary': Color(0xFFE5E2D9),        // Main color for general paths
        'outline': Color(0xFFD5D2C9),        // Outline color
        'surface': Color(0xFFEAE7DE),        // Surface color
        'accent': Color(0xFFAA9977),         // Accent markers
        'pattern': Color(0xFFCAC7BE),        // Pattern color
        'text': Color(0xFF555544),           // Text color
        'shadow': Color(0x40000000),         // Shadow color with opacity
      },
    },
    'dark': {
      'pedestrian': {
        'primary': Color(0xFF373737),        // Dark gray for pedestrian areas
        'outline': Color(0xFF454545),        // Outline for pedestrian areas
        'surface': Color(0xFF373737),        // Surface color for areas
        'accent': Color(0xFF555555),         // Accent color for pedestrian features
        'furniture': Color(0xFF666666),      // Color for street furniture
        'text': Color(0xFFAAAAAA),           // Text color
        'shadow': Color(0x70000000),         // Shadow color with opacity
      },
      'footway': {
        'primary': Color(0xFF353535),        // Main color for footways
        'outline': Color(0xFF454545),        // Outline color
        'surface': Color(0xFF3A3A3A),        // Surface color
        'accent': Color(0xFF555555),         // Accent lines or markers
        'pattern': Color(0xFF454545),        // Pattern color (dashed lines, etc.)
        'text': Color(0xFFAAAAAA),           // Text/label color
        'shadow': Color(0x70000000),         // Shadow color with opacity
      },
      'cycleway': {
        'primary': Color(0xFF304530),        // Dark green for cycle paths
        'outline': Color(0xFF405540),        // Outline color
        'surface': Color(0xFF354A35),        // Surface color
        'accent': Color(0xFF506550),         // Accent lines or markers
        'pattern': Color(0xFF405040),        // Pattern color
        'text': Color(0xFF90B090),           // Text/label color
        'shadow': Color(0x70000000),         // Shadow color with opacity
      },
      'track': {
        'primary': Color(0xFF443A30),        // Dark brown for tracks/trails
        'outline': Color(0xFF544A40),        // Outline color
        'surface': Color(0xFF493F35),        // Surface color
        'accent': Color(0xFF5A4B3C),         // Accent lines or markers
        'pattern': Color(0xFF544A40),        // Pattern color
        'text': Color(0xFFB09980),           // Text/label color
        'shadow': Color(0x70000000),         // Shadow color with opacity
      },
      'crossing': {
        'primary': Color(0xFF383838),        // Dark gray for crossings
        'outline': Color(0xFF484848),        // Outline
        'surface': Color(0xFF505050),        // Surface (stripes)
        'accent': Color(0xFF606060),         // Accent color
        'signal': Color(0xFFFF8800),         // Signal color (orange)
        'text': Color(0xFFAAAAAA),           // Text color
        'shadow': Color(0x70000000),         // Shadow color with opacity
      },
      'steps': {
        'primary': Color(0xFF333333),        // Main color for steps
        'outline': Color(0xFF444444),        // Outline color
        'surface': Color(0xFF393939),        // Surface color
        'accent': Color(0xFF505050),         // Step lines
        'handrail': Color(0xFF777777),       // Handrail color
        'text': Color(0xFFAAAAAA),           // Text color
        'shadow': Color(0x70000000),         // Shadow color with opacity
      },
      'path': {
        'primary': Color(0xFF3A3832),        // Main color for general paths
        'outline': Color(0xFF4A4842),        // Outline color
        'surface': Color(0xFF403C36),        // Surface color
        'accent': Color(0xFF5A5348),         // Accent markers
        'pattern': Color(0xFF4A4640),        // Pattern color
        'text': Color(0xFFBBBBAA),           // Text color
        'shadow': Color(0x70000000),         // Shadow color with opacity
      },
    },
    'satellite': {
      'pedestrian': {
        'primary': Color(0x90FFFFFF),        // Translucent white for areas
        'outline': Color(0xA0FFFFFF),        // White outline
        'surface': Color(0x90FFFFFF),        // Translucent surface
        'accent': Color(0xA0FFFFFF),         // Accent markers
        'furniture': Color(0xB0FFFFFF),      // Street furniture
        'text': Color(0xFFFFFFFF),           // Text color
        'shadow': Color(0x80000000),         // Shadow color with opacity
      },
      'footway': {
        'primary': Color(0x90FFFFFF),        // Translucent white for footways
        'outline': Color(0xA0FFFFFF),        // White outline
        'surface': Color(0x90FFFFFF),        // Translucent surface
        'accent': Color(0xB0FFFFFF),         // Accent markers
        'pattern': Color(0xA0FFFFFF),        // Pattern color
        'text': Color(0xFFFFFFFF),           // Text color
        'shadow': Color(0x80000000),         // Shadow color with opacity
      },
      'cycleway': {
        'primary': Color(0x9000FF00),        // Translucent green for cycle paths
        'outline': Color(0xA000FF00),        // Green outline
        'surface': Color(0x9000FF00),        // Translucent surface
        'accent': Color(0xB0FFFFFF),         // White accent markers
        'pattern': Color(0xA000FF00),        // Pattern color
        'text': Color(0xFFFFFFFF),           // Text color
        'shadow': Color(0x80000000),         // Shadow color with opacity
      },
      'track': {
        'primary': Color(0x90D2B48C),        // Translucent tan for tracks
        'outline': Color(0xA0D2B48C),        // Tan outline
        'surface': Color(0x90D2B48C),        // Translucent surface
        'accent': Color(0xB0FFFFFF),         // White accent markers
        'pattern': Color(0xA0D2B48C),        // Pattern color
        'text': Color(0xFFFFFFFF),           // Text color
        'shadow': Color(0x80000000),         // Shadow color with opacity
      },
      'crossing': {
        'primary': Color(0x90FFFFFF),        // Translucent white for crossings
        'outline': Color(0xA0FFFFFF),        // White outline
        'surface': Color(0xB0FFFFFF),        // Stripes (more opaque)
        'accent': Color(0xD0FFFFFF),         // Accent (more opaque)
        'signal': Color(0xFFFFAA00),         // Signal color (bright orange)
        'text': Color(0xFFFFFFFF),           // Text color
        'shadow': Color(0x80000000),         // Shadow color with opacity
      },
      'steps': {
        'primary': Color(0x90FFFFFF),        // Translucent white for steps
        'outline': Color(0xA0FFFFFF),        // White outline
        'surface': Color(0x90FFFFFF),        // Translucent surface
        'accent': Color(0xC0FFFFFF),         // Step lines (more opaque)
        'handrail': Color(0xD0FFFFFF),       // Handrail color (more opaque)
        'text': Color(0xFFFFFFFF),           // Text color
        'shadow': Color(0x80000000),         // Shadow color with opacity
      },
      'path': {
        'primary': Color(0x90FFFFCC),        // Translucent cream for paths
        'outline': Color(0xA0FFFFCC),        // Cream outline
        'surface': Color(0x90FFFFCC),        // Translucent surface
        'accent': Color(0xB0FFFFFF),         // White accent markers
        'pattern': Color(0xA0FFFFCC),        // Pattern color
        'text': Color(0xFFFFFFFF),           // Text color
        'shadow': Color(0x80000000),         // Shadow color with opacity
      },
    },
    // Add more themes as needed
  };

  /// Get color palette for the specified theme
  /// Defaults to light theme if the specified theme is not found
  Map<String, Map<String, Color>> getColorPalette(String theme) {
    return _themePalettes[theme] ?? _themePalettes['light']!;
  }

  /// Get a specific color based on feature type, theme, and color key
  /// Returns a fallback color if the specific color is not found
  Color getColor(String theme, String featureType, String colorKey) {
    // Get theme palette (fallback to light if not found)
    final themePalette = _themePalettes[theme] ?? _themePalettes['light']!;
    
    // Get feature colors (fallback to pedestrian if not found)
    final featureColors = themePalette[featureType] ?? themePalette['pedestrian']!;
    
    // Get specific color (fallback to primary if not found)
    return featureColors[colorKey] ?? featureColors['primary']!;
  }

  /// Adjust color opacity
  Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  /// Darken color by a percentage (0-100)
  Color darken(Color color, double percent) {
    assert(percent >= 0 && percent <= 100);
    final factor = 1 - (percent / 100);
    return Color.fromARGB(
      color.alpha,
      (color.red * factor).round().clamp(0, 255),
      (color.green * factor).round().clamp(0, 255),
      (color.blue * factor).round().clamp(0, 255),
    );
  }

  /// Lighten color by a percentage (0-100)
  Color lighten(Color color, double percent) {
    assert(percent >= 0 && percent <= 100);
    final factor = percent / 100;
    return Color.fromARGB(
      color.alpha,
      (color.red + ((255 - color.red) * factor)).round().clamp(0, 255),
      (color.green + ((255 - color.green) * factor)).round().clamp(0, 255),
      (color.blue + ((255 - color.blue) * factor)).round().clamp(0, 255),
    );
  }

  /// Get a color for a specific surface type
  Color getSurfaceColor(String theme, String featureType, String surface) {
    final baseColor = getColor(theme, featureType, 'surface');
    
    // Adjust based on surface type
    switch (surface.toLowerCase()) {
      case 'asphalt':
        return darken(baseColor, 10);
      case 'concrete':
        return lighten(baseColor, 5);
      case 'paved':
        return baseColor;
      case 'unpaved':
      case 'gravel':
        return darken(baseColor, 5);
      case 'ground':
      case 'dirt':
        return darken(baseColor, 15).withOpacity(0.9);
      case 'grass':
        return Color.lerp(baseColor, Color(0xFF90EE90), 0.3)!;
      case 'wood':
      case 'boardwalk':
        return Color.lerp(baseColor, Color(0xFF8B4513), 0.3)!;
      default:
        return baseColor;
    }
  }
} 