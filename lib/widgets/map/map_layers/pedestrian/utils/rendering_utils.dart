import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2

/// Utility class providing helper methods for map rendering calculations
class RenderingUtils {
  /// Get a line width scaled based on the current zoom level
  /// 
  /// [zoomLevel] - The current map zoom level
  /// [minWidth] - The minimum width at low zoom levels
  /// [maxWidth] - The maximum width at high zoom levels
  static double getScaledLineWidth(double zoomLevel, double minWidth, double maxWidth) {
    if (zoomLevel <= 14) return minWidth;
    if (zoomLevel >= 19) return maxWidth;
    
    // Linear interpolation between min and max based on zoom
    return minWidth + (maxWidth - minWidth) * (zoomLevel - 14) / 5;
  }
  
  /// Get a scale factor for sizing elements based on zoom level
  /// 
  /// [zoomLevel] - The current map zoom level
  /// [baseZoom] - The zoom level at which the scale factor is 1.0 (defaults to 16)
  static double getScaleFactor(double zoomLevel, {double baseZoom = 16.0}) {
    if (zoomLevel <= baseZoom - 2) return 0.5;
    if (zoomLevel >= baseZoom + 2) return 2.0;
    
    // Exponential scaling for better visual perception
    return math.pow(2, (zoomLevel - baseZoom) / 2).toDouble();
  }
  
  /// Calculate the angle between two points in radians
  static double calculateAngle(Offset p1, Offset p2) {
    return math.atan2(p2.dy - p1.dy, p2.dx - p1.dx);
  }
  
  /// Calculate the distance between two points
  static double calculateDistance(Offset p1, Offset p2) {
    final double dx = p2.dx - p1.dx;
    final double dy = p2.dy - p1.dy;
    return math.sqrt(dx * dx + dy * dy);
  }
  
  /// Create a smooth path through a collection of points using Catmull-Rom splines
  /// 
  /// [points] - Collection of points to create a smooth path through
  /// [tension] - Controls how tight the curve is (0.0-1.0, default 0.5)
  /// [closed] - Whether the path should be closed (connect last point to first)
  static Path createSmoothPath(
    List<Offset> points, {
    double tension = 0.5,
    bool closed = false
  }) {
    if (points.length < 2) {
      // Need at least 2 points to create a path
      final path = Path();
      if (points.isNotEmpty) {
        path.moveTo(points[0].dx, points[0].dy);
      }
      return path;
    }
    
    // Create a path with smooth curves
    final path = Path();
    
    // For closed paths, we need to consider points before the first and after the last
    List<Offset> allPoints = List.from(points);
    if (closed) {
      // Add last point at beginning and first point at end
      allPoints.insert(0, points.last);
      allPoints.add(points[0]);
      allPoints.add(points[1]);
    } else {
      // For open paths, duplicate first and last points
      allPoints.insert(0, points[0]);
      allPoints.add(points.last);
    }
    
    path.moveTo(allPoints[1].dx, allPoints[1].dy);
    
    // Create Catmull-Rom spline segments
    for (int i = 1; i < allPoints.length - 2; i++) {
      final p0 = allPoints[i - 1];
      final p1 = allPoints[i];
      final p2 = allPoints[i + 1];
      final p3 = allPoints[i + 2];
      
      // Calculate control points for cubic curve
      final double t = tension;
      
      // First control point
      final cp1x = p1.dx + (p2.dx - p0.dx) / 6 * t;
      final cp1y = p1.dy + (p2.dy - p0.dy) / 6 * t;
      
      // Second control point
      final cp2x = p2.dx - (p3.dx - p1.dx) / 6 * t;
      final cp2y = p2.dy - (p3.dy - p1.dy) / 6 * t;
      
      path.cubicTo(cp1x, cp1y, cp2x, cp2y, p2.dx, p2.dy);
    }
    
    if (closed) {
      path.close();
    }
    
    return path;
  }
  
  /// Create a dashed pattern along a path
  /// 
  /// [path] - The Path to apply dashes to
  /// [dashLength] - Length of each dash
  /// [gapLength] - Length of gap between dashes
  /// [phase] - Starting phase for the pattern (offset)
  static Path createDashedPath(
    Path path, {
    required double dashLength,
    required double gapLength,
    double phase = 0.0
  }) {
    final Path dashedPath = Path();
    final PathMetrics pathMetrics = path.computeMetrics();
    
    for (final PathMetric metric in pathMetrics) {
      double distance = 0.0;
      bool draw = true;
      
      // Apply the phase offset
      if (phase > 0) {
        final patternLength = dashLength + gapLength;
        distance = phase % patternLength;
        draw = distance < dashLength;
        distance = math.min(distance, patternLength);
      }
      
      // Create the dashed pattern
      while (distance < metric.length) {
        final start = distance;
        final end = math.min(start + (draw ? dashLength : gapLength), metric.length);
        
        if (draw) {
          final pathSegment = metric.extractPath(start, end);
          dashedPath.addPath(pathSegment, Offset.zero);
        }
        
        distance = end;
        draw = !draw;
      }
    }
    
    return dashedPath;
  }
  
  /// Creates a path with a drop shadow effect
  /// 
  /// [canvas] - The canvas to draw on
  /// [path] - The path to draw shadow for
  /// [shadowColor] - Color of the shadow
  /// [elevation] - How far the shadow is offset (height effect)
  /// [direction] - Direction of the shadow (in radians, 0 = right)
  static void drawPathWithShadow(
    Canvas canvas,
    Path path,
    Color shadowColor,
    double elevation, {
    double direction = math.pi / 4, // Default 45 degrees
    double blur = 4.0
  }) {
    final Paint shadowPaint = Paint()
      ..color = shadowColor
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, blur);
    
    // Calculate shadow offset based on direction and elevation
    final double offsetX = math.cos(direction) * elevation;
    final double offsetY = math.sin(direction) * elevation;
    
    final shadowPath = Path();
    shadowPath.addPath(path, Offset(offsetX, offsetY));
    
    // Draw shadow first, then the main path will be drawn on top
    canvas.drawPath(shadowPath, shadowPaint);
  }
  
  /// Create a radial gradient for light effects
  /// 
  /// [center] - Center point of the light
  /// [radius] - Radius of the effect
  /// [color] - Base color of the light
  static Shader createLightGradient(Offset center, double radius, Color color) {
    return RadialGradient(
      colors: [
        color,
        color.withOpacity(0.0),
      ],
      stops: const [0.0, 1.0],
    ).createShader(
      Rect.fromCircle(
        center: center,
        radius: radius,
      ),
    );
  }
} 