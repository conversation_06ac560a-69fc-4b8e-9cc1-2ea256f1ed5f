import 'package:flutter/material.dart';

/// A class to manage color themes and styling for pedestrian footways
class FootwayTheme {
  /// The primary mode of the theme (light or dark)
  final bool isDarkMode;

  /// Custom accent color (optional)
  final Color? accentColor;

  /// Creates a new footway theme with specified mode
  /// 
  /// [isDarkMode] - Whether to use dark mode colors
  /// [accentColor] - Optional accent color to use for highlights
  FootwayTheme({
    this.isDarkMode = false,
    this.accentColor,
  });

  /// Get the base color for a footway based on surface type
  Color getSurfaceColor(String? surface) {
    switch (surface?.toLowerCase()) {
      case 'asphalt':
        return isDarkMode ? const Color(0xFF303030) : const Color(0xFF505050);
      case 'concrete':
        return isDarkMode ? const Color(0xFF3A3A3A) : const Color(0xFFCCCCCC);
      case 'paving_stones':
        return isDarkMode ? const Color(0xFF444444) : const Color(0xFFBBBBBB);
      case 'cobblestone':
        return isDarkMode ? const Color(0xFF3F3F3F) : const Color(0xFF999999);
      case 'gravel':
        return isDarkMode ? const Color(0xFF353535) : const Color(0xFFAAAAAA);
      case 'dirt':
      case 'earth':
      case 'ground':
        return isDarkMode ? const Color(0xFF332211) : const Color(0xFF8B7355);
      case 'grass':
        return isDarkMode ? const Color(0xFF223322) : const Color(0xFF88AA88);
      case 'sand':
        return isDarkMode ? const Color(0xFF443322) : const Color(0xFFD2B48C);
      case 'wood':
        return isDarkMode ? const Color(0xFF442211) : const Color(0xFF8B5A2B);
      default:
        // Default to a neutral gray for unknown surfaces
        return isDarkMode ? const Color(0xFF333333) : const Color(0xFFAAAAAA);
    }
  }

  /// Get a color for tactile paving
  Color getTactilePavingColor() {
    // Yellow is the standard color for tactile paving in most countries
    return isDarkMode ? const Color(0xFFCC9900) : const Color(0xFFFFCC00);
  }

  /// Get a color for lit areas
  Color getLightingColor() {
    // Warm yellow glow for lighting
    return isDarkMode ? const Color(0xFFFFDD99) : const Color(0xFFFFEEBB);
  }

  /// Get a color for footway borders/outlines
  Color getBorderColor() {
    return isDarkMode ? const Color(0xFF555555) : const Color(0xFF777777);
  }

  /// Get a highlight color for selected elements
  Color getHighlightColor() {
    // Use accent color if provided, otherwise use a default blue
    return accentColor ?? (isDarkMode ? Colors.blue[300]! : Colors.blue[600]!);
  }

  /// Get a color for footways with steps
  Color getStepsColor() {
    return isDarkMode ? const Color(0xFF444455) : const Color(0xFF8888AA);
  }

  /// Get a color for handrails
  Color getHandrailColor() {
    return isDarkMode ? const Color(0xFF888888) : const Color(0xFF666666);
  }
  
  /// Get a color for wheelchair accessible elements
  Color getAccessibleColor() {
    // Standard accessibility blue
    return isDarkMode ? const Color(0xFF0066BB) : const Color(0xFF0088CC);
  }
  
  /// Get a set of colors for a heat map effect (based on traffic/popularity)
  List<Color> getHeatMapColors() {
    if (isDarkMode) {
      return [
        const Color(0xFF001133), // Low traffic (dark blue)
        const Color(0xFF003366), 
        const Color(0xFF004C99), 
        const Color(0xFF006633), // Medium traffic (green)
        const Color(0xFF998800), 
        const Color(0xFFAA5500), 
        const Color(0xFFBB2200), // High traffic (red)
      ];
    } else {
      return [
        const Color(0xFF99CCFF), // Low traffic (light blue)
        const Color(0xFF66AAFF), 
        const Color(0xFF3388FF), 
        const Color(0xFF00CC66), // Medium traffic (green)
        const Color(0xFFFFCC00), 
        const Color(0xFFFF8800), 
        const Color(0xFFFF3300), // High traffic (red)
      ];
    }
  }
  
  /// Get a color for incline indicators
  Color getInclineColor(double incline) {
    // The steeper the incline, the more intense the color
    final double absIncline = incline.abs();
    
    if (isDarkMode) {
      if (absIncline < 3) return const Color(0xFF339933); // Gentle
      if (absIncline < 6) return const Color(0xFF999933); // Moderate
      if (absIncline < 10) return const Color(0xFF993333); // Steep
      return const Color(0xFF992222); // Very steep
    } else {
      if (absIncline < 3) return const Color(0xFF33CC33); // Gentle
      if (absIncline < 6) return const Color(0xFFCCCC33); // Moderate
      if (absIncline < 10) return const Color(0xFFCC6633); // Steep
      return const Color(0xFFCC3333); // Very steep
    }
  }
  
  /// Get shadow color for elements
  Color getShadowColor() {
    return isDarkMode ? Colors.black.withOpacity(0.6) : Colors.black.withOpacity(0.3);
  }
  
  /// Get a theme variant with the opposite mode
  FootwayTheme toggleDarkMode() {
    return FootwayTheme(
      isDarkMode: !isDarkMode,
      accentColor: accentColor,
    );
  }
  
  /// Create a custom variant of this theme with a different accent color
  FootwayTheme withAccentColor(Color newAccent) {
    return FootwayTheme(
      isDarkMode: isDarkMode,
      accentColor: newAccent,
    );
  }
} 