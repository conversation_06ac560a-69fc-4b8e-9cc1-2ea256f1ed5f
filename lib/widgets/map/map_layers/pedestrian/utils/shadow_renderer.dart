import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2

/// Utility class for rendering shadows on pedestrian paths and areas
/// Provides consistent 2.5D effects across different pedestrian elements
class ShadowRenderer {
  final double zoomLevel;
  final double tiltFactor;
  
  ShadowRenderer({
    required this.zoomLevel,
    required this.tiltFactor,
  });
  
  // Draw shadow for a path
  void drawPathShadow(Canvas canvas, Path path, Color shadowColor, double elevation) {
    if (tiltFactor < 0.1) return; // Skip shadow if tilt is minimal
    
    // Adjust shadow properties based on zoom level and tilt
    final double shadowOpacity = tiltFactor * 0.7; // More tilt = more visible shadow
    final double shadowOffset = elevation * tiltFactor;
    final double shadowBlur = elevation * 2.0;
    
    // Create shadow paint with blur
    final Paint shadowPaint = Paint()
      ..color = shadowColor.withOpacity(shadowOpacity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, shadowBlur);
    
    // Create the offset shadow path
    final Path shadowPath = Path.from(path);
    
    // Draw shadow
    canvas.drawPath(
      shadowPath.shift(Offset(shadowOffset, shadowOffset)), 
      shadowPaint
    );
  }
  
  // Draw shadow for an area (polygon)
  void drawAreaShadow(Canvas canvas, List<Offset> points, Color shadowColor, double elevation) {
    if (tiltFactor < 0.1) return; // Skip shadow if tilt is minimal
    
    // Adjust shadow properties based on zoom level and tilt
    final double shadowOpacity = tiltFactor * 0.5; // More tilt = more visible shadow
    final double shadowOffset = elevation * tiltFactor;
    final double shadowBlur = elevation * 3.0;
    
    // Create the shadow path
    final Path shadowPath = Path();
    
    if (points.isEmpty) return;
    
    shadowPath.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      shadowPath.lineTo(points[i].dx, points[i].dy);
    }
    shadowPath.close();
    
    // Create shadow paint with blur
    final Paint shadowPaint = Paint()
      ..color = shadowColor.withOpacity(shadowOpacity)
      ..style = PaintingStyle.fill
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, shadowBlur);
    
    // Draw shadow
    canvas.drawPath(
      shadowPath.shift(Offset(shadowOffset, shadowOffset)), 
      shadowPaint
    );
  }
  
  // Draw shadow for a point feature like a marker
  void drawPointShadow(Canvas canvas, Offset center, double radius, Color shadowColor, double elevation) {
    if (tiltFactor < 0.1) return; // Skip shadow if tilt is minimal
    
    // Adjust shadow properties based on zoom level and tilt
    final double shadowOpacity = tiltFactor * 0.6;
    final double shadowOffset = elevation * tiltFactor;
    final double shadowBlur = elevation * 2.0;
    
    // Create shadow paint with blur
    final Paint shadowPaint = Paint()
      ..color = shadowColor.withOpacity(shadowOpacity)
      ..style = PaintingStyle.fill
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, shadowBlur);
    
    // Draw shadow
    canvas.drawCircle(
      center.translate(shadowOffset, shadowOffset), 
      radius, 
      shadowPaint
    );
  }
} 