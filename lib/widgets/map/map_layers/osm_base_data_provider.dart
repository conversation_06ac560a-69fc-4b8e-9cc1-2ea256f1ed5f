import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../map_caching/map_bounds_manager.dart';
import '../map_caching/map_cache_coordinator.dart';
import '../map_caching/osm_throttle_manager.dart';

/// Base class for all OSM data providers that implements bounded fetching
abstract class OSMBaseDataProvider {
  // Bounds management
  late final MapBoundsManager _boundsManager;
  
  // Common dependencies
  final OSMThrottleManager _throttleManager = OSMThrottleManager();
  
  // State tracking
  bool _isLoading = false;
  bool _needsRefresh = true;
  bool _hasError = false;
  bool _didInitialFetch = false;
  
  // Current view parameters
  late LatLngBounds _visibleBounds;
  late double _zoomLevel;
  bool _isMapMoving = false;
  
  // Callback for error handling
  final Function(String)? onError;
  
  OSMBaseDataProvider({
    required LatLngBounds initialBounds,
    required double initialZoom,
    this.onError,
  }) {
    _boundsManager = MapBoundsManager(initialBounds);
    _visibleBounds = initialBounds;
    _zoomLevel = initialZoom;
  }
  
  /// Update user location to influence bounds calculations
  void updateUserLocation(LatLng? location) {
    _boundsManager.updateUserLocation(location);
  }
  
  /// Update the current view parameters and check if we need to fetch new data
  /// This method is final to ensure consistent bounds checking across all providers
  final bool shouldUpdateData({
    required LatLngBounds newBounds,
    required double newZoom,
    required bool isMoving,
  }) {
    // Update current state
    _visibleBounds = newBounds;
    _zoomLevel = newZoom;
    _isMapMoving = isMoving;
    
    // Skip if we're still loading
    if (_isLoading) {
      return false;
    }
    
    // Get the center point of the new bounds
    final center = LatLng(
      (newBounds.north + newBounds.south) / 2,
      (newBounds.east + newBounds.west) / 2
    );
    
    // If we're outside both the initial bounds and expanded bounds, don't fetch
    if (!_boundsManager.isWithinInitialBounds(center) && 
        !_boundsManager.shouldFetchForBounds(newBounds)) {
      debugPrint('Skipping fetch: Outside allowed bounds');
      return false;
    }
    
    // If map is moving, only allow fetches within initial bounds
    if (isMoving) {
      final shouldFetch = _boundsManager.isWithinInitialBounds(center);
      if (!shouldFetch) {
        debugPrint('Skipping fetch while moving: Outside initial bounds');
      }
      return shouldFetch;
    }
    
    // For non-moving state, check with bounds manager
    final shouldFetch = _boundsManager.shouldFetchForBounds(newBounds);
    if (!shouldFetch) {
      debugPrint('Skipping fetch: Bounds manager rejected request');
    }
    return shouldFetch;
  }
  
  /// Get the bounds to use for fetching data
  final LatLngBounds getBoundsForFetching() {
    final fetchBounds = _boundsManager.getBoundsForFetching(_visibleBounds);
    debugPrint('Fetching data for bounds: ${fetchBounds.toString()}');
    return fetchBounds;
  }
  
  /// Check if we're within the initial bounds
  final bool isWithinInitialBounds() {
    return _boundsManager.isWithinInitialBounds(LatLng(
      (_visibleBounds.north + _visibleBounds.south) / 2,
      (_visibleBounds.east + _visibleBounds.west) / 2
    ));
  }
  
  // Getters for state
  bool get isLoading => _isLoading;
  bool get needsRefresh => _needsRefresh;
  bool get hasError => _hasError;
  bool get didInitialFetch => _didInitialFetch;
  LatLngBounds get visibleBounds => _visibleBounds;
  double get zoomLevel => _zoomLevel;
  bool get isMapMoving => _isMapMoving;
  MapBoundsManager get boundsManager => _boundsManager;
  
  // Protected setters for subclasses
  @protected
  set isLoading(bool value) => _isLoading = value;
  
  @protected
  set needsRefresh(bool value) => _needsRefresh = value;
  
  @protected
  set hasError(bool value) => _hasError = value;
  
  @protected
  set didInitialFetch(bool value) => _didInitialFetch = value;
  
  /// Reset error state
  void resetErrorState() {
    _hasError = false;
  }
  
  /// Get the throttle manager for rate limiting
  @protected
  OSMThrottleManager get throttleManager => _throttleManager;
} 