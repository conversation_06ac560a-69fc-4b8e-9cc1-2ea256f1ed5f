import 'dart:async';
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';  // Import for MapCamera
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2 to prevent conflict

/// A custom map layer that renders trees and other vegetation
/// with advanced 2.5D effects that respond to tilt changes.
class TreesLayer extends StatefulWidget {
  /// The detail level for rendering (1-3 where 3 is highest)
  final int detailLevel;
  
  /// Tilt factor used for simulating 3D perspective (0-1)
  final double tiltFactor;
  
  /// The current season, affecting colors and appearance
  final String season;
  
  /// MapCamera for proper coordinate projection during map movement
  final MapCamera? mapCamera;
  
  const TreesLayer({
    Key? key,
    this.detailLevel = 2,
    this.tiltFactor = 0.0,
    this.season = 'default',
    this.mapCamera,
  }) : super(key: key);

  @override
  State<TreesLayer> createState() => _TreesLayerState();
}

class _TreesLayerState extends State<TreesLayer> with SingleTickerProviderStateMixin {
  /// Animation controller for tree swaying effect
  late AnimationController _swayController;
  
  /// Animation for the swaying effect
  late Animation<double> _swayAnimation;
  
  /// Maps each season to its appropriate colors
  /// Contains foliage and trunk colors for each season
  final Map<String, Map<String, Color>> _seasonalColors = {
    'default': {
      'foliage': const Color(0xFF2E7D32), // Medium green
      'trunk': const Color(0xFF5D4037),   // Medium brown
      'highlight': const Color(0xFF81C784), // Light green highlight
    },
    'spring': {
      'foliage': const Color(0xFF66BB6A), // Brighter green
      'trunk': const Color(0xFF6D4C41),   // Lighter brown
      'highlight': const Color(0xFFA5D6A7), // Pale green highlight
    },
    'autumn': {
      'foliage': const Color(0xFFE57373), // Reddish orange
      'trunk': const Color(0xFF5D4037),   // Medium brown
      'highlight': const Color(0xFFFFCCBC), // Pale orange highlight
    },
    'winter': {
      'foliage': const Color(0xFFB0BEC5), // Bluish gray
      'trunk': const Color(0xFF4E342E),   // Dark brown
      'highlight': const Color(0xFFCFD8DC), // Light gray highlight
    }
  };
  
  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller for swaying effect
    _swayController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    );
    
    // Create a gentle sine wave animation for swaying
    _swayAnimation = Tween<double>(
      begin: -0.5,
      end: 0.5,
    ).animate(
      CurvedAnimation(
        parent: _swayController,
        curve: Curves.easeInOut,
      ),
    );
    
    // Start the animation with repeat
    _swayController.repeat(reverse: true);
  }
  
  @override
  void dispose() {
    _swayController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // Get colors based on current season
    final String seasonKey = widget.season.isNotEmpty ? 
        ((_seasonalColors.containsKey(widget.season) ? widget.season : 'default')) : 
        'default';
        
    final Map<String, Color> colors = _seasonalColors[seasonKey]!;
    
    return AnimatedBuilder(
      animation: _swayAnimation,
      builder: (context, child) {
        // Calculate sway factor - only apply when tilted
        final double swayFactor = widget.tiltFactor > 0.1 ? 
            _swayAnimation.value * widget.tiltFactor : 0.0;
            
        return CustomPaint(
          size: Size.infinite,
            painter: TreesPainter(
            foliageColor: colors['foliage']!,
            trunkColor: colors['trunk']!,
            highlightColor: colors['highlight']!,
              detailLevel: widget.detailLevel,
              tiltFactor: widget.tiltFactor,
            swayFactor: swayFactor,
            season: seasonKey,
            mapCamera: widget.mapCamera,
          ),
        );
      },
    );
  }
}

/// Painter that renders trees and vegetation with enhanced 2.5D effects
class TreesPainter extends CustomPainter {
  final Color foliageColor;
  final Color trunkColor;
  final Color highlightColor;
  final int detailLevel;
  final double tiltFactor;
  final double swayFactor;
  final String season;
  final math.Random _random = math.Random(123); // Different seed than other layers
  final MapCamera? mapCamera;
  
  // Tree types with more variety
  final List<String> _treeTypes = ['pine', 'oak', 'maple', 'bush', 'palm', 'birch', 'cypress'];
  
  TreesPainter({
    required this.foliageColor,
    required this.trunkColor,
    required this.highlightColor,
    required this.detailLevel,
    required this.tiltFactor,
    required this.swayFactor,
    this.season = 'default',
    this.mapCamera,
  });
  
  /// Project a geographic point to screen coordinates
  Offset _projectPoint(LatLng position) {
    if (mapCamera != null) {
      final screenPoint = mapCamera!.latLngToScreenPoint(position);
      if (screenPoint != null) {
        return Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
      }
    }
    
    // Fallback if mapCamera is not available
    return Offset.zero;
  }
  
  /// Check if a point is within the visible bounds
  bool _isInVisibleBounds(LatLng position) {
    if (mapCamera != null) {
      final bounds = mapCamera!.visibleBounds;
      return bounds.contains(position);
    }
    return true; // Assume visible if no mapCamera
  }
  
  @override
  void paint(Canvas canvas, Size size) {
    // If mapCamera is available, we could use it for more accurate positioning
    // but for simplicity and backward compatibility, we'll keep the existing tree generation logic
    
    // Number of trees to render depends on detail level
    final int treeCount;
    if (detailLevel == 1) {
      treeCount = 20;   // Low detail
    } else if (detailLevel == 2) {
      treeCount = 35;   // Medium detail
    } else {
      treeCount = 55;   // High detail
    }
    
    // Create clusters of trees rather than random placement
    final clusters = 6 + detailLevel * 2;
    final List<Offset> clusterCenters = [];
    
    // Generate cluster centers along the bottom and sides of the screen
    for (int i = 0; i < clusters / 2; i++) {
      final clusterX = (i / (clusters / 2)) * size.width;
      final clusterY = size.height * (0.6 + 0.4 * _random.nextDouble());
      clusterCenters.add(Offset(clusterX, clusterY));
    }
    
    // Add random cluster centers
    for (int i = 0; i < clusters / 2; i++) {
      final clusterX = _random.nextDouble() * size.width;
      final clusterY = size.height * (0.5 + 0.5 * _random.nextDouble());
      clusterCenters.add(Offset(clusterX, clusterY));
    }
    
    // Sort clusters by depth for proper drawing order
    clusterCenters.sort((a, b) => b.dy.compareTo(a.dy));
    
    // Draw ground shadows first
    _drawGroundShadows(canvas, size, clusterCenters, treeCount);
    
    // Draw trees in each cluster
    final treesPerCluster = treeCount ~/ clusterCenters.length;
    for (final clusterCenter in clusterCenters) {
      // Create a distribution of tree types for this cluster
      final Map<String, double> treeTypeProbabilities = _getClusterTreeTypeProbabilities(clusterCenter, size);
      
      for (int i = 0; i < treesPerCluster; i++) {
        // Tree position relative to cluster center
        final distance = 20.0 + 100.0 * _random.nextDouble();
        final angle = _random.nextDouble() * 2 * math.pi;
        final x = clusterCenter.dx + math.cos(angle) * distance;
        final y = clusterCenter.dy + math.sin(angle) * distance * 0.5; // Elliptical distribution
        
        // Skip trees that would appear off-screen
        if (x < -50 || x > size.width + 50 || y < -50 || y > size.height + 50) {
          continue;
        }
        
        // Tree size - larger in foreground, smaller in background
        final foregroundFactor = y / size.height;
        final treeSize = 20 + 50 * foregroundFactor * (_random.nextDouble() * 0.4 + 0.8);
        
        // Select tree type based on weighted probabilities for the cluster
        final treeType = _selectWeightedTreeType(treeTypeProbabilities);
        
        // Draw tree with sway animation
        _drawEnhancedTree(
          canvas, 
          x, 
          y, 
          treeSize, 
          treeType, 
          foregroundFactor,
        );
      }
    }
    
    // Draw additional ground vegetation for higher detail levels
    if (detailLevel >= 3) {
      _drawGroundVegetation(canvas, size);
    }
  }
  
  // Get tree type probabilities based on location in the scene
  Map<String, double> _getClusterTreeTypeProbabilities(Offset position, Size size) {
    // Edge distance factor (0 = edge, 1 = center)
    final edgeFactor = math.min(
      math.min(position.dx, size.width - position.dx) / (size.width / 2),
      math.min(position.dy, size.height - position.dy) / (size.height / 2)
    );
    
    // Bottom of screen factor (0 = top, 1 = bottom)
    final bottomFactor = position.dy / size.height;
    
    // Left-right factor (-1 = left, 1 = right)
    final horizontalFactor = (position.dx / size.width) * 2 - 1;
    
    // Probabilities will sum to 1.0
    final Map<String, double> probabilities = {
      'pine': 0.1,
      'oak': 0.1,
      'maple': 0.1,
      'bush': 0.1,
      'palm': 0.1,
      'birch': 0.1,
      'cypress': 0.1,
    };
    
    // Edge areas get more pines and bushes
    if (edgeFactor < 0.3) {
      probabilities['pine'] = 0.4;
      probabilities['bush'] = 0.3;
      probabilities['cypress'] = 0.2;
      probabilities['oak'] = 0.05;
      probabilities['maple'] = 0.05;
      probabilities['palm'] = 0.0;
      probabilities['birch'] = 0.0;
    } 
    // Near bottom of screen - more oaks, maples, palms
    else if (bottomFactor > 0.8) {
      // Left side gets more oaks/maples
      if (horizontalFactor < 0) {
        probabilities['oak'] = 0.35;
        probabilities['maple'] = 0.3;
        probabilities['birch'] = 0.15;
        probabilities['palm'] = 0.1;
        probabilities['bush'] = 0.1;
        probabilities['pine'] = 0.0;
        probabilities['cypress'] = 0.0;
      } 
      // Right side gets more palms in warm seasons
      else {
        if (season == 'spring' || season == 'default') {
          probabilities['palm'] = 0.35;
          probabilities['oak'] = 0.2;
          probabilities['maple'] = 0.2;
          probabilities['birch'] = 0.1;
          probabilities['bush'] = 0.15;
          probabilities['pine'] = 0.0;
          probabilities['cypress'] = 0.0;
    } else {
          probabilities['oak'] = 0.3;
          probabilities['maple'] = 0.3;
          probabilities['birch'] = 0.15;
          probabilities['palm'] = 0.05;
          probabilities['bush'] = 0.2;
          probabilities['pine'] = 0.0;
          probabilities['cypress'] = 0.0;
        }
      }
    }
    // Middle areas - mixed vegetation
    else {
      probabilities['oak'] = 0.25;
      probabilities['maple'] = 0.2;
      probabilities['pine'] = 0.15;
      probabilities['birch'] = 0.15;
      probabilities['bush'] = 0.15;
      probabilities['cypress'] = 0.1;
      probabilities['palm'] = 0.0;
    }
    
    return probabilities;
  }
  
  // Select a tree type based on weighted probabilities
  String _selectWeightedTreeType(Map<String, double> probabilities) {
    final double rand = _random.nextDouble();
    double cumulativeProbability = 0.0;
    
    for (final type in probabilities.keys) {
      cumulativeProbability += probabilities[type]!;
      if (rand <= cumulativeProbability) {
        return type;
      }
    }
    
    // Fallback
    return 'oak';
  }
  
  // Draw ground shadows for trees
  void _drawGroundShadows(Canvas canvas, Size size, List<Offset> clusterCenters, int treeCount) {
    final treesPerCluster = treeCount ~/ clusterCenters.length;
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.12)
      ..style = PaintingStyle.fill
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 5);
    
    for (final clusterCenter in clusterCenters) {
      for (int i = 0; i < treesPerCluster; i++) {
        // Shadow position (match tree distribution)
        final distance = 20.0 + 100.0 * _random.nextDouble();
        final angle = _random.nextDouble() * 2 * math.pi;
        final x = clusterCenter.dx + math.cos(angle) * distance;
        final y = clusterCenter.dy + math.sin(angle) * distance * 0.5;
        
        // Skip off-screen shadows
        if (x < -30 || x > size.width + 30 || y < -30 || y > size.height + 30) {
          continue;
        }
        
        // Shadow size - larger in foreground, smaller in background
        final foregroundFactor = y / size.height;
        final shadowSize = 10 + 30 * foregroundFactor * (_random.nextDouble() * 0.4 + 0.8);
        
        // Draw oval shadow on ground
        canvas.drawOval(
          Rect.fromCenter(
            center: Offset(x, y),
            width: shadowSize * 1.2,
            height: shadowSize * 0.6,
          ),
          shadowPaint
        );
      }
    }
  }
  
  // Draw enhanced trees with 3D effects
  void _drawEnhancedTree(
    Canvas canvas, 
    double x, 
    double y, 
    double size, 
    String treeType,
    double foregroundFactor
  ) {
    // Apply perspective offsets based on tilt
    final perspectiveOffsetX = tiltFactor * size * swayFactor * 2;
    final perspectiveOffsetY = -tiltFactor * size * 0.5; // Trees appear to rise with tilt
    
    // Apply sway animation, stronger for taller trees
    final swayOffsetX = swayFactor * size * 0.3;
    
    // Calculate final position with perspective and sway
    final double posX = x + perspectiveOffsetX + swayOffsetX;
    final double posY = y + perspectiveOffsetY;
    
    // Trunk height varies by tree type
    double trunkHeightFactor;
      switch (treeType) {
        case 'pine':
      case 'cypress': 
        trunkHeightFactor = 0.3; // Short trunk for conifers
          break;
      case 'palm': 
        trunkHeightFactor = 0.8; // Tall trunk for palms
          break;
        case 'bush':
        trunkHeightFactor = 0.1; // Very short trunk for bushes
          break;
      case 'birch':
        trunkHeightFactor = 0.6; // Taller trunk for birch
          break;
      default: 
        trunkHeightFactor = 0.5; // Medium trunk for deciduous trees
    }
    
    final trunkHeight = size * trunkHeightFactor;
    final trunkWidth = size * 0.1;
    
    // Draw trunk
    _drawTreeTrunk(canvas, posX, posY, trunkWidth, trunkHeight, treeType);
    
    // Draw foliage based on tree type
    switch (treeType) {
      case 'pine':
      case 'cypress':
        _drawConiferousFoliage(canvas, posX, posY - trunkHeight, size, treeType);
        break;
      case 'palm':
        _drawPalmFoliage(canvas, posX, posY - trunkHeight, size);
        break;
      case 'bush':
        _drawBushFoliage(canvas, posX, posY - trunkHeight * 0.5, size);
        break;
      case 'birch':
        _drawDeciduousFoliage(canvas, posX, posY - trunkHeight, size, 'birch');
        break;
      default:
        _drawDeciduousFoliage(canvas, posX, posY - trunkHeight, size, treeType);
    }
  }
  
  // Draw tree trunk with 3D effects
  void _drawTreeTrunk(Canvas canvas, double x, double y, double width, double height, String treeType) {
    // Different trunk colors for different tree types
    Color baseTrunkColor;
    switch (treeType) {
      case 'birch':
        baseTrunkColor = const Color(0xFFE0E0E0); // Light gray for birch
        break;
      case 'palm':
        baseTrunkColor = const Color(0xFF8D6E63); // Lighter brown for palm
        break;
      default:
        baseTrunkColor = trunkColor;
    }
    
    // Create trunk path
    final Path trunkPath = Path();
    trunkPath.moveTo(x - width/2, y);
    trunkPath.lineTo(x + width/2, y);
    trunkPath.lineTo(x + width/2 * 0.8, y - height); // Taper at top
    trunkPath.lineTo(x - width/2 * 0.8, y - height);
    trunkPath.close();
    
    // Create gradient for 3D effect
    final Paint trunkPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [
          _darkenColor(baseTrunkColor, 0.7),  // Dark side
          baseTrunkColor,                     // Mid tone
          _lightenColor(baseTrunkColor, 1.2), // Highlight
        ],
        stops: const [0.2, 0.5, 0.8],
      ).createShader(trunkPath.getBounds())
      ..style = PaintingStyle.fill;
    
    // Draw trunk
    canvas.drawPath(trunkPath, trunkPaint);
    
    // Draw trunk outline for better definition
    final Paint outlinePaint = Paint()
      ..color = _darkenColor(baseTrunkColor, 0.5)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;
    
    canvas.drawPath(trunkPath, outlinePaint);
    
    // Add bark texture for birch trees
    if (treeType == 'birch' && detailLevel >= 3) {
      _addBirchBarkTexture(canvas, trunkPath.getBounds());
    }
  }
  
  // Draw coniferous tree foliage (pine, cypress)
  void _drawConiferousFoliage(Canvas canvas, double x, double y, double size, String treeType) {
    final int layers = treeType == 'cypress' ? 5 : 4;
    final double layerSpacing = size / 5;
    final baseColor = treeType == 'cypress' ? 
        _darkenColor(foliageColor, 0.9) : foliageColor;
    
    for (int i = 0; i < layers; i++) {
      final double layerSize = size * (1.0 - i * 0.15);
      final double layerY = y - i * layerSpacing;
      
      // Create triangular path
      final Path layerPath = Path();
      layerPath.moveTo(x, layerY - layerSize * 0.8); // Point at top
      layerPath.lineTo(x + layerSize/2, layerY);
      layerPath.lineTo(x - layerSize/2, layerY);
      layerPath.close();
      
      // Adjust color for each layer, with top layers slightly lighter
      final adjustment = 1.0 + (i * 0.05);
      final layerColor = _lightenColor(baseColor, adjustment);
      
      // Create radial gradient for 3D effect
      final Paint layerPaint = Paint()
        ..shader = RadialGradient(
          center: Alignment.topCenter,
          radius: 1.0,
          colors: [
            _lightenColor(layerColor, 1.3),
            layerColor,
            _darkenColor(layerColor, 0.8),
          ],
          stops: const [0.1, 0.5, 0.9],
        ).createShader(layerPath.getBounds())
        ..style = PaintingStyle.fill;
      
      canvas.drawPath(layerPath, layerPaint);
      
      // Add outline
      final Paint outlinePaint = Paint()
        ..color = _darkenColor(layerColor, 0.8).withOpacity(0.7)
        ..strokeWidth = 0.5
        ..style = PaintingStyle.stroke;
      
      canvas.drawPath(layerPath, outlinePaint);
    }
  }
  
  // Draw deciduous tree foliage (oak, maple)
  void _drawDeciduousFoliage(Canvas canvas, double x, double y, double size, String treeType) {
    // Different shapes based on tree type
    final bool isRounded = treeType != 'maple';
    final baseColor = treeType == 'birch' ? 
        _lightenColor(foliageColor, 1.1) : foliageColor;
    
    // Create path for foliage
    late Path foliagePath;
    late Rect bounds;
    
    if (isRounded) {
      // Circular/oval foliage for oak, birch
      bounds = Rect.fromCenter(
        center: Offset(x, y - size * 0.3),
        width: size * 1.2,
        height: size * 1.4,
      );
      
      foliagePath = Path()..addOval(bounds);
    } else {
      // Star-like shape for maple
      foliagePath = Path();
      final double centerX = x;
      final double centerY = y - size * 0.3;
      final double radius = size * 0.7;
      
      for (int i = 0; i < 5; i++) {
        final double angle = i * (math.pi * 2 / 5);
        final double pointX = centerX + math.cos(angle) * radius;
        final double pointY = centerY + math.sin(angle) * radius;
        
        if (i == 0) {
          foliagePath.moveTo(pointX, pointY);
        } else {
          // Create curves between points for organic shape
          final double controlX = centerX + math.cos(angle - 0.3) * radius * 0.7;
          final double controlY = centerY + math.sin(angle - 0.3) * radius * 0.7;
          foliagePath.quadraticBezierTo(controlX, controlY, pointX, pointY);
        }
      }
      
      // Close the path back to the first point
      final double controlX = centerX + math.cos(-0.3) * radius * 0.7;
      final double controlY = centerY + math.sin(-0.3) * radius * 0.7;
      foliagePath.quadraticBezierTo(controlX, controlY, centerX + math.cos(0) * radius, centerY + math.sin(0) * radius);
      
      bounds = foliagePath.getBounds();
    }
    
    // Create radial gradient for 3D effect
    final Paint foliagePaint = Paint()
      ..shader = RadialGradient(
        center: const Alignment(-0.3, -0.3), // Light source from top-left
        radius: 1.3,
        colors: [
          _lightenColor(baseColor, 1.4),
          baseColor,
          _darkenColor(baseColor, 0.8),
        ],
        stops: const [0.1, 0.5, 0.9],
      ).createShader(bounds)
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(foliagePath, foliagePaint);
    
    // Add subtle outline
    final Paint outlinePaint = Paint()
      ..color = _darkenColor(baseColor, 0.7).withOpacity(0.6)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;
    
    canvas.drawPath(foliagePath, outlinePaint);
    
    // Add highlight spots for 3D effect
    if (detailLevel >= 3) {
      final Paint highlightPaint = Paint()
        ..color = _lightenColor(baseColor, 1.5).withOpacity(0.3)
        ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
        Offset(x - size * 0.2, y - size * 0.5),
        size * 0.15,
        highlightPaint
      );
    }
  }
  
  // Draw palm tree foliage
  void _drawPalmFoliage(Canvas canvas, double x, double y, double size) {
    final int fronds = 7 + (detailLevel * 2);
    
    // Draw palm fronds as curved lines radiating from center
    for (int i = 0; i < fronds; i++) {
      final double angle = (i / fronds) * math.pi * 2;
      final double frondLength = size * 0.8;
      
      // Create a path for the frond
      final Path frondPath = Path();
      frondPath.moveTo(x, y);
      
      // Control points for curved frond
      final double controlX = x + math.cos(angle) * frondLength * 0.5;
      final double controlY = y + math.sin(angle) * frondLength * 0.5 - frondLength * 0.2;
      final double endX = x + math.cos(angle) * frondLength;
      final double endY = y + math.sin(angle) * frondLength;
      
      frondPath.quadraticBezierTo(controlX, controlY, endX, endY);
      
      // Create a gradient for the frond
      final Paint frondPaint = Paint()
        ..shader = LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            _lightenColor(foliageColor, 1.2),
            foliageColor,
          ],
        ).createShader(Rect.fromPoints(
          Offset(x, y),
          Offset(endX, endY),
        ))
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3.0;
      
      canvas.drawPath(frondPath, frondPaint);
    }
  }
  
  // Draw bush-like foliage
  void _drawBushFoliage(Canvas canvas, double x, double y, double size) {
    // Draw several overlapping circles for bush effect
    for (int i = 0; i < 5; i++) {
      final double offsetX = size * 0.3 * (_random.nextDouble() - 0.5);
      final double offsetY = size * 0.3 * (_random.nextDouble() - 0.5);
      final double circleSize = size * (0.5 + _random.nextDouble() * 0.3);
      
      // Adjust color slightly for each segment
      final adjustmentFactor = 0.9 + _random.nextDouble() * 0.2;
      final segmentColor = _lightenColor(foliageColor, adjustmentFactor);
      
      final Paint bushPaint = Paint()
        ..shader = RadialGradient(
          center: const Alignment(-0.2, -0.2),
          radius: 1.0,
          colors: [
            _lightenColor(segmentColor, 1.2),
            segmentColor,
            _darkenColor(segmentColor, 0.9),
          ],
          stops: const [0.1, 0.5, 0.9],
        ).createShader(Rect.fromCenter(
          center: Offset(x + offsetX, y + offsetY),
          width: circleSize,
          height: circleSize,
        ))
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        Offset(x + offsetX, y + offsetY),
        circleSize / 2,
        bushPaint
      );
    }
  }
  
  // Add birch bark texture
  void _addBirchBarkTexture(Canvas canvas, Rect trunkBounds) {
    final Paint linePaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    // Add horizontal stripes for birch bark
    final double stripeSpacing = trunkBounds.height / 10;
    for (double y = trunkBounds.top + stripeSpacing; y < trunkBounds.bottom; y += stripeSpacing) {
      // Randomize line length and position
      final double lineLength = trunkBounds.width * (0.3 + _random.nextDouble() * 0.4);
      final double startX = trunkBounds.left + (trunkBounds.width - lineLength) * _random.nextDouble();
      
      canvas.drawLine(
        Offset(startX, y),
        Offset(startX + lineLength, y),
        linePaint
      );
    }
  }
  
  // Draw ground vegetation (small plants, grass)
  void _drawGroundVegetation(Canvas canvas, Size size) {
    final vegetationCount = (size.width * size.height / 20000).round();
    final Paint grassPaint = Paint()
      ..color = _lightenColor(foliageColor, 1.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    for (int i = 0; i < vegetationCount; i++) {
      final x = _random.nextDouble() * size.width;
      final y = size.height * (0.7 + _random.nextDouble() * 0.3); // Only bottom third
      
      // Draw grass blades or small plants
      final int blades = 3 + _random.nextInt(3);
      for (int j = 0; j < blades; j++) {
        final double angle = math.pi / 2 - math.pi / 6 + (_random.nextDouble() * math.pi / 3);
        final double length = 3.0 + _random.nextDouble() * 5.0;
        
        final double endX = x + math.cos(angle) * length;
        final double endY = y - math.sin(angle) * length;
        
        canvas.drawLine(
          Offset(x, y),
          Offset(endX, endY),
          grassPaint
        );
      }
    }
  }
  
  // Helper methods for color manipulation
  Color _lightenColor(Color color, double factor) {
    return Color.fromARGB(
      color.alpha,
      math.min(255, (color.red * factor).round()),
      math.min(255, (color.green * factor).round()),
      math.min(255, (color.blue * factor).round()),
    );
  }
  
  Color _darkenColor(Color color, double factor) {
    return Color.fromARGB(
      color.alpha,
      (color.red * factor).round(),
      (color.green * factor).round(),
      (color.blue * factor).round(),
    );
  }
  
  @override
  bool shouldRepaint(TreesPainter oldDelegate) {
    return oldDelegate.foliageColor != foliageColor ||
           oldDelegate.trunkColor != trunkColor ||
           oldDelegate.highlightColor != highlightColor ||
           oldDelegate.detailLevel != detailLevel ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.swayFactor != swayFactor ||
           oldDelegate.season != season ||
           oldDelegate.mapCamera != mapCamera; // Add mapCamera to repaint criteria
  }
} 