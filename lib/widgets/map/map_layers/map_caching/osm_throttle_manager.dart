import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

/// Manages throttling of OSM API requests to prevent rate limiting
class OSMThrottleManager {
  static const Duration _minRequestInterval = Duration(milliseconds: 500);
  static const Duration _requestTimeout = Duration(minutes: 2);
  static const int _maxRequestsPerArea = 3;
  
  DateTime? _lastRequestTime;
  final Map<String, List<DateTime>> _requestHistory = {};
  final Map<String, LatLngBounds> _requestBounds = {};

  /// Check if a new request can be made for the given area
  Future<bool> canMakeRequest(String layerType, LatLngBounds bounds) async {
    // Check global throttling first
    if (_lastRequestTime != null) {
      final timeSinceLastRequest = DateTime.now().difference(_lastRequestTime!);
      if (timeSinceLastRequest < _minRequestInterval) {
        debugPrint('Request too soon after last request');
        return false;
      }
    }

    // Generate area key
    final areaKey = _getAreaKey(layerType, bounds);

    // Clean up old requests
    _cleanupOldRequests();

    // Check request history for this area
    final requests = _requestHistory[areaKey] ?? [];
    if (requests.length >= _maxRequestsPerArea) {
      debugPrint('Too many requests for area $areaKey');
      return false;
    }

    // Check if we already have a request for overlapping bounds
    if (_requestBounds.containsKey(areaKey)) {
      final existingBounds = _requestBounds[areaKey]!;
      if (_boundsOverlap(existingBounds, bounds)) {
        debugPrint('Overlapping request already exists for $areaKey');
        return false;
      }
    }

    return true;
  }

  /// Record a successful request for the given area
  void recordRequest(String layerType, LatLngBounds bounds) {
    final areaKey = _getAreaKey(layerType, bounds);
    final now = DateTime.now();

    // Update request history
    if (!_requestHistory.containsKey(areaKey)) {
      _requestHistory[areaKey] = [];
    }
    _requestHistory[areaKey]!.add(now);

    // Update bounds
    _requestBounds[areaKey] = bounds;

    // Update last request time
    _lastRequestTime = now;
  }

  /// Throttles requests to ensure minimum interval between them
  Future<void> throttleRequest() async {
    if (_lastRequestTime != null) {
      final timeSinceLastRequest = DateTime.now().difference(_lastRequestTime!);
      if (timeSinceLastRequest < _minRequestInterval) {
        await Future.delayed(_minRequestInterval - timeSinceLastRequest);
      }
    }
    _lastRequestTime = DateTime.now();
  }

  /// Clean up old requests from history
  void _cleanupOldRequests() {
    final now = DateTime.now();
    _requestHistory.removeWhere((key, requests) {
      requests.removeWhere(
        (time) => now.difference(time) > _requestTimeout
      );
      return requests.isEmpty;
    });

    // Also clean up old bounds
    final expiredKeys = _requestBounds.keys.where(
      (key) => !_requestHistory.containsKey(key)
    ).toList();
    for (final key in expiredKeys) {
      _requestBounds.remove(key);
    }
  }

  /// Generate a key for an area based on rounded coordinates
  String _getAreaKey(String layerType, LatLngBounds bounds) {
    return '${layerType}_${bounds.south.toStringAsFixed(2)}_${bounds.west.toStringAsFixed(2)}_${bounds.north.toStringAsFixed(2)}_${bounds.east.toStringAsFixed(2)}';
  }

  /// Check if two bounding boxes overlap
  bool _boundsOverlap(LatLngBounds a, LatLngBounds b) {
    return !(a.west > b.east ||
            a.east < b.west ||
            a.south > b.north ||
            a.north < b.south);
  }
} 