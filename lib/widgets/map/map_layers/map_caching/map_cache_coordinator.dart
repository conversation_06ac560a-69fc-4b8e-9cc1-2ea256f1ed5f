import 'package:latlong2/latlong.dart';

/// Types of map data that can be cached
enum MapDataType {
  specializedGreen,
  buildings,
  roads,
  water,
  parks,
  pedestrian,
  recreation,
  parking
}

/// Coordinates caching of map data to reduce API calls
class MapCacheCoordinator {
  static final MapCacheCoordinator _instance = MapCacheCoordinator._internal();
  factory MapCacheCoordinator() => _instance;
  MapCacheCoordinator._internal();

  /// Get data from cache or fetch if missing
  Future<List<Map<String, dynamic>>?> getData({
    required MapDataType type,
    required String key,
    required LatLng southwest,
    required LatLng northeast,
    required double zoomLevel,
    required Future<List<Map<String, dynamic>>> Function() fetchIfMissing,
  }) async {
    // TODO: Implement actual caching logic
    return await fetchIfMissing();
  }
} 