import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:async';

import 'osm_parking_data_provider.dart';
import 'osm_parking_painter.dart';

/// A Flutter Map layer for rendering OpenStreetMap parking facilities
/// including parking lots, parking spaces, and parking structures
class OSMParkingLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final bool isMapMoving;
  final String theme;
  final bool enhancedDetail;
  final ValueChanged<OSMParkingDataProvider>? onDataProviderCreated;
  final MapCamera? mapCamera;

  const OSMParkingLayer({
    Key? key,
    this.tiltFactor = 1.0,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.theme = 'vibrant',
    this.enhancedDetail = true,
    this.onDataProviderCreated,
    this.mapCamera,
  }) : super(key: key);

  @override
  State<OSMParkingLayer> createState() => _OSMParkingLayerState();
}

class _OSMParkingLayerState extends State<OSMParkingLayer> {
  late OSMParkingDataProvider _dataProvider;
  bool _isLoading = true;
  bool _hasNetworkError = false;
  Map<String, List<Map<String, dynamic>>> _parkingData = {};
  String _currentTheme = 'vibrant';
  
  @override
  void initState() {
    super.initState();
    
    _currentTheme = widget.theme;
    
    // Initialize the data provider
    _dataProvider = OSMParkingDataProvider(
      initialZoomLevel: widget.zoomLevel,
      initialBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving,
      onError: _handleDataError,
    );
    
    if (widget.onDataProviderCreated != null) {
      widget.onDataProviderCreated!(_dataProvider);
    }
    
    // Initial data load
    _fetchParkingData();
  }
  
  // Handle network/data errors
  void _handleDataError(String errorMessage) {
    if (mounted) {
      setState(() {
        _hasNetworkError = true;
      });
    }
    debugPrint('OSMParkingLayer error: $errorMessage');
  }

  @override
  void didUpdateWidget(OSMParkingLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update current theme if changed
    if (widget.theme != oldWidget.theme) {
      _currentTheme = widget.theme;
    }
    
    // Notify data provider of changes
    _dataProvider.updateParameters(
      zoomLevel: widget.zoomLevel,
      visibleBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving,
    );
    
    // Determine if we need to fetch new data
    bool shouldFetch = _dataProvider.shouldFetchNewData(
      oldZoomLevel: oldWidget.zoomLevel,
      oldBounds: oldWidget.visibleBounds,
      oldIsMoving: oldWidget.isMapMoving,
      newZoomLevel: widget.zoomLevel,
      newBounds: widget.visibleBounds,
      newIsMoving: widget.isMapMoving,
    );
    
    if (shouldFetch) {
      _fetchParkingData();
    }
  }

  void _fetchParkingData() async {
    setState(() {
      _isLoading = true;
    });
    
    // Use the data provider to fetch data
    await _dataProvider.fetchParkingData();
    
    if (mounted) {
      setState(() {
        _parkingData = _dataProvider.parkingData;
        _isLoading = false;
        
        // If there was a successful fetch, clear any previous error state
        if (_hasNetworkError && !_dataProvider.hasError) {
          _hasNetworkError = false;
        }
      });
    }
  }
  
  // Retry after network error
  void _retryAfterNetworkError() {
    if (_hasNetworkError) {
      setState(() {
        _hasNetworkError = false;
      });
      
      _dataProvider.resetErrorState();
      _fetchParkingData();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't render parking facilities at low zoom levels
    if (widget.zoomLevel < 13) {
      return const SizedBox.shrink();
    }
    
    // If data is not yet loaded, show a placeholder
    if (_isLoading && !_dataProvider.hasInitialData) {
      return const SizedBox.shrink();
    }
    
    // Show error overlay with retry button if there was a network error
    if (_hasNetworkError) {
      return GestureDetector(
        onTap: _retryAfterNetworkError,
        child: Container(
          color: Colors.transparent,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'Tap to retry loading parking facilities',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ),
      );
    }
    
    // Create the custom painter that will render the parking facilities
    return CustomPaint(
      painter: OSMParkingPainter(
        parkingData: _parkingData,
        zoomLevel: widget.zoomLevel,
        visibleBounds: widget.visibleBounds,
        theme: _currentTheme,
        enhancedDetail: widget.enhancedDetail,
        tiltFactor: widget.tiltFactor,
      ),
      size: Size.infinite,
    );
  }
} 