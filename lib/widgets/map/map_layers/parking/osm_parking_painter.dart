import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

/// Custom painter to render OpenStreetMap parking facilities with enhanced 2.5D styling
class OSMParkingPainter extends CustomPainter {
  final Map<String, List<Map<String, dynamic>>> parkingData;
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final bool enhancedDetail;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  
  OSMParkingPainter({
    required this.parkingData,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    required this.theme,
    required this.enhancedDetail,
  });

  // Colors for different parking facility types and themes
  final Map<String, Map<String, Map<String, Color>>> _parkingColorPalette = {
    'vibrant': {
      'parking_lot': {
        'fill': const Color(0xFF64B5F6).withOpacity(0.3),  // Blue for regular parking
        'outline': const Color(0xFF2196F3).withOpacity(0.8),
        'symbol': const Color(0xFF1976D2).withOpacity(0.9),
      },
      'surface': {
        'fill': const Color(0xFF81D4FA).withOpacity(0.3),  // Light blue for surface parking
        'outline': const Color(0xFF03A9F4).withOpacity(0.8),
        'symbol': const Color(0xFF0288D1).withOpacity(0.9),
      },
      'underground': {
        'fill': const Color(0xFF4DB6AC).withOpacity(0.3),  // Teal for underground parking
        'outline': const Color(0xFF009688).withOpacity(0.8),
        'symbol': const Color(0xFF00796B).withOpacity(0.9),
      },
      'multistorey': {
        'fill': const Color(0xFF4DD0E1).withOpacity(0.3),  // Cyan for multi-storey parking
        'outline': const Color(0xFF00BCD4).withOpacity(0.8),
        'symbol': const Color(0xFF0097A7).withOpacity(0.9),
      },
      'street': {
        'fill': const Color(0xFFB3E5FC).withOpacity(0.3),  // Very light blue for street parking
        'outline': const Color(0xFF29B6F6).withOpacity(0.8),
        'symbol': const Color(0xFF0288D1).withOpacity(0.9),
      },
    },
    'dark': {
      'parking_lot': {
        'fill': const Color(0xFF1976D2).withOpacity(0.2),  // Darker blue for regular parking
        'outline': const Color(0xFF1565C0).withOpacity(0.7),
        'symbol': const Color(0xFF0D47A1).withOpacity(0.8),
      },
      'surface': {
        'fill': const Color(0xFF0288D1).withOpacity(0.2),  // Darker light blue for surface parking
        'outline': const Color(0xFF0277BD).withOpacity(0.7),
        'symbol': const Color(0xFF01579B).withOpacity(0.8),
      },
      'underground': {
        'fill': const Color(0xFF00796B).withOpacity(0.2),  // Darker teal for underground parking
        'outline': const Color(0xFF00695C).withOpacity(0.7),
        'symbol': const Color(0xFF004D40).withOpacity(0.8),
      },
      'multistorey': {
        'fill': const Color(0xFF0097A7).withOpacity(0.2),  // Darker cyan for multi-storey parking
        'outline': const Color(0xFF00838F).withOpacity(0.7),
        'symbol': const Color(0xFF006064).withOpacity(0.8),
      },
      'street': {
        'fill': const Color(0xFF0277BD).withOpacity(0.2),  // Darker light blue for street parking
        'outline': const Color(0xFF01579B).withOpacity(0.7),
        'symbol': const Color(0xFF0D47A1).withOpacity(0.8),
      },
    },
    'monochrome': {
      'parking_lot': {
        'fill': const Color(0xFFE0E0E0).withOpacity(0.25),  // Light gray
        'outline': const Color(0xFF9E9E9E).withOpacity(0.7),
        'symbol': const Color(0xFF757575).withOpacity(0.8),
      },
      'surface': {
        'fill': const Color(0xFFEEEEEE).withOpacity(0.25),  // Very light gray
        'outline': const Color(0xFFBDBDBD).withOpacity(0.7),
        'symbol': const Color(0xFF9E9E9E).withOpacity(0.8),
      },
      'underground': {
        'fill': const Color(0xFFE0E0E0).withOpacity(0.2),  // Light gray
        'outline': const Color(0xFF9E9E9E).withOpacity(0.7),
        'symbol': const Color(0xFF757575).withOpacity(0.8),
      },
      'multistorey': {
        'fill': const Color(0xFFEEEEEE).withOpacity(0.25),  // Very light gray
        'outline': const Color(0xFFBDBDBD).withOpacity(0.7),
        'symbol': const Color(0xFF9E9E9E).withOpacity(0.8),
      },
      'street': {
        'fill': const Color(0xFFF5F5F5).withOpacity(0.25),  // Almost white
        'outline': const Color(0xFFE0E0E0).withOpacity(0.7),
        'symbol': const Color(0xFFBDBDBD).withOpacity(0.8),
      },
    },
  };

  @override
  void paint(Canvas canvas, Size size) {
    final themeColors = _parkingColorPalette[theme] ?? _parkingColorPalette['vibrant']!;
    
    // Don't render anything if there's no data to display
    if (parkingData.isEmpty) return;
    if (parkingData.values.every((list) => list.isEmpty)) return;
    
    // Draw different types of parking facilities with appropriate elevation
    
    // 1. Draw general parking lots with low elevation
    _drawParkingLots(canvas, size, parkingData['parking_lots'] ?? [], themeColors['parking_lot']!, 0.05);
    
    // 2. Draw surface parking with minimal elevation
    _drawParkingLots(canvas, size, parkingData['parking_surface'] ?? [], themeColors['surface']!, 0.03);
    
    // 3. Draw underground parking with underground visual cues
    _drawUndergroundParking(canvas, size, parkingData['parking_underground'] ?? [], themeColors['underground']!);
    
    // 4. Draw multi-storey parking with higher elevation for 2.5D effect
    _drawParkingLots(canvas, size, parkingData['parking_multistorey'] ?? [], themeColors['multistorey']!, 0.25);
    
    // 5. Draw street parking with minimal elevation
    _drawStreetParking(canvas, size, parkingData['street_parking'] ?? [], themeColors['street']!);
  }
  
  // Draw parking lots with 2.5D elevation effect
  void _drawParkingLots(Canvas canvas, Size size, List<Map<String, dynamic>> parkingLots, 
                        Map<String, Color> colors, double elevationFactor) {
    if (parkingLots.isEmpty) return;
    
    final fillPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    final outlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..isAntiAlias = true;
    
    final symbolPaint = Paint()
      ..color = colors['symbol']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..isAntiAlias = true;
    
    for (final lot in parkingLots) {
      // Apply the 2.5D elevation effect based on tiltFactor
      final double elevation = elevationFactor * tiltFactor;
      
      // Draw parking area as a filled polygon
      final points = lot['points'] as List? ?? [];
      final LatLng? center = lot['center'] as LatLng?;
      final Map<String, dynamic>? tags = lot['tags'] as Map<String, dynamic>?;
      
      if (points.isEmpty && center != null) {
        // Handle point-based parking markers with appropriate 2.5D effect
        _drawParkingMarker(canvas, size, center, colors, elevation, tags);
        continue;
      }
      
      if (points.isNotEmpty) {
        // Convert geographic points to screen coordinates
        final List<Offset> screenPoints = _convertToScreenPoints(points, size);
        
        // Draw shadow for 2.5D effect - shadow offset based on elevation
        if (elevation > 0.01 && tiltFactor > 0.1) {
          _drawShadow(canvas, screenPoints, colors['fill']!.withOpacity(0.3), elevation);
        }
        
        // Draw the parking area with fill
        final Path path = Path();
        path.addPolygon(screenPoints, true);
        canvas.drawPath(path, fillPaint);
        canvas.drawPath(path, outlinePaint);
        
        // Draw capacity indicator and parking symbol if zoom level is high enough
        if (zoomLevel > 16 && center != null) {
          // Calculate capacity-based size
          int capacity = _getCapacity(tags);
          double symbolSize = capacity > 0 ? 
              math.min(6 + math.log(capacity) * 1.5, 16) : 
              8.0;
          
        final Offset centerPoint = _projectPoint(center, size);
        
          // Draw parking symbol (P) at center
          _drawParkingSymbol(canvas, centerPoint, symbolSize, colors['symbol']!);
          
          // Draw additional visual elements for enhanced detail
          if (enhancedDetail && zoomLevel > 17) {
            _drawParkingDetail(canvas, screenPoints, centerPoint, colors, tags);
          }
        }
      }
    }
  }
  
  // Enhanced shadow drawing for 2.5D effect
  void _drawShadow(Canvas canvas, List<Offset> points, Color shadowColor, double elevation) {
    // Create shadow path with slight offset for 2.5D effect
    final shadowOffset = Offset(elevation * 4, elevation * 4);
    final Path shadowPath = Path();
    shadowPath.addPolygon(points, true);
    
    // Create shadow paint with blur
    final Paint shadowPaint = Paint()
      ..color = shadowColor
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, elevation * 3);
        
    // Draw shadow slightly offset from the main shape
    canvas.drawPath(shadowPath.shift(shadowOffset), shadowPaint);
  }
  
  // Draw underground parking with special styling
  void _drawUndergroundParking(Canvas canvas, Size size, List<Map<String, dynamic>> parkingLots, 
                           Map<String, Color> colors) {
    if (parkingLots.isEmpty) return;
    
    final fillPaint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Specialized outline for underground parking with dashed effect
    final dashedOutlinePaint = Paint()
      ..color = colors['outline']!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..isAntiAlias = true;
    
    for (final lot in parkingLots) {
      final points = lot['points'] as List? ?? [];
      final LatLng? center = lot['center'] as LatLng?;
      final Map<String, dynamic>? tags = lot['tags'] as Map<String, dynamic>?;
      
      if (points.isNotEmpty) {
        // Convert geographic points to screen coordinates
        final List<Offset> screenPoints = _convertToScreenPoints(points, size);
        
        // Draw underground parking with dotted outline
        final Path path = Path();
        path.addPolygon(screenPoints, true);
        
        // Fill with special pattern for underground parking
        canvas.drawPath(path, fillPaint);
        
        // Draw dashed outline to indicate underground
        _drawDashedLine(canvas, screenPoints, dashedOutlinePaint);
        
        // Add special underground symbol
        if (center != null && zoomLevel > 16) {
          final Offset centerPoint = _projectPoint(center, size);
          _drawUndergroundSymbol(canvas, centerPoint, colors['symbol']!);
        }
      }
    }
  }
  
  // Draw dashed line
  void _drawDashedLine(Canvas canvas, List<Offset> points, Paint paint) {
    if (points.length < 2) return;
    
        final Path path = Path();
    path.moveTo(points.first.dx, points.first.dy);
    
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    
    final dashPath = Path();
    final PathMetrics pathMetrics = path.computeMetrics();
    
    for (PathMetric metric in pathMetrics) {
      double distance = 0.0;
      bool draw = true;
      
      while (distance < metric.length) {
        final double length = draw ? 4.0 : 2.0;
        if (draw && distance + length < metric.length) {
          final Path extractPath = metric.extractPath(distance, distance + length);
          dashPath.addPath(extractPath, Offset.zero);
        }
        distance += length;
        draw = !draw;
      }
    }
    
    canvas.drawPath(dashPath, paint);
  }
  
  // Enhanced street parking visualization with 2.5D elements
  void _drawStreetParking(Canvas canvas, Size size, List<Map<String, dynamic>> streetParking, 
                       Map<String, Color> colors) {
    if (streetParking.isEmpty) return;
    
    final Paint linePaint = Paint()
          ..color = colors['outline']!
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3.0
          ..isAntiAlias = true;
        
    final Paint symbolPaint = Paint()
      ..color = colors['symbol']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
        
    for (final element in streetParking) {
      // Handle street parking elements
      final LatLng? center = element['center'] as LatLng?;
      final points = element['points'] as List? ?? [];
      final String? elementType = element['element_type'] as String?;
      
      if (center != null) {
        // Draw street parking marker
        final Offset centerPoint = _projectPoint(center, size);
        
        // Draw street parking space indicator
        canvas.drawCircle(centerPoint, 3.0, symbolPaint);
        
        // Add detail at high zoom
        if (zoomLevel > 17 && elementType == 'parking_space') {
          _drawParkingSpaceSymbol(canvas, centerPoint, colors);
        }
      } else if (points.isNotEmpty && elementType == 'parking_aisle') {
        // Draw parking aisle
        final List<Offset> screenPoints = _convertToScreenPoints(points, size);
        
        if (screenPoints.length > 1) {
          final Path path = Path();
          path.moveTo(screenPoints[0].dx, screenPoints[0].dy);
          
          for (int i = 1; i < screenPoints.length; i++) {
            path.lineTo(screenPoints[i].dx, screenPoints[i].dy);
        }
        
          // Draw with slight elevation effect for 2.5D
          if (tiltFactor > 0.1) {
            final shadowPath = Path.from(path);
            final shadowPaint = Paint()
              ..color = Colors.black.withOpacity(0.2)
              ..style = PaintingStyle.stroke
              ..strokeWidth = 3.0
              ..maskFilter = MaskFilter.blur(BlurStyle.normal, 1.0);
            
            canvas.drawPath(shadowPath.shift(Offset(1.0 * tiltFactor, 1.0 * tiltFactor)), shadowPaint);
          }
          
          canvas.drawPath(path, linePaint);
        }
      }
    }
  }
  
  // Draw specialized parking symbols
  void _drawParkingSymbol(Canvas canvas, Offset center, double size, Color color) {
    // "P" symbol for parking
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw parking symbol background
    canvas.drawCircle(center, size, paint);
    
    // Draw "P" text
    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: 'P',
        style: TextStyle(
          color: Colors.white,
          fontSize: size * 1.2,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas, 
      center.translate(-textPainter.width / 2, -textPainter.height / 2),
    );
  }
  
  // Draw underground parking symbol
  void _drawUndergroundSymbol(Canvas canvas, Offset center, Color color) {
    // "P" with down arrow for underground parking
    _drawParkingSymbol(canvas, center, 8.0, color);
          
    // Add down arrow indicator
    final Paint arrowPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..isAntiAlias = true;
    
    // Draw down arrow
    final Path arrowPath = Path();
    arrowPath.moveTo(center.dx, center.dy + 6);
    arrowPath.lineTo(center.dx - 3, center.dy + 3);
    arrowPath.moveTo(center.dx, center.dy + 6);
    arrowPath.lineTo(center.dx + 3, center.dy + 3);
    
    canvas.drawPath(arrowPath, arrowPaint);
  }
  
  // Additional parking detail drawing when zoomed in
  void _drawParkingDetail(Canvas canvas, List<Offset> points, Offset center, 
                       Map<String, Color> colors, Map<String, dynamic>? tags) {
    // Add parking lot details like parking spaces and driving lanes at high zoom levels
    if (points.length < 3) return;
    
    // Draw parking lot pattern - simplified representation of parking spaces
    // Calculate bounding box
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    for (final point in points) {
      minX = math.min(minX, point.dx);
      maxX = math.max(maxX, point.dx);
      minY = math.min(minY, point.dy);
      maxY = math.max(maxY, point.dy);
    }
    
    // Only draw details if the parking lot is large enough on screen
    final double width = maxX - minX;
    final double height = maxY - minY;
    final double area = width * height;
    
    if (area < 1000) return; // Skip if too small to see details
    
    // Draw simplified parking spaces
    final Paint spacePaint = Paint()
      ..color = colors['outline']!.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    // Determine orientation based on shape
    bool isHorizontal = width > height;
    
    // Draw simplified parking spaces
    if (isHorizontal) {
      // Draw horizontal parking spaces
      double spaceWidth = 8.0;
      int spaceCount = math.max(2, (width / spaceWidth).floor());
      double actualSpacing = width / spaceCount;
      
      for (int i = 1; i < spaceCount; i++) {
        double x = minX + i * actualSpacing;
        canvas.drawLine(Offset(x, minY), Offset(x, maxY), spacePaint);
      }
    } else {
      // Draw vertical parking spaces
      double spaceHeight = 8.0;
      int spaceCount = math.max(2, (height / spaceHeight).floor());
      double actualSpacing = height / spaceCount;
      
      for (int i = 1; i < spaceCount; i++) {
        double y = minY + i * actualSpacing;
        canvas.drawLine(Offset(minX, y), Offset(maxX, y), spacePaint);
      }
    }
    
    // If the lot has fee information, indicate it
    if (tags != null && tags.containsKey('fee') && tags['fee'] == 'yes') {
      _drawFeeIndicator(canvas, center);
    }
  }
  
  // Draw a fee indicator
  void _drawFeeIndicator(Canvas canvas, Offset center) {
    final symbolOffset = Offset(center.dx + 12, center.dy - 12);
    
    // Draw fee symbol (€ or $)
    final TextPainter textPainter = TextPainter(
      text: const TextSpan(
        text: '€',
        style: TextStyle(
          color: Color(0xFF4CAF50),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    // Draw background circle
    canvas.drawCircle(
      symbolOffset, 
      8.0, 
      Paint()..color = Colors.white.withOpacity(0.8)
    );
    
    textPainter.paint(
      canvas, 
      symbolOffset.translate(-textPainter.width / 2, -textPainter.height / 2),
    );
  }
  
  // Draw individual street parking space
  void _drawParkingSpaceSymbol(Canvas canvas, Offset center, Map<String, Color> colors) {
    final Paint paint = Paint()
      ..color = colors['fill']!
      ..style = PaintingStyle.fill;
    
    // Draw small rectangle representing a parking space
    final Rect spaceRect = Rect.fromCenter(center: center, width: 6.0, height: 10.0);
    canvas.drawRect(spaceRect, paint);
    
    // Draw outline
    canvas.drawRect(
      spaceRect, 
      Paint()
        ..color = colors['outline']!
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0
    );
  }
  
  // Draw parking marker for point-based data
  void _drawParkingMarker(Canvas canvas, Size size, LatLng center, 
                      Map<String, Color> colors, double elevation, Map<String, dynamic>? tags) {
    final Offset centerPoint = _projectPoint(center, size);
    
    // Draw shadow for 2.5D effect
    if (elevation > 0.01 && tiltFactor > 0.1) {
      canvas.drawCircle(
        centerPoint.translate(elevation * 2, elevation * 2),
        6.0,
        Paint()
          ..color = Colors.black.withOpacity(0.3)
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, elevation * 2)
      );
  }
  
    // Draw main parking symbol
    _drawParkingSymbol(canvas, centerPoint, 6.0, colors['symbol']!);
    
    // If high detail and high zoom, add extra information
    if (enhancedDetail && zoomLevel > 17 && tags != null) {
      if (tags.containsKey('capacity')) {
        _drawCapacityIndicator(canvas, centerPoint, tags['capacity']);
      }
      
      if (tags.containsKey('fee') && tags['fee'] == 'yes') {
        _drawFeeIndicator(canvas, centerPoint);
      }
    }
  }
  
  // Draw parking capacity indicator
  void _drawCapacityIndicator(Canvas canvas, Offset center, dynamic capacityValue) {
    int capacity = 0;
    if (capacityValue is int) {
      capacity = capacityValue;
    } else if (capacityValue is String) {
      capacity = int.tryParse(capacityValue) ?? 0;
    }
    
    if (capacity <= 0) return;
    
    final symbolOffset = Offset(center.dx - 12, center.dy - 12);
    
    // Draw capacity text
    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: capacity.toString(),
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    // Draw background circle
    canvas.drawCircle(
      symbolOffset, 
      8.0, 
      Paint()..color = Colors.white.withOpacity(0.8)
    );
    
    textPainter.paint(
      canvas, 
      symbolOffset.translate(-textPainter.width / 2, -textPainter.height / 2),
    );
  }
  
  // Helper to extract parking capacity from tags
  int _getCapacity(Map<String, dynamic>? tags) {
    if (tags == null || !tags.containsKey('capacity')) return 0;
    
    final dynamic capacityValue = tags['capacity'];
    if (capacityValue is int) return capacityValue;
    if (capacityValue is String) return int.tryParse(capacityValue) ?? 0;
    
    return 0;
  }
  
  // Convert geographic points to screen coordinates
  List<Offset> _convertToScreenPoints(List points, Size size) {
    return points.map((point) {
      final LatLng latLng = point as LatLng;
      return _projectPoint(latLng, size);
    }).toList();
  }
  
  // Project a geographic point to the screen
  Offset _projectPoint(LatLng point, Size size) {
    // Calculate the position on the screen
    final percentX = (point.longitude - visibleBounds.west) / 
                    (visibleBounds.east - visibleBounds.west);
    final percentY = (point.latitude - visibleBounds.north) / 
                    (visibleBounds.south - visibleBounds.north);
    
    return Offset(
      size.width * percentX,
      size.height * percentY
    );
  }
  
  @override
  bool shouldRepaint(OSMParkingPainter oldDelegate) {
    return oldDelegate.parkingData != parkingData ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.visibleBounds != visibleBounds ||
           oldDelegate.theme != theme ||
           oldDelegate.enhancedDetail != enhancedDetail;
  }
} 