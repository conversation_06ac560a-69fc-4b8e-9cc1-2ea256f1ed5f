import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

import 'osm_data_processor.dart';
import '../../../services/map_cache_manager.dart';
import '../map_caching/map_cache_extension.dart';

/// A custom layer to render OpenStreetMap parks and vegetation in 2.5D
class OSMParksLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final bool isMapMoving;
  final String theme; // Added theme parameter for consistency with buildings
  final bool enableSeasons; // Added option to enable seasonal variations
  final bool enhancedDetail; // Added option for extra detail mode
  final Color parkColor; // Add parkColor field
  final Color forestColor; // Add forestColor field
  
  const OSMParksLayer({
    Key? key,
    this.tiltFactor = 1.0,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.theme = 'vibrant', // Default to vibrant theme
    this.enableSeasons = true, // Enable seasonal coloring by default
    this.enhancedDetail = true, // Enable enhanced detail by default
    this.parkColor = const Color(0xFF43A047),  // Green by default
    this.forestColor = const Color(0xFF2E7D32), // Darker green for forests
  }) : super(key: key);

  @override
  State<OSMParksLayer> createState() => _OSMParksLayerState();
}

class _OSMParksLayerState extends State<OSMParksLayer> with SingleTickerProviderStateMixin {
  final OSMDataProcessor _dataProcessor = OSMDataProcessor();
  final MapCacheManager _cacheManager = MapCacheManager();
  
  Map<String, List<Map<String, dynamic>>> _parksData = {};
  bool _isLoading = true;
  bool _needsRefresh = true;
  String _lastBoundsKey = "";
  String _currentTheme = 'vibrant';
  String _currentSeason = 'summer';
  
  // Track last fetch params to avoid unnecessary fetches
  LatLngBounds? _lastFetchedBounds;
  double _lastFetchedZoom = 0;
  bool _didInitialFetch = false;
  
  // Track current zoom level bucket for optimized rendering
  int _currentZoomBucket = 3;
  
  // Animation controllers for enhanced effects
  late AnimationController _animationController;
  late Animation<double> _windAnimation;
  late Animation<double> _shadowAnimation;
  
  // Advanced vegetation color palettes for different themes
  final Map<String, Map<String, Map<String, Color>>> _vegetationColorPalette = {
    'vibrant': {
      'park': {
        'base': const Color(0xFF43A047), 
        'detail': const Color(0xFF66BB6A),
        'highlight': const Color(0xFFA5D6A7)
      },
      'forest': {
        'base': const Color(0xFF2E7D32), 
        'detail': const Color(0xFF388E3C),
        'highlight': const Color(0xFF81C784)
      },
      'grass': {
        'base': const Color(0xFF66BB6A), 
        'detail': const Color(0xFF81C784),
        'highlight': const Color(0xFFA5D6A7)
      },
      'meadow': {
        'base': const Color(0xFF81C784), 
        'detail': const Color(0xFFA5D6A7),
        'highlight': const Color(0xFFC8E6C9)
      },
      'garden': {
        'base': const Color(0xFF4CAF50), 
        'detail': const Color(0xFF66BB6A),
        'highlight': const Color(0xFF81C784)
      },
      'wood': {
        'base': const Color(0xFF33691E), 
        'detail': const Color(0xFF558B2F),
        'highlight': const Color(0xFF689F38)
      },
      'orchard': {
        'base': const Color(0xFFAED581), 
        'detail': const Color(0xFF8BC34A),
        'highlight': const Color(0xFFDCEDC8)
      },
      'vineyard': {
        'base': const Color(0xFFCDDC39), 
        'detail': const Color(0xFFAFB42B),
        'highlight': const Color(0xFFF0F4C3)
      },
      'default': {
        'base': const Color(0xFF4CAF50), 
        'detail': const Color(0xFF81C784),
        'highlight': const Color(0xFFA5D6A7)
      },
    },
    'dark': {
      'park': {
        'base': const Color(0xFF2E7D32).withAlpha(220), 
        'detail': const Color(0xFF1B5E20).withAlpha(220),
        'highlight': const Color(0xFF388E3C).withAlpha(200)
      },
      'forest': {
        'base': const Color(0xFF1B5E20).withAlpha(220), 
        'detail': const Color(0xFF0A3D12).withAlpha(220),
        'highlight': const Color(0xFF2E7D32).withAlpha(200)
      },
      'grass': {
        'base': const Color(0xFF388E3C).withAlpha(200), 
        'detail': const Color(0xFF2E7D32).withAlpha(200),
        'highlight': const Color(0xFF43A047).withAlpha(180)
      },
      'meadow': {
        'base': const Color(0xFF558B2F).withAlpha(200), 
        'detail': const Color(0xFF33691E).withAlpha(200),
        'highlight': const Color(0xFF7CB342).withAlpha(180)
      },
      'garden': {
        'base': const Color(0xFF2E7D32).withAlpha(220), 
        'detail': const Color(0xFF1B5E20).withAlpha(220),
        'highlight': const Color(0xFF388E3C).withAlpha(200)
      },
      'wood': {
        'base': const Color(0xFF1B5E20).withAlpha(230), 
        'detail': const Color(0xFF0A3D12).withAlpha(230),
        'highlight': const Color(0xFF2E7D32).withAlpha(210)
      },
      'orchard': {
        'base': const Color(0xFF33691E).withAlpha(220), 
        'detail': const Color(0xFF1B5E20).withAlpha(220),
        'highlight': const Color(0xFF558B2F).withAlpha(200)
      },
      'vineyard': {
        'base': const Color(0xFF827717).withAlpha(220), 
        'detail': const Color(0xFF9E9D24).withAlpha(220),
        'highlight': const Color(0xFFAFB42B).withAlpha(200)
      },
      'default': {
        'base': const Color(0xFF2E7D32).withAlpha(220), 
        'detail': const Color(0xFF1B5E20).withAlpha(220),
        'highlight': const Color(0xFF388E3C).withAlpha(200)
      },
    },
    // Monochrome Uber-like theme
    'monochrome': {
      'park': {
        'base': const Color(0xFF4B5563), 
        'detail': const Color(0xFF374151),
        'highlight': const Color(0xFF6B7280)
      },
      'forest': {
        'base': const Color(0xFF374151), 
        'detail': const Color(0xFF1F2937),
        'highlight': const Color(0xFF4B5563)
      },
      'grass': {
        'base': const Color(0xFF6B7280), 
        'detail': const Color(0xFF4B5563),
        'highlight': const Color(0xFF9CA3AF)
      },
      'meadow': {
        'base': const Color(0xFF9CA3AF), 
        'detail': const Color(0xFF6B7280),
        'highlight': const Color(0xFFD1D5DB)
      },
      'garden': {
        'base': const Color(0xFF4B5563), 
        'detail': const Color(0xFF374151),
        'highlight': const Color(0xFF6B7280)
      },
      'wood': {
        'base': const Color(0xFF1F2937), 
        'detail': const Color(0xFF111827),
        'highlight': const Color(0xFF374151)
      },
      'orchard': {
        'base': const Color(0xFF4B5563), 
        'detail': const Color(0xFF374151),
        'highlight': const Color(0xFF6B7280)
      },
      'vineyard': {
        'base': const Color(0xFF6B7280), 
        'detail': const Color(0xFF4B5563),
        'highlight': const Color(0xFF9CA3AF)
      },
      'default': {
        'base': const Color(0xFF4B5563), 
        'detail': const Color(0xFF374151),
        'highlight': const Color(0xFF6B7280)
      },
    },
  };
  
  // Seasonal color variations that are automatically applied based on date
  final Map<String, Map<String, Map<String, Color>>> _seasonalColors = {
    'spring': {
      'park': {
        'base': const Color(0xFF66BB6A), 
        'detail': const Color(0xFF81C784),
        'highlight': const Color(0xFFA5D6A7)
      },
      'forest': {
        'base': const Color(0xFF388E3C), 
        'detail': const Color(0xFF2E7D32),
        'highlight': const Color(0xFF43A047)
      },
      'meadow': {
        'base': const Color(0xFF9CCC65), 
        'detail': const Color(0xFF8BC34A),
        'highlight': const Color(0xFFAED581)
      },
      'grass': {
        'base': const Color(0xFF7CB342), 
        'detail': const Color(0xFF689F38),
        'highlight': const Color(0xFF8BC34A)
      },
      'garden': {
        'base': const Color(0xFF66BB6A), 
        'detail': const Color(0xFF4CAF50),
        'highlight': const Color(0xFF81C784)
      },
    },
    'summer': {
      'park': {
        'base': const Color(0xFF43A047), 
        'detail': const Color(0xFF388E3C),
        'highlight': const Color(0xFF66BB6A)
      },
      'forest': {
        'base': const Color(0xFF2E7D32), 
        'detail': const Color(0xFF1B5E20),
        'highlight': const Color(0xFF388E3C)
      },
      'meadow': {
        'base': const Color(0xFF7CB342), 
        'detail': const Color(0xFF689F38),
        'highlight': const Color(0xFF8BC34A)
      },
      'grass': {
        'base': const Color(0xFF66BB6A), 
        'detail': const Color(0xFF4CAF50),
        'highlight': const Color(0xFF81C784)
      },
      'garden': {
        'base': const Color(0xFF4CAF50), 
        'detail': const Color(0xFF388E3C),
        'highlight': const Color(0xFF66BB6A)
      },
    },
    'autumn': {
      'park': {
        'base': const Color(0xFFAFB42B), 
        'detail': const Color(0xFFC0CA33),
        'highlight': const Color(0xFFD4E157)
      },
      'forest': {
        'base': const Color(0xFFFF8F00), 
        'detail': const Color(0xFFFF6F00),
        'highlight': const Color(0xFFFFA000)
      },
      'meadow': {
        'base': const Color(0xFFFDD835), 
        'detail': const Color(0xFFFBC02D),
        'highlight': const Color(0xFFFFEE58)
      },
      'grass': {
        'base': const Color(0xFFDCE775), 
        'detail': const Color(0xFFD4E157),
        'highlight': const Color(0xFFE6EE9C)
      },
      'garden': {
        'base': const Color(0xFFFFB300), 
        'detail': const Color(0xFFFF8F00),
        'highlight': const Color(0xFFFFCA28)
      },
    },
    'winter': {
      'park': {
        'base': const Color(0xFFCFD8DC), 
        'detail': const Color(0xFFB0BEC5),
        'highlight': const Color(0xFFECEFF1)
      },
      'forest': {
        'base': const Color(0xFF546E7A), 
        'detail': const Color(0xFF455A64),
        'highlight': const Color(0xFF78909C)
      },
      'meadow': {
        'base': const Color(0xFFECEFF1), 
        'detail': const Color(0xFFCFD8DC),
        'highlight': const Color(0xFFF5F5F5)
      },
      'grass': {
        'base': const Color(0xFFE0E0E0), 
        'detail': const Color(0xFFBDBDBD),
        'highlight': const Color(0xFFEEEEEE)
      },
      'garden': {
        'base': const Color(0xFFB0BEC5), 
        'detail': const Color(0xFF90A4AE),
        'highlight': const Color(0xFFCFD8DC)
      },
    },
  };
  
  // Time of day variations
  final Map<String, double> _timeOfDayEffects = {
    'dawn': 0.85,    // Dawn lighting factor
    'morning': 1.0,  // Morning lighting factor
    'noon': 1.1,     // Noon lighting factor (brightest)
    'afternoon': 1.05, // Afternoon lighting factor
    'dusk': 0.9,     // Dusk lighting factor
    'night': 0.7,    // Night lighting factor (darkest)
  };
  
  // Enhanced texture patterns for different vegetation types
  final Map<String, List<List<double>>> _vegetationPatterns = {
    'forest': [
      [0.1, 0.3, 0.5, 0.7, 0.9],
      [0.2, 0.4, 0.6, 0.8, 0.2],
      [0.3, 0.5, 0.7, 0.9, 0.3],
      [0.4, 0.6, 0.8, 0.2, 0.4],
      [0.5, 0.7, 0.9, 0.3, 0.5],
    ],
    'park': [
      [0.2, 0.1, 0.3, 0.2, 0.15],
      [0.1, 0.25, 0.15, 0.3, 0.1],
      [0.25, 0.15, 0.2, 0.1, 0.25],
      [0.15, 0.3, 0.1, 0.25, 0.15],
      [0.3, 0.1, 0.25, 0.15, 0.3],
    ],
    'meadow': [
      [0.05, 0.07, 0.06, 0.08, 0.05],
      [0.07, 0.06, 0.05, 0.07, 0.06],
      [0.06, 0.08, 0.07, 0.06, 0.08],
      [0.08, 0.05, 0.06, 0.08, 0.07],
      [0.05, 0.07, 0.08, 0.05, 0.06],
    ],
  };
  
  @override
  void initState() {
    super.initState();
    
    _currentTheme = widget.theme;
    _currentZoomBucket = _getZoomBucket(widget.zoomLevel);
    
    // Detect current season
    if (widget.enableSeasons) {
      _detectCurrentSeason();
    }
    
    // Initialize animation controllers for enhanced effects
    _animationController = AnimationController(
      duration: const Duration(seconds: 3), 
      vsync: this,
    );
    
    _windAnimation = Tween<double>(
      begin: -0.05,
      end: 0.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _shadowAnimation = Tween<double>(
      begin: 0.0,
      end: 0.3,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    // Loop the animation for continuous effect
    _animationController.repeat(reverse: true);
    
    _fetchParksData();
  }
  
  // Get the current zoom bucket (0-5) for optimization
  int _getZoomBucket(double zoomLevel) {
    if (zoomLevel < 6) return 0;  // Global view
    if (zoomLevel < 9) return 1;  // Continental view
    if (zoomLevel < 12) return 2; // Country view
    if (zoomLevel < 15) return 3; // Regional view
    if (zoomLevel < 18) return 4; // Neighborhood view
    return 5;                     // Street view
  }
  
  // Detect current season based on date and possibly location
  void _detectCurrentSeason() {
    final now = DateTime.now();
    final month = now.month;
    
    // Northern hemisphere seasons (could be inverted for southern hemisphere)
    if (month >= 3 && month <= 5) {
      _currentSeason = 'spring';
    } else if (month >= 6 && month <= 8) {
      _currentSeason = 'summer';
    } else if (month >= 9 && month <= 11) {
      _currentSeason = 'autumn';
    } else {
      _currentSeason = 'winter';
    }
    
    debugPrint('Detected season: $_currentSeason for vegetation rendering');
  }
  
  // Determine time of day for lighting effects
  String _getTimeOfDay() {
    final now = DateTime.now();
    final hour = now.hour;
    
    if (hour >= 5 && hour < 8) {
      return 'dawn';
    } else if (hour >= 8 && hour < 11) {
      return 'morning';
    } else if (hour >= 11 && hour < 14) {
      return 'noon';
    } else if (hour >= 14 && hour < 17) {
      return 'afternoon';
    } else if (hour >= 17 && hour < 20) {
      return 'dusk';
    } else {
      return 'night';
    }
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  void didUpdateWidget(OSMParksLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Check if theme changed
    if (oldWidget.theme != widget.theme) {
      setState(() {
        _currentTheme = widget.theme;
      });
    }
    
    // Get a key to identify current map bounds
    final newBoundsKey = _getBoundsKey();
    
    // Update current zoom bucket
    _currentZoomBucket = _getZoomBucket(widget.zoomLevel);
    
    // Fetch new data when map bounds change significantly or zoom changes
    if (oldWidget.visibleBounds != widget.visibleBounds || 
        oldWidget.zoomLevel != widget.zoomLevel ||
        _lastBoundsKey != newBoundsKey ||
        _lastFetchedZoom.round() != widget.zoomLevel.round()) {
      
      _needsRefresh = true;
      
      // If map is actively moving, delay the fetch to avoid too many API calls
      if (widget.isMapMoving) {
        _delayedFetch();
      } else {
        _fetchParksData();
      }
    }
    
    // Update seasonal colors if they were toggled
    if (oldWidget.enableSeasons != widget.enableSeasons && widget.enableSeasons) {
      _detectCurrentSeason();
    }
  }
  
  // Get a key to identify current map bounds, with reduced precision for fewer unnecessary refreshes
  String _getBoundsKey() {
    final sw = widget.visibleBounds.southWest;
    final ne = widget.visibleBounds.northEast;
    
    // Reduce precision for bounds (3 decimal places ≈ 100m accuracy)
    final key = '${sw.latitude.toStringAsFixed(3)},${sw.longitude.toStringAsFixed(3)}_'
               '${ne.latitude.toStringAsFixed(3)},${ne.longitude.toStringAsFixed(3)}_'
               '${widget.zoomLevel.toStringAsFixed(1)}';
    return key;
  }
  
  // Delay fetch to prevent excessive API calls during continuous panning/zooming
  void _delayedFetch() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted && _needsRefresh) {
        _fetchParksData();
      }
    });
  }
  
  void _fetchParksData() async {
    setState(() {
      _isLoading = true;
    });
    
    // Update bounds key
    _lastBoundsKey = _getBoundsKey();
    
    // Use the map bounds to fetch data
    final southwest = widget.visibleBounds.southWest;
    final northeast = widget.visibleBounds.northEast;
    
    // Calculate detail level based on zoom (similar to building layer)
    // Zoom levels typically range from 1-20, we want detailLevel from 0.1-1.0
    final double detailLevel = math.min(1.0, math.max(0.1, (widget.zoomLevel - 5) / 15));
    
    // Store zoom level for tracking changes
    _lastFetchedZoom = widget.zoomLevel;
    _lastFetchedBounds = widget.visibleBounds;
    
    // Pass detail level to data processor
    final parksData = await _dataProcessor.fetchParksData(
      southwest, 
      northeast,
      detailLevel: detailLevel
    );
    
    if (mounted) {
      setState(() {
        _parksData = parksData;
        _isLoading = false;
        _needsRefresh = false;
        _didInitialFetch = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Return empty container while loading or if data is empty
    if (_isLoading || _parksData.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // Get the appropriate color palette based on theme and season
    final Map<String, Color> parkTypeColors = {};
    
    // Start with theme colors
    if (_vegetationColorPalette.containsKey(_currentTheme)) {
      // Convert the nested map structure to a flat map for the painter
      _vegetationColorPalette[_currentTheme]!.forEach((type, colors) {
        parkTypeColors[type] = colors['base']!;
      });
    }
    
    // Apply seasonal colors if enabled
    if (widget.enableSeasons && _seasonalColors.containsKey(_currentSeason)) {
      _seasonalColors[_currentSeason]!.forEach((type, colors) {
        parkTypeColors[type] = colors['base']!;
      });
    }
    
    // Add default colors for common types if not already set
    parkTypeColors.putIfAbsent('park', () => widget.parkColor.withOpacity(0.65));
    parkTypeColors.putIfAbsent('forest', () => widget.forestColor.withOpacity(0.75));
    parkTypeColors.putIfAbsent('grass', () => const Color(0xFF66BB6A).withOpacity(0.55));
    parkTypeColors.putIfAbsent('meadow', () => const Color(0xFF81C784).withOpacity(0.6));
    parkTypeColors.putIfAbsent('garden', () => const Color(0xFF4CAF50).withOpacity(0.7));
    parkTypeColors.putIfAbsent('wood', () => const Color(0xFF33691E).withOpacity(0.75));
    
    return AnimatedBuilder(
      animation: _windAnimation,
      builder: (context, child) {
        return CustomPaint(
          painter: OSMParksPainter(
            parks: _parksData['parks'] ?? [],
            trees: _parksData['trees'] ?? [],
            parkColors: parkTypeColors,
            tiltFactor: widget.tiltFactor,
            zoomLevel: widget.zoomLevel,
            mapBounds: widget.visibleBounds,
            windFactor: _windAnimation.value,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

/// Custom painter to render parks and vegetation in 2.5D
class OSMParksPainter extends CustomPainter {
  final List<dynamic> parks;
  final List<dynamic> trees;
  final Map<String, Color> parkColors;
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds mapBounds;
  final double windFactor;
  
  OSMParksPainter({
    required this.parks,
    required this.trees,
    required this.parkColors,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.mapBounds,
    required this.windFactor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Remove any rendering skips based on tilt factor
    // Continue with rendering even if tilt is zero
    
    // Bounds conversion helpers
    final sw = mapBounds.southWest;
    final ne = mapBounds.northEast;
    final mapWidth = ne.longitude - sw.longitude;
    final mapHeight = ne.latitude - sw.latitude;
    
    // Performance optimization: Skip tiny parks that would be just a few pixels
    final minPixelSize = 3.0; // Don't render areas smaller than this many pixels
    final metersPerPixelLat = (mapHeight * 111320) / size.height; // approx conversion
    
    // First pass: Draw base layers of parks with slight shadows for 3D effect
    for (final park in parks) {
      final List<LatLng> points = park['points'] as List<LatLng>;
      if (points.length < 3) continue; // Need at least 3 points for an area
      
      final String parkType = park['type'] as String;
      final double elevation = (park['elevation'] as double) * tiltFactor;
      
      // Get color for this park type or use default
      final Color parkColor = parkColors[parkType] ?? 
                              parkColors['park'] ?? 
                              const Color(0xFF4CAF50).withOpacity(0.5);
      
      // Convert LatLng points to screen coordinates
      final List<Offset> screenPoints = points.map((latLng) {
        // Map from LatLng to screen coordinates
        final double x = (latLng.longitude - sw.longitude) / mapWidth * size.width;
        final double y = (1 - (latLng.latitude - sw.latitude) / mapHeight) * size.height;
        return Offset(x, y - elevation); // Apply slight elevation for 2.5D effect
      }).toList();
      
      // Quick check for minimum rendering size (performance optimization)
      final Rect bounds = _getBounds(screenPoints);
      if (bounds.width < minPixelSize || bounds.height < minPixelSize) {
        continue; // Skip very small areas
      }
      
      // First draw a subtle shadow for 3D effect - only if we have tilt
      if (tiltFactor > 0.3 && elevation > 0.05) {
        _drawParkShadow(canvas, screenPoints, parkColor, elevation);
      }
      
      // Then draw the actual park area - always do this in both 2D and 2.5D modes
      _drawParkArea(canvas, screenPoints, parkColor, parkType);
    }
    
    // Second pass: Add surface details if zoom level is appropriate
    if (zoomLevel >= 15.0) {
      for (final park in parks) {
        final List<LatLng> points = park['points'] as List<LatLng>;
        if (points.length < 3) continue;
        
        final String parkType = park['type'] as String;
        final double elevation = (park['elevation'] as double) * tiltFactor;
        
        // Get color for this park type
        final Color parkColor = parkColors[parkType] ?? 
                                parkColors['park'] ?? 
                                const Color(0xFF4CAF50).withOpacity(0.5);
        
        final List<Offset> screenPoints = points.map((latLng) {
          final double x = (latLng.longitude - sw.longitude) / mapWidth * size.width;
          final double y = (1 - (latLng.latitude - sw.latitude) / mapHeight) * size.height;
          return Offset(x, y - elevation);
        }).toList();
        
        // Add detailed vegetation patterns
        final Rect bounds = _getBounds(screenPoints);
        if (bounds.width >= 20 && bounds.height >= 20) { // Only add details to larger areas
          _addParkDetails(canvas, screenPoints, parkColor, parkType);
        }
      }
    }
    
    // Third pass: Draw individual trees if zoom level is high enough
    // Changed to render trees even with low tilt for consistent experience
    if (zoomLevel >= 16.0 && trees.isNotEmpty) {
      // Limit the number of trees for performance
      final treesToDraw = zoomLevel > 18.0 ? trees : trees.take(50).toList();
      _drawTrees(canvas, treesToDraw, size);
    }
  }
  
  // Helper function to get the bounding box of a list of points
  Rect _getBounds(List<Offset> points) {
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;
    
    for (final point in points) {
      minX = math.min(minX, point.dx);
      minY = math.min(minY, point.dy);
      maxX = math.max(maxX, point.dx);
      maxY = math.max(maxY, point.dy);
    }
    
    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }
  
  // Draw a subtle shadow for 3D effect
  void _drawParkShadow(Canvas canvas, List<Offset> points, Color baseColor, double elevation) {
    // Create shadow path with slight offset
    final Path shadowPath = Path();
    shadowPath.moveTo(points[0].dx + elevation * 1.5, points[0].dy + elevation * 2.0);
    
    for (int i = 1; i < points.length; i++) {
      shadowPath.lineTo(points[i].dx + elevation * 1.5, points[i].dy + elevation * 2.0);
    }
    
    shadowPath.close();
    
    // Draw shadow with semi-transparent black
    final Paint shadowPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.black.withOpacity(0.2 + elevation * 0.2);
    
    canvas.drawPath(shadowPath, shadowPaint);
  }
  
  /// Draw a park or forest area
  void _drawParkArea(Canvas canvas, List<Offset> points, Color color, String parkType) {
    // Create path for the park area
    final Path parkPath = Path();
    parkPath.moveTo(points[0].dx, points[0].dy);
    
    for (int i = 1; i < points.length; i++) {
      parkPath.lineTo(points[i].dx, points[i].dy);
    }
    
    parkPath.close(); // Close the path to form an area
    
    // Draw park area fill
    final Paint fillPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = color;
    
    canvas.drawPath(parkPath, fillPaint);
    
    // Draw a very subtle border for definition
    final Paint borderPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5
      ..color = color.withAlpha(150);
    
    canvas.drawPath(parkPath, borderPaint);
  }
  
  /// Add detailed vegetation patterns to parks
  void _addParkDetails(Canvas canvas, List<Offset> points, Color baseColor, String parkType) {
    // Skip for certain types or if too few points
    if (points.length < 5) return;
    
    // Calculate a simple bounding box for the park
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;
    
    for (final point in points) {
      minX = math.min(minX, point.dx);
      minY = math.min(minY, point.dy);
      maxX = math.max(maxX, point.dx);
      maxY = math.max(maxY, point.dy);
    }
    
    // Create a path to clip detail rendering to the park area
    final Path clipPath = Path();
    clipPath.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      clipPath.lineTo(points[i].dx, points[i].dy);
    }
    clipPath.close();
    
    // Apply clip to limit drawing to park area
    canvas.save();
    canvas.clipPath(clipPath);
    
    // Choose pattern based on park type
    if (parkType == 'forest' || parkType == 'wood') {
      _addForestPattern(canvas, Rect.fromLTRB(minX, minY, maxX, maxY), baseColor);
    } else if (parkType == 'park' || parkType == 'garden') {
      _addParkPattern(canvas, Rect.fromLTRB(minX, minY, maxX, maxY), baseColor);
    } else if (parkType == 'grass' || parkType == 'meadow') {
      _addGrassPattern(canvas, Rect.fromLTRB(minX, minY, maxX, maxY), baseColor);
    }
    
    canvas.restore();
  }
  
  /// Add a forest-specific pattern
  void _addForestPattern(Canvas canvas, Rect bounds, Color baseColor) {
    final random = math.Random(42); // Fixed seed for consistent pattern
    final int treeDensity = 20 + (zoomLevel ~/ 2); // Increase density with zoom
    
    final double spacing = math.min(bounds.width, bounds.height) / math.sqrt(treeDensity);
    
    // Enhanced forest pattern with more varied tree colors
    final List<Color> treeColors = [
      HSLColor.fromColor(baseColor).withLightness(
          (HSLColor.fromColor(baseColor).lightness + 0.1).clamp(0.0, 1.0)).toColor(),
      HSLColor.fromColor(baseColor).withLightness(
          (HSLColor.fromColor(baseColor).lightness + 0.05).clamp(0.0, 1.0)).toColor(),
      HSLColor.fromColor(baseColor).withLightness(
          (HSLColor.fromColor(baseColor).lightness - 0.05).clamp(0.0, 1.0)).toColor(),
    ];
    
    // Draw small tree symbols
    for (int i = 0; i < treeDensity; i++) {
      final double x = bounds.left + random.nextDouble() * bounds.width;
      final double y = bounds.top + random.nextDouble() * bounds.height;
      
      // Draw simple tree dot with varied colors
      final Paint treePaint = Paint()
        ..color = treeColors[random.nextInt(treeColors.length)]
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(Offset(x, y), 1.5, treePaint);
    }
  }
  
  /// Add a park-specific pattern
  void _addParkPattern(Canvas canvas, Rect bounds, Color baseColor) {
    final random = math.Random(24); // Different seed from forest
    
    // Create a grid pattern
    final gridSize = 20.0;
    final Paint linePaint = Paint()
      ..color = baseColor.withOpacity(0.3)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;
    
    // Enhanced colors for park details
    final List<Color> flowerColors = [
      const Color(0xFFFFF176).withOpacity(0.7),  // Yellow flowers
      const Color(0xFFFFCDD2).withOpacity(0.6),  // Pink flowers
      const Color(0xFFB3E5FC).withOpacity(0.6),  // Blue flowers
      const Color(0xFFE1BEE7).withOpacity(0.6),  // Purple flowers
      const Color(0xFFFFCCBC).withOpacity(0.6),  // Orange flowers
    ];
    
    // Draw more interesting scattered dots for flowers or shrubs
    for (int i = 0; i < 40; i++) {  // Increased from 30 to 40 for more flowers
      final double x = bounds.left + random.nextDouble() * bounds.width;
      final double y = bounds.top + random.nextDouble() * bounds.height;
      
      // Random dot color - higher chance of flowers for more color
      final Paint dotPaint = Paint()
        ..color = random.nextDouble() > 0.6  // 40% chance of flower vs 30% before
            ? flowerColors[random.nextInt(flowerColors.length)]
            : HSLColor.fromColor(baseColor).withLightness(
                (HSLColor.fromColor(baseColor).lightness + 0.15).clamp(0.0, 1.0)).toColor()
        ..style = PaintingStyle.fill;
      
      // Small dots for detailed park features
      canvas.drawCircle(Offset(x, y), random.nextDouble() * 1.2 + 0.5, dotPaint);
    }
  }
  
  /// Add a grass-specific pattern with more varied grass
  void _addGrassPattern(Canvas canvas, Rect bounds, Color baseColor) {
    final random = math.Random(36); // Different seed again
    
    // More varied grass shades
    final List<Color> grassShades = [
      HSLColor.fromColor(baseColor).withLightness(
          (HSLColor.fromColor(baseColor).lightness + 0.15).clamp(0.0, 1.0)).toColor().withOpacity(0.3),
      HSLColor.fromColor(baseColor).withLightness(
          (HSLColor.fromColor(baseColor).lightness + 0.05).clamp(0.0, 1.0)).toColor().withOpacity(0.3),
      HSLColor.fromColor(baseColor).withLightness(
          (HSLColor.fromColor(baseColor).lightness).clamp(0.0, 1.0)).toColor().withOpacity(0.3),
    ];
    
    // Draw more grass-like lines with varied colors and lengths
    for (int i = 0; i < 60; i++) {  // Increased from 40 to 60 for more density
      final double x = bounds.left + random.nextDouble() * bounds.width;
      final double y = bounds.top + random.nextDouble() * bounds.height;
      
      final Paint grassPaint = Paint()
        ..color = grassShades[random.nextInt(grassShades.length)]
        ..strokeWidth = 0.5 + random.nextDouble() * 0.4  // More varied thickness
        ..style = PaintingStyle.stroke;
      
      // Simple grass blade with more varied angles and lengths
      final double angle = random.nextDouble() * math.pi;
      final double length = 2.0 + random.nextDouble() * 4.0;  // Longer grass blades
      
      canvas.drawLine(
        Offset(x, y),
        Offset(x + math.cos(angle) * length, y + math.sin(angle) * length),
        grassPaint
      );
    }
  }
  
  /// Draw individual trees (for high zoom levels)
  void _drawTrees(Canvas canvas, List<dynamic> trees, Size size) {
    // Skip if no trees or zoom level too low, but don't restrict based on tilt
    if (trees.isEmpty || zoomLevel < 16.0) return;
    
    // Limit number of trees to draw for performance
    final maxTrees = math.min(trees.length, 100);
    final treesToDraw = trees.take(maxTrees).toList();
    
    // Bounds conversion helpers
    final sw = mapBounds.southWest;
    final ne = mapBounds.northEast;
    final mapWidth = ne.longitude - sw.longitude;
    final mapHeight = ne.latitude - sw.latitude;
    
    // Draw each tree
    for (final tree in treesToDraw) {
      final LatLng location = tree['location'] as LatLng;
      
      // Convert LatLng to screen coordinates
      final double x = (location.longitude - sw.longitude) / mapWidth * size.width;
      final double y = (1 - (location.latitude - sw.latitude) / mapHeight) * size.height;
      
      // Apply wind effect to tree position
      final double windOffset = windFactor * 3.0;
      
      // Draw simple tree with trunk and foliage
      _drawTreeSymbol(canvas, Offset(x + windOffset, y), 5.0 * math.min(1.0, (zoomLevel - 15.0) / 3.0));
    }
  }
  
  /// Draw a simple tree symbol with more detail
  void _drawTreeSymbol(Canvas canvas, Offset position, double size) {
    // Apply scaling based on zoom level for more natural transitions
    final scaleFactor = math.min(1.0, (zoomLevel - 15.0) / 3.0);
    final adjustedSize = size * scaleFactor;
    
    // Skip tiny trees for performance
    if (adjustedSize < 1.5) return;
    
    // Tree height - use tilt factor to create 3D effect
    final double height = adjustedSize * (1.0 + tiltFactor);
    
    // Vary tree colors slightly based on position for natural variation
    final random = math.Random(position.dx.toInt() * 1000 + position.dy.toInt());
    
    // Tree colors with more variety and realism
    final List<Color> trunkColors = [
      const Color(0xFF6D4C41),  // Standard brown
      const Color(0xFF5D4037),  // Darker brown
      const Color(0xFF8D6E63),  // Lighter brown
    ];
    
    final List<Color> foliageColors = [
      const Color(0xFF388E3C),  // Medium green
      const Color(0xFF2E7D32),  // Dark green
      const Color(0xFF43A047),  // Light green
      const Color(0xFF33691E),  // Forest green
    ];
    
    // Select colors with slight randomness
    final Paint trunkPaint = Paint()
      ..color = trunkColors[random.nextInt(trunkColors.length)]
      ..style = PaintingStyle.fill;
    
    final Paint foliagePaint = Paint()
      ..color = foliageColors[random.nextInt(foliageColors.length)]
      ..style = PaintingStyle.fill;
    
    // Draw shadow first (if tilt is significant)
    if (tiltFactor > 0.3) {
      final Paint shadowPaint = Paint()
        ..color = Colors.black.withOpacity(0.3)
        ..style = PaintingStyle.fill;
      
      canvas.drawOval(
        Rect.fromCenter(
          center: Offset(position.dx + height * 0.3, position.dy + height * 0.15),
          width: adjustedSize * 0.8,
          height: adjustedSize * 0.3
        ),
        shadowPaint
      );
    }
    
    // Draw trunk - adjust based on tilt for 3D perspective
    final trunkWidth = math.max(1.0, adjustedSize * 0.25);
    final trunkHeight = height * 0.7;
    
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(position.dx, position.dy + adjustedSize * 0.1),
        width: trunkWidth,
        height: trunkHeight
      ),
      trunkPaint
    );
    
    // Draw foliage - more complex for larger trees
    if (adjustedSize > 3.0) {
      // 3D effect - multiple stacked layers of foliage with offset
      final baseFoliageSize = adjustedSize * 1.2;
      
      // Bottom foliage layer
      canvas.drawOval(
        Rect.fromCenter(
          center: Offset(position.dx, position.dy - height * 0.25),
          width: baseFoliageSize,
          height: baseFoliageSize * 0.8
        ),
        foliagePaint
      );
      
      // Middle foliage layer (slightly smaller and higher)
      canvas.drawOval(
        Rect.fromCenter(
          center: Offset(position.dx - tiltFactor * 0.5, position.dy - height * 0.4),
          width: baseFoliageSize * 0.85,
          height: baseFoliageSize * 0.7
        ),
        Paint()..color = foliagePaint.color.withOpacity(0.9)
      );
      
      // Top foliage layer (smallest and highest)
      canvas.drawOval(
        Rect.fromCenter(
          center: Offset(position.dx - tiltFactor * 1.0, position.dy - height * 0.55),
          width: baseFoliageSize * 0.7,
          height: baseFoliageSize * 0.6
        ),
        Paint()..color = foliagePaint.color.withOpacity(0.85)
      );
      
      // Add highlight for sun effect if tree is large enough
      if (adjustedSize > 6.0) {
        canvas.drawOval(
          Rect.fromCenter(
            center: Offset(position.dx - tiltFactor * 0.5 + baseFoliageSize * 0.2, 
                          position.dy - height * 0.35 - baseFoliageSize * 0.15),
            width: baseFoliageSize * 0.3,
            height: baseFoliageSize * 0.2
          ),
          Paint()..color = HSLColor.fromColor(foliagePaint.color)
                          .withLightness(0.7)
                          .toColor()
                          .withOpacity(0.3)
        );
      }
    } else {
      // Simpler tree for smaller sizes
      canvas.drawOval(
        Rect.fromCenter(
          center: Offset(position.dx, position.dy - height * 0.3),
          width: adjustedSize * 1.2,
          height: adjustedSize * 0.9
        ),
        foliagePaint
      );
    }
  }
  
  @override
  bool shouldRepaint(OSMParksPainter oldDelegate) {
    return oldDelegate.parks != parks ||
           oldDelegate.trees != trees ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.mapBounds != mapBounds ||
           oldDelegate.windFactor != windFactor;
  }
} 