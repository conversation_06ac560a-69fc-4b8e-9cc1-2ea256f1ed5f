import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

// Extension to add normalize method to Offset
extension OffsetExtensions on Offset {
  Offset normalize() {
    final double magnitude = distance;
    if (magnitude == 0) return Offset.zero;
    return Offset(dx / magnitude, dy / magnitude);
  }
}

/// Custom painter to render OpenStreetMap roads with enhanced 2.5D styling
class OSMRoadsPainter extends CustomPainter {
  final List<Map<String, dynamic>> roads;
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final int zoomBucket;
  final bool showDetails;
  final double? animationValue; // Optional animation value for traffic flow effects
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  final MapCamera? mapCamera;
  
  OSMRoadsPainter({
    required this.roads,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    required this.theme,
    required this.zoomBucket,
    required this.showDetails,
    this.animationValue,
    this.mapCamera,
  });

  // Road color palettes for different themes
  final Map<String, Map<String, Color>> _roadColorPalette = {
    'vibrant': {
      'motorway': const Color(0xFFE91E63).withOpacity(0.8),  // Pink/magenta for motorways
      'trunk': const Color(0xFFEC407A).withOpacity(0.7),     // Lighter pink for trunk roads
      'primary': const Color(0xFFF48FB1).withOpacity(0.65),   // Pale pink for primary roads
      'secondary': const Color(0xFFF8BBD0).withOpacity(0.6), // Very light pink for secondary
      'tertiary': const Color(0xFFFFFFFF).withOpacity(0.5),  // White with more opacity for tertiary
      'residential': const Color(0xFFECEFF1).withOpacity(0.4), // Light gray for residential
      'service': const Color(0xFFCFD8DC).withOpacity(0.35),   // Light gray for service roads
      'unclassified': const Color(0xFFBDBDBD).withOpacity(0.3), // Medium gray for unclassified
      'living_street': const Color(0xFFB0BEC5).withOpacity(0.35), // Blue-gray for living streets
      'pedestrian': const Color(0xFFAED581).withOpacity(0.35),  // Light green for pedestrian ways
      'footway': const Color(0xFFCE93D8).withOpacity(0.35),     // Light purple for footways
      'cycleway': const Color(0xFF4DB6AC).withOpacity(0.35),    // Teal for cycleways
      'path': const Color(0xFFD7CCC8).withOpacity(0.35),        // Beige for paths
      'track': const Color(0xFFBCAAA4).withOpacity(0.35),       // Brown for tracks
    },
    'dark': {
      'motorway': const Color(0xFFC2185B).withOpacity(0.8),  // Darker pink for motorways
      'trunk': const Color(0xFFD81B60).withOpacity(0.7),    // Dark pink for trunk roads
      'primary': const Color(0xFFE91E63).withOpacity(0.65),  // Pink for primary roads
      'secondary': const Color(0xFFEC407A).withOpacity(0.6), // Light pink for secondary
      'tertiary': const Color(0xFFF48FB1).withOpacity(0.5), // Very light pink for tertiary
      'residential': const Color(0xFF78909C).withOpacity(0.4), // Dark gray for residential
      'service': const Color(0xFF607D8B).withOpacity(0.35),  // Dark gray for service roads
      'unclassified': const Color(0xFF546E7A).withOpacity(0.3), // Dark gray for unclassified
      'living_street': const Color(0xFF455A64).withOpacity(0.35), // Dark blue-gray for living streets
      'pedestrian': const Color(0xFF689F38).withOpacity(0.35),  // Dark green for pedestrian ways
      'footway': const Color(0xFF8E24AA).withOpacity(0.35),     // Dark purple for footways
      'cycleway': const Color(0xFF00897B).withOpacity(0.35),    // Dark teal for cycleways
      'path': const Color(0xFF8D6E63).withOpacity(0.35),        // Dark beige for paths
      'track': const Color(0xFF795548).withOpacity(0.35),       // Dark brown for tracks
    },
    'monochrome': {
      'motorway': const Color(0xFFEEEEEE).withOpacity(0.8),  // White for motorways
      'trunk': const Color(0xFFE0E0E0).withOpacity(0.7),    // Light gray for trunk roads
      'primary': const Color(0xFFBDBDBD).withOpacity(0.65),  // Medium gray for primary roads
      'secondary': const Color(0xFFAAAAAA).withOpacity(0.6), // Gray for secondary
      'tertiary': const Color(0xFF9E9E9E).withOpacity(0.5), // Darker gray for tertiary
      'residential': const Color(0xFF757575).withOpacity(0.4), // Dark gray for residential
      'service': const Color(0xFF616161).withOpacity(0.35),  // Darker gray for service roads
      'unclassified': const Color(0xFF424242).withOpacity(0.3), // Very dark gray for unclassified
      'living_street': const Color(0xFF616161).withOpacity(0.35), // Dark gray for living streets
      'pedestrian': const Color(0xFF9E9E9E).withOpacity(0.35),  // Medium gray for pedestrian ways
      'footway': const Color(0xFFBDBDBD).withOpacity(0.35),     // Light gray for footways
      'cycleway': const Color(0xFFE0E0E0).withOpacity(0.35),    // Very light gray for cycleways
      'path': const Color(0xFF757575).withOpacity(0.35),        // Dark gray for paths
      'track': const Color(0xFF616161).withOpacity(0.35),       // Darker gray for tracks
    },
  };

  @override
  void paint(Canvas canvas, Size size) {
    if (roads.isEmpty) return;
    
    // Sort roads by importance in DESCENDING order (major roads first)
    final sortedRoads = List<Map<String, dynamic>>.from(roads);
    sortedRoads.sort((a, b) {
      // Get road types for comparison
      final String typeA = _getRoadType(a);
      final String typeB = _getRoadType(b);
      
      // Get importance rank (higher numbers = more important roads like motorways)
      final int rankA = _getRoadImportanceRank(typeA);
      final int rankB = _getRoadImportanceRank(typeB);
      
      // Sort by importance rank in descending order (draw major roads first)
      return rankB.compareTo(rankA);
    });
    
    // Performance optimization: limit the number of roads to render based on zoom level
    // and prioritize important roads
    int maxRoads = 500; // Default limit
    
    // More aggressive limits at higher zoom levels where roads are more detailed
    if (zoomLevel > 18) {
      maxRoads = 150; 
    } else if (zoomLevel > 17) {
      maxRoads = 250;
    } else if (zoomLevel > 16) {
      maxRoads = 350;
    }
    
    // Filter visible roads first to improve performance
    final List<Map<String, dynamic>> visibleRoads = sortedRoads.where((road) {
      final List points = road['points'] as List;
      if (points.length < 2) return false;
      
      // Quick check for at least one point in visible bounds
      // We don't need to check every point, just sample a few
      final int sampleStep = math.max(1, points.length ~/ 4); // Check at most 4 points
      for (int i = 0; i < points.length; i += sampleStep) {
        final LatLng latLng = points[i] as LatLng;
        if (visibleBounds.contains(latLng)) {
          return true;
        }
      }
      return false;
    }).toList();
    
    // If we still have too many roads, limit them based on importance
    final List<Map<String, dynamic>> roadsToDraw = visibleRoads.length > maxRoads
        ? visibleRoads.sublist(0, maxRoads)
        : visibleRoads;
    
    // Draw each road with enhanced 2.5D effects
    for (final road in roadsToDraw) {
      _drawEnhancedRoad(canvas, size, road);
    }
  }
  
  // Get road type from road data
  String _getRoadType(Map<String, dynamic> road) {
    final Map<String, dynamic>? tags = road['tags'] as Map<String, dynamic>?;
    if (tags == null) return 'unclassified';
    
    if (tags.containsKey('highway')) {
      return tags['highway'] as String;
    }
    
    return 'unclassified';
  }
  
  // Determine importance rank for sorting
  int _getRoadImportanceRank(String roadType) {
    switch (roadType) {
      case 'motorway':
        return 10;
      case 'trunk':
        return 9;
      case 'primary':
        return 8;
      case 'secondary':
        return 7;
      case 'tertiary':
        return 6;
      case 'residential':
        return 5;
      case 'unclassified':
        return 4;
      case 'service':
        return 3;
      case 'living_street':
        return 2;
      case 'footway':
      case 'pedestrian':
      case 'cycleway':
        return 1;
      default:
        return 0;
    }
  }
  
  /// Project a geographic point to screen coordinates
  Offset _projectPoint(double lat, double lng, Size size) {
    // CRITICAL FIX: Always use MapCamera when available for projection during map movement
    if (mapCamera != null) {
      try {
        final screenPoint = mapCamera!.latLngToScreenPoint(LatLng(lat, lng));
        return Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
      } catch (e) {
        debugPrint('Error projecting road point with camera: $e');
        // Fall back to simple projection on error
      }
    }
    
    // Fall back to simple linear projection if no camera or camera projection fails
    final sw = visibleBounds.southWest;
    final ne = visibleBounds.northEast;
    
    // Calculate x position based on longitude
    final double x = size.width * (lng - sw.longitude) / (ne.longitude - sw.longitude);
    
    // Calculate y position based on latitude
    final double y = size.height * (1.0 - (lat - sw.latitude) / (ne.latitude - sw.latitude));
    
    return Offset(x, y);
  }
  
  // Draw a single road with enhanced 2.5D effects
  void _drawEnhancedRoad(Canvas canvas, Size size, Map<String, dynamic> road) {
    final List points = road['points'] as List;
    if (points.length < 2) return; // Need at least 2 points for a line
    
    // Performance optimization: skip very long roads at high zoom levels
    if (zoomLevel > 17 && points.length > 100) {
      // For long roads at high zoom, simplify by sampling fewer points
      final List<LatLng> simplifiedPoints = [];
      final int sampleRate = (points.length / 50).ceil(); // Aim for about 50 points max
      
      for (int i = 0; i < points.length; i += sampleRate) {
        simplifiedPoints.add(points[i] as LatLng);
      }
      
      // Make sure we include the last point
      if (points.isNotEmpty && simplifiedPoints.last != points.last) {
        simplifiedPoints.add(points.last as LatLng);
      }
      
      // Continue drawing with simplified points
      _drawRoadWithPoints(canvas, size, road, simplifiedPoints);
    } else {
      // Draw with all points for lower zoom or shorter roads
      _drawRoadWithPoints(canvas, size, road, points.cast<LatLng>());
    }
  }
  
  // Draw a road with the given points
  void _drawRoadWithPoints(Canvas canvas, Size size, Map<String, dynamic> road, List<LatLng> points) {
    // Extract road properties
    final Map<String, dynamic>? tags = road['tags'] as Map<String, dynamic>?;
    final String roadType = _getRoadType(road);
    
    // Get road color based on road type and theme
    final Color roadColor = _getRoadColor(roadType);
    
    // Get road width based on road type and zoom level
    final double roadWidth = _getRoadWidth(roadType);
    
    // Calculate elevation effect based on road type and tilt factor
    final double elevationEffect = _calculateRoadElevation(roadType, tiltFactor);
    
    // Convert geographic coordinates to screen coordinates with elevation
    final List<Offset> screenPoints = [];
    for (int i = 0; i < points.length; i++) {
      final LatLng point = points[i] as LatLng;
      final Offset basePoint = _projectPoint(point.latitude, point.longitude, size);
      
      // Apply elevation effect - elevate roads slightly above the base map
      // Major roads are elevated higher than minor roads
      Offset elevatedPoint = Offset(
        basePoint.dx, 
        basePoint.dy - elevationEffect
      );
      
      screenPoints.add(elevatedPoint);
    }
    
    // Create path for the road
    final Path roadPath = _createRoadPath(screenPoints, roadWidth);
    
    // Create enhanced road effect with subtle 3D feeling
    _drawRoadWithEnhancedEffects(canvas, roadPath, screenPoints, roadType, roadColor, roadWidth, elevationEffect);
    
    // Draw road names if zoom level is high enough
    if (showDetails && zoomLevel >= 16 && tags != null && tags.containsKey('name')) {
      // Skip drawing road names to remove all street labels
      // _drawEnhancedRoadName(canvas, screenPoints, tags['name'] as String, roadType, roadWidth);
    }
    
    // Draw lane markings and traffic flow for major roads at high zoom levels
    if (_isMainRoad(roadType) && zoomLevel >= 16) {
      _drawEnhancedLaneMarkings(canvas, roadPath, screenPoints, roadType, roadWidth);
    }
  }
  
  // Calculate road elevation based on road type and tilt factor
  double _calculateRoadElevation(String roadType, double tiltFactor) {
    // Base elevation effect scales with tilt factor
    double baseElevation = tiltFactor * 2.0;
    
    // Apply zoom-based enhancement for 3D effect
    double zoomEnhancement = _getZoomElevationFactor();
    baseElevation *= zoomEnhancement;
    
    // Scale elevation by road importance
    double importanceMultiplier;
    switch (roadType) {
      case 'motorway':
        importanceMultiplier = 1.0;
        break;
      case 'trunk':
        importanceMultiplier = 0.9;
        break;
      case 'primary':
        importanceMultiplier = 0.8;
        break;
      case 'secondary':
        importanceMultiplier = 0.7;
        break;
      case 'tertiary':
        importanceMultiplier = 0.6;
        break;
      case 'residential':
      case 'unclassified':
        importanceMultiplier = 0.5;
        break;
      default:
        importanceMultiplier = 0.4;
    }
    
    return baseElevation * importanceMultiplier;
  }
  
  // Get zoom-dependent elevation enhancement factor
  double _getZoomElevationFactor() {
    // Start enhancing 3D effect at zoom 14
    if (zoomLevel <= 14) return 1.0;
    if (zoomLevel >= 20) return 2.5; // Maximum 2.5x enhancement at zoom 20
    
    // Linear interpolation between zoom 14 and 20
    return 1.0 + (zoomLevel - 14) / 2.4;
  }
  
  // Create a path for the road with proper width
  Path _createRoadPath(List<Offset> points, double width) {
    if (points.length < 2) return Path();
    
    // For very basic roads at low zoom, just use a simple stroke path
    if (width < 2.0 || zoomLevel < 13) {
      final Path simplePath = Path();
      simplePath.moveTo(points.first.dx, points.first.dy);
      for (int i = 1; i < points.length; i++) {
        simplePath.lineTo(points[i].dx, points[i].dy);
      }
      return simplePath;
    }
    
    // For wider roads, create an actual polygon path with width
    final Path widePath = Path();
    
    // Create points for both sides of the road
    List<Offset> leftPoints = [];
    List<Offset> rightPoints = [];
    
    // Generate points for both sides of the road
    for (int i = 0; i < points.length; i++) {
      // Get current point and vectors to adjacent points
      Offset current = points[i];
      Offset? prev = i > 0 ? points[i - 1] : null;
      Offset? next = i < points.length - 1 ? points[i + 1] : null;
      
      // Calculate bisector vector (perpendicular to road direction)
      Offset bisector;
      
      if (prev == null) {
        // First point - use direction to next point
        Offset dir = (next! - current).normalize();
        bisector = Offset(-dir.dy, dir.dx); // 90 degree rotation
      } else if (next == null) {
        // Last point - use direction from previous point
        Offset dir = (current - prev).normalize();
        bisector = Offset(-dir.dy, dir.dx); // 90 degree rotation
      } else {
        // Middle point - use average of directions to create smooth corners
        Offset dir1 = (current - prev).normalize();
        Offset dir2 = (next - current).normalize();
        
        // Calculate bisector (perpendicular to average direction)
        Offset avgDir = Offset(
          (dir1.dx + dir2.dx) / 2,
          (dir1.dy + dir2.dy) / 2
        ).normalize();
        
        bisector = Offset(-avgDir.dy, avgDir.dx);
        
        // Adjust bisector length at sharp corners to avoid spikes
        final double dot = dir1.dx * dir2.dx + dir1.dy * dir2.dy;
        if (dot < 0.7) { // Angle is more than ~45 degrees
          // Increase bisector length for sharper corners
          double lengthFactor = 1.0 / math.max(0.3, math.sqrt((1.0 + dot) / 2.0));
          bisector = Offset(bisector.dx * lengthFactor, bisector.dy * lengthFactor);
        }
      }
      
      // Calculate points on both sides of the road
      double halfWidth = width / 2.0;
      leftPoints.add(Offset(
        current.dx + bisector.dx * halfWidth,
        current.dy + bisector.dy * halfWidth
      ));
      
      rightPoints.add(Offset(
        current.dx - bisector.dx * halfWidth,
        current.dy - bisector.dy * halfWidth
      ));
    }
    
    // Build the path - start from the left side, go forward, then right side backward, to close the shape
    if (leftPoints.isNotEmpty) {
      widePath.moveTo(leftPoints.first.dx, leftPoints.first.dy);
      
      // Add left side points
      for (int i = 1; i < leftPoints.length; i++) {
        widePath.lineTo(leftPoints[i].dx, leftPoints[i].dy);
      }
      
      // Add right side points in reverse order
      for (int i = rightPoints.length - 1; i >= 0; i--) {
        widePath.lineTo(rightPoints[i].dx, rightPoints[i].dy);
      }
      
      // Close the path
      widePath.close();
    }
    
    return widePath;
  }
  
  // Draw road with enhanced 2.5D effects
  void _drawRoadWithEnhancedEffects(
    Canvas canvas, 
    Path roadPath, 
    List<Offset> points, 
    String roadType, 
    Color baseColor, 
    double width,
    double elevation
  ) {
    // Skip drawing if the path is empty
    if (points.length < 2) return;
    
    // Apply subtle color variation based on road type
    Color roadColor = _addColorVariation(baseColor, variationAmount: 10);
    
    // Calculate shadow intensity based on tilt and road type
    double shadowOpacity = math.min(0.5, tiltFactor * 0.8);
    if (_isMainRoad(roadType)) {
      shadowOpacity *= 1.2; // Stronger shadows for main roads
    }
    
    // Enhance shadow intensity with zoom level
    shadowOpacity = math.min(0.6, shadowOpacity * (1.0 + (zoomLevel - 14) * 0.04).clamp(1.0, 1.5));
    
    // Shadow paint for roads
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(shadowOpacity)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0) // Add blur for softer shadows
      ..isAntiAlias = true;
    
    // Calculate shadow offset based on tilt factor and enhanced by zoom
    final zoomMultiplier = math.min(1.0 + (zoomLevel - 15) * 0.1, 1.5);
    final shadowMultiplier = math.min(1.5, 1.0 + (tiltFactor * 0.5)) * zoomMultiplier;
    final Offset shadowOffset = Offset(1.0 * shadowMultiplier, 1.0 * shadowMultiplier);
    
    // Draw shadow for elevated roads
    if (elevation > 0.1) {
      final Path shadowPath = roadPath.shift(shadowOffset);
      canvas.drawPath(shadowPath, shadowPaint);
    }
    
    // Create a gradient for the road surface to add depth
    final Rect roadBounds = roadPath.getBounds();
    final Gradient roadGradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color.lerp(roadColor, Colors.white, 0.05) ?? roadColor,
        roadColor,
        Color.lerp(roadColor, Colors.black, 0.1) ?? roadColor,
      ],
      stops: const [0.0, 0.5, 1.0],
    );
    
    // Road paint with gradient for 3D effect
    final Paint roadPaint = Paint()
      ..shader = roadGradient.createShader(roadBounds)
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw the main road surface
    canvas.drawPath(roadPath, roadPaint);
    
    // Add a subtle edge highlight for 3D effect
    final Paint edgePaint = Paint()
      ..color = Colors.black.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5
      ..isAntiAlias = true;
    
    canvas.drawPath(roadPath, edgePaint);
    
    // Add road texture details at higher zoom levels
    if (zoomLevel >= 16 && width > 3.0) {
      _addRoadTextureDetails(canvas, roadPath, roadColor, roadType);
    }
  }
  
  // Add subtle texture details to the road surface
  void _addRoadTextureDetails(Canvas canvas, Path roadPath, Color baseColor, String roadType) {
    // Different texture patterns based on road type
    if (_isMainRoad(roadType)) {
      // For main roads, add subtle lane dividers
      _addRoadLaneDividers(canvas, roadPath, baseColor, roadType);
    } else if (roadType == 'residential' || roadType == 'unclassified') {
      // For smaller roads, add subtle texture dots
      _addSubtleRoadTexture(canvas, roadPath, baseColor);
    }
  }
  
  // Add lane dividers to main roads
  void _addRoadLaneDividers(Canvas canvas, Path roadPath, Color baseColor, String roadType) {
    // Get metrics to traverse the path
    final PathMetrics metrics = roadPath.computeMetrics();
    
    for (final metric in metrics) {
      final double pathLength = metric.length;
      final int laneCount = _getLaneCount(roadType);
      
      // Skip if path is too short
      if (pathLength < 10.0) continue;
      
      // Draw lane dividers along the path
      final double dashLength = 10.0; // Length of each dash
      final double gapLength = 10.0; // Length of gap between dashes
      final double spacing = dashLength + gapLength;
      
      // Animate dashes if animation value is provided
      double animationOffset = 0.0;
      if (animationValue != null) {
        animationOffset = spacing * (animationValue ?? 0.0);
      }
      
      // Draw dashed lines
      for (double distance = animationOffset % spacing; 
           distance < pathLength; 
           distance += spacing) {
        // For each dash position
        final Tangent? tangent = metric.getTangentForOffset(distance);
        if (tangent == null) continue;
        
        // Draw dash at this position
        final Paint dashPaint = Paint()
          ..color = Colors.white.withOpacity(0.5)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0
          ..strokeCap = StrokeCap.round
          ..isAntiAlias = true;
        
        // Get position and direction
        final Offset pos = tangent.position;
        final Offset dir = tangent.vector.normalize();
        
        // Draw dash
        canvas.drawLine(
          pos,
          Offset(pos.dx + dir.dx * dashLength, pos.dy + dir.dy * dashLength),
          dashPaint
        );
      }
    }
  }
  
  // Get lane count based on road type
  int _getLaneCount(String roadType) {
    switch (roadType) {
      case 'motorway':
        return 3;
      case 'trunk':
      case 'primary':
        return 2;
      default:
        return 1;
    }
  }
  
  // Add subtle texture to road surface
  void _addSubtleRoadTexture(Canvas canvas, Path roadPath, Color baseColor) {
    // Clip to road path to ensure texture stays within road
    canvas.save();
    canvas.clipPath(roadPath);
    
    // Get road bounds
    final Rect bounds = roadPath.getBounds();
    
    // Create a semi-transparent texture overlay
    final Color textureColor = Color.lerp(baseColor, Colors.grey, 0.1) ?? baseColor;
    final Paint texturePaint = Paint()
      ..color = textureColor.withOpacity(0.1)
      ..style = PaintingStyle.fill;
    
    // Draw subtle texture dots
    final double dotSpacing = 10.0;
    final double dotSize = 1.0;
    
    for (double x = bounds.left; x < bounds.right; x += dotSpacing) {
      for (double y = bounds.top; y < bounds.bottom; y += dotSpacing) {
        // Add slight randomness to dot positions
        final double offsetX = _random.nextDouble() * 4.0 - 2.0;
        final double offsetY = _random.nextDouble() * 4.0 - 2.0;
        
        canvas.drawCircle(
          Offset(x + offsetX, y + offsetY), 
          dotSize, 
          texturePaint
        );
      }
    }
    
    canvas.restore();
  }
  
  // Draw enhanced lane markings and traffic flow
  void _drawEnhancedLaneMarkings(Canvas canvas, Path roadPath, List<Offset> points, String roadType, double roadWidth) {
    // Skip lane markings for smaller roads
    if (roadWidth < 4.0) return;
    
    // Get path metrics to traverse along the road
    final PathMetrics metrics = roadPath.computeMetrics();
    
    for (final metric in metrics) {
      final double length = metric.length;
      
      // Skip if road segment is too short
      if (length < 20.0) continue;
      
      // Determine lane marking pattern based on road type
      double dashLength = 10.0;
      double gapLength = 5.0;
      
      switch (roadType) {
        case 'motorway':
          dashLength = 20.0;
          gapLength = 5.0;
          break;
        case 'trunk':
        case 'primary':
          dashLength = 15.0;
          gapLength = 5.0;
          break;
        default:
          dashLength = 10.0;
          gapLength = 10.0;
      }
      
      // Total pattern length (dash + gap)
      final double patternLength = dashLength + gapLength;
      
      // Animation offset if animation value is provided
      double animOffset = 0.0;
      if (animationValue != null) {
        animOffset = -(animationValue ?? 0.0) * patternLength; // Negative for forward movement
      }
      
      // Lane marking style
      final Paint laneMarkingPaint = Paint()
        ..color = Colors.white.withOpacity(0.8)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5
        ..strokeCap = StrokeCap.round
        ..isAntiAlias = true;
      
      // Draw lane markings along the path
      for (double distance = animOffset % patternLength; 
           distance < length; 
           distance += patternLength) {
        
        // Only draw the dash part, not the gap
        if (distance + dashLength > 0 && distance < length) {
          final double startDist = math.max(0.0, distance);
          final double endDist = math.min(distance + dashLength, length);
          
          // Get tangent at start point to draw along the path
          final Tangent? startTangent = metric.getTangentForOffset(startDist);
          final Tangent? endTangent = metric.getTangentForOffset(endDist);
          
          if (startTangent != null && endTangent != null) {
            canvas.drawLine(
              startTangent.position, 
              endTangent.position, 
              laneMarkingPaint
            );
          }
        }
      }
    }
  }
  
  // Draw enhanced road name with better visibility
  void _drawEnhancedRoadName(Canvas canvas, List<Offset> points, String name, String roadType, double roadWidth) {
    if (points.length < 2) return;
    
    // Find a suitable segment in the middle of the road to place the text
    int midIndex = points.length ~/ 2;
    if (midIndex >= points.length - 1) midIndex = points.length - 2;
    
    Offset start = points[midIndex];
    Offset end = points[midIndex + 1];
    
    // Calculate angle for text rotation
    double angle = math.atan2(end.dy - start.dy, end.dx - start.dx);
    
    // Flip text if angle would make it upside down
    if (angle > math.pi / 2 || angle < -math.pi / 2) {
      angle += math.pi;
      Offset temp = start;
      start = end;
      end = temp;
    }
    
    // Create enhanced text style with better visibility
    final bool isMainRoad = _isMainRoad(roadType);
    final double fontSize = isMainRoad ? 12.0 : 10.0;
    
    // Background color for text
    final Color bgColor = isMainRoad 
        ? Colors.white.withOpacity(0.7) 
        : Colors.white.withOpacity(0.5);
    
    // Text color based on theme
    final Color textColor = theme == 'dark' ? Colors.black : Colors.black;
    
    // Create text painter
    final textStyle = TextStyle(
      color: textColor,
      fontSize: fontSize,
      fontWeight: isMainRoad ? FontWeight.bold : FontWeight.normal,
    );
    
    final textSpan = TextSpan(
      text: name,
      style: textStyle,
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    // Calculate text position
    Offset center = Offset(
      (start.dx + end.dx) / 2,
      (start.dy + end.dy) / 2,
    );
    
    // Calculate background rectangle size with padding
    final double padding = 4.0;
    final double bgWidth = textPainter.width + padding * 2;
    final double bgHeight = textPainter.height + padding * 2;
    
    // Save canvas state before rotating
    canvas.save();
    
    // Translate to the center point
    canvas.translate(center.dx, center.dy);
    
    // Rotate canvas
    canvas.rotate(angle);
    
    // Draw background rounded rectangle
    final Paint bgPaint = Paint()
      ..color = bgColor
      ..style = PaintingStyle.fill;
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset.zero,
          width: bgWidth,
          height: bgHeight
        ),
        Radius.circular(bgHeight / 2)
      ),
      bgPaint,
    );
    
    // Draw text centered on the road
    textPainter.paint(
      canvas, 
      Offset(-textPainter.width / 2, -textPainter.height / 2),
    );
    
    // Restore canvas to original state
    canvas.restore();
  }
  
  // Add subtle color variation to road color
  Color _addColorVariation(Color baseColor, {int variationAmount = 10}) {
    // Only add variation for higher zoom levels
    if (zoomLevel < 14) return baseColor;
    
    // Add small random variation to color components
    final int variation = math.min(variationAmount, 15); // Limit maximum variation
    final int r = (baseColor.red + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int g = (baseColor.green + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int b = (baseColor.blue + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    
    return Color.fromRGBO(r, g, b, baseColor.opacity);
  }
  
  // Get appropriate color for a road type
  Color _getRoadColor(String roadType) {
    // Use theme if available, otherwise fallback to vibrant
    final String effectiveTheme = _roadColorPalette.containsKey(theme) ? theme : 'vibrant';
    
    // Get the color for this road type, or fall back to unclassified if not found
    return _roadColorPalette[effectiveTheme]![roadType] ?? 
           _roadColorPalette[effectiveTheme]!['unclassified']!;
  }
  
  // Get appropriate width for a road type, scaled by zoom level
  double _getRoadWidth(String roadType) {
    // Base widths for different road types
    double baseWidth;
    switch (roadType) {
      case 'motorway':
        baseWidth = 6.0;
        break;
      case 'trunk':
        baseWidth = 5.5;
        break;
      case 'primary':
        baseWidth = 5.0;
        break;
      case 'secondary':
        baseWidth = 4.5;
        break;
      case 'tertiary':
        baseWidth = 4.0;
        break;
      case 'residential':
        baseWidth = 3.5;
        break;
      case 'service':
        baseWidth = 3.0;
        break;
      case 'unclassified':
        baseWidth = 3.0;
        break;
      case 'living_street':
        baseWidth = 3.0;
        break;
      case 'pedestrian':
        baseWidth = 2.5;
        break;
      case 'footway':
        baseWidth = 2.0;
        break;
      case 'cycleway':
        baseWidth = 2.0;
        break;
      case 'path':
        baseWidth = 1.5;
        break;
      case 'track':
        baseWidth = 1.5;
        break;
      default:
        baseWidth = 2.0;
    }
    
    // Scale width based on zoom level
    double zoomScale;
    if (zoomLevel < 10) {
      zoomScale = 0.4;
    } else if (zoomLevel < 12) {
      zoomScale = 0.6;
    } else if (zoomLevel < 14) {
      zoomScale = 0.8;
    } else if (zoomLevel < 16) {
      zoomScale = 1.0;
    } else if (zoomLevel < 18) {
      zoomScale = 1.2;
    } else {
      zoomScale = 1.4;
    }
    
    return baseWidth * zoomScale;
  }
  
  // Check if a road is a main road (for shadow and lane markings)
  bool _isMainRoad(String roadType) {
    return roadType == 'motorway' || 
           roadType == 'trunk' || 
           roadType == 'primary' || 
           roadType == 'secondary';
  }

  @override
  bool shouldRepaint(OSMRoadsPainter oldDelegate) {
    // CRITICAL FIX: Always repaint when mapCamera changes
    if (oldDelegate.mapCamera != mapCamera) {
      return true; // Camera changed - must repaint for smooth panning
    }
    
    return oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.visibleBounds != visibleBounds ||
           oldDelegate.theme != theme ||
           oldDelegate.zoomBucket != zoomBucket ||
           oldDelegate.showDetails != showDetails ||
           oldDelegate.animationValue != animationValue ||
           oldDelegate.roads != roads;
  }
} 