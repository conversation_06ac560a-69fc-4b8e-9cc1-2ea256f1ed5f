import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

import '../osm_data_processor.dart';
import '../../../../services/map_cache_manager.dart';
import '../../map_caching/zoom_level_manager.dart';
import '../../map_caching/map_cache_extension.dart';
import '../../map_caching/map_cache_coordinator.dart';

/// Data provider class that handles road data fetching and caching
/// This class is responsible for all the data-related logic, separating it from UI rendering
class OSMRoadsDataProvider {
  // Data processing and caching dependencies
  final OSMDataProcessor _dataProcessor = OSMDataProcessor();
  final MapCacheManager _cacheManager = MapCacheManager();
  final ZoomLevelManager _zoomManager = ZoomLevelManager();
  
  // Callback for error handling
  final Function(String errorMessage)? onError;
  
  // State variables
  List<Map<String, dynamic>> _roads = [];
  bool _isLoading = true;
  bool _needsRefresh = true;
  String _lastBoundsKey = "";
  bool _hasError = false;
  bool _didInitialFetch = false;
  
  // Track last fetch params to avoid unnecessary fetches
  LatLngBounds? _lastFetchedBounds;
  double _lastFetchedZoom = 0;
  
  // Current parameters
  double _zoomLevel;
  LatLngBounds _visibleBounds;
  bool _isMapMoving;
  
  // Track current zoom level bucket for optimized rendering
  int _currentZoomBucket = 3;
  
  // Detail level to use (can be overridden by initial param)
  int _detailLevel = 3;
  
  // Keep track of the last request time for rate limiting
  DateTime _lastRequestTime = DateTime.now().subtract(const Duration(seconds: 30));
  
  /// Constructor that takes initial parameters and optional error callback
  OSMRoadsDataProvider({
    required double initialZoomLevel,
    required LatLngBounds initialBounds,
    bool isMapMoving = false,
    this.onError,
    int? initialDetailLevel,
  }) : 
    _zoomLevel = initialZoomLevel,
    _visibleBounds = initialBounds,
    _isMapMoving = isMapMoving {
    // Initialize zoom bucket
    _currentZoomBucket = _getZoomBucket(initialZoomLevel);
    
    // Set detail level if provided
    if (initialDetailLevel != null) {
      _detailLevel = initialDetailLevel;
    }
  }
  
  /// Update the parameters when map changes
  void updateParameters({
    required double zoomLevel,
    required LatLngBounds visibleBounds,
    required bool isMapMoving,
  }) {
    // Update current parameters
    _zoomLevel = zoomLevel;
    _visibleBounds = visibleBounds;
    _isMapMoving = isMapMoving;
    
    // Update zoom bucket if needed
    final newZoomBucket = _getZoomBucket(zoomLevel);
    if (newZoomBucket != _currentZoomBucket) {
      _currentZoomBucket = newZoomBucket;
      _needsRefresh = true;
    }
  }
  
  /// Determine if we need to fetch new data based on parameter changes
  bool shouldFetchNewData({
    required double oldZoomLevel,
    required LatLngBounds oldBounds,
    required bool oldIsMoving,
    required double newZoomLevel,
    required LatLngBounds newBounds,
    required bool newIsMoving,
  }) {
    // Check if zoom bucket has changed
    final oldZoomBucket = _getZoomBucket(oldZoomLevel);
    final newZoomBucket = _getZoomBucket(newZoomLevel);
    final zoomBucketChanged = oldZoomBucket != newZoomBucket;
    
    // Check if bounds have changed significantly
    bool boundsChanged = false;
    if (oldBounds != newBounds) {
      final newBoundsKey = _getBoundsKey(newBounds, newZoomLevel);
      boundsChanged = newBoundsKey != _lastBoundsKey;
    }
    
    // Track if map has stopped moving
    final wasMoving = oldIsMoving;
    final isMovingNow = newIsMoving;
    final stoppedMoving = wasMoving && !isMovingNow;
    
    // Decide if we should fetch new data
    if ((boundsChanged && !isMovingNow) || 
        (stoppedMoving && _needsRefresh) || 
        zoomBucketChanged) {
      
      // If we just stopped moving, we can delay the fetch slightly
      if (stoppedMoving) {
        _needsRefresh = true;
        return false; // We'll fetch in a moment when the map settles
      } else {
        return true; // Fetch immediately
      }
    }
    
    return false;
  }
  
  /// Fetch road data
  Future<void> fetchRoadsData() async {
    // Skip if we're at global view level where detailed roads aren't needed
    if (_currentZoomBucket <= 2 && _zoomLevel < 10) {
      _roads = [];
      _isLoading = false;
      _needsRefresh = false;
      _didInitialFetch = true;
      _hasError = false;
      return;
    }
    
    _isLoading = !_didInitialFetch; // Only show loading on first fetch
    
    // Update bounds key
    _lastBoundsKey = _getBoundsKey(_visibleBounds, _zoomLevel);
    
    // Check if bounds or zoom has significantly changed
    bool shouldSkipFetch = false;
    if (_lastFetchedBounds != null && _lastFetchedZoom > 0) {
      final boundsDistance = _calculateBoundsDistance(_visibleBounds, _lastFetchedBounds!);
      final zoomDifference = (_zoomLevel - _lastFetchedZoom).abs();
      
      // If we're moving the map and have cached data, delay the fetch
      if (_isMapMoving && _roads.isNotEmpty) {
        shouldSkipFetch = true;
      }
      // If bounds haven't changed much and zoom level is similar, use cached data
      else if (boundsDistance < 0.05 && zoomDifference < 1.0 && _roads.isNotEmpty) {
        shouldSkipFetch = true;
      }
    }
    
    if (shouldSkipFetch) {
      _isLoading = false;
      _needsRefresh = true; // Mark for refresh when map movement stops
      return;
    }
    
    // Generate cache key for the MapCacheCoordinator
    final cacheKey = 'roads_${_visibleBounds.southWest.latitude.toStringAsFixed(4)}_${_visibleBounds.southWest.longitude.toStringAsFixed(4)}_${_visibleBounds.northEast.latitude.toStringAsFixed(4)}_${_visibleBounds.northEast.longitude.toStringAsFixed(4)}_${_currentZoomBucket}';
    
    try {
      // Use MapCacheCoordinator to get data from cache or fetch from network
      final roadsData = await MapCacheCoordinator().getData(
        type: MapDataType.roads,
        key: cacheKey,
        southwest: _visibleBounds.southWest,
        northeast: _visibleBounds.northEast,
        zoomLevel: _zoomLevel,
        fetchIfMissing: () async {
          // Adapt detail level based on zoom bucket
          final detailLevel = _getDetailLevel(_currentZoomBucket);
          
          // Calculate a safe data request region based on Overpass API limits
          final safeRequestBounds = _calculateSafeRequestBounds(
            _visibleBounds,
            detailLevel,
            _zoomLevel
          );
          
          // Fetch road data with appropriate detail level
          return await _dataProcessor.fetchRoadData(
            safeRequestBounds.southWest,
            safeRequestBounds.northEast,
          );
        }
      );
      
      _roads = roadsData ?? [];
      _isLoading = false;
      _needsRefresh = false;
      _lastFetchedBounds = _visibleBounds;
      _lastFetchedZoom = _zoomLevel;
      _didInitialFetch = true;
      _hasError = false;
    } catch (e) {
      debugPrint('Error in OSMRoadsDataProvider: $e');
      _isLoading = false;
      _hasError = true;
      _didInitialFetch = true; // We did try to fetch
      
      // Notify listeners about the error
      onError?.call(e.toString());
    }
  }
  
  /// Reset error state to try again
  void resetErrorState() {
    _hasError = false;
    _dataProcessor.resetErrorState();
  }
  
  /// Get a key to identify current map bounds, with reduced precision for fewer unnecessary refreshes
  String _getBoundsKey(LatLngBounds bounds, double zoom) {
    final sw = bounds.southWest;
    final ne = bounds.northEast;
    
    // Reduce precision for bounds (3 decimal places ≈ 100m accuracy)
    return 'bounds_${sw.latitude.toStringAsFixed(4)}_${sw.longitude.toStringAsFixed(4)}_${ne.latitude.toStringAsFixed(4)}_${ne.longitude.toStringAsFixed(4)}_${zoom.toStringAsFixed(1)}';
  }
  
  /// Calculate a distance metric between two bounds
  double _calculateBoundsDistance(LatLngBounds bounds1, LatLngBounds bounds2) {
    // Simple Euclidean distance between centers
    final center1 = LatLng(
      (bounds1.northEast.latitude + bounds1.southWest.latitude) / 2,
      (bounds1.northEast.longitude + bounds1.southWest.longitude) / 2
    );
    
    final center2 = LatLng(
      (bounds2.northEast.latitude + bounds2.southWest.latitude) / 2,
      (bounds2.northEast.longitude + bounds2.southWest.longitude) / 2
    );
    
    // Approximate using flat-earth model for small distances
    return math.sqrt(
      math.pow(center1.latitude - center2.latitude, 2) +
      math.pow(center1.longitude - center2.longitude, 2)
    );
  }
  
  /// Calculate a safe request bounds that won't exceed Overpass API limits
  LatLngBounds _calculateSafeRequestBounds(LatLngBounds visibleBounds, double detailLevel, double zoomLevel) {
    final double latDelta = visibleBounds.northEast.latitude - visibleBounds.southWest.latitude;
    final double lonDelta = visibleBounds.northEast.longitude - visibleBounds.southWest.longitude;
    
    // Calculate center of visible bounds
    final LatLng center = LatLng(
      visibleBounds.southWest.latitude + latDelta * 0.5,
      visibleBounds.southWest.longitude + lonDelta * 0.5
    );
    
    // Adjust maximum query area based on zoom level
    // Higher zoom levels can query smaller areas with higher detail
    double maxAreaSize = 0.04; // Default max area (~ 4km)
    
    if (zoomLevel < 12) {
      // At low zoom levels, use lower detail but larger areas
      maxAreaSize = 0.1 * math.min(1.0, detailLevel);
    } else if (zoomLevel < 15) {
      // Medium zoom levels
      maxAreaSize = 0.06 * math.min(1.0, detailLevel);
    } else {
      // High zoom levels, use smaller areas for more detail
      maxAreaSize = 0.04 * math.min(1.0, detailLevel);
    }
    
    // If current area is too large, focus on a smaller region
    if (latDelta > maxAreaSize || lonDelta > maxAreaSize) {
      final double halfSize = maxAreaSize / 2;
      
      return LatLngBounds(
        LatLng(
          center.latitude - halfSize,
          center.longitude - halfSize
        ),
        LatLng(
          center.latitude + halfSize,
          center.longitude + halfSize
        )
      );
    }
    
    // Otherwise use the original bounds
    return visibleBounds;
  }
  
  /// Get the zoom bucket (1-5) for current zoom level
  int _getZoomBucket(double zoom) {
    if (zoom < 8) return 1;
    if (zoom < 11) return 2;
    if (zoom < 14) return 3;
    if (zoom < 17) return 4;
    return 5;
  }
  
  /// Get appropriate detail level for current zoom bucket
  double _getDetailLevel(int zoomBucket) {
    // If we have a custom detail level set, use it to influence the detail
    if (_detailLevel < 3) {
      // For lower custom detail levels, reduce overall detail
      switch (zoomBucket) {
        case 1: return 0.1; // World - minimal detail
        case 2: return 0.2; // Continental - very low detail
        case 3: return 0.3; // Regional - low detail
        case 4: return 0.5; // Local - medium detail
        case 5: return 0.7; // Fully zoomed - high but not full detail
        default: return 0.3;
      }
    }
    
    // Default detail levels for high quality
    switch (zoomBucket) {
      case 1: return 0.2; // World - minimal detail
      case 2: return 0.4; // Continental - low detail
      case 3: return 0.6; // Regional - medium detail
      case 4: return 0.8; // Local - high detail
      case 5: return 1.0; // Fully zoomed - full detail
      default: return 0.6;
    }
  }
  
  /// Clean up resources
  void dispose() {
    // Nothing to dispose of at the moment, but here for future use
  }
  
  // Getters for public access
  List<Map<String, dynamic>> get roads => _roads;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  bool get hasInitialData => _didInitialFetch;
  int getCurrentZoomBucket() => _currentZoomBucket;
} 