import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;
import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import '../../../utils/bounding_box_utilities.dart';
import './osm_roads_painter.dart';

/// A layer that displays OpenStreetMap roads with enhanced 2.5D styling
class OSMRoadsLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final Color? roadColor;
  final Color? motorwayColor;
  final Color? trunkRoadColor;
  final Color? primaryRoadColor;
  final String theme;
  final bool showDetails;
  
  const OSMRoadsLayer({
    Key? key,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    this.roadColor,
    this.motorwayColor,
    this.trunkRoadColor,
    this.primaryRoadColor,
    this.theme = 'vibrant',
    this.showDetails = true,
  }) : super(key: key);

  @override
  State<OSMRoadsLayer> createState() => _OSMRoadsLayerState();
}

class _OSMRoadsLayerState extends State<OSMRoadsLayer> with SingleTickerProviderStateMixin {
  List<Map<String, dynamic>> _roads = [];
  bool _isLoading = true;
  LatLngBounds? _currentBounds;
  late final AnimationController _trafficFlowAnimation;
  
  // Calculate zoom bucket for detail levels
  int get _zoomBucket {
    if (widget.zoomLevel >= 18) return 5;
    if (widget.zoomLevel >= 16) return 4;
    if (widget.zoomLevel >= 14) return 3;
    if (widget.zoomLevel >= 12) return 2;
    if (widget.zoomLevel >= 10) return 1;
    return 0;
  }
  
  @override
  void initState() {
    super.initState();
    
    // Initialize traffic flow animation controller
    _trafficFlowAnimation = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    )..repeat();
    
    // Initial load
    _loadRoadData();
  }
  
  @override
  void didUpdateWidget(OSMRoadsLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Check if map bounds have changed significantly to reload data
    if (_currentBounds == null ||
        oldWidget.zoomLevel != widget.zoomLevel ||
        _calculateBoundsDistance(_currentBounds!, widget.visibleBounds) > 0.05) {
      _loadRoadData();
    }
  }
  
  @override
  void dispose() {
    _trafficFlowAnimation.dispose();
    super.dispose();
  }
  
  // Load road data from OpenStreetMap Overpass API
  Future<void> _loadRoadData() async {
    if (widget.zoomLevel < 10) {
      // Skip loading data at very low zoom levels
      if (mounted) {
        setState(() {
          _roads = [];
          _isLoading = false;
        });
      }
      return;
    }
    
    // Set current bounds and loading state
    _currentBounds = widget.visibleBounds;
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }
    
    try {
      // Implementation of Overpass API query to fetch road data
      // ... existing code ...
      
    } catch (e) {
      debugPrint('Error fetching road data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          // Keep existing roads if we have them
        });
      }
    }
  }
  
  // Calculate a distance metric between two bounds
  double _calculateBoundsDistance(LatLngBounds bounds1, LatLngBounds bounds2) {
    // ... existing code ...
  }

  @override
  Widget build(BuildContext context) {
    // Return empty container while loading or if roads list is empty
    if (_isLoading || _roads.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // Use AnimatedBuilder to pass animation value to the painter
    return AnimatedBuilder(
      animation: _trafficFlowAnimation,
      builder: (context, child) {
        return CustomPaint(
          painter: OSMRoadsPainter(
            roads: _roads,
            tiltFactor: widget.tiltFactor,
            zoomLevel: widget.zoomLevel,
            visibleBounds: widget.visibleBounds,
            theme: widget.theme,
            zoomBucket: _zoomBucket,
            showDetails: widget.showDetails,
            animationValue: _trafficFlowAnimation.value,
          ),
          size: Size.infinite,
        );
      },
    );
  }
} 