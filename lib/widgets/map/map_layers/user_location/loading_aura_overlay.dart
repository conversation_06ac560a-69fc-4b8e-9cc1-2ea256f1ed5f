import 'dart:math' as math;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import '../../map_caching/map_cache_coordinator.dart';

/// A loading aura overlay that visualizes data loading from the user's location
class LoadingAuraOverlay extends StatefulWidget {
  final LatLng? userLocation;
  final double zoomLevel;
  final MapController mapController;

  const LoadingAuraOverlay({
    Key? key,
    required this.userLocation,
    required this.zoomLevel,
    required this.mapController,
  }) : super(key: key);

  @override
  State<LoadingAuraOverlay> createState() => _LoadingAuraOverlayState();
}

class _LoadingAuraOverlayState extends State<LoadingAuraOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final MapCacheCoordinator _cacheCoordinator = MapCacheCoordinator();

  // Properties for the loading visualization
  double _loadingProgress = 0.0;
  final List<_LoadingRing> _loadingRings = [];
  bool _hasError = false;
  String? _errorMessage;
  Timer? _updateTimer;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    try {
      _animationController = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 2000),
      )..repeat();

      // Setup rings with different radii and colors
      _setupLoadingRings();

      // Start updates after a short delay to allow widget to fully initialize
      Future.microtask(() {
        if (mounted) {
          _updateUserLocation();
          _startUpdates();
          _isInitialized = true;
        }
      });
    } catch (e) {
      debugPrint('Error initializing LoadingAuraOverlay: $e');
      _setError('Initialization error: $e');
    }
  }

  @override
  void didUpdateWidget(LoadingAuraOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update user location if it changed
    if (widget.userLocation != oldWidget.userLocation ||
        widget.zoomLevel != oldWidget.zoomLevel) {
      _updateUserLocation();
    }
  }
  
  void _updateUserLocation() {
    if (!mounted) return;
    
    if (widget.userLocation != null) {
      try {
        _cacheCoordinator.setUserLocation(widget.userLocation!);
        _clearError();
      } catch (e) {
        debugPrint('Error setting user location: $e');
        _setError('Location error: $e');
      }
    }
  }
  
  void _setError(String message) {
    if (mounted) {
      setState(() {
        _hasError = true;
        _errorMessage = message;
      });
    }
  }
  
  void _clearError() {
    if (_hasError && mounted) {
      setState(() {
        _hasError = false;
        _errorMessage = null;
      });
    }
  }

  void _setupLoadingRings() {
    _loadingRings.clear();
    
    // Add multiple rings with different properties
    _loadingRings.add(_LoadingRing(
      baseRadius: 30,
      radiusVariation: 10,
      color: Colors.blue.withOpacity(0.3),
      pulseFactor: 1.0,
      pulseOffset: 0.0,
    ));
    
    _loadingRings.add(_LoadingRing(
      baseRadius: 45,
      radiusVariation: 15,
      color: Colors.green.withOpacity(0.25),
      pulseFactor: 0.8,
      pulseOffset: 0.3,
    ));
    
    _loadingRings.add(_LoadingRing(
      baseRadius: 70,
      radiusVariation: 20,
      color: Colors.purple.withOpacity(0.2),
      pulseFactor: 0.6,
      pulseOffset: 0.6,
    ));
  }

  void _startUpdates() {
    // Cancel existing timer if any
    _stopUpdates();
    
    // Start new update cycle
    _updateProgress();
  }
  
  void _stopUpdates() {
    _updateTimer?.cancel();
    _updateTimer = null;
  }

  void _updateProgress() {
    if (!mounted) return;
    
    try {
      final bounds = widget.mapController.camera.visibleBounds;
      
      // Calculate the loading percentage
      final poiProgress = _cacheCoordinator.getLoadingPercentage(MapDataType.poi, bounds);
      
      if (mounted) {
        setState(() {
          _loadingProgress = poiProgress;
          _clearError();
        });
      }
    } catch (e) {
      debugPrint('Error updating loading progress: $e');
      _setError('Update error: $e');
    } finally {
      // Always schedule next update with safe delay, even if there was an error
      if (mounted) {
        _updateTimer = Timer(const Duration(milliseconds: 500), _updateProgress);
      }
    }
  }

  @override
  void dispose() {
    try {
      _stopUpdates();
      _animationController.dispose();
    } catch (e) {
      debugPrint('Error disposing LoadingAuraOverlay: $e');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Nothing to show if location is not available
    if (widget.userLocation == null || !_isInitialized) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return CustomPaint(
          painter: _LoadingAuraPainter(
            userLocation: widget.userLocation!,
            mapController: widget.mapController,
            animationValue: _animationController.value,
            loadingRings: _loadingRings,
            loadingProgress: _loadingProgress,
            hasError: _hasError,
            errorMessage: _errorMessage,
          ),
          child: Container(),
        );
      },
    );
  }
}

class _LoadingRing {
  final double baseRadius;
  final double radiusVariation;
  final Color color;
  final double pulseFactor;
  final double pulseOffset;
  
  _LoadingRing({
    required this.baseRadius,
    required this.radiusVariation,
    required this.color,
    required this.pulseFactor,
    required this.pulseOffset,
  });
  
  double getCurrentRadius(double animationValue) {
    // Create a pulsing effect with the offset
    final adjustedValue = (animationValue + pulseOffset) % 1.0;
    final pulse = math.sin(adjustedValue * math.pi * 2) * pulseFactor;
    
    // Base radius plus variation based on pulse
    return baseRadius + (radiusVariation * pulse);
  }
}

class _LoadingAuraPainter extends CustomPainter {
  final LatLng userLocation;
  final MapController mapController;
  final double animationValue;
  final List<_LoadingRing> loadingRings;
  final double loadingProgress;
  final bool hasError;
  final String? errorMessage;
  
  _LoadingAuraPainter({
    required this.userLocation,
    required this.mapController,
    required this.animationValue,
    required this.loadingRings,
    required this.loadingProgress,
    this.hasError = false,
    this.errorMessage,
  });

  @override
  void paint(Canvas canvas, Size size) {
    Offset point;
    
    try {
      // Convert user location to screen coordinates using current API
      final pixelPoint = mapController.camera.latLngToScreenPoint(userLocation);
      point = Offset(pixelPoint.x.toDouble(), pixelPoint.y.toDouble());
    } catch (e) {
      // Fallback to center of canvas if projection fails
      debugPrint('Error converting location to screen coordinates: $e');
      point = Offset(size.width / 2, size.height / 2);
    }

    // Draw loading progress arc
    final progressPaint = Paint()
      ..color = hasError ? Colors.red.withOpacity(0.7) : Colors.blue.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0;
    
    // Draw progress arc (outer ring)
    canvas.drawArc(
      Rect.fromCircle(center: point, radius: 90),
      -math.pi / 2, // Start from top
      2 * math.pi * loadingProgress, // Arc angle based on progress
      false,
      progressPaint,
    );
    
    // Draw each loading ring
    for (final ring in loadingRings) {
      final currentRadius = ring.getCurrentRadius(animationValue);
      final paint = Paint()
        ..color = hasError ? ring.color.withRed(200) : ring.color // Redder if error
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;
        
      canvas.drawCircle(point, currentRadius, paint);
      
      // Add subtle dot pattern on the ring
      final dotPaint = Paint()
        ..color = (hasError ? ring.color.withRed(200) : ring.color).withOpacity(0.7)
        ..style = PaintingStyle.fill;
        
      final numDots = (currentRadius / 5).round().clamp(8, 36);
      
      for (int i = 0; i < numDots; i++) {
        final angle = (i / numDots) * 2 * math.pi + (animationValue * math.pi);
        final dotRadius = 1.5;
        final dotCenter = Offset(
          point.dx + currentRadius * math.cos(angle),
          point.dy + currentRadius * math.sin(angle),
        );
        
        canvas.drawCircle(dotCenter, dotRadius, dotPaint);
      }
    }
    
    // Draw center point for user location
    final userLocationPaint = Paint()
      ..color = hasError ? Colors.red : Colors.blue
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(point, 6, userLocationPaint);
    
    // Add a white border around the user location point
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
      
    canvas.drawCircle(point, 6, borderPaint);
    
    // Show loading percentage text or error message
    final displayText = hasError ? (errorMessage?.substring(0, math.min(20, errorMessage?.length ?? 0)) ?? 'Error') : '${(loadingProgress * 100).round()}%';
    final textStyle = TextStyle(
      color: Colors.white,
      fontSize: 12,
      fontWeight: FontWeight.bold,
      shadows: [
        Shadow(
          blurRadius: 3,
          color: Colors.black.withOpacity(0.5),
          offset: const Offset(1, 1),
        ),
      ],
    );
    
    final textSpan = TextSpan(
      text: displayText,
      style: textStyle,
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas, 
      Offset(point.dx - textPainter.width / 2, point.dy - 25),
    );
  }

  @override
  bool shouldRepaint(covariant _LoadingAuraPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
           oldDelegate.loadingProgress != loadingProgress ||
           oldDelegate.userLocation != userLocation ||
           oldDelegate.hasError != hasError ||
           oldDelegate.errorMessage != errorMessage;
  }
} 