import 'package:latlong2/latlong.dart';
import 'package:flutter_map/flutter_map.dart';

/// Utility functions for working with map bounds
class MapBounds {
  /// Convert bounds to a bounding box string for OSM queries
  static String toBBoxString(LatLngBounds bounds) {
    return '${bounds.south},${bounds.west},${bounds.north},${bounds.east}';
  }

  /// Check if a point is within bounds
  static bool contains(LatLngBounds bounds, LatLng point) {
    return point.latitude >= bounds.south &&
           point.latitude <= bounds.north &&
           point.longitude >= bounds.west &&
           point.longitude <= bounds.east;
  }

  /// Calculate the center point of bounds
  static LatLng center(LatLngBounds bounds) {
    return LatLng(
      (bounds.north + bounds.south) / 2,
      (bounds.east + bounds.west) / 2,
    );
  }
} 