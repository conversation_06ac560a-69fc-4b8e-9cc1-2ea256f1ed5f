import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A class to manage elevation relationships between different map elements
/// Prevents z-index conflicts and physics issues when multiple 2.5D elements overlap
class ElevationManager {
  // Default elevation base values for different feature types
  static const Map<String, double> _baseElevations = {
    // Natural features base elevations
    'forest': 4.0,
    'wood': 3.5,
    'meadow': 1.4,
    'grassland': 1.2,
    'savanna': 1.2,
    'prairie': 1.4,
    'steppe': 1.1,
    'park': 1.0,
    'garden': 1.1,
    'grass': 0.8,
    'orchard': 1.5,
    'vineyard': 1.2,
    
    // Building base elevations (higher priority)
    'building': 5.0,
    'building_part': 5.0,
    
    // Road base elevations (intermediate priority)
    'motorway': 2.5,
    'trunk': 2.3,
    'primary': 2.1,
    'secondary': 1.9,
    'tertiary': 1.7,
    'residential': 1.5,
    'path': 1.2,
    
    // Individual elements
    'tree': 4.5, // Independent trees are taller than forests for better visibility
    
    // Default for unspecified types
    'default': 1.0,
  };
  
  // Collision adjustment priorities - higher numbers mean higher priority
  static const Map<String, int> _priorities = {
    'building': 100,
    'building_part': 100,
    'tree': 90,
    'forest': 80,
    'wood': 75,
    'motorway': 70,
    'trunk': 65,
    'primary': 60,
    'secondary': 55,
    'tertiary': 50,
    'residential': 45,
    'orchard': 40,
    'vineyard': 40,
    'garden': 35,
    'park': 30,
    'meadow': 25,
    'grassland': 20,
    'grass': 15,
    'path': 10,
    'default': 1,
  };
  
  // Cache for computed elevations to avoid recalculations
  final Map<String, Map<String, double>> _elevationCache = {};
  
  /// Get the base elevation for a feature type
  double getBaseElevation(String featureType) {
    return _baseElevations[featureType] ?? _baseElevations['default']!;
  }
  
  /// Get the priority for a feature type (used for z-ordering and collision resolution)
  int getPriority(String featureType) {
    return _priorities[featureType] ?? _priorities['default']!;
  }
  
  /// Calculate the adjusted elevation factor based on zoom level
  double getZoomElevationFactor(double zoomLevel) {
    // Start enhancing 3D effect at zoom 14
    if (zoomLevel <= 14) return 1.0;
    if (zoomLevel >= 20) return 2.5; // Maximum 2.5x enhancement at zoom 20
    
    // Non-linear interpolation for smoother transition
    final double t = (zoomLevel - 14) / 6; // 0 to 1 scale between zoom 14 and 20
    return 1.0 + math.pow(t, 1.2) * 1.5; // Non-linear enhancement
  }
  
  /// Get an adjusted elevation multiplier based on feature size
  double getSizeElevationMultiplier(Rect bounds) {
    // Dynamic and non-linear scaling for more natural appearance
    final double area = bounds.width * bounds.height;
    
    if (area > 50000) return 0.7;
    if (area > 20000) return 0.8;
    if (area > 10000) return 0.9;
    if (area > 5000) return 1.0;
    if (area > 2000) return 1.1;
    return 1.2; // Smallest areas get slight emphasis for visibility
  }
  
  /// Resolve elevation conflicts for multiple features that may overlap
  /// Returns adjusted elevations that prevent z-fighting
  Map<String, double> resolveElevationConflicts(
    List<Map<String, dynamic>> features,
    double zoomLevel,
    double tiltFactor
  ) {
    if (features.isEmpty) return {};
    
    // Generate a unique key for this resolution request
    final String cacheKey = _generateCacheKey(features, zoomLevel, tiltFactor);
    
    // Return cached result if available
    if (_elevationCache.containsKey(cacheKey)) {
      return _elevationCache[cacheKey]!;
    }
    
    final Map<String, double> adjustedElevations = {};
    final Map<String, List<Rect>> featureBounds = {};
    final Map<String, String> featureTypes = {};
    
    // First pass: initialize with base values and collect bounds
    for (final feature in features) {
      final String id = feature['id'] as String;
      final String type = feature['type'] as String;
      final Rect bounds = feature['bounds'] as Rect;
      
      // Store feature information
      featureTypes[id] = type;
      
      // Calculate base elevation
      final double baseElevation = getBaseElevation(type);
      final double zoomAdjustedElevation = baseElevation * getZoomElevationFactor(zoomLevel) * tiltFactor;
      final double sizeAdjustedElevation = zoomAdjustedElevation * getSizeElevationMultiplier(bounds);
      
      // Set initial elevation
      adjustedElevations[id] = sizeAdjustedElevation;
      
      // Store bounds for intersection testing
      if (!featureBounds.containsKey(type)) {
        featureBounds[type] = [];
      }
      featureBounds[type]!.add(bounds);
    }
    
    // Second pass: resolve overlaps based on priorities
    final List<String> sortedIds = adjustedElevations.keys.toList()
      ..sort((a, b) {
        final int priorityA = getPriority(featureTypes[a]!);
        final int priorityB = getPriority(featureTypes[b]!);
        return priorityB.compareTo(priorityA); // Higher priority first
      });
    
    // Check for overlaps and adjust elevations to prevent z-fighting
    for (int i = 0; i < sortedIds.length; i++) {
      for (int j = i + 1; j < sortedIds.length; j++) {
        final String idA = sortedIds[i];
        final String idB = sortedIds[j];
        final String typeA = featureTypes[idA]!;
        final String typeB = featureTypes[idB]!;
        
        // Only adjust if there's an actual overlap
        bool hasOverlap = false;
        for (final boundsA in featureBounds[typeA]!) {
          for (final boundsB in featureBounds[typeB]!) {
            if (boundsA.overlaps(boundsB)) {
              hasOverlap = true;
              break;
            }
          }
          if (hasOverlap) break;
        }
        
        if (hasOverlap) {
          // Higher priority feature (idA) keeps its elevation
          // Lower priority feature (idB) gets adjusted down
          final double minSeparation = 0.1; // Minimum elevation difference to prevent z-fighting
          final double elevDiff = adjustedElevations[idA]! - adjustedElevations[idB]!;
          
          if (elevDiff < minSeparation) {
            // Adjust the lower priority item's elevation down
            adjustedElevations[idB] = adjustedElevations[idA]! - minSeparation;
          }
        }
      }
    }
    
    // Cache the result for future use
    _elevationCache[cacheKey] = Map.from(adjustedElevations);
    
    // Limit cache size to prevent memory issues
    if (_elevationCache.length > 100) {
      _pruneCache();
    }
    
    return adjustedElevations;
  }
  
  /// Generate a cache key for elevation resolution
  String _generateCacheKey(List<Map<String, dynamic>> features, double zoomLevel, double tiltFactor) {
    final StringBuffer buffer = StringBuffer();
    buffer.write('z${zoomLevel.toStringAsFixed(1)}_t${tiltFactor.toStringAsFixed(1)}_');
    
    // Sort features by ID for consistent cache keys
    final sortedFeatures = List<Map<String, dynamic>>.from(features)
      ..sort((a, b) => (a['id'] as String).compareTo(b['id'] as String));
    
    for (final feature in sortedFeatures) {
      buffer.write('${feature['id']}_${feature['type']}_');
    }
    
    return buffer.toString();
  }
  
  /// Prune the elevation cache when it gets too large
  void _pruneCache() {
    if (_elevationCache.length <= 100) return;
    
    // Simple strategy: remove oldest 50% of entries
    final keys = _elevationCache.keys.toList();
    final keysToRemove = keys.sublist(0, keys.length ~/ 2);
    
    for (final key in keysToRemove) {
      _elevationCache.remove(key);
    }
  }
  
  /// Calculate shadow projection parameters for consistent lighting across elements
  Map<String, dynamic> getShadowParameters(double elevation, double zoomLevel) {
    // Shadow offset increases with elevation and zoom level
    final double baseOffset = elevation * 0.3; // Base shadow offset
    final double zoomFactor = math.min(1.5, 1.0 + (zoomLevel - 15) * 0.1);
    
    final double offsetX = baseOffset * zoomFactor * 2.2; // Horizontal offset
    final double offsetY = baseOffset * zoomFactor * 2.2; // Vertical offset
    
    // Shadow opacity also varies with elevation
    final double shadowOpacity = math.min(0.45, 0.25 + elevation * 0.05);
    
    // Shadow blur increases with elevation
    final double shadowBlur = math.min(4.0, 1.5 + elevation * 0.3);
    
    return {
      'offsetX': offsetX,
      'offsetY': offsetY,
      'opacity': shadowOpacity,
      'blur': shadowBlur,
    };
  }
  
  /// Apply consistent ambient occlusion parameters for more realistic depth
  Map<String, dynamic> getAmbientOcclusionParameters(double elevation, Rect bounds) {
    // AO intensity based on elevation
    final double aoIntensity = math.min(0.35, 0.18 + elevation * 0.08);
    
    // Dynamic inset calculation based on feature size and elevation
    final double insetBase = math.min(3.0, 1.0 + bounds.width * 0.002);
    final double inset = math.min(2.5, insetBase + elevation * 0.4);
    
    return {
      'intensity': aoIntensity,
      'inset': inset,
      'radius': inset * 1.8, // Blur radius for the AO effect
    };
  }
  
  /// Check if a feature should have its elevated sides rendered
  /// (Used to avoid drawing sides that would be hidden by higher priority features)
  bool shouldDrawElevatedSides(
    String featureId,
    String featureType,
    List<Map<String, dynamic>> nearbyFeatures,
    Rect bounds
  ) {
    // If no nearby features, always draw sides
    if (nearbyFeatures.isEmpty) return true;
    
    final int priority = getPriority(featureType);
    
    // Check if this feature is completely contained within a higher priority feature
    for (final nearby in nearbyFeatures) {
      final String nearbyType = nearby['type'] as String;
      final Rect nearbyBounds = nearby['bounds'] as Rect;
      final int nearbyPriority = getPriority(nearbyType);
      
      // If this feature is inside a higher priority one, don't draw elevated sides
      if (nearbyPriority > priority && nearbyBounds.contains(bounds)) {
        return false;
      }
    }
    
    return true;
  }
} 