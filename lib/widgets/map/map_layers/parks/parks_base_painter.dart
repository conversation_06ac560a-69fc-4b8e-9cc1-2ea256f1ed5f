import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

import './renderers/forest_renderer.dart';
import './renderers/grass_renderer.dart';
import './renderers/meadow_renderer.dart';
import './renderers/garden_renderer.dart';
import './renderers/tree_renderer.dart';
import './parks_colors.dart';
import './parks_geometry_utils.dart';

/// Main parks painter that coordinates all specialized renderers
/// Handles feature detection, classification and delegation to appropriate renderers
class ParksBasePainter extends CustomPainter {
  final Map<String, dynamic> geoJson;
  final double zoomLevel;
  final Size size;
  final String theme;
  final String season;
  final bool enhancedDetail;
  
  // Helper utilities
  final ParkGeometryUtils _geometryUtils = ParkGeometryUtils();
  final ParksColors _colorsHelper = ParksColors();
  
  // Specialized renderers
  late final ForestRenderer _forestRenderer;
  late final GrassRenderer _grassRenderer;
  late final MeadowRenderer _meadowRenderer;
  late final GardenRenderer _gardenRenderer;
  late final TreeRenderer _treeRenderer;
  
  ParksBasePainter({
    required this.geoJson,
    required this.zoomLevel,
    required this.size,
    this.theme = 'vibrant',
    this.season = 'default',
    this.enhancedDetail = true,
  }) {
    // Initialize all specialized renderers
    _forestRenderer = ForestRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      season: season,
      enhancedDetail: enhancedDetail
    );
    
    _grassRenderer = GrassRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      season: season,
      enhancedDetail: enhancedDetail
    );
    
    _meadowRenderer = MeadowRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      season: season,
      enhancedDetail: enhancedDetail
    );
    
    _gardenRenderer = GardenRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      season: season,
      enhancedDetail: enhancedDetail
    );
    
    _treeRenderer = TreeRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      season: season,
      enhancedDetail: enhancedDetail
    );
  }
  
  @override
  void paint(Canvas canvas, Size size) {
    if (!geoJson.containsKey('features') || geoJson['features'] == null) {
      return;
    }
    
    final features = geoJson['features'] as List;
    
    // First pass: Draw areas (polygons)
    _renderAreaFeatures(canvas, size, features);
    
    // Second pass: Draw point features (individual trees, etc.)
    if (zoomLevel >= 17) {
      _renderPointFeatures(canvas, size, features);
    }
  }
  
  /// Render area features (polygons) with appropriate specialized renderers
  void _renderAreaFeatures(Canvas canvas, Size size, List features) {
    // Process features by type for appropriate layering
    // Draw in this order: larger areas first, then more detailed areas
    
    // First draw larger natural areas
    for (final feature in features) {
      if (feature['geometry'] == null || feature['geometry']['type'] != 'Polygon') continue;
      if (!_isLargeNaturalArea(feature)) continue;
      
      _renderFeature(canvas, size, feature);
    }
    
    // Then draw smaller, more detailed areas
    for (final feature in features) {
      if (feature['geometry'] == null || feature['geometry']['type'] != 'Polygon') continue;
      if (_isLargeNaturalArea(feature)) continue;
      
      _renderFeature(canvas, size, feature);
    }
  }
  
  /// Render point features (individual trees, etc.)
  void _renderPointFeatures(Canvas canvas, Size size, List features) {
    if (zoomLevel < 17) return; // Only render points at higher zoom levels
    
    for (final feature in features) {
      if (feature['geometry'] == null || feature['geometry']['type'] != 'Point') continue;
      
      final coordinates = feature['geometry']['coordinates'] as List;
      if (coordinates.length < 2) continue;
      
      final point = Offset(
        coordinates[0].toDouble() * size.width,
        coordinates[1].toDouble() * size.height
      );
      
      _renderPointFeature(canvas, size, feature, point);
    }
  }
  
  /// Render a specific feature with the appropriate renderer
  void _renderFeature(Canvas canvas, Size size, Map<String, dynamic> feature) {
    final coordinates = feature['geometry']['coordinates'] as List;
    if (coordinates.isEmpty || coordinates[0] is! List) return;
    
    final outerRing = coordinates[0] as List;
    if (outerRing.length < 3) return;
    
    // Convert coordinates to screen points
    final points = _convertCoordinatesToPoints(outerRing, size);
    
    // Simplify polygon for performance at lower zoom levels
    final simplifiedPoints = _simplifyPolygon(points);
    
    // Determine feature type based on tags
    final featureType = _classifyFeature(feature);
    
    // Use appropriate renderer based on feature type
    switch (featureType.type) {
      case 'forest':
      case 'wood':
        _forestRenderer.renderForestArea(
          canvas, 
          size, 
          feature, 
          simplifiedPoints, 
          isWoodland: featureType.type == 'wood'
        );
        break;
      
      case 'grass':
      case 'park':
      case 'garden':
        _grassRenderer.renderGrassArea(
          canvas, 
          size, 
          feature, 
          simplifiedPoints, 
          subtype: featureType.type
        );
        break;
      
      case 'meadow':
      case 'grassland':
        _meadowRenderer.renderMeadowArea(
          canvas, 
          size, 
          feature, 
          simplifiedPoints, 
          subtype: featureType.type
        );
        break;
      
      case 'orchard':
      case 'vineyard':
        _gardenRenderer.renderGardenArea(
          canvas, 
          size, 
          feature, 
          simplifiedPoints, 
          subtype: featureType.type
        );
        break;
      
      default:
        // Use grass renderer as fallback for any unrecognized type
        _grassRenderer.renderGrassArea(
          canvas, 
          size, 
          feature, 
          simplifiedPoints
        );
        break;
    }
    
    // For park areas with trees at high zoom, add individual trees
    if (zoomLevel >= 18 && featureType.hasIndividualTrees) {
      _addIndividualTrees(canvas, simplifiedPoints, featureType.type);
    }
  }
  
  /// Render a point feature (e.g., individual tree)
  void _renderPointFeature(Canvas canvas, Size size, Map<String, dynamic> feature, Offset point) {
    final tags = feature['properties'] ?? {};
    
    if (tags['natural'] == 'tree') {
      String treeType = 'default';
      
      // Determine tree type if specified
      if (tags.containsKey('leaf_type')) {
        final leafType = tags['leaf_type'];
        if (leafType == 'broadleaved') {
          treeType = 'broadleaf';
        } else if (leafType == 'needleleaved') {
          treeType = 'conifer';
        }
      } else if (tags.containsKey('species')) {
        final species = tags['species'].toString().toLowerCase();
        if (species.contains('pine') || species.contains('spruce') || species.contains('fir')) {
          treeType = 'conifer';
        } else if (species.contains('palm')) {
          treeType = 'palm';
        } else if (species.contains('oak') || species.contains('maple') || species.contains('birch')) {
          treeType = 'broadleaf';
        }
      }
      
      // Get tree size based on attributes or default
      double treeSize = 1.0;
      if (tags.containsKey('height')) {
        try {
          final height = double.parse(tags['height'].toString());
          treeSize = (height / 10).clamp(0.5, 2.0); // Normalize height
        } catch (_) {}
      }
      
      // Add slight random rotation for variation
      final rotation = math.Random(42).nextDouble() * 0.5;
      
      // Render the tree using tree renderer
      _treeRenderer.renderTree(
        canvas,
        point,
        treeType: treeType,
        size: treeSize,
        rotation: rotation
      );
    }
  }
  
  /// Add decorative individual trees to park areas at high zoom
  void _addIndividualTrees(Canvas canvas, List<Offset> areaPoints, String areaType) {
    if (zoomLevel < 18 || !enhancedDetail) return;
    
    // Get area bounds
    final bounds = _calculateBounds(areaPoints);
    
    // Determine tree density based on area type
    double treeDensity = 0.0003; // Base density
    String primaryTreeType = 'default';
    
    // Adjust parameters based on area type
    switch (areaType) {
      case 'forest':
        treeDensity = 0.0006;
        primaryTreeType = 'broadleaf';
        break;
      case 'wood':
        treeDensity = 0.0005;
        primaryTreeType = math.Random().nextBool() ? 'conifer' : 'broadleaf';
        break;
      case 'park':
        treeDensity = 0.0002;
        primaryTreeType = 'broadleaf';
        break;
      case 'garden':
        treeDensity = 0.0001;
        primaryTreeType = 'broadleaf';
        break;
      default:
        treeDensity = 0.00005;
        primaryTreeType = 'default';
        break;
    }
    
    // Scale density based on zoom level
    treeDensity *= (zoomLevel - 17);
    
    // Calculate number of trees based on area and density
    final area = bounds.width * bounds.height;
    final numTrees = (area * treeDensity).round().clamp(0, 30);
    
    // Secondary tree type for diversity
    final String secondaryTreeType = primaryTreeType == 'broadleaf' ? 'conifer' : 'broadleaf';
    
    // Create a path for the area to check if points are inside
    final areaPath = Path()..addPolygon(areaPoints, true);
    
    // Create a random number generator with a fixed seed for consistency
    final random = math.Random(42);
    
    // Add trees at random positions within the area
    for (int i = 0; i < numTrees; i++) {
      // Generate position
      double x = bounds.left + random.nextDouble() * bounds.width;
      double y = bounds.top + random.nextDouble() * bounds.height;
      
      // Check if point is inside the area
      if (!_isPointInPath(areaPath, Offset(x, y))) continue;
      
      // Randomize tree type with bias toward primary type
      final String treeType = random.nextDouble() < 0.7 ? primaryTreeType : secondaryTreeType;
      
      // Randomize tree size
      final double treeSize = 0.8 + random.nextDouble() * 0.5;
      
      // Randomize rotation
      final double rotation = random.nextDouble() * math.pi * 0.1;
      
      // Render the tree
      _treeRenderer.renderTree(
        canvas,
        Offset(x, y),
        treeType: treeType,
        size: treeSize,
        rotation: rotation
      );
    }
  }
  
  /// Convert GeoJSON coordinates to screen points
  List<Offset> _convertCoordinatesToPoints(List coordinates, Size size) {
    List<Offset> points = [];
    
    for (final coord in coordinates) {
      if (coord is! List || coord.length < 2) continue;
      
      final double x = coord[0].toDouble() * size.width;
      final double y = coord[1].toDouble() * size.height;
      
      points.add(Offset(x, y));
    }
    
    return points;
  }
  
  /// Simplify polygon based on zoom level for performance
  List<Offset> _simplifyPolygon(List<Offset> points) {
    if (points.length <= 3) return points;
    
    // Adjust simplification tolerance based on zoom level
    double tolerance = 1.0;
    if (zoomLevel < 14) {
      tolerance = 2.0;
    } else if (zoomLevel < 16) {
      tolerance = 1.5;
    } else if (zoomLevel < 18) {
      tolerance = 1.0;
    } else {
      tolerance = 0.5;
    }
    
    // Use geometry utils to simplify the polygon
    return _geometryUtils.simplifyPolygon(points, tolerance);
  }
  
  /// Calculate bounds of a list of points
  Rect _calculateBounds(List<Offset> points) {
    if (points.isEmpty) return Rect.zero;
    
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;
    
    for (final point in points) {
      if (point.dx < minX) minX = point.dx;
      if (point.dy < minY) minY = point.dy;
      if (point.dx > maxX) maxX = point.dx;
      if (point.dy > maxY) maxY = point.dy;
    }
    
    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }
  
  /// Check if a point is inside a path (simple approximation)
  bool _isPointInPath(Path path, Offset point) {
    final Rect bounds = path.getBounds();
    if (!bounds.contains(point)) return false;
    
    // Simple implementation - in a full version, would use proper point-in-polygon algorithm
    return true;
  }
  
  /// Classify a feature based on OSM tags
  FeatureType _classifyFeature(Map<String, dynamic> feature) {
    final Map<String, dynamic> properties = feature['properties'] ?? {};
    final Map<String, dynamic> tags = properties['tags'] ?? properties;
    
    // Default classification
    String type = 'grass';
    bool hasIndividualTrees = false;
    
    // Check landuse tags
    if (tags.containsKey('landuse')) {
      final landuse = tags['landuse'].toString().toLowerCase();
      
      if (landuse == 'forest') {
        type = 'forest';
        hasIndividualTrees = true;
      } else if (landuse == 'meadow' || landuse == 'grassland') {
        type = 'meadow';
      } else if (landuse == 'orchard') {
        type = 'orchard';
        hasIndividualTrees = true;
      } else if (landuse == 'vineyard') {
        type = 'vineyard';
      } else if (landuse == 'grass') {
        type = 'grass';
      }
    }
    
    // Check natural tags
    if (tags.containsKey('natural')) {
      final natural = tags['natural'].toString().toLowerCase();
      
      if (natural == 'wood') {
        type = 'wood';
        hasIndividualTrees = true;
      } else if (natural == 'grassland') {
        type = 'meadow';
      } else if (natural == 'scrub') {
        type = 'meadow';
      }
    }
    
    // Check leisure tags
    if (tags.containsKey('leisure')) {
      final leisure = tags['leisure'].toString().toLowerCase();
      
      if (leisure == 'park') {
        type = 'park';
        hasIndividualTrees = zoomLevel >= 18;
      } else if (leisure == 'garden') {
        type = 'garden';
        hasIndividualTrees = zoomLevel >= 18;
      }
    }
    
    return FeatureType(type, hasIndividualTrees);
  }
  
  /// Check if a feature is a large natural area
  bool _isLargeNaturalArea(Map<String, dynamic> feature) {
    final featureType = _classifyFeature(feature);
    return featureType.type == 'forest' || featureType.type == 'wood' || featureType.type == 'meadow';
  }
  
  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    if (oldDelegate is ParksBasePainter) {
      return oldDelegate.zoomLevel != zoomLevel || 
             oldDelegate.theme != theme || 
             oldDelegate.season != season ||
             oldDelegate.enhancedDetail != enhancedDetail;
    }
    return true;
  }
}

/// Helper class to store feature classification information
class FeatureType {
  final String type;
  final bool hasIndividualTrees;
  
  FeatureType(this.type, this.hasIndividualTrees);
} 