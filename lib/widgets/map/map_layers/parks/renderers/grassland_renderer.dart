import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

import '../parks_colors.dart';
import '../parks_geometry_utils.dart';
import '../texture_generator.dart';

/// Specialized renderer for native grasslands such as prairies, savannas, and steppes
/// Provides enhanced rendering with texturing and seasonal variations
class GrasslandRenderer {
  final ParkGeometryUtils _geometryUtils = ParkGeometryUtils();
  final ParksColors _colorsHelper = ParksColors();
  final double zoomLevel;
  final String theme;
  final String season;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  late final TextureGenerator _textureGenerator;
  
  GrasslandRenderer({
    required this.zoomLevel,
    required this.theme,
    required this.season,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  }) {
    _textureGenerator = TextureGenerator(
      zoomLevel: zoomLevel,
      season: season,
    );
  }
  
  /// Normalize an offset (create a unit vector with the same direction)
  Offset _normalizeOffset(Offset offset) {
    final double length = math.sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
    if (length == 0) return Offset.zero;
    return Offset(offset.dx / length, offset.dy / length);
  }
  
  /// Main render method for grassland areas
  void renderGrasslandArea(Canvas canvas, Size size, Map<String, dynamic> feature, List<Offset> points, {String subtype = 'prairie'}) {
    if (points.length < 3) return;
    
    // Get colors for this grassland subtype (prairie, savanna, steppe)
    final String featureType = 'grassland_$subtype';
    final colors = _colorsHelper.getFeatureColors(featureType, theme, season);
    
    // Create the base path for the grassland area
    final Path grasslandPath = Path()..addPolygon(points, true);
    final Rect bounds = grasslandPath.getBounds();
    
    // Calculate appropriate elevation for the grassland type
    final double baseElevation = _getBaseElevation(subtype);
    final double elevationMultiplier = _getElevationMultiplier(bounds);
    // Apply non-linear tilt enhancement from buildings renderer
    final double scaledElevation = baseElevation * _getElevationFactor() * elevationMultiplier * _getZoomTiltEnhancement();
    
    // Draw enhanced shadow with softer edges for subtle 3D effect
    if (scaledElevation > 0.4 && zoomLevel >= 15) {
      _drawEnhancedShadow(canvas, grasslandPath, scaledElevation);
    }
    
    // Add elevated sides for stronger 2.5D effect at higher zoom levels
    if (scaledElevation > 0.5 && zoomLevel >= 16 && enhancedDetail) {
      _addGrasslandElevatedSides(canvas, grasslandPath, points, colors, scaledElevation);
    }
    
    // Apply subtle elevation effect for large areas at higher zoom levels
    if (bounds.width > 50 && bounds.height > 50 && zoomLevel >= 15 && enhancedDetail) {
      _drawElevationEffect(canvas, grasslandPath, colors);
    }
    
    // Draw main fill with appropriate paint - use color variation for enhanced realism
    final Paint fillPaint = _getGrasslandFillPaint(colors, bounds, subtype);
    canvas.drawPath(grasslandPath, fillPaint);
    
    // Draw borders for definition at higher zoom levels
    if (zoomLevel >= 15) {
      final Paint borderPaint = Paint()
        ..color = _getBorderColor(colors['base']!)
        ..style = PaintingStyle.stroke
        ..strokeWidth = _getBorderWidth()
        ..isAntiAlias = true;
      
      canvas.drawPath(grasslandPath, borderPaint);
    }
    
    // Add texture at higher zoom levels
    if (zoomLevel >= 15 && enhancedDetail) {
      _addGrasslandTexture(canvas, grasslandPath, colors, subtype);
    }
    
    // Add special features for specific grassland types
    if (zoomLevel >= 16 && enhancedDetail) {
      switch (subtype) {
        case 'savanna':
          _addSavannaFeatures(canvas, grasslandPath, colors);
          break;
        case 'prairie':
          _addPrairieFeatures(canvas, grasslandPath, colors);
          break;
        case 'steppe':
          _addSteppeFeatures(canvas, grasslandPath, colors);
          break;
      }
    }
    
    // Add ambient occlusion around edges for more depth
    if (zoomLevel >= 16) {
      _addAmbientOcclusion(canvas, grasslandPath, scaledElevation);
    }
  }
  
  /// Get base elevation for different grassland subtypes
  double _getBaseElevation(String subtype) {
    switch (subtype) {
      case 'prairie':
        return 1.8; // Prairies have significantly taller grasses (enhanced from 1.4)
      case 'savanna':
        return 1.6; // Savannas have substantial height due to scattered trees (enhanced from 1.2)
      case 'steppe':
        return 1.4; // Steppes have good height for visibility (enhanced from 1.1)
      default:
        return 1.5; // Default with enhanced height for better 3D effect (enhanced from 1.2)
    }
  }
  
  /// Get elevation factor based on zoom level with more aggressive scaling
  double _getElevationFactor() {
    // Enhanced scaling algorithm based on zoom level and tilt factor
    if (zoomLevel < 12) return 0.7 * tiltFactor;
    if (zoomLevel < 14) return 0.9 * tiltFactor;
    if (zoomLevel < 16) return 1.2 * tiltFactor;
    if (zoomLevel < 18) return 1.8 * tiltFactor;
    if (zoomLevel < 20) return 2.3 * tiltFactor;
    return 2.7 * tiltFactor; // Increased maximum elevation at highest zoom
  }
  
  /// Non-linear tilt enhancement like buildings renderer
  double _getZoomTiltEnhancement() {
    if (zoomLevel <= 14) return 1.0;
    if (zoomLevel >= 20) return 2.8;
    
    // Non-linear power function for smooth transition
    return 1.0 + math.pow((zoomLevel - 14) / 6, 1.2) * 1.8;
  }
  
  /// Calculate appropriate elevation multiplier based on feature size
  double _getElevationMultiplier(Rect bounds) {
    // Dynamic and non-linear scaling for more natural appearance
    final double area = bounds.width * bounds.height;
    if (area > 50000) return 0.7;
    if (area > 20000) return 0.8;
    if (area > 10000) return 0.9;
    if (area > 5000) return 1.0;
    if (area > 2000) return 1.1;
    if (area > 1000) return 1.2;
    return 1.3; // Smallest areas get significant emphasis for visibility
  }
  
  /// Draw enhanced shadow with proper perspective and softer edges
  void _drawEnhancedShadow(Canvas canvas, Path grasslandPath, double elevation) {
    // Enhanced shadow opacity calculations
    final double baseOpacity = 0.3 * _getElevationFactor();
    final double zoomEnhancement = math.min(1.6, 1.0 + (zoomLevel - 15) * 0.18);
    final double shadowOpacity = math.min(0.5, baseOpacity * zoomEnhancement);
    
    // Improved shadow offset with better directional light component
    final double offsetMultiplier = math.min(1.0 + (zoomLevel - 15) * 0.15, 2.0);
    final double offsetX = 2.2 * elevation * 0.3 * offsetMultiplier;
    final double offsetY = 2.2 * elevation * 0.3 * offsetMultiplier;
    
    // Use matrix transformation for perspective-correct shadows like buildings
    final Matrix4 shadowMatrix = Matrix4.identity()
      ..translate(offsetX, offsetY)
      ..scale(1.03 + elevation * 0.025, 1.06 + elevation * 0.035); // More pronounced vertical stretch
    
    final Path shadowPath = grasslandPath.transform(shadowMatrix.storage);
    
    // Enhanced shadow quality with improved blur effects
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(shadowOpacity)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, math.min(4.5, 1.5 + elevation * 0.35))
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw shadow beneath the grassland with better quality
    canvas.drawPath(shadowPath, shadowPaint);
  }
  
  /// Create grassland-specific elevated sides for better 2.5D effect
  void _addGrasslandElevatedSides(Canvas canvas, Path grasslandPath, List<Offset> points, Map<String, Color> colors, double elevation) {
    if (elevation < 0.4 || zoomLevel < 16) return;
    
    // Enhanced colors for sides with better depth effect and saturation
    final Color baseSideColor = _getSideColor(colors['base']!);
    
    // Calculate normals for better corner handling (from buildings technique)
    List<Offset> normals = [];
    for (int i = 0; i < points.length; i++) {
      final Offset prev = points[(i - 1 + points.length) % points.length];
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      
      // Calculate normal at this point by averaging adjacent segment normals
      final Offset v1 = current - prev;
      final Offset v2 = next - current;
      
      final Offset n1 = _normalizeOffset(Offset(-v1.dy, v1.dx));
      final Offset n2 = _normalizeOffset(Offset(-v2.dy, v2.dx));
      final Offset avgNormal = _normalizeOffset(Offset((n1.dx + n2.dx) / 2, (n1.dy + n2.dy) / 2));
      
      normals.add(avgNormal);
    }
    
    // Create side path with enhanced directional lighting
    final Path sidePath = Path();
    
    // Use buildings technique: Wall orientation detection
    for (int i = 0; i < points.length; i++) {
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      final Offset currentNormal = normals[i];
      final Offset nextNormal = normals[(i + 1) % points.length];
      
      // Calculate wall direction to determine if it faces viewer
      final Offset wallDir = _normalizeOffset(next - current);
      final bool isWallFacingViewer = wallDir.dy >= 0 || wallDir.dx.abs() > 0.8;
      
      // Only draw sides for segments facing the viewer
      if (isWallFacingViewer) {
        // Start with current point
        if (!sidePath.contains(current)) {
          sidePath.moveTo(current.dx, current.dy);
        }
        
        // Draw to next point
        sidePath.lineTo(next.dx, next.dy);
        
        // Enhanced grassland-specific undulations - more subtle than meadows
        final double undulationFactor = 0.2 + _random.nextDouble() * 0.2;
        final double midPointElevation = elevation * (0.9 + undulationFactor);
        
        // Draw with enhanced perspective
        final double perspectiveFactorNext = 0.15 + elevation * 0.01;
        sidePath.lineTo(
          next.dx + nextNormal.dx * elevation * perspectiveFactorNext, 
          next.dy + elevation + nextNormal.dy * elevation * 0.12
        );
        
        // Draw back to current point with enhanced perspective
        final double perspectiveFactorCurrent = 0.15 + elevation * 0.01;
        sidePath.lineTo(
          current.dx + currentNormal.dx * elevation * perspectiveFactorCurrent, 
          current.dy + elevation + currentNormal.dy * elevation * 0.12
        );
        
        // Close this side segment
        sidePath.lineTo(current.dx, current.dy);
      }
    }
    
    // Create enhanced gradient for dynamic lighting effect with directional light
    final Rect bounds = sidePath.getBounds();
    
    // Use multi-stop gradient with directional lighting like buildings
    final Paint sidePaint = Paint()
      ..shader = LinearGradient(
        begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
        end: const Alignment(0.3, 0.0),
        colors: [
          baseSideColor,
          HSLColor.fromColor(baseSideColor)
            .withLightness(math.max(0.1, HSLColor.fromColor(baseSideColor).lightness * 0.5))
            .toColor(),
        ],
        stops: const [0.7, 1.0],
      ).createShader(bounds)
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw enhanced grassland sides
    canvas.drawPath(sidePath, sidePaint);
    
    // Add subtle edge highlight for better definition
    final Paint edgePaint = Paint()
      ..color = Colors.black.withOpacity(0.25)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    canvas.drawPath(sidePath, edgePaint);
  }
  
  /// Get a paint for filling grassland areas with enhanced directional lighting
  Paint _getGrasslandFillPaint(Map<String, Color> colors, Rect bounds, String subtype) {
    // For low zoom levels, use a simple solid color
    if (zoomLevel < 14 || !enhancedDetail) {
      return Paint()
        ..color = colors['base']!
        ..style = PaintingStyle.fill
        ..isAntiAlias = true;
    }
    
    // Apply color variation like buildings renderer
    final Color baseColor = _addColorVariation(colors['base']!);
    final Color highlightColor = _addColorVariation(colors['highlight']!);
    final Color detailColor = _addColorVariation(colors['detail']!);
    
    // Use directional lighting with multi-stop gradients like buildings
    switch (subtype) {
      case 'prairie':
        return Paint()
          ..shader = LinearGradient(
            begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
            end: const Alignment(0.3, 0.0),
            colors: [
              highlightColor,
              baseColor,
              detailColor,
            ],
            stops: const [0.0, 0.6, 1.0],
          ).createShader(bounds)
          ..style = PaintingStyle.fill;
      case 'savanna':
        return Paint()
          ..shader = LinearGradient(
            begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
            end: const Alignment(0.3, 0.0),
            colors: [
              _colorsHelper.adjustBrightness(highlightColor, 0.05),
              baseColor,
              _colorsHelper.adjustBrightness(detailColor, -0.05),
            ],
            stops: const [0.0, 0.5, 1.0],
          ).createShader(bounds)
          ..style = PaintingStyle.fill;
      case 'steppe':
        return Paint()
          ..shader = LinearGradient(
            begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
            end: const Alignment(0.3, 0.0),
            colors: [
              highlightColor,
              baseColor,
              _colorsHelper.adjustBrightness(detailColor, -0.1),
            ],
            stops: const [0.0, 0.7, 1.0],
          ).createShader(bounds)
          ..style = PaintingStyle.fill;
      default:
        return Paint()
          ..shader = LinearGradient(
            begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
            end: const Alignment(0.3, 0.0),
            colors: [
              highlightColor,
              baseColor,
              detailColor,
            ],
            stops: const [0.0, 0.5, 1.0],
          ).createShader(bounds)
          ..style = PaintingStyle.fill;
    }
  }
  
  /// Add ambient occlusion around edges for enhanced 3D depth
  void _addAmbientOcclusion(Canvas canvas, Path grasslandPath, double elevation) {
    // Create inset path for ambient occlusion with dynamic inset based on feature size
    final double inset = math.max(1.0, math.min(3.0, elevation * 0.4));
    
    // Create AO path by insetting the original path
    final Path aoPath = Path.combine(
      PathOperation.difference,
      grasslandPath,
      Path.combine(
        PathOperation.intersect,
        grasslandPath,
        Path()..addRect(grasslandPath.getBounds().inflate(-inset))
      )
    );
    
    // Draw ambient occlusion with sophisticated AO effect
    final Rect bounds = grasslandPath.getBounds();
    final Paint aoPaint = Paint()
      ..shader = RadialGradient(
        center: Alignment.center,
        radius: 1.0,
        colors: [
          Colors.black.withOpacity(0.3 + elevation * 0.025),
          Colors.black.withOpacity(0.0),
        ],
        stops: const [0.0, 1.0],
      ).createShader(bounds)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 2.0)
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(aoPath, aoPaint);
  }
  
  /// Add subtle color variation like buildings renderer
  Color _addColorVariation(Color baseColor, {int variationAmount = 15}) {
    // Only add variation for higher zoom levels
    if (zoomLevel < 15) return baseColor;
    
    final int variation = math.min(variationAmount, 15);
    final int r = (baseColor.red + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int g = (baseColor.green + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int b = (baseColor.blue + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    
    return Color.fromRGBO(r, g, b, baseColor.opacity);
  }
  
  /// Get a dark color for the sides of the grassland with enhanced saturation
  Color _getSideColor(Color baseColor) {
    return HSLColor.fromColor(baseColor)
      .withSaturation(math.min(1.0, HSLColor.fromColor(baseColor).saturation * 1.2))
      .withLightness(math.max(0.15, HSLColor.fromColor(baseColor).lightness * 0.5))
      .toColor();
  }
  
  /// Get appropriate border color that provides good contrast
  Color _getBorderColor(Color baseColor) {
    return HSLColor.fromColor(baseColor)
      .withSaturation(math.min(1.0, HSLColor.fromColor(baseColor).saturation * 1.1))
      .withLightness(math.max(0.1, HSLColor.fromColor(baseColor).lightness * 0.7))
      .toColor()
      .withOpacity(0.4);
  }
  
  /// Get appropriate border width based on zoom level
  double _getBorderWidth() {
    if (zoomLevel >= 18) return 0.7;
    if (zoomLevel >= 16) return 0.5;
    return 0.3;
  }
  
  /// Create subtle elevation effect for grasslands
  void _drawElevationEffect(Canvas canvas, Path grasslandPath, Map<String, Color> colors) {
    // Create a slightly lighter version of the base color for elevation highlights
    final Color elevationColor = _colorsHelper.adjustBrightness(colors['base']!, 0.08);
    
    // Draw enhanced elevation contours with better visibility and naturalism
    final Paint elevationPaint = Paint()
      ..color = elevationColor.withOpacity(0.35)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.2;
    
    // Create an offset path for the elevation effect
    final Rect bounds = grasslandPath.getBounds();
    
    // Save the canvas state and clip to grassland area
    canvas.save();
    canvas.clipPath(grasslandPath);
    
    // Draw a series of subtly spaced contour lines with enhanced realism
    final int numContours = (zoomLevel > 16) ? 6 : 4;
    final double contourSpacing = bounds.height / (numContours + 1);
    
    for (int i = 1; i <= numContours; i++) {
      final double y = bounds.top + contourSpacing * i;
      
      // Create a more complex wavy contour line for enhanced natural appearance
      final Path contourPath = Path();
      contourPath.moveTo(bounds.left, y);
      
      // Add more realistic randomness to the contour line for a more natural look
      double currentX = bounds.left;
      final double stepX = bounds.width / 25; // More points for smoother curves
      
      // Add multi-frequency variation for more natural terrain
      double phase = _random.nextDouble() * math.pi * 2;
      
      while (currentX < bounds.right) {
        final double nextX = currentX + stepX;
        
        // Enhanced undulation with multiple frequencies for natural terrain
        final double nextY = y + 
                (_random.nextDouble() * 3 - 1.5) + 
                math.sin(currentX / bounds.width * math.pi * 2 + phase) * 2.0 +
                math.sin(currentX / bounds.width * math.pi * 4 + phase) * 1.0;
        
        contourPath.lineTo(nextX, nextY);
        currentX = nextX;
      }
      
      // Draw with enhanced appearance
      canvas.drawPath(contourPath, elevationPaint);
      
      // Add perpendicular lines at random points for more terrain detail at high zoom
      if (zoomLevel >= 17) {
        final int numCrossLines = math.min(5, (bounds.width / 150).round()) + 1;
        
        for (int j = 0; j < numCrossLines; j++) {
          final double x = bounds.left + bounds.width * (j + 0.5) / (numCrossLines + 1);
          final double length = 5.0 + _random.nextDouble() * 10.0;
          
          final Paint crossPaint = Paint()
            ..color = elevationColor.withOpacity(0.25)
            ..style = PaintingStyle.stroke
            ..strokeWidth = 0.8;
          
          // Draw short perpendicular lines for enhanced detail
          canvas.drawLine(
            Offset(x - length/2, y + (_random.nextDouble() * 4 - 2)),
            Offset(x + length/2, y + (_random.nextDouble() * 4 - 2)),
            crossPaint
          );
        }
      }
    }
    
    // Restore canvas state
    canvas.restore();
  }
  
  /// Add texture to grassland areas based on their subtype
  void _addGrasslandTexture(Canvas canvas, Path grasslandPath, Map<String, Color> colors, String subtype) {
    // Skip detailed textures at lower zoom levels
    if (zoomLevel < 15) return;
    
    final Rect bounds = grasslandPath.getBounds();
    
    // Save canvas state to clip to the grassland area
    canvas.save();
    canvas.clipPath(grasslandPath);
    
    // Use the texture generator to add appropriate texture
    String textureType;
    double density;
    
    switch (subtype) {
      case 'savanna':
        textureType = 'savanna';
        density = 0.8; // Savannas are sparser
        break;
      case 'steppe':
        textureType = 'steppe';
        density = 0.9; // Steppes are moderate
        break;
      case 'prairie':
      default:
        textureType = 'prairie';
        density = 1.2; // Prairies are dense
    }
    
    // Generate appropriate grass texture
    _textureGenerator.generateGrassTexture(
      canvas, 
      bounds, 
      colors, 
      textureType: textureType, 
      density: density
    );
    
    // Restore canvas state
    canvas.restore();
  }
  
  /// Add special features for savanna grasslands (scattered trees, etc.)
  void _addSavannaFeatures(Canvas canvas, Path grasslandPath, Map<String, Color> colors) {
    if (zoomLevel < 16) return;
    
    final Rect bounds = grasslandPath.getBounds();
    
    // Save canvas state to clip to the grassland area
    canvas.save();
    canvas.clipPath(grasslandPath);
    
    // Add scattered trees - characteristic of savannas
    final int numTrees = (bounds.width * bounds.height / 5000).round();
    final Color treeColor = _colorsHelper.adjustBrightness(colors['detail']!, -0.2);
    final Paint treePaint = Paint()
      ..color = treeColor.withOpacity(0.6)
      ..style = PaintingStyle.fill;
    
    for (int i = 0; i < numTrees; i++) {
      final double x = bounds.left + _random.nextDouble() * bounds.width;
      final double y = bounds.top + _random.nextDouble() * bounds.height;
      
      // Draw tree
      canvas.drawCircle(Offset(x, y), 1.5 + _random.nextDouble() * 2.0, treePaint);
      
      // Add shadow at higher zoom levels
      if (zoomLevel >= 17) {
        final Paint shadowPaint = Paint()
          ..color = Colors.black.withOpacity(0.2)
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(
          Offset(x + 1.0, y + 1.0), 
          1.0 + _random.nextDouble() * 1.5, 
          shadowPaint
        );
      }
    }
    
    // Restore canvas state
    canvas.restore();
  }
  
  /// Add special features for prairie grasslands (wildflowers, etc.)
  void _addPrairieFeatures(Canvas canvas, Path grasslandPath, Map<String, Color> colors) {
    if (zoomLevel < 16) return;
    
    final Rect bounds = grasslandPath.getBounds();
    
    // Save canvas state to clip to the grassland area
    canvas.save();
    canvas.clipPath(grasslandPath);
    
    // Add wildflower patches in spring and summer
    if ((season == 'spring' || season == 'summer') && zoomLevel >= 17) {
      final int numPatches = (bounds.width * bounds.height / 10000).round();
      
      for (int i = 0; i < numPatches; i++) {
        final double x = bounds.left + _random.nextDouble() * bounds.width;
        final double y = bounds.top + _random.nextDouble() * bounds.height;
        final double size = 3.0 + _random.nextDouble() * 5.0;
        
        // Seasonal color variations
        Color patchColor;
        if (season == 'spring') {
          final List<Color> springColors = [
            Colors.purple.withOpacity(0.3),
            Colors.blue.withOpacity(0.3),
            Colors.yellow.withOpacity(0.3),
          ];
          patchColor = springColors[_random.nextInt(springColors.length)];
        } else {
          final List<Color> summerColors = [
            Colors.yellow.withOpacity(0.3),
            Colors.orange.withOpacity(0.3),
            Colors.pink.withOpacity(0.3),
            Colors.purple.withOpacity(0.3),
          ];
          patchColor = summerColors[_random.nextInt(summerColors.length)];
        }
        
        final Paint patchPaint = Paint()
          ..color = patchColor
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(x, y), size, patchPaint);
      }
    }
    
    // Restore canvas state
    canvas.restore();
  }
  
  /// Add special features for steppe grasslands (rocky outcrops, etc.)
  void _addSteppeFeatures(Canvas canvas, Path grasslandPath, Map<String, Color> colors) {
    if (zoomLevel < 16) return;
    
    final Rect bounds = grasslandPath.getBounds();
    
    // Save canvas state to clip to the grassland area
    canvas.save();
    canvas.clipPath(grasslandPath);
    
    // Add rocky outcrops - characteristic of steppes
    final int numRocks = (bounds.width * bounds.height / 8000).round();
    final Color rockColor = Colors.grey.shade700.withOpacity(0.6);
    final Paint rockPaint = Paint()
      ..color = rockColor
      ..style = PaintingStyle.fill;
    
    for (int i = 0; i < numRocks; i++) {
      final double x = bounds.left + _random.nextDouble() * bounds.width;
      final double y = bounds.top + _random.nextDouble() * bounds.height;
      
      // Draw rocky outcrop
      if (_random.nextBool()) {
        // Circular rock
        canvas.drawCircle(Offset(x, y), 1.0 + _random.nextDouble() * 1.5, rockPaint);
      } else {
        // Irregular rock shape
        final Path rockPath = Path();
        rockPath.moveTo(x, y);
        
        final int numPoints = 5 + _random.nextInt(3);
        final double radius = 1.0 + _random.nextDouble() * 2.0;
        
        for (int j = 0; j < numPoints; j++) {
          final double angle = j * (2 * math.pi / numPoints);
          final double jitter = 0.5 + _random.nextDouble() * 0.5;
          rockPath.lineTo(
            x + math.cos(angle) * radius * jitter,
            y + math.sin(angle) * radius * jitter
          );
        }
        
        rockPath.close();
        canvas.drawPath(rockPath, rockPaint);
      }
    }
    
    // Restore canvas state
    canvas.restore();
  }
} 