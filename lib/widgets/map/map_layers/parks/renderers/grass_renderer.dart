import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

import '../parks_colors.dart';
import '../parks_geometry_utils.dart';

/// Specialized renderer for grass, meadows, parks and other natural grassy areas
/// Provides enhanced rendering with texturing and seasonal variations
class GrassRenderer {
  final ParkGeometryUtils _geometryUtils = ParkGeometryUtils();
  final ParksColors _colorsHelper = ParksColors();
  final double zoomLevel;
  final String theme;
  final String season;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  
  GrassRenderer({
    required this.zoomLevel,
    required this.theme,
    required this.season,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  });
  
  /// Normalize an offset (create a unit vector with the same direction)
  Offset _normalizeOffset(Offset offset) {
    final double length = math.sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
    if (length == 0) return Offset.zero;
    return Offset(offset.dx / length, offset.dy / length);
  }
  
  /// Main render method for grassy areas - SIMPLIFIED FOR BETTER PERFORMANCE
  void renderGrassArea(Canvas canvas, Size size, Map<String, dynamic> feature, List<Offset> points, {String subtype = 'grass'}) {
    if (points.length < 3) return;
    
    // Get colors for this grass subtype (grass, meadow, park, garden, etc)
    final String featureType = subtype;
    final colors = _colorsHelper.getFeatureColors(featureType, theme, season);
    
    // Create the base path for the grass area
    final Path grassPath = Path()..addPolygon(points, true);
    
    // Get the base elevation but reduce it for better performance
    final double baseElevation = _getBaseElevation(subtype) * 0.7;
    final double scaledElevation = baseElevation * tiltFactor;
    
    // SIMPLIFIED: Only draw shadow if we have significant elevation and decent zoom
    if (scaledElevation > 0.5 && tiltFactor > 0.2 && zoomLevel >= 16) {
      _drawSimplifiedShadow(canvas, grassPath, scaledElevation);
    }
    
    // Draw main fill with plain color for better performance
    final Paint fillPaint = Paint()
      ..color = colors['base']!
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    canvas.drawPath(grassPath, fillPaint);
    
    // Only draw border at higher zoom levels
    if (zoomLevel >= 16) {
      final Paint borderPaint = Paint()
        ..color = _getBorderColor(colors['base']!)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.5
        ..isAntiAlias = true;
      
      canvas.drawPath(grassPath, borderPaint);
    }
    
    // REMOVED: Extra texture, park features, ambient occlusion and elevated sides 
    // to improve performance during animations
  }
  
  /// Get base elevation for different grass subtypes (simplified values)
  double _getBaseElevation(String subtype) {
    switch (subtype) {
      case 'meadow':
        return 1.0;
      case 'park':
        return 0.8;
      case 'garden':
        return 0.8;
      default:
        return 0.7;
    }
  }
  
  /// Draw a simplified shadow for better performance
  void _drawSimplifiedShadow(Canvas canvas, Path grassPath, double elevation) {
    final double shadowOpacity = 0.2 * tiltFactor;
    
    // Simple offset calculation
    final double offsetX = 1.5 * elevation;
    final double offsetY = 1.5 * elevation;
    
    // Non-uniform shadow scaling for better perspective effect
    final Matrix4 shadowMatrix = Matrix4.identity()
      ..translate(offsetX, offsetY)
      ..scale(1.02, 1.02);
    
    final Path shadowPath = grassPath.transform(shadowMatrix.storage);
    
    // Simple shadow with minimal blur
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(shadowOpacity)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 1.5)
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw shadow beneath the grass
    canvas.drawPath(shadowPath, shadowPaint);
  }
  
  /// Get border color with simplified calculation
  Color _getBorderColor(Color baseColor) {
    return HSLColor.fromColor(baseColor)
      .withLightness(math.max(0.1, HSLColor.fromColor(baseColor).lightness - 0.1))
      .toColor()
      .withOpacity(0.6);
  }
} 