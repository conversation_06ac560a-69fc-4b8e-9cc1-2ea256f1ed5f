import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

import '../parks_colors.dart';
import '../shadow_renderer.dart';

/// Enhanced renderer for individual trees with sophisticated 3D effects
/// Provides detailed tree rendering with realistic shadowing, lighting, and perspective
class TreeRenderer {
  final ParksColors _colorsHelper = ParksColors();
  final double zoomLevel;
  final String theme;
  final String season;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  late final ShadowRenderer _shadowRenderer;
  
  TreeRenderer({
    required this.zoomLevel,
    required this.theme,
    required this.season,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  }) {
    _shadowRenderer = ShadowRenderer(
      zoomLevel: zoomLevel,
      tiltFactor: tiltFactor,
    );
  }
  
  /// Main method to render a single tree with enhanced 3D effects
  void renderTree(Canvas canvas, Offset position, {
    String treeType = 'default',
    double size = 1.0,
    double rotation = 0.0,
  }) {
    // Only render trees at higher zoom levels
    if (zoomLevel < 17) return;
    
    // Get the appropriate colors for the tree type
    final Map<String, Color> colors = _getTreeColors(treeType);
    
    // Scale the tree based on zoom level and provided size with enhanced variable
    final double scaledSize = size * _getZoomScaleFactor();
    
    // Draw appropriate tree type with enhanced 3D rendering
    switch (treeType) {
      case 'pine':
      case 'conifer':
        _drawEnhancedConiferTree(canvas, position, colors, scaledSize, rotation);
        break;
      case 'palm':
        _drawEnhancedPalmTree(canvas, position, colors, scaledSize, rotation);
        break;
      case 'deciduous':
      case 'broadleaf':
      default:
        _drawEnhancedBroadleafTree(canvas, position, colors, scaledSize, rotation);
        break;
    }
  }
  
  /// Get appropriate colors for tree type and season
  Map<String, Color> _getTreeColors(String treeType) {
    final String featureType = _mapTreeTypeToFeature(treeType);
    return _colorsHelper.getFeatureColors(featureType, theme, season);
  }
  
  /// Map tree type to feature type for color lookup
  String _mapTreeTypeToFeature(String treeType) {
    switch (treeType) {
      case 'pine':
      case 'conifer':
        return 'forest';
      case 'palm':
        return 'wood';
      case 'deciduous':
      case 'broadleaf':
      default:
        return 'forest';
    }
  }
  
  /// Draw enhanced conifer/pine tree with 3D perspective and lighting
  void _drawEnhancedConiferTree(Canvas canvas, Offset position, Map<String, Color> colors, double size, double rotation) {
    // Enhanced shadow with perspective distortion
    _drawEnhancedTreeShadow(canvas, position, size, treeType: 'conifer');
    
    // Enhanced 3D trunk with perspective and lighting
    _drawEnhanced3DTrunk(canvas, position, size, treeType: 'conifer', rotation: rotation);
    
    // Draw conifer shape with multiple triangle layers and enhanced 3D lighting
    final int layers = zoomLevel >= 18 ? 4 : 3; // Extra layer for more detail
    final double layerWidth = size * 1.2;
    final double layerHeight = size * 1.8;
    final double layerSpacing = size * 0.4;
    
    // Base foliage color enhanced with season-appropriate saturation
    final Color foliageBaseColor = _adjustForSeason(colors['base']!);
    
    for (int i = 0; i < layers; i++) {
      final double layerY = position.dy - (i * layerSpacing) - size * 0.5;
      final double currentWidth = layerWidth * (1 - (i * 0.2));
      final double currentHeight = layerHeight * (0.8 - (i * 0.15));
      
      // Create 3D triangle with perspective
      final Path treePath = Path();
      treePath.moveTo(position.dx, layerY - currentHeight / 2); // Taller peak
      treePath.lineTo(position.dx + currentWidth / 2, layerY + currentHeight / 2);
      treePath.lineTo(position.dx - currentWidth / 2, layerY + currentHeight / 2);
      treePath.close();
      
      // Enhanced gradient for 3D lighting effect based on layer position
      final Paint layerPaint = Paint()
        ..shader = RadialGradient(
          center: const Alignment(-0.3, -0.3), // Light from top-left
          radius: 1.2,
          colors: [
            HSLColor.fromColor(foliageBaseColor).withLightness(
              math.min(0.7, HSLColor.fromColor(foliageBaseColor).lightness * 1.5)
            ).toColor(),
            foliageBaseColor,
            HSLColor.fromColor(foliageBaseColor).withLightness(
              math.max(0.2, HSLColor.fromColor(foliageBaseColor).lightness * 0.7)
            ).toColor(),
          ],
          stops: const [0.1, 0.5, 1.0],
        ).createShader(treePath.getBounds())
        ..style = PaintingStyle.fill;
      
      canvas.drawPath(treePath, layerPaint);
      
      // Add edge highlight for better definition
      final Paint edgePaint = Paint()
        ..color = Colors.black.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.7;
      
      canvas.drawPath(treePath, edgePaint);
    }
    
    // Add enhanced detail highlights at high zoom for sun through branches effect
    if (zoomLevel >= 18 && enhancedDetail) {
      final Paint highlightPaint = Paint()
        ..color = colors['highlight']!.withOpacity(0.4)
        ..style = PaintingStyle.fill;
      
      final int numHighlights = 5;
      
      for (int i = 0; i < numHighlights; i++) {
        final double highlightX = position.dx + ((_random.nextDouble() * 2) - 1.0) * size * 0.4;
        final double highlightY = position.dy - size * 0.5 - _random.nextDouble() * size * 0.8;
        final double highlightSize = 0.3 + _random.nextDouble() * 0.5;
        
        canvas.drawCircle(Offset(highlightX, highlightY), highlightSize, highlightPaint);
      }
    }
  }
  
  /// Draw enhanced broadleaf/deciduous tree with 3D perspective and realistic canopy
  void _drawEnhancedBroadleafTree(Canvas canvas, Offset position, Map<String, Color> colors, double size, double rotation) {
    // Enhanced shadow with perspective distortion
    _drawEnhancedTreeShadow(canvas, position, size, treeType: 'broadleaf');
    
    // Enhanced 3D trunk with perspective and lighting
    _drawEnhanced3DTrunk(canvas, position, size, treeType: 'broadleaf', rotation: rotation);
    
    // Enhanced 3D foliage with realistic shape and lighting
    // Season affects canopy appearance
    double canopyWidth = size * 1.8;
    double canopyHeight = size * 2.0; // Taller canopy for better 3D appearance
    
    // Get base foliage color adjusted for season
    final Color foliageColor = _adjustForSeason(colors['base']!);
    
    // Create foliage position with appropriate offset from trunk
    final Offset foliagePosition = Offset(
      position.dx,
      position.dy - (size * 0.7) // Position canopy at top of trunk
    );
    
    // Create enhanced 3D gradient with better lighting
    final Paint foliagePaint = Paint()
      ..shader = RadialGradient(
        center: const Alignment(-0.4, -0.4), // Light from top-left
        radius: 1.2,
        colors: [
          HSLColor.fromColor(foliageColor).withLightness(
            math.min(0.8, HSLColor.fromColor(foliageColor).lightness * 1.6)
          ).toColor(),
          foliageColor,
          HSLColor.fromColor(foliageColor).withLightness(
            math.max(0.15, HSLColor.fromColor(foliageColor).lightness * 0.6)
          ).toColor(),
        ],
        stops: const [0.1, 0.5, 0.9],
      ).createShader(Rect.fromCenter(
        center: foliagePosition,
        width: canopyWidth,
        height: canopyHeight
      ))
      ..style = PaintingStyle.fill;
    
    // Draw enhanced 3D foliage
    canvas.drawOval(
      Rect.fromCenter(
        center: foliagePosition, 
        width: canopyWidth, 
        height: canopyHeight
      ),
      foliagePaint
    );
    
    // Add subtle edge highlight for better definition
    final Paint edgePaint = Paint()
      ..color = Colors.black.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.8;
    
    canvas.drawOval(
      Rect.fromCenter(
        center: foliagePosition, 
        width: canopyWidth, 
        height: canopyHeight
      ),
      edgePaint
    );
    
    // Add subtle highlight spots to enhance 3D appearance at high zoom
    if (zoomLevel >= 18 && enhancedDetail) {
      final Paint highlightPaint = Paint()
        ..color = Colors.white.withOpacity(0.3)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        Offset(foliagePosition.dx - canopyWidth * 0.2, foliagePosition.dy - canopyHeight * 0.25),
        canopyWidth * 0.1,
        highlightPaint
      );
    }
  }
  
  /// Draw enhanced palm tree with 3D effects
  void _drawEnhancedPalmTree(Canvas canvas, Offset position, Map<String, Color> colors, double size, double rotation) {
    // Enhanced shadow with perspective distortion
    _drawEnhancedTreeShadow(canvas, position, size, treeType: 'palm');
    
    // Palm trunk is taller and narrower
    _drawEnhanced3DTrunk(
      canvas, 
      position, 
      size, 
      treeType: 'palm', 
      heightMultiplier: 1.5, 
      widthMultiplier: 0.7, 
      rotation: rotation
    );
    
    // Create palm fronds as individual shapes
    final Color frondColor = _adjustForSeason(colors['base']!);
    final double frondSize = size * 1.0;
    
    // Create frond position at top of trunk
    final Offset frondPosition = Offset(
      position.dx,
      position.dy - (size * 1.2) // Position fronds at top of tall trunk
    );
    
    // Draw multiple fronds in a radial pattern
    final int numFronds = 7;
    for (int i = 0; i < numFronds; i++) {
      final double angle = (i * (math.pi * 2 / numFronds)) + rotation;
      
      // Create a curved path for each frond
      final Path frondPath = Path();
      frondPath.moveTo(frondPosition.dx, frondPosition.dy);
      
      // Create a curved frond with bezier curve
      frondPath.quadraticBezierTo(
        frondPosition.dx + math.cos(angle) * frondSize * 0.5,
        frondPosition.dy + math.sin(angle) * frondSize * 0.5 - frondSize * 0.3,
        frondPosition.dx + math.cos(angle) * frondSize,
        frondPosition.dy + math.sin(angle) * frondSize * 0.8
      );
      
      // Widen the frond
      frondPath.quadraticBezierTo(
        frondPosition.dx + math.cos(angle) * frondSize * 0.6,
        frondPosition.dy + math.sin(angle) * frondSize * 0.6,
        frondPosition.dx,
        frondPosition.dy
      );
      
      // Paint with gradient for 3D effect
      final Paint frondPaint = Paint()
        ..shader = LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            HSLColor.fromColor(frondColor).withLightness(
              math.min(0.8, HSLColor.fromColor(frondColor).lightness * 1.4)
            ).toColor(),
            frondColor,
          ],
        ).createShader(frondPath.getBounds())
        ..style = PaintingStyle.fill;
      
      canvas.drawPath(frondPath, frondPaint);
    }
  }
  
  /// Draw enhanced 3D trunk with perspective and lighting
  void _drawEnhanced3DTrunk(Canvas canvas, Offset position, double size, {
    required String treeType,
    double widthMultiplier = 1.0,
    double heightMultiplier = 1.0,
    double rotation = 0.0
  }) {
    // Get trunk color based on tree type
    final Color trunkBaseColor = _getTrunkColor(treeType);
    
    // Calculate trunk dimensions with modifiers
    final double trunkWidth = size * 0.25 * widthMultiplier;
    final double trunkHeight = size * 0.8 * heightMultiplier;
    final double perspectiveNarrow = 0.3; // Perspective narrowing factor
    
    // Apply slight rotation for variety
    canvas.save();
    canvas.translate(position.dx, position.dy);
    canvas.rotate(rotation);
    
    // Create trunk path with perspective (narrower at top)
    final Path trunkPath = Path()
      ..moveTo(-trunkWidth/2, trunkHeight/2)
      ..lineTo(trunkWidth/2, trunkHeight/2)
      ..lineTo((trunkWidth/2) * (1-perspectiveNarrow), -trunkHeight/2)
      ..lineTo((-trunkWidth/2) * (1-perspectiveNarrow), -trunkHeight/2)
      ..close();
    
    // Enhanced trunk paint with 3D lighting gradient
    final Paint trunkPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [
          HSLColor.fromColor(trunkBaseColor).withLightness(
            math.max(0.1, HSLColor.fromColor(trunkBaseColor).lightness * 0.7)
          ).toColor(),
          trunkBaseColor,
          HSLColor.fromColor(trunkBaseColor).withLightness(
            math.min(0.6, HSLColor.fromColor(trunkBaseColor).lightness * 1.4)
          ).toColor(),
        ],
        stops: const [0.2, 0.5, 0.8],
      ).createShader(trunkPath.getBounds())
      ..style = PaintingStyle.fill;
    
    // Draw enhanced trunk
    canvas.drawPath(trunkPath, trunkPaint);
    
    // Add trunk edge for better definition
    final Paint trunkEdgePaint = Paint()
      ..color = Colors.black.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.7;
    
    canvas.drawPath(trunkPath, trunkEdgePaint);
    canvas.restore();
  }
  
  /// Draw enhanced tree shadow with perspective distortion for 3D effect
  void _drawEnhancedTreeShadow(Canvas canvas, Offset position, double size, {
    required String treeType
  }) {
    if (!enhancedDetail) return;
    
    // Determine shadow parameters based on tree type
    double shadowWidth;
    double shadowHeight;
    
    switch (treeType) {
      case 'conifer':
        shadowWidth = size * 1.2;
        shadowHeight = size * 0.6;
        break;
      case 'palm':
        shadowWidth = size * 1.5;
        shadowHeight = size * 0.5;
        break;
      case 'broadleaf':
      default:
        shadowWidth = size * 1.5;
        shadowHeight = size * 0.7;
        break;
    }
    
    // Use shadow renderer for consistent shadow styling
    _shadowRenderer.drawOvalShadow(
      canvas,
      position,
      shadowWidth,
      shadowHeight,
      size * 0.5, // Using size to determine elevation
      opacity: 0.3,
      perspectiveDistortion: true
    );
  }
  
  /// Get trunk color based on tree type
  Color _getTrunkColor([String treeType = 'default']) {
    // Base trunk color
    Color trunkColor = const Color(0xFF795548);
    
    switch (treeType) {
      case 'conifer':
        // Darker trunk for conifers
        trunkColor = const Color(0xFF5D4037);
        break;
      case 'palm':
        // More orange-brown for palms
        trunkColor = const Color(0xFF8D6E63);
        break;
      default:
        trunkColor = const Color(0xFF795548);
    }
    
    // For winter, make trunks slightly darker
    if (season == 'winter') {
      trunkColor = HSLColor.fromColor(trunkColor)
        .withLightness(HSLColor.fromColor(trunkColor).lightness * 0.9)
        .toColor();
    }
    
    return trunkColor;
  }
  
  /// Adjust foliage color based on season
  Color _adjustForSeason(Color baseColor) {
    switch (season) {
      case 'autumn':
        // More red/orange for autumn
        return HSLColor.fromColor(baseColor)
          .withHue(30)
          .withSaturation(0.8)
          .withLightness(0.5)
          .toColor();
      case 'winter':
        // More muted/grey for winter
        return HSLColor.fromColor(baseColor)
          .withSaturation(0.2)
          .withLightness(0.4)
          .toColor();
      case 'spring':
        // Brighter, more yellow-green for spring
        return HSLColor.fromColor(baseColor)
          .withSaturation(0.8)
          .withLightness(0.6)
          .toColor();
      default:
        // Default summer color is vibrant green
        return baseColor;
    }
  }
  
  /// Get zoom-based scale factor for tree size
  double _getZoomScaleFactor() {
    if (zoomLevel >= 19) return 5.0;
    if (zoomLevel >= 18) return 3.5;
    return 2.0;
  }
} 