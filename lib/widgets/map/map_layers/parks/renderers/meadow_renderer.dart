import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

import '../parks_colors.dart';
import '../parks_geometry_utils.dart';

/// Specialized renderer for meadows, open grasslands, and wildflower areas
/// Provides enhanced rendering with detailed wildflower patterns and seasonal variations
class MeadowRenderer {
  final ParkGeometryUtils _geometryUtils = ParkGeometryUtils();
  final ParksColors _colorsHelper = ParksColors();
  final double zoomLevel;
  final String theme;
  final String season;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  
  MeadowRenderer({
    required this.zoomLevel,
    required this.theme,
    required this.season,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  });
  
  /// Normalize an offset (create a unit vector with the same direction)
  Offset _normalizeOffset(Offset offset) {
    final double length = math.sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
    if (length == 0) return Offset.zero;
    return Offset(offset.dx / length, offset.dy / length);
  }
  
  /// Main render method for meadow areas
  void renderMeadowArea(Canvas canvas, Size size, Map<String, dynamic> feature, List<Offset> points, {String subtype = 'meadow'}) {
    if (points.length < 3) return;
    
    // Get colors for this specific meadow type
    final String featureType = subtype;
    final colors = _colorsHelper.getFeatureColors(featureType, theme, season);
    
    // Create the base path for the meadow area
    final Path meadowPath = Path()..addPolygon(points, true);
    final Rect bounds = meadowPath.getBounds();
    
    // Apply 3D elevation effect for meadow areas with nice undulation
    final double baseElevation = 1.2; // Enhanced meadow elevation (increased from 0.8)
    final double elevationMultiplier = _getElevationMultiplier(bounds);
    // Apply non-linear tilt enhancement from buildings renderer
    final double scaledElevation = baseElevation * _getElevationFactor() * elevationMultiplier * _getZoomTiltEnhancement();
    
    // Draw shadow with softer edges for subtle 3D effect
    if (scaledElevation > 0.5 && zoomLevel >= 15) {
      _drawEnhancedShadow(canvas, meadowPath, scaledElevation);
    }
    
    // Add elevated sides for stronger 2.5D effect
    if (scaledElevation > 0.5 && zoomLevel >= 16) {
      _addMeadowElevatedSides(canvas, meadowPath, points, colors, scaledElevation);
    }
    
    // Draw main fill with appropriate paint
    final Paint fillPaint = _getMeadowFillPaint(colors, meadowPath.getBounds(), subtype);
    canvas.drawPath(meadowPath, fillPaint);
    
    // Draw border with subtle style for meadow areas
    if (zoomLevel >= 15) {
      final Paint borderPaint = Paint()
        ..color = _getBorderColor(colors['base']!)
        ..style = PaintingStyle.stroke
        ..strokeWidth = _getBorderWidth()
        ..isAntiAlias = true;
      
      canvas.drawPath(meadowPath, borderPaint);
    }
    
    // Add texture based on zoom level and season
    if (zoomLevel >= 15 && enhancedDetail) {
      _addMeadowTexture(canvas, meadowPath, colors, subtype);
    }
    
    // Add special seasonal elements like wildflowers or fallen leaves
    if (zoomLevel >= 16 && enhancedDetail) {
      _addSeasonalElements(canvas, meadowPath, colors);
    }
    
    // Add ambient occlusion around edges for more depth
    if (zoomLevel >= 16) {
      _addAmbientOcclusion(canvas, meadowPath, scaledElevation);
    }
  }
  
  /// Get elevation factor based on zoom level and tilt setting with more aggressive scaling
  double _getElevationFactor() {
    // Enhanced scaling algorithm based on zoom level and tilt factor
    if (zoomLevel < 12) return 0.7 * tiltFactor;
    if (zoomLevel < 14) return 0.9 * tiltFactor;
    if (zoomLevel < 16) return 1.2 * tiltFactor;
    if (zoomLevel < 18) return 1.7 * tiltFactor;
    if (zoomLevel < 20) return 2.2 * tiltFactor;
    return 2.6 * tiltFactor; // Increased maximum elevation at highest zoom
  }
  
  /// Non-linear tilt enhancement like buildings renderer
  double _getZoomTiltEnhancement() {
    if (zoomLevel <= 14) return 1.0;
    if (zoomLevel >= 20) return 2.5;
    
    // Non-linear power function for smooth transition
    return 1.0 + math.pow((zoomLevel - 14) / 6, 1.2) * 1.5;
  }
  
  /// Calculate appropriate elevation multiplier based on feature size
  double _getElevationMultiplier(Rect bounds) {
    // Dynamic and non-linear scaling for more natural appearance
    final double area = bounds.width * bounds.height;
    if (area > 50000) return 0.7;
    if (area > 20000) return 0.8;
    if (area > 10000) return 0.9;
    if (area > 5000) return 1.0;
    if (area > 2000) return 1.1;
    if (area > 1000) return 1.2;
    return 1.3; // Smallest meadows get significant emphasis
  }
  
  /// Draw enhanced shadow with proper perspective and softer edges
  void _drawEnhancedShadow(Canvas canvas, Path meadowPath, double elevation) {
    // Enhanced shadow opacity calculations
    final double baseOpacity = 0.3 * _getElevationFactor();
    final double zoomEnhancement = math.min(1.6, 1.0 + (zoomLevel - 15) * 0.2);
    final double shadowOpacity = math.min(0.5, baseOpacity * zoomEnhancement);
    
    // Improved shadow offset with better directional light component
    final double offsetMultiplier = math.min(1.0 + (zoomLevel - 15) * 0.15, 2.0);
    final double offsetX = 2.2 * elevation * 0.3 * offsetMultiplier;
    final double offsetY = 2.2 * elevation * 0.3 * offsetMultiplier;
    
    // Use matrix transformation for perspective-correct shadows like buildings
    final Matrix4 shadowMatrix = Matrix4.identity()
      ..translate(offsetX, offsetY)
      ..scale(1.03 + elevation * 0.025, 1.06 + elevation * 0.035); // More vertical stretch for richer 3D effect
    
    final Path shadowPath = meadowPath.transform(shadowMatrix.storage);
    
    // Enhanced shadow quality with improved blur effects
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(shadowOpacity)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, math.min(4.5, 1.5 + elevation * 0.35))
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw shadow beneath the meadow with better quality
    canvas.drawPath(shadowPath, shadowPaint);
  }
  
  /// Add meadow-specific elevated sides for better 2.5D effects
  void _addMeadowElevatedSides(Canvas canvas, Path meadowPath, List<Offset> points, Map<String, Color> colors, double elevation) {
    // Enhanced colors for sides with better saturation for meadows
    final Color baseSideColor = _getSideColor(colors['base']!);
    
    // Calculate normals for better corner handling (from buildings technique)
    List<Offset> normals = [];
    for (int i = 0; i < points.length; i++) {
      final Offset prev = points[(i - 1 + points.length) % points.length];
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      
      // Calculate normal at this point by averaging adjacent segment normals
      final Offset v1 = current - prev;
      final Offset v2 = next - current;
      
      final Offset n1 = _normalizeOffset(Offset(-v1.dy, v1.dx));
      final Offset n2 = _normalizeOffset(Offset(-v2.dy, v2.dx));
      final Offset avgNormal = _normalizeOffset(Offset((n1.dx + n2.dx) / 2, (n1.dy + n2.dy) / 2));
      
      normals.add(avgNormal);
    }
    
    // Create side path with enhanced directional lighting
    final Path sidePath = Path();
    
    // Use buildings technique: Wall orientation detection
    for (int i = 0; i < points.length; i++) {
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      final Offset currentNormal = normals[i];
      final Offset nextNormal = normals[(i + 1) % points.length];
      
      // Calculate wall direction to determine if it faces viewer
      final Offset wallDir = _normalizeOffset(next - current);
      final bool isWallFacingViewer = wallDir.dy >= 0 || wallDir.dx.abs() > 0.8;
      
      // Only draw sides for segments facing the viewer
      if (isWallFacingViewer) {
        // Start with current point
        if (!sidePath.contains(current)) {
          sidePath.moveTo(current.dx, current.dy);
        }
        
        // Draw to next point
        sidePath.lineTo(next.dx, next.dy);
        
        // Add some undulation to sides - meadow-specific enhancement
        final double undulationFactor = 0.3 + _random.nextDouble() * 0.3;
        final double midPointElevation = elevation * (0.85 + undulationFactor);
        
        // Find midpoint with undulation
        final Offset midPoint = Offset(
          (current.dx + next.dx) / 2,
          (current.dy + next.dy) / 2
        );
        
        // Calculate midpoint normal
        final Offset midNormal = _normalizeOffset(Offset(
          (currentNormal.dx + nextNormal.dx) / 2,
          (currentNormal.dy + nextNormal.dy) / 2
        ));
        
        // Draw to midpoint first with enhanced perspective
        final double perspectiveFactorNext = 0.15 + elevation * 0.01;
        sidePath.lineTo(
          next.dx + nextNormal.dx * elevation * perspectiveFactorNext, 
          next.dy + elevation + nextNormal.dy * elevation * 0.1
        );
        
        // Draw back to current point with enhanced perspective
        final double perspectiveFactorCurrent = 0.15 + elevation * 0.01;
        sidePath.lineTo(
          current.dx + currentNormal.dx * elevation * perspectiveFactorCurrent, 
          current.dy + elevation + currentNormal.dy * elevation * 0.1
        );
        
        // Close this side segment
        sidePath.lineTo(current.dx, current.dy);
      }
    }
    
    // Create enhanced gradient for dynamic lighting effect with directional light
    final Rect bounds = sidePath.getBounds();
    
    // Use multi-stop gradient with directional lighting like buildings
    final Paint sidePaint = Paint()
      ..shader = LinearGradient(
        begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
        end: const Alignment(0.3, 0.0),
        colors: [
          baseSideColor,
          HSLColor.fromColor(baseSideColor)
            .withLightness(math.max(0.1, HSLColor.fromColor(baseSideColor).lightness * 0.5))
            .toColor(),
        ],
        stops: const [0.7, 1.0],
      ).createShader(bounds)
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw enhanced meadow sides
    canvas.drawPath(sidePath, sidePaint);
    
    // Add subtle edge highlight for better definition
    final Paint edgePaint = Paint()
      ..color = Colors.black.withOpacity(0.25)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    canvas.drawPath(sidePath, edgePaint);
  }
  
  /// Add ambient occlusion around edges for enhanced 3D depth
  void _addAmbientOcclusion(Canvas canvas, Path meadowPath, double elevation) {
    // Create inset path for ambient occlusion with dynamic inset based on feature size
    final double inset = math.max(1.0, math.min(2.5, elevation * 0.4));
    
    // Create AO path by insetting the original path
    final Path aoPath = Path.combine(
      PathOperation.difference,
      meadowPath,
      Path.combine(
        PathOperation.intersect,
        meadowPath,
        Path()..addRect(meadowPath.getBounds().inflate(-inset))
      )
    );
    
    // Draw ambient occlusion with sophisticated AO effect
    final Rect bounds = meadowPath.getBounds();
    final Paint aoPaint = Paint()
      ..shader = RadialGradient(
        center: Alignment.center,
        radius: 1.0,
        colors: [
          Colors.black.withOpacity(0.25 + elevation * 0.025),
          Colors.black.withOpacity(0.0),
        ],
        stops: const [0.0, 1.0],
      ).createShader(bounds)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 1.8)
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(aoPath, aoPaint);
  }
  
  /// Get a paint for filling meadow areas with proper styling
  Paint _getMeadowFillPaint(Map<String, Color> colors, Rect bounds, String subtype) {
    // For low zoom levels, use a simple solid color
    if (zoomLevel < 14 || !enhancedDetail) {
      return Paint()
        ..color = colors['base']!
        ..style = PaintingStyle.fill
        ..isAntiAlias = true;
    }
    
    // Apply color variation like buildings renderer
    final Color baseColor = _addColorVariation(colors['base']!);
    final Color highlightColor = _addColorVariation(colors['highlight']!);
    final Color detailColor = _addColorVariation(colors['detail']!);
    
    // Different seasonal gradients with directional lighting like buildings
    switch (season) {
      case 'spring':
        return _createSpringMeadowPaint(
          {'base': baseColor, 'highlight': highlightColor, 'detail': detailColor}, 
          bounds
        );
      case 'summer':
        return _createSummerMeadowPaint(
          {'base': baseColor, 'highlight': highlightColor, 'detail': detailColor}, 
          bounds
        );
      case 'autumn':
        return _createAutumnMeadowPaint(
          {'base': baseColor, 'highlight': highlightColor, 'detail': detailColor}, 
          bounds
        );
      case 'winter':
        return _createWinterMeadowPaint(
          {'base': baseColor, 'highlight': highlightColor, 'detail': detailColor}, 
          bounds
        );
      default:
        return _createDefaultMeadowPaint(
          {'base': baseColor, 'highlight': highlightColor, 'detail': detailColor}, 
          bounds
        );
    }
  }
  
  /// Add subtle color variation like buildings renderer
  Color _addColorVariation(Color baseColor, {int variationAmount = 15}) {
    // Only add variation for higher zoom levels
    if (zoomLevel < 15) return baseColor;
    
    final int variation = math.min(variationAmount, 15);
    final int r = (baseColor.red + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int g = (baseColor.green + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int b = (baseColor.blue + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    
    return Color.fromRGBO(r, g, b, baseColor.opacity);
  }
  
  /// Create spring meadow paint with vibrant fresh growth and directional lighting
  Paint _createSpringMeadowPaint(Map<String, Color> colors, Rect bounds) {
    return Paint()
      ..shader = LinearGradient(
        begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
        end: const Alignment(0.3, 0.0),
        colors: [
          _colorsHelper.adjustBrightness(colors['highlight']!, 0.1),
          colors['base']!,
          colors['detail']!,
        ],
        stops: const [0.2, 0.6, 1.0],
      ).createShader(bounds)
      ..style = PaintingStyle.fill;
  }
  
  /// Create summer meadow paint with mature growth patterns and directional lighting
  Paint _createSummerMeadowPaint(Map<String, Color> colors, Rect bounds) {
    return Paint()
      ..shader = LinearGradient(
        begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
        end: const Alignment(0.3, 0.0),
        colors: [
          colors['base']!,
          colors['highlight']!,
          colors['detail']!,
          colors['base']!,
        ],
        stops: const [0.0, 0.3, 0.7, 1.0],
      ).createShader(bounds)
      ..style = PaintingStyle.fill;
  }
  
  /// Create autumn meadow paint with warmer tones
  Paint _createAutumnMeadowPaint(Map<String, Color> colors, Rect bounds) {
    return Paint()
      ..shader = LinearGradient(
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
        colors: [
          colors['highlight']!,
          colors['base']!,
          _colorsHelper.adjustBrightness(colors['detail']!, -0.05),
        ],
        stops: const [0.2, 0.5, 0.9],
      ).createShader(bounds)
      ..style = PaintingStyle.fill;
  }
  
  /// Create winter meadow paint with more muted appearance
  Paint _createWinterMeadowPaint(Map<String, Color> colors, Rect bounds) {
    return Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          _colorsHelper.adjustBrightness(colors['base']!, 0.05),
          colors['base']!,
          _colorsHelper.adjustBrightness(colors['base']!, -0.05),
        ],
        stops: const [0.2, 0.5, 0.8],
      ).createShader(bounds)
      ..style = PaintingStyle.fill;
  }
  
  /// Create default meadow paint for cases where season isn't specified
  Paint _createDefaultMeadowPaint(Map<String, Color> colors, Rect bounds) {
    return Paint()
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          colors['highlight']!,
          colors['base']!,
        ],
        stops: const [0.3, 1.0],
      ).createShader(bounds)
      ..style = PaintingStyle.fill;
  }
  
  /// Add texture specific to meadow areas
  void _addMeadowTexture(Canvas canvas, Path meadowPath, Map<String, Color> colors, String subtype) {
    if (zoomLevel < 15) return;
    
    final Rect bounds = meadowPath.getBounds();
    double density = _getTextureDensity();
    
    // Texture colors
    final Color lightColor = colors['highlight']!;
    final Color baseColor = colors['base']!;
    final Color darkColor = colors['detail']!;
    
    // Save canvas state to clip to the meadow area
    canvas.save();
    canvas.clipPath(meadowPath);
    
    // Create different texture patterns based on season
    switch (season) {
      case 'spring':
        _createSpringTexture(canvas, bounds, lightColor, baseColor, darkColor, density);
        break;
      case 'summer':
        _createSummerTexture(canvas, bounds, lightColor, baseColor, darkColor, density);
        break;
      case 'autumn':
        _createAutumnTexture(canvas, bounds, lightColor, baseColor, darkColor, density);
        break;
      case 'winter':
        _createWinterTexture(canvas, bounds, lightColor, baseColor, darkColor, density);
        break;
      default:
        _createDefaultTexture(canvas, bounds, lightColor, baseColor, darkColor, density);
    }
    
    // Restore canvas state
    canvas.restore();
  }
  
  /// Create spring texture with fresh growth and early wildflowers
  void _createSpringTexture(Canvas canvas, Rect bounds, Color lightColor, Color baseColor, Color darkColor, double density) {
    // Spring has small and emerging wildflowers
    final int numElements = (bounds.width * bounds.height * density / 80).round();
    
    // Spring wildflower colors
    final List<Color> springColors = [
      lightColor,
      baseColor,
      Colors.yellow.withOpacity(0.4),
      Colors.purple.withOpacity(0.3),
      Colors.white.withOpacity(0.4),
      Colors.pink.withOpacity(0.3),
    ];
    
    for (int i = 0; i < numElements; i++) {
      final double x = bounds.left + _random.nextDouble() * bounds.width;
      final double y = bounds.top + _random.nextDouble() * bounds.height;
      
      // Mix of grass blades and wildflowers
      if (_random.nextDouble() < 0.7) {
        // Grass blade
        final Paint bladePaint = Paint()
          ..color = (_random.nextBool() ? lightColor : baseColor).withOpacity(0.4)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;
        
        final double length = 1.0 + _random.nextDouble() * 1.5;
        final double angle = _random.nextDouble() * math.pi;
        
        canvas.drawLine(
          Offset(x, y),
          Offset(
            x + math.cos(angle) * length,
            y + math.sin(angle) * length
          ),
          bladePaint
        );
      } else {
        // Small spring wildflower
        final Color flowerColor = springColors[_random.nextInt(springColors.length)];
        final Paint flowerPaint = Paint()
          ..color = flowerColor
          ..style = PaintingStyle.fill;
        
        final double size = 0.5 + _random.nextDouble();
        canvas.drawCircle(Offset(x, y), size, flowerPaint);
      }
    }
  }
  
  /// Create summer texture with lush growth and full wildflowers
  void _createSummerTexture(Canvas canvas, Rect bounds, Color lightColor, Color baseColor, Color darkColor, double density) {
    // Summer has the most wildflowers and tallest grass
    final int numElements = (bounds.width * bounds.height * density / 60).round();
    
    // Summer wildflower colors - vibrant and abundant
    final List<Color> summerColors = [
      lightColor,
      baseColor,
      Colors.yellow.withOpacity(0.5),
      Colors.purple.withOpacity(0.4),
      Colors.red.withOpacity(0.3),
      Colors.blue.withOpacity(0.4),
      Colors.pink.withOpacity(0.4),
      Colors.white.withOpacity(0.5),
    ];
    
    for (int i = 0; i < numElements; i++) {
      final double x = bounds.left + _random.nextDouble() * bounds.width;
      final double y = bounds.top + _random.nextDouble() * bounds.height;
      
      if (_random.nextDouble() < 0.6) {
        // Tall grass blades
        final Paint bladePaint = Paint()
          ..color = (_random.nextBool() ? lightColor : baseColor).withOpacity(0.4)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.6;
        
        final double length = 1.5 + _random.nextDouble() * 2.5;
        final double angle = _random.nextDouble() * math.pi;
        
        canvas.drawLine(
          Offset(x, y),
          Offset(
            x + math.cos(angle) * length,
            y + math.sin(angle) * length
          ),
          bladePaint
        );
      } else {
        // Summer wildflower - larger and more colorful
        final Color flowerColor = summerColors[_random.nextInt(summerColors.length)];
        final Paint flowerPaint = Paint()
          ..color = flowerColor
          ..style = PaintingStyle.fill;
        
        final double size = 0.8 + _random.nextDouble() * 1.5;
        canvas.drawCircle(Offset(x, y), size, flowerPaint);
        
        // Add flower center for larger flowers
        if (size > 1.5 && zoomLevel >= 17) {
          final Paint centerPaint = Paint()
            ..color = Colors.yellow.withOpacity(0.7)
            ..style = PaintingStyle.fill;
          
          canvas.drawCircle(Offset(x, y), size * 0.3, centerPaint);
        }
      }
    }
  }
  
  /// Create autumn texture with dried grass and seed heads
  void _createAutumnTexture(Canvas canvas, Rect bounds, Color lightColor, Color baseColor, Color darkColor, double density) {
    // Autumn has dried grasses and fewer flowers, more seed heads
    final int numElements = (bounds.width * bounds.height * density / 70).round();
    
    // Autumn colors - more muted, browns and golds
    final List<Color> autumnColors = [
      lightColor,
      baseColor,
      darkColor,
      Colors.brown.withOpacity(0.3),
      Colors.orange.withOpacity(0.3),
      Colors.amber.withOpacity(0.4),
    ];
    
    for (int i = 0; i < numElements; i++) {
      final double x = bounds.left + _random.nextDouble() * bounds.width;
      final double y = bounds.top + _random.nextDouble() * bounds.height;
      
      // Mostly dried grass in autumn
      if (_random.nextDouble() < 0.8) {
        // Dried grass or seed head
        final Paint grassPaint = Paint()
          ..color = autumnColors[_random.nextInt(3)].withOpacity(0.4)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;
        
        final double length = 1.0 + _random.nextDouble() * 2.0;
        final double angle = _random.nextDouble() * math.pi;
        
        canvas.drawLine(
          Offset(x, y),
          Offset(
            x + math.cos(angle) * length,
            y + math.sin(angle) * length
          ),
          grassPaint
        );
        
        // Sometimes add a seed head
        if (_random.nextDouble() < 0.3 && zoomLevel >= 17) {
          final Paint seedPaint = Paint()
            ..color = autumnColors[3 + _random.nextInt(3)].withOpacity(0.5)
            ..style = PaintingStyle.fill;
          
          canvas.drawCircle(
            Offset(
              x + math.cos(angle) * length * 0.8,
              y + math.sin(angle) * length * 0.8
            ),
            0.5,
            seedPaint
          );
        }
      } else {
        // Few remaining autumn flowers
        final Color flowerColor = autumnColors[3 + _random.nextInt(3)];
        final Paint flowerPaint = Paint()
          ..color = flowerColor
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(x, y), 0.7 + _random.nextDouble(), flowerPaint);
      }
    }
  }
  
  /// Create winter texture with dormant vegetation
  void _createWinterTexture(Canvas canvas, Rect bounds, Color lightColor, Color baseColor, Color darkColor, double density) {
    // Winter has sparse, dormant vegetation
    final int numElements = (bounds.width * bounds.height * density / 120).round();
    
    for (int i = 0; i < numElements; i++) {
      final double x = bounds.left + _random.nextDouble() * bounds.width;
      final double y = bounds.top + _random.nextDouble() * bounds.height;
      
      // Winter is mostly just dormant grass stems
      final Paint stemPaint = Paint()
        ..color = (_random.nextBool() ? lightColor : baseColor).withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.4;
      
      final double length = 0.8 + _random.nextDouble() * 1.2;
      final double angle = _random.nextDouble() * math.pi;
      
      canvas.drawLine(
        Offset(x, y),
        Offset(
          x + math.cos(angle) * length,
          y + math.sin(angle) * length
        ),
        stemPaint
      );
    }
  }
  
  /// Create default texture when season is unspecified
  void _createDefaultTexture(Canvas canvas, Rect bounds, Color lightColor, Color baseColor, Color darkColor, double density) {
    final int numElements = (bounds.width * bounds.height * density / 80).round();
    
    for (int i = 0; i < numElements; i++) {
      final double x = bounds.left + _random.nextDouble() * bounds.width;
      final double y = bounds.top + _random.nextDouble() * bounds.height;
      
      if (_random.nextDouble() < 0.7) {
        // Simple grass texture
        final Paint bladePaint = Paint()
          ..color = (_random.nextBool() ? lightColor : baseColor).withOpacity(0.4)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;
        
        final double length = 1.0 + _random.nextDouble() * 2.0;
        final double angle = _random.nextDouble() * math.pi;
        
        canvas.drawLine(
          Offset(x, y),
          Offset(
            x + math.cos(angle) * length,
            y + math.sin(angle) * length
          ),
          bladePaint
        );
      } else {
        // Simple flower dots
        final Color dotColor = _random.nextBool() ? lightColor : darkColor;
        final Paint dotPaint = Paint()
          ..color = dotColor.withOpacity(0.4)
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(x, y), 0.5 + _random.nextDouble(), dotPaint);
      }
    }
  }
  
  /// Add special seasonal elements to meadow
  void _addSeasonalElements(Canvas canvas, Path meadowPath, Map<String, Color> colors) {
    if (zoomLevel < 16) return;
    
    final Rect bounds = meadowPath.getBounds();
    
    // Save canvas state to clip to the meadow area
    canvas.save();
    canvas.clipPath(meadowPath);
    
    // Add elements based on season
    switch (season) {
      case 'spring':
        _addSpringElements(canvas, bounds, colors);
        break;
      case 'summer':
        _addSummerElements(canvas, bounds, colors);
        break;
      case 'autumn':
        _addAutumnElements(canvas, bounds, colors);
        break;
      case 'winter':
        _addWinterElements(canvas, bounds, colors);
        break;
    }
    
    // Restore canvas state
    canvas.restore();
  }
  
  /// Add special spring elements like patches of early blooming flowers
  void _addSpringElements(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    // Spring has patches of early blooming flowers
    if (zoomLevel >= 17) {
      final int numPatches = (bounds.width * bounds.height / 10000).round().clamp(1, 5);
      
      for (int i = 0; i < numPatches; i++) {
        final double patchX = bounds.left + _random.nextDouble() * bounds.width;
        final double patchY = bounds.top + _random.nextDouble() * bounds.height;
        final double patchSize = 3.0 + _random.nextDouble() * 5.0;
        
        // Create a flower patch
        final List<Color> springFlowerColors = [
          Colors.yellow.withOpacity(0.3),
          Colors.purple.withOpacity(0.2),
          Colors.white.withOpacity(0.3),
          Colors.pink.withOpacity(0.2),
        ];
        
        final Color patchColor = springFlowerColors[_random.nextInt(springFlowerColors.length)];
        
        // Draw the patch as a collection of small dots
        final int numFlowers = (patchSize * patchSize * 2).round();
        
        for (int j = 0; j < numFlowers; j++) {
          final double offsetX = ((_random.nextDouble() * 2) - 1.0) * patchSize;
          final double offsetY = ((_random.nextDouble() * 2) - 1.0) * patchSize;
          
          // Apply circular distribution
          final double distanceFromCenter = math.sqrt(offsetX * offsetX + offsetY * offsetY);
          if (distanceFromCenter <= patchSize) {
            final Paint flowerPaint = Paint()
              ..color = patchColor
              ..style = PaintingStyle.fill;
            
            canvas.drawCircle(
              Offset(patchX + offsetX, patchY + offsetY),
              0.3 + _random.nextDouble() * 0.5,
              flowerPaint
            );
          }
        }
      }
    }
  }
  
  /// Add special summer elements like dense wildflower patches
  void _addSummerElements(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    // Summer has more defined wildflower patches
    if (zoomLevel >= 17) {
      final int numPatches = (bounds.width * bounds.height / 8000).round().clamp(2, 7);
      
      for (int i = 0; i < numPatches; i++) {
        final double patchX = bounds.left + _random.nextDouble() * bounds.width;
        final double patchY = bounds.top + _random.nextDouble() * bounds.height;
        final double patchSize = 4.0 + _random.nextDouble() * 6.0;
        
        // Summer flower patch colors
        final List<Color> summerFlowerColors = [
          Colors.yellow.withOpacity(0.4),
          Colors.purple.withOpacity(0.3),
          Colors.red.withOpacity(0.3),
          Colors.blue.withOpacity(0.3),
          Colors.pink.withOpacity(0.4),
          Colors.white.withOpacity(0.4),
        ];
        
        final Color patchColor = summerFlowerColors[_random.nextInt(summerFlowerColors.length)];
        
        // Draw the patch as a collection of flowers
        final int numFlowers = (patchSize * patchSize * 3).round();
        
        for (int j = 0; j < numFlowers; j++) {
          final double offsetX = ((_random.nextDouble() * 2) - 1.0) * patchSize;
          final double offsetY = ((_random.nextDouble() * 2) - 1.0) * patchSize;
          
          // Apply circular distribution with fuzzy edges
          final double distanceFromCenter = math.sqrt(offsetX * offsetX + offsetY * offsetY);
          if (distanceFromCenter <= patchSize * (0.7 + _random.nextDouble() * 0.3)) {
            final Paint flowerPaint = Paint()
              ..color = patchColor
              ..style = PaintingStyle.fill;
            
            final double flowerSize = 0.4 + _random.nextDouble() * 0.7;
            canvas.drawCircle(
              Offset(patchX + offsetX, patchY + offsetY),
              flowerSize,
              flowerPaint
            );
            
            // Add flower centers for larger ones
            if (flowerSize > 0.6 && zoomLevel >= 18) {
              final Paint centerPaint = Paint()
                ..color = Colors.yellow.withOpacity(0.6)
                ..style = PaintingStyle.fill;
              
              canvas.drawCircle(
                Offset(patchX + offsetX, patchY + offsetY),
                flowerSize * 0.4,
                centerPaint
              );
            }
          }
        }
      }
    }
  }
  
  /// Add special autumn elements like fallen leaves
  void _addAutumnElements(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    // Autumn has some fallen leaves and seed pods
    if (zoomLevel >= 17) {
      final int numLeaves = (bounds.width * bounds.height / 1500).round().clamp(5, 30);
      
      final List<Color> autumnLeafColors = [
        Colors.brown.withOpacity(0.4),
        Colors.orange.withOpacity(0.4),
        Colors.red.withOpacity(0.4),
        Colors.amber.withOpacity(0.4),
      ];
      
      for (int i = 0; i < numLeaves; i++) {
        final double x = bounds.left + _random.nextDouble() * bounds.width;
        final double y = bounds.top + _random.nextDouble() * bounds.height;
        
        final Color leafColor = autumnLeafColors[_random.nextInt(autumnLeafColors.length)];
        final Paint leafPaint = Paint()
          ..color = leafColor
          ..style = PaintingStyle.fill;
        
        // Simple leaf shape
        if (zoomLevel >= 18) {
          // More detailed leaf at very high zoom
          final Path leafPath = Path();
          final double leafSize = 1.0 + _random.nextDouble();
          final double rotation = _random.nextDouble() * math.pi * 2;
          
          // Create a basic leaf shape
          leafPath.moveTo(x, y - leafSize);
          leafPath.quadraticBezierTo(
            x + leafSize, y, 
            x, y + leafSize
          );
          leafPath.quadraticBezierTo(
            x - leafSize, y, 
            x, y - leafSize
          );
          
          // Rotate the leaf
          final Matrix4 rotationMatrix = Matrix4.identity()
            ..translate(x, y)
            ..rotateZ(rotation)
            ..translate(-x, -y);
          
          canvas.drawPath(leafPath.transform(rotationMatrix.storage), leafPaint);
        } else {
          // Simple oval leaf at medium zoom
          canvas.drawOval(
            Rect.fromCenter(
              center: Offset(x, y),
              width: 1.0 + _random.nextDouble(),
              height: 2.0 + _random.nextDouble()
            ),
            leafPaint
          );
        }
      }
    }
  }
  
  /// Add special winter elements like frost patterns on dormant vegetation
  void _addWinterElements(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    // Winter might have frost patterns or snow patches
    if (zoomLevel >= 17) {
      // Add some frost highlights on vegetation
      final int numFrostPoints = (bounds.width * bounds.height / 2000).round().clamp(3, 20);
      
      final Paint frostPaint = Paint()
        ..color = Colors.white.withOpacity(0.3)
        ..style = PaintingStyle.fill;
      
      for (int i = 0; i < numFrostPoints; i++) {
        final double x = bounds.left + _random.nextDouble() * bounds.width;
        final double y = bounds.top + _random.nextDouble() * bounds.height;
        
        // Simple frost dot
        canvas.drawCircle(Offset(x, y), 0.3 + _random.nextDouble() * 0.5, frostPaint);
        
        // Sometimes add frost radiating lines
        if (_random.nextBool() && zoomLevel >= 18) {
          final int numLines = 3 + _random.nextInt(3);
          for (int j = 0; j < numLines; j++) {
            final double angle = j * (math.pi * 2 / numLines);
            final double length = 0.5 + _random.nextDouble();
            
            canvas.drawLine(
              Offset(x, y),
              Offset(
                x + math.cos(angle) * length,
                y + math.sin(angle) * length
              ),
              frostPaint..strokeWidth = 0.3
            );
          }
        }
      }
    }
  }
  
  /// Get texture density based on zoom level and detail setting
  double _getTextureDensity() {
    if (!enhancedDetail) return 0.5;
    if (zoomLevel >= 18) return 1.8;
    if (zoomLevel >= 17) return 1.4;
    if (zoomLevel >= 16) return 1.0;
    return 0.5;
  }
  
  /// Get a dark color for the sides of the meadow with enhanced saturation
  Color _getSideColor(Color baseColor) {
    return HSLColor.fromColor(baseColor)
      .withSaturation(math.min(1.0, HSLColor.fromColor(baseColor).saturation * 1.2))
      .withLightness(math.max(0.15, HSLColor.fromColor(baseColor).lightness * 0.5))
      .toColor();
  }
  
  /// Get appropriate border color that provides good contrast
  Color _getBorderColor(Color baseColor) {
    return HSLColor.fromColor(baseColor)
      .withSaturation(math.min(1.0, HSLColor.fromColor(baseColor).saturation * 1.1))
      .withLightness(math.max(0.1, HSLColor.fromColor(baseColor).lightness * 0.75))
      .toColor()
      .withOpacity(0.4);
  }
  
  /// Get appropriate border width based on zoom level
  double _getBorderWidth() {
    if (zoomLevel >= 18) return 0.7;
    if (zoomLevel >= 16) return 0.5;
    return 0.3;
  }
} 