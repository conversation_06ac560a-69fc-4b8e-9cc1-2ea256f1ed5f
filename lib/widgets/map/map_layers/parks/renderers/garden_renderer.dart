import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

import '../parks_colors.dart';
import '../parks_geometry_utils.dart';

/// Specialized renderer for gardens, orchards, and landscaped areas
/// Provides enhanced rendering with decorative elements and organized patterns
class GardenRenderer {
  final ParkGeometryUtils _geometryUtils = ParkGeometryUtils();
  final ParksColors _colorsHelper = ParksColors();
  final double zoomLevel;
  final String theme;
  final String season;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  
  GardenRenderer({
    required this.zoomLevel,
    required this.theme,
    required this.season,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  });
  
  /// Normalize an offset (create a unit vector with the same direction)
  Offset _normalizeOffset(Offset offset) {
    final double length = math.sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
    if (length == 0) return Offset.zero;
    return Offset(offset.dx / length, offset.dy / length);
  }
  
  /// Main render method for garden areas
  void renderGardenArea(Canvas canvas, Size size, Map<String, dynamic> feature, List<Offset> points, {String subtype = 'garden'}) {
    if (points.length < 3) return;
    
    // Get colors for this specific garden type
    final String featureType = subtype;
    final colors = _colorsHelper.getFeatureColors(featureType, theme, season);
    
    // Create the base path for the garden area
    final Path gardenPath = Path()..addPolygon(points, true);
    final Rect bounds = gardenPath.getBounds();
    
    // Apply 3D elevation effect based on garden type
    final double baseElevation = _getBaseElevation(subtype);
    final double elevationMultiplier = _getElevationMultiplier(bounds);
    // Apply non-linear tilt enhancement from buildings renderer
    final double scaledElevation = baseElevation * _getElevationFactor() * elevationMultiplier * _getZoomTiltEnhancement();
    
    // Draw shadow with softer edges for subtle 3D effect
    if (scaledElevation > 0.4 && zoomLevel >= 15) {
      _drawEnhancedShadow(canvas, gardenPath, scaledElevation);
    }
    
    // Add elevated sides for stronger 2.5D effect
    if (scaledElevation > 0.4 && zoomLevel >= 16) {
      _addGardenElevatedSides(canvas, gardenPath, points, colors, scaledElevation, subtype);
    }
    
    // Draw main fill with appropriate paint - use color variation for enhanced realism
    final Paint fillPaint = _getGardenFillPaint(colors, bounds, subtype);
    canvas.drawPath(gardenPath, fillPaint);
    
    // Draw border for garden areas
    final Paint borderPaint = Paint()
      ..color = _getBorderColor(colors['base']!)
      ..style = PaintingStyle.stroke
      ..strokeWidth = _getBorderWidth()
      ..isAntiAlias = true;
    
    canvas.drawPath(gardenPath, borderPaint);
    
    // Add garden-specific patterns based on subtype and zoom level
    if (zoomLevel >= 15 && enhancedDetail) {
      canvas.save();
      canvas.clipPath(gardenPath);
      
      if (subtype == 'orchard') {
        _addOrchardPattern(canvas, bounds, colors);
      } else if (subtype == 'vineyard') {
        _addVineyardPattern(canvas, bounds, colors);
      } else if (subtype == 'garden') {
        _addGardenPattern(canvas, bounds, colors);
      }
      
      canvas.restore();
    }
    
    // Add decorative elements at higher zoom levels
    if (zoomLevel >= 16 && enhancedDetail) {
      canvas.save();
      canvas.clipPath(gardenPath);
      
      if (subtype == 'orchard') {
        _addOrchardElements(canvas, bounds, colors);
      } else if (subtype == 'vineyard') {
        _addVineyardElements(canvas, bounds, colors);
      } else if (subtype == 'garden') {
        _addGardenElements(canvas, bounds, colors);
      }
      
      canvas.restore();
    }
    
    // Add ambient occlusion around edges for more depth
    if (zoomLevel >= 16) {
      _addAmbientOcclusion(canvas, gardenPath, scaledElevation);
    }
  }
  
  /// Get base elevation for different garden subtypes
  double _getBaseElevation(String subtype) {
    switch (subtype) {
      case 'orchard':
        return 2.0; // Orchards have significantly taller trees (enhanced from 1.5)
      case 'vineyard':
        return 1.6; // Vineyards have substantial height variations (enhanced from 1.2)
      case 'garden':
        return 1.8; // Gardens have varied plants with good height (enhanced from 1.3)
      default:
        return 1.4; // Default height with better visibility (enhanced from 1.0)
    }
  }
  
  /// Get elevation factor based on zoom level and tilt setting
  double _getElevationFactor() {
    // Enhanced scaling algorithm based on zoom level and tilt with more aggressive values
    if (zoomLevel < 12) return 0.7 * tiltFactor;
    if (zoomLevel < 14) return 0.9 * tiltFactor;
    if (zoomLevel < 16) return 1.2 * tiltFactor;
    if (zoomLevel < 18) return 1.8 * tiltFactor;
    if (zoomLevel < 20) return 2.3 * tiltFactor;
    return 2.7 * tiltFactor; // Increased maximum elevation at highest zoom
  }
  
  /// Non-linear tilt enhancement like buildings renderer
  double _getZoomTiltEnhancement() {
    if (zoomLevel <= 14) return 1.0;
    if (zoomLevel >= 20) return 2.8;
    
    // Non-linear power function for smooth transition
    return 1.0 + math.pow((zoomLevel - 14) / 6, 1.2) * 1.8;
  }
  
  /// Calculate appropriate elevation multiplier based on feature size
  double _getElevationMultiplier(Rect bounds) {
    // Dynamic and non-linear scaling for more natural appearance
    final double area = bounds.width * bounds.height;
    if (area > 40000) return 0.7;
    if (area > 20000) return 0.8;
    if (area > 10000) return 0.9;
    if (area > 5000) return 1.0;
    if (area > 2000) return 1.1;
    if (area > 1000) return 1.2;
    return 1.3; // Smallest gardens get significant emphasis
  }
  
  /// Draw enhanced shadow with proper perspective and softer edges
  void _drawEnhancedShadow(Canvas canvas, Path gardenPath, double elevation) {
    // Enhanced shadow opacity with better zoom adaptation
    final double baseOpacity = 0.3 * _getElevationFactor();
    final double zoomEnhancement = math.min(1.6, 1.0 + (zoomLevel - 15) * 0.18);
    final double shadowOpacity = math.min(0.5, baseOpacity * zoomEnhancement);
    
    // Improved shadow offset with better directional light component
    final double offsetMultiplier = math.min(1.0 + (zoomLevel - 15) * 0.15, 2.0);
    final double offsetX = 2.2 * elevation * 0.35 * offsetMultiplier;
    final double offsetY = 2.2 * elevation * 0.35 * offsetMultiplier;
    
    // Use matrix transformation for perspective-correct shadows like buildings
    final Matrix4 shadowMatrix = Matrix4.identity()
      ..translate(offsetX, offsetY)
      ..scale(1.03 + elevation * 0.025, 1.06 + elevation * 0.035); // More pronounced vertical stretch
    
    final Path shadowPath = gardenPath.transform(shadowMatrix.storage);
    
    // Enhanced shadow quality with adaptive blur
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(shadowOpacity)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, math.min(4.5, 1.5 + elevation * 0.35))
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw shadow beneath the garden with better quality
    canvas.drawPath(shadowPath, shadowPaint);
  }
  
  /// Create garden-specific elevated sides for better 2.5D effect
  void _addGardenElevatedSides(Canvas canvas, Path gardenPath, List<Offset> points, Map<String, Color> colors, double elevation, String subtype) {
    if (elevation < 0.4 || zoomLevel < 16) return;
    
    // Enhanced colors for sides with better depth effect and saturation
    final Color baseSideColor = _getSideColor(colors['base']!, subtype);
    
    // Calculate normals for better corner handling (from buildings technique)
    List<Offset> normals = [];
    for (int i = 0; i < points.length; i++) {
      final Offset prev = points[(i - 1 + points.length) % points.length];
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      
      // Calculate normal at this point by averaging adjacent segment normals
      final Offset v1 = current - prev;
      final Offset v2 = next - current;
      
      final Offset n1 = _normalizeOffset(Offset(-v1.dy, v1.dx));
      final Offset n2 = _normalizeOffset(Offset(-v2.dy, v2.dx));
      final Offset avgNormal = _normalizeOffset(Offset((n1.dx + n2.dx) / 2, (n1.dy + n2.dy) / 2));
      
      normals.add(avgNormal);
    }
    
    // Create side path with enhanced directional lighting
    final Path sidePath = Path();
    
    // Use buildings technique: Wall orientation detection
    for (int i = 0; i < points.length; i++) {
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      final Offset currentNormal = normals[i];
      final Offset nextNormal = normals[(i + 1) % points.length];
      
      // Calculate wall direction to determine if it faces viewer
      final Offset wallDir = _normalizeOffset(next - current);
      final bool isWallFacingViewer = wallDir.dy >= 0 || wallDir.dx.abs() > 0.8;
      
      // Only draw sides for segments facing the viewer
      if (isWallFacingViewer) {
        // Start with current point
        if (!sidePath.contains(current)) {
          sidePath.moveTo(current.dx, current.dy);
        }
        
        // Draw to next point
        sidePath.lineTo(next.dx, next.dy);
        
        // Garden-specific undulations based on garden type
        double undulationFactor = 0.0;
        if (subtype == 'orchard') {
          undulationFactor = 0.3 + _random.nextDouble() * 0.2; // More dramatic for orchards
        } else if (subtype == 'vineyard') {
          undulationFactor = 0.2 + _random.nextDouble() * 0.2; // Moderate for vineyards
        } else {
          undulationFactor = 0.15 + _random.nextDouble() * 0.15; // Subtle for regular gardens
        }
        
        final double midPointElevation = elevation * (0.9 + undulationFactor);
        
        // Draw with enhanced perspective
        final double perspectiveFactorNext = 0.15 + elevation * 0.01;
        sidePath.lineTo(
          next.dx + nextNormal.dx * elevation * perspectiveFactorNext, 
          next.dy + elevation + nextNormal.dy * elevation * 0.12
        );
        
        // Draw back to current point with enhanced perspective
        final double perspectiveFactorCurrent = 0.15 + elevation * 0.01;
        sidePath.lineTo(
          current.dx + currentNormal.dx * elevation * perspectiveFactorCurrent, 
          current.dy + elevation + currentNormal.dy * elevation * 0.12
        );
        
        // Close this side segment
        sidePath.lineTo(current.dx, current.dy);
      }
    }
    
    // Create enhanced gradient for dynamic lighting effect with directional light
    final Rect bounds = sidePath.getBounds();
    
    // Use multi-stop gradient with directional lighting like buildings
    final Paint sidePaint = Paint()
      ..shader = LinearGradient(
        begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
        end: const Alignment(0.3, 0.0),
        colors: [
          baseSideColor,
          HSLColor.fromColor(baseSideColor)
            .withLightness(math.max(0.1, HSLColor.fromColor(baseSideColor).lightness * 0.5))
            .toColor(),
        ],
        stops: const [0.7, 1.0],
      ).createShader(bounds)
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw enhanced garden sides
    canvas.drawPath(sidePath, sidePaint);
    
    // Add subtle edge highlight for better definition
    final Paint edgePaint = Paint()
      ..color = Colors.black.withOpacity(0.25)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    canvas.drawPath(sidePath, edgePaint);
  }
  
  /// Get a dark color for the sides of the garden with enhanced saturation
  Color _getSideColor(Color baseColor, String subtype) {
    final HSLColor hsl = HSLColor.fromColor(baseColor);
    double saturationBoost = 1.2;
    
    // Different saturation boosts based on garden type
    if (subtype == 'orchard') {
      saturationBoost = 1.3; // More saturated for orchards
    } else if (subtype == 'vineyard') {
      saturationBoost = 1.25; // Moderate for vineyards
    }
    
    return HSLColor.fromColor(baseColor)
      .withSaturation(math.min(1.0, hsl.saturation * saturationBoost))
      .withLightness(math.max(0.15, hsl.lightness * 0.5))
      .toColor();
  }
  
  /// Get a paint for filling garden areas with enhanced directional lighting
  Paint _getGardenFillPaint(Map<String, Color> colors, Rect bounds, String subtype) {
    // For low zoom levels, use a simple solid color
    if (zoomLevel < 14 || !enhancedDetail) {
      return Paint()
        ..color = colors['base']!
        ..style = PaintingStyle.fill
        ..isAntiAlias = true;
    }
    
    // Apply color variation like buildings renderer
    final Color baseColor = _addColorVariation(colors['base']!);
    final Color highlightColor = _addColorVariation(colors['highlight']!);
    final Color detailColor = _addColorVariation(colors['detail']!);
    
    // Use directional lighting with multi-stop gradients like buildings
    switch (subtype) {
      case 'orchard':
        return Paint()
          ..shader = LinearGradient(
            begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
            end: const Alignment(0.3, 0.0),
            colors: [
              highlightColor,
              baseColor,
              detailColor,
            ],
            stops: const [0.0, 0.6, 1.0],
          ).createShader(bounds)
          ..style = PaintingStyle.fill;
      case 'vineyard':
        return Paint()
          ..shader = LinearGradient(
            begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
            end: const Alignment(0.3, 0.0),
            colors: [
              _colorsHelper.adjustBrightness(highlightColor, 0.05),
              baseColor,
              _colorsHelper.adjustBrightness(detailColor, -0.05),
            ],
            stops: const [0.0, 0.5, 1.0],
          ).createShader(bounds)
          ..style = PaintingStyle.fill;
      case 'garden':
      default:
        return Paint()
          ..shader = LinearGradient(
            begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
            end: const Alignment(0.3, 0.0),
            colors: [
              highlightColor,
              baseColor,
              detailColor,
            ],
            stops: const [0.0, 0.5, 1.0],
          ).createShader(bounds)
          ..style = PaintingStyle.fill;
    }
  }
  
  /// Add ambient occlusion around edges for enhanced 3D depth
  void _addAmbientOcclusion(Canvas canvas, Path gardenPath, double elevation) {
    // Create inset path for ambient occlusion with dynamic inset based on feature size
    final double inset = math.max(1.0, math.min(3.0, elevation * 0.4));
    
    // Create AO path by insetting the original path
    final Path aoPath = Path.combine(
      PathOperation.difference,
      gardenPath,
      Path.combine(
        PathOperation.intersect,
        gardenPath,
        Path()..addRect(gardenPath.getBounds().inflate(-inset))
      )
    );
    
    // Draw ambient occlusion with sophisticated AO effect
    final Rect bounds = gardenPath.getBounds();
    final Paint aoPaint = Paint()
      ..shader = RadialGradient(
        center: Alignment.center,
        radius: 1.0,
        colors: [
          Colors.black.withOpacity(0.3 + elevation * 0.025),
          Colors.black.withOpacity(0.0),
        ],
        stops: const [0.0, 1.0],
      ).createShader(bounds)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 2.0)
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(aoPath, aoPaint);
  }
  
  /// Add subtle color variation like buildings renderer
  Color _addColorVariation(Color baseColor, {int variationAmount = 15}) {
    // Only add variation for higher zoom levels
    if (zoomLevel < 15) return baseColor;
    
    final int variation = math.min(variationAmount, 15);
    final int r = (baseColor.red + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int g = (baseColor.green + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int b = (baseColor.blue + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    
    return Color.fromRGBO(r, g, b, baseColor.opacity);
  }
  
  /// Get appropriate border color that provides good contrast
  Color _getBorderColor(Color baseColor) {
    return HSLColor.fromColor(baseColor)
      .withSaturation(math.min(1.0, HSLColor.fromColor(baseColor).saturation * 1.1))
      .withLightness(math.max(0.1, HSLColor.fromColor(baseColor).lightness * 0.7))
      .toColor()
      .withOpacity(0.4);
  }
  
  /// Get appropriate border width based on zoom level
  double _getBorderWidth() {
    if (zoomLevel >= 18) return 0.7;
    if (zoomLevel >= 16) return 0.5;
    return 0.3;
  }
  
  /// Add orchard pattern (regularly spaced trees)
  void _addOrchardPattern(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    if (zoomLevel < 16) return;
    
    final double spacing = math.max(6.0, bounds.width / 15);
    final Color treeColor = _colorsHelper.adjustBrightness(colors['detail']!, -0.1);
    
    for (double x = bounds.left + spacing/2; x < bounds.right; x += spacing) {
      for (double y = bounds.top + spacing/2; y < bounds.bottom; y += spacing) {
        // Draw tree spots
        final Paint treePaint = Paint()
          ..color = treeColor.withOpacity(0.3)
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(x, y), 1.0, treePaint);
      }
    }
  }
  
  /// Add vineyard pattern (rows of vines)
  void _addVineyardPattern(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    if (zoomLevel < 16) return;
    
    final double rowSpacing = math.max(5.0, bounds.height / 12);
    final Color rowColor = _colorsHelper.adjustBrightness(colors['detail']!, -0.1);
    final Paint rowPaint = Paint()
      ..color = rowColor.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.8;
    
    // Draw horizontal vine rows
    for (double y = bounds.top + rowSpacing/2; y < bounds.bottom; y += rowSpacing) {
      canvas.drawLine(
        Offset(bounds.left, y),
        Offset(bounds.right, y),
        rowPaint
      );
    }
  }
  
  /// Add garden pattern (decorative layout)
  void _addGardenPattern(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    if (zoomLevel < 16) return;
    
    // Garden has different patterns - we'll use a central circular pattern
    final double centerX = bounds.left + bounds.width / 2;
    final double centerY = bounds.top + bounds.height / 2;
    final double radius = math.min(bounds.width, bounds.height) * 0.3;
    
    // Draw circular pattern
    if (bounds.width > 20 && bounds.height > 20) {
      final Paint circlePaint = Paint()
        ..color = colors['highlight']!.withOpacity(0.2)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;
      
      canvas.drawCircle(Offset(centerX, centerY), radius, circlePaint);
      
      // Add radiating paths
      final int numPaths = 6;
      final Paint pathPaint = Paint()
        ..color = colors['highlight']!.withOpacity(0.15)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.8;
      
      for (int i = 0; i < numPaths; i++) {
        final double angle = i * (math.pi * 2 / numPaths);
        
        canvas.drawLine(
          Offset(centerX, centerY),
          Offset(
            centerX + math.cos(angle) * radius * 1.2,
            centerY + math.sin(angle) * radius * 1.2
          ),
          pathPaint
        );
      }
    }
  }
  
  /// Add orchard detailed elements (individual trees)
  void _addOrchardElements(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    if (zoomLevel < 17) return;
    
    final double spacing = math.max(10.0, bounds.width / 12);
    final Color trunkColor = Colors.brown.withOpacity(0.5);
    final Color canopyColor = colors['detail']!;
    
    for (double x = bounds.left + spacing/2; x < bounds.right; x += spacing) {
      for (double y = bounds.top + spacing/2; y < bounds.bottom; y += spacing) {
        // Add slight randomization to position
        final double xOffset = ((_random.nextDouble() * 2) - 1.0) * spacing * 0.1;
        final double yOffset = ((_random.nextDouble() * 2) - 1.0) * spacing * 0.1;
        final double treeX = x + xOffset;
        final double treeY = y + yOffset;
        
        // Only draw if we're at high zoom levels
        if (zoomLevel >= 18) {
          // Draw tree trunk
          final Paint trunkPaint = Paint()
            ..color = trunkColor
            ..style = PaintingStyle.fill;
          
          canvas.drawRect(
            Rect.fromLTWH(treeX - 0.3, treeY - 0.8, 0.6, 1.6),
            trunkPaint
          );
        }
        
        // Draw tree canopy
        final Paint canopyPaint = Paint()
          ..color = canopyColor.withOpacity(0.5)
          ..style = PaintingStyle.fill;
        
        final double canopySize = 1.5 + _random.nextDouble() * 0.5;
        canvas.drawCircle(Offset(treeX, treeY - 1.0), canopySize, canopyPaint);
      }
    }
  }
  
  /// Add vineyard detailed elements (vine patterns)
  void _addVineyardElements(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    if (zoomLevel < 17) return;
    
    final double rowSpacing = math.max(8.0, bounds.height / 10);
    final double plantSpacing = math.max(5.0, bounds.width / 15);
    final Color vineColor = colors['detail']!;
    
    // Draw individual vine plants on rows
    for (double y = bounds.top + rowSpacing/2; y < bounds.bottom; y += rowSpacing) {
      for (double x = bounds.left + plantSpacing/2; x < bounds.right; x += plantSpacing) {
        // Add slight randomization
        final double xOffset = ((_random.nextDouble() * 2) - 1.0) * plantSpacing * 0.1;
        final double vineX = x + xOffset;
        
        if (zoomLevel >= 18) {
          // Draw vine stake
          final Paint stakePaint = Paint()
            ..color = Colors.brown.shade600.withOpacity(0.5)
            ..style = PaintingStyle.stroke
            ..strokeWidth = 0.5;
          
          canvas.drawLine(
            Offset(vineX, y - 1.0),
            Offset(vineX, y + 1.0),
            stakePaint
          );
        }
        
        // Draw vine
        final Paint vinePaint = Paint()
          ..color = vineColor.withOpacity(0.5)
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(vineX, y), 0.8, vinePaint);
      }
    }
  }
  
  /// Add garden detailed elements (decorative features)
  void _addGardenElements(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    if (zoomLevel < 17) return;
    
    final double centerX = bounds.left + bounds.width / 2;
    final double centerY = bounds.top + bounds.height / 2;
    
    // Garden fountain or central feature
    if (bounds.width > 30 && bounds.height > 30) {
      // Central feature
      final Paint centerPaint = Paint()
        ..color = Colors.blue.withOpacity(0.3)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        Offset(centerX, centerY),
        2.0,
        centerPaint
      );
      
      // Garden beds
      final Paint bedPaint = Paint()
        ..color = colors['detail']!.withOpacity(0.4)
        ..style = PaintingStyle.fill;
      
      // Add flower beds around the center
      final int numBeds = 4;
      final double bedDistance = math.min(bounds.width, bounds.height) * 0.25;
      final double bedSize = 3.0;
      
      for (int i = 0; i < numBeds; i++) {
        final double angle = i * (math.pi * 2 / numBeds);
        final double bedX = centerX + math.cos(angle) * bedDistance;
        final double bedY = centerY + math.sin(angle) * bedDistance;
        
        canvas.drawOval(
          Rect.fromCenter(
            center: Offset(bedX, bedY),
            width: bedSize * 2,
            height: bedSize
          ),
          bedPaint
        );
        
        // Add flowers to beds at highest zoom
        if (zoomLevel >= 18) {
          _addFlowersToGardenBed(canvas, Offset(bedX, bedY), bedSize, colors);
        }
      }
      
      // Add garden paths at high zoom
      if (zoomLevel >= 18) {
        final Paint pathPaint = Paint()
          ..color = Colors.brown.shade200.withOpacity(0.4)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;
        
        // Connect center to each bed
        for (int i = 0; i < numBeds; i++) {
          final double angle = i * (math.pi * 2 / numBeds);
          final double bedX = centerX + math.cos(angle) * bedDistance;
          final double bedY = centerY + math.sin(angle) * bedDistance;
          
          canvas.drawLine(
            Offset(centerX, centerY),
            Offset(bedX, bedY),
            pathPaint
          );
        }
      }
    }
  }
  
  /// Add flowers to a garden bed
  void _addFlowersToGardenBed(Canvas canvas, Offset center, double bedSize, Map<String, Color> colors) {
    final int numFlowers = (bedSize * 4).round();
    
    // Different flower colors based on season
    List<Color> flowerColors = [];
    
    switch (season) {
      case 'spring':
        flowerColors = [
          Colors.pink.withOpacity(0.6),
          Colors.purple.withOpacity(0.5),
          Colors.white.withOpacity(0.6),
        ];
        break;
      case 'summer':
        flowerColors = [
          Colors.red.withOpacity(0.6),
          Colors.yellow.withOpacity(0.6),
          Colors.orange.withOpacity(0.5),
          Colors.blue.withOpacity(0.5),
        ];
        break;
      case 'autumn':
        flowerColors = [
          Colors.orange.withOpacity(0.6),
          Colors.yellow.withOpacity(0.5),
          Colors.amber.withOpacity(0.6),
        ];
        break;
      case 'winter':
        flowerColors = [
          Colors.white.withOpacity(0.4),
          colors['highlight']!.withOpacity(0.3),
        ];
        break;
      default:
        flowerColors = [
          Colors.red.withOpacity(0.5),
          Colors.yellow.withOpacity(0.5),
          Colors.blue.withOpacity(0.5),
        ];
    }
    
    for (int i = 0; i < numFlowers; i++) {
      final double xOffset = ((_random.nextDouble() * 2) - 1.0) * bedSize;
      final double yOffset = ((_random.nextDouble() * 2) - 1.0) * (bedSize * 0.5);
      
      // Apply oval distribution to fit the bed shape
      final double normalizedDist = math.sqrt(
        (xOffset * xOffset) / (bedSize * bedSize) + 
        (yOffset * yOffset) / ((bedSize * 0.5) * (bedSize * 0.5))
      );
      
      if (normalizedDist <= 0.9) {
        final Color flowerColor = flowerColors[_random.nextInt(flowerColors.length)];
        final Paint flowerPaint = Paint()
          ..color = flowerColor
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(
          Offset(center.dx + xOffset, center.dy + yOffset),
          0.4 + _random.nextDouble() * 0.3,
          flowerPaint
        );
      }
    }
  }
} 