import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

import '../parks_colors.dart';
import '../parks_geometry_utils.dart';
import '../texture_generator.dart';

/// Specialized renderer for forest and wooded areas
/// Provides enhanced rendering with tree canopy texture and shadow effects
class ForestRenderer {
  final ParkGeometryUtils _geometryUtils = ParkGeometryUtils();
  final ParksColors _colorsHelper = ParksColors();
  final double zoomLevel;
  final String theme;
  final String season;
  final bool enhancedDetail;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  late final TextureGenerator _textureGenerator;
  
  ForestRenderer({
    required this.zoomLevel,
    required this.theme,
    required this.season,
    this.enhancedDetail = true,
    this.tiltFactor = 1.0,
  }) {
    _textureGenerator = TextureGenerator(
      zoomLevel: zoomLevel,
      season: season,
    );
  }
  
  /// Normalize an offset (create a unit vector with the same direction)
  Offset _normalizeOffset(Offset offset) {
    final double length = math.sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
    if (length == 0) return Offset.zero;
    return Offset(offset.dx / length, offset.dy / length);
  }
  
  /// Main render method for forest areas
  void renderForestArea(Canvas canvas, Size size, Map<String, dynamic> feature, List<Offset> points, {bool isWoodland = false}) {
    if (points.length < 3) return;
    
    // Get colors for forest or woodland
    final String featureType = isWoodland ? 'wood' : 'forest';
    final colors = _colorsHelper.getFeatureColors(featureType, theme, season);
    
    // Create the base path for the forest area
    final Path forestPath = Path()..addPolygon(points, true);
    final Rect bounds = forestPath.getBounds();
    
    // Apply 3D elevation effect for forests (they rise above the landscape)
    final double baseElevation = isWoodland ? 3.5 : 4.5; // Enhanced base elevation
    
    // Dynamic elevation based on feature size, zoom level, and tilt
    final double elevationMultiplier = _getElevationMultiplier(bounds);
    // Apply non-linear tilt enhancement and more aggressive elevation scaling
    final double scaledElevation = baseElevation * _getElevationFactor() * elevationMultiplier * _getZoomTiltEnhancement();
    
    // Draw shadow with softer edges and perspective distortion
    if (scaledElevation > 1.0 && zoomLevel >= 14) {
      _drawEnhancedForestShadow(canvas, forestPath, scaledElevation);
    }
    
    // Draw enhanced 3D sides with better perspective
    if (scaledElevation > 0.5 && zoomLevel >= 14) {
      _drawEnhanced3DSides(canvas, points, colors, scaledElevation);
    }
    
    // Get enhanced paint for filling with proper styling
    final Paint fillPaint = _getForestFillPaint(colors, forestPath.getBounds(), isWoodland);
    
    // Draw main fill
    canvas.drawPath(forestPath, fillPaint);
    
    // Draw border with enhanced style and detail
    if (zoomLevel >= 14) {
      final Paint borderPaint = Paint()
        ..color = _getBorderColor(colors['detail']!)
        ..style = PaintingStyle.stroke
        ..strokeWidth = _getBorderWidth()
        ..isAntiAlias = true;
      
      canvas.drawPath(forestPath, borderPaint);
    }
    
    // Add enhanced forest canopy texture at higher zoom levels
    if (zoomLevel >= 15 && enhancedDetail) {
      _addEnhancedForestTexture(canvas, forestPath, colors, isWoodland);
    }
    
    // Add forest floor details at high zoom levels
    if (zoomLevel >= 17 && enhancedDetail) {
      _addForestFloorDetails(canvas, forestPath, colors, isWoodland);
    }
    
    // Add clearings and trails at high zoom levels for larger forests
    if (zoomLevel >= 17 && enhancedDetail && bounds.width > 100 && bounds.height > 100) {
      _addForestClearingsAndTrails(canvas, forestPath, bounds, colors);
    }
    
    // Add ambient occlusion around edges for enhanced 3D depth
    if (zoomLevel >= 16 && enhancedDetail) {
      _addAmbientOcclusion(canvas, forestPath, scaledElevation);
    }
  }
  
  /// Draw the elevated sides of the forest with enhanced 3D effect and perspective
  void _drawEnhanced3DSides(Canvas canvas, List<Offset> points, Map<String, Color> colors, double elevation) {
    // Create enhanced dark color for sides with more natural shadowing
    final Color baseSideColor = _getSideColor(colors['base']!);
    
    // Enhanced algorithm to detect and handle corners better
    // We'll calculate normals for better corner handling
    List<Offset> normals = [];
    for (int i = 0; i < points.length; i++) {
      final Offset prev = points[(i - 1 + points.length) % points.length];
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      
      // Calculate normal at this point by averaging adjacent segment normals
      final Offset v1 = current - prev;
      final Offset v2 = next - current;
      
      final Offset n1 = _normalizeOffset(Offset(-v1.dy, v1.dx));
      final Offset n2 = _normalizeOffset(Offset(-v2.dy, v2.dx));
      final Offset avgNormal = _normalizeOffset(Offset((n1.dx + n2.dx) / 2, (n1.dy + n2.dy) / 2));
      
      normals.add(avgNormal);
    }
    
    // Create side path with enhanced directional lighting
    final Path sidePath = Path();
    
    // Find bottom-most points to draw sides (similar to OSMParksPainter)
    for (int i = 0; i < points.length; i++) {
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      final Offset currentNormal = normals[i];
      final Offset nextNormal = normals[(i + 1) % points.length];
      
      // Use building technique: Calculate wall direction to determine if it faces viewer
      final Offset wallDir = _normalizeOffset(next - current);
      final bool isWallFacingViewer = wallDir.dy >= 0 || wallDir.dx.abs() > 0.8;
      
      // Only draw sides for segments facing the viewer
      if (isWallFacingViewer) {
        // Start with current point
        if (!sidePath.contains(current)) {
          sidePath.moveTo(current.dx, current.dy);
        }
        
        // Draw to next point
        sidePath.lineTo(next.dx, next.dy);
        
        // Draw down to create elevation with perspective effect
        // Use enhanced perspective correction like buildings renderer
        final double perspectiveFactorNext = 0.15 + elevation * 0.01; // Dynamic perspective based on height
        sidePath.lineTo(
          next.dx + nextNormal.dx * elevation * perspectiveFactorNext, 
          next.dy + elevation + nextNormal.dy * elevation * 0.1
        );
        
        // Draw back to complete the side
        final double perspectiveFactorCurrent = 0.15 + elevation * 0.01;
        sidePath.lineTo(
          current.dx + currentNormal.dx * elevation * perspectiveFactorCurrent, 
          current.dy + elevation + currentNormal.dy * elevation * 0.1
        );
        
        // Close this side segment
        sidePath.lineTo(current.dx, current.dy);
      }
    }
    
    // Create enhanced gradient for dynamic lighting effect based on elevation
    final Rect bounds = sidePath.getBounds();
    
    // Use directional lighting with multi-stop gradients like buildings renderer
    final Paint sidePaint = Paint()
      ..shader = LinearGradient(
        begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
        end: const Alignment(0.3, 0.0),
        colors: [
          baseSideColor,
          HSLColor.fromColor(baseSideColor)
            .withLightness(math.max(0.1, HSLColor.fromColor(baseSideColor).lightness * 0.5))
            .toColor(),
        ],
        stops: const [0.7, 1.0],
      ).createShader(bounds)
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw enhanced 3D sides
    canvas.drawPath(sidePath, sidePaint);
    
    // Add subtle edge highlight for better definition
    final Paint edgePaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    canvas.drawPath(sidePath, edgePaint);
  }
  
  /// Calculate appropriate elevation multiplier based on forest size
  double _getElevationMultiplier(Rect bounds) {
    // Larger forests appear proportionally less elevated (more natural)
    final double area = bounds.width * bounds.height;
    
    // Non-linear scaling based on size for more natural appearance
    if (area > 50000) return 0.7;
    if (area > 20000) return 0.85;
    if (area > 5000) return 1.0;
    if (area > 1000) return 1.15;
    return 1.25; // Smallest forests get more emphasis for visibility
  }
  
  /// Draw enhanced shadow with proper perspective and softer edges
  void _drawEnhancedForestShadow(Canvas canvas, Path forestPath, double elevation) {
    // Shadow opacity increases with zoom level and elevation
    final double baseOpacity = 0.3 * _getElevationFactor();
    final double zoomEnhancement = math.min(1.7, 1.0 + (zoomLevel - 14) * 0.12);
    final double shadowOpacity = math.min(0.55, baseOpacity * zoomEnhancement);
    
    // Calculate shadow offset based on elevation, enhanced by zoom level
    final double offsetMultiplier = math.min(1.0 + (zoomLevel - 15) * 0.15, 2.2);
    final double offsetX = 2.2 * elevation * 0.3 * offsetMultiplier;
    final double offsetY = 2.2 * elevation * 0.3 * offsetMultiplier;
    
    // Use non-uniform matrix transformation for perspective-correct shadows like buildings
    final Matrix4 shadowMatrix = Matrix4.identity()
      ..translate(offsetX, offsetY)
      ..scale(1.03 + elevation * 0.015, 1.06 + elevation * 0.02); // Enhanced shadow distortion
    
    final Path shadowPath = forestPath.transform(shadowMatrix.storage);
    
    // Enhanced shadow paint with blur for softer edges
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(shadowOpacity)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, math.min(5.0, 1.5 + elevation * 0.2))
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Draw shadow beneath the forest
    canvas.drawPath(shadowPath, shadowPaint);
  }
  
  /// Add ambient occlusion around edges for enhanced 3D depth
  void _addAmbientOcclusion(Canvas canvas, Path forestPath, double elevation) {
    // Create inset path for ambient occlusion
    final double inset = math.max(1.0, math.min(3.0, elevation * 0.4));
    
    // Create AO path by inetting the original path
    final Path aoPath = Path.combine(
      PathOperation.difference,
      forestPath,
      Path.combine(
        PathOperation.intersect,
        forestPath,
        Path()..addRect(forestPath.getBounds().inflate(-inset))
      )
    );
    
    // Draw ambient occlusion with gradient for more realistic effect
    final Rect bounds = forestPath.getBounds();
    final Paint aoPaint = Paint()
      ..shader = RadialGradient(
        center: Alignment.center,
        radius: 1.0,
        colors: [
          Colors.black.withOpacity(0.3 + elevation * 0.02),
          Colors.black.withOpacity(0.0),
        ],
        stops: const [0.0, 1.0],
      ).createShader(bounds)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 2.0)
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(aoPath, aoPaint);
  }
  
  /// Get a paint for filling forest areas with enhanced styling
  Paint _getForestFillPaint(Map<String, Color> colors, Rect bounds, bool isWoodland) {
    // For low zoom levels, use a simple solid color
    if (zoomLevel < 14 || !enhancedDetail) {
      return Paint()
        ..color = colors['base']!
        ..style = PaintingStyle.fill
        ..isAntiAlias = true;
    }
    
    // Create rich directional lighting gradient based on season and forest type
    if (season == 'autumn') {
      // More dramatic lighting in autumn with rich colors
      final Color enhancedColor = _addColorVariation(colors['base']!);
      
      return Paint()
        ..shader = LinearGradient(
          begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
          end: const Alignment(0.3, 0.0),
          colors: [
            colors['highlight']!,
            enhancedColor,
            _colorsHelper.adjustBrightness(colors['detail']!, -0.1),
          ],
          stops: const [0.0, 0.6, 1.0],
        ).createShader(bounds)
        ..style = PaintingStyle.fill;
    } else if (season == 'winter') {
      // More subdued lighting in winter with less contrast
      final Color enhancedColor = _addColorVariation(colors['base']!);
      
      return Paint()
        ..shader = LinearGradient(
          begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
          end: const Alignment(0.3, 0.0),
          colors: [
            _colorsHelper.adjustBrightness(colors['highlight']!, -0.1),
            enhancedColor,
            _colorsHelper.adjustBrightness(colors['detail']!, -0.05),
          ],
          stops: const [0.0, 0.7, 1.0],
        ).createShader(bounds)
        ..style = PaintingStyle.fill;
    } else {
      // Standard rich lighting for spring/summer with color variation
      final Color enhancedColor = _addColorVariation(colors['base']!);
      
      return Paint()
        ..shader = LinearGradient(
          begin: const Alignment(-0.3, 1.0), // Tilted light source consistent with buildings
          end: const Alignment(0.3, 0.0),
          colors: [
            colors['highlight']!,
            enhancedColor,
            colors['detail']!,
          ],
          stops: const [0.0, 0.5, 1.0],
        ).createShader(bounds)
        ..style = PaintingStyle.fill;
    }
  }
  
  /// Add subtle color variation like buildings renderer
  Color _addColorVariation(Color baseColor, {int variationAmount = 15}) {
    // Only add variation for higher zoom levels
    if (zoomLevel < 15) return baseColor;
    
    final int variation = math.min(variationAmount, 15);
    final int r = (baseColor.red + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int g = (baseColor.green + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    final int b = (baseColor.blue + (_random.nextInt(variation) - (variation~/2))).clamp(0, 255);
    
    return Color.fromRGBO(r, g, b, baseColor.opacity);
  }
  
  /// Add enhanced forest canopy texture with more sophisticated details
  void _addEnhancedForestTexture(Canvas canvas, Path forestPath, Map<String, Color> colors, bool isWoodland) {
    final Rect bounds = forestPath.getBounds();
    
    // Skip detailed textures at lower zoom levels
    if (zoomLevel < 15) return;
    
    // Save canvas state to clip to the forest area
    canvas.save();
    canvas.clipPath(forestPath);
    
    // Use texture generator for consistent texturing with enhanced color variation
    _textureGenerator.generateForestCanopyTexture(
      canvas,
      bounds,
      colors,
      density: isWoodland ? 0.8 : 1.0, // Woodlands are slightly less dense
      isConiferous: isWoodland ? false : true // Coniferous trees for regular forests, deciduous for woodlands
    );
    
    // Add highlights for sun through trees at high zoom levels
    if (zoomLevel >= 17 && (season != 'winter')) {
      _addSunlightThroughTrees(canvas, bounds, colors);
    }
    
    // Restore canvas state
    canvas.restore();
  }
  
  /// Add subtle highlights to simulate sunlight filtering through the canopy
  void _addSunlightThroughTrees(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final int highlightCount = (bounds.width * bounds.height / 10000).round().clamp(3, 15);
    
    final Paint highlightPaint = Paint()
      ..color = Colors.white.withOpacity(0.15)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);
    
    for (int i = 0; i < highlightCount; i++) {
      final double x = bounds.left + _random.nextDouble() * bounds.width;
      final double y = bounds.top + _random.nextDouble() * bounds.height;
      final double size = 5 + _random.nextDouble() * 8;
      
      canvas.drawCircle(Offset(x, y), size, highlightPaint);
    }
  }
  
  /// Add forest floor details like fallen logs, rocks, undergrowth
  void _addForestFloorDetails(Canvas canvas, Path forestPath, Map<String, Color> colors, bool isWoodland) {
    final Rect bounds = forestPath.getBounds();
    final double density = isWoodland ? 0.5 : 0.8; // Dense forests have more floor details
    
    // Calculate number of details based on area and density
    final int detailCount = (bounds.width * bounds.height * density / 8000).round().clamp(3, 30);
    
    for (int i = 0; i < detailCount; i++) {
      final double x = bounds.left + _random.nextDouble() * bounds.width;
      final double y = bounds.top + _random.nextDouble() * bounds.height;
      
      // Randomly choose between different floor details
      final int detailType = _random.nextInt(3); // 0 = log, 1 = rock, 2 = undergrowth
      
      switch (detailType) {
        case 0: // Fallen log
          _drawFallenLog(canvas, Offset(x, y), colors);
          break;
        case 1: // Rock
          _drawForestRock(canvas, Offset(x, y), colors);
          break;
        case 2: // Undergrowth patch
          _drawUndergrowthPatch(canvas, Offset(x, y), colors);
          break;
      }
    }
  }
  
  /// Draw a fallen log on the forest floor
  void _drawFallenLog(Canvas canvas, Offset position, Map<String, Color> colors) {
    final double length = 3.0 + _random.nextDouble() * 7.0;
    final double width = 1.0 + _random.nextDouble() * 1.5;
    final double angle = _random.nextDouble() * math.pi;
    
    // Create log path
    final Path logPath = Path();
    logPath.addRRect(RRect.fromRectAndRadius(
      Rect.fromCenter(center: position, width: length, height: width),
      Radius.circular(width / 2)
    ));
    
    // Rotate log
    final Matrix4 rotationMatrix = Matrix4.identity()
      ..translate(position.dx, position.dy)
      ..rotateZ(angle)
      ..translate(-position.dx, -position.dy);
    
    final Path rotatedLogPath = logPath.transform(rotationMatrix.storage);
    
    // Brown color for log
    final Color logColor = HSLColor.fromColor(const Color(0xFF8D6E63))
        .withLightness(0.3 + _random.nextDouble() * 0.2)
        .toColor();
    
    final Paint logPaint = Paint()
      ..color = logColor.withOpacity(0.7)
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(rotatedLogPath, logPaint);
    
    // Add subtle shadow
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.2)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1.0);
    
    final Path shadowPath = rotatedLogPath.shift(const Offset(0.5, 0.5));
    canvas.drawPath(shadowPath, shadowPaint);
  }
  
  /// Draw a rock on the forest floor
  void _drawForestRock(Canvas canvas, Offset position, Map<String, Color> colors) {
    final double size = 1.5 + _random.nextDouble() * 2.5;
    
    // Create irregular rock shape
    final Path rockPath = Path();
    rockPath.moveTo(position.dx, position.dy);
    
    final int numPoints = 5 + _random.nextInt(3);
    
    for (int i = 0; i < numPoints; i++) {
      final double angle = i * (2 * math.pi / numPoints);
      final double jitter = 0.7 + _random.nextDouble() * 0.6;
      rockPath.lineTo(
        position.dx + math.cos(angle) * size * jitter,
        position.dy + math.sin(angle) * size * jitter
      );
    }
    
    rockPath.close();
    
    // Gray color for rock
    final Color rockColor = HSLColor.fromColor(Colors.grey)
        .withLightness(0.3 + _random.nextDouble() * 0.3)
        .toColor();
    
    final Paint rockPaint = Paint()
      ..color = rockColor.withOpacity(0.8)
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(rockPath, rockPaint);
    
    // Add subtle shadow
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.2)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1.0);
    
    final Path shadowPath = rockPath.shift(const Offset(0.3, 0.3));
    canvas.drawPath(shadowPath, shadowPaint);
  }
  
  /// Draw undergrowth patch on forest floor
  void _drawUndergrowthPatch(Canvas canvas, Offset position, Map<String, Color> colors) {
    final double size = 2.0 + _random.nextDouble() * 4.0;
    
    // Create a radial gradient for undergrowth
    final Paint undergrowthPaint = Paint()
      ..shader = RadialGradient(
        center: Alignment.center,
        radius: 1.0,
        colors: [
          _colorsHelper.adjustBrightness(colors['base']!, -0.1).withOpacity(0.7),
          _colorsHelper.adjustBrightness(colors['base']!, -0.1).withOpacity(0.0),
        ],
        stops: const [0.0, 1.0],
      ).createShader(Rect.fromCircle(center: position, radius: size))
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(position, size, undergrowthPaint);
  }
  
  /// Add forest clearings and trails for large forest areas
  void _addForestClearingsAndTrails(Canvas canvas, Path forestPath, Rect bounds, Map<String, Color> colors) {
    // Add a clearing if the forest is large enough
    if (bounds.width > 150 && bounds.height > 150 && _random.nextDouble() < 0.7) {
      _addForestClearing(canvas, bounds, colors);
    }
    
    // Add trails through the forest
    if (bounds.width > 120 && _random.nextDouble() < 0.8) {
      _addForestTrail(canvas, bounds, colors);
    }
  }
  
  /// Add a natural clearing in the forest
  void _addForestClearing(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    // Position clearing somewhere within the forest, but not at the edge
    final double clearingX = bounds.left + bounds.width * (0.3 + _random.nextDouble() * 0.4);
    final double clearingY = bounds.top + bounds.height * (0.3 + _random.nextDouble() * 0.4);
    final double clearingSize = math.min(bounds.width, bounds.height) * (0.1 + _random.nextDouble() * 0.15);
    
    // Create irregular clearing shape
    final Path clearingPath = Path();
    clearingPath.moveTo(clearingX, clearingY);
    
    final int numPoints = 8 + _random.nextInt(4);
    
    for (int i = 0; i < numPoints; i++) {
      final double angle = i * (2 * math.pi / numPoints);
      final double jitter = 0.7 + _random.nextDouble() * 0.6;
      clearingPath.lineTo(
        clearingX + math.cos(angle) * clearingSize * jitter,
        clearingY + math.sin(angle) * clearingSize * jitter
      );
    }
    
    clearingPath.close();
    
    // Use a slightly lighter green for the clearing
    final Color clearingColor = HSLColor.fromColor(colors['base']!)
        .withLightness(math.min(0.75, HSLColor.fromColor(colors['base']!).lightness * 1.3))
        .toColor();
    
    final Paint clearingPaint = Paint()
      ..color = clearingColor
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(clearingPath, clearingPaint);
    
    // Add some grass texture to the clearing
    final Paint grassPaint = Paint()
      ..color = colors['highlight']!.withOpacity(0.2)
      ..style = PaintingStyle.fill;
    
    final Rect clearingBounds = clearingPath.getBounds();
    final int grassDots = (clearingBounds.width * clearingBounds.height / 100).round();
    
    canvas.save();
    canvas.clipPath(clearingPath);
    
    for (int i = 0; i < grassDots; i++) {
      final double x = clearingBounds.left + _random.nextDouble() * clearingBounds.width;
      final double y = clearingBounds.top + _random.nextDouble() * clearingBounds.height;
      canvas.drawCircle(Offset(x, y), 0.5 + _random.nextDouble(), grassPaint);
    }
    
    canvas.restore();
  }
  
  /// Add a trail through the forest
  void _addForestTrail(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    // Create a curved trail through the forest
    final Path trailPath = Path();
    
    // Start trail at one edge
    bool horizontalTrail = bounds.width > bounds.height;
    
    if (horizontalTrail) {
      // Horizontal trail
      final double startY = bounds.top + bounds.height * (0.3 + _random.nextDouble() * 0.4);
      trailPath.moveTo(bounds.left, startY);
      
      // Add a curve through the forest
      trailPath.quadraticBezierTo(
        bounds.left + bounds.width * 0.5,
        bounds.top + bounds.height * (0.3 + _random.nextDouble() * 0.4),
        bounds.right,
        bounds.top + bounds.height * (0.3 + _random.nextDouble() * 0.4)
      );
    } else {
      // Vertical trail
      final double startX = bounds.left + bounds.width * (0.3 + _random.nextDouble() * 0.4);
      trailPath.moveTo(startX, bounds.top);
      
      // Add a curve through the forest
      trailPath.quadraticBezierTo(
        bounds.left + bounds.width * (0.3 + _random.nextDouble() * 0.4),
        bounds.top + bounds.height * 0.5,
        bounds.left + bounds.width * (0.3 + _random.nextDouble() * 0.4),
        bounds.bottom
      );
    }
    
    // Trail paint - dirt color
    final Paint trailPaint = Paint()
      ..color = const Color(0xFFA1887F).withOpacity(0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5 + _random.nextDouble() * 0.5;
    
    canvas.drawPath(trailPath, trailPaint);
  }
  
  /// Get elevation factor based on zoom level and tilt setting with more aggressive scaling
  double _getElevationFactor() {
    // Scale elevation based on zoom level and tilt factor with enhanced values
    if (zoomLevel < 12) return 0.7 * tiltFactor;
    if (zoomLevel < 14) return 0.9 * tiltFactor;
    if (zoomLevel < 16) return 1.2 * tiltFactor;
    if (zoomLevel < 18) return 1.8 * tiltFactor;
    if (zoomLevel < 20) return 2.5 * tiltFactor;
    return 3.0 * tiltFactor; // Extreme enhancement at highest zoom
  }
  
  /// Non-linear tilt enhancement like buildings renderer
  double _getZoomTiltEnhancement() {
    if (zoomLevel <= 14) return 1.0;
    if (zoomLevel >= 20) return 3.0;
    
    // Non-linear power function for smooth transition
    return 1.0 + math.pow((zoomLevel - 14) / 6, 1.2) * 2.0;
  }
  
  /// Get a dark color for the sides of the forest
  Color _getSideColor(Color baseColor) {
    return HSLColor.fromColor(baseColor)
      .withLightness(math.max(0.15, HSLColor.fromColor(baseColor).lightness * 0.5))
      .toColor();
  }
  
  /// Get appropriate border color that provides good contrast
  Color _getBorderColor(Color baseColor) {
    return HSLColor.fromColor(baseColor)
      .withSaturation(math.min(1.0, HSLColor.fromColor(baseColor).saturation * 1.2))
      .withLightness(math.max(0.1, HSLColor.fromColor(baseColor).lightness * 0.7))
      .toColor()
      .withOpacity(0.4);
  }
  
  /// Get appropriate border width based on zoom level
  double _getBorderWidth() {
    if (zoomLevel >= 18) return 0.8;
    if (zoomLevel >= 16) return 0.6;
    return 0.4;
  }
  
  /// Get texture density based on zoom level
  double _getTextureDensity() {
    if (zoomLevel >= 18) return 2.0;
    if (zoomLevel >= 17) return 1.5;
    if (zoomLevel >= 16) return 1.0;
    return 0.5;
  }
} 