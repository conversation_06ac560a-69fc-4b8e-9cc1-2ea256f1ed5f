import 'package:flutter/material.dart';

/// Utility class to handle color palettes for different vegetation types
class ParksColors {
  // Advanced vegetation color palettes for different themes and seasons
  static final Map<String, Map<String, Map<String, Map<String, Color>>>> _colorPalettes = {
    // Base theme colors
    'vibrant': {
      // Regular season colors
      'default': {
        'park': {
          'base': const Color(0xFF43A047), 
          'detail': const Color(0xFF66BB6A),
          'highlight': const Color(0xFFA5D6A7)
        },
        'forest': {
          'base': const Color(0xFF2E7D32), 
          'detail': const Color(0xFF388E3C),
          'highlight': const Color(0xFF81C784)
        },
        'grass': {
          'base': const Color(0xFF66BB6A), 
          'detail': const Color(0xFF81C784),
          'highlight': const Color(0xFFA5D6A7)
        },
        'meadow': {
          'base': const Color(0xFF81C784), 
          'detail': const Color(0xFFA5D6A7),
          'highlight': const Color(0xFFC8E6C9)
        },
        'garden': {
          'base': const Color(0xFF4CAF50), 
          'detail': const Color(0xFF66BB6A),
          'highlight': const Color(0xFF81C784)
        },
        'wood': {
          'base': const Color(0xFF33691E), 
          'detail': const Color(0xFF558B2F),
          'highlight': const Color(0xFF689F38)
        },
        'orchard': {
          'base': const Color(0xFFAED581), 
          'detail': const Color(0xFF8BC34A),
          'highlight': const Color(0xFFDCEDC8)
        },
        'vineyard': {
          'base': const Color(0xFFCDDC39), 
          'detail': const Color(0xFFAFB42B),
          'highlight': const Color(0xFFF0F4C3)
        },
        'other': {
          'base': const Color(0xFF4CAF50), 
          'detail': const Color(0xFF81C784),
          'highlight': const Color(0xFFA5D6A7)
        },
      },
      // Spring colors - brighter and more vibrant
      'spring': {
        'park': {
          'base': const Color(0xFF66BB6A), 
          'detail': const Color(0xFF81C784),
          'highlight': const Color(0xFFA5D6A7)
        },
        'forest': {
          'base': const Color(0xFF388E3C), 
          'detail': const Color(0xFF43A047),
          'highlight': const Color(0xFF66BB6A)
        },
        'grass': {
          'base': const Color(0xFF7CB342), 
          'detail': const Color(0xFF8BC34A),
          'highlight': const Color(0xFFAED581)
        },
        'meadow': {
          'base': const Color(0xFF9CCC65), 
          'detail': const Color(0xFFAED581),
          'highlight': const Color(0xFFCDDC39)
        },
        'garden': {
          'base': const Color(0xFF66BB6A), 
          'detail': const Color(0xFF81C784),
          'highlight': const Color(0xFFA5D6A7)
        },
        'wood': {
          'base': const Color(0xFF558B2F), 
          'detail': const Color(0xFF689F38),
          'highlight': const Color(0xFF7CB342)
        },
        'orchard': {
          'base': const Color(0xFFAED581), 
          'detail': const Color(0xFF9CCC65),
          'highlight': const Color(0xFF8BC34A)
        },
        'vineyard': {
          'base': const Color(0xFFCDDC39), 
          'detail': const Color(0xFFD4E157),
          'highlight': const Color(0xFFDCE775)
        },
        'other': {
          'base': const Color(0xFF66BB6A), 
          'detail': const Color(0xFF81C784),
          'highlight': const Color(0xFFA5D6A7)
        },
      },
      // Autumn colors - more red/orange/yellow
      'autumn': {
        'park': {
          'base': const Color(0xFFFFB74D), 
          'detail': const Color(0xFFFFCC80),
          'highlight': const Color(0xFFFFE0B2)
        },
        'forest': {
          'base': const Color(0xFFF57C00), 
          'detail': const Color(0xFFFB8C00),
          'highlight': const Color(0xFFFFB74D)
        },
        'grass': {
          'base': const Color(0xFFD4E157), 
          'detail': const Color(0xFFDCE775),
          'highlight': const Color(0xFFE6EE9C)
        },
        'meadow': {
          'base': const Color(0xFFFFF176), 
          'detail': const Color(0xFFFFF59D),
          'highlight': const Color(0xFFFFF9C4)
        },
        'garden': {
          'base': const Color(0xFFFF8F00), 
          'detail': const Color(0xFFFFA000),
          'highlight': const Color(0xFFFFB300)
        },
        'wood': {
          'base': const Color(0xFFBF360C), 
          'detail': const Color(0xFFD84315),
          'highlight': const Color(0xFFE64A19)
        },
        'orchard': {
          'base': const Color(0xFFFF6F00), 
          'detail': const Color(0xFFFF8F00),
          'highlight': const Color(0xFFFFA000)
        },
        'vineyard': {
          'base': const Color(0xFFC0CA33), 
          'detail': const Color(0xFFD4E157),
          'highlight': const Color(0xFFDCE775)
        },
        'other': {
          'base': const Color(0xFFFF9800), 
          'detail': const Color(0xFFFFA726),
          'highlight': const Color(0xFFFFB74D)
        },
      },
      // Winter colors - more muted
      'winter': {
        'park': {
          'base': const Color(0xFFB0BEC5), 
          'detail': const Color(0xFFCFD8DC),
          'highlight': const Color(0xFFECEFF1)
        },
        'forest': {
          'base': const Color(0xFF546E7A), 
          'detail': const Color(0xFF607D8B),
          'highlight': const Color(0xFF78909C)
        },
        'grass': {
          'base': const Color(0xFFBDBDBD), 
          'detail': const Color(0xFFE0E0E0),
          'highlight': const Color(0xFFEEEEEE)
        },
        'meadow': {
          'base': const Color(0xFFE0E0E0), 
          'detail': const Color(0xFFEEEEEE),
          'highlight': const Color(0xFFF5F5F5)
        },
        'garden': {
          'base': const Color(0xFF78909C), 
          'detail': const Color(0xFF90A4AE),
          'highlight': const Color(0xFFB0BEC5)
        },
        'wood': {
          'base': const Color(0xFF455A64), 
          'detail': const Color(0xFF546E7A),
          'highlight': const Color(0xFF607D8B)
        },
        'orchard': {
          'base': const Color(0xFF90A4AE), 
          'detail': const Color(0xFFB0BEC5),
          'highlight': const Color(0xFFCFD8DC)
        },
        'vineyard': {
          'base': const Color(0xFF9E9E9E), 
          'detail': const Color(0xFFBDBDBD),
          'highlight': const Color(0xFFE0E0E0)
        },
        'other': {
          'base': const Color(0xFF90A4AE), 
          'detail': const Color(0xFFB0BEC5),
          'highlight': const Color(0xFFCFD8DC)
        },
      },
    },
    // Dark theme colors
    'dark': {
      // Regular season colors for dark theme
      'default': {
        'park': {
          'base': const Color(0xFF2E7D32).withAlpha(220), 
          'detail': const Color(0xFF388E3C).withAlpha(220),
          'highlight': const Color(0xFF43A047).withAlpha(200)
        },
        'forest': {
          'base': const Color(0xFF1B5E20).withAlpha(220), 
          'detail': const Color(0xFF2E7D32).withAlpha(220),
          'highlight': const Color(0xFF388E3C).withAlpha(200)
        },
        'grass': {
          'base': const Color(0xFF388E3C).withAlpha(200), 
          'detail': const Color(0xFF43A047).withAlpha(200),
          'highlight': const Color(0xFF4CAF50).withAlpha(180)
        },
        'meadow': {
          'base': const Color(0xFF558B2F).withAlpha(200), 
          'detail': const Color(0xFF689F38).withAlpha(200),
          'highlight': const Color(0xFF7CB342).withAlpha(180)
        },
        'garden': {
          'base': const Color(0xFF2E7D32).withAlpha(220), 
          'detail': const Color(0xFF388E3C).withAlpha(220),
          'highlight': const Color(0xFF43A047).withAlpha(200)
        },
        'wood': {
          'base': const Color(0xFF1B5E20).withAlpha(230), 
          'detail': const Color(0xFF2E7D32).withAlpha(230),
          'highlight': const Color(0xFF388E3C).withAlpha(210)
        },
        'orchard': {
          'base': const Color(0xFF33691E).withAlpha(220), 
          'detail': const Color(0xFF558B2F).withAlpha(220),
          'highlight': const Color(0xFF689F38).withAlpha(200)
        },
        'vineyard': {
          'base': const Color(0xFF827717).withAlpha(220), 
          'detail': const Color(0xFF9E9D24).withAlpha(220),
          'highlight': const Color(0xFFAFB42B).withAlpha(200)
        },
        'other': {
          'base': const Color(0xFF2E7D32).withAlpha(220), 
          'detail': const Color(0xFF388E3C).withAlpha(220),
          'highlight': const Color(0xFF43A047).withAlpha(200)
        },
      },
      // Other seasons in dark theme have similar patterns but with appropriate color shifts
    },
    // Monochrome theme
    'monochrome': {
      'default': {
        'park': {
          'base': const Color(0xFF4B5563), 
          'detail': const Color(0xFF6B7280),
          'highlight': const Color(0xFF9CA3AF)
        },
        'forest': {
          'base': const Color(0xFF374151), 
          'detail': const Color(0xFF4B5563),
          'highlight': const Color(0xFF6B7280)
        },
        'grass': {
          'base': const Color(0xFF6B7280), 
          'detail': const Color(0xFF9CA3AF),
          'highlight': const Color(0xFFD1D5DB)
        },
        'meadow': {
          'base': const Color(0xFF9CA3AF), 
          'detail': const Color(0xFFD1D5DB),
          'highlight': const Color(0xFFE5E7EB)
        },
        'garden': {
          'base': const Color(0xFF4B5563), 
          'detail': const Color(0xFF6B7280),
          'highlight': const Color(0xFF9CA3AF)
        },
        'wood': {
          'base': const Color(0xFF1F2937), 
          'detail': const Color(0xFF374151),
          'highlight': const Color(0xFF4B5563)
        },
        'orchard': {
          'base': const Color(0xFF4B5563), 
          'detail': const Color(0xFF6B7280),
          'highlight': const Color(0xFF9CA3AF)
        },
        'vineyard': {
          'base': const Color(0xFF6B7280), 
          'detail': const Color(0xFF9CA3AF),
          'highlight': const Color(0xFFD1D5DB)
        },
        'other': {
          'base': const Color(0xFF4B5563), 
          'detail': const Color(0xFF6B7280),
          'highlight': const Color(0xFF9CA3AF)
        },
      },
      // No seasonal variations for monochrome
    },
  };

  /// Get colors for a specific feature type based on theme and season
  Map<String, Color> getFeatureColors(String featureType, String theme, String season) {
    // Default to using vibrant theme and default season if not found
    final String effectiveTheme = _colorPalettes.containsKey(theme) ? theme : 'vibrant';
    
    // For theme, check if the requested season exists, otherwise use default
    final themeColors = _colorPalettes[effectiveTheme]!;
    final String effectiveSeason = themeColors.containsKey(season) ? season : 'default';
    
    // For season, check if the requested feature type exists, otherwise use 'other'
    final seasonColors = themeColors[effectiveSeason]!;
    final String effectiveFeatureType = seasonColors.containsKey(featureType) ? featureType : 'other';
    
    return seasonColors[effectiveFeatureType]!;
  }
  
  /// Get all theme colors for the specified theme and season
  Map<String, Map<String, Color>> getThemeColors(String theme, String season) {
    // Default to using vibrant theme and default season if not found
    final String effectiveTheme = _colorPalettes.containsKey(theme) ? theme : 'vibrant';
    
    // For theme, check if the requested season exists, otherwise use default
    final themeColors = _colorPalettes[effectiveTheme]!;
    final String effectiveSeason = themeColors.containsKey(season) ? season : 'default';
    
    return themeColors[effectiveSeason]!;
  }
  
  /// Get a sport-specific accent color for pitch markings
  Color getSportColor(String sportType) {
    switch (sportType) {
      case 'soccer':
      case 'football':
        return Colors.green.shade600;
      case 'tennis':
        return Colors.green.shade800;
      case 'basketball':
        return Colors.orange.shade700;
      case 'baseball':
        return Colors.brown.shade400;
      case 'cricket':
        return Colors.lightGreen.shade600;
      case 'golf':
        return Colors.green.shade600;
      default:
        return Colors.green.shade600;
    }
  }
  
  /// Get contrasting color for pitch markings
  Color getContrastColor(Color backgroundColor) {
    // Calculate relative luminance (simplified)
    final double luminance = (0.299 * backgroundColor.red + 
                             0.587 * backgroundColor.green + 
                             0.114 * backgroundColor.blue) / 255;
    
    // Use white for dark backgrounds, black for light backgrounds
    return luminance > 0.5 ? Colors.black.withOpacity(0.7) : Colors.white.withOpacity(0.8);
  }
  
  /// Brighten or darken a color by percentage
  Color adjustBrightness(Color color, double amount) {
    final int r = (color.red + (amount * 255)).round().clamp(0, 255);
    final int g = (color.green + (amount * 255)).round().clamp(0, 255);
    final int b = (color.blue + (amount * 255)).round().clamp(0, 255);
    
    return Color.fromARGB(color.alpha, r, g, b);
  }
} 