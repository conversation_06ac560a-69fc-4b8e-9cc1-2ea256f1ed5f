import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

import '../osm_data_processor.dart';
import '../../../../services/map_cache_manager.dart';
import '../../map_caching/map_cache_extension.dart';
import '../../map_caching/zoom_level_manager.dart';
import '../../map_caching/map_cache_coordinator.dart';

/// Data provider class that handles park and green space data fetching and caching
/// This class is responsible for all the park data-related logic, separating it from UI rendering
class OSMParksDataProvider {
  // Data processing and caching dependencies
  final OSMDataProcessor _dataProcessor = OSMDataProcessor();
  final MapCacheManager _cacheManager = MapCacheManager();
  final ZoomLevelManager _zoomManager = ZoomLevelManager();
  
  // Callback for error handling
  final Function(String errorMessage)? onError;
  
  // State variables for different types of green spaces
  Map<String, List<Map<String, dynamic>>> _parksData = {
    'park': [],
    'forest': [],
    'grass': [],
    'meadow': [],
    'garden': [],
    'wood': [],
    'orchard': [],
    'vineyard': [],
    'trees': [],
    'other': [],
  };
  
  bool _isLoading = true;
  bool _needsRefresh = true;
  String _lastBoundsKey = "";
  bool _hasError = false;
  bool _didInitialFetch = false;
  
  // Track current season for automatic seasonal coloring
  String _currentSeason = 'summer';
  
  // Track last fetch params to avoid unnecessary fetches
  LatLngBounds? _lastFetchedBounds;
  double _lastFetchedZoom = 0;
  
  // Current parameters
  double _zoomLevel;
  LatLngBounds _visibleBounds;
  bool _isMapMoving;
  
  // Track current zoom level bucket for optimized rendering
  int _currentZoomBucket = 3;
  
  // Keep track of the last request time for rate limiting
  DateTime _lastRequestTime = DateTime.now().subtract(const Duration(seconds: 30));
  
  /// Constructor that takes initial parameters and optional error callback
  OSMParksDataProvider({
    required double initialZoomLevel,
    required LatLngBounds initialBounds,
    bool isMapMoving = false,
    this.onError,
  }) : 
    _zoomLevel = initialZoomLevel,
    _visibleBounds = initialBounds,
    _isMapMoving = isMapMoving {
    // Initialize zoom bucket
    _currentZoomBucket = _getZoomBucket(initialZoomLevel);
    
    // Determine current season based on date
    _currentSeason = _determineSeason();
  }
  
  /// Update the parameters when map changes
  void updateParameters({
    double? zoomLevel,
    LatLngBounds? visibleBounds,
    bool? isMapMoving,
    String? theme,
    String? season,
    bool? enhancedDetail,
  }) {
    // Update current parameters
    if (zoomLevel != null) _zoomLevel = zoomLevel;
    if (visibleBounds != null) _visibleBounds = visibleBounds;
    if (isMapMoving != null) _isMapMoving = isMapMoving;
    if (season != null) _currentSeason = season;
    
    // Update zoom bucket if needed
    if (zoomLevel != null) {
      final newZoomBucket = _getZoomBucket(zoomLevel);
      if (newZoomBucket != _currentZoomBucket) {
        _currentZoomBucket = newZoomBucket;
        _needsRefresh = true;
      }
    }
  }
  
  /// Main method for fetching parks data
  Future<void> fetchParksData() async {
    try {
      // Skip if we're at global view level where detailed park data isn't needed
      if (_currentZoomBucket <= 1 && _zoomLevel < 7) {
        _clearAllParksData();
        _isLoading = false;
        _needsRefresh = false;
        _didInitialFetch = true;
        _hasError = false;
        return;
      }
      
      _isLoading = !_didInitialFetch; // Only show loading on first fetch
      
      // Update bounds key
      _lastBoundsKey = _getBoundsKey(_visibleBounds, _zoomLevel);
      
      // Check if bounds or zoom has significantly changed
      bool shouldSkipFetch = false;
      if (_lastFetchedBounds != null && _lastFetchedZoom > 0) {
        final boundsDistance = _calculateBoundsDistance(_visibleBounds, _lastFetchedBounds!);
        final zoomDifference = (_zoomLevel - _lastFetchedZoom).abs();
        
        // If we're moving the map and have cached data, delay the fetch
        if (_isMapMoving && !_areAllParksEmpty()) {
          shouldSkipFetch = true;
        }
        // REDUCED distance threshold to refresh more often
        else if (boundsDistance < 0.08 && zoomDifference < 0.8 && !_areAllParksEmpty()) {
          shouldSkipFetch = true;
        }
      }
      
      if (shouldSkipFetch) {
        _isLoading = false;
        _needsRefresh = true; // Mark for refresh when map movement stops
        return;
      }
      
      // Generate cache key for the MapCacheCoordinator
      final cacheKey = 'parks_${_visibleBounds.southWest.latitude.toStringAsFixed(4)}_${_visibleBounds.southWest.longitude.toStringAsFixed(4)}_${_visibleBounds.northEast.latitude.toStringAsFixed(4)}_${_visibleBounds.northEast.longitude.toStringAsFixed(4)}_${_currentZoomBucket}';
      
      try {
        // Use MapCacheCoordinator to get data from cache or fetch from network
        final parksData = await MapCacheCoordinator().getData(
          type: MapDataType.parks,
          key: cacheKey,
          southwest: _visibleBounds.southWest,
          northeast: _visibleBounds.northEast,
          zoomLevel: _zoomLevel,
          fetchIfMissing: () async {
            // Adapt detail level based on zoom bucket
            final detailLevel = _getDetailLevel(_currentZoomBucket);
            
            // Calculate a safe data request region based on API limits
            final safeRequestBounds = _calculateSafeRequestBounds(
              _visibleBounds,
              detailLevel,
              _zoomLevel
            );
            
            // Fetch parks data with appropriate detail level
            return await _dataProcessor.fetchParksData(
              safeRequestBounds.southWest,
              safeRequestBounds.northEast,
              detailLevel: detailLevel
            );
          }
        );
        
        // Process and categorize the data
        if (parksData != null) {
          _categorizeParksData(parksData);
        } else {
          _clearAllParksData();
        }
        
        _isLoading = false;
        _needsRefresh = false;
        _lastFetchedBounds = _visibleBounds;
        _lastFetchedZoom = _zoomLevel;
        _didInitialFetch = true;
        _hasError = false;
      } catch (e) {
        debugPrint('Error in OSMParksDataProvider: $e');
        _isLoading = false;
        _hasError = true;
        _didInitialFetch = true; // We did try to fetch
        
        // Notify listeners about the error
        onError?.call(e.toString());
      }
    } catch (e) {
      debugPrint('Error in OSMParksDataProvider.fetchParksData: $e');
      _isLoading = false;
      _hasError = true;
      _didInitialFetch = true;
      onError?.call('Error in parks data provider: $e');
    }
  }
  
  /// Convenience method to fetch parks data for a specific bounds and zoom level
  /// Returns the categorized parks data
  Future<Map<String, List<Map<String, dynamic>>>> fetchParksInBounds(
    LatLngBounds bounds,
    double zoom,
  ) async {
    // Update parameters
    updateParameters(
      zoomLevel: zoom,
      visibleBounds: bounds,
    );
    
    // Fetch the data
    await fetchParksData();
    
    // Return the categorized data
    return _parksData;
  }
  
  /// Determine if we need to fetch new data based on parameter changes
  bool shouldFetchNewData({
    required double oldZoomLevel,
    required LatLngBounds oldBounds,
    required bool oldIsMoving,
    required double newZoomLevel,
    required LatLngBounds newBounds,
    required bool newIsMoving,
  }) {
    // Check if zoom bucket has changed
    final oldZoomBucket = _getZoomBucket(oldZoomLevel);
    final newZoomBucket = _getZoomBucket(newZoomLevel);
    final zoomBucketChanged = oldZoomBucket != newZoomBucket;
    
    // Check if bounds have changed significantly
    bool boundsChanged = false;
    if (oldBounds != newBounds) {
      final newBoundsKey = _getBoundsKey(newBounds, newZoomLevel);
      boundsChanged = newBoundsKey != _lastBoundsKey;
    }
    
    // Track if map has stopped moving
    final wasMoving = oldIsMoving;
    final isMovingNow = newIsMoving;
    final stoppedMoving = wasMoving && !isMovingNow;
    
    // Decide if we should fetch new data
    if ((boundsChanged && !isMovingNow) || 
        (stoppedMoving && _needsRefresh) || 
        zoomBucketChanged) {
      
      // If we just stopped moving, we can delay the fetch slightly
      if (stoppedMoving) {
        _needsRefresh = true;
        return false; // We'll fetch in a moment when the map settles
      } else {
        return true; // Fetch immediately
      }
    }
    
    return false;
  }
  
  /// Reset error state to try again
  void resetErrorState() {
    _hasError = false;
    _dataProcessor.resetErrorState();
  }
  
  /// Process the raw data from the API and categorize it into park types
  void _categorizeParksData(Map<String, List<Map<String, dynamic>>> rawData) {
    // Clear existing data
    _clearAllParksData();
    
    // Process parks
    if (rawData.containsKey('parks')) {
      for (final park in rawData['parks']!) {
        final tags = park['tags'] as Map<String, dynamic>?;
        
        if (tags == null) {
          continue;
        }
        
        // Skip features that should be handled by specialized green layer
        if (_isSpecializedGreenFeature(tags)) {
          continue;
        }
        
        // Determine the type of green space
        if (tags.containsKey('leisure') && tags['leisure'] == 'park') {
          _parksData['park']!.add(park);
        } 
        // IMPROVED grass detection - check for multiple tag combinations
        else if (tags.containsKey('landuse') && tags['landuse'] == 'grass') {
          _parksData['grass']!.add(park);
        }
        else if (tags.containsKey('leisure') && tags['leisure'] == 'pitch') {
          // Add pitches to grass category for visibility
          _parksData['grass']!.add(park);
        }
        else if (tags.containsKey('leisure') && 
                (tags['leisure'] == 'recreation_ground' || 
                 tags['leisure'] == 'common' ||
                 tags['leisure'] == 'sports_centre' ||
                 tags['leisure'] == 'stadium' ||
                 tags['leisure'] == 'golf_course')) {
          // Add other grass-like recreational areas
          _parksData['grass']!.add(park);
        }
        else if (tags.containsKey('landuse') && tags['landuse'] == 'forest') {
          _parksData['forest']!.add(park);
        }
        else if (tags.containsKey('landuse') && tags['landuse'] == 'meadow') {
          _parksData['meadow']!.add(park);
        }
        else if (tags.containsKey('natural') && tags['natural'] == 'wood') {
          _parksData['wood']!.add(park);
        }
        else if (tags.containsKey('landuse') && tags['landuse'] == 'garden') {
          _parksData['garden']!.add(park);
        }
        else if (tags.containsKey('leisure') && tags['leisure'] == 'garden') {
          _parksData['garden']!.add(park);
        }
        else if (tags.containsKey('landuse') && tags['landuse'] == 'orchard') {
          _parksData['orchard']!.add(park);
        }
        else if (tags.containsKey('landuse') && tags['landuse'] == 'vineyard') {
          _parksData['vineyard']!.add(park);
        }
        else if (tags.containsKey('natural') && tags['natural'] == 'tree') {
          _parksData['trees']!.add(park);
        }
        else {
          _parksData['other']!.add(park);
        }
      }
    }
    
    // Trees are handled separately at high detail levels
    if (rawData.containsKey('trees')) {
      for (final tree in rawData['trees']!) {
        _parksData['trees']!.add(tree);
      }
    }
    
    // Debug log of categorized counts
    for (final key in _parksData.keys) {
      debugPrint('Parks category $key: ${_parksData[key]!.length} elements');
    }
  }
  
  /// Check if a feature should be handled by the specialized green layer
  bool _isSpecializedGreenFeature(Map<String, dynamic> tags) {
    // Agricultural and farming features
    if (tags['landuse'] == 'farmland' || 
        tags['landuse'] == 'farm' ||
        tags['landuse'] == 'orchard' ||
        tags['landuse'] == 'vineyard' ||
        tags['landuse'] == 'aquaculture') {
      return true;
    }

    // Natural specialized areas
    if (tags['natural'] == 'wetland' ||
        tags['natural'] == 'bog' ||
        tags['natural'] == 'heath' ||
        tags['natural'] == 'tundra') {
      return true;
    }

    // Horticultural areas
    if (tags['landuse'] == 'greenhouse_horticulture' ||
        tags['landuse'] == 'plant_nursery') {
      return true;
    }

    // Special purpose areas
    if (tags['landuse'] == 'cemetery' ||
        tags['landuse'] == 'graveyard') {
      return true;
    }

    // Check for agricultural meadows
    if (tags['meadow'] == 'agricultural') {
      return true;
    }

    // Check for crop fields
    if (tags['crop'] != null) {
      return true;
    }

    return false;
  }
  
  /// Clear all parks data categories
  void _clearAllParksData() {
    for (final key in _parksData.keys) {
      _parksData[key] = [];
    }
  }
  
  /// Check if all parks categories are empty
  bool _areAllParksEmpty() {
    for (final list in _parksData.values) {
      if (list.isNotEmpty) return false;
    }
    return true;
  }
  
  /// Get a key to identify current map bounds, with reduced precision for fewer unnecessary refreshes
  String _getBoundsKey(LatLngBounds bounds, double zoom) {
    final sw = bounds.southWest;
    final ne = bounds.northEast;
    
    // Reduce precision for bounds (3 decimal places ≈ 100m accuracy)
    return 'bounds_${sw.latitude.toStringAsFixed(3)}_${sw.longitude.toStringAsFixed(3)}_${ne.latitude.toStringAsFixed(3)}_${ne.longitude.toStringAsFixed(3)}_${zoom.toStringAsFixed(1)}';
  }
  
  /// Calculate a distance metric between two bounds
  double _calculateBoundsDistance(LatLngBounds bounds1, LatLngBounds bounds2) {
    // Simple Euclidean distance between centers
    final center1 = LatLng(
      (bounds1.northEast.latitude + bounds1.southWest.latitude) / 2,
      (bounds1.northEast.longitude + bounds1.southWest.longitude) / 2
    );
    
    final center2 = LatLng(
      (bounds2.northEast.latitude + bounds2.southWest.latitude) / 2,
      (bounds2.northEast.longitude + bounds2.southWest.longitude) / 2
    );
    
    // Approximate using flat-earth model for small distances
    return math.sqrt(
      math.pow(center1.latitude - center2.latitude, 2) +
      math.pow(center1.longitude - center2.longitude, 2)
    );
  }
  
  /// Calculate a safe request bounds that won't exceed API limits
  LatLngBounds _calculateSafeRequestBounds(LatLngBounds visibleBounds, double detailLevel, double zoomLevel) {
    final double latDelta = visibleBounds.northEast.latitude - visibleBounds.southWest.latitude;
    final double lonDelta = visibleBounds.northEast.longitude - visibleBounds.southWest.longitude;
    
    // Calculate center of visible bounds
    final LatLng center = LatLng(
      visibleBounds.southWest.latitude + latDelta * 0.5,
      visibleBounds.southWest.longitude + lonDelta * 0.5
    );
    
    // Parks can handle larger areas than buildings at lower zoom levels
    double maxAreaSize = 0.1; // Default max area (~ 10km)
    
    if (zoomLevel < 10) {
      // At low zoom levels, use lower detail but larger areas
      maxAreaSize = 0.2 * math.min(1.0, detailLevel);
    } else if (zoomLevel < 14) {
      // Medium zoom levels
      maxAreaSize = 0.12 * math.min(1.0, detailLevel);
    } else {
      // High zoom levels, use smaller areas for more detail
      maxAreaSize = 0.08 * math.min(1.0, detailLevel);
    }
    
    // If current area is too large, focus on a smaller region
    if (latDelta > maxAreaSize || lonDelta > maxAreaSize) {
      final double halfSize = maxAreaSize / 2;
      
      return LatLngBounds(
        LatLng(
          center.latitude - halfSize,
          center.longitude - halfSize
        ),
        LatLng(
          center.latitude + halfSize,
          center.longitude + halfSize
        )
      );
    }
    
    // Otherwise use the original bounds
    return visibleBounds;
  }
  
  /// Get the zoom bucket (1-5) for current zoom level
  int _getZoomBucket(double zoom) {
    if (zoom < 8) return 1;   // World view - minimal detail
    if (zoom < 10) return 2;  // Continental - low detail
    if (zoom < 13) return 3;  // Regional - medium detail
    if (zoom < 16) return 4;  // Local - high detail
    return 5;                 // Fully zoomed - full detail
  }
  
  /// Get appropriate detail level for current zoom bucket
  double _getDetailLevel(int zoomBucket) {
    switch (zoomBucket) {
      case 1: return 0.2; // World - minimal detail
      case 2: return 0.4; // Continental - low detail
      case 3: return 0.6; // Regional - medium detail
      case 4: return 0.8; // Local - high detail
      case 5: return 1.0; // Fully zoomed - full detail
      default: return 0.6;
    }
  }
  
  /// Determine the current season based on date (for northern hemisphere)
  String _determineSeason() {
    final now = DateTime.now();
    final month = now.month;
    
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }
  
  /// Update seasonal coloring (can be called to refresh based on user preference)
  void updateSeason(String season) {
    if (['spring', 'summer', 'autumn', 'winter'].contains(season)) {
      _currentSeason = season;
    }
  }
  
  /// Clean up resources
  void dispose() {
    // Nothing to dispose of at the moment, but here for future use
  }
  
  // Getters for public access
  Map<String, List<Map<String, dynamic>>> get parksData => _parksData;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  bool get hasInitialData => _didInitialFetch;
  int getCurrentZoomBucket() => _currentZoomBucket;
  String get currentSeason => _currentSeason;
} 