import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:ui';
import 'dart:math' as math;

/// Advanced geometry utilities for park features with enhanced polygon processing
class ParkGeometryUtils {
  final math.Random _random = math.Random(42); // Fixed seed for consistent results
  
  /// Simplify a polygon using enhanced <PERSON>-<PERSON> algorithm
  /// Returns simplified polygon with optimized number of points
  List<Offset> simplifyPolygon(List<Offset> points, double epsilon) {
    if (points.length <= 3) return points;
    
    // Find the point with the maximum distance
    int findFarthest(List<Offset> pts, int startIdx, int endIdx) {
      double maxDistance = 0;
      int index = startIdx;
      
      final Offset start = pts[startIdx];
      final Offset end = pts[endIdx];
      
      if (startIdx + 1 == endIdx) return -1;
      
      for (int i = startIdx + 1; i < endIdx; i++) {
        final double distance = _perpendicularDistance(pts[i], start, end);
        if (distance > maxDistance) {
          maxDistance = distance;
          index = i;
        }
      }
      
      if (maxDistance > epsilon) return index;
      return -1;
    }
    
    // Recursive simplification function
    void simplifySection(List<Offset> pts, int startIdx, int endIdx, double eps, List<bool> markers) {
      int index = findFarthest(pts, startIdx, endIdx);
      
      if (index != -1) {
        // If the maximum distance is greater than epsilon, recursively simplify
        markers[index] = true;
        simplifySection(pts, startIdx, index, eps, markers);
        simplifySection(pts, index, endIdx, eps, markers);
      }
    }
    
    // Initialize all points as "to be removed" except first and last
    List<bool> markers = List<bool>.filled(points.length, false);
    markers[0] = true;
    markers[points.length - 1] = true;
    
    // Run simplification
    simplifySection(points, 0, points.length - 1, epsilon, markers);
    
    // Construct the simplified polygon
    List<Offset> simplified = [];
    for (int i = 0; i < markers.length; i++) {
      if (markers[i]) simplified.add(points[i]);
    }
    
    // Return original points if we simplified too aggressively
    if (simplified.length < 3) {
      return points;
    }
    
    // Add extra stabilization by ensuring minimum segment length
    simplified = _ensureMinimumSegmentLength(simplified, 2.0);
    
    return simplified;
  }
  
  /// Ensure minimum segment length to prevent twitching
  List<Offset> _ensureMinimumSegmentLength(List<Offset> points, double minLength) {
    if (points.length <= 3) return points;
    
    List<Offset> result = [points.first];
    
    for (int i = 1; i < points.length; i++) {
      final Offset lastPoint = result.last;
      final Offset currentPoint = points[i];
      
      if ((currentPoint - lastPoint).distance >= minLength) {
        result.add(currentPoint);
      }
    }
    
    // Ensure we keep at least 3 points for a valid polygon
    if (result.length < 3) {
      return points;
    }
    
    return result;
  }
  
  /// Calculate perpendicular distance from a point to a line segment
  double _perpendicularDistance(Offset point, Offset lineStart, Offset lineEnd) {
    // Handle case where line start and end are the same point
    if (lineStart == lineEnd) {
      return (point - lineStart).distance;
    }
    
    // Calculate perpendicular distance
    final double area = ((lineEnd.dx - lineStart.dx) * (lineStart.dy - point.dy) - 
                        (lineStart.dx - point.dx) * (lineEnd.dy - lineStart.dy)).abs();
    final double bottom = (lineEnd - lineStart).distance;
    
    return area / bottom;
  }
  
  /// Apply improved Chaikin's algorithm for polygon smoothing with edge preservation
  List<Offset> smoothPolygon(List<Offset> points, {double tension = 0.25, int iterations = 1}) {
    if (points.length <= 3) return points;
    
    // Check if we have sharp corners that should be preserved
    final List<int> sharpCorners = _findSharpCorners(points);
    
    List<Offset> result = List<Offset>.from(points);
    
    for (int iter = 0; iter < iterations; iter++) {
      final List<Offset> smoothed = [];
      
      // Process each edge of the polygon
      for (int i = 0; i < result.length; i++) {
        final Offset current = result[i];
        final Offset next = result[(i + 1) % result.length];
        
        // Add current point (preserve sharp corners)
        if (sharpCorners.contains(i)) {
          smoothed.add(current);
          continue;
        }
        
        // Calculate interpolated points with dynamic tension
        final Offset p1 = Offset(
          current.dx * (1 - tension) + next.dx * tension,
          current.dy * (1 - tension) + next.dy * tension
        );
        
        final Offset p2 = Offset(
          current.dx * tension + next.dx * (1 - tension),
          current.dy * tension + next.dy * (1 - tension)
        );
        
        // Add the new points only if the segment is long enough
        if ((next - current).distance > 3.0) {
          smoothed.add(p1);
          smoothed.add(p2);
        } else {
          // For very short segments, just add the midpoint to avoid visual artifacts
          smoothed.add(Offset(
            (current.dx + next.dx) / 2,
            (current.dy + next.dy) / 2
          ));
        }
      }
      
      result = smoothed;
    }
    
    return result;
  }
  
  /// Find sharp corners that should be preserved during smoothing
  List<int> _findSharpCorners(List<Offset> points) {
    final List<int> sharpCorners = [];
    final double thresholdAngle = 0.6; // Approximately 35 degrees
    
    for (int i = 0; i < points.length; i++) {
      final Offset prev = points[(i - 1 + points.length) % points.length];
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      
      // Calculate vectors
      final Offset v1 = _normalizeOffset(current - prev);
      final Offset v2 = _normalizeOffset(next - current);
      
      // Calculate dot product to find angle
      final double dotProduct = v1.dx * v2.dx + v1.dy * v2.dy;
      
      // If dot product is below threshold, it's a sharp corner
      if (dotProduct < thresholdAngle) {
        sharpCorners.add(i);
      }
    }
    
    return sharpCorners;
  }
  
  /// Normalize an offset (create a unit vector)
  Offset _normalizeOffset(Offset offset) {
    final double magnitude = offset.distance;
    if (magnitude == 0) return Offset.zero;
    return Offset(offset.dx / magnitude, offset.dy / magnitude);
  }
  
  /// Project a geographic coordinate (lat/lon) to screen coordinates
  Offset projectPoint(double latitude, double longitude, LatLngBounds bounds, Size size) {
    // Calculate the position relative to the bounds
    final double x = (longitude - bounds.west) / 
                    (bounds.east - bounds.west);
    final double y = 1.0 - (latitude - bounds.south) / 
                    (bounds.north - bounds.south);
    
    // Convert to screen coordinates
    return Offset(x * size.width, y * size.height);
  }
  
  /// Calculate the center point of a feature from its points
  Offset? calculateFeatureCenter(List<Offset> points) {
    if (points.isEmpty) return null;
    
    double sumX = 0;
    double sumY = 0;
    
    for (final point in points) {
      sumX += point.dx;
      sumY += point.dy;
    }
    
    return Offset(sumX / points.length, sumY / points.length);
  }
  
  /// Get appropriate epsilon value for Douglas-Peucker simplification based on zoom level
  double getSimplificationEpsilon(double zoomLevel) {
    // Use smaller epsilon (more detail) at higher zoom levels
    if (zoomLevel >= 18) return 0.75;
    if (zoomLevel >= 16) return 1.0;
    if (zoomLevel >= 14) return 1.5;
    if (zoomLevel >= 12) return 2.0;
    return 2.5; // More aggressive simplification at lower zoom levels
  }
  
  /// Apply both simplification and smoothing in one step with zoom-aware parameters
  List<Offset> optimizePolygon(List<Offset> points, double zoomLevel, {
    bool shouldSmooth = true, 
    bool preserveSharpCorners = true
  }) {
    if (points.length < 3) return points;
    
    // First apply simplification to reduce point count
    final double epsilon = getSimplificationEpsilon(zoomLevel);
    final List<Offset> simplified = simplifyPolygon(points, epsilon);
    
    // Skip smoothing for low point count or if not requested
    if (!shouldSmooth || simplified.length < 5 || zoomLevel < 15) {
      return simplified;
    }
    
    // Then apply smoothing for better visual appearance
    final double tension = zoomLevel >= 17 ? 0.2 : 0.25;
    final int iterations = zoomLevel >= 17 ? 2 : 1;
    
    return smoothPolygon(
      simplified, 
      tension: tension, 
      iterations: iterations
    );
  }
  
  /// Sort features by distance from viewer for proper z-ordering
  List<Map<String, dynamic>> sortFeaturesByDistance(List<Map<String, dynamic>> features, Size size) {
    if (features.isEmpty) return features;
    
    // Create a copy of the list to avoid modifying the original
    final List<Map<String, dynamic>> sortedFeatures = List<Map<String, dynamic>>.from(features);
    
    // Sort features by their distance from the bottom of the screen (where the viewer is)
    sortedFeatures.sort((a, b) {
      // Calculate center of each feature
      final List<Offset>? aPoints = a['points'] as List<Offset>?;
      final List<Offset>? bPoints = b['points'] as List<Offset>?;
      
      if (aPoints == null || bPoints == null) return 0;
      
      final Offset? aCenter = calculateFeatureCenter(aPoints);
      final Offset? bCenter = calculateFeatureCenter(bPoints);
      
      if (aCenter == null || bCenter == null) return 0;
      
      // Calculate distance from the bottom of the screen
      final double aDistance = size.height - aCenter.dy;
      final double bDistance = size.height - bCenter.dy;
      
      // Sort from farthest to nearest (painter draws back-to-front)
      return bDistance.compareTo(aDistance);
    });
    
    return sortedFeatures;
  }
  
  /// Check if a point is inside a polygon using raycasting algorithm
  bool isPointInPolygon(List<Offset> vertices, Offset point) {
    if (vertices.length < 3) return false;
    
    int i, j;
    bool c = false;
    
    j = vertices.length - 1;
    for (i = 0; i < vertices.length; i++) {
      if (((vertices[i].dy > point.dy) != (vertices[j].dy > point.dy)) &&
          (point.dx < (vertices[j].dx - vertices[i].dx) * (point.dy - vertices[i].dy) / 
           (vertices[j].dy - vertices[i].dy) + vertices[i].dx)) {
        c = !c;
      }
      j = i;
    }
    
    return c;
  }
  
  /// Calculate the center point of a polygon
  Offset calculatePolygonCenter(List<Offset> points) {
    if (points.isEmpty) return Offset.zero;
    
    double sumX = 0;
    double sumY = 0;
    
    for (final point in points) {
      sumX += point.dx;
      sumY += point.dy;
    }
    
    return Offset(sumX / points.length, sumY / points.length);
  }
  
  /// Calculate bounds of a list of points
  Rect calculateBounds(List<Offset> points) {
    if (points.isEmpty) return Rect.zero;
    
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;
    
    for (final point in points) {
      if (point.dx < minX) minX = point.dx;
      if (point.dy < minY) minY = point.dy;
      if (point.dx > maxX) maxX = point.dx;
      if (point.dy > maxY) maxY = point.dy;
    }
    
    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }
}