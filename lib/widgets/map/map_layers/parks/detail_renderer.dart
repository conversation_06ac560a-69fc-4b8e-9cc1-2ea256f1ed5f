import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'dart:ui';

import './parks_colors.dart';

/// Utility class for rendering small detailed elements in parks
/// This provides specialized details that can be added to various park types
class DetailRenderer {
  final double zoomLevel;
  final String season;
  final bool enhancedDetail;
  final double tiltFactor;
  final ParksColors _colorsHelper = ParksColors();
  final math.Random _random = math.Random(42); // Fixed seed for consistency
  
  DetailRenderer({
    required this.zoomLevel,
    required this.season,
    this.enhancedDetail = false,
    this.tiltFactor = 1.0,
  });
  
  /// Add appropriate detail elements for the given feature type
  void addDetailsForFeatureType(Canvas canvas, Rect bounds, String featureType, Map<String, Color> colors) {
    // Skip details below certain zoom levels
    if (zoomLevel < 16) return;
    if (bounds.width < 10 || bounds.height < 10) return;
    
    // Add appropriate details based on feature type
    switch (featureType) {
      case 'park':
        _addParkDetails(canvas, bounds, colors);
        break;
      case 'forest':
        _addForestDetails(canvas, bounds, colors);
        break;
      case 'garden':
        _addGardenDetails(canvas, bounds, colors);
        break;
      case 'meadow':
        _addMeadowDetails(canvas, bounds, colors);
        break;
      case 'grass':
        _addGrassDetails(canvas, bounds, colors);
        break;
      default:
        // No specific details for unknown feature types
        break;
    }
  }
  
  /// Add park details like benches, paths, etc.
  void _addParkDetails(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    // Only add substantial details at high zoom
    if (zoomLevel < 17) return;
    
    // Add benches at certain zoom levels
    if (zoomLevel >= 18) {
      _addBenches(canvas, bounds, colors);
    }
    
    // Add pathways
    if (zoomLevel >= 17) {
      _addPathways(canvas, bounds, colors);
    }
    
    // Add playground or gazebo in larger parks
    if (bounds.width > 100 && bounds.height > 100 && zoomLevel >= 18) {
      if (_random.nextBool()) {
        _addPlayground(canvas, bounds, colors);
      } else {
        _addGazebo(canvas, bounds, colors);
      }
    }
  }
  
  /// Add forest details like fallen logs, clearings
  void _addForestDetails(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    if (zoomLevel < 17) return;
    
    // Add small clearings
    if (bounds.width > 100 && bounds.height > 100) {
      final int numClearings = math.min(3, (bounds.width * bounds.height / 10000).round());
      
      for (int i = 0; i < numClearings; i++) {
        final double x = bounds.left + bounds.width * 0.2 + _random.nextDouble() * bounds.width * 0.6;
        final double y = bounds.top + bounds.height * 0.2 + _random.nextDouble() * bounds.height * 0.6;
        final double radius = 5.0 + _random.nextDouble() * 10.0;
        
        final Paint clearingPaint = Paint()
          ..color = colors['highlight']!.withOpacity(0.4)
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(x, y), radius, clearingPaint);
      }
    }
    
    // Add fallen logs at very high zoom
    if (zoomLevel >= 18) {
      final int numLogs = (bounds.width * bounds.height / 5000).round();
      final Color logColor = Colors.brown.shade700.withOpacity(0.6);
      
      for (int i = 0; i < numLogs; i++) {
        final double x = bounds.left + _random.nextDouble() * bounds.width;
        final double y = bounds.top + _random.nextDouble() * bounds.height;
        final double length = 5.0 + _random.nextDouble() * 10.0;
        final double width = 1.0 + _random.nextDouble() * 1.5;
        final double angle = _random.nextDouble() * math.pi;
        
        final Paint logPaint = Paint()
          ..color = logColor
          ..style = PaintingStyle.fill;
        
        canvas.save();
        canvas.translate(x, y);
        canvas.rotate(angle);
        canvas.drawRect(Rect.fromLTWH(-length/2, -width/2, length, width), logPaint);
        canvas.restore();
      }
    }
  }
  
  /// Add garden details like flowerbeds, decorative elements
  void _addGardenDetails(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    if (zoomLevel < 17) return;
    
    // Add garden paths
    if (bounds.width > 50 && bounds.height > 50) {
      _addGardenPaths(canvas, bounds, colors);
    }
    
    // Add flower beds at higher zoom
    if (zoomLevel >= 18) {
      _addFlowerBeds(canvas, bounds, colors);
    }
    
    // Add gazebo or fountain in larger gardens
    if (bounds.width > 80 && bounds.height > 80 && zoomLevel >= 18) {
      if (_random.nextBool()) {
        _addGazebo(canvas, bounds, colors);
      } else {
        _addFountain(canvas, bounds, colors);
      }
    }
  }
  
  /// Add meadow details like rocks, wildflower patches
  void _addMeadowDetails(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    if (zoomLevel < 17) return;
    
    // Add rocks
    if (zoomLevel >= 17) {
      final int numRocks = (bounds.width * bounds.height / 3000).round();
      
      for (int i = 0; i < numRocks; i++) {
        final double x = bounds.left + _random.nextDouble() * bounds.width;
        final double y = bounds.top + _random.nextDouble() * bounds.height;
        final double size = 1.0 + _random.nextDouble() * 3.0;
        
        final Paint rockPaint = Paint()
          ..color = Colors.grey.shade700.withOpacity(0.7)
          ..style = PaintingStyle.fill;
        
        // Irregular rock shape
        final Path rockPath = Path();
        rockPath.moveTo(x, y - size);
        
        for (int j = 0; j < 6; j++) {
          final double angle = j * math.pi / 3;
          final double radius = size * (0.8 + _random.nextDouble() * 0.4);
          rockPath.lineTo(
            x + math.cos(angle) * radius,
            y + math.sin(angle) * radius
          );
        }
        
        rockPath.close();
        canvas.drawPath(rockPath, rockPaint);
      }
    }
    
    // Add wildflower patches at high zoom in spring/summer
    if (zoomLevel >= 18 && (season == 'spring' || season == 'summer')) {
      _addWildflowerPatches(canvas, bounds, colors);
    }
  }
  
  /// Add grass details like small objects, mowing patterns
  void _addGrassDetails(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    if (zoomLevel < 17) return;
    
    // Add occasional animal tracks at higher zoom
    if (zoomLevel >= 18 && _random.nextDouble() < 0.3) {
      _addAnimalTracks(canvas, bounds, colors);
    }
    
    // Add occasional random objects
    if (zoomLevel >= 18) {
      final int numObjects = (bounds.width * bounds.height / 10000).round();
      
      for (int i = 0; i < numObjects; i++) {
        final double x = bounds.left + _random.nextDouble() * bounds.width;
        final double y = bounds.top + _random.nextDouble() * bounds.height;
        
        // Random tiny object (ball, frisbee, etc)
        final Color objectColor = [
          Colors.red, 
          Colors.blue, 
          Colors.yellow, 
          Colors.green,
          Colors.orange,
        ][_random.nextInt(5)].withOpacity(0.7);
        
        final Paint objectPaint = Paint()
          ..color = objectColor
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(x, y), 1.0 + _random.nextDouble(), objectPaint);
      }
    }
  }
  
  /// Add benches to park areas
  void _addBenches(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final int numBenches = math.min(5, (bounds.width * bounds.height / 5000).round());
    final Color benchColor = Colors.brown.shade800.withOpacity(0.7);
    
    for (int i = 0; i < numBenches; i++) {
      final double x = bounds.left + bounds.width * 0.1 + _random.nextDouble() * bounds.width * 0.8;
      final double y = bounds.top + bounds.height * 0.1 + _random.nextDouble() * bounds.height * 0.8;
      final double width = 4.0 + _random.nextDouble() * 2.0;
      final double length = 8.0 + _random.nextDouble() * 4.0;
      final double angle = _random.nextDouble() * math.pi;
      
      final Paint benchPaint = Paint()
        ..color = benchColor
        ..style = PaintingStyle.fill;
      
      canvas.save();
      canvas.translate(x, y);
      canvas.rotate(angle);
      canvas.drawRect(Rect.fromLTWH(-length/2, -width/2, length, width), benchPaint);
      canvas.restore();
    }
  }
  
  /// Add pathways to area
  void _addPathways(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final Paint pathPaint = Paint()
      ..color = Colors.grey.shade300.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    // Simple path pattern based on area size
    if (bounds.width > 80 && bounds.height > 80) {
      // Crossing paths
      canvas.drawLine(
        Offset(bounds.left + bounds.width * 0.2, bounds.top + bounds.height * 0.5),
        Offset(bounds.left + bounds.width * 0.8, bounds.top + bounds.height * 0.5),
        pathPaint
      );
      
      canvas.drawLine(
        Offset(bounds.left + bounds.width * 0.5, bounds.top + bounds.height * 0.2),
        Offset(bounds.left + bounds.width * 0.5, bounds.top + bounds.height * 0.8),
        pathPaint
      );
    } else if (bounds.width > 40 || bounds.height > 40) {
      // Single path
      canvas.drawLine(
        Offset(bounds.left + bounds.width * 0.2, bounds.top + bounds.height * 0.2),
        Offset(bounds.left + bounds.width * 0.8, bounds.top + bounds.height * 0.8),
        pathPaint
      );
    }
  }
  
  /// Add garden paths (curved)
  void _addGardenPaths(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final Paint pathPaint = Paint()
      ..color = Colors.grey.shade300.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    
    final double centerX = bounds.left + bounds.width / 2;
    final double centerY = bounds.top + bounds.height / 2;
    
    // Curved path
    final Path path = Path();
    path.moveTo(bounds.left + bounds.width * 0.2, bounds.top + bounds.height * 0.2);
    path.quadraticBezierTo(
      centerX, 
      centerY, 
      bounds.left + bounds.width * 0.8, 
      bounds.top + bounds.height * 0.8
    );
    
    canvas.drawPath(path, pathPaint);
    
    // Secondary path
    if (bounds.width > 60 && bounds.height > 60) {
      final Path path2 = Path();
      path2.moveTo(bounds.left + bounds.width * 0.2, bounds.top + bounds.height * 0.8);
      path2.quadraticBezierTo(
        centerX, 
        centerY, 
        bounds.left + bounds.width * 0.8, 
        bounds.top + bounds.height * 0.2
      );
      
      canvas.drawPath(path2, pathPaint);
    }
  }
  
  /// Add flower beds to gardens
  void _addFlowerBeds(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final int numBeds = math.min(3, (bounds.width * bounds.height / 5000).round());
    
    for (int i = 0; i < numBeds; i++) {
      final double x = bounds.left + bounds.width * 0.2 + _random.nextDouble() * bounds.width * 0.6;
      final double y = bounds.top + bounds.height * 0.2 + _random.nextDouble() * bounds.height * 0.6;
      final double radius = 5.0 + _random.nextDouble() * 10.0;
      
      // Flower bed color based on season
      Color bedColor;
      switch (season) {
        case 'spring':
          bedColor = Colors.purple.withOpacity(0.5);
          break;
        case 'summer':
          bedColor = Colors.red.withOpacity(0.5);
          break;
        case 'autumn':
          bedColor = Colors.orange.withOpacity(0.5);
          break;
        case 'winter':
          bedColor = Colors.brown.withOpacity(0.3);
          break;
        default:
          bedColor = Colors.pink.withOpacity(0.5);
      }
      
      final Paint bedPaint = Paint()
        ..color = bedColor
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(Offset(x, y), radius, bedPaint);
      
      // Add border
      final Paint borderPaint = Paint()
        ..color = Colors.brown.shade600.withOpacity(0.6)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.8;
      
      canvas.drawCircle(Offset(x, y), radius, borderPaint);
      
      // Add flowers at very high zoom
      if (zoomLevel >= 19 && enhancedDetail) {
        _addFlowersToFlowerBed(canvas, Offset(x, y), radius, season);
      }
    }
  }
  
  /// Add flowers to flower bed
  void _addFlowersToFlowerBed(Canvas canvas, Offset center, double radius, String season) {
    final int numFlowers = (radius * radius / 3).round();
    
    // Flower colors based on season
    List<Color> flowerColors;
    switch (season) {
      case 'spring':
        flowerColors = [
          Colors.purple.withOpacity(0.7),
          Colors.pink.withOpacity(0.7),
          Colors.white.withOpacity(0.7),
          Colors.yellow.withOpacity(0.7),
        ];
        break;
      case 'summer':
        flowerColors = [
          Colors.red.withOpacity(0.7),
          Colors.orange.withOpacity(0.7),
          Colors.yellow.withOpacity(0.7),
          Colors.blue.withOpacity(0.7),
          Colors.purple.withOpacity(0.7),
        ];
        break;
      case 'autumn':
        flowerColors = [
          Colors.orange.withOpacity(0.7),
          Colors.amber.withOpacity(0.7),
          Colors.brown.withOpacity(0.5),
          Colors.red.withOpacity(0.6),
        ];
        break;
      case 'winter':
        flowerColors = [
          Colors.white.withOpacity(0.5),
          Colors.grey.withOpacity(0.4),
          Colors.brown.withOpacity(0.3),
        ];
        break;
      default:
        flowerColors = [
          Colors.pink.withOpacity(0.7),
          Colors.red.withOpacity(0.7),
          Colors.purple.withOpacity(0.7),
          Colors.yellow.withOpacity(0.7),
        ];
    }
    
    for (int i = 0; i < numFlowers; i++) {
      final double distance = _random.nextDouble() * radius * 0.9;
      final double angle = _random.nextDouble() * math.pi * 2;
      final double x = center.dx + math.cos(angle) * distance;
      final double y = center.dy + math.sin(angle) * distance;
      
      final Color flowerColor = flowerColors[_random.nextInt(flowerColors.length)];
      final Paint flowerPaint = Paint()
        ..color = flowerColor
        ..style = PaintingStyle.fill;
      
      // Small flower
      canvas.drawCircle(Offset(x, y), 0.8, flowerPaint);
      
      // Add yellow center to flower
      final Paint centerPaint = Paint()
        ..color = Colors.yellow.withOpacity(0.8)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(Offset(x, y), 0.3, centerPaint);
    }
  }
  
  /// Add a playground to park
  void _addPlayground(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final double centerX = bounds.left + bounds.width * (0.3 + _random.nextDouble() * 0.4);
    final double centerY = bounds.top + bounds.height * (0.3 + _random.nextDouble() * 0.4);
    final double size = math.min(bounds.width, bounds.height) * 0.15;
    
    // Playground area
    final Paint areaPaint = Paint()
      ..color = Colors.brown.shade300.withOpacity(0.4)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(centerX, centerY), size, areaPaint);
    
    // Add playground equipment
    final Paint equipmentPaint = Paint()
      ..color = Colors.red.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    
    // Simple swingset representation
    canvas.drawLine(
      Offset(centerX - size * 0.5, centerY - size * 0.3),
      Offset(centerX + size * 0.5, centerY - size * 0.3),
      equipmentPaint
    );
    
    // Swing supports
    canvas.drawLine(
      Offset(centerX - size * 0.3, centerY - size * 0.3),
      Offset(centerX - size * 0.3, centerY + size * 0.3),
      equipmentPaint
    );
    
    canvas.drawLine(
      Offset(centerX + size * 0.3, centerY - size * 0.3),
      Offset(centerX + size * 0.3, centerY + size * 0.3),
      equipmentPaint
    );
  }
  
  /// Add a gazebo to park or garden
  void _addGazebo(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final double centerX = bounds.left + bounds.width * (0.3 + _random.nextDouble() * 0.4);
    final double centerY = bounds.top + bounds.height * (0.3 + _random.nextDouble() * 0.4);
    final double size = math.min(bounds.width, bounds.height) * 0.1;
    
    // Base
    final Paint basePaint = Paint()
      ..color = Colors.grey.shade400.withOpacity(0.6)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(centerX, centerY), size, basePaint);
    
    // Roof
    final Paint roofPaint = Paint()
      ..color = Colors.brown.shade800.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    final Path roofPath = Path();
    roofPath.moveTo(centerX, centerY - size * 1.5);
    
    for (int i = 0; i < 6; i++) {
      final double angle = i * math.pi / 3;
      roofPath.lineTo(
        centerX + math.cos(angle) * size * 1.2,
        centerY + math.sin(angle) * size * 1.2
      );
    }
    
    roofPath.close();
    canvas.drawPath(roofPath, roofPaint);
    
    // Pillars
    final Paint pillarPaint = Paint()
      ..color = Colors.grey.shade600.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    for (int i = 0; i < 6; i++) {
      final double angle = i * math.pi / 3;
      canvas.drawLine(
        Offset(
          centerX + math.cos(angle) * size,
          centerY + math.sin(angle) * size
        ),
        Offset(
          centerX + math.cos(angle) * size,
          centerY + math.sin(angle) * size - size * 1.2
        ),
        pillarPaint
      );
    }
  }
  
  /// Add a fountain to park or garden
  void _addFountain(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final double centerX = bounds.left + bounds.width * (0.3 + _random.nextDouble() * 0.4);
    final double centerY = bounds.top + bounds.height * (0.3 + _random.nextDouble() * 0.4);
    final double size = math.min(bounds.width, bounds.height) * 0.1;
    
    // Outer basin
    final Paint basinPaint = Paint()
      ..color = Colors.grey.shade400.withOpacity(0.6)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(centerX, centerY), size, basinPaint);
    
    // Water
    final Paint waterPaint = Paint()
      ..color = Colors.blue.shade300.withOpacity(0.5)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(centerX, centerY), size * 0.8, waterPaint);
    
    // Center piece
    final Paint centerPaint = Paint()
      ..color = Colors.grey.shade600.withOpacity(0.7)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(centerX, centerY), size * 0.3, centerPaint);
    
    // Water spray at higher zoom levels
    if (zoomLevel >= 19 && enhancedDetail) {
      final Paint sprayPaint = Paint()
        ..color = Colors.white.withOpacity(0.6)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.5;
      
      for (int i = 0; i < 8; i++) {
        final double angle = i * math.pi / 4;
        final double length = size * 0.4 + _random.nextDouble() * size * 0.3;
        
        canvas.drawLine(
          Offset(centerX, centerY),
          Offset(
            centerX + math.cos(angle) * length,
            centerY + math.sin(angle) * length
          ),
          sprayPaint
        );
      }
    }
  }
  
  /// Add wildflower patches to meadow
  void _addWildflowerPatches(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final int numPatches = math.min(5, (bounds.width * bounds.height / 5000).round());
    
    // Flower colors based on season
    List<Color> flowerColors;
    switch (season) {
      case 'spring':
        flowerColors = [
          Colors.purple.withOpacity(0.6),
          Colors.pink.withOpacity(0.6),
          Colors.white.withOpacity(0.6),
          Colors.yellow.withOpacity(0.6),
        ];
        break;
      case 'summer':
        flowerColors = [
          Colors.red.withOpacity(0.6),
          Colors.orange.withOpacity(0.6),
          Colors.yellow.withOpacity(0.6),
          Colors.blue.withOpacity(0.6),
          Colors.purple.withOpacity(0.6),
        ];
        break;
      default:
        flowerColors = [
          Colors.purple.withOpacity(0.5),
          Colors.yellow.withOpacity(0.5),
          Colors.white.withOpacity(0.5),
        ];
    }
    
    for (int i = 0; i < numPatches; i++) {
      final double x = bounds.left + _random.nextDouble() * bounds.width;
      final double y = bounds.top + _random.nextDouble() * bounds.height;
      final double radius = 3.0 + _random.nextDouble() * 8.0;
      final Color patchColor = flowerColors[_random.nextInt(flowerColors.length)];
      
      final Paint patchPaint = Paint()
        ..color = patchColor
        ..style = PaintingStyle.fill;
      
      // Irregular patch shape
      final Path patchPath = Path();
      patchPath.moveTo(x, y - radius);
      
      for (int j = 0; j < 8; j++) {
        final double angle = j * math.pi / 4;
        final double patchRadius = radius * (0.7 + _random.nextDouble() * 0.6);
        patchPath.lineTo(
          x + math.cos(angle) * patchRadius,
          y + math.sin(angle) * patchRadius
        );
      }
      
      patchPath.close();
      canvas.drawPath(patchPath, patchPaint);
    }
  }
  
  /// Add animal tracks to grass area
  void _addAnimalTracks(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final double startX = bounds.left + _random.nextDouble() * bounds.width * 0.3;
    final double startY = bounds.top + _random.nextDouble() * bounds.height * 0.3;
    
    final Paint trackPaint = Paint()
      ..color = Colors.brown.shade700.withOpacity(0.3)
      ..style = PaintingStyle.fill;
    
    // Create meandering path
    double currentX = startX;
    double currentY = startY;
    double currentAngle = _random.nextDouble() * math.pi * 2;
    
    final int numSteps = 10 + _random.nextInt(10);
    final double stepSize = 5.0 + _random.nextDouble() * 5.0;
    
    for (int i = 0; i < numSteps; i++) {
      // Slightly adjust direction
      currentAngle += (_random.nextDouble() - 0.5) * math.pi / 4;
      
      // Move forward
      currentX += math.cos(currentAngle) * stepSize;
      currentY += math.sin(currentAngle) * stepSize;
      
      // Stay within bounds
      if (currentX < bounds.left || currentX > bounds.right || 
          currentY < bounds.top || currentY > bounds.bottom) {
        break;
      }
      
      // Draw footprint
      _drawFootprint(canvas, Offset(currentX, currentY), trackPaint, currentAngle);
    }
  }
  
  /// Draw a single animal footprint
  void _drawFootprint(Canvas canvas, Offset position, Paint paint, double angle) {
    canvas.save();
    canvas.translate(position.dx, position.dy);
    canvas.rotate(angle);
    
    // Small animal paw print
    canvas.drawCircle(Offset(0, 0), 0.8, paint);
    canvas.drawCircle(Offset(-0.8, -0.8), 0.6, paint);
    canvas.drawCircle(Offset(0.8, -0.8), 0.6, paint);
    canvas.drawCircle(Offset(-0.8, 0.8), 0.6, paint);
    canvas.drawCircle(Offset(0.8, 0.8), 0.6, paint);
    
    canvas.restore();
  }
} 