import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

import './renderers/forest_renderer.dart';
import './renderers/grass_renderer.dart';
import './renderers/meadow_renderer.dart';
import './renderers/garden_renderer.dart';
import './renderers/tree_renderer.dart';
import './parks_colors.dart';
import './parks_geometry_utils.dart' hide LatLng, LatLngBounds;
import './texture_generator.dart';
import './shadow_renderer.dart';
import './detail_renderer.dart';

// Extension to add normalize method to Offset
extension OffsetExtensions on Offset {
  Offset normalize() {
    final double magnitude = distance;
    if (magnitude == 0) return Offset.zero;
    return Offset(dx / magnitude, dy / magnitude);
  }
}

/// Enhanced custom painter to render OpenStreetMap parks and green spaces with sophisticated 2.5D visual effects
class OSMParksPainter extends CustomPainter {
  final Map<String, List<Map<String, dynamic>>> parksData;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final String season;
  final bool enhancedDetail;
  final MapCamera? mapCamera;
  final double tiltFactor;
  
  // Helper utilities
  final ParkGeometryUtils _geometryUtils = ParkGeometryUtils();
  final ParksColors _colorsHelper = ParksColors();
  
  // Specialized renderers
  late final ForestRenderer _forestRenderer;
  late final GrassRenderer _grassRenderer;
  late final MeadowRenderer _meadowRenderer;
  late final GardenRenderer _gardenRenderer;
  late final TreeRenderer _treeRenderer;
  late final DetailRenderer _detailRenderer;
  late final ShadowRenderer _shadowRenderer;
  late final TextureGenerator _textureGenerator;
  
  final math.Random _random = math.Random(42); // Fixed seed for consistent rendering
  
  OSMParksPainter({
    required this.parksData,
    required this.zoomLevel,
    required this.visibleBounds,
    required this.theme,
    required this.season,
    this.enhancedDetail = true,
    this.mapCamera,
    this.tiltFactor = 1.0,
  }) {
    // Initialize all specialized renderers with tilt factor
    _forestRenderer = ForestRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      season: season,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _grassRenderer = GrassRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      season: season,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _meadowRenderer = MeadowRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      season: season,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _gardenRenderer = GardenRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      season: season,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _treeRenderer = TreeRenderer(
      zoomLevel: zoomLevel,
      theme: theme,
      season: season,
      enhancedDetail: enhancedDetail,
      tiltFactor: tiltFactor,
    );
    
    _detailRenderer = DetailRenderer(
      zoomLevel: zoomLevel,
      season: season,
      enhancedDetail: enhancedDetail,
    );
    
    _shadowRenderer = ShadowRenderer(
      zoomLevel: zoomLevel,
      tiltFactor: tiltFactor,
    );
    
    _textureGenerator = TextureGenerator(
      zoomLevel: zoomLevel,
      season: season,
    );
  }

  @override
  void paint(Canvas canvas, Size size) {
    // Don't render anything if there's no data to display
    if (parksData.isEmpty) return;
    if (parksData.values.every((list) => list.isEmpty)) return;
    
    // Sort features by distance from viewer for proper z-ordering
    final sortedFeatures = _sortFeaturesByDistance(size);
    
    // Process data by type in order to layer properly for 3D appearance
    final renderOrder = ['forest', 'wood', 'meadow', 'grass', 'vineyard', 'orchard', 'park', 'garden', 'other'];
    
    // First pass: Draw larger areas first (back to front ordering)
    for (final type in renderOrder) {
      final items = sortedFeatures[type] ?? [];
      for (final item in items) {
        _renderParkFeature(canvas, size, item, type);
      }
    }
    
    // Second pass: Draw individual trees at high zoom levels (on top)
    if (zoomLevel >= 17) {
      _renderIndividualTrees(canvas, size);
    }
  }
  
  /// Sort features by distance from viewer for proper z-ordering
  Map<String, List<Map<String, dynamic>>> _sortFeaturesByDistance(Size size) {
    final Map<String, List<Map<String, dynamic>>> sortedFeatures = {};
    
    for (final entry in parksData.entries) {
      final type = entry.key;
      if (type == 'trees') continue; // Trees are handled separately
      
      final features = List<Map<String, dynamic>>.from(entry.value);
      
      // Sort features by their distance from the bottom of the screen (where the viewer is)
      features.sort((a, b) {
        // Calculate center of each feature
        final aCenter = _calculateFeatureCenter(a['points'] as List?);
        final bCenter = _calculateFeatureCenter(b['points'] as List?);
        
        if (aCenter == null || bCenter == null) return 0;
        
        // Calculate distance from the bottom of the screen
        final aDistance = size.height - _projectPoint(aCenter, size).dy;
        final bDistance = size.height - _projectPoint(bCenter, size).dy;
        
        // Sort from farthest to nearest (painter draws back-to-front)
        return bDistance.compareTo(aDistance);
      });
      
      sortedFeatures[type] = features;
    }
    
    return sortedFeatures;
  }
  
  /// Calculate the center point of a feature from its points
  LatLng? _calculateFeatureCenter(List? points) {
    if (points == null || points.isEmpty) return null;
    
    double sumLat = 0;
    double sumLng = 0;
    int validPoints = 0;
    
    for (final point in points) {
      if (point is LatLng) {
        sumLat += point.latitude;
        sumLng += point.longitude;
        validPoints++;
      }
    }
    
    if (validPoints == 0) return null;
    return LatLng(sumLat / validPoints, sumLng / validPoints);
  }
  
  /// Render individual trees (point features) with enhanced 3D effect
  void _renderIndividualTrees(Canvas canvas, Size size) {
    // SIMPLIFIED: Skip individual tree rendering for better performance
    // This was causing significant performance issues during animations
    return;
  }
  
  /// Render a specific park feature with the appropriate specialized renderer
  void _renderParkFeature(Canvas canvas, Size size, Map<String, dynamic> feature, String featureType) {
    // Extract geometry from feature
    final List<LatLng>? points = feature['points'] as List<LatLng>?;
    final Map<String, dynamic>? tags = feature['tags'] as Map<String, dynamic>?;
    
    if (points == null || points.isEmpty) return;
    
    // Convert to screen coordinates
    final List<Offset> screenPoints = _convertToScreenPoints(points, size);
    
    // Simplify polygon for better performance with advanced Douglas-Peucker algorithm
    final double simplificationTolerance = _getSimplificationTolerance();
    final List<Offset> simplifiedPoints = _geometryUtils.simplifyPolygon(screenPoints, simplificationTolerance);
    
    // Apply smoothing if needed for curved shapes
    List<Offset> finalPoints = simplifiedPoints;
    if (zoomLevel >= 15 && _shouldSmoothFeature(featureType) && simplifiedPoints.length >= 5) {
      // Apply enhanced Chaikin's algorithm for smoother curves
      final double smoothingTension = 0.25;
      finalPoints = _geometryUtils.smoothPolygon(simplifiedPoints, tension: smoothingTension);
    }
    
    // Get color palette for the feature type
    final Map<String, Color> colors = _getColorPalette(featureType);
    
    // Use the appropriate renderer based on feature type
    switch (featureType) {
      case 'forest':
      case 'wood':
        _forestRenderer.renderForestArea(
          canvas, 
          size, 
          feature, 
          finalPoints, 
          isWoodland: featureType == 'wood'
        );
        break;
        
      case 'park':
      case 'grass':
        _grassRenderer.renderGrassArea(
          canvas, 
          size, 
          feature, 
          finalPoints, 
          subtype: featureType
        );
        break;
        
      case 'meadow':
      case 'grassland':
        _meadowRenderer.renderMeadowArea(
          canvas, 
          size, 
          feature, 
          finalPoints, 
          subtype: featureType
        );
        break;
        
      case 'garden':
        _grassRenderer.renderGrassArea(
          canvas, 
          size, 
          feature, 
          finalPoints, 
          subtype: 'garden'
        );
        break;
        
      case 'orchard':
      case 'vineyard':
        _gardenRenderer.renderGardenArea(
          canvas, 
          size, 
          feature, 
          finalPoints, 
          subtype: featureType
        );
        break;
        
      default:
        // Default to grass renderer for unknown types
        _grassRenderer.renderGrassArea(
          canvas, 
          size, 
          feature, 
          finalPoints
        );
        break;
    }
  }
  
  /// Determine if a feature should have polygon smoothing applied
  bool _shouldSmoothFeature(String featureType) {
    // Don't smooth features that should have straight edges
    switch (featureType) {
      case 'vineyard': // Vineyards typically have straight rows
      case 'orchard': // Orchards often have grid patterns
        return false;
      default:
        return true;
    }
  }
  
  /// Get appropriate polygon simplification tolerance based on zoom level
  double _getSimplificationTolerance() {
    // Use smaller tolerance (more detail) at higher zoom levels
    if (zoomLevel >= 18) return 0.75;
    if (zoomLevel >= 16) return 1.0;
    if (zoomLevel >= 14) return 1.5;
    if (zoomLevel >= 12) return 2.0;
    return 2.5; // More aggressive simplification at lower zoom levels
  }
  
  /// Get appropriate color palette for the feature type
  Map<String, Color> _getColorPalette(String featureType) {
    final Map<String, Map<String, Color>> themePalette = _colorsHelper.getThemeColors(theme, season);
    return themePalette[featureType] ?? themePalette['other']!;
  }
  
  /// Convert geographic points to screen coordinates
  List<Offset> _convertToScreenPoints(List<dynamic> points, Size size) {
    return points.map<Offset>((point) {
      final LatLng latLng = point as LatLng;
      return _projectPoint(latLng, size);
    }).toList();
  }
  
  /// Project a geographic point to the screen
  Offset _projectPoint(LatLng point, Size size) {
    // Use MapCamera for more accurate projection during map movement when available
    if (mapCamera != null) {
      final screenPoint = mapCamera!.latLngToScreenPoint(point);
      if (screenPoint != null) {
        return Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
      }
    }
    
    // Fall back to simple linear projection if no camera or camera projection fails
    final double percentX = (point.longitude - visibleBounds.west) / 
                          (visibleBounds.east - visibleBounds.west);
    final double percentY = (point.latitude - visibleBounds.north) / 
                          (visibleBounds.south - visibleBounds.north);
    
    return Offset(
      size.width * percentX,
      size.height * percentY
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    if (oldDelegate is OSMParksPainter) {
      return oldDelegate.parksData != parksData ||
             oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.visibleBounds != visibleBounds ||
           oldDelegate.theme != theme ||
           oldDelegate.season != season ||
           oldDelegate.enhancedDetail != enhancedDetail ||
             oldDelegate.tiltFactor != tiltFactor ||
             oldDelegate.mapCamera != mapCamera;
    }
    return true;
  }
} 