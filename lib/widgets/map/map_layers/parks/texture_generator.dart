import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

import './parks_colors.dart';

/// Utility class for generating realistic vegetation textures
class TextureGenerator {
  final double zoomLevel;
  final String season;
  final ParksColors _colorsHelper = ParksColors();
  final math.Random _random = math.Random(42); // Fixed seed for consistent textures

  TextureGenerator({
    required this.zoomLevel,
    required this.season,
  });

  /// Helper to create a Paint with common settings
  Paint _basePaint(Color color, {PaintingStyle style = PaintingStyle.fill, double strokeWidth = 1.0, double blurSigma = 0.0}) {
    final paint = Paint()
      ..color = color
      ..style = style
      ..strokeWidth = strokeWidth
      ..isAntiAlias = true;
    if (blurSigma > 0.0) {
      paint.maskFilter = MaskFilter.blur(BlurStyle.normal, blurSigma);
    }
    return paint;
  }

  double _randomSmallOffset() => (_random.nextDouble() - 0.5) * 1.5;

  /// Adjust density based on zoom
  double _getTextureDensity(double baseDensity) {
    if (zoomLevel >= 19) return baseDensity * 1.2;
    if (zoomLevel >= 18) return baseDensity * 1.0;
    if (zoomLevel >= 17) return baseDensity * 0.7;
    if (zoomLevel >= 16) return baseDensity * 0.5;
    return baseDensity * 0.2;
  }

  /// --- FOREST CANOPY TEXTURE ---
  void generateForestCanopyTexture(Canvas canvas, Rect bounds, Map<String, Color> colors, {bool isConiferous = false, double density = 1.0}) {
    if (zoomLevel < 16) return;
    final adjustedDensity = _getTextureDensity(density);
    final lightColor = colors['highlight']!;
    final darkColor = _colorsHelper.adjustBrightness(colors['base']!, -0.1);
    final deepShadowColor = _colorsHelper.adjustBrightness(colors['base']!, -0.25);
    final numCircles = (bounds.width * bounds.height * adjustedDensity / 90).round();

    // Add a subtle base texture for depth
    if (zoomLevel >= 17) {
      // Background texture
      for (int i = 0; i < numCircles / 2; i++) {
        final x = bounds.left + _random.nextDouble() * bounds.width;
        final y = bounds.top + _random.nextDouble() * bounds.height;
        final radius = 2.0 + _random.nextDouble() * 3.0;
        final color = deepShadowColor.withOpacity(0.15);
        canvas.drawCircle(Offset(x, y), radius, _basePaint(color, blurSigma: 1.2));
      }
    }

    // Main canopy texture
    for (int i = 0; i < numCircles; i++) {
      final x = bounds.left + _random.nextDouble() * bounds.width;
      final y = bounds.top + _random.nextDouble() * bounds.height;
      final baseSize = isConiferous ? 0.8 : 1.0;
      final variation = isConiferous ? 1.5 : 2.0;
      final radius = baseSize + _random.nextDouble() * variation;
      final color = (i.isEven ? lightColor : darkColor).withOpacity(0.28);
      
      // Add depth through layering - shadow layer first
      if (zoomLevel >= 17 && _random.nextDouble() < 0.4) {
        // Shadow under foliage for depth
        canvas.drawCircle(
          Offset(x + 0.5, y + 0.5), 
          radius * 1.2, 
          _basePaint(deepShadowColor.withOpacity(0.2), blurSigma: 1.5)
        );
      }
      
      canvas.drawCircle(Offset(x, y), radius, _basePaint(color, blurSigma: 0.8));
    }

    if (zoomLevel >= 17) {
      // Enhanced foliage highlights for 3D effect
      final numHighlights = (numCircles * 0.15).round();
      for (int i = 0; i < numHighlights; i++) {
        final x = bounds.left + _random.nextDouble() * bounds.width;
        final y = bounds.top + _random.nextDouble() * bounds.height;
        
        // Simulate sunlight hitting the top of trees
        // More concentrated highlights to simulate elevation
        final highlightColor = _colorsHelper.adjustBrightness(lightColor, 0.1);
        canvas.drawCircle(
          Offset(x, y), 
          0.5 + _random.nextDouble() * 1.5, 
          _basePaint(highlightColor.withOpacity(0.5), blurSigma: 1.2)
        );
      }
      
      // Add subtle bark texture for trees at high zoom levels
      if (zoomLevel >= 18 && isConiferous) {
        final numTrunks = (numCircles * 0.05).round();
        final trunkColor = Colors.brown.shade700.withOpacity(0.3);
        
        for (int i = 0; i < numTrunks; i++) {
          final x = bounds.left + _random.nextDouble() * bounds.width;
          final y = bounds.top + _random.nextDouble() * bounds.height;
          final trunkWidth = 0.8;
          final trunkHeight = 1.5 + _random.nextDouble() * 1.0;
          
          canvas.drawRect(
            Rect.fromCenter(center: Offset(x, y), width: trunkWidth, height: trunkHeight),
            _basePaint(trunkColor)
          );
        }
      }
    }
  }

  /// --- GRASS TEXTURES ---
  void generateGrassTexture(Canvas canvas, Rect bounds, Map<String, Color> colors, {String textureType = 'standard', double density = 1.0}) {
    if (zoomLevel < 16) return;
    
    double modifier = 1.0;
    if (textureType == 'meadow') {
      modifier = 1.5;
    } else if (textureType == 'park') {
      modifier = 0.8;
    }
    
    final adjustedDensity = _getTextureDensity(density) * modifier;
    final lightColor = colors['highlight']!;
    final baseColor = colors['base']!;

    switch (textureType) {
      case 'meadow':
        _generateMeadowTexture(canvas, bounds, lightColor, baseColor, adjustedDensity);
        break;
      case 'park':
        _generateParkTexture(canvas, bounds, lightColor, baseColor, adjustedDensity);
        break;
      default:
        _generateStandardGrassTexture(canvas, bounds, lightColor, baseColor, adjustedDensity);
    }
  }

  void _generateStandardGrassTexture(Canvas canvas, Rect bounds, Color lightColor, Color baseColor, double density) {
    final numDots = (bounds.width * bounds.height * density / 100).round();
    for (int i = 0; i < numDots; i++) {
      final x = bounds.left + _random.nextDouble() * bounds.width + _randomSmallOffset();
      final y = bounds.top + _random.nextDouble() * bounds.height + _randomSmallOffset();
      final color = (_random.nextBool() ? lightColor : baseColor).withOpacity(0.25);
      canvas.drawCircle(Offset(x, y), 0.5 + _random.nextDouble() * 0.6, _basePaint(color, blurSigma: 0.5));
    }
  }

  void _generateParkTexture(Canvas canvas, Rect bounds, Color lightColor, Color baseColor, double density) {
    final shadowColor = _colorsHelper.adjustBrightness(baseColor, -0.2);
    final highlightColor = _colorsHelper.adjustBrightness(lightColor, 0.1);
    
    // Enhanced mowing patterns with 3D effects
    final isStriped = _random.nextBool();
    if (isStriped && zoomLevel >= 17) {
      final stripeWidth = 8.0 + _random.nextDouble() * 4.0;
      
      // Add subtle terrain undulation
      if (zoomLevel >= 18) {
        final undulationCount = (bounds.width / 20).round();
        for (int i = 0; i < undulationCount; i++) {
          final x = bounds.left + _random.nextDouble() * bounds.width;
          final y = bounds.top + _random.nextDouble() * bounds.height;
          final radius = 5.0 + _random.nextDouble() * 10.0;
          
          // Create very subtle elevation changes
          final elevationPaint = _basePaint(
            (_random.nextBool() ? highlightColor : shadowColor).withOpacity(0.07),
            blurSigma: 3.0
          );
          canvas.drawCircle(Offset(x, y), radius, elevationPaint);
        }
      }
      
      // Enhanced stripes with shadow and highlights for 3D effect
      for (double x = bounds.left; x < bounds.right; x += stripeWidth * 2) {
        // Shadow strip for depth
        if (zoomLevel >= 17) {
          final shadowStripe = Rect.fromLTWH(x + 0.5, bounds.top, stripeWidth, bounds.height);
          canvas.drawRect(shadowStripe, _basePaint(shadowColor.withOpacity(0.03), blurSigma: 1.0));
        }
        
        // Main stripe
        final stripe = Rect.fromLTWH(x, bounds.top, stripeWidth, bounds.height);
        canvas.drawRect(stripe, _basePaint(lightColor.withOpacity(0.1)));
        
        // Highlight edge for 3D effect
        if (zoomLevel >= 18) {
          final highlightEdge = Rect.fromLTWH(x - 0.5, bounds.top, 1.0, bounds.height);
          canvas.drawRect(highlightEdge, _basePaint(highlightColor.withOpacity(0.1), blurSigma: 0.5));
        }
      }
    } else if (zoomLevel >= 17) {
      // Enhanced circular mowing pattern with 3D effects
      final centerX = bounds.left + bounds.width / 2;
      final centerY = bounds.top + bounds.height / 2;
      
      // Base texture for terrain variation
      if (zoomLevel >= 18) {
        final terrainVariations = (bounds.width * bounds.height / 200).round();
        for (int i = 0; i < terrainVariations; i++) {
          final x = bounds.left + _random.nextDouble() * bounds.width;
          final y = bounds.top + _random.nextDouble() * bounds.height;
          final radius = 3.0 + _random.nextDouble() * 8.0;
          
          final variationPaint = _basePaint(
            (_random.nextBool() ? highlightColor : shadowColor).withOpacity(0.05),
            blurSigma: 2.0
          );
          canvas.drawCircle(Offset(x, y), radius, variationPaint);
        }
      }
      
      // Enhanced circular pattern with better 3D visual
      for (double radius = 10; radius < bounds.width / 2; radius += 10) {
        // Shadow ring for depth
        if (zoomLevel >= 18) {
          canvas.drawCircle(
            Offset(centerX + 0.5, centerY + 0.5), 
            radius + 0.5, 
            _basePaint(shadowColor.withOpacity(0.05), style: PaintingStyle.stroke, strokeWidth: 3.0, blurSigma: 1.0)
          );
        }
        
        // Main ring
        canvas.drawCircle(
          Offset(centerX, centerY), 
          radius, 
          _basePaint(lightColor.withOpacity(0.15), style: PaintingStyle.stroke, strokeWidth: 3.0)
        );
        
        // Highlight ring for 3D effect
        if (zoomLevel >= 18) {
          canvas.drawCircle(
            Offset(centerX - 0.5, centerY - 0.5), 
            radius - 0.5, 
            _basePaint(highlightColor.withOpacity(0.08), style: PaintingStyle.stroke, strokeWidth: 1.0, blurSigma: 0.5)
          );
        }
      }
    }

    if (zoomLevel >= 18) {
      // Add small grass details with enhanced 3D effects
      final numDots = (bounds.width * bounds.height * density / 180).round();
      for (int i = 0; i < numDots; i++) {
        final x = bounds.left + _random.nextDouble() * bounds.width;
        final y = bounds.top + _random.nextDouble() * bounds.height;
        final size = 0.4 + _random.nextDouble() * 0.3;
        final color = (_random.nextBool() ? lightColor : baseColor).withOpacity(0.2);
        
        // Shadow for grass details
        if (_random.nextDouble() < 0.3) {
          canvas.drawCircle(
            Offset(x + 0.2, y + 0.2), 
            size * 0.8, 
            _basePaint(shadowColor.withOpacity(0.1), blurSigma: 0.8)
          );
        }
        
        // Main detail
        canvas.drawCircle(Offset(x, y), size, _basePaint(color, blurSigma: 0.5));
        
        // Highlight for some details
        if (_random.nextDouble() < 0.2) {
          canvas.drawCircle(
            Offset(x - 0.1, y - 0.1), 
            size * 0.3, 
            _basePaint(highlightColor.withOpacity(0.2), blurSigma: 0.3)
          );
        }
      }
    }
  }

  /// --- MEADOW TEXTURES ---
  void _generateMeadowTexture(Canvas canvas, Rect bounds, Color lightColor, Color baseColor, double density) {
    switch (season) {
      case 'spring':
        _generateSeasonalMeadow(canvas, bounds, lightColor, baseColor, density, seasonModifier: 0.9);
        break;
      case 'summer':
        _generateSeasonalMeadow(canvas, bounds, lightColor, baseColor, density, seasonModifier: 0.7);
        break;
      case 'autumn':
        _generateSeasonalMeadow(canvas, bounds, lightColor, baseColor, density, seasonModifier: 1.0, autumnColors: true);
        break;
      case 'winter':
        _generateWinterMeadowTexture(canvas, bounds, lightColor, baseColor, density);
        break;
      default:
        _generateSeasonalMeadow(canvas, bounds, lightColor, baseColor, density);
    }
  }

  void _generateSeasonalMeadow(Canvas canvas, Rect bounds, Color lightColor, Color baseColor, double density, {double seasonModifier = 1.0, bool autumnColors = false}) {
    final int numElements = (bounds.width * bounds.height * density / (80 * seasonModifier)).round();
    
    // Enhanced color palette with more depth variation
    final shadowColor = _colorsHelper.adjustBrightness(baseColor, -0.15);
    final highlightColor = _colorsHelper.adjustBrightness(lightColor, 0.1);
    
    final List<Color> colors = [
      lightColor,
      baseColor,
      highlightColor.withOpacity(0.6),
      Colors.yellow.withOpacity(0.5),
      Colors.purple.withOpacity(0.4),
      if (!autumnColors) Colors.pink.withOpacity(0.4),
      if (!autumnColors) Colors.white.withOpacity(0.5),
      if (autumnColors) Colors.brown.withOpacity(0.4),
      if (autumnColors) Colors.orange.withOpacity(0.4),
    ];

    // Add ground texture for depth - subtle variations
    if (zoomLevel >= 17) {
      final int groundTextures = (numElements * 0.3).round();
      for (int i = 0; i < groundTextures; i++) {
        final x = bounds.left + _random.nextDouble() * bounds.width;
        final y = bounds.top + _random.nextDouble() * bounds.height;
        final radius = 1.0 + _random.nextDouble() * 2.0;
        
        // Very subtle ground variation
        final groundColor = _random.nextBool()
            ? shadowColor.withOpacity(0.08)
            : baseColor.withOpacity(0.06);
            
        canvas.drawCircle(
          Offset(x, y), 
          radius, 
          _basePaint(groundColor, blurSigma: 1.5)
        );
      }
    }

    // Main vegetation elements with enhanced 3D
    for (int i = 0; i < numElements; i++) {
      final x = bounds.left + _random.nextDouble() * bounds.width + _randomSmallOffset();
      final y = bounds.top + _random.nextDouble() * bounds.height + _randomSmallOffset();
      final isBlade = _random.nextDouble() < 0.7;

      if (isBlade) {
        // Enhanced grass blades with shadows
        final angle = _random.nextDouble() * math.pi;
        final length = 1.0 + _random.nextDouble() * 2.0;
        final color = (_random.nextBool() ? lightColor : baseColor).withOpacity(0.3);
        
        // Draw shadow first for blades
        if (zoomLevel >= 18 && _random.nextDouble() < 0.3) {
          final shadowPaint = _basePaint(
            shadowColor.withOpacity(0.15),
            style: PaintingStyle.stroke, 
            strokeWidth: 0.5,
            blurSigma: 0.8
          );
          
          canvas.drawLine(
            Offset(x + 0.3, y + 0.3),
            Offset(x + 0.3 + math.cos(angle) * length, y + 0.3 + math.sin(angle) * length),
            shadowPaint,
          );
        }
        
        // Draw the blade with enhanced style
        final bladePaint = _basePaint(color, style: PaintingStyle.stroke, strokeWidth: 0.5);
        canvas.drawLine(
          Offset(x, y),
          Offset(x + math.cos(angle) * length, y + math.sin(angle) * length),
          bladePaint,
        );
        
        // Add highlight to tips of some blades for sun reflection
        if (zoomLevel >= 18 && _random.nextDouble() < 0.2) {
          final tipHighlightPaint = _basePaint(
            highlightColor.withOpacity(0.5),
            blurSigma: 0.6
          );
          
          canvas.drawCircle(
            Offset(x + math.cos(angle) * length, y + math.sin(angle) * length),
            0.2,
            tipHighlightPaint
          );
        }
      } else {
        // Enhanced flowers with shadows
        final color = colors[_random.nextInt(colors.length)];
        final size = 0.6 + _random.nextDouble() * 1.0;
        
        // Add shadow for flowers
        if (zoomLevel >= 17) {
          final shadowPaint = _basePaint(
            shadowColor.withOpacity(0.2),
            blurSigma: 1.0
          );
          canvas.drawCircle(Offset(x + 0.2, y + 0.2), size * 0.8, shadowPaint);
        }
        
        // Draw the flower
        final flowerPaint = _basePaint(color, blurSigma: 0.8);
        canvas.drawCircle(Offset(x, y), size, flowerPaint);
        
        // Add highlight for some flowers
        if (zoomLevel >= 18 && _random.nextDouble() < 0.4) {
          final highlightPaint = _basePaint(
            Colors.white.withOpacity(0.3),
            blurSigma: 0.6
          );
          canvas.drawCircle(Offset(x - 0.1, y - 0.1), size * 0.3, highlightPaint);
        }
      }
    }
  }

  void _generateWinterMeadowTexture(Canvas canvas, Rect bounds, Color lightColor, Color baseColor, double density) {
    final int numElements = (bounds.width * bounds.height * density / 120).round();
    final shadowColor = _colorsHelper.adjustBrightness(baseColor, -0.15);
    final highlightColor = _colorsHelper.adjustBrightness(lightColor, 0.1);
    
    // Add subtle snow variations for 3D effect if zoom level is high enough
    if (zoomLevel >= 17) {
      final int snowVariations = (numElements * 0.5).round();
      for (int i = 0; i < snowVariations; i++) {
        final x = bounds.left + _random.nextDouble() * bounds.width;
        final y = bounds.top + _random.nextDouble() * bounds.height;
        final radius = 1.0 + _random.nextDouble() * 3.0;
        
        // Subtle snow drifts
        final snowColor = Colors.white.withOpacity(0.05 + _random.nextDouble() * 0.05);
        canvas.drawCircle(
          Offset(x, y), 
          radius, 
          _basePaint(snowColor, blurSigma: 1.8)
        );
      }
    }
    
    // Add some shadow areas for depth in winter landscape
    if (zoomLevel >= 18) {
      final int shadowPatches = (numElements * 0.2).round();
      for (int i = 0; i < shadowPatches; i++) {
        final x = bounds.left + _random.nextDouble() * bounds.width;
        final y = bounds.top + _random.nextDouble() * bounds.height;
        final radius = 1.5 + _random.nextDouble() * 2.5;
        
        // Subtle shadows in snow for undulation
        final patchColor = shadowColor.withOpacity(0.06);
        canvas.drawCircle(
          Offset(x, y), 
          radius, 
          _basePaint(patchColor, blurSigma: 2.0)
        );
      }
    }

    // Main winter vegetation elements
    for (int i = 0; i < numElements; i++) {
      final x = bounds.left + _random.nextDouble() * bounds.width + _randomSmallOffset();
      final y = bounds.top + _random.nextDouble() * bounds.height + _randomSmallOffset();
      final angle = _random.nextDouble() * math.pi;
      final length = 0.8 + _random.nextDouble() * 1.5;
      final color = (_random.nextBool() ? lightColor : baseColor).withOpacity(0.25);
      
      // Draw shadow for stems to create depth
      if (zoomLevel >= 17 && _random.nextDouble() < 0.4) {
        final shadowPaint = _basePaint(
          shadowColor.withOpacity(0.1), 
          style: PaintingStyle.stroke, 
          strokeWidth: 0.4, 
          blurSigma: 0.6
        );
        
        canvas.drawLine(
          Offset(x + 0.2, y + 0.2),
          Offset(x + 0.2 + math.cos(angle) * length, y + 0.2 + math.sin(angle) * length),
          shadowPaint,
        );
      }
      
      // Main stem
      final stemPaint = _basePaint(color, style: PaintingStyle.stroke, strokeWidth: 0.4);
      canvas.drawLine(
        Offset(x, y),
        Offset(x + math.cos(angle) * length, y + math.sin(angle) * length),
        stemPaint,
      );
      
      // Add snow on some stems for 3D effect
      if (zoomLevel >= 18 && _random.nextDouble() < 0.3) {
        final snowPaint = _basePaint(
          Colors.white.withOpacity(0.3),
          blurSigma: 0.4
        );
        
        // Small snow accumulation point
        final snowPosition = _random.nextDouble();
        canvas.drawCircle(
          Offset(
            x + math.cos(angle) * length * snowPosition,
            y + math.sin(angle) * length * snowPosition
          ),
          0.3,
          snowPaint
        );
      }
    }
    
    // Add some distinct snow clumps for additional 3D effect at higher zoom
    if (zoomLevel >= 18) {
      final int snowClumps = (numElements * 0.1).round();
      for (int i = 0; i < snowClumps; i++) {
        final x = bounds.left + _random.nextDouble() * bounds.width;
        final y = bounds.top + _random.nextDouble() * bounds.height;
        
        // Snow clump with highlight and shadow
        final clumpSize = 0.5 + _random.nextDouble() * 0.7;
        
        // Shadow beneath snow
        canvas.drawCircle(
          Offset(x + 0.1, y + 0.1),
          clumpSize,
          _basePaint(shadowColor.withOpacity(0.1), blurSigma: 0.7)
        );
        
        // Main snow clump
        canvas.drawCircle(
          Offset(x, y),
          clumpSize,
          _basePaint(Colors.white.withOpacity(0.25), blurSigma: 0.3)
        );
        
        // Highlight on snow for 3D effect
        canvas.drawCircle(
          Offset(x - 0.1, y - 0.1),
          clumpSize * 0.5,
          _basePaint(Colors.white.withOpacity(0.15), blurSigma: 0.2)
        );
      }
    }
  }

  /// Generate garden pattern
  void generateGardenPattern(Canvas canvas, Rect bounds, Map<String, Color> colors, {
    String gardenType = 'formal',
    double density = 1.0,
  }) {
    if (zoomLevel < 16) return;
    
    final double centerX = bounds.left + bounds.width / 2;
    final double centerY = bounds.top + bounds.height / 2;
    
    // First add subtle terrain variations for all garden types
    if (zoomLevel >= 17) {
      _addGardenTerrainVariation(canvas, bounds, colors);
    }
    
    switch (gardenType) {
      case 'formal':
        _generateFormalGardenPattern(canvas, bounds, colors, centerX, centerY);
        break;
      case 'orchard':
        _generateOrchardPattern(canvas, bounds, colors, density);
        break;
      case 'vineyard':
        _generateVineyardPattern(canvas, bounds, colors);
        break;
      default:
        _generateFormalGardenPattern(canvas, bounds, colors, centerX, centerY);
    }
  }
  
  /// Add subtle terrain variation for all garden types
  void _addGardenTerrainVariation(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final baseColor = colors['base']!;
    final lightColor = colors['highlight']!;
    final shadowColor = _colorsHelper.adjustBrightness(baseColor, -0.2);
    
    final int variationCount = (bounds.width * bounds.height / 300).round();
    
    for (int i = 0; i < variationCount; i++) {
      final x = bounds.left + _random.nextDouble() * bounds.width;
      final y = bounds.top + _random.nextDouble() * bounds.height;
      final radius = 3.0 + _random.nextDouble() * 6.0;
      
      // Alternate between very subtle highlights and shadows
      final color = _random.nextBool()
          ? lightColor.withOpacity(0.05)
          : shadowColor.withOpacity(0.04);
          
      canvas.drawCircle(
        Offset(x, y),
        radius,
        _basePaint(color, blurSigma: 2.0)
      );
    }
  }

  /// Generate formal garden pattern with circular and geometric layouts
  void _generateFormalGardenPattern(Canvas canvas, Rect bounds, Map<String, Color> colors, double centerX, double centerY) {
    // Formal gardens have geometric patterns
    final double radius = math.min(bounds.width, bounds.height) * 0.3;
    final baseColor = colors['base']!;
    final shadowColor = _colorsHelper.adjustBrightness(baseColor, -0.2);
    final highlightColor = _colorsHelper.adjustBrightness(colors['highlight']!, 0.1);
    
    // Skip tiny areas
    if (bounds.width < 20 || bounds.height < 20) return;
    
    // Draw enhanced circular pattern with 3D effect
    // Shadow circle for depth
    if (zoomLevel >= 17) {
      final Paint shadowCirclePaint = Paint()
        ..color = shadowColor.withOpacity(0.1)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 0.8);
      
      canvas.drawCircle(Offset(centerX + 0.5, centerY + 0.5), radius + 0.5, shadowCirclePaint);
    }
    
    // Main circle
    final Paint circlePaint = Paint()
      ..color = colors['highlight']!.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    canvas.drawCircle(Offset(centerX, centerY), radius, circlePaint);
    
    // Highlight circle for 3D effect
    if (zoomLevel >= 18) {
      final Paint highlightCirclePaint = Paint()
        ..color = highlightColor.withOpacity(0.15)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.5
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 0.5);
      
      canvas.drawCircle(Offset(centerX - 0.3, centerY - 0.3), radius - 0.5, highlightCirclePaint);
    }
    
    // Add radiating paths with enhanced 3D effects
    final int numPaths = 6;
    
    for (int i = 0; i < numPaths; i++) {
      final double angle = i * (math.pi * 2 / numPaths);
      
      // Shadow path for depth
      if (zoomLevel >= 17) {
        final Paint shadowPathPaint = Paint()
          ..color = shadowColor.withOpacity(0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.8
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, 0.7);
        
        canvas.drawLine(
          Offset(centerX + 0.4, centerY + 0.4),
          Offset(
            centerX + 0.4 + math.cos(angle) * radius * 1.2,
            centerY + 0.4 + math.sin(angle) * radius * 1.2
          ),
          shadowPathPaint
        );
      }
      
      // Main path
      final Paint pathPaint = Paint()
        ..color = colors['highlight']!.withOpacity(0.15)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.8;
      
      canvas.drawLine(
        Offset(centerX, centerY),
        Offset(
          centerX + math.cos(angle) * radius * 1.2,
          centerY + math.sin(angle) * radius * 1.2
        ),
        pathPaint
      );
      
      // Highlight path for 3D effect
      if (zoomLevel >= 18) {
        final Paint highlightPathPaint = Paint()
          ..color = highlightColor.withOpacity(0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.4
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, 0.4);
        
        canvas.drawLine(
          Offset(centerX - 0.2, centerY - 0.2),
          Offset(
            centerX - 0.2 + math.cos(angle) * radius * 1.2,
            centerY - 0.2 + math.sin(angle) * radius * 1.2
          ),
          highlightPathPaint
        );
      }
    }
    
    // Add garden beds along the circle with 3D effect
    if (zoomLevel >= 18) {
      final int numBeds = 12;
      for (int i = 0; i < numBeds; i++) {
        final double angle = i * (math.pi * 2 / numBeds);
        final double bedRadius = radius * 0.8;
        final double x = centerX + math.cos(angle) * bedRadius;
        final double y = centerY + math.sin(angle) * bedRadius;
        final double bedSize = 2.0 + _random.nextDouble() * 1.5;
        
        // Shadow for garden bed
        canvas.drawCircle(
          Offset(x + 0.3, y + 0.3),
          bedSize,
          _basePaint(shadowColor.withOpacity(0.15), blurSigma: 0.8)
        );
        
        // Main garden bed
        canvas.drawCircle(
          Offset(x, y),
          bedSize,
          _basePaint(colors['detail']!.withOpacity(0.3))
        );
        
        // Highlight for garden bed
        canvas.drawCircle(
          Offset(x - 0.2, y - 0.2),
          bedSize * 0.6,
          _basePaint(highlightColor.withOpacity(0.1), blurSigma: 0.5)
        );
      }
    }
  }
  
  /// Generate orchard pattern with regularly spaced trees
  void _generateOrchardPattern(Canvas canvas, Rect bounds, Map<String, Color> colors, double density) {
    final double spacing = math.max(6.0, bounds.width / 15);
    final Color baseColor = colors['base']!;
    final Color treeColor = _colorsHelper.adjustBrightness(colors['detail']!, -0.1);
    final Color shadowColor = _colorsHelper.adjustBrightness(baseColor, -0.2);
    final Color highlightColor = _colorsHelper.adjustBrightness(colors['highlight']!, 0.1);
    
    for (double x = bounds.left + spacing/2; x < bounds.right; x += spacing) {
      for (double y = bounds.top + spacing/2; y < bounds.bottom; y += spacing) {
        // Enhanced tree representation with 3D effects
        final double treeSize = 1.0 + _random.nextDouble() * 0.5;
        
        // Tree shadow for depth
        if (zoomLevel >= 17) {
          final Paint treeShadowPaint = Paint()
            ..color = shadowColor.withOpacity(0.2)
            ..style = PaintingStyle.fill
            ..maskFilter = MaskFilter.blur(BlurStyle.normal, 1.0);
          
          canvas.drawCircle(Offset(x + 0.4, y + 0.4), treeSize * 1.2, treeShadowPaint);
        }
        
        // Main tree
        final Paint treePaint = Paint()
          ..color = treeColor.withOpacity(0.3)
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(x, y), treeSize, treePaint);
        
        // Tree highlight for 3D effect
        if (zoomLevel >= 18) {
          final Paint treeHighlightPaint = Paint()
            ..color = highlightColor.withOpacity(0.2)
            ..style = PaintingStyle.fill
            ..maskFilter = MaskFilter.blur(BlurStyle.normal, 0.7);
          
          canvas.drawCircle(Offset(x - 0.2, y - 0.2), treeSize * 0.5, treeHighlightPaint);
        }
        
        // Add tree shadow on ground for better 3D effect at highest zoom
        if (zoomLevel >= 19) {
          final Paint groundShadowPaint = Paint()
            ..color = shadowColor.withOpacity(0.1)
            ..style = PaintingStyle.fill
            ..maskFilter = MaskFilter.blur(BlurStyle.normal, 1.5);
          
          // Oval shadow projected on ground
          final shadowRect = Rect.fromCenter(
            center: Offset(x + 0.7, y + 0.7),
            width: treeSize * 2.2,
            height: treeSize * 1.5
          );
          
          canvas.drawOval(shadowRect, groundShadowPaint);
        }
      }
    }
  }
  
  /// Generate vineyard pattern with rows
  void _generateVineyardPattern(Canvas canvas, Rect bounds, Map<String, Color> colors) {
    final double rowSpacing = math.max(5.0, bounds.height / 12);
    final Color baseColor = colors['base']!;
    final Color rowColor = _colorsHelper.adjustBrightness(colors['detail']!, -0.1);
    final Color shadowColor = _colorsHelper.adjustBrightness(baseColor, -0.2);
    final Color highlightColor = _colorsHelper.adjustBrightness(colors['highlight']!, 0.1);
    
    // Draw enhanced horizontal vine rows with 3D effects
    for (double y = bounds.top + rowSpacing/2; y < bounds.bottom; y += rowSpacing) {
      // Row shadow for depth
      if (zoomLevel >= 17) {
        final Paint rowShadowPaint = Paint()
          ..color = shadowColor.withOpacity(0.15)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.8
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, 0.7);
        
        canvas.drawLine(
          Offset(bounds.left, y + 0.4),
          Offset(bounds.right, y + 0.4),
          rowShadowPaint
        );
      }
      
      // Main row
      final Paint rowPaint = Paint()
        ..color = rowColor.withOpacity(0.4)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.8;
      
      canvas.drawLine(
        Offset(bounds.left, y),
        Offset(bounds.right, y),
        rowPaint
      );
      
      // Row highlight for 3D effect
      if (zoomLevel >= 18) {
        final Paint rowHighlightPaint = Paint()
          ..color = highlightColor.withOpacity(0.2)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.4
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, 0.4);
        
        canvas.drawLine(
          Offset(bounds.left, y - 0.3),
          Offset(bounds.right, y - 0.3),
          rowHighlightPaint
        );
      }
      
      // Add vine plants along the rows at higher zoom levels
      if (zoomLevel >= 18) {
        final double plantSpacing = math.max(6.0, bounds.width / 15);
        for (double x = bounds.left + plantSpacing/2; x < bounds.right; x += plantSpacing) {
          final double plantSize = 0.8 + _random.nextDouble() * 0.4;
          
          // Plant shadow for depth
          final Paint plantShadowPaint = Paint()
            ..color = shadowColor.withOpacity(0.2)
            ..style = PaintingStyle.fill
            ..maskFilter = MaskFilter.blur(BlurStyle.normal, 0.7);
          
          canvas.drawCircle(Offset(x + 0.3, y + 0.3), plantSize, plantShadowPaint);
          
          // Main plant
          final Paint plantPaint = Paint()
            ..color = rowColor.withOpacity(0.5)
            ..style = PaintingStyle.fill;
          
          canvas.drawCircle(Offset(x, y), plantSize, plantPaint);
          
          // Plant highlight for 3D effect
          final Paint plantHighlightPaint = Paint()
            ..color = highlightColor.withOpacity(0.25)
            ..style = PaintingStyle.fill
            ..maskFilter = MaskFilter.blur(BlurStyle.normal, 0.4);
          
          canvas.drawCircle(Offset(x - 0.2, y - 0.2), plantSize * 0.4, plantHighlightPaint);
        }
      }
    }
  }
}
