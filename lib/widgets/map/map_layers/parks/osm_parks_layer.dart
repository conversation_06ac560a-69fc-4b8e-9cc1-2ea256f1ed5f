import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:async';

import 'osm_parks_data_provider.dart';
import 'osm_parks_painter.dart';

/// A Flutter Map layer for rendering OpenStreetMap parks and green spaces
/// with enhanced visual representation
class OSMParksLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final bool isMapMoving;
  final String theme;
  final String season;
  final bool enhancedDetail;
  final double detailLevel;
  final ValueChanged<OSMParksDataProvider>? onDataProviderCreated;
  final MapCamera? mapCamera;

  const OSMParksLayer({
    Key? key,
    this.tiltFactor = 1.0,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.theme = 'vibrant',
    this.season = 'default',
    this.enhancedDetail = true,
    this.detailLevel = 1.0,
    this.onDataProviderCreated,
    this.mapCamera,
  }) : super(key: key);

  @override
  State<OSMParksLayer> createState() => _OSMParksLayerState();
}

class _OSMParksLayerState extends State<OSMParksLayer> {
  late OSMParksDataProvider _dataProvider;
  bool _isLoading = true;
  bool _hasNetworkError = false;
  Map<String, List<Map<String, dynamic>>> _parksData = {};
  String _currentTheme = 'vibrant';
  String _currentSeason = 'default';
  MapCamera? _mapCamera;
  
  @override
  void initState() {
    super.initState();
    
    _currentTheme = widget.theme;
    _currentSeason = widget.season;
    
    // Initialize the data provider
    _dataProvider = OSMParksDataProvider(
      initialZoomLevel: widget.zoomLevel,
      initialBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving,
      onError: _handleDataError,
    );
    
    if (widget.onDataProviderCreated != null) {
      widget.onDataProviderCreated!(_dataProvider);
    }
    
    // Initial data load
    _fetchParksData();
  }
  
  // Handle network/data errors
  void _handleDataError(String errorMessage) {
    if (mounted) {
      setState(() {
        _hasNetworkError = true;
      });
    }
    debugPrint('OSMParksLayer error: $errorMessage');
  }

  @override
  void didUpdateWidget(OSMParksLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update current theme/season if changed
    if (widget.theme != oldWidget.theme) {
      _currentTheme = widget.theme;
    }
    
    if (widget.season != oldWidget.season) {
      _currentSeason = widget.season;
    }
    
    // Notify data provider of changes
    _dataProvider.updateParameters(
      zoomLevel: widget.zoomLevel,
      visibleBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving,
    );
    
    // Update theme and season separately
    if (widget.theme != oldWidget.theme || widget.season != oldWidget.season) {
      _dataProvider.updateSeason(widget.season);
    }
    
    // Determine if we need to fetch new data
    bool shouldFetch = _dataProvider.shouldFetchNewData(
      oldZoomLevel: oldWidget.zoomLevel,
      oldBounds: oldWidget.visibleBounds,
      oldIsMoving: oldWidget.isMapMoving,
      newZoomLevel: widget.zoomLevel,
      newBounds: widget.visibleBounds,
      newIsMoving: widget.isMapMoving,
    );
    
    if (shouldFetch) {
      _fetchParksData();
    }
  }

  void _fetchParksData() async {
    setState(() {
      _isLoading = true;
    });
    
    // Use the data provider to fetch data
    await _dataProvider.fetchParksData();
    
    if (mounted) {
      setState(() {
        _parksData = _dataProvider.parksData;
        _isLoading = false;
        
        // If there was a successful fetch, clear any previous error state
        if (_hasNetworkError && !_dataProvider.hasError) {
          _hasNetworkError = false;
        }
      });
    }
  }
  
  // Retry after network error
  void _retryAfterNetworkError() {
    if (_hasNetworkError) {
      setState(() {
        _hasNetworkError = false;
      });
      
      _dataProvider.resetErrorState();
      _fetchParksData();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't render parks at world or continental level unless zoomed in
    final zoomBucket = _dataProvider.getCurrentZoomBucket();
    if (zoomBucket <= 2 && widget.zoomLevel < 9) {
      return const SizedBox.shrink();
    }
    
    // If data is not yet loaded, show a placeholder
    if (_isLoading && !_dataProvider.hasInitialData) {
      return const SizedBox.shrink(); // Could show a loading indicator if desired
    }
    
    // Show error overlay with retry button if there was a network error
    if (_hasNetworkError) {
      return GestureDetector(
        onTap: _retryAfterNetworkError,
        child: Container(
          color: Colors.transparent,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'Tap to retry loading parks',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ),
      );
    }
    
    // Create the custom painter that will render the parks
    return CustomPaint(
      painter: OSMParksPainter(
        parksData: _parksData,
        zoomLevel: widget.zoomLevel,
        visibleBounds: widget.visibleBounds,
        theme: _currentTheme,
        season: _currentSeason,
        enhancedDetail: widget.enhancedDetail,
        tiltFactor: widget.tiltFactor,
        mapCamera: _mapCamera,
      ),
      size: Size.infinite,
    );
  }
} 