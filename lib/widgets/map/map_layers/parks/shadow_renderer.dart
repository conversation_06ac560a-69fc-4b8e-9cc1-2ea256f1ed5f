import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;

/// Enhanced utility class for rendering shadows and elevation effects
/// Provides sophisticated 2.5D shadow styling across all park renderers
class ShadowRenderer {
  final double zoomLevel;
  final double tiltFactor;
  final math.Random _random = math.Random(42); // Fixed seed for consistent effects
  
  ShadowRenderer({
    required this.zoomLevel,
    this.tiltFactor = 1.0,
  });
  
  /// Draw an enhanced shadow for a polygon feature with elevation and perspective
  void drawPolygonShadow(Canvas canvas, Path path, double elevation, {
    double opacity = 0.25,
    double offsetRatio = 1.0,
    double blurRadius = 3.0,
    bool enhancedPerspective = true,
  }) {
    // Skip if elevation is too small to be visible
    if (elevation < 0.5) return;
    
    // Adjust shadow parameters based on zoom level and tilting for dynamic 3D effect
    final double adjustedElevation = _adjustElevationForZoom(elevation);
    final double adjustedOpacity = _adjustOpacityForZoom(opacity);
    
    // Shadow opacity increases with zoom level and elevation
    final double baseOpacity = 0.3 * _getElevationFactor();
    final double zoomEnhancement = math.min(1.5, 1.0 + (zoomLevel - 15) * 0.1);
    final double shadowOpacity = math.min(0.5, baseOpacity * zoomEnhancement);
    
    // Calculate shadow offset based on elevation, enhanced by zoom level for better 3D effect
    final double offsetMultiplier = math.min(1.0 + (zoomLevel - 15) * 0.1, 1.8);
    final double offsetX = 2.0 * adjustedElevation * 0.2 * offsetMultiplier * offsetRatio;
    final double offsetY = 2.0 * adjustedElevation * 0.2 * offsetMultiplier * offsetRatio;
    
    // Choose between simple and enhanced perspective shadow based on parameter
    if (enhancedPerspective && zoomLevel >= 15) {
      // Create an enhanced perspective shadow with scaling and skewing for more dramatic effect
      final Matrix4 shadowMatrix = Matrix4.identity()
        ..translate(offsetX, offsetY)
        ..scale(1.03, 1.03); // Slight scaling for better perspective shadow
      
      // Apply the transformation to the path
      final Path shadowPath = path.transform(shadowMatrix.storage);
      
      // Create enhanced shadow paint with variable blur based on elevation
      final Paint shadowPaint = Paint()
        ..color = Colors.black.withOpacity(shadowOpacity)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, math.min(3.5, 1.5 + adjustedElevation * 0.1))
        ..style = PaintingStyle.fill
        ..isAntiAlias = true;
      
      // Draw shadow beneath the feature
      canvas.drawPath(shadowPath, shadowPaint);
    } else {
      // Simple shadow for lower zoom levels or when enhanced perspective is not needed
      final Offset shadowOffset = Offset(offsetX, offsetY);
      
      // Create a matrix for transforming the shadow path
      final Matrix4 shadowMatrix = Matrix4.identity()
        ..translate(shadowOffset.dx, shadowOffset.dy);
      
      // Transform the path to create the shadow
      final Path shadowPath = path.transform(shadowMatrix.storage);
      
      // Create shadow paint
      final Paint shadowPaint = Paint()
        ..color = Colors.black.withOpacity(adjustedOpacity)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, blurRadius);
      
      // Draw the shadow
      canvas.drawPath(shadowPath, shadowPaint);
    }
  }
  
  /// Draw an enhanced shadow for a circle with 3D elevation effect
  void drawCircleShadow(Canvas canvas, Offset center, double radius, double elevation, {
    double opacity = 0.25,
    double offsetRatio = 1.0,
    double blurRadius = 2.0,
    bool enhancedPerspective = true,
  }) {
    // Adjust shadow parameters based on zoom level and elevation
    final double adjustedElevation = _adjustElevationForZoom(elevation);
    final double adjustedOpacity = _adjustOpacityForZoom(opacity);
    
    // Skip if elevation is too small to be visible
    if (adjustedElevation < 0.3) return;
    
    // Calculate enhanced offset with perspective for better 3D effect
    final double offsetMultiplier = math.min(1.0 + (zoomLevel - 15) * 0.1, 1.8) * tiltFactor;
    final double offsetX = adjustedElevation * 0.2 * offsetMultiplier * offsetRatio;
    final double offsetY = adjustedElevation * 0.2 * offsetMultiplier * offsetRatio;
    
    // Create shadow offset
    final Offset shadowOffset = Offset(offsetX, offsetY);
    
    // Apply enhanced perspective shadow for higher zoom levels
    if (enhancedPerspective && zoomLevel >= 16) {
      // Enhanced shadow with oval distortion for perspective
      final double shadowWidth = radius * (1.0 + adjustedElevation * 0.05);
      final double shadowHeight = radius * (0.5 + adjustedElevation * 0.05); // Flatten for perspective
      
      // Create shadow paint with softer edges at higher zoom
      final Paint shadowPaint = Paint()
        ..color = Colors.black.withOpacity(adjustedOpacity)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, blurRadius + (zoomLevel >= 17 ? 1.0 : 0.0));
      
      // Draw oval shadow with perspective distortion
      canvas.drawOval(
        Rect.fromCenter(
          center: center + shadowOffset,
          width: shadowWidth,
          height: shadowHeight
        ),
        shadowPaint
      );
    } else {
      // Standard circle shadow for lower zoom
      final Paint shadowPaint = Paint()
        ..color = Colors.black.withOpacity(adjustedOpacity)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, blurRadius);
      
      // Draw the shadow
      canvas.drawCircle(
        center + shadowOffset,
        radius,
        shadowPaint
      );
    }
  }
  
  /// Draw an enhanced oval shadow with perspective distortion for objects like trees
  void drawOvalShadow(Canvas canvas, Offset center, double width, double height, double elevation, {
    double opacity = 0.25,
    double offsetRatio = 1.0,
    double blurRadius = 2.0,
    bool perspectiveDistortion = true,
  }) {
    // Adjust shadow parameters based on zoom level
    final double adjustedElevation = _adjustElevationForZoom(elevation);
    final double adjustedOpacity = _adjustOpacityForZoom(opacity);
    
    // Skip if elevation is too small to be visible
    if (adjustedElevation < 0.3) return;
    
    // Create enhanced shadow offset with dynamic adjustment based on zoom
    final double offsetMultiplier = math.min(1.0 + (zoomLevel - 15) * 0.1, 1.8) * tiltFactor;
    final double offsetX = adjustedElevation * 0.3 * offsetMultiplier * offsetRatio;
    final double offsetY = adjustedElevation * 0.3 * offsetMultiplier * offsetRatio;
    final Offset shadowOffset = Offset(offsetX, offsetY);
    
    // Apply perspective distortion to make shadow more realistic
    double shadowWidth = width;
    double shadowHeight = height;
    
    if (perspectiveDistortion && zoomLevel >= 16) {
      // Apply perspective distortion - flatten the shadow and slightly enlarge based on elevation
      shadowWidth = width * (1.0 + adjustedElevation * 0.05);
      shadowHeight = height * (0.4 + adjustedElevation * 0.05); // More flattened for better 3D effect
    }
    
    // Create enhanced shadow paint with variable blur based on zoom
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(adjustedOpacity)
      ..style = PaintingStyle.fill
      ..maskFilter = MaskFilter.blur(
        BlurStyle.normal, 
        blurRadius + (zoomLevel >= 17 ? 1.0 : 0.0)
      );
    
    // Draw the enhanced perspective shadow
    canvas.drawOval(
      Rect.fromCenter(
        center: center + shadowOffset,
        width: shadowWidth,
        height: shadowHeight
      ),
      shadowPaint
    );
  }
  
  /// Draw enhanced elevated sides for features with sophisticated 3D effect and perspective
  void drawElevatedSides(Canvas canvas, List<Offset> points, Color baseColor, double elevation, {
    List<int>? edgesToDraw,
    bool enhancedLighting = true,
  }) {
    if (points.length < 3) return;
    
    // Adjust elevation based on zoom level
    final double adjustedElevation = _adjustElevationForZoom(elevation);
    if (adjustedElevation < 0.5) return; // Skip if elevation too small
    
    // Calculate darker color for sides with enhanced shadowing
    final Color baseSideColor = _getSideColor(baseColor);
    
    // Find bottom-most points to draw sides with proper perspective
    final Path sidePath = Path();
    
    // If no specific edges specified, automatically determine visible edges based on perspective
    final List<int> visibleEdges = edgesToDraw ?? _determineVisibleEdges(points);
    
    // Process each edge of the polygon
    for (int i = 0; i < points.length; i++) {
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      
      // If this edge should be drawn as elevated
      if (visibleEdges.contains(i)) {
        // Draw perspective side with subtle enhancement
        final double perspectiveOffset = adjustedElevation * 0.1; // Slight perspective shift
        
        // Start with current point
        sidePath.moveTo(current.dx, current.dy);
        
        // Line to next point along the top edge
        sidePath.lineTo(next.dx, next.dy);
        
        // Draw down with perspective to create 3D effect
        sidePath.lineTo(next.dx + perspectiveOffset, next.dy + adjustedElevation);
        
        // Draw back to complete the side with perspective
        sidePath.lineTo(current.dx + perspectiveOffset, current.dy + adjustedElevation);
        
        // Close this side segment
        sidePath.lineTo(current.dx, current.dy);
      }
    }
    
    // Apply enhanced lighting if requested
    if (enhancedLighting && zoomLevel >= 15) {
      // Create gradient for dynamic lighting effect based on elevation
      final Rect bounds = sidePath.getBounds();
      final Paint sidePaint = Paint()
        ..shader = LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            baseSideColor,
            HSLColor.fromColor(baseSideColor)
              .withLightness(math.max(0.1, HSLColor.fromColor(baseSideColor).lightness * 0.6))
              .toColor(),
          ],
          stops: const [0.0, 1.0],
        ).createShader(bounds)
        ..style = PaintingStyle.fill
        ..isAntiAlias = true;
      
      // Draw enhanced 3D sides with gradient
      canvas.drawPath(sidePath, sidePaint);
      
      // Add subtle edge highlight for better definition
      final Paint edgePaint = Paint()
        ..color = Colors.black.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.5;
      
      canvas.drawPath(sidePath, edgePaint);
    } else {
      // Simple flat coloring for lower zooms or when enhanced lighting is not needed
      final Paint sidePaint = Paint()
        ..color = baseSideColor
        ..style = PaintingStyle.fill
        ..isAntiAlias = true;
      
      canvas.drawPath(sidePath, sidePaint);
    }
  }
  
  /// Determine which edges should be visible based on orientation relative to viewer
  List<int> _determineVisibleEdges(List<Offset> points) {
    final List<int> visibleEdges = [];
    
    // Determine visible sides based on edge direction
    // Generally, edges facing the bottom of the screen should be visible (facing viewer)
    for (int i = 0; i < points.length; i++) {
      final Offset current = points[i];
      final Offset next = points[(i + 1) % points.length];
      
      // Check if this segment faces the viewer (towards bottom of screen)
      final double dy = next.dy - current.dy;
      final double dx = next.dx - current.dx;
      
      // Edge is visible if it faces down or to the right (simplified heuristic)
      if (dy > -1.0 || dx > 0) {
        visibleEdges.add(i);
      }
    }
    
    return visibleEdges;
  }
  
  /// Create a path for an elevated edge with enhanced perspective
  Path _createElevatedEdge(Offset start, Offset end, double elevation, {
    bool isVertical = false,
    bool isReversed = false,
    double perspectiveOffset = 0.1,
  }) {
    final Path edgePath = Path();
    
    // Direction of elevation
    final double offsetX = isVertical 
        ? (isReversed ? -elevation : elevation) 
        : elevation * perspectiveOffset;
    final double offsetY = isVertical 
        ? elevation * perspectiveOffset 
        : (isReversed ? -elevation : elevation);
    
    // Create a quadrilateral for the edge with perspective shift
    edgePath.moveTo(start.dx, start.dy);
    edgePath.lineTo(start.dx + offsetX, start.dy + offsetY);
    edgePath.lineTo(end.dx + offsetX, end.dy + offsetY);
    edgePath.lineTo(end.dx, end.dy);
    edgePath.close();
    
    return edgePath;
  }
  
  /// Get side color for elevation effect with enhanced darkness
  Color _getSideColor(Color baseColor) {
    // Convert to HSL for better control over darkening
    final HSLColor hslColor = HSLColor.fromColor(baseColor);
    
    // Create darker side color with preserved saturation
    return hslColor
        .withLightness(math.max(0.15, hslColor.lightness * 0.7))
        .toColor();
  }
  
  /// Get elevation factor based on zoom level and tilt
  double _getElevationFactor() {
    // Start with normal elevation at zoom 14, then increase it to 3x at zoom 20
    if (zoomLevel <= 14) return 1.0 * tiltFactor;
    if (zoomLevel >= 20) return 3.0 * tiltFactor;
    
    // Non-linear interpolation for smoother transition - stronger enhancement at higher zooms
    return (1.0 + math.pow((zoomLevel - 14) / 6, 1.5) * 2.0) * tiltFactor;
  }
  
  /// Adjust elevation based on zoom level with better scaling
  double _adjustElevationForZoom(double elevation) {
    return elevation * _getElevationFactor();
  }
  
  /// Adjust opacity based on zoom level with better progression
  double _adjustOpacityForZoom(double opacity) {
    if (zoomLevel >= 18) return opacity;
    if (zoomLevel >= 16) return opacity * 0.8;
    if (zoomLevel >= 14) return opacity * 0.6;
    return opacity * 0.4;
  }
} 