import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:async';

import 'osm_grass_data_provider.dart';
import 'osm_grass_painter.dart';

/// A Flutter Map layer for rendering OpenStreetMap grass areas
/// with enhanced visual representation
class OSMGrassLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final bool isMapMoving;
  final String theme;
  final String season;
  final bool enhancedDetail;
  final double detailLevel;
  final ValueChanged<OSMGrassDataProvider>? onDataProviderCreated;
  final MapCamera? mapCamera;

  const OSMGrassLayer({
    Key? key,
    this.tiltFactor = 1.0,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.theme = 'vibrant',
    this.season = 'default',
    this.enhancedDetail = true,
    this.detailLevel = 1.0,
    this.onDataProviderCreated,
    this.mapCamera,
  }) : super(key: key);

  @override
  State<OSMGrassLayer> createState() => _OSMGrassLayerState();
}

class _OSMGrassLayerState extends State<OSMGrassLayer> {
  late OSMGrassDataProvider _dataProvider;
  bool _isLoading = true;
  bool _hasNetworkError = false;
  List<Map<String, dynamic>> _grassData = [];
  String _currentTheme = 'vibrant';
  String _currentSeason = 'default';
  
  @override
  void initState() {
    super.initState();
    
    _currentTheme = widget.theme;
    _currentSeason = widget.season;
    
    // Initialize the data provider
    _dataProvider = OSMGrassDataProvider(
      initialZoomLevel: widget.zoomLevel,
      initialBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving,
      onError: _handleDataError,
    );
    
    if (widget.onDataProviderCreated != null) {
      widget.onDataProviderCreated!(_dataProvider);
    }
    
    // Initial data load
    _fetchGrassData();
  }
  
  // Handle network/data errors
  void _handleDataError(String errorMessage) {
    if (mounted) {
      setState(() {
        _hasNetworkError = true;
      });
    }
    debugPrint('OSMGrassLayer error: $errorMessage');
  }

  @override
  void didUpdateWidget(OSMGrassLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update current theme/season if changed
    if (widget.theme != oldWidget.theme) {
      _currentTheme = widget.theme;
    }
    
    if (widget.season != oldWidget.season) {
      _currentSeason = widget.season;
    }
    
    // Notify data provider of changes
    _dataProvider.updateParameters(
      zoomLevel: widget.zoomLevel,
      visibleBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving,
      theme: widget.theme,
      season: widget.season,
    );
    
    // Determine if we need to fetch new data
    bool shouldFetch = _dataProvider.shouldFetchNewData();
    
    if (shouldFetch) {
      _fetchGrassData();
    }
  }

  void _fetchGrassData() async {
    setState(() {
      _isLoading = true;
    });
    
    // Use the data provider to fetch data
    await _dataProvider.fetchGrassData();
    
    if (mounted) {
      setState(() {
        _grassData = _dataProvider.grassData;
        _isLoading = false;
        
        // If there was a successful fetch, clear any previous error state
        if (_hasNetworkError && !_dataProvider.hasError) {
          _hasNetworkError = false;
        }
      });
    }
  }
  
  // Retry after network error
  void _retryAfterNetworkError() {
    if (_hasNetworkError) {
      setState(() {
        _hasNetworkError = false;
      });
      
      _dataProvider.resetErrorState();
      _fetchGrassData();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Debug info
    debugPrint('OSMGrassLayer rebuilding: isMoving=${widget.isMapMoving}, zoom=${widget.zoomLevel}, bounds=${widget.visibleBounds}');
    
    // Don't render grass areas at low zoom levels
    if (widget.zoomLevel < 14) {
      return const SizedBox.shrink();
    }
    
    // If data is not yet loaded, show a placeholder
    if (_isLoading && !_dataProvider.hasInitialData) {
      return const SizedBox.shrink(); // Could show a loading indicator if desired
    }
    
    // Show error overlay with retry button if there was a network error
    if (_hasNetworkError) {
      return GestureDetector(
        onTap: _retryAfterNetworkError,
        child: Container(
          color: Colors.transparent,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'Tap to retry loading grass areas',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ),
      );
    }
    
    // Create the custom painter that will render the grass areas
    return CustomPaint(
      size: Size.infinite,
      painter: OSMGrassPainter(
        grassData: _grassData,
        tiltFactor: widget.tiltFactor,
        zoomLevel: widget.zoomLevel,
        visibleBounds: widget.visibleBounds,
        theme: _currentTheme,
        season: _currentSeason,
        enhancedDetail: widget.enhancedDetail,
        mapCamera: widget.mapCamera,
      ),
    );
  }
} 