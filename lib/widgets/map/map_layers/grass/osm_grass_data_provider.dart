import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

import '../osm_data_processor.dart';
import '../../../../services/map_cache_manager.dart';
import '../../map_caching/map_cache_extension.dart';
import '../../map_caching/zoom_level_manager.dart';
import '../../map_caching/map_cache_coordinator.dart';

/// Data provider class that handles grass area data fetching and caching
/// This class is responsible for all the grass data-related logic, separating it from UI rendering
class OSMGrassDataProvider {
  // Data processing and caching dependencies
  final OSMDataProcessor _dataProcessor = OSMDataProcessor();
  final MapCacheManager _cacheManager = MapCacheManager();
  final ZoomLevelManager _zoomManager = ZoomLevelManager();
  
  // Callback for error handling
  final Function(String errorMessage)? onError;
  
  // State variables for grass areas
  List<Map<String, dynamic>> _grassData = [];
  
  bool _isLoading = true;
  bool _needsRefresh = true;
  String _lastBoundsKey = "";
  bool _hasError = false;
  bool _didInitialFetch = false;
  
  // Track current season for automatic seasonal coloring
  String _currentSeason = 'summer';
  
  // Track last fetch params to avoid unnecessary fetches
  LatLngBounds? _lastFetchedBounds;
  double _lastFetchedZoom = 0;
  
  // Current parameters
  double _zoomLevel;
  LatLngBounds _visibleBounds;
  bool _isMapMoving;
  
  // Track current zoom level bucket for optimized rendering
  int _currentZoomBucket = 3;
  
  // Keep track of the last request time for rate limiting
  DateTime _lastRequestTime = DateTime.now().subtract(const Duration(seconds: 30));
  
  /// Constructor that takes initial parameters and optional error callback
  OSMGrassDataProvider({
    required double initialZoomLevel,
    required LatLngBounds initialBounds,
    bool isMapMoving = false,
    this.onError,
  }) : 
    _zoomLevel = initialZoomLevel,
    _visibleBounds = initialBounds,
    _isMapMoving = isMapMoving {
    // Initialize zoom bucket
    _currentZoomBucket = _getZoomBucket(initialZoomLevel);
    
    // Determine current season based on date
    _currentSeason = _determineSeason();
  }
  
  /// Getter for grass data
  List<Map<String, dynamic>> get grassData => _grassData;
  
  /// Getter for loading state
  bool get isLoading => _isLoading;
  
  /// Getter for error state
  bool get hasError => _hasError;
  
  /// Getter for initial data state
  bool get hasInitialData => _didInitialFetch && !_isLoading;
  
  /// Reset error state for retrying
  void resetErrorState() {
    _hasError = false;
  }
  
  /// Get current zoom bucket
  int getCurrentZoomBucket() {
    return _currentZoomBucket;
  }
  
  /// Update the parameters when map changes
  void updateParameters({
    double? zoomLevel,
    LatLngBounds? visibleBounds,
    bool? isMapMoving,
    String? theme,
    String? season,
    bool? enhancedDetail,
  }) {
    // Update current parameters
    if (zoomLevel != null) _zoomLevel = zoomLevel;
    if (visibleBounds != null) _visibleBounds = visibleBounds;
    if (isMapMoving != null) _isMapMoving = isMapMoving;
    if (season != null) _currentSeason = season;
    
    // Update zoom bucket if needed
    if (zoomLevel != null) {
      final newZoomBucket = _getZoomBucket(zoomLevel);
      if (newZoomBucket != _currentZoomBucket) {
        _currentZoomBucket = newZoomBucket;
        _needsRefresh = true;
      }
    }
  }
  
  /// Determine if we should fetch new data based on parameter changes
  bool shouldFetchNewData() {
    // If we're below zoom level 14, we don't need detailed grass data
    if (_zoomLevel < 14) {
      return false;
    }
    
    // If we're still loading or don't need a refresh, skip
    if (_isLoading || !_needsRefresh) {
      return false;
    }
    
    // If map is moving and we already have data, delay fetching
    if (_isMapMoving && _grassData.isNotEmpty) {
      return false;
    }
    
    // If the bounds key has changed significantly, fetch new data
    final newBoundsKey = _getBoundsKey(_visibleBounds, _zoomLevel);
    if (newBoundsKey != _lastBoundsKey) {
      _lastBoundsKey = newBoundsKey;
      return true;
    }
    
    // Otherwise, don't fetch new data
    return false;
  }
  
  /// Main method for fetching grass area data
  Future<void> fetchGrassData() async {
    try {
      // Skip if we're at a zoom level where detailed grass data isn't needed
      if (_zoomLevel < 14) {
        _grassData = [];
        _isLoading = false;
        _needsRefresh = false;
        _didInitialFetch = true;
        _hasError = false;
        return;
      }
      
      _isLoading = !_didInitialFetch; // Only show loading on first fetch
      
      // Update bounds key
      _lastBoundsKey = _getBoundsKey(_visibleBounds, _zoomLevel);
      
      // Check if bounds or zoom has significantly changed
      bool shouldSkipFetch = false;
      if (_lastFetchedBounds != null && _lastFetchedZoom > 0) {
        final boundsDistance = _calculateBoundsDistance(_visibleBounds, _lastFetchedBounds!);
        final zoomDifference = (_zoomLevel - _lastFetchedZoom).abs();
        
        // If we're moving the map and have cached data, delay the fetch
        if (_isMapMoving && _grassData.isNotEmpty) {
          shouldSkipFetch = true;
        }
        // If bounds haven't changed much and zoom level is similar, use cached data
        else if (boundsDistance < 0.1 && zoomDifference < 1.0 && _grassData.isNotEmpty) {
          shouldSkipFetch = true;
        }
      }
      
      if (shouldSkipFetch) {
        _isLoading = false;
        _needsRefresh = true; // Mark for refresh when map movement stops
        return;
      }
      
      // Generate cache key for the MapCacheCoordinator
      final cacheKey = 'grass_${_visibleBounds.southWest.latitude.toStringAsFixed(4)}_${_visibleBounds.southWest.longitude.toStringAsFixed(4)}_${_visibleBounds.northEast.latitude.toStringAsFixed(4)}_${_visibleBounds.northEast.longitude.toStringAsFixed(4)}_${_currentZoomBucket}';
      
      try {
        // Use MapCacheCoordinator to get data from cache or fetch from network
        final grassData = await MapCacheCoordinator().getData(
          type: MapDataType.grass,
          key: cacheKey,
          southwest: _visibleBounds.southWest,
          northeast: _visibleBounds.northEast,
          zoomLevel: _zoomLevel,
          fetchIfMissing: () async {
            // Calculate a safe data request region based on API limits
            final safeRequestBounds = _calculateSafeRequestBounds(
              _visibleBounds,
              _getDetailLevel(_currentZoomBucket),
              _zoomLevel
            );
            
            // Fetch grass data with appropriate detail level
            return await _dataProcessor.fetchGrassData(
              safeRequestBounds.southWest,
              safeRequestBounds.northEast,
              detailLevel: _getDetailLevel(_currentZoomBucket)
            );
          }
        );
        
        // Process the data
        if (grassData != null) {
          _grassData = List<Map<String, dynamic>>.from(grassData);
        } else {
          _grassData = [];
        }
        
        _isLoading = false;
        _needsRefresh = false;
        _lastFetchedBounds = _visibleBounds;
        _lastFetchedZoom = _zoomLevel;
        _didInitialFetch = true;
        _hasError = false;
      } catch (e) {
        debugPrint('Error in OSMGrassDataProvider: $e');
        _isLoading = false;
        _hasError = true;
        _didInitialFetch = true; // We did try to fetch
        
        // Notify listeners about the error
        onError?.call(e.toString());
      }
    } catch (e) {
      debugPrint('Error in OSMGrassDataProvider.fetchGrassData: $e');
      _isLoading = false;
      _hasError = true;
      _didInitialFetch = true;
    }
  }
  
  /// Determine the current season based on date
  String _determineSeason() {
    final now = DateTime.now();
    final month = now.month;
    
    if (month >= 3 && month <= 5) {
      return 'spring';
    } else if (month >= 6 && month <= 8) {
      return 'summer';
    } else if (month >= 9 && month <= 11) {
      return 'autumn';
    } else {
      return 'winter';
    }
  }
  
  /// Get a zoom bucket (1-4) based on zoom level
  int _getZoomBucket(double zoomLevel) {
    if (zoomLevel < 10) {
      return 1; // Global view
    } else if (zoomLevel < 14) {
      return 2; // Regional view
    } else if (zoomLevel < 17) {
      return 3; // Local view
    } else {
      return 4; // Detailed view
    }
  }
  
  /// Get detail level (1-3) based on zoom bucket
  int _getDetailLevel(int zoomBucket) {
    switch (zoomBucket) {
      case 1: return 1; // Low detail
      case 2: return 1; // Low detail
      case 3: return 2; // Medium detail
      case 4: return 3; // High detail
      default: return 2; // Medium detail as default
    }
  }
  
  /// Create a bounds key for comparing visible regions
  String _getBoundsKey(LatLngBounds bounds, double zoom) {
    final precision = zoom < 14 ? 2 : 3;
    return '${bounds.southWest.latitude.toStringAsFixed(precision)}_${bounds.southWest.longitude.toStringAsFixed(precision)}_${bounds.northEast.latitude.toStringAsFixed(precision)}_${bounds.northEast.longitude.toStringAsFixed(precision)}_${zoom.toStringAsFixed(1)}';
  }
  
  /// Calculate approximate distance between two bounds (in degrees)
  double _calculateBoundsDistance(LatLngBounds bounds1, LatLngBounds bounds2) {
    final center1 = bounds1.center;
    final center2 = bounds2.center;
    return math.sqrt(math.pow(center1.latitude - center2.latitude, 2) + math.pow(center1.longitude - center2.longitude, 2));
  }
  
  /// Calculate safe request bounds to avoid overloading the API
  LatLngBounds _calculateSafeRequestBounds(LatLngBounds visibleBounds, int detailLevel, double zoomLevel) {
    // At high zoom levels with high detail, we need to be more restrictive
    double expansionFactor;
    if (zoomLevel >= 18 && detailLevel == 3) {
      expansionFactor = 0.1; // Very small expansion at highest zoom
    } else if (zoomLevel >= 16) {
      expansionFactor = 0.2; // Small expansion at high zoom
    } else if (zoomLevel >= 14) {
      expansionFactor = 0.3; // Medium expansion at medium zoom
    } else {
      expansionFactor = 0.5; // Larger expansion at low zoom
    }
    
    // Calculate expanded bounds
    final center = visibleBounds.center;
    final latDelta = (visibleBounds.northEast.latitude - visibleBounds.southWest.latitude) * expansionFactor;
    final lngDelta = (visibleBounds.northEast.longitude - visibleBounds.southWest.longitude) * expansionFactor;
    
    return LatLngBounds(
      LatLng(
        visibleBounds.southWest.latitude - latDelta,
        visibleBounds.southWest.longitude - lngDelta,
      ),
      LatLng(
        visibleBounds.northEast.latitude + latDelta,
        visibleBounds.northEast.longitude + lngDelta,
      ),
    );
  }
} 