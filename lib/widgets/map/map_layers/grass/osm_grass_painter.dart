import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

/// CustomPainter for rendering grass areas with enhanced visual effects
class OSMGrassPainter extends CustomPainter {
  final List<Map<String, dynamic>> grassData;
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final String theme;
  final String season;
  final bool enhancedDetail;
  final MapCamera? mapCamera;
  
  final math.Random _random = math.Random(42); // Fixed seed for consistent random patterns
  
  // Grass color palettes for different themes and seasons
  final Map<String, Map<String, Map<String, Color>>> _colorPalettes = {
    'vibrant': {
      'default': {
        'base': const Color(0xFF66BB6A),       // Medium green
        'highlight': const Color(0xFF81C784),  // Light green
        'shadow': const Color(0xFF388E3C),     // Dark green
      },
      'spring': {
        'base': const Color(0xFF7CB342),       // Fresh spring green
        'highlight': const Color(0xFF9CCC65),  // Light spring green
        'shadow': const Color(0xFF558B2F),     // Dark spring green
      },
      'summer': {
        'base': const Color(0xFF66BB6A),       // Medium green
        'highlight': const Color(0xFF81C784),  // Light green
        'shadow': const Color(0xFF388E3C),     // Dark green
      },
      'autumn': {
        'base': const Color(0xFFAED581),       // Lighter yellow-green
        'highlight': const Color(0xFFDCE775),  // Yellow-green
        'shadow': const Color(0xFF8BC34A),     // Muted green
      },
      'winter': {
        'base': const Color(0xFFB0BEC5),       // Bluish gray
        'highlight': const Color(0xFFCFD8DC),  // Light gray
        'shadow': const Color(0xFF90A4AE),     // Dark gray
      }
    },
    'dark': {
      'default': {
        'base': const Color(0xFF2E7D32),       // Darker green
        'highlight': const Color(0xFF4CAF50),  // Medium green
        'shadow': const Color(0xFF1B5E20),     // Very dark green
      },
      // Similar entries for other seasons
      'spring': {
        'base': const Color(0xFF558B2F),       // Darker spring green
        'highlight': const Color(0xFF7CB342),  // Medium spring green
        'shadow': const Color(0xFF33691E),     // Very dark spring green
      },
      'summer': {
        'base': const Color(0xFF2E7D32),       // Darker green
        'highlight': const Color(0xFF4CAF50),  // Medium green
        'shadow': const Color(0xFF1B5E20),     // Very dark green
      },
      'autumn': {
        'base': const Color(0xFF8BC34A),       // Muted green
        'highlight': const Color(0xFFAED581),  // Light yellow-green
        'shadow': const Color(0xFF689F38),     // Darker yellow-green
      },
      'winter': {
        'base': const Color(0xFF78909C),       // Medium blue-gray
        'highlight': const Color(0xFF90A4AE),  // Light blue-gray
        'shadow': const Color(0xFF546E7A),     // Dark blue-gray
      }
    },
    'monochrome': {
      'default': {
        'base': const Color(0xFFE0E0E0),       // Light gray
        'highlight': const Color(0xFFF5F5F5),  // Very light gray
        'shadow': const Color(0xFFBDBDBD),     // Medium gray
      },
      // Similar entries for all seasons (monochrome has less variation)
      'spring': {
        'base': const Color(0xFFE0E0E0),       // Light gray
        'highlight': const Color(0xFFF5F5F5),  // Very light gray
        'shadow': const Color(0xFFBDBDBD),     // Medium gray
      },
      'summer': {
        'base': const Color(0xFFE0E0E0),       // Light gray
        'highlight': const Color(0xFFF5F5F5),  // Very light gray
        'shadow': const Color(0xFFBDBDBD),     // Medium gray
      },
      'autumn': {
        'base': const Color(0xFFEEEEEE),       // Lighter gray
        'highlight': const Color(0xFFFAFAFA),  // Nearly white
        'shadow': const Color(0xFFE0E0E0),     // Light gray
      },
      'winter': {
        'base': const Color(0xFFE0E0E0),       // Light gray
        'highlight': const Color(0xFFF5F5F5),  // Very light gray
        'shadow': const Color(0xFFBDBDBD),     // Medium gray
      }
    }
  };
  
  OSMGrassPainter({
    required this.grassData,
    required this.tiltFactor,
    required this.zoomLevel,
    required this.visibleBounds,
    this.theme = 'vibrant',
    this.season = 'default',
    this.enhancedDetail = true,
    this.mapCamera,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Skip rendering if no data or at very low zoom
    if (grassData.isEmpty || zoomLevel < 14) {
      return;
    }
    
    // Get the color palette based on theme and season
    final String themeKey = _colorPalettes.containsKey(theme) ? theme : 'vibrant';
    final String seasonKey = _colorPalettes[themeKey]!.containsKey(season) ? season : 'default';
    final Map<String, Color> colors = _colorPalettes[themeKey]![seasonKey]!;
    
    // Calculate the projection from geographic to screen coordinates
    final projection = _getMapProjection(size);
    
    // Sort grass areas by size for proper rendering order
    final sortedGrassAreas = List<Map<String, dynamic>>.from(grassData);
    sortedGrassAreas.sort((a, b) {
      final aCoords = (a['geometry']['coordinates'] as List<dynamic>)[0] as List<dynamic>;
      final bCoords = (b['geometry']['coordinates'] as List<dynamic>)[0] as List<dynamic>;
      return aCoords.length.compareTo(bCoords.length);
    });
    
    // Render each grass area
    for (final area in sortedGrassAreas) {
      _renderGrassArea(canvas, area, colors, size, projection);
    }
  }
  
  /// Render a single grass area with enhanced visuals
  void _renderGrassArea(
    Canvas canvas, 
    Map<String, dynamic> area, 
    Map<String, Color> colors, 
    Size size,
    MapProjection projection
  ) {
    try {
      // Extract coordinates from geometry
      final List<dynamic> coordinates = (area['geometry']['coordinates'] as List<dynamic>)[0] as List<dynamic>;
      
      // Create a path for the grass area
      final Path grassPath = Path();
      
      // Move to the first point
      if (coordinates.isEmpty) return;
      
      final firstPoint = _projectPoint(coordinates[0][1], coordinates[0][0], projection, size);
      grassPath.moveTo(firstPoint.dx, firstPoint.dy);
      
      // Add lines to all other points
      for (int i = 1; i < coordinates.length; i++) {
        final point = _projectPoint(coordinates[i][1], coordinates[i][0], projection, size);
        grassPath.lineTo(point.dx, point.dy);
      }
      
      // Close the path
      grassPath.close();
      
      // Base fill for the grass area
      final Paint grassPaint = Paint()
        ..color = colors['base']!
        ..style = PaintingStyle.fill;
      
      canvas.drawPath(grassPath, grassPaint);
      
      // Add texture based on detail level and tilt
      if (enhancedDetail) {
        if (zoomLevel >= 16) {
          _addDetailedGrassTexture(canvas, grassPath, colors, size);
        } else {
          _addSimpleGrassTexture(canvas, grassPath, colors);
        }
      }
      
      // Add border to the grass area
      final Paint borderPaint = Paint()
        ..color = colors['shadow']!.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;
      
      canvas.drawPath(grassPath, borderPaint);
      
    } catch (e) {
      debugPrint('Error rendering grass area: $e');
    }
  }
  
  /// Add a simple grass texture with a pattern
  void _addSimpleGrassTexture(Canvas canvas, Path grassPath, Map<String, Color> colors) {
    final Paint texturePaint = Paint()
      ..color = colors['highlight']!.withOpacity(0.3)
      ..style = PaintingStyle.fill;
    
    // Create a simplified texture path
    final texturePath = Path();
    
    // Get the bounds of the grass area
    final Rect bounds = grassPath.getBounds();
    
    // Add a simple pattern based on bounds
    final spacing = 8.0;
    for (double x = bounds.left; x < bounds.right; x += spacing) {
      for (double y = bounds.top; y < bounds.bottom; y += spacing) {
        final point = Offset(x + _random.nextDouble() * 4 - 2, y + _random.nextDouble() * 4 - 2);
        
        // Only add texture within the grass area
        if (_pointInPath(point, grassPath)) {
          final smallDotSize = 1.0 + _random.nextDouble() * 1.5;
          texturePath.addOval(Rect.fromCircle(center: point, radius: smallDotSize));
        }
      }
    }
    
    canvas.drawPath(texturePath, texturePaint);
  }
  
  /// Add detailed grass texture with more realistic elements
  void _addDetailedGrassTexture(Canvas canvas, Path grassPath, Map<String, Color> colors, Size size) {
    // Save the canvas state for clipping
    canvas.save();
    
    // Clip to the grass area
    canvas.clipPath(grassPath);
    
    // Get the bounds of the grass area
    final Rect bounds = grassPath.getBounds();
    
    // Determine detail level based on zoom and tilt
    final int bladeCount;
    if (zoomLevel >= 18) {
      bladeCount = 200;
    } else if (zoomLevel >= 17) {
      bladeCount = 120;
    } else {
      bladeCount = 70;
    }
    
    // Create grass blades
    for (int i = 0; i < bladeCount; i++) {
      // Random position within bounds
      final x = bounds.left + _random.nextDouble() * bounds.width;
      final y = bounds.top + _random.nextDouble() * bounds.height;
      final position = Offset(x, y);
      
      // Check if the point is within the grass area
      if (_pointInPath(position, grassPath)) {
        // Blade properties
        final height = 2.0 + _random.nextDouble() * 4.0 * (1 + tiltFactor);
        final width = 0.5 + _random.nextDouble() * 1.0;
        final angle = -30 + _random.nextDouble() * 60; // -30 to 30 degrees
        
        // Blade color with variation
        final baseColor = colors['base']!;
        final bladeColor = Color.fromARGB(
          baseColor.alpha,
          (baseColor.red + (_random.nextInt(20) - 10)).clamp(0, 255),
          (baseColor.green + (_random.nextInt(20) - 5)).clamp(0, 255),
          (baseColor.blue + (_random.nextInt(20) - 10)).clamp(0, 255),
        );
        
        // Draw the grass blade
        final Paint bladePaint = Paint()
          ..color = bladeColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = width
          ..strokeCap = StrokeCap.round;
        
        // Apply tilt effect to the blade angle
        final tiltedAngle = angle - (tiltFactor * 20); // Tilt blades more as tilt increases
        
        // Draw blade as a curved line
        final bladePath = Path();
        bladePath.moveTo(position.dx, position.dy);
        
        // Add a subtle curve for more natural appearance
        final endX = position.dx + math.sin(tiltedAngle * math.pi / 180) * height;
        final endY = position.dy - math.cos(tiltedAngle * math.pi / 180) * height;
        final controlX = position.dx + math.sin((tiltedAngle + 10) * math.pi / 180) * height * 0.7;
        final controlY = position.dy - math.cos((tiltedAngle + 10) * math.pi / 180) * height * 0.7;
        
        bladePath.quadraticBezierTo(controlX, controlY, endX, endY);
        
        canvas.drawPath(bladePath, bladePaint);
      }
    }
    
    // Restore canvas state
    canvas.restore();
  }
  
  /// Check if a point is inside a path
  bool _pointInPath(Offset point, Path path) {
    return path.contains(point);
  }
  
  /// Project a geographic point to screen coordinates
  Offset _projectPoint(double lat, double lng, MapProjection projection, Size size) {
    if (mapCamera != null) {
      // Use MapCamera for more accurate projection if available
      final screenPoint = mapCamera!.latLngToScreenPoint(LatLng(lat, lng));
      if (screenPoint != null) {
        return Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
      }
    }
    
    // Fallback to manual projection
    return projection(LatLng(lat, lng));
  }
  
  /// Create a function to project from geographic to screen coordinates
  MapProjection _getMapProjection(Size size) {
    if (mapCamera != null) {
      // Use MapCamera for projection if available
      return (LatLng latlng) {
        final screenPoint = mapCamera!.latLngToScreenPoint(latlng);
        if (screenPoint != null) {
          return Offset(screenPoint.x.toDouble(), screenPoint.y.toDouble());
        }
        return _manualProject(latlng, size);
      };
    }
    
    // Fallback to manual projection
    return (LatLng latlng) => _manualProject(latlng, size);
  }
  
  /// Manual projection from geographic to screen coordinates
  Offset _manualProject(LatLng latlng, Size size) {
    final sw = visibleBounds.southWest;
    final ne = visibleBounds.northEast;
    
    final widthRatio = (latlng.longitude - sw.longitude) / (ne.longitude - sw.longitude);
    final heightRatio = (ne.latitude - latlng.latitude) / (ne.latitude - sw.latitude);
    
    return Offset(
      widthRatio * size.width,
      heightRatio * size.height,
    );
  }
  
  @override
  bool shouldRepaint(OSMGrassPainter oldDelegate) {
    // IMPORTANT FIX: Always repaint when mapCamera changes to ensure smooth movement during drag
    return oldDelegate.zoomLevel != zoomLevel ||
           oldDelegate.tiltFactor != tiltFactor ||
           oldDelegate.visibleBounds != visibleBounds ||
           oldDelegate.theme != theme ||
           oldDelegate.season != season ||
           oldDelegate.enhancedDetail != enhancedDetail ||
           oldDelegate.mapCamera != mapCamera ||
           oldDelegate.grassData != grassData;
  }
}

/// Type definition for map projection function
typedef MapProjection = Offset Function(LatLng); 