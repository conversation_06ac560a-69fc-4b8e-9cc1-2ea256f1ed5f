import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

import 'water/osm_water_data_provider.dart';
import 'water/osm_water_painter.dart';

/// A custom layer to render OpenStreetMap water features with clean separation of concerns
class OSMWaterFeaturesLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final bool isMapMoving;
  final String theme; // Added theme parameter for customization
  final Color waterColor;
  final Color waterOutlineColor;
  final MapCamera? mapCamera; // Add map camera parameter
  final int? detailLevel;
  
  const OSMWaterFeaturesLayer({
    Key? key,
    this.tiltFactor = 1.0,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.theme = 'vibrant', // Default to vibrant theme
    this.waterColor = const Color(0xFF1976D2), // Primary blue by default
    this.waterOutlineColor = const Color(0xFF0D47A1), // Darker blue for outlines
    this.mapCamera, // Add map camera parameter
    this.detailLevel,
  }) : super(key: key);

  @override
  State<OSMWaterFeaturesLayer> createState() => _OSMWaterFeaturesLayerState();
}

class _OSMWaterFeaturesLayerState extends State<OSMWaterFeaturesLayer> with SingleTickerProviderStateMixin {
  // Data provider handles all data fetching and caching logic
  late final OSMWaterDataProvider _dataProvider;
  
  // Animation controller for water ripple effect
  late AnimationController _animationController;
  late Animation<double> _rippleAnimation;
  
  // Track local state
  String _currentTheme = 'vibrant';
  bool _hasNetworkError = false;
  
  @override
  void initState() {
    super.initState();
    _currentTheme = widget.theme;
    
    // Initialize animation controller for ripple effect
    _animationController = AnimationController(
      duration: const Duration(seconds: 4), // Slow ripple animation
      vsync: this,
    );
    
    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    // Loop the animation for continuous ripple
    _animationController.repeat(reverse: false);
    
    // Initialize data provider
    _dataProvider = OSMWaterDataProvider(
      initialZoomLevel: widget.zoomLevel,
      initialBounds: widget.visibleBounds,
      onError: _handleDataError,
    );
    
    // Initial data fetch
    _fetchWaterFeaturesData();
  }
  
  // Handle network/data errors
  void _handleDataError(String errorMessage) {
    if (mounted) {
      setState(() {
        _hasNetworkError = true;
      });
    }
    debugPrint('OSMWaterFeaturesLayer error: $errorMessage');
  }
  
  @override
  void didUpdateWidget(OSMWaterFeaturesLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update current theme if changed
    if (widget.theme != oldWidget.theme) {
      _currentTheme = widget.theme;
    }
    
    // Notify data provider of changes
    _dataProvider.updateParameters(
      zoomLevel: widget.zoomLevel,
      visibleBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving
    );
    
    // Determine if we need to fetch new data
    bool shouldFetch = _dataProvider.shouldFetchNewData(
      oldZoomLevel: oldWidget.zoomLevel,
      oldBounds: oldWidget.visibleBounds,
      oldIsMoving: oldWidget.isMapMoving,
      newZoomLevel: widget.zoomLevel,
      newBounds: widget.visibleBounds,
      newIsMoving: widget.isMapMoving,
    );
    
    if (shouldFetch) {
      _fetchWaterFeaturesData();
    }
  }
  
  void _fetchWaterFeaturesData() async {
    // Use the data provider to fetch data
    await _dataProvider.fetchWaterFeaturesData();
    
    // If there was a successful fetch, clear any previous error state
    if (_hasNetworkError && !_dataProvider.hasError) {
      setState(() {
        _hasNetworkError = false;
      });
    }
  }
  
  // Retry after network error
  void _retryAfterNetworkError() {
    if (_hasNetworkError) {
      setState(() {
        _hasNetworkError = false;
      });
      
      _dataProvider.resetErrorState();
      _fetchWaterFeaturesData();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine effective theme (adapt to system theme or use explicit theme)
    final effectiveTheme = widget.theme == 'auto' 
        ? MediaQuery.of(context).platformBrightness == Brightness.dark ? 'dark' : 'vibrant'
        : widget.theme;
    
    // Get current zoom bucket for rendering optimizations
    final zoomBucket = _dataProvider.getCurrentZoomBucket();
    
    // Don't render detailed water at world level unless zoomed in
    if (zoomBucket <= 1 && widget.zoomLevel < 8) {
      return const SizedBox.shrink();
    }
    
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_dataProvider.isLoading && !_dataProvider.hasInitialData) {
          // Show loading indicator on first load
          return Center(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
          );
        }
        
        // Show error overlay with retry button if there was a network error
        if (_hasNetworkError) {
          return Stack(
            children: [
              // Show existing water features if we have them
              if (_dataProvider.waterFeatures.isNotEmpty)
                AnimatedBuilder(
                  animation: _rippleAnimation,
                  builder: (context, child) {
                    return CustomPaint(
                      size: Size(
                        constraints.maxWidth,
                        constraints.maxHeight,
                      ),
                      painter: OSMWaterPainter(
                        waterFeatures: _dataProvider.waterFeatures,
                        tiltFactor: widget.tiltFactor,
                        zoomLevel: widget.zoomLevel,
                        visibleBounds: widget.visibleBounds,
                        theme: effectiveTheme,
                        zoomBucket: zoomBucket,
                        showDetails: zoomBucket >= 4,
                        animationValue: _rippleAnimation.value,
                        waterColor: widget.waterColor,
                        waterOutlineColor: widget.waterOutlineColor,
                        mapCamera: widget.mapCamera,
                      ),
                    );
                  },
                ),
              
              // Overlay with retry button
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'Network error loading water data',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _retryAfterNetworkError,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        }

        if (_dataProvider.waterFeatures.isEmpty && _dataProvider.hasInitialData) {
          // No water features found but we did search
          return const SizedBox.shrink();
        }
    
        // Render water features with animation
        return AnimatedBuilder(
          animation: _rippleAnimation,
          builder: (context, child) {
            return CustomPaint(
              size: Size(
                constraints.maxWidth,
                constraints.maxHeight,
              ),
              painter: OSMWaterPainter(
                waterFeatures: _dataProvider.waterFeatures,
                tiltFactor: widget.tiltFactor,
                zoomLevel: widget.zoomLevel,
                visibleBounds: widget.visibleBounds,
                theme: effectiveTheme,
                zoomBucket: zoomBucket,
                showDetails: zoomBucket >= 4,
                animationValue: _rippleAnimation.value,
                waterColor: widget.waterColor,
                waterOutlineColor: widget.waterOutlineColor,
                mapCamera: widget.mapCamera,
              ),
            );
          },
        );
      },
    );
  }
  
  @override
  void dispose() {
    // Clean up resources
    _animationController.dispose();
    _dataProvider.dispose();
    super.dispose();
  }
} 