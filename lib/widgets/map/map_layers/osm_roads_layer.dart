import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' hide Path; // Hide Path from latlong2
import 'dart:math' as math;
import 'dart:ui'; // Explicitly import dart:ui for Path

import 'roads/osm_roads_data_provider.dart';
import 'roads/osm_roads_painter.dart';

/// A custom layer to render OpenStreetMap roads with clean separation of concerns
class OSMRoadsLayer extends StatefulWidget {
  final double tiltFactor;
  final double zoomLevel;
  final LatLngBounds visibleBounds;
  final bool isMapMoving;
  final String theme; // Added theme parameter for customization
  final int detailLevel; // Added detailLevel parameter
  final MapCamera? mapCamera;
  
  const OSMRoadsLayer({
    Key? key,
    this.tiltFactor = 1.0,
    required this.zoomLevel,
    required this.visibleBounds,
    this.isMapMoving = false,
    this.theme = 'vibrant', // Default to vibrant theme
    this.detailLevel = 3, // Default to high detail
    this.mapCamera,
  }) : super(key: key);

  @override
  State<OSMRoadsLayer> createState() => _OSMRoadsLayerState();
}

class _OSMRoadsLayerState extends State<OSMRoadsLayer> {
  // Data provider handles all data fetching and caching logic
  late final OSMRoadsDataProvider _dataProvider;
  
  // Track local state
  String _currentTheme = 'vibrant';
  bool _hasNetworkError = false;
  
  @override
  void initState() {
    super.initState();
    _currentTheme = widget.theme;
    
    // Initialize data provider
    _dataProvider = OSMRoadsDataProvider(
      initialZoomLevel: widget.zoomLevel,
      initialBounds: widget.visibleBounds,
      onError: _handleDataError,
      initialDetailLevel: widget.detailLevel, // Pass detail level to data provider
    );
    
    // Initial data fetch
    _fetchRoadsData();
  }
  
  // Handle network/data errors
  void _handleDataError(String errorMessage) {
    if (mounted) {
      setState(() {
        _hasNetworkError = true;
      });
    }
    debugPrint('OSMRoadsLayer error: $errorMessage');
  }
  
  @override
  void didUpdateWidget(OSMRoadsLayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update current theme if changed
    if (widget.theme != oldWidget.theme) {
      _currentTheme = widget.theme;
    }
    
    // Notify data provider of changes
    _dataProvider.updateParameters(
      zoomLevel: widget.zoomLevel,
      visibleBounds: widget.visibleBounds,
      isMapMoving: widget.isMapMoving
    );
    
    // Determine if we need to fetch new data
    bool shouldFetch = _dataProvider.shouldFetchNewData(
      oldZoomLevel: oldWidget.zoomLevel,
      oldBounds: oldWidget.visibleBounds,
      oldIsMoving: oldWidget.isMapMoving,
      newZoomLevel: widget.zoomLevel,
      newBounds: widget.visibleBounds,
      newIsMoving: widget.isMapMoving,
    );
    
    if (shouldFetch) {
      _fetchRoadsData();
    }
  }
  
  void _fetchRoadsData() async {
    // Use the data provider to fetch data
    await _dataProvider.fetchRoadsData();
    
    // If there was a successful fetch, clear any previous error state
    if (_hasNetworkError && !_dataProvider.hasError) {
      setState(() {
        _hasNetworkError = false;
      });
    }
  }
  
  // Retry after network error
  void _retryAfterNetworkError() {
    if (_hasNetworkError) {
      setState(() {
        _hasNetworkError = false;
      });
      
      _dataProvider.resetErrorState();
      _fetchRoadsData();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine effective theme (adapt to system theme or use explicit theme)
    final effectiveTheme = widget.theme == 'auto' 
        ? MediaQuery.of(context).platformBrightness == Brightness.dark ? 'dark' : 'vibrant'
        : widget.theme;
    
    // Get current zoom bucket for rendering optimizations
    final zoomBucket = _dataProvider.getCurrentZoomBucket();
    
    // Don't render roads at world or continental level unless zoomed in
    if (zoomBucket <= 2 && widget.zoomLevel < 10) {
      return const SizedBox.shrink();
    }
    
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_dataProvider.isLoading && !_dataProvider.hasInitialData) {
          // Show loading indicator on first load
          return Center(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
          );
        }
        
        // Show error overlay with retry button if there was a network error
        if (_hasNetworkError) {
          return Stack(
            children: [
              // Show existing roads if we have them
              if (_dataProvider.roads.isNotEmpty)
                CustomPaint(
                  size: Size(
                    constraints.maxWidth,
                    constraints.maxHeight,
                  ),
                  painter: OSMRoadsPainter(
                    roads: _dataProvider.roads,
                    tiltFactor: widget.tiltFactor,
                    zoomLevel: widget.zoomLevel,
                    visibleBounds: widget.visibleBounds,
                    theme: effectiveTheme,
                    zoomBucket: zoomBucket,
                    showDetails: zoomBucket >= 4,
                    mapCamera: widget.mapCamera,
                  ),
                ),
              
              // Overlay with retry button
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'Network error loading road data',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _retryAfterNetworkError,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        }

        if (_dataProvider.roads.isEmpty && _dataProvider.hasInitialData) {
          // No roads found but we did search
          return const SizedBox.shrink();
        }
    
        return CustomPaint(
          size: Size(
            constraints.maxWidth,
            constraints.maxHeight,
          ),
          painter: OSMRoadsPainter(
            roads: _dataProvider.roads,
            tiltFactor: widget.tiltFactor,
            zoomLevel: widget.zoomLevel,
            visibleBounds: widget.visibleBounds,
            theme: effectiveTheme,
            zoomBucket: zoomBucket,
            showDetails: zoomBucket >= 4,
            mapCamera: widget.mapCamera,
          ),
        );
      },
    );
  }
  
  @override
  void dispose() {
    // Clean up the data provider
    _dataProvider.dispose();
    super.dispose();
  }
} 