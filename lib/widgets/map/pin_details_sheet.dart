import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/themes.dart';
import '../../models/pin.dart';
import '../../providers/map_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/api/music_service.dart';

class PinDetailsSheet extends StatefulWidget {
  final Map<String, dynamic> pin;
  
  const PinDetailsSheet({
    Key? key,
    required this.pin,
  }) : super(key: key);

  @override
  State<PinDetailsSheet> createState() => _PinDetailsSheetState();
}

class _PinDetailsSheetState extends State<PinDetailsSheet> {
  bool _isPlaying = false;
  bool _isExpanded = false;
  
  // Helper getters for pin data with improved fallbacks - following exact same pattern as title/artist
  String get title => widget.pin['track_title'] ?? widget.pin['title'] ?? 'Unknown Track';
  String get artist => widget.pin['track_artist'] ?? widget.pin['artist'] ?? 'Unknown Artist';
  String get ownerName {
    print('🔍 DEBUG: Processing owner data:');
    print('📌 Full pin data: ${widget.pin}');
    print('👤 Owner object: ${widget.pin['owner']}');
    print('📝 Owner name: ${widget.pin['owner_name']}');
    print('👤 Username: ${widget.pin['username']}');
    
    // First try to get from owner object
    final ownerObj = widget.pin['owner'];
    if (ownerObj is Map<String, dynamic>) {
      print('✅ Owner is a Map: $ownerObj');
      final username = ownerObj['username'];
      if (username != null && username is String) {
        print('✅ Found username in owner object: $username');
        return username;
      }
      print('❌ No valid username in owner object');
    } else {
      print('❌ Owner is not a Map: $ownerObj');
    }
    
    // Try owner_name
    final ownerName = widget.pin['owner_name'];
    if (ownerName != null && ownerName is String) {
      print('✅ Found owner_name: $ownerName');
      return ownerName;
    }
    
    // Try username
    final username = widget.pin['username'];
    if (username != null && username is String) {
      print('✅ Found username: $username');
      return username;
    }
    
    print('❌ No valid owner information found, using Anonymous');
    return 'Anonymous';
  }
  String get description => widget.pin['caption'] ?? widget.pin['description'] ?? '';
  String get duration => widget.pin['duration'] ?? _formatDurationFromMs(widget.pin['duration_ms']);  // Use duration_ms as fallback
  String get createdAt => widget.pin['created_at'] ?? widget.pin['dateCreated'] ?? '';
  String get service => widget.pin['service'] ?? 'spotify';
  
  // Interaction counts with direct field access
  int get likeCount => widget.pin['like_count'] ?? 0;
  int get collectCount => widget.pin['collect_count'] ?? widget.pin['collectionCount'] ?? 0;
  int get viewCount => widget.pin['view_count'] ?? 0;
  int get shareCount => widget.pin['share_count'] ?? 0;
  
  // Other pin properties with direct access
  bool get isPrivate => widget.pin['is_private'] ?? false;
  double get distance => widget.pin['distance']?.toDouble() ?? 0.0;
  String get trackUrl => widget.pin['track_url'] ?? '';
  String get album => widget.pin['album'] ?? widget.pin['track_album'] ?? '';
  String get rarity => widget.pin['rarity'] ?? 'common';
  int get skin => widget.pin['skin'] ?? 1;
  double get auraRadius => widget.pin['aura_radius']?.toDouble() ?? 50.0;
  double get scale => widget.pin['scale']?.toDouble() ?? 1.0;
  
  // Format relative time directly from string
  String _formatRelativeTime(String dateStr) {
    if (dateStr.isEmpty) return 'Recently';
    try {
      final date = DateTime.parse(dateStr);
      final now = DateTime.now();
      final difference = now.difference(date);
      
      if (difference.inDays == 0) {
        if (difference.inHours == 0) {
          return '${difference.inMinutes} minutes ago';
        }
        return '${difference.inHours} hours ago';
      } else if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} days ago';
      } else {
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      }
    } catch (e) {
      return 'Recently';
    }
  }

  // Helper method to format duration from milliseconds
  String _formatDurationFromMs(dynamic durationMs) {
    if (durationMs == null) return '';
    final ms = durationMs is int ? durationMs : int.tryParse(durationMs.toString()) ?? 0;
    final minutes = (ms / 60000).floor();
    final seconds = ((ms % 60000) / 1000).floor();
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    // Log the pin details in the same format as the API response
    print('✅ Pin Details:');
    print('  - ID: ${widget.pin['id']}');
    print('  - Title: ${widget.pin['track_title'] ?? widget.pin['title']}');
    print('  - Location: lat: ${widget.pin['latitude'] ?? widget.pin['location']?['coordinates']?[1]}, lng: ${widget.pin['longitude'] ?? widget.pin['location']?['coordinates']?[0]}');
    print('  - Track: ${widget.pin['track_title']} by ${widget.pin['track_artist']}');
    print('  - Owner: ${ownerName}');
    print('  - Created: ${_formatRelativeTime(createdAt)}');
    print('  - Caption: ${widget.pin['caption']}');
    print('  - Service: ${widget.pin['service']}');
    print('  - Aura Radius: ${widget.pin['aura_radius']}');
    print('  - Distance: ${widget.pin['distance']}m');
    print('  - Interactions: ${widget.pin['interaction_count']}');
    
    return Consumer2<MapProvider, AuthProvider>(
      builder: (context, mapProvider, authProvider, child) {
        final bool isOwner = widget.pin['userId'] == authProvider.currentUser?.id;
        final bool canCollect = !isOwner && !widget.pin['isCollected'] && widget.pin['isWithinRange'];
        
        return DraggableScrollableSheet(
          initialChildSize: 0.4,
          minChildSize: 0.2,
          maxChildSize: 0.9,
          builder: (_, scrollController) {
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Column(
                children: [
                  // Drag handle
                  Container(
                    width: 40,
                    height: 5,
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  
                  // Content
                  Expanded(
                    child: ListView(
                      controller: scrollController,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      children: [
                        // Header with title and actions
                        _buildHeader(isOwner, mapProvider),
                        const SizedBox(height: 16),
                        
                        // Music track card
                        _buildMusicTrackCard(),
                        const SizedBox(height: 24),
                        
                        // Description if present
                        if (description.isNotEmpty) ...[
                          Text(
                            description,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 24),
                        ],
                        
                        // Pin metadata
                        _buildMetadataSection(),
                        const SizedBox(height: 16),
                        
                        // Action buttons
                        _buildActionButtons(canCollect, mapProvider),
                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
  
  // Header with title and actions
  Widget _buildHeader(bool isOwner, MapProvider mapProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Title
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    isPrivate ? Icons.lock : Icons.public,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    isPrivate ? 'Private pin' : 'Public pin',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getRarityColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _getRarityColor().withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      rarity.toUpperCase(),
                      style: TextStyle(
                        fontSize: 12,
                        color: _getRarityColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // Actions menu (if owner)
        if (isOwner)
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              if (value == 'edit') {
                // TODO: Implement edit functionality
              } else if (value == 'delete') {
                _showDeleteConfirmation(context, mapProvider);
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 20),
                    SizedBox(width: 8),
                    Text('Edit pin'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete pin', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
      ],
    );
  }
  
  Color _getRarityColor() {
    switch (rarity) {
      case 'legendary':
        return Colors.amber;
      case 'epic':
        return Colors.purple;
      case 'rare':
        return Colors.blue;
      case 'uncommon':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
  
  // Music track card with play controls
  Widget _buildMusicTrackCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    _buildServiceIcon(service),
                    const SizedBox(width: 8),
                    
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'by $artist',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[700],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (album.isNotEmpty)
                            Text(
                              'from $album',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                    
                    // Duration badge
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        duration,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton.icon(
                      onPressed: _togglePlayState,
                      icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
                      label: Text(_isPlaying ? 'Pause' : 'Play Preview'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  // Metadata section with direct data access
  Widget _buildMetadataSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Pin Details',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        _buildMetadataItem(
          icon: Icons.person,
          label: 'Dropped by',
          value: ownerName,
        ),
        
        _buildMetadataItem(
          icon: Icons.access_time,
          label: 'Dropped',
          value: _formatRelativeTime(createdAt),
        ),

        _buildMetadataItem(
          icon: Icons.location_on,
          label: 'Location',
          value: widget.pin['locationName'] ?? 'Nearby',
        ),
        
        _buildMetadataItem(
          icon: Icons.music_note,
          label: 'Duration',
          value: duration,
        ),
        
        _buildMetadataItem(
          icon: Icons.remove_red_eye,
          label: 'Views',
          value: viewCount.toString(),
        ),
        
        _buildMetadataItem(
          icon: Icons.bookmark,
          label: 'Collections',
          value: collectCount.toString(),
        ),
        
        // Only show distance if it's greater than 0
        if (distance > 0)
          _buildMetadataItem(
            icon: Icons.near_me,
            label: 'Distance',
            value: distance < 1000 
              ? '${distance.toStringAsFixed(0)}m away'
              : '${(distance / 1000).toStringAsFixed(2)}km away',
          ),

        // Show exact coordinates if available
        if (widget.pin['latitude'] != null && widget.pin['longitude'] != null)
          _buildMetadataItem(
            icon: Icons.gps_fixed,
            label: 'Coordinates',
            value: '${widget.pin['latitude'].toStringAsFixed(6)}, ${widget.pin['longitude'].toStringAsFixed(6)}',
          ),
      ],
    );
  }
  
  // Metadata item
  Widget _buildMetadataItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
  
  // Action buttons
  Widget _buildActionButtons(bool canCollect, MapProvider mapProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        // Like button
        _buildActionButton(
          icon: Icons.favorite,
          color: Colors.red,
          label: 'Like',
          count: likeCount,
          onPressed: () {
            // Toggle like
            mapProvider.toggleLikePin(widget.pin['id']);
          },
        ),
        
        // Collect button
        _buildActionButton(
          icon: Icons.bookmark,
          color: Theme.of(context).primaryColor,
          label: 'Collect',
          count: collectCount,
          onPressed: canCollect 
            ? () => mapProvider.collectPin(widget.pin['id']) 
            : null,
        ),
        
        // Share button
        _buildActionButton(
          icon: Icons.share,
          color: Colors.grey[700]!,
          label: 'Share',
          count: shareCount,
          onPressed: () {
            // TODO: Implement share functionality
          },
        ),
      ],
    );
  }
  
  // Action button
  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required String label,
    required int count,
    VoidCallback? onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              '$label ($count)',
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Service icon
  Widget _buildServiceIcon(String serviceType) {
    IconData iconData;
    Color iconColor;
    
    switch (serviceType.toLowerCase()) {
      case 'spotify':
        iconData = Icons.music_note;
        iconColor = Colors.green;
        break;
      case 'apple':
        iconData = Icons.music_note;
        iconColor = Colors.red;
        break;
      case 'soundcloud':
        iconData = Icons.music_note;
        iconColor = Colors.orange;
        break;
      default:
        iconData = Icons.music_note;
        iconColor = Colors.grey;
    }
    
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(iconData, color: iconColor, size: 20),
    );
  }
  
  // Toggle play state
  void _togglePlayState() {
    setState(() {
      _isPlaying = !_isPlaying;
    });
    // TODO: Implement actual music playback
  }
  
  // Show delete confirmation dialog
  Future<void> _showDeleteConfirmation(BuildContext context, MapProvider mapProvider) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete pin?'),
        content: const Text(
          'This will permanently delete this pin. This action cannot be undone.'
        ),
        actions: [
          TextButton(
            child: const Text('CANCEL'),
            onPressed: () => Navigator.pop(context),
          ),
          TextButton(
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
            onPressed: () async {
              Navigator.pop(context); // Close dialog
              
              final success = await mapProvider.deletePin(widget.pin['id']);
              if (success) {
                Navigator.pop(context); // Close bottom sheet
                
                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Pin deleted successfully')),
                );
              } else {
                // Show error
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Failed to delete pin. Please try again.')),
                );
              }
            },
          ),
        ],
      ),
    );
  }
} 