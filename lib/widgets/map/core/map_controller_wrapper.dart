import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math';
import 'package:flutter/scheduler.dart';

import 'map_constants.dart';
import '../../../config/constants.dart';
import '../animations/zoom_animation.dart';

/// Wrapper around the flutter_map MapController that adds additional functionality
/// and error handling
class MapControllerWrapper with ChangeNotifier {
  // The underlying flutter_map controller
  final MapController? _mapController;
  
  // Map state
  LatLng _currentCenter = LatLng(AppConstants.defaultLatitude, AppConstants.defaultLongitude);
  double _currentZoom = MapConstants.defaultZoom;
  double _currentRotation = 0.0;
  LatLngBounds _visibleBounds = LatLngBounds(
    LatLng(AppConstants.defaultLatitude - 0.05, AppConstants.defaultLongitude - 0.05),
    LatLng(AppConstants.defaultLatitude + 0.05, AppConstants.defaultLongitude + 0.05)
  );
  bool _isMoving = false;
  bool _isZooming = false;
  bool _isInitialized = false;
  
  // Animation controllers
  ZoomAnimation? _zoomAnimation;
  TickerProvider? _tickerProvider;
  
  // Event tracking
  final StreamController<MapEvent> _localEventController = StreamController<MapEvent>.broadcast();
  StreamSubscription<MapEvent>? _mapEventSubscription;
  
  // Error handling
  String? _lastErrorMessage;
  
  // Movement debounce timer
  Timer? _movementDebounceTimer;
  
  // UBER-STYLE OPTIMIZATION: Track last frame time for smooth animation
  int _lastFrameTime = 0;
  bool _hasScheduledFrameUpdate = false;
  
  // PERFORMANCE OPTIMIZATION: Cache transformed points to avoid recalculation during movement
  final Map<LatLng, Point<double>> _pointCache = {};
  
  // UBER-STYLE OPTIMIZATION: Movement prediction for responsive UI
  LatLng? _predictedCenter;
  double? _predictedZoom;
  
  /// Constructor
  MapControllerWrapper({
    MapController? mapController,
    LatLng? initialCenter,
    double initialZoom = MapConstants.defaultZoom,
  }) : _mapController = mapController {
    if (initialCenter != null) {
      _currentCenter = initialCenter;
    }
    _currentZoom = initialZoom;
    
    _setupEventStream();
  }
  
  /// Initialize animations - must be called with a valid TickerProvider
  void initializeAnimations(TickerProvider tickerProvider) {
    _tickerProvider = tickerProvider;
    
    // Initialize zoom animation
    _zoomAnimation = ZoomAnimation(
      vsync: tickerProvider,
      initialZoom: _currentZoom,
      onZoomChanged: (zoom) {
        // Apply new zoom to map
        if (!_isMoving) {
          _mapController!.move(_currentCenter, zoom);
          _currentZoom = zoom;
        }
      },
      minZoom: MapConstants.minZoom,
      maxZoom: MapConstants.maxZoom,
    );
  }
  
  /// Access to the underlying MapController
  MapController? get controller => _mapController;
  
  /// Current camera position
  MapCamera get camera => _mapController!.camera;
  
  /// Current map center
  LatLng get center => _currentCenter;
  
  /// Current zoom level
  double get zoom => _currentZoom;
  
  /// Current rotation
  double get rotation => _currentRotation;
  
  /// Current visible bounds
  LatLngBounds get visibleBounds => _visibleBounds;
  
  /// Whether the map is currently moving
  bool get isMoving => _isMoving;
  
  /// Whether the map is currently zooming
  bool get isZooming => _isZooming;
  
  /// Whether the map is initialized
  bool get isInitialized => _isInitialized;
  
  /// Last error message
  String? get lastErrorMessage => _lastErrorMessage;
  
  /// Local event stream that can be modified by this wrapper
  Stream<MapEvent> get mapEventStream => _localEventController.stream;
  
  /// Camera prediction for ultra-responsive UI
  LatLng get predictedCenter => _predictedCenter ?? _currentCenter;
  double get predictedZoom => _predictedZoom ?? _currentZoom;
  
  /// Move the map to the specified position
  void moveTo({
    required LatLng position,
    double? zoom,
    bool animate = false,
    Duration animationDuration = MapConstants.cameraMoveAnimationDuration,
  }) {
    try {
      if (animate && _zoomAnimation != null && _tickerProvider != null) {
        // Use ZoomAnimation for smooth transitions
        if (zoom != null) {
          _zoomAnimation!.animateToWithDuration(zoom, animationDuration);
        }
        
        // Animate position (could be enhanced with TranslationAnimation in the future)
        _mapController!.move(
          position,
          zoom ?? _currentZoom,
        );
      } else {
        // Instant move
        _mapController!.move(
          position,
          zoom ?? _currentZoom,
        );
      }
      
      // Update local state
      _currentCenter = position;
      if (zoom != null) _currentZoom = zoom;
      _visibleBounds = _mapController!.camera.visibleBounds;
      
      // Notify listeners about the change
      notifyListeners();
    } catch (e) {
      _lastErrorMessage = 'Error moving map: $e';
      debugPrint('${MapConstants.logTag}: $_lastErrorMessage');
    }
  }
  
  /// Zoom in by one level
  void zoomIn() {
    if (_zoomAnimation != null) {
      _zoomAnimation!.zoomIn();
    } else {
      moveTo(position: _currentCenter, zoom: _currentZoom + 1.0);
    }
  }
  
  /// Zoom out by one level
  void zoomOut() {
    if (_zoomAnimation != null) {
      _zoomAnimation!.zoomOut();
    } else {
      moveTo(position: _currentCenter, zoom: _currentZoom - 1.0);
    }
  }
  
  /// Update zoom level directly without animation
  void updateZoom(double zoomLevel) {
    try {
      // Ensure zoom is within bounds
      final boundedZoom = zoomLevel.clamp(
        MapConstants.minZoom, 
        MapConstants.maxZoom
      );
      
      // Update internal state
      _currentZoom = boundedZoom;
      
      // Sync zoom animation with current zoom if available
      if (_zoomAnimation != null) {
        _zoomAnimation!.setZoom(boundedZoom);
      }
      
      // Notify listeners
      notifyListeners();
    } catch (e) {
      _lastErrorMessage = 'Error updating zoom: $e';
      debugPrint('${MapConstants.logTag}: $_lastErrorMessage');
    }
  }
  
  /// Set the rotation of the map
  void setRotation(double angle) {
    try {
      // Update rotation
      _mapController!.rotate(angle);
      
      // Notify listeners about the change
      notifyListeners();
    } catch (e) {
      _lastErrorMessage = 'Error rotating map: $e';
      debugPrint('${MapConstants.logTag}: $_lastErrorMessage');
    }
  }
  
  /// Transform a LatLng to a screen point with caching for performance
  Point<double>? latLngToScreenPoint(LatLng latLng) {
    if (_mapController == null) return null;
    
    // UBER-STYLE OPTIMIZATION: Use cached point if available during movement
    if (_isMoving && _pointCache.containsKey(latLng)) {
      return _pointCache[latLng];
    }
    
    try {
      final point = _mapController!.camera.latLngToScreenPoint(latLng);
      
      // Cache the point for reuse during movement
      if (_isMoving) {
        _pointCache[latLng] = point;
      }
      
      return point;
    } catch (e) {
      _lastErrorMessage = 'Error transforming point: $e';
      debugPrint('${MapConstants.logTag}: $_lastErrorMessage');
      return null;
    }
  }
  
  /// UBER-STYLE OPTIMIZATION: Batch transform multiple points at once
  /// for enhanced performance during rendering
  List<Point<double>?> batchLatLngToScreenPoint(List<LatLng> latLngs) {
    final camera = _mapController?.camera;
    if (camera == null) return List.filled(latLngs.length, null);
    
    return latLngs.map((latLng) {
      // Use cached point if available during movement
      if (_isMoving && _pointCache.containsKey(latLng)) {
        return _pointCache[latLng];
      }
      
      try {
        final point = camera.latLngToScreenPoint(latLng);
        
        // Cache the point for reuse during movement
        if (_isMoving) {
          _pointCache[latLng] = point;
        }
        
        return point;
      } catch (e) {
        return null;
      }
    }).toList();
  }
  
  /// Get the detail level based on the current zoom
  int getDetailLevel() {
    if (_currentZoom < MapConstants.lowDetailThreshold) {
      return MapConstants.lowDetail;
    } else if (_currentZoom < MapConstants.mediumDetailThreshold) {
      return MapConstants.mediumDetail;
    } else {
      return MapConstants.highDetail;
    }
  }
  
  /// Setup the map event stream
  void _setupEventStream() {
    if (_mapController == null) return;
    
    _mapEventSubscription?.cancel();
    _mapEventSubscription = _mapController!.mapEventStream.listen(_handleMapEvent);
  }
  
  /// Handle map events with frame-synchronized updates for perfect layer sync
  void _handleMapEvent(MapEvent event) {
    if (event is MapEventMoveStart) {
      _isMoving = true;
      
      // CRITICAL FIX: Clear point cache at the start of movement
      _pointCache.clear();
      
      // Update position immediately for perfect synchronization
      _updatePosition();
      
      // CRITICAL FIX: Only notify listeners if position actually changed
      if (_hasPositionChanged()) {
        Future.microtask(() => notifyListeners());
      }
    } else if (event is MapEventMove) {
      _isMoving = true;
      
      // CRITICAL FIX: Only predict next position during actual panning (not zooming)
      if (!_isZooming) {
        _predictNextCameraPosition(event);
      }
      
      // Update current position immediately (atomic update)
      _updatePosition();
      
      // CRITICAL FIX: Use more efficient frame updates during panning
      if (!_isZooming) {
        _scheduleEfficientFrameUpdate();
      } else {
        // During zoom operations, use normal frame updates
        _scheduleFrameUpdate();
      }
    } else if (event is MapEventMoveEnd) {
      _isMoving = false;
      _isZooming = false;
      
      // Reset predictions on move end
      _predictedCenter = null;
      _predictedZoom = null;
      
      // Update position immediately
      _updatePosition();
      
      // Clear point cache after movement ends
      _pointCache.clear();
      
      // Notify listeners directly - no need for debouncing at movement end
      notifyListeners();
    }
    
    // For movement events that may not be picked up by the standard handlers
    if (event is MapEventRotateStart || 
        event is MapEventRotateEnd || 
        event is MapEventRotate) {
      _updateVisibleBounds();
      
      // Fix event rotation access - get rotation through camera
      if (event is MapEventRotate && _mapController?.camera != null) {
        _currentRotation = _mapController!.camera.rotation;
      }
      
      notifyListeners();
    }
    
    // Handle initialization - replace MapEventMapLoad with MapEventMoveEnd
    // Flutter Map 6.x doesn't have MapEventMapLoad
    if (!_isInitialized && event is MapEventMoveEnd) {
      _isInitialized = true;
      
      // Update initial state
      if (_mapController?.camera != null) {
        _currentCenter = _mapController!.camera.center;
        _currentZoom = _mapController!.camera.zoom;
        _updateVisibleBounds();
      }
      
      // Schedule a microtask to ensure the map is properly initialized
      SchedulerBinding.instance.scheduleFrameCallback((_) {
        _updateVisibleBounds();
        notifyListeners();
      });
    }
  }
  
  /// CRITICAL FIX: Check if position has changed significantly enough to warrant an update
  bool _hasPositionChanged() {
    if (_mapController?.camera == null) return false;
    
    final camera = _mapController!.camera;
    final latDiff = (_currentCenter.latitude - camera.center.latitude).abs();
    final lngDiff = (_currentCenter.longitude - camera.center.longitude).abs();
    
    // Only consider it changed if position differs by more than a small threshold
    return latDiff > 0.00001 || lngDiff > 0.00001;
  }
  
  /// CRITICAL FIX: More efficient frame update scheduling for panning
  void _scheduleEfficientFrameUpdate() {
    if (!_hasScheduledFrameUpdate) {
      _hasScheduledFrameUpdate = true;
      
      // Use a more efficient update mechanism during panning
      SchedulerBinding.instance.scheduleFrameCallback((_) {
        _hasScheduledFrameUpdate = false;
        
        // Only notify if position has changed significantly
        if (_hasPositionChanged()) {
          notifyListeners();
        }
      });
    }
  }
  
  /// Schedule a standard frame update for non-panning operations
  void _scheduleFrameUpdate() {
    if (!_hasScheduledFrameUpdate) {
      _hasScheduledFrameUpdate = true;
      
      // Use SchedulerBinding to synchronize with the rendering pipeline
      SchedulerBinding.instance.scheduleFrameCallback((_) {
        _hasScheduledFrameUpdate = false;
        notifyListeners();
      });
    }
  }
  
  /// UBER-STYLE OPTIMIZATION: Predict the next camera position for ultra-responsive UI
  void _predictNextCameraPosition(MapEventMove event) {
    // Only predict during drag events to avoid interference with other gestures
    if (event.source == MapEventSource.dragEnd || event.source == MapEventSource.onDrag) {
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      if (_lastFrameTime > 0) {
        final deltaTime = (currentTime - _lastFrameTime) / 1000.0; // in seconds
        if (deltaTime > 0 && deltaTime < 0.1) { // Only predict if frame time is reasonable
          final previousCenter = _currentCenter;
          _updatePosition(); // Get current position
          
          // Calculate velocity with smoothing
          final latVelocity = (_currentCenter.latitude - previousCenter.latitude) / deltaTime;
          final lngVelocity = (_currentCenter.longitude - previousCenter.longitude) / deltaTime;
          
          // Predict just 1 frame ahead (about 16ms at 60fps) to reduce overshooting
          _predictedCenter = LatLng(
            _currentCenter.latitude + latVelocity * 0.016,
            _currentCenter.longitude + lngVelocity * 0.016,
          );
        }
      }
      _lastFrameTime = currentTime;
    } else {
      // Reset predictions for other move sources
      _predictedCenter = null;
      _predictedZoom = null;
    }
  }
  
  /// Update the current position components in a single atomic operation
  void _updatePosition({bool initial = false}) {
    if (_mapController != null) {
      try {
        // PERFECT SYNC: Get all camera values in one atomic operation to prevent tearing
        final camera = _mapController!.camera;
        _currentCenter = camera.center;
        _currentZoom = camera.zoom;
        _currentRotation = camera.rotation;
        _visibleBounds = camera.visibleBounds;
        
        if (initial) {
          // Initial notification after position is set
          notifyListeners();
        }
      } catch (e) {
        _lastErrorMessage = 'Error updating position: $e';
        debugPrint('${MapConstants.logTag}: $_lastErrorMessage');
      }
    }
  }
  
  /// Update the visible bounds
  void _updateVisibleBounds() {
    if (_mapController == null || _mapController!.camera == null) return;
    
    try {
      LatLngBounds? bounds = _mapController!.camera.visibleBounds;
      if (bounds != null) {
        _visibleBounds = bounds;
      }
    } catch (e) {
      debugPrint('MapControllerWrapper: Error updating visible bounds: $e');
    }
  }
  
  /// Clean up resources
  @override
  void dispose() {
    _mapEventSubscription?.cancel();
    _localEventController.close();
    _movementDebounceTimer?.cancel();
    
    if (_zoomAnimation != null) {
      _zoomAnimation!.dispose();
    }
    
    super.dispose();
  }
} 