import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'zoom_state_manager.dart';
import 'zoom_animation_controller.dart';
import 'zoom_rendering_strategy.dart';
import 'map_constants.dart';

/// Main coordinator for all zoom-related functionality.
/// This class ties together state management, animations,
/// and rendering strategy to provide a cohesive zoom experience.
class MapZoomCoordinator {
  // Core components
  final ZoomStateManager stateManager;
  final ZoomAnimationController animationController;
  final ZoomRenderingStrategy renderingStrategy;
  
  // The Flutter Map controller
  final MapController mapController;
  
  // TickerProvider for animations
  final TickerProvider tickerProvider;
  
  // Constructor
  MapZoomCoordinator({
    required this.stateManager,
    required this.animationController,
    required this.renderingStrategy,
    required this.mapController,
    required this.tickerProvider,
  }) {
    // Set up listeners to keep components in sync
    _setupListeners();
  }
  
  // Factory constructor to create all dependencies
  factory MapZoomCoordinator.create({
    required TickerProvider tickerProvider,
    required MapController mapController,
    double initialZoom = MapConstants.defaultZoom,
    bool use2DDuringZoom = true,
  }) {
    // Create the state manager
    final stateManager = ZoomStateManager(
      initialZoom: initialZoom,
      minZoom: MapConstants.minZoom,
      maxZoom: MapConstants.maxZoom,
    );
    
    // Create the animation controller
    final animationController = ZoomAnimationController(
      tickerProvider: tickerProvider,
      stateManager: stateManager,
    );
    
    // Create the rendering strategy
    final renderingStrategy = ZoomRenderingStrategy(
      stateManager: stateManager,
      use2DDuringZoom: use2DDuringZoom,
    );
    
    return MapZoomCoordinator(
      stateManager: stateManager,
      animationController: animationController,
      renderingStrategy: renderingStrategy,
      mapController: mapController,
      tickerProvider: tickerProvider,
    );
  }
  
  // Set up listeners to keep the map controller in sync with our state
  void _setupListeners() {
    // Listen for zoom changes from the state manager
    stateManager.onZoomStream.listen((zoom) {
      // Only update the map controller if it's not already at this zoom
      if ((mapController.camera.zoom - zoom).abs() > 0.01) {
        mapController.move(mapController.camera.center, zoom);
      }
    });
    
    // Listen for map events to update our state
    mapController.mapEventStream.listen(_handleMapEvent);
  }
  
  // Handle map events from the Flutter Map controller
  void _handleMapEvent(MapEvent event) {
    if (event is MapEventMoveStart) {
      // Map movement started
      if (!stateManager.isZooming) {
        stateManager.beginZoomOperation(isProgrammatic: false);
      }
    } else if (event is MapEventMove) {
      // Check if zoom has changed significantly
      final newZoom = mapController.camera.zoom;
      if ((stateManager.zoom - newZoom).abs() > 0.01) {
        stateManager.updateZoom(newZoom, fromUser: true);
      }
    } else if (event is MapEventMoveEnd) {
      // Movement ended
      if (stateManager.isZooming) {
        stateManager.completeZoomUpdate();
      }
    }
  }
  
  // Public API
  
  /// Get the tilt angle for current state
  double getTilt() {
    return renderingStrategy.getTiltForCurrentState();
  }
  
  /// Get detail level for current state
  int getDetailLevel() {
    return renderingStrategy.getDetailLevel();
  }
  
  /// Check if we should show 3D buildings in the current state
  bool shouldShow3DBuildings() {
    return renderingStrategy.shouldShow3DBuildings();
  }
  
  /// Get full rendering parameters
  Map<String, dynamic> getRenderingParameters() {
    return renderingStrategy.getRenderingParameters();
  }
  
  /// Add a listener for zoom changes
  void addZoomListener(void Function(double) listener) {
    stateManager.onZoomStream.listen(listener);
  }
  
  /// Add a listener for zoom level changes
  void addZoomLevelListener(void Function(int) listener) {
    stateManager.onZoomLevelStream.listen(listener);
  }
  
  /// Add a listener for zooming state changes
  void addZoomingStateListener(void Function(bool) listener) {
    stateManager.onZoomingStateStream.listen(listener);
  }
  
  /// Zoom in by one level
  void zoomIn() {
    animationController.zoomIn();
  }
  
  /// Zoom out by one level
  void zoomOut() {
    animationController.zoomOut();
  }
  
  /// Zoom to a specific level
  void zoomToLevel(int level) {
    animationController.jumpToZoomLevel(level);
  }
  
  /// Animate to a specific zoom value
  void zoomTo(double zoom, {Duration? duration}) {
    animationController.animateTo(zoom, duration: duration);
  }
  
  /// Move to a location and zoom level
  void moveTo({
    required LatLng location,
    double? zoom,
    Duration? duration,
  }) {
    if (zoom != null) {
      // If we have a new zoom, start an animation
      stateManager.beginZoomOperation(isProgrammatic: true);
      
      // If duration provided, animate
      if (duration != null) {
        // Set up a controller for this specific animation
        final controller = AnimationController(
          duration: duration,
          vsync: tickerProvider,
        );
        
        // Create animation for the zoom
        final zoomAnimation = Tween<double>(
          begin: stateManager.zoom,
          end: zoom,
        ).animate(CurvedAnimation(
          parent: controller,
          curve: Curves.easeInOut,
        ));
        
        // Update on each frame
        controller.addListener(() {
          mapController.move(location, zoomAnimation.value);
        });
        
        // Clean up when done
        controller.addStatusListener((status) {
          if (status == AnimationStatus.completed || status == AnimationStatus.dismissed) {
            stateManager.completeZoomUpdate();
            controller.dispose();
          }
        });
        
        // Start animation
        controller.forward();
      } else {
        // No duration, move immediately
        mapController.move(location, zoom);
        stateManager.updateZoom(zoom, fromUser: false);
        stateManager.completeZoomUpdate();
      }
    } else {
      // Just move without changing zoom
      mapController.move(location, stateManager.zoom);
    }
  }
  
  /// Toggle between 2D and 3D modes
  void toggle2D3DMode() {
    stateManager.toggle2D3DMode();
  }
  
  /// Set whether to use 2D during zooming
  void setUse2DDuringZoom(bool use2D) {
    renderingStrategy.setUse2DDuringZoom(use2D);
  }
  
  /// Dispose resources
  void dispose() {
    animationController.dispose();
    stateManager.dispose();
  }
} 