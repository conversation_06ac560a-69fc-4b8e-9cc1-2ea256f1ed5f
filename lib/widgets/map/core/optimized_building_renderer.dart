import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart';
import 'package:flutter/rendering.dart';

/// Manages efficient rendering of buildings with optimizations
class OptimizedBuildingRenderer extends RenderBox {
  // Rendering settings
  static const int _maxBatchSize = 1000;
  static const double _minBuildingSize = 4.0;
  static const double _maxBuildingSize = 100.0;

  // Shader settings
  static const String _vertexShader = '''
    uniform mat4 uProjectionMatrix;
    uniform float uDetailLevel;
    
    attribute vec3 position;
    attribute vec3 normal;
    attribute mat4 instanceMatrix;
    
    varying vec3 vNormal;
    varying float vHeight;
    
    void main() {
      // Apply instance transform
      vec4 worldPosition = instanceMatrix * vec4(position * uDetailLevel, 1.0);
      
      // Project to screen space
      gl_Position = uProjectionMatrix * worldPosition;
      
      // Pass data to fragment shader
      vNormal = (instanceMatrix * vec4(normal, 0.0)).xyz;
      vHeight = worldPosition.y;
    }
  ''';

  static const String _fragmentShader = '''
    uniform vec3 uLightDirection;
    uniform vec3 uBuildingColor;
    uniform float uAmbientLight;
    
    varying vec3 vNormal;
    varying float vHeight;
    
    void main() {
      // Calculate lighting
      vec3 normal = normalize(vNormal);
      float diffuse = max(dot(normal, normalize(uLightDirection)), 0.0);
      float light = uAmbientLight + (1.0 - uAmbientLight) * diffuse;
      
      // Height-based color variation
      vec3 color = uBuildingColor * (0.8 + 0.2 * vHeight);
      
      // Output final color
      gl_FragColor = vec4(color * light, 1.0);
    }
  ''';

  // Render state
  late final _BuildingShader _shader;
  Matrix4 _projectionMatrix = Matrix4.identity();
  double _detailLevel = 1.0;
  List<_BuildingBatch> _batches = [];
  bool _needsGeometryUpdate = true;

  // Performance tracking
  int _lastFrameCount = 0;
  Duration _lastFrameTime = Duration.zero;
  double _averageFrameTime = 0.0;

  OptimizedBuildingRenderer() {
    _initializeShader();
  }

  /// Initialize the building shader
  void _initializeShader() {
    _shader = _BuildingShader(
      vertexShader: _vertexShader,
      fragmentShader: _fragmentShader,
    );
  }

  /// Update building geometries
  void updateGeometries(List<_BuildingGeometry> geometries, double detailLevel) {
    _detailLevel = detailLevel;
    
    // Split into batches for efficient rendering
    _batches = _createBatches(geometries);
    _needsGeometryUpdate = true;
    
    markNeedsPaint();
  }

  /// Create optimized batches of buildings
  List<_BuildingBatch> _createBatches(List<_BuildingGeometry> geometries) {
    final batches = <_BuildingBatch>[];
    
    // Sort by material for efficient state changes
    geometries.sort((a, b) => a.materialId.compareTo(b.materialId));
    
    var currentBatch = _BuildingBatch(
      materialId: geometries.first.materialId,
      geometries: [],
    );
    
    for (final geometry in geometries) {
      // Start new batch if material changes or batch is full
      if (geometry.materialId != currentBatch.materialId ||
          currentBatch.geometries.length >= _maxBatchSize) {
        batches.add(currentBatch);
        currentBatch = _BuildingBatch(
          materialId: geometry.materialId,
          geometries: [],
        );
      }
      
      currentBatch.geometries.add(geometry);
    }
    
    // Add final batch
    if (currentBatch.geometries.isNotEmpty) {
      batches.add(currentBatch);
    }
    
    return batches;
  }

  @override
  void performLayout() {
    size = constraints.biggest;
  }

  @override
  bool get sizedByParent => true;

  @override
  void paint(PaintingContext context, Offset offset) {
    final canvas = context.canvas;
    
    // Skip if no batches
    if (_batches.isEmpty) return;
    
    // Update projection matrix
    _updateProjectionMatrix();
    
    // Start frame timing
    final frameStart = DateTime.now();
    
    // Bind shader
    _shader.bind();
    _shader.setProjectionMatrix(_projectionMatrix);
    _shader.setDetailLevel(_detailLevel);
    
    // Draw batches
    for (final batch in _batches) {
      _drawBatch(canvas, batch);
    }
    
    // Unbind shader
    _shader.unbind();
    
    // Update performance metrics
    _updatePerformanceMetrics(frameStart);
  }

  /// Update the projection matrix
  void _updateProjectionMatrix() {
    final aspectRatio = size.width / size.height;
    _projectionMatrix = Matrix4.perspective(
      45.0 * (pi / 180.0), // FOV
      aspectRatio,
      0.1, // near
      1000.0, // far
    );
  }

  /// Draw a batch of buildings
  void _drawBatch(Canvas canvas, _BuildingBatch batch) {
    // Set material uniforms
    _shader.setMaterial(batch.materialId);
    
    // Draw all geometries in batch
    for (final geometry in batch.geometries) {
      // Skip if too small or too large
      final screenSize = _calculateScreenSize(geometry);
      if (screenSize < _minBuildingSize || screenSize > _maxBuildingSize) {
        continue;
      }
      
      // Draw building
      _drawGeometry(canvas, geometry);
    }
  }

  /// Calculate screen-space size of building
  double _calculateScreenSize(BuildingGeometry geometry) {
    // Project bounding sphere to screen space
    final center = geometry.transform.getTranslation();
    final radius = geometry.boundingSphere;
    
    final projected = _projectionMatrix.transform3(center);
    final projectedRadius = radius * projected.z;
    
    return projectedRadius * size.width;
  }

  /// Draw a single building geometry
  void _drawGeometry(Canvas canvas, _BuildingGeometry geometry) {
    // Set instance transform
    _shader.setInstanceMatrix(geometry.transform);
    
    // Draw vertices
    canvas.drawVertices(
      geometry.vertices,
      BlendMode.srcOver,
      Paint()..shader = _shader,
    );
  }

  /// Update performance metrics
  void _updatePerformanceMetrics(DateTime frameStart) {
    final frameTime = DateTime.now().difference(frameStart);
    
    // Update running average
    _averageFrameTime = (_averageFrameTime * 0.9) + (frameTime.inMicroseconds * 0.1);
    
    // Update frame count
    _lastFrameCount++;
    _lastFrameTime = frameTime;
  }

  /// Get current performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    return {
      'frameTime': _averageFrameTime / 1000.0, // Convert to milliseconds
      'frameCount': _lastFrameCount,
      'batchCount': _batches.length,
      'geometryCount': _batches.fold<int>(
        0,
        (sum, batch) => sum + batch.geometries.length,
      ),
    };
  }

  @override
  void dispose() {
    _shader.dispose();
    super.dispose();
  }
}

/// Represents a batch of buildings sharing the same material
class _BuildingBatch {
  final String materialId;
  final List<_BuildingGeometry> geometries;

  _BuildingBatch({
    required this.materialId,
    required this.geometries,
  });
}

/// Represents a building's geometry and transform
class _BuildingGeometry {
  final String materialId;
  final Float32List vertices;
  final Matrix4 transform;
  final double boundingSphere;

  _BuildingGeometry({
    required this.materialId,
    required this.vertices,
    required this.transform,
    required this.boundingSphere,
  });
}

/// Custom shader for building rendering
class _BuildingShader {
  // Shader program and uniforms
  late final int _program;
  late final int _projectionMatrixLocation;
  late final int _detailLevelLocation;
  late final int _instanceMatrixLocation;
  
  _BuildingShader({
    required String vertexShader,
    required String fragmentShader,
  }) {
    _program = _createShaderProgram(vertexShader, fragmentShader);
    _getUniformLocations();
  }

  /// Create and compile shader program
  int _createShaderProgram(String vertexShader, String fragmentShader) {
    // This is a placeholder - actual implementation would use platform-specific graphics API
    return 0;
  }

  /// Get uniform locations
  void _getUniformLocations() {
    // This is a placeholder - actual implementation would use platform-specific graphics API
    _projectionMatrixLocation = 0;
    _detailLevelLocation = 1;
    _instanceMatrixLocation = 2;
  }

  /// Bind shader for use
  void bind() {
    // This is a placeholder - actual implementation would use platform-specific graphics API
  }

  /// Unbind shader
  void unbind() {
    // This is a placeholder - actual implementation would use platform-specific graphics API
  }

  /// Set projection matrix uniform
  void setProjectionMatrix(Matrix4 matrix) {
    // This is a placeholder - actual implementation would use platform-specific graphics API
  }

  /// Set detail level uniform
  void setDetailLevel(double level) {
    // This is a placeholder - actual implementation would use platform-specific graphics API
  }

  /// Set instance matrix uniform
  void setInstanceMatrix(Matrix4 matrix) {
    // This is a placeholder - actual implementation would use platform-specific graphics API
  }

  /// Set material uniforms
  void setMaterial(String materialId) {
    // This is a placeholder - actual implementation would use platform-specific graphics API
  }

  /// Clean up resources
  void dispose() {
    // This is a placeholder - actual implementation would use platform-specific graphics API
  }
} 