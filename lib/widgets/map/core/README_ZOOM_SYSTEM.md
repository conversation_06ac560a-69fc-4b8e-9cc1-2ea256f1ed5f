# Consolidated Zoom Management System

This document describes the new consolidated zoom management system for BOPMaps, which replaces the scattered approach previously used across multiple components.

## Overview

The new zoom system follows a clean architecture with clear separation of concerns:

1. **ZoomStateManager** - Single source of truth for all zoom-related state
2. **ZoomAnimationController** - Handles smooth animations between zoom levels
3. **ZoomRenderingStrategy** - Determines how content is rendered at different zoom levels
4. **MapZoomCoordinator** - Coordinates all zoom components and provides a unified API

## Key Benefits

- **Consistent State**: All components now work with the same zoom state
- **Clear Responsibility**: Each component has a well-defined role
- **Simplified Debugging**: Zoom issues can be isolated to specific components
- **Enhanced Performance**: Optimized rendering during zoom operations
- **Configurable Behavior**: Easy to adjust zoom behavior through configuration
- **2D During Zoom**: Option to automatically switch to 2D during zoom operations

## Components

### 1. ZoomStateManager

The central state store that maintains:
- Current zoom value
- Current zoom level (1-5)
- Zooming state (in progress or not)
- Zoom mode (2D/3D)
- Zoom velocity

```dart
// Example: Getting zoom state
final currentZoom = zoomStateManager.zoom;
final isZooming = zoomStateManager.isZooming;
final zoomLevel = zoomStateManager.zoomLevel;
```

### 2. ZoomAnimationController

Handles smooth transitions between zoom levels with:
- Configurable animation durations
- Custom animation curves
- Proper state updates
- Animation lifecycle management

```dart
// Example: Animating to a zoom level
zoomAnimationController.animateTo(15.0);
zoomAnimationController.zoomIn();
zoomAnimationController.zoomOut();
```

### 3. ZoomRenderingStrategy

Controls how map content is rendered at different zoom levels:
- Detail level adjustment
- 2D/3D mode switching during zoom
- Simplified rendering during rapid zoom

```dart
// Example: Getting rendering information
final detailLevel = zoomRenderingStrategy.getDetailLevel();
final should3D = zoomRenderingStrategy.shouldShow3DBuildings();
final params = zoomRenderingStrategy.getRenderingParameters();
```

### 4. MapZoomCoordinator

The main API for zoom operations:
- Coordinates all zoom components
- Provides a simplified public API
- Manages interaction with the map controller

```dart
// Example: Using the coordinator
mapZoomCoordinator.zoomIn();
mapZoomCoordinator.zoomOut();
mapZoomCoordinator.toggle2D3DMode();
mapZoomCoordinator.zoomToLevel(4);
```

## 2D During Zoom Feature

A key enhancement in this system is the ability to automatically switch to 2D mode during zoom operations, then restore 3D mode once zooming completes. This solves the issue of tiles disappearing during fast zooming in 2.5D mode.

To enable this feature:

```dart
// Enable 2D during zoom
zoomRenderingStrategy.setUse2DDuringZoom(true);

// Or through the coordinator
mapZoomCoordinator.setUse2DDuringZoom(true);
```

## Migration Guide

### Step 1: Initialize the New System

In your `FlutterMapWidgetState` class:

```dart
late MapZoomCoordinator _zoomCoordinator;

@override
void initState() {
  super.initState();
  
  // Initialize the zoom coordinator
  _zoomCoordinator = ZoomSystemIntegration.initializeInWidget(
    tickerProvider: this,
    mapController: _mapController,
    initialZoom: _currentZoom,
    setState: setState,
    tiltAnimController: _animationController,
    tiltAnimation: _tiltAnimation,
    mapSettings: mapSettings,
  );
}
```

### Step 2: Replace Zoom Operations

Replace direct zoom operations with calls to the coordinator:

```dart
// Before
void _zoomIn() {
  final currentZoom = _mapController.camera.zoom;
  final targetZoom = math.min(currentZoom + 1.0, MapConstants.maxZoom);
  setState(() { _isZooming = true; });
  _mapController.move(_currentCenter, targetZoom);
}

// After
void _zoomIn() {
  _zoomCoordinator.zoomIn();
}
```

### Step 3: Use the Rendering Strategy

Update your rendering code to use the strategy:

```dart
// Before
final effectiveDetailLevel = _isZooming
    ? math.max(1, _stateManager.buildingDetailLevel - 1)
    : _stateManager.buildingDetailLevel;

// After
final effectiveDetailLevel = _zoomCoordinator.getDetailLevel();
```

### Step 4: Update Tilt Handling

Use the coordinator for tilt management:

```dart
// Before
_tiltAngle = _is2DModeActive ? 0.0 : 0.5;

// After
final tiltValue = _zoomCoordinator.getTilt();
```

## Example Usage

See `examples/zoom_system_usage_example.dart` for a complete implementation example.

## Performance Optimizations

The new system includes several performance optimizations:

1. **Reduced Detail During Zoom**: Automatically reduces rendering detail during zooming
2. **2D Mode During Zoom**: Optionally switches to 2D during zooming to improve performance
3. **Debounced Updates**: Limits state updates during rapid zoom changes
4. **Predictive Loading**: Anticipates zoom end state and prepares rendering parameters

## Backward Compatibility

To ease migration, a compatibility layer is provided:

```dart
// Get the compatibility layer
final compatLayer = ZoomSystemIntegration.createCompatibilityLayer(_zoomCoordinator);

// Use legacy methods
compatLayer.zoomIn();
compatLayer.zoomOut();
final currentZoom = compatLayer.currentZoom;
``` 