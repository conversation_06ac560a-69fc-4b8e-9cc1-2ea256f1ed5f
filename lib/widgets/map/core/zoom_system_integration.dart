import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'map_zoom_coordinator.dart';
import 'zoom_state_manager.dart';
import 'zoom_rendering_strategy.dart';
import 'map_constants.dart';
import '../../../providers/map_settings_provider.dart';

/// This class helps integrate the new consolidated zoom system
/// with the existing map widget structure. It serves as a bridge
/// during the transition to the new architecture.
class ZoomSystemIntegration {
  /// Initialize the new zoom system in the FlutterMapWidgetState
  static MapZoomCoordinator initializeInWidget({
    required TickerProvider tickerProvider,
    required MapController mapController,
    required double initialZoom,
    required void Function(void Function()) setState,
    required AnimationController tiltAnimController,
    required Animation<double> tiltAnimation,
    required MapSettingsProvider mapSettings,
  }) {
    // Create the zoom coordinator
    final zoomCoordinator = MapZoomCoordinator.create(
      tickerProvider: tickerProvider,
      mapController: mapController,
      initialZoom: initialZoom,
      use2DDuringZoom: true, // Enable the 2D-during-zoom feature
    );
    
    // Set up listeners to update the tilt animation based on the new system
    zoomCoordinator.stateManager.onZoomingStateStream.listen((isZooming) {
      _updateTiltAnimation(
        isZooming: isZooming,
        setState: setState,
        tiltAnimController: tiltAnimController,
        zoomCoordinator: zoomCoordinator,
        targetTiltValue: isZooming ? 0.0 : zoomCoordinator.getTilt(),
      );
    });
    
    // Listen for zoom mode changes from the state manager
    zoomCoordinator.stateManager.onZoomModeStream.listen((mode) {
      _updateTiltAnimation(
        isZooming: zoomCoordinator.stateManager.isZooming,
        setState: setState,
        tiltAnimController: tiltAnimController,
        zoomCoordinator: zoomCoordinator, 
        targetTiltValue: mode == ZoomMode.mode3D ? zoomCoordinator.getTilt() : 0.0,
      );
    });
    
    // Configure the strategy based on map settings
    _configureFromSettings(zoomCoordinator, mapSettings);
    
    return zoomCoordinator;
  }
  
  /// Update the tilt animation
  static void _updateTiltAnimation({
    required bool isZooming,
    required void Function(void Function()) setState,
    required AnimationController tiltAnimController,
    required MapZoomCoordinator zoomCoordinator,
    required double targetTiltValue,
  }) {
    setState(() {
      // Create a new tilt animation with the target value
      final newTiltAnimation = Tween<double>(
        begin: tiltAnimController.value * tiltAnimController.upperBound,
        end: targetTiltValue,
      ).animate(CurvedAnimation(
        parent: tiltAnimController,
        curve: Curves.easeOutCubic,
      ));
      
      // Update the animation controller
      tiltAnimController.value = 0.0;
      tiltAnimController.duration = isZooming ? 
          const Duration(milliseconds: 150) : // Faster for zoom start
          MapConstants.tiltAnimationDuration;   // Normal for other changes
      
      // Start the animation
      tiltAnimController.forward();
    });
  }
  
  /// Configure the zoom system from map settings
  static void _configureFromSettings(
    MapZoomCoordinator zoomCoordinator, 
    MapSettingsProvider mapSettings,
  ) {
    // Set initial mode based on 3D buildings setting
    final initialMode = mapSettings.use3DBuildings ? ZoomMode.mode3D : ZoomMode.mode2D;
    zoomCoordinator.stateManager.setZoomMode(initialMode);
    
    // Other settings could be configured here as needed
  }
  
  /// Update the zoom system when map settings change
  static void updateFromSettings(
    MapZoomCoordinator zoomCoordinator,
    MapSettingsProvider mapSettings,
  ) {
    final newMode = mapSettings.use3DBuildings ? ZoomMode.mode3D : ZoomMode.mode2D;
    
    // Only update if there's a change
    if ((newMode == ZoomMode.mode3D) != (zoomCoordinator.stateManager.zoomMode == ZoomMode.mode3D)) {
      zoomCoordinator.stateManager.setZoomMode(newMode);
    }
  }
  
  /// Create a backward compatibility layer for older code
  static ZoomBackwardCompatibility createCompatibilityLayer(MapZoomCoordinator coordinator) {
    return ZoomBackwardCompatibility(coordinator);
  }
}

/// A class that provides backward compatibility with the old zoom system API
class ZoomBackwardCompatibility {
  final MapZoomCoordinator _coordinator;
  
  ZoomBackwardCompatibility(this._coordinator);
  
  // Legacy API methods
  void zoomIn() => _coordinator.zoomIn();
  void zoomOut() => _coordinator.zoomOut();
  
  // Legacy property getters
  double get currentZoom => _coordinator.stateManager.zoom;
  int get buildingDetailLevel => _coordinator.getDetailLevel();
  bool get isZooming => _coordinator.stateManager.isZooming;
  
  // Legacy tilt access
  double getTilt() => _coordinator.getTilt();
} 