import 'dart:async';
import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import '../../../config/constants.dart';
import '../utils/pan_math_utils.dart';

/// Manages the pan state and optimizations for the map.
/// 
/// This class is responsible for:
/// 1. Tracking pan velocity and direction
/// 2. Managing pan-specific render modes
/// 3. Coordinating with zoom and tilt systems
/// 4. Optimizing performance during pan operations
class PanStateManager {
  // Callbacks
  final Function(PanStartDetails)? onPanStart;
  final Function(PanUpdateDetails)? onPanUpdate;
  final Function(PanEndDetails)? onPanEnd;
  
  // Constants for pan behavior
  static const double _velocityThreshold = 1000.0; // pixels per second
  static const double _highVelocityThreshold = 2000.0;
  static const Duration _panTimeout = Duration(milliseconds: 150);
  static const int _velocityHistorySize = 5;
  
  // State
  bool _isActivelyPanning = false;
  DateTime _lastPanTime = DateTime.now();
  vector.Vector2 _lastPanPosition = vector.Vector2.zero();
  vector.Vector2 _panVelocity = vector.Vector2.zero();
  List<vector.Vector2> _velocityHistory = [];
  Timer? _panTimeoutTimer;
  
  // Performance mode state
  bool _isInHighPerformanceMode = false;
  bool _isInEmergencyMode = false;
  int _rapidPanCount = 0;
  DateTime _lastRapidPanTime = DateTime.now();
  
  // Getters
  bool get isActivelyPanning => _isActivelyPanning;
  vector.Vector2 get velocity => _panVelocity;
  bool get isHighVelocityPan => _panVelocity.length > _highVelocityThreshold;
  bool get shouldUseSimplifiedRendering => _isInHighPerformanceMode || _isInEmergencyMode;
  
  // Constructor
  PanStateManager({
    this.onPanStart,
    this.onPanUpdate,
    this.onPanEnd,
  });
  
  /// Handles the start of a pan operation
  void handlePanStart(PanStartDetails details) {
    _isActivelyPanning = true;
    _lastPanTime = DateTime.now();
    _lastPanPosition = vector.Vector2(details.localPosition.dx, details.localPosition.dy);
    _velocityHistory.clear();
    
    // Reset performance modes
    _isInHighPerformanceMode = false;
    _checkEmergencyMode();
    
    onPanStart?.call(details);
  }
  
  /// Handles updates during a pan operation
  void handlePanUpdate(PanUpdateDetails details) {
    // Calculate time delta
    final now = DateTime.now();
    final timeDelta = now.difference(_lastPanTime).inMicroseconds / 1000000.0;
    
    // Update velocity
    final currentPosition = vector.Vector2(details.localPosition.dx, details.localPosition.dy);
    final displacement = currentPosition - _lastPanPosition;
    final instantVelocity = displacement.scaled(1.0 / timeDelta);
    
    // Update velocity history
    _velocityHistory.add(instantVelocity);
    if (_velocityHistory.length > _velocityHistorySize) {
      _velocityHistory.removeAt(0);
    }
    
    // Calculate smoothed velocity
    _panVelocity = _calculateSmoothedVelocity();
    
    // Check for high velocity pan
    _updatePerformanceMode();
    
    // Reset timeout timer
    _panTimeoutTimer?.cancel();
    _panTimeoutTimer = Timer(_panTimeout, _handlePanTimeout);
    
    // Update state
    _lastPanTime = now;
    _lastPanPosition = currentPosition;
    
    onPanUpdate?.call(details);
  }
  
  /// Handles the end of a pan operation
  void handlePanEnd(PanEndDetails details) {
    _isActivelyPanning = false;
    _panTimeoutTimer?.cancel();
    
    // Gradually restore normal rendering mode
    _schedulePerformanceModeReset();
    
    onPanEnd?.call(details);
  }
  
  /// Calculates smoothed velocity from history
  vector.Vector2 _calculateSmoothedVelocity() {
    if (_velocityHistory.isEmpty) return vector.Vector2.zero();
    
    final smoothed = vector.Vector2.zero();
    double totalWeight = 0.0;
    
    for (int i = 0; i < _velocityHistory.length; i++) {
      final weight = (i + 1) / _velocityHistory.length;
      smoothed.add(_velocityHistory[i].scaled(weight));
      totalWeight += weight;
    }
    
    return smoothed.scaled(1.0 / totalWeight);
  }
  
  /// Updates the performance mode based on current pan behavior
  void _updatePerformanceMode() {
    final velocity = _panVelocity.length;
    
    if (velocity > _highVelocityThreshold) {
      _isInHighPerformanceMode = true;
      _checkRapidPanCount();
    }
  }
  
  /// Checks and updates rapid pan count for emergency mode
  void _checkRapidPanCount() {
    final now = DateTime.now();
    final timeSinceLastRapid = now.difference(_lastRapidPanTime);
    
    if (timeSinceLastRapid.inMilliseconds < 100) {
      _rapidPanCount++;
      if (_rapidPanCount > 10) {
        _enableEmergencyMode();
      }
    } else {
      _rapidPanCount = 1;
    }
    
    _lastRapidPanTime = now;
  }
  
  /// Enables emergency mode for extreme performance optimization
  void _enableEmergencyMode() {
    if (!_isInEmergencyMode) {
      _isInEmergencyMode = true;
      Timer(const Duration(seconds: 5), () {
        _isInEmergencyMode = false;
        _rapidPanCount = 0;
      });
    }
  }
  
  /// Handles pan timeout
  void _handlePanTimeout() {
    _isActivelyPanning = false;
    _schedulePerformanceModeReset();
  }
  
  /// Schedules a reset of performance mode
  void _schedulePerformanceModeReset() {
    if (_isInHighPerformanceMode) {
      Timer(const Duration(milliseconds: 300), () {
        _isInHighPerformanceMode = false;
      });
    }
  }
  
  /// Checks if emergency mode should be enabled
  void _checkEmergencyMode() {
    final now = DateTime.now();
    if (now.difference(_lastRapidPanTime).inSeconds < 1) {
      _rapidPanCount++;
      if (_rapidPanCount > 15) {
        _enableEmergencyMode();
      }
    } else {
      _rapidPanCount = 0;
    }
  }
  
  /// Disposes of resources
  void dispose() {
    _panTimeoutTimer?.cancel();
    _velocityHistory.clear();
  }
} 