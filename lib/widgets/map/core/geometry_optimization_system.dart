import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart';

/// Manages geometry optimizations for building rendering
class GeometryOptimizationSystem {
  // LOD thresholds
  static const int _highDetailVertexCount = 500;
  static const int _mediumDetailVertexCount = 250;
  static const int _lowDetailVertexCount = 100;

  // Culling settings
  static const double _cullingFrustumMargin = 50.0;
  static const double _occlusionCullingThreshold = 0.1;

  // Instancing settings
  static const int _maxInstancesPerBatch = 1000;
  static const int _instanceDataSize = 16; // 4x4 matrix

  // Cache for optimized geometries
  final Map<String, List<Vector3>> _geometryCache = {};
  final Map<String, List<int>> _indexCache = {};

  /// Simplify building geometry based on detail level
  List<Vector3> simplifyGeometry(List<Vector3> vertices, double detailLevel) {
    if (detailLevel >= 0.8) {
      return vertices; // Use full detail
    }

    final targetVertexCount = _calculateTargetVertexCount(detailLevel);
    return _decimateGeometry(vertices, targetVertexCount);
  }

  /// Calculate target vertex count based on detail level
  int _calculateTargetVertexCount(double detailLevel) {
    if (detailLevel >= 0.6) {
      return _highDetailVertexCount;
    } else if (detailLevel >= 0.3) {
      return _mediumDetailVertexCount;
    }
    return _lowDetailVertexCount;
  }

  /// Decimate geometry to target vertex count
  List<Vector3> _decimateGeometry(List<Vector3> vertices, int targetCount) {
    if (vertices.length <= targetCount) {
      return vertices;
    }

    // Use vertex clustering for simplification
    final simplified = <Vector3>[];
    final gridSize = (vertices.length / targetCount).ceil();
    
    for (var i = 0; i < vertices.length; i += gridSize) {
      final cluster = vertices.sublist(
        i,
        (i + gridSize) > vertices.length ? vertices.length : i + gridSize,
      );
      simplified.add(_calculateClusterCenter(cluster));
    }

    return simplified;
  }

  /// Calculate center point of a vertex cluster
  Vector3 _calculateClusterCenter(List<Vector3> cluster) {
    final sum = Vector3.zero();
    for (final vertex in cluster) {
      sum.add(vertex);
    }
    return sum.scaled(1.0 / cluster.length);
  }

  /// Prepare geometry for instanced rendering
  Map<String, dynamic> prepareForInstancing(
    List<Vector3> vertices,
    List<Matrix4> transforms,
  ) {
    final instanceData = Float32List(_maxInstancesPerBatch * _instanceDataSize);
    final instanceCount = transforms.length.clamp(0, _maxInstancesPerBatch);

    for (var i = 0; i < instanceCount; i++) {
      final offset = i * _instanceDataSize;
      final transform = transforms[i];
      transform.storage.asMap().forEach((index, value) {
        instanceData[offset + index] = value;
      });
    }

    return {
      'vertices': vertices,
      'instanceData': instanceData,
      'instanceCount': instanceCount,
    };
  }

  /// Perform view frustum culling
  List<Matrix4> performFrustumCulling(
    List<Matrix4> transforms,
    Matrix4 viewProjection,
    Rect viewport,
  ) {
    final culledTransforms = <Matrix4>[];
    final frustum = _calculateFrustum(viewProjection, viewport);

    for (final transform in transforms) {
      if (_isInFrustum(transform, frustum)) {
        culledTransforms.add(transform);
      }
    }

    return culledTransforms;
  }

  /// Calculate view frustum planes
  List<Vector4> _calculateFrustum(Matrix4 viewProjection, Rect viewport) {
    // Extract frustum planes from view-projection matrix
    final planes = <Vector4>[];
    
    // Left plane
    planes.add(Vector4(
      viewProjection.row0.w + viewProjection.row0.x,
      viewProjection.row1.w + viewProjection.row1.x,
      viewProjection.row2.w + viewProjection.row2.x,
      viewProjection.row3.w + viewProjection.row3.x,
    ));

    // Right plane
    planes.add(Vector4(
      viewProjection.row0.w - viewProjection.row0.x,
      viewProjection.row1.w - viewProjection.row1.x,
      viewProjection.row2.w - viewProjection.row2.x,
      viewProjection.row3.w - viewProjection.row3.x,
    ));

    // Add margin to frustum
    for (var plane in planes) {
      plane.scale(_cullingFrustumMargin);
    }

    return planes;
  }

  /// Check if object is in view frustum
  bool _isInFrustum(Matrix4 transform, List<Vector4> frustumPlanes) {
    final position = Vector3(
      transform.getTranslation().x,
      transform.getTranslation().y,
      transform.getTranslation().z,
    );

    for (final plane in frustumPlanes) {
      if (plane.dot(Vector4(position.x, position.y, position.z, 1.0)) < 0) {
        return false;
      }
    }

    return true;
  }

  /// Perform occlusion culling
  List<Matrix4> performOcclusionCulling(
    List<Matrix4> transforms,
    List<double> depths,
  ) {
    if (transforms.length != depths.length) {
      throw ArgumentError('Transforms and depths must have same length');
    }

    // Sort by depth
    final indexedTransforms = List<MapEntry<int, double>>.generate(
      transforms.length,
      (i) => MapEntry(i, depths[i]),
    )..sort((a, b) => a.value.compareTo(b.value));

    // Keep only visible objects
    final visibleTransforms = <Matrix4>[];
    var lastDepth = 0.0;

    for (final entry in indexedTransforms) {
      if (entry.value - lastDepth > _occlusionCullingThreshold) {
        visibleTransforms.add(transforms[entry.key]);
        lastDepth = entry.value;
      }
    }

    return visibleTransforms;
  }

  /// Clear geometry cache
  void clearCache() {
    _geometryCache.clear();
    _indexCache.clear();
  }
} 