import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'map_constants.dart';
import '../map_caching/zoom_level_manager.dart';

/// Represents the current zoom state of the map
class ZoomState {
  final double zoom;
  final int zoomLevel;
  final bool isZooming;
  final bool isProgrammatic;
  final ZoomMode zoomMode;
  final double zoomVelocity;

  const ZoomState({
    required this.zoom,
    required this.zoomLevel,
    required this.isZooming,
    this.isProgrammatic = false,
    required this.zoomMode,
    this.zoomVelocity = 0.0,
  });

  /// Initial state constructor
  factory ZoomState.initial() {
    return const ZoomState(
      zoom: MapConstants.defaultZoom,
      zoomLevel: 3, // Default to regional view
      isZooming: false,
      isProgrammatic: false,
      zoomMode: ZoomMode.mode3D,
      zoomVelocity: 0.0,
    );
  }

  /// Create a copy with some properties changed
  ZoomState copyWith({
    double? zoom,
    int? zoomLevel,
    bool? isZooming,
    bool? isProgrammatic,
    ZoomMode? zoomMode,
    double? zoomVelocity,
  }) {
    return ZoomState(
      zoom: zoom ?? this.zoom,
      zoomLevel: zoomLevel ?? this.zoomLevel,
      isZooming: isZooming ?? this.isZooming,
      isProgrammatic: isProgrammatic ?? this.isProgrammatic,
      zoomMode: zoomMode ?? this.zoomMode,
      zoomVelocity: zoomVelocity ?? this.zoomVelocity,
    );
  }
}

/// Represents the 2D/3D viewing mode of the map
enum ZoomMode {
  mode2D,
  mode3D,
  modeTransition,
}

/// A consolidated manager that serves as the single source of truth
/// for all zoom-related state across the application.
class ZoomStateManager extends ChangeNotifier {
  // Private state
  ZoomState _state = ZoomState.initial();
  
  // Tracks the last time zoom was updated for velocity calculations
  DateTime? _lastZoomUpdateTime;
  
  // Streaming controllers for individual property changes
  final _zoomController = StreamController<double>.broadcast();
  final _zoomLevelController = StreamController<int>.broadcast();
  final _zoomingStateController = StreamController<bool>.broadcast();
  final _zoomModeController = StreamController<ZoomMode>.broadcast();
  
  // The consolidated state notifier (for widgets that need the full state)
  final ValueNotifier<ZoomState> zoomStateNotifier = ValueNotifier<ZoomState>(ZoomState.initial());
  
  // Zoom level mapping (matches ZoomLevelManager's implementation)
  final Map<int, double> _zoomLevelToZoomValue = {
    1: 5.0,  // World view
    2: 9.0,  // Continental view
    3: 12.0, // Regional view
    4: 15.0, // Local area view
    5: 18.0, // Fully zoomed view
  };
  
  // Dependencies - using the existing ZoomLevelManager for backward compatibility
  // This will eventually be phased out as functionality moves into this class
  final ZoomLevelManager _zoomLevelManager = ZoomLevelManager();
  
  // Boundary constraints
  final double _minZoom;
  final double _maxZoom;
  
  // Callbacks to notify external systems of changes
  final void Function(double)? onZoomChanged;
  final void Function(int)? onZoomLevelChanged;
  final void Function(bool)? onZoomingStateChanged;
  final void Function(ZoomMode)? onZoomModeChanged;
  
  // Constructor
  ZoomStateManager({
    double initialZoom = MapConstants.defaultZoom,
    double minZoom = MapConstants.minZoom,
    double maxZoom = MapConstants.maxZoom,
    this.onZoomChanged,
    this.onZoomLevelChanged,
    this.onZoomingStateChanged,
    this.onZoomModeChanged,
  }) : _minZoom = minZoom,
       _maxZoom = maxZoom {
    // Initialize with provided zoom
    _updateZoomValue(initialZoom, notify: false);
    
    // Ensure the legacy ZoomLevelManager is in sync
    _zoomLevelManager.updateZoomLevel(initialZoom);
  }
  
  // Getters for individual properties
  double get zoom => _state.zoom;
  int get zoomLevel => _state.zoomLevel;
  bool get isZooming => _state.isZooming;
  bool get isProgrammaticZoom => _state.isProgrammatic;
  ZoomMode get zoomMode => _state.zoomMode;
  double get zoomVelocity => _state.zoomVelocity;
  
  // Stream getters for reactive UI components
  Stream<double> get onZoomStream => _zoomController.stream;
  Stream<int> get onZoomLevelStream => _zoomLevelController.stream;
  Stream<bool> get onZoomingStateStream => _zoomingStateController.stream;
  Stream<ZoomMode> get onZoomModeStream => _zoomModeController.stream;
  
  // Is the map in 2D mode?
  bool get is2DMode => _state.zoomMode == ZoomMode.mode2D;
  
  // Get the tilt value based on current mode and zoom level
  double getTiltForCurrentState() {
    // If zooming, optionally return 0 for 2D mode
    if (_state.isZooming && shouldUse2DDuringZoom) {
      return 0.0;
    }
    
    // Otherwise use normal tilt based on mode
    switch (_state.zoomMode) {
      case ZoomMode.mode2D:
        return 0.0;
      case ZoomMode.mode3D:
        return _getOptimalTiltForZoomLevel(_state.zoomLevel);
      case ZoomMode.modeTransition:
        // During transition, use an intermediate value
        return _getOptimalTiltForZoomLevel(_state.zoomLevel) * 0.5;
    }
  }
  
  // Config property to control behavior during zooming
  bool shouldUse2DDuringZoom = false;
  
  // Update the zoom value
  void _updateZoomValue(double newZoom, {bool notify = true}) {
    final oldZoom = _state.zoom;
    final oldZoomLevel = _state.zoomLevel;
    
    // Enforce bounds
    final boundedZoom = newZoom.clamp(_minZoom, _maxZoom);
    
    // Calculate velocity for physics effects
    double velocity = 0.0;
    if (_lastZoomUpdateTime != null) {
      final elapsed = DateTime.now().difference(_lastZoomUpdateTime!).inMilliseconds;
      if (elapsed > 0) {
        velocity = (boundedZoom - oldZoom) / elapsed * 1000; // Units: zoom/second
      }
    }
    _lastZoomUpdateTime = DateTime.now();
    
    // Determine new zoom level
    final newZoomLevel = _determineZoomLevel(boundedZoom);
    
    // Update the state
    _state = _state.copyWith(
      zoom: boundedZoom,
      zoomLevel: newZoomLevel,
      zoomVelocity: velocity,
    );
    
    // Update the legacy ZoomLevelManager for compatibility
    _zoomLevelManager.updateZoomLevel(boundedZoom);
    
    // Notify listeners and streams if requested
    if (notify) {
      if (boundedZoom != oldZoom) {
        _zoomController.add(boundedZoom);
        onZoomChanged?.call(boundedZoom);
      }
      
      if (newZoomLevel != oldZoomLevel) {
        _zoomLevelController.add(newZoomLevel);
        onZoomLevelChanged?.call(newZoomLevel);
      }
      
      // Update the consolidated state notifier
      zoomStateNotifier.value = _state;
      notifyListeners();
    }
  }
  
  // Begin a zoom operation
  void beginZoomOperation({bool isProgrammatic = false}) {
    if (!_state.isZooming) {
      _state = _state.copyWith(
        isZooming: true,
        isProgrammatic: isProgrammatic,
      );
      
      _zoomingStateController.add(true);
      onZoomingStateChanged?.call(true);
      zoomStateNotifier.value = _state;
      notifyListeners();
    }
  }
  
  // End a zoom operation
  void endZoomOperation() {
    if (_state.isZooming) {
      _state = _state.copyWith(
        isZooming: false,
        isProgrammatic: false,
        zoomVelocity: 0.0,
      );
      
      _zoomingStateController.add(false);
      onZoomingStateChanged?.call(false);
      zoomStateNotifier.value = _state;
      notifyListeners();
    }
  }
  
  // Set the zoom mode
  void setZoomMode(ZoomMode mode) {
    if (_state.zoomMode != mode) {
      _state = _state.copyWith(zoomMode: mode);
      
      _zoomModeController.add(mode);
      onZoomModeChanged?.call(mode);
      zoomStateNotifier.value = _state;
      notifyListeners();
      
      // Update the legacy ZoomLevelManager for compatibility
      _zoomLevelManager.toggle2DMode();
    }
  }
  
  // Toggle between 2D and 3D modes
  void toggle2D3DMode() {
    final newMode = _state.zoomMode == ZoomMode.mode3D ? 
      ZoomMode.mode2D : ZoomMode.mode3D;
    setZoomMode(newMode);
  }
  
  // Determine zoom level from zoom value
  int _determineZoomLevel(double zoom) {
    if (zoom <= 7.0) return 1;
    if (zoom <= 10.0) return 2;
    if (zoom <= 13.0) return 3;
    if (zoom <= 16.0) return 4;
    return 5;
  }
  
  // Get the optimal tilt for a zoom level
  double _getOptimalTiltForZoomLevel(int zoomLevel) {
    switch (zoomLevel) {
      case 1: return 0.0; // No tilt for world view
      case 2: return 0.2; // Slight tilt for continental
      case 3: return 0.4; // More noticeable tilt for regional
      case 4: return 0.6; // Significant tilt for local area
      case 5: return 0.8; // Maximum tilt for fully zoomed
      default: return 0.5;
    }
  }
  
  // Get zoom value for a specific level
  double getZoomValueForLevel(int level) {
    return _zoomLevelToZoomValue[level.clamp(1, 5)] ?? 12.0;
  }
  
  // Public API for updating zoom
  void updateZoom(double zoom, {bool fromUser = false}) {
    beginZoomOperation(isProgrammatic: !fromUser);
    _updateZoomValue(zoom);
  }
  
  // Jump to a specific zoom level
  void jumpToZoomLevel(int level) {
    if (level < 1) level = 1;
    if (level > 5) level = 5;
    
    final targetZoom = getZoomValueForLevel(level);
    updateZoom(targetZoom, fromUser: false);
    
    // For backward compatibility
    _zoomLevelManager.jumpToZoomLevel(level);
  }
  
  // Zoom in by one level
  void zoomIn() {
    updateZoom(zoom + 1.0, fromUser: false);
  }
  
  // Zoom out by one level
  void zoomOut() {
    updateZoom(zoom - 1.0, fromUser: false);
  }
  
  // Zoom in by a specific amount
  void zoomInBy(double amount) {
    if (amount <= 0) return;
    updateZoom(zoom + amount, fromUser: false);
  }
  
  // Zoom out by a specific amount
  void zoomOutBy(double amount) {
    if (amount <= 0) return;
    updateZoom(zoom - amount, fromUser: false);
  }
  
  // Mark the end of a zoom update
  void completeZoomUpdate() {
    endZoomOperation();
  }
  
  // Get optimized rendering parameters for the current state
  Map<String, dynamic> getRenderingParameters() {
    // Get params from legacy manager for consistency
    final params = _zoomLevelManager.getOptimizedRenderingParameters();
    
    // Override render3D based on current mode and zooming state
    if (shouldUse2DDuringZoom && _state.isZooming) {
      params['render3D'] = false;
    } else {
      params['render3D'] = _state.zoomMode == ZoomMode.mode3D;
    }
    
    return params;
  }
  
  // Clean up resources
  @override
  void dispose() {
    _zoomController.close();
    _zoomLevelController.close();
    _zoomingStateController.close();
    _zoomModeController.close();
    zoomStateNotifier.dispose();
    super.dispose();
  }
} 