import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:geolocator/geolocator.dart';

/// Constants used throughout the map implementation
class MapConstants {
  // Private constructor to prevent instantiation
  MapConstants._();
  
  // Default map position
  static const double defaultLatitude = 37.7749;
  static const double defaultLongitude = -122.4194;
  static const double defaultZoom = 15.7;
  
  // Map zoom limits
  static const double minZoom = 2.0;
  static const double maxZoom = 19.0;
  
  // Zoom levels for detail rendering
  static const double lowDetailThreshold = 14.0;
  static const double mediumDetailThreshold = 16.0;
  static const double highDetailThreshold = 17.0;
  
  // Detail levels
  static const int lowDetail = 1;
  static const int mediumDetail = 2;
  static const int highDetail = 3;
  
  // Animation durations
  static const Duration tiltAnimationDuration = Duration(milliseconds: 500);
  static const Duration zoomAnimationDuration = Duration(milliseconds: 300);
  static const Duration cameraMoveAnimationDuration = Duration(milliseconds: 500);
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  
  // Default map appearance
  static const double defaultTiltAngle = 0.4;
  static const double maxTiltAngle = 0.6;
  static const double defaultRotationAngle = 0.0;
  
  // Map movement debounce
  static const Duration mapMovementDebounceTime = Duration(milliseconds: 16);
  
  // Feature identification
  static const double featureIdentificationRadius = 0.0003; // ~30 meters
  
  // Tile error handling
  static const int maxFallbackLevels = 3;
  
  // Location tracking
  static const int maxLocationRetries = 3;
  static const Duration locationRetryDelay = Duration(seconds: 2);
  static const Duration locationTimeoutDuration = Duration(seconds: 5);
  
  // Location update distance
  static const double locationMinUpdateDistanceInMeters = 5.0;
  
  // Location accuracy - using Geolocator's LocationAccuracy enum
  static const LocationAccuracy defaultLocationAccuracy = LocationAccuracy.high;
  
  // Location error messages
  static const String locationPermissionDeniedMessage = "Location permission denied. Please enable location to see your position on the map.";
  static const String locationServicesDisabledMessage = "Location services are disabled. Please enable location in your device settings.";
  static const String locationTimeoutMessage = "Could not get your location. Please check your connection and try again.";
  
  // Startup performance timings
  static const Duration maxAllowableStartupTime = Duration(seconds: 3);
  static const Duration locationWarmupTime = Duration(seconds: 1);
  
  // Logging tag
  static String get logTag => 'MapWidget';
  
  // Debug info
  static bool get showDebugInfo => kDebugMode;
  
  // Default bounds (approximately 2km squared area)
  static LatLngBounds get defaultBounds => LatLngBounds(
    LatLng(defaultLatitude - 0.009, defaultLongitude - 0.009), // ~1km in each direction
    LatLng(defaultLatitude + 0.009, defaultLongitude + 0.009)  // ~1km in each direction
  );
} 