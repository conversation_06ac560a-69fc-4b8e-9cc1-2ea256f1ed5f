import 'package:flutter/material.dart';
import 'zoom_state_manager.dart';
import 'map_constants.dart';

/// A controller to handle smooth animations for zoom transitions.
/// This controller works with the ZoomStateManager to ensure
/// that animations stay in sync with the application state.
class ZoomAnimationController {
  // The ticker provider for animations
  final TickerProvider _tickerProvider;
  
  // The state manager to keep in sync
  final ZoomStateManager _stateManager;
  
  // Animation controllers
  late AnimationController _zoomAnimationController;
  late Animation<double> _zoomAnimation;
  
  // Constructor
  ZoomAnimationController({
    required TickerProvider tickerProvider,
    required ZoomStateManager stateManager,
  }) : _tickerProvider = tickerProvider,
       _stateManager = stateManager {
    _initializeAnimations();
  }
  
  // Initialize animations
  void _initializeAnimations() {
    // Set up the zoom animation controller
    _zoomAnimationController = AnimationController(
      vsync: _tickerProvider,
      duration: MapConstants.zoomAnimationDuration,
    );
    
    // Set up the initial animation (will be replaced in animateTo)
    _zoomAnimation = Tween<double>(
      begin: _stateManager.zoom,
      end: _stateManager.zoom,
    ).animate(CurvedAnimation(
      parent: _zoomAnimationController,
      curve: Curves.easeOutCubic,
    ));
    
    // Add listener to update the state manager
    _zoomAnimationController.addListener(_updateZoom);
    
    // Add status listener to handle animation completion
    _zoomAnimationController.addStatusListener(_handleAnimationStatus);
  }
  
  // Handle animation status changes
  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed || status == AnimationStatus.dismissed) {
      // Animation has finished, notify the state manager
      _stateManager.completeZoomUpdate();
    }
  }
  
  // Update zoom based on animation value
  void _updateZoom() {
    final newZoom = _zoomAnimation.value;
    // Only update if there's a meaningful change
    if ((_stateManager.zoom - newZoom).abs() > 0.01) {
      _stateManager.updateZoom(newZoom, fromUser: false);
    }
  }
  
  // Animate to a specific zoom level
  void animateTo(double targetZoom, {Duration? duration, Curve curve = Curves.easeOutCubic}) {
    // Skip if already at target
    if ((_stateManager.zoom - targetZoom).abs() < 0.01) return;
    
    // Stop any running animation
    if (_zoomAnimationController.isAnimating) {
      _zoomAnimationController.stop();
    }
    
    // Notify state manager that we're starting a zoom operation
    _stateManager.beginZoomOperation(isProgrammatic: true);
    
    // Set up the animation duration
    _zoomAnimationController.duration = duration ?? MapConstants.zoomAnimationDuration;
    
    // Create new animation
    _zoomAnimation = Tween<double>(
      begin: _stateManager.zoom,
      end: targetZoom,
    ).animate(CurvedAnimation(
      parent: _zoomAnimationController,
      curve: curve,
    ));
    
    // Start the animation
    _zoomAnimationController.forward(from: 0.0);
  }
  
  // Interrupt any current animation
  void cancelAnimation() {
    if (_zoomAnimationController.isAnimating) {
      _zoomAnimationController.stop();
      _stateManager.completeZoomUpdate();
    }
  }
  
  // Zoom in by one level
  void zoomIn({double amount = 1.0, Duration? duration}) {
    final targetZoom = _stateManager.zoom + amount;
    animateTo(targetZoom, duration: duration);
  }
  
  // Zoom out by one level
  void zoomOut({double amount = 1.0, Duration? duration}) {
    final targetZoom = _stateManager.zoom - amount;
    animateTo(targetZoom, duration: duration);
  }
  
  // Jump to a specific zoom level without animation
  void jumpToZoomLevel(int level) {
    // Stop any running animation
    cancelAnimation();
    
    // Get the zoom value for the level
    final targetZoom = _stateManager.getZoomValueForLevel(level);
    
    // Update the state manager directly
    _stateManager.updateZoom(targetZoom, fromUser: false);
    _stateManager.completeZoomUpdate();
  }
  
  // Dispose resources
  void dispose() {
    _zoomAnimationController.removeListener(_updateZoom);
    _zoomAnimationController.removeStatusListener(_handleAnimationStatus);
    _zoomAnimationController.dispose();
  }
  
  // Check if animation is in progress
  bool get isAnimating => _zoomAnimationController.isAnimating;
} 