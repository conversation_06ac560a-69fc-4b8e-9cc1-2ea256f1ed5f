import 'dart:async';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import '../map_layers/osm_data_processor.dart';

/// Manages progressive loading of map tiles with prioritization
class ProgressiveTileLoader {
  // Loading settings
  static const int _maxConcurrentLoads = 4;
  static const Duration _loadTimeout = Duration(seconds: 5);
  static const Duration _retryDelay = Duration(seconds: 1);
  static const int _maxRetries = 3;

  // Priority zones (in screen percentage from center)
  static const double _highPriorityZone = 0.3; // 30% from center
  static const double _mediumPriorityZone = 0.6; // 60% from center

  // Dependencies
  final OSMDataProcessor _dataProcessor;
  
  // State
  final Map<String, _TileLoadState> _loadingTiles = {};
  final List<_TileRequest> _loadQueue = [];
  int _activeLoads = 0;
  bool _isProcessingQueue = false;

  // Callbacks
  final Function(String, Map<String, dynamic>) onTileLoaded;
  final Function(String) onTileError;

  ProgressiveTileLoader({
    required OSMDataProcessor dataProcessor,
    required this.onTileLoaded,
    required this.onTileError,
  }) : _dataProcessor = dataProcessor;

  /// Request loading of a tile
  void requestTile({
    required String tileId,
    required LatLng southwest,
    required LatLng northeast,
    required Offset screenPosition,
    required Size screenSize,
    required String loadingPriority,
  }) {
    // Skip if already loading
    if (_loadingTiles.containsKey(tileId)) {
      return;
    }

    // Calculate priority
    final priority = _calculatePriority(screenPosition, screenSize, loadingPriority);

    // Create request
    final request = _TileRequest(
      tileId: tileId,
      southwest: southwest,
      northeast: northeast,
      priority: priority,
      retryCount: 0,
    );

    // Add to queue
    _addToQueue(request);
  }

  /// Calculate loading priority
  double _calculatePriority(Offset position, Size screenSize, String basePriority) {
    // Convert screen position to relative coordinates (-1 to 1)
    final relativeX = (position.dx / screenSize.width) * 2 - 1;
    final relativeY = (position.dy / screenSize.height) * 2 - 1;
    
    // Calculate distance from center
    final distanceFromCenter = (relativeX * relativeX + relativeY * relativeY).sqrt();

    // Base priority multiplier from loading priority
    double baseMultiplier;
    switch (basePriority) {
      case 'high':
        baseMultiplier = 1.0;
        break;
      case 'medium':
        baseMultiplier = 0.7;
        break;
      case 'low':
        baseMultiplier = 0.4;
        break;
      default:
        baseMultiplier = 0.5;
    }

    // Calculate zone multiplier
    double zoneMultiplier;
    if (distanceFromCenter <= _highPriorityZone) {
      zoneMultiplier = 1.0;
    } else if (distanceFromCenter <= _mediumPriorityZone) {
      zoneMultiplier = 0.7;
    } else {
      zoneMultiplier = 0.4;
    }

    return baseMultiplier * zoneMultiplier;
  }

  /// Add request to queue
  void _addToQueue(_TileRequest request) {
    // Find insertion point to maintain priority order
    final insertIndex = _loadQueue.indexWhere(
      (element) => element.priority < request.priority,
    );

    if (insertIndex == -1) {
      _loadQueue.add(request);
    } else {
      _loadQueue.insert(insertIndex, request);
    }

    // Start processing if not already running
    if (!_isProcessingQueue) {
      _processQueue();
    }
  }

  /// Process the load queue
  Future<void> _processQueue() async {
    if (_isProcessingQueue) return;
    _isProcessingQueue = true;

    while (_loadQueue.isNotEmpty && _activeLoads < _maxConcurrentLoads) {
      final request = _loadQueue.removeAt(0);
      _activeLoads++;

      // Start load with timeout
      _loadingTiles[request.tileId] = _TileLoadState(
        request: request,
        completer: Completer<void>(),
      );

      _loadTile(request).timeout(
        _loadTimeout,
        onTimeout: () => _handleLoadTimeout(request),
      );
    }

    _isProcessingQueue = false;
  }

  /// Load a single tile
  Future<void> _loadTile(_TileRequest request) async {
    try {
      // Load tile data
      final tileData = await _dataProcessor.fetchTileData(
        request.southwest,
        request.northeast,
      );

      // Process successful load
      if (!_loadingTiles.containsKey(request.tileId)) {
        return; // Request was cancelled
      }

      onTileLoaded(request.tileId, tileData);
      _loadingTiles.remove(request.tileId);
      _activeLoads--;
      
      // Continue processing queue
      _processQueue();
    } catch (e) {
      // Handle error
      _handleLoadError(request, e);
    }
  }

  /// Handle load timeout
  void _handleLoadTimeout(_TileRequest request) {
    if (!_loadingTiles.containsKey(request.tileId)) {
      return; // Request was cancelled
    }

    // Retry if possible
    if (request.retryCount < _maxRetries) {
      _retryLoad(request);
    } else {
      _handleLoadError(request, TimeoutException('Tile load timeout'));
    }
  }

  /// Handle load error
  void _handleLoadError(_TileRequest request, Object error) {
    if (!_loadingTiles.containsKey(request.tileId)) {
      return; // Request was cancelled
    }

    // Retry if possible
    if (request.retryCount < _maxRetries) {
      _retryLoad(request);
    } else {
      // Report error and clean up
      onTileError(request.tileId);
      _loadingTiles.remove(request.tileId);
      _activeLoads--;
      _processQueue();
    }
  }

  /// Retry loading a tile
  void _retryLoad(_TileRequest request) {
    final newRequest = _TileRequest(
      tileId: request.tileId,
      southwest: request.southwest,
      northeast: request.northeast,
      priority: request.priority,
      retryCount: request.retryCount + 1,
    );

    // Clean up old request
    _loadingTiles.remove(request.tileId);
    _activeLoads--;

    // Add back to queue after delay
    Future.delayed(_retryDelay, () {
      _addToQueue(newRequest);
    });
  }

  /// Cancel loading of a tile
  void cancelTile(String tileId) {
    _loadingTiles.remove(tileId);
    _loadQueue.removeWhere((request) => request.tileId == tileId);
  }

  /// Clear all pending loads
  void clear() {
    _loadQueue.clear();
    _loadingTiles.clear();
    _activeLoads = 0;
    _isProcessingQueue = false;
  }
}

/// Represents a tile loading request
class _TileRequest {
  final String tileId;
  final LatLng southwest;
  final LatLng northeast;
  final double priority;
  final int retryCount;

  _TileRequest({
    required this.tileId,
    required this.southwest,
    required this.northeast,
    required this.priority,
    required this.retryCount,
  });
}

/// Represents the loading state of a tile
class _TileLoadState {
  final _TileRequest request;
  final Completer<void> completer;

  _TileLoadState({
    required this.request,
    required this.completer,
  });
} 