import 'dart:async';
import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart';

/// Manages progressive Level of Detail (LOD) and performance optimizations during panning
class PanOptimizationSystem {
  // Performance thresholds
  static const double _lowVelocityThreshold = 100.0;
  static const double _mediumVelocityThreshold = 300.0;
  static const double _highVelocityThreshold = 500.0;

  // LOD settings
  static const double _maxBuildingDetail = 1.0;
  static const double _minBuildingDetail = 0.2;
  static const Duration _detailRestoreDelay = Duration(milliseconds: 150);

  // State
  double _currentDetailLevel = _maxBuildingDetail;
  bool _isPanning = false;
  Timer? _detailRestoreTimer;
  Vector2 _currentPanVelocity = Vector2.zero();

  // Callbacks
  final Function(double) onDetailLevelChanged;
  final Function(Map<String, dynamic>) onRenderingParamsChanged;

  PanOptimizationSystem({
    required this.onDetailLevelChanged,
    required this.onRenderingParamsChanged,
  });

  /// Handle pan start
  void onPanStart() {
    _isPanning = true;
    _detailRestoreTimer?.cancel();
    _updateOptimizations();
  }

  /// Handle pan update with velocity
  void onPanUpdate(Offset delta) {
    _currentPanVelocity = Vector2(delta.dx, delta.dy);
    _updateOptimizationsByVelocity();
  }

  /// Handle pan end
  void onPanEnd() {
    _isPanning = false;
    _currentPanVelocity = Vector2.zero();
    _scheduleDetailRestore();
  }

  /// Update optimizations based on current pan velocity
  void _updateOptimizationsByVelocity() {
    final speed = _currentPanVelocity.length;
    double targetDetail;

    if (speed < _lowVelocityThreshold) {
      targetDetail = _maxBuildingDetail;
    } else if (speed < _mediumVelocityThreshold) {
      targetDetail = _interpolateDetail(speed, _lowVelocityThreshold, _mediumVelocityThreshold, 0.8, 0.6);
    } else if (speed < _highVelocityThreshold) {
      targetDetail = _interpolateDetail(speed, _mediumVelocityThreshold, _highVelocityThreshold, 0.6, 0.4);
    } else {
      targetDetail = _minBuildingDetail;
    }

    _updateDetailLevel(targetDetail);
    _updateRenderingParams(speed);
  }

  /// Interpolate detail level based on velocity
  double _interpolateDetail(
    double speed,
    double minSpeed,
    double maxSpeed,
    double maxDetail,
    double minDetail,
  ) {
    final ratio = (speed - minSpeed) / (maxSpeed - minSpeed);
    return maxDetail - (ratio * (maxDetail - minDetail));
  }

  /// Update the detail level and notify listeners
  void _updateDetailLevel(double targetDetail) {
    if (_currentDetailLevel != targetDetail) {
      _currentDetailLevel = targetDetail;
      onDetailLevelChanged(targetDetail);
    }
  }

  /// Update rendering parameters based on performance needs
  void _updateRenderingParams(double speed) {
    final params = <String, dynamic>{
      'buildingDetail': _currentDetailLevel,
      'useSimplifiedGeometry': speed > _mediumVelocityThreshold,
      'useOcclusionCulling': true,
      'useGeometryInstancing': true,
      'maxVisibleBuildings': _calculateMaxVisibleBuildings(speed),
      'useProgressiveLoading': true,
      'loadingPriority': _calculateLoadingPriority(speed),
    };

    onRenderingParamsChanged(params);
  }

  /// Calculate maximum visible buildings based on speed
  int _calculateMaxVisibleBuildings(double speed) {
    if (speed < _lowVelocityThreshold) return 1000;
    if (speed < _mediumVelocityThreshold) return 500;
    if (speed < _highVelocityThreshold) return 250;
    return 100;
  }

  /// Calculate loading priority based on speed
  String _calculateLoadingPriority(double speed) {
    if (speed < _lowVelocityThreshold) return 'high';
    if (speed < _highVelocityThreshold) return 'medium';
    return 'low';
  }

  /// Schedule detail level restoration
  void _scheduleDetailRestore() {
    _detailRestoreTimer?.cancel();
    _detailRestoreTimer = Timer(_detailRestoreDelay, () {
      _updateDetailLevel(_maxBuildingDetail);
      _updateRenderingParams(0.0);
    });
  }

  /// Update all optimizations
  void _updateOptimizations() {
    if (_isPanning) {
      _updateOptimizationsByVelocity();
    } else {
      _updateDetailLevel(_maxBuildingDetail);
      _updateRenderingParams(0.0);
    }
  }

  /// Clean up resources
  void dispose() {
    _detailRestoreTimer?.cancel();
  }
} 