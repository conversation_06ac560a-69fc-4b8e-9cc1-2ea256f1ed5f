import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;

import '../../../providers/map_settings_provider.dart';
import 'map_constants.dart';
import 'map_controller_wrapper.dart';
import '../map_caching/map_bounds_manager.dart';

/// Different tile fallback levels when the primary tile source fails
enum TileFallbackLevel {
  primary,   // No fallback
  fallback1, // First fallback
  fallback2, // Second fallback
  fallback3, // Third fallback
}

/// The status of the map loading
enum MapLoadingStatus {
  initializing,  // Map is initializing
  loading,       // Map is loading
  ready,         // Map is ready
  error,         // Map has encountered an error
}

/// Manager for the state of the map.
/// This class centralizes state management and handles transitions between states.
class MapStateManager with ChangeNotifier {
  // Dependencies
  final MapControllerWrapper _controllerWrapper;
  late final MapBoundsManager _boundsManager;
  
  // Map state
  MapLoadingStatus _loadingStatus = MapLoadingStatus.initializing;
  bool _isUsingFallbackTiles = false;
  TileFallbackLevel _fallbackLevel = TileFallbackLevel.primary;
  bool _isFirstLoad = true;
  int _buildingDetailLevel = MapConstants.highDetail;
  String _lastTileUrl = '';
  String? _errorMessage;
  bool _initialLoadAttempted = false;
  bool _mapLoadFailed = false;
  
  // Enhanced state options
  bool _useRealOSMData = true;
  bool _showDebugInfo = MapConstants.showDebugInfo;
  
  // Feature identification state
  bool _showFeatureInfo = false;
  LatLng? _tappedLocation;
  String _featureInfoText = 'Tap the map to identify features';
  bool _isLoadingFeatureInfo = false;
  
  // Appearance state
  double _tiltAngle = MapConstants.defaultTiltAngle;
  double _rotationAngle = MapConstants.defaultRotationAngle;
  
  // 3D buildings state
  bool _use3DBuildings = true;
  
  // Timer for checking if map is successfully loaded
  Timer? _mapLoadCheckTimer;
  
  /// In-memory cache for state preservation during UI changes
  Map<String, dynamic> _stateCache = {};
  
  /// Constructor
  MapStateManager({
    required MapControllerWrapper controllerWrapper,
  }) : _controllerWrapper = controllerWrapper {
    // Initialize bounds manager with default bounds
    _boundsManager = MapBoundsManager(MapConstants.defaultBounds);
    
    // Listen for map events
    _controllerWrapper.mapEventStream.listen(_handleMapEvent);
    
    // Listen to controller events
    _controllerWrapper.addListener(_onControllerUpdate);
    
    // Schedule initial state setup
    _scheduleInitialMapLoadCheck();
  }
  
  /// Initialize animations with a TickerProvider
  void initializeAnimations(TickerProvider tickerProvider) {
    _controllerWrapper.initializeAnimations(tickerProvider);
  }
  
  /// Current loading status
  MapLoadingStatus get loadingStatus => _loadingStatus;
  
  /// Whether the map is initializing
  bool get isInitializing => _loadingStatus == MapLoadingStatus.initializing;
  
  /// Whether the map is loading
  bool get isLoading => _loadingStatus == MapLoadingStatus.loading;
  
  /// Whether the map is ready
  bool get isReady => _loadingStatus == MapLoadingStatus.ready;
  
  /// Whether the map has encountered an error
  bool get hasError => _loadingStatus == MapLoadingStatus.error;
  
  /// Whether the map is using fallback tiles
  bool get isUsingFallbackTiles => _isUsingFallbackTiles;
  
  /// Current fallback level
  TileFallbackLevel get fallbackLevel => _fallbackLevel;
  
  /// Current building detail level
  int get buildingDetailLevel => _buildingDetailLevel;
  
  /// Last tile URL
  String get lastTileUrl => _lastTileUrl;
  
  /// Error message
  String? get errorMessage => _errorMessage;
  
  /// Whether the map load has been attempted
  bool get initialLoadAttempted => _initialLoadAttempted;
  
  /// Whether the map load failed
  bool get mapLoadFailed => _mapLoadFailed;
  
  /// Whether to use real OSM data
  bool get useRealOSMData => _useRealOSMData;
  
  /// Whether to show debug info
  bool get showDebugInfo => _showDebugInfo;
  
  /// Whether to show feature info
  bool get showFeatureInfo => _showFeatureInfo;
  
  /// Whether to use 3D buildings
  bool get use3DBuildings => _use3DBuildings;
  
  /// Tapped location
  LatLng? get tappedLocation => _tappedLocation;
  
  /// Feature info text
  String get featureInfoText => _featureInfoText;
  
  /// Whether feature info is loading
  bool get isLoadingFeatureInfo => _isLoadingFeatureInfo;
  
  /// Current tilt angle
  double get tiltAngle => _tiltAngle;
  
  /// Current rotation angle
  double get rotationAngle => _rotationAngle;
  
  /// Whether the map is currently processing OSM data
  bool get isProcessingOSM => _controllerWrapper.isMoving && _useRealOSMData;
  
  /// Current visible bounds
  LatLngBounds get visibleBounds => _controllerWrapper.visibleBounds;
  
  /// Current zoom level
  double get currentZoom => _controllerWrapper.zoom;
  
  /// Current center
  LatLng get currentCenter => _controllerWrapper.center;
  
  /// Handle map events from the controller wrapper
  void _handleMapEvent(MapEvent event) {
    // First load and initialization
    if (_isFirstLoad && (event is MapEventMoveEnd)) {
      _handleFirstLoad();
    }
    
    // If we get any map events, the map has loaded successfully
    if (_mapLoadFailed) {
      _mapLoadFailed = false;
      notifyListeners();
    }
    
    // Update detail level on move end
    if (event is MapEventMoveEnd) {
      _determineDetailLevel();
    }
  }
  
  /// Handle the first map load
  void _handleFirstLoad() {
    _isFirstLoad = false;
    _loadingStatus = MapLoadingStatus.ready;
    _initialLoadAttempted = true;
    _determineDetailLevel();
    notifyListeners();
  }
  
  /// Schedule a check to see if the map loaded successfully
  void _scheduleInitialMapLoadCheck() {
    _mapLoadCheckTimer?.cancel();
    _mapLoadCheckTimer = Timer(const Duration(seconds: 5), () {
      if (_isUsingFallbackTiles) {
        _mapLoadFailed = true;
        _errorMessage = 'Failed to load map tiles';
        _loadingStatus = MapLoadingStatus.error;
        notifyListeners();
      }
    });
  }
  
  /// Handle tile error
  void handleTileError(Object error, StackTrace stackTrace) {
    final errorMsg = error.toString();
    String tileUrl = "unknown";
    
    // Try to extract coordinates from the error message
    if (errorMsg.contains('uri=')) {
      final uriPart = errorMsg.split('uri=').last.split(' ').first;
      tileUrl = uriPart;
    } else {
      tileUrl = 'Tile Load Error';
    }
    
    _lastTileUrl = 'ERROR: $tileUrl';
    
    // Log error
    debugPrint('${MapConstants.logTag}: Tile error - Using fallback');
    
    // Update fallback state
    if (!_isUsingFallbackTiles) {
      _isUsingFallbackTiles = true;
      _fallbackLevel = TileFallbackLevel.fallback1;
    } else if (_fallbackLevel == TileFallbackLevel.fallback1) {
      _fallbackLevel = TileFallbackLevel.fallback2;
    } else if (_fallbackLevel == TileFallbackLevel.fallback2) {
      _fallbackLevel = TileFallbackLevel.fallback3;
    }
    
    // Notify listeners of change
    notifyListeners();
  }
  
  /// Determine the detail level based on the current zoom
  void _determineDetailLevel() {
    final zoom = _controllerWrapper.zoom;
    int newLevel = _controllerWrapper.getDetailLevel();
    
    if (_buildingDetailLevel != newLevel) {
      debugPrint('${MapConstants.logTag}: Detail Level changing from $_buildingDetailLevel to $newLevel');
      _buildingDetailLevel = newLevel;
      notifyListeners();
    }
  }
  
  /// Set the tilt angle
  void setTiltAngle(double angle) {
    if (angle >= 0 && angle <= MapConstants.maxTiltAngle) {
      _tiltAngle = angle;
      notifyListeners();
    }
  }
  
  /// Set the rotation angle
  void setRotationAngle(double angle) {
    _rotationAngle = angle;
    _controllerWrapper.setRotation(angle);
    notifyListeners();
  }
  
  /// Zoom in by one level with animation
  void zoomIn() {
    _controllerWrapper.zoomIn();
  }
  
  /// Zoom out by one level with animation
  void zoomOut() {
    _controllerWrapper.zoomOut();
  }
  
  /// Move the map to a new position with optional zoom
  void moveTo({
    required LatLng position,
    double? zoom,
    bool animate = true,
  }) {
    _controllerWrapper.moveTo(
      position: position,
      zoom: zoom,
      animate: animate,
      animationDuration: MapConstants.cameraMoveAnimationDuration,
    );
  }
  
  /// Move to a specific position and zoom level
  void moveToSpecific({
    required LatLng position,
    double? zoom,
    bool animate = false,
  }) {
    _controllerWrapper.moveTo(
      position: position,
      zoom: zoom,
      animate: animate,
    );
  }
  
  /// Update the current zoom level externally
  /// This is useful for synchronizing the zoom level after animations
  void updateZoomLevel(double zoomLevel) {
    // Make sure the zoom level is within bounds
    final boundedZoom = zoomLevel.clamp(
      MapConstants.minZoom, 
      MapConstants.maxZoom
    );
    
    // Only update if there's a meaningful change
    if ((boundedZoom - _controllerWrapper.zoom).abs() > 0.01) {
      _controllerWrapper.updateZoom(boundedZoom);
      _determineDetailLevel();
      notifyListeners();
    }
  }
  
  /// Toggle real OSM data
  void toggleRealOSMData() {
    _useRealOSMData = !_useRealOSMData;
    notifyListeners();
  }
  
  /// Toggle debug info
  void toggleDebugInfo() {
    _showDebugInfo = !_showDebugInfo;
    notifyListeners();
  }
  
  /// Set the map to a retry state
  void retryMapLoading() {
    _mapLoadFailed = false;
    _isUsingFallbackTiles = false;
    _fallbackLevel = TileFallbackLevel.primary;
    _errorMessage = null;
    _loadingStatus = MapLoadingStatus.loading;
    
    // Schedule a check to see if the map loaded successfully
    _scheduleInitialMapLoadCheck();
    
    notifyListeners();
  }
  
  /// Set feature info from a tap
  void setFeatureInfo({
    required LatLng location,
    required String info,
    bool isLoading = false,
  }) {
    _tappedLocation = location;
    _featureInfoText = info;
    _isLoadingFeatureInfo = isLoading;
    _showFeatureInfo = true;
    notifyListeners();
  }
  
  /// Clear feature info
  void clearFeatureInfo() {
    _showFeatureInfo = false;
    _tappedLocation = null;
    _featureInfoText = 'Tap the map to identify features';
    _isLoadingFeatureInfo = false;
    notifyListeners();
  }
  
  /// Set loading state for feature info
  void setFeatureInfoLoading(bool loading) {
    _isLoadingFeatureInfo = loading;
    notifyListeners();
  }
  
  /// Set whether to use 3D buildings
  void setUse3DBuildings(bool use) {
    if (_use3DBuildings != use) {
      _use3DBuildings = use;
      notifyListeners();
    }
  }
  
  /// Reset the OSM data layers to force a reload
  void resetOSMDataLayers() {
    notifyListeners();
  }
  
  /// Save critical state to memory cache for UI navigation
  void saveStateToCache() {
    _stateCache = {
      'center': _controllerWrapper.center,
      'zoom': _controllerWrapper.zoom,
      'tilt': _tiltAngle,
      'rotation': _rotationAngle,
      'showFeatureInfo': _showFeatureInfo,
      'use3DBuildings': _use3DBuildings,
      'showDebugInfo': _showDebugInfo,
    };
  }
  
  /// Restore state from memory cache when returning from UI navigation
  void restoreStateFromCache() {
    if (_stateCache.isEmpty) return;
    
    // Restore critical state if available
    if (_stateCache.containsKey('tilt')) {
      _tiltAngle = _stateCache['tilt'];
    }
    if (_stateCache.containsKey('rotation')) {
      _rotationAngle = _stateCache['rotation'];
    }
    if (_stateCache.containsKey('showFeatureInfo')) {
      _showFeatureInfo = _stateCache['showFeatureInfo'];
    }
    if (_stateCache.containsKey('use3DBuildings')) {
      _use3DBuildings = _stateCache['use3DBuildings'];
    }
    if (_stateCache.containsKey('showDebugInfo')) {
      _showDebugInfo = _stateCache['showDebugInfo'];
    }
    
    // Move map to cached position if available
    if (_stateCache.containsKey('center') && _stateCache.containsKey('zoom')) {
      _controllerWrapper.moveTo(
        position: _stateCache['center'],
        zoom: _stateCache['zoom'],
        animate: false,
      );
    }
    
    notifyListeners();
  }
  
  /// Handle controller updates
  void _onControllerUpdate() {
    if (_controllerWrapper.controller == null) return;
    
    // Only update our internal state values without triggering controller methods
    // This prevents the infinite loop of updates
    if (_controllerWrapper.isInitialized) {
      _determineDetailLevel();
      notifyListeners();
    }
  }
  
  @override
  void dispose() {
    _mapLoadCheckTimer?.cancel();
    _controllerWrapper.removeListener(_onControllerUpdate);
    super.dispose();
  }
} 