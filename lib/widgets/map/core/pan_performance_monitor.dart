import 'dart:async';
import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart' as vector;

/// Monitors and optimizes performance during pan operations.
/// 
/// This class handles:
/// 1. Frame rate monitoring
/// 2. Memory usage tracking
/// 3. Performance optimization suggestions
/// 4. Automatic mode switching based on performance
class PanPerformanceMonitor {
  // Configuration
  final Duration sampleInterval;
  final int frameRateThreshold;
  final int memoryThreshold;
  
  // Callbacks
  final Function(bool)? onPerformanceModeChange;
  final Function(Map<String, dynamic>)? onMetricsUpdate;
  
  // State
  bool _isMonitoring = false;
  bool _isInHighPerformanceMode = false;
  Timer? _monitoringTimer;
  
  // Performance metrics
  final List<double> _frameRates = [];
  final List<int> _memoryUsage = [];
  final List<double> _panVelocities = [];
  int _dropCount = 0;
  
  // Constants
  static const int _maxSamples = 60;
  static const Duration _defaultInterval = Duration(milliseconds: 500);
  static const int _defaultFrameThreshold = 45;
  static const int _defaultMemoryThreshold = 200; // MB
  
  // Constructor
  PanPerformanceMonitor({
    this.sampleInterval = _defaultInterval,
    this.frameRateThreshold = _defaultFrameThreshold,
    this.memoryThreshold = _defaultMemoryThreshold,
    this.onPerformanceModeChange,
    this.onMetricsUpdate,
  });
  
  /// Start monitoring performance
  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    _monitoringTimer = Timer.periodic(sampleInterval, _collectMetrics);
  }
  
  /// Stop monitoring performance
  void stopMonitoring() {
    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
  }
  
  /// Collect performance metrics
  void _collectMetrics(Timer timer) {
    // Get current metrics
    final metrics = _getCurrentMetrics();
    
    // Update history
    _updateMetricsHistory(metrics);
    
    // Check for performance issues
    _checkPerformance(metrics);
    
    // Notify listeners
    onMetricsUpdate?.call(metrics);
  }
  
  /// Get current performance metrics
  Map<String, dynamic> _getCurrentMetrics() {
    return {
      'frameRate': _getCurrentFrameRate(),
      'memoryUsage': _getCurrentMemoryUsage(),
      'dropCount': _dropCount,
      'averagePanVelocity': _calculateAveragePanVelocity(),
      'isHighPerformanceMode': _isInHighPerformanceMode,
    };
  }
  
  /// Get current frame rate
  double _getCurrentFrameRate() {
    // In a real implementation, this would use the actual frame time
    // For now, we'll simulate it
    return 60.0; // Replace with actual frame rate measurement
  }
  
  /// Get current memory usage in MB
  int _getCurrentMemoryUsage() {
    // In a real implementation, this would use platform-specific memory APIs
    // For now, we'll simulate it
    return 100; // Replace with actual memory usage
  }
  
  /// Update metrics history
  void _updateMetricsHistory(Map<String, dynamic> metrics) {
    _frameRates.add(metrics['frameRate']);
    _memoryUsage.add(metrics['memoryUsage']);
    
    // Keep history size limited
    if (_frameRates.length > _maxSamples) {
      _frameRates.removeAt(0);
    }
    if (_memoryUsage.length > _maxSamples) {
      _memoryUsage.removeAt(0);
    }
    if (_panVelocities.length > _maxSamples) {
      _panVelocities.removeAt(0);
    }
  }
  
  /// Check for performance issues
  void _checkPerformance(Map<String, dynamic> metrics) {
    final shouldUseHighPerformance = _shouldUseHighPerformanceMode(metrics);
    
    if (shouldUseHighPerformance != _isInHighPerformanceMode) {
      _isInHighPerformanceMode = shouldUseHighPerformance;
      onPerformanceModeChange?.call(shouldUseHighPerformance);
    }
  }
  
  /// Determine if high performance mode should be used
  bool _shouldUseHighPerformanceMode(Map<String, dynamic> metrics) {
    // Check frame rate
    if (metrics['frameRate'] < frameRateThreshold) {
      return true;
    }
    
    // Check memory usage
    if (metrics['memoryUsage'] > memoryThreshold) {
      return true;
    }
    
    // Check drop count
    if (metrics['dropCount'] > 5) {
      return true;
    }
    
    return false;
  }
  
  /// Record a frame drop
  void recordFrameDrop() {
    _dropCount++;
  }
  
  /// Record pan velocity
  void recordPanVelocity(vector.Vector2 velocity) {
    _panVelocities.add(velocity.length);
  }
  
  /// Calculate average pan velocity
  double _calculateAveragePanVelocity() {
    if (_panVelocities.isEmpty) return 0.0;
    
    return _panVelocities.reduce((a, b) => a + b) / _panVelocities.length;
  }
  
  /// Get performance suggestions based on current metrics
  List<String> getPerformanceSuggestions() {
    final suggestions = <String>[];
    final metrics = _getCurrentMetrics();
    
    if (metrics['frameRate'] < frameRateThreshold) {
      suggestions.add('Consider reducing map detail level');
    }
    
    if (metrics['memoryUsage'] > memoryThreshold) {
      suggestions.add('Consider clearing tile cache');
    }
    
    if (_calculateAveragePanVelocity() > 1000) {
      suggestions.add('Consider enabling high performance mode during rapid pan');
    }
    
    return suggestions;
  }
  
  /// Get current performance status
  String getPerformanceStatus() {
    final metrics = _getCurrentMetrics();
    
    if (metrics['frameRate'] < 30) {
      return 'Poor';
    } else if (metrics['frameRate'] < frameRateThreshold) {
      return 'Fair';
    } else {
      return 'Good';
    }
  }
  
  /// Reset metrics
  void resetMetrics() {
    _frameRates.clear();
    _memoryUsage.clear();
    _panVelocities.clear();
    _dropCount = 0;
  }
  
  /// Dispose of resources
  void dispose() {
    stopMonitoring();
    resetMetrics();
  }
} 