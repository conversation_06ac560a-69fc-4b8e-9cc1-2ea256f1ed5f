import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map/flutter_map.dart';

import 'zoom_state_manager.dart';
import 'map_constants.dart';

/// Controls how map content is rendered based on zoom level and state.
/// This separates the rendering decisions from other concerns.
class ZoomRenderingStrategy {
  // The state manager to observe
  final ZoomStateManager _stateManager;
  
  // Configuration
  bool _use2DDuringZoom = false;
  bool _useSimplifiedRenderingDuringZoom = true;
  
  ZoomRenderingStrategy({
    required ZoomStateManager stateManager,
    bool use2DDuringZoom = false,
    bool useSimplifiedRenderingDuringZoom = true,
  }) : _stateManager = stateManager,
       _use2DDuringZoom = use2DDuringZoom,
       _useSimplifiedRenderingDuringZoom = useSimplifiedRenderingDuringZoom {
    // Configure the state manager
    _stateManager.shouldUse2DDuringZoom = _use2DDuringZoom;
  }
  
  /// Get the appropriate tilt for the current state
  double getTiltForCurrentState() {
    return _stateManager.getTiltForCurrentState();
  }
  
  /// Get detail level for current zoom
  int getDetailLevel() {
    // If zooming and simplified rendering is enabled, reduce detail
    if (_stateManager.isZooming && _useSimplifiedRenderingDuringZoom) {
      final baseDetailLevel = _getDetailLevelForZoom(_stateManager.zoom);
      return baseDetailLevel > 1 ? baseDetailLevel - 1 : baseDetailLevel;
    }
    
    // Otherwise use normal detail level
    return _getDetailLevelForZoom(_stateManager.zoom);
  }
  
  /// Get detail level for specific zoom value
  int _getDetailLevelForZoom(double zoom) {
    if (zoom <= MapConstants.lowDetailThreshold) return MapConstants.lowDetail;
    if (zoom <= MapConstants.mediumDetailThreshold) return MapConstants.mediumDetail;
    return MapConstants.highDetail;
  }
  
  /// Should 3D buildings be shown
  bool shouldShow3DBuildings() {
    // If zooming and 2D mode is enabled during zoom, don't show 3D buildings
    if (_stateManager.isZooming && _use2DDuringZoom) {
      return false;
    }
    
    // Otherwise follow the state manager's mode
    return _stateManager.zoomMode == ZoomMode.mode3D;
  }
  
  /// Get full rendering parameters for the current state
  Map<String, dynamic> getRenderingParameters() {
    final params = _stateManager.getRenderingParameters();
    
    // Override with zoom-specific optimizations
    if (_stateManager.isZooming) {
      // Reduce detail during zooming
      if (_useSimplifiedRenderingDuringZoom) {
        params['detailLevel'] = _getSimplifiedDetailLevel(params['detailLevel']);
      }
      
      // Disable 3D during zooming if configured
      if (_use2DDuringZoom) {
        params['render3D'] = false;
      }
    }
    
    return params;
  }
  
  /// Convert a detail level to a simplified version
  String _getSimplifiedDetailLevel(String detailLevel) {
    switch (detailLevel) {
      case 'high': return 'medium';
      case 'medium': return 'low';
      case 'low': return 'very-low';
      default: return detailLevel;
    }
  }
  
  /// Configure whether to use 2D mode during zooming
  void setUse2DDuringZoom(bool use2D) {
    _use2DDuringZoom = use2D;
    _stateManager.shouldUse2DDuringZoom = use2D;
  }
  
  /// Configure whether to use simplified rendering during zooming
  void setUseSimplifiedRenderingDuringZoom(bool useSimplified) {
    _useSimplifiedRenderingDuringZoom = useSimplified;
  }
} 