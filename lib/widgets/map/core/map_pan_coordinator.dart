import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import 'pan_state_manager.dart';
import 'render_mode_transition_controller.dart';
import 'pan_optimized_tile_manager.dart';
import 'pan_performance_monitor.dart';
import '../utils/pan_math_utils.dart';

/// Coordinates all pan-related components and provides a unified API for pan operations.
/// 
/// This class:
/// 1. Integrates all pan-related components
/// 2. Provides a simple API for the map widget
/// 3. Manages state transitions and mode changes
/// 4. Handles performance optimization
class MapPanCoordinator {
  // Core components
  final PanStateManager _panStateManager;
  final RenderModeTransitionController _transitionController;
  final PanOptimizedTileManager _tileManager;
  final PanPerformanceMonitor _performanceMonitor;
  
  // Callbacks
  final Function(bool)? onPerformanceModeChange;
  final Function(double)? onTiltChange;
  final Function()? onTransitionComplete;
  
  // State
  bool _isInitialized = false;
  bool _isDisposed = false;
  
  // Constructor
  MapPanCoordinator({
    required TickerProvider vsync,
    required int maxTileCacheSize,
    this.onPerformanceModeChange,
    this.onTiltChange,
    this.onTransitionComplete,
  }) : _panStateManager = PanStateManager(
         onPanStart: null, // Will be set in initialize
         onPanUpdate: null,
         onPanEnd: null,
       ),
       _transitionController = RenderModeTransitionController(
         vsync: vsync,
         onModeChange: null, // Will be set in initialize
         onTransitionComplete: null,
       ),
       _tileManager = PanOptimizedTileManager(
         maxCacheSize: maxTileCacheSize,
         predictiveLoadingEnabled: true,
       ),
       _performanceMonitor = PanPerformanceMonitor(
         onPerformanceModeChange: null, // Will be set in initialize
       );
  
  /// Initialize the coordinator and all its components
  void initialize() {
    if (_isInitialized) return;
    
    // Set up callbacks
    _panStateManager = PanStateManager(
      onPanStart: _handlePanStart,
      onPanUpdate: _handlePanUpdate,
      onPanEnd: _handlePanEnd,
    );
    
    _transitionController = RenderModeTransitionController(
      vsync: vsync,
      onModeChange: (tilt) {
        onTiltChange?.call(tilt);
      },
      onTransitionComplete: () {
        onTransitionComplete?.call();
      },
    );
    
    _performanceMonitor = PanPerformanceMonitor(
      onPerformanceModeChange: (useHighPerformance) {
        onPerformanceModeChange?.call(useHighPerformance);
        _updatePerformanceMode(useHighPerformance);
      },
    );
    
    // Start monitoring
    _performanceMonitor.startMonitoring();
    
    _isInitialized = true;
  }
  
  /// Handle pan start
  void _handlePanStart(PanStartDetails details) {
    if (_isDisposed) return;
    
    // Reset performance monitoring
    _performanceMonitor.resetMetrics();
    
    // Prepare for pan operation
    _transitionController.pauseTransition();
    _tileManager.clearCache();
  }
  
  /// Handle pan update
  void _handlePanUpdate(PanUpdateDetails details) {
    if (_isDisposed) return;
    
    // Calculate pan velocity
    final velocity = vector.Vector2(details.delta.dx, details.delta.dy);
    
    // Update components
    _panStateManager.handlePanUpdate(details);
    _transitionController.handlePanUpdate(details);
    _tileManager.updatePanVelocity(velocity);
    _performanceMonitor.recordPanVelocity(velocity);
    
    // Check for performance issues
    if (_panStateManager.shouldUseSimplifiedRendering) {
      _transitionController.pauseTransition();
      _transitionController.resetTo2D();
    }
  }
  
  /// Handle pan end
  void _handlePanEnd(PanEndDetails details) {
    if (_isDisposed) return;
    
    // Reset components
    _panStateManager.handlePanEnd(details);
    _transitionController.resumeTransition();
    
    // Check if we should restore 2.5D mode
    if (!_performanceMonitor._isInHighPerformanceMode) {
      _transitionController.toggleMode(force25D: true);
    }
  }
  
  /// Update performance mode across components
  void _updatePerformanceMode(bool useHighPerformance) {
    if (_isDisposed) return;
    
    // Update transition speed
    _transitionController.updateTransitionSpeed(useHighPerformance);
    
    // Force 2D mode in high performance mode
    if (useHighPerformance) {
      _transitionController.resetTo2D();
    }
  }
  
  /// Get current performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    final panMetrics = _panStateManager.velocity.length;
    final tileMetrics = _tileManager.getPerformanceMetrics();
    final performanceStatus = _performanceMonitor.getPerformanceStatus();
    
    return {
      'panVelocity': panMetrics,
      'tileCache': tileMetrics,
      'performanceStatus': performanceStatus,
      'is2DMode': _transitionController.is2DMode,
      'transitionProgress': _transitionController.getTransitionProgress(),
    };
  }
  
  /// Get performance suggestions
  List<String> getPerformanceSuggestions() {
    return _performanceMonitor.getPerformanceSuggestions();
  }
  
  /// Force switch to 2D mode
  void forceTo2D() {
    if (_isDisposed) return;
    _transitionController.resetTo2D();
  }
  
  /// Toggle between 2D and 2.5D modes
  void toggleRenderMode() {
    if (_isDisposed) return;
    if (!_performanceMonitor._isInHighPerformanceMode) {
      _transitionController.toggleMode();
    }
  }
  
  /// Check if currently in high performance mode
  bool isInHighPerformanceMode() {
    return _performanceMonitor._isInHighPerformanceMode;
  }
  
  /// Get current tilt value
  double getCurrentTilt() {
    return _transitionController.currentTilt;
  }
  
  /// Cleanup resources
  void dispose() {
    _isDisposed = true;
    _performanceMonitor.dispose();
    _transitionController.dispose();
    _tileManager.dispose();
  }
} 