import 'dart:async';
import 'package:flutter/material.dart';
import '../utils/pan_math_utils.dart';

/// Controller for managing smooth transitions between render modes (2D/2.5D).
/// 
/// This class handles:
/// 1. Smooth animations between render modes
/// 2. Interruption handling
/// 3. Performance-based mode switching
/// 4. Coordination with pan and zoom states
class RenderModeTransitionController {
  // Dependencies
  final TickerProvider vsync;
  final Function(double)? onModeChange;
  final Function()? onTransitionComplete;
  
  // Animation controllers
  late AnimationController _transitionController;
  late Animation<double> _tiltAnimation;
  
  // State
  bool _is2DMode = true;
  bool _isTransitioning = false;
  bool _transitionPaused = false;
  Timer? _transitionDebounceTimer;
  
  // Constants
  static const Duration _defaultTransitionDuration = Duration(milliseconds: 300);
  static const Duration _fastTransitionDuration = Duration(milliseconds: 150);
  static const Duration _debounceDelay = Duration(milliseconds: 100);
  static const double _minTiltAngle = 0.0;
  static const double _maxTiltAngle = 0.5;
  
  // Constructor
  RenderModeTransitionController({
    required this.vsync,
    this.onModeChange,
    this.onTransitionComplete,
  }) {
    _initializeAnimations();
  }
  
  // Getters
  bool get is2DMode => _is2DMode;
  bool get isTransitioning => _isTransitioning;
  double get currentTilt => _tiltAnimation.value;
  
  /// Initialize animation controllers and animations
  void _initializeAnimations() {
    _transitionController = AnimationController(
      vsync: vsync,
      duration: _defaultTransitionDuration,
    );
    
    _tiltAnimation = Tween<double>(
      begin: _minTiltAngle,
      end: _maxTiltAngle,
    ).animate(CurvedAnimation(
      parent: _transitionController,
      curve: Curves.easeInOutCubic,
    ));
    
    // Add listener for animation updates
    _tiltAnimation.addListener(() {
      onModeChange?.call(_tiltAnimation.value);
    });
    
    // Add status listener for completion
    _transitionController.addStatusListener(_handleAnimationStatus);
  }
  
  /// Handle animation status changes
  void _handleAnimationStatus(AnimationStatus status) {
    switch (status) {
      case AnimationStatus.completed:
        _isTransitioning = false;
        _is2DMode = false;
        onTransitionComplete?.call();
        break;
      case AnimationStatus.dismissed:
        _isTransitioning = false;
        _is2DMode = true;
        onTransitionComplete?.call();
        break;
      case AnimationStatus.forward:
      case AnimationStatus.reverse:
        _isTransitioning = true;
        break;
    }
  }
  
  /// Toggle between 2D and 2.5D modes
  void toggleMode({bool force2D = false, bool force25D = false}) {
    if (_transitionPaused) return;
    
    // Cancel any pending transitions
    _transitionDebounceTimer?.cancel();
    
    // Determine target mode
    final targetIs2D = force2D || (!force25D && !_is2DMode);
    
    // Start transition after debounce
    _transitionDebounceTimer = Timer(_debounceDelay, () {
      _startTransition(targetIs2D);
    });
  }
  
  /// Start the transition animation
  void _startTransition(bool to2D) {
    if (_transitionPaused) return;
    
    // Calculate optimal duration based on current tilt
    final currentTilt = _tiltAnimation.value;
    final targetTilt = to2D ? _minTiltAngle : _maxTiltAngle;
    final progress = (currentTilt - _minTiltAngle) / (_maxTiltAngle - _minTiltAngle);
    final remainingProgress = to2D ? progress : (1 - progress);
    
    // Adjust duration based on remaining distance
    final adjustedDuration = Duration(
      milliseconds: (_defaultTransitionDuration.inMilliseconds * remainingProgress).round(),
    );
    
    // Update animation
    _transitionController.duration = adjustedDuration;
    if (to2D) {
      _transitionController.reverse();
    } else {
      _transitionController.forward();
    }
  }
  
  /// Handle pan updates
  void handlePanUpdate(PanUpdateDetails details) {
    // If panning fast enough, automatically switch to 2D mode
    if (PanMathUtils.shouldUseSimplifiedRendering(details.delta.distance)) {
      pauseTransition();
      _force2DMode();
    }
  }
  
  /// Force immediate switch to 2D mode
  void _force2DMode() {
    if (_is2DMode) return;
    
    _transitionController.duration = _fastTransitionDuration;
    _transitionController.reverse();
  }
  
  /// Pause transitions (useful during rapid pan operations)
  void pauseTransition() {
    _transitionPaused = true;
    if (_isTransitioning) {
      _transitionController.stop();
    }
  }
  
  /// Resume transitions
  void resumeTransition() {
    _transitionPaused = false;
    // Optional: Continue any interrupted transition
    if (_isTransitioning) {
      _startTransition(_is2DMode);
    }
  }
  
  /// Update transition speed based on performance
  void updateTransitionSpeed(bool isHighPerformance) {
    _transitionController.duration = isHighPerformance
        ? _fastTransitionDuration
        : _defaultTransitionDuration;
  }
  
  /// Get current transition progress
  double getTransitionProgress() {
    return _tiltAnimation.value / _maxTiltAngle;
  }
  
  /// Reset to 2D mode immediately
  void resetTo2D() {
    _transitionController.value = 0.0;
    _is2DMode = true;
    _isTransitioning = false;
  }
  
  /// Cleanup resources
  void dispose() {
    _transitionDebounceTimer?.cancel();
    _transitionController.dispose();
  }
} 