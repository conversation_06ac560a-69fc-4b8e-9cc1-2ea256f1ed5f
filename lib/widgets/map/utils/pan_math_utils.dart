import 'dart:math' as math;
import 'package:vector_math/vector_math_64.dart' as vector;
import 'package:flutter/material.dart';

/// Utility class for pan-related mathematical calculations.
/// 
/// This class provides optimized calculations for:
/// 1. Velocity smoothing and prediction
/// 2. Performance thresholds
/// 3. Optimization calculations
class PanMathUtils {
  // Constants for calculations
  static const double _minVelocityThreshold = 100.0; // pixels per second
  static const double _maxVelocityThreshold = 5000.0;
  static const double _velocitySmoothing = 0.85;
  static const int _predictionSteps = 3;
  
  /// Calculates a smoothed velocity from a series of instantaneous velocities
  static vector.Vector2 calculateSmoothedVelocity(List<vector.Vector2> velocityHistory) {
    if (velocityHistory.isEmpty) return vector.Vector2.zero();
    
    final smoothed = vector.Vector2.zero();
    double totalWeight = 0.0;
    
    // Apply exponential weighting to recent velocities
    for (int i = 0; i < velocityHistory.length; i++) {
      final weight = math.pow(_velocitySmoothing, velocityHistory.length - i - 1).toDouble();
      smoothed.add(velocityHistory[i].scaled(weight));
      totalWeight += weight;
    }
    
    return smoothed.scaled(1.0 / totalWeight);
  }
  
  /// Predicts future position based on current velocity and deceleration
  static Offset predictFuturePosition(Offset currentPosition, vector.Vector2 velocity, double timeSeconds) {
    if (velocity.length < _minVelocityThreshold) return currentPosition;
    
    // Apply natural deceleration
    final deceleratedVelocity = velocity.scaled(math.pow(0.9, timeSeconds).toDouble());
    
    return Offset(
      currentPosition.dx + deceleratedVelocity.x * timeSeconds,
      currentPosition.dy + deceleratedVelocity.y * timeSeconds,
    );
  }
  
  /// Calculates optimal rendering detail level based on pan velocity
  static int calculateDetailLevel(double velocity, int baseLevel) {
    if (velocity < _minVelocityThreshold) return baseLevel;
    
    // Reduce detail level as velocity increases
    final reduction = (velocity / _maxVelocityThreshold * 2).floor();
    return math.max(1, baseLevel - reduction);
  }
  
  /// Determines if simplified rendering should be used based on velocity
  static bool shouldUseSimplifiedRendering(double velocity) {
    return velocity > _maxVelocityThreshold * 0.5;
  }
  
  /// Calculates the optimal tile loading radius based on velocity
  static double calculateTileLoadRadius(double velocity, double baseRadius) {
    if (velocity < _minVelocityThreshold) return baseRadius;
    
    // Increase radius in the direction of movement
    final velocityFactor = math.min(velocity / _maxVelocityThreshold, 1.0);
    return baseRadius * (1.0 + velocityFactor);
  }
  
  /// Calculates predictive loading bounds based on current pan
  static Rect calculatePredictiveLoadBounds(Rect visibleBounds, vector.Vector2 velocity) {
    if (velocity.length < _minVelocityThreshold) return visibleBounds;
    
    // Predict future position for multiple steps
    double maxPredictionX = 0.0;
    double maxPredictionY = 0.0;
    
    for (int i = 1; i <= _predictionSteps; i++) {
      final factor = math.pow(0.8, i).toDouble();
      maxPredictionX += velocity.x * 0.2 * factor;
      maxPredictionY += velocity.y * 0.2 * factor;
    }
    
    // Expand bounds in the direction of movement
    return Rect.fromLTRB(
      visibleBounds.left + math.min(0, maxPredictionX),
      visibleBounds.top + math.min(0, maxPredictionY),
      visibleBounds.right + math.max(0, maxPredictionX),
      visibleBounds.bottom + math.max(0, maxPredictionY),
    );
  }
  
  /// Calculates the optimal animation duration based on velocity
  static Duration calculateAnimationDuration(double velocity) {
    // Faster animations for higher velocities
    final baseDuration = 300.0; // milliseconds
    final velocityFactor = math.min(velocity / _maxVelocityThreshold, 1.0);
    final adjustedDuration = baseDuration * (1.0 - velocityFactor * 0.7);
    
    return Duration(milliseconds: adjustedDuration.round());
  }
  
  /// Calculates the optimal curve for animation based on velocity
  static Curve getOptimalAnimationCurve(double velocity) {
    if (velocity > _maxVelocityThreshold * 0.7) {
      return Curves.easeOutCubic; // Faster curve for high velocity
    } else if (velocity > _maxVelocityThreshold * 0.3) {
      return Curves.easeInOutCubic; // Balanced curve for medium velocity
    } else {
      return Curves.easeInOutQuart; // Smoother curve for low velocity
    }
  }
  
  /// Calculates memory-optimized tile cache size based on velocity
  static int calculateOptimalCacheSize(double velocity, int baseSize) {
    if (velocity < _minVelocityThreshold) return baseSize;
    
    // Increase cache size with velocity, but with diminishing returns
    final velocityFactor = math.sqrt(math.min(velocity / _maxVelocityThreshold, 1.0));
    return (baseSize * (1.0 + velocityFactor * 0.5)).round();
  }
  
  /// Determines if emergency performance mode should be activated
  static bool shouldActivateEmergencyMode(List<double> recentVelocities) {
    if (recentVelocities.length < 3) return false;
    
    // Check for sustained high velocity
    int highVelocityCount = 0;
    for (final velocity in recentVelocities) {
      if (velocity > _maxVelocityThreshold * 0.8) {
        highVelocityCount++;
      }
    }
    
    return highVelocityCount >= recentVelocities.length * 0.7;
  }
} 