import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map_marker_cluster/flutter_map_marker_cluster.dart';
import 'package:flutter_map_marker_popup/flutter_map_marker_popup.dart';
import 'dart:math' as math;
import 'package:vector_math/vector_math_64.dart' as vector;
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';

import '../../config/constants.dart';
import '../../providers/map_provider.dart';
import 'map_pin_widget.dart';
import 'user_location_marker.dart';
import 'map_styles.dart';

// Import new zoom system components
import 'core/map_zoom_coordinator.dart';
import 'core/zoom_state_manager.dart';
import 'core/zoom_rendering_strategy.dart';
import 'core/zoom_system_integration.dart';

// Import new components
import 'components/map_popup_builder.dart';
import 'components/map_control_button.dart';
import 'components/custom_marker.dart';
import 'components/map_controls.dart';
import 'components/markers_factory.dart';
import 'components/map_transformer.dart';
import 'components/map_error_handler.dart';
import 'components/map_layers_builder.dart';
import 'components/map_loading_indicator.dart';
import 'map_layers/user_location/loading_aura_overlay.dart';
import 'animations/zoom_animation.dart';
import 'core/map_constants.dart';
import 'core/map_controller_wrapper.dart';
import 'core/map_state_manager.dart';
import 'map_caching/map_cache_coordinator.dart';
import 'map_caching/persistent_map_cache.dart';
import '../../providers/map_settings_provider.dart';
import '../../providers/tile_cache_provider.dart';

// Extension for string capitalization
extension StringExtensions on String {
  String capitalize() {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }
}

class FlutterMapWidget extends StatefulWidget {
  final MapProvider mapProvider;
  final Function(Map<String, dynamic>) onPinTap;

  const FlutterMapWidget({
    Key? key,
    required this.mapProvider,
    required this.onPinTap,
  }) : super(key: key);

  @override
  FlutterMapWidgetState createState() => FlutterMapWidgetState();
}

// Make the class public but keep the original name for compatibility
class FlutterMapWidgetState extends State<FlutterMapWidget> with TickerProviderStateMixin {
  final MapController _mapController = MapController();
  final PopupController _popupController = PopupController();
  
  // Controllers and state managers
  late MapControllerWrapper _controllerWrapper;
  late MapStateManager _stateManager;
  
  // Emergency performance mode to handle freezes
  bool _emergencyPerformanceMode = false;
  DateTime _lastEmergencyModeActivation = DateTime.now().subtract(const Duration(hours: 1));
  int _rapidEventCounter = 0;
  Timer? _emergencyModeTimer;
  Timer? _rapidEventTimer;
  static const int _rapidEventThreshold = 10; // Number of events in short time to trigger emergency mode
  static const Duration _emergencyModeDuration = Duration(seconds: 30); // How long to stay in emergency mode
  static const Duration _rapidEventWindow = Duration(milliseconds: 500); // Window to count rapid events
  
  // We'll keep the tilt animation for compatibility
  late AnimationController _animationController;
  late Animation<double> _tiltAnimation;
  
  // Public accessor for tilt animation value
  Animation<double> get tiltAnimation => _tiltAnimation;
  
  // Animation controllers for smooth movement
  AnimationController? _moveAnimationController;
  Animation<double>? _latAnimation;
  Animation<double>? _lngAnimation;
  
  // Add a state variable to track when we're getting user location
  bool _isGettingUserLocation = false;
  // Track failed location attempts to show appropriate messages
  int _locationFailedAttempts = 0;
  // Max retries before giving up on location
  static const int _maxLocationRetries = 3;

  // New properties for startup location handling
  bool _isFirstLocationUpdate = true;
  bool _initialLocationRequested = false;
  final int _initialZoomLevel = 15; // Changed to match new default zoom level
  
  // Public accessors needed by other components for backwards compatibility
  LatLngBounds get visibleBounds => _stateManager.visibleBounds;
  double get currentZoom => _stateManager.currentZoom;
  bool get isProcessingOSM => _controllerWrapper.isMoving && _stateManager.useRealOSMData;
  bool get isUsingFallbackTiles => _stateManager.isUsingFallbackTiles;
  int get buildingDetailLevel => _stateManager.buildingDetailLevel;
  TileFallbackLevel get fallbackLevel => _stateManager.fallbackLevel;
  String get lastTileUrl => _stateManager.lastTileUrl;
  
  // Compatibility method for fallbackLevel as int for older code
  int get fallbackLevelInt => _stateManager.fallbackLevel.index;

  // For smoother zoom transitions
  Timer? _zoomDebounceTimer;
  double _lastZoomLevel = 16.0;
  bool _isZooming = false;
  final Duration _zoomDebounceDelay = const Duration(milliseconds: 150);

  // Add a flag to check if new zoom system is initialized
  bool _hasNewZoomSystem = false;
  MapZoomCoordinator? _zoomCoordinator;

  // Add field to store provider reference
  MapSettingsProvider? _mapSettingsProvider;

  @override
  void initState() {
    super.initState();
    
    // Initialize controller wrapper and state manager with higher zoom
    _controllerWrapper = MapControllerWrapper(
      mapController: _mapController,
      initialCenter: LatLng(AppConstants.defaultLatitude, AppConstants.defaultLongitude),
      initialZoom: _initialZoomLevel.toDouble(), // Using higher zoom level
    );
    
    _stateManager = MapStateManager(
      controllerWrapper: _controllerWrapper,
    );
    
    _lastZoomLevel = _initialZoomLevel.toDouble(); // Initialize to match default zoom
    
    // Initialize the animation controller for tilt effect
    _animationController = AnimationController(
      duration: MapStyles.tiltAnimationDuration,
      vsync: this,
    );
    
    // Set an initial tilt angle of 0.0 to start in 2D mode
    _stateManager.setTiltAngle(0.0);
    
    // Create a tilt animation starting at 0 (2D)
    _tiltAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0, // Start with 0.0 (2D mode)
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    // Initialize animations for controller wrapper and state manager
    _controllerWrapper.initializeAnimations(this);
    _stateManager.initializeAnimations(this);
    
    // Start the animation
    _animationController.forward();
    
    // Subscribe to map controller zoom changes
    _mapController.mapEventStream.listen((event) {
      // Check for rapid events that might indicate performance issues
      _detectRapidEvents();
      
      // Get current zoom from camera
      final currentZoom = _mapController.camera.zoom;
      
      // CRITICAL FIX: Only update state during significant zoom changes
      if (event is MapEventMove) {
        // During move, only update state if zoom actually changed significantly
        if ((_lastZoomLevel - currentZoom).abs() > 0.1) {  // Increased threshold from 0.01 to 0.1
          setState(() {
            _isZooming = true;
            _lastZoomLevel = currentZoom;
          });
          
          // Reset the debounce timer when zoom changes during movement
          _zoomDebounceTimer?.cancel();
          _zoomDebounceTimer = Timer(_zoomDebounceDelay, () {
            if (mounted) {
              setState(() {
                _isZooming = false;
                _lastZoomLevel = currentZoom;
              });
              // Force update state manager with final zoom level
              _stateManager.updateZoomLevel(currentZoom);
            }
          });
        } else {
          // CRITICAL FIX: For pure panning (no zoom change), use a more efficient update
          if (mounted) {
            // Use a microtask to batch visual updates
            Future.microtask(() {
              if (mounted) {
                setState(() {
                  // Only update camera position, not zoom state
                });
              }
            });
          }
        }
      } else if (event is MapEventMoveStart) {
        // On move start, capture the current zoom as starting point
        _lastZoomLevel = currentZoom;
      } else if (event is MapEventMoveEnd) {
        // Movement ended, update zoom and perform any needed tasks
        setState(() {
          _isZooming = false;
          _lastZoomLevel = currentZoom;
        });
        
        // Force update state manager with final zoom level
        _stateManager.updateZoomLevel(currentZoom);
        
        // After the map stops moving, prefetch tiles for the visible area
        _prefetchVisibleRegion();
      }
    });
    
    // Schedule a post-frame callback to update the map position
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeMapPosition();
      }
    });

    // Add a post-frame callback to set up the MapSettingsProvider listener
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Get MapSettingsProvider and sync with map state manager
      if (mounted) {
        final mapSettings = Provider.of<MapSettingsProvider>(context, listen: false);
        // Initialize 3D buildings state
        _stateManager.setUse3DBuildings(mapSettings.use3DBuildings);
        
        // Listen for changes to MapSettingsProvider
        mapSettings.addListener(_onMapSettingsChanged);
        
        // Initialize the new zoom system
        _initializeNewZoomSystem();
      }
    });
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Store reference to provider when dependencies change
    _mapSettingsProvider = Provider.of<MapSettingsProvider>(context, listen: false);
  }

  @override
  void dispose() {
    if (_hasNewZoomSystem && _zoomCoordinator != null) {
      _zoomCoordinator!.dispose();
    }
    
    // Safely remove listener using stored reference
    if (_mapSettingsProvider != null) {
      _mapSettingsProvider!.removeListener(_onMapSettingsChanged);
    }
    
    _zoomDebounceTimer?.cancel();
    _emergencyModeTimer?.cancel();
    _rapidEventTimer?.cancel();
    _animationController.dispose();
    _moveAnimationController?.dispose();
    _stateManager.dispose();
    _mapController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Create markers from pins using the factory
    final markers = MarkersFactory.createMarkers(widget.mapProvider.pins, widget.onPinTap);
    
    // Get MapSettingsProvider to access 3D buildings setting
    final mapSettings = Provider.of<MapSettingsProvider>(context);
    
    return AnimatedBuilder(
      animation: Listenable.merge([_tiltAnimation, _stateManager]),
      builder: (context, child) {
        return Stack(
          children: [
            GestureDetector(
              // Add gesture controls for tilt and rotation adjustment
              onVerticalDragUpdate: (details) {
                // Adjust tilt on vertical drag
                final newTilt = _stateManager.tiltAngle - details.delta.dy * 0.01;
                // Limit tilt range for good UX
                if (newTilt >= 0 && newTilt <= MapStyles.maxTiltAngle) {
                  _stateManager.setTiltAngle(newTilt);
                    _tiltAnimation = Tween<double>(
                      begin: _tiltAnimation.value,
                    end: _stateManager.tiltAngle,
                    ).animate(CurvedAnimation(
                      parent: _animationController,
                      curve: Curves.easeOutCubic,
                    ));
                    _animationController.reset();
                    _animationController.forward();
                }
              },
              onHorizontalDragUpdate: (details) {
                // Adjust rotation on horizontal drag
                _stateManager.setRotationAngle(_stateManager.rotationAngle + details.delta.dx * 0.005);
              },
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Apply perspective transformation for the 2.5D effect
                  return Transform(
                    alignment: Alignment.center,
                    transform: MapTransformer.build25DMatrix(_tiltAnimation.value),
                    child: MapTransformer.buildTiltedContainer(
                      tiltValue: _tiltAnimation.value,
                      constraints: constraints,
                      child: Stack(
                        children: [
                          PopupScope(
                            popupController: _popupController,
                            child: FlutterMap(
                              mapController: _mapController,
                              options: MapOptions(
                                initialCenter: _stateManager.currentCenter,
                                initialZoom: 16.0,
                                minZoom: MapConstants.minZoom,
                                maxZoom: MapConstants.maxZoom,
                                onMapEvent: (event) {
                                  // Forward events to controller wrapper
                                  debugPrint('Map event: ${event.runtimeType}');
                                  // Controller wrapper has a subscription to mapEventStream
                                },
                                // Add tap handler for feature identification
                                onTap: (_, point) => _handleMapTap(point),
                                // Add rotation for the map itself
                                rotation: _stateManager.rotationAngle,
                                interactionOptions: const InteractionOptions(
                                  enableScrollWheel: true,
                                  enableMultiFingerGestureRace: true,
                                  flags: InteractiveFlag.all, // Enable all interactions
                                ),
                                maxBounds: null, // Remove bounds restriction
                              ),
                              children: [
                                // Base tile layer (Uber-like styled map)
                                MapLayersBuilder.buildBaseTileLayer(
                                  urlTemplate: MapStyles.getTileUrl(isDarkMode),
                                  tiltAnimationValue: _tiltAnimation.value,
                                  errorTileCallback: _handleTileError,
                                  backgroundColor: isDarkMode ? Colors.black : Colors.white,
                                  context: context,
                                ),
                                
                                // First Fallback tile layer - uses OSM with dark overlay
                                if (_stateManager.isUsingFallbackTiles && _stateManager.fallbackLevel == TileFallbackLevel.fallback1)
                                  MapLayersBuilder.buildFallbackTileLayer(
                                    urlTemplate: MapStyles.getFallbackTileUrl(isDarkMode),
                                    tiltAnimationValue: _tiltAnimation.value,
                                    errorTileCallback: _handleTileError,
                                    backgroundColor: isDarkMode ? Colors.black : Colors.white,
                                    context: context,
                                  ),
                                
                                // Second Fallback tile layer - uses OSM France with dark overlay
                                if (_stateManager.isUsingFallbackTiles && _stateManager.fallbackLevel == TileFallbackLevel.fallback2)
                                  MapLayersBuilder.buildFallbackTileLayer(
                                    urlTemplate: MapStyles.getSecondaryFallbackTileUrl(isDarkMode),
                                    tiltAnimationValue: _tiltAnimation.value,
                                    errorTileCallback: _handleTileError,
                                    backgroundColor: isDarkMode ? Colors.black : Colors.white,
                                    context: context,
                                  ),
                                
                                // Third Fallback tile layer - uses Stamen Toner (already dark)
                                if (_stateManager.isUsingFallbackTiles && _stateManager.fallbackLevel == TileFallbackLevel.fallback3)
                                  MapLayersBuilder.buildFallbackTileLayer(
                                    urlTemplate: MapStyles.getTertiaryFallbackTileUrl(isDarkMode),
                                    tiltAnimationValue: _tiltAnimation.value,
                                    errorTileCallback: _handleTileError,
                                    backgroundColor: isDarkMode ? Colors.black : Colors.white,
                                    context: context,
                                  ),
                                
                                // Dynamic layers based on settings
                                ...(
                                // Either use real OSM data or decorative layers based on the toggle
                                  _stateManager.useRealOSMData 
                                  ? () {
                                      // CRITICAL FIX: Always get fresh map camera during panning
                                      final mapCamera = _safeGetMapCamera();
                                      
                                      // Always force fresh camera during pan/move for smoother rendering
                                      if (_controllerWrapper.isMoving && mapCamera == null) {
                                        debugPrint('⚠️ Warning: No map camera available during movement!');
                                      }
                                      
                                      // During active zooming, we can optionally:
                                      // 1. Show simplified versions of the layers
                                      // 2. Freeze layer updates until zooming completes
                                      // 3. Apply scaling transformations to keep objects at consistent scale
                                      
                                      // Determine which zoom level to use for rendering during transitions
                                      final effectiveZoomLevel = _isZooming 
                                          ? _lastZoomLevel // Use the last stable zoom level during transitions
                                          : _stateManager.currentZoom;
                                      
                                      // Determine the appropriate detail level for the current state
                                      final effectiveDetailLevel = _isZooming
                                          ? math.max(1, _stateManager.buildingDetailLevel - 1) // Reduce detail during zoom
                                          : _stateManager.buildingDetailLevel;
                                      
                                      // Allow the visibleBounds to update during zooming, but use stable zoom level
                                      return MapLayersBuilder.buildOSMDataLayers(
                                        tiltValue: _tiltAnimation.value,
                                        zoomLevel: effectiveZoomLevel,
                                        isMapMoving: _controllerWrapper.isMoving || _isZooming,
                                        visibleBounds: _stateManager.visibleBounds,
                                        detailLevel: effectiveDetailLevel,
                                        mapCamera: mapCamera,
                                        emergencyPerformanceMode: _emergencyPerformanceMode,
                                        use3DBuildings: mapSettings.use3DBuildings, // Pass the 3D buildings setting here
                                      );
                                    }()
                                  : () {
                                      // Add a safety check for the map camera for decorative layers too
                                      final mapCamera = _safeGetMapCamera();
                                      
                                      return MapLayersBuilder.buildDecorativeLayers(
                                        tiltValue: _tiltAnimation.value,
                                        mapCamera: mapCamera,
                                        use3DBuildings: mapSettings.use3DBuildings, // Pass the 3D buildings setting here as well
                                      );
                                    }()
                                ),
                                
                                // Add user location marker and aura
                                if (widget.mapProvider.currentPosition != null)
                                  Stack(
                                    children: [
                                      // LoadingAuraOverlay removed - no more pulsing animation
                                      // LoadingAuraOverlay(
                                      //   userLocation: LatLng(
                                      //     widget.mapProvider.currentPosition!.latitude,
                                      //     widget.mapProvider.currentPosition!.longitude
                                      //   ),
                                      //   zoomLevel: _stateManager.currentZoom,
                                      //   mapController: _mapController,
                                      // ),
                                      
                                      CircleLayer(
                                        circles: [
                                          CircleMarker(
                                            point: LatLng(
                                              widget.mapProvider.currentPosition!.latitude,
                                              widget.mapProvider.currentPosition!.longitude
                                            ),
                                            radius: 8,
                                            color: Colors.blue.withOpacity(0.7),
                                            borderColor: Colors.white,
                                            borderStrokeWidth: 2,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  
                                // Music pins layer
                                MarkerClusterLayerWidget(
                                  options: MarkerClusterLayerOptions(
                                    maxClusterRadius: 45,
                                    size: const Size(40, 40),
                                    padding: const EdgeInsets.all(50),
                                    markers: markers,
                                    builder: (context, markers) {
                                      return MarkersFactory.buildCluster(context, markers);
                                    },
                                  ),
                                ),
                                
                                // Popup layer for pins
                                PopupMarkerLayerWidget(
                                  options: PopupMarkerLayerOptions(
                                    markers: markers,
                                    popupController: _popupController,
                                    popupDisplayOptions: PopupDisplayOptions(
                                      builder: (_, Marker marker) => MapPopupBuilder(marker: marker),
                                      snap: PopupSnap.markerTop,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          // Map loading indicators - REMOVED as requested
                          // MapLoadingIndicator(
                          //   isMapMoving: _controllerWrapper.isMoving,
                          //   buildingDetailLevel: _stateManager.buildingDetailLevel,
                          //   isGettingUserLocation: _isGettingUserLocation,
                          //   isProviderLoading: widget.mapProvider.isLoading,
                          //   initialLoadAttempted: _stateManager.initialLoadAttempted,
                          // ),
                          
                          // Remove the map controls since they're now in MapScreen
                          // DEBUG: Zoom level indicator overlay
                          Positioned(
                            top: MediaQuery.of(context).padding.top + 80,
                            right: 16,
                            child: Container(), // Removed MapDebugInfo widget
                          ),
                            
                          // Feature identification overlay
                          if (_stateManager.showFeatureInfo && _stateManager.tappedLocation != null)
                            Positioned(
                              bottom: 160,
                              left: 10,
                              right: 10,
                              child: GestureDetector(
                                onTap: () => _stateManager.clearFeatureInfo(),
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.7),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            "Feature Info at (${_stateManager.tappedLocation!.latitude.toStringAsFixed(6)}, ${_stateManager.tappedLocation!.longitude.toStringAsFixed(6)})",
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          InkWell(
                                            onTap: () => _stateManager.clearFeatureInfo(),
                                            child: const Icon(Icons.close, color: Colors.white, size: 20),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      _stateManager.isLoadingFeatureInfo
                                          ? const Center(
                                              child: SizedBox(
                                                width: 20,
                                                height: 20,
                                                child: CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                                ),
                                              ),
                                            )
                                          : Text(
                                              _stateManager.featureInfoText,
                                              style: const TextStyle(color: Colors.white),
                                            ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Error handler component - modified to suppress initial loading messages
            MapErrorHandler(
              mapLoadFailed: false, // Force to false to suppress the map loading retry button
              hasNetworkError: widget.mapProvider.hasNetworkError,
              initialLoadAttempted: true, // Force to true to suppress "Loading map..." message
              errorMessage: _stateManager.errorMessage ?? widget.mapProvider.errorMessage,
              onRetry: _retryMapLoading,
            ),
          ],
        );
      },
    );
  }
  
  @override
  void didUpdateWidget(FlutterMapWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Check if the map provider instance has changed
    if (widget.mapProvider != oldWidget.mapProvider) {
      // Something in the MapProvider changed
      
      // Check if the center position changed
      if (widget.mapProvider.currentCenter != oldWidget.mapProvider.currentCenter) {
        // The provider's center was updated (likely through animateToLocation)
        animateToLocation(
          widget.mapProvider.currentCenter.latitude, 
          widget.mapProvider.currentCenter.longitude,
          zoom: widget.mapProvider.zoom
        );
      }
      
      // Other provider changes can be handled here
    }
  }
  
  @override
  void deactivate() {
    // This is called when the widget is removed from tree temporarily (like during navigation)
    // Pause any animations or processing that shouldn't run while invisible
    debugPrint('Map widget deactivated - preserving state while invisible');
    _zoomDebounceTimer?.cancel();
    _emergencyModeTimer?.cancel();
    _rapidEventTimer?.cancel();
    
    // Don't call _controllerWrapper dispose here since we want to preserve state
    super.deactivate();
  }
  
  @override
  void activate() {
    // This is called when the widget is re-inserted into the tree
    debugPrint('Map widget reactivated - resuming from preserved state');
    super.activate();
    
    // Don't reload the whole map, just refresh the visual state
    if (mounted) {
      setState(() {
        // No need to change any state, just trigger a rebuild
      });
    }
  }
  
  // Method to reset the map view (e.g., when clicked on map tab while already on map)
  void resetMapView() {
    // Center on current location if available
    if (widget.mapProvider.currentPosition != null) {
      final lat = widget.mapProvider.currentPosition!.latitude;
      final lng = widget.mapProvider.currentPosition!.longitude;
      animateToLocation(lat, lng, zoom: _initialZoomLevel.toDouble());
    } else {
      // If location not available, center on default location
      animateToLocation(
        AppConstants.defaultLatitude, 
        AppConstants.defaultLongitude,
        zoom: 15.0
      );
    }
  }
  
  // Method to animate to location with smooth transitions
  void animateToLocation(double lat, double lng, {double? zoom}) {
    if (!mounted) return;
    
    debugPrint('Animating to location: $lat, $lng with zoom: $zoom');
    _stateManager.moveTo(
      position: LatLng(lat, lng),
      zoom: zoom,
      animate: true,
    );
  }
  
  // Handle map taps for feature identification
  void _handleMapTap(LatLng point) async {
    debugPrint('Map tapped at: ${point.latitude}, ${point.longitude}');
    
    // Get the MapSettingsProvider to check if feature info is enabled
    final mapSettings = Provider.of<MapSettingsProvider>(context, listen: false);
    
    // If feature info is disabled, do nothing
    if (!mapSettings.showFeatureInfo) {
      return;
    }
    
    _stateManager.setFeatureInfo(
      location: point,
      info: "Loading feature information...",
      isLoading: true,
    );
    
    try {
      // Create a small bounding box around the tapped point
      final double delta = MapConstants.featureIdentificationRadius;
      final southWest = LatLng(point.latitude - delta, point.longitude - delta);
      final northEast = LatLng(point.latitude + delta, point.longitude + delta);
      
      // Use the Overpass API to query features at this location
      final features = await _queryOSMFeatures(southWest, northEast);
      
      if (mounted) {
          if (features.isEmpty) {
          _stateManager.setFeatureInfo(
            location: point,
            info: "No features found at this location.",
            isLoading: false,
          );
          } else {
          _stateManager.setFeatureInfo(
            location: point,
            info: _formatFeatureInfo(features),
            isLoading: false,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _stateManager.setFeatureInfo(
          location: point,
          info: "Error loading feature information: ${e.toString()}",
          isLoading: false,
        );
      }
      debugPrint('Error getting feature info: $e');
    }
  }
  
  // Query OSM features at a location using Overpass API
  Future<List<Map<String, dynamic>>> _queryOSMFeatures(LatLng southWest, LatLng northEast) async {
    try {
      // Create a bounding box string for the Overpass API query
      final bbox = '${southWest.latitude},${southWest.longitude},${northEast.latitude},${northEast.longitude}';
      
      // Construct an Overpass API query
      // This query gets buildings, highways, amenities, and other common features
      final query = """
        [out:json];
        (
          node(${bbox});
          way(${bbox});
          relation(${bbox});
        );
        out body;
        >;
        out skel qt;
      """;
      
      // Encode the query for the GET request
      final encodedQuery = Uri.encodeComponent(query);
      
      // Choose an endpoint from the list
      const endpoint = 'https://overpass-api.de/api/interpreter';
      
      // Make the request
      final uri = Uri.parse('$endpoint?data=$encodedQuery');
      final response = await http.get(uri, headers: {
        'Accept': 'application/json',
        'User-Agent': 'BOPMaps Flutter App',
      }).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> elements = data['elements'] ?? [];
        
        // Convert the elements to a simpler list of features
        final List<Map<String, dynamic>> features = [];
        
        for (final element in elements) {
          // Add features that actually have tags (contain useful information)
          if (element['tags'] != null && (element['tags'] as Map).isNotEmpty) {
            final feature = {
              'id': element['id'],
              'type': element['type'],
              'tags': element['tags'],
            };
            
            // For nodes, include coordinates directly
            if (element['type'] == 'node') {
              feature['lat'] = element['lat'];
              feature['lon'] = element['lon'];
            }
            
            features.add(feature);
          }
        }
        
        return features;
      } else {
        debugPrint('Error from Overpass API: ${response.statusCode} ${response.body}');
        return [];
      }
    } catch (e) {
      debugPrint('Error querying OSM features: $e');
      return [];
    }
  }
  
  // Format OSM features into readable text
  String _formatFeatureInfo(List<Map<String, dynamic>> features) {
    StringBuffer buffer = StringBuffer();
    buffer.writeln("${features.length} features found:");
    buffer.writeln();
    
    int count = 0;
    for (final feature in features) {
      // Limit the number of features shown to avoid overwhelming the UI
      if (count >= 10) {
        buffer.writeln("...and ${features.length - 10} more");
        break;
      }
      
      final type = feature['type'];
      final tags = feature['tags'] as Map<String, dynamic>;
      
      // Get the most descriptive tag for this feature
      String name = tags['name'] ?? 
                    tags['ref'] ?? 
                    tags['addr:housenumber'] ?? 
                    tags['building'] ?? 
                    tags['highway'] ?? 
                    tags['amenity'] ?? 
                    tags['leisure'] ?? 
                    tags['landuse'] ?? 
                    type;
      
      // Add feature type if we have it
      if (tags['building'] != null) {
        name = "${tags['building'] == 'yes' ? 'Building' : tags['building']}${tags['name'] != null ? ': ${tags['name']}' : ''}";
      } else if (tags['highway'] != null) {
        name = "${tags['highway'].toString().toUpperCase()}${tags['name'] != null ? ': ${tags['name']}' : ''}";
      } else if (tags['amenity'] != null) {
        name = "${tags['amenity'].toString().toUpperCase()}${tags['name'] != null ? ': ${tags['name']}' : ''}";
      }
      
      buffer.writeln("• $name");
      
      // Add a few key details if available
      if (tags['addr:street'] != null) {
        final houseNumber = tags['addr:housenumber'] ?? '';
        buffer.writeln("  Address: $houseNumber ${tags['addr:street']}");
      }
      
      // Add a blank line between features
      buffer.writeln();
      count++;
    }
    
    return buffer.toString();
  }
  
  // Public method to toggle tilt (2D/3D)
  void toggleTilt() async {
    debugPrint('🗺️ MAP: Toggle tilt button pressed');
    
    try {
      // Use the centralized settings method to toggle 3D mode
      final mapSettings = Provider.of<MapSettingsProvider>(context, listen: false);
      final newIs3D = await mapSettings.toggleMapViewMode();
      
      debugPrint('🗺️ MAP: Map view toggled to ${newIs3D ? "3D" : "2D"} mode via settings');
      
      // The _onMapSettingsChanged listener will handle the actual tilt change
      // No need to do anything else here as the listener will update the UI
    } catch (e) {
      debugPrint('🗺️ MAP: Error toggling tilt: $e');
      
      // Fallback to direct toggle if settings provider fails
      if (_hasNewZoomSystem && _zoomCoordinator != null) {
        debugPrint('🗺️ MAP: Falling back to direct toggle with zoom coordinator');
        _zoomCoordinator!.toggle2D3DMode();
      } else {
        debugPrint('🗺️ MAP: Falling back to direct toggle with legacy implementation');
        _toggleTilt();
      }
    }
  }

  // Legacy tilt toggling implementation using direct angles
  void _toggleTilt() {
    debugPrint('Current tilt angle: ${_stateManager.tiltAngle}');
    setState(() {
      // Force exact values for 2D (0.0) and 3D (defaultTiltAngle)
      // to ensure we can properly toggle between states
      final is3D = _stateManager.tiltAngle > 0.01; // Use a smaller threshold
      final newTiltAngle = is3D ? 0.0 : MapConstants.defaultTiltAngle;
      debugPrint('Toggling tilt from ${_stateManager.tiltAngle} to $newTiltAngle');
      
      // Update state manager with the exact value
      _stateManager.setTiltAngle(newTiltAngle);
      
      // Always keep 3D buildings enabled to ensure they render properly
      // The tilt angle will determine if they appear 3D (tilted) or 2D (flat)
      _stateManager.setUse3DBuildings(true);
      
      // Create a new animation with exact values
      _tiltAnimation = Tween<double>(
        begin: is3D ? _stateManager.tiltAngle : 0.0,
        end: newTiltAngle,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ));
      
      // Reset and start the animation
      _animationController.reset();
      _animationController.forward();
      
      // Update MapSettingsProvider if needed
      try {
        final mapSettings = Provider.of<MapSettingsProvider>(context, listen: false);
        final newUse3DBuildings = !is3D; // Toggle to opposite of current state
        
        // Only update if there's a change needed
        if (mapSettings.use3DBuildings != newUse3DBuildings) {
          debugPrint('Updating MapSettingsProvider.use3DBuildings to $newUse3DBuildings');
          mapSettings.toggle3DBuildings();
        }
      } catch (e) {
        debugPrint('Error updating MapSettingsProvider: $e');
      }
    });
  }

  // Public methods for external access from MapScreen
  // These are called by the MapScreen UI controls
  void zoomIn() {
    // Use the consolidated zoom system if available
    if (_hasNewZoomSystem) {
      _zoomCoordinator!.zoomIn();
    } else {
      // Fall back to legacy implementation
      _zoomIn();
    }
  }
  
  void zoomOut() {
    // Use the consolidated zoom system if available
    if (_hasNewZoomSystem) {
      _zoomCoordinator!.zoomOut();
    } else {
      // Fall back to legacy implementation
      _zoomOut();
    }
  }

  // Handle location button press
  void _onLocationButtonPressed() {
    if (widget.mapProvider.currentPosition != null) {
      widget.mapProvider.toggleLocationTracking();
      animateToLocation(
        widget.mapProvider.currentPosition!.latitude,
        widget.mapProvider.currentPosition!.longitude,
        zoom: 16,
      );
    } else {
      // Try to request location again if not available
      _requestUserLocation(forceRefresh: true).then((userLocation) {
        if (mounted && userLocation != null) {
          widget.mapProvider.toggleLocationTracking();
          animateToLocation(
            userLocation.latitude,
            userLocation.longitude,
            zoom: 16,
          );
        } else if (mounted) {
          // Show snackbar after build is complete
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Could not determine your location. Please check your device settings.'),
                  duration: Duration(seconds: 3),
                ),
              );
            }
          });
        }
      });
    }
  }
  
  // Retry map loading
  void _retryMapLoading() {
    _stateManager.retryMapLoading();
    
    // Use user's location if available
    final LatLng targetLocation;
    if (widget.mapProvider.currentPosition != null) {
      targetLocation = LatLng(
        widget.mapProvider.currentPosition!.latitude,
        widget.mapProvider.currentPosition!.longitude
      );
      debugPrint('Retrying with user location: ${targetLocation.latitude}, ${targetLocation.longitude}');
    } else {
      targetLocation = LatLng(AppConstants.defaultLatitude, AppConstants.defaultLongitude);
      debugPrint('Retrying with default location');
      
      // Try to get user location if not available
      _requestUserLocation().then((userLocation) {
        if (mounted && userLocation != null) {
          // Move map to user location once we get it
          _stateManager.moveTo(
            position: userLocation,
            zoom: 16.0,
            animate: true,
          );
          debugPrint('Later moved map to user location: ${userLocation.latitude}, ${userLocation.longitude}');
        }
      });
    }
    
    // Try to reload map
    _stateManager.moveTo(
      position: targetLocation,
      zoom: 16.0,
      animate: false,
    );
  }

  // Separate method for initializing map position for better separation of concerns
  void _initializeMapPosition() {
    // Try to log helpful debug info
    if (widget.mapProvider.currentPosition != null) {
      print("🗺️ MAP: Initial location is already available at startup: "
          "Lat: ${widget.mapProvider.currentPosition!.latitude}, "
          "Lng: ${widget.mapProvider.currentPosition!.longitude}");
    } else {
      print("🗺️ MAP: No location available at startup, will request and animate when ready");
    }
    
    // Use user's current position if available, otherwise use default
    final LatLng initialPosition;
    if (widget.mapProvider.currentPosition != null) {
      initialPosition = LatLng(
        widget.mapProvider.currentPosition!.latitude,
        widget.mapProvider.currentPosition!.longitude
      );
      print("🗺️ MAP: Using user's current location as initial center: ${initialPosition.latitude}, ${initialPosition.longitude}");
      
      // Start at our more zoomed in level
      _stateManager.moveTo(
        position: initialPosition,
        zoom: _initialZoomLevel.toDouble(),
        animate: false,
      );
      
      // Flag that we already have the initial location
      _initialLocationRequested = true;
    } else {
      // If user location is not available, request it first
      print("🗺️ MAP: Requesting user location for initial positioning...");
      _initialLocationRequested = true;
      
      _requestUserLocation().then((userLocation) {
        if (mounted && userLocation != null) {
          // Move map to user location once we get it
          print("🗺️ MAP: Received delayed location, animating map to: ${userLocation.latitude}, ${userLocation.longitude}");
          
          // Animate to user location with more zoomed in level
          _stateManager.moveTo(
            position: userLocation,
            zoom: _initialZoomLevel.toDouble(),
            animate: true,
          );
          
          print("🗺️ MAP: Map animated to user location with zoom level: $_initialZoomLevel");
        } else if (mounted) {
          print("🗺️ MAP: Could not get user location, staying at default position");
        }
      });
      
      // Meanwhile use default position
      initialPosition = LatLng(AppConstants.defaultLatitude, AppConstants.defaultLongitude);
      print("🗺️ MAP: Using default location temporarily: ${initialPosition.latitude}, ${initialPosition.longitude}");
      
      // Start at our more zoomed in level
      _stateManager.moveTo(
        position: initialPosition,
        zoom: _initialZoomLevel.toDouble(),
        animate: false,
      );
    }
  }

  // Method to request user location if not already available
  Future<LatLng?> _requestUserLocation({bool forceRefresh = false}) async {
    if (!mounted) return null;
    
    try {
      // Disable the location loading indicator as requested
      // setState(() {
      //   _isGettingUserLocation = true;
      // });
      
      // Log to terminal that we're requesting user location
      print("🗺️ MAP: Requesting user location (forceRefresh: $forceRefresh)");
      
      // Use existing position if available and not forcing a refresh
      if (!forceRefresh && widget.mapProvider.currentPosition != null) {
        final existingLat = widget.mapProvider.currentPosition!.latitude; 
        final existingLng = widget.mapProvider.currentPosition!.longitude;
        
        print("🗺️ MAP: Using existing position: Lat: $existingLat, Lng: $existingLng");
        return LatLng(existingLat, existingLng);
      }
      
      // Reset failed attempts if forcing refresh
      if (forceRefresh) {
        _locationFailedAttempts = 0;
      }
      
      // Try to request permission and get location
      print("🗺️ MAP: Requesting location permission...");
      final permission = await widget.mapProvider.requestLocationPermission();
      print("🗺️ MAP: Location permission status: $permission");
      
      // Retry a few times with delays between attempts
      for (int attempt = 0; attempt < _maxLocationRetries; attempt++) {
        if (!mounted) return null;
        
        print("🗺️ MAP: Attempting to get user location (attempt ${attempt + 1}/$_maxLocationRetries)");
        // Force a refresh of the location in the provider
        await widget.mapProvider.refreshPins();
        
        // Wait for location to be acquired with a gradually increasing delay
        final delay = Duration(seconds: 1 + attempt);
        print("🗺️ MAP: Waiting ${delay.inSeconds}s for location update...");
        await Future.delayed(delay);
      
        if (widget.mapProvider.currentPosition != null) {
          _locationFailedAttempts = 0;
          final lat = widget.mapProvider.currentPosition!.latitude;
          final lng = widget.mapProvider.currentPosition!.longitude;
          
          print("🗺️ MAP: Successfully got user location on attempt ${attempt + 1}: Lat: $lat, Lng: $lng");
          
          // If this is first location and we've already initialized the map, animate to it
          if (_isFirstLocationUpdate && !forceRefresh) {
            _isFirstLocationUpdate = false;
            print("🗺️ MAP: This is the first location update, animating map to user location");
            
            // We'll handle this outside this method
          }
          
          return LatLng(lat, lng);
        }
      }
      
      // Increment failed attempts, useful for UI feedback
      _locationFailedAttempts++;
      print("🗺️ MAP: Failed to get user location after $_maxLocationRetries attempts. Total failures: $_locationFailedAttempts");
      
      // If we've failed multiple times, it might be a permissions or device issue
      if (_locationFailedAttempts >= 2) {
        print("🗺️ MAP: Multiple location failures - likely a permission or system issue");
      }
      
      return null;
    } catch (e) {
      print("🗺️ MAP: Error requesting user location: $e");
      _locationFailedAttempts++;
      return null;
    } finally {
      if (mounted) {
        // Disable the location loading indicator as requested
        // setState(() {
        //   _isGettingUserLocation = false;
        // });
      }
    }
  }

  // Modify the existing _safeGetMapCamera method to ensure latest camera data during panning
  MapCamera? _safeGetMapCamera() {
    try {
      // CRITICAL FIX: Always get fresh camera data during movement
      if (_mapController.camera != null) {
        // Force controller to update its state before returning camera
        final currentCamera = _mapController.camera;
        
        // CRITICAL FIX: Only add timestamp during actual movement to reduce unnecessary repaints
        if (_controllerWrapper.isMoving && !_isZooming) {
          // Use reflection to add a hidden property to the camera object
          // that won't affect its functionality but will make shouldRepaint detect it as different
          (currentCamera as dynamic)._lastUpdateTimestamp = DateTime.now().millisecondsSinceEpoch;
        }
        
        return currentCamera;
      }
    } catch (e) {
      debugPrint('Map camera not ready yet: $e');
    }
    return null;
  }
  
  // Improved zoom in with smoother animation handling
  void _zoomIn() {
    // Capture current zoom before starting zoom operation
    final currentZoom = _mapController.camera.zoom;
    final targetZoom = math.min(currentZoom + 1.0, MapConstants.maxZoom);
    
    // Only proceed if we're not already at max zoom
    if (currentZoom >= MapConstants.maxZoom) return;
    
    debugPrint('Zooming in from $currentZoom to $targetZoom');
    
    // Set zooming state
    setState(() {
      _isZooming = true;
      // Keep using current zoom level for rendering during animation
      _lastZoomLevel = currentZoom;
    });
    
    // Perform the zoom operation
    _stateManager.zoomIn();
    
    // Set a timer to end the zooming state after animation completes
    _zoomDebounceTimer?.cancel();
    _zoomDebounceTimer = Timer(_zoomDebounceDelay, () {
      if (mounted) {
        // Get the actual final zoom (might be different from targetZoom)
        final actualZoom = _mapController.camera.zoom;
        
        setState(() {
          _isZooming = false;
          _lastZoomLevel = actualZoom;
        });
        
        // Force the state manager to recognize the final zoom
        _stateManager.updateZoomLevel(actualZoom);
        debugPrint('Zoom in completed at $actualZoom');
      }
    });
  }
  
  // Improved zoom out with smoother animation handling
  void _zoomOut() {
    // Capture current zoom before starting zoom operation
    final currentZoom = _mapController.camera.zoom;
    final targetZoom = math.max(currentZoom - 1.0, MapConstants.minZoom);
    
    // Only proceed if we're not already at min zoom
    if (currentZoom <= MapConstants.minZoom) return;
    
    debugPrint('Zooming out from $currentZoom to $targetZoom');
    
    // Set zooming state
    setState(() {
      _isZooming = true;
      // Keep using current zoom level for rendering during animation
      _lastZoomLevel = currentZoom;
    });
    
    // Perform the zoom operation
    _stateManager.zoomOut();
    
    // Set a timer to end the zooming state after animation completes
    _zoomDebounceTimer?.cancel();
    _zoomDebounceTimer = Timer(_zoomDebounceDelay, () {
      if (mounted) {
        // Get the actual final zoom (might be different from targetZoom)
        final actualZoom = _mapController.camera.zoom;
        
        setState(() {
          _isZooming = false;
          _lastZoomLevel = actualZoom;
        });
        
        // Force the state manager to recognize the final zoom
        _stateManager.updateZoomLevel(actualZoom);
        debugPrint('Zoom out completed at $actualZoom');
      }
    });
  }

  // Detect rapid map events that might indicate a performance issue
  void _detectRapidEvents() {
    _rapidEventCounter++;
    
    // Reset the timer on each event
    _rapidEventTimer?.cancel();
    _rapidEventTimer = Timer(_rapidEventWindow, () {
      // If we had many events in a short window, might indicate performance issues
      if (_rapidEventCounter > _rapidEventThreshold) {
        _activateEmergencyMode();
      }
      
      // Reset counter after the window
      _rapidEventCounter = 0;
    });
  }
  
  // Activate emergency performance mode to prevent freezes
  void _activateEmergencyMode() {
    final now = DateTime.now();
    // Don't reactivate too frequently
    if (now.difference(_lastEmergencyModeActivation) < const Duration(seconds: 10)) {
      return;
    }
    
    debugPrint('🚨 EMERGENCY PERFORMANCE MODE ACTIVATED - Reducing detail to prevent freezes');
    setState(() {
      _emergencyPerformanceMode = true;
      _lastEmergencyModeActivation = now;
    });
    
    // Cancel any previous timer
    _emergencyModeTimer?.cancel();
    
    // Schedule timer to exit emergency mode after delay
    _emergencyModeTimer = Timer(_emergencyModeDuration, () {
      if (mounted) {
        setState(() {
          _emergencyPerformanceMode = false;
        });
        debugPrint('✅ Emergency performance mode deactivated');
      }
    });
  }
  
  // Allow external components to check if emergency mode is active
  bool get isInEmergencyMode => _emergencyPerformanceMode;

  // Add a method to clear the map cache
  void _clearMapCache({bool clearAll = true, String? dataType}) async {
    try {
      // 1. Get references to cache managers
      final cacheCoordinator = MapCacheCoordinator();
      final persistentCache = PersistentMapCache();
      
      // 2. Show a loading indicator for feedback
      setState(() {
        _emergencyPerformanceMode = true; // Use emergency mode temporarily to show we're working
      });
      
      // 3. Clear all types of caches or specific ones
      if (clearAll) {
        // Clear persistent storage first
        await persistentCache.clearAllCaches();
        
        // Force complete cache clearing for all data types by explicitly calling each type
        // This ensures buildings and all other layers are completely cleared
        await persistentCache.clearBuildingsCache();
        await persistentCache.clearRoadsCache();
        await persistentCache.clearPedestrianCache();
        await persistentCache.clearRecreationCache();
        await persistentCache.clearWaterCache();
        
        // Clear all memory caches, not just stale zones
        cacheCoordinator.clearAllMemoryCaches();
        
        // Clear all loaded zones completely, not just marking them stale
        final staleDuration = Duration.zero;
        cacheCoordinator.clearStaleZones(staleDuration);
        
        // For buildings specifically, try a more aggressive approach
        try {
          cacheCoordinator.clearTypeFromMemory('buildings');
          debugPrint('Buildings memory cache additionally cleared - extra check');
        } catch (e) {
          debugPrint('Error during buildings specific clearing: $e');
        }
        
        debugPrint('All map caches forcefully cleared, including all buildings');
      } else if (dataType != null) {
        // Handle specific data types
        switch (dataType) {
          case 'buildings':
            await persistentCache.clearBuildingsCache();
            // Also clear from memory cache
            try {
              cacheCoordinator.clearTypeFromMemory('buildings');
            } catch (e) {
              debugPrint('Error clearing buildings from memory: $e');
            }
            debugPrint('Buildings cache cleared completely');
            break;
          case 'roads':
            await persistentCache.clearRoadsCache();
            cacheCoordinator.clearTypeFromMemory('roads');
            debugPrint('Roads cache cleared');
            break;
          case 'pedestrian':
            await persistentCache.clearPedestrianCache();
            cacheCoordinator.clearTypeFromMemory('pedestrian');
            debugPrint('Pedestrian cache cleared');
            break;
          case 'recreation':
            await persistentCache.clearRecreationCache();
            cacheCoordinator.clearTypeFromMemory('recreation');
            debugPrint('Recreation cache cleared');
            break;
          case 'water':
            await persistentCache.clearWaterCache();
            cacheCoordinator.clearTypeFromMemory('water');
            debugPrint('Water features cache cleared');
            break;
          default:
            debugPrint('Unknown cache type: $dataType');
        }
      }
      
      // 4. Force map layers to reload by resetting state
      _stateManager.resetOSMDataLayers();
      
      // 5. Show a short snackbar with success message
      final message = clearAll 
          ? 'All map caches completely cleared' 
          : '${dataType?.capitalize() ?? "Unknown"} cache cleared';
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.green,
        ),
      );
      
      // 6. Force map reload by reinitialization - more aggressive approach
      final currentCenter = _stateManager.currentCenter;
      final currentZoom = _stateManager.currentZoom;
      
      // More significant position change to force complete reload
      final newCenter = LatLng(
        currentCenter.latitude + 0.002,
        currentCenter.longitude + 0.002
      );
      
      // First zoom out to force tile reload
      _stateManager.moveTo(
        position: newCenter,
        zoom: currentZoom - 2, // Zoom out more dramatically
        animate: true,
      );
      
      // Move back to original position after a short delay
      await Future.delayed(const Duration(milliseconds: 1000)); // Longer delay for full reload
      
      // Then zoom back in
      _stateManager.moveTo(
        position: currentCenter,
        zoom: currentZoom,
        animate: true,
      );
      
      // Turn off emergency mode
      setState(() {
        _emergencyPerformanceMode = false;
      });
      
      debugPrint('Map completely refreshed with all caches cleared');
    } catch (e) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error clearing cache: $e'),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.red,
        ),
      );
      
      setState(() {
        _emergencyPerformanceMode = false;
      });
      
      debugPrint('Error clearing map cache: $e');
    }
  }

  // This method is called when the widget is temporarily deactivated
  // but we want to preserve its state (for navigating screens)
  void preserveState() {
    debugPrint('Preserving map state for navigation');
    
    // Store any critical state in member variables
    _stateManager.saveStateToCache();
    
    // Cancel any potentially disruptive timers or animations
    _zoomDebounceTimer?.cancel();
    _emergencyModeTimer?.cancel();
    _rapidEventTimer?.cancel();
    
    // Optionally pause any heavy processing
    if (_emergencyPerformanceMode) {
      // Already in performance mode - keep it that way
    } else {
      // Set a flag to prevent heavy operations
      _emergencyPerformanceMode = true;
      _lastEmergencyModeActivation = DateTime.now();
    }
  }
  
  // This method is called when the widget is reactivated
  // and we want to restore its state
  void restoreState() {
    debugPrint('Restoring map state after navigation');
    
    // Restore state from cache if needed
    _stateManager.restoreStateFromCache();
    
    // Reset emergency performance mode
    if (DateTime.now().difference(_lastEmergencyModeActivation) > 
        const Duration(seconds: 5)) {
      _emergencyPerformanceMode = false;
    }
    
    // Force refresh for visual correctness
    if (mounted) {
      setState(() {
        // This triggers a rebuild with updated state
      });
    }
  }

  // On map settings changed
  void _onMapSettingsChanged() {
    try {
      // Get the map settings provider
      final mapSettings = Provider.of<MapSettingsProvider>(context, listen: false);
      
      // Update 3D buildings state - IMPORTANT: Keep this true even in 2D mode
      // to ensure buildings are rendered, just with zero tilt
      _stateManager.setUse3DBuildings(true);
      
      // Update the new zoom system if initialized
      if (_hasNewZoomSystem && _zoomCoordinator != null) {
        ZoomSystemIntegration.updateFromSettings(_zoomCoordinator!, mapSettings);
      }
      
      // Ensure 3D view is updated if needed
      final shouldBe3D = mapSettings.use3DBuildings;
      final is3D = _stateManager.tiltAngle > 0.01; // Use a smaller threshold for detecting 3D mode
      
      debugPrint('🗺️ MAP: MapSettings changed - should be 3D: $shouldBe3D, current is 3D: $is3D, tilt: ${_stateManager.tiltAngle}');
      
      if (shouldBe3D != is3D) {
        debugPrint('🗺️ MAP: Updating tilt to match settings (${shouldBe3D ? "3D" : "2D"} mode)');
        
        // Set the tilt angle directly based on the setting - always use exact values
        final newTiltAngle = shouldBe3D ? MapConstants.defaultTiltAngle : 0.0;
        
        // Directly update state manager
        setState(() {
          // Set exact tilt angle
          _stateManager.setTiltAngle(newTiltAngle);
          
          // Create a new animation with exact values
          _tiltAnimation = Tween<double>(
            begin: is3D ? _stateManager.tiltAngle : 0.0,
            end: newTiltAngle,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ));
          
          // Reset and start the animation
          _animationController.reset();
          _animationController.forward();
          
          debugPrint('🗺️ MAP: Tilt animation started from ${is3D ? _stateManager.tiltAngle : 0.0} to $newTiltAngle');
        });
      }
    } catch (e) {
      debugPrint('🗺️ MAP: Error in _onMapSettingsChanged: $e');
    }
  }

  // Add this function inside the FlutterMapWidgetState class if it doesn't exist
  void _handleTileError(Object error, StackTrace stackTrace) {
    _stateManager.handleTileError(error, stackTrace);
  }

  // Prefetch tiles for the current visible region
  void _prefetchVisibleRegion() {
    if (!mounted) return;
    
    try {
      // Get the visible bounds
      final bounds = _mapController.camera.visibleBounds;
      final zoom = _mapController.camera.zoom.round(); // Use rounded zoom level
      
      // Get the tile cache provider
      final tileCacheProvider = Provider.of<TileCacheProvider>(context, listen: false);
      
      // Only prefetch if the tile cache service is available
      if (tileCacheProvider.service != null) {
        // Get the current bounds
        final north = bounds.north;
        final south = bounds.south;
        final east = bounds.east;
        final west = bounds.west;
        
        // Request prefetching of the current view area
        tileCacheProvider.service!.prefetchTiles(
          north, south, east, west, [zoom]
        );
        
        debugPrint('Prefetching tiles for zoom $zoom in visible region');
      }
    } catch (e) {
      debugPrint('Error prefetching tiles: $e');
    }
  }

  // Initialize the new zoom system
  void _initializeNewZoomSystem() {
    try {
      // Get the map settings provider
      final mapSettings = Provider.of<MapSettingsProvider>(context, listen: false);
      
      // Initialize the new zoom system with our widget
      _zoomCoordinator = ZoomSystemIntegration.initializeInWidget(
        tickerProvider: this,
        mapController: _mapController,
        initialZoom: _stateManager.currentZoom,
        setState: setState,
        tiltAnimController: _animationController,
        tiltAnimation: _tiltAnimation,
        mapSettings: mapSettings,
      );
      
      // Register zoom listeners
      _zoomCoordinator!.addZoomingStateListener((isZooming) {
        if (mounted) {
          setState(() {
            _isZooming = isZooming;
          });
        }
      });
      
      // Listen for zoom changes to update lastZoomLevel
      _zoomCoordinator!.addZoomListener((zoom) {
        if (mounted) {
          setState(() {
            _lastZoomLevel = zoom;
          });
        }
        
        // Update the state manager to stay in sync
        _stateManager.updateZoomLevel(zoom);
      });
      
      _hasNewZoomSystem = true;
      
      debugPrint('🔍 New zoom system initialized');
    } catch (e) {
      debugPrint('❌ Error initializing new zoom system: $e');
      _hasNewZoomSystem = false;
    }
  }
} 