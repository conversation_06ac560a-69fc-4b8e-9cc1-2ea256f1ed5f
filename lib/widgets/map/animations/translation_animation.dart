import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import '../core/map_constants.dart';

/// Class to handle smooth transitions for map position changes
class TranslationAnimation {
  /// The animation controller for position changes
  late final AnimationController _controller;

  /// The animation for latitude values
  Animation<double>? _latAnimation;

  /// The animation for longitude values
  Animation<double>? _longAnimation;

  /// Callback when center position has changed
  final void Function(LatLng) _onCenterChanged;

  /// Current center position
  LatLng _center;

  /// Constructor
  TranslationAnimation({
    required TickerProvider vsync,
    required LatLng initialCenter,
    required void Function(LatLng) onCenterChanged,
  }) : _center = initialCenter,
       _onCenterChanged = onCenterChanged {
    _controller = AnimationController(
      vsync: vsync,
      duration: MapConstants.defaultAnimationDuration,
    )..addListener(_handleAnimationUpdate);
  }

  /// Current center position
  LatLng get center => _center;

  /// Whether the animation is currently running
  bool get isAnimating => _controller.isAnimating;

  /// Handle animation updates and notify listeners
  void _handleAnimationUpdate() {
    if (_latAnimation != null && _longAnimation != null) {
      final newCenter = LatLng(
        _latAnimation!.value,
        _longAnimation!.value,
      );
      _center = newCenter;
      _onCenterChanged(newCenter);
    }
  }

  /// Animate to a new center position with default duration
  void animateTo(LatLng newCenter) {
    animateToWithDuration(newCenter, MapConstants.defaultAnimationDuration);
  }

  /// Animate to a new center position with specified duration
  void animateToWithDuration(LatLng newCenter, Duration duration) {
    // If already at the target position, don't animate
    if (newCenter == _center) return;

    // Stop any ongoing animation
    _controller.stop();
    
    // Update controller duration if different
    if (_controller.duration != duration) {
      _controller.duration = duration;
    }

    // Create latitude animation
    _latAnimation = Tween<double>(
      begin: _center.latitude,
      end: newCenter.latitude,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    // Create longitude animation
    _longAnimation = Tween<double>(
      begin: _center.longitude,
      end: newCenter.longitude,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    // Reset and start the animation
    _controller.reset();
    _controller.forward();
  }

  /// Set center immediately without animation
  void setCenter(LatLng newCenter) {
    // Stop any ongoing animation
    _controller.stop();
    
    // Update the center
    _center = newCenter;
    
    // Notify listeners
    _onCenterChanged(newCenter);
  }

  /// Pan the map by the specified delta values
  void panBy({
    required double latitudeDelta,
    required double longitudeDelta,
  }) {
    final newCenter = LatLng(
      _center.latitude + latitudeDelta,
      _center.longitude + longitudeDelta,
    );
    animateTo(newCenter);
  }

  /// Clean up resources
  void dispose() {
    _controller.dispose();
  }
} 