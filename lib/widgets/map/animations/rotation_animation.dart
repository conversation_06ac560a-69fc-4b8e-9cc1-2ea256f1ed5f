import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../core/map_constants.dart';

/// Manages rotation animations for the map
class RotationAnimation {
  // Animation controller
  final AnimationController _controller;
  late Animation<double> _animation;
  
  // Current rotation angle in radians
  double _currentRotation;
  
  // Callback for when rotation changes
  final void Function(double)? onRotationChanged;
  
  /// Constructor
  RotationAnimation({
    required TickerProvider vsync,
    double initialRotation = 0.0,
    this.onRotationChanged,
  }) : _controller = AnimationController(
         duration: MapConstants.rotationAnimationDuration,
         vsync: vsync,
       ),
       _currentRotation = initialRotation {
    
    // Initialize animation
    _setupAnimation(initialRotation);
  }
  
  /// The current rotation animation
  Animation<double> get animation => _animation;
  
  /// The current rotation angle in radians
  double get currentRotation => _currentRotation;
  
  /// Set up the animation with a target rotation
  void _setupAnimation(double targetRotation) {
    _animation = Tween<double>(
      begin: _currentRotation,
      end: targetRotation,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
  }
  
  /// Animate to a new rotation angle (in radians)
  void animateTo(double newRotation) {
    // Normalize rotation angle to between 0 and 2*pi
    newRotation = newRotation % (2 * math.pi);
    
    // Choose the shortest path to the new rotation
    double currentNormalized = _currentRotation % (2 * math.pi);
    double diff = newRotation - currentNormalized;
    
    // If the difference is more than pi, it's shorter to go the other way
    if (diff.abs() > math.pi) {
      if (diff > 0) {
        newRotation -= 2 * math.pi;
      } else {
        newRotation += 2 * math.pi;
      }
    }
    
    _currentRotation = newRotation;
    
    // Create a new animation from current value to target
    _setupAnimation(newRotation);
    
    // Notify listener if provided
    if (onRotationChanged != null) {
      onRotationChanged!(newRotation);
    }
    
    // Reset and start animation
    _controller.reset();
    _controller.forward();
  }
  
  /// Reset rotation to 0 (north)
  void resetRotation() {
    animateTo(0.0);
  }
  
  /// Rotate by a delta amount (in radians)
  void rotateBy(double deltaRadians) {
    animateTo(_currentRotation + deltaRadians);
  }
  
  /// Dispose resources
  void dispose() {
    _controller.dispose();
  }
} 