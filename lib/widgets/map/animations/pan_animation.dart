import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import '../core/map_constants.dart';

/// Handles smooth panning animations between map positions
class PanAnimation {
  // Animation controller
  late final AnimationController _controller;
  
  // Animations for latitude and longitude
  Animation<double>? _latAnimation;
  Animation<double>? _lngAnimation;
  
  // Callback for when center changes
  final void Function(LatLng) _onCenterChanged;
  
  // Current center position
  LatLng _center;
  
  /// Constructor
  PanAnimation({
    required TickerProvider vsync,
    required LatLng initialCenter,
    required void Function(LatLng) onCenterChanged,
  }) : _center = initialCenter,
       _onCenterChanged = onCenterChanged {
    _controller = AnimationController(
      duration: MapConstants.defaultAnimationDuration,
      vsync: vsync,
    );
    
    // Add listener to apply animation values
    _controller.addListener(_onAnimationTick);
  }
  
  /// Current center position
  LatLng get center => _center;
  
  /// Check if animation is currently running
  bool get isAnimating => _controller.isAnimating;
  
  /// Apply the current animation values
  void _onAnimationTick() {
    if (_latAnimation == null || _lngAnimation == null) return;
    
    final newCenter = LatLng(
      _latAnimation!.value,
      _lngAnimation!.value,
    );
    
    // Only trigger callback if values actually changed
    if (newCenter.latitude != _center.latitude || 
        newCenter.longitude != _center.longitude) {
      _center = newCenter;
      _onCenterChanged(newCenter);
    }
  }
  
  /// Animate to a new center position
  void animateTo(LatLng target) {
    animateToWithDuration(target, MapConstants.defaultAnimationDuration);
  }
  
  /// Animate to a new center position with custom duration
  void animateToWithDuration(LatLng target, Duration duration) {
    // Stop any existing animation
    _controller.stop();
    
    // Update controller duration if different
    if (_controller.duration != duration) {
      _controller.duration = duration;
    }
    
    // Create new animations
    _latAnimation = Tween<double>(
      begin: _center.latitude,
      end: target.latitude,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _lngAnimation = Tween<double>(
      begin: _center.longitude,
      end: target.longitude,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    // Reset and start the animation
    _controller.reset();
    _controller.forward();
  }
  
  /// Pan map by given offsets in degrees
  void panBy(double latOffset, double lngOffset) {
    final newCenter = LatLng(
      _center.latitude + latOffset,
      _center.longitude + lngOffset,
    );
    animateTo(newCenter);
  }
  
  /// Update center position without animation
  void setCenter(LatLng newCenter) {
    if (_center != newCenter) {
      _center = newCenter;
      _onCenterChanged(newCenter);
    }
  }
  
  /// Clean up resources
  void dispose() {
    _controller.dispose();
  }
}

/// Custom tween for LatLng interpolation
class LatLngTween extends Tween<LatLng> {
  LatLngTween({required LatLng begin, required LatLng end}) 
    : super(begin: begin, end: end);

  @override
  LatLng lerp(double t) {
    return LatLng(
      begin!.latitude + (end!.latitude - begin!.latitude) * t,
      begin!.longitude + (end!.longitude - begin!.longitude) * t,
    );
  }
} 