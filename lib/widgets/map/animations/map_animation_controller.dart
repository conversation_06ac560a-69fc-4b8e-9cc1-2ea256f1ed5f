import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'translation_animation.dart';
import 'zoom_animation.dart';
import '../core/map_constants.dart';

/// Controller for handling all map animations including zoom and translation
class MapAnimationController {
  // Animation controllers for different animation types
  late final TranslationAnimation _translationAnimation;
  late final ZoomAnimation _zoomAnimation;
  
  // Callbacks for animations
  final void Function(LatLng) _onCenterChanged;
  final void Function(double) _onZoomChanged;
  
  /// Constructor
  MapAnimationController({
    required TickerProvider vsync,
    required LatLng initialCenter,
    required double initialZoom,
    required void Function(LatLng) onCenterChanged,
    required void Function(double) onZoomChanged,
    double minZoom = 2.0,
    double maxZoom = 19.0,
  }) : _onCenterChanged = onCenterChanged,
       _onZoomChanged = onZoomChanged {
    
    // Initialize translation animation
    _translationAnimation = TranslationAnimation(
      vsync: vsync,
      initialCenter: initialCenter,
      onCenterChanged: _onCenterChanged,
    );
    
    // Initialize zoom animation
    _zoomAnimation = ZoomAnimation(
      vsync: vsync,
      initialZoom: initialZoom,
      onZoomChanged: _onZoomChanged,
      minZoom: minZoom,
      maxZoom: maxZoom,
    );
  }
  
  /// Current center position
  LatLng get center => _translationAnimation.center;
  
  /// Current zoom level
  double get zoom => _zoomAnimation.zoom;
  
  /// Check if any animation is currently running
  bool get isAnimating => _translationAnimation.isAnimating || _zoomAnimation.isAnimating;
  
  /// Animate to a new center position and zoom level
  void animateTo({
    LatLng? center,
    double? zoom,
    Duration? duration,
  }) {
    final effectiveDuration = duration ?? MapConstants.defaultAnimationDuration;
    
    // Update center if provided
    if (center != null && center != this.center) {
      _translationAnimation.animateToWithDuration(center, effectiveDuration);
    }
    
    // Update zoom if provided
    if (zoom != null && zoom != this.zoom) {
      _zoomAnimation.animateToWithDuration(zoom, effectiveDuration);
    }
  }
  
  /// Update center and zoom without animation
  void moveTo({LatLng? center, double? zoom}) {
    if (center != null) {
      _translationAnimation.setCenter(center);
    }
    
    if (zoom != null) {
      _zoomAnimation.setZoom(zoom);
    }
  }
  
  /// Pan the map by the specified offsets
  void panBy(double latOffset, double lngOffset) {
    _translationAnimation.panBy(latOffset, lngOffset);
  }
  
  /// Zoom in by one level
  void zoomIn() {
    _zoomAnimation.zoomIn();
  }
  
  /// Zoom out by one level
  void zoomOut() {
    _zoomAnimation.zoomOut();
  }
  
  /// Zoom in or out by a custom amount
  void zoomBy(double amount) {
    if (amount > 0) {
      _zoomAnimation.zoomInBy(amount);
    } else if (amount < 0) {
      _zoomAnimation.zoomOutBy(-amount);
    }
  }
  
  /// Fit bounds with animation
  void fitBounds(LatLngBounds bounds, {double padding = 0.0}) {
    // Calculate center of bounds
    final center = bounds.center;
    
    // Calculate zoom level to fit bounds
    final double zoom = _calculateZoomLevel(bounds, padding);
    
    // Animate to new center and zoom
    animateTo(center: center, zoom: zoom);
  }
  
  /// Calculate zoom level to fit bounds
  double _calculateZoomLevel(LatLngBounds bounds, double padding) {
    // This is a simplified calculation - in a real implementation,
    // you would need to consider the map size and aspect ratio
    final double latDiff = (bounds.north - bounds.south).abs();
    final double lngDiff = (bounds.east - bounds.west).abs();
    
    // Choose the more constraining dimension
    final double maxDiff = latDiff > lngDiff ? latDiff : lngDiff;
    
    // Apply simple logarithmic calculation (customize this based on your needs)
    double zoom = 15 - (maxDiff * 10);
    
    // Apply padding by slightly reducing zoom
    if (padding > 0) {
      zoom -= padding / 100;
    }
    
    return zoom.clamp(_zoomAnimation._minZoom, _zoomAnimation._maxZoom);
  }
  
  /// Clean up resources
  void dispose() {
    _translationAnimation.dispose();
    _zoomAnimation.dispose();
  }
} 