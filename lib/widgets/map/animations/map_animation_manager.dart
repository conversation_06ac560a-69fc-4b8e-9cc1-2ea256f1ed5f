import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'pan_animation.dart';
import 'zoom_animation.dart';
import '../core/map_constants.dart';
import 'translation_animation.dart';

/// Manages all map animations including pan and zoom
class MapAnimationManager {
  // Controllers
  final PanAnimation _panAnimation;
  final ZoomAnimation _zoomAnimation;
  final MapController _mapController;
  
  // Current map state
  LatLng _currentCenter;
  double _currentZoom;
  
  /// Translation animation controller
  late final TranslationAnimation _translationAnimation;
  
  /// Constructor
  MapAnimationManager({
    required TickerProvider vsync,
    required MapController mapController,
    required LatLng initialCenter,
    required double initialZoom,
    required void Function(LatLng) onCenterChanged,
    required void Function(double) onZoomChanged,
  }) : _mapController = mapController,
       _currentCenter = initialCenter,
       _currentZoom = initialZoom,
       _panAnimation = PanAnimation(
         vsync: vsync,
         initialCenter: initialCenter,
         onCenterChanged: (center) {
           mapController.move(center, mapController.zoom);
         },
       ),
       _zoomAnimation = ZoomAnimation(
         vsync: vsync,
         initialZoom: initialZoom,
         onZoomChanged: (zoom) {
           mapController.move(mapController.center, zoom);
         },
       ) {
    // Initialize translation animation
    _translationAnimation = TranslationAnimation(
      vsync: vsync,
      initialCenter: initialCenter,
      onCenterChanged: onCenterChanged,
    );
  }
  
  /// Access to the pan animation controller
  PanAnimation get panAnimation => _panAnimation;
  
  /// Access to the zoom animation controller
  ZoomAnimation get zoomAnimation => _zoomAnimation;
  
  /// Current center position
  LatLng get currentCenter => _currentCenter;
  
  /// Current zoom level
  double get currentZoom => _currentZoom;
  
  /// Check if any animation is running
  bool get isAnimating => _translationAnimation.isAnimating || _zoomAnimation.isAnimating;
  
  /// Animate to a new position and zoom level
  void animateTo({
    LatLng? center,
    double? zoom,
    Duration? duration,
  }) {
    final effectiveDuration = duration ?? MapConstants.defaultAnimationDuration;
    
    // Animate center if provided
    if (center != null && center != this.currentCenter) {
      _translationAnimation.animateToWithDuration(center, effectiveDuration);
    }
    
    // Animate zoom if provided
    if (zoom != null && zoom != this.currentZoom) {
      _zoomAnimation.animateToWithDuration(zoom, effectiveDuration);
    }
  }
  
  /// Set map position without animation
  void setPosition({
    LatLng? center,
    double? zoom,
  }) {
    // Update center if provided
    if (center != null) {
      _translationAnimation.setCenter(center);
    }
    
    // Update zoom if provided
    if (zoom != null) {
      _zoomAnimation.setZoom(zoom);
    }
  }
  
  /// Pan the map by the specified offset
  void panBy({
    required double latitudeDelta,
    required double longitudeDelta,
  }) {
    _translationAnimation.panBy(
      latitudeDelta: latitudeDelta,
      longitudeDelta: longitudeDelta,
    );
  }
  
  /// Zoom in by one level (or specified delta)
  void zoomIn([double delta = 1.0]) {
    _zoomAnimation.zoomIn(delta);
  }
  
  /// Zoom out by one level (or specified delta)
  void zoomOut([double delta = 1.0]) {
    _zoomAnimation.zoomOut(delta);
  }
  
  /// Stop all running animations
  void stopAnimations() {
    _translationAnimation.setCenter(_translationAnimation.center);
    _zoomAnimation.setZoom(_zoomAnimation.zoom);
  }
  
  /// Animate to a position with bounds
  void animateToBounds(
    LatLngBounds bounds, {
    Duration? duration,
    double padding = 0.0,
  }) {
    // Calculate center and zoom to fit bounds
    final centerZoom = _mapController.centerZoomFitBounds(
      bounds,
      options: FitBoundsOptions(padding: EdgeInsets.all(padding)),
    );
    
    // Animate to the new position
    animateTo(
      center: centerZoom.center,
      zoom: centerZoom.zoom,
      duration: duration,
    );
  }
  
  /// Update current position (without animation)
  void updateCurrentPosition(LatLng center, double zoom) {
    _currentCenter = center;
    _currentZoom = zoom;
  }
  
  /// Dispose resources
  void dispose() {
    _panAnimation.dispose();
    _zoomAnimation.dispose();
    _translationAnimation.dispose();
  }
} 