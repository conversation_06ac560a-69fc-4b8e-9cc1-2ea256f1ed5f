import 'package:flutter/material.dart';
import '../core/map_constants.dart';

/// Handles smooth animations between different tilt angles
class TiltAnimation {
  // Animation controller
  late final AnimationController _controller;
  
  // Tilt animation
  Animation<double>? _tiltAnimation;
  
  // Callback for when tilt changes
  final void Function(double) _onTiltChanged;
  
  // Current tilt angle in radians
  double _tilt;
  
  /// Constructor
  TiltAnimation({
    required TickerProvider vsync,
    required double initialTilt,
    required void Function(double) onTiltChanged,
  }) : _tilt = initialTilt,
       _onTiltChanged = onTiltChanged {
    _controller = AnimationController(
      duration: MapConstants.defaultAnimationDuration,
      vsync: vsync,
    );
    
    // Add listener to apply animation values
    _controller.addListener(_onAnimationTick);
  }
  
  /// Current tilt angle in radians
  double get tilt => _tilt;
  
  /// Check if animation is currently running
  bool get isAnimating => _controller.isAnimating;
  
  /// Apply the current animation values
  void _onAnimationTick() {
    if (_tiltAnimation == null) return;
    
    final newTilt = _tiltAnimation!.value;
    
    // Only trigger callback if value actually changed
    if (newTilt != _tilt) {
      _tilt = newTilt;
      _onTiltChanged(newTilt);
    }
  }
  
  /// Animate to a new tilt angle
  void animateTo(double targetTilt) {
    animateToWithDuration(targetTilt, MapConstants.defaultAnimationDuration);
  }
  
  /// Animate to a new tilt angle with custom duration
  void animateToWithDuration(double targetTilt, Duration duration) {
    // Stop any existing animation
    _controller.stop();
    
    // Update controller duration if different
    if (_controller.duration != duration) {
      _controller.duration = duration;
    }
    
    // Create new animation
    _tiltAnimation = Tween<double>(
      begin: _tilt,
      end: targetTilt,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    // Reset and start the animation
    _controller.reset();
    _controller.forward();
  }
  
  /// Increment tilt by given amount (in radians)
  void increaseTilt(double amount) {
    animateTo(_tilt + amount);
  }
  
  /// Decrement tilt by given amount (in radians)
  void decreaseTilt(double amount) {
    animateTo(_tilt - amount);
  }
  
  /// Reset tilt to zero (flat map)
  void resetTilt() {
    animateTo(0.0);
  }
  
  /// Update tilt without animation
  void setTilt(double newTilt) {
    if (_tilt != newTilt) {
      _tilt = newTilt;
      _onTiltChanged(newTilt);
    }
  }
  
  /// Clean up resources
  void dispose() {
    _controller.dispose();
  }
} 