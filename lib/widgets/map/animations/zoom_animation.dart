import 'package:flutter/material.dart';
import '../core/map_constants.dart';

/// Class to handle smooth animations for map zoom level changes
class ZoomAnimation {
  late final AnimationController _controller;
  late Animation<double> _zoomAnimation;
  
  // Current zoom level
  double _zoom;
  
  // Zoom limits
  final double _minZoom;
  final double _maxZoom;
  
  // Callback for when the zoom changes
  final void Function(double) _onZoomChanged;
  
  /// Constructor
  ZoomAnimation({
    required TickerProvider vsync,
    required double initialZoom,
    required void Function(double) onZoomChanged,
    double minZoom = MapConstants.minZoom,
    double maxZoom = MapConstants.maxZoom,
  }) : _zoom = initialZoom,
       _minZoom = minZoom,
       _maxZoom = maxZoom,
       _onZoomChanged = onZoomChanged {
    
    // Initialize animation controller
    _controller = AnimationController(
      vsync: vsync,
      duration: MapConstants.zoomAnimationDuration,
    );
    
    // Add listener to trigger zoom updates
    _controller.addListener(_updateZoom);
    
    // Initialize animation (will be replaced when animating)
    _zoomAnimation = Tween<double>(begin: initialZoom, end: initialZoom)
        .animate(_controller);
  }
  
  /// Current zoom level
  double get zoom => _zoom;
  
  /// Minimum allowed zoom level
  double get minZoom => _minZoom;
  
  /// Maximum allowed zoom level
  double get maxZoom => _maxZoom;
  
  /// Check if animation is currently running
  bool get isAnimating => _controller.isAnimating;
  
  /// Update the zoom based on animation value
  void _updateZoom() {
    final newZoom = _zoomAnimation.value;
    if (newZoom != _zoom) {
      _zoom = newZoom;
      _onZoomChanged(newZoom);
    }
  }
  
  /// Animate to a new zoom level with default duration
  void animateTo(double newZoom) {
    animateToWithDuration(newZoom, MapConstants.zoomAnimationDuration);
  }
  
  /// Animate to a new zoom level with specified duration
  void animateToWithDuration(double newZoom, Duration duration) {
    // Clamp zoom level to valid range
    newZoom = newZoom.clamp(_minZoom, _maxZoom);
    
    if (newZoom == _zoom) return;
    
    // Update controller duration
    _controller.duration = duration;
    
    // Stop any running animation
    _controller.stop();
    
    // Set up new animation
    _zoomAnimation = Tween<double>(
      begin: _zoom,
      end: newZoom,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    // Reset controller and forward
    _controller.reset();
    _controller.forward();
  }
  
  /// Set zoom without animation
  void setZoom(double newZoom) {
    // Clamp zoom level to valid range
    newZoom = newZoom.clamp(_minZoom, _maxZoom);
    
    if (newZoom == _zoom) return;
    
    // Stop any running animation
    _controller.stop();
    
    // Update zoom
    _zoom = newZoom;
    _onZoomChanged(newZoom);
    
    // Update animation value
    _zoomAnimation = Tween<double>(begin: newZoom, end: newZoom)
        .animate(_controller);
  }
  
  /// Zoom in by one level (or specified delta)
  void zoomIn([double delta = 1.0]) {
    animateTo(_zoom + delta);
  }
  
  /// Zoom out by one level (or specified delta)
  void zoomOut([double delta = 1.0]) {
    animateTo(_zoom - delta);
  }
  
  /// Zoom in by a specific amount (used by MapAnimationController)
  void zoomInBy(double amount) {
    if (amount <= 0) return;
    animateTo(_zoom + amount);
  }
  
  /// Zoom out by a specific amount (used by MapAnimationController)
  void zoomOutBy(double amount) {
    if (amount <= 0) return;
    animateTo(_zoom - amount);
  }
  
  /// Clean up resources
  void dispose() {
    _controller.removeListener(_updateZoom);
    _controller.dispose();
  }
} 