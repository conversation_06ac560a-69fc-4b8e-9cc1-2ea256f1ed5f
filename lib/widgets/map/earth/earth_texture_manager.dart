import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui' as ui;

class EarthTextureManager {
  static final EarthTextureManager _instance = EarthTextureManager._internal();
  factory EarthTextureManager() => _instance;
  
  EarthTextureManager._internal();
  
  ui.Image? _dayTexture;
  ui.Image? _nightTexture;
  ui.Image? _specularMap;
  ui.Image? _bumpMap;
  ui.Image? _cloudMap;
  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;
  
  final List<VoidCallback> _listeners = [];
  
  bool get isLoaded => _dayTexture != null;
  bool get hasError => _hasError;
  String? get errorMessage => _errorMessage;
  ui.Image? get dayTexture => _dayTexture;
  ui.Image? get nightTexture => _nightTexture;
  ui.Image? get specularMap => _specularMap;
  ui.Image? get bumpMap => _bumpMap;
  ui.Image? get cloudMap => _cloudMap;
  
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }
  
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }
  
  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }
  
  Future<void> loadTextures() async {
    if (_isLoading || isLoaded) return;
    
    _isLoading = true;
    _hasError = false;
    _errorMessage = null;
    
    try {
      debugPrint('Loading Earth textures...');
      
      // Load day texture first
      _dayTexture = await _loadImageFromAsset(
        'assets/textures/earth_day.jpg',
        targetWidth: 4096,
        targetHeight: 2048,
      );
      
      if (_dayTexture == null) {
        throw Exception('Failed to load day texture');
      }
      
      debugPrint('Day texture loaded successfully: ${_dayTexture!.width}x${_dayTexture!.height}');
      
      // Load night texture
      _nightTexture = await _loadImageFromAsset(
        'assets/textures/earth_night.jpg',
        targetWidth: 4096,
        targetHeight: 2048,
      );
      
      if (_nightTexture != null) {
        debugPrint('Night texture loaded successfully: ${_nightTexture!.width}x${_nightTexture!.height}');
      }
      
      // Load specular map
      _specularMap = await _loadImageFromAsset(
        'assets/textures/earth_specular.jpg',
        targetWidth: 2048,
        targetHeight: 1024,
      );
      
      // Load bump map
      _bumpMap = await _loadImageFromAsset(
        'assets/textures/earth_bump.jpg',
        targetWidth: 2048,
        targetHeight: 1024,
      );
      
      // Load cloud map
      _cloudMap = await _loadImageFromAsset(
        'assets/textures/earth_clouds.jpg',
        targetWidth: 2048,
        targetHeight: 1024,
      );
      
      debugPrint('Earth textures loaded successfully');
      _notifyListeners();
      
    } catch (e) {
      debugPrint('Error loading Earth textures: $e');
      _hasError = true;
      _errorMessage = e.toString();
      _dayTexture?.dispose();
      _nightTexture?.dispose();
      _specularMap?.dispose();
      _bumpMap?.dispose();
      _cloudMap?.dispose();
      _dayTexture = null;
      _nightTexture = null;
      _specularMap = null;
      _bumpMap = null;
      _cloudMap = null;
    } finally {
      _isLoading = false;
    }
  }
  
  Future<ui.Image?> _loadImageFromAsset(
    String asset, {
    int? targetWidth,
    int? targetHeight,
  }) async {
    try {
      final data = await rootBundle.load(asset);
      final bytes = data.buffer.asUint8List();
      final codec = await ui.instantiateImageCodec(
        bytes,
        targetWidth: targetWidth,
        targetHeight: targetHeight,
      );
      final frameInfo = await codec.getNextFrame();
      return frameInfo.image;
    } catch (e) {
      debugPrint('Error loading texture $asset: $e');
      return null;
    }
  }
  
  void dispose() {
    _listeners.clear();
    _dayTexture?.dispose();
    _nightTexture?.dispose();
    _specularMap?.dispose();
    _bumpMap?.dispose();
    _cloudMap?.dispose();
    _dayTexture = null;
    _nightTexture = null;
    _specularMap = null;
    _bumpMap = null;
    _cloudMap = null;
    _hasError = false;
    _errorMessage = null;
  }
} 