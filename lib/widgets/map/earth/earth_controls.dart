import 'package:flutter/material.dart';

class EarthControls extends StatelessWidget {
  final VoidCallback onZoomIn;
  final VoidCallback onZoomOut;
  final VoidCallback onReset;
  
  const EarthControls({
    Key? key,
    required this.onZoomIn,
    required this.onZoomOut,
    required this.onReset,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        color: isDark
            ? theme.colorScheme.surface.withOpacity(0.8)
            : theme.colorScheme.surface.withOpacity(0.9),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Zoom in button
          _buildControlButton(
            context: context,
            icon: Icons.add,
            onTap: onZoomIn,
            tooltip: 'Zoom In',
          ),
          
          // Divider
          Container(
            height: 1,
            color: theme.colorScheme.onSurface.withOpacity(0.1),
          ),
          
          // Zoom out button
          _buildControlButton(
            context: context,
            icon: Icons.remove,
            onTap: onZoomOut,
            tooltip: 'Zoom Out',
          ),
          
          // Divider
          Container(
            height: 1,
            color: theme.colorScheme.onSurface.withOpacity(0.1),
          ),
          
          // Reset button
          _buildControlButton(
            context: context,
            icon: Icons.refresh,
            onTap: onReset,
            tooltip: 'Reset View',
          ),
        ],
      ),
    );
  }
  
  Widget _buildControlButton({
    required BuildContext context,
    required IconData icon,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    final theme = Theme.of(context);
    
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: 40,
            height: 40,
            alignment: Alignment.center,
            child: Icon(
              icon,
              size: 20,
              color: theme.colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
        ),
      ),
    );
  }
} 