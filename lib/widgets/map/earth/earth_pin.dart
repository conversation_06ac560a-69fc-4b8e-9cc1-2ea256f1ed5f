import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import 'dart:math' as math;
import '../../../models/pin.dart';
import '../../../utils/earth_calculations.dart';

class EarthPin extends StatefulWidget {
  final dynamic pin;
  final vector.Matrix4 projection;
  final VoidCallback onTap;
  final bool isSelected;
  
  const EarthPin({
    Key? key,
    required this.pin,
    required this.projection,
    required this.onTap,
    this.isSelected = false,
  }) : super(key: key);
  
  @override
  State<EarthPin> createState() => _EarthPinState();
}

class _EarthPinState extends State<EarthPin> with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize pulse animation
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    // Start continuous pulse
    _pulseController.repeat(reverse: true);
  }
  
  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }
  
  vector.Vector3 _latLongToPoint(double lat, double long) {
    // Use the earth calculations utility for consistent positioning
    return EarthCalculations.latLongToPoint(lat, long);
  }
  
  Color _getPinColor(ThemeData theme) {
    // Use custom color if available
    if (widget.pin is Map && widget.pin['custom_color'] != null) {
      return Color(widget.pin['custom_color']);
    }
    
    // Otherwise use rarity-based color with fallback
    final rarity = widget.pin is Map ? 
        (widget.pin['rarity'] ?? 'Common') : 
        (widget.pin.rarity.toString().split('.').last);
        
    switch (rarity) {
      case 'Legendary':
        return Colors.purple[400] ?? Colors.purple;
      case 'Epic':
        return Colors.deepOrange[300] ?? Colors.deepOrange;
      case 'Rare':
        return Colors.blue[300] ?? Colors.blue;
      case 'Uncommon':
        return Colors.green[300] ?? Colors.green;
      case 'Common':
      default:
        // Use a default color if theme primary is not visible
        return theme.colorScheme.primary.computeLuminance() < 0.1 
            ? Colors.blue 
            : theme.colorScheme.primary;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    
    // Convert pin location to 3D point
    final double latitude = widget.pin is Map ? 
        (widget.pin['latitude'] ?? 0.0) : 
        widget.pin.latitude;
    final double longitude = widget.pin is Map ? 
        (widget.pin['longitude'] ?? 0.0) : 
        widget.pin.longitude;
        
    final point = _latLongToPoint(latitude, longitude);
    
    // Project point to screen space
    final projected = widget.projection.transform3(point);
    if (projected.z < 0) return const SizedBox(); // Behind the globe
    
    // Calculate depth-based scaling for 3D effect
    final depthFactor = (1.0 - (projected.z * 0.15).clamp(0.0, 0.5));
    
    // Convert to screen coordinates
    final x = (projected.x / projected.z + 1) * size.width / 2;
    final y = (-projected.y / projected.z + 1) * size.height / 2;
    
    // Calculate opacity based on depth - pins closer to edges are more transparent
    final opacity = ((1.0 - projected.z) * 1.2).clamp(0.4, 1.0);
    
    // Get pin color based on rarity/custom color
    final pinColor = _getPinColor(theme);
    
    return Positioned(
      left: x - 30, // Adjusted for center of soundwave effect
      top: y - 45, // Adjusted for center of soundwave effect
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          // Enhanced pulse effect when selected
          final scale = widget.isSelected
              ? 1.2 + (_pulseAnimation.value - 1.0) * 0.5
              : _pulseAnimation.value;
          
          return Transform.scale(
            scale: scale * depthFactor,
            child: GestureDetector(
              onTap: widget.onTap,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Stack contains both the aura and the pin
                  Stack(
                    children: [
                      // Sound wave aura effect
                      SizedBox(
                        width: 60,
                        height: 60,
                        child: CustomPaint(
                          painter: _SoundWaveAuraPainter(
                            color: pinColor,
                            animationValue: _pulseAnimation.value,
                            opacity: opacity * (widget.isSelected ? 0.8 : 0.4),
                          ),
                        ),
                      ),
                      
                      // Pin positioned in center of aura
                      Positioned(
                        left: 12.5, // Centered horizontally (60-35)/2
                        top: 12.5, // Centered vertically
                        child: Stack(
                          children: [
                            // Pin shadow for 3D effect
                            Icon(
                              Icons.location_on,
                              color: Colors.black.withOpacity(0.3 * opacity),
                              size: 35,
                            ),
                            
                            // Actual pin with depth-based opacity
                            Icon(
                              Icons.location_on,
                              color: pinColor.withOpacity(opacity),
                              size: 30,
                            ),
                            
                            // White highlight for 3D effect
                            Positioned(
                              top: 5,
                              left: 5,
                              child: Icon(
                                Icons.circle,
                                color: Colors.white.withOpacity(0.7 * opacity),
                                size: 8,
                              ),
                            ),
                            
                            // Selection indicator
                            if (widget.isSelected)
                              Container(
                                width: 30,
                                height: 30,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.white.withOpacity(opacity),
                                    width: 2,
                                  ),
                                  shape: BoxShape.circle,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  // Pin title (only show when selected)
                  if (widget.isSelected)
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surface.withOpacity(0.8),
                        borderRadius: BorderRadius.circular(4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        widget.pin is Map ? 
                            (widget.pin['title'] ?? 'Unknown') : 
                            widget.pin.title,
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Custom painter that draws subtle sound wave rings
class _SoundWaveAuraPainter extends CustomPainter {
  final Color color;
  final double animationValue;
  final double opacity;
  
  _SoundWaveAuraPainter({
    required this.color,
    required this.animationValue,
    required this.opacity,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = size.width / 2;
    
    // Draw multiple wave rings
    final numberOfRings = 3;
    for (int i = 0; i < numberOfRings; i++) {
      // Calculate ring properties based on animation
      final ringProgress = (animationValue + i / numberOfRings) % 1.0;
      final ringRadius = maxRadius * ringProgress * 0.8;
      final ringOpacity = opacity * (1.0 - ringProgress);
      final ringWidth = 1.0 + (1.0 - ringProgress) * 1.5;
      
      // Create paint for this ring
      final paint = Paint()
        ..color = color.withOpacity(ringOpacity)
        ..style = PaintingStyle.stroke
        ..strokeWidth = ringWidth
        ..strokeCap = StrokeCap.round;
      
      // Draw dashed circle to create sound wave effect
      final dashCount = 12;
      final dashLength = 2 * math.pi / dashCount;
      
      for (int j = 0; j < dashCount; j++) {
        final dashStart = j * dashLength;
        final dashEnd = dashStart + dashLength * 0.6; // 60% of each segment is visible
        
        canvas.drawArc(
          Rect.fromCircle(center: center, radius: ringRadius),
          dashStart,
          dashEnd - dashStart,
          false,
          paint,
        );
      }
    }
  }
  
  @override
  bool shouldRepaint(_SoundWaveAuraPainter oldDelegate) =>
    oldDelegate.color != color ||
    oldDelegate.animationValue != animationValue ||
    oldDelegate.opacity != opacity;
} 