import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import 'dart:math' as math;
import '../../../config/themes.dart';
import 'earth_sphere_painter.dart';
import 'earth_atmosphere_painter.dart';
import 'earth_texture_manager.dart';

class EarthGlobe extends StatefulWidget {
  final vector.Matrix4 projection;
  final ThemeData theme;
  final double rotationAngle;  // Add rotation angle parameter
  
  const EarthGlobe({
    Key? key,
    required this.projection,
    required this.theme,
    required this.rotationAngle,  // Make it required
  }) : super(key: key);
  
  @override
  State<EarthGlobe> createState() => _EarthGlobeState();
}

class _EarthGlobeState extends State<EarthGlobe> with SingleTickerProviderStateMixin {
  late final AnimationController _rotationController;
  final EarthTextureManager _textureManager = EarthTextureManager();
  
  // Light direction for realistic shading
  final vector.Vector3 _lightDirection = vector.Vector3(1.0, -0.5, 1.0)..normalize();
  
  @override
  void initState() {
    super.initState();
    
    // Setup rotation animation without auto-repeat
    _rotationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 60),
    );
    
    // Start loading textures and listen for changes
    _textureManager.addListener(_handleTextureLoaded);
    _textureManager.loadTextures();
  }
  
  void _handleTextureLoaded() {
    if (mounted) {
      setState(() {});
    }
  }
  
  @override
  void dispose() {
    _textureManager.removeListener(_handleTextureLoaded);
    _rotationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final isDark = widget.theme.brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;
    final globeSize = size.width * 0.9; // Make globe larger
    
    return ClipOval(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Earth sphere with texture
          SizedBox(
            width: globeSize,
            height: globeSize,
            child: AspectRatio(
              aspectRatio: 1.0,
              child: CustomPaint(
                painter: EarthSpherePainter(
                  projection: widget.projection,
                  isDark: isDark,
                  lightDirection: _lightDirection,
                  ambientLight: 0.3,
                  diffuseLight: 0.7,
                  specularLight: 0.4,
                  shininess: 10.0,
                  cloudOpacity: 0.3,
                  rotationAngle: widget.rotationAngle, // Use rotation from parent
                ),
              ),
            ),
          ),
          
          // Show loading indicator while textures are loading
          if (!_textureManager.isLoaded && !_textureManager.hasError)
            const Center(
              child: CircularProgressIndicator(),
            ),
          
          // Show error message if texture loading failed
          if (_textureManager.hasError)
            Center(
              child: Text(
                'Failed to load Earth textures\n${_textureManager.errorMessage}',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.red[300],
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
} 