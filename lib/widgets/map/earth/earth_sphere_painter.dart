import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'dart:typed_data';
import '../../../utils/sphere_geometry.dart';
import 'earth_texture_manager.dart';

class EarthSpherePainter extends CustomPainter {
  final vector.Matrix4 projection;
  final bool isDark;
  final vector.Vector3 lightDirection;
  final double ambientLight;
  final double diffuseLight;
  final double specularLight;
  final double shininess;
  final double cloudOpacity;
  final double rotationAngle;
  
  final EarthTextureManager _textureManager = EarthTextureManager();
  
  EarthSpherePainter({
    required this.projection,
    required this.isDark,
    required this.lightDirection,
    required this.ambientLight,
    required this.diffuseLight,
    required this.specularLight,
    required this.shininess,
    this.cloudOpacity = 0.5,
    this.rotationAngle = 0.0,
  }) {
    _textureManager.loadTextures();
  }
  
  @override
  void paint(Canvas canvas, Size size) {
    if (!_textureManager.isLoaded) {
      _drawPlaceholder(canvas, size);
      return;
    }
    
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width < size.height ? size.width / 2 : size.height / 2;
    
    final rect = Rect.fromCenter(
      center: center,
      width: radius * 2,
      height: radius * 2,
    );
    
    // Create a path for the circle and clip to it
    final circlePath = Path()..addOval(rect);
    canvas.save();
    canvas.clipPath(circlePath);
    
    // Draw black background
    canvas.drawRect(rect, Paint()..color = Colors.black);
    
    // Create matrix for texture mapping with proper rotation
    final matrix = Float64List(16);
    final textureWidth = _textureManager.dayTexture!.width.toDouble();
    final textureHeight = _textureManager.dayTexture!.height.toDouble();
    
    // Calculate scale to fit the texture properly - adjusted for better continent visibility
    final scaleX = (radius * 2.2) / textureWidth;  // Increased scale factor
    final scaleY = (radius * 2.2) / textureHeight;  // Increased scale factor
    
    // Center the texture first
    matrix[0] = scaleX;
    matrix[5] = scaleY;
    matrix[10] = 1.0;
    matrix[12] = center.dx - (radius * 1.1);  // Adjust position to account for scale
    matrix[13] = center.dy - (radius * 1.1);  // Adjust position to account for scale
    matrix[15] = 1.0;
    
    // Apply rotation around the center - using negative angle to match natural drag direction
    final rotationMatrix = Matrix4.identity();
    rotationMatrix.setRotationY(-rotationAngle);  // Using setRotationY instead of rotateY
    
    // Combine the matrices
    final combined = Matrix4.fromFloat64List(matrix)..multiply(rotationMatrix);
    
    // Draw day texture with proper filtering
    final dayPaint = Paint()
      ..isAntiAlias = true
      ..filterQuality = FilterQuality.high
      ..shader = ImageShader(
        _textureManager.dayTexture!,
        TileMode.clamp,
        TileMode.clamp,
        combined.storage
      );
    
    canvas.drawRect(rect, dayPaint);
    
    // Apply night texture with terminator
    if (_textureManager.nightTexture != null) {
      final nightPaint = Paint()
        ..isAntiAlias = true
        ..filterQuality = FilterQuality.high
        ..shader = ImageShader(
          _textureManager.nightTexture!,
          TileMode.clamp,
          TileMode.clamp,
          combined.storage
        )
        ..blendMode = BlendMode.multiply;
      
      // Calculate terminator based on light direction
      final terminatorAngle = math.atan2(lightDirection.y, lightDirection.x);
      final gradientCenter = Offset(
        center.dx + math.cos(terminatorAngle) * radius,
        center.dy + math.sin(terminatorAngle) * radius,
      );
      
      // Create gradient mask for day/night transition
      final gradientPaint = Paint()
        ..shader = ui.Gradient.radial(
          gradientCenter,
          radius * 2.5,
          [
            Colors.white,
            Colors.white.withOpacity(0.7),
            Colors.transparent
          ],
          [0.0, 0.3, 0.7],
          TileMode.clamp,
          Matrix4.identity().storage
        );
      
      canvas.saveLayer(rect, Paint());
      canvas.drawRect(rect, nightPaint);
      canvas.drawRect(rect, gradientPaint..blendMode = BlendMode.dstOut);
      canvas.restore();
    }
    
    // Apply specular highlights with reduced opacity
    final specularPaint = Paint()
      ..shader = ui.Gradient.radial(
        Offset(
          center.dx + lightDirection.x * radius * 0.5,
          center.dy + lightDirection.y * radius * 0.5,
        ),
        radius * 1.5,
        [
          Colors.white.withOpacity(0.2),  // Reduced opacity
          Colors.transparent,
        ],
        [0.0, 0.5],
      );
    
    canvas.drawRect(rect, specularPaint..blendMode = BlendMode.screen);
    
    // Apply atmospheric scattering
    final atmospherePaint = Paint()
      ..shader = ui.Gradient.radial(
        Offset(
          center.dx + lightDirection.x * radius * 0.3,
          center.dy + lightDirection.y * radius * 0.3,
        ),
        radius * 2,
        [
          Colors.lightBlue.withOpacity(0.2),
          Colors.transparent,
          Colors.black.withOpacity(0.3),
        ],
        [0.0, 0.5, 1.0],
      );
    
    canvas.drawRect(rect, atmospherePaint..blendMode = BlendMode.screen);
    canvas.restore();
    
    // Draw atmosphere glow
    final glowPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = radius * 0.15
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 12.0)
      ..shader = ui.Gradient.radial(
        center,
        radius,
        [
          Colors.lightBlue.withOpacity(0.3),
          Colors.lightBlue.withOpacity(0.0),
        ],
      );
    
    canvas.drawCircle(center, radius * 1.05, glowPaint);
  }
  
  void _drawPlaceholder(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width < size.height ? size.width / 2.5 : size.height / 2.5;
    
    // Draw a more sophisticated placeholder with gradient and atmosphere
    final spherePaint = Paint()
      ..style = PaintingStyle.fill
      ..shader = ui.Gradient.radial(
        Offset(
          center.dx + lightDirection.x * radius * 0.3,
          center.dy + lightDirection.y * radius * 0.3,
        ),
        radius * 2,
        [
          isDark ? Colors.blue[800]! : Colors.blue[500]!,
          isDark ? Colors.blue[900]! : Colors.blue[700]!,
        ],
        [0.0, 1.0],
      );
    
    canvas.drawCircle(center, radius, spherePaint);
    
    // Add atmospheric glow to placeholder
    final atmospherePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = radius * 0.1
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8.0)
      ..shader = ui.Gradient.radial(
        center,
        radius,
        [
          Colors.lightBlue.withOpacity(0.2),
          Colors.transparent,
        ],
      );
    
    canvas.drawCircle(center, radius * 1.05, atmospherePaint);
  }
  
  @override
  bool shouldRepaint(EarthSpherePainter oldDelegate) {
    return oldDelegate.projection != projection ||
           oldDelegate.isDark != isDark ||
           oldDelegate.lightDirection != lightDirection ||
           oldDelegate.ambientLight != ambientLight ||
           oldDelegate.diffuseLight != diffuseLight ||
           oldDelegate.specularLight != specularLight ||
           oldDelegate.shininess != shininess ||
           oldDelegate.cloudOpacity != cloudOpacity ||
           oldDelegate.rotationAngle != rotationAngle;
  }
} 