import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

class EarthBackground extends StatefulWidget {
  final bool isDark;
  
  const EarthBackground({
    Key? key,
    required this.isDark,
  }) : super(key: key);

  @override
  State<EarthBackground> createState() => _EarthBackgroundState();
}

class _EarthBackgroundState extends State<EarthBackground> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 120),
    );
    
    // Start with minimal animation for subtle effects only
    _animationController.value = 0.5; // Start at middle point
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    
    return Stack(
      fit: StackFit.expand,
      children: [
        // Deep space background - subtle cosmic gradient
        Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: const Alignment(0.1, -0.2),
              radius: 1.8,
              colors: widget.isDark
                  ? [
                      const Color(0xFF101114),
                      const Color(0xFF0A0B0E),
                      const Color(0xFF050507),
                    ]
                  : [
                      const Color(0xFF152642),
                      const Color(0xFF0D162A),
                      const Color(0xFF060C18),
                    ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
        ),
        
        // Premium nebula effect (static)
        Opacity(
          opacity: widget.isDark ? 0.4 : 0.3,
          child: CustomPaint(
            painter: _NebulaPainter(
              colors: widget.isDark
                  ? [
                      theme.colorScheme.primary.withRed(theme.colorScheme.primary.red ~/ 2),
                      theme.colorScheme.primary.withAlpha(40),
                      theme.colorScheme.secondary.withAlpha(25),
                    ]
                  : [
                      theme.colorScheme.primary.withAlpha(40),
                      theme.colorScheme.secondary.withAlpha(25),
                      theme.colorScheme.primary.withRed(theme.colorScheme.primary.red ~/ 2),
                    ],
              // Static nebula - no animation
            ),
            size: Size.infinite,
          ),
        ),
        
        // Static star field with layered depth
        CustomPaint(
          painter: _PremiumStarFieldPainter(
            starCount: 250,
            isDark: widget.isDark,
            primaryColor: theme.colorScheme.primary,
            secondaryColor: theme.colorScheme.secondary,
            size: size,
            // No animation value passed to avoid movement
          ),
          size: Size.infinite,
        ),
        
        // Subtle lens flare effect (static)
        Opacity(
          opacity: 0.3,
          child: CustomPaint(
            painter: _LensFlarePainter(
              isDark: widget.isDark,
              primaryColor: theme.colorScheme.primary,
              // No animation value for static effect
            ),
            size: Size.infinite,
          ),
        ),
        
        // Ultra-subtle light rays (static)
        Opacity(
          opacity: widget.isDark ? 0.05 : 0.07,
          child: CustomPaint(
            painter: _LightRaysPainter(
              isDark: widget.isDark,
              primaryColor: theme.colorScheme.primary,
              // No animation value for static effect
            ),
            size: Size.infinite,
          ),
        ),
        
        // Cinematic vignette
        Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.5,
              colors: [
                Colors.transparent,
                widget.isDark 
                    ? Colors.black.withOpacity(0.7)
                    : Colors.black.withOpacity(0.6),
              ],
              stops: const [0.2, 1.0],
            ),
          ),
        ),
      ],
    );
  }
}

class _NebulaPainter extends CustomPainter {
  final List<Color> colors;
  final double? animationValue; // Optional now
  
  _NebulaPainter({
    required this.colors,
    this.animationValue,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    
    // Create several overlapping nebula clouds - now static
    for (int i = 0; i < 3; i++) {
      final centerX = width * (0.3 + 0.5 * i / 3);
      final centerY = height * (0.4 + 0.3 * i / 3);
      
      // Static radius
      final radius = math.min(width, height) * (0.5 + i * 0.2);
      
      final paintGradient = Paint()
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 50.0)
        ..shader = RadialGradient(
          colors: [
            colors[i % colors.length].withOpacity(0.3 - i * 0.1),
            colors[i % colors.length].withOpacity(0.1 - i * 0.03),
            Colors.transparent,
          ],
          stops: const [0.0, 0.5, 1.0],
        ).createShader(
          Rect.fromCircle(center: Offset(centerX, centerY), radius: radius),
        );
      
      canvas.drawCircle(Offset(centerX, centerY), radius, paintGradient);
    }
  }
  
  @override
  bool shouldRepaint(_NebulaPainter oldDelegate) =>
      oldDelegate.colors != colors;
}

class _PremiumStarFieldPainter extends CustomPainter {
  final int starCount;
  final bool isDark;
  final Color primaryColor;
  final Color secondaryColor;
  final Size size;
  final double? animationValue; // Optional now
  final List<_PremiumStar> stars;
  
  _PremiumStarFieldPainter({
    required this.starCount,
    required this.isDark,
    required this.primaryColor,
    required this.secondaryColor,
    required this.size,
    this.animationValue,
  }) : stars = List.generate(
         starCount,
         (index) {
           final random = math.Random();
           // Create multiple layers for enhanced parallax
           final layer = index % 5; // 0-4, 5 layers for depth
           
           // Variation in brightness and color
           final variation = random.nextDouble();
           
           // Calculate base color with variation
           final baseColorValue = random.nextDouble();
           
           return _PremiumStar(
             x: random.nextDouble(),
             y: random.nextDouble(),
             size: (0.4 + random.nextDouble() * 1.0) * (layer + 1) * 0.5,
             brightness: random.nextDouble() * 0.7 + 0.3, // Brighter base value
             colorValue: baseColorValue,
             variation: variation,
             layer: layer,
             twinkleSpeed: 1.0 + random.nextDouble() * 2.0, // Random twinkle speed
             twinklePhase: random.nextDouble() * math.pi * 2, // Random phase
           );
         },
       );
  
  @override
  void paint(Canvas canvas, Size canvasSize) {
    // Sort stars by layer to ensure proper depth rendering
    final sortedStars = List<_PremiumStar>.from(stars)
      ..sort((a, b) => a.layer.compareTo(b.layer));
    
    for (final star in sortedStars) {
      // Calculate color based on star's properties
      final useSecondaryColor = star.colorValue > 0.5;
      final baseColor = useSecondaryColor 
          ? Color.lerp(primaryColor, secondaryColor, star.variation)!
          : Color.lerp(Colors.white, primaryColor, star.variation * 0.5)!;
      
      // Layer-based depth factors
      final layerFactor = (star.layer + 1) / 5; // 0.2 to 1.0
      
      // Static brightness without twinkle animation
      final brightness = isDark 
          ? (star.brightness * 0.7 + 0.3) * layerFactor // Brighter in dark mode
          : (star.brightness * 0.4 + 0.1) * layerFactor; // Dimmer in light mode
      
      // Static position - no parallax movement
      final position = Offset(
        star.x * canvasSize.width,
        star.y * canvasSize.height,
      );
      
      // Premium glow effect with multiple layers
      final glowPaint = Paint()
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(
          BlurStyle.normal,
          star.size * layerFactor,
        );
      
      // Outer glow
      canvas.drawCircle(
        position,
        star.size * 3 * layerFactor,
        glowPaint..color = baseColor.withOpacity(brightness * 0.2),
      );
      
      // Middle glow
      canvas.drawCircle(
        position,
        star.size * 1.5 * layerFactor,
        glowPaint..color = baseColor.withOpacity(brightness * 0.5),
      );
      
      // Core with slight color variation
      final coreColor = Color.lerp(
        baseColor, 
        Colors.white,
        star.layer / 4 * star.brightness,
      )!;
      
      canvas.drawCircle(
        position,
        star.size * layerFactor,
        Paint()
          ..style = PaintingStyle.fill
          ..color = coreColor.withOpacity(brightness),
      );
      
      // Subtle lens flare for brightest stars
      if (star.layer >= 3 && star.brightness > 0.8) {
        // Horizontal flare
        canvas.drawLine(
          position - Offset(star.size * 4 * layerFactor, 0),
          position + Offset(star.size * 4 * layerFactor, 0),
          Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = star.size * 0.2 * layerFactor
            ..color = baseColor.withOpacity(brightness * 0.2)
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0),
        );
        
        // Vertical flare
        canvas.drawLine(
          position - Offset(0, star.size * 4 * layerFactor),
          position + Offset(0, star.size * 4 * layerFactor),
          Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = star.size * 0.2 * layerFactor
            ..color = baseColor.withOpacity(brightness * 0.2)
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0),
        );
      }
    }
  }
  
  @override
  bool shouldRepaint(_PremiumStarFieldPainter oldDelegate) =>
      oldDelegate.isDark != isDark ||
      oldDelegate.primaryColor != primaryColor ||
      oldDelegate.secondaryColor != secondaryColor ||
      oldDelegate.size != size;
}

class _LensFlarePainter extends CustomPainter {
  final bool isDark;
  final Color primaryColor;
  final double? animationValue; // Optional now
  
  _LensFlarePainter({
    required this.isDark,
    required this.primaryColor,
    this.animationValue,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Fixed position lens flare
    final center = Offset(size.width * 0.7, size.height * 0.3);
    final radius = math.min(size.width, size.height) * 0.3;
    
    // Ultra-subtle lens flare
    final flareGradient = RadialGradient(
      colors: [
        isDark 
            ? primaryColor.withOpacity(0.1)
            : primaryColor.withOpacity(0.08),
        isDark 
            ? primaryColor.withOpacity(0.05)
            : primaryColor.withOpacity(0.03),
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 1.0],
    ).createShader(
      Rect.fromCircle(center: center, radius: radius),
    );
    
    canvas.drawCircle(
      center,
      radius,
      Paint()
        ..style = PaintingStyle.fill
        ..shader = flareGradient
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 30.0),
    );
    
    // Add subtle anamorphic flare lines
    final flarePaint = Paint()
      ..style = PaintingStyle.stroke
      ..color = primaryColor.withOpacity(0.1)
      ..strokeWidth = 1.0
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);
    
    // Horizontal flare
    canvas.drawLine(
      Offset(center.dx - radius * 1.5, center.dy),
      Offset(center.dx + radius * 1.5, center.dy),
      flarePaint,
    );
    
    // Vertical flare
    canvas.drawLine(
      Offset(center.dx, center.dy - radius * 0.5),
      Offset(center.dx, center.dy + radius * 0.5),
      flarePaint,
    );
  }
  
  @override
  bool shouldRepaint(_LensFlarePainter oldDelegate) =>
      oldDelegate.isDark != isDark ||
      oldDelegate.primaryColor != primaryColor;
}

class _LightRaysPainter extends CustomPainter {
  final bool isDark;
  final Color primaryColor;
  final double? animationValue; // Optional now
  
  _LightRaysPainter({
    required this.isDark,
    required this.primaryColor,
    this.animationValue,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width * 0.35, size.height * 0.4);
    final radius = math.max(size.width, size.height);
    
    // Create static rays emanating from point
    for (int i = 0; i < 12; i++) {
      final angle = i * (math.pi / 6); // Fixed angles, no animation
      final rayEnd = Offset(
        center.dx + math.cos(angle) * radius,
        center.dy + math.sin(angle) * radius,
      );
      
      // Ultra-subtle ray brush
      final rayPaint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = radius * 0.01
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 30.0)
        ..shader = ui.Gradient.linear(
          center,
          rayEnd,
          [
            primaryColor.withOpacity(0.15),
            primaryColor.withOpacity(0.05),
            Colors.transparent,
          ],
          [0.0, 0.5, 1.0],
        );
      
      canvas.drawLine(center, rayEnd, rayPaint);
    }
  }
  
  @override
  bool shouldRepaint(_LightRaysPainter oldDelegate) =>
      oldDelegate.isDark != isDark ||
      oldDelegate.primaryColor != primaryColor;
}

class _PremiumStar {
  final double x;
  final double y;
  final double size;
  final double brightness;
  final double colorValue;
  final double variation;
  final int layer;
  final double twinkleSpeed;
  final double twinklePhase;
  
  _PremiumStar({
    required this.x,
    required this.y,
    required this.size,
    required this.brightness,
    required this.colorValue,
    required this.variation,
    required this.layer,
    required this.twinkleSpeed,
    required this.twinklePhase,
  });
} 