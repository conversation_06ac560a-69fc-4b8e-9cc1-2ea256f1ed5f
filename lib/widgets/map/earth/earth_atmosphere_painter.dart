import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import 'dart:math' as math;
import 'dart:ui' as ui;

class EarthAtmospherePainter extends CustomPainter {
  final vector.Matrix4 projection;
  final bool isDark;
  final Color atmosphereColor;
  final double glowIntensity;
  final double glowFalloff;
  
  const EarthAtmospherePainter({
    required this.projection,
    required this.isDark,
    required this.atmosphereColor,
    required this.glowIntensity,
    required this.glowFalloff,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.height / 2;
    
    // Create radial gradients for the glow effect
    final innerGlow = Paint()
      ..shader = ui.Gradient.radial(
        center,
        radius * 1.0,
        [
          atmosphereColor.withOpacity(glowIntensity),
          atmosphereColor.withOpacity(0),
        ],
        [0.8, 1.0],
      )
      ..maskFilter = MaskFilter.blur(
        BlurStyle.normal,
        glowFalloff * 10,
      );
    
    final outerGlow = Paint()
      ..shader = ui.Gradient.radial(
        center,
        radius * 1.2,
        [
          atmosphereColor.withOpacity(glowIntensity * 0.5),
          atmosphereColor.withOpacity(0),
        ],
        [0.7, 1.0],
      )
      ..maskFilter = MaskFilter.blur(
        BlurStyle.normal,
        glowFalloff * 20,
      );
    
    // Draw multiple layers of glow for a more realistic effect
    for (int i = 0; i < 3; i++) {
      final scale = 1.0 + (i * 0.1);
      canvas.drawCircle(
        center,
        radius * scale,
        i == 0 ? innerGlow : outerGlow,
      );
    }
    
    // Add a subtle rim light effect
    final rimLight = Paint()
      ..shader = ui.Gradient.radial(
        center,
        radius * 1.02,
        [
          Colors.white.withOpacity(isDark ? 0.3 : 0.2),
          Colors.white.withOpacity(0),
        ],
        [0.95, 1.0],
      )
      ..maskFilter = MaskFilter.blur(
        BlurStyle.normal,
        2.0,
      );
    
    canvas.drawCircle(center, radius * 1.02, rimLight);
  }
  
  @override
  bool shouldRepaint(EarthAtmospherePainter oldDelegate) {
    return oldDelegate.projection != projection ||
           oldDelegate.isDark != isDark ||
           oldDelegate.atmosphereColor != atmosphereColor ||
           oldDelegate.glowIntensity != glowIntensity ||
           oldDelegate.glowFalloff != glowFalloff;
  }
} 