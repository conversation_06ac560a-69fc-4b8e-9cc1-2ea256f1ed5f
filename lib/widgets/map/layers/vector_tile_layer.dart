import 'dart:async';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

import '../../../services/vector_tiles/core/vector_tile_types.dart';
import '../../../services/vector_tiles/core/tile_provider.dart';
import '../../../services/vector_tiles/rendering/tile_renderer.dart';

/// A widget that renders vector tiles as a map layer.
class VectorTileLayer extends StatefulWidget {
  final TileProvider tileProvider;
  final VectorTileStyle style;
  final bool visible;
  final double opacity;
  final int? minZoom;
  final int? maxZoom;

  const VectorTileLayer({
    super.key,
    required this.tileProvider,
    required this.style,
    this.visible = true,
    this.opacity = 1.0,
    this.minZoom,
    this.maxZoom,
  });

  @override
  State<VectorTileLayer> createState() => _VectorTileLayerState();
}

class _VectorTileLayerState extends State<VectorTileLayer> {
  final TileRenderer _renderer = TileRenderer();
  final Map<String, VectorTile> _tiles = {};
  Timer? _cleanupTimer;
  bool _disposed = false;

  @override
  void initState() {
    super.initState();
    _startCleanupTimer();
  }

  @override
  void dispose() {
    _disposed = true;
    _cleanupTimer?.cancel();
    _tiles.clear();
    super.dispose();
  }

  /// Start the cleanup timer to remove stale tiles
  void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _cleanupTiles(),
    );
  }

  /// Clean up stale tiles
  void _cleanupTiles() {
    if (_disposed) return;

    final now = DateTime.now();
    _tiles.removeWhere((_, tile) {
      return tile.isStale(const Duration(hours: 1));
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.visible) return const SizedBox.shrink();

    return StreamBuilder<MapState>(
      stream: MapStateManager.instance.stream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.shrink();

        final state = snapshot.data!;
        if (!_isInZoomRange(state.zoom)) return const SizedBox.shrink();

        return CustomPaint(
          painter: _VectorTileLayerPainter(
            tiles: _tiles,
            renderer: _renderer,
            style: widget.style,
            opacity: widget.opacity,
            transform: _calculateTransform(state),
          ),
          child: _TileLoader(
            state: state,
            tileProvider: widget.tileProvider,
            onTileLoaded: _onTileLoaded,
          ),
        );
      },
    );
  }

  /// Check if current zoom level is within range
  bool _isInZoomRange(double zoom) {
    final intZoom = zoom.round();
    if (widget.minZoom != null && intZoom < widget.minZoom!) return false;
    if (widget.maxZoom != null && intZoom > widget.maxZoom!) return false;
    return true;
  }

  /// Calculate the transform matrix for rendering
  Matrix4 _calculateTransform(MapState state) {
    final matrix = Matrix4.identity();
    
    // Apply map transformations
    matrix.translate(state.center.longitude, state.center.latitude);
    matrix.scale(state.zoom);
    matrix.rotateZ(state.rotation);
    
    return matrix;
  }

  /// Handle loaded tiles
  void _onTileLoaded(VectorTile tile) {
    if (_disposed) return;
    
    setState(() {
      final key = '${tile.coordinate.z}_${tile.coordinate.x}_${tile.coordinate.y}';
      _tiles[key] = tile;
    });
  }
}

/// Custom painter for rendering vector tiles
class _VectorTileLayerPainter extends CustomPainter {
  final Map<String, VectorTile> tiles;
  final TileRenderer renderer;
  final VectorTileStyle style;
  final double opacity;
  final Matrix4 transform;

  _VectorTileLayerPainter({
    required this.tiles,
    required this.renderer,
    required this.style,
    required this.opacity,
    required this.transform,
  });

  @override
  void paint(Canvas canvas, Size size) {
    canvas.save();
    
    // Apply opacity
    if (opacity < 1.0) {
      canvas.saveLayer(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Paint()..color = Colors.white.withOpacity(opacity),
      );
    }

    // Render tiles
    for (final tile in tiles.values) {
      renderer.renderTile(canvas, tile, style, size, transform);
    }

    if (opacity < 1.0) {
      canvas.restore();
    }

    canvas.restore();
  }

  @override
  bool shouldRepaint(_VectorTileLayerPainter oldDelegate) {
    return tiles != oldDelegate.tiles ||
           opacity != oldDelegate.opacity ||
           transform != oldDelegate.transform;
  }
}

/// Widget that handles loading tiles
class _TileLoader extends StatefulWidget {
  final MapState state;
  final TileProvider tileProvider;
  final void Function(VectorTile) onTileLoaded;

  const _TileLoader({
    required this.state,
    required this.tileProvider,
    required this.onTileLoaded,
  });

  @override
  State<_TileLoader> createState() => _TileLoaderState();
}

class _TileLoaderState extends State<_TileLoader> {
  Set<String> _loadingTiles = {};
  Timer? _loadTimer;

  @override
  void initState() {
    super.initState();
    _scheduleLoading();
  }

  @override
  void didUpdateWidget(_TileLoader oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.state != widget.state) {
      _scheduleLoading();
    }
  }

  @override
  void dispose() {
    _loadTimer?.cancel();
    super.dispose();
  }

  /// Schedule tile loading with debouncing
  void _scheduleLoading() {
    _loadTimer?.cancel();
    _loadTimer = Timer(const Duration(milliseconds: 100), _loadTiles);
  }

  /// Load tiles for current view
  Future<void> _loadTiles() async {
    final bounds = widget.state.bounds;
    final zoom = widget.state.zoom.round();

    // Get tile coordinates for current view
    final coordinates = widget.tileProvider.getTileCoordinates(bounds, zoom);

    // Filter out already loading tiles
    final newCoordinates = coordinates.where((coord) {
      final key = '${coord.z}_${coord.x}_${coord.y}';
      return !_loadingTiles.contains(key);
    }).toList();

    if (newCoordinates.isEmpty) return;

    // Update loading state
    setState(() {
      for (final coord in newCoordinates) {
        _loadingTiles.add('${coord.z}_${coord.x}_${coord.y}');
      }
    });

    // Load tiles
    try {
      final tiles = await widget.tileProvider.getTiles(newCoordinates);
      for (final tile in tiles) {
        widget.onTileLoaded(tile);
      }
    } finally {
      if (mounted) {
        setState(() {
          for (final coord in newCoordinates) {
            _loadingTiles.remove('${coord.z}_${coord.x}_${coord.y}');
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) => const SizedBox.shrink();
}

/// Represents the current state of the map
class MapState {
  final LatLng center;
  final double zoom;
  final double rotation;
  final LatLngBounds bounds;

  const MapState({
    required this.center,
    required this.zoom,
    required this.rotation,
    required this.bounds,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MapState &&
          runtimeType == other.runtimeType &&
          center == other.center &&
          zoom == other.zoom &&
          rotation == other.rotation &&
          bounds == other.bounds;

  @override
  int get hashCode =>
      center.hashCode ^
      zoom.hashCode ^
      rotation.hashCode ^
      bounds.hashCode;
}

/// Manages the map state and notifies listeners of changes
class MapStateManager {
  static final MapStateManager _instance = MapStateManager._internal();
  static MapStateManager get instance => _instance;

  final _controller = StreamController<MapState>.broadcast();
  Stream<MapState> get stream => _controller.stream;

  MapStateManager._internal();

  void updateState(MapState state) {
    _controller.add(state);
  }

  void dispose() {
    _controller.close();
  }
} 
 