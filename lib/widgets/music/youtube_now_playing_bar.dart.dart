import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../config/themes.dart';
import '../../models/music_track.dart';
import '../../models/music/bop_drop.dart';
import '../../providers/youtube_provider.dart';
import '../../providers/now_playing_provider.dart';
import '../../providers/recommendation_context_provider.dart';
import '../../services/api/bop_drops_service.dart';
import 'youtube_expanded_now_playing_view.dart';
import '../../models/collection_model.dart';
import '../bottomsheets/collection_selector_bottomsheet.dart';

class YouTubeNowPlayingBar extends StatefulWidget {
  final double bottomPadding;
  final bool showProgressBar;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const YouTubeNowPlayingBar({
    Key? key,
    this.bottomPadding = 0,
    this.showProgressBar = true,
    this.onTap,
    this.onDismiss,
  }) : super(key: key);

  @override
  State<YouTubeNowPlayingBar> createState() => _YouTubeNowPlayingBarState();
}

class _YouTubeNowPlayingBarState extends State<YouTubeNowPlayingBar> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _expansionController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _heightAnimation;
  late Animation<double> _albumArtScaleAnimation;
  late Animation<double> _contentFadeAnimation;
  late Animation<Offset> _contentSlideAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _expansionController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _slideAnimation = Tween<double>(begin: 30, end: 0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    // Expansion animations
    _heightAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _expansionController,
        curve: Curves.easeInOutCubic,
      ),
    );

    _albumArtScaleAnimation = Tween<double>(begin: 1, end: 4).animate(
      CurvedAnimation(
        parent: _expansionController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
      ),
    );

    _contentFadeAnimation = Tween<double>(begin: 1, end: 0).animate(
      CurvedAnimation(
        parent: _expansionController,
        curve: const Interval(0.0, 0.3, curve: Curves.easeOut),
      ),
    );

    _contentSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0, 0.5),
    ).animate(
      CurvedAnimation(
        parent: _expansionController,
        curve: const Interval(0.0, 0.4, curve: Curves.easeOut),
      ),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _expansionController.dispose();
    super.dispose();
  }

  // Method to handle adding the current track to a collection from the mini-player
  void _handleAddToCollection(BuildContext context, MusicTrack? track) {
    if (track == null) return;

    // Open the same bottom-sheet used in the expanded view
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CollectionSelectorBottomSheet(track: track),
    );
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final size = mediaQuery.size;
    final isSmallScreen = size.width < 360;
    final youtubeProvider = Provider.of<YouTubeProvider>(context);
    final nowPlayingProvider = Provider.of<NowPlayingProvider>(context);
    
    // Handle expansion state changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (nowPlayingProvider.isExpanded && !_expansionController.isAnimating && _expansionController.value == 0) {
        _expansionController.forward();
      } else if (!nowPlayingProvider.isExpanded && !_expansionController.isAnimating && _expansionController.value == 1) {
        _expansionController.reverse();
      }
    });
    
    // Only show if we have active playback
    if (!youtubeProvider.hasActivePlayback || youtubeProvider.currentTrack == null) {
      if (kDebugMode) {
        print('❌ NowPlayingBar hidden: No active playback or track');
      }
      return const SizedBox.shrink();
    }
    
    // Calculate optimal height based on screen size to avoid overflow
    final collapsedHeight = isSmallScreen ? 60.0 : 68.0;
    
    return Material(
      color: Colors.transparent,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, _slideAnimation.value),
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: _buildMainContent(
                context, 
                size, 
                collapsedHeight + widget.bottomPadding,
                youtubeProvider,
                nowPlayingProvider,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMainContent(
    BuildContext context, 
    Size size, 
    double collapsedHeight, 
    YouTubeProvider youtubeProvider,
    NowPlayingProvider nowPlayingProvider,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDarkMode 
      ? Theme.of(context).colorScheme.surface.withOpacity(0.95)
      : Theme.of(context).colorScheme.surface.withOpacity(0.98);
    final textColor = isDarkMode ? Colors.white : Colors.black;

    // Calculate the available height accounting for all possible space consumers
    final bottomNavHeight = kBottomNavigationBarHeight;
    final safeAreaBottom = MediaQuery.of(context).padding.bottom;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final additionalPadding = 16.0; // Account for various internal paddings
    final maxHeight = size.height - bottomNavHeight - safeAreaBottom - additionalPadding;

    return AnimatedBuilder(
      animation: _expansionController,
      builder: (context, child) {
        final currentHeight = collapsedHeight + (maxHeight - collapsedHeight) * _heightAnimation.value;
        
        return Material(
          elevation: 8,
          color: Colors.transparent,
          child: GestureDetector(
            onVerticalDragStart: (details) {
              nowPlayingProvider.startDrag(details.globalPosition.dy);
            },
            onVerticalDragUpdate: (details) {
              nowPlayingProvider.updateDrag(details.globalPosition.dy);
            },
            onVerticalDragEnd: (details) {
              if (!youtubeProvider.hasActivePlayback) return;
              nowPlayingProvider.endDrag(details.primaryVelocity ?? 0);
            },
            onTap: () {
              if (youtubeProvider.hasActivePlayback) {
                nowPlayingProvider.toggleExpanded();
                widget.onTap?.call();
              }
            },
            child: Container(
              height: currentHeight,
              constraints: BoxConstraints(
                maxHeight: maxHeight,
              ),
              decoration: BoxDecoration(
                color: backgroundColor,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Theme.of(context).colorScheme.primary.withOpacity(isDarkMode ? 0.05 : 0.02),
                    backgroundColor,
                    backgroundColor,
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    blurRadius: 15,
                    offset: const Offset(0, -2),
                    spreadRadius: 1,
                  ),
                ],
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Stack(
                    children: [
                      // Collapsed view
                      if (_heightAnimation.value < 1)
                        Opacity(
                          opacity: _contentFadeAnimation.value,
                          child: SlideTransition(
                            position: _contentSlideAnimation,
                            child: _buildCollapsedView(
                              context, 
                              collapsedHeight, 
                              backgroundColor, 
                              textColor,
                              youtubeProvider,
                            ),
                          ),
                        ),
                      
                      // Expanded view (render only when sufficient height to avoid overflow)
                      if (_heightAnimation.value > 0.9)
                        Opacity(
                          opacity: 1 - _contentFadeAnimation.value,
                          child: YouTubeExpandedNowPlayingView(
                            key: const ValueKey('expanded'),
                            onCollapse: () => nowPlayingProvider.setExpanded(false),
                            height: size.height,
                            track: youtubeProvider.currentTrack!,
                            isPlaying: youtubeProvider.isPlaying,
                            onPlayPause: () async {
                              if (youtubeProvider.isPlaying) {
                                await youtubeProvider.pause();
                              } else {
                                await youtubeProvider.play();
                              }
                            },
                            onSeek: (position) async {
                              await youtubeProvider.seek(Duration(milliseconds: position));
                            },
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCollapsedView(
    BuildContext context, 
    double height, 
    Color backgroundColor, 
    Color textColor,
    YouTubeProvider youtubeProvider,
  ) {
    final track = youtubeProvider.currentTrack;
    if (track == null) return const SizedBox.shrink();
    
    // Get recommendation context
    final recContext = Provider.of<RecommendationContextProvider>(context);
    final currentRec = recContext.getCurrentRecommendationForTrack(track);
    final isFromRecs = recContext.isPlayingRecommendations && currentRec != null;
    
    // Calculate optimal sizes based on bar height to avoid overflow
    final albumSize = height - 12.0; // 6px padding top and bottom
    final isSmallScreen = MediaQuery.of(context).size.width < 360;
    final iconSize = isSmallScreen ? 24.0 : 26.0;
    final smallIconSize = isSmallScreen ? 20.0 : 22.0; // For add to collection button
    
    // Recommendation element sizes
    final avatarSize = isSmallScreen ? 24.0 : 28.0;
    final likeButtonSize = isSmallScreen ? 28.0 : 32.0;
    final likeIconSize = isSmallScreen ? 18.0 : 20.0;
    
    return Container(
      key: const ValueKey('collapsed'),
      color: backgroundColor,
      child: SafeArea(
        top: false,
        bottom: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.showProgressBar)
              _buildProgressBar(youtubeProvider),
            SizedBox(
              height: height - (widget.showProgressBar ? 4 : 0),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildAlbumArt(track, albumSize),
                    const SizedBox(width: 12),
                    _buildTrackInfo(track, textColor),
                    
                    // NEW: Recommendation elements (only when playing recommendations)
                    if (isFromRecs) ...[
                      SizedBox(width: isSmallScreen ? 6 : 8),
                      _buildRecommenderAvatar(currentRec!.user, avatarSize),
                      SizedBox(width: isSmallScreen ? 4 : 6),
                      _buildLikeButton(currentRec, likeButtonSize, likeIconSize),
                      SizedBox(width: isSmallScreen ? 6 : 8),
                    ],
                    
                    // Add to Collection button (unchanged)
                    IconButton(
                      icon: Icon(
                        Icons.playlist_add, // Consider Icons.collections_bookmark_outlined
                        color: textColor.withOpacity(0.8),
                        size: smallIconSize,
                      ),
                      onPressed: () => _handleAddToCollection(context, track),
                      padding: const EdgeInsets.symmetric(horizontal: 8.0), // Add some padding
                      constraints: const BoxConstraints(),
                    ),
                    _buildPlayPauseButton(youtubeProvider, track, iconSize, textColor),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar(YouTubeProvider provider) {
    return StreamBuilder<Duration>(
      stream: provider.positionStream,
      builder: (context, snapshot) {
        final position = snapshot.data ?? provider.position;
        final duration = provider.duration;
        final progress = duration.inMilliseconds > 0 
            ? position.inMilliseconds / duration.inMilliseconds 
            : 0.0;
        final theme = Theme.of(context);
        final isDark = theme.brightness == Brightness.dark;
        
        return LinearProgressIndicator(
          value: progress,
          backgroundColor: theme.colorScheme.primary.withOpacity(isDark ? 0.2 : 0.1),
          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          minHeight: 2,
        );
      },
    );
  }

  Widget _buildAlbumArt(MusicTrack track, double size) {
    // Convert Spotify URI to HTTP URL if needed
    String imageUrl = track.albumArt;
    if (imageUrl.startsWith('spotify:image:')) {
      final imageId = imageUrl.split(':').last;
      imageUrl = 'https://i.scdn.co/image/$imageId';
    }

    return Hero(
      tag: 'now_playing_album_art_${track.id}',
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            imageUrl,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              print('Error loading album art: $error');
              return Container(
                color: Colors.grey[800],
                child: const Icon(Icons.music_note, color: Colors.white),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTrackInfo(MusicTrack track, Color textColor) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            track.title,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
          Text(
            track.artist,
            style: TextStyle(
              color: textColor.withOpacity(0.7),
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildPlayPauseButton(YouTubeProvider provider, MusicTrack track, double iconSize, Color color) {
    return IconButton(
      icon: Icon(
        provider.isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
        color: color,
        size: iconSize,
      ),
      onPressed: () async {
        if (provider.isPlaying) {
          await provider.pause();
        } else {
          await provider.play();
        }
      },
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(),
    );
  }

  Widget _buildRecommenderAvatar(BopDropUser user, double size) {
    final theme = Theme.of(context);
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
          width: 1.0,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(size / 2),
        child: user.avatarUrl != null && user.avatarUrl!.isNotEmpty
            ? Image.network(
                user.avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildAvatarFallback(user, size),
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    width: size,
                    height: size,
                    color: theme.colorScheme.surface,
                    child: Center(
                      child: SizedBox(
                        width: size * 0.4,
                        height: size * 0.4,
                        child: CircularProgressIndicator(
                          strokeWidth: 1.5,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            theme.colorScheme.primary.withOpacity(0.7),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              )
            : _buildAvatarFallback(user, size),
      ),
    );
  }

  Widget _buildAvatarFallback(BopDropUser user, double size) {
    final theme = Theme.of(context);
    
    return Container(
      width: size,
      height: size,
      color: theme.colorScheme.primary.withOpacity(0.1),
      child: Center(
        child: Text(
          user.initials,
          style: TextStyle(
            color: theme.colorScheme.primary,
            fontSize: size * 0.4, // Responsive text size
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildLikeButton(BopDrop bopDrop, double buttonSize, double iconSize) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(buttonSize / 2),
      child: InkWell(
        onTap: () => _toggleLike(bopDrop),
        borderRadius: BorderRadius.circular(buttonSize / 2),
        child: Container(
          width: buttonSize,
          height: buttonSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: bopDrop.isLikedByUser 
                ? Colors.red.withOpacity(0.1)
                : Colors.transparent,
          ),
          child: Center(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                bopDrop.isLikedByUser ? Icons.favorite : Icons.favorite_border,
                key: ValueKey(bopDrop.isLikedByUser),
                color: bopDrop.isLikedByUser 
                    ? Colors.red 
                    : theme.colorScheme.onSurface.withOpacity(0.6),
                size: iconSize,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _toggleLike(BopDrop bopDrop) async {
    try {
      HapticFeedback.lightImpact();
      
      if (bopDrop.isLikedByUser) {
        await BopDropsService.unlikeBopDrop(bopDrop.id);
        if (kDebugMode) {
          print('🎵 [NowPlayingBar] Unliked bop drop: ${bopDrop.trackTitle}');
        }
      } else {
        await BopDropsService.likeBopDrop(bopDrop.id);
        if (kDebugMode) {
          print('🎵 [NowPlayingBar] Liked bop drop: ${bopDrop.trackTitle}');
        }
      }
      
      // Show subtle feedback
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              bopDrop.isLikedByUser 
                  ? 'Removed like from ${bopDrop.user.displayName}\'s recommendation'
                  : 'Liked ${bopDrop.user.displayName}\'s recommendation!',
            ),
            duration: const Duration(seconds: 2),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [NowPlayingBar] Error toggling like: $e');
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to update like. Please try again.'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }
} 