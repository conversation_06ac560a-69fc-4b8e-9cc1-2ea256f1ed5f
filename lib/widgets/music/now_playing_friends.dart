import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/themes.dart';
import '../../models/music_track.dart';
import '../../providers/spotify_provider.dart';
import 'friend_music_controls.dart';
import 'dismissible_music_container.dart';
import 'friend_listening_card.dart';
import 'recent_tracks_section.dart';

class NowPlayingFriends extends StatefulWidget {
  const NowPlayingFriends({Key? key}) : super(key: key);

  @override
  State<NowPlayingFriends> createState() => _NowPlayingFriendsState();
}

class _NowPlayingFriendsState extends State<NowPlayingFriends> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _slideAnimation = Tween<double>(begin: 30, end: 0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  void _handleDismiss() {
    _animationController.reverse().then((_) {
      // Handle dismissal
    });
  }

  Widget _buildHeader(BuildContext context, bool isSmallScreen) {
    return Padding(
      padding: EdgeInsets.fromLTRB(14, isSmallScreen ? 8 : 10, 14, isSmallScreen ? 6 : 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(
                Icons.headphones_rounded,
                size: isSmallScreen ? 16 : 18,
                color: AppTheme.primaryColor,
              ),
              const SizedBox(width: 6),
              Text(
                'Friends Listening',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: isSmallScreen ? 13 : 14,
                  letterSpacing: -0.3,
                ),
              ),
            ],
          ),
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.refresh_rounded),
                onPressed: () {},
                tooltip: 'Refresh',
                iconSize: isSmallScreen ? 18 : 20,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              const SizedBox(width: 6),
              IconButton(
                icon: Icon(_isExpanded ? Icons.unfold_less_rounded : Icons.unfold_more_rounded),
                onPressed: _toggleExpanded,
                tooltip: _isExpanded ? 'Show less' : 'Show more',
                iconSize: isSmallScreen ? 18 : 20,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFriendsList(bool isSmallScreen) {
    return SizedBox(
      height: isSmallScreen ? 120 : 140,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        physics: const BouncingScrollPhysics(),
        itemCount: 3,
        itemBuilder: (context, index) {
          final track = MusicTrack(
            id: 'track$index',
            title: 'Track ${index + 1}',
            artist: 'Artist ${index + 1}',
            album: 'Album ${index + 1}',
            albumArt: 'https://via.placeholder.com/300',
            albumArtUrl: 'https://via.placeholder.com/300',
            previewUrl: '',
            url: '',
            service: 'spotify',
            serviceType: 'spotify',
            genres: [],
            durationMs: 180000,
            uri: 'spotify:track:mock$index',
            explicit: false,
            popularity: 80,
          );

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: FriendListeningCard(
              name: 'Friend ${index + 1}',
              avatarUrl: 'https://via.placeholder.com/150',
              track: track,
              isSmallScreen: isSmallScreen,
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;
    
    return DismissibleMusicContainer(
      isSmallScreen: isSmallScreen,
      onDismiss: _handleDismiss,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, _slideAnimation.value),
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: Container(
                margin: EdgeInsets.fromLTRB(12, 0, 12, isSmallScreen ? 8 : 12),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.04),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final headerHeight = isSmallScreen ? 36.0 : 40.0;
                    final listHeight = isSmallScreen ? 110.0 : 130.0;
                    final recentSectionHeight = isSmallScreen ? 85.0 : 95.0;
                    final dividerHeight = 1.0;
                    
                    final totalHeight = _isExpanded 
                        ? headerHeight + listHeight + dividerHeight + recentSectionHeight
                        : headerHeight + listHeight;
                    
                    return SizedBox(
                      height: totalHeight,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: headerHeight,
                            child: _buildHeader(context, isSmallScreen),
                          ),
                          SizedBox(
                            height: listHeight,
                            child: _buildFriendsList(isSmallScreen),
                          ),
                          if (_isExpanded) ...[
                            const Divider(height: 1, thickness: 1),
                            SizedBox(
                              height: recentSectionHeight,
                              child: RecentTracksSection(
                                isSmallScreen: isSmallScreen,
                                recentTracks: Provider.of<SpotifyProvider>(context).recentTracks ?? [],
                              ),
                            ),
                          ],
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }
} 