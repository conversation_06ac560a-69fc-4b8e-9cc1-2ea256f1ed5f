import 'package:flutter/material.dart';
import '../../config/themes.dart';
import '../../models/music_track.dart';

class FriendMusicControls extends StatelessWidget {
  final MusicTrack track;
  final VoidCallback onPlay;
  final VoidCallback onPause;
  final bool isPlaying;
  final bool isSmallScreen;
  
  const FriendMusicControls({
    Key? key,
    required this.track,
    required this.onPlay,
    required this.onPause,
    required this.isPlaying,
    this.isSmallScreen = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.6),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildControlButton(
            context,
            icon: isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
            onPressed: isPlaying ? onPause : onPlay,
          ),
          if (!isSmallScreen) ...[
            const SizedBox(width: 4),
            _buildControlButton(
              context,
              icon: Icons.playlist_add_rounded,
              onPressed: () {
                // Add to playlist functionality
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildControlButton(
    BuildContext context, {
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(15),
        child: Container(
          width: isSmallScreen ? 28 : 32,
          height: isSmallScreen ? 28 : 32,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.transparent,
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: isSmallScreen ? 18 : 20,
          ),
        ),
      ),
    );
  }
} 