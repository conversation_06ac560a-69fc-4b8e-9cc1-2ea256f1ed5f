import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import '../../providers/spotify_provider.dart';
import '../../models/music_track.dart';
import '../../screens/music/player_screen.dart';
import '../../config/theme_constants.dart';

/// A mini player widget that appears at the bottom of the screen
/// when music is playing. It can be expanded to the full player.
class MiniPlayer extends StatelessWidget {
  const MiniPlayer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final track = spotifyProvider.currentTrack;
    
    // If no track is playing, don't display the mini player
    if (track == null || !spotifyProvider.isConnected) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: () => _openFullPlayer(context, track),
      child: Container(
        width: double.infinity,
        height: 64,
        margin: const EdgeInsets.only(bottom: 8, left: 8, right: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 10,
              spreadRadius: -2,
            ),
          ],
        ),
        clipBehavior: Clip.antiAlias,
        child: Stack(
          children: [
            // Blurred background with gradient
            Positioned.fill(
              child: _buildBackground(track),
            ),
            
            // Actual player content
            Positioned.fill(
              child: Material(
                color: Colors.transparent,
                child: Row(
                  children: [
                    // Album art
                    _buildAlbumArt(track),
                    
                    // Track info
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              track.title,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              track.artist,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 12,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    // Controls
                    Row(
                      children: [
                        // Previous button
                        IconButton(
                          icon: Icon(
                            Icons.skip_previous,
                            color: Colors.white.withOpacity(0.9),
                            size: 22,
                          ),
                          onPressed: spotifyProvider.skipPrevious,
                          iconSize: 22,
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(
                            minWidth: 36,
                            minHeight: 36,
                          ),
                        ),
                        
                        // Play/Pause button
                        IconButton(
                          icon: Icon(
                            spotifyProvider.isPlaying
                                ? Icons.pause_circle_filled
                                : Icons.play_circle_filled,
                            color: Colors.white,
                            size: 34,
                          ),
                          onPressed: () {
                            if (spotifyProvider.isPlaying) {
                              spotifyProvider.pause();
                            } else {
                              spotifyProvider.resume();
                            }
                          },
                          iconSize: 34,
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(
                            minWidth: 46,
                            minHeight: 46,
                          ),
                        ),
                        
                        // Next button
                        IconButton(
                          icon: Icon(
                            Icons.skip_next,
                            color: Colors.white.withOpacity(0.9),
                            size: 22,
                          ),
                          onPressed: spotifyProvider.skipNext,
                          iconSize: 22,
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(
                            minWidth: 36,
                            minHeight: 36,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            // Progress indicator at the bottom
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildProgressIndicator(spotifyProvider),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build the background of the mini player with blur effect
  Widget _buildBackground(MusicTrack track) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Stack(
        children: [
          // Gradient background
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primaryColorLight.withOpacity(0.8),
                  musicPinColor.withOpacity(0.7),
                ],
              ),
            ),
          ),
          
          // Blur filter for a modern glass effect
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              color: Colors.black.withOpacity(0.2),
            ),
          ),
          
          // Optional subtle pattern or texture overlay
          Opacity(
            opacity: 0.05,
            child: Image.network(
              track.albumArt.isNotEmpty ? track.albumArt : 'https://via.placeholder.com/200',
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              errorBuilder: (context, error, stackTrace) {
                return Container(color: Colors.transparent);
              },
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build the album art with nice rounded corners
  Widget _buildAlbumArt(MusicTrack track) {
    return Container(
      width: 50,
      height: 50,
      margin: const EdgeInsets.only(left: 7),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 5,
            spreadRadius: 0,
          ),
        ],
      ),
      clipBehavior: Clip.antiAlias,
      child: track.albumArt.isNotEmpty
          ? Image.network(
              track.albumArt,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[800],
                  child: const Icon(
                    Icons.music_note,
                    color: Colors.white54,
                  ),
                );
              },
            )
          : Container(
              color: Colors.grey[800],
              child: const Icon(
                Icons.music_note,
                color: Colors.white54,
              ),
            ),
    );
  }
  
  /// Build a slim progress indicator
  Widget _buildProgressIndicator(SpotifyProvider provider) {
    // This is a simplified progress bar since we don't have continuous position updates
    return Container(
      height: 2.5,
      width: double.infinity,
      color: Colors.black.withOpacity(0.3),
      alignment: Alignment.centerLeft,
      child: StreamBuilder<int>(
        stream: provider.positionStream,
        initialData: 0,
        builder: (context, snapshot) {
          // Calculate progress percentage based on current position and duration
          final position = snapshot.data ?? 0;
          final duration = provider.duration;
          
          double progress = 0.0;
          if (duration > 0) {
            progress = position / duration;
            // Clamp to valid range
            progress = progress.clamp(0.0, 1.0);
          }
          
          return FractionallySizedBox(
            widthFactor: progress,
            child: Container(
              height: 2.5,
              color: Colors.white,
            ),
          );
        },
      ),
    );
  }
  
  /// Open the full-screen player
  void _openFullPlayer(BuildContext context, MusicTrack track) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => PlayerScreen(
          track: {
            'id': track.id,
            'title': track.title,
            'artist': track.artist,
            'albumArtUrl': track.albumArt,
            'uri': track.uri,
          },
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;
          
          var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          var offsetAnimation = animation.drive(tween);
          
          return SlideTransition(
            position: offsetAnimation,
            child: child,
          );
        },
      ),
    );
  }
} 