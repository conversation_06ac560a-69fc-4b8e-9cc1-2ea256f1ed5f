import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../models/music_track.dart';
import '../../models/collection_model.dart';
import '../../config/themes.dart';
import '../../providers/spotify_provider.dart';
import '../../services/collection_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../services/music/hybrid_queue_manager.dart';
import '../../utils/event_bus.dart';
import 'components/animated_wave_background.dart';
import 'components/music_intensity_analyzer.dart';
import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/rendering.dart';
import '../bottomsheets/collection_selector_bottomsheet.dart';

/// A modern, rich expanded view for the now playing screen
/// Displays album art, track information, and playback controls
class ExpandedNowPlayingView extends StatefulWidget {
  final VoidCallback onCollapse;
  final double height;
  final MusicTrack track;
  final bool isPlaying;
  final Function() onPlayPause;
  final Function(int) onSeek;

  const ExpandedNowPlayingView({
    Key? key,
    required this.onCollapse,
    required this.height,
    required this.track,
    required this.isPlaying,
    required this.onPlayPause,
    required this.onSeek,
  }) : super(key: key);

  @override
  State<ExpandedNowPlayingView> createState() => _ExpandedNowPlayingViewState();
}

class _ExpandedNowPlayingViewState extends State<ExpandedNowPlayingView> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final MusicIntensityAnalyzer _intensityAnalyzer = MusicIntensityAnalyzer();
  double _dragStartY = 0;
  double _dragUpdateY = 0;
  bool _isDragging = false;
  bool _isSeekDragging = false;
  double _dragValue = 0.0;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    _animationController.repeat(); // Start the shimmer animation
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleDragStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
      _dragStartY = details.globalPosition.dy;
    });
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    setState(() {
      _dragUpdateY = details.globalPosition.dy;
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!_isDragging) return;
    
    final dragDistance = _dragUpdateY - _dragStartY;
    final dragVelocity = details.primaryVelocity ?? 0;
    
    if (dragDistance > 50 || dragVelocity > 500) {
      widget.onCollapse();
    }
    
    setState(() {
      _isDragging = false;
    });
  }

  void _handleAddToCollection(BuildContext context, MusicTrack? track) {
    if (track == null) return;
    
    // Show collection selection bottom sheet
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CollectionSelectorBottomSheet(track: track),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SpotifyProvider>(
      builder: (context, spotifyProvider, child) {
        final mediaQuery = MediaQuery.of(context);
        final size = mediaQuery.size;
        final scaleFactor = (size.height / 812.0).clamp(0.8, 1.1); // Base height 812 (iPhone X)
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
        final textColor = isDarkMode ? Colors.white : Colors.black;
        final subTextColor = isDarkMode ? Colors.white.withOpacity(0.7) : Colors.black.withOpacity(0.7);
        final backgroundColor = isDarkMode 
          ? Theme.of(context).colorScheme.surface.withOpacity(0.95)
          : Theme.of(context).colorScheme.surface.withOpacity(0.98);
        final isSmallScreen = mediaQuery.size.width < 360;
        final theme = Theme.of(context);
        
        // Calculate music intensity
        final intensity = _intensityAnalyzer.calculateIntensity(
          isPlaying: widget.isPlaying,
          currentPositionMs: spotifyProvider.currentPositionMs,
          durationMs: widget.track.durationMs,
        );
        
        return MediaQuery(
          data: mediaQuery.copyWith(textScaleFactor: scaleFactor),
          child: GestureDetector(
            onVerticalDragStart: _handleDragStart,
            onVerticalDragUpdate: _handleDragUpdate,
            onVerticalDragEnd: _handleDragEnd,
            child: Container(
              decoration: BoxDecoration(
                color: backgroundColor,
              ),
              padding: EdgeInsets.only(top: 56),
              child: Stack(
                children: [
                  // Animated background
                  Positioned.fill(
                    child: AnimatedWaveBackground(
                      color: theme.colorScheme.primary,
                      isPlaying: widget.isPlaying,
                      intensity: intensity,
                    ),
                  ),
                  
                  // Status bar spacer
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: mediaQuery.padding.top,
                      decoration: BoxDecoration(
                        color: backgroundColor,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            theme.colorScheme.primary.withOpacity(isDarkMode ? 0.08 : 0.03),
                            backgroundColor,
                            backgroundColor,
                          ],
                          stops: const [0.0, 0.3, 1.0],
                        ),
                      ),
                    ),
                  ),
                  
                  // Main content
                  Column(
                    children: [
                      // Status bar spacer
                      Container(
                        height: mediaQuery.padding.top,
                      ),
                      
                      // Main content
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            final size = mediaQuery.size;
                            final scaleFactor = (size.height / 812.0).clamp(0.8, 1.1); // Base height 812 (iPhone X)
                            
                            return Column(
                              children: [
                                // Header with collapse button
                                SizedBox(
                                  height: 64 * scaleFactor,
                                  child: _buildHeader(textColor, scaleFactor),
                                ),
                                
                                // Main content area
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      // Album art
                                      Expanded(
                                        flex: 36,
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(horizontal: 32 * scaleFactor),
                                          child: Center(
                                            child: LayoutBuilder(
                                              builder: (context, artConstraints) {
                                                final artSize = math.min(artConstraints.maxWidth, artConstraints.maxHeight);
                                                return _buildAlbumArt(artSize, textColor);
                                              },
                                            ),
                                          ),
                                        ),
                                      ),
                                      
                                      // Track info
                                      Padding(
                                        padding: EdgeInsets.fromLTRB(24 * scaleFactor, 16 * scaleFactor, 24 * scaleFactor, 16 * scaleFactor),
                                        child: _buildTrackInfo(textColor, subTextColor, scaleFactor),
                                      ),
                                      
                                      // Spacer to push controls down
                                      const Spacer(flex: 1),

                                      // Progress bar & controls
                                      Padding(
                                        padding: EdgeInsets.symmetric(horizontal: 24 * scaleFactor),
                                        child: _buildProgressBar(context, textColor, scaleFactor, spotifyProvider),
                                      ),
                                      
                                      Padding(
                                        padding: EdgeInsets.symmetric(vertical: 16 * scaleFactor),
                                        child: _buildPlaybackControls(textColor, subTextColor, scaleFactor, spotifyProvider),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(Color textColor, double scaleFactor) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16 * scaleFactor),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: Icon(Icons.keyboard_arrow_down, color: textColor, size: 28 * scaleFactor),
            onPressed: widget.onCollapse,
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(minWidth: 36 * scaleFactor, minHeight: 36 * scaleFactor),
          ),
          Text(
            'Now Playing',
            style: TextStyle(
              color: textColor,
              fontSize: 16 * scaleFactor,
              fontWeight: FontWeight.w600,
            ),
          ),
          IconButton(
            icon: Icon(Icons.more_vert, color: textColor, size: 28 * scaleFactor),
            onPressed: () {
              showModalBottomSheet(context: context, builder: (context) => _buildOptionsMenu(context));
            },
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(minWidth: 36 * scaleFactor, minHeight: 36 * scaleFactor),
          ),
        ],
      ),
    );
  }

  Widget _buildAlbumArt(double size, Color textColor) {
    String imageUrl = widget.track.albumArt;
    if (imageUrl.startsWith('spotify:image:')) {
      final imageId = imageUrl.split(':').last;
      imageUrl = 'https://i.scdn.co/image/$imageId';
    }

    return Hero(
      tag: 'now_playing_album_art_${widget.track.id}',
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24 * size / 300),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 30 * size / 300,
              offset: Offset(0, 15 * size / 300),
              spreadRadius: 5 * size / 300,
            ),
            BoxShadow(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.15),
              blurRadius: 40 * size / 300,
              offset: Offset(0, 20 * size / 300),
              spreadRadius: 10 * size / 300,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24 * size / 300),
          child: Stack(
            children: [
              // Album art image
              Image.network(
                imageUrl,
                fit: BoxFit.cover,
                width: size,
                height: size,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: Colors.grey[800],
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded / 
                                loadingProgress.expectedTotalBytes!
                            : null,
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  if (kDebugMode) {
                    print('Error loading album art: $error');
                  }
                  return Container(
                    color: Colors.grey[800],
                    child: Icon(
                      Icons.music_note,
                      size: size * 0.3,
                      color: textColor.withOpacity(0.5),
                    ),
                  );
                },
              ),
              // Subtle gradient overlay
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.1),
                    ],
                  ),
                ),
              ),
              // Animated shine effect when playing
              if (widget.isPlaying)
                AnimatedPositioned(
                  duration: const Duration(milliseconds: 1500),
                  curve: Curves.easeInOut,
                  left: -size * 0.5,
                  top: -size * 0.5,
                  child: Transform(
                    transform: Matrix4.rotationZ(math.pi / 4)..translate(0.0, -size * 0.5),
                    child: Container(
                      width: size * 2,
                      height: size * 2,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.white.withOpacity(0),
                            Colors.white.withOpacity(0.1),
                            Colors.white.withOpacity(0),
                          ],
                          stops: const [0.0, 0.5, 1.0],
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrackInfo(Color textColor, Color subTextColor, double scaleFactor) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return ClipRRect(
      borderRadius: BorderRadius.circular(20 * scaleFactor),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 24 * scaleFactor,
            vertical: 20 * scaleFactor,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20 * scaleFactor),
            color: theme.colorScheme.surface.withOpacity(0.5),
            border: Border.all(
              color: theme.colorScheme.primary.withOpacity(0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withOpacity(0.05),
                blurRadius: 20,
                spreadRadius: -5,
              ),
            ],
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.surface.withOpacity(isDark ? 0.7 : 0.8),
                theme.colorScheme.surface.withOpacity(isDark ? 0.6 : 0.7),
              ],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                widget.track.title,
                style: TextStyle(
                  color: textColor,
                  fontSize: 24 * scaleFactor,
                  fontWeight: FontWeight.bold,
                  letterSpacing: -0.5,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 10 * scaleFactor),
              Text(
                widget.track.artist,
                style: TextStyle(
                  color: subTextColor,
                  fontSize: 18 * scaleFactor,
                  letterSpacing: -0.3,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 6 * scaleFactor),
              Text(
                widget.track.album,
                style: TextStyle(
                  color: subTextColor.withOpacity(0.8),
                  fontSize: 16 * scaleFactor,
                  letterSpacing: -0.2,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressBar(BuildContext context, Color textColor, double scaleFactor, SpotifyProvider spotifyProvider) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return ClipRRect(
      borderRadius: BorderRadius.circular(20 * scaleFactor),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 20 * scaleFactor,
            vertical: 16 * scaleFactor,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20 * scaleFactor),
            color: theme.colorScheme.surface.withOpacity(0.3),
            border: Border.all(
              color: theme.colorScheme.primary.withOpacity(0.1),
              width: 1,
            ),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.surface.withOpacity(isDark ? 0.4 : 0.5),
                theme.colorScheme.surface.withOpacity(isDark ? 0.3 : 0.4),
              ],
            ),
          ),
          child: Column(
            children: [
              SliderTheme(
                data: SliderThemeData(
                  activeTrackColor: theme.colorScheme.primary.withOpacity(isDark ? 0.9 : 0.8),
                  inactiveTrackColor: theme.colorScheme.primary.withOpacity(isDark ? 0.2 : 0.1),
                  thumbColor: theme.colorScheme.primary,
                  overlayColor: theme.colorScheme.primary.withOpacity(0.2),
                  trackHeight: 4.0 * scaleFactor,
                  thumbShape: RoundSliderThumbShape(
                    enabledThumbRadius: 6 * scaleFactor,
                    pressedElevation: 8,
                  ),
                  overlayShape: RoundSliderOverlayShape(overlayRadius: 20 * scaleFactor),
                ),
                child: Slider(
                  value: _isSeekDragging ? _dragValue : spotifyProvider.playbackPosition,
                  onChanged: (value) {
                    setState(() {
                      _isSeekDragging = true;
                      _dragValue = value;
                    });
                  },
                  onChangeEnd: (value) {
                    setState(() {
                      _isSeekDragging = false;
                    });
                    final position = (value * widget.track.durationMs).round();
                    widget.onSeek(position);
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(spotifyProvider.currentPositionMs),
                      style: TextStyle(
                        color: textColor.withOpacity(0.7),
                        fontSize: 13 * scaleFactor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _formatDuration(widget.track.durationMs),
                      style: TextStyle(
                        color: textColor.withOpacity(0.7),
                        fontSize: 13 * scaleFactor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaybackControls(Color textColor, Color subTextColor, double scaleFactor, SpotifyProvider spotifyProvider) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24 * scaleFactor),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: Icon(
              Icons.shuffle_rounded,
              color: spotifyProvider.shuffleEnabled
                  ? theme.colorScheme.primary
                  : textColor.withOpacity(0.7),
              size: 28 * scaleFactor,
            ),
            onPressed: () async {
              final success = await spotifyProvider.toggleShuffle();
              if (!success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Failed to toggle shuffle')),
                );
              }
            },
          ),
          IconButton(
            icon: Icon(
              Icons.skip_previous_rounded,
              color: textColor.withOpacity(0.9),
              size: 36 * scaleFactor,
            ),
            onPressed: () async {
              final success = await spotifyProvider.skipPrevious();
              if (!success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Failed to skip to previous track')),
                );
              }
            },
          ),
          Container(
            width: 72 * scaleFactor,
            height: 72 * scaleFactor,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colorScheme.primary.withOpacity(isDark ? 0.9 : 0.8),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withOpacity(0.3),
                  blurRadius: 12 * scaleFactor,
                  spreadRadius: 2 * scaleFactor,
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.onPlayPause,
                customBorder: const CircleBorder(),
                child: Icon(
                  widget.isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
                  color: Colors.white,
                  size: 36 * scaleFactor,
                ),
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.skip_next_rounded,
              color: textColor.withOpacity(0.9),
              size: 36 * scaleFactor,
            ),
            onPressed: () async {
              final success = await spotifyProvider.skipNext();
              if (!success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Failed to skip to next track')),
                );
              }
            },
          ),
          IconButton(
            icon: Icon(
              Icons.queue_music_rounded,
              color: textColor.withOpacity(0.8),
              size: 28 * scaleFactor,
            ),
            onPressed: () => _showQueueDialog(context, spotifyProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionsMenu(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.collections_bookmark_outlined),
            title: const Text('Add to Collection'),
            onTap: () {
              Navigator.pop(context);
              _handleAddToCollection(context, widget.track);
            },
          ),
          ListTile(
            leading: const Icon(Icons.playlist_add),
            title: const Text('Add to playlist'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Add to playlist (coming soon)'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Share (coming soon)'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.album),
            title: const Text('View album'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('View album (coming soon)'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('View artist'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('View artist (coming soon)'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  String _formatDuration(int milliseconds) {
    final duration = Duration(milliseconds: milliseconds);
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  /// Show queue dialog with current queue and upcoming tracks
  void _showQueueDialog(BuildContext context, SpotifyProvider spotifyProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => QueueDialog(spotifyProvider: spotifyProvider),
    );
  }
}

/// Stateful queue dialog that can update items in place
class QueueDialog extends StatefulWidget {
  final SpotifyProvider spotifyProvider;

  const QueueDialog({Key? key, required this.spotifyProvider}) : super(key: key);

  @override
  State<QueueDialog> createState() => _QueueDialogState();
}

class _QueueDialogState extends State<QueueDialog> {
  Map<String, dynamic>? queueData;
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadQueue();
  }

  Future<void> _loadQueue() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });
      
      final data = await widget.spotifyProvider.hybridQueueManager.getCombinedQueue();
      
      if (mounted) {
        setState(() {
          queueData = data;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          error = e.toString();
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.queue_music_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Queue',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close_rounded),
                ),
              ],
            ),
          ),
          
          // Queue content
          Expanded(
            child: _buildQueueContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildQueueContent() {
    if (isLoading) {
      return _buildQueueSkeletonLoader();
    }
    
    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_rounded,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load queue',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: _loadQueue,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }
    
    if (queueData == null) {
      return const Center(child: CircularProgressIndicator());
    }
    
    final currentlyPlaying = queueData!['currently_playing'];
    final localQueue = queueData!['local_queue'] as List<MusicTrack>;
    
    if (localQueue.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.queue_music_rounded,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No tracks in queue',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add some tracks to get started!',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }
    
    return CustomScrollView(
      slivers: [
        // Currently Playing Section
        if (currentlyPlaying != null) ...[
          SliverToBoxAdapter(
            child: _buildCurrentlyPlayingSection(currentlyPlaying),
          ),
        ],
        
        // Managed Queue Section (draggable)
        SliverToBoxAdapter(
          child: _buildQueueSectionHeader(
            'Queue (${localQueue.length})',
            'Drag to reorder • Swipe to remove',
            Theme.of(context).colorScheme.primary,
          ),
        ),
        SliverReorderableList(
          itemCount: localQueue.length,
          onReorder: _onReorder,
          itemBuilder: (context, index) {
            final track = localQueue[index];
            return _buildManagedQueueItem(
              key: ValueKey('local_${track.id}'),
              track: track,
              index: index,
              onRemove: () => _onRemove(index),
            );
          },
        ),
      ],
    );
  }

  void _onReorder(int oldIndex, int newIndex) async {
    // Adjust newIndex for Flutter's reorderable list behavior
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    
    try {
      // Update UI immediately for smooth experience
      final localQueue = queueData!['local_queue'] as List<MusicTrack>;
      final track = localQueue.removeAt(oldIndex);
      localQueue.insert(newIndex, track);
      
      setState(() {
        queueData!['local_queue'] = localQueue;
      });
      
      // Update backend in background
      await widget.spotifyProvider.hybridQueueManager.reorderLocalQueue(oldIndex, newIndex);
      
    } catch (e) {
      // If backend update fails, revert the UI change
      _loadQueue();
    }
  }

  void _onRemove(int index) async {
    try {
      // Update UI immediately for smooth experience
      final localQueue = queueData!['local_queue'] as List<MusicTrack>;
      final removedTrack = localQueue.removeAt(index);
      
      setState(() {
        queueData!['local_queue'] = localQueue;
      });
      
      // Update backend in background
      await widget.spotifyProvider.hybridQueueManager.removeFromLocalQueue(index);
      
    } catch (e) {
      // If backend update fails, revert the UI change
      _loadQueue();
    }
  }

  // Helper methods moved from parent class
  Widget _buildQueueSkeletonLoader() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: 8,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildShimmerBox(width: 24, height: 16, borderRadius: 4),
                const SizedBox(width: 12),
                _buildShimmerBox(width: 48, height: 48, borderRadius: 4),
              ],
            ),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(
                  width: MediaQuery.of(context).size.width * 0.6,
                  height: 16,
                  borderRadius: 4,
                ),
                const SizedBox(height: 8),
                _buildShimmerBox(
                  width: MediaQuery.of(context).size.width * 0.4,
                  height: 14,
                  borderRadius: 4,
                ),
              ],
            ),
            trailing: _buildShimmerBox(width: 32, height: 14, borderRadius: 4),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({
    required double width,
    required double height,
    required double borderRadius,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        color: Colors.grey.withOpacity(0.3),
      ),
    );
  }

  Widget _buildCurrentlyPlayingSection(Map<String, dynamic> currentlyPlaying) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.play_arrow_rounded,
              size: 18,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 16),
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: Colors.grey.withOpacity(0.2),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: _buildAlbumArtForQueue(currentlyPlaying),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  currentlyPlaying['name'] ?? 'Unknown Track',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  _getArtistName(currentlyPlaying),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  'Now Playing',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          if (currentlyPlaying['duration_ms'] != null)
            Text(
              _formatDuration(currentlyPlaying['duration_ms']),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildQueueSectionHeader(String title, String subtitle, Color color) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagedQueueItem({
    required Key key,
    required MusicTrack track,
    required int index,
    required VoidCallback onRemove,
  }) {
    return ReorderableDragStartListener(
      key: key,
      index: index,
      child: Dismissible(
        key: ValueKey('dismissible_${track.id}'),
        direction: DismissDirection.endToStart,
        background: Container(
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.only(right: 20),
          color: Colors.red,
          child: const Icon(
            Icons.delete_rounded,
            color: Colors.white,
          ),
        ),
        onDismissed: (direction) => onRemove(),
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
          child: Material(
            color: Colors.transparent,
            child: ListTile(
              leading: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 24,
                    alignment: Alignment.center,
                    child: Text(
                      '${index + 1}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: Colors.grey.withOpacity(0.2),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: track.albumArt.isNotEmpty
                          ? Image.network(
                              track.albumArt,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => Icon(
                                Icons.music_note_rounded,
                                color: Colors.grey[400],
                              ),
                            )
                          : Icon(
                              Icons.music_note_rounded,
                              color: Colors.grey[400],
                            ),
                    ),
                  ),
                ],
              ),
              title: Text(
                track.title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              subtitle: Text(
                track.artist,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _formatDuration(track.durationMs),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.drag_handle_rounded,
                    color: Colors.grey[400],
                    size: 20,
                  ),
                ],
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumArtForQueue(Map<String, dynamic> item) {
    final album = item['album'] as Map<String, dynamic>?;
    final images = album?['images'] as List<dynamic>?;
    final imageUrl = images?.isNotEmpty == true ? images!.first['url'] as String? : null;
    
    if (imageUrl != null) {
      return Image.network(
        imageUrl,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Icon(
          Icons.music_note_rounded,
          color: Colors.grey[400],
        ),
      );
    } else {
      return Icon(
        Icons.music_note_rounded,
        color: Colors.grey[400],
      );
    }
  }
  
  String _getArtistName(Map<String, dynamic> item) {
    final artists = item['artists'] as List<dynamic>?;
    if (artists?.isNotEmpty == true) {
      return artists!.map((artist) => artist['name'] as String? ?? 'Unknown').join(', ');
    }
    return 'Unknown Artist';
  }

  String _formatDuration(int milliseconds) {
    final duration = Duration(milliseconds: milliseconds);
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
} 