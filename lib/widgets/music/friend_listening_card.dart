import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/music_track.dart';
import '../../providers/spotify_provider.dart';
import '../../config/themes.dart';

class FriendListeningCard extends StatelessWidget {
  final String name;
  final String avatarUrl;
  final MusicTrack track;
  final bool isSmallScreen;

  const FriendListeningCard({
    Key? key,
    required this.name,
    required this.avatarUrl,
    required this.track,
    required this.isSmallScreen,
  }) : super(key: key);

  // Avatar section
  Widget _buildAvatar(BuildContext context) {
    return Container(
      width: isSmallScreen ? 40 : 48,
      height: isSmallScreen ? 40 : 48,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Theme.of(context).colorScheme.surfaceVariant,
      ),
      child: ClipOval(
        child: Icon(
          Icons.person_rounded,
          size: isSmallScreen ? 24 : 28,
          color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final isPlaying = spotifyProvider.currentTrack?.id == track.id && 
                     spotifyProvider.isPlaying;
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (isPlaying) {
            spotifyProvider.pause();
          } else {
            spotifyProvider.playTrack(track.uri);
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: isSmallScreen ? 100 : 120,
          padding: const EdgeInsets.all(8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Avatar with online indicator
              Stack(
                children: [
                  _buildAvatar(context),
                  Positioned(
                    right: 0,
                    bottom: 0,
                    child: Container(
                      width: isSmallScreen ? 10 : 12,
                      height: isSmallScreen ? 10 : 12,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Theme.of(context).cardColor,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // Track info
              Flexible(
                child: Text(
                  track.title,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: isSmallScreen ? 11 : 12,
                    height: 1.1,
                    letterSpacing: -0.2,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 2),
              Flexible(
                child: Text(
                  track.artist,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 10 : 11,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                    height: 1.1,
                    letterSpacing: -0.1,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 4),
              // Play/pause indicator
              Container(
                width: isSmallScreen ? 24 : 28,
                height: isSmallScreen ? 24 : 28,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isPlaying 
                      ? AppTheme.primaryColor.withOpacity(0.1)
                      : Theme.of(context).colorScheme.surfaceVariant,
                ),
                child: Icon(
                  isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
                  size: isSmallScreen ? 16 : 18,
                  color: isPlaying 
                      ? AppTheme.primaryColor
                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 