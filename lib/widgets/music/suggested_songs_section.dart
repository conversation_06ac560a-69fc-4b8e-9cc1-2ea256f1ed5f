import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/music_track.dart';
import 'track_card.dart';

/// A horizontal scrollable section displaying suggested songs based on user's artists
class SuggestedSongsSection extends StatelessWidget {
  final List<MusicTrack> suggestedSongs;
  final bool isLoading;
  final Function(MusicTrack) onTrackSelected;
  final Function(MusicTrack)? onTrackPlay;
  final String title;
  final String subtitle;

  const SuggestedSongsSection({
    Key? key,
    required this.suggestedSongs,
    required this.isLoading,
    required this.onTrackSelected,
    this.onTrackPlay,
    this.title = 'Suggested for You',
    this.subtitle = 'Based on your favorite artists',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.auto_awesome,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            subtitle,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Content
          SizedBox(
            height: 140, // Increased height for longer cards
            child: isLoading
                ? _buildLoadingState(context, isDark)
                : suggestedSongs.isEmpty
                    ? _buildEmptyState(context, isDark)
                    : _buildSongsList(context),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context, bool isDark) {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: 3,
      itemBuilder: (context, index) {
        return Container(
          width: 320, // Increased width for longer cards
          margin: const EdgeInsets.only(right: 12),
          child: _buildLoadingCard(context, isDark),
        );
      },
    );
  }

  Widget _buildLoadingCard(BuildContext context, bool isDark) {
    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Album art shimmer
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: isDark ? Colors.grey[800] : Colors.grey[300],
                borderRadius: BorderRadius.circular(10),
              ),
              child: _buildShimmerEffect(isDark),
            ),
            
            const SizedBox(width: 16),
            
            // Text content shimmer
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    height: 16,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: isDark ? Colors.grey[800] : Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: _buildShimmerEffect(isDark),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 12,
                    width: 120,
                    decoration: BoxDecoration(
                      color: isDark ? Colors.grey[800] : Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: _buildShimmerEffect(isDark),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerEffect(bool isDark) {
    return ShaderMask(
      shaderCallback: (bounds) {
        return LinearGradient(
          colors: [
            Colors.transparent,
            isDark ? Colors.grey[700]! : Colors.white,
            Colors.transparent,
          ],
          stops: const [0.0, 0.5, 1.0],
          begin: const Alignment(-1.0, -0.5),
          end: const Alignment(1.0, 0.5),
          tileMode: TileMode.clamp,
        ).createShader(bounds);
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: isDark ? Colors.grey[850] : Colors.white,
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isDark) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.music_note_outlined,
              size: 32,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.4),
            ),
            const SizedBox(height: 8),
            Text(
              'No suggestions available',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.6),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSongsList(BuildContext context) {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: suggestedSongs.length,
      itemBuilder: (context, index) {
        final track = suggestedSongs[index];
        return Container(
          width: 320, // Increased width for longer cards
          margin: const EdgeInsets.only(right: 12),
          child: TrackCard(
            track: track,
            onTap: () {
              HapticFeedback.lightImpact();
              onTrackSelected(track);
            },
            subtitle: 'Suggested',
            showPlayButton: true, // Enable play button for suggested songs
            onPlay: onTrackPlay != null ? () {
              HapticFeedback.lightImpact();
              onTrackPlay!(track);
            } : null,
            enableSwipeToQueue: false,
          ),
        );
      },
    );
  }
}
