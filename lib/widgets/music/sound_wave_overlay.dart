import 'package:flutter/material.dart';
import 'dart:math' as math;

class SoundWaveOverlay extends StatefulWidget {
  final bool isPlaying;
  final bool isDayMode;
  final int? currentPosition; // Current playback position in milliseconds
  final int? duration; // Track duration in milliseconds
  final String? trackId; // Current track ID to detect changes

  const SoundWaveOverlay({
    Key? key,
    required this.isPlaying,
    required this.isDayMode,
    this.currentPosition,
    this.duration,
    this.trackId,
  }) : super(key: key);

  @override
  State<SoundWaveOverlay> createState() => _SoundWaveOverlayState();
}

class _SoundWaveOverlayState extends State<SoundWaveOverlay>
    with TickerProviderStateMixin {
  late List<AnimationController> _waveControllers;
  late List<Animation<double>> _waveAnimations;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  late AnimationController _colorController;
  late Animation<double> _colorAnimation;
  late AnimationController _beatController;
  late Animation<double> _beatAnimation;
  
  // Cache for expensive position calculations
  static final Map<int, Offset> _positionCache = {};
  
  // Music-reactive properties with smoothing
  String? _lastTrackId;
  double _currentAmplitude = 0.6; // Start with moderate amplitude
  double _targetAmplitude = 0.6; // Target for smooth transitions
  double _beatIntensity = 0.8; // Reduced from 1.0 for subtlety
  int _lastBeatTime = 0;
  late int _estimatedBPM;
  
  // Smoothing variables
  double _smoothedAmplitude = 0.6;
  double _smoothedBeatIntensity = 0.8;
  final double _smoothingFactor = 0.15; // Lower = smoother transitions

  @override
  void initState() {
    super.initState();
    
    // Estimate BPM for beat detection (more conservative range)
    _estimatedBPM = 110 + (math.Random().nextInt(50)); // 110-160 BPM range
    
    // Create 4 wave controllers with more consistent, slower timing
    _waveControllers = List.generate(4, (index) => 
      AnimationController(
        duration: Duration(milliseconds: _calculateWaveDuration(index)), 
        vsync: this,
      )
    );
    
    // Create wave animations with smoother, less aggressive curves
    _waveAnimations = _waveControllers.asMap().entries.map((entry) {
      final curves = [
        Curves.easeInOutSine,      // Smooth sine wave
        Curves.easeInOutQuad,      // Gentle quadratic
        Curves.easeInOutCubic,     // Smooth cubic
        Curves.easeInOutSine,      // Back to sine for consistency
      ];
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: entry.value, curve: curves[entry.key])
      );
    }).toList();
    
    // Much more subtle beat animation
    _beatController = AnimationController(
      duration: Duration(milliseconds: (60000 / _estimatedBPM).round()),
      vsync: this,
    );
    
    _beatAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _beatController, curve: Curves.easeOutCubic) // Smoother curve
    );
    
    // Very subtle pulse animation
    _pulseController = AnimationController(
      duration: Duration(milliseconds: (60000 / _estimatedBPM * 3).round()), // Slower pulse
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(begin: 0.98, end: 1.04).animate( // Much smaller range
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOutSine)
    );
    
    // Slower, more elegant color transitions
    _colorController = AnimationController(
      duration: Duration(milliseconds: _estimatedBPM * 40), // Even slower
      vsync: this,
    );
    
    _colorAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _colorController, curve: Curves.easeInOutSine)
    );
    
    // Start animations if playing
    if (widget.isPlaying) {
      _startAnimations();
    }
  }

  int _calculateWaveDuration(int cornerIndex) {
    // More consistent timing based on BPM
    final baseDuration = (60000 / _estimatedBPM * 2.5).round(); // Slower base
    final variations = [0, 150, 300, 225]; // Smaller variations
    return baseDuration + variations[cornerIndex];
  }

  void _updateMusicReactiveProperties() {
    if (widget.currentPosition == null || widget.duration == null) return;
    
    final position = widget.currentPosition!;
    final duration = widget.duration!;
    
    // Calculate amplitude with smoother, more realistic simulation
    final timeInSeconds = position / 1000.0;
    
    // More subtle frequency simulation
    final lowFreq = math.sin(timeInSeconds * 0.3) * 0.15; // Reduced amplitude
    final midFreq = math.sin(timeInSeconds * 1.2) * 0.2;  // Reduced amplitude
    final highFreq = math.sin(timeInSeconds * 4.0) * 0.15; // Reduced amplitude
    
    _targetAmplitude = (0.6 + lowFreq + midFreq + highFreq).clamp(0.3, 0.9); // Narrower range
    
    // Smooth amplitude transitions
    _smoothedAmplitude += (_targetAmplitude - _smoothedAmplitude) * _smoothingFactor;
    _currentAmplitude = _smoothedAmplitude;
    
    // More conservative beat detection
    final beatInterval = (60000 / _estimatedBPM).round();
    final currentBeatTime = (position / beatInterval).floor() * beatInterval;
    
    if (currentBeatTime != _lastBeatTime && (currentBeatTime - _lastBeatTime).abs() > beatInterval * 0.8) {
      _lastBeatTime = currentBeatTime;
      _triggerBeat();
    }
    
    // Smoother beat intensity based on song progress
    final progress = position / duration;
    final targetBeatIntensity = (0.6 + math.sin(progress * math.pi) * 0.3).clamp(0.4, 0.9); // Reduced range
    _smoothedBeatIntensity += (targetBeatIntensity - _smoothedBeatIntensity) * _smoothingFactor;
    _beatIntensity = _smoothedBeatIntensity;
  }

  void _triggerBeat() {
    // Only trigger beat occasionally and subtly
    if (math.Random().nextDouble() < 0.4) { // Reduced from 1.0 to 0.4
      _beatController.forward(from: 0.0);
    }
    
    // Very rarely trigger pulse
    if (math.Random().nextDouble() < 0.15) { // Reduced from 0.3 to 0.15
      _pulseController.forward(from: 0.0).then((_) {
        _pulseController.reverse();
      });
    }
  }

  void _startAnimations() {
    // Update music-reactive properties
    _updateMusicReactiveProperties();
    
    // Start wave controllers with more subtle delays
    for (int i = 0; i < _waveControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () { // Longer delays
        if (mounted) _waveControllers[i].repeat();
      });
    }
    
    // Start other animations
    _beatController.repeat();
    _pulseController.repeat(reverse: true);
    _colorController.repeat();
  }

  void _stopAnimations() {
    for (var controller in _waveControllers) {
      controller.stop();
      controller.reset();
    }
    _pulseController.stop();
    _pulseController.reset();
    _colorController.stop();
    _colorController.reset();
    _beatController.stop();
    _beatController.reset();
  }

  void _adjustAnimationsForNewTrack() {
    // More conservative BPM estimation
    _estimatedBPM = 100 + (math.Random().nextInt(60)); // 100-160 BPM
    
    // Reset smoothed values for new track
    _smoothedAmplitude = 0.6;
    _smoothedBeatIntensity = 0.8;
    
    // Update animation durations
    final beatDuration = (60000 / _estimatedBPM).round();
    _beatController.duration = Duration(milliseconds: beatDuration);
    _pulseController.duration = Duration(milliseconds: beatDuration * 3); // Slower pulse
    
    // Update wave controllers
    for (int i = 0; i < _waveControllers.length; i++) {
      _waveControllers[i].duration = Duration(milliseconds: _calculateWaveDuration(i));
    }
    
    if (widget.isPlaying) {
      _startAnimations();
    }
  }

  @override
  void didUpdateWidget(SoundWaveOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Handle track changes
    if (widget.trackId != null && widget.trackId != _lastTrackId) {
      _lastTrackId = widget.trackId;
      _adjustAnimationsForNewTrack();
    }
    
    // Handle play/pause state changes
    if (widget.isPlaying != oldWidget.isPlaying) {
      if (widget.isPlaying) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    }
    
    // Update music-reactive properties when position changes
    if (widget.isPlaying && 
        (widget.currentPosition != oldWidget.currentPosition ||
         widget.duration != oldWidget.duration)) {
      _updateMusicReactiveProperties();
    }
  }

  @override
  void dispose() {
    for (var controller in _waveControllers) {
      controller.dispose();
    }
    _pulseController.dispose();
    _colorController.dispose();
    _beatController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isPlaying) return const SizedBox.shrink();
    
    return Stack(
      children: [
        // Top-left corner waves
        _buildCornerWaves(0, Alignment.topLeft),
        // Top-right corner waves  
        _buildCornerWaves(1, Alignment.topRight),
        // Bottom-left corner waves
        _buildCornerWaves(2, Alignment.bottomLeft),
        // Bottom-right corner waves
        _buildCornerWaves(3, Alignment.bottomRight),
      ],
    );
  }

  Widget _buildCornerWaves(int cornerIndex, Alignment alignment) {
    // Calculate position once and cache it
    final position = _positionCache.putIfAbsent(cornerIndex, () {
      final angles = [
        -math.pi / 4,      // Top-left (10:30)
        math.pi / 4,       // Top-right (1:30)  
        -3 * math.pi / 4,  // Bottom-left (7:30)
        3 * math.pi / 4,   // Bottom-right (4:30)
      ];
      
      final angle = angles[cornerIndex];
      final distanceFromGlobeCenter = (cornerIndex == 0 || cornerIndex == 2)
          ? 140.0  // Top-left and bottom-left both closer to Earth
          : 200.0; // Top-right and bottom-right moved further away
      
      return Offset(
        math.cos(angle) * distanceFromGlobeCenter,
        math.sin(angle) * distanceFromGlobeCenter,
      );
    });
    
    return Positioned(
      left: MediaQuery.of(context).size.width / 2 - 60 + position.dx,
      top: MediaQuery.of(context).size.height / 2 * 0.85 - 60 + position.dy,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _waveAnimations[cornerIndex], 
          _pulseAnimation, 
          _colorAnimation,
          _beatAnimation,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value * (1.0 + _beatAnimation.value * 0.05), // Reduced beat scale
            child: RepaintBoundary(
              child: CustomPaint(
                size: const Size(120, 120),
                painter: SmoothSoundWavePainter(
                  progress: _waveAnimations[cornerIndex].value,
                  colorProgress: _colorAnimation.value,
                  beatProgress: _beatAnimation.value,
                  amplitude: _currentAmplitude,
                  beatIntensity: _beatIntensity,
                  isDayMode: widget.isDayMode,
                  cornerIndex: cornerIndex,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class SmoothSoundWavePainter extends CustomPainter {
  final double progress;
  final double colorProgress;
  final double beatProgress;
  final double amplitude;
  final double beatIntensity;
  final bool isDayMode;
  final int cornerIndex;
  
  // Cache expensive calculations
  static final Map<int, List<double>> _angleCache = {};
  static final Map<int, double> _rotationCache = {};
  static final Map<String, Paint> _paintCache = {};

  SmoothSoundWavePainter({
    required this.progress,
    required this.colorProgress,
    required this.beatProgress,
    required this.amplitude,
    required this.beatIntensity,
    required this.isDayMode,
    required this.cornerIndex,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw smooth, elegant wave layers
    _drawSubtleGlowWaves(canvas, center, size);
    _drawMainWaves(canvas, center);
    _drawAccentWaves(canvas, center);
  }

  void _drawSubtleGlowWaves(Canvas canvas, Offset center, Size size) {
    final glowPaint = _getGlowPaint();
    final rotationAngle = _getRotationAngle(cornerIndex);
    
    // Only 2 glow waves for cleaner look
    for (int waveIndex = 0; waveIndex < 2; waveIndex++) {
      final waveProgress = (progress + waveIndex * 0.4) % 1.0;
      final baseRadius = 25.0 + (waveIndex * 30.0);
      
      // Much more subtle amplitude effect
      final amplitudeMultiplier = 0.85 + (amplitude * 0.3); // Reduced from 0.6
      final beatMultiplier = 1.0 + (beatProgress * 0.15 * beatIntensity); // Reduced from 0.3
      final currentRadius = baseRadius * amplitudeMultiplier * beatMultiplier + (waveProgress * 25.0);
      final opacity = (1.0 - waveProgress) * 0.2 * amplitude; // Reduced opacity
      
      glowPaint.color = _getWaveColor().withOpacity(opacity);
      glowPaint.strokeWidth = (6.0 - (waveIndex * 1.0)) * (0.9 + amplitude * 0.2); // Thinner lines
      
      final path = _buildSmoothWavePath(center, currentRadius, rotationAngle);
      canvas.drawPath(path, glowPaint);
    }
  }

  void _drawMainWaves(Canvas canvas, Offset center) {
    final paint = _getMainPaint();
    final rotationAngle = _getRotationAngle(cornerIndex);
    
    // 3 main waves for elegant layering
    for (int waveIndex = 0; waveIndex < 3; waveIndex++) {
      final waveProgress = (progress + waveIndex * 0.35) % 1.0;
      final baseRadius = 20.0 + (waveIndex * 20.0);
      
      // Subtle music reactivity
      final amplitudeEffect = 0.9 + (amplitude * 0.2); // Much more subtle
      final beatEffect = 1.0 + (beatProgress * 0.1 * beatIntensity); // Very subtle
      final currentRadius = baseRadius * amplitudeEffect * beatEffect + (waveProgress * 30.0);
      
      // Smooth opacity transitions
      final baseOpacity = 0.7 - waveIndex * 0.2;
      final opacity = (1.0 - waveProgress) * baseOpacity * (0.7 + amplitude * 0.3);
      
      // Elegant gradient
      final gradient = _createSmoothRadialGradient(center, currentRadius, opacity);
      paint.shader = gradient;
      paint.strokeWidth = (3.0 - (waveIndex * 0.5)) * (0.8 + amplitude * 0.3); // Thinner, more elegant
      
      final path = _buildSmoothWavePath(center, currentRadius, rotationAngle);
      canvas.drawPath(path, paint);
    }
  }

  void _drawAccentWaves(Canvas canvas, Offset center) {
    // Only draw accent waves during strong beats for special moments
    if (beatProgress < 0.3 && beatIntensity > 0.7) {
      final accentPaint = _getAccentPaint();
      final rotationAngle = _getRotationAngle(cornerIndex);
      
      final accentRadius = 15.0 + (beatProgress * 40.0 * beatIntensity);
      final opacity = (0.3 - beatProgress) * 3.0 * beatIntensity * 0.4; // Very subtle
      
      accentPaint.color = _getWaveColor().withOpacity(opacity);
      accentPaint.strokeWidth = 2.0 * beatIntensity;
      
      final path = _buildSmoothWavePath(center, accentRadius, rotationAngle);
      canvas.drawPath(path, accentPaint);
    }
  }

  Paint _getGlowPaint() {
    return _paintCache.putIfAbsent('glow', () => Paint()
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0) // Reduced blur
    );
  }

  Paint _getMainPaint() {
    return _paintCache.putIfAbsent('main', () => Paint()
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 0.8) // Subtle blur
    );
  }

  Paint _getAccentPaint() {
    return _paintCache.putIfAbsent('accent', () => Paint()
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1.5)
    );
  }

  Shader _createSmoothRadialGradient(Offset center, double radius, double opacity) {
    final colors = _getSmoothGradientColors(opacity);
    return RadialGradient(
      center: Alignment.center,
      radius: 0.9,
      colors: colors,
      stops: const [0.0, 0.6, 1.0], // Smoother gradient stops
    ).createShader(Rect.fromCircle(center: center, radius: radius));
  }

  List<Color> _getSmoothGradientColors(double opacity) {
    final baseColor = _getWaveColor();
    final shiftedColor = _getSubtleShiftedColor(baseColor);
    
    return [
      baseColor.withOpacity(opacity),
      shiftedColor.withOpacity(opacity * 0.6),
      baseColor.withOpacity(0.0),
    ];
  }

  Color _getSubtleShiftedColor(Color baseColor) {
    // Much more subtle hue shifting
    final hsl = HSLColor.fromColor(baseColor);
    final hueShift = (colorProgress * 30.0) + (beatProgress * 10.0 * beatIntensity); // Reduced
    final shiftedHue = (hsl.hue + hueShift) % 360.0;
    return hsl.withHue(shiftedHue).toColor();
  }

  Path _buildSmoothWavePath(Offset center, double radius, double rotationAngle) {
    final path = Path();
    final angles = _getCachedAngles(120);
    
    if (angles.isEmpty) return path;
    
    // Start the path
    final startAngle = angles[0] + rotationAngle;
    final startX = center.dx + math.cos(startAngle) * radius;
    final startY = center.dy + math.sin(startAngle) * radius;
    path.moveTo(startX, startY);
    
    // Create very smooth curves with minimal variation
    for (int i = 1; i < angles.length - 1; i += 2) {
      final angle1 = angles[i] + rotationAngle;
      final angle2 = angles[math.min(i + 1, angles.length - 1)] + rotationAngle;
      
      // Minimal radius variation for smooth, elegant curves
      final radiusVariation = radius * (1.0 + (amplitude - 0.6) * 0.05 * math.sin(i.toDouble())); // Much smaller variation
      
      final x1 = center.dx + math.cos(angle1) * radiusVariation;
      final y1 = center.dy + math.sin(angle1) * radiusVariation;
      final x2 = center.dx + math.cos(angle2) * radius;
      final y2 = center.dy + math.sin(angle2) * radius;
      
      path.quadraticBezierTo(x1, y1, x2, y2);
    }
    
    return path;
  }

  // Cache rotation angles to avoid repeated calculations
  double _getRotationAngle(int cornerIndex) {
    return _rotationCache.putIfAbsent(cornerIndex, () {
      switch (cornerIndex) {
        case 0: return -3 * math.pi / 4; // Top-left
        case 1: return 3 * math.pi / 4 + math.pi; // Top-right
        case 2: return math.pi; // Bottom-left
        case 3: return -3 * math.pi / 2; // Bottom-right
        default: return 0.0;
      }
    });
  }

  List<double> _getCachedAngles(int arcDegrees) {
    return _angleCache.putIfAbsent(arcDegrees, () {
      return List.generate(31, (i) => (i * 4) * math.pi / 180);
    });
  }

  Color _getWaveColor() {
    if (isDayMode) {
      // Elegant day colors with subtle transitions
      final t = (math.sin(colorProgress * math.pi * 2) + 1) / 2;
      final beatColorShift = beatProgress * 0.1; // Much more subtle
      return Color.lerp(
        Color.lerp(const Color(0xFF00B4FF), const Color(0xFF40C4FF), beatColorShift)!, // Softer blue
        Color.lerp(const Color(0xFF00E5FF), const Color(0xFF80DEEA), beatColorShift)!, // Softer cyan
        t,
      )!;
    } else {
      // Elegant night colors with subtle transitions
      final t = (math.sin(colorProgress * math.pi * 2) + 1) / 2;
      final beatColorShift = beatProgress * 0.1; // Much more subtle
      return Color.lerp(
        Color.lerp(const Color(0xFF9C27B0), const Color(0xFFAB47BC), beatColorShift)!, // Softer purple
        Color.lerp(const Color(0xFFE91E63), const Color(0xFFF06292), beatColorShift)!, // Softer magenta
        t,
      )!;
    }
  }

  @override
  bool shouldRepaint(SmoothSoundWavePainter oldDelegate) {
    return oldDelegate.progress != progress || 
           oldDelegate.colorProgress != colorProgress ||
           oldDelegate.beatProgress != beatProgress ||
           oldDelegate.amplitude != amplitude ||
           oldDelegate.beatIntensity != beatIntensity ||
           oldDelegate.isDayMode != isDayMode;
  }
} 