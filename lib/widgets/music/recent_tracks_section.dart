import 'package:flutter/material.dart';
import '../../models/music/track.dart';
import 'recent_track_card.dart';

class RecentTracksSection extends StatelessWidget {
  final bool isSmallScreen;
  final List<Track> recentTracks;

  const RecentTracksSection({
    Key? key,
    required this.isSmallScreen,
    required this.recentTracks,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final titleHeight = isSmallScreen ? 16.0 : 18.0;
    final spacing = 4.0;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        SizedBox(
          height: titleHeight,
          child: Row(
            children: [
              Icon(
                Icons.history_rounded,
                size: isSmallScreen ? 14 : 16,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
              const SizedBox(width: 4),
              Text(
                'Recently Played',
                style: TextStyle(
                  fontSize: isSmallScreen ? 12 : 13,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: spacing),
        // Recent tracks list
        SizedBox(
          height: isSmallScreen ? 56 : 64,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: recentTracks.length.clamp(0, 5),
            itemBuilder: (context, index) {
              return Padding(
                padding: EdgeInsets.only(right: index < 4 ? 8 : 0),
                child: RecentTrackCard(
                  track: recentTracks[index],
                  isSmallScreen: isSmallScreen,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
} 