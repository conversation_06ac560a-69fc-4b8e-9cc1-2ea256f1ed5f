import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/spotify_provider.dart';
import '../../theme/app_theme.dart';
import '../../models/music/track.dart';

class RecentTrackCard extends StatelessWidget {
  final Track track;
  final bool isSmallScreen;

  const RecentTrackCard({
    Key? key,
    required this.track,
    required this.isSmallScreen,
  }) : super(key: key);

  Widget _buildAlbumArt(BuildContext context) {
    return Container(
      width: isSmallScreen ? 48 : 56,
      height: isSmallScreen ? 48 : 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).colorScheme.surfaceVariant,
      ),
      child: Icon(
        Icons.album_rounded,
        size: isSmallScreen ? 24 : 28,
        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final spotifyProvider = Provider.of<SpotifyProvider>(context);
    final isPlaying = spotifyProvider.currentTrack?.id == track.id && 
                     spotifyProvider.isPlaying;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (isPlaying) {
            spotifyProvider.pause();
          } else {
            spotifyProvider.playTrack(track.uri);
          }
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: isSmallScreen ? 48 : 56,
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  _buildAlbumArt(context),
                  if (isPlaying)
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: AppTheme.primaryColor.withOpacity(0.1),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.pause_rounded,
                            size: isSmallScreen ? 20 : 24,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
} 