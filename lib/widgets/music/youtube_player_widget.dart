import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import '../../services/music/youtube_player_service.dart';
import 'package:provider/provider.dart';
import '../../providers/youtube_provider.dart';
import '../../widgets/bottomsheets/collection_selector_bottomsheet.dart';
import '../../utils/event_bus.dart';
import '../../models/music_track.dart';

class YouTubePlayerWidget extends StatefulWidget {
  final YouTubePlayerService service;
  final double size;
  final bool enablePiP;
  final VoidCallback? onPiPEnter;
  final VoidCallback? onPiPExit;
  final BuildContext? navigatorContext;
  final Function(MusicTrack)? onAddToCollection;

  const YouTubePlayerWidget({
    super.key,
    required this.service,
    this.size = 200.0,
    this.enablePiP = false,
    this.onPiPEnter,
    this.onPiPExit,
    this.navigatorContext,
    this.onAddToCollection,
  });

  @override
  _YouTubePlayerWidgetState createState() => _YouTubePlayerWidgetState();
}

class _YouTubePlayerWidgetState extends State<YouTubePlayerWidget> {
  Offset _position = Offset.zero;
  bool _isDragging = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<YouTubeProvider>(
      builder: (context, youtubeProvider, child) {
        final isProcessing = youtubeProvider.isProcessingRequest;
        final isPlaying = youtubeProvider.isPlaying;
        final currentTrack = youtubeProvider.currentTrack;

        final player = Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: widget.service.controller != null
                ? YoutubePlayer(
                    aspectRatio: 1/1,
                    controller: widget.service.controller!,
                    gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{},
                  )
                : Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2,
                      ),
                    ),
                  ),
            ),
            
            // Loading overlay when processing request
            if (isProcessing)
              Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2,
                      ),
                      if (currentTrack != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          currentTrack.title,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            
            // Close button in top-left corner
            Positioned(
              top: 4,
              left: 4,
              child: GestureDetector(
                onTap: isProcessing ? null : () => _closePlayer(context),
                child: Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: isProcessing 
                        ? Colors.black.withOpacity(0.4)
                        : Colors.black.withOpacity(0.7),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isProcessing 
                          ? Colors.white.withOpacity(0.2)
                          : Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.close,
                    color: isProcessing 
                        ? Colors.white.withOpacity(0.5)
                        : Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),

            // Add to Collection button in top-right corner
            Positioned(
              top: 4,
              right: 4,
              child: GestureDetector(
                onTap: isProcessing ? null : () {
                  if (currentTrack != null) {
                    if (widget.onAddToCollection != null) {
                      // Use the callback - parent screen will handle the modal
                      widget.onAddToCollection!(currentTrack);
                    } else {
                      // Fallback: try to show modal in current context
                      try {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor: Colors.transparent,
                          builder: (context) => CollectionSelectorBottomSheet(track: currentTrack),
                        );
                      } catch (e) {
                        if (kDebugMode) {
                          print('❌ Error showing collection bottom sheet: $e');
                        }
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Added "${currentTrack.title}" to collection'),
                            duration: const Duration(seconds: 2),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    }
                  }
                },
                child: Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: isProcessing 
                        ? Colors.black.withOpacity(0.4)
                        : Colors.black.withOpacity(0.7),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isProcessing 
                          ? Colors.white.withOpacity(0.2)
                          : Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.playlist_add,
                    color: isProcessing 
                        ? Colors.white.withOpacity(0.5)
                        : Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),

            // Previous button (bottom left)
            if (!isProcessing && currentTrack != null)
              Positioned(
                bottom: 4,
                left: 4,
                child: GestureDetector(
                  onTap: () => _skipToPrevious(context),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.skip_previous,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
              ),

            // Next button (bottom right)
            if (!isProcessing && currentTrack != null)
              Positioned(
                bottom: 4,
                right: 4,
                child: GestureDetector(
                  onTap: () => _skipToNext(context),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.skip_next,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
              ),
          ],
        );

        if (!widget.enablePiP) {
          return player;
        }

        return Positioned(
          left: _position.dx,
          top: _position.dy,
          child: GestureDetector(
            onPanStart: isProcessing ? null : (details) {
              setState(() {
                _isDragging = true;
              });
            },
            onPanUpdate: isProcessing ? null : (details) {
              setState(() {
                _position += details.delta;
              });
            },
            onPanEnd: isProcessing ? null : (details) {
              setState(() {
                _isDragging = false;
              });
            },
            child: AnimatedContainer(
              duration: Duration(milliseconds: _isDragging ? 0 : 200),
              transform: Matrix4.identity()..scale(_isDragging ? 1.05 : 1.0),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(_isDragging ? 0.3 : 0.2),
                      blurRadius: _isDragging ? 12 : 8,
                      offset: Offset(0, _isDragging ? 6 : 4),
                    ),
                  ],
                ),
                child: PiPWidget(
                  child: player,
                  onPiPClose: isProcessing ? () {} : () {
                    widget.onPiPExit?.call();
                  },
                  pipBorderRadius: 8,
                  elevation: 4,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _skipToPrevious(BuildContext context) {
    try {
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
      youtubeProvider.playPrevious();
      
      if (kDebugMode) {
        print('🎵 Skipping to previous track');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error skipping to previous: $e');
      }
    }
  }

  void _skipToNext(BuildContext context) {
    try {
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
      youtubeProvider.playNext();
      
      if (kDebugMode) {
        print('🎵 Skipping to next track');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error skipping to next: $e');
      }
    }
  }

  void _closePlayer(BuildContext context) {
    try {
      // Get the YouTube provider and stop playback
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
      
      // Don't allow closing if currently processing
      if (youtubeProvider.isProcessingRequest) {
        if (kDebugMode) {
          print('🎬 [YouTubePlayerWidget] Cannot close player while processing request');
        }
        return;
      }
      
      youtubeProvider.stop();
      
      // Close PiP if it's active
      if (widget.enablePiP) {
        widget.onPiPExit?.call();
      }
      
      if (kDebugMode) {
        print('🎬 [YouTubePlayerWidget] Player closed by user');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubePlayerWidget] Error closing player: $e');
      }
    }
  }
} 