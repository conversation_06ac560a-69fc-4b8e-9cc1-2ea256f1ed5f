import 'package:flutter/material.dart';
import 'package:flutter/services.dart';  // Add this import for HapticFeedback
import 'package:provider/provider.dart';
import 'dart:async';  // Add this import for Timer and Completer
import '../../config/themes.dart';
import '../common/shimmer_loading.dart';
import 'track_card.dart';
import 'suggested_songs_section.dart';
import 'now_playing_bar.dart';
import 'apple_now_playing_bar.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/user_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/now_playing_provider.dart';
import '../../screens/search/ai_search/ai_search_provider.dart';
import '../../services/ai/global_ai_provider_service.dart';
import 'dart:math' as math;
import '../../models/music_track.dart';
import '../../models/music/track.dart';  // Add Track model import
import '../../services/music/spotify_service.dart';
import 'dart:ui';  // Add this import for ImageFilter
import 'package:flutter/rendering.dart';

class TrackSelector extends StatefulWidget {
  final Function(MusicTrack) onTrackSelected;
  final bool showSearchBar;
  final bool showRecentlyPlayed;
  final bool showTopTracks;
  final bool showLikedSongs;
  final bool showPlaylists;
  final VoidCallback? onSuggestedSongsRefreshNeeded;

  const TrackSelector({
    Key? key,
    required this.onTrackSelected,
    this.showSearchBar = true,
    this.showRecentlyPlayed = true,
    this.showTopTracks = true,
    this.showLikedSongs = true,
    this.showPlaylists = true,
    this.onSuggestedSongsRefreshNeeded,
  }) : super(key: key);

  @override
  State<TrackSelector> createState() => _TrackSelectorState();
}

class _TrackSelectorState extends State<TrackSelector> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  
  // Spotify API service
  final SpotifyService _spotifyService = SpotifyService();
  late SpotifyProvider _spotifyProvider;  // Add SpotifyProvider field
  
  // Data
  List<MusicTrack> _recentlyPlayed = [];
  List<MusicTrack> _topTracks = [];
  List<MusicTrack> _likedSongs = [];
  List<MusicTrack> _searchResults = [];
  List<MusicTrack> _suggestedSongs = [];
  List<Map<String, dynamic>> _playlists = [];
  Map<String, List<MusicTrack>> _playlistTracks = {};

  // Error handling
  String? _errorMessage;

  // Loading states
  bool _isLoadingRecent = false;
  bool _isLoadingTop = false;
  bool _isLoadingSearch = false;
  bool _isLoadingLiked = false;
  bool _isLoadingPlaylists = false;
  bool _isLoadingSuggested = false;

  // Search debouncing
  Timer? _searchDebounceTimer;
  Completer<void>? _currentSearchCompleter;
  
  // Track which tabs are enabled
  List<String> get _enabledTabs {
    final tabs = <String>[];
    // Always show search when search bar is enabled
    if (widget.showSearchBar) tabs.add('search');
    if (widget.showLikedSongs) tabs.add('liked');
    if (widget.showRecentlyPlayed) tabs.add('recent');
    if (widget.showTopTracks) tabs.add('top');
    if (widget.showPlaylists) tabs.add('playlists');
    return tabs;
  }
  
  @override
  void initState() {
    super.initState();
    _initTabController();
    _spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

    // Use a post-frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
      _setupAIProviderListener();
    });
  }

  void _setupAIProviderListener() async {
    final globalAIProvider = GlobalAIProviderService.instance;
    final aiProvider = await globalAIProvider.getProvider(context);
    if (aiProvider != null) {
      aiProvider.addListener(_onAIProviderChanged);
    }
  }

  void _onAIProviderChanged() {
    // Only refresh suggested songs when AI provider data changes
    // Add a small delay to prevent excessive rebuilds
    if (mounted) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _loadSuggestedSongs();
        }
      });
    }
  }
  
  void _initTabController() {
    final tabCount = _enabledTabs.length;
    _tabController = TabController(
      length: tabCount > 0 ? tabCount : 1,
      vsync: this,
    );
  }
  
  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    
    try {
      // Check if we can load data from Spotify Provider first
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // Only load Spotify-specific data if connected
      if (spotifyProvider.isConnected) {
        // Load liked songs if available
        if (widget.showLikedSongs && spotifyProvider.likedSongs.isNotEmpty) {
          setState(() {
            _likedSongs = spotifyProvider.likedSongs;
          });
        } else if (widget.showLikedSongs) {
          _loadLikedSongs();
        }
        
        // Load from API if needed
        if (widget.showRecentlyPlayed) {
          _loadRecentlyPlayed();
        }
        
        if (widget.showTopTracks) {
          _loadTopTracks();
        }

        if (widget.showPlaylists) {
          _loadPlaylists();
        }
      } else {
        print('🎵 [TrackSelector] Spotify not connected - only search functionality available');
        setState(() {
          _isLoading = false;
        });
      }

      // Load suggested songs based on user's artists (works even without Spotify connection)
      _loadSuggestedSongs();
    } catch (e) {
      print('⚠️ [TrackSelector] Error loading initial data: $e');
      setState(() {
        _errorMessage = null; // Don't show error if just Spotify features failed
        _isLoading = false;
      });
    }
  }
  
  Future<void> _loadRecentlyPlayed() async {
    if (_isLoadingRecent) return;
    
    setState(() {
      _isLoadingRecent = true;
    });
    
    try {
      // Use SpotifyProvider instead of SpotifyService for better authentication handling
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // Check if provider is connected first
      if (!spotifyProvider.isConnected) {
        final connected = await spotifyProvider.connect();
        if (!connected) {
          // Fail silently for recently played if not connected
          setState(() {
            _recentlyPlayed = [];
            _isLoadingRecent = false;
            _isLoading = false;
          });
          return;
        }
      }
      
      final tracks = await spotifyProvider.getRecentlyPlayed(limit: 50);
      
      setState(() {
        _recentlyPlayed = tracks;
        _isLoadingRecent = false;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading recently played: $e');
      setState(() {
        _recentlyPlayed = [];
        _isLoadingRecent = false;
        _isLoading = false;
      });
    }
  }
  
  Future<void> _loadTopTracks() async {
    if (_isLoadingTop) return;
    
    setState(() {
      _isLoadingTop = true;
    });
    
    try {
      // Use SpotifyProvider instead of SpotifyService for better authentication handling
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // Check if provider is connected first
      if (!spotifyProvider.isConnected) {
        final connected = await spotifyProvider.connect();
        if (!connected) {
          // Fail silently for top tracks if not connected
          setState(() {
            _topTracks = [];
            _isLoadingTop = false;
            _isLoading = false;
          });
          return;
        }
      }
      
      final tracks = await spotifyProvider.getTopTracks(timeRange: 'medium_term', limit: 50);
      
      setState(() {
        _topTracks = tracks;
        _isLoadingTop = false;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading top tracks: $e');
      setState(() {
        _topTracks = [];
        _isLoadingTop = false;
        _isLoading = false;
      });
    }
  }
  
  Future<void> _loadLikedSongs() async {
    if (_isLoadingLiked) return;
    
    setState(() {
      _isLoadingLiked = true;
    });
    
    try {
      // Use SpotifyProvider instead of SpotifyService for better authentication handling
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // Check if provider is connected first
      if (!spotifyProvider.isConnected) {
        final connected = await spotifyProvider.connect();
        if (!connected) {
          // Fail silently for liked songs if not connected
          setState(() {
            _likedSongs = [];
            _isLoadingLiked = false;
            _isLoading = false;
          });
          return;
        }
      }
      
      // Load liked songs using the provider
      await spotifyProvider.loadLikedSongs(refresh: true);
      
      setState(() {
        _likedSongs = spotifyProvider.likedSongs;
        _isLoadingLiked = false;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading liked songs: $e');
      setState(() {
        _likedSongs = [];
        _isLoadingLiked = false;
        _isLoading = false;
      });
    }
  }
  
  Future<void> _loadMoreLikedSongs() async {
    if (_isLoadingLiked) return;

    setState(() {
      _isLoadingLiked = true;
    });

    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

      // Check if we have more songs to load
      if (!spotifyProvider.hasMoreLikedSongs) {
        setState(() => _isLoadingLiked = false);
        return;
      }

      await spotifyProvider.loadMoreLikedSongs();

      setState(() {
        _likedSongs = spotifyProvider.likedSongs;
        _isLoadingLiked = false;
      });
    } catch (e) {
      print('Error loading more liked songs: $e');
      setState(() => _isLoadingLiked = false);
    }
  }

  /// Public method to refresh suggested songs (can be called from outside)
  void refreshSuggestedSongs() {
    _loadSuggestedSongs();
  }

  Future<void> _loadSuggestedSongs() async {
    if (_isLoadingSuggested) return;

    setState(() {
      _isLoadingSuggested = true;
    });

    try {
      // Use the getProvider method which will automatically initialize if needed
      final globalAIProvider = GlobalAIProviderService.instance;
      final aiProvider = await globalAIProvider.getProvider(context);
      
      if (aiProvider != null) {
        final cachedArtistTracks = aiProvider.categorizedRecommendations['artistBased'];

        if (cachedArtistTracks != null && cachedArtistTracks.isNotEmpty) {
          print('✅ [TrackSelector] Using ${cachedArtistTracks.length} cached artist-based tracks from global AI provider');

          // Enhanced randomization for variety
          final allTracks = List<MusicTrack>.from(cachedArtistTracks);
          final selectedTracks = <MusicTrack>[];
          final random = math.Random();
          
          // Use weighted random selection for better variety
          // Group tracks by artist to ensure variety
          final tracksByArtist = <String, List<MusicTrack>>{};
          for (final track in allTracks) {
            final artist = track.artist;
            tracksByArtist[artist] = tracksByArtist[artist] ?? [];
            tracksByArtist[artist]!.add(track);
          }
          
          // Select tracks with artist diversity (max 3 per artist)
          final artistKeys = tracksByArtist.keys.toList()..shuffle(random);
          final maxPerArtist = 3;
          
          for (final artist in artistKeys) {
            final artistTracks = tracksByArtist[artist]!..shuffle(random);
            final takeCount = math.min(maxPerArtist, artistTracks.length);
            selectedTracks.addAll(artistTracks.take(takeCount));
            
            if (selectedTracks.length >= 20) break;
          }
          
          // If we still need more tracks, add remaining randomly
          if (selectedTracks.length < 20) {
            final remainingTracks = allTracks.where((track) => !selectedTracks.contains(track)).toList();
            remainingTracks.shuffle(random);
            selectedTracks.addAll(remainingTracks.take(20 - selectedTracks.length));
          }
          
          // Final shuffle for randomness
          selectedTracks.shuffle(random);

          setState(() {
            _suggestedSongs = selectedTracks.take(20).toList();
            _isLoadingSuggested = false;
          });

          print('✅ [TrackSelector] Loaded ${_suggestedSongs.length} suggested songs from global cache with enhanced variety');
          return;
        }
      }
      
      // Final fallback to original logic if no cached tracks available
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final user = userProvider.currentUser;

      // Get user's top artists
      final topArtists = user?.topArtists;

      if (topArtists != null && topArtists.isNotEmpty) {
        print('🎵 [TrackSelector] No cached tracks available, loading suggested songs for artists: ${topArtists.take(3).join(', ')}');

        // Use Spotify service to get tracks inspired by user's artists
        final tracks = await _spotifyService.searchTracksInspiredByArtists(
          topArtists.take(5).toList(), // Use top 5 artists
          limit: 20,
        );

        setState(() {
          _suggestedSongs = tracks;
          _isLoadingSuggested = false;
        });

        print('✅ [TrackSelector] Loaded ${tracks.length} suggested songs from API');
      } else {
        print('🎵 [TrackSelector] No top artists found, skipping suggested songs');
        setState(() {
          _suggestedSongs = [];
          _isLoadingSuggested = false;
        });
      }
    } catch (e) {
      print('❌ [TrackSelector] Error loading suggested songs: $e');
      setState(() {
        _suggestedSongs = [];
        _isLoadingSuggested = false;
      });
    }
  }
  
  void _onSearch(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();

      // Clear results if query is empty
      if (_searchQuery.isEmpty) {
        _searchResults = [];
        _isLoadingSearch = false;
        return;
      }
    });

    // Don't search for very short queries
    if (query.length < 2) return;

    // Cancel any existing search timer
    _searchDebounceTimer?.cancel();

    // Cancel any ongoing search request
    if (_currentSearchCompleter != null && !_currentSearchCompleter!.isCompleted) {
      _currentSearchCompleter!.complete();
    }

    // Set loading state immediately for better UX
    setState(() {
      _isLoadingSearch = true;
    });

    // Start new debounced search
    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _performSearch(query);
    });
  }

  Future<void> _performSearch(String query) async {
    // Create new completer for this search
    _currentSearchCompleter = Completer<void>();

    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

      // First try the provider if connected for better caching
      if (spotifyProvider.isConnected) {
        try {
          final results = await spotifyProvider.searchTracks(query);

          // Check if this search was cancelled
          if (_currentSearchCompleter!.isCompleted) return;

          setState(() {
            _searchResults = results;
            _isLoadingSearch = false;
            _errorMessage = null;
          });
          _currentSearchCompleter!.complete();
          return;
        } catch (e) {
          print('Provider search failed, falling back to service: $e');
          // Check if this search was cancelled before continuing
          if (_currentSearchCompleter!.isCompleted) return;
        }
      }

      // Always try direct service call (uses backend client credentials when not connected)
      final results = await _spotifyService.searchTracks(query, context: 'TRACK_SELECTOR');

      // Check if this search was cancelled
      if (_currentSearchCompleter!.isCompleted) return;

      setState(() {
        _searchResults = results;
        _isLoadingSearch = false;
        _errorMessage = null;
      });
      _currentSearchCompleter!.complete();
    } catch (e) {
      print('Error searching for tracks: $e');

      // Check if this search was cancelled
      if (_currentSearchCompleter!.isCompleted) return;

      setState(() {
        _errorMessage = 'Error searching for tracks. Please try again.';
        _isLoadingSearch = false;
        _searchResults = [];
      });
      _currentSearchCompleter!.complete();
    }
  }

  Future<void> _playTrack(MusicTrack track) async {
    try {
      await _spotifyProvider.playTrack(track);
    } catch (e) {
      print('Error playing track: $e');
    }
  }

  Future<void> _loadPlaylists() async {
    if (_isLoadingPlaylists) return;
    
    setState(() {
      _isLoadingPlaylists = true;
    });
    
    try {
      // Use SpotifyProvider instead of SpotifyService for better authentication handling
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // Check if provider is connected first
      if (!spotifyProvider.isConnected) {
        final connected = await spotifyProvider.connect();
        if (!connected) {
          // Fail silently for playlists if not connected
          setState(() {
            _playlists = [];
            _isLoadingPlaylists = false;
            _isLoading = false;
          });
          return;
        }
      }
      
      final playlists = await spotifyProvider.getUserPlaylistsFromApi();
      
      setState(() {
        _playlists = playlists;
        _isLoadingPlaylists = false;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading playlists: $e');
      setState(() {
        _playlists = [];
        _isLoadingPlaylists = false;
        _isLoading = false;
      });
    }
  }

  Future<void> _loadPlaylistTracks(String playlistId) async {
    try {
      // Use SpotifyProvider instead of SpotifyService for better authentication handling
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      // Check if provider is connected first
      if (!spotifyProvider.isConnected) {
        final connected = await spotifyProvider.connect();
        if (!connected) {
          throw Exception('Failed to connect to Spotify');
        }
      }
      
      final tracks = await spotifyProvider.getPlaylistTracks(playlistId);
      if (mounted) {
        setState(() {
          _playlistTracks[playlistId] = tracks;
        });
      }
    } catch (e) {
      print('Error loading playlist tracks: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load playlist tracks: ${e.toString()}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Make background color match the parent screen
    final backgroundColor = Theme.of(context).colorScheme.surface;

    return Consumer3<SpotifyProvider, AppleMusicProvider, NowPlayingProvider>(
      builder: (context, spotifyProvider, appleMusicProvider, nowPlayingProvider, child) {
        // Check if there's active playback for now playing bar
        final hasNowPlaying = spotifyProvider.hasActivePlayback ||
                              (appleMusicProvider.isPlaying && appleMusicProvider.currentTrack != null);

        return Stack(
          children: [
            Container(
              color: backgroundColor,
              child: Column(
                children: [
          // Error message - Modernized design
          if (_errorMessage != null)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.red.shade200),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.shade100,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.error_outline, color: Colors.red[700], size: 18),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Loading Error',
                          style: TextStyle(
                            color: Colors.red[700], 
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _errorMessage!,
                          style: TextStyle(color: Colors.red[700], fontSize: 12),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.refresh, size: 18, color: Colors.red[700]),
                    onPressed: () {
                      // Attempt to reload the data
                      _loadInitialData();
                      // Clear the error
                      setState(() {
                        _errorMessage = null;
                      });
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: Icon(Icons.close, size: 18, color: Colors.red[700]),
                    onPressed: () {
                      setState(() {
                        _errorMessage = null;
                      });
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          
          // Search bar
          if (widget.showSearchBar)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withOpacity(0.05)
                          : Colors.black.withOpacity(0.03),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.05),
                        width: 1,
                      ),
                    ),
                    child: TextField(
                      controller: _searchController,
                      style: TextStyle(
                        fontSize: 15,
                        letterSpacing: 0.3,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withOpacity(0.9)
                            : Colors.black.withOpacity(0.9),
                      ),
                      decoration: InputDecoration(
                        hintText: 'Search songs, artists, or albums',
                        hintStyle: TextStyle(
                          fontSize: 15,
                          letterSpacing: 0.3,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withOpacity(0.5)
                              : Colors.black.withOpacity(0.5),
                        ),
                        prefixIcon: Icon(
                          Icons.search_rounded,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withOpacity(0.6)
                              : Colors.black.withOpacity(0.6),
                          size: 22,
                        ),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? _isLoadingSearch
                                ? Padding(
                                    padding: const EdgeInsets.all(14),
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? Colors.white.withOpacity(0.6)
                                          : Colors.black.withOpacity(0.6),
                                    ),
                                  )
                                : IconButton(
                                    icon: Icon(
                                      Icons.close_rounded,
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? Colors.white.withOpacity(0.6)
                                          : Colors.black.withOpacity(0.6),
                                      size: 20,
                                    ),
                                    onPressed: () {
                                      _searchController.clear();
                                      _onSearch('');
                                    },
                                  )
                            : null,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                      ),
                      onChanged: _onSearch,
                      cursorColor: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withOpacity(0.7)
                          : Colors.black.withOpacity(0.7),
                      cursorWidth: 1.5,
                      cursorRadius: const Radius.circular(2),
                    ),
                  ),
                ),
              ),
            ),
            
          // Show search results if there's a query
          if (_searchQuery.isNotEmpty)
            Expanded(
              child: _buildSearchResults(),
            )
          else
            Expanded(
              child: Column(
                children: [
                  // Suggested songs section - show when not searching and user has artists
                  if (_suggestedSongs.isNotEmpty || _isLoadingSuggested)
                    SuggestedSongsSection(
                      suggestedSongs: _suggestedSongs,
                      isLoading: _isLoadingSuggested,
                      onTrackSelected: widget.onTrackSelected,
                      onTrackPlay: _playTrack, // Add play functionality
                    ),

                  // Tab bar - only show if we have multiple tabs
                  if (_tabController.length > 1)
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                      child: Container(
                        height: 40,
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.black12
                              : Colors.black.withOpacity(0.03),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: TabBar(
                          controller: _tabController,
                          padding: EdgeInsets.zero,
                          labelPadding: const EdgeInsets.symmetric(horizontal: 6),
                          indicator: BoxDecoration(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withOpacity(0.1)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          dividerColor: Colors.transparent,
                          indicatorSize: TabBarIndicatorSize.tab,
                          labelColor: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                          unselectedLabelColor: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white60
                              : Colors.black54,
                          labelStyle: const TextStyle(
                            fontSize: 11.5,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.1,
                          ),
                          unselectedLabelStyle: const TextStyle(
                            fontSize: 11.5,
                            fontWeight: FontWeight.w500,
                          ),
                          isScrollable: false,
                          tabs: _buildTabs(),
                        ),
                      ),
                    ),
                  
                  // Tab content
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: _buildTabViews(),
                    ),
                  ),
                ],
              ),
            ),
                ],
              ),
            ),

            // Now Playing bar - show appropriate bar based on active service
            if (hasNowPlaying)
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Card(
                  margin: const EdgeInsets.all(16),
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(16)),
                  ),
                  elevation: 8,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: _buildNowPlayingBar(spotifyProvider, appleMusicProvider),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// Build the appropriate now playing bar based on which service is currently active
  Widget _buildNowPlayingBar(SpotifyProvider spotifyProvider, AppleMusicProvider appleMusicProvider) {
    // Determine which service is currently playing
    final spotifyActive = spotifyProvider.hasActivePlayback && spotifyProvider.currentTrack != null;
    final appleActive = appleMusicProvider.isPlaying && appleMusicProvider.currentTrack != null;

    // Show Apple Music bar if Apple Music is playing
    if (appleActive && !spotifyActive) {
      return AppleNowPlayingBar(
        bottomPadding: MediaQuery.of(context).padding.bottom,
        showProgressBar: true,
        onTap: () {
          // Handle tap to expand Apple Music player
        },
        onDismiss: () {
          // Handle dismiss
        },
      );
    }

    // Show Spotify bar by default (or if Spotify is playing)
    return NowPlayingBar(
      bottomPadding: MediaQuery.of(context).padding.bottom,
      showProgressBar: true,
      onTap: () {
        // Handle tap to expand Spotify player
      },
      onDismiss: () {
        // Handle dismiss
      },
    );
  }
  
  List<Widget> _buildTabs() {
    final tabs = <Widget>[];
    
    // Add search tab if search bar is enabled and it's in _enabledTabs
    if (widget.showSearchBar && _enabledTabs.contains('search')) {
      tabs.add(
        const Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.search_rounded, size: 13),
              SizedBox(width: 3),
              Text('Search'),
            ],
          ),
        ),
      );
    }
    
    if (widget.showLikedSongs) {
      tabs.add(
        const Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.favorite_rounded, size: 13),
              SizedBox(width: 3),
              Text('Liked'),
            ],
          ),
        ),
      );
    }
    
    if (widget.showRecentlyPlayed) {
      tabs.add(
        const Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.history_rounded, size: 13),
              SizedBox(width: 3),
              Text('Recent'),
            ],
          ),
        ),
      );
    }
    
    if (widget.showTopTracks) {
      tabs.add(
        const Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.star_rounded, size: 13),
              SizedBox(width: 3),
              Text('Top'),
            ],
          ),
        ),
      );
    }
    
    if (widget.showPlaylists) {
      tabs.add(
        Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.playlist_play_rounded, size: 12),
              const SizedBox(width: 2),
              Flexible(
                child: Text(
                  'Playlists',
                  style: const TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return tabs;
  }

  List<Widget> _buildTabViews() {
    return _enabledTabs.map((tab) {
      switch (tab) {
        case 'search':
          return _buildSearchTabView();
        case 'liked':
          return _buildLikedSongsList();
        case 'recent':
          return _buildTrackList(_recentlyPlayed, isLoading: _isLoadingRecent);
        case 'top':
          return _buildTrackList(_topTracks, isLoading: _isLoadingTop);
        case 'playlists':
          return _buildPlaylistsList();
        default:
          return const Center(child: Text('Unknown tab'));
      }
    }).toList();
  }
  
  Widget _buildTrackList(List<MusicTrack> tracks, {bool isLoading = false}) {
    if (isLoading) {
      return _buildLoadingShimmer(context);
    }
    
    if (tracks.isEmpty) {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      if (spotifyProvider.isConnected) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      } else {
        // If we're not connected, show reconnect options
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.music_off,
                size: 64,
                color: Colors.grey.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              const Text(
                'Spotify connection may have been lost',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () async {
                  // Try to reconnect
                  final connected = await spotifyProvider.connect();
                  if (connected) {
                    // Refresh data
                    _loadInitialData();
                    
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Successfully reconnected to Spotify'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Failed to reconnect to Spotify'),
                        backgroundColor: Colors.red,
                        duration: Duration(seconds: 3),
                      ),
                    );
                  }
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Reconnect to Spotify'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
              ),
            ],
          ),
        );
      }
    }
    
    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: tracks.length,
      itemBuilder: (context, index) {
        final track = tracks[index];
        return TrackCard(
          track: track,
          title: track.title,
          artist: track.artist,
          albumArt: track.albumArt,
          duration: track.formattedDuration,
          onTap: () => widget.onTrackSelected(track),
          isSelected: false,
          showPlayButton: track.previewUrl != null || track.uri.isNotEmpty,
          onPlay: () => _playTrack(track),
        );
      },
    );
  }
  
  Widget _buildLikedSongsList() {
    if (_isLoadingLiked && _likedSongs.isEmpty) {
      return _buildLoadingShimmer(context);
    }
    
    if (_likedSongs.isEmpty) {
      return Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.pink.shade100,
                      Colors.pink.shade200,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.pink.withOpacity(0.2),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.favorite,
                  size: 40,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'No liked songs found',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
              const SizedBox(height: 12),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  'Like songs on Spotify to see them here. Your liked songs will appear automatically.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    height: 1.4,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  OutlinedButton.icon(
                    onPressed: () {
                      // Launch Spotify
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Opening Spotify app...'),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    },
                    icon: const Icon(Icons.open_in_new),
                    label: const Text('Open Spotify'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Reload data
                      _loadLikedSongs();
                      
                      // Show feedback
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Refreshing your liked songs...'),
                          duration: Duration(seconds: 1),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Refresh'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      elevation: 0,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }
    
    // Using a NotificationListener for infinite scrolling
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        // Load more when reaching near the bottom (80% of the way down)
        if (scrollInfo.metrics.pixels > scrollInfo.metrics.maxScrollExtent * 0.8) {
          _loadMoreLikedSongs();
        }
        return true;
      },
      child: ListView.builder(
        padding: const EdgeInsets.only(bottom: 80),
        itemCount: _likedSongs.length + (_isLoadingLiked ? 1 : 0),
        itemBuilder: (context, index) {
          // Show a loading indicator at the end while loading more
          if (index == _likedSongs.length) {
            return const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            );
          }
          
          final track = _likedSongs[index];
          return TrackCard(
            track: track,
            title: track.title,
            artist: track.artist,
            albumArt: track.albumArt,
            duration: track.formattedDuration,
            onTap: () => widget.onTrackSelected(track),
            isSelected: false,
            showPlayButton: track.previewUrl != null || track.uri.isNotEmpty,
            onPlay: () => _playTrack(track),
          );
        },
      ),
    );
  }
  
  Widget _buildSearchTabView() {
    // Show search instructions or results
    if (_searchQuery.isEmpty) {
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24),
        child: Column(
            mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.search_rounded,
              size: 64,
              color: Colors.grey.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            const Text(
              'Search for music',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Type in the search bar above to find tracks',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'You can search for:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildSearchTip(Icons.music_note, 'Song titles'),
                  _buildSearchTip(Icons.person, 'Artist names'),
                  _buildSearchTip(Icons.album, 'Album names'),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ],
          ),
        ),
      );
    } else {
      return _buildSearchResults();
    }
  }

  Widget _buildSearchTip(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isLoadingSearch) {
      return _buildLoadingShimmer(context);
    }
    
    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No results found for "$_searchQuery"',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Try a different search term',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 80),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final track = _searchResults[index];
        return TrackCard(
          track: track,
          title: track.title,
          artist: track.artist,
          albumArt: track.albumArt,
          duration: track.formattedDuration,
          onTap: () => widget.onTrackSelected(track),
          isSelected: false,
          showPlayButton: track.previewUrl != null || track.uri.isNotEmpty,
          onPlay: () => _playTrack(track),
        );
      },
    );
  }
  
  Widget _buildPlaylistsList() {
    if (_isLoadingPlaylists) {
      return _buildLoadingShimmer(context);
    }

    if (_playlists.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.playlist_play_rounded,
              size: 64,
              color: Colors.grey.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            const Text(
              'No playlists found',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            TextButton.icon(
              onPressed: _loadPlaylists,
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 80),
      itemCount: _playlists.length,
      itemBuilder: (context, index) {
        final playlist = _playlists[index];
        final playlistId = playlist['id'] as String;
        final playlistName = playlist['name'] as String;
        final imageUrl = playlist['image_url'] as String?;
        final trackCount = playlist['tracks_count'] as int? ?? 0;

        return ListTile(
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          leading: Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey.withOpacity(0.1),
            ),
            child: imageUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => const Icon(
                        Icons.playlist_play_rounded,
                        size: 32,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : const Icon(
                    Icons.playlist_play_rounded,
                    size: 32,
                    color: Colors.grey,
                  ),
          ),
          title: Text(
            playlistName,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
            ),
          ),
          subtitle: Text(
            '$trackCount tracks',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (context) => DraggableScrollableSheet(
                initialChildSize: 0.7,
                minChildSize: 0.5,
                maxChildSize: 0.95,
                builder: (context, scrollController) => Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey.withOpacity(0.2),
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            if (imageUrl != null)
                              Container(
                                width: 40,
                                height: 40,
                                margin: const EdgeInsets.only(right: 12),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(6),
                                  child: Image.network(
                                    imageUrl,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    playlistName,
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    '$trackCount tracks',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: FutureBuilder<void>(
                          future: _loadPlaylistTracks(playlistId),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState == ConnectionState.waiting) {
                              return _buildLoadingShimmer(context);
                            }

                            final tracks = _playlistTracks[playlistId] ?? [];
                            if (tracks.isEmpty) {
                              return const Center(
                                child: Text('No tracks in this playlist'),
                              );
                            }

                            return ListView.builder(
                              controller: scrollController,
                              padding: const EdgeInsets.only(bottom: 80),
                              itemCount: tracks.length,
                              itemBuilder: (context, index) {
                                final track = tracks[index];
                                return TrackCard(
                                  track: track,
                                  title: track.title,
                                  artist: track.artist,
                                  albumArt: track.albumArt,
                                  duration: track.formattedDuration,
                                  onTap: () {
                                    Navigator.pop(context);
                                    widget.onTrackSelected(track);
                                  },
                                  isSelected: false,
                                  showPlayButton: track.previewUrl != null || track.uri.isNotEmpty,
                                  onPlay: () => _playTrack(track),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
  
  Widget _buildLoadingShimmer(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return ListView.builder(
      itemCount: 5,
      padding: const EdgeInsets.only(top: 8),
      itemBuilder: (context, index) {
        return Card(
          elevation: 1,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            height: 88,
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Album art shimmer
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: isDark ? Colors.grey[800] : Colors.grey[300],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: _buildShimmerEffect(isDark),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Text content shimmer
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        height: 16,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: isDark ? Colors.grey[800] : Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: _buildShimmerEffect(isDark),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 12,
                        width: 120,
                        decoration: BoxDecoration(
                          color: isDark ? Colors.grey[800] : Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: _buildShimmerEffect(isDark),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 10,
                        width: 80,
                        decoration: BoxDecoration(
                          color: isDark ? Colors.grey[800] : Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: _buildShimmerEffect(isDark),
                      ),
                    ],
                  ),
                ),
                
                // Play button shimmer
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: isDark ? Colors.grey[800] : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: _buildShimmerEffect(isDark),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildShimmerEffect(bool isDark) {
    return ShaderMask(
      blendMode: BlendMode.srcATop,
      shaderCallback: (Rect bounds) {
        return LinearGradient(
          colors: [
            isDark ? Colors.grey[800]! : Colors.grey[300]!,
            isDark ? Colors.grey[700]! : Colors.grey[100]!,
            isDark ? Colors.grey[800]! : Colors.grey[300]!,
          ],
          stops: const [0.1, 0.5, 0.9],
          begin: const Alignment(-1.0, -0.5),
          end: const Alignment(1.0, 0.5),
          tileMode: TileMode.clamp,
        ).createShader(bounds);
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: isDark ? Colors.grey[850] : Colors.white,
      ),
    );
  }
  
  @override
  void dispose() {
    // Clean up search debouncing
    _searchDebounceTimer?.cancel();
    if (_currentSearchCompleter != null && !_currentSearchCompleter!.isCompleted) {
      _currentSearchCompleter!.complete();
    }

    // Remove AI provider listener
    final globalAIProvider = GlobalAIProviderService.instance;
    if (globalAIProvider.isInitialized && globalAIProvider.aiProvider != null) {
      globalAIProvider.aiProvider!.removeListener(_onAIProviderChanged);
    }

    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }
} 