import 'package:flutter/material.dart';
import '../../../config/themes.dart';

/// Sleek playback controls for music player
/// Includes shuffle, previous, play/pause, next, and repeat buttons
class PlaybackControls extends StatelessWidget {
  final bool isPlaying;
  final VoidCallback onPlayPause;
  final VoidCallback? onSkipNext;
  final VoidCallback? onSkipPrevious;
  final VoidCallback? onShuffle;
  final VoidCallback? onRepeat;
  final bool isShuffleEnabled;
  final bool isRepeatEnabled;
  
  // Size configuration
  final double playButtonSize;
  final double mainIconSize;
  final double secondaryIconSize;
  final double smallIconSize;

  const PlaybackControls({
    Key? key,
    required this.isPlaying,
    required this.onPlayPause,
    this.onSkipNext,
    this.onSkipPrevious,
    this.onShuffle,
    this.onRepeat,
    this.isShuffleEnabled = false,
    this.isRepeatEnabled = false,
    this.playButtonSize = 56.0,
    this.mainIconSize = 32.0,
    this.secondaryIconSize = 28.0,
    this.smallIconSize = 20.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Shuffle button
        _buildSecondaryButton(
          context,
          icon: Icons.shuffle_rounded,
          onPressed: onShuffle,
          size: smallIconSize,
          isSmallScreen: isSmallScreen,
        ),
        
        // Previous track button
        _buildMainButton(
          context,
          icon: Icons.skip_previous_rounded,
          onPressed: onSkipPrevious,
          size: secondaryIconSize,
          isSmallScreen: isSmallScreen,
        ),
        
        // Play/Pause button
        _buildPlayPauseButton(context, isSmallScreen),
        
        // Next track button
        _buildMainButton(
          context,
          icon: Icons.skip_next_rounded,
          onPressed: onSkipNext,
          size: secondaryIconSize,
          isSmallScreen: isSmallScreen,
        ),
        
        // Repeat button
        _buildSecondaryButton(
          context,
          icon: Icons.repeat_rounded,
          onPressed: onRepeat,
          size: smallIconSize,
          isSmallScreen: isSmallScreen,
        ),
      ],
    );
  }
  
  /// Build a fancy play/pause button with gradient and shadow
  Widget _buildPlayPauseButton(BuildContext context, bool isSmallScreen) {
    return Container(
      width: playButtonSize,
      height: playButtonSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withOpacity(0.8),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            blurRadius: 12,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPlayPause,
          customBorder: const CircleBorder(),
          splashColor: Colors.white24,
          highlightColor: Colors.white10,
          child: Center(
            child: Icon(
              isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
              color: Colors.white,
              size: mainIconSize,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainButton(
    BuildContext context, {
    required IconData icon,
    required VoidCallback? onPressed,
    required double size,
    required bool isSmallScreen,
  }) {
    return Container(
      width: size * 1.5,
      height: size * 1.5,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withOpacity(0.1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          customBorder: const CircleBorder(),
          splashColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          highlightColor: Theme.of(context).colorScheme.primary.withOpacity(0.05),
          child: Center(
            child: Icon(
              icon,
              color: Colors.white.withOpacity(onPressed != null ? 0.9 : 0.3),
              size: size,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSecondaryButton(
    BuildContext context, {
    required IconData icon,
    required VoidCallback? onPressed,
    required double size,
    required bool isSmallScreen,
  }) {
    return SizedBox(
      width: size * 1.8,
      height: size * 1.8,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          customBorder: const CircleBorder(),
          splashColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          highlightColor: Theme.of(context).colorScheme.primary.withOpacity(0.05),
          child: Center(
            child: Icon(
              icon,
              color: Colors.white.withOpacity(onPressed != null ? 0.7 : 0.3),
              size: size,
            ),
          ),
        ),
      ),
    );
  }
} 