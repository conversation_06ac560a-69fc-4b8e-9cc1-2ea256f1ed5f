import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui';

class AnimatedWaveBackground extends StatefulWidget {
  final Color color;
  final double amplitude;
  final double frequency;
  final bool isPlaying;
  final double intensity;

  const AnimatedWaveBackground({
    Key? key,
    required this.color,
    this.amplitude = 12.0,
    this.frequency = 2.0,
    required this.isPlaying,
    this.intensity = 1.0,
  }) : super(key: key);

  @override
  State<AnimatedWaveBackground> createState() => _AnimatedWaveBackgroundState();
}

class _AnimatedWaveBackgroundState extends State<AnimatedWaveBackground> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  final List<double> _randomOffsets = List.generate(3, (index) => math.Random().nextDouble() * 2 * math.pi);
  final List<Color> _particleColors = [];
  final List<Offset> _particlePositions = [];
  final List<double> _particleSizes = [];
  final List<double> _particleSpeeds = [];
  static const int _particleCount = 50;
  double _previousValue = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    
    // Add listener to handle smooth transitions
    _controller.addListener(_handleAnimationTransition);
    
    if (widget.isPlaying) {
      _controller.repeat();
    }
    _initializeParticles();
  }

  void _handleAnimationTransition() {
    // Check for loop point transition
    if (_controller.value < _previousValue) {
      // We've looped, update random offsets smoothly
      for (int i = 0; i < _randomOffsets.length; i++) {
        _randomOffsets[i] = _randomOffsets[i] + 2 * math.pi;
      }
    }
    _previousValue = _controller.value;
  }

  void _initializeParticles() {
    final random = math.Random();
    for (int i = 0; i < _particleCount; i++) {
      _particleColors.add(Colors.white.withOpacity(0.1 + random.nextDouble() * 0.2));
      _particlePositions.add(Offset(
        random.nextDouble(),
        random.nextDouble(),
      ));
      _particleSizes.add(2 + random.nextDouble() * 4);
      _particleSpeeds.add(0.2 + random.nextDouble() * 0.3);
    }
  }

  @override
  void didUpdateWidget(AnimatedWaveBackground oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isPlaying != oldWidget.isPlaying) {
      if (widget.isPlaying) {
        // Smooth restart
        final lastValue = _controller.value;
        _controller.reset();
        _controller.forward(from: lastValue);
        _controller.repeat();
      } else {
        // Smooth stop
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        // Calculate smooth transition values
        final smoothRotation = _controller.value * 2 * math.pi;
        final smoothWavePhase = _controller.value * 2 * math.pi;
        
        return Stack(
          children: [
            // Base gradient background with smooth rotation
            Transform.rotate(
              angle: math.sin(smoothRotation) * 0.05,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(
                      -0.5 + math.sin(smoothWavePhase) * 0.5,
                      -0.5 + math.cos(smoothWavePhase) * 0.5,
                    ),
                    end: Alignment(
                      1.5 + math.cos(smoothWavePhase) * 0.5,
                      1.5 + math.sin(smoothWavePhase) * 0.5,
                    ),
                    colors: [
                      theme.colorScheme.primary.withOpacity(isDark ? 0.8 : 0.7),
                      theme.colorScheme.secondary.withOpacity(isDark ? 0.7 : 0.6),
                      theme.colorScheme.tertiary.withOpacity(isDark ? 0.6 : 0.5),
                    ],
                    stops: const [0.2, 0.5, 0.8],
                  ),
                ),
              ),
            ),
            
            // Animated particles with smooth transitions
            if (widget.isPlaying)
              CustomPaint(
                painter: _ParticlePainter(
                  colors: _particleColors,
                  positions: _particlePositions,
                  sizes: _particleSizes,
                  speeds: _particleSpeeds,
                  time: smoothWavePhase,
                  intensity: widget.intensity,
                ),
                size: Size.infinite,
              ),
            
            // Animated wave overlay with smooth transitions
            CustomPaint(
              painter: _WavePainter(
                color: Colors.white,
                amplitude: widget.amplitude * widget.intensity,
                frequency: widget.frequency,
                time: smoothWavePhase,
                randomOffsets: _randomOffsets,
                opacity: isDark ? 0.05 : 0.03,
                isPlaying: widget.isPlaying,
                intensity: widget.intensity,
              ),
              size: Size.infinite,
            ),

            // Glass blur effect
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.05 : 0.02),
                      (isDark ? Colors.white : Colors.black).withOpacity(0.0),
                    ],
                  ),
                ),
              ),
            ),

            // Subtle pattern overlay with smooth rotation
            Positioned.fill(
              child: Transform.rotate(
                angle: -math.sin(smoothRotation) * 0.02,
                child: Container(
                  decoration: BoxDecoration(
                    backgroundBlendMode: isDark ? BlendMode.overlay : BlendMode.multiply,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        (isDark ? Colors.white : Colors.black).withOpacity(0.03),
                        (isDark ? Colors.white : Colors.black).withOpacity(0.01),
                        (isDark ? Colors.black : Colors.white).withOpacity(0.01),
                        (isDark ? Colors.black : Colors.white).withOpacity(0.03),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

class _ParticlePainter extends CustomPainter {
  final List<Color> colors;
  final List<Offset> positions;
  final List<double> sizes;
  final List<double> speeds;
  final double time;
  final double intensity;

  _ParticlePainter({
    required this.colors,
    required this.positions,
    required this.sizes,
    required this.speeds,
    required this.time,
    required this.intensity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (var i = 0; i < positions.length; i++) {
      final paint = Paint()
        ..color = colors[i].withOpacity(colors[i].opacity * intensity);
      
      // Create smooth continuous motion
      final continuousTime = time + speeds[i] * 2 * math.pi;
      final yOffset = math.sin(continuousTime + positions[i].dx * math.pi) * 
                     size.height * 0.1 * intensity;
      
      final position = Offset(
        positions[i].dx * size.width,
        (positions[i].dy * size.height + yOffset) % size.height,
      );
      
      canvas.drawCircle(position, sizes[i] * intensity, paint);
    }
  }

  @override
  bool shouldRepaint(_ParticlePainter oldDelegate) => true;
}

class _WavePainter extends CustomPainter {
  final Color color;
  final double amplitude;
  final double frequency;
  final double time;
  final List<double> randomOffsets;
  final double opacity;
  final bool isPlaying;
  final double intensity;

  _WavePainter({
    required this.color,
    required this.amplitude,
    required this.frequency,
    required this.time,
    required this.randomOffsets,
    required this.opacity,
    required this.isPlaying,
    required this.intensity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(opacity * (isPlaying ? 1.0 : 0.5))
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

    final width = size.width;
    final height = size.height;

    for (var i = 0; i < 3; i++) {
      final path = Path();
      path.moveTo(0, height);

      for (var x = 0.0; x <= width; x++) {
        final normalizedX = x / width;
        
        // Create continuous wave pattern
        final continuousTime = time + randomOffsets[i];
        
        final primaryWave = math.sin(normalizedX * frequency * math.pi * 2 + continuousTime);
        final secondaryWave = math.sin(normalizedX * frequency * 1.5 * math.pi * 2 + continuousTime * 1.5) * 0.5;
        final tertiaryWave = math.sin(normalizedX * frequency * 2 * math.pi * 2 + continuousTime * 2) * 0.25;
        
        final combinedWave = (primaryWave + secondaryWave + tertiaryWave) / 1.75;
        final waveHeight = combinedWave * amplitude * (1 + i * 0.5) * intensity;
        
        path.lineTo(x, height / 2 + waveHeight);
      }

      path.lineTo(width, height);
      path.close();

      // Smooth gradient effect
      final gradient = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          color.withOpacity(opacity * 1.2 * intensity),
          color.withOpacity(opacity * 0.8 * intensity),
        ],
      ).createShader(Rect.fromLTWH(0, 0, width, height));

      paint.shader = gradient;
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(_WavePainter oldDelegate) =>
      oldDelegate.time != time ||
      oldDelegate.amplitude != amplitude ||
      oldDelegate.intensity != intensity;
} 