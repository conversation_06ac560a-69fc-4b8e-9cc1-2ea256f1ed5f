import 'package:flutter/material.dart';
import 'dart:math' as math;

class MusicIntensityAnalyzer {
  static const int _sampleSize = 10;
  final List<double> _intensityHistory = List.filled(_sampleSize, 0.5);
  int _currentIndex = 0;
  double _currentIntensity = 0.5;
  final math.Random _random = math.Random();

  // Simulated intensity based on playback state and position
  double calculateIntensity({
    required bool isPlaying,
    required int currentPositionMs,
    required int durationMs,
    double baseIntensity = 0.5,
  }) {
    if (!isPlaying) {
      _currentIntensity = _currentIntensity * 0.95 + baseIntensity * 0.05;
      return _currentIntensity;
    }

    // Create a pseudo-random but consistent intensity based on position
    final positionFactor = (currentPositionMs % 1000) / 1000.0;
    final beatIntensity = math.sin(positionFactor * 2 * math.pi) * 0.3;
    
    // Add some natural variation
    final randomFactor = _random.nextDouble() * 0.1;
    
    // Calculate new intensity
    double newIntensity = baseIntensity + beatIntensity + randomFactor;
    newIntensity = newIntensity.clamp(0.2, 1.0);
    
    // Smooth transitions using moving average
    _intensityHistory[_currentIndex] = newIntensity;
    _currentIndex = (_currentIndex + 1) % _sampleSize;
    
    _currentIntensity = _intensityHistory.reduce((a, b) => a + b) / _sampleSize;
    
    return _currentIntensity;
  }
} 