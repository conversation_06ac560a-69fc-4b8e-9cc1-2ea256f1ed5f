import 'package:flutter/material.dart';
import '../../../config/themes.dart';

/// A sleek, modern track progress slider for music playback
/// Shows current position, total duration, and allows seeking
class TrackProgressSlider extends StatelessWidget {
  final Duration currentPosition;
  final Duration totalDuration;
  final bool isPlaying;
  final Function(Duration) onSeek;

  const TrackProgressSlider({
    Key? key,
    required this.currentPosition,
    required this.totalDuration,
    required this.isPlaying,
    required this.onSeek,
  }) : super(key: key);

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${duration.inHours > 0 ? '${duration.inHours}:' : ''}$twoDigitMinutes:$twoDigitSeconds";
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final timeStyle = TextStyle(
      color: Colors.white.withOpacity(0.7),
      fontSize: isSmallScreen ? 11 : 12,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SliderTheme(
          data: SliderThemeData(
            trackHeight: 4,
            activeTrackColor: AppTheme.primaryColor,
            inactiveTrackColor: Colors.white.withOpacity(0.2),
            thumbColor: AppTheme.primaryColor,
            overlayColor: AppTheme.primaryColor.withOpacity(0.12),
            thumbShape: RoundSliderThumbShape(
              enabledThumbRadius: isSmallScreen ? 6 : 7,
              elevation: 4,
              pressedElevation: 8,
            ),
            overlayShape: RoundSliderOverlayShape(
              overlayRadius: isSmallScreen ? 16 : 20,
            ),
            trackShape: CustomTrackShape(),
          ),
          child: Container(
            height: isSmallScreen ? 24 : 28,
            margin: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 8 : 12,
            ),
            child: Slider(
              value: currentPosition.inMilliseconds.toDouble(),
              min: 0,
              max: totalDuration.inMilliseconds.toDouble(),
              onChanged: (value) {
                onSeek(Duration(milliseconds: value.round()));
              },
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 12 : 16,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(currentPosition),
                style: timeStyle,
              ),
              Text(
                _formatDuration(totalDuration),
                style: timeStyle,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class CustomTrackShape extends RoundedRectSliderTrackShape {
  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight ?? 4;
    final double trackLeft = offset.dx;
    final double trackTop = offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }
} 