import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../config/themes.dart';

/// A sleek audio wave visualizer that animates based on playback state
class AudioWaveVisualizer extends StatelessWidget {
  final bool isPlaying;
  final double animationValue;
  final Color? waveColor;
  final int barCount;

  const AudioWaveVisualizer({
    Key? key,
    required this.isPlaying,
    required this.animationValue,
    this.waveColor,
    this.barCount = 27, // Must be an odd number
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    
    final defaultColor = AppTheme.primaryColor;
    final effectiveColor = waveColor ?? defaultColor;
    
    final barWidth = isSmallScreen ? 3.0 : 4.0;
    final spacing = isSmallScreen ? 3.0 : 4.0;
    final minHeight = isSmallScreen ? 3.0 : 4.0;
    final maxHeight = isSmallScreen ? 20.0 : 24.0;

    return SizedBox(
      height: maxHeight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(barCount, (index) {
          // Create a unique animation phase for each bar
          final phase = (index / barCount) * 2 * math.pi;
          final heightFactor = isPlaying
              ? (0.3 + 0.7 * (0.5 + 0.5 * _calculateBarHeight(index, phase)))
              : 0.3; // Shorter bars when paused

          return Padding(
            padding: EdgeInsets.symmetric(horizontal: spacing / 2),
            child: Container(
              width: barWidth,
              height: minHeight + (maxHeight - minHeight) * heightFactor,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(barWidth / 2),
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    effectiveColor.withOpacity(0.6),
                    effectiveColor,
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: effectiveColor.withOpacity(0.2),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  double _calculateBarHeight(int index, double phase) {
    if (!isPlaying) return 0.3;
    
    // Create a more organic wave pattern using multiple sine waves
    final t = animationValue * 2 * math.pi + phase;
    final wave1 = math.sin(t);
    final wave2 = math.sin(t * 1.5) * 0.5;
    final wave3 = math.sin(t * 2) * 0.3;
    
    return (wave1 + wave2 + wave3).abs() / 1.8;
  }
} 