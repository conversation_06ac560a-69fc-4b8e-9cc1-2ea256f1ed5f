import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../models/music_track.dart';
import '../../models/collection_model.dart';
import '../../config/themes.dart';
import '../../providers/apple_music_provider.dart';
import '../../services/collection_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../utils/event_bus.dart';
import 'components/animated_wave_background.dart';
import 'components/music_intensity_analyzer.dart';
import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/rendering.dart';
import '../bottomsheets/collection_selector_bottomsheet.dart';

/// A modern, rich expanded view for the now playing screen
/// Displays album art, track information, and playback controls
class AppleExpandedNowPlayingView extends StatefulWidget {
  final VoidCallback onCollapse;
  final double height;
  final MusicTrack track;
  final bool isPlaying;
  final Function() onPlayPause;
  final Function(int) onSeek;

  const AppleExpandedNowPlayingView({
    Key? key,
    required this.onCollapse,
    required this.height,
    required this.track,
    required this.isPlaying,
    required this.onPlayPause,
    required this.onSeek,
  }) : super(key: key);

  @override
  State<AppleExpandedNowPlayingView> createState() => _AppleExpandedNowPlayingViewState();
}

class _AppleExpandedNowPlayingViewState extends State<AppleExpandedNowPlayingView> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final MusicIntensityAnalyzer _intensityAnalyzer = MusicIntensityAnalyzer();
  double _dragStartY = 0;
  double _dragUpdateY = 0;
  bool _isDragging = false;
  bool _isSeekDragging = false;
  double _dragValue = 0.0;
  
  // Button debouncing
  DateTime? _lastSkipNextTime;
  DateTime? _lastSkipPreviousTime;
  static const Duration _buttonDebounceDelay = Duration(milliseconds: 300);
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleDragStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
      _dragStartY = details.globalPosition.dy;
    });
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    setState(() {
      _dragUpdateY = details.globalPosition.dy;
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!_isDragging) return;
    
    final dragDistance = _dragUpdateY - _dragStartY;
    final dragVelocity = details.primaryVelocity ?? 0;
    
    if (dragDistance > 50 || dragVelocity > 500) {
      widget.onCollapse();
    }
    
    setState(() {
      _isDragging = false;
    });
  }

  void _handleAddToCollection(BuildContext context, MusicTrack? track) {
    if (track == null) return;
    
    // Show collection selection bottom sheet
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CollectionSelectorBottomSheet(track: track),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppleMusicProvider>(
      builder: (context, appleMusicProvider, child) {
        final mediaQuery = MediaQuery.of(context);
        final size = mediaQuery.size;
        final scaleFactor = (size.height / 812.0).clamp(0.8, 1.1); // Base height 812 (iPhone X)
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
        final textColor = isDarkMode ? Colors.white : Colors.black;
        final subTextColor = isDarkMode ? Colors.white.withOpacity(0.7) : Colors.black.withOpacity(0.7);
        final backgroundColor = isDarkMode 
          ? Theme.of(context).colorScheme.surface.withOpacity(0.95)
          : Theme.of(context).colorScheme.surface.withOpacity(0.98);
        final isSmallScreen = mediaQuery.size.width < 360;
        final theme = Theme.of(context);
        
        // Calculate music intensity
        final intensity = _intensityAnalyzer.calculateIntensity(
          isPlaying: widget.isPlaying,
          currentPositionMs: appleMusicProvider.position.inMilliseconds,
          durationMs: widget.track.durationMs,
        );
        
        return MediaQuery(
          data: mediaQuery.copyWith(textScaleFactor: scaleFactor),
          child: GestureDetector(
            onVerticalDragStart: _handleDragStart,
            onVerticalDragUpdate: _handleDragUpdate,
            onVerticalDragEnd: _handleDragEnd,
            child: Container(
              decoration: BoxDecoration(
                color: backgroundColor,
              ),
              padding: EdgeInsets.only(top: 56),
              child: Stack(
                children: [
                  // Animated background
                  Positioned.fill(
                    child: AnimatedWaveBackground(
                      color: theme.colorScheme.primary,
                      isPlaying: widget.isPlaying,
                      intensity: intensity,
                    ),
                  ),
                  
                  // Status bar spacer
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: mediaQuery.padding.top,
                      decoration: BoxDecoration(
                        color: backgroundColor,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            theme.colorScheme.primary.withOpacity(isDarkMode ? 0.08 : 0.03),
                            backgroundColor,
                            backgroundColor,
                          ],
                          stops: const [0.0, 0.3, 1.0],
                        ),
                      ),
                    ),
                  ),
                  
                  // Main content
                  Column(
                    children: [
                      // Status bar spacer
                      Container(
                        height: mediaQuery.padding.top,
                      ),
                      
                      // Main content
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            final size = mediaQuery.size;
                            final scaleFactor = (size.height / 812.0).clamp(0.8, 1.1); // Base height 812 (iPhone X)
                            
                            return Column(
                              children: [
                                // Header with collapse button
                                SizedBox(
                                  height: 64 * scaleFactor,
                                  child: _buildHeader(textColor, scaleFactor),
                                ),
                                
                                // Main content area
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      // Album art
                                      Expanded(
                                        flex: 36,
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(horizontal: 32 * scaleFactor),
                                          child: Center(
                                            child: LayoutBuilder(
                                              builder: (context, artConstraints) {
                                                final artSize = math.min(artConstraints.maxWidth, artConstraints.maxHeight);
                                                return _buildAlbumArt(artSize, textColor);
                                              },
                                            ),
                                          ),
                                        ),
                                      ),
                                      
                                      // Track info
                                      Padding(
                                        padding: EdgeInsets.fromLTRB(24 * scaleFactor, 16 * scaleFactor, 24 * scaleFactor, 16 * scaleFactor),
                                        child: _buildTrackInfo(textColor, subTextColor, scaleFactor),
                                      ),
                                      
                                      // Spacer to push controls down
                                      const Spacer(flex: 1),

                                      // Progress bar & controls
                                      Padding(
                                        padding: EdgeInsets.symmetric(horizontal: 24 * scaleFactor),
                                        child: _buildProgressBar(context, textColor, scaleFactor, appleMusicProvider),
                                      ),
                                      
                                      Padding(
                                        padding: EdgeInsets.symmetric(vertical: 16 * scaleFactor),
                                        child: _buildPlaybackControls(textColor, subTextColor, scaleFactor, appleMusicProvider),
                                      ),
                                      
                                      // Bottom spacing to account for safe area
                                      SizedBox(height: mediaQuery.padding.bottom + 8),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(Color textColor, double scaleFactor) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16 * scaleFactor),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: Icon(Icons.keyboard_arrow_down, color: textColor, size: 28 * scaleFactor),
            onPressed: widget.onCollapse,
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(minWidth: 36 * scaleFactor, minHeight: 36 * scaleFactor),
          ),
          Text(
            'Now Playing',
            style: TextStyle(
              color: textColor,
              fontSize: 16 * scaleFactor,
              fontWeight: FontWeight.w600,
            ),
          ),
          IconButton(
            icon: Icon(Icons.more_vert, color: textColor, size: 28 * scaleFactor),
            onPressed: () {
              showModalBottomSheet(context: context, builder: (context) => _buildOptionsMenu(context));
            },
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(minWidth: 36 * scaleFactor, minHeight: 36 * scaleFactor),
          ),
        ],
      ),
    );
  }

  Widget _buildAlbumArt(double size, Color textColor) {
    String imageUrl = widget.track.albumArt;

    return Hero(
      tag: 'apple_now_playing_album_art_${widget.track.id}',
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24 * size / 300),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 30 * size / 300,
              offset: Offset(0, 15 * size / 300),
              spreadRadius: 5 * size / 300,
            ),
            BoxShadow(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.15),
              blurRadius: 40 * size / 300,
              offset: Offset(0, 20 * size / 300),
              spreadRadius: 10 * size / 300,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24 * size / 300),
          child: Stack(
            children: [
              // Album art image
              Image.network(
                imageUrl,
                fit: BoxFit.cover,
                width: size,
                height: size,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: Colors.grey[800],
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded / 
                                loadingProgress.expectedTotalBytes!
                            : null,
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  if (kDebugMode) {
                    print('Error loading album art: $error');
                  }
                  return Container(
                    color: Colors.grey[800],
                    child: Icon(
                      Icons.music_note,
                      size: size * 0.3,
                      color: textColor.withOpacity(0.5),
                    ),
                  );
                },
              ),
              // Subtle gradient overlay
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.1),
                    ],
                  ),
                ),
              ),
              // Animated shine effect when playing
              if (widget.isPlaying)
                AnimatedPositioned(
                  duration: const Duration(milliseconds: 1500),
                  curve: Curves.easeInOut,
                  left: -size * 0.5,
                  top: -size * 0.5,
                  child: Transform(
                    transform: Matrix4.rotationZ(math.pi / 4)..translate(0.0, -size * 0.5),
                    child: Container(
                      width: size * 2,
                      height: size * 2,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.white.withOpacity(0),
                            Colors.white.withOpacity(0.1),
                            Colors.white.withOpacity(0),
                          ],
                          stops: const [0.0, 0.5, 1.0],
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrackInfo(Color textColor, Color subTextColor, double scaleFactor) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return ClipRRect(
      borderRadius: BorderRadius.circular(20 * scaleFactor),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 24 * scaleFactor,
            vertical: 20 * scaleFactor,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20 * scaleFactor),
            color: theme.colorScheme.surface.withOpacity(0.5),
            border: Border.all(
              color: theme.colorScheme.primary.withOpacity(0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withOpacity(0.05),
                blurRadius: 20,
                spreadRadius: -5,
              ),
            ],
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.surface.withOpacity(isDark ? 0.7 : 0.8),
                theme.colorScheme.surface.withOpacity(isDark ? 0.6 : 0.7),
              ],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                widget.track.title,
                style: TextStyle(
                  color: textColor,
                  fontSize: 24 * scaleFactor,
                  fontWeight: FontWeight.bold,
                  letterSpacing: -0.5,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 10 * scaleFactor),
              Text(
                widget.track.artist,
                style: TextStyle(
                  color: subTextColor,
                  fontSize: 18 * scaleFactor,
                  letterSpacing: -0.3,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 6 * scaleFactor),
              Text(
                widget.track.album,
                style: TextStyle(
                  color: subTextColor.withOpacity(0.8),
                  fontSize: 16 * scaleFactor,
                  letterSpacing: -0.2,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressBar(BuildContext context, Color textColor, double scaleFactor, AppleMusicProvider appleMusicProvider) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return ClipRRect(
      borderRadius: BorderRadius.circular(20 * scaleFactor),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 20 * scaleFactor,
            vertical: 16 * scaleFactor,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20 * scaleFactor),
            color: theme.colorScheme.surface.withOpacity(0.3),
            border: Border.all(
              color: theme.colorScheme.primary.withOpacity(0.1),
              width: 1,
            ),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.surface.withOpacity(isDark ? 0.4 : 0.5),
                theme.colorScheme.surface.withOpacity(isDark ? 0.3 : 0.4),
              ],
            ),
          ),
          child: Column(
            children: [
              SliderTheme(
                data: SliderThemeData(
                  activeTrackColor: theme.colorScheme.primary.withOpacity(isDark ? 0.9 : 0.8),
                  inactiveTrackColor: theme.colorScheme.primary.withOpacity(isDark ? 0.2 : 0.1),
                  thumbColor: theme.colorScheme.primary,
                  overlayColor: theme.colorScheme.primary.withOpacity(0.2),
                  trackHeight: 4.0 * scaleFactor,
                  thumbShape: RoundSliderThumbShape(
                    enabledThumbRadius: 6 * scaleFactor,
                    pressedElevation: 8,
                  ),
                  overlayShape: RoundSliderOverlayShape(overlayRadius: 20 * scaleFactor),
                ),
                child: Slider(
                  value: _isSeekDragging 
                    ? _dragValue 
                    : (appleMusicProvider.duration.inMilliseconds > 0 
                        ? (appleMusicProvider.position.inMilliseconds / appleMusicProvider.duration.inMilliseconds).clamp(0.0, 1.0)
                        : 0.0),
                  onChanged: (value) {
                    setState(() {
                      _isSeekDragging = true;
                      _dragValue = value;
                    });
                  },
                  onChangeEnd: (value) {
                    setState(() {
                      _isSeekDragging = false;
                    });
                    final position = (value * widget.track.durationMs).round();
                    widget.onSeek(position);
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(appleMusicProvider.position.inMilliseconds),
                      style: TextStyle(
                        color: textColor.withOpacity(0.7),
                        fontSize: 13 * scaleFactor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _formatDuration(widget.track.durationMs),
                      style: TextStyle(
                        color: textColor.withOpacity(0.7),
                        fontSize: 13 * scaleFactor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaybackControls(Color textColor, Color subTextColor, double scaleFactor, AppleMusicProvider appleMusicProvider) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24 * scaleFactor),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: Icon(
              Icons.shuffle_rounded,
              color: appleMusicProvider.isShuffleEnabled
                  ? theme.colorScheme.primary
                  : textColor.withOpacity(0.7),
              size: 28 * scaleFactor,
            ),
            onPressed: () async {
              final success = await appleMusicProvider.toggleShuffle();
              if (!success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Failed to toggle shuffle')),
                );
              }
            },
          ),
          IconButton(
            icon: Icon(
              Icons.skip_previous_rounded,
              color: appleMusicProvider.hasPrevious 
                  ? textColor.withOpacity(0.9)
                  : textColor.withOpacity(0.3),
              size: 36 * scaleFactor,
            ),
            onPressed: appleMusicProvider.hasPrevious ? () async {
              // Debounce rapid button presses
              final now = DateTime.now();
              if (_lastSkipPreviousTime != null && 
                  now.difference(_lastSkipPreviousTime!) < _buttonDebounceDelay) {
                if (kDebugMode) {
                  print('🎵 [UI] Previous button press ignored - too rapid');
                }
                return;
              }
              _lastSkipPreviousTime = now;
              
              if (kDebugMode) {
                print('🎵 [UI] Previous button pressed');
              }
              final success = await appleMusicProvider.skipToPreviousItem();
              if (kDebugMode) {
                print('🎵 [UI] Previous skip result: $success');
              }
              if (!success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Failed to skip to previous track')),
                );
              }
            } : null,
          ),
          Container(
            width: 72 * scaleFactor,
            height: 72 * scaleFactor,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colorScheme.primary.withOpacity(isDark ? 0.9 : 0.8),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withOpacity(0.3),
                  blurRadius: 12 * scaleFactor,
                  spreadRadius: 2 * scaleFactor,
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.onPlayPause,
                customBorder: const CircleBorder(),
                child: Icon(
                  widget.isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
                  color: Colors.white,
                  size: 36 * scaleFactor,
                ),
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.skip_next_rounded,
              color: appleMusicProvider.hasNext 
                  ? textColor.withOpacity(0.9)
                  : textColor.withOpacity(0.3),
              size: 36 * scaleFactor,
            ),
            onPressed: appleMusicProvider.hasNext ? () async {
              // Debounce rapid button presses
              final now = DateTime.now();
              if (_lastSkipNextTime != null && 
                  now.difference(_lastSkipNextTime!) < _buttonDebounceDelay) {
                if (kDebugMode) {
                  print('🎵 [UI] Next button press ignored - too rapid');
                }
                return;
              }
              _lastSkipNextTime = now;
              
              if (kDebugMode) {
                print('🎵 [UI] Next button pressed');
              }
              final success = await appleMusicProvider.skipToNextItem();
              if (kDebugMode) {
                print('🎵 [UI] Next skip result: $success');
              }
              if (!success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Failed to skip to next track')),
                );
              }
            } : null,
          ),
          IconButton(
            icon: Icon(
              appleMusicProvider.repeatMode == 'one'
                  ? Icons.repeat_one_rounded
                  : Icons.repeat_rounded,
              color: appleMusicProvider.repeatMode != 'off'
                  ? theme.colorScheme.primary
                  : textColor.withOpacity(0.7),
              size: 28 * scaleFactor,
            ),
            onPressed: () async {
              final success = await appleMusicProvider.toggleRepeatMode();
              if (!success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Failed to toggle repeat mode')),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOptionsMenu(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.collections_bookmark_outlined),
            title: const Text('Add to Collection'),
            onTap: () {
              Navigator.pop(context);
              _handleAddToCollection(context, widget.track);
            },
          ),
          ListTile(
            leading: const Icon(Icons.playlist_add),
            title: const Text('Add to playlist'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Add to playlist (coming soon)'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Share (coming soon)'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.album),
            title: const Text('View album'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('View album (coming soon)'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('View artist'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('View artist (coming soon)'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  String _formatDuration(int milliseconds) {
    final duration = Duration(milliseconds: milliseconds);
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
