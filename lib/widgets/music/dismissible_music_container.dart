import 'package:flutter/material.dart';

class DismissibleMusicContainer extends StatefulWidget {
  final Widget child;
  final VoidCallback onDismiss;
  final bool isSmallScreen;

  const DismissibleMusicContainer({
    Key? key,
    required this.child,
    required this.onDismiss,
    this.isSmallScreen = false,
  }) : super(key: key);

  @override
  State<DismissibleMusicContainer> createState() => _DismissibleMusicContainerState();
}

class _DismissibleMusicContainerState extends State<DismissibleMusicContainer> {
  bool _isDismissed = false;

  @override
  Widget build(BuildContext context) {
    if (_isDismissed) return const SizedBox.shrink();

    return Stack(
      children: [
        widget.child,
        Positioned(
          top: widget.isSmallScreen ? 8 : 10,
          right: widget.isSmallScreen ? 8 : 10,
          child: _buildDismissButton(context),
        ),
      ],
    );
  }

  Widget _buildDismissButton(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          setState(() => _isDismissed = true);
          widget.onDismiss();
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.4),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.close_rounded,
            size: widget.isSmallScreen ? 16 : 18,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ),
    );
  }
} 