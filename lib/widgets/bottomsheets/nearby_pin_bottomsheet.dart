import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import 'dart:ui' as ui;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/youtube_provider.dart';
import '../../providers/pin_provider.dart';
import '../../models/music_track.dart';
import 'dart:convert';

class NearbyPinBottomSheet extends StatefulWidget {
  final Map<String, dynamic> pinData;
  final VoidCallback? onPlayPressed;
  final Function(String)? onStartHighlight;
  final Function(String, String)? onShowCaption;

  const NearbyPinBottomSheet({
    Key? key,
    required this.pinData,
    this.onPlayPressed,
    this.onStartHighlight,
    this.onShowCaption,
  }) : super(key: key);

  @override
  State<NearbyPinBottomSheet> createState() => _NearbyPinBottomSheetState();
}

class _NearbyPinBottomSheetState extends State<NearbyPinBottomSheet> {
  @override
  void initState() {
    super.initState();
    // Update PiP params when bottomsheet is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 350), () {
        PictureInPicture.updatePiPParams(
          pipParams: PiPParams(
            pipWindowHeight: 125,
            pipWindowWidth: 125,
            bottomSpace: MediaQuery.of(context).size.height *
                0.45, // Match bottomsheet height
            leftSpace: 0,
            rightSpace: 0,
            topSpace: 0,
            maxSize: const Size(125, 125),
            minSize: const Size(125, 125),
            movable: true,
            resizable: false,
            initialCorner: PIPViewCorner.bottomLeft,
          ),
        );
      });
    });
  }

  @override
  void dispose() {
    // Restore default PiP params when bottomsheet is closed
    PictureInPicture.updatePiPParams(
      pipParams: const PiPParams(
        pipWindowHeight: 125,
        pipWindowWidth: 125,
        bottomSpace: 64,
        leftSpace: 0,
        rightSpace: 0,
        topSpace: 64,
        maxSize: Size(125, 125),
        minSize: Size(125, 125),
        movable: true,
        resizable: false,
        initialCorner: PIPViewCorner.bottomLeft,
      ),
    );
    super.dispose();
  }

  Color _getPinColor(String rarity) {
    switch (rarity) {
      case 'legendary':
        return const Color(0xFFFFE55C);
      case 'epic':
        return const Color(0xFFE91E63);
      case 'rare':
        return const Color(0xFF00E5FF);
      case 'uncommon':
        return const Color(0xFF4CAF50);
      default:
        return const Color(0xFF2196F3);
    }
  }

  String _getPinRarity(Map<String, dynamic> pinData) {
    final skinDetails = pinData['skin_details'] ?? pinData['skinDetails'];
    if (skinDetails is Map<String, dynamic>) {
      final metadata = skinDetails['metadata'];
      if (metadata is Map<String, dynamic>) {
        if (metadata.containsKey('rarity')) {
          return metadata['rarity']?.toString().toLowerCase() ?? 'common';
        }
      } else if (metadata is String && metadata.isNotEmpty) {
        try {
          final decoded = jsonDecode(metadata);
          if (decoded is Map<String, dynamic> &&
              decoded.containsKey('rarity')) {
            return decoded['rarity']?.toString().toLowerCase() ?? 'common';
          }
        } catch (e) {
          debugPrint('Could not decode metadata json: $e');
        }
      }
    }
    // Fallback to top-level rarity
    return pinData['rarity']?.toString().toLowerCase() ?? 'common';
  }

  /// Handle music playback through the appropriate provider with automatic fallback
  /// Priority: Apple Music -> YouTube (final fallback)
  Future<void> _handlePlayMusic(BuildContext parentContext) async {
    try {
      // Create a mutable copy of the pin data
      final pinData = Map<String, dynamic>.from(widget.pinData);

      // Get pin provider
      final pinProvider = Provider.of<PinProvider>(parentContext, listen: false);

      // IMMEDIATELY set the currently playing pin data in PinProvider before attempting playback
      final pinId = pinData['id']?.toString();
      if (pinId != null) {
        debugPrint('🎵 [NearbyPinBottomSheet] Setting currently playing pin in provider: $pinId');
        pinProvider.setCurrentlyPlayingPin(pinId, pinData);
        debugPrint('🎵 [NearbyPinBottomSheet] Pin data set in provider successfully');
      }

      // Try Apple Music first with YouTube fallback
      bool playbackSuccess = await _tryAppleMusicWithYouTubeFallback(parentContext, pinData);

      // Check if widget is still mounted after async operations
      if (!mounted) return;

      // Show error if playback failed on all services
      if (!playbackSuccess) {
        // All services failed - clear the pin data
        pinProvider.clearCurrentlyPlayingPin();

        ScaffoldMessenger.of(parentContext).showSnackBar(
          const SnackBar(
            content: Text('Could not play track on any available service'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      } else {
        // LEGACY: Still call the highlight callbacks for backwards compatibility with SnapchatStyleMapScreen
        if (pinId != null && widget.onStartHighlight != null) {
          widget.onStartHighlight!(pinId);
          debugPrint('🎵 Legacy highlight callback called for pin: $pinId');

          // Also show caption if available
          final caption = pinData['caption'] as String? ??
              pinData['description'] as String?;
          if (caption != null &&
              caption.isNotEmpty &&
              widget.onShowCaption != null) {
            widget.onShowCaption!(pinId, caption);
            debugPrint(
                '🎵 Legacy caption callback called for pin: $pinId with caption: "$caption"');
          }
        }
      }

      // Call the original callback if provided (for backwards compatibility)
      widget.onPlayPressed?.call();
    } catch (e) {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Clear pin data on error
      final pinProvider = Provider.of<PinProvider>(parentContext, listen: false);
      pinProvider.clearCurrentlyPlayingPin();

      ScaffoldMessenger.of(parentContext).showSnackBar(
        SnackBar(
          content: Text('Error playing music: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Try Apple Music first, fallback to YouTube if needed
  Future<bool> _tryAppleMusicWithYouTubeFallback(BuildContext context, Map<String, dynamic> pinData) async {
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);

    bool success = false;

    // Always try Apple Music first using the same approach as TrackCard
    try {
      if (kDebugMode) {
        print('🎵 [NearbyPinBottomSheet] Attempting Apple Music playback for pin');
        print('🎵 [NearbyPinBottomSheet] Pin data keys: ${pinData.keys.toList()}');
        print('🎵 [NearbyPinBottomSheet] Track title: ${pinData['track_title'] ?? pinData['title']}');
        print('🎵 [NearbyPinBottomSheet] Track artist: ${pinData['track_artist'] ?? pinData['artist']}');
        print('🎵 [NearbyPinBottomSheet] Artwork URL: ${pinData['artwork_url'] ?? pinData['image']}');
      }

      // Convert pin data to MusicTrack using correct pin field names
      final originalUri = pinData['uri'] ?? pinData['track_uri'] ?? '';
      final originalUrl = pinData['track_url'] ?? pinData['url'] ?? '';
      final service = pinData['service'] ?? 'spotify'; // Default to spotify for pins

      // If the original URI is from Spotify, clear it for Apple Music search
      final cleanUri = originalUri.contains('spotify:') ? '' : originalUri;
      final cleanUrl = originalUrl.contains('spotify.com') ? '' : originalUrl;

      final musicTrack = MusicTrack(
        id: pinData['id']?.toString() ?? '',
        title: pinData['track_title'] ?? pinData['title'] ?? pinData['songTitle'] ?? 'Unknown Song',
        artist: pinData['track_artist'] ?? pinData['artist'] ?? 'Unknown Artist',
        album: pinData['album'] ?? '',
        albumArt: pinData['artwork_url'] ?? pinData['image'] ?? '',
        uri: cleanUri, // Use cleaned URI to force Apple Music search
        durationMs: (pinData['duration_ms'] is int) ? pinData['duration_ms'] :
                   (pinData['duration'] is int) ? pinData['duration'] : 0,
        url: cleanUrl, // Use cleaned URL
        service: service,
        serviceType: service == 'apple_music' || service == 'apple' ? 'apple' : 'spotify',
        genres: [],
        explicit: false,
        popularity: 0,
      );

      if (kDebugMode) {
        print('🎵 [NearbyPinBottomSheet] Created MusicTrack: ${musicTrack.title} by ${musicTrack.artist}');
        print('🎵 [NearbyPinBottomSheet] MusicTrack service: ${musicTrack.service}');
        print('🎵 [NearbyPinBottomSheet] MusicTrack serviceType: ${musicTrack.serviceType}');
        print('🎵 [NearbyPinBottomSheet] Attempting Apple Music first, then YouTube fallback if needed');
      }

      // Use the same approach as TrackCard
      final needsSearch = musicTrack.service != 'apple_music' && musicTrack.service != 'apple';
      
      if (needsSearch) {
        // Use the method that shows snackbar for no exact match (same as TrackCard)
        success = await appleMusicProvider.playTrackBySearchWithFallback(musicTrack, context);
      } else {
        // For Apple Music tracks, use queue manager directly (same as TrackCard)
        final queueManager = appleMusicProvider.queueManager;
        success = await queueManager.setQueue(
          tracks: [musicTrack],
          collectionType: 'single_track',
          startIndex: 0,
        );
      }

      if (kDebugMode) {
        print('🎵 [NearbyPinBottomSheet] Apple Music result: success=$success, needsSearch=$needsSearch');
      }

      if (success) {
        final trackTitle = pinData['title'] ?? pinData['track_title'] ?? 'Unknown Song';
        if (kDebugMode) {
          print('✅ [NearbyPinBottomSheet] Apple Music playback successful: $trackTitle');
        }
        return true;
      }
      
      // Only fallback to YouTube automatically if it's not a search scenario
      // For search scenarios, the snackbar will handle user-controlled fallback (same as TrackCard)
      if (!success) {
        if (kDebugMode) {
          print('🎵 [NearbyPinBottomSheet] Apple Music failed, trying YouTube fallback...');
        }
        if (context.mounted) {
          return await _tryYouTubeFallback(context, pinData);
        }
        return false;
      }
      
      // For search scenarios that failed, don't auto-fallback (user gets snackbar option)
      return false;

    } catch (e) {
      if (kDebugMode) {
        print('🎵 [NearbyPinBottomSheet] Apple Music error, trying YouTube fallback: $e');
      }
      if (context.mounted) {
        return await _tryYouTubeFallback(context, pinData);
      }
      return false;
    }
  }

  /// Try YouTube as final fallback when all other services fail
  Future<bool> _tryYouTubeFallback(BuildContext context, Map<String, dynamic> pinData) async {
    try {
      if (kDebugMode) {
        print('🎵 [NearbyPinBottomSheet] Trying YouTube fallback for: ${pinData['title']}');
      }
      
      // Get YouTube provider
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
      
      // Use YouTube provider's playTrackFromPin method for proper pin communication
      final success = await youtubeProvider.playTrackFromPin(pinData, context: context);
      
      if (success) {
        final trackTitle = pinData['title'] ?? pinData['track_title'] ?? 'Unknown Song';
        // Removed the snackbar notification
        if (kDebugMode) {
          print('✅ [NearbyPinBottomSheet] YouTube fallback successful: $trackTitle');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ [NearbyPinBottomSheet] YouTube fallback failed');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [NearbyPinBottomSheet] Error in YouTube fallback: $e');
      }
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final rarity = _getPinRarity(widget.pinData);
    final pinColor = _getPinColor(rarity);

    // Attempt to resolve artwork (album cover)
    final artworkUrl = widget.pinData['artwork_url'] ??
        widget.pinData['albumArt'] ??
        widget.pinData['album_art'] ??
        widget.pinData['image_url'];

    // Attempt to resolve user profile picture
    final profilePicUrl = widget.pinData['owner']?['profile_pic'] ??
        widget.pinData['profile_pic'];

    // Debug profile picture URL
    debugPrint('🖼️ [NearbyPinBottomSheet] Profile pic debug:');
    debugPrint('  - Full pin data keys: ${widget.pinData.keys.toList()}');
    debugPrint('  - Owner data: ${widget.pinData['owner']}');
    debugPrint('  - Direct profile_pic: ${widget.pinData['profile_pic']}');
    debugPrint('  - Final profilePicUrl: $profilePicUrl');
    debugPrint(
        '  - URL is valid: ${profilePicUrl != null && profilePicUrl.isNotEmpty && Uri.tryParse(profilePicUrl)?.hasAbsolutePath == true}');

    return Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, -8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        child: BackdropFilter(
          filter: ui.ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.25),
                  Colors.white.withOpacity(0.1),
                  Colors.black.withOpacity(0.1),
                  Colors.black.withOpacity(0.2),
                ],
              ),
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // Header with close button
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Pin in your aura',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            shadows: [
                              Shadow(
                                color: Colors.black.withOpacity(0.5),
                                offset: const Offset(1, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                        ),
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Song details with album cover
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Album cover
                              if (artworkUrl != null &&
                                  artworkUrl.isNotEmpty) ...[
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Container(
                                    width: 80,
                                    height: 80,
                                    child: CachedNetworkImage(
                                      imageUrl: artworkUrl,
                                      fit: BoxFit.cover,
                                      placeholder: (context, url) => Container(
                                        color: Colors.white24,
                                        child: const Center(
                                            child: CircularProgressIndicator(
                                                strokeWidth: 2)),
                                      ),
                                      errorWidget: (context, url, error) =>
                                          Container(
                                        color: Colors.white24,
                                        child: const Center(
                                            child: Icon(Icons.music_note,
                                                color: Colors.white54,
                                                size: 32)),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                              ],
                              // Song info
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.pinData['title'] ??
                                          widget.pinData['track_title'] ??
                                          'Unknown Track',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 6),
                                    Text(
                                      widget.pinData['artist'] ??
                                          widget.pinData['track_artist'] ??
                                          'Unknown Artist',
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.8),
                                        fontSize: 15,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    if (widget.pinData['caption'] != null &&
                                        widget.pinData['caption']
                                            .toString()
                                            .isNotEmpty) ...[
                                      const SizedBox(height: 8),
                                      Text(
                                        widget.pinData['caption'],
                                        style: TextStyle(
                                          color: Colors.white.withOpacity(0.7),
                                          fontSize: 13,
                                          fontStyle: FontStyle.italic,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Enhanced profile section with better design
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.08),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.15),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              // Profile picture with enhanced styling
                              Container(
                                padding: const EdgeInsets.all(3),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: LinearGradient(
                                    colors: [
                                      pinColor.withOpacity(0.6),
                                      pinColor.withOpacity(0.3),
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: pinColor.withOpacity(0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withOpacity(0.1),
                                  ),
                                  child: CircleAvatar(
                                    radius: 22,
                                    backgroundColor:
                                        Colors.white.withOpacity(0.2),
                                    backgroundImage: profilePicUrl != null &&
                                            profilePicUrl.isNotEmpty &&
                                            Uri.tryParse(profilePicUrl)
                                                    ?.hasAbsolutePath ==
                                                true
                                        ? CachedNetworkImageProvider(
                                            profilePicUrl,
                                            errorListener: (error) {
                                              debugPrint(
                                                  '❌ [NearbyPinBottomSheet] CachedNetworkImage error for $profilePicUrl: $error');
                                            },
                                          )
                                        : null,
                                    child: profilePicUrl == null ||
                                            profilePicUrl.isEmpty ||
                                            Uri.tryParse(profilePicUrl)
                                                    ?.hasAbsolutePath !=
                                                true
                                        ? Icon(
                                            Icons.person,
                                            color:
                                                Colors.white.withOpacity(0.7),
                                            size: 24,
                                          )
                                        : null,
                                  ),
                                ),
                              ),

                              const SizedBox(width: 16),

                              // Profile info
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '@${widget.pinData['owner']?['username'] ?? widget.pinData['username'] ?? 'Unknown'}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Pin Creator',
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.6),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Rarity badge with enhanced styling
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      pinColor.withOpacity(0.3),
                                      pinColor.withOpacity(0.2),
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: pinColor.withOpacity(0.5),
                                    width: 1.5,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: pinColor.withOpacity(0.2),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Text(
                                  rarity.toUpperCase(),
                                  style: TextStyle(
                                    color: pinColor,
                                    fontSize: 11,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Play button with cross-platform support
                        Container(
                          width: double.infinity,
                          height: 56,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                pinColor.withOpacity(0.8),
                                pinColor.withOpacity(0.6),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: pinColor.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(16),
                              onTap: () {
                                // Get the parent context before closing the bottom sheet
                                final parentContext =
                                    Navigator.of(context).context;

                                // Close the bottom sheet first
                                Navigator.pop(context);

                                // Then handle playing music with the parent context
                                _handlePlayMusic(parentContext);
                              },
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.play_arrow,
                                    color: Colors.white,
                                    size: 28,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Play Music',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                        // Added padding below play button
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
