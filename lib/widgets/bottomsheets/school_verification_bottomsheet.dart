import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import 'dart:ui';
import '../../services/verification/school_verification_service.dart';
import 'dart:math' as math;

class SchoolVerificationBottomSheet {
  static void show(
    BuildContext context, {
    required Function(Map<String, dynamic>) onVerificationComplete,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      isDismissible: true,
      enableDrag: true,
      builder: (context) => _SchoolVerificationContent(
        onVerificationComplete: onVerificationComplete,
      ),
    );
  }
}

class _SchoolVerificationContent extends StatefulWidget {
  final Function(Map<String, dynamic>) onVerificationComplete;

  const _SchoolVerificationContent({
    required this.onVerificationComplete,
  });

  @override
  State<_SchoolVerificationContent> createState() =>
      _SchoolVerificationContentState();
}

class _SchoolVerificationContentState extends State<_SchoolVerificationContent>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _codeFormKey = GlobalKey<FormState>();
  final _schoolVerificationService = SchoolVerificationService();
  final _emailController = TextEditingController();
  final _schoolNameController = TextEditingController();
  final _codeController = TextEditingController();

  late AnimationController _animationController;
  late AnimationController _slideController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  bool _isLoading = false;
  bool _isCodeSent = false; // Track if we're in code verification phase
  String? _errorMessage;
  String? _schoolEmail; // Store the email for code verification
  String? _detectedSchoolName; // Store auto-detected school name

  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _schoolNameFocusNode = FocusNode();
  final FocusNode _codeFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
    _slideController.forward();

    // Update PiP params when bottomsheet is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PictureInPicture.updatePiPParams(
        pipParams: PiPParams(
          pipWindowHeight: 125,
          pipWindowWidth: 125,
          bottomSpace: MediaQuery.of(context).size.height *
              0.8, // Match bottomsheet height
          leftSpace: 0,
          rightSpace: 0,
          topSpace: 0,
          maxSize: const Size(125, 125),
          minSize: const Size(125, 125),
          movable: true,
          resizable: false,
          initialCorner: PIPViewCorner.bottomLeft,
        ),
      );
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _slideController.dispose();
    _emailController.dispose();
    _schoolNameController.dispose();
    _codeController.dispose();
    _emailFocusNode.dispose();
    _schoolNameFocusNode.dispose();
    _codeFocusNode.dispose();
    // Restore default PiP params when bottomsheet is closed
    PictureInPicture.updatePiPParams(
      pipParams: const PiPParams(
        pipWindowHeight: 125,
        pipWindowWidth: 125,
        bottomSpace: 64,
        leftSpace: 0,
        rightSpace: 0,
        topSpace: 64,
        maxSize: Size(125, 125),
        minSize: Size(125, 125),
        movable: true,
        resizable: false,
        initialCorner: PIPViewCorner.bottomLeft,
      ),
    );
    super.dispose();
  }

  Future<void> _submitVerification() async {
    // Dismiss keyboard before validation
    FocusScope.of(context).unfocus();

    if (_isCodeSent) {
      // We're in code verification phase
      await _verifyCode();
    } else {
      // We're in initial email submission phase
      await _submitEmailVerification();
    }
  }

  Future<void> _submitEmailVerification() async {
    if (!_formKey.currentState!.validate()) {
      HapticFeedback.heavyImpact();
      return;
    }

    HapticFeedback.mediumImpact();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await _schoolVerificationService.submitVerificationRequest(
        email: _emailController.text.trim().toLowerCase(),
        schoolName: _schoolNameController.text.trim().isNotEmpty
            ? _schoolNameController.text.trim()
            : null,
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (result['success']) {
        HapticFeedback.mediumImpact();
        setState(() {
          _isCodeSent = true;
          _schoolEmail = _emailController.text.trim();
          _detectedSchoolName = result['school_name'];
          _errorMessage = null;
        });

        // Focus on code input
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _codeFocusNode.requestFocus();
        });
      } else {
        setState(() {
          _errorMessage = result['message'];
        });
        HapticFeedback.heavyImpact();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Network error. Please try again.';
        });
        HapticFeedback.heavyImpact();
      }
    }
  }

  Future<void> _verifyCode() async {
    if (!_codeFormKey.currentState!.validate()) {
      HapticFeedback.heavyImpact();
      return;
    }

    HapticFeedback.mediumImpact();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await _schoolVerificationService.verifyCode(
        code: _codeController.text.trim(),
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (result['success']) {
        HapticFeedback.mediumImpact();

        // Show appropriate success message based on auto_approval
        final isAutoApproved = result['auto_approved'] == true;
        final status = result['status'];

        Map<String, dynamic> completionResult = {
          'success': true,
          'message': isAutoApproved
              ? '🎉 Verification complete! You\'re now a verified student.'
              : 'Email verified! Your request is under review.',
          'status': status,
          'auto_approved': isAutoApproved,
        };

        widget.onVerificationComplete(completionResult);
        if (mounted) _closeBottomSheet();
      } else {
        setState(() {
          _errorMessage = result['message'];
        });
        HapticFeedback.heavyImpact();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Network error. Please try again.';
        });
        HapticFeedback.heavyImpact();
      }
    }
  }

  Future<void> _resendCode() async {
    HapticFeedback.lightImpact();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await _schoolVerificationService.resendCode();

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (result['success']) {
        HapticFeedback.mediumImpact();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'New code sent!'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = result['message'];
        });
        HapticFeedback.heavyImpact();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to resend code. Please try again.';
        });
        HapticFeedback.heavyImpact();
      }
    }
  }

  void _goBackToEmailForm() {
    HapticFeedback.lightImpact();
    setState(() {
      _isCodeSent = false;
      _errorMessage = null;
      _codeController.clear();
    });
  }

  Future<void> _closeBottomSheet() async {
    await _slideController.reverse();
    if (mounted) Navigator.of(context).pop();
  }

  void _handleCancel() {
    HapticFeedback.lightImpact();
    FocusScope.of(context).unfocus();
    _closeBottomSheet();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);
    final keyboardHeight = mediaQuery.viewInsets.bottom;
    final screenHeight = mediaQuery.size.height;
    final safeAreaBottom = mediaQuery.padding.bottom;
    final screenWidth = mediaQuery.size.width;
    final isSmallScreen = screenWidth < 360;

    return AnimatedBuilder(
      animation: Listenable.merge([_animationController, _slideController]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * screenHeight * 0.3),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Material(
              type: MaterialType.transparency,
              child: Container(
                height: screenHeight,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.4 * _fadeAnimation.value),
                ),
                child: GestureDetector(
                  onTap: () {
                    FocusScope.of(context).unfocus();
                    _handleCancel();
                  },
                  behavior: HitTestBehavior.opaque,
                  child: DraggableScrollableSheet(
                    initialChildSize: keyboardHeight > 0 ? 0.9 : 0.7,
                    minChildSize: 0.5,
                    maxChildSize: 0.95,
                    expand: false,
                    builder: (context, scrollController) {
                      return GestureDetector(
                        onTap: () {}, // Prevent dismissal when tapping content
                        child: Transform.scale(
                          scale: _scaleAnimation.value,
                          child: Container(
                            decoration: BoxDecoration(
                              color: theme.colorScheme.surface,
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(24),
                              ),
                            ),
                            child: Column(
                              children: [
                                // Handle bar
                                Container(
                                  margin: const EdgeInsets.only(top: 12),
                                  width: 32,
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),

                                // Content with proper keyboard handling
                                Expanded(
                                  child: SingleChildScrollView(
                                    controller: scrollController,
                                    keyboardDismissBehavior:
                                        ScrollViewKeyboardDismissBehavior
                                            .onDrag,
                                    padding: EdgeInsets.fromLTRB(
                                      20,
                                      16,
                                      20,
                                      math.max(keyboardHeight, safeAreaBottom) +
                                          20,
                                    ),
                                    child: _isCodeSent
                                        ? _buildCodeVerificationForm(
                                            theme, isSmallScreen)
                                        : _buildEmailForm(theme, isSmallScreen),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeData theme, bool isSmallScreen) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 18),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.secondary,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.primary.withOpacity(0.25),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                _isCodeSent
                    ? Icons.mark_email_read_rounded
                    : Icons.school_rounded,
                color: Colors.white,
                size: isSmallScreen ? 28 : 32,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _isCodeSent ? 'Verify Your Email' : 'School Verification',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: isSmallScreen ? 18 : 22,
                      height: 1.2,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    _isCodeSent
                        ? 'Enter the 6-digit code sent to $_schoolEmail'
                        : 'Connect your school to unlock rankings and compete with classmates',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                      fontSize: isSmallScreen ? 13 : 14,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        // Show detected school info if available
        if (_isCodeSent && _detectedSchoolName != null) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.school_outlined,
                  color: theme.colorScheme.primary,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _detectedSchoolName!,
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                      fontSize: isSmallScreen ? 12 : 13,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildEmailForm(ThemeData theme, bool isSmallScreen) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(theme, isSmallScreen),
          SizedBox(height: isSmallScreen ? 24 : 32),

          // Form fields
          _buildFormFields(theme, isSmallScreen),

          // Error message
          if (_errorMessage != null) ...[
            const SizedBox(height: 16),
            _buildErrorMessage(theme),
          ],

          SizedBox(height: isSmallScreen ? 24 : 32),

          // Action buttons
          _buildActionButtons(theme, isSmallScreen),
        ],
      ),
    );
  }

  Widget _buildCodeVerificationForm(ThemeData theme, bool isSmallScreen) {
    return Form(
      key: _codeFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(theme, isSmallScreen),
          SizedBox(height: isSmallScreen ? 24 : 32),

          // Form fields
          _buildCodeVerificationFields(theme, isSmallScreen),

          // Error message
          if (_errorMessage != null) ...[
            const SizedBox(height: 16),
            _buildErrorMessage(theme),
          ],

          SizedBox(height: isSmallScreen ? 24 : 32),

          // Action buttons
          _buildActionButtons(theme, isSmallScreen),
        ],
      ),
    );
  }

  Widget _buildFormFields(ThemeData theme, bool isSmallScreen) {
    return Column(
      children: [
        // Email field
        _buildTextField(
          controller: _emailController,
          focusNode: _emailFocusNode,
          label: 'School Email (.edu)',
          icon: Icons.alternate_email_rounded,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          onSubmitted: (_) => _schoolNameFocusNode.requestFocus(),
          validator: (value) {
            if (value?.trim().isEmpty ?? true) return 'Email required';
            if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value!)) {
              return 'Invalid email format';
            }
            if (!value.toLowerCase().endsWith('.edu')) {
              return 'Must be a .edu email address';
            }
            return null;
          },
          theme: theme,
          isSmallScreen: isSmallScreen,
        ),
        SizedBox(height: isSmallScreen ? 16 : 20),

        // School name field
        _buildTextField(
          controller: _schoolNameController,
          focusNode: _schoolNameFocusNode,
          label: 'School Name',
          icon: Icons.account_balance_rounded,
          textInputAction: TextInputAction.done,
          onSubmitted: (_) => _submitVerification(),
          validator: (value) {
            if (value?.trim().isEmpty ?? true) return 'School name required';
            if (value!.trim().length < 3)
              return 'Please enter full school name';
            return null;
          },
          theme: theme,
          isSmallScreen: isSmallScreen,
        ),
      ],
    );
  }

  Widget _buildCodeVerificationFields(ThemeData theme, bool isSmallScreen) {
    return Column(
      children: [
        // Code field
        _buildTextField(
          controller: _codeController,
          focusNode: _codeFocusNode,
          label: 'Verification Code',
          icon: Icons.code_rounded,
          keyboardType: TextInputType.number,
          textInputAction: TextInputAction.done,
          onSubmitted: (_) => _verifyCode(),
          validator: (value) {
            if (value?.trim().isEmpty ?? true)
              return 'Verification code required';
            return null;
          },
          theme: theme,
          isSmallScreen: isSmallScreen,
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String label,
    required IconData icon,
    required ThemeData theme,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    Function(String)? onSubmitted,
    String? Function(String?)? validator,
    bool isSmallScreen = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        focusNode: focusNode,
        keyboardType: keyboardType,
        textInputAction: textInputAction,
        onFieldSubmitted: onSubmitted,
        validator: validator,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontSize: isSmallScreen ? 14 : 16,
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
            fontSize: isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(10),
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: isSmallScreen ? 18 : 20,
              color: theme.colorScheme.primary,
            ),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: theme.colorScheme.outline.withOpacity(0.1),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: theme.colorScheme.primary,
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: theme.colorScheme.error,
              width: 1.5,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: theme.colorScheme.error,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: theme.colorScheme.surface.withOpacity(0.8),
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16,
            vertical: isSmallScreen ? 16 : 18,
          ),
          errorStyle: TextStyle(
            color: theme.colorScheme.error,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorMessage(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.error.withOpacity(0.1),
            theme.colorScheme.error.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.error.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: theme.colorScheme.error.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.error_outline_rounded,
              color: theme.colorScheme.error,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.w600,
                fontSize: 13,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme, bool isSmallScreen) {
    return Column(
      children: [
        // Submit button
        Container(
          width: double.infinity,
          height: isSmallScreen ? 48 : 52,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.secondary,
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withOpacity(0.25),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: _isLoading ? null : _submitVerification,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              foregroundColor: Colors.white,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 0,
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _isCodeSent
                            ? Icons.verified_user_rounded
                            : Icons.send_rounded,
                        size: isSmallScreen ? 18 : 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _isCodeSent ? 'Verify Code' : 'Send Verification Code',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 14 : 16,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.3,
                        ),
                      ),
                    ],
                  ),
          ),
        ),

        const SizedBox(height: 12),

        // Secondary button (Cancel/Back or Resend)
        if (_isCodeSent) ...[
          SizedBox(
            width: double.infinity,
            height: isSmallScreen ? 44 : 48,
            child: TextButton(
              onPressed: _isLoading ? null : _resendCode,
              style: TextButton.styleFrom(
                foregroundColor: theme.colorScheme.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: theme.colorScheme.primary.withOpacity(0.3),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.refresh_rounded,
                    size: isSmallScreen ? 16 : 18,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'Resend Code',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 13 : 15,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.3,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            width: double.infinity,
            height: isSmallScreen ? 44 : 48,
            child: TextButton(
              onPressed: _isLoading ? null : _goBackToEmailForm,
              style: TextButton.styleFrom(
                foregroundColor: theme.colorScheme.onSurface.withOpacity(0.7),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Text(
                'Back to Email',
                style: TextStyle(
                  fontSize: isSmallScreen ? 13 : 15,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.3,
                ),
              ),
            ),
          ),
        ] else ...[
          SizedBox(
            width: double.infinity,
            height: isSmallScreen ? 44 : 48,
            child: TextButton(
              onPressed: _isLoading ? null : _handleCancel,
              style: TextButton.styleFrom(
                foregroundColor: theme.colorScheme.onSurface.withOpacity(0.7),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Text(
                'Cancel',
                style: TextStyle(
                  fontSize: isSmallScreen ? 13 : 15,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.3,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
