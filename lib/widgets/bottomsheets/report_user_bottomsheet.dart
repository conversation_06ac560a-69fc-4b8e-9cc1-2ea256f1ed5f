import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import '../../services/moderation_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';

class ReportUserBottomSheet extends StatefulWidget {
  final int userId;
  final String username;
  final VoidCallback? onReported;

  const ReportUserBottomSheet({
    Key? key,
    required this.userId,
    required this.username,
    this.onReported,
  }) : super(key: key);

  @override
  State<ReportUserBottomSheet> createState() => _ReportUserBottomSheetState();

  static Future<void> show(
    BuildContext context, {
    required int userId,
    required String username,
    VoidCallback? onReported,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: true,
      enableDrag: true,
      builder: (context) => ReportUserBottomSheet(
        userId: userId,
        username: username,
        onReported: onReported,
      ),
    );
  }
}

class _ReportUserBottomSheetState extends State<ReportUserBottomSheet>
    with SingleTickerProviderStateMixin {
  late final ModerationService _moderationService;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  List<Map<String, dynamic>> _reportTypes = [];
  Map<String, dynamic>? _selectedReportType;
  bool _isLoadingReportTypes = true;
  bool _isSubmitting = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    final apiService = ApiService();
    final authService = AuthService(apiService);
    _moderationService = ModerationService(apiService, authService);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadReportTypes();
    _animationController.forward();
    // Update PiP params when bottomsheet is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PictureInPicture.updatePiPParams(
        pipParams: PiPParams(
          pipWindowHeight: 125,
          pipWindowWidth: 125,
          bottomSpace: MediaQuery.of(context).size.height *
              0.75, // Match bottomsheet height
          leftSpace: 0,
          rightSpace: 0,
          topSpace: 0,
          maxSize: const Size(125, 125),
          minSize: const Size(125, 125),
          movable: true,
          resizable: false,
          initialCorner: PIPViewCorner.bottomLeft,
        ),
      );
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    // Restore default PiP params when bottomsheet is closed
    PictureInPicture.updatePiPParams(
      pipParams: const PiPParams(
        pipWindowHeight: 125,
        pipWindowWidth: 125,
        bottomSpace: 64,
        leftSpace: 0,
        rightSpace: 0,
        topSpace: 64,
        maxSize: Size(125, 125),
        minSize: Size(125, 125),
        movable: true,
        resizable: false,
        initialCorner: PIPViewCorner.bottomLeft,
      ),
    );
    super.dispose();
  }

  Future<void> _loadReportTypes() async {
    try {
      final reportTypes = await _moderationService.getReportTypes();
      setState(() {
        _reportTypes = reportTypes;
        _isLoadingReportTypes = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load report types';
        _isLoadingReportTypes = false;
      });
    }
  }

  Future<void> _submitReport() async {
    if (_selectedReportType == null || _isSubmitting) return;

    setState(() {
      _isSubmitting = true;
      _error = null;
    });

    try {
      HapticFeedback.mediumImpact();

      final response = await _moderationService.reportUser(
        userId: widget.userId,
        reportTypeId: _selectedReportType!['id'],
      );

      if (response.success) {
        widget.onReported?.call();
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Report submitted for ${widget.username}'),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
              action: SnackBarAction(
                label: 'OK',
                textColor: Colors.white,
                onPressed: () {},
              ),
            ),
          );
        }
      } else {
        setState(() {
          _error = response.message ?? 'Failed to submit report';
        });
      }
    } catch (e) {
      setState(() {
        _error = 'An error occurred while submitting the report';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;

    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, (1 - _fadeAnimation.value) * 100),
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: GestureDetector(
                onTap:
                    () {}, // Prevent dismissing when tapping the sheet itself
                child: Container(
                  height: screenHeight * 0.75,
                  decoration: BoxDecoration(
                    color: theme.scaffoldBackgroundColor,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(24),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Handle bar
                      Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.onSurface.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(2),
                        ),
                        alignment: Alignment.center,
                      ),

                      // Header
                      Padding(
                        padding: const EdgeInsets.fromLTRB(24, 8, 24, 24),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.orange.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: const Icon(
                                Icons.flag_rounded,
                                color: Colors.orange,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Report ${widget.username}',
                                    style: theme.textTheme.titleLarge?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Help us keep BOP Maps safe for everyone',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.colorScheme.onSurface
                                          .withOpacity(0.7),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Content
                      Expanded(
                        child: _isLoadingReportTypes
                            ? _buildLoadingState()
                            : SingleChildScrollView(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 24),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Report type selection
                                    Text(
                                      'What\'s the issue?',
                                      style:
                                          theme.textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    const SizedBox(height: 16),

                                    if (_reportTypes.isNotEmpty)
                                      ..._reportTypes.map((reportType) {
                                        final isSelected =
                                            _selectedReportType?['id'] ==
                                                reportType['id'];
                                        return _buildReportTypeCard(
                                            reportType, isSelected);
                                      }).toList()
                                    else
                                      _buildNoReportTypesState(),

                                    const SizedBox(height: 24),

                                    // Error message
                                    if (_error != null)
                                      Container(
                                        margin:
                                            const EdgeInsets.only(bottom: 16),
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          color: Colors.red.withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                            color: Colors.red.withOpacity(0.3),
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            const Icon(
                                              Icons.error_outline,
                                              color: Colors.red,
                                              size: 20,
                                            ),
                                            const SizedBox(width: 12),
                                            Expanded(
                                              child: Text(
                                                _error!,
                                                style: const TextStyle(
                                                  color: Colors.red,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                      ),

                      // Bottom actions
                      Container(
                        padding: EdgeInsets.fromLTRB(
                          24,
                          16,
                          24,
                          24 + MediaQuery.of(context).padding.bottom,
                        ),
                        decoration: BoxDecoration(
                          color: theme.scaffoldBackgroundColor,
                          border: Border(
                            top: BorderSide(
                              color: theme.colorScheme.outline.withOpacity(0.1),
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed: _isSubmitting
                                    ? null
                                    : () => Navigator.of(context).pop(),
                                style: OutlinedButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  side: BorderSide(
                                    color: theme.colorScheme.primary,
                                  ),
                                  foregroundColor: theme.colorScheme.primary,
                                ),
                                child: const Text('Cancel'),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 2,
                              child: ElevatedButton(
                                onPressed: (_selectedReportType != null &&
                                        !_isSubmitting)
                                    ? _submitReport
                                    : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.orange,
                                  foregroundColor: Colors.white,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 0,
                                ),
                                child: _isSubmitting
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      )
                                    : const Text(
                                        'Submit Report',
                                        style: TextStyle(
                                            fontWeight: FontWeight.w600),
                                      ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildReportTypeCard(
      Map<String, dynamic> reportType, bool isSelected) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedReportType = isSelected ? null : reportType;
          });
          HapticFeedback.selectionClick();
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primary.withOpacity(0.1)
                : theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected
                  ? theme.colorScheme.primary.withOpacity(0.3)
                  : theme.colorScheme.outline.withOpacity(0.1),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : Colors.transparent,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.outline,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 14,
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      reportType['name'] ?? 'Unknown',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? theme.colorScheme.primary : null,
                      ),
                    ),
                    if (reportType['description'] != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        reportType['description'],
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading report options...'),
        ],
      ),
    );
  }

  Widget _buildNoReportTypesState() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'No report options available',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please try again later or contact support.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
