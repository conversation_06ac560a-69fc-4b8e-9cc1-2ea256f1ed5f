import 'package:flutter/material.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import '../../models/music_track.dart';
import '../../theme/spacing.dart';

class CaptionInputBottomSheet extends StatefulWidget {
  final MusicTrack? selectedTrack;
  final TextEditingController controller;
  final String defaultCaption;

  const CaptionInputBottomSheet({
    Key? key,
    required this.selectedTrack,
    required this.controller,
    required this.defaultCaption,
  }) : super(key: key);

  @override
  State<CaptionInputBottomSheet> createState() =>
      _CaptionInputBottomSheetState();
}

class _CaptionInputBottomSheetState extends State<CaptionInputBottomSheet> {
  late FocusNode _focusNode;
  bool _isKeyboardVisible = false;
  final int _maxLength = 280;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Set initial text immediately (already set by parent)
    if (widget.controller.text.isEmpty) {
      widget.controller.text = widget.defaultCaption;
    }

    // Defer text selection and auto-focus to make the bottom sheet appear faster
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        widget.controller.selection = TextSelection(
          baseOffset: 0,
          extentOffset: widget.controller.text.length,
        );
        // Request focus with a tiny delay to allow the bottom sheet to appear first
        Future.microtask(() {
          if (mounted) {
            _focusNode.requestFocus();
          }
        });
      }
    });

    // Update PiP params when bottomsheet is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PictureInPicture.updatePiPParams(
        pipParams: PiPParams(
          pipWindowHeight: 125,
          pipWindowWidth: 125,
          bottomSpace: MediaQuery.of(context).size.height *
              0.6, // Match bottomsheet height
          leftSpace: 0,
          rightSpace: 0,
          topSpace: 0,
          maxSize: const Size(125, 125),
          minSize: const Size(125, 125),
          movable: true,
          resizable: false,
          initialCorner: PIPViewCorner.bottomLeft,
        ),
      );
    });
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    // Restore default PiP params when bottomsheet is closed
    PictureInPicture.updatePiPParams(
      pipParams: const PiPParams(
        pipWindowHeight: 125,
        pipWindowWidth: 125,
        bottomSpace: 64,
        leftSpace: 0,
        rightSpace: 0,
        topSpace: 64,
        maxSize: Size(125, 125),
        minSize: Size(125, 125),
        movable: true,
        resizable: false,
        initialCorner: PIPViewCorner.bottomLeft,
      ),
    );
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isKeyboardVisible = _focusNode.hasFocus;
    });
  }

  Widget _buildCompactTrackInfo() {
    if (widget.selectedTrack == null) return const SizedBox.shrink();

    String imageUrl = widget.selectedTrack!.albumArt;
    if (imageUrl.startsWith('spotify:image:')) {
      final imageId = imageUrl.split(':').last;
      imageUrl = 'https://i.scdn.co/image/$imageId';
    }

    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(Spacing.radiusSmall),
          child: Image.network(
            imageUrl,
            width: 32,
            height: 32,
            fit: BoxFit.cover,
            errorBuilder: (_, __, ___) => Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(Spacing.radiusSmall),
              ),
              child: Icon(
                Icons.music_note,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ),
        const SizedBox(width: Spacing.small),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                widget.selectedTrack!.title,
                style:
                    const TextStyle(fontWeight: FontWeight.w500, fontSize: 13),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                widget.selectedTrack!.artist,
                style: TextStyle(
                  fontSize: 12,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        // Add photo icon button
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(Spacing.radiusSmall),
          ),
          child: IconButton(
            onPressed: _addPhoto,
            icon: Icon(
              Icons.add_photo_alternate_outlined,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            tooltip: 'Add Photo',
            padding: const EdgeInsets.all(6),
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernInput() {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final surfaceColor = Theme.of(context).colorScheme.surface;
    final onSurfaceColor = Theme.of(context).colorScheme.onSurface;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _focusNode.hasFocus
                  ? primaryColor
                  : onSurfaceColor.withOpacity(0.2),
              width: _focusNode.hasFocus ? 2 : 1,
            ),
            boxShadow: _focusNode.hasFocus
                ? [
                    BoxShadow(
                      color: primaryColor.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: TextField(
            controller: widget.controller,
            focusNode: _focusNode,
            autofocus: false, // Now handled manually for better performance
            maxLines: 4,
            minLines: 3,
            maxLength: _maxLength,
            textCapitalization: TextCapitalization.sentences,
            keyboardType: TextInputType.multiline,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => _submitCaption(),
            onChanged: (_) => setState(() {}),
            style: TextStyle(
              fontSize: 16,
              height: 1.4,
              color: onSurfaceColor,
            ),
            decoration: InputDecoration(
              hintText: 'Share your vibe...',
              hintStyle: TextStyle(
                color: onSurfaceColor.withOpacity(0.6),
                fontSize: 16,
                height: 1.4,
              ),
              border: InputBorder.none,
              focusedBorder: InputBorder.none,
              enabledBorder: InputBorder.none,
              errorBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              counterText: '',
              filled: false,
            ),
          ),
        ),
        const SizedBox(height: Spacing.small),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Character count with circular progress
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    value: widget.controller.text.length / _maxLength,
                    backgroundColor: onSurfaceColor.withOpacity(0.1),
                    strokeWidth: 2,
                    color: widget.controller.text.length > _maxLength * 0.8
                        ? Theme.of(context).colorScheme.error
                        : primaryColor,
                  ),
                ),
                Text(
                  '${widget.controller.text.length}',
                  style: TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w500,
                    color: widget.controller.text.length > _maxLength * 0.8
                        ? Theme.of(context).colorScheme.error
                        : primaryColor,
                  ),
                ),
              ],
            ),
            // Drop Pin button
            TextButton.icon(
              onPressed: _submitCaption,
              icon: Icon(Icons.push_pin_rounded, size: 16, color: primaryColor),
              label: Text(
                'Drop Pin',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: primaryColor,
                ),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: Spacing.medium,
                  vertical: Spacing.micro,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _submitCaption() {
    final caption = widget.controller.text.trim();
    Navigator.pop(context, caption.isEmpty ? widget.defaultCaption : caption);
  }

  void _addPhoto() {
    // TODO: Implement photo selection functionality
    // This could open image picker or camera
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.photo_camera, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Text('Photo feature coming soon!'),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return Padding(
      padding: EdgeInsets.only(bottom: keyboardHeight),
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(Spacing.radiusXLarge),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(
                  top: Spacing.small,
                  bottom: Spacing.micro,
                ),
                width: 36,
                height: 4,
                decoration: BoxDecoration(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(Spacing.micro),
                ),
              ),
              // Track info
              Padding(
                padding: const EdgeInsets.fromLTRB(
                  Spacing.large,
                  Spacing.small,
                  Spacing.large,
                  Spacing.medium,
                ),
                child: _buildCompactTrackInfo(),
              ),
              const Divider(height: 1),
              // Caption input
              Padding(
                padding: const EdgeInsets.all(Spacing.large),
                child: _buildModernInput(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
