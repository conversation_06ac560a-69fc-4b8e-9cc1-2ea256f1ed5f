import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import '../../services/api/pin_engagement_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../providers/gamification_provider.dart';

/// Dedicated comment bottom sheet widget for pin comments
///
/// 🎯 FEATURES:
/// - Clean separation of concerns from map screen
/// - Reusable across different screens
/// - Pin-specific comment system
/// - Real API integration with ultra-fast endpoints
/// - Beautiful modern design
/// - Proper keyboard handling
class CommentBottomSheet extends StatefulWidget {
  final int pinId;
  final String? pinTitle;
  final VoidCallback? onCommentPosted;

  const CommentBottomSheet({
    super.key,
    required this.pinId,
    this.pinTitle,
    this.onCommentPosted,
  });

  @override
  State<CommentBottomSheet> createState() => _CommentBottomSheetState();

  /// Static method to show the comment bottom sheet
  static void show(
    BuildContext context, {
    required int pinId,
    String? pinTitle,
    VoidCallback? onCommentPosted,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      useSafeArea: false,
      builder: (context) => CommentBottomSheet(
        pinId: pinId,
        pinTitle: pinTitle,
        onCommentPosted: onCommentPosted,
      ),
    );
  }
}

class _CommentBottomSheetState extends State<CommentBottomSheet>
    with TickerProviderStateMixin {
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _inputKey = GlobalKey(); // Key for the input container

  bool _isPosting = false;
  bool _isKeyboardVisible = false;
  bool _isLoadingComments = true;
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  // ⚡ NEW: Real comment data
  List<Comment> _comments = [];
  int _commentCount = 0;
  bool _canComment = true;
  String? _trackTitle;
  String? _trackArtist;
  String? _errorMessage;
  PinEngagementService? _engagementService;

  @override
  void initState() {
    super.initState();

    // Initialize services
    _initializeServices();

    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start entrance animation
    _animationController.forward();

    // Listen to keyboard visibility and scroll to input when focused
    _commentFocusNode.addListener(() {
      setState(() {
        _isKeyboardVisible = _commentFocusNode.hasFocus;
      });

      // Scroll to input field when keyboard appears
      if (_commentFocusNode.hasFocus) {
        _scrollToInput();
      }
    });

    // ⚡ Load real comment data
    _loadComments();

    // Update PiP params when bottomsheet is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PictureInPicture.updatePiPParams(
        pipParams: PiPParams(
          pipWindowHeight: 125,
          pipWindowWidth: 125,
          bottomSpace: MediaQuery.of(context).size.height *
              0.7, // Match bottomsheet height
          leftSpace: 0,
          rightSpace: 0,
          topSpace: 0,
          maxSize: const Size(125, 125),
          minSize: const Size(125, 125),
          movable: true,
          resizable: false,
          initialCorner: PIPViewCorner.bottomLeft,
        ),
      );
    });
  }

  void _initializeServices() {
    final apiService = context.read<ApiService>();
    final authService = context.read<AuthService>();
    _engagementService = PinEngagementService(apiService, authService);
  }

  /// ⚡ Load comments from API using ultra-fast endpoint
  Future<void> _loadComments() async {
    if (_engagementService == null) return;

    final stopwatch = Stopwatch()..start();

    try {
      setState(() {
        _isLoadingComments = true;
        _errorMessage = null;
      });

      // ⚡ Use ultra-fast comment info endpoint
      final commentInfo =
          await _engagementService!.getQuickCommentInfo(widget.pinId);

      stopwatch.stop();
      if (kDebugMode) {
        print(
            '⚡ CommentBottomSheet: Comments loaded in ${stopwatch.elapsedMilliseconds}ms');
      }

      if (mounted) {
        setState(() {
          _comments = commentInfo.comments;
          _commentCount = commentInfo.commentCount;
          _canComment = commentInfo.canComment;
          _trackTitle = commentInfo.trackTitle ?? widget.pinTitle;
          _trackArtist = commentInfo.trackArtist;
          _isLoadingComments = false;
        });
      }
    } catch (e) {
      stopwatch.stop();
      if (mounted) {
        setState(() {
          _isLoadingComments = false;
          _errorMessage = 'Failed to load comments: $e';
        });
        if (kDebugMode) {
          print(
              '❌ Failed to load comments after ${stopwatch.elapsedMilliseconds}ms: $e');
        }
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _commentController.dispose();
    _commentFocusNode.dispose();
    _scrollController.dispose();
    // Restore default PiP params when bottomsheet is closed
    PictureInPicture.updatePiPParams(
      pipParams: const PiPParams(
        pipWindowHeight: 125,
        pipWindowWidth: 125,
        bottomSpace: 64,
        leftSpace: 0,
        rightSpace: 0,
        topSpace: 64,
        maxSize: Size(125, 125),
        minSize: Size(125, 125),
        movable: true,
        resizable: false,
        initialCorner: PIPViewCorner.bottomLeft,
      ),
    );
    super.dispose();
  }

  // Scroll to input field to ensure it's visible above keyboard
  void _scrollToInput() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (_scrollController.hasClients && mounted) {
        final context = _inputKey.currentContext;
        if (context != null) {
          // Scroll to the bottom to show the input field
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOutCubic,
          );
        }
      }
    });
  }

  void _handleCommentPost() {
    final commentText = _commentController.text.trim();
    if (commentText.isEmpty || !_canComment || _engagementService == null)
      return;

    // Dismiss keyboard first
    _commentFocusNode.unfocus();

    setState(() {
      _isPosting = true;
    });

    // Haptic feedback
    HapticFeedback.mediumImpact();

    // ⚡ Use real API call instead of mock delay
    _postCommentToAPI(commentText);
  }

  /// ⚡ Post comment using ultra-fast API endpoint
  Future<void> _postCommentToAPI(String commentText) async {
    final stopwatch = Stopwatch()..start();

    try {
      // ⚡ Use ultra-fast comment post endpoint
      final commentPost =
          await _engagementService!.postComment(widget.pinId, commentText);

      stopwatch.stop();
      if (kDebugMode) {
        print(
            '⚡ CommentBottomSheet: Comment posted in ${stopwatch.elapsedMilliseconds}ms');
      }

      if (mounted) {
        // Clear input and update state
        _commentController.clear();

        // ⚡ Add new comment to the list (it's at the top since backend returns newest first)
        setState(() {
          _comments.insert(
              0, commentPost.newComment); // Add to beginning for newest first
          _commentCount = commentPost.commentCount;
          _isPosting = false;
        });

        // Success haptic
        HapticFeedback.lightImpact();

        widget.onCommentPosted?.call();
        
        // 🎯 TRACK GAMIFICATION: Comment given for social challenges
        try {
          final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
          await gamificationProvider.handleCommentGiven(widget.pinId, commentText);
          print('🎯 Gamification: Comment tracked successfully');
        } catch (e) {
          print('⚠️ Gamification tracking failed for comment: $e');
        }

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green[400],
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text('Comment posted successfully!'),
                ),
              ],
            ),
            backgroundColor: Colors.green[50],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.all(16),
            duration: const Duration(seconds: 2),
          ),
        );

        // Auto-scroll to show new comment at top
        Future.delayed(const Duration(milliseconds: 100), () {
          if (_scrollController.hasClients && mounted) {
            _scrollController.animateTo(
              0, // Scroll to top to show new comment
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOutCubic,
            );
          }
        });
      }
    } catch (e) {
      stopwatch.stop();
      if (mounted) {
        setState(() {
          _isPosting = false;
        });

        if (kDebugMode) {
          print(
              '❌ Failed to post comment after ${stopwatch.elapsedMilliseconds}ms: $e');
        }

        // Error haptic
        HapticFeedback.heavyImpact();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Colors.red[400],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('Failed to post comment: $e'),
                ),
              ],
            ),
            backgroundColor: Colors.red[50],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.all(16),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);
    final keyboardHeight = mediaQuery.viewInsets.bottom;
    final screenHeight = mediaQuery.size.height;
    final safeAreaBottom = mediaQuery.padding.bottom;

    // Calculate proper height - default to 75% of screen, adjust for keyboard
    double sheetHeight;
    if (_isKeyboardVisible && keyboardHeight > 0) {
      // When keyboard is visible, take most of the screen but leave space for status bar
      sheetHeight = screenHeight - mediaQuery.padding.top - 40;
    } else {
      // Normal state - 75% of screen height (slightly taller for better space usage)
      sheetHeight = screenHeight * 0.75;
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Align(
          alignment: Alignment.bottomCenter,
          child: Transform.translate(
            offset: Offset(0, _slideAnimation.value * sheetHeight),
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: Container(
                width: double.infinity,
                height: sheetHeight,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      theme.scaffoldBackgroundColor,
                      theme.scaffoldBackgroundColor.withOpacity(0.98),
                    ],
                  ),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.15),
                      blurRadius: 30,
                      offset: const Offset(0, -8),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: theme.colorScheme.primary.withOpacity(0.08),
                      blurRadius: 50,
                      offset: const Offset(0, -12),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Compact drag handle
                    Container(
                      margin: const EdgeInsets.only(top: 8, bottom: 12),
                      child: Container(
                        width: 36,
                        height: 4,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              theme.colorScheme.primary.withOpacity(0.4),
                              theme.colorScheme.primary.withOpacity(0.2),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),

                    // Compact header
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            theme.colorScheme.primary.withOpacity(0.08),
                            theme.colorScheme.primary.withOpacity(0.04),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: theme.colorScheme.primary.withOpacity(0.15),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color:
                                  theme.colorScheme.primary.withOpacity(0.12),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Icon(
                              Icons.chat_bubble_rounded,
                              color: theme.colorScheme.primary,
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Comments',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (_trackTitle != null) ...[
                                  const SizedBox(height: 2),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.music_note,
                                        size: 12,
                                        color: theme.colorScheme.onSurface
                                            .withOpacity(0.6),
                                      ),
                                      const SizedBox(width: 4),
                                      Expanded(
                                        child: Text(
                                          _trackTitle!,
                                          style: theme.textTheme.bodySmall
                                              ?.copyWith(
                                            color: theme.colorScheme.onSurface
                                                .withOpacity(0.7),
                                            fontWeight: FontWeight.w500,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color:
                                  theme.colorScheme.primary.withOpacity(0.12),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color:
                                    theme.colorScheme.primary.withOpacity(0.25),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              'Pin ${widget.pinId}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w600,
                                fontSize: 11,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Main content area - split between scrollable comments and fixed input
                    Expanded(
                      child: Column(
                        children: [
                          // Scrollable comments area only
                          Expanded(
                            child: SingleChildScrollView(
                              controller: _scrollController,
                              child: Container(
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                decoration: BoxDecoration(
                                  color: theme.cardColor.withOpacity(0.6),
                                  borderRadius: BorderRadius.circular(14),
                                  border: Border.all(
                                    color: theme.dividerColor.withOpacity(0.12),
                                    width: 1,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(
                                      12), // Back to normal padding
                                  child: Column(
                                    children: [
                                      // ⚡ Real comments from API
                                      if (_isLoadingComments) ...[
                                        // Loading state
                                        _buildLoadingState(theme),
                                      ] else if (_errorMessage != null) ...[
                                        // Error state
                                        _buildErrorState(theme),
                                      ] else if (_comments.isEmpty) ...[
                                        // Empty state
                                        _buildEmptyState(theme),
                                      ] else ...[
                                        // Real comments list
                                        ..._comments
                                            .asMap()
                                            .entries
                                            .map((entry) {
                                          final index = entry.key;
                                          final comment = entry.value;
                                          return Column(
                                            children: [
                                              if (index > 0)
                                                const SizedBox(height: 8),
                                              _buildCommentTile(
                                                profilePicUrl: comment.user
                                                    ?.profilePicUrl, // Use real profile pic
                                                username:
                                                    comment.user?.username ??
                                                        'Unknown User',
                                                comment: comment.text,
                                                timestamp: comment.timeAgo,
                                                isVerified:
                                                    false, // TODO: Add verification status to User model
                                                theme: theme,
                                              ),
                                            ],
                                          );
                                        }).toList(),
                                      ],
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Fixed input field at bottom
                          Container(
                            key: _inputKey,
                            margin: const EdgeInsets.symmetric(horizontal: 16),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  theme.cardColor,
                                  theme.cardColor.withOpacity(0.95),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(18),
                              border: Border.all(
                                color: _isKeyboardVisible
                                    ? theme.colorScheme.primary.withOpacity(0.3)
                                    : theme.dividerColor.withOpacity(0.2),
                                width: _isKeyboardVisible ? 2 : 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: theme.shadowColor.withOpacity(0.08),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                                if (_isKeyboardVisible)
                                  BoxShadow(
                                    color: theme.colorScheme.primary
                                        .withOpacity(0.12),
                                    blurRadius: 16,
                                    offset: const Offset(0, 0),
                                  ),
                              ],
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                // Compact user avatar
                                Container(
                                  width: 32,
                                  height: 32,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        theme.colorScheme.primary,
                                        theme.colorScheme.primary
                                            .withOpacity(0.8),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: theme.colorScheme.primary
                                            .withOpacity(0.3),
                                        blurRadius: 6,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.person,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                                const SizedBox(width: 10),

                                // Compact text input
                                Expanded(
                                  child: Container(
                                    constraints: const BoxConstraints(
                                      minHeight: 36,
                                      maxHeight: 100,
                                    ),
                                    child: TextField(
                                      controller: _commentController,
                                      focusNode: _commentFocusNode,
                                      decoration: InputDecoration(
                                        hintText: 'Share your thoughts...',
                                        hintStyle: theme.textTheme.bodyMedium
                                            ?.copyWith(
                                          color: theme.colorScheme.onSurface
                                              .withOpacity(0.5),
                                          fontSize: 14,
                                        ),
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(16),
                                          borderSide: BorderSide.none,
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(16),
                                          borderSide: BorderSide.none,
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(16),
                                          borderSide: BorderSide.none,
                                        ),
                                        filled: true,
                                        fillColor: theme.colorScheme.surface
                                            .withOpacity(0.8),
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 8,
                                        ),
                                        counterText: '',
                                        isDense: true,
                                      ),
                                      style:
                                          theme.textTheme.bodyMedium?.copyWith(
                                        height: 1.3,
                                        fontSize: 14,
                                      ),
                                      maxLines: null,
                                      maxLength: 500,
                                      enabled: !_isPosting && _canComment,
                                      onSubmitted: (_) => _handleCommentPost(),
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      keyboardType: TextInputType.multiline,
                                      textInputAction: TextInputAction.newline,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),

                                // Compact send button
                                Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(18),
                                    onTap: (_isPosting || !_canComment)
                                        ? null
                                        : _handleCommentPost,
                                    child: AnimatedContainer(
                                      duration:
                                          const Duration(milliseconds: 200),
                                      width: 36,
                                      height: 36,
                                      decoration: BoxDecoration(
                                        gradient: (_isPosting || !_canComment)
                                            ? LinearGradient(
                                                colors: [
                                                  theme.disabledColor,
                                                  theme.disabledColor
                                                      .withOpacity(0.8),
                                                ],
                                              )
                                            : LinearGradient(
                                                colors: [
                                                  theme.colorScheme.primary,
                                                  theme.colorScheme.primary
                                                      .withOpacity(0.8),
                                                ],
                                              ),
                                        borderRadius: BorderRadius.circular(18),
                                        boxShadow: (_isPosting || !_canComment)
                                            ? null
                                            : [
                                                BoxShadow(
                                                  color: theme
                                                      .colorScheme.primary
                                                      .withOpacity(0.4),
                                                  blurRadius: 8,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ],
                                      ),
                                      child: _isPosting
                                          ? SizedBox(
                                              width: 16,
                                              height: 16,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                        Color>(
                                                  Colors.white,
                                                ),
                                              ),
                                            )
                                          : Icon(
                                              Icons.send_rounded,
                                              color: Colors.white,
                                              size: 16,
                                            ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Bottom padding for keyboard/safe area
                          SizedBox(
                            height: _isKeyboardVisible
                                ? keyboardHeight + 8
                                : safeAreaBottom > 0
                                    ? safeAreaBottom
                                    : 8,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCommentTile({
    required String? profilePicUrl,
    required String username,
    required String comment,
    required String timestamp,
    required bool isVerified,
    required ThemeData theme,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.cardColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: theme.dividerColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary.withOpacity(0.8),
                  theme.colorScheme.secondary.withOpacity(0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: profilePicUrl != null
                ? ClipOval(
                    child: Image.network(
                      profilePicUrl,
                      fit: BoxFit.cover,
                      width: 32,
                      height: 32,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          width: 32,
                          height: 32,
                          color:
                              theme.colorScheme.surfaceVariant.withOpacity(0.3),
                          child: Center(
                            child: SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color:
                                    theme.colorScheme.primary.withOpacity(0.5),
                              ),
                            ),
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 16,
                        );
                      },
                    ),
                  )
                : Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 16,
                  ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      username,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                        fontSize: 13,
                      ),
                    ),
                    if (isVerified) ...[
                      const SizedBox(width: 3),
                      Icon(
                        Icons.verified,
                        size: 13,
                        color: theme.colorScheme.primary,
                      ),
                    ],
                    const Spacer(),
                    Text(
                      timestamp,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  comment,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    height: 1.3,
                    color: theme.colorScheme.onSurface.withOpacity(0.8),
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary.withOpacity(0.1),
                  theme.colorScheme.primary.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              Icons.chat_bubble_outline_rounded,
              size: 28,
              color: theme.colorScheme.primary.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'No comments yet',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 6),
          Text(
            'Be the first to share your thoughts!',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
              height: 1.3,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Column(
      children: [
        // Skeleton comment items
        ..._buildSkeletonComments(theme),
      ],
    );
  }

  List<Widget> _buildSkeletonComments(ThemeData theme) {
    return List.generate(3, (index) {
      return Column(
        children: [
          if (index > 0) const SizedBox(height: 8),
          _buildSkeletonComment(theme),
        ],
      );
    });
  }

  Widget _buildSkeletonComment(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.cardColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: theme.dividerColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Skeleton avatar
          _buildShimmerContainer(
            width: 32,
            height: 32,
            borderRadius: 16,
            theme: theme,
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Skeleton username
                    _buildShimmerContainer(
                      width: 80,
                      height: 12,
                      borderRadius: 6,
                      theme: theme,
                    ),
                    const Spacer(),
                    // Skeleton timestamp
                    _buildShimmerContainer(
                      width: 30,
                      height: 10,
                      borderRadius: 5,
                      theme: theme,
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                // Skeleton comment text - varying widths for realism
                _buildShimmerContainer(
                  width: double.infinity,
                  height: 11,
                  borderRadius: 5.5,
                  theme: theme,
                ),
                const SizedBox(height: 4),
                _buildShimmerContainer(
                  width: MediaQuery.of(context).size.width * 0.6, // 60% width
                  height: 11,
                  borderRadius: 5.5,
                  theme: theme,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerContainer({
    required double width,
    required double height,
    required double borderRadius,
    required ThemeData theme,
  }) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
            gradient: LinearGradient(
              begin: Alignment(-1.0, -0.3),
              end: Alignment(1.0, 0.3),
              colors: [
                theme.colorScheme.surfaceVariant.withOpacity(0.3),
                theme.colorScheme.surfaceVariant.withOpacity(0.1),
                theme.colorScheme.surfaceVariant.withOpacity(0.3),
              ],
              stops: [
                _animationController.value - 0.3,
                _animationController.value,
                _animationController.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary.withOpacity(0.1),
                  theme.colorScheme.primary.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              Icons.error,
              size: 28,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Failed to load comments',
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 6),
          Text(
            _errorMessage!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
              height: 1.3,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
