import 'package:flutter/material.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import 'package:provider/provider.dart';
import 'package:geocoding/geocoding.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/music_track.dart';
import '../../models/collection_model.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../services/collection_service.dart';
import '../../utils/event_bus.dart';
import '../../providers/map_provider.dart';
import '../../providers/pin_provider.dart';
import '../../screens/ar/ar_pin_placement_screen.dart';
import '../../screens/map/snapchat_style_map_screen.dart';
import '../../widgets/common/cloudinary_image.dart';
import '../../utils/text_encoding_utils.dart';
import 'package:flutter/foundation.dart';

class CollectionSelectorBottomSheet extends StatefulWidget {
  final MusicTrack track;

  const CollectionSelectorBottomSheet({
    Key? key,
    required this.track,
  }) : super(key: key);

  @override
  State<CollectionSelectorBottomSheet> createState() =>
      _CollectionSelectorBottomSheetState();
}

class _CollectionSelectorBottomSheetState
    extends State<CollectionSelectorBottomSheet> {
  @override
  void initState() {
    super.initState();
    // Update PiP params when bottomsheet is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PictureInPicture.updatePiPParams(
        pipParams: PiPParams(
          pipWindowHeight: 125,
          pipWindowWidth: 125,
          bottomSpace: MediaQuery.of(context).size.height *
              0.7, // Match bottomsheet height
          leftSpace: 0,
          rightSpace: 0,
          topSpace: 0,
          maxSize: const Size(125, 125),
          minSize: const Size(125, 125),
          movable: true,
          resizable: false,
          initialCorner: PIPViewCorner.bottomLeft,
        ),
      );
    });
  }

  @override
  void dispose() {
    // Restore default PiP params when bottomsheet is closed
    PictureInPicture.updatePiPParams(
      pipParams: const PiPParams(
        pipWindowHeight: 125,
        pipWindowWidth: 125,
        bottomSpace: 64,
        leftSpace: 0,
        rightSpace: 0,
        topSpace: 64,
        maxSize: Size(125, 125),
        minSize: Size(125, 125),
        movable: true,
        resizable: false,
        initialCorner: PIPViewCorner.bottomLeft,
      ),
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      margin: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 20),
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                children: [
                  Icon(
                    Icons.collections_bookmark,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Add to Collection',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            // Collections section
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: _buildCollectionList(context, widget.track),
                    ),
                  ],
                ),
              ),
            ),
            // Music service buttons at the bottom
            _buildMusicServiceButtons(context, widget.track),
          ],
        ),
      ),
    );
  }

  /// Lazy loads the user's collections and builds the list UI
  Widget _buildCollectionList(BuildContext context, MusicTrack track) {
    return FutureBuilder<List<Collection>>(
      future: _fetchUserCollections(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline,
                    size: 48,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.5)),
                const SizedBox(height: 16),
                Text('Failed to load collections',
                    style: TextStyle(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.7))),
              ],
            ),
          );
        }

        final collections = snapshot.data ?? [];

        if (collections.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.collections_bookmark_outlined,
                    size: 48,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.5)),
                const SizedBox(height: 16),
                Text('No collections found',
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.7))),
                const SizedBox(height: 8),
                Text('Create a collection to organize your music',
                    style: TextStyle(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.5))),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          itemCount: collections.length,
          itemBuilder: (context, index) {
            final collection = collections[index];

            if (kDebugMode) {
              print('📋 [CollectionSelector] Building collection item $index:');
              print('   Collection ID: ${collection.id}');
              print('   Collection Name: ${collection.name}');
              print('   Cover Image URLs: ${collection.coverImageUrls}');
              print(
                  '   Cover Image URLs length: ${collection.coverImageUrls.length}');
              print('   Primary Color: ${collection.primaryColor}');
              print('   Is Public: ${collection.isPublic}');
            }

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                    color: Theme.of(context).dividerColor.withOpacity(0.3)),
                gradient: collection.isPublic
                    ? null
                    : LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.purple.withOpacity(0.05),
                          Colors.purple.withOpacity(0.02),
                        ],
                      ),
              ),
              child: ListTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                leading: Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: (collection.primaryColor ??
                            Theme.of(context).colorScheme.primary)
                        .withOpacity(0.1),
                    border: collection.isPublic
                        ? null
                        : Border.all(
                            color: Colors.purple.withOpacity(0.3), width: 1.5),
                  ),
                  child: collection.coverImageUrls.isNotEmpty
                      ? _buildOptimizedImage(
                          collection.coverImageUrls.first,
                          collection.primaryColor ??
                              Theme.of(context).colorScheme.primary)
                      : _buildDefaultIcon(collection.primaryColor ??
                          Theme.of(context).colorScheme.primary),
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        TextEncodingUtils.cleanCollectionText(collection.name,
                            isDescription: false),
                        style: const TextStyle(
                            fontWeight: FontWeight.w600, fontSize: 16),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (!collection.isPublic) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.purple.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                              color: Colors.purple.withOpacity(0.3), width: 1),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.lock,
                                size: 12, color: Colors.purple.shade700),
                            const SizedBox(width: 2),
                            Text(
                              'Private',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: Colors.purple.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ] else ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.green.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                              color: Colors.green.withOpacity(0.3), width: 1),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.public,
                                size: 12, color: Colors.green.shade700),
                            const SizedBox(width: 2),
                            Text(
                              'Public',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: Colors.green.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Text(
                      '${collection.itemCount} songs',
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                      ),
                    ),
                    if (!collection.isPublic) ...[
                      const SizedBox(height: 2),
                      Text(
                        'Virtual pins only',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.purple.withOpacity(0.7),
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
                trailing: Icon(Icons.add_circle_outline,
                    color: Theme.of(context).colorScheme.primary),
                onTap: () =>
                    _addTrackToCollection(context, widget.track, collection),
              ),
            );
          },
        );
      },
    );
  }

  /// Fetches the current user's collections from the backend
  Future<List<Collection>> _fetchUserCollections() async {
    try {
      final apiService = ApiService();
      final collectionService =
          CollectionService(apiService, AuthService(apiService));
      return await collectionService.getMyCollections();
    } catch (e) {
      debugPrint('Error fetching collections: $e');
      return [];
    }
  }

  /// Checks if a track already exists as a pin
  Future<bool> _isTrackAlreadyPin(MusicTrack track) async {
    try {
      // Get the map provider to check existing pins
      final mapProvider = Provider.of<MapProvider>(context, listen: false);
      final existingPins = mapProvider.pins;

      // Check if any existing pin matches this track
      return existingPins.any((pin) =>
          pin['track_title']?.toLowerCase() == track.title.toLowerCase() &&
          pin['track_artist']?.toLowerCase() == track.artist.toLowerCase());
    } catch (e) {
      debugPrint('Error checking if track is already a pin: $e');
      return false;
    }
  }

  /// Adds a track to the chosen collection and provides user feedback
  Future<void> _addTrackToCollection(BuildContext sheetContext,
      MusicTrack track, Collection collection) async {
    // Close the bottom sheet first
    Navigator.pop(sheetContext);

    // Get the root context before showing any dialogs
    final rootContext = Navigator.of(sheetContext, rootNavigator: true).context;

    if (!collection.isPublic) {
      // Private collection – create a virtual pin
      _addToPrivateCollection(rootContext, track, collection);
    } else {
      // Public collection – check if there's a currently playing pin first
      final pinProvider = Provider.of<PinProvider>(rootContext, listen: false);
      
      if (pinProvider.hasCurrentlyPlayingPin) {
        // User has a currently playing pin - add it directly without AR placement
        await _addCurrentlyPlayingPinToCollection(rootContext, pinProvider, collection);
        return;
      }
      
      // No currently playing pin - check if song is already a pin first
      final isAlreadyPin = await _isTrackAlreadyPin(track);

      if (isAlreadyPin) {
        ScaffoldMessenger.of(rootContext).showSnackBar(
          SnackBar(
            content: Text('"${track.title}" is already a pin on the map'),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
        return;
      }

      // Open AR pin placement screen for public collections
      _openARPinPlacement(rootContext, track, collection);
    }
  }

  /// Adds the currently playing pin directly to a public collection
  Future<void> _addCurrentlyPlayingPinToCollection(
      BuildContext rootContext, PinProvider pinProvider, Collection collection) async {
    try {
      // Show loading dialog
      showDialog(
        context: rootContext,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      final pinData = pinProvider.currentlyPlayingPinData!;
      final pinIdString = pinProvider.currentlyPlayingPinId!;
      
      // Convert pin ID to integer
      final pinId = int.tryParse(pinIdString);
      if (pinId == null) {
        throw Exception('Invalid pin ID: $pinIdString');
      }
      
      debugPrint('🎵 [CollectionSelector] Adding currently playing pin to collection:');
      debugPrint('   Pin ID: $pinId');
      debugPrint('   Collection: ${collection.name} (ID: ${collection.id})');
      debugPrint('   Pin data: ${pinData['title'] ?? pinData['track_title']} by ${pinData['artist'] ?? pinData['track_artist']}');

      final apiService = ApiService();
      final collectionService = CollectionService(apiService, AuthService(apiService));

      // Use the existing addPinToCollection method instead of creating a new pin
      final updatedCollection = await collectionService.addPinToCollection(
        collection.id,
        pinId,
      );

      // Check if the root context is still valid before proceeding
      if (!rootContext.mounted) return;

      Navigator.pop(rootContext); // Close loading dialog

      String message;
      Color backgroundColor;

      if (updatedCollection != null) {
        message = '🎵 Added currently playing pin to "${TextEncodingUtils.cleanCollectionText(collection.name, isDescription: false)}"! 🎉';
        backgroundColor = Colors.green;
        
        // Notify listeners of the update
        EventBus().emit(CollectionUpdatedEvent(collection.id.toString()));
      } else {
        message = 'Failed to add pin to collection';
        backgroundColor = Colors.red;
      }

      ScaffoldMessenger.of(rootContext).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 4),
        ),
      );
    } catch (e) {
      debugPrint('❌ Error adding currently playing pin to collection: $e');
      
      // Check if the root context is still valid before showing error
      if (!rootContext.mounted) return;

      Navigator.pop(rootContext); // Close loading dialog
      ScaffoldMessenger.of(rootContext).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// Handles adding track to private collection (virtual pins)
  Future<void> _addToPrivateCollection(
      BuildContext rootContext, MusicTrack track, Collection collection) async {
    // Debug logging to understand the track data we're working with
    if (kDebugMode) {
      print('🎵 [CollectionSelector] Adding track to private collection:');
      print('   Track title: ${track.title}');
      print('   Track artist: ${track.artist}');
      print('   Track service: ${track.service}');
      print('   Track albumArt: ${track.albumArt}');
      print('   Track albumArtUrl: ${track.albumArtUrl}');
      print('   Track URL: ${track.url}');
      print('   Track URI: ${track.uri}');
      print('   Collection: ${collection.name} (ID: ${collection.id})');
    }

    // Show loading indicator
    showDialog(
      context: rootContext,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );

    try {
      final apiService = ApiService();
      final collectionService =
          CollectionService(apiService, AuthService(apiService));

      // Try to get location name for context, but don't require it for virtual pins
      String description = 'Added to collection';
      String? locationName;
      try {
        final mapProvider =
            Provider.of<MapProvider>(rootContext, listen: false);
        final currentPos = mapProvider.currentPosition;
        if (currentPos != null) {
          locationName =
              await _getLocationName(currentPos.latitude, currentPos.longitude);
          description = 'Added from $locationName';
        }
      } catch (e) {
        debugPrint('Could not get location for virtual pin description: $e');
      }

      final resolvedTrackUrl = _resolveTrackUrl(track);
      final artworkUrl = _getValidArtworkUrl(
          track.albumArt.isNotEmpty ? track.albumArt : track.albumArtUrl);

      if (kDebugMode) {
        print('📤 [CollectionSelector] Adding track to private collection:');
        print('   Collection: ${collection.name} (ID: ${collection.id})');
        print('   Track: ${track.title} by ${track.artist}');
        print('   Service: ${track.service}');
        print('   Resolved URL: $resolvedTrackUrl');
        print('   Artwork URL: $artworkUrl (${artworkUrl.length} chars)');
        print('   Description: $description');
      }

      final result =
          await collectionService.addVirtualTrackToCollectionWithDetails(
        collectionId: collection.id,
        trackTitle: track.title,
        trackArtist: track.artist,
        trackUrl: resolvedTrackUrl,
        service: track.service,
        album: track.album,
        artworkUrl: artworkUrl,
        durationMs: track.durationMs ?? 0,
        description: description,
        locationName: locationName,
      );

      // Check if the root context is still valid before proceeding
      if (!rootContext.mounted) return;

      Navigator.pop(rootContext); // Close loading dialog

      final success = result['success'] == true;
      String message;
      Color backgroundColor;

      if (success) {
        message =
            'Added "${track.title}" to "${TextEncodingUtils.cleanCollectionText(collection.name, isDescription: false)}" (virtual pin)';
        backgroundColor = Colors.green;
      } else {
        // Handle specific error cases
        final statusCode = result['statusCode'];
        if (statusCode == 409) {
          message = 'This track is already in the collection';
          backgroundColor = Colors.orange;
        } else {
          message = result['message'] ?? 'Failed to add track to collection';
          backgroundColor = Colors.red;
        }
      }

      ScaffoldMessenger.of(rootContext).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 3),
        ),
      );

      // Notify listeners of the update
      if (success) {
        EventBus().emit(CollectionUpdatedEvent(collection.id.toString()));
      }
    } catch (e) {
      // Check if the root context is still valid before showing error
      if (!rootContext.mounted) return;

      Navigator.pop(rootContext); // Close loading dialog
      ScaffoldMessenger.of(rootContext).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// Opens AR pin placement screen for public collections
  void _openARPinPlacement(
      BuildContext rootContext, MusicTrack track, Collection collection) {
    // Get current location for AR
    final mapProvider = Provider.of<MapProvider>(rootContext, listen: false);
    final currentPos = mapProvider.currentPosition;

    // Navigate to AR screen
    Navigator.push(
      rootContext,
      MaterialPageRoute(
        fullscreenDialog: true,
        builder: (context) => ARPinPlacementScreen(
          initialLatitude: currentPos?.latitude,
          initialLongitude: currentPos?.longitude,
          selectedTrack: track,
        ),
      ),
    ).then((success) async {
      if (success == true) {
        // Pin was successfully created, now add it to the collection
        await _addPinToCollection(rootContext, track, collection);

        // Refresh the map pins
        mapProvider.refreshPins();

        // Force map style reload to show new pin immediately
        final mapState =
            rootContext.findAncestorStateOfType<SnapchatStyleMapScreenState>();
        mapState?.reloadMapStyle();

        // Show success message
        ScaffoldMessenger.of(rootContext).showSnackBar(
          SnackBar(
            content: Text(
                '🎵 Pin created and added to "${TextEncodingUtils.cleanCollectionText(collection.name, isDescription: false)}"! 🎉'),
            duration: const Duration(seconds: 4),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    });
  }

  /// Adds the newly created pin to the collection
  Future<void> _addPinToCollection(
      BuildContext rootContext, MusicTrack track, Collection collection) async {
    try {
      final apiService = ApiService();
      final collectionService =
          CollectionService(apiService, AuthService(apiService));
      final mapProvider = Provider.of<MapProvider>(rootContext, listen: false);
      final currentPos = mapProvider.currentPosition;

      if (currentPos == null) {
        debugPrint(
            'No current position available for adding pin to collection');
        return;
      }

      // Get location name for better context
      final locationName =
          await _getLocationName(currentPos.latitude, currentPos.longitude);

      final resolvedTrackUrl = _resolveTrackUrl(track);
      final validArtworkUrl = _getValidArtworkUrl(track.albumArt);

      if (kDebugMode) {
        print('📤 [CollectionSelector] Adding track to public collection:');
        print('   Collection: ${collection.name} (ID: ${collection.id})');
        print('   Track: ${track.title} by ${track.artist}');
        print('   Service: ${track.service}');
        print('   Resolved URL: $resolvedTrackUrl');
        print(
            '   Artwork URL: $validArtworkUrl (${validArtworkUrl.length} chars)');
        print('   Location: $locationName');
      }

      final success = await collectionService.addTrackToCollection(
        collectionId: collection.id,
        trackTitle: track.title,
        trackArtist: track.artist,
        trackUrl: resolvedTrackUrl,
        service: track.service,
        latitude: currentPos.latitude,
        longitude: currentPos.longitude,
        album: track.album,
        artworkUrl: validArtworkUrl,
        durationMs: track.durationMs ?? 0,
        description: 'Added from $locationName',
        locationName: locationName,
      );

      // Notify listeners of the update
      if (success) {
        EventBus().emit(CollectionUpdatedEvent(collection.id.toString()));
      } else {
        if (kDebugMode) {
          print(
              '❌ [CollectionSelector] Failed to add track to public collection');
        }
      }
    } catch (e) {
      debugPrint('Error adding pin to collection: $e');
      // Don't show error to user since the pin was already created successfully
    }
  }

  /// Ensures we have a valid artwork URL or provides a fallback
  String _getValidArtworkUrl(String? artworkUrl) {
    // Debug logging to understand what we're receiving
    if (kDebugMode) {
      print('🎨 [CollectionSelector] Processing artwork URL:');
      print('   Original artworkUrl: $artworkUrl');
      print('   Is null: ${artworkUrl == null}');
      print('   Is empty: ${artworkUrl?.isEmpty}');
      print('   Starts with http: ${artworkUrl?.startsWith('http')}');
    }

    String finalUrl;

    if (artworkUrl != null && artworkUrl.isNotEmpty) {
      // Handle HTTP/HTTPS URLs
      if (artworkUrl.startsWith('http')) {
        finalUrl = artworkUrl;
        if (kDebugMode) {
          print('   ✅ Using valid HTTP URL: $artworkUrl');
        }
      }
      // Handle Spotify image URIs
      else if (artworkUrl.startsWith('spotify:image:')) {
        final imageId = artworkUrl.split(':').last;
        finalUrl = 'https://i.scdn.co/image/$imageId';
        if (kDebugMode) {
          print('   🎵 Converting Spotify URI to HTTP: $finalUrl');
        }
      }
      // Handle relative URLs by making them absolute
      else if (artworkUrl.startsWith('/')) {
        finalUrl =
            'https://example.com$artworkUrl'; // Replace with your actual domain
        if (kDebugMode) {
          print('   🔗 Converting relative URL to absolute: $finalUrl');
        }
      }
      // If it's not empty but doesn't start with http, use fallback
      else {
        if (kDebugMode) {
          print('   ⚠️ Unrecognized artwork URL format: $artworkUrl');
        }
        finalUrl = 'https://via.placeholder.com/300x300?text=Track';
      }
    } else {
      // Provide a fallback image URL if no valid artwork URL is available
      finalUrl = 'https://via.placeholder.com/300x300?text=Track';
    }

    // Ensure the URL doesn't exceed 200 characters (backend limit)
    if (finalUrl.length > 200) {
      if (kDebugMode) {
        print(
            '   ⚠️ URL too long (${finalUrl.length} chars), using short fallback');
      }
      finalUrl = 'https://via.placeholder.com/300?text=Art';
    }

    if (kDebugMode) {
      print('   🔄 Final URL: $finalUrl (${finalUrl.length} chars)');
    }

    return finalUrl;
  }

  /// Gets a specific location name using reverse geocoding
  Future<String> _getLocationName(double latitude, double longitude) async {
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;

        // Debug logging to see what data is available
        debugPrint('🗺️ Geocoding data for ($latitude, $longitude):');
        debugPrint('   name: "${placemark.name}"');
        debugPrint('   street: "${placemark.street}"');
        debugPrint('   subThoroughfare: "${placemark.subThoroughfare}"');
        debugPrint('   thoroughfare: "${placemark.thoroughfare}"');
        debugPrint('   locality: "${placemark.locality}"');
        debugPrint('   subLocality: "${placemark.subLocality}"');
        debugPrint('   administrativeArea: "${placemark.administrativeArea}"');
        debugPrint(
            '   subAdministrativeArea: "${placemark.subAdministrativeArea}"');
        debugPrint('   postalCode: "${placemark.postalCode}"');
        debugPrint('   country: "${placemark.country}"');

        // Build a specific location name from available components
        List<String> locationParts = [];

        // Add specific building/establishment name if available
        if (placemark.name != null &&
            placemark.name!.isNotEmpty &&
            placemark.name! != placemark.locality &&
            !placemark.name!.contains(RegExp(r'^\d+'))) {
          // Skip if name starts with number (likely an address)
          locationParts.add(placemark.name!);
        }

        // Add locality (city/town) if available and different from name
        if (placemark.locality != null &&
            placemark.locality!.isNotEmpty &&
            !locationParts.any((part) => part
                .toLowerCase()
                .contains(placemark.locality!.toLowerCase()))) {
          locationParts.add(placemark.locality!);
        }

        // Only add state if we just have name without city
        if (locationParts.length == 1 &&
            placemark.administrativeArea != null &&
            placemark.administrativeArea!.isNotEmpty) {
          locationParts.add(placemark.administrativeArea!);
        }

        // Join the parts with commas - should typically be "Name, City"
        final locationName = locationParts.join(', ');

        if (locationName.isNotEmpty) {
          return locationName;
        }
      }

      // Fallback to coordinates if no meaningful location found
      return 'Location (${latitude.toStringAsFixed(4)}, ${longitude.toStringAsFixed(4)})';
    } catch (e) {
      debugPrint('Error getting location name: $e');
      return 'Unknown Location';
    }
  }

  /// Converts Spotify and Apple Music URIs to HTTP URLs where possible
  String _resolveTrackUrl(MusicTrack track) {
    if (kDebugMode) {
      print('🔗 [CollectionSelector] Resolving track URL:');
      print('   Track service: ${track.service}');
      print('   Track URL: ${track.url}');
      print('   Track URI: ${track.uri}');
    }

    // First check if we already have a valid HTTP URL
    if (track.url.isNotEmpty && track.url.startsWith('http')) {
      if (kDebugMode) {
        print('   ✅ Using existing HTTP URL: ${track.url}');
      }
      return track.url;
    }

    // Handle Spotify URIs
    if (track.uri.startsWith('spotify:track:')) {
      final id = track.uri.split(':').last;
      final spotifyUrl = 'https://open.spotify.com/track/$id';
      if (kDebugMode) {
        print('   🎵 Converting Spotify URI to URL: $spotifyUrl');
      }
      return spotifyUrl;
    }

    // Handle Apple Music URIs - create proper Apple Music URLs
    if (track.uri.startsWith('apple:track:') ||
        track.uri.startsWith('applemusic:track:')) {
      final id = track.uri.split(':').last;
      // Apple Music URLs follow this format for individual tracks
      final appleMusicUrl = 'https://music.apple.com/us/song/$id';
      if (kDebugMode) {
        print('   🍎 Converting Apple Music URI to URL: $appleMusicUrl');
      }
      return appleMusicUrl;
    }

    // For Apple Music service without proper URI, try to construct URL from track ID
    if (track.service.toLowerCase().contains('apple') && track.id.isNotEmpty) {
      final appleMusicUrl = 'https://music.apple.com/us/song/${track.id}';
      if (kDebugMode) {
        print('   🍎 Creating Apple Music URL from ID: $appleMusicUrl');
      }
      return appleMusicUrl;
    }

    // For SoundCloud, try to use the original URL or construct one
    if (track.service.toLowerCase().contains('soundcloud')) {
      if (track.url.isNotEmpty) {
        return track.url;
      }
      // SoundCloud URLs typically follow this pattern, but we'd need the actual permalink
      if (kDebugMode) {
        print('   🔊 SoundCloud track without URL, returning URI as fallback');
      }
      return track.uri.isNotEmpty ? track.uri : track.id;
    }

    // If no valid conversion is possible, try the original URL first, then URI
    final fallbackUrl = track.url.isNotEmpty ? track.url : track.uri;
    if (kDebugMode) {
      print('   ⚠️ Using fallback URL: $fallbackUrl');
    }
    return fallbackUrl;
  }

  /// Builds optimized image widget for collection covers
  Widget _buildOptimizedImage(String imageUrl, Color fallbackColor) {
    if (kDebugMode) {
      print('🖼️ [CollectionSelector] Building optimized image:');
      print('   Image URL: $imageUrl');
      print('   Fallback color: $fallbackColor');
    }

    // Check if it's a Cloudinary URL to extract public ID
    String? publicId;
    if (imageUrl.contains('res.cloudinary.com')) {
      try {
        final uri = Uri.parse(imageUrl);
        final segments = uri.pathSegments;
        // Cloudinary URL pattern: /<cloud_name>/image/upload/<version?>/<folder...>/<file.ext>
        // We need to drop the first 3 segments (<cloud_name>/image/upload) and optional version (starting with 'v')
        int startIndex = 3; // after cloud_name/image/upload
        if (segments.length > 3 && segments[3].startsWith('v')) {
          // Skip version segment
          startIndex = 4;
        }
        if (segments.length > startIndex) {
          publicId = segments.sublist(startIndex).join('/');
          // Remove extension
          publicId = publicId!.replaceAll(RegExp(r'\.[^.]*$'), '');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ [CollectionSelector] Failed to parse Cloudinary URL: $e');
        }
      }
    }

    if (publicId != null && kDebugMode) {
      print('   Extracted Cloudinary public ID: $publicId');
    }

    if (publicId != null) {
      if (kDebugMode) {
        print('   Using CloudinaryImage widget');
      }
      return CloudinaryImage(
        publicId: publicId,
        width: 56,
        height: 56,
        quality: 85,
        format: 'webp',
        borderRadius: BorderRadius.circular(12),
        placeholder: Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: fallbackColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(Icons.image,
              color: fallbackColor.withOpacity(0.5), size: 24),
        ),
        errorWidget: Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: fallbackColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(Icons.broken_image,
              color: fallbackColor.withOpacity(0.7), size: 24),
        ),
      );
    } else {
      if (kDebugMode) {
        print('   Using regular Image.network widget');
      }
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.network(
          imageUrl,
          width: 56,
          height: 56,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: fallbackColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(fallbackColor),
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            if (kDebugMode) {
              print('   ❌ Image.network error: $error');
              print('   Stack trace: $stackTrace');
            }
            return Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: fallbackColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(Icons.broken_image,
                  color: fallbackColor.withOpacity(0.7), size: 24),
            );
          },
        ),
      );
    }
  }

  /// Builds a default icon for collection covers
  Widget _buildDefaultIcon(Color fallbackColor) {
    if (kDebugMode) {
      print('🖼️ [CollectionSelector] Building default icon');
    }
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: fallbackColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(Icons.collections_bookmark,
          color: fallbackColor.withOpacity(0.5), size: 24),
    );
  }

  /// Builds the music service buttons (Spotify and Apple Music) at the bottom
  Widget _buildMusicServiceButtons(BuildContext context, MusicTrack track) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 16, 20, 0),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Open in Music App',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              // Spotify Button
              Expanded(
                child: _buildMusicServiceButton(
                  context: context,
                  track: track,
                  service: 'spotify',
                  icon: FontAwesomeIcons.spotify,
                  label: 'Spotify',
                  color: const Color(0xFF1DB954),
                  onTap: () => _openInSpotify(context, track),
                ),
              ),
              const SizedBox(width: 8),
              // Apple Music Button
              Expanded(
                child: _buildMusicServiceButton(
                  context: context,
                  track: track,
                  service: 'apple_music',
                  icon: FontAwesomeIcons.music,
                  label: 'Apple Music',
                  color: const Color(0xFFFA243C),
                  onTap: () => _openInAppleMusic(context, track),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds individual music service button
  Widget _buildMusicServiceButton({
    required BuildContext context,
    required MusicTrack track,
    required String service,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.08),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: color.withOpacity(0.25),
              width: 0.5,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              FaIcon(
                icon,
                color: color,
                size: 16,
              ),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Opens track in Spotify with cross-referencing logic
  Future<void> _openInSpotify(BuildContext context, MusicTrack track) async {
    String spotifyUrl;

    try {
      // If track already has a Spotify URL, use it
      if (track.url.contains('open.spotify.com')) {
        spotifyUrl = track.url;
      }
      // If track has Spotify URI, convert to URL
      else if (track.uri.startsWith('spotify:track:')) {
        final trackId = track.uri.split(':').last;
        spotifyUrl = 'https://open.spotify.com/track/$trackId';
      }
      // Cross-reference: search for track on Spotify using artist and title
      else {
        // For cross-referencing, we'll construct a search URL
        final query = Uri.encodeComponent('${track.artist} ${track.title}');
        spotifyUrl = 'https://open.spotify.com/search/$query';
      }

      final uri = Uri.parse(spotifyUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Could not open Spotify. Please make sure Spotify is installed.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error opening Spotify: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error opening Spotify'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Opens track in Apple Music with cross-referencing logic
  Future<void> _openInAppleMusic(BuildContext context, MusicTrack track) async {
    String appleMusicUrl;

    try {
      // If track already has an Apple Music URL, use it
      if (track.url.contains('music.apple.com')) {
        appleMusicUrl = track.url;
      }
      // If track has Apple Music URI, convert to URL
      else if (track.uri.startsWith('apple:track:')) {
        final trackId = track.uri.split(':').last;
        appleMusicUrl = 'https://music.apple.com/us/song/$trackId';
      }
      // Cross-reference: search for track on Apple Music using artist and title
      else {
        // For cross-referencing, we'll construct a search URL
        final query = Uri.encodeComponent('${track.artist} ${track.title}');
        appleMusicUrl = 'https://music.apple.com/search?term=$query';
      }

      final uri = Uri.parse(appleMusicUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Could not open Apple Music. Please make sure Apple Music is installed.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error opening Apple Music: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error opening Apple Music'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
