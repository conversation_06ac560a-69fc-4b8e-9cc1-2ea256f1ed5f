import 'package:flutter/material.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import 'dart:ui' as ui;
import 'package:geolocator/geolocator.dart';
import 'package:cached_network_image/cached_network_image.dart';

class DistantPinBottomSheet extends StatefulWidget {
  final Map<String, dynamic> pinData;
  final Position? currentPosition;

  const DistantPinBottomSheet({
    Key? key,
    required this.pinData,
    this.currentPosition,
  }) : super(key: key);

  @override
  State<DistantPinBottomSheet> createState() => _DistantPinBottomSheetState();
}

class _DistantPinBottomSheetState extends State<DistantPinBottomSheet> {
  @override
  void initState() {
    super.initState();
    // Update PiP params when bottomsheet is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PictureInPicture.updatePiPParams(
        pipParams: PiPParams(
          pipWindowHeight: 125,
          pipWindowWidth: 125,
          bottomSpace: MediaQuery.of(context).size.height *
              0.65, // Match bottomsheet height
          leftSpace: 0,
          rightSpace: 0,
          topSpace: 0,
          maxSize: const Size(125, 125),
          minSize: const Size(125, 125),
          movable: true,
          resizable: false,
          initialCorner: PIPViewCorner.bottomLeft,
        ),
      );
    });
  }

  @override
  void dispose() {
    // Restore default PiP params when bottomsheet is closed
    PictureInPicture.updatePiPParams(
      pipParams: const PiPParams(
        pipWindowHeight: 125,
        pipWindowWidth: 125,
        bottomSpace: 64,
        leftSpace: 0,
        rightSpace: 0,
        topSpace: 64,
        maxSize: Size(125, 125),
        minSize: Size(125, 125),
        movable: true,
        resizable: false,
        initialCorner: PIPViewCorner.bottomLeft,
      ),
    );
    super.dispose();
  }

  Color _getPinColor(String rarity) {
    switch (rarity) {
      case 'legendary':
        return const Color(0xFFFFE55C);
      case 'epic':
        return const Color(0xFFE91E63);
      case 'rare':
        return const Color(0xFF00E5FF);
      case 'uncommon':
        return const Color(0xFF4CAF50);
      default:
        return const Color(0xFF2196F3);
    }
  }

  double _calculateDistanceInMeters(
      double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
  }

  @override
  Widget build(BuildContext context) {
    final rarity = widget.pinData['rarity']?.toLowerCase() ?? 'common';
    final pinColor = _getPinColor(rarity);

    final artworkUrl = widget.pinData['artwork_url'] ??
        widget.pinData['albumArt'] ??
        widget.pinData['album_art'] ??
        widget.pinData['image_url'];
    final profilePicUrl = widget.pinData['owner']?['profile_pic'] ??
        widget.pinData['profile_pic'];

    // Debug profile picture URL
    debugPrint('🖼️ [DistantPinBottomSheet] Profile pic debug:');
    debugPrint('  - Full pin data keys: ${widget.pinData.keys.toList()}');
    debugPrint('  - Owner data: ${widget.pinData['owner']}');
    debugPrint('  - Direct profile_pic: ${widget.pinData['profile_pic']}');
    debugPrint('  - Final profilePicUrl: $profilePicUrl');
    debugPrint(
        '  - URL is valid: ${profilePicUrl != null && profilePicUrl.isNotEmpty && Uri.tryParse(profilePicUrl)?.hasAbsolutePath == true}');

    // Attempt to resolve pin skin image
    String? skinImageUrl;
    if (widget.pinData['skinDetails'] != null &&
        widget.pinData['skinDetails']['image'] != null) {
      skinImageUrl = widget.pinData['skinDetails']['image'].toString();
    }

    // Calculate distance if we have coordinates
    double? distance;
    if (widget.currentPosition != null &&
        widget.pinData['latitude'] != null &&
        widget.pinData['longitude'] != null) {
      distance = _calculateDistanceInMeters(
        widget.currentPosition!.latitude,
        widget.currentPosition!.longitude,
        widget.pinData['latitude'],
        widget.pinData['longitude'],
      );
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, -8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        child: BackdropFilter(
          filter: ui.ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.15),
                  Colors.white.withOpacity(0.08),
                  Colors.black.withOpacity(0.1),
                  Colors.black.withOpacity(0.25),
                ],
              ),
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // Header with close button
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Distant Pin',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            shadows: [
                              Shadow(
                                color: Colors.black.withOpacity(0.5),
                                offset: const Offset(1, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                        ),
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Song details (mysterious) with album cover
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Rotating pin skin preview with lock overlay
                              Stack(
                                alignment: Alignment.center,
                                children: [
                                  if (skinImageUrl != null &&
                                      skinImageUrl.isNotEmpty)
                                    _RotatingCircularSkin(
                                        imageUrl: skinImageUrl, size: 80)
                                  else
                                    Container(
                                      width: 80,
                                      height: 80,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: pinColor.withOpacity(0.2),
                                      ),
                                      child: Icon(Icons.push_pin,
                                          color: pinColor, size: 32),
                                    ),
                                  // Removed lock overlay as per new design
                                ],
                              ),
                              const SizedBox(width: 16),
                              // Mystery info
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Hidden Track',
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.9),
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 6),
                                    Text(
                                      'Get closer to unlock',
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.7),
                                        fontSize: 15,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Enhanced profile section with better design
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.08),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.15),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              // Profile picture with enhanced styling
                              Container(
                                padding: const EdgeInsets.all(3),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: LinearGradient(
                                    colors: [
                                      pinColor.withOpacity(0.6),
                                      pinColor.withOpacity(0.3),
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: pinColor.withOpacity(0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withOpacity(0.1),
                                  ),
                                  child: CircleAvatar(
                                    radius: 22,
                                    backgroundColor:
                                        Colors.white.withOpacity(0.2),
                                    backgroundImage: profilePicUrl != null &&
                                            profilePicUrl.isNotEmpty &&
                                            Uri.tryParse(profilePicUrl)
                                                    ?.hasAbsolutePath ==
                                                true
                                        ? CachedNetworkImageProvider(
                                            profilePicUrl,
                                            errorListener: (error) {
                                              debugPrint(
                                                  '❌ [DistantPinBottomSheet] CachedNetworkImage error for $profilePicUrl: $error');
                                            },
                                          )
                                        : null,
                                    child: profilePicUrl == null ||
                                            profilePicUrl.isEmpty ||
                                            Uri.tryParse(profilePicUrl)
                                                    ?.hasAbsolutePath !=
                                                true
                                        ? Icon(
                                            Icons.person,
                                            color:
                                                Colors.white.withOpacity(0.7),
                                            size: 24,
                                          )
                                        : null,
                                  ),
                                ),
                              ),

                              const SizedBox(width: 16),

                              // Profile info
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '@${widget.pinData['owner']?['username'] ?? widget.pinData['username'] ?? 'Unknown'}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Pin Creator',
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.6),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Distance info
                        if (distance != null) ...[
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.location_on_outlined,
                                  color: Colors.white.withOpacity(0.7),
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    distance! > 1000
                                        ? '${(distance! / 1000).toStringAsFixed(1)} km away'
                                        : '${distance!.toStringAsFixed(0)} m away',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),

                                // Rarity badge with enhanced styling
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        pinColor.withOpacity(0.3),
                                        pinColor.withOpacity(0.2),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: pinColor.withOpacity(0.5),
                                      width: 1.5,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: pinColor.withOpacity(0.2),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Text(
                                    '${rarity.toUpperCase()} PIN',
                                    style: TextStyle(
                                      color: pinColor,
                                      fontSize: 11,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                        ],

                        // Mystery message
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.orange.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.orange.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.explore,
                                color: Colors.orange.withOpacity(0.8),
                                size: 32,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'Get closer to discover',
                                style: TextStyle(
                                  color: Colors.orange.withOpacity(0.9),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Walk to this pin to unlock the hidden song and play it!',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 14,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),

                        // Added padding below the last element
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Rotating circular skin image widget
class _RotatingCircularSkin extends StatefulWidget {
  final String imageUrl;
  final double size;

  const _RotatingCircularSkin({
    Key? key,
    required this.imageUrl,
    this.size = 80,
  }) : super(key: key);

  @override
  State<_RotatingCircularSkin> createState() => _RotatingCircularSkinState();
}

class _RotatingCircularSkinState extends State<_RotatingCircularSkin>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 12),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform(
            alignment: Alignment.center,
            transform: Matrix4.identity()
              ..setEntry(3, 2, 0.001) // perspective
              ..rotateY(_controller.value * 2 * 3.1415926),
            child: child,
          );
        },
        child: ClipOval(
          child: CachedNetworkImage(
            imageUrl: widget.imageUrl,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: Colors.white24,
              child: const Center(
                  child: CircularProgressIndicator(strokeWidth: 2)),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.white24,
              child: const Center(
                  child: Icon(Icons.push_pin, color: Colors.white54, size: 32)),
            ),
          ),
        ),
      ),
    );
  }
}
