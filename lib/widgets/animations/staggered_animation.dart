import 'package:flutter/material.dart';

/// A widget that applies a staggered animation to its child
/// with a delay based on the index
class StaggeredAnimation extends StatelessWidget {
  final int index;
  final AnimationController controller;
  final Widget child;
  final double slideOffset;
  final Offset Function(int)? customOffset;
  
  const StaggeredAnimation({
    Key? key,
    required this.index,
    required this.controller,
    required this.child,
    this.slideOffset = 100.0,
    this.customOffset,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Calculate a delay based on the item index
    final double delay = (index * 0.15).clamp(0.0, 1.0);
    
    // Create delayed animation for each item
    final Animation<double> delayedAnimation = CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay,
        1.0,
        curve: Curves.easeOutQuart,
      ),
    );
    
    return AnimatedBuilder(
      animation: delayedAnimation,
      builder: (context, child) {
        final double slideValue = slideOffset * (1.0 - delayedAnimation.value);
        
        // Determine offset - either from custom function or default slide up
        Offset offset;
        if (customOffset != null) {
          offset = customOffset!(index) * (1.0 - delayedAnimation.value);
        } else {
          offset = Offset(0, slideValue);
        }
        
        return Opacity(
          opacity: delayedAnimation.value,
          child: Transform.translate(
            offset: offset,
            child: child,
          ),
        );
      },
      child: child,
    );
  }
}

/// A widget that applies a staggered fade animation to its child
class StaggeredFadeAnimation extends StatelessWidget {
  final int index;
  final AnimationController controller;
  final Widget child;
  
  const StaggeredFadeAnimation({
    Key? key,
    required this.index,
    required this.controller,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Calculate a delay based on the item index
    final double delay = (index * 0.1).clamp(0.0, 0.9);
    
    // Create delayed animation for each item
    final Animation<double> delayedAnimation = CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay,
        delay + 0.4,
        curve: Curves.easeIn,
      ),
    );
    
    return AnimatedBuilder(
      animation: delayedAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: delayedAnimation.value,
          child: child,
        );
      },
      child: child,
    );
  }
}

/// A widget that applies a scale animation to its child
class ScaleAnimation extends StatelessWidget {
  final AnimationController controller;
  final Widget child;
  final Curve curve;
  
  const ScaleAnimation({
    Key? key,
    required this.controller,
    required this.child,
    this.curve = Curves.elasticOut,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Animation<double> scaleAnimation = CurvedAnimation(
      parent: controller,
      curve: curve,
    );
    
    return AnimatedBuilder(
      animation: scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: scaleAnimation.value,
          child: child,
        );
      },
      child: child,
    );
  }
}

/// A widget that applies a combined slide, fade, and scale animation
class CombinedAnimation extends StatelessWidget {
  final int index;
  final AnimationController controller;
  final Widget child;
  final double slideDistance;
  final Curve slideCurve;
  final Curve fadeCurve;
  final Curve scaleCurve;
  
  const CombinedAnimation({
    Key? key,
    required this.index,
    required this.controller,
    required this.child,
    this.slideDistance = 100.0,
    this.slideCurve = Curves.easeOutQuart,
    this.fadeCurve = Curves.easeIn,
    this.scaleCurve = Curves.easeOut,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Calculate a delay based on the item index
    final double delay = (index * 0.1).clamp(0.0, 0.9);
    
    // Create staggered animations
    final Animation<double> slideAnimation = CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay,
        delay + 0.5,
        curve: slideCurve,
      ),
    );
    
    final Animation<double> fadeAnimation = CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay,
        delay + 0.3,
        curve: fadeCurve,
      ),
    );
    
    final Animation<double> scaleAnimation = CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay,
        delay + 0.5,
        curve: scaleCurve,
      ),
    );
    
    return AnimatedBuilder(
      animation: Listenable.merge([slideAnimation, fadeAnimation, scaleAnimation]),
      builder: (context, child) {
        return Opacity(
          opacity: fadeAnimation.value,
          child: Transform.translate(
            offset: Offset(0, slideDistance * (1.0 - slideAnimation.value)),
            child: Transform.scale(
              scale: 0.8 + (0.2 * scaleAnimation.value),
              child: child,
            ),
          ),
        );
      },
      child: child,
    );
  }
} 