import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:geocoding/geocoding.dart';
import '../../services/location/location_manager.dart';
import '../../services/verification/school_verification_service.dart';
import '../../services/ranking_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import 'components/leaderboard_card.dart';
import 'components/leaderboard_filter_chip.dart';
import 'components/leaderboard_podium.dart';
import '../../utils/navigation_helper.dart';
import '../../utils/app_tab.dart';
import '../bottomsheets/school_verification_bottomsheet.dart';

class LeaderboardTab extends StatefulWidget {
  const LeaderboardTab({Key? key}) : super(key: key);

  @override
  State<LeaderboardTab> createState() => _LeaderboardTabState();
}

class _LeaderboardTabState extends State<LeaderboardTab> with AutomaticKeepAliveClientMixin {
  final LocationManager _locationManager = LocationManager();
  final SchoolVerificationService _schoolVerificationService = SchoolVerificationService();
  late final RankingService _rankingService;
  
  String? _currentLocation;
  String _currentFilter = 'Local'; // Changed default filter
  bool _isLoadingLocation = false;
  bool _isLoadingLeaderboard = true;
  bool _isInitialized = false;
  bool _isUserVerified = false; // Track user verification status
  String? _userSchoolName; // Store user's school name
  
  // Cache for leaderboard data
  final Map<String, List<Map<String, dynamic>>> _cachedLeaderboardData = {};
  List<Map<String, dynamic>> _leaderboardData = [];
  
  static final _bucket = PageStorageBucket();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _rankingService = RankingService(ApiService(), AuthService(ApiService()));
    
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      
      // Check verification status first
      await _checkVerificationStatus();
      
      // Restore state
      final savedState = _bucket.readState(context, identifier: 'leaderboard_state') as Map<String, dynamic>?;
      if (savedState != null && mounted) {
        final filter = savedState['filter'] as String? ?? 'Local';
        setState(() {
          _currentFilter = filter;
        });
      }
      
      setState(() => _isInitialized = true);
      
      // Load initial data
      if (_currentFilter == 'Local') {
        await _initializeLocation();
      } else if (_currentFilter == 'School') {
        // Load school data if user is verified
        if (_isUserVerified) {
          await _loadLeaderboardData(filter: 'School');
        } else {
          setState(() => _isLoadingLeaderboard = false);
      }
      } else {
        await _loadLeaderboardData();
      }
    });
  }

  /// Check user's school verification status
  Future<void> _checkVerificationStatus() async {
    try {
      final verificationStatus = await _schoolVerificationService.checkVerificationStatus();
      
      setState(() {
        _isUserVerified = verificationStatus['is_verified'] ?? false;
        _userSchoolName = verificationStatus['school_name'];
      });
    } catch (e) {
      setState(() {
        _isUserVerified = false;
        _userSchoolName = null;
      });
    }
  }

  Future<void> _initializeData() async {
    // If we have cached data for the current filter, use it immediately
    if (_cachedLeaderboardData.containsKey(_currentFilter)) {
      setState(() {
        _leaderboardData = _cachedLeaderboardData[_currentFilter]!;
        _isLoadingLeaderboard = false;
      });
    }

    // If we need location data and don't have it yet, get it
    if (_currentFilter == 'Local' && _currentLocation == null) {
      await _initializeLocation();
    } else {
      // If we don't have cached data for the current filter, load it
      if (!_cachedLeaderboardData.containsKey(_currentFilter)) {
        await _loadLeaderboardData(filter: _currentFilter);
      }
    }
  }

  void _saveCurrentState() {
    if (!mounted) return;
    
    final state = {
      'current_filter': _currentFilter,
      'current_location': _currentLocation,
      'cached_data': _cachedLeaderboardData,
    };
    
    _bucket.writeState(context, state, identifier: 'leaderboard_state');
  }

  Future<void> _initializeLocation() async {
    // Don't initialize location if we're not on Local filter
    if (_currentFilter != 'Local') return;
    
    // Don't initialize if we already have location and data
    if (_currentLocation != null && _cachedLeaderboardData.containsKey('Local')) return;

    setState(() => _isLoadingLocation = true);
    try {
      final position = await _locationManager.requestCurrentLocation();
      if (position != null) {
        final placemarks = await placemarkFromCoordinates(
          position.latitude,
          position.longitude,
        );
        
        if (placemarks.isNotEmpty) {
          final city = placemarks.first.locality ?? placemarks.first.subAdministrativeArea;
          setState(() {
            _currentLocation = city;
            _isLoadingLocation = false;
          });
          // Save state after getting location
          _saveCurrentState();
          // Only load data if we're still on Local filter
          if (_currentFilter == 'Local') {
            await _loadLeaderboardData(filter: 'Local');
          }
        }
      }
    } catch (e) {
      print('Location error: $e');
      setState(() {
        _isLoadingLocation = false;
        // Only change filter if we're currently on Local
        if (_currentFilter == 'Local') {
          _currentFilter = 'Global';
          _loadLeaderboardData(filter: 'Global');
        }
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Could not get your location. Showing global rankings.'),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () {
                _initializeLocation();
              },
            ),
          ),
        );
      }
    }
  }

  Future<void> _loadLeaderboardData({String? filter}) async {
    final targetFilter = filter ?? _currentFilter;
    
    // Return cached data if available and not forcing refresh
    if (_cachedLeaderboardData.containsKey(targetFilter) && filter == null) {
      setState(() {
        _leaderboardData = _cachedLeaderboardData[targetFilter]!;
        _isLoadingLeaderboard = false;
      });
      return;
    }
    
    setState(() => _isLoadingLeaderboard = true);
    
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (targetFilter == 'School') {
        // Return empty data for School filter
        setState(() {
          _leaderboardData = [];
          _isLoadingLeaderboard = false;
        });
        return;
      }
      
      // Sample data - replace with actual API call
      final sampleData = List.generate(20, (index) => {
        'name': 'User ${index + 1}',
        'username': 'user${index + 1}',
        'avatar': 'https://i.pravatar.cc/150?img=${index + 1}',
        'score': '${1000 - (index * 50)}',
        'change': index % 3 == 0 ? 'up' : (index % 3 == 1 ? 'down' : 'same'),
        'changeAmount': '${(index % 5 + 1) * 10}',
      });
      
      // Cache the data
      _cachedLeaderboardData[targetFilter] = sampleData;
      
      setState(() {
        _leaderboardData = sampleData;
        _isLoadingLeaderboard = false;
      });
      
      // Save state after successful load
      _saveCurrentState();
    } catch (e) {
      print('Error loading leaderboard: $e');
      setState(() => _isLoadingLeaderboard = false);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load leaderboard: ${e.toString()}'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _refreshLeaderboard() async {
    // Force reload by passing current filter
    await _loadLeaderboardData(filter: _currentFilter);
  }

  void _handleCreateChallenge() {
    // Navigate to challenges tab using the AppTab enum
    NavigationHelper.navigateToTab(context, AppTab.challenges.index);
  }

  Widget _buildSchoolConnectionPrompt(bool isSmallScreen) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: isSmallScreen ? 16 : 24,
          vertical: isSmallScreen ? 16 : 24,  // Reduced vertical padding
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,  // Reduced from 100
              height: 80,  // Reduced from 100
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.school_outlined,
                size: 36,  // Reduced from 48
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),  // Reduced from 20
            Text(
              'Connect to Your School',
              style: TextStyle(
                fontSize: isSmallScreen ? 16 : 18,  // Reduced from 18/20
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 6),  // Reduced from 8
            Text(
              'See how you rank among your fellow students!',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: isSmallScreen ? 12 : 13,  // Reduced from 13/14
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),  // Reduced from 24
            ElevatedButton(
              onPressed: () {
                SchoolVerificationBottomSheet.show(
                  context,
                    onVerificationComplete: (result) {
                      if (result['success']) {
                        // Refresh the leaderboard to show school rankings
                        _refreshLeaderboard();
                      }
                    },
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 16 : 20,
                  vertical: isSmallScreen ? 8 : 10,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.add_circle_outline,
                    size: isSmallScreen ? 14 : 16,  // Reduced from 16/18
                  ),
                  SizedBox(width: isSmallScreen ? 4 : 6),  // Reduced from 6/8
                  Text(
                    'Connect Now',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 12 : 13,  // Reduced from 13/14
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return SafeArea(
      top: false,
      bottom: false,
      child: Column(
        children: [
          // Filter header
          Container(
            height: isSmallScreen ? 40 : 48,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor.withOpacity(0.05),
                  width: 1,
                ),
              ),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: 0,
              vertical: isSmallScreen ? 4 : 6,
            ),
            child: ListView(
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 24),
              children: [
                if (_currentLocation != null)
                  LeaderboardFilterChip(
                    label: _currentLocation!,
                    isSelected: _currentFilter == 'Local',
                    onTap: () {
                      setState(() => _currentFilter = 'Local');
                      _loadLeaderboardData();
                    },
                    isSmallScreen: isSmallScreen,
                    icon: Icons.location_on,
                  ),
                if (_currentLocation != null)
                  const SizedBox(width: 8),
                LeaderboardFilterChip(
                  label: 'Friends',
                  isSelected: _currentFilter == 'Friends',
                  onTap: () {
                    setState(() => _currentFilter = 'Friends');
                    _loadLeaderboardData();
                  },
                  isSmallScreen: isSmallScreen,
                  icon: Icons.people,
                  isCompact: true, // Make friends chip more compact
                ),
                const SizedBox(width: 8),
                LeaderboardFilterChip(
                  label: 'School',
                  isSelected: _currentFilter == 'School',
                  onTap: () {
                    setState(() => _currentFilter = 'School');
                    _loadLeaderboardData();
                  },
                  isSmallScreen: isSmallScreen,
                  icon: Icons.school,
                ),
                const SizedBox(width: 8),
                LeaderboardFilterChip(
                  label: 'Global',
                  isSelected: _currentFilter == 'Global',
                  onTap: () {
                    setState(() => _currentFilter = 'Global');
                    _loadLeaderboardData();
                  },
                  isSmallScreen: isSmallScreen,
                  icon: Icons.public,
                ),
              ],
            ),
          ),
          
          // Leaderboard content
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshLeaderboard,
              child: _currentFilter == 'School'
                ? _buildSchoolConnectionPrompt(isSmallScreen)
                : _isLoadingLeaderboard
                  ? _buildLoadingList(isSmallScreen)
                  : ListView.builder(
                      padding: EdgeInsets.fromLTRB(
                        isSmallScreen ? 8 : 12,
                        0, // No top padding
                        isSmallScreen ? 8 : 12,
                        MediaQuery.of(context).padding.bottom + (isSmallScreen ? 8 : 12),
                      ),
                      itemCount: _leaderboardData.length - 2 + 1, // +1 for the podium
                      itemBuilder: (context, index) {
                        if (index == 0) {
                          // Always show the podium at the top, outside of scrollable content
                          return Padding(
                            padding: EdgeInsets.only(top: 0),
                            child: _leaderboardData.length >= 3
                                ? LeaderboardPodium(
                                    leaderboardData: _leaderboardData.take(3).toList(),
                                    isSmallScreen: isSmallScreen,
                                    onCreateChallenge: _handleCreateChallenge,
                                  )
                                : const SizedBox.shrink(),
                          );
                        }
                        // index 1 is rank 4, index 2 is rank 5, etc.
                        final dataIndex = index + 2 - 1; // Skip first 3 (0,1,2)
                        return LeaderboardCard(
                          rank: dataIndex + 1,
                          user: _leaderboardData[dataIndex],
                          isSmallScreen: isSmallScreen,
                        );
                      },
                      physics: const AlwaysScrollableScrollPhysics(),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingList(bool isSmallScreen) {
    return ListView.builder(
      padding: EdgeInsets.fromLTRB(
        isSmallScreen ? 8 : 12,
        isSmallScreen ? 8 : 12,
        isSmallScreen ? 8 : 12,
        MediaQuery.of(context).padding.bottom + (isSmallScreen ? 8 : 12),
      ),
      itemCount: 10,
      itemBuilder: (context, index) {
        return LeaderboardCard(
          rank: index + 1,
          user: {
            'name': '',
            'username': '',
            'avatar': '',
            'score': '',
            'change': 'same',
            'changeAmount': '',
          },
          isSmallScreen: isSmallScreen,
          isLoading: true,
        );
      },
    );
  }
} 