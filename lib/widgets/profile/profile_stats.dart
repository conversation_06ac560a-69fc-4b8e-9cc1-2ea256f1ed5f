import 'package:flutter/material.dart';
import '../../config/themes.dart';

class ProfileStats extends StatelessWidget {
  final Size size;
  final Map<String, String> stats;

  const ProfileStats({
    Key? key,
    required this.size,
    required this.stats,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isSmallScreen = size.width < 360;
    
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: isSmallScreen ? 8 : 10,
        horizontal: isSmallScreen ? 12 : 16,
      ),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatItem(context, Icons.push_pin_outlined, stats['pins'] ?? '0', isSmallScreen, Theme.of(context).colorScheme.primary),
          _buildDivider(context),
          _buildStatItem(context, Icons.local_fire_department, stats['streak'] ?? '0', isSmallScreen, Colors.deepOrangeAccent),
          _buildDivider(context),
          _buildStatItem(context, Icons.people_outline, stats['following'] ?? '0', isSmallScreen, Theme.of(context).colorScheme.tertiary),
          _buildDivider(context),
          _buildStatItem(context, Icons.favorite_border, stats['followers'] ?? '0', isSmallScreen, Theme.of(context).colorScheme.primary),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, IconData icon, String value, bool isSmallScreen, Color accentColor) {
    final theme = Theme.of(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: isSmallScreen ? 16 : 18,
                color: isDark 
                    ? accentColor.withOpacity(0.9)
                    : accentColor.withOpacity(0.8),
              ),
              const SizedBox(width: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: isSmallScreen ? 14 : 15,
                  fontWeight: FontWeight.w400,
                  color: theme.colorScheme.onSurface,
                  height: 1.1,
                  letterSpacing: -0.3,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      height: 20,
      width: 1,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).dividerColor.withOpacity(0),
            Theme.of(context).dividerColor.withOpacity(0.08),
            Theme.of(context).dividerColor.withOpacity(0.08),
            Theme.of(context).dividerColor.withOpacity(0),
          ],
          stops: const [0.0, 0.2, 0.8, 1.0],
        ),
      ),
    );
  }
} 