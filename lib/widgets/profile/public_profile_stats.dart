import 'package:flutter/material.dart';
import '../../models/public_user_profile.dart';

class PublicProfileStats extends StatelessWidget {
  final PublicUserProfile profile;
  final Size size;

  const PublicProfileStats({
    Key? key,
    required this.profile,
    required this.size,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = size.width;
    final isSmallScreen = screenWidth < 360;
    
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 16 : 20,
        vertical: isSmallScreen ? 12 : 16,
      ),
      padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.onSurface.withOpacity(0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Pins stat
          Expanded(
            child: _buildStatItem(
              context,
              icon: Icons.push_pin,
              label: 'Pins',
              value: profile.pinCount.toString(),
              isSmallScreen: isSmallScreen,
            ),
          ),
          
          // Divider
          Container(
            width: 1,
            height: isSmallScreen ? 40 : 50,
            color: theme.colorScheme.onSurface.withOpacity(0.1),
          ),
          
          // Collections stat
          Expanded(
            child: _buildStatItem(
              context,
              icon: Icons.collections,
              label: 'Collections',
              value: profile.publicCollectionCount.toString(),
              isSmallScreen: isSmallScreen,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required bool isSmallScreen,
  }) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Icon(
          icon,
          size: isSmallScreen ? 20 : 24,
          color: theme.colorScheme.primary,
        ),
        SizedBox(height: isSmallScreen ? 6 : 8),
        Text(
          value,
          style: TextStyle(
            fontSize: isSmallScreen ? 18 : 22,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: isSmallScreen ? 2 : 4),
        Text(
          label,
          style: TextStyle(
            fontSize: isSmallScreen ? 11 : 12,
            color: theme.colorScheme.onSurface.withOpacity(0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
} 