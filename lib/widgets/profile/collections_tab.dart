import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:ui' as ui;
import 'dart:math' as math;
import 'dart:async';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import '../../config/themes.dart';
import '../../models/collection_model.dart';
import '../../screens/collections/create_collection_screen.dart';
import '../../screens/collections/collection_detail_screen.dart';
import '../../services/collection_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../widgets/navigation/app_navigation_system.dart';
import 'package:shimmer/shimmer.dart';
import '../../utils/event_bus.dart';
import '../../widgets/common/cloudinary_image.dart';
import '../../utils/text_encoding_utils.dart';
// Import your API service here
// import '../../services/api_service.dart'; 

class CollectionsTab extends StatefulWidget {
  const CollectionsTab({Key? key}) : super(key: key);

  @override
  State<CollectionsTab> createState() => _CollectionsTabState();
}

class _CollectionsTabState extends State<CollectionsTab> 
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  List<Collection> _collections = [];
  List<Collection> _filteredCollections = [];
  bool _isLoading = true;
  bool _isGridView = false; // Default to list view instead of grid view
  String _sortBy = 'Recent';
  bool _isSearching = false;
  String _searchQuery = '';
  
  // Animation controllers
  late AnimationController _fabAnimController;
  late AnimationController _listAnimController;
  late AnimationController _searchAnimController;
  late Animation<double> _fabScaleAnimation;
  late Animation<Offset> _fabSlideAnimation;
  late Animation<double> _searchAnimation;
  
  // Add cache map
  final Map<String, List<Collection>> _collectionsCache = {};
  
  // Search controller
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController(); // Add scroll controller

  // Services
  late CollectionService _collectionService;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initServices();
    _fetchCollections();

    // Listen for collection update events to refresh in real-time
    _eventSub = EventBus().stream.listen((event) {
      if (event is CollectionUpdatedEvent) {
        if (kDebugMode) {
          print('📝 [CollectionsTab] Received CollectionUpdatedEvent for collection: ${event.collectionId}');
        }
        // Clear cache and refresh collections list
        _collectionsCache.clear();
        _fetchCollections();
      }
    });

    // Listen for search input changes
    _searchController.addListener(() {
      _filterCollections(_searchController.text);
    });

    // Listen for focus changes to rebuild search bar
    _searchFocusNode.addListener(() {
      setState(() {
        // Rebuild to show focus state changes
      });
      
      // Adjust YouTube position based on focus state
      if (_searchFocusNode.hasFocus) {
        _adjustYouTubePosition(isSearching: true);
        // Immediately scroll to show search area when focus is gained
        _scrollToTopForSearch();
      } else {
        // Always restore YouTube position when search field loses focus
        // User is done searching even if search mode is still active
        _adjustYouTubePosition(isSearching: false);
      }
    });
  }

  void _initServices() {
    final apiService = ApiService();
    _collectionService = CollectionService(
      apiService,
      AuthService(apiService),
    );
  }

  void _initAnimations() {
    _fabAnimController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _listAnimController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _searchAnimController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimController,
      curve: Curves.elasticOut,
    ));

    _fabSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _fabAnimController,
      curve: Curves.easeOutCubic,
    ));

    _searchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _searchAnimController,
      curve: Curves.easeOutCubic,
    ));

    // Start FAB animation after a delay
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) _fabAnimController.forward();
    });
  }

  @override
  void dispose() {
    // Restore YouTube position if we were searching when disposed
    if (_isSearching || _searchFocusNode.hasFocus) {
      _adjustYouTubePosition(isSearching: false);
    }
    
    _fabAnimController.dispose();
    _listAnimController.dispose();
    _searchAnimController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _scrollController.dispose(); // Dispose scroll controller

    _eventSub.cancel();
    super.dispose();
  }

  Future<void> _fetchCollections() async {
    // Check cache first
    if (_collectionsCache.containsKey(_sortBy)) {
      setState(() {
        _collections = _collectionsCache[_sortBy]!;
        _isLoading = false;
      });
      _listAnimController.forward();
      return;
    }

    setState(() => _isLoading = true);

    try {
      final collections = await _collectionService.getMyCollections();
      if (!mounted) return;

      // Sort collections based on current sort option
      switch (_sortBy) {
        case 'Name':
          collections.sort((a, b) => a.name.compareTo(b.name));
          break;
        case 'Size':
          collections.sort((a, b) => b.itemCount.compareTo(a.itemCount));
          break;
        case 'Recent':
        default:
          collections.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));
      }

      // Cache the sorted collections
      _collectionsCache[_sortBy] = collections;

      setState(() {
        _collections = collections;
        _filteredCollections = collections;
        _isLoading = false;
      });
      
      // Apply current search filter if active
      if (_searchQuery.isNotEmpty) {
        _filterCollections(_searchQuery);
      }
      
      _listAnimController.forward();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load collections: $e'),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            backgroundColor: Colors.red.shade600,
          ),
        );
      }
      setState(() => _isLoading = false);
    }
  }

  // Update the sort method to use cache
  void _handleSort(String newSortBy) {
    if (_sortBy == newSortBy) return;
    
    HapticFeedback.selectionClick();
    setState(() {
      _sortBy = newSortBy;
      _collections = [];
    });
    
    _listAnimController.reset();
    _fetchCollections();
  }

  void _toggleView() {
    HapticFeedback.lightImpact();
    setState(() => _isGridView = !_isGridView);
    _listAnimController.reset();
    _listAnimController.forward();
  }

  void _toggleSearch() {
    HapticFeedback.lightImpact();
    setState(() {
      _isSearching = !_isSearching;
    });
    
    if (_isSearching) {
      _searchAnimController.forward();
      // Position will be adjusted when focus is gained
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _searchFocusNode.requestFocus();
        }
      });
    } else {
      _searchAnimController.reverse();
      // Always restore YouTube position when search is completely closed
      _adjustYouTubePosition(isSearching: false);
      _searchController.clear();
      _searchQuery = '';
      _filteredCollections = _collections;
      _searchFocusNode.unfocus();
    }
  }

  void _filterCollections(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredCollections = _collections;
      } else {
        _filteredCollections = _collections.where((collection) {
          final name = collection.name.toLowerCase();
          final description = collection.description?.toLowerCase() ?? '';
          final searchLower = query.toLowerCase();
          
          return name.contains(searchLower) || 
                 description.contains(searchLower);
        }).toList();
      }
    });
    
    // Scroll to show search results immediately when user starts typing
    if (query.isNotEmpty) {
      _scrollToTopForSearch();
    }
    
    // If no search results found, restore YouTube position since there's nothing to scroll through
    if (query.isNotEmpty && _filteredCollections.isEmpty) {
      _adjustYouTubePosition(isSearching: false);
    } else if (query.isNotEmpty && _searchFocusNode.hasFocus) {
      // If there are results and still searching, keep YouTube in search position
      _adjustYouTubePosition(isSearching: true);
    }
  }

  void _onSearchChanged(String value) {
    _filterCollections(value);
  }

  Future<void> _createNewCollection() async {
    HapticFeedback.mediumImpact();
    
    final result = await Navigator.push<Collection>(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateCollectionScreen(),
      ),
    );
    
    if (result != null && mounted) {
      setState(() {
        _collections.insert(0, result);
        // Clear cache to refresh data
        _collectionsCache.clear();
      });
      
      // Show success message with animation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.check, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Collection Created!',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    Text(
                      result.name,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green.shade600,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          margin: const EdgeInsets.all(16),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final padding = isSmallScreen ? 12.0 : 16.0;
    
    // Use filtered collections for display
    final displayCollections = _isSearching ? _filteredCollections : _collections;
    
    if (displayCollections.isEmpty && !_isLoading) {
      return _isSearching && _searchQuery.isNotEmpty 
        ? _buildEmptySearchState() 
        : _buildEmptyState();
    }

    return Stack(
      children: [
        RefreshIndicator(
          onRefresh: () async {
            _collectionsCache.clear();
            await _fetchCollections();
          },
          color: Theme.of(context).colorScheme.primary,
          child: CustomScrollView(
            controller: _scrollController, // Add scroll controller
            slivers: [
              // Modern search bar
              if (_isSearching)
                SliverToBoxAdapter(
                  child: AnimatedBuilder(
                    animation: _searchAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, 20 * (1 - _searchAnimation.value)),
                        child: Opacity(
                          opacity: _searchAnimation.value,
                          child: Container(
                            margin: EdgeInsets.fromLTRB(padding, 4, padding, 8), // Reduced margins for compact design
                            child: _buildModernSearchBar(),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              
              // Control header
              SliverPersistentHeader(
                pinned: true,
                delegate: _ModernCollectionHeaderDelegate(
                  isSmallScreen: isSmallScreen,
                  isGridView: _isGridView,
                  onViewToggle: _toggleView,
                  sortBy: _sortBy,
                  onSortChanged: _handleSort,
                  onSearch: _toggleSearch,
                  isSearching: _isSearching,
                ),
              ),
              
              // Collections content
              SliverPadding(
                padding: EdgeInsets.fromLTRB(
                  padding,
                  padding / 2,
                  padding,
                  padding + MediaQuery.of(context).padding.bottom + 100,
                ),
                sliver: _isLoading
                  ? _buildLoadingGrid(isSmallScreen, padding)
                  : AnimatedBuilder(
                      animation: _listAnimController,
                      builder: (context, child) {
                        return _isGridView
                          ? _buildCollectionsGridModern(isSmallScreen, padding, displayCollections)
                          : _buildCollectionsListModern(isSmallScreen, padding, displayCollections);
                      },
                    ),
              ),
            ],
          ),
        ),
        
        // Enhanced floating action button
        Positioned(
          bottom: 24 + MediaQuery.of(context).padding.bottom,
          right: 24,
          child: AnimatedBuilder(
            animation: _fabAnimController,
            builder: (context, child) {
              return SlideTransition(
                position: _fabSlideAnimation,
                child: ScaleTransition(
                  scale: _fabScaleAnimation,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: FloatingActionButton(
                      onPressed: _createNewCollection,
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Icon(Icons.add, size: 28),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildModernSearchBar() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Colors matching the music chat design
    final searchBarColor = isDark 
        ? theme.colorScheme.surfaceVariant.withOpacity(0.3)
        : theme.colorScheme.surfaceVariant.withOpacity(0.5);
    final searchBarBorder = theme.colorScheme.outline.withOpacity(0.2);
    
    return Container(
      height: 48, // Smaller, sleeker height like music chat
      decoration: BoxDecoration(
        color: searchBarColor,
        borderRadius: BorderRadius.circular(24), // Fully rounded like music chat
        border: _searchFocusNode.hasFocus || _searchController.text.isNotEmpty
            ? null // No border when active for cleaner look
            : Border.all(
                color: searchBarBorder,
                width: 1,
              ),
        // Subtle shadow for depth
        boxShadow: [
          if (_searchFocusNode.hasFocus) BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.15),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Search icon with primary color when active
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: Icon(
              Icons.search_rounded,
              size: 20,
              color: _searchFocusNode.hasFocus || _searchController.text.isNotEmpty
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurfaceVariant,
            ),
          ),
          
          // Text field
          Expanded(
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              onChanged: _onSearchChanged,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
              cursorColor: theme.colorScheme.primary,
              cursorHeight: 18,
              cursorWidth: 2,
              decoration: InputDecoration(
                hintText: 'Search collections...',
                hintStyle: TextStyle(
                  color: theme.colorScheme.onSurfaceVariant.withOpacity(0.6),
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 14,
                ),
              ),
              onTap: () {
                HapticFeedback.selectionClick();
                // Immediately scroll to optimal search position when tapping search
                _scrollToTopForSearch();
                if (!_searchFocusNode.hasFocus) {
                  _adjustYouTubePosition(isSearching: true);
                }
              },
              onSubmitted: (_) {
                // Auto-scroll to show results when submitted
                _scrollToTopForSearch();
              },
            ),
          ),
          
          // Clear button
          if (_searchQuery.isNotEmpty)
            IconButton(
              icon: Icon(
                Icons.close_rounded,
                size: 18,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              onPressed: () {
                HapticFeedback.lightImpact();
                _searchController.clear();
                _onSearchChanged('');
              },
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
              padding: EdgeInsets.zero,
            ),
        ],
      ),
    );
  }

  // Add method to adjust YouTube embed position
  void _adjustYouTubePosition({required bool isSearching}) {
    try {
      final context = AppNavigationSystem().navigatorKey.currentContext;
      if (context != null) {
        final screenHeight = MediaQuery.of(context).size.height;
        final screenWidth = MediaQuery.of(context).size.width;
        
        // Add small delay to ensure proper positioning
        Future.delayed(const Duration(milliseconds: 100), () {
          if (isSearching) {
            // Move YouTube to upper area when searching
            final searchBarHeight = 120.0; // Height of search bar + margins
            final adjustedTopSpace = 64.0 + searchBarHeight;
            
            PictureInPicture.updatePiPParams(
              pipParams: PiPParams(
                pipWindowHeight: 125,
                pipWindowWidth: 125,
                bottomSpace: screenHeight * 0.5, // Higher up for search results
                leftSpace: 12,
                rightSpace: screenWidth - 125 - 12,
                topSpace: adjustedTopSpace,
                maxSize: const Size(125, 125),
                minSize: const Size(125, 125),
                movable: true,
                resizable: false,
                initialCorner: PIPViewCorner.topRight, // Move to top right
              ),
            );
            
            if (kDebugMode) {
              print('🎬 [CollectionsTab] Moved YouTube to search position: topRight, topSpace=$adjustedTopSpace');
            }
          } else {
            // Restore EXACT original YouTube position using same logic as YouTubeProvider
            // Calculate responsive spacing (based on current perfect positioning)
            // Current: leftSpace: 12, rightSpace: 260 on ~390px screens
            final leftSpacePercent = 12 / 390; // ~3.08%
            final rightSpacePercent = 260 / 390; // ~66.67%
            
            final responsiveLeftSpace = (screenWidth * leftSpacePercent).round().toDouble();
            final responsiveRightSpace = (screenWidth * rightSpacePercent).round().toDouble();
            
            PictureInPicture.updatePiPParams(
              pipParams: PiPParams(
                pipWindowHeight: 125,
                pipWindowWidth: 125,
                bottomSpace: 64, // Exact original bottom space
                leftSpace: responsiveLeftSpace, // Exact original responsive left spacing
                rightSpace: responsiveRightSpace, // Exact original responsive right spacing
                topSpace: 64, // Exact original top space
                maxSize: const Size(125, 125),
                minSize: const Size(125, 125),
                movable: true,
                resizable: false,
                initialCorner: PIPViewCorner.topLeft, // Exact original corner
              ),
            );
            
            if (kDebugMode) {
              print('🎬 [CollectionsTab] Restored YouTube to original position: topLeft');
              print('🎬 [CollectionsTab] Screen: ${screenWidth}x$screenHeight');
              print('🎬 [CollectionsTab] Position: left=$responsiveLeftSpace, right=$responsiveRightSpace, bottom=64, top=64');
            }
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [CollectionsTab] Error adjusting YouTube position: $e');
      }
    }
  }

  Widget _buildEmptySearchState() {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isVerySmallHeight = screenHeight < 700;
    
    // Make the empty search state much more compact
    final iconSize = isSmallScreen ? 40.0 : (isVerySmallHeight ? 45.0 : 50.0);
    final titleFontSize = isSmallScreen ? 16.0 : (isVerySmallHeight ? 17.0 : 18.0);
    final descriptionFontSize = isSmallScreen ? 12.0 : (isVerySmallHeight ? 13.0 : 14.0);
    final primarySpacing = isSmallScreen ? 12.0 : (isVerySmallHeight ? 14.0 : 16.0);
    final secondarySpacing = isSmallScreen ? 4.0 : (isVerySmallHeight ? 6.0 : 8.0);
    final horizontalPadding = isSmallScreen ? 20.0 : 32.0;
    
    return SingleChildScrollView(
      child: Container(
        constraints: BoxConstraints(
          // Ensure it fits within available space, accounting for search bar and header
          minHeight: 200,
          maxHeight: screenHeight * 0.6, // Maximum 60% of screen height
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: iconSize + 20,
                height: iconSize + 20,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      Theme.of(context).colorScheme.primary.withOpacity(0.05),
                    ],
                  ),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                    width: 2,
                  ),
                ),
                child: Icon(
                  Icons.search_off,
                  size: iconSize,
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
                ),
              ),
              SizedBox(height: primarySpacing),
              Text(
                'No Results Found',
                style: TextStyle(
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.w700,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              SizedBox(height: secondarySpacing),
              Flexible( // Use Flexible to prevent overflow
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                  child: Text(
                    'No collections match "${_searchQuery}". Try searching with different keywords.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: descriptionFontSize,
                      height: 1.3,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                    maxLines: 3, // Limit lines to prevent overflow
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              SizedBox(height: primarySpacing),
              TextButton.icon(
                onPressed: () {
                  _searchController.clear();
                  _onSearchChanged('');
                },
                icon: const Icon(Icons.clear, size: 16),
                label: Text(
                  'Clear Search',
                  style: TextStyle(fontSize: descriptionFontSize),
                ),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.primary,
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 16 : 20,
                    vertical: isSmallScreen ? 8 : 10,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isVerySmallHeight = screenHeight < 700;
    
    // Make sizes responsive - reduced all values to make it more compact
    final iconContainerSize = isSmallScreen ? 80.0 : (isVerySmallHeight ? 90.0 : 100.0);
    final iconSize = isSmallScreen ? 35.0 : (isVerySmallHeight ? 40.0 : 45.0);
    final titleFontSize = isSmallScreen ? 18.0 : (isVerySmallHeight ? 19.0 : 20.0);
    final descriptionFontSize = isSmallScreen ? 13.0 : (isVerySmallHeight ? 14.0 : 15.0);
    final primarySpacing = isSmallScreen ? 16.0 : (isVerySmallHeight ? 18.0 : 20.0);
    final secondarySpacing = isSmallScreen ? 6.0 : (isVerySmallHeight ? 8.0 : 10.0);
    final bottomSpacing = isSmallScreen ? 18.0 : (isVerySmallHeight ? 20.0 : 24.0);
    final horizontalPadding = isSmallScreen ? 20.0 : 32.0;
    
    return AnimatedBuilder(
      animation: _fabAnimController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fabAnimController,
          child: SingleChildScrollView(
            child: Container(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height * 0.25,
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: iconContainerSize,
                      height: iconContainerSize,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Theme.of(context).colorScheme.primary.withOpacity(0.1),
                            Theme.of(context).colorScheme.primary.withOpacity(0.05),
                          ],
                        ),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        Icons.collections_bookmark_outlined,
                        size: iconSize,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    SizedBox(height: primarySpacing),
                    Text(
                      'No Collections Yet',
                      style: TextStyle(
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: secondarySpacing),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                      child: Text(
                        'Create your first collection to start organizing your music pins by theme, mood, or location',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: descriptionFontSize,
                          height: 1.2,
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ),
                    SizedBox(height: bottomSpacing),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(14),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.primary.withOpacity(0.25),
                            blurRadius: 16,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: _createNewCollection,
                        icon: Icon(Icons.add, size: isSmallScreen ? 18 : 20),
                        label: Text(
                          'Create Collection',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 13 : 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            horizontal: isSmallScreen ? 20 : 24,
                            vertical: isSmallScreen ? 10 : 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(14),
                          ),
                          elevation: 0,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCollectionCard(Collection collection, bool isSmallScreen) {
    final Color primaryColor = collection.primaryColor ?? AppTheme.primaryColor;
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Material(
          color: Theme.of(context).cardColor,
          child: InkWell(
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CollectionDetailScreen(collection: collection),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Cover image section
                AspectRatio(
                  aspectRatio: 1,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Background gradient
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              primaryColor.withOpacity(0.2),
                              primaryColor.withOpacity(0.1),
                            ],
                          ),
                        ),
                      ),
                      // Cover images
                      Center(
                        child: _buildCoverImageStack(
                          collection.coverImageUrls,
                          primaryColor,
                          isSmallScreen,
                        ),
                      ),
                                                // Privacy icon in top right
                          Positioned(
                            top: 12,
                            right: 12,
                            child: Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.4),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                              child: Icon(
                                collection.isPublic ? Icons.public : Icons.lock,
                                size: isSmallScreen ? 12 : 14,
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ),
                          
                          // Overlay gradient
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              padding: EdgeInsets.all(isSmallScreen ? 12 : 14),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.bottomCenter,
                                  end: Alignment.topCenter,
                                  colors: [
                                    Colors.black.withOpacity(0.7),
                                    Colors.transparent,
                                  ],
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    TextEncodingUtils.cleanCollectionText(collection.name, isDescription: false),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                      fontSize: isSmallScreen ? 14 : 16,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.3),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.push_pin,
                                          size: isSmallScreen ? 12 : 14,
                                          color: Colors.white.withOpacity(0.9),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          collection.itemCount.toString(),
                                          style: TextStyle(
                                            color: Colors.white.withOpacity(0.9),
                                            fontSize: isSmallScreen ? 11 : 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedCoverImageStack(List<String> images, Color color, bool isSmallScreen) {
    if (images.isEmpty) {
      return Container(
        width: isSmallScreen ? 90 : 110,
        height: isSmallScreen ? 90 : 110,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withOpacity(0.15),
              color.withOpacity(0.08),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 2,
          ),
        ),
        child: Icon(
          Icons.collections_bookmark,
          size: isSmallScreen ? 40 : 48,
          color: color.withOpacity(0.8),
        ),
      );
    }

    final stackSize = isSmallScreen ? 110.0 : 130.0;
    final imageSize = stackSize * 0.75;

    return SizedBox(
      width: stackSize,
      height: stackSize,
      child: Stack(
        alignment: Alignment.center,
        children: [
          if (images.length > 2)
            Positioned(
              left: 0,
              top: stackSize * 0.1,
              child: Transform.rotate(
                angle: -0.15,
                child: _buildEnhancedCoverImage(images[2], imageSize * 0.8, color),
              ),
            ),
          if (images.length > 1)
            Positioned(
              right: 0,
              top: stackSize * 0.1,
              child: Transform.rotate(
                angle: 0.15,
                child: _buildEnhancedCoverImage(images[1], imageSize * 0.8, color),
              ),
            ),
          Positioned(
            bottom: 0,
            child: _buildEnhancedCoverImage(images[0], imageSize, color),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedCoverImage(String imageUrl, double size, Color fallbackColor) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.25),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Image.network(
          imageUrl,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  fallbackColor.withOpacity(0.2),
                  fallbackColor.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              Icons.music_note,
              size: size * 0.4,
              color: fallbackColor.withOpacity(0.8),
            ),
          ),
        ),
      ),
    );
  }

  String _formatTimeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo ago';
    } else if (difference.inDays > 7) {
      return '${(difference.inDays / 7).floor()}w ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'now';
    }
  }

  Widget _buildCoverImageStack(List<String> images, Color color, bool isSmallScreen) {
    if (images.isEmpty) {
      return Container(
        width: isSmallScreen ? 80 : 100,
        height: isSmallScreen ? 80 : 100,
        decoration: BoxDecoration(
          color: color.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          Icons.library_music,
          size: isSmallScreen ? 32 : 40,
          color: color,
        ),
      );
    }

    final stackSize = isSmallScreen ? 100.0 : 120.0;
    final imageSize = stackSize * 0.7;

    return SizedBox(
      width: stackSize,
      height: stackSize,
      child: Stack(
        alignment: Alignment.center,
        children: [
          if (images.length > 2)
            Positioned(
              left: 0,
              top: stackSize * 0.15,
              child: Transform.rotate(
                angle: -0.2,
                child: _buildCoverImage(images[2], imageSize * 0.85, color),
              ),
            ),
          if (images.length > 1)
            Positioned(
              right: 0,
              top: stackSize * 0.15,
              child: Transform.rotate(
                angle: 0.2,
                child: _buildCoverImage(images[1], imageSize * 0.85, color),
              ),
            ),
          Positioned(
            bottom: 0,
            child: _buildCoverImage(images[0], imageSize, color),
          ),
        ],
      ),
    );
  }

  Widget _buildCoverImage(String imageUrl, double size, Color fallbackColor) {
    // Check if it's a Cloudinary URL to extract public ID
    String? publicId;
    if (imageUrl.contains('res.cloudinary.com')) {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      if (pathSegments.length >= 6) {
        // Extract public ID from Cloudinary URL
        publicId = pathSegments.sublist(6).join('/').replaceAll(RegExp(r'\.[^.]*$'), '');
      }
    }

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: publicId != null
          ? CloudinaryImage(
              publicId: publicId,
              width: size,
              height: size,
              quality: 85,
              format: 'webp',
              borderRadius: BorderRadius.circular(12),
              errorWidget: Container(
                decoration: BoxDecoration(
                  color: fallbackColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.library_music,
                  size: size * 0.4,
                  color: fallbackColor,
                ),
              ),
            )
          : ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                imageUrl,
                fit: BoxFit.cover,
                width: size,
                height: size,
                errorBuilder: (context, error, stackTrace) => Container(
                  decoration: BoxDecoration(
                    color: fallbackColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.library_music,
                    size: size * 0.4,
                    color: fallbackColor,
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildLoadingGrid(bool isSmallScreen, double padding) {
    return SliverGrid(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getGridCrossAxisCount(),
        mainAxisSpacing: padding,
        crossAxisSpacing: padding,
        childAspectRatio: 1,
      ),
      delegate: SliverChildBuilderDelegate(
        (context, index) => _buildShimmerCard(isSmallScreen),
        childCount: 6,
      ),
    );
  }

  Widget _buildShimmerCard(bool isSmallScreen) {
    return Shimmer.fromColors(
      baseColor: Theme.of(context).cardColor,
      highlightColor: Theme.of(context).colorScheme.surface,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  int _getGridCrossAxisCount() {
    final width = MediaQuery.of(context).size.width;
    if (width > 900) return 3;
    if (width > 600) return 2;
    return 2;
  }

  Widget _buildCollectionsGridModern(bool isSmallScreen, double padding, List<Collection> collections) {
    return SliverGrid(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getGridCrossAxisCount(),
        mainAxisSpacing: padding,
        crossAxisSpacing: padding,
        childAspectRatio: 0.95,
      ),
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          return _buildModernCollectionCard(collections[index], isSmallScreen);
        },
        childCount: collections.length,
      ),
    );
  }

  Widget _buildCollectionsListModern(bool isSmallScreen, double padding, List<Collection> collections) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          return Padding(
            padding: EdgeInsets.only(bottom: padding),
            child: _buildModernCollectionListItem(collections[index], isSmallScreen),
          );
        },
        childCount: collections.length,
      ),
    );
  }

  Widget _buildModernCollectionCard(Collection collection, bool isSmallScreen) {
    final Color primaryColor = collection.primaryColor ?? AppTheme.primaryColor;
    
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 400 + (math.Random().nextInt(200))),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, animation, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - animation)),
          child: Opacity(
            opacity: animation,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: primaryColor.withOpacity(0.1), 
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withOpacity(0.08),
                    blurRadius: 24,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.04),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Material(
                  color: Theme.of(context).cardColor,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(24),
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => CollectionDetailScreen(collection: collection),
                        ),
                      );
                    },
                    child: Stack(
                      children: [
                        // Background gradient
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  primaryColor.withOpacity(0.05),
                                  primaryColor.withOpacity(0.02),
                                ],
                              ),
                            ),
                          ),
                        ),
                        
                        // Cover images with enhanced positioning
                        Positioned.fill(
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Center(
                              child: _buildEnhancedCoverImageStack(
                                collection.coverImageUrls,
                                primaryColor,
                                isSmallScreen,
                              ),
                            ),
                          ),
                        ),
                        
                        // Privacy icon in top right
                        Positioned(
                          top: 12,
                          right: 12,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.4),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              collection.isPublic ? Icons.public : Icons.lock,
                              size: 16,
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                        ),
                        
                        // Enhanced info overlay
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter,
                                stops: const [0.0, 0.6, 1.0],
                                colors: [
                                  Colors.black.withOpacity(0.8),
                                  Colors.black.withOpacity(0.4),
                                  Colors.transparent,
                                ],
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  TextEncodingUtils.cleanCollectionText(collection.name, isDescription: false),
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                    fontSize: isSmallScreen ? 16 : 18,
                                    letterSpacing: -0.3,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: primaryColor.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: primaryColor.withOpacity(0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.location_on,
                                            size: 14,
                                            color: Colors.white.withOpacity(0.95),
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            collection.itemCount.toString(),
                                            style: TextStyle(
                                              color: Colors.white.withOpacity(0.95),
                                              fontSize: isSmallScreen ? 12 : 13,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Spacer(),
                                    Text(
                                      _formatTimeAgo(collection.lastUpdated),
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.7),
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernCollectionListItem(Collection collection, bool isSmallScreen) {
    final Color primaryColor = collection.primaryColor ?? AppTheme.primaryColor;
    return Container(
      height: 88,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: Theme.of(context).dividerColor.withOpacity(0.08), width: 1.2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(18),
        child: Material(
          color: Theme.of(context).cardColor,
          child: InkWell(
            borderRadius: BorderRadius.circular(18),
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CollectionDetailScreen(collection: collection),
              ),
            ),
            child: Stack(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Container(
                      width: 80,
                      height: 88,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(18),
                          bottomLeft: Radius.circular(18),
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            primaryColor.withOpacity(0.18),
                            primaryColor.withOpacity(0.08),
                          ],
                        ),
                      ),
                      child: Center(
                        child: collection.coverImageUrls.isNotEmpty
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: Image.network(
                                  collection.coverImageUrls.first,
                                  width: 56,
                                  height: 56,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) => Container(
                                    width: 56,
                                    height: 56,
                                    decoration: BoxDecoration(
                                      color: primaryColor.withOpacity(0.15),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.library_music,
                                      size: 28,
                                      color: primaryColor,
                                    ),
                                  ),
                                ),
                              )
                            : Container(
                                width: 56,
                                height: 56,
                                decoration: BoxDecoration(
                                  color: primaryColor.withOpacity(0.15),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.library_music,
                                  size: 28,
                                  color: primaryColor,
                                ),
                              ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 6, horizontal: isSmallScreen ? 12 : 16),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                TextEncodingUtils.cleanCollectionText(collection.name, isDescription: false),
                                style: TextStyle(
                                  fontWeight: FontWeight.w700,
                                  fontSize: isSmallScreen ? 15 : 17,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 2),
                                decoration: BoxDecoration(
                                  color: primaryColor.withOpacity(0.12),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.push_pin, size: 13, color: primaryColor),
                                    const SizedBox(width: 4),
                                    Text(
                                      collection.itemCount.toString(),
                                      style: TextStyle(
                                        color: primaryColor,
                                        fontSize: isSmallScreen ? 11 : 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                // Privacy icon in top right
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).dividerColor.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      collection.isPublic ? Icons.public : Icons.lock,
                      size: 14,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  late final StreamSubscription _eventSub;

  /// Extracts public ID from Cloudinary URL for optimization
  String _extractPublicId(String imageUrl) {
    if (imageUrl.contains('res.cloudinary.com')) {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      if (pathSegments.length >= 6) {
        // Extract public ID from Cloudinary URL
        return pathSegments.sublist(6).join('/').replaceAll(RegExp(r'\.[^.]*$'), '');
      }
    }
    return imageUrl; // Fallback to original URL
  }

  void _scrollToTopForSearch() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        final screenHeight = MediaQuery.of(context).size.height;
        
        // Calculate intelligent scroll position for optimal search UX
        // Profile header expands to 27% of screen height, collapses to 56px toolbar
        final expandedHeaderHeight = screenHeight * 0.27;
        final collapsedHeaderHeight = 56.0;
        final headerScrollDistance = expandedHeaderHeight - collapsedHeaderHeight;
        
        // Add space for tab bar and positioning the search results optimally
        final tabBarHeight = 48.0;
        final searchBarHeight = 48.0;
        final optimalPadding = 20.0; // Small padding for breathing room
        
        // Total scroll position: collapse header + tab bar + search bar + padding
        final targetPosition = headerScrollDistance + tabBarHeight + searchBarHeight + optimalPadding;
        
        // Don't scroll past the maximum scroll extent
        final maxScroll = _scrollController.position.maxScrollExtent;
        final scrollTo = math.min(targetPosition, maxScroll);
        
        if (kDebugMode) {
          print('🔍 [CollectionsTab] Smart scroll positioning:');
          print('  📱 Screen height: ${screenHeight.toStringAsFixed(0)}px');
          print('  📏 Header collapse distance: ${headerScrollDistance.toStringAsFixed(0)}px');
          print('  🎯 Target scroll position: ${targetPosition.toStringAsFixed(0)}px');
          print('  ✅ Final scroll position: ${scrollTo.toStringAsFixed(0)}px');
        }
        
        _scrollController.animateTo(
          scrollTo,
          duration: const Duration(milliseconds: 400), // Slightly longer for smoother feel
          curve: Curves.easeOutCubic, // Better easing curve
        );
      }
    });
  }
}

class _CollectionHeaderDelegate extends SliverPersistentHeaderDelegate {
  final bool isSmallScreen;
  final bool isGridView;
  final VoidCallback onViewToggle;
  final String sortBy;
  final ValueChanged<String> onSortChanged;
  final VoidCallback onSearch;
  final bool isSearching;

  _CollectionHeaderDelegate({
    required this.isSmallScreen,
    required this.isGridView,
    required this.onViewToggle,
    required this.sortBy,
    required this.onSortChanged,
    required this.onSearch,
    required this.isSearching,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final theme = Theme.of(context);
    return Container(
      color: theme.scaffoldBackgroundColor,
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 12 : 16,
        vertical: isSmallScreen ? 8 : 12,
      ),
      child: Row(
        children: [
          // View toggle
          IconButton(
            onPressed: onViewToggle,
            icon: Icon(
              isGridView ? Icons.view_list : Icons.grid_view,
              size: isSmallScreen ? 20 : 24,
            ),
            style: IconButton.styleFrom(
              backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
            ),
          ),
          const SizedBox(width: 12),
          
          // Sort dropdown
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.sort,
                  size: isSmallScreen ? 16 : 18,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                DropdownButton<String>(
                  value: sortBy,
                  items: ['Recent', 'Name', 'Size']
                      .map((label) => DropdownMenuItem(
                            value: label,
                            child: Text(
                              label,
                              style: TextStyle(
                                fontSize: isSmallScreen ? 12 : 14,
                              ),
                            ),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) onSortChanged(value);
                  },
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                  underline: const SizedBox(),
                  icon: Icon(
                    Icons.arrow_drop_down,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          
          const Spacer(),
          
          // Search button
          IconButton(
            onPressed: onSearch,
            icon: Icon(this.isSearching ? Icons.close : Icons.search),
            style: IconButton.styleFrom(
              backgroundColor: this.isSearching 
                ? theme.colorScheme.primary.withOpacity(0.2)
                : theme.colorScheme.primary.withOpacity(0.1),
              foregroundColor: this.isSearching 
                ? theme.colorScheme.primary
                : null,
            ),
          ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => isSmallScreen ? 56 : 72;

  @override
  double get minExtent => isSmallScreen ? 56 : 72;

  @override
  bool shouldRebuild(covariant _CollectionHeaderDelegate oldDelegate) {
    return isSmallScreen != oldDelegate.isSmallScreen ||
           isGridView != oldDelegate.isGridView ||
           sortBy != oldDelegate.sortBy ||
           isSearching != oldDelegate.isSearching;
  }
}

// Add custom painter for dashed border
class DashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double gap;

  DashedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.gap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final Path path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        const Radius.circular(16),
      ));

    final Path dashPath = Path();
    final double dashWidth = 6;

    for (ui.PathMetric metric in path.computeMetrics()) {
      double distance = 0;
      while (distance < metric.length) {
        dashPath.addPath(
          metric.extractPath(distance, distance + dashWidth),
          Offset.zero,
        );
        distance += dashWidth + gap;
      }
    }

    canvas.drawPath(dashPath, paint);
  }

  @override
  bool shouldRepaint(DashedBorderPainter oldDelegate) {
    return color != oldDelegate.color ||
           strokeWidth != oldDelegate.strokeWidth ||
           gap != oldDelegate.gap;
  }
}

class _ModernCollectionHeaderDelegate extends SliverPersistentHeaderDelegate {
  final bool isSmallScreen;
  final bool isGridView;
  final VoidCallback onViewToggle;
  final String sortBy;
  final ValueChanged<String> onSortChanged;
  final VoidCallback onSearch;
  final bool isSearching;

  _ModernCollectionHeaderDelegate({
    required this.isSmallScreen,
    required this.isGridView,
    required this.onViewToggle,
    required this.sortBy,
    required this.onSortChanged,
    required this.onSearch,
    required this.isSearching,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final theme = Theme.of(context);
    return Container(
      color: theme.scaffoldBackgroundColor,
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 12 : 16,
        vertical: isSmallScreen ? 8 : 12,
      ),
      child: Row(
        children: [
          // View toggle
          IconButton(
            onPressed: onViewToggle,
            icon: Icon(
              isGridView ? Icons.view_list : Icons.grid_view,
              size: isSmallScreen ? 20 : 24,
            ),
            style: IconButton.styleFrom(
              backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
            ),
          ),
          const SizedBox(width: 12),
          
          // Sort dropdown
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.sort,
                  size: isSmallScreen ? 16 : 18,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                DropdownButton<String>(
                  value: sortBy,
                  items: ['Recent', 'Name', 'Size']
                      .map((label) => DropdownMenuItem(
                            value: label,
                            child: Text(
                              label,
                              style: TextStyle(
                                fontSize: isSmallScreen ? 12 : 14,
                              ),
                            ),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) onSortChanged(value);
                  },
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                  underline: const SizedBox(),
                  icon: Icon(
                    Icons.arrow_drop_down,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          
          const Spacer(),
          
          // Search button
          IconButton(
            onPressed: onSearch,
            icon: Icon(isSearching ? Icons.close : Icons.search),
            style: IconButton.styleFrom(
              backgroundColor: isSearching 
                ? theme.colorScheme.primary.withOpacity(0.2)
                : theme.colorScheme.primary.withOpacity(0.1),
              foregroundColor: isSearching 
                ? theme.colorScheme.primary
                : null,
            ),
          ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => isSmallScreen ? 56 : 72;

  @override
  double get minExtent => isSmallScreen ? 56 : 72;

  @override
  bool shouldRebuild(covariant _ModernCollectionHeaderDelegate oldDelegate) {
    return isSmallScreen != oldDelegate.isSmallScreen ||
           isGridView != oldDelegate.isGridView ||
           sortBy != oldDelegate.sortBy ||
           isSearching != oldDelegate.isSearching;
  }
} 