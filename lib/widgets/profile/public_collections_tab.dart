import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/public_profile_provider.dart';
import '../../models/collection_model.dart';
import 'package:shimmer/shimmer.dart';
import '../../services/collection_service.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../screens/collections/collection_detail_screen.dart';
import '../../widgets/common/cloudinary_image.dart';
import '../../utils/text_encoding_utils.dart';

class PublicCollectionsTab extends StatefulWidget {
  final int userId;

  const PublicCollectionsTab({
    Key? key,
    required this.userId,
  }) : super(key: key);

  @override
  State<PublicCollectionsTab> createState() => _PublicCollectionsTabState();
}

class _PublicCollectionsTabState extends State<PublicCollectionsTab> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  List<Collection> _collections = [];
  bool _isLoading = false;
  late CollectionService _collectionService;

  @override
  void initState() {
    super.initState();
    
    // Initialize services
    final apiService = ApiService();
    _collectionService = CollectionService(
      apiService,
      AuthService(apiService),
    );
    
    _fetchPublicCollections();
  }

  Future<void> _fetchPublicCollections() async {
    setState(() => _isLoading = true);
    
    try {
      final collections = await _collectionService.getCollections();
      setState(() {
        _collections = collections.where((c) => c.isPublic).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load public collections: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    if (_isLoading) {
      return _buildLoadingShimmer();
    }

    if (_collections.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _fetchPublicCollections,
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverGrid(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: _getCrossAxisCount(context),
                childAspectRatio: 0.8,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final collection = _collections[index];
                  return _buildCollectionCard(context, collection);
                },
                childCount: _collections.length,
              ),
            ),
          ),
          
          // Loading indicator for pagination
          if (_isLoading && _collections.isNotEmpty)
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ),
          
          // Bottom spacing
          const SliverToBoxAdapter(
            child: SizedBox(height: 80),
          ),
        ],
      ),
    );
  }

  Widget _buildCollectionCard(BuildContext context, Collection collection) {
    final theme = Theme.of(context);
    final isSmallScreen = MediaQuery.of(context).size.width < 360;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _handleCollectionTap(context, collection),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Collection cover
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                  color: theme.colorScheme.surfaceVariant,
                ),
                child: _buildCoverImage(collection),
              ),
            ),
            
            // Collection info
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      TextEncodingUtils.cleanCollectionText(collection.name, isDescription: false),
                      style: TextStyle(
                        fontSize: isSmallScreen ? 13 : 14,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    if (collection.description.isNotEmpty)
                      Text(
                        TextEncodingUtils.cleanCollectionText(collection.description, isDescription: true),
                        style: TextStyle(
                          fontSize: isSmallScreen ? 11 : 12,
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    const Spacer(),
                    Row(
                      children: [
                        Icon(
                          Icons.music_note,
                          size: isSmallScreen ? 12 : 14,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${collection.itemCount} items',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 10 : 11,
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const Spacer(),
                        if (collection.isPublic)
                          Icon(
                            Icons.public,
                            size: isSmallScreen ? 12 : 14,
                            color: Colors.green,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoverImage(Collection collection) {
    final theme = Theme.of(context);
    final primaryColor = collection.primaryColor ?? theme.colorScheme.primary;
    const size = 80.0;
    
    if (collection.coverImageUrls.isEmpty) {
      return Container(
        width: size,
        height: size,
        margin: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: primaryColor.withOpacity(0.3),
          ),
        ),
        child: Icon(
          Icons.collections_bookmark,
          color: primaryColor,
          size: 32,
        ),
      );
    }

    final coverUrl = collection.coverImageUrls.first;
    String? publicId;
    if (coverUrl.contains('res.cloudinary.com')) {
      final uri = Uri.parse(coverUrl);
      final pathSegments = uri.pathSegments;
      if (pathSegments.length >= 6) {
        publicId = pathSegments.sublist(6).join('/').replaceAll(RegExp(r'\.[^.]*$'), '');
      }
    }

    return Container(
      width: size,
      height: size,
      margin: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: publicId != null
                         ? CloudinaryImage(
                 publicId: publicId,
                 width: size,
                 height: size,
                 quality: 85,
                 format: 'webp',
                 errorWidget: _buildPlaceholder(theme),
               )
            : Image.network(
                coverUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildPlaceholder(theme),
              ),
      ),
    );
  }

  Widget _buildPlaceholder(ThemeData theme) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: theme.colorScheme.surfaceVariant,
      child: Icon(
        Icons.collections,
        size: 48,
        color: theme.colorScheme.primary.withOpacity(0.5),
      ),
    );
  }

  Widget _buildLoadingShimmer() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Shimmer.fromColors(
      baseColor: isDark ? Colors.grey[800]! : Colors.grey[300]!,
      highlightColor: isDark ? Colors.grey[700]! : Colors.grey[100]!,
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: _getCrossAxisCount(context),
          childAspectRatio: 0.8,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: 6,
        itemBuilder: (context, index) => Container(
          decoration: BoxDecoration(
            color: isDark ? Colors.grey[850] : Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.collections_outlined,
              size: 48,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No public collections yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This user hasn\'t shared any public collections',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 1;
    } else if (screenWidth < 600) {
      return 2;
    } else if (screenWidth < 900) {
      return 3;
    } else {
      return 4;
    }
  }

  void _handleCollectionTap(BuildContext context, Collection collection) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CollectionDetailScreen(collection: collection),
      ),
    );
  }
} 