import 'package:flutter/material.dart';
import '../../config/themes.dart';

class BOPHistoryTab extends StatelessWidget {
  const BOPHistoryTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Sample data for BOP history
    final List<Map<String, dynamic>> activities = [
      {
        'type': 'dropped_pin',
        'title': 'Dropped a new pin',
        'details': 'Hotel California by Eagles',
        'location': 'Central Park, NY',
        'time': '2 hours ago',
        'image': 'https://i.scdn.co/image/ab67616d0000b273d1d6c90b97b9f8d6e5495df7'
      },
      {
        'type': 'collected_pin',
        'title': 'Collected a pin',
        'details': 'Blinding Lights by The Weeknd',
        'location': 'Times Square, NY',
        'time': 'Yesterday',
        'image': 'https://i.scdn.co/image/ab67616d0000b273c5649add07ed3720be9d5526'
      },
      {
        'type': 'followed_user',
        'title': 'Started following',
        'details': 'SarahMusic92',
        'time': '3 days ago',
        'image': 'https://randomuser.me/api/portraits/women/42.jpg'
      },
      {
        'type': 'friend_request',
        'title': 'Accepted friend request',
        'details': 'MusicExplorer',
        'time': '5 days ago',
        'image': 'https://randomuser.me/api/portraits/men/22.jpg'
      },
      {
        'type': 'created_collection',
        'title': 'Created new collection',
        'details': 'Summer Vibes 2023',
        'count': '12 pins',
        'time': '1 week ago',
      },
      {
        'type': 'milestone',
        'title': 'Reached milestone',
        'details': '50 pins collected',
        'time': '2 weeks ago',
      },
      {
        'type': 'achievement',
        'title': 'Earned achievement',
        'details': 'NYC Explorer',
        'time': '3 weeks ago',
      }
    ];

    return ListView.builder(
      padding: EdgeInsets.fromLTRB(
        8, 6, 8,
        MediaQuery.of(context).padding.bottom + 8,
      ),
      itemCount: activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        final isSmallScreen = MediaQuery.of(context).size.width < 360;
        
        return Container(
          margin: EdgeInsets.only(bottom: isSmallScreen ? 4 : 6),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 10),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 10),
            child: Material(
              color: Theme.of(context).cardColor,
              child: InkWell(
                onTap: () {
                  // Navigate to activity detail if applicable
                },
                child: Padding(
                  padding: EdgeInsets.all(isSmallScreen ? 8 : 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Activity Icon or Image
                      _buildActivityAvatar(context, activity, isSmallScreen),
                      SizedBox(width: isSmallScreen ? 8 : 10),
                      // Activity Details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Activity Title with time indicator
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        activity['title'],
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontSize: isSmallScreen ? 13 : 14,
                                          height: 1.1,
                                        ),
                                      ),
                                      const SizedBox(height: 2),
                                      Text(
                                        activity['details'],
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontSize: isSmallScreen ? 12 : 13,
                                          height: 1.1,
                                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Text(
                                  activity['time'],
                                  style: TextStyle(
                                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                                    fontSize: isSmallScreen ? 10 : 11,
                                    height: 1.1,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            
                            // Location info for pin-related activities
                            if (activity['location'] != null)
                              Row(
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    size: isSmallScreen ? 12 : 13,
                                    color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    activity['location'],
                                    style: TextStyle(
                                      fontSize: isSmallScreen ? 10 : 11,
                                      height: 1.1,
                                      color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
                                    ),
                                  ),
                                ],
                              ),
                              
                            // Pin count for collections
                            if (activity['count'] != null)
                              Row(
                                children: [
                                  Icon(
                                    Icons.push_pin,
                                    size: isSmallScreen ? 12 : 13,
                                    color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    activity['count'],
                                    style: TextStyle(
                                      fontSize: isSmallScreen ? 10 : 11,
                                      height: 1.1,
                                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                    ),
                                  ),
                                ],
                              ),
                              
                            // Action buttons for certain activity types
                            if (_shouldShowActionButtons(activity['type']))
                              Padding(
                                padding: const EdgeInsets.only(top: 8),
                                child: Row(
                                  children: [
                                    _buildActionButton(
                                      context,
                                      icon: Icons.play_arrow_rounded,
                                      label: 'Listen',
                                      color: const Color(0xFF1DB954), // Spotify green
                                      isSmallScreen: isSmallScreen,
                                    ),
                                    const SizedBox(width: 8),
                                    _buildActionButton(
                                      context,
                                      icon: Icons.share_outlined,
                                      label: 'Share',
                                      color: Theme.of(context).colorScheme.primary,
                                      isSmallScreen: isSmallScreen,
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildActivityAvatar(BuildContext context, Map<String, dynamic> activity, bool isSmallScreen) {
    // Different visuals based on activity type
    switch (activity['type']) {
      case 'dropped_pin':
        return Container(
          width: isSmallScreen ? 40 : 48,
          height: isSmallScreen ? 40 : 48,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
            boxShadow: [
              BoxShadow(
                color: Colors.transparent,
                blurRadius: 0,
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
            child: Image.network(
              activity['image'] ?? '',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                ),
                child: Icon(
                  Icons.music_note,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ),
        );
        
      case 'collected_pin':
        return Stack(
          children: [
            Container(
              width: isSmallScreen ? 40 : 48,
              height: isSmallScreen ? 40 : 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.transparent,
                    blurRadius: 0,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                child: Image.network(
                  activity['image'] ?? '',
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                    ),
                    child: Icon(
                      Icons.music_note,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 1.5),
                ),
                child: const Icon(
                  Icons.check,
                  size: 10,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
        
      case 'followed_user':
      case 'friend_request':
        return Container(
          width: isSmallScreen ? 40 : 48,
          height: isSmallScreen ? 40 : 48,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.transparent,
                blurRadius: 0,
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 24),
            child: Image.network(
              activity['image'] ?? '',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.person,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ),
        );
        
      case 'created_collection':
        return Container(
          width: isSmallScreen ? 40 : 48,
          height: isSmallScreen ? 40 : 48,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
          ),
          child: Icon(
            Icons.library_music,
            color: Theme.of(context).colorScheme.primary,
          ),
        );
        
      case 'milestone':
        return Container(
          width: isSmallScreen ? 40 : 48,
          height: isSmallScreen ? 40 : 48,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
          ),
          child: Icon(
            Icons.flag,
            color: Theme.of(context).colorScheme.primary,
          ),
        );
        
      case 'achievement':
        return Container(
          width: isSmallScreen ? 40 : 48,
          height: isSmallScreen ? 40 : 48,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
          ),
          child: Icon(
            Icons.emoji_events,
            color: Theme.of(context).colorScheme.primary,
          ),
        );
        
      default:
        return Container(
          width: isSmallScreen ? 40 : 48,
          height: isSmallScreen ? 40 : 48,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
          ),
          child: Icon(
            Icons.music_note,
            color: Theme.of(context).colorScheme.primary,
          ),
        );
    }
  }
  
  bool _shouldShowActionButtons(String type) {
    return type == 'dropped_pin' || type == 'collected_pin';
  }
  
  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required bool isSmallScreen,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 16),
        color: color.withOpacity(0.1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 16),
          onTap: () {
            // Handle button action
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: isSmallScreen ? 16 : 20,
                  color: color,
                ),
                const SizedBox(width: 4),
                Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontSize: isSmallScreen ? 12 : 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 