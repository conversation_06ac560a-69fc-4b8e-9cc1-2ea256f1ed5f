import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../../config/themes.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/user_provider.dart';
import '../../providers/settings_provider.dart';
import '../../providers/apple_music_provider.dart';
import '../../providers/spotify_provider.dart';
import '../profile/components/global_bops_indicator.dart';
import '../../screens/map/my_bop_map.dart';
import 'package:flutter/services.dart';
import 'profile_stats.dart';
import '../map/components/expandable_search_bar.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import '../../config/constants.dart';
import 'dart:async';
import '../common/cached_avatar.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ProfileHeader extends StatefulWidget {
  final dynamic user;
  final Size size;
  final String? selectedService;
  final ValueChanged<String>? onServiceSelected;

  const ProfileHeader({
    Key? key,
    required this.user,
    required this.size,
    this.selectedService,
    this.onServiceSelected,
  }) : super(key: key);

  @override
  State<ProfileHeader> createState() => _ProfileHeaderState();
}

class _ProfileHeaderState extends State<ProfileHeader> with SingleTickerProviderStateMixin {
  bool _isEditButtonHovered = false;
  late AnimationController _animationController;
  late Animation<double> _gradientAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );

    _gradientAnimation = Tween<double>(
      begin: -0.5,
      end: 1.5,
    ).animate(_animationController);
    
    // Start animation if enabled
    final settings = Provider.of<SettingsProvider>(context, listen: false);
    if (settings.isAnimatedBackgroundEnabled) {
      _animationController.repeat(reverse: true);
    }
    
    // Initialize UserProvider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<UserProvider>(context, listen: false).initialize();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Update animation state when setting changes
    final settings = Provider.of<SettingsProvider>(context);
    if (settings.isAnimatedBackgroundEnabled) {
      if (!_animationController.isAnimating) {
        _animationController.repeat(reverse: true);
      }
    } else {
      _animationController.stop();
      // Reset to middle position when animation is disabled
      _animationController.value = 0.5;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get screen width to ensure proper proportional sizing
    final screenWidth = MediaQuery.of(context).size.width;
    
    // Calculate responsive values while keeping proportions consistent
    final double headerHeight = widget.size.height * 0.15;
    final double avatarSize = screenWidth < 360 ? 65 : 70;
    final double avatarOuterSize = avatarSize + 10;
    final double fontSize = screenWidth < 360 ? 18 : 20;
    final double bioFontSize = screenWidth < 360 ? 12 : 13;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final settings = Provider.of<SettingsProvider>(context);
    
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        // Use UserProvider data if available, fallback to widget.user
        final displayName = userProvider.displayUsername;
        final profilePicUrl = userProvider.profilePictureUrl;
        
        return Container(
          color: Theme.of(context).scaffoldBackgroundColor,
          child: Stack(
            children: [
              // Background with optional animation
              if (settings.isAnimatedBackgroundEnabled)
                AnimatedBuilder(
                  animation: _gradientAnimation,
                  builder: (context, child) => _buildGradientBackground(headerHeight, isDark),
                )
              else
                _buildGradientBackground(headerHeight, isDark),
              
              // Subtle pattern overlay
              Positioned.fill(
                child: Container(
                  height: headerHeight,
                  decoration: BoxDecoration(
                    backgroundBlendMode: isDark ? BlendMode.overlay : BlendMode.multiply,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        (isDark ? Colors.white : Colors.black).withOpacity(0.03),
                        (isDark ? Colors.white : Colors.black).withOpacity(0.01),
                        (isDark ? Colors.black : Colors.white).withOpacity(0.01),
                        (isDark ? Colors.black : Colors.white).withOpacity(0.03),
                      ],
                    ),
                  ),
                ),
              ),
              
              // Main content with responsive positioning
              Positioned(
                bottom: headerHeight * 0.10,  // Reduced from 0.25 to make content lower in the header
                left: 0,
                right: 0,
                child: Column(
                  children: [
                    // Profile info row (image, name, edit button)
                    Container(
                      margin: EdgeInsets.only(bottom: 12),  // Slightly reduced margin
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Profile picture with enhanced effects
                          Container(
                            margin: EdgeInsets.only(left: screenWidth * 0.06),
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                // Enhanced glow effect
                                Container(
                                  width: avatarOuterSize,
                                  height: avatarOuterSize,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Theme.of(context).colorScheme.primary.withOpacity(isDark ? 0.2 : 0.15),
                                        blurRadius: 15,
                                        spreadRadius: 2,
                                      ),
                                      BoxShadow(
                                        color: Theme.of(context).colorScheme.secondary.withOpacity(isDark ? 0.15 : 0.1),
                                        blurRadius: 8,
                                        offset: const Offset(4, 4),
                                      ),
                                    ],
                                  ),
                                ),
                                // Profile picture with glass effect
                                MouseRegion(
                                  cursor: MaterialStateMouseCursor.clickable,
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 200),
                                    width: avatarSize,
                                    height: avatarSize,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.8 : 0.1),
                                        width: 2,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(isDark ? 0.1 : 0.05),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(avatarSize / 2),
                                      child: BackdropFilter(
                                        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                                        child: profilePicUrl != null && 
                                              profilePicUrl.isNotEmpty && 
                                              Uri.tryParse(profilePicUrl)?.hasAbsolutePath == true
                                          ? CachedNetworkImage(
                                              imageUrl: profilePicUrl,
                                              fit: BoxFit.cover,
                                              placeholder: (context, url) => Container(
                                                color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.1 : 0.05),
                                                child: Icon(
                                                  Icons.person,
                                                  size: avatarSize / 2,
                                                  color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.8 : 0.6),
                                                ),
                                              ),
                                              errorWidget: (context, url, error) => Container(
                                                color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.1 : 0.05),
                                                child: Icon(
                                                  Icons.person,
                                                  size: avatarSize / 2,
                                                  color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.8 : 0.6),
                                                ),
                                              ),
                                              fadeInDuration: const Duration(milliseconds: 300),
                                              fadeOutDuration: const Duration(milliseconds: 100),
                                            )
                                        : Container(
                                            color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.1 : 0.05),
                                            child: Icon(
                                              Icons.person,
                                              size: avatarSize / 2,
                                              color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.8 : 0.6),
                                            ),
                                          ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          // User info with enhanced text styling
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    displayName,
                                    style: TextStyle(
                                      fontSize: screenWidth < 360 ? 17 : 19,
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context).colorScheme.onSurface.withOpacity(isDark ? 0.95 : 0.87),
                                      letterSpacing: 0.2,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black.withOpacity(isDark ? 0.2 : 0.1),
                                          offset: const Offset(0, 1),
                                          blurRadius: 2,
                                        ),
                                      ],
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Enhanced edit button with glass effect
                          Padding(
                            padding: EdgeInsets.only(right: screenWidth * 0.06),
                            child: MouseRegion(
                              cursor: MaterialStateMouseCursor.clickable,
                              onEnter: (_) => setState(() => _isEditButtonHovered = true),
                              onExit: (_) => setState(() => _isEditButtonHovered = false),
                              child: GestureDetector(
                                onTap: () {
                                  _showEditProfileBottomSheet(context);
                                },
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  padding: EdgeInsets.symmetric(
                                    horizontal: screenWidth < 360 ? 10 : 12,
                                    vertical: 6
                                  ),
                                  decoration: BoxDecoration(
                                    color: _isEditButtonHovered
                                      ? (isDark ? Colors.white : Theme.of(context).colorScheme.surface).withOpacity(isDark ? 0.15 : 0.9)
                                      : (isDark ? Colors.white : Theme.of(context).colorScheme.surface).withOpacity(isDark ? 0.1 : 0.8),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: (isDark ? Colors.white : Theme.of(context).colorScheme.onSurface)
                                          .withOpacity(_isEditButtonHovered ? (isDark ? 0.5 : 0.15) : (isDark ? 0.3 : 0.1)),
                                      width: 1
                                    ),
                                    boxShadow: _isEditButtonHovered ? [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(isDark ? 0.1 : 0.05),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ] : [],
                                  ),
                                  child: Text(
                                    'Edit',
                                    style: TextStyle(
                                      fontSize: screenWidth < 360 ? 10 : 11,
                                      fontWeight: FontWeight.w600,
                                      color: isDark 
                                          ? Colors.white.withOpacity(0.9)
                                          : Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Connected services indicators and PRO badge row
                    Container(
                      margin: EdgeInsets.zero,  // Removed bottom margin
                      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.06),
                      child: Row(
                        children: [
                          Consumer<SpotifyProvider>(
                            builder: (context, spotifyProvider, child) {
                              final isSelected = widget.selectedService == 'spotify';
                              final isAppleMusicSelected = widget.selectedService == 'apple_music';
                              return _buildServiceIconWithImage(
                                context,
                                'assets/images/logo/BOPmaps.png',
                                isAppleMusicSelected ? Colors.grey : Theme.of(context).colorScheme.primary,
                                true, // Always show as connected since we're not doing auth
                                screenWidth: screenWidth,
                                isSelected: isSelected,
                                onTap: () => widget.onServiceSelected?.call('spotify'),
                              );
                            },
                          ),
                          Consumer<AppleMusicProvider>(
                            builder: (context, appleMusicProvider, child) {
                              final isSelected = widget.selectedService == 'apple_music';
                              final isBopmapsSelected = widget.selectedService == 'spotify';
                              return _buildServiceIcon(
                                context,
                                FontAwesomeIcons.itunesNote, 
                                isBopmapsSelected ? Colors.grey : const Color(0xFFFA243C),
                                appleMusicProvider.isConnected, 
                                screenWidth: screenWidth,
                                isSelected: isSelected,
                                onTap: () => widget.onServiceSelected?.call('apple_music'),
                              );
                            },
                          ),
                          SizedBox(width: screenWidth < 360 ? 6 : 8),
                          Expanded(
                            child: GlobalBopsIndicator(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const MyBOPMap(),
                                  ),
                                );
                              },
                              isActive: false,
                            ),
                          ),
                          // TODO: Add PRO badge
                          // SizedBox(width: screenWidth < 360 ? 6 : 8),
                          // Container(
                          //   padding: EdgeInsets.symmetric(
                          //     horizontal: screenWidth < 360 ? 6 : 8,
                          //     vertical: 4,
                          //   ),
                          //   decoration: BoxDecoration(
                          //     color: Theme.of(context).brightness == Brightness.dark
                          //         ? Colors.white.withOpacity(0.1)
                          //         : Colors.black.withOpacity(0.05),
                          //     borderRadius: BorderRadius.circular(12),
                          //     border: Border.all(
                          //       color: Theme.of(context).brightness == Brightness.dark
                          //           ? Colors.white.withOpacity(0.2)
                          //           : Colors.black.withOpacity(0.1),
                          //       width: 1,
                          //     ),
                          //   ),
                          //   child: Row(
                          //     mainAxisSize: MainAxisSize.min,
                          //     children: [
                          //       Icon(
                          //         Icons.star,
                          //         size: screenWidth < 360 ? 10 : 11,
                          //         color: Theme.of(context).brightness == Brightness.dark
                          //             ? Colors.amber.shade300
                          //             : Colors.amber.shade600,
                          //       ),
                          //       const SizedBox(width: 3),
                          //       Text(
                          //         'PRO',
                          //         style: TextStyle(
                          //           color: Theme.of(context).brightness == Brightness.dark
                          //               ? Colors.white
                          //               : Colors.black.withOpacity(0.8),
                          //           fontSize: screenWidth < 360 ? 9 : 10,
                          //           fontWeight: FontWeight.w600,
                          //           letterSpacing: 0.2,
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGradientBackground(double headerHeight, bool isDark) {
    return Container(
      height: headerHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(
            _gradientAnimation.value * 0.8,
            -0.5,
          ),
          end: Alignment(
            1 - _gradientAnimation.value * 0.8,
            1.5,
          ),
          colors: [
            Theme.of(context).colorScheme.primary.withOpacity(isDark ? 0.8 : 0.7),
            Theme.of(context).colorScheme.secondary.withOpacity(isDark ? 0.7 : 0.6),
            Theme.of(context).colorScheme.tertiary.withOpacity(isDark ? 0.6 : 0.5),
          ],
          stops: const [0.2, 0.5, 0.8],
        ),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.05 : 0.02),
                (isDark ? Colors.white : Colors.black).withOpacity(0.0),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildServiceIcon(
    BuildContext context,
    IconData icon, 
    Color color, 
    bool isActive, 
    {required double screenWidth, VoidCallback? onTap, bool isSelected = false}
  ) {
    final double iconSize = screenWidth < 360 ? 15 : 16;
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: 10),
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: isSelected 
              ? color.withOpacity(isDarkMode ? 0.9 : 0.95)
              : isActive 
                  ? color.withOpacity(isDarkMode ? 0.7 : 0.8)
                  : (isDarkMode ? Colors.white.withOpacity(0.2) : Colors.black.withOpacity(0.05)),
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected 
                ? color
                : isActive 
                    ? color.withOpacity(0.7)
                    : (isDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.1)),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isActive 
                  ? color.withOpacity(isDarkMode ? 0.2 : 0.3)
                  : Colors.black.withOpacity(0.05),
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Icon(
          icon,
          size: iconSize,
          color: isSelected || isActive 
              ? Colors.white
              : (isDarkMode ? color : color.withOpacity(0.9)),
        ),
      ),
    );
  }

  Widget _buildServiceIconWithImage(
    BuildContext context,
    String imagePath, 
    Color color, 
    bool isActive, 
    {required double screenWidth, VoidCallback? onTap, bool isSelected = false}
  ) {
    final double iconSize = screenWidth < 360 ? 15 : 16;
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: 10),
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: isSelected 
              ? color.withOpacity(isDarkMode ? 0.9 : 0.95)
              : isActive 
                  ? color.withOpacity(isDarkMode ? 0.7 : 0.8)
                  : (isDarkMode ? Colors.white.withOpacity(0.2) : Colors.black.withOpacity(0.05)),
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected 
                ? color
                : isActive 
                    ? color.withOpacity(0.7)
                    : (isDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.1)),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isActive 
                  ? color.withOpacity(isDarkMode ? 0.2 : 0.3)
                  : Colors.black.withOpacity(0.05),
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(iconSize / 2),
          child: Image.asset(
            imagePath,
            width: iconSize,
            height: iconSize,
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  void _showEditProfileBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildEditProfileBottomSheet(context),
    );
  }

  Widget _buildEditProfileBottomSheet(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        return EditProfileBottomSheet(
          currentUser: userProvider.headerUser ?? userProvider.currentUser ?? widget.user,
          onProfileUpdated: () {
            // Refresh user data after update
            userProvider.refreshAll();
          },
        );
      },
    );
  }
}

class EditProfileBottomSheet extends StatefulWidget {
  final dynamic currentUser;
  final VoidCallback onProfileUpdated;

  const EditProfileBottomSheet({
    Key? key,
    required this.currentUser,
    required this.onProfileUpdated,
  }) : super(key: key);

  @override
  State<EditProfileBottomSheet> createState() => _EditProfileBottomSheetState();
}

class _EditProfileBottomSheetState extends State<EditProfileBottomSheet>
    with SingleTickerProviderStateMixin {
  late TextEditingController _usernameController;
  late TextEditingController _bioController;
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late ScrollController _scrollController;
  
  final FocusNode _usernameFocusNode = FocusNode();
  final FocusNode _bioFocusNode = FocusNode();
  final ImagePicker _picker = ImagePicker();
  
  bool _isUsernameValid = true;
  bool _isUpdating = false;
  bool _isCheckingUsername = false;
  String? _usernameError;
  String? _selectedImagePath;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController(text: widget.currentUser?.username ?? '');
    _bioController = TextEditingController(text: widget.currentUser?.bio ?? '');
    _scrollController = ScrollController();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
    
    // Add listeners for validation
    _usernameController.addListener(_validateUsername);
    
    // Add focus listeners to scroll to focused field
    _usernameFocusNode.addListener(_onUsernameFocusChange);
    _bioFocusNode.addListener(_onBioFocusChange);
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _bioController.dispose();
    _animationController.dispose();
    _scrollController.dispose();
    _usernameFocusNode.dispose();
    _bioFocusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _validateUsername() {
    final value = _usernameController.text;
    final currentUsername = widget.currentUser?.username ?? '';
    
    // Cancel previous timer
    _debounceTimer?.cancel();
    
    setState(() {
      if (value.isEmpty) {
        _isUsernameValid = false;
        _usernameError = 'Username is required';
        _isCheckingUsername = false;
      } else if (value.length < 2) {
        _isUsernameValid = false;
        _usernameError = 'Username must be at least 2 characters';
        _isCheckingUsername = false;
      } else if (value.length > 30) {
        _isUsernameValid = false;
        _usernameError = 'Username must be 30 characters or less';
        _isCheckingUsername = false;
      } else if (!RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(value)) {
        _isUsernameValid = false;
        _usernameError = 'Username can only contain letters, numbers, underscore, and hyphen';
        _isCheckingUsername = false;
      } else if (value == currentUsername) {
        // Same as current username - valid but no need to check availability
        _isUsernameValid = true;
        _usernameError = null;
        _isCheckingUsername = false;
      } else {
        // Valid format, check availability after debounce
        _isUsernameValid = true;
        _usernameError = null;
        _isCheckingUsername = true;
        
        // Debounce the API call
        _debounceTimer = Timer(const Duration(milliseconds: 800), () {
          _checkUsernameAvailability(value);
        });
      }
    });
  }

  Future<void> _checkUsernameAvailability(String username) async {
    if (!mounted) return;
    
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      
      final isAvailable = await userProvider.checkUsernameAvailability(username);
      
      if (mounted) {
        setState(() {
          _isCheckingUsername = false;
          if (isAvailable) {
            // Username is available
            _isUsernameValid = true;
            _usernameError = null;
          } else {
            // Username is taken
            _isUsernameValid = false;
            _usernameError = 'This username is already taken';
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isCheckingUsername = false;
          // For errors, assume username is valid to not block user
          _isUsernameValid = true;
          _usernameError = null;
        });
      }
      
      if (kDebugMode) {
        print('❌ Error checking username availability: $e');
      }
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      HapticFeedback.lightImpact();
      
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );
      
      if (image != null) {
        setState(() {
          _selectedImagePath = image.path;
        });
        HapticFeedback.mediumImpact();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Choose Photo Source',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildSourceOption(
                    icon: Icons.camera_alt_rounded,
                    label: 'Camera',
                    onTap: () {
                      Navigator.pop(context);
                      _pickImage(ImageSource.camera);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSourceOption(
                    icon: Icons.photo_library_rounded,
                    label: 'Gallery',
                    onTap: () {
                      Navigator.pop(context);
                      _pickImage(ImageSource.gallery);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final themeColor = Theme.of(context).colorScheme.primary;
    
    return Material(
      color: themeColor.withOpacity(0.1),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: themeColor,
              ),
              const SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _updateProfile() async {
    if (!_isUsernameValid || _isUpdating) return;
    
    setState(() {
      _isUpdating = true;
    });
    
    try {
      HapticFeedback.mediumImpact();
      
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      
      bool usernameSuccess = true;
      bool bioSuccess = true;
      bool profilePicSuccess = true;
      
      // Update username if changed
      final currentUsername = widget.currentUser?.username ?? '';
      if (_usernameController.text.trim() != currentUsername) {
        if (kDebugMode) {
          print('🔄 Username changed from "$currentUsername" to "${_usernameController.text.trim()}"');
        }
        usernameSuccess = await userProvider.updateUsername(_usernameController.text.trim());
      }
      
      // Update bio if changed
      final currentBio = widget.currentUser?.bio ?? '';
      if (_bioController.text.trim() != currentBio) {
        if (kDebugMode) {
          print('🔄 Bio changed from "$currentBio" to "${_bioController.text.trim()}"');
        }
        bioSuccess = await userProvider.updateBio(_bioController.text.trim());
      }
      
      // Update profile picture if changed
      if (_selectedImagePath != null) {
        if (kDebugMode) {
          print('🔄 Uploading new profile picture: $_selectedImagePath');
        }
        profilePicSuccess = await userProvider.updateProfilePicture(_selectedImagePath!);
      }
      
      if (usernameSuccess && bioSuccess && profilePicSuccess && mounted) {
        HapticFeedback.heavyImpact();
        widget.onProfileUpdated();
        Navigator.pop(context);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 12),
                Text('Profile updated successfully!'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      } else if (mounted) {
        // Show specific error message
        String errorMessage = 'Failed to update profile';
        if (!usernameSuccess && !bioSuccess && !profilePicSuccess) {
          errorMessage = 'Failed to update username, bio, and profile picture';
        } else if (!usernameSuccess && !bioSuccess) {
          errorMessage = 'Failed to update username and bio';
        } else if (!usernameSuccess && !profilePicSuccess) {
          errorMessage = 'Failed to update username and profile picture';
        } else if (!bioSuccess && !profilePicSuccess) {
          errorMessage = 'Failed to update bio and profile picture';
        } else if (!usernameSuccess) {
          errorMessage = userProvider.error ?? 'Failed to update username';
        } else if (!bioSuccess) {
          errorMessage = userProvider.error ?? 'Failed to update bio';
        } else if (!profilePicSuccess) {
          errorMessage = userProvider.error ?? 'Failed to upload profile picture';
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final themeColor = theme.colorScheme.primary;
    final screenHeight = MediaQuery.of(context).size.height;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    return GestureDetector(
      // Dismiss keyboard when tapping outside
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, _slideAnimation.value * screenHeight),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: screenHeight * 0.9,
                ),
                decoration: BoxDecoration(
                  color: theme.scaffoldBackgroundColor,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.outline.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    
                    // Header
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: themeColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.edit_rounded,
                              color: themeColor,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Edit Profile',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.w700,
                                    color: theme.colorScheme.onSurface,
                                    letterSpacing: -0.5,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Update your profile information',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Content with keyboard-aware scrolling
                    Flexible(
                      child: NotificationListener<ScrollNotification>(
                        onNotification: (notification) {
                          // Dismiss keyboard when scrolling
                          if (notification is ScrollStartNotification) {
                            FocusScope.of(context).unfocus();
                          }
                          return false;
                        },
                        child: SingleChildScrollView(
                          // Keyboard-aware padding
                          padding: EdgeInsets.only(
                            left: 24,
                            right: 24,
                            bottom: 24 + (isKeyboardVisible ? keyboardHeight + 16 : 0),
                          ),
                          // Enable physics for better scroll behavior
                          physics: const ClampingScrollPhysics(),
                          // Reverse scroll direction to keep focused field visible
                          reverse: false,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Profile Picture Section - hide when keyboard is visible for more space
                              if (!isKeyboardVisible) ...[
                                Center(
                                  child: GestureDetector(
                                    onTap: () {
                                      // Dismiss keyboard before showing image picker
                                      FocusScope.of(context).unfocus();
                                      Future.delayed(const Duration(milliseconds: 200), () {
                                        _showImageSourceDialog();
                                      });
                                    },
                                    child: Container(
                                      width: 120,
                                      height: 120,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: themeColor.withOpacity(0.3),
                                          width: 3,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: themeColor.withOpacity(0.2),
                                            blurRadius: 20,
                                            spreadRadius: 2,
                                          ),
                                        ],
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(60),
                                        child: Stack(
                                          children: [
                                            // Profile image
                                            if (_selectedImagePath != null)
                                              Image.file(
                                                File(_selectedImagePath!),
                                                width: 120,
                                                height: 120,
                                                fit: BoxFit.cover,
                                              )
                                            else if (widget.currentUser?.profilePicUrl != null)
                                              CachedNetworkImage(
                                                imageUrl: widget.currentUser!.profilePicUrl!,
                                                fit: BoxFit.cover,
                                                placeholder: (context, url) => Container(
                                                  color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.1 : 0.05),
                                                  child: Icon(
                                                    Icons.person,
                                                    size: 60,
                                                    color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.8 : 0.6),
                                                  ),
                                                ),
                                                errorWidget: (context, url, error) => Container(
                                                  color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.1 : 0.05),
                                                  child: Icon(
                                                    Icons.person,
                                                    size: 60,
                                                    color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.8 : 0.6),
                                                  ),
                                                ),
                                              )
                                            else
                                              _buildDefaultAvatar(),
                                            
                                            // Overlay
                                            Container(
                                              width: 120,
                                              height: 120,
                                              decoration: BoxDecoration(
                                                color: Colors.black.withOpacity(0.3),
                                                shape: BoxShape.circle,
                                              ),
                                              child: Icon(
                                                Icons.camera_alt_rounded,
                                                color: Colors.white,
                                                size: 32,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                
                                const SizedBox(height: 8),
                                
                                Center(
                                  child: Text(
                                    'Tap to change photo',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                
                                const SizedBox(height: 32),
                              ] else ...[
                                // Small spacing when keyboard is visible
                                const SizedBox(height: 16),
                              ],
                              
                              // Username Field
                              _buildInputField(
                                label: 'Username',
                                controller: _usernameController,
                                focusNode: _usernameFocusNode,
                                icon: Icons.person_outline_rounded,
                                isValid: _isUsernameValid,
                                errorMessage: _usernameError,
                                hintText: 'Enter your username',
                                maxLength: 30,
                              ),
                              
                              const SizedBox(height: 24),
                              
                              // Bio Field
                              _buildInputField(
                                label: 'Bio',
                                controller: _bioController,
                                focusNode: _bioFocusNode,
                                icon: Icons.info_outline_rounded,
                                isValid: true,
                                hintText: 'Tell us about yourself',
                                maxLines: 3,
                                maxLength: 150,
                              ),
                              
                              const SizedBox(height: 32),
                              
                              // Update Button - always visible
                              SizedBox(
                                width: double.infinity,
                                height: 56,
                                child: ElevatedButton(
                                  onPressed: _isUsernameValid && !_isUpdating ? () {
                                    // Dismiss keyboard before updating
                                    FocusScope.of(context).unfocus();
                                    _updateProfile();
                                  } : null,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: themeColor,
                                    foregroundColor: Colors.white,
                                    elevation: 0,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    disabledBackgroundColor: theme.colorScheme.outline.withOpacity(0.3),
                                  ),
                                  child: _isUpdating
                                    ? const SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.check_rounded,
                                            size: 20,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Update Profile',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              letterSpacing: 0.2,
                                            ),
                                          ),
                                        ],
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    final theme = Theme.of(context);
    final themeColor = theme.colorScheme.primary;
    
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: themeColor.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.person_rounded,
        size: 60,
        color: themeColor,
      ),
    );
  }

  Widget _buildInputField({
    required String label,
    required TextEditingController controller,
    required FocusNode focusNode,
    required IconData icon,
    required bool isValid,
    required String hintText,
    String? errorMessage,
    int maxLines = 1,
    int? maxLength,
  }) {
    final theme = Theme.of(context);
    final themeColor = theme.colorScheme.primary;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 8),
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface.withOpacity(0.8),
              letterSpacing: 0.2,
            ),
          ),
        ),
        
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: controller,
            focusNode: focusNode,
            maxLines: maxLines,
            maxLength: maxLength,
            textInputAction: maxLines > 1 ? TextInputAction.newline : TextInputAction.next,
            onSubmitted: (value) {
              if (maxLines == 1) {
                // Move to next field or dismiss keyboard
                if (label == 'Username') {
                  // Move to bio field
                  FocusScope.of(context).nextFocus();
                } else {
                  // Dismiss keyboard
                  FocusScope.of(context).unfocus();
                }
              }
            },
            onTap: () {
              // Ensure cursor is at the end when tapping
              if (controller.selection == TextSelection.fromPosition(TextPosition(offset: controller.text.length - 1))) {
                controller.selection = TextSelection.fromPosition(TextPosition(offset: controller.text.length));
              }
            },
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface,
              letterSpacing: 0.2,
            ),
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: TextStyle(
                fontSize: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.4),
                fontWeight: FontWeight.w400,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.only(left: 12, right: 8),
                child: Icon(
                  icon,
                  color: focusNode.hasFocus 
                    ? themeColor
                    : theme.colorScheme.onSurface.withOpacity(0.5),
                  size: 20,
                ),
              ),
              suffixIcon: controller.text.isNotEmpty
                ? Container(
                    margin: const EdgeInsets.only(right: 12),
                    child: Icon(
                      isValid ? Icons.check_circle_rounded : Icons.error_rounded,
                      color: isValid ? Colors.green.shade600 : Colors.red.shade600,
                      size: 20,
                    ),
                  )
                : null,
              filled: true,
              fillColor: theme.colorScheme.surface,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: themeColor,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.red.shade400,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.red.shade400,
                  width: 2,
                ),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: maxLines > 1 ? 16 : 16,
              ),
              prefixIconConstraints: const BoxConstraints(
                minWidth: 48,
                minHeight: 48,
              ),
              suffixIconConstraints: const BoxConstraints(
                minWidth: 48,
                minHeight: 48,
              ),
              counterText: maxLength != null ? null : '',
            ),
            inputFormatters: maxLength != null ? [
              LengthLimitingTextInputFormatter(maxLength),
              if (label == 'Username')
                FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9_-]')),
            ] : null,
          ),
        ),
        
        // Error/Success message
        if (errorMessage != null || (isValid && controller.text.isNotEmpty && label == 'Username'))
          Padding(
            padding: const EdgeInsets.only(top: 8, left: 4),
            child: errorMessage != null
              ? Text(
                  errorMessage,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.red.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                )
              : Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 14,
                      color: Colors.green.shade600,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Username looks great!',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
          ),
      ],
    );
  }

  void _onUsernameFocusChange() {
    if (_usernameFocusNode.hasFocus) {
      _scrollToField(0); // Username is first field
    }
  }

  void _onBioFocusChange() {
    if (_bioFocusNode.hasFocus) {
      _scrollToField(1); // Bio is second field
    }
  }

  void _scrollToField(int fieldIndex) {
    // Delay to allow keyboard to appear
    Future.delayed(const Duration(milliseconds: 300), () {
      if (_scrollController.hasClients && mounted) {
        final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
        if (keyboardHeight > 0) {
          // Calculate scroll position based on field
          double scrollOffset = 0;
          if (fieldIndex == 0) {
            // Username field - scroll to show it above keyboard
            scrollOffset = 100;
          } else if (fieldIndex == 1) {
            // Bio field - scroll more to show the multi-line field
            scrollOffset = 250;
          }
          
          _scrollController.animateTo(
            scrollOffset,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });
  }
}

class ProfileHeaderTopBar extends StatelessWidget {
  final VoidCallback? onSettings;
  final ValueChanged<String>? onSearch;
  final VoidCallback? onNotifications;
  final bool showBottomNav;

  const ProfileHeaderTopBar({
    Key? key,
    this.onSettings,
    this.onSearch,
    this.onNotifications,
    this.showBottomNav = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!showBottomNav) return SizedBox.shrink();
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 4),
      child: Row(
        children: [
          IconButton(
            icon: Icon(Icons.settings, color: isDark ? Colors.white.withOpacity(0.9) : Colors.black87),
            onPressed: onSettings,
            tooltip: 'Settings',
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: SizedBox(
                height: 40,
                child: ExpandableSearchBar(
                  onSearch: onSearch ?? (_) {},
                  hintText: 'Search profiles...',
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  foregroundColor: isDark ? Colors.white.withOpacity(0.9) : Colors.black87,
                  iconSize: 24,
                ),
              ),
            ),
          ),
          IconButton(
            icon: Icon(Icons.notifications_outlined, color: isDark ? Colors.white.withOpacity(0.9) : Colors.black87),
            onPressed: onNotifications,
            tooltip: 'Notifications',
          ),
        ],
      ),
    );
  }
}

/// Composite widget to preserve the original profile header and stats design
class ProfileHeaderFullDesign extends StatelessWidget {
  final dynamic user;
  final Size size;
  final Map<String, String> stats;
  final VoidCallback? onSettings;
  final ValueChanged<String>? onSearch;
  final VoidCallback? onNotifications;
  final bool showBottomNav;

  const ProfileHeaderFullDesign({
    Key? key,
    required this.user,
    required this.size,
    required this.stats,
    this.onSettings,
    this.onSearch,
    this.onNotifications,
    this.showBottomNav = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ProfileHeaderTopBar(
          onSettings: onSettings,
          onSearch: onSearch,
          onNotifications: onNotifications,
          showBottomNav: showBottomNav,
        ),
        ProfileHeader(user: user, size: size),
        ProfileStats(size: size, stats: stats),
      ],
    );
  }
} 