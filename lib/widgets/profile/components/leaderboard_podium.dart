import 'package:flutter/material.dart';

class LeaderboardPodium extends StatelessWidget {
  final List<Map<String, dynamic>> leaderboardData;
  final bool isSmallScreen;
  final VoidCallback? onCreateChallenge;
  final Function(Map<String, dynamic>)? onUserTap;

  const LeaderboardPodium({
    Key? key,
    required this.leaderboardData,
    this.isSmallScreen = false,
    this.onCreateChallenge,
    this.onUserTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Safely get users, defaulting to null if they don't exist
    final firstPlace = leaderboardData.isNotEmpty ? leaderboardData[0] : null;
    final secondPlace = leaderboardData.length > 1 ? leaderboardData[1] : null;
    final thirdPlace = leaderboardData.length > 2 ? leaderboardData[2] : null;
    
    // If no users at all, return empty container
    if (firstPlace == null) {
      return const SizedBox.shrink();
    }
    
    // Reduced heights for more compact look
    final double firstPlaceHeight = isSmallScreen ? 50.0 : 60.0;
    final double secondPlaceHeight = isSmallScreen ? 38.0 : 48.0;
    final double thirdPlaceHeight = isSmallScreen ? 26.0 : 36.0;
    final showCrown = true;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          margin: EdgeInsets.only(top: isSmallScreen ? 6 : 8, bottom: 6),
          padding: EdgeInsets.zero,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              // Enhanced spotlight background with dual gradients
              Positioned.fill(
                child: IgnorePointer(
                  child: Stack(
                    children: [
                      // Primary spotlight
                      Center(
                        child: Container(
                          width: isSmallScreen ? 200 : 250,
                          height: isSmallScreen ? 110 : 140,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                theme.colorScheme.primary.withOpacity(0.12),
                                Colors.transparent,
                              ],
                              radius: 0.85,
                            ),
                          ),
                        ),
                      ),
                      // Secondary subtle glow
                      Positioned(
                        top: isSmallScreen ? 40 : 50,
                        left: 0,
                        right: 0,
                        child: Center(
                          child: Container(
                            width: isSmallScreen ? 160 : 200,
                            height: isSmallScreen ? 80 : 100,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: RadialGradient(
                                colors: [
                                  theme.colorScheme.secondary.withOpacity(0.08),
                                  Colors.transparent,
                                ],
                                radius: 0.7,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Podium content
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: isSmallScreen ? 140 : 160,
                    child: Stack(
                      alignment: Alignment.bottomCenter,
                      clipBehavior: Clip.none,
                      children: [
                        // Podium platforms with staggered animation
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            // Second place platform (only if second place exists)
                            if (secondPlace != null) ...[
                              _buildPodiumPlatform(
                                context: context,
                                height: secondPlaceHeight,
                                width: isSmallScreen ? 60 : 75,
                                rank: 2,
                                isSmallScreen: isSmallScreen,
                              ),
                              const SizedBox(width: 2),
                            ],
                            // First place platform (always exists at this point)
                            _buildPodiumPlatform(
                              context: context,
                              height: firstPlaceHeight,
                              width: isSmallScreen ? 68 : 83,
                              rank: 1,
                              isSmallScreen: isSmallScreen,
                            ),
                            // Third place platform (only if third place exists)
                            if (thirdPlace != null) ...[
                              const SizedBox(width: 2),
                              _buildPodiumPlatform(
                                context: context,
                                height: thirdPlaceHeight,
                                width: isSmallScreen ? 52 : 67,
                                rank: 3,
                                isSmallScreen: isSmallScreen,
                              ),
                            ],
                          ],
                        ),
                        // Users on podium
                        // Second place user (only if exists)
                        if (secondPlace != null)
                          Positioned(
                            bottom: secondPlaceHeight + (isSmallScreen ? 4 : 6),
                            left: isSmallScreen ? 52 : 70,
                            child: _buildPodiumUser(
                              context: context,
                              user: secondPlace,
                              rank: 2,
                              size: isSmallScreen ? 42 : 52,
                              isSmallScreen: isSmallScreen,
                            ),
                          ),
                        // First place user (always exists)
                        Positioned(
                          bottom: firstPlaceHeight + (isSmallScreen ? 10 : 12),
                          child: SizedBox(
                            width: (isSmallScreen ? 46 : 56) + 20,
                            height: (isSmallScreen ? 46 : 56) + 24,
                            child: Stack(
                              clipBehavior: Clip.none,
                              alignment: Alignment.topCenter,
                              children: [
                                Positioned(
                                  top: (isSmallScreen ? 2 : 4),
                                  left: 0,
                                  right: 0,
                                  child: _buildPodiumUser(
                                    context: context,
                                    user: firstPlace!,
                                    rank: 1,
                                    size: isSmallScreen ? 46 : 56,
                                    isSmallScreen: isSmallScreen,
                                    glow: true,
                                  ),
                                ),
                                if (showCrown)
                                  Positioned(
                                    top: -2,
                                    left: 0,
                                    right: 0,
                                    child: Center(child: _buildCrown(size: isSmallScreen ? 20 : 26)),
                                  ),
                              ],
                            ),
                          ),
                        ),
                        // Third place user (only if exists)
                        if (thirdPlace != null)
                          Positioned(
                            bottom: thirdPlaceHeight + (isSmallScreen ? 4 : 6),
                            right: isSmallScreen ? 52 : 70,
                            child: _buildPodiumUser(
                              context: context,
                              user: thirdPlace,
                              rank: 3,
                              size: isSmallScreen ? 38 : 48,
                              isSmallScreen: isSmallScreen,
                            ),
                          ),
                      ],
                    ),
                  ),
                  // Enhanced divider with gradient
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 12 : 20,
                    ),
                    child: Container(
                      height: 0.7,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            theme.dividerColor.withOpacity(0.01),
                            theme.dividerColor.withOpacity(0.08),
                            theme.dividerColor.withOpacity(0.08),
                            theme.dividerColor.withOpacity(0.01),
                          ],
                          stops: const [0.0, 0.2, 0.8, 1.0],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (onCreateChallenge != null) ...[
          const SizedBox(height: 4),
          _buildChallengeButton(context),
          const SizedBox(height: 4),
        ],
      ],
    );
  }

  Widget _buildPodiumPlatform({
    required BuildContext context,
    required double height,
    required double width,
    required int rank,
    required bool isSmallScreen,
  }) {
    final theme = Theme.of(context);
    Color platformColor;
    List<Color> gradientColors;
    switch (rank) {
      case 1:
        platformColor = const Color(0xFFFFD700);
        gradientColors = [
          const Color(0xFFFFF9C4).withOpacity(0.9),
          const Color(0xFFFFD700),
          const Color(0xFFFBC02D),
        ]; // Gold
        break;
      case 2:
        platformColor = const Color(0xFFC0C0C0);
        gradientColors = [
          const Color(0xFFF5F5F5).withOpacity(0.9),
          const Color(0xFFC0C0C0),
          const Color(0xFFB0B0B0),
        ]; // Silver
        break;
      case 3:
        platformColor = const Color(0xFFCD7F32);
        gradientColors = [
          const Color(0xFFFFE0B2).withOpacity(0.9),
          const Color(0xFFCD7F32),
          const Color(0xFF8D5524),
        ]; // Bronze
        break;
      default:
        platformColor = theme.colorScheme.primary;
        gradientColors = [
          theme.colorScheme.primary.withOpacity(0.7),
          theme.colorScheme.primary,
        ];
    }
    
    return Container(
      width: width,
      height: height,
      margin: EdgeInsets.zero,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
          stops: rank == 1 ? const [0.0, 0.5, 1.0] : null,
        ),
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(10),
        ),
        boxShadow: [
          BoxShadow(
            color: platformColor.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
          BoxShadow(
            color: platformColor.withOpacity(0.1),
            blurRadius: 16,
            offset: const Offset(0, 4),
            spreadRadius: -2,
          ),
        ],
      ),
      child: Stack(
        children: [
          // Subtle shine effect
          if (rank == 1) Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 20,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.3),
                    Colors.white.withOpacity(0.1),
                    Colors.white.withOpacity(0),
                  ],
                ),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(10),
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '#$rank',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                  fontSize: isSmallScreen ? 12 : 14,
                  letterSpacing: 0.5,
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.3),
                      offset: const Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPodiumUser({
    required BuildContext context,
    required Map<String, dynamic> user,
    required int rank,
    required double size,
    required bool isSmallScreen,
    bool glow = false,
  }) {
    final theme = Theme.of(context);
    Color rankColor;
    switch (rank) {
      case 1:
        rankColor = const Color(0xFFFFD700);
        break;
      case 2:
        rankColor = const Color(0xFFC0C0C0);
        break;
      case 3:
        rankColor = const Color(0xFFCD7F32);
        break;
      default:
        rankColor = theme.colorScheme.primary;
    }
    final String avatarUrl = user['avatar']?.isNotEmpty == true 
        ? user['avatar'] 
        : 'https://ui-avatars.com/api/?name=${user['name']}&background=random';
    
    return GestureDetector(
      onTap: onUserTap != null ? () => onUserTap!(user) : null,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Avatar with border and enhanced glow
          Stack(
            alignment: Alignment.center,
            children: [
              if (glow) ...[
                // Outer glow
                AnimatedContainer(
                  duration: const Duration(seconds: 2),
                  curve: Curves.easeInOut,
                  width: size + 16,
                  height: size + 16,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: rankColor.withOpacity(0.25),
                        blurRadius: 20,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
                // Inner glow
                AnimatedContainer(
                  duration: const Duration(seconds: 1),
                  curve: Curves.easeInOut,
                  width: size + 8,
                  height: size + 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: rankColor.withOpacity(0.3),
                        blurRadius: 12,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                ),
              ],
              Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: rankColor,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: rankColor.withOpacity(0.2),
                      blurRadius: 6,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(size),
                  child: Image.network(
                    avatarUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      child: Center(
                        child: Icon(
                          Icons.person,
                          color: theme.colorScheme.primary,
                          size: size / 2,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          // Name label with glass effect
          Container(
            constraints: BoxConstraints(maxWidth: size * 1.4),
            padding: const EdgeInsets.symmetric(
              horizontal: 6,
              vertical: 2,
            ),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Text(
              user['name'],
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: isSmallScreen ? 9 : 11,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.5),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 1),
          // Score badge
          Container(
            constraints: BoxConstraints(maxWidth: size * 1.1),
            padding: const EdgeInsets.symmetric(
              horizontal: 4,
              vertical: 1,
            ),
            decoration: BoxDecoration(
              color: rankColor.withOpacity(0.9),
              borderRadius: BorderRadius.circular(6),
              boxShadow: [
                BoxShadow(
                  color: rankColor.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Text(
              user['score'],
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: isSmallScreen ? 8 : 10,
                letterSpacing: 0.2,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCrown({required double size}) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Enhanced glow effect
        Container(
          width: size * 1.4,
          height: size * 1.1,
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(size * 0.4),
            gradient: RadialGradient(
              colors: [
                Colors.amber.withOpacity(0.3),
                Colors.amber.withOpacity(0.1),
                Colors.transparent,
              ],
              stops: const [0.2, 0.6, 1.0],
            ),
          ),
        ),
        // Crown icon with enhanced effects
        ShaderMask(
          shaderCallback: (bounds) => const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFFFE082),
              Color(0xFFFFD700),
              Color(0xFFFBC02D),
            ],
          ).createShader(bounds),
          child: Icon(
            Icons.workspace_premium,
            color: Colors.white,
            size: size,
          ),
        ),
      ],
    );
  }

  Widget _buildChallengeButton(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onCreateChallenge,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 12 : 14,
              vertical: isSmallScreen ? 6 : 8,
            ),
            decoration: BoxDecoration(
              color: isDark 
                  ? theme.cardColor
                  : theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.primary.withOpacity(isDark ? 0.5 : 0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.02),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.emoji_events_outlined,
                  size: isSmallScreen ? 14 : 16,
                  color: theme.colorScheme.primary,
                ),
                SizedBox(width: isSmallScreen ? 6 : 8),
                Text(
                  'Challenges',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 12 : 13,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 