import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../../../models/public_user_profile.dart';
import '../../common/cached_avatar.dart';

class UserProfileHeader extends StatelessWidget {
  final PublicUserProfile profile;
  final Size size;
  final int bopDropsCount;

  const UserProfileHeader({
    Key? key,
    required this.profile,
    required this.size,
    this.bopDropsCount = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = size.width;
    final isDark = theme.brightness == Brightness.dark;
    
    // Calculate responsive values
    final double headerHeight = size.height * 0.12;
    final double avatarSize = screenWidth < 360 ? 60 : 65;
    final double avatarOuterSize = avatarSize + 10;
    final double fontSize = screenWidth < 360 ? 16 : 18;
    
    return Container(
      color: theme.scaffoldBackgroundColor,
      child: Stack(
        children: [
          // Beautiful gradient background matching main profile
          Container(
            height: headerHeight,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  theme.colorScheme.primary.withOpacity(isDark ? 0.4 : 0.3),
                  theme.colorScheme.secondary.withOpacity(isDark ? 0.3 : 0.2),
                  theme.colorScheme.tertiary.withOpacity(isDark ? 0.2 : 0.1),
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
          ),
          
          // Enhanced pattern overlay with blur effect
          Positioned.fill(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
            child: Container(
              height: headerHeight,
              decoration: BoxDecoration(
                backgroundBlendMode: isDark ? BlendMode.overlay : BlendMode.multiply,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                      (isDark ? Colors.white : Colors.black).withOpacity(0.05),
                      (isDark ? Colors.white : Colors.black).withOpacity(0.02),
                      (isDark ? Colors.black : Colors.white).withOpacity(0.02),
                      (isDark ? Colors.black : Colors.white).withOpacity(0.05),
                  ],
                  ),
                ),
              ),
            ),
          ),
          
          // Main content positioned like your profile header
          Positioned(
            bottom: headerHeight * 0.10,
            left: 0,
            right: 0,
            child: Column(
              children: [
                // Profile info row (image, name) - like your design
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Profile picture with enhanced effects like yours
                      Container(
                        margin: EdgeInsets.only(left: screenWidth * 0.06),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            // Enhanced glow effect
                            Container(
                              width: avatarOuterSize,
                              height: avatarOuterSize,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: theme.colorScheme.primary.withOpacity(isDark ? 0.2 : 0.15),
                                    blurRadius: 15,
                                    spreadRadius: 2,
                                  ),
                                  BoxShadow(
                                    color: theme.colorScheme.secondary.withOpacity(isDark ? 0.15 : 0.1),
                                    blurRadius: 8,
                                    offset: const Offset(4, 4),
                                  ),
                                ],
                              ),
                            ),
                            // Profile picture with glass effect
                            Container(
                              width: avatarSize,
                              height: avatarSize,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: (isDark ? Colors.white : Colors.black).withOpacity(isDark ? 0.8 : 0.1),
                                  width: 2,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(isDark ? 0.1 : 0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(avatarSize / 2),
                                child: Hero(
                                  tag: 'profile_${profile.id}',
                                  child: CachedAvatar(
                                    imageUrl: profile.profilePicUrl,
                                    radius: avatarSize / 2,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // User info with enhanced text styling like yours
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                children: [
                                  Flexible(
                                    child: Text(
                                      profile.displayName,
                                      style: TextStyle(
                                        fontSize: fontSize,
                                        fontWeight: FontWeight.w600,
                                        color: theme.colorScheme.onSurface.withOpacity(isDark ? 0.95 : 0.87),
                                        letterSpacing: 0.2,
                                        shadows: [
                                          Shadow(
                                            color: Colors.black.withOpacity(isDark ? 0.2 : 0.1),
                                            offset: const Offset(0, 1),
                                            blurRadius: 2,
                                          ),
                                        ],
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  if (profile.isVerified) ...[
                                    const SizedBox(width: 6),
                                    Icon(
                                      Icons.verified,
                                      color: theme.colorScheme.primary,
                                      size: screenWidth < 360 ? 16 : 18,
                                    ),
                                  ],
                                ],
                              ),
                              const SizedBox(height: 2),
                              Text(
                                '@${profile.username}',
                                style: TextStyle(
                                  fontSize: screenWidth < 360 ? 12 : 13,
                                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                                  letterSpacing: 0.1,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 