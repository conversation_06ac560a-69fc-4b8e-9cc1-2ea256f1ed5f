import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:shimmer/shimmer.dart';
import '../../../services/api/pin_engagement_service.dart';
import '../../../services/api_service.dart';
import '../../../services/auth_service.dart';
import '../../../models/pin_engagement.dart';
import '../../../models/user.dart';
import 'engagement_tab_view.dart';
import 'engagement_stats_header.dart';

class TrackEngagementBottomSheet extends StatefulWidget {
  final Map<String, dynamic> pin;

  const TrackEngagementBottomSheet({
    Key? key,
    required this.pin,
  }) : super(key: key);

  @override
  State<TrackEngagementBottomSheet> createState() => _TrackEngagementBottomSheetState();
}

class _TrackEngagementBottomSheetState extends State<TrackEngagementBottomSheet>
    with SingleTickerProviderStateMixin, TickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  
  late PinEngagementService _engagementService;
  PinEngagement? _engagement;
  bool _isLoading = true;
  String? _errorMessage;
  
  // Current selected tab index (0 = likes, 1 = views, 2 = comments)
  int _selectedTabIndex = 0;
  
  final List<String> _tabTypes = ['likes', 'views', 'comments'];

  @override
  void initState() {
    super.initState();
    _engagementService = PinEngagementService(ApiService(), AuthService(ApiService()));
    
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    
    _slideAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    
    _loadEngagementData();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadEngagementData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final pinId = widget.pin['id'] is String 
          ? int.parse(widget.pin['id']) 
          : widget.pin['id'];

      if (kDebugMode) {
        print('🎯 [TrackEngagement] ======== API REQUEST ========');
        print('🎯 [TrackEngagement] Requesting engagement for pin ID: $pinId');
        print('🎯 [TrackEngagement] Pin data: ${widget.pin}');
        print('🎯 [TrackEngagement] ===============================');
      }

      final engagement = await _engagementService.getPinEngagement(pinId);
      
      if (kDebugMode) {
        print('🎯 [TrackEngagement] ======== API RESPONSE ========');
        print('🎯 [TrackEngagement] Raw engagement data:');
        print('🎯 [TrackEngagement] Pin ID: ${engagement.pinId}');
        print('🎯 [TrackEngagement] Total Interactions: ${engagement.totalInteractions}');
        print('🎯 [TrackEngagement] Engagement Rate: ${engagement.engagementRate}%');
        print('🎯 [TrackEngagement] Is Owner: ${engagement.isOwner}');
        print('🎯 [TrackEngagement] ---- Counts ----');
        print('🎯 [TrackEngagement] Likes: ${engagement.counts.likes}');
        print('🎯 [TrackEngagement] Views: ${engagement.counts.views}');
        print('🎯 [TrackEngagement] Comments: ${engagement.counts.comments}');
        print('🎯 [TrackEngagement] Collects: ${engagement.counts.collects}');
        print('🎯 [TrackEngagement] Shares: ${engagement.counts.shares}');
        print('🎯 [TrackEngagement] Total from counts: ${engagement.counts.total}');
        print('🎯 [TrackEngagement] Top interaction: ${engagement.counts.topInteractionType}');
        print('🎯 [TrackEngagement] ---- Recent Activity (${engagement.recentActivity.length} items) ----');
        for (int i = 0; i < engagement.recentActivity.length; i++) {
          final activity = engagement.recentActivity[i];
          print('🎯 [TrackEngagement] Activity $i:');
          print('🎯 [TrackEngagement]   ID: ${activity.id}');
          print('🎯 [TrackEngagement]   User: ${activity.user.username} (${activity.user.id})');
          print('🎯 [TrackEngagement]   Type: ${activity.interactionType}');
          print('🎯 [TrackEngagement]   Time: ${activity.createdAt} (${activity.timeAgo})');
          print('🎯 [TrackEngagement]   Pin ID: ${activity.pinId}');
        }
        print('🎯 [TrackEngagement] ---- Detailed Engagement ----');
        if (engagement.detailedEngagement != null) {
          final detailed = engagement.detailedEngagement!;
          print('🎯 [TrackEngagement] Has detailed engagement data: true');
          print('🎯 [TrackEngagement] Upvotes (${detailed.upvotes.length}):');
          for (int i = 0; i < detailed.upvotes.length; i++) {
            final upvote = detailed.upvotes[i];
            print('🎯 [TrackEngagement]   - ${upvote.displayName} (@${upvote.username}) at ${upvote.timeAgo}');
          }
          print('🎯 [TrackEngagement] Views (${detailed.views.length}):');
          for (int i = 0; i < detailed.views.length; i++) {
            final view = detailed.views[i];
            print('🎯 [TrackEngagement]   - ${view.displayName} (@${view.username}) at ${view.timeAgo}');
          }
          print('🎯 [TrackEngagement] Comments (${detailed.comments.length}):');
          for (int i = 0; i < detailed.comments.length; i++) {
            final comment = detailed.comments[i];
            print('🎯 [TrackEngagement]   - ${comment.displayName} (@${comment.username}): "${comment.text}" at ${comment.timeAgo}');
          }
        } else {
          print('🎯 [TrackEngagement] Has detailed engagement data: false');
        }
        print('🎯 [TrackEngagement] ================================');
      }
      
      if (mounted) {
        setState(() {
          _engagement = engagement;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [TrackEngagement] ======== API ERROR ========');
        print('❌ [TrackEngagement] Error loading engagement data: $e');
        print('❌ [TrackEngagement] Error type: ${e.runtimeType}');
        print('❌ [TrackEngagement] Stack trace: ${StackTrace.current}');
        print('❌ [TrackEngagement] =============================');
      }
      
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final themeColor = theme.colorScheme.primary;
    final screenHeight = MediaQuery.of(context).size.height;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, _slideAnimation.value * screenHeight),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: screenHeight * 0.9,
                ),
                decoration: BoxDecoration(
                  color: theme.scaffoldBackgroundColor,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.outline.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    
                    // Header
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: isKeyboardVisible ? 16 : 24,
                        vertical: isKeyboardVisible ? 16 : 24,
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: themeColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.analytics_rounded,
                              color: themeColor,
                              size: screenHeight < 600 ? 20 : 24,
                            ),
                          ),
                          SizedBox(width: screenHeight < 600 ? 12 : 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Track Engagement',
                                  style: TextStyle(
                                    fontSize: screenHeight < 600 ? 20 : 24,
                                    fontWeight: FontWeight.w700,
                                    color: theme.colorScheme.onSurface,
                                    letterSpacing: -0.5,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${widget.pin['songTitle'] ?? 'Unknown Track'}',
                                  style: TextStyle(
                                    fontSize: screenHeight < 600 ? 12 : 14,
                                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            icon: Icon(
                              Icons.close_rounded,
                              color: theme.colorScheme.onSurface.withOpacity(0.6),
                              size: screenHeight < 600 ? 20 : 24,
                            ),
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                        ],
                      ),
                    ),
                    
                    // Content
                    Flexible(
                      child: _buildContent(theme, isDark, themeColor),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildContent(ThemeData theme, bool isDark, Color themeColor) {
    if (_isLoading) {
      return _buildLoadingState(theme);
    }
    
    if (_errorMessage != null) {
      return _buildErrorState(theme, themeColor);
    }
    
    if (_engagement == null) {
      return _buildEmptyState(theme);
    }

    return Column(
      children: [
        // Interactive Stats Header (acts as tab controller)
        EngagementStatsHeader(
          engagement: _engagement!,
          pin: widget.pin,
          selectedTabIndex: _selectedTabIndex,
          onTabChanged: (index) {
            setState(() {
              _selectedTabIndex = index;
            });
          },
        ),
        
        const SizedBox(height: 4),
        
        // Content for selected tab
        Expanded(
          child: EngagementTabView(
            pin: widget.pin,
            engagement: _engagement!,
            tabType: _tabTypes[_selectedTabIndex],
            engagementService: _engagementService,
            key: ValueKey(_selectedTabIndex), // Force rebuild when tab changes
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    
    return Shimmer.fromColors(
      baseColor: isDark ? Colors.grey[800]! : Colors.grey[300]!,
      highlightColor: isDark ? Colors.grey[700]! : Colors.grey[100]!,
      period: const Duration(milliseconds: 1500),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Stats Header Skeleton
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // Main engagement stat
                  Container(
                    height: 32,
                    width: 120,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 16,
                    width: 200,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  const SizedBox(height: 20),
                  // Stats row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: List.generate(4, (index) => Column(
                      children: [
                        Container(
                          height: 24,
                          width: 36,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          height: 12,
                          width: 50,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    )),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Tab Navigation Skeleton
            Container(
              height: 48,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: List.generate(3, (index) => Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(4),
                    height: 40,
                    decoration: BoxDecoration(
                      color: index == 0 ? Colors.grey[400] : Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                )),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // User Activity List Skeleton
            ...List.generate(4, (index) => Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Row(
                children: [
                  // Profile picture
                  Container(
                    width: 40,
                    height: 40,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // User info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 16,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 6),
                        Container(
                          height: 12,
                          width: 100,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Action icon
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme, Color themeColor) {
    return Container(
      padding: const EdgeInsets.all(48),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: 48,
            color: Colors.red.withOpacity(0.6),
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load engagement data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'Unknown error occurred',
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadEngagementData,
            style: ElevatedButton.styleFrom(
              backgroundColor: themeColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(48),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 48,
            color: theme.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No engagement data available',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This track hasn\'t received any interactions yet.',
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
} 