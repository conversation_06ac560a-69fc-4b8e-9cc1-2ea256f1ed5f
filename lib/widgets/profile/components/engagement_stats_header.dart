import 'package:flutter/material.dart';
import '../../../models/pin_engagement.dart';

class EngagementStatsHeader extends StatelessWidget {
  final PinEngagement engagement;
  final Map<String, dynamic> pin;
  final int selectedTabIndex;
  final ValueChanged<int> onTabChanged;

  const EngagementStatsHeader({
    Key? key,
    required this.engagement,
    required this.pin,
    required this.selectedTabIndex,
    required this.onTabChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isVerySmallScreen = screenWidth < 320;

    final tabs = [
      {'type': 'likes', 'icon': Icons.favorite, 'color': Colors.red, 'count': engagement.counts.likes},
      {'type': 'views', 'icon': Icons.visibility, 'color': Colors.green, 'count': engagement.counts.views},
      {'type': 'comments', 'icon': Icons.comment, 'color': Colors.blue, 'count': engagement.counts.comments},
    ];

    return Container(
      margin: EdgeInsets.fromLTRB(
        isVerySmallScreen ? 12 : (isSmallScreen ? 16 : 24), 
        0, 
        isVerySmallScreen ? 12 : (isSmallScreen ? 16 : 24), 
        isSmallScreen ? 12 : 16 // Reduced bottom margin
      ),
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16), // Reduced padding
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(isDark ? 0.1 : 0.05),
            theme.colorScheme.secondary.withOpacity(isDark ? 0.08 : 0.03),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.1 : 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Track Info Row
          Row(
            children: [
              // Album Art
              Container(
                width: isSmallScreen ? 44 : 48, // Slightly smaller
                height: isSmallScreen ? 44 : 48,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: pin['image'] != null
                      ? Image.network(
                          pin['image'],
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => 
                              _buildPlaceholder(theme, isSmallScreen),
                        )
                      : _buildPlaceholder(theme, isSmallScreen),
                ),
              ),
              
              SizedBox(width: isSmallScreen ? 10 : 12), // Reduced spacing
              
              // Track Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      pin['songTitle'] ?? 'Unknown Track',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 14 : 16,
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      pin['artist'] ?? 'Unknown Artist',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 12 : 14,
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: isSmallScreen ? 3 : 6), // Reduced spacing
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: isSmallScreen ? 12 : 14,
                          color: theme.colorScheme.primary.withOpacity(0.7),
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            pin['location'] ?? 'Unknown Location',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 10 : 12,
                              color: theme.colorScheme.primary.withOpacity(0.7),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          SizedBox(height: isSmallScreen ? 12 : 16), // Reduced spacing
          
          // Interactive Stats Grid - Acts as Tab Controller
          Row(
            children: tabs.asMap().entries.map((entry) {
              final index = entry.key;
              final tab = entry.value;
              final isSelected = selectedTabIndex == index;
              
              return Expanded(
                child: GestureDetector(
                  onTap: () => onTabChanged(index),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 2 : 4),
                    padding: EdgeInsets.symmetric(
                      vertical: isSmallScreen ? 10 : 14, 
                      horizontal: isSmallScreen ? 6 : 10
                    ),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? (tab['color'] as Color).withOpacity(0.15)
                          : theme.colorScheme.surface.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected 
                            ? (tab['color'] as Color).withOpacity(0.4)
                            : (tab['color'] as Color).withOpacity(0.2),
                        width: isSelected ? 2 : 1,
                      ),
                      boxShadow: isSelected ? [
                        BoxShadow(
                          color: (tab['color'] as Color).withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Column(
                      children: [
                        Icon(
                          tab['icon'] as IconData,
                          size: isSmallScreen ? 18 : 22,
                          color: isSelected 
                              ? tab['color'] as Color
                              : (tab['color'] as Color).withOpacity(0.7),
                        ),
                        SizedBox(height: isSmallScreen ? 3 : 6),
                        Text(
                          _formatNumber(tab['count'] as int),
                          style: TextStyle(
                            fontSize: isSmallScreen ? 16 : 18,
                            fontWeight: FontWeight.w700,
                            color: isSelected 
                                ? tab['color'] as Color
                                : theme.colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          _getTabLabel(tab['type'] as String),
                          style: TextStyle(
                            fontSize: isSmallScreen ? 10 : 12,
                            fontWeight: FontWeight.w500,
                            color: isSelected 
                                ? (tab['color'] as Color).withOpacity(0.8)
                                : theme.colorScheme.onSurface.withOpacity(0.6),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          
          // Total Interactions - Made more compact
          SizedBox(height: isSmallScreen ? 8 : 12), // Reduced spacing
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 10 : 14, // Reduced padding
              vertical: isSmallScreen ? 6 : 8 // Reduced padding
            ),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface.withOpacity(0.5),
              borderRadius: BorderRadius.circular(10), // Slightly smaller radius
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.analytics_outlined,
                  size: isSmallScreen ? 14 : 16, // Smaller icon
                  color: theme.colorScheme.primary,
                ),
                SizedBox(width: isSmallScreen ? 4 : 6), // Reduced spacing
                Text(
                  'Total: ',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 11 : 13, // Smaller text
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                Text(
                  _formatNumber(engagement.totalInteractions),
                  style: TextStyle(
                    fontSize: isSmallScreen ? 11 : 13, // Smaller text
                    fontWeight: FontWeight.w700,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholder(ThemeData theme, bool isSmallScreen) {
    return Container(
      color: theme.colorScheme.surfaceVariant,
      child: Icon(
        Icons.music_note,
        size: isSmallScreen ? 20 : 24,
        color: theme.colorScheme.primary.withOpacity(0.5),
      ),
    );
  }

  String _getTabLabel(String type) {
    switch (type) {
      case 'likes':
        return 'Likes';
      case 'views':
        return 'Views';
      case 'comments':
        return 'Comments';
      default:
        return type;
    }
  }

  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    }
    return number.toString();
  }
} 