import 'package:flutter/material.dart';

class PinCardSkeleton extends StatefulWidget {
  final bool isSmallScreen;

  const PinCardSkeleton({
    Key? key,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  State<PinCardSkeleton> createState() => _PinCardSkeletonState();
}

class _PinCardSkeletonState extends State<PinCardSkeleton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double imageSize = widget.isSmallScreen ? 64 : 72;
    final double borderRadius = widget.isSmallScreen ? 8 : 10;
    final double contentPadding = widget.isSmallScreen ? 6 : 8;

    return Container(
      margin: EdgeInsets.only(bottom: widget.isSmallScreen ? 4 : 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Material(
          color: Theme.of(context).cardColor,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildAlbumArtSkeleton(context, imageSize),
              _buildPinInfoSkeleton(context, contentPadding),
              _buildActionButtonSkeleton(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumArtSkeleton(BuildContext context, double imageSize) {
    return SizedBox(
      width: imageSize,
      height: imageSize,
      child: Stack(
        children: [
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return _buildShimmerContainer(
                context,
                width: imageSize,
                height: imageSize,
                borderRadius: 0,
              );
            },
          ),
          Positioned(
            top: 4,
            left: 4,
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return _buildShimmerContainer(
                  context,
                  width: 20,
                  height: 20,
                  borderRadius: 10,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPinInfoSkeleton(BuildContext context, double contentPadding) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(contentPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Song title
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return _buildShimmerContainer(
                  context,
                  width: MediaQuery.of(context).size.width * 0.45,
                  height: widget.isSmallScreen ? 13 : 14,
                  borderRadius: 6,
                );
              },
            ),
            const SizedBox(height: 4),
            // Artist
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return _buildShimmerContainer(
                  context,
                  width: MediaQuery.of(context).size.width * 0.35,
                  height: widget.isSmallScreen ? 11 : 12,
                  borderRadius: 6,
                );
              },
            ),
            const SizedBox(height: 4),
            // Location info
            Row(
              children: [
                AnimatedBuilder(
                  animation: _animation,
                  builder: (context, child) {
                    return _buildShimmerContainer(
                      context,
                      width: widget.isSmallScreen ? 11 : 12,
                      height: widget.isSmallScreen ? 11 : 12,
                      borderRadius: 6,
                    );
                  },
                ),
                const SizedBox(width: 4),
                AnimatedBuilder(
                  animation: _animation,
                  builder: (context, child) {
                    return _buildShimmerContainer(
                      context,
                      width: MediaQuery.of(context).size.width * 0.25,
                      height: widget.isSmallScreen ? 10 : 11,
                      borderRadius: 6,
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 4),
            // Stats row
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatChipSkeleton(context),
                const SizedBox(width: 6),
                _buildStatChipSkeleton(context),
                const SizedBox(width: 6),
                _buildStatChipSkeleton(context),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatChipSkeleton(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return _buildShimmerContainer(
          context,
          width: widget.isSmallScreen ? 32 : 36,
          height: widget.isSmallScreen ? 16 : 18,
          borderRadius: 8,
        );
      },
    );
  }

  Widget _buildActionButtonSkeleton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: widget.isSmallScreen ? 4 : 6),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return _buildShimmerContainer(
            context,
            width: 24,
            height: 24,
            borderRadius: 12,
          );
        },
      ),
    );
  }

  Widget _buildShimmerContainer(
    BuildContext context, {
    required double width,
    required double height,
    required double borderRadius,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final baseColor = isDark
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFE0E0E0);
    final highlightColor = isDark
        ? const Color(0xFF3A3A3A)
        : const Color(0xFFF5F5F5);

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          begin: Alignment(-1.0 - _animation.value, 0.0),
          end: Alignment(1.0 - _animation.value, 0.0),
          colors: [
            baseColor,
            highlightColor,
            baseColor,
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
    );
  }
} 