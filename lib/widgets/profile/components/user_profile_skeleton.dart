import 'package:flutter/material.dart';
import 'dart:ui';

/// Modern skeleton loading for user profile screen
/// Mimics the exact structure with smooth shimmer animations
class UserProfileSkeleton extends StatefulWidget {
  const UserProfileSkeleton({Key? key}) : super(key: key);

  @override
  State<UserProfileSkeleton> createState() => _UserProfileSkeletonState();
}

class _UserProfileSkeletonState extends State<UserProfileSkeleton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    // Start the shimmer animation and repeat
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;
    final isDark = theme.brightness == Brightness.dark;
    
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        automaticallyImplyLeading: false,
        elevation: 0,
        toolbarHeight: 0,
      ),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            // Header skeleton
            SliverAppBar(
              expandedHeight: size.height * 0.22,
              toolbarHeight: 56.0,
              collapsedHeight: 56.0,
              floating: false,
              pinned: false,
              stretch: true,
              backgroundColor: Colors.transparent,
              elevation: 0,
              shadowColor: Colors.transparent,
              automaticallyImplyLeading: false,
              leading: Container(
                margin: const EdgeInsets.all(8),
                child: _buildShimmerContainer(
                  width: 32,
                  height: 32,
                  borderRadius: 16,
                  baseColor: Colors.white.withOpacity(0.2),
                  highlightColor: Colors.white.withOpacity(0.4),
                ),
              ),
              actions: [
                Container(
                  margin: const EdgeInsets.all(8),
                  child: _buildShimmerContainer(
                    width: 32,
                    height: 32,
                    borderRadius: 16,
                    baseColor: Colors.white.withOpacity(0.2),
                    highlightColor: Colors.white.withOpacity(0.4),
                  ),
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        theme.colorScheme.primary.withOpacity(0.3),
                        theme.colorScheme.secondary.withOpacity(0.2),
                        theme.colorScheme.tertiary.withOpacity(0.1),
                      ],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Background effects
                      Positioned(
                        top: 60,
                        right: -20,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withOpacity(0.05),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: -30,
                        left: -40,
                        child: Container(
                          width: 180,
                          height: 180,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withOpacity(0.03),
                          ),
                        ),
                      ),
                      // Profile content
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                theme.scaffoldBackgroundColor.withOpacity(0.8),
                                theme.scaffoldBackgroundColor,
                              ],
                              stops: const [0.0, 0.7, 1.0],
                            ),
                          ),
                          child: SafeArea(
                            bottom: false,
                            child: _buildProfileHeaderSkeleton(theme),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // Stats skeleton
            SliverToBoxAdapter(
              child: _buildStatsSkeleton(theme),
            ),
            
            // Action buttons skeleton
            SliverToBoxAdapter(
              child: _buildActionButtonsSkeleton(theme),
            ),
            
            // Bio skeleton
            SliverToBoxAdapter(
              child: _buildBioSkeleton(theme),
            ),
            
            // Tabs skeleton
            SliverPersistentHeader(
              pinned: false,
              delegate: _SliverAppBarDelegate(
                _buildTabsSkeleton(theme, screenWidth),
              ),
            ),
          ];
        },
        body: _buildTabContentSkeleton(theme),
      ),
    );
  }

  Widget _buildProfileHeaderSkeleton(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        children: [
          // Profile picture skeleton
          _buildShimmerContainer(
            width: 80,
            height: 80,
            borderRadius: 40,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
          
          const SizedBox(width: 16),
          
          // Profile info skeleton
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Name skeleton
                _buildShimmerContainer(
                  width: MediaQuery.of(context).size.width * 0.4,
                  height: 20,
                  borderRadius: 10,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
                
                const SizedBox(height: 8),
                
                // Username skeleton
                _buildShimmerContainer(
                  width: MediaQuery.of(context).size.width * 0.3,
                  height: 16,
                  borderRadius: 8,
                  baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                ),
                
                const SizedBox(height: 12),
                
                // Stats row skeleton
                Row(
                  children: [
                    _buildShimmerContainer(
                      width: 60,
                      height: 14,
                      borderRadius: 7,
                      baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                      highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                    ),
                    const SizedBox(width: 16),
                    _buildShimmerContainer(
                      width: 60,
                      height: 14,
                      borderRadius: 7,
                      baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                      highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSkeleton(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(3, (index) => Column(
          children: [
            _buildShimmerContainer(
              width: 32,
              height: 24,
              borderRadius: 12,
              baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
            ),
            const SizedBox(height: 4),
            _buildShimmerContainer(
              width: 60,
              height: 12,
              borderRadius: 6,
              baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
            ),
          ],
        )),
      ),
    );
  }

  Widget _buildActionButtonsSkeleton(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildShimmerContainer(
              width: double.infinity,
              height: 44,
              borderRadius: 22,
              baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
            ),
          ),
          const SizedBox(width: 12),
          _buildShimmerContainer(
            width: 44,
            height: 44,
            borderRadius: 22,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
        ],
      ),
    );
  }

  Widget _buildBioSkeleton(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.1),
        ),
      ),
      child: Column(
        children: [
          _buildShimmerContainer(
            width: double.infinity,
            height: 14,
            borderRadius: 7,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
          const SizedBox(height: 6),
          _buildShimmerContainer(
            width: MediaQuery.of(context).size.width * 0.7,
            height: 14,
            borderRadius: 7,
            baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
          ),
        ],
      ),
    );
  }

  Widget _buildTabsSkeleton(ThemeData theme, double screenWidth) {
    return Container(
      color: theme.scaffoldBackgroundColor,
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth < 360 ? 16 : 24,
        vertical: 8,
      ),
      child: Row(
        children: List.generate(3, (index) => Expanded(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: _buildShimmerContainer(
              width: double.infinity,
              height: 32,
              borderRadius: 16,
              baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
            ),
          ),
        )),
      ),
    );
  }

  Widget _buildTabContentSkeleton(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header skeleton
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildShimmerContainer(
                width: 120,
                height: 20,
                borderRadius: 10,
                baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
              ),
              _buildShimmerContainer(
                width: 32,
                height: 16,
                borderRadius: 8,
                baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Grid content skeleton
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 0.85,
            ),
            itemCount: 6,
            itemBuilder: (context, index) => _buildGridItemSkeleton(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildGridItemSkeleton(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image skeleton
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: _buildShimmerContainer(
                width: double.infinity,
                height: double.infinity,
                borderRadius: 16,
                baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
              ),
            ),
          ),
          
          // Content skeleton
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildShimmerContainer(
                    width: double.infinity,
                    height: 14,
                    borderRadius: 7,
                    baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                    highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                  ),
                  const SizedBox(height: 6),
                  _buildShimmerContainer(
                    width: MediaQuery.of(context).size.width * 0.3,
                    height: 12,
                    borderRadius: 6,
                    baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                    highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      _buildShimmerContainer(
                        width: 24,
                        height: 12,
                        borderRadius: 6,
                        baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                        highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                      ),
                      const SizedBox(width: 8),
                      _buildShimmerContainer(
                        width: 24,
                        height: 12,
                        borderRadius: 6,
                        baseColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                        highlightColor: theme.colorScheme.surfaceVariant.withOpacity(0.6),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerContainer({
    required double width,
    required double height,
    required double borderRadius,
    required Color baseColor,
    required Color highlightColor,
  }) {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
            gradient: LinearGradient(
              begin: Alignment(-1.0, -0.3),
              end: Alignment(1.0, 0.3),
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                (_shimmerAnimation.value - 0.3).clamp(0.0, 1.0),
                _shimmerAnimation.value.clamp(0.0, 1.0),
                (_shimmerAnimation.value + 0.3).clamp(0.0, 1.0),
              ],
            ),
          ),
        );
      },
    );
  }
}

// Helper delegate for sliver app bar
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _SliverAppBarDelegate(this.child);

  @override
  double get minExtent => 56.0;

  @override
  double get maxExtent => 56.0;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
} 