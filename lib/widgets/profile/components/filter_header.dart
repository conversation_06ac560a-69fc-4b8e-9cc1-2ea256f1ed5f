import 'package:flutter/material.dart';

class FilterHeader extends StatelessWidget {
  final List<String> filterOptions;
  final String currentFilter;
  final Function(String) onFilterSelected;
  final bool isSmallScreen;

  const FilterHeader({
    Key? key,
    required this.filterOptions,
    required this.currentFilter,
    required this.onFilterSelected,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: isSmallScreen ? 40 : 48,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor.withOpacity(0.05),
            width: 1,
          ),
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 0,
        vertical: isSmallScreen ? 4.0 : 6.0,
      ),
      margin: EdgeInsets.zero,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16.0 : 24.0),
        child: Row(
          children: filterOptions.map((filter) {
            final isSelected = filter == currentFilter;
            final theme = Theme.of(context);
            final primary = theme.colorScheme.primary;
            final isDarkMode = theme.brightness == Brightness.dark;
            
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOut,
                decoration: BoxDecoration(
                  color: isSelected 
                    ? primary.withOpacity(0.08)
                    : theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected
                      ? primary.withOpacity(0.2)
                      : isDarkMode
                          ? Colors.white
                          : Colors.black,
                    width: 1,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => onFilterSelected(filter),
                    borderRadius: BorderRadius.circular(16),
                    splashColor: primary.withOpacity(0.1),
                    highlightColor: primary.withOpacity(0.05),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 8 : 10,
                        vertical: isSmallScreen ? 4 : 6,
                      ),
                      child: Text(
                        filter,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 11 : 12,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                          color: isSelected
                            ? primary
                            : theme.colorScheme.onSurface.withOpacity(0.8),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

class SliverFilterHeaderDelegate extends SliverPersistentHeaderDelegate {
  final List<String> filterOptions;
  final String currentFilter;
  final Function(String) onFilterSelected;
  final bool isSmallScreen;

  SliverFilterHeaderDelegate({
    required this.filterOptions,
    required this.currentFilter,
    required this.onFilterSelected,
    required this.isSmallScreen,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return FilterHeader(
      filterOptions: filterOptions,
      currentFilter: currentFilter,
      onFilterSelected: onFilterSelected,
      isSmallScreen: isSmallScreen,
    );
  }

  @override
  double get maxExtent => isSmallScreen ? 40 : 48;

  @override
  double get minExtent => isSmallScreen ? 40 : 48;

  @override
  bool shouldRebuild(SliverFilterHeaderDelegate oldDelegate) {
    return oldDelegate.currentFilter != currentFilter ||
           oldDelegate.isSmallScreen != isSmallScreen;
  }
} 