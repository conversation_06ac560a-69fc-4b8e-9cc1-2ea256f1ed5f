import 'package:flutter/material.dart';
import '../../../models/user.dart';
import '../../../models/pin_engagement.dart';
import '../../../models/pin_interaction.dart';
import '../../../services/api/pin_engagement_service.dart';

class UserEngagementTile extends StatelessWidget {
  final dynamic item; // Can be UserEngagementDetails, CommentDetails, or PinInteraction
  final String tabType;
  final ValueChanged<int> onUserTap;

  const UserEngagementTile({
    Key? key,
    required this.item,
    required this.tabType,
    required this.onUserTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final avatarSize = isSmallScreen ? 44.0 : 48.0;
    final padding = isSmallScreen ? 12.0 : 16.0;

    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.1 : 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onUserTap(_getUserId()),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Row(
              children: [
                // Profile Picture
                _buildProfilePicture(theme, avatarSize, isSmallScreen),
                
                SizedBox(width: isSmallScreen ? 10 : 12),
                
                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Username and verified badge
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              _getDisplayName(),
                              style: TextStyle(
                                fontSize: isSmallScreen ? 14 : 16,
                                fontWeight: FontWeight.w600,
                                color: theme.colorScheme.onSurface,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (_isVerified())
                            Container(
                              margin: const EdgeInsets.only(left: 8),
                              child: Icon(
                                Icons.verified,
                                size: isSmallScreen ? 14 : 16,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                        ],
                      ),
                      
                      SizedBox(height: isSmallScreen ? 2 : 4),
                      
                      // Bio or interaction details
                      if (tabType == 'comments')
                        _buildCommentContent(theme, isSmallScreen)
                      else
                        _buildUserBio(theme, isSmallScreen),
                    ],
                  ),
                ),
                
                // Right side content
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Interaction badge
                    _buildInteractionBadge(theme, isSmallScreen),
                    
                    SizedBox(height: isSmallScreen ? 2 : 4),
                    
                    // Time
                    Text(
                      _getTimeAgo(),
                      style: TextStyle(
                        fontSize: isSmallScreen ? 10 : 12,
                        color: theme.colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfilePicture(ThemeData theme, double size, bool isSmallScreen) {
    final profilePic = _getProfilePictureUrl();
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipOval(
        child: profilePic != null && profilePic.isNotEmpty
            ? Image.network(
                profilePic,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => 
                    _buildDefaultAvatar(theme, isSmallScreen),
              )
            : _buildDefaultAvatar(theme, isSmallScreen),
      ),
    );
  }

  Widget _buildDefaultAvatar(ThemeData theme, bool isSmallScreen) {
    final username = _getDisplayName();
    final initial = username.isNotEmpty ? username[0].toUpperCase() : '?';
    
    return Container(
      color: theme.colorScheme.primary.withOpacity(0.1),
      child: Center(
        child: Text(
          initial,
          style: TextStyle(
            fontSize: isSmallScreen ? 16 : 18,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildCommentContent(ThemeData theme, bool isSmallScreen) {
    // Handle both new CommentDetails and old Comment models
    String commentText = '';
    bool isEdited = false;
    
    if (item is CommentDetails) {
      final comment = item as CommentDetails;
      commentText = comment.text;
      isEdited = comment.isEdited;
    } else if (item is Comment) {
      final comment = item as Comment;
      commentText = comment.text;
      isEdited = comment.isEdited;
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          commentText,
          style: TextStyle(
            fontSize: isSmallScreen ? 12 : 14,
            color: theme.colorScheme.onSurface.withOpacity(0.8),
            height: 1.3,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        if (isEdited) ...[
          SizedBox(height: isSmallScreen ? 2 : 4),
          Text(
            'Edited',
            style: TextStyle(
              fontSize: isSmallScreen ? 10 : 12,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildUserBio(ThemeData theme, bool isSmallScreen) {
    // For likes and views, show the username or a simple subtitle
    final username = _getUsername();
    
    return Text(
      '@$username',
      style: TextStyle(
        fontSize: isSmallScreen ? 11 : 13,
        color: theme.colorScheme.onSurface.withOpacity(0.6),
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildInteractionBadge(ThemeData theme, bool isSmallScreen) {
    final (icon, color, label) = _getInteractionDetails();
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 6 : 8,
        vertical: isSmallScreen ? 3 : 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: isSmallScreen ? 12 : 14,
            color: color,
          ),
          if (label.isNotEmpty) ...[
            SizedBox(width: isSmallScreen ? 3 : 4),
            Text(
              label,
              style: TextStyle(
                fontSize: isSmallScreen ? 9 : 11,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Helper methods to extract data from different item types
  int _getUserId() {
    if (item is UserEngagementDetails) {
      return (item as UserEngagementDetails).userId;
    } else if (item is CommentDetails) {
      return (item as CommentDetails).userId;
    } else if (item is PinInteraction) {
      final interaction = item as PinInteraction;
      return interaction.user.id;
    } else if (item is User) {
      return (item as User).id;
    } else if (item is Comment) {
      return (item as Comment).userId;
    }
    return 0;
  }

  String _getDisplayName() {
    if (item is UserEngagementDetails) {
      return (item as UserEngagementDetails).displayName;
    } else if (item is CommentDetails) {
      return (item as CommentDetails).displayName;
    } else if (item is PinInteraction) {
      final interaction = item as PinInteraction;
      return interaction.user.displayName ?? interaction.user.displayNameComputed;
    } else if (item is User) {
      final user = item as User;
      return user.displayName ?? user.displayNameComputed;
    } else if (item is Comment && (item as Comment).user != null) {
      final user = (item as Comment).user!;
      return user.displayName ?? user.displayNameComputed;
    }
    return 'Unknown User';
  }

  String _getUsername() {
    if (item is UserEngagementDetails) {
      return (item as UserEngagementDetails).username;
    } else if (item is CommentDetails) {
      return (item as CommentDetails).username;
    } else if (item is PinInteraction) {
      final interaction = item as PinInteraction;
      return interaction.user.username;
    } else if (item is User) {
      return (item as User).username;
    } else if (item is Comment && (item as Comment).user != null) {
      return (item as Comment).user!.username;
    }
    return 'unknown';
  }

  String? _getProfilePictureUrl() {
    if (item is UserEngagementDetails) {
      return (item as UserEngagementDetails).profilePic;
    } else if (item is CommentDetails) {
      return (item as CommentDetails).profilePic;
    } else if (item is PinInteraction) {
      final interaction = item as PinInteraction;
      return interaction.user.profilePicUrl ?? interaction.user.profilePictureUrl;
    } else if (item is User) {
      final user = item as User;
      return user.profilePicUrl ?? user.profilePictureUrl;
    } else if (item is Comment && (item as Comment).user != null) {
      final user = (item as Comment).user!;
      return user.profilePicUrl ?? user.profilePictureUrl;
    }
    return null;
  }

  String _getTimeAgo() {
    if (item is UserEngagementDetails) {
      return (item as UserEngagementDetails).timeAgo;
    } else if (item is CommentDetails) {
      return (item as CommentDetails).timeAgo;
    } else if (item is PinInteraction) {
      final interaction = item as PinInteraction;
      return interaction.timeAgo;
    } else if (item is Comment) {
      return (item as Comment).timeAgo;
    }
    return '';
  }

  bool _isVerified() {
    // For now, assume no verification badge for detailed engagement data
    // This could be extended if the API provides verification status
    if (item is PinInteraction) {
      final interaction = item as PinInteraction;
      return interaction.user.isVerified;
    } else if (item is User) {
      return (item as User).isVerified;
    }
    return false;
  }

  (IconData, Color, String) _getInteractionDetails() {
    switch (tabType) {
      case 'likes':
        return (Icons.favorite, Colors.red, 'Liked');
      case 'views':
        return (Icons.visibility, Colors.green, 'Viewed');
      case 'comments':
        return (Icons.comment, Colors.blue, 'Commented');
      default:
        return (Icons.info, Colors.grey, '');
    }
  }
} 