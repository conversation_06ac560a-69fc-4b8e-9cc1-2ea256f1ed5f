import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../services/api/pin_engagement_service.dart';
import '../../../models/pin_engagement.dart';
import '../../../models/user.dart';
import '../../../models/pin_interaction.dart';
import 'user_engagement_tile.dart';
import '../../../screens/profile/user_profile_screen.dart';

class EngagementTabView extends StatefulWidget {
  final Map<String, dynamic> pin;
  final PinEngagement engagement;
  final String tabType;
  final PinEngagementService engagementService;

  const EngagementTabView({
    Key? key,
    required this.pin,
    required this.engagement,
    required this.tabType,
    required this.engagementService,
  }) : super(key: key);

  @override
  State<EngagementTabView> createState() => _EngagementTabViewState();
}

class _EngagementTabViewState extends State<EngagementTabView>
    with AutomaticKeepAliveClientMixin {
  
  final ScrollController _scrollController = ScrollController();
  
  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  List<dynamic> _getItemsForTab() {
    // Use detailed engagement data if available
    if (widget.engagement.detailedEngagement != null) {
      switch (widget.tabType) {
        case 'likes':
          return widget.engagement.detailedEngagement!.likes;
        case 'views':
          return widget.engagement.detailedEngagement!.views;
        case 'comments':
          return widget.engagement.detailedEngagement!.comments;
        default:
          return [];
      }
    }
    
    // Fallback to recent activity if detailed engagement is not available
    return widget.engagement.recentActivity
        .where((activity) {
          switch (widget.tabType) {
            case 'likes':
              return activity.isLike || activity.isUpvote;
            case 'views':
              return activity.isView;
            case 'comments':
              return activity.interactionType == 'comment';
            default:
              return false;
          }
        })
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final horizontalPadding = screenWidth < 360 ? 16.0 : (screenWidth < 400 ? 20.0 : 24.0);
    
    final items = _getItemsForTab();

    if (kDebugMode) {
      print('🎯 [EngagementTabView] ======== TAB DATA ========');
      print('🎯 [EngagementTabView] Tab: ${widget.tabType}');
      print('🎯 [EngagementTabView] Items count: ${items.length}');
      print('🎯 [EngagementTabView] Has detailed engagement: ${widget.engagement.detailedEngagement != null}');
      if (widget.engagement.detailedEngagement != null) {
        print('🎯 [EngagementTabView] Detailed engagement data:');
        print('🎯 [EngagementTabView]   - Likes: ${widget.engagement.detailedEngagement!.likes.length}');
        print('🎯 [EngagementTabView]   - Views: ${widget.engagement.detailedEngagement!.views.length}');
        print('🎯 [EngagementTabView]   - Comments: ${widget.engagement.detailedEngagement!.comments.length}');
      }
      print('🎯 [EngagementTabView] ===============================');
    }

    if (items.isEmpty) {
      return _buildEmptyState(theme);
    }

    return ListView.builder(
      controller: _scrollController,
      physics: const AlwaysScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding, vertical: 16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        
        return UserEngagementTile(
          item: item,
          tabType: widget.tabType,
          onUserTap: (userId) => _navigateToUserProfile(userId),
        );
      },
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    String getMessage() {
      switch (widget.tabType) {
        case 'likes':
          return 'No likes yet';
        case 'views':
          return 'No views yet';
        case 'comments':
          return 'No comments yet';
        default:
          return 'No data available';
      }
    }

    String getSubMessage() {
      switch (widget.tabType) {
        case 'likes':
          return 'Be the first to like this track!';
        case 'views':
          return 'Views will appear here when people discover this track.';
        case 'comments':
          return 'Start the conversation!';
        default:
          return 'Check back later.';
      }
    }

    IconData getIcon() {
      switch (widget.tabType) {
        case 'likes':
          return Icons.favorite_outline;
        case 'views':
          return Icons.visibility_outlined;
        case 'comments':
          return Icons.chat_bubble_outline;
        default:
          return Icons.info_outline;
      }
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(48),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              getIcon(),
              size: 48,
              color: theme.colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              getMessage(),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              getSubMessage(),
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToUserProfile(int userId) {
    if (kDebugMode) {
      print('🎯 [EngagementTabView] Navigate to user profile: $userId');
    }
    
    // Skip navigation for invalid user IDs
    if (userId <= 0) {
      if (kDebugMode) {
        print('🎯 [EngagementTabView] Invalid user ID: $userId');
      }
      return;
    }
    
    // Navigate to user profile
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(
          userId: userId,
          showBottomNav: false,
        ),
      ),
    );
  }
} 