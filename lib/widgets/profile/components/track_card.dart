import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../../providers/spotify_provider.dart';
import '../../../providers/apple_music_provider.dart';
import '../../../providers/youtube_provider.dart';
import '../../../models/music_track.dart';
import '../../../services/music/hybrid_queue_manager.dart';

class TrackCard extends StatelessWidget {
  final MusicTrack track;
  final bool isSmallScreen;
  final VoidCallback? onTap;
  final VoidCallback? onPlay;
  final List<PopupMenuItem>? additionalOptions;

  const TrackCard({
    Key? key,
    required this.track,
    required this.isSmallScreen,
    this.onTap,
    this.onPlay,
    this.additionalOptions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double imageSize = isSmallScreen ? 44 : 48;
    final double borderRadius = 8;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        // Remove shadow for flatter design
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Dismissible(
          key: ValueKey('track_${track.id}'),
          direction: DismissDirection.startToEnd,
          confirmDismiss: (direction) async {
            // Add to queue and show feedback, but don't actually dismiss
            await _addToQueue(context);
            return false; // Don't dismiss the card
          },
          background: Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(left: 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: [
                  Colors.transparent,
                  Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  Theme.of(context).colorScheme.primary.withOpacity(0.3),
                ],
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.queue_music_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  'Add to Queue',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          child: Material(
            color: Theme.of(context).brightness == Brightness.light
                ? Theme.of(context).scaffoldBackgroundColor
                : Theme.of(context).cardColor,
            child: InkWell(
              onTap: onTap ?? () => _playTrack(context),
              child: SizedBox(
                height: isSmallScreen ? 52 : 56,
                child: Row(
                  children: [
                    _buildAlbumArt(context, imageSize),
                    _buildTrackInfo(context),
                    _buildShareButton(context),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumArt(BuildContext context, double imageSize) {
    return Padding(
      padding: const EdgeInsets.all(4),
      child: SizedBox(
        width: imageSize,
        height: imageSize,
        child: track.albumArt.isNotEmpty
            ? Image.network(
                track.albumArt,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildPlaceholder(context, imageSize),
              )
            : _buildPlaceholder(context, imageSize),
      ),
    );
  }

  Widget _buildPlaceholder(BuildContext context, double imageSize) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceVariant,
      child: Icon(
        Icons.music_note,
        size: imageSize * 0.4,
        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      ),
    );
  }

  Widget _buildTrackInfo(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              track.title.isNotEmpty ? track.title : 'Unknown Track',
              style: TextStyle(
                fontSize: isSmallScreen ? 13 : 14,
                fontWeight: FontWeight.w500,
                letterSpacing: -0.2,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              track.artist.isNotEmpty ? track.artist : 'Unknown Artist',
              style: TextStyle(
                fontSize: isSmallScreen ? 11 : 12,
                color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.8),
                letterSpacing: -0.1,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareButton(BuildContext context) {
    return PopupMenuButton(
      icon: const Icon(Icons.more_vert),
      color: Theme.of(context).colorScheme.primary,
      itemBuilder: (context) => [
        PopupMenuItem(
          child: const Text('Share'),
          onTap: () {
            // Existing share logic
            showModalBottomSheet(
              context: context,
              builder: (context) => Container(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).padding.bottom + 16,
                  top: 16,
                  left: 16,
                  right: 16,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Share "${track.title}"',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildShareOption(
                          context,
                          icon: Icons.message_outlined,
                          label: 'Message',
                          onTap: () {
                            Navigator.pop(context);
                            // TODO: Implement message sharing
                          },
                        ),
                        _buildShareOption(
                          context,
                          icon: Icons.copy_outlined,
                          label: 'Copy Link',
                          onTap: () {
                            Navigator.pop(context);
                            // TODO: Implement link copying
                          },
                        ),
                        _buildShareOption(
                          context,
                          icon: Icons.more_horiz,
                          label: 'More',
                          onTap: () {
                            Navigator.pop(context);
                            // TODO: Implement more sharing options
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              ),
            );
          },
        ),
        // Add additional options if provided
        if (additionalOptions != null) ...additionalOptions!,
      ],
      onSelected: (value) {},
      padding: EdgeInsets.zero,
      constraints: BoxConstraints(
        minWidth: isSmallScreen ? 32 : 36,
        minHeight: isSmallScreen ? 32 : 36,
      ),
    );
  }

  Widget _buildShareOption(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 72,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 28),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Modify _playTrack method to use onPlay if provided
  void _playTrack(BuildContext context) {
    if (onPlay != null) {
      onPlay!();
    } else {
      // Try Apple Music first, then fallback to YouTube
      _tryAppleMusicWithYouTubeFallback(context);
    }
  }

  // Try Apple Music first, fallback to YouTube
  Future<void> _tryAppleMusicWithYouTubeFallback(BuildContext context) async {
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
    final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
    
    bool success = false;

    // Always try Apple Music first (bypass connection check like collection_detail_screen.dart)
    try {
      if (kDebugMode) {
        print('🎵 [TrackCard] Attempting Apple Music playback for: ${track.title}');
      }

      // Use queue manager directly like the working implementation
      final queueManager = appleMusicProvider.queueManager;
      success = await queueManager.setQueue(
        tracks: [track],
        collectionType: 'single_track',
        startIndex: 0,
      );

      if (kDebugMode) {
        print('🎵 [TrackCard] Apple Music queue result: success=$success');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🎵 [TrackCard] Apple Music queue failed: $e');
      }
      success = false;
    }

    // Fallback to YouTube if Apple Music failed
    if (!success) {
      if (kDebugMode) {
        print('🎵 [TrackCard] Apple Music failed, trying YouTube fallback...');
      }
      if (context.mounted) {
        await _tryYouTubeFallback(context);
      }
    } else {
      if (kDebugMode) {
        print('✅ [TrackCard] Successfully played track on Apple Music: ${track.title}');
      }
    }
  }

  // Try YouTube as fallback
  Future<void> _tryYouTubeFallback(BuildContext context) async {
    try {
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
      
      // Create MusicTrack for YouTube
      final musicTrack = MusicTrack(
        id: track.id,
        title: track.title,
        artist: track.artist,
        album: track.album,
        albumArt: track.albumArt,
        uri: track.uri,
        durationMs: track.durationMs,
        url: track.url,
        service: 'youtube',
        serviceType: 'youtube',
        genres: track.genres,
        explicit: track.explicit,
        popularity: track.popularity,
      );
      
      final success = await youtubeProvider.playTrack(musicTrack);
      
      if (success) {
        if (kDebugMode) {
          print('✅ [TrackCard] Successfully played track on YouTube: ${track.title}');
        }
      } else {
        if (kDebugMode) {
          print('❌ [TrackCard] YouTube playback failed: ${track.title}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [TrackCard] YouTube fallback error: $e');
      }
    }
  }

  // Add to queue functionality
  Future<void> _addToQueue(BuildContext context) async {
    try {
      final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

      bool success = false;

      // Prioritize Apple Music when connected
      if (appleMusicProvider.isConnected) {
        // Use Apple Music queue manager to add track
        final queueManager = appleMusicProvider.queueManager;
        queueManager.addToQueue(track);
        success = true; // addToQueue doesn't return a value, assume success
      } else if (spotifyProvider.isConnected) {
        // Fallback to Spotify hybrid queue manager
        success = await spotifyProvider.hybridQueueManager.addTrackToQueue(track);
      }
      
      if (success && context.mounted) {
        // Show success feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.queue_music_rounded,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Added "${track.title}" to queue',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      } else if (context.mounted) {
        // Show error feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error_outline_rounded,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Failed to add "${track.title}" to queue',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding to queue: $e'),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
} 