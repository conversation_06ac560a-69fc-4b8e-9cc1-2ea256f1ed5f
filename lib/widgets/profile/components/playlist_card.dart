import 'package:flutter/material.dart';

class PlaylistCard extends StatelessWidget {
  final Map<String, dynamic> playlist;
  final bool isSmallScreen;
  final VoidCallback? onTap;

  const PlaylistCard({
    Key? key,
    required this.playlist,
    required this.isSmallScreen,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double imageSize = isSmallScreen ? 56 : 64;
    final double borderRadius = isSmallScreen ? 8 : 10;

    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 4 : 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Material(
          color: Theme.of(context).cardColor,
          child: InkWell(
            onTap: onTap,
            child: Row(
              children: [
                _buildPlaylistImage(context, imageSize),
                _buildPlaylistInfo(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaylistImage(BuildContext context, double imageSize) {
    String? imageUrl;
    
    // Handle different image formats (Spotify vs Apple Music)
    if (playlist['images'] != null && (playlist['images'] as List).isNotEmpty) {
      // Spotify format
      imageUrl = playlist['images'][0]['url'];
    } else if (playlist['image_url'] != null && playlist['image_url'].toString().isNotEmpty) {
      // Apple Music format
      imageUrl = playlist['image_url'];
    }
    
    return SizedBox(
      width: imageSize,
      height: imageSize,
      child: imageUrl != null
          ? Image.network(
              imageUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => _buildPlaceholder(context, imageSize),
            )
          : _buildPlaceholder(context, imageSize),
    );
  }

  Widget _buildPlaceholder(BuildContext context, double imageSize) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceVariant,
      child: Icon(
        Icons.playlist_play,
        size: imageSize * 0.4,
        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      ),
    );
  }

  Widget _buildPlaylistInfo(BuildContext context) {
    final owner = playlist['owner']?['display_name'] ?? 'Unknown';
    
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              playlist['name'] ?? 'Untitled Playlist',
              style: TextStyle(
                fontSize: isSmallScreen ? 13 : 14,
                fontWeight: FontWeight.w600,
                letterSpacing: -0.2,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              'By $owner',
              style: TextStyle(
                fontSize: isSmallScreen ? 11 : 12,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                letterSpacing: -0.1,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }


} 