import 'package:bop_maps/providers/youtube_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../../providers/spotify_provider.dart';
import '../../../providers/apple_music_provider.dart';
import '../../../models/music_track.dart';
import '../../../services/music/youtube_player_service.dart';
import 'stat_chip.dart';
import 'music_service_icon.dart';
import '../../../providers/pin_provider.dart';

class PinCard extends StatelessWidget {
  final Map<String, dynamic> pin;
  final bool isSmallScreen;
  final VoidCallback? onEngagementTap;
  final VoidCallback? onPlay;

  const PinCard({
    Key? key,
    required this.pin,
    required this.isSmallScreen,
    this.onEngagementTap,
    this.onPlay,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double imageSize = isSmallScreen ? 64 : 72;
    final double borderRadius = isSmallScreen ? 8 : 10;
    final double contentPadding = isSmallScreen ? 6 : 8;

    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 4 : 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Dismissible(
          key: ValueKey('pin_${pin['id']}'),
          direction: DismissDirection.startToEnd,
          confirmDismiss: (direction) async {
            // Add to YouTube queue and show feedback, but don't actually dismiss
            await _addToYouTubeQueue(context);
            return false; // Don't dismiss the card
          },
          background: Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(left: 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: [
                  Colors.transparent,
                  const Color(0xFFFF0000).withOpacity(0.1), // YouTube red
                  const Color(0xFFFF0000).withOpacity(0.3), // YouTube red
                ],
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.queue_music_rounded,
                  color: const Color(0xFFFF0000), // YouTube red
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  'Add to Queue',
                  style: TextStyle(
                    color: const Color(0xFFFF0000), // YouTube red
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          child: Material(
            color: Theme.of(context).cardColor,
            child: InkWell(
              onTap: onEngagementTap,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _buildAlbumArt(context, imageSize),
                  _buildPinInfo(context, contentPadding),
                  _buildActionButtons(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumArt(BuildContext context, double imageSize) {
    return SizedBox(
      width: imageSize,
      height: imageSize,
      child: Stack(
        children: [
          if (pin['image'] != null)
            Image.network(
              pin['image']!,
              fit: BoxFit.cover,
              width: imageSize,
              height: imageSize,
              errorBuilder: (context, error, stackTrace) => _buildPlaceholder(context, imageSize),
            )
          else
            _buildPlaceholder(context, imageSize),
          Positioned(
            top: 4,
            left: 4,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                shape: BoxShape.circle,
              ),
              child: MusicServiceIcon(service: pin['service'] ?? ''),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholder(BuildContext context, double imageSize) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceVariant,
      child: Icon(
        Icons.music_note,
        size: imageSize * 0.4,
        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      ),
    );
  }

  Widget _buildPinInfo(BuildContext context, double contentPadding) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(contentPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              pin['songTitle'] ?? 'Unknown Song',
              style: TextStyle(
                fontSize: isSmallScreen ? 13 : 14,
                fontWeight: FontWeight.w600,
                letterSpacing: -0.2,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              pin['artist'] ?? 'Unknown Artist',
              style: TextStyle(
                fontSize: isSmallScreen ? 11 : 12,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                letterSpacing: -0.1,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            _buildLocationInfo(context),
            const SizedBox(height: 2),
            _buildStatsRow(context),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationInfo(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.location_on,
          size: isSmallScreen ? 11 : 12,
          color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
        ),
        const SizedBox(width: 2),
        Expanded(
          child: Text(
            pin['location'] ?? 'Unknown Location',
            style: TextStyle(
              fontSize: isSmallScreen ? 10 : 11,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
              letterSpacing: -0.1,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildStatsRow(BuildContext context) {
    // Extract engagement counts from the new API response structure
    final engagementCounts = pin['engagement_counts'] as Map<String, dynamic>?;
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        StatChip(
          icon: Icons.favorite,
          count: (engagementCounts?['likes'] ?? pin['likes'] ?? 0).toString(),
          color: Colors.red,
          isSmallScreen: isSmallScreen,
        ),
        const SizedBox(width: 6),
        StatChip(
          icon: Icons.comment,
          count: (engagementCounts?['comments'] ?? pin['comments'] ?? 0).toString(),
          color: Colors.blue,
          isSmallScreen: isSmallScreen,
        ),
        const SizedBox(width: 6),
        StatChip(
          icon: Icons.play_arrow,
          count: (engagementCounts?['views'] ?? pin['plays'] ?? pin['views'] ?? 0).toString(),
          color: Colors.green,
          isSmallScreen: isSmallScreen,
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: isSmallScreen ? 4 : 6),
      child: IconButton(
        icon: Icon(
          Icons.play_circle_filled, 
          color: _getServiceColor(),
        ),
        onPressed: onPlay ?? () => _playTrack(context),
        tooltip: 'Play track',
        iconSize: isSmallScreen ? 32 : 36,
        padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
        constraints: BoxConstraints(
          minWidth: isSmallScreen ? 48 : 52,
          minHeight: isSmallScreen ? 48 : 52,
        ),
      ),
    );
  }

  Color _getServiceColor() {
    final service = pin['service'] ?? '';
    switch (service.toLowerCase()) {
      case 'spotify':
        return const Color(0xFF1DB954);
      case 'apple_music':
      case 'apple':
        return const Color(0xFFFA243C);
      case 'soundcloud':
        return const Color(0xFFFF5500);
      default:
        return const Color(0xFF1DB954);
    }
  }

  Future<void> _playTrack(BuildContext context) async {
    try {
      final service = pin['service'] ?? '';
      
      if (kDebugMode) {
        print('🎵 [PinCard] Playing track: ${pin['songTitle'] ?? pin['title']} by ${pin['artist']} (service: $service)');
        print('🎵 [PinCard] Pin data: ${pin.keys.toList()}');
      }
      
      // Get PinProvider to record interactions
      final pinProvider = Provider.of<PinProvider>(context, listen: false);
      
      // Attempt to normalize Apple Music URIs to the `apple:track:` form so
      // the AppleMusicProvider can bypass the search step and play directly.
      String? originalUri = pin['uri']?.toString();
      String normalizedUri = originalUri ?? '';

      if ((service == 'apple_music' || service == 'apple') && originalUri != null) {
        if (originalUri.contains('music.apple.com') && originalUri.contains('?i=')) {
          // Album-style URL with ?i=trackId parameter
          final trackId = originalUri.split('?i=').last.split('&').first;
          if (trackId.isNotEmpty) {
            normalizedUri = 'apple:track:$trackId';
            if (kDebugMode) {
              print('🍎 [PinCard] Extracted Apple Music track ID "$trackId" from album URL');
            }
          }
        } else if (originalUri.contains('music.apple.com') && originalUri.contains('/song/')) {
          // Direct song URL format …/song/name/trackId
          final parts = originalUri.split('/');
          final trackId = parts.isNotEmpty ? parts.last.split('?').first : '';
          if (trackId.isNotEmpty) {
            normalizedUri = 'apple:track:$trackId';
            if (kDebugMode) {
              print('🍎 [PinCard] Extracted Apple Music track ID "$trackId" from song URL');
            }
          }
        }
      }

      final pinData = {
        'id': pin['id']?.toString() ?? '',
        'title': pin['songTitle'] ?? pin['title'],
        'track_title': pin['songTitle'] ?? pin['title'],
        'artist': pin['artist'],
        'track_artist': pin['artist'],
        'album': pin['genre'] ?? pin['album'],
        'artwork_url': pin['image'],
        'uri': normalizedUri.isNotEmpty ? normalizedUri : originalUri,
        'url': originalUri,
        'service': service,
        'track_url': originalUri,
        'caption': pin['caption'],
        'description': pin['description'],
        'duration': pin['duration'] ?? pin['duration_ms'] ?? pin['track_duration'],
        'duration_ms': pin['duration_ms'] ?? pin['duration'],
        'owner': {
          'username': pin['username'] ?? 'Unknown',
          'profile_pic': pin['profile_pic'],
        },
        'interaction_count': pin['engagement_counts'] ?? pin['interaction_count'] ?? {},
      };

      // Set currently playing pin (this will automatically record view and collect interactions)
      final pinId = pin['id']?.toString();
      if (pinId != null) {
        pinProvider.setCurrentlyPlayingPin(pinId, pinData);
      }

      bool success = false;
      final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      
      if (service == 'spotify') {
        // For BOPmaps pins, try Apple Music first, then YouTube fallback
        success = await _tryAppleMusicWithYouTubeFallback(context, pinData);
      } else if (service == 'apple_music' || service == 'apple') {
        success = await _tryAppleMusicWithYouTubeFallback(context, pinData);
      } else {
        // For unsupported services, try Apple Music first, then YouTube as fallback
        if (kDebugMode) {
          print('🎵 [PinCard] Unsupported service: $service, trying Apple Music then YouTube fallback');
        }
        success = await _tryAppleMusicWithYouTubeFallback(context, pinData);
      }

      if (!success) {
        // Clear the pin data if playback failed
        if (pinId != null) {
          pinProvider.clearCurrentlyPlayingPin();
        }
        _showError(context, 'Failed to play track');
      } else {
        final trackTitle = pin['songTitle'] ?? pin['title'] ?? 'Unknown Song';
        // Removed the snackbar notification
        if (kDebugMode) {
          print('✅ [PinCard] Track playback successful: $trackTitle');
        }
      }
    } catch (e) {
      // Clear the pin data on error
      final pinProvider = Provider.of<PinProvider>(context, listen: false);
      final pinId = pin['id']?.toString();
      if (pinId != null) {
        pinProvider.clearCurrentlyPlayingPin();
      }
      
      if (kDebugMode) {
        print('❌ [PinCard] Error playing track: $e');
      }
      
      // Provide more specific error messages
      String errorMessage = 'Error playing track';
      if (e.toString().toLowerCase().contains('unauthorized') ||
          e.toString().toLowerCase().contains('401')) {
        errorMessage = 'Authentication expired. Please reconnect in Settings.';
      } else if (e.toString().toLowerCase().contains('subscription')) {
        errorMessage = 'Music subscription required';
      } else if (e.toString().toLowerCase().contains('not found')) {
        errorMessage = 'Track not available';
      } else {
        errorMessage = 'Error playing track: ${e.toString().split(':').last.trim()}';
      }
      
      _showError(context, errorMessage);
    }
  }

  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showInfo(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
            ),
            const SizedBox(width: 12),
            Text(message),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showSuccess(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Try Apple Music first, fallback to YouTube if needed
  Future<bool> _tryAppleMusicWithYouTubeFallback(BuildContext context, Map<String, dynamic> pinData) async {
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);

    bool success = false;

    // Always try Apple Music first using the same approach as TrackCard
    try {
      if (kDebugMode) {
        print('🎵 [PinCard] Attempting Apple Music playback for pin');
        print('🎵 [PinCard] Pin data keys: ${pinData.keys.toList()}');
        print('🎵 [PinCard] Track title: ${pinData['track_title'] ?? pinData['title']}');
        print('🎵 [PinCard] Track artist: ${pinData['track_artist'] ?? pinData['artist']}');
        print('🎵 [PinCard] Artwork URL: ${pinData['artwork_url'] ?? pinData['image']}');
      }

      // Convert pin data to MusicTrack using correct pin field names
      final originalUri = pinData['uri'] ?? pinData['track_uri'] ?? '';
      final originalUrl = pinData['track_url'] ?? pinData['url'] ?? '';
      final service = pinData['service'] ?? 'spotify'; // Default to spotify for pins

      // If the original URI is from Spotify, clear it for Apple Music search
      final cleanUri = originalUri.contains('spotify:') ? '' : originalUri;
      final cleanUrl = originalUrl.contains('spotify.com') ? '' : originalUrl;

      final musicTrack = MusicTrack(
        id: pinData['id']?.toString() ?? '',
        title: pinData['track_title'] ?? pinData['title'] ?? pinData['songTitle'] ?? 'Unknown Song',
        artist: pinData['track_artist'] ?? pinData['artist'] ?? 'Unknown Artist',
        album: pinData['album'] ?? '',
        albumArt: pinData['artwork_url'] ?? pinData['image'] ?? '',
        uri: cleanUri, // Use cleaned URI to force Apple Music search
        durationMs: (pinData['duration_ms'] is int) ? pinData['duration_ms'] :
                   (pinData['duration'] is int) ? pinData['duration'] : 0,
        url: cleanUrl, // Use cleaned URL
        service: service,
        serviceType: service == 'apple_music' || service == 'apple' ? 'apple' : 'spotify',
        genres: [],
        explicit: false,
        popularity: 0,
      );

      if (kDebugMode) {
        print('🎵 [PinCard] Created MusicTrack: ${musicTrack.title} by ${musicTrack.artist}');
        print('🎵 [PinCard] MusicTrack service: ${musicTrack.service}');
        print('🎵 [PinCard] MusicTrack serviceType: ${musicTrack.serviceType}');
        print('🎵 [PinCard] Attempting Apple Music first, then YouTube fallback if needed');
      }

      // Use the same approach as TrackCard
      final needsSearch = musicTrack.service != 'apple_music' && musicTrack.service != 'apple';
      
      if (needsSearch) {
        // Use the method that shows snackbar for no exact match (same as TrackCard)
        success = await appleMusicProvider.playTrackBySearchWithFallback(musicTrack, context);
      } else {
        // For Apple Music tracks, use queue manager directly (same as TrackCard)
        final queueManager = appleMusicProvider.queueManager;
        success = await queueManager.setQueue(
          tracks: [musicTrack],
          collectionType: 'single_track',
          startIndex: 0,
        );
      }

      if (kDebugMode) {
        print('🎵 [PinCard] Apple Music result: success=$success, needsSearch=$needsSearch');
      }

      if (success) {
        final trackTitle = pinData['title'] ?? pinData['track_title'] ?? 'Unknown Song';
        if (kDebugMode) {
          print('✅ [PinCard] Apple Music playback successful: $trackTitle');
        }
        return true;
      }
      
      // Only fallback to YouTube automatically if it's not a search scenario
      // For search scenarios, the snackbar will handle user-controlled fallback (same as TrackCard)
      if (!success) {
        if (kDebugMode) {
          print('🎵 [PinCard] Apple Music failed, trying YouTube fallback...');
        }
        if (context.mounted) {
          return await _tryYouTubeFallback(context, pinData);
        }
        return false;
      }
      
      // For search scenarios that failed, don't auto-fallback (user gets snackbar option)
      return false;

    } catch (e) {
      if (kDebugMode) {
        print('🎵 [PinCard] Apple Music error, trying YouTube fallback: $e');
      }
      if (context.mounted) {
        return await _tryYouTubeFallback(context, pinData);
      }
      return false;
    }
  }



  /// Try YouTube as final fallback when all other services fail
  Future<bool> _tryYouTubeFallback(BuildContext context, Map<String, dynamic> pinData) async {
    try {
      if (kDebugMode) {
        print('🎵 [PinCard] Trying YouTube fallback for: ${pinData['title']}');
      }
      
      // Get YouTube provider
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
      
      // Use YouTube provider's playTrackFromPin method for proper pin communication
      final success = await youtubeProvider.playTrackFromPin(pinData, context: context);
      
      if (success) {
        final trackTitle = pinData['title'] ?? pinData['track_title'] ?? 'Unknown Song';
        // Removed the snackbar notification
        if (kDebugMode) {
          print('✅ [PinCard] YouTube fallback successful: $trackTitle');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ [PinCard] YouTube fallback failed');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [PinCard] Error in YouTube fallback: $e');
      }
      return false;
    }
  }

  Future<void> _addToYouTubeQueue(BuildContext context) async {
    try {
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
      
      // Initialize YouTube provider if not already initialized
      if (!youtubeProvider.isInitialized) {
        final initialized = await youtubeProvider.initialize();
        if (!initialized) {
          if (context.mounted) {
            _showError(context, 'Failed to initialize YouTube player');
          }
          return;
        }
      }
      
      // Create MusicTrack object from pin data
      final track = MusicTrack(
        id: pin['id']?.toString() ?? '',
        title: pin['songTitle'] ?? pin['title'] ?? 'Unknown Title',
        artist: pin['artist'] ?? 'Unknown Artist',
        album: pin['genre'] ?? pin['album'] ?? '',
        albumArt: pin['image'] ?? '',
        uri: pin['uri']?.toString() ?? '',
        url: pin['url']?.toString() ?? '',
        service: 'youtube', // Convert to YouTube for queueing
        serviceType: 'youtube',
        genres: [],
        durationMs: 0,
        explicit: false,
        popularity: 0,
      );
      
      // Add to YouTube queue
      youtubeProvider.addToQueue(track);
      
      if (context.mounted) {
        // Show success feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.queue_music_rounded,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Added "${track.title}" to YouTube queue',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: const Color(0xFFFF0000), // YouTube red
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [PinCard] Error adding to YouTube queue: $e');
      }
      if (context.mounted) {
        _showError(context, 'Failed to add to YouTube queue: $e');
      }
    }
  }
}