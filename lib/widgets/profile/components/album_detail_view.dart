import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import '../../../models/music_track.dart';
import '../../../providers/spotify_provider.dart';
import '../../../providers/apple_music_provider.dart';
import 'track_card.dart';

class AlbumDetailView extends StatefulWidget {
  final Map<String, dynamic> album;
  final bool isSmallScreen;
  final String service;
  final VoidCallback onBack;

  const AlbumDetailView({
    Key? key,
    required this.album,
    required this.isSmallScreen,
    required this.service,
    required this.onBack,
  }) : super(key: key);

  @override
  State<AlbumDetailView> createState() => _AlbumDetailViewState();
}

class _AlbumDetailViewState extends State<AlbumDetailView> {
  List<MusicTrack> _tracks = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String? _error;
  int _totalTrackCount = 0;
  int _currentOffset = 0;
  static const int _pageSize = 50;

  @override
  void initState() {
    super.initState();
    _loadAlbumTracks();
    _loadAlbumMetadata();
    // Reset parent NestedScrollView scroll to top when opening detail
    SchedulerBinding.instance.addPostFrameCallback((_) {
      final primaryController = PrimaryScrollController.of(context);
      if (primaryController != null && primaryController.hasClients) {
        primaryController.jumpTo(0);
      }
    });
  }

  Future<void> _loadAlbumMetadata() async {
    if (widget.service == 'apple_music') {
      print('🔍 Loading metadata for Apple Music album: ${widget.album['id']}');
      try {
        // For albums, try to get metadata from the album data itself
        final trackCount = widget.album['attributes']?['trackCount'] ?? 
                          widget.album['track_count'] ?? 
                          widget.album['trackCount'] ?? 0;
        print('✅ Got track count from album data: $trackCount');
        setState(() {
          _totalTrackCount = trackCount;
        });
      } catch (e) {
        print('❌ Error loading album metadata: $e');
        setState(() {
          _totalTrackCount = 0;
        });
      }
    } else {
      // For Spotify, use the existing logic
      setState(() {
        _totalTrackCount = widget.album['total_tracks'] ?? widget.album['track_count'] ?? 0;
      });
    }
  }

  Future<void> _loadAlbumTracks() async {
    try {
      if (widget.service == 'apple_music') {
        final appleProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        
        // Fetch real album tracks from Apple Music
        final tracks = await appleProvider.getAlbumTracks(
          widget.album['id'],
          limit: _pageSize,
          offset: 0,
        );
        
        if (mounted) {
          setState(() {
            _tracks = tracks;
            _currentOffset = tracks.length;
            _isLoading = false;
          });
        }
      } else {
        // For Spotify albums, implement actual track fetching
        final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
        // TODO: Implement getAlbumTracks method in SpotifyProvider
        // final tracks = await spotifyProvider.getAlbumTracks(widget.album['id']);
        
        // Placeholder implementation for Spotify
        final albumName = widget.album['name'] ?? 'Unknown Album';
        final artistName = widget.album['artists']?[0]?['name'] ?? 'Unknown Artist';
        final albumArt = widget.album['images']?[0]?['url'] ?? '';
        final trackCount = widget.album['total_tracks'] ?? 10;
        
        _tracks = List.generate(trackCount, (index) => MusicTrack(
          id: '${widget.album['id']}_track_${index + 1}',
          title: 'Track ${index + 1}',
          artist: artistName,
          album: albumName,
          albumArt: albumArt,
          uri: '${widget.album['id']}_track_${index + 1}',
          durationMs: 180000 + (index * 15000),
          url: '',
          service: 'spotify',
          serviceType: 'spotify',
          genres: [],
          explicit: false,
          popularity: 0,
        ));
        
        if (mounted) {
          setState(() {
            _tracks = _tracks;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to load album tracks: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreTracks() async {
    if (_isLoadingMore) return;

    if (widget.service == 'apple_music') {
      // Check if we've loaded all tracks
      if (_currentOffset >= _totalTrackCount) {
        return;
      }

      setState(() {
        _isLoadingMore = true;
      });

      try {
        print('🔍 Loading more Apple Music album tracks: offset=$_currentOffset, limit=$_pageSize');
        final appleProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        final moreTracks = await appleProvider.getAlbumTracks(
          widget.album['id'],
          limit: _pageSize,
          offset: _currentOffset,
        );
        
        if (mounted) {
          setState(() {
            _tracks.addAll(moreTracks);
            _currentOffset += moreTracks.length;
            _isLoadingMore = false;
          });
          print('✅ Loaded ${moreTracks.length} more album tracks. Total: ${_tracks.length}/$_totalTrackCount');
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
        }
        print('Error loading more Apple Music album tracks: $e');
      }
    } else {
      // Spotify logic for album tracks
      // TODO: Implement Spotify album track pagination
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(context),
        Expanded(
          child: _buildContent(context),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: widget.onBack,
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
            ),
          ),
          const SizedBox(width: 12),
          _buildAlbumImage(context),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.album['attributes']?['name'] ?? widget.album['name'] ?? 'Unknown Album',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  'By ${widget.album['attributes']?['artistName'] ?? widget.album['artist'] ?? 'Unknown Artist'}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (_totalTrackCount > 0)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      '$_totalTrackCount tracks',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          _buildPlayButton(context),
        ],
      ),
    );
  }

  Widget _buildAlbumImage(BuildContext context) {
    String? imageUrl = widget.album['attributes']?['artwork']?['url'] ?? 
                      widget.album['image_url'] ?? 
                      widget.album['images']?[0]?['url'];
    
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: SizedBox(
        width: 60,
        height: 60,
        child: imageUrl != null && imageUrl.isNotEmpty
            ? Image.network(
                imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildImagePlaceholder(context),
              )
            : _buildImagePlaceholder(context),
      ),
    );
  }

  Widget _buildImagePlaceholder(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceVariant,
      child: Icon(
        Icons.album,
        size: 30,
        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      ),
    );
  }

  Widget _buildPlayButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: const Icon(Icons.play_arrow),
        color: Theme.of(context).colorScheme.onPrimary,
        onPressed: () {
          // Play the entire album starting from the first track
          if (_tracks.isNotEmpty) {
            if (widget.service == 'apple_music') {
              Provider.of<AppleMusicProvider>(context, listen: false).playAlbum(
                albumId: widget.album['id'],
                startIndex: 0,
              );
            } else {
              // For Spotify, play individual track for now
              // TODO: Implement Spotify album playback
              Provider.of<SpotifyProvider>(context, listen: false).playTrack(_tracks.first);
            }
          }
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final albumId = widget.album['id'];
    final isApple = widget.service == 'apple_music';
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
    final total = _totalTrackCount;
    final isLoadingMore = _isLoadingMore;
    
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red.withOpacity(0.8),
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _error = null;
                  _isLoading = true;
                });
                _loadAlbumTracks();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_tracks.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.music_off,
              size: 48,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No tracks found in this album',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadAlbumTracks();
        _loadAlbumMetadata();
      },
      child: NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          if (notification.metrics.pixels > notification.metrics.maxScrollExtent * 0.8) {
            _loadMoreTracks();
          }
          return false;
        },
        child: ListView.builder(
          primary: true,
          itemCount: _tracks.length + (isLoadingMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == _tracks.length) {
              return const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              );
            }
            final track = _tracks[index];
            return TrackCard(
              track: track,
              isSmallScreen: widget.isSmallScreen,
              onTap: () async {
                if (isApple) {
                  try {
                    // Play the entire album starting from this track
                    final success = await appleMusicProvider.playAlbum(
                      albumId: widget.album['id'],
                      startIndex: index,
                    );
                    if (!success && mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Failed to play "${track.title}"'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    } else if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Now playing: ${track.title} from album'),
                          backgroundColor: Colors.green,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error playing track: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                } else {
                  // For Spotify, play individual track for now
                  // TODO: Implement Spotify album playback
                  spotifyProvider.playTrack(track, context: context);
                }
              },
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
} 