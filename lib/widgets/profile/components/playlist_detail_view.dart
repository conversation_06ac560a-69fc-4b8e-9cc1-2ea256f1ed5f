import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import '../../../models/music_track.dart';
import '../../../providers/spotify_provider.dart';
import '../../../providers/apple_music_provider.dart';
import '../../../services/music/shuffle_service.dart';
import 'track_card.dart';
import 'package:flutter/foundation.dart';

class PlaylistDetailView extends StatefulWidget {
  final Map<String, dynamic> playlist;
  final bool isSmallScreen;
  final VoidCallback onBack;

  const PlaylistDetailView({
    Key? key,
    required this.playlist,
    required this.isSmallScreen,
    required this.onBack,
  }) : super(key: key);

  @override
  State<PlaylistDetailView> createState() => _PlaylistDetailViewState();
}

class _PlaylistDetailViewState extends State<PlaylistDetailView> {
  List<MusicTrack> _tracks = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String? _error;
  int _totalTrackCount = 0;
  int _currentOffset = 0;
  static const int _pageSize = 50;
  
  // Add scroll controller for reliable scroll reset
  late final ScrollController _scrollController;
  
  // Flag to track if this is the first build
  bool _isFirstBuild = true;

  @override
  void initState() {
    super.initState();
    // Initialize scroll controller
    _scrollController = ScrollController();
    
    _loadPlaylistTracks();
    _loadPlaylistMetadata();
    
    // Multiple scroll reset attempts to ensure it always starts at top
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _resetScrollToTop();
    });
    
    // Additional reset after a short delay to catch any late scroll events
    Future.delayed(const Duration(milliseconds: 100), () {
      _resetScrollToTop();
    });
    
    // Final reset after a longer delay to ensure it's at top
    Future.delayed(const Duration(milliseconds: 300), () {
      _resetScrollToTop();
    });
  }
  
  /// Aggressively reset scroll position to top
  void _resetScrollToTop() {
    try {
      // Reset primary controller
      final primaryController = PrimaryScrollController.of(context);
      if (primaryController != null && primaryController.hasClients) {
        primaryController.jumpTo(0);
      }
      
      // Reset our custom scroll controller
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(0);
      }
      
      // Force a rebuild to ensure scroll position is at top
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      // Ignore any scroll controller errors
    }
  }

  Future<void> _loadPlaylistMetadata() async {
    if (widget.playlist['service'] == 'apple_music') {
      print('🔍 Loading metadata for Apple Music playlist: ${widget.playlist['id']}');
      try {
        final appleProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        final metadata = await appleProvider.getPlaylistMetadata(widget.playlist['id']);
        if (mounted && metadata != null) {
          final trackCount = metadata['trackCount'] ?? 0;
          print('✅ Got track count from metadata: $trackCount');
          setState(() {
            _totalTrackCount = trackCount;
          });
        } else {
          print('❌ No metadata returned for playlist');
          setState(() {
            _totalTrackCount = 0;
          });
        }
      } catch (e) {
        print('❌ Error loading playlist metadata: $e');
        // For Apple Music, if metadata fails, set to 0 since initial count is unreliable
        setState(() {
          _totalTrackCount = 0;
        });
      }
    } else {
      // For Spotify, use the existing logic
      setState(() {
        _totalTrackCount = widget.playlist['tracks']?['total'] ?? 0;
      });
    }
  }

  Future<void> _loadPlaylistTracks() async {
    try {
      if (widget.playlist['service'] == 'apple_music') {
        final appleProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        final tracks = await appleProvider.getPlaylistTracks(
          widget.playlist['id'],
          limit: _pageSize,
          offset: 0,
        );
        if (mounted) {
          setState(() {
            _tracks = tracks;
            _currentOffset = tracks.length;
            _isLoading = false;
          });
          
          // Reset scroll to top after loading data
          SchedulerBinding.instance.addPostFrameCallback((_) {
            _resetScrollToTop();
          });
        }
      } else {
        final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
        final tracks = await spotifyProvider.getPlaylistTracks(widget.playlist['id']);
        if (mounted) {
          setState(() {
            _tracks = tracks;
            _isLoading = false;
          });
          
          // Reset scroll to top after loading data
          SchedulerBinding.instance.addPostFrameCallback((_) {
            _resetScrollToTop();
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to load playlist tracks';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreTracks() async {
    if (_isLoadingMore) return;

    if (widget.playlist['service'] == 'apple_music') {
      // Check if we've loaded all tracks
      if (_currentOffset >= _totalTrackCount) {
        return;
      }

      setState(() {
        _isLoadingMore = true;
      });

      try {
        print('🔍 Loading more Apple Music tracks: offset=$_currentOffset, limit=$_pageSize');
        final appleProvider = Provider.of<AppleMusicProvider>(context, listen: false);
        final moreTracks = await appleProvider.getPlaylistTracks(
          widget.playlist['id'],
          limit: _pageSize,
          offset: _currentOffset,
        );
        
        if (mounted) {
          setState(() {
            _tracks.addAll(moreTracks);
            _currentOffset += moreTracks.length;
            _isLoadingMore = false;
          });
          print('✅ Loaded ${moreTracks.length} more tracks. Total: ${_tracks.length}/$_totalTrackCount');
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
        }
        print('Error loading more Apple Music tracks: $e');
      }
    } else {
      // Spotify logic
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      final playlistId = widget.playlist['id'];
      
      // Check if we're already loading or if there are no more tracks
      if (spotifyProvider.isLoadingPlaylistTracks(playlistId) || 
          !spotifyProvider.hasMorePlaylistTracks(playlistId)) {
        return;
      }
      
      try {
        final tracks = await spotifyProvider.loadMorePlaylistTracks(playlistId);
        if (mounted) {
          setState(() {
            _tracks = tracks;
          });
        }
      } catch (e) {
        // Handle error silently for pagination
        print('Error loading more tracks: $e');
      }
    }
  }

  /// Shuffle the playlist tracks
  Future<void> _shufflePlaylist() async {
    if (_tracks.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No songs to shuffle')),
      );
      return;
    }

    try {
      // Show shuffle options dialog
      final shuffleMode = await _showShuffleOptionsDialog();
      if (shuffleMode == null) return; // User cancelled
      
      // Show loading state
      final loadingSnackBar = ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
              const SizedBox(width: 12),
              Text('Shuffling "${widget.playlist['name']}" playlist...'),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.primary,
          duration: const Duration(seconds: 10), // Longer duration for shuffle operation
        ),
      );
      
      // Convert tracks to pin format for the shuffle service
      final pins = _tracks.map((track) => {
        'id': track.id,
        'title': track.title,
        'artist': track.artist,
        'album': track.album,
        'imageUrl': track.albumArt,
        'track_url': track.url,
        'url': track.url,
        'duration': track.formattedDuration,
        'platform': track.service,
        'service': track.service,
        'uri': track.uri,
        'popularity': track.popularity,
      }).toList();
      
      // Get providers
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
      
      // Use the shuffle service
      final result = await ShuffleService.shuffleCollection(
        pins: pins,
        spotifyProvider: spotifyProvider,
        appleMusicProvider: appleMusicProvider,
        mode: shuffleMode,
        clearExistingQueue: true,
        maxTracks: 50, // Reasonable limit
      );
      
      // Clear loading snackbar
      ScaffoldMessenger.of(context).clearSnackBars();
      
      // Show result
      if (result.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.shuffle_rounded,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(child: Text(result.message)),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Theme.of(context).colorScheme.onError,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(child: Text(result.message)),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      // Clear any existing snackbars
      ScaffoldMessenger.of(context).clearSnackBars();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to shuffle playlist: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
  
  /// Show dialog to select shuffle mode
  Future<ShuffleMode?> _showShuffleOptionsDialog() async {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    
    return showModalBottomSheet<ShuffleMode>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.6,
          decoration: BoxDecoration(
            color: theme.scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: theme.dividerColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Header section
              Container(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
                child: Column(
                  children: [
                    // Icon and title
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: primaryColor.withOpacity(0.15),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.shuffle_rounded,
                            color: primaryColor,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Choose Shuffle Style',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                              Text(
                                '${_tracks.length} songs ready',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Options list
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    children: [
                      _buildShuffleOption(
                        title: 'Random Shuffle',
                        icon: Icons.casino_rounded,
                        gradient: LinearGradient(colors: [Colors.purple.shade400, Colors.purple.shade600]),
                        mode: ShuffleMode.random,
                        theme: theme,
                      ),
                      const SizedBox(height: 8),
                      
                      _buildShuffleOption(
                        title: 'Smart Shuffle',
                        icon: Icons.trending_up_rounded,
                        gradient: LinearGradient(colors: [Colors.orange.shade400, Colors.red.shade500]),
                        mode: ShuffleMode.weighted,
                        theme: theme,
                        isRecommended: true,
                      ),
                      const SizedBox(height: 8),
                      

                      
                      _buildShuffleOption(
                        title: 'Artist Spaced',
                        icon: Icons.people_outline_rounded,
                        gradient: LinearGradient(colors: [Colors.blue.shade400, Colors.indigo.shade500]),
                        mode: ShuffleMode.artistSpaced,
                        theme: theme,
                      ),
                    ],
                  ),
                ),
              ),
              
              // Cancel button
              Container(
                padding: const EdgeInsets.fromLTRB(24, 12, 24, 20),
                child: SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(color: theme.dividerColor),
                      ),
                    ),
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  /// Build a shuffle option tile
  Widget _buildShuffleOption({
    required String title,
    required IconData icon,
    required Gradient gradient,
    required ShuffleMode mode,
    required ThemeData theme,
    bool isRecommended = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => Navigator.of(context).pop(mode),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    gradient: gradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            title,
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          if (isRecommended) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.orange.shade100,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'RECOMMENDED',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange.shade700,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: theme.colorScheme.onSurface.withOpacity(0.4),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Reset scroll position only on the very first build
    if (_isFirstBuild && _scrollController.hasClients) {
      _scrollController.jumpTo(0);
      _isFirstBuild = false;
    }
    
    final playlistId = widget.playlist['id'];
    final isApple = widget.playlist['service'] == 'apple_music';
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
    final total = isApple
        ? _totalTrackCount
        : Provider.of<SpotifyProvider>(context).totalPlaylistTracks(playlistId);
    final isLoadingMore = isApple
        ? _isLoadingMore
        : Provider.of<SpotifyProvider>(context).isLoadingPlaylistTracks(playlistId);
    
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _error = null;
                  _isLoading = true;
                });
                _loadPlaylistTracks();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // Wrap in a ScrollConfiguration to isolate scroll behavior
    // and ensure this view always starts at the top
    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(
        scrollbars: true,
        overscroll: true,
      ),
      child: RefreshIndicator(
        onRefresh: () async {
          _loadPlaylistTracks();
          _loadPlaylistMetadata();
        },
        child: NotificationListener<ScrollNotification>(
          onNotification: (notification) {
            if (notification.metrics.pixels > notification.metrics.maxScrollExtent * 0.8) {
              _loadMoreTracks();
            }
            return false; // Allow normal scroll behavior
          },
          child: CustomScrollView(
            controller: _scrollController,
            slivers: [
              // Artwork banner at top
              SliverToBoxAdapter(
                child: widget.playlist['image_url'] != null && (widget.playlist['image_url'] as String).isNotEmpty
                    ? Image.network(
                        widget.playlist['image_url'],
                        width: double.infinity,
                        height: 200,
                        fit: BoxFit.cover,
                      )
                    : SizedBox(
                        height: 200,
                        child: GridView.count(
                          crossAxisCount: 2,
                          physics: const NeverScrollableScrollPhysics(),
                          children: _tracks.take(4).map((track) {
                            return Image.network(
                              track.albumArt,
                              fit: BoxFit.cover,
                            );
                          }).toList(),
                        ),
                      ),
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                    8.0, 
                    MediaQuery.of(context).size.height * 0.03, // 3% of screen height for responsive top padding
                    8.0, 
                    8.0
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back, size: 20),
                        onPressed: widget.onBack,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          widget.playlist['name'] ?? 'Untitled Playlist',
                          style: Theme.of(context).textTheme.titleMedium,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // Shuffle button
                      Container(
                        margin: const EdgeInsets.only(right: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          icon: Icon(
                            Icons.shuffle_rounded,
                            size: 20,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          onPressed: _tracks.isNotEmpty ? _shufflePlaylist : null,
                          tooltip: 'Shuffle playlist',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    if (index == _tracks.length) {
                      return const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Center(
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ),
                      );
                    }
                    final track = _tracks[index];
                    return TrackCard(
                      track: track,
                      isSmallScreen: widget.isSmallScreen,
                      onTap: () async {
                        if (isApple) {
                          try {
                            if (kDebugMode) {
                              print('🎵 [PlaylistDetailView] User tapped track: ${track.title} at index $index');
                              print('🎵 [PlaylistDetailView] Total tracks in playlist: ${_tracks.length}');
                              print('🎵 [PlaylistDetailView] Track at index $index: ${_tracks[index].title}');
                            }
                            
                            // Use playCollection to avoid re-fetching paginated tracks,
                            // which was causing playback to start from the beginning.
                            // This ensures the selected track is played correctly from the currently loaded list.
                            final success = await appleMusicProvider.playCollection(
                              tracks: _tracks,
                              collectionType: 'playlist',
                              startIndex: index,
                            );
                            if (!success && mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Failed to play "${track.title}"'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            } else if (mounted) {
                              if (kDebugMode) {
                                print('🎵 [PlaylistDetailView] ✅ Successfully initiated playback of: ${track.title}');
                              }
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Now playing: ${track.title} from playlist'),
                                  backgroundColor: Colors.green,
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            }
                          } catch (e) {
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Error playing track: $e'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        } else {
                          // For Spotify, play individual track for now
                          // TODO: Implement Spotify playlist playback
                          spotifyProvider.playTrack(track, context: context);
                        }
                      },
                    );
                  },
                  childCount: _tracks.length + (isLoadingMore ? 1 : 0),
                ),
              ),
              // Track count at bottom
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  child: Center(
                    child: Text(
                      '${_tracks.length} of $total tracks',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
} 