import 'package:flutter/material.dart';

class TrackCardSkeleton extends StatefulWidget {
  final bool isSmallScreen;

  const TrackCardSkeleton({
    Key? key,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  State<TrackCardSkeleton> createState() => _TrackCardSkeletonState();
}

class _TrackCardSkeletonState extends State<TrackCardSkeleton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double imageSize = widget.isSmallScreen ? 44 : 48;
    final double borderRadius = 8;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Material(
          color: Theme.of(context).brightness == Brightness.light
              ? Theme.of(context).scaffoldBackgroundColor
              : Theme.of(context).cardColor,
          child: SizedBox(
            height: widget.isSmallScreen ? 52 : 56,
            child: Row(
              children: [
                _buildAlbumArtSkeleton(context, imageSize),
                _buildTrackInfoSkeleton(context),
                _buildShareButtonSkeleton(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumArtSkeleton(BuildContext context, double imageSize) {
    return Padding(
      padding: const EdgeInsets.all(4),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return _buildShimmerContainer(
            context,
            width: imageSize,
            height: imageSize,
            borderRadius: 4,
          );
        },
      ),
    );
  }

  Widget _buildTrackInfoSkeleton(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return _buildShimmerContainer(
                  context,
                  width: MediaQuery.of(context).size.width * 0.5,
                  height: widget.isSmallScreen ? 13 : 14,
                  borderRadius: 6,
                );
              },
            ),
            const SizedBox(height: 6),
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return _buildShimmerContainer(
                  context,
                  width: MediaQuery.of(context).size.width * 0.35,
                  height: widget.isSmallScreen ? 11 : 12,
                  borderRadius: 6,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareButtonSkeleton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return _buildShimmerContainer(
            context,
            width: 24,
            height: 24,
            borderRadius: 12,
          );
        },
      ),
    );
  }

  Widget _buildShimmerContainer(
    BuildContext context, {
    required double width,
    required double height,
    required double borderRadius,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final baseColor = isDark
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFE0E0E0);
    final highlightColor = isDark
        ? const Color(0xFF3A3A3A)
        : const Color(0xFFF5F5F5);

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          begin: Alignment(-1.0 - _animation.value, 0.0),
          end: Alignment(1.0 - _animation.value, 0.0),
          colors: [
            baseColor,
            highlightColor,
            baseColor,
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
    );
  }
} 