import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../models/public_user_profile.dart';
import '../../../models/friendship_status.dart';
import '../../../models/user.dart';
import '../../../providers/public_profile_provider.dart';
import '../../../screens/friends/music_chat_screen.dart';
import '../../../screens/map/my_bop_map.dart';
import '../../bottomsheets/block_user_bottomsheet.dart';
import '../../bottomsheets/report_user_bottomsheet.dart';

class UserProfileActions extends StatelessWidget {
  final PublicUserProfile profile;

  const UserProfileActions({
    Key? key,
    required this.profile,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.06,
        vertical: 8,
      ),
      child: Column(
        children: [
          // Primary actions row - Message and Friend status
          Row(
            children: [
              // Message button - takes more space as primary action
              Expanded(
                flex: 3,
                child: _buildPrimaryActionButton(
                  context: context,
                  icon: Icons.chat_bubble_outline_rounded,
                  label: 'Message',
                  onTap: () => _openMusicChat(context),
                  theme: theme,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Friend action button
              Expanded(
                flex: 2,
                child: _buildFriendActionButton(context, theme),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Secondary actions row - smaller and simpler
          Row(
            children: [
              // Map button with globe icon
              Expanded(
                child: _buildSecondaryActionButton(
                  context: context,
                  icon: Icons.language_rounded, // Globe icon
                  label: 'Map',
                  onTap: () => _openMap(context),
                  theme: theme,
                ),
              ),
              
              const SizedBox(width: 8),
              
              // More actions button
              Expanded(
                child: _buildSecondaryActionButton(
                  context: context,
                  icon: Icons.more_horiz_rounded,
                  label: 'More',
                  onTap: () => _showMoreActions(context),
                  theme: theme,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrimaryActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required ThemeData theme,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.mediumImpact();
        onTap();
      },
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.primary.withOpacity(0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.primary.withOpacity(0.25),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 18,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecondaryActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required ThemeData theme,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: theme.colorScheme.primary.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 16,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFriendActionButton(BuildContext context, ThemeData theme) {
    return Consumer<PublicProfileProvider>(
      builder: (context, provider, child) {
        if (provider.isLoadingFriendshipStatus) {
          return Container(
            height: 48,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withOpacity(0.2),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 14,
                  height: 14,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  'Loading...',
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          );
        }

        return _buildFriendStatusButton(context, provider, theme);
      },
    );
  }

  Widget _buildFriendStatusButton(BuildContext context, PublicProfileProvider provider, ThemeData theme) {
    late IconData icon;
    late String label;
    late Color backgroundColor;
    late Color textColor;
    late Color borderColor;
    late VoidCallback onTap;

    switch (provider.friendshipStatus) {
      case FriendshipStatus.friends:
        icon = Icons.person_remove_rounded;
        label = 'Unfriend';
        backgroundColor = Colors.red.withOpacity(0.1);
        textColor = Colors.red;
        borderColor = Colors.red.withOpacity(0.3);
        onTap = () => _handleUnfriend(context, provider);
        break;
      
      case FriendshipStatus.pendingSent:
        icon = Icons.schedule_rounded;
        label = 'Pending';
        backgroundColor = Colors.orange.withOpacity(0.1);
        textColor = Colors.orange;
        borderColor = Colors.orange.withOpacity(0.3);
        onTap = () => _showPendingInfo(context);
        break;
      
      case FriendshipStatus.pendingReceived:
        icon = Icons.person_add_rounded;
        label = 'Accept';
        backgroundColor = Colors.green.withOpacity(0.1);
        textColor = Colors.green;
        borderColor = Colors.green.withOpacity(0.3);
        onTap = () => _handleAcceptFriend(context, provider);
        break;
      
      case FriendshipStatus.none:
      default:
        icon = Icons.person_add_rounded;
        label = 'Add Friend';
        backgroundColor = theme.colorScheme.primary.withOpacity(0.1);
        textColor = theme.colorScheme.primary;
        borderColor = theme.colorScheme.primary.withOpacity(0.3);
        onTap = () => _handleAddFriend(context, provider);
        break;
    }

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: borderColor, width: 1),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 16,
              color: textColor,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: textColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openMusicChat(BuildContext context) {
    // Create a User object from PublicUserProfile for MusicChatScreen
    final user = User(
      id: profile.id,
      username: profile.displayName, // Use display name instead of username
      email: '', // Not available in public profile
      profilePicUrl: profile.profilePicUrl,
      bio: profile.bio,
      location: profile.location,
      isVerified: false, // Remove verified status as requested
      favoriteGenres: [], // Not available in public profile
      connectedServices: {}, // Not available in public profile
      createdAt: DateTime.now(), // Not available in public profile
      lastActive: profile.lastActive,
    );
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MusicChatScreen(friend: user),
      ),
    );
  }

  void _openMap(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MyBOPMap(
          userId: profile.id.toString(),
          username: profile.displayName,
        ),
      ),
    );
  }

  void _showMoreActions(BuildContext context) {
    final theme = Theme.of(context);
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            
            Text(
              'More Actions',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            _buildMoreActionItem(
              context: context,
              icon: Icons.share_rounded,
              title: 'Share Profile',
              subtitle: 'Share ${profile.displayName}\'s profile',
              onTap: () => _shareProfile(context),
              theme: theme,
            ),
            
            _buildMoreActionItem(
              context: context,
              icon: Icons.block_rounded,
              title: 'Block User',
              subtitle: 'Block ${profile.displayName}',
              onTap: () => _blockUser(context),
              theme: theme,
              isDestructive: true,
            ),
            
            _buildMoreActionItem(
              context: context,
              icon: Icons.report_rounded,
              title: 'Report User',
              subtitle: 'Report inappropriate content',
              onTap: () => _reportUser(context),
              theme: theme,
              isDestructive: true,
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildMoreActionItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required ThemeData theme,
    bool isDestructive = false,
  }) {
    final color = isDestructive ? Colors.red : theme.colorScheme.onSurface;
    
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (isDestructive ? Colors.red : theme.colorScheme.primary).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: isDestructive ? Colors.red : theme.colorScheme.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: color.withOpacity(0.7),
          fontSize: 12,
        ),
      ),
      onTap: () {
        Navigator.of(context).pop();
        onTap();
      },
    );
  }

  void _shareProfile(BuildContext context) {
    // Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon')),
    );
  }

  void _blockUser(BuildContext context) {
    HapticFeedback.mediumImpact();
    
    // Show beautiful block bottom sheet
    BlockUserBottomSheet.show(
      context,
      userId: profile.id,
      username: profile.displayName,
      onBlocked: () {
        // Handle successful block - could update UI or navigate back
        print('User ${profile.displayName} has been blocked');
      },
    );
  }

  void _reportUser(BuildContext context) {
    HapticFeedback.mediumImpact();
    
    // Show beautiful report bottom sheet
    ReportUserBottomSheet.show(
      context,
      userId: profile.id,
      username: profile.displayName,
      onReported: () {
        // Handle successful report
        print('User ${profile.displayName} has been reported');
      },
    );
  }

  void _showPendingInfo(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Friend request is pending approval'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  Future<void> _handleUnfriend(BuildContext context, PublicProfileProvider provider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unfriend User'),
        content: Text('Are you sure you want to unfriend ${provider.currentProfile?.displayName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Unfriend'),
          ),
        ],
      ),
    );
    
    if (confirmed == true && context.mounted) {
      final success = await provider.unfriend(profile.id);
      if (success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('User unfriended')),
        );
      }
    }
  }

  Future<void> _handleAddFriend(BuildContext context, PublicProfileProvider provider) async {
    final success = await provider.sendFriendRequest(profile.id);
    if (success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Friend request sent')),
      );
    } else if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to send friend request')),
      );
    }
  }

  Future<void> _handleAcceptFriend(BuildContext context, PublicProfileProvider provider) async {
    final success = await provider.acceptFriendRequest();
    if (success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Friend request accepted')),
      );
    } else if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to accept friend request')),
      );
    }
  }
} 