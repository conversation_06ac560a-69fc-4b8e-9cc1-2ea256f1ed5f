import 'package:flutter/material.dart';
import '../../../models/public_user_profile.dart';

class UserProfileStats extends StatelessWidget {
  final PublicUserProfile profile;
  final Size size;
  final int bopDropsCount;

  const UserProfileStats({
    Key? key,
    required this.profile,
    required this.size,
    this.bopDropsCount = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = size.width;
    final isDark = theme.brightness == Brightness.dark;
    
    final stats = {
      'pins': '${profile.pinCount}',
      'bops': '$bopDropsCount',
      'collections': '${profile.publicCollectionCount}',
      'friends': '0', // Will be updated with real data
    };

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.06,
        vertical: 12,
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 16,
      ),
      decoration: BoxDecoration(
        color: isDark 
            ? Colors.white.withOpacity(0.05)
            : Colors.black.withOpacity(0.02),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark 
              ? Colors.white.withOpacity(0.1)
              : Colors.black.withOpacity(0.05),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.1 : 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: stats.entries.map((entry) {
          return Expanded(
            child: _buildStatItem(
              context,
              entry.value,
              _getStatLabel(entry.key),
              theme,
              screenWidth,
            ),
          );
        }).toList(),
      ),
    );
  }

  String _getStatLabel(String key) {
    switch (key) {
      case 'pins':
        return 'Pins';
      case 'bops':
        return 'Bops';
      case 'collections':
        return 'Collections';
      case 'friends':
        return 'Friends';
      default:
        return key.toUpperCase();
    }
  }

  Widget _buildStatItem(
    BuildContext context,
    String value,
    String label,
    ThemeData theme,
    double screenWidth,
  ) {
    final isDark = theme.brightness == Brightness.dark;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: screenWidth < 360 ? 16 : 18,
            fontWeight: FontWeight.w700,
            color: theme.colorScheme.primary,
            letterSpacing: 0.3,
            shadows: [
              Shadow(
                color: Colors.black.withOpacity(isDark ? 0.2 : 0.1),
                offset: const Offset(0, 1),
                blurRadius: 2,
              ),
            ],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: screenWidth < 360 ? 11 : 12,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface.withOpacity(isDark ? 0.7 : 0.6),
            letterSpacing: 0.2,
          ),
        ),
      ],
    );
  }
} 