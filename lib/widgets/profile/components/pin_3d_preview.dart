import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:model_viewer_plus/model_viewer_plus.dart';

class Pin3DPreview extends StatefulWidget {
  final String modelPath;
  final double initialScale;
  final bool autoRotate;
  final Color? backgroundColor;
  final bool isLocked;
  final VoidCallback? onTap;
  
  const Pin3DPreview({
    Key? key,
    required this.modelPath,
    this.initialScale = 1.0,
    this.autoRotate = false,
    this.backgroundColor,
    this.isLocked = false,
    this.onTap,
  }) : super(key: key);

  @override
  State<Pin3DPreview> createState() => _Pin3DPreviewState();
}

class _Pin3DPreviewState extends State<Pin3DPreview> with SingleTickerProviderStateMixin {
  late AnimationController _rotationController;
  double _rotationY = 0.0;
  
  @override
  void initState() {
    super.initState();
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..addListener(() {
      if (widget.autoRotate) {
        setState(() {
          _rotationY = _rotationController.value * 2 * math.pi;
        });
      }
    });
    
    if (widget.autoRotate) {
      _rotationController.repeat();
    }
  }
  
  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = widget.backgroundColor ?? theme.cardColor;
    
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(16),
        ),
        clipBehavior: Clip.antiAlias,
        child: Transform(
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001) // perspective
            ..rotateY(_rotationY),
          alignment: Alignment.center,
          child: Image.asset(
            widget.modelPath,
            fit: BoxFit.contain,
            color: widget.isLocked ? Colors.black45 : null,
            colorBlendMode: widget.isLocked ? BlendMode.darken : null,
          ),
        ),
      ),
    );
  }
} 