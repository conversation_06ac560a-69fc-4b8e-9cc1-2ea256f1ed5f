import 'package:flutter/material.dart';

class AlbumCard extends StatelessWidget {
  final Map<String, dynamic> album;
  final bool isSmallScreen;
  final VoidCallback? onTap;

  const AlbumCard({
    Key? key,
    required this.album,
    required this.isSmallScreen,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double imageSize = isSmallScreen ? 56 : 64;
    final double borderRadius = isSmallScreen ? 8 : 10;

    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 4 : 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Material(
          color: Theme.of(context).cardColor,
          child: InkWell(
            onTap: onTap,
            child: Row(
              children: [
                _buildAlbumImage(context, imageSize),
                _buildAlbumInfo(context),
                _buildTrackCount(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumImage(BuildContext context, double imageSize) {
    // Handle different image formats (Apple Music nested vs simple)
    String? imageUrl;
    if (album['attributes']?['artwork']?['url'] != null) {
      // Apple Music format
      imageUrl = album['attributes']['artwork']['url'];
    } else if (album['image_url'] != null && album['image_url'].toString().isNotEmpty) {
      // Simple format
      imageUrl = album['image_url'];
    } else if (album['images'] != null && (album['images'] as List).isNotEmpty) {
      // Spotify format
      imageUrl = album['images'][0]['url'];
    }
    
    return SizedBox(
      width: imageSize,
      height: imageSize,
      child: imageUrl != null
          ? Image.network(
              imageUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => _buildPlaceholder(context, imageSize),
            )
          : _buildPlaceholder(context, imageSize),
    );
  }

  Widget _buildPlaceholder(BuildContext context, double imageSize) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceVariant,
      child: Icon(
        Icons.album,
        size: imageSize * 0.4,
        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      ),
    );
  }

  Widget _buildAlbumInfo(BuildContext context) {
    // Handle different data formats
    final albumName = album['attributes']?['name'] ?? album['name'] ?? 'Untitled Album';
    final artistName = album['attributes']?['artistName'] ?? album['artist'] ?? 'Unknown Artist';
    
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              albumName,
              style: TextStyle(
                fontSize: isSmallScreen ? 13 : 14,
                fontWeight: FontWeight.w600,
                letterSpacing: -0.2,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              'By $artistName',
              style: TextStyle(
                fontSize: isSmallScreen ? 11 : 12,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                letterSpacing: -0.1,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrackCount(BuildContext context) {
    // Handle different track count formats
    final trackCount = album['attributes']?['trackCount'] ?? 
                      album['track_count'] ?? 
                      album['trackCount'] ?? 
                      album['total_tracks'] ?? 0;
    
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 8 : 12,
        vertical: 4,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '$trackCount',
            style: TextStyle(
              fontSize: isSmallScreen ? 12 : 13,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          Text(
            trackCount == 1 ? 'track' : 'tracks',
            style: TextStyle(
              fontSize: isSmallScreen ? 10 : 11,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }
} 