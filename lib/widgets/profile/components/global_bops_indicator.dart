import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class GlobalBopsIndicator extends StatefulWidget {
  final VoidCallback onTap;
  final bool isActive;
  
  const GlobalBopsIndicator({
    Key? key,
    required this.onTap,
    this.isActive = false,
  }) : super(key: key);

  @override
  State<GlobalBopsIndicator> createState() => _GlobalBopsIndicatorState();
}

class _GlobalBopsIndicatorState extends State<GlobalBopsIndicator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isHovering = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
      reverseCurve: Curves.easeInCubic,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _controller.reverse();
  }

  void _handleTapCancel() {
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        onTap: () {
          HapticFeedback.lightImpact();
          widget.onTap();
        },
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                height: isSmallScreen ? 34 : 38,
                constraints: BoxConstraints(
                  maxWidth: isSmallScreen ? 120 : 140,
                  minWidth: 0,  // Allow shrinking
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 10 : 12,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 17 : 19),
                  color: theme.scaffoldBackgroundColor.withOpacity(isDark ? 0.75 : 0.9),
                  border: Border.all(
                    color: widget.isActive || _isHovering
                        ? theme.colorScheme.primary.withOpacity(isDark ? 0.3 : 0.2)
                        : Colors.white.withOpacity(isDark ? 0.1 : 0.05),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: theme.colorScheme.primary.withOpacity(isDark ? 0.15 : 0.08),
                      blurRadius: 10,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 17 : 19),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.public_rounded,
                          size: isSmallScreen ? 15 : 17,
                          color: (_isHovering || widget.isActive)
                              ? theme.colorScheme.primary
                              : theme.colorScheme.primary.withOpacity(isDark ? 0.9 : 0.8),
                        ),
                        SizedBox(width: isSmallScreen ? 5 : 7),
                        Flexible(
                          child: Text(
                            'My Bop Map',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 12 : 13,
                              fontWeight: FontWeight.w600,
                              color: (_isHovering || widget.isActive)
                                  ? theme.colorScheme.onSurface
                                  : theme.colorScheme.onSurface.withOpacity(isDark ? 0.9 : 0.85),
                              letterSpacing: 0.2,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: isSmallScreen ? 5 : 7),
                        Icon(
                          Icons.share_rounded,
                          size: isSmallScreen ? 13 : 15,
                          color: (_isHovering || widget.isActive)
                              ? theme.colorScheme.secondary
                              : theme.colorScheme.secondary.withOpacity(isDark ? 0.9 : 0.8),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
} 