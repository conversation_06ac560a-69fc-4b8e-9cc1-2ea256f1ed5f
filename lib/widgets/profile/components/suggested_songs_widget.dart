import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:lottie/lottie.dart';
import '../../../providers/apple_music_provider.dart';
import '../../../providers/spotify_provider.dart';
import '../../../models/music_track.dart';

import '../../../services/ai/global_ai_provider_service.dart';
import 'track_card.dart';
import 'track_card_skeleton.dart';
import 'dart:async';

/// Shared widget for suggested songs functionality
/// Uses the global AI provider for consistent state across the app
class SuggestedSongsWidget extends StatefulWidget {
  final String currentFilter;
  final VoidCallback? onRefresh;
  final Function(MusicTrack)? onTrackPlay;
  final bool isSmallScreen;
  
  const SuggestedSongsWidget({
    Key? key,
    required this.currentFilter,
    this.onRefresh,
    this.onTrackPlay,
    this.isSmallScreen = false,
  }) : super(key: key);

  @override
  State<SuggestedSongsWidget> createState() => _SuggestedSongsWidgetState();
}

class _SuggestedSongsWidgetState extends State<SuggestedSongsWidget> 
    with AutomaticKeepAliveClientMixin {
  
  // Use global AI provider - no more static instances
  List<MusicTrack> _suggestedSongs = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String? _errorMessage;
  DateTime? _lastLoadTime;
  static const Duration _cacheExpiry = Duration(hours: 1);
  
  // Scroll controller for pagination
  final ScrollController _scrollController = ScrollController();

  // Timer for periodic sync with global provider
  Timer? _syncTimer;
  
  @override
  bool get wantKeepAlive => true;
  
  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Set initial loading state
    _isLoading = true;

    // Initialize immediately after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeIfNeeded();
    });

    // Start periodic sync timer to check for global provider updates
    _startSyncTimer();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // If we don't have data and we're not loading, try to initialize
    if (_suggestedSongs.isEmpty && !_isLoading && !_isLoadingMore) {
      if (kDebugMode) {
        print('🔄 didChangeDependencies: Attempting to initialize...');
      }
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeIfNeeded();
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _syncTimer?.cancel();
    super.dispose();
  }

  /// Start periodic timer to sync with global provider
  void _startSyncTimer() {
    _syncTimer?.cancel(); // Cancel any existing timer
    _syncTimer = Timer.periodic(const Duration(milliseconds: 1000), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      // Try to sync with global provider if we don't have data
      if (_suggestedSongs.isEmpty && !_isLoading && !_isLoadingMore) {
        if (kDebugMode) {
          print('🔄 Timer: Attempting to sync with global provider...');
        }
        _tryGlobalProviderSync();
      }

      // Cancel timer after we have data or after 30 seconds
      if (_suggestedSongs.isNotEmpty || timer.tick > 30) {
        if (kDebugMode) {
          print('⏹️ Stopping sync timer - hasData: ${_suggestedSongs.isNotEmpty}, tick: ${timer.tick}');
        }
        timer.cancel();
      }
    });
  }

  /// Try to sync with global AI provider without loading (fast path)
  void _tryGlobalProviderSync() {
    final globalAIProvider = GlobalAIProviderService.instance;
    final aiProvider = globalAIProvider.aiProvider;

    // Check if global provider has artist-based data available
    if (aiProvider != null &&
        aiProvider.currentRecommendations.isNotEmpty &&
        aiProvider.currentCategory == 'artistBased' &&
        !aiProvider.isLoading) {

      if (kDebugMode) {
        print('🔄 Syncing suggested songs from global provider (${aiProvider.currentRecommendations.length} tracks)');
      }

      // Use the already loaded data from global provider
      if (mounted) {
        setState(() {
          _suggestedSongs = List<MusicTrack>.from(aiProvider.currentRecommendations);
          _lastLoadTime = DateTime.now();
          _isLoading = false;
          _errorMessage = null;
        });
      }
    }
  }
  
  /// Load suggestions using the global AI provider
  Future<void> _initializeIfNeeded() async {
    if (kDebugMode) {
      print('🎯 _initializeIfNeeded called - _isLoading: $_isLoading, _suggestedSongs.length: ${_suggestedSongs.length}');
    }

    // Check if cache is still valid
    if (_suggestedSongs.isNotEmpty &&
        _lastLoadTime != null &&
        DateTime.now().difference(_lastLoadTime!) < _cacheExpiry) {
      if (kDebugMode) {
        print('📦 Using cached suggested songs (${_suggestedSongs.length} tracks)');
      }
      // Still set loading to false if we have cached data
      if (mounted && _isLoading) {
        setState(() {
          _isLoading = false;
        });
      }
      return;
    }

    if (!mounted) return;

    // Don't start new loading if we already have data and are loading more
    if (_isLoading && _suggestedSongs.isNotEmpty) {
      if (kDebugMode) {
        print('⚠️ Already loading with data, skipping initialization');
      }
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      if (kDebugMode) {
        print('🎯 Initializing suggested songs widget...');
      }

      // First try to sync with existing global provider data
      if (_tryGlobalProviderSyncImmediate()) {
        if (kDebugMode) {
          print('✅ Synced with existing global provider data immediately');
        }
        return;
      }

      // Use the getProvider method which will automatically initialize if needed
      final globalAIProvider = GlobalAIProviderService.instance;
      final provider = await globalAIProvider.getProvider(context);

      if (provider == null) {
        if (kDebugMode) {
          print('⚠️ Global AI provider not available after initialization attempt');
        }
        throw Exception('AI provider not available. Please check your connection.');
      }

      // Ensure we're using artist-based recommendations
      if (provider.currentCategory != 'artistBased') {
        if (kDebugMode) {
          print('🔄 Switching to artist-based recommendations...');
        }
        if (mounted) {
          provider.onCategoryChanged('artistBased', context);
        }
        // Wait for category change to complete
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Wait for recommendations to load if they're currently loading
      // Check both the provider's loading state and if it has data
      int attempts = 0;
      const maxAttempts = 30; // Increased to give more time

      while (attempts < maxAttempts && mounted) {
        if (kDebugMode) {
          print('🔄 Waiting for AI provider... attempt ${attempts + 1}/$maxAttempts (isLoading: ${provider.isLoading}, hasData: ${provider.currentRecommendations.isNotEmpty})');
        }

        // Break if we have data and provider is not loading
        if (provider.currentRecommendations.isNotEmpty && !provider.isLoading) {
          break;
        }

        // Break if provider is not loading and we've waited enough
        if (!provider.isLoading && attempts > 10) {
          if (kDebugMode) {
            print('⚠️ Provider not loading and no data after ${attempts + 1} attempts');
          }
          break;
        }

        await Future.delayed(const Duration(milliseconds: 300));
        attempts++;
      }

      if (mounted) {
        final recommendations = List<MusicTrack>.from(provider.currentRecommendations);

        setState(() {
          _suggestedSongs = recommendations;
          _isLoading = false;
          _lastLoadTime = DateTime.now();

          // Clear error if we got data
          if (recommendations.isNotEmpty) {
            _errorMessage = null;
          }
        });

        if (kDebugMode) {
          print('✅ Suggested songs loaded from global provider with ${_suggestedSongs.length} tracks');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading suggested songs: $e');
      }
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load suggestions. Please try again.';
          _isLoading = false;
          _isLoadingMore = false;
        });
      }
    }
  }

  /// Try to sync with global AI provider immediately (no async)
  bool _tryGlobalProviderSyncImmediate() {
    final globalAIProvider = GlobalAIProviderService.instance;
    final aiProvider = globalAIProvider.aiProvider;

    // Check if global provider has artist-based data available
    if (aiProvider != null &&
        aiProvider.currentRecommendations.isNotEmpty &&
        aiProvider.currentCategory == 'artistBased' &&
        !aiProvider.isLoading) {

      if (kDebugMode) {
        print('🔄 Syncing suggested songs from global provider immediately (${aiProvider.currentRecommendations.length} tracks)');
      }

      // Use the already loaded data from global provider
      if (mounted) {
        setState(() {
          _suggestedSongs = List<MusicTrack>.from(aiProvider.currentRecommendations);
          _lastLoadTime = DateTime.now();
          _isLoading = false;
          _errorMessage = null;
        });
        return true;
      }
    }

    return false;
  }
  
  /// Handle scroll for pagination
  void _onScroll() {
    // Trigger load more at 60% scroll height and prevent multiple requests
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.6 &&
        !_isLoading && !_isLoadingMore) {
      _loadMoreSuggestions();
    }
  }
  
  /// Load more suggestions using pagination
  Future<void> _loadMoreSuggestions() async {
    if (!mounted || _isLoadingMore) return;

    final globalAIProvider = GlobalAIProviderService.instance;
    final provider = await globalAIProvider.getProvider(context);
    if (provider == null || _isLoading || !mounted) return;

    try {
      if (kDebugMode) {
        print('🔄 Loading more suggested songs...');
      }

      setState(() {
        _isLoadingMore = true;
      });

      // Trigger load more on the global AI provider
      // Check mounted again before using context
      if (mounted) {
        await provider.loadMoreRecommendations(context);
      }

      // Update our local state with new recommendations
      if (mounted) {
        setState(() {
          _suggestedSongs = List<MusicTrack>.from(provider.currentRecommendations);
          _isLoadingMore = false;
          _lastLoadTime = DateTime.now();
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading more suggestions: $e');
      }
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }
  
  /// Shuffle and play all suggested songs
  Future<void> _shuffleSuggestedSongs() async {
    if (_suggestedSongs.isEmpty && !_isLoading) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No suggested songs to shuffle')),
      );
      return;
    }

    try {
      final appleProvider = Provider.of<AppleMusicProvider>(context, listen: false);
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

      // Create a shuffled copy of the suggested songs
      final shuffledTracks = List<MusicTrack>.from(_suggestedSongs);
      shuffledTracks.shuffle();

      bool success = false;

      if (appleProvider.isConnected) {
        // For Apple Music, set up queue with shuffle enabled
        const startIndex = 0; // Always start from first track when shuffling

        success = await appleProvider.playCollection(
          tracks: shuffledTracks,
          collectionType: 'suggested_songs',
          startIndex: startIndex,
          collectionMetadata: {
            'name': 'Suggested Songs',
            'shuffled': true,
          },
        );

        if (success && kDebugMode) {
          print('✅ Apple Music queue set up with ${_suggestedSongs.length} suggested songs');
        }
      } else if (spotifyProvider.isConnected) {
        // For Spotify, use the original callback if provided
        if (widget.onTrackPlay != null && shuffledTracks.isNotEmpty) {
          widget.onTrackPlay!(shuffledTracks.first);
          success = true;
        }
      }

      if (success) {
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Shuffling ${shuffledTracks.length} suggested songs'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to start shuffle playback'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error shuffling suggested songs: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error starting shuffle playback'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Clear data (useful for logout or refresh)
  void clearData() {
    if (mounted) {
      setState(() {
        _suggestedSongs.clear();
        _isLoading = false;
        _isLoadingMore = false;
        _errorMessage = null;
        _lastLoadTime = null;
      });
      
      if (kDebugMode) {
        print('🗑️ Cleared suggested songs data');
      }
    }
  }
  
  /// Shuffle and get new songs randomly from AI provider cache
  Future<void> refreshData() async {
    if (!mounted) return;

    if (kDebugMode) {
      print('🎲 Starting suggested songs shuffle from cache...');
    }

    setState(() {
      _isLoading = true;
      _isLoadingMore = false; // Reset load more flag when refreshing
      _errorMessage = null;
    });

    try {
      // Use the getProvider method which will automatically initialize if needed
      final globalAIProvider = GlobalAIProviderService.instance;
      final provider = await globalAIProvider.getProvider(context);

      if (provider == null) {
        if (kDebugMode) {
          print('⚠️ Global AI provider not available for shuffle');
        }
        throw Exception('AI provider not available. Please check your connection.');
      }

      // Ensure we're on artist-based category for suggested songs
      if (provider.currentCategory != 'artistBased') {
        if (kDebugMode) {
          print('🔄 Switching to artist-based for shuffle...');
        }
        if (mounted) {
          provider.onCategoryChanged('artistBased', context);
          // Wait for category change to complete
          await Future.delayed(const Duration(milliseconds: 300));
        }
      }

      // Get all cached recommendations from the provider
      final allCachedTracks = List<MusicTrack>.from(provider.currentRecommendations);

      if (allCachedTracks.isEmpty) {
        if (kDebugMode) {
          print('⚠️ No cached tracks available, initializing...');
        }
        // If no cached data, initialize first
        await _initializeIfNeeded();
        return;
      }

      // Shuffle the cached tracks to get a random selection
      allCachedTracks.shuffle();

      // Take a fresh random selection (up to 50 tracks for variety)
      final shuffledSelection = allCachedTracks.take(50).toList();

      if (mounted) {
        setState(() {
          _suggestedSongs = shuffledSelection;
          _isLoading = false;
          _lastLoadTime = DateTime.now(); // Update cache time
        });

        if (kDebugMode) {
          print('✅ Suggested songs shuffled with ${_suggestedSongs.length} tracks from cache (${allCachedTracks.length} total available)');
        }

        // Show success feedback
        if (mounted && _suggestedSongs.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Shuffled ${_suggestedSongs.length} fresh suggestions!'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error shuffling suggested songs: $e');
      }
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to shuffle suggestions. Please try again.';
          _isLoading = false;
          _isLoadingMore = false;
        });

        // Show error feedback
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to shuffle suggestions. Please try again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    super.build(context);

    // Check if global AI provider is still initializing
    final globalAIProvider = GlobalAIProviderService.instance;
    final isGlobalProviderInitializing = globalAIProvider.isInitializing;
    final aiProvider = globalAIProvider.aiProvider;
    final isAIProviderLoading = aiProvider?.isLoading ?? false;

    // ALWAYS show loading state by default when we have no data
    // Only show empty/error states when we've explicitly determined there's no data available
    if (_suggestedSongs.isEmpty) {
      // Show error state only if we have an explicit error message
      if (_errorMessage != null) {
        if (kDebugMode) {
          print('❌ Showing error state: $_errorMessage');
        }
        return _buildErrorState();
      }

      // Show loading state by default when no data (this is the key fix)
      if (kDebugMode) {
        print('Showing loading state (default for no data)');
      }
      return _buildLoadingState();
    }

    // Show content when we have data
    if (kDebugMode) {
      print('✅ Showing content with ${_suggestedSongs.length} songs');
    }
    return _buildContent();
  }
  
  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      itemCount: 10,
      itemBuilder: (context, index) {
        return TrackCardSkeleton(isSmallScreen: widget.isSmallScreen);
      },
    );
  }
  
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red.withValues(alpha: 0.8),
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading suggestions',
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'Unknown error',
            style: TextStyle(
              color: Colors.grey.withValues(alpha: 0.7),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: refreshData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.auto_awesome,
            size: 48,
            color: Colors.grey.withValues(alpha: 0.6),
          ),
          const SizedBox(height: 16),
          Text(
            'No suggestions available',
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Try listening to more music to get personalized recommendations!',
            style: TextStyle(
              color: Colors.grey.withValues(alpha: 0.7),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildContent() {
    return RefreshIndicator(
      onRefresh: refreshData,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        itemCount: _suggestedSongs.length + 1 + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          // Add shuffle header at the top
          if (index == 0) {
            return _buildShuffleHeader();
          }
          
          // Adjust index for header
          final dataIndex = index - 1;
          
          // Show loading indicator at the end if loading more
          if (dataIndex == _suggestedSongs.length) {
            if (_isLoadingMore) {
              return Padding(
                padding: const EdgeInsets.all(16.0),
                child: Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }
          
          // Show track card
          final track = _suggestedSongs[dataIndex];
          return TrackCard(
            track: track,
            isSmallScreen: widget.isSmallScreen,
            onTap: () async {
              // Use Apple Music queue manager for individual track playback
              final appleProvider = Provider.of<AppleMusicProvider>(context, listen: false);
              final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

              if (appleProvider.isConnected) {
                // For Apple Music, set up queue with all suggested songs starting from selected track
                final trackIndex = _suggestedSongs.indexOf(track);
                if (trackIndex != -1) {
                  final queueManager = appleProvider.queueManager;
                  await queueManager.setQueue(
                    tracks: _suggestedSongs,
                    collectionType: 'suggested_songs',
                    startIndex: trackIndex,
                    collectionMetadata: {
                      'name': 'Suggested Songs',
                      'shuffled': false,
                    },
                  );
                }
              } else if (spotifyProvider.isConnected && widget.onTrackPlay != null) {
                // Fallback to original callback for Spotify
                widget.onTrackPlay!(track);
              }
            },
          );
        },
      ),
    );
  }
  
  Widget _buildShuffleHeader() {
    return Builder(
      builder: (context) => Container(
        margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        children: [
            // Add Lottie animation to the left of the title
            SizedBox(
              width: 30,
              height: 30,
              child: Lottie.asset(
                'assets/anim/ai_search.json',
                fit: BoxFit.contain,
                repeat: true,
                animate: true,
              ),
          ),
            const SizedBox(width: 8),
          Expanded(
              child: Text(
                'BOP AI Curated',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
            ),
            // Refresh/Shuffle list button (dice icon as per user preference)
            Container(
              margin: const EdgeInsets.only(left: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.casino, // Dice icon as preferred by user
                  size: 18,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                onPressed: refreshData,
                tooltip: 'Shuffle suggestions from cache',
              ),
            ),
            const SizedBox(width: 4),
            // Play shuffle button
            Container(
              margin: const EdgeInsets.only(left: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.shuffle_rounded,
                  size: 18,
                  color: Theme.of(context).colorScheme.primary,
                ),
                onPressed: _shuffleSuggestedSongs,
                tooltip: 'Shuffle and play all songs',
              ),
            ),
        ],
      ),
    ));
  }
}
