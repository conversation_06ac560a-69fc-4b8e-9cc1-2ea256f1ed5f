import 'package:flutter/material.dart';

class ArtistCardSkeleton extends StatefulWidget {
  final bool isSmallScreen;

  const ArtistCardSkeleton({
    Key? key,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  State<ArtistCardSkeleton> createState() => _ArtistCardSkeletonState();
}

class _ArtistCardSkeletonState extends State<ArtistCardSkeleton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double imageSize = widget.isSmallScreen ? 40 : 48;
    final double borderRadius = widget.isSmallScreen ? 8 : 10;

    return Container(
      margin: EdgeInsets.only(bottom: widget.isSmallScreen ? 4 : 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Material(
          color: Theme.of(context).cardColor,
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: widget.isSmallScreen ? 8 : 12,
              vertical: widget.isSmallScreen ? 6 : 8,
            ),
            child: Row(
              children: [
                _buildArtistImageSkeleton(context, imageSize),
                const SizedBox(width: 12),
                _buildArtistInfoSkeleton(context),
                _buildPopularityIndicatorSkeleton(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildArtistImageSkeleton(BuildContext context, double imageSize) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return _buildShimmerContainer(
          context,
          width: imageSize,
          height: imageSize,
          borderRadius: imageSize / 2,
        );
      },
    );
  }

  Widget _buildArtistInfoSkeleton(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return _buildShimmerContainer(
                context,
                width: MediaQuery.of(context).size.width * 0.4,
                height: widget.isSmallScreen ? 13 : 14,
                borderRadius: 6,
              );
            },
          ),
          const SizedBox(height: 4),
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return _buildShimmerContainer(
                context,
                width: MediaQuery.of(context).size.width * 0.25,
                height: widget.isSmallScreen ? 11 : 12,
                borderRadius: 6,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPopularityIndicatorSkeleton(BuildContext context) {
    final size = widget.isSmallScreen ? 32.0 : 36.0;
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return _buildShimmerContainer(
          context,
          width: size,
          height: size,
          borderRadius: size / 2,
        );
      },
    );
  }

  Widget _buildShimmerContainer(
    BuildContext context, {
    required double width,
    required double height,
    required double borderRadius,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final baseColor = isDark
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFE0E0E0);
    final highlightColor = isDark
        ? const Color(0xFF3A3A3A)
        : const Color(0xFFF5F5F5);

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          begin: Alignment(-1.0 - _animation.value, 0.0),
          end: Alignment(1.0 - _animation.value, 0.0),
          colors: [
            baseColor,
            highlightColor,
            baseColor,
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
    );
  }
} 