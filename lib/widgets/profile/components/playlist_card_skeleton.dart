import 'package:flutter/material.dart';

class PlaylistCardSkeleton extends StatefulWidget {
  final bool isSmallScreen;

  const PlaylistCardSkeleton({
    Key? key,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  State<PlaylistCardSkeleton> createState() => _PlaylistCardSkeletonState();
}

class _PlaylistCardSkeletonState extends State<PlaylistCardSkeleton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double imageSize = widget.isSmallScreen ? 56 : 64;
    final double borderRadius = widget.isSmallScreen ? 8 : 10;

    return Container(
      margin: EdgeInsets.only(bottom: widget.isSmallScreen ? 4 : 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Material(
          color: Theme.of(context).cardColor,
          child: Row(
            children: [
              _buildPlaylistImageSkeleton(context, imageSize),
              _buildPlaylistInfoSkeleton(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaylistImageSkeleton(BuildContext context, double imageSize) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return _buildShimmerContainer(
          context,
          width: imageSize,
          height: imageSize,
          borderRadius: 0,
        );
      },
    );
  }

  Widget _buildPlaylistInfoSkeleton(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(widget.isSmallScreen ? 6 : 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return _buildShimmerContainer(
                  context,
                  width: MediaQuery.of(context).size.width * 0.5,
                  height: widget.isSmallScreen ? 13 : 14,
                  borderRadius: 6,
                );
              },
            ),
            const SizedBox(height: 4),
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return _buildShimmerContainer(
                  context,
                  width: MediaQuery.of(context).size.width * 0.3,
                  height: widget.isSmallScreen ? 11 : 12,
                  borderRadius: 6,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerContainer(
    BuildContext context, {
    required double width,
    required double height,
    required double borderRadius,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final baseColor = isDark
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFE0E0E0);
    final highlightColor = isDark
        ? const Color(0xFF3A3A3A)
        : const Color(0xFFF5F5F5);

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          begin: Alignment(-1.0 - _animation.value, 0.0),
          end: Alignment(1.0 - _animation.value, 0.0),
          colors: [
            baseColor,
            highlightColor,
            baseColor,
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
    );
  }
} 