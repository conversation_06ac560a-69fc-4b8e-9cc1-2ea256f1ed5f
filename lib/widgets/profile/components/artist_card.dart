import 'package:flutter/material.dart';

class ArtistCard extends StatelessWidget {
  final Map<String, dynamic> artist;
  final bool isSmallScreen;

  const ArtistCard({
    Key? key,
    required this.artist,
    required this.isSmallScreen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double imageSize = isSmallScreen ? 40 : 48;
    final double borderRadius = isSmallScreen ? 8 : 10;

    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 4 : 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Material(
          color: Theme.of(context).cardColor,
          child: InkWell(
            onTap: () {
              // Navigate to artist page or open in Spotify
              if (artist['url'] != null) {
                // Open URL
              }
            },
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 8 : 12,
                vertical: isSmallScreen ? 6 : 8,
              ),
              child: Row(
                children: [
                  _buildArtistImage(context, imageSize),
                  const SizedBox(width: 12),
                  _buildArtistInfo(context),
                  _buildPopularityIndicator(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildArtistImage(BuildContext context, double imageSize) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(imageSize / 2),
      child: SizedBox(
        width: imageSize,
        height: imageSize,
        child: artist['image_url'] != null
            ? Image.network(
                artist['image_url'],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildPlaceholder(context, imageSize),
              )
            : _buildPlaceholder(context, imageSize),
      ),
    );
  }

  Widget _buildPlaceholder(BuildContext context, double imageSize) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceVariant,
      child: Icon(
        Icons.person,
        size: imageSize * 0.5,
        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      ),
    );
  }

  Widget _buildArtistInfo(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            artist['name'] ?? 'Unknown Artist',
            style: TextStyle(
              fontSize: isSmallScreen ? 13 : 14,
              fontWeight: FontWeight.w500,
              letterSpacing: -0.2,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (artist['genres'] != null && (artist['genres'] as List).isNotEmpty) ...[
            const SizedBox(height: 2),
            Text(
              (artist['genres'] as List).take(2).join(', '),
              style: TextStyle(
                fontSize: isSmallScreen ? 11 : 12,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                letterSpacing: -0.1,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPopularityIndicator(BuildContext context) {
    final popularity = artist['popularity'] ?? 0;
    final color = popularity >= 80 
        ? Colors.green 
        : popularity >= 60 
            ? Colors.blue 
            : popularity >= 40 
                ? Colors.orange 
                : Colors.red;

    return Container(
      width: isSmallScreen ? 32 : 36,
      height: isSmallScreen ? 32 : 36,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          '$popularity',
          style: TextStyle(
            fontSize: isSmallScreen ? 12 : 13,
            fontWeight: FontWeight.w500,
            color: color,
          ),
        ),
      ),
    );
  }
}