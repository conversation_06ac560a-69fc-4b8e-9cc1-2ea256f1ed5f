import 'package:flutter/material.dart';

class MusicServiceIcon extends StatelessWidget {
  final String service;

  const MusicServiceIcon({
    Key? key,
    required this.service,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    switch (service.toLowerCase()) {
      case 'spotify':
        return Icon(
          Icons.music_note,
          size: 14,
          color: const Color(0xFF1DB954), // Spotify green
        );
      case 'apple':
        return Icon(
          Icons.music_note,
          size: 14,
          color: const Color(0xFFFA243C), // Apple Music red
        );
      case 'soundcloud':
        return Icon(
          Icons.cloud,
          size: 14,
          color: const Color(0xFFFF7700), // SoundCloud orange
        );
      default:
        return const Icon(
          Icons.music_note,
          size: 14,
          color: Colors.white,
        );
    }
  }
} 