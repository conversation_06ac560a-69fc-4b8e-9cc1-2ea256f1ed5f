import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:latlong2/latlong.dart';

import '../providers/indoor_scan_provider.dart';
import '../widgets/scan_details_view.dart';
import '../utils/scan_map_integration.dart';
import '../models/indoor_scan_result.dart';

/// Screen that displays detailed information about a specific indoor scan
class ScanDetailsScreen extends StatelessWidget {
  static const String routeName = '/indoor-scan/details';

  const ScanDetailsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<IndoorScanProvider>(
      builder: (context, provider, child) {
        final scan = provider.currentScan;
        
        if (scan == null) {
          // If no scan is selected, navigate back
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pop();
          });
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
        
        final dateFormat = DateFormat('MMMM d, yyyy - h:mm a');
        
        return Scaffold(
          appBar: AppBar(
            title: Text(scan.scanName),
            actions: [
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Sharing functionality coming soon'),
                    ),
                  );
                },
                tooltip: 'Share Scan',
              ),
            ],
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header information card
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Scan Information',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const Divider(),
                          Text('Date Scanned: ${dateFormat.format(scan.scanDate)}'),
                          const SizedBox(height: 8),
                          Text('Dimensions: ${scan.dimensions.width.toStringAsFixed(1)}m × ${scan.dimensions.height.toStringAsFixed(1)}m'),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildStatColumn(context, 'Walls', scan.metadata['wallCount'] ?? 0),
                              _buildStatColumn(context, 'Doors', scan.metadata['doorCount'] ?? 0),
                              _buildStatColumn(context, 'Windows', scan.metadata['windowCount'] ?? 0),
                              _buildStatColumn(context, 'Objects', scan.metadata['objectCount'] ?? 0),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // 3D Preview
                  Text(
                    '3D Preview',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Card(
                    elevation: 2,
                    child: Container(
                      height: 250,
                      width: double.infinity,
                      padding: const EdgeInsets.all(8.0),
                      child: ScanDetailsView(scan: scan),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Map Integration
                  Text(
                    'Map Integration',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Position this indoor scan on your map by selecting a location:',
                          ),
                          const SizedBox(height: 16),
                          Container(
                            height: 150,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.map_outlined,
                                    size: 48,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Map Preview',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Tap to select location',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  icon: const Icon(Icons.location_on),
                                  label: const Text('Select Location on Map'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Theme.of(context).primaryColor,
                                    foregroundColor: Colors.white,
                                  ),
                                  onPressed: () {
                                    _showLocationPicker(context, scan);
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          icon: const Icon(Icons.file_download),
                          label: const Text('Export Scan'),
                          onPressed: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Export feature coming soon!')),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          icon: const Icon(Icons.add_location),
                          label: const Text('Add to Map'),
                          onPressed: () {
                            _showAddToMapDialog(context, scan);
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatColumn(BuildContext context, String label, int value) {
    return Column(
      children: [
        Text(
          value.toString(),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
  
  void _showLocationPicker(BuildContext context, IndoorScanResult scan) {
    // In a real implementation, this would open a map for the user to select a location
    // For now, we'll simulate this with a simple dialog that has some predefined locations
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Location'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose a location to place your indoor scan:'),
            const SizedBox(height: 16),
            // Some example locations - in a real app, this would be a map widget
            ListTile(
              leading: const Icon(Icons.location_on),
              title: const Text('Current Location'),
              subtitle: const Text('37.7749, -122.4194'),
              onTap: () {
                Navigator.pop(context);
                _addScanToMap(context, scan, const LatLng(37.7749, -122.4194));
              },
            ),
            ListTile(
              leading: const Icon(Icons.business),
              title: const Text('Office Building'),
              subtitle: const Text('37.7833, -122.4167'),
              onTap: () {
                Navigator.pop(context);
                _addScanToMap(context, scan, const LatLng(37.7833, -122.4167));
              },
            ),
            ListTile(
              leading: const Icon(Icons.home),
              title: const Text('Home'),
              subtitle: const Text('37.7699, -122.4260'),
              onTap: () {
                Navigator.pop(context);
                _addScanToMap(context, scan, const LatLng(37.7699, -122.4260));
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
        ],
      ),
    );
  }
  
  void _addScanToMap(BuildContext context, IndoorScanResult scan, LatLng location) {
    // Use our utility class to add the scan to the map
    ScanMapIntegration.addScanToMap(scan, location);
    
    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Added ${scan.scanName} to map at ${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)}'),
        backgroundColor: Colors.green,
      ),
    );
  }
  
  void _showAddToMapDialog(BuildContext context, IndoorScanResult scan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add to Map'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Would you like to add this indoor scan to your map?'),
            SizedBox(height: 16),
            Text(
              'This will allow you to visualize the indoor space as part of your 2.5D map view.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showLocationPicker(context, scan);
            },
            child: const Text('SELECT LOCATION'),
          ),
        ],
      ),
    );
  }
} 