import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/indoor_scan_provider.dart';
import '../widgets/indoor_scan_view.dart';

/// Screen for performing a new indoor scan with the device's LiDAR sensor
class IndoorScanScreen extends StatelessWidget {
  static const String routeName = '/indoor-scan/new';

  const IndoorScanScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('New Indoor Scan'),
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: Consumer<IndoorScanProvider>(
        builder: (context, provider, child) {
          if (!provider.hasLidarCapability) {
            return _buildCompatibilityWarning(context);
          }
          
          return const IndoorScanView();
        },
      ),
    );
  }

  Widget _buildCompatibilityWarning(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 70,
              color: Colors.orange,
            ),
            const SizedBox(height: 24),
            Text(
              'Device Not Compatible',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Your device does not have a LiDAR scanner, which is required for accurate indoor scanning.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Text(
              'Compatible devices include:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              '• iPhone 12 Pro, iPhone 12 Pro Max\n'
              '• iPhone 13 Pro, iPhone 13 Pro Max\n'
              '• iPhone 14 Pro, iPhone 14 Pro Max\n'
              '• iPhone 15 Pro, iPhone 15 Pro Max\n'
              '• iPad Pro (2020 or later)',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Go Back'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 12,
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () {
                // Allow to continue anyway for demo/testing purposes
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (_) => const Scaffold(
                      body: IndoorScanView(),
                    ),
                  ),
                );
              },
              child: const Text('Continue Anyway (Demo)'),
            ),
          ],
        ),
      ),
    );
  }
} 