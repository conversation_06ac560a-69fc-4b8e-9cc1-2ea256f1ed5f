import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/indoor_scan_provider.dart';
import '../widgets/scan_result_card.dart';
import 'scan_details_screen.dart';
import 'indoor_scan_screen.dart';

/// Screen that displays a list of all saved indoor scans
class ScanListScreen extends StatelessWidget {
  static const String routeName = '/indoor-scan/list';

  const ScanListScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Indoor Scans'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              _showInfoDialog(context);
            },
            tooltip: 'About Indoor Scanning',
          ),
        ],
      ),
      body: Consumer<IndoorScanProvider>(
        builder: (context, provider, child) {
          if (provider.savedScans.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.view_in_ar_outlined,
                    size: 80,
                    color: Theme.of(context).disabledColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No indoor scans yet',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Start by creating a new scan',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pushNamed(IndoorScanScreen.routeName);
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('New Scan'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Info card at the top
              Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Indoor Scanning',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Use your device\'s LiDAR scanner to create 3D models of indoor spaces.',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // List of scan results
              Expanded(
                child: ListView.builder(
                  itemCount: provider.savedScans.length,
                  itemBuilder: (context, index) {
                    final scan = provider.savedScans[index];
                    return ScanResultCard(
                      scan: scan,
                      onTap: () {
                        provider.selectScan(scan.id);
                        Navigator.of(context).pushNamed(
                          ScanDetailsScreen.routeName,
                        );
                      },
                      onDelete: () {
                        // Show confirmation dialog
                        showDialog(
                          context: context,
                          builder: (ctx) => AlertDialog(
                            title: const Text('Delete Scan'),
                            content: Text(
                              'Are you sure you want to delete the scan "${scan.scanName}"?',
                            ),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  Navigator.of(ctx).pop();
                                },
                                child: const Text('Cancel'),
                              ),
                              TextButton(
                                onPressed: () {
                                  // Delete scan logic would go here
                                  Navigator.of(ctx).pop();
                                },
                                child: const Text('Delete'),
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.of(context).pushNamed(IndoorScanScreen.routeName);
        },
        icon: const Icon(Icons.add),
        label: const Text('New Scan'),
      ),
    );
  }

  void _showInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('About Indoor Scanning'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Indoor scanning allows you to create 3D models of indoor spaces using your device\'s LiDAR sensor.',
              ),
              const SizedBox(height: 16),
              Text(
                'Key Features:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              _buildFeatureItem(
                '• Room measurement and floor plan creation',
              ),
              _buildFeatureItem(
                '• Furniture and object detection',
              ),
              _buildFeatureItem(
                '• 3D model export',
              ),
              _buildFeatureItem(
                '• Integration with 2.5D maps',
              ),
              const SizedBox(height: 16),
              Text(
                'Requirements:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              _buildFeatureItem(
                '• Device with LiDAR sensor (iPhone 12 Pro or newer)',
              ),
              _buildFeatureItem(
                '• Well-lit environment',
              ),
              _buildFeatureItem(
                '• Clear line of sight to walls and objects',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
            },
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(text),
    );
  }
} 