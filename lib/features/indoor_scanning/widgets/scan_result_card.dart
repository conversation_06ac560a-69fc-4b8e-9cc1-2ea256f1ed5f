import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/indoor_scan_result.dart';

/// A card widget that displays information about a saved indoor scan
class ScanResultCard extends StatelessWidget {
  final IndoorScanResult scan;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const ScanResultCard({
    Key? key,
    required this.scan,
    this.onTap,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('MMM d, yyyy - h:mm a');
    
    return Card(
      elevation: 3,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              child: Row(
                children: [
                  const Icon(Icons.map, size: 28),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          scan.scanName,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          dateFormat.format(scan.scanDate),
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  if (onDelete != null)
                    IconButton(
                      icon: const Icon(Icons.delete_outline),
                      onPressed: onDelete,
                      tooltip: 'Delete Scan',
                    ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow(
                    context,
                    'Dimensions',
                    '${scan.dimensions.width.toStringAsFixed(1)} × ${scan.dimensions.height.toStringAsFixed(1)} m',
                    Icons.straighten,
                  ),
                  const SizedBox(height: 8),
                  _buildInfoRow(
                    context,
                    'Objects Detected',
                    _formatObjects(scan.metadata),
                    Icons.chair,
                  ),
                  const SizedBox(height: 8),
                  _buildInfoRow(
                    context,
                    'Processing Status',
                    scan.isProcessed ? 'Processed' : 'Pending',
                    scan.isProcessed ? Icons.check_circle : Icons.pending,
                  ),
                ],
              ),
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12),
              color: Theme.of(context).primaryColor.withOpacity(0.05),
              child: Center(
                child: Text(
                  'Tap to View Details',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Theme.of(context).hintColor),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  String _formatObjects(Map<String, dynamic> metadata) {
    final List<dynamic>? furniture = metadata['furniture'] as List<dynamic>?;
    if (furniture == null || furniture.isEmpty) {
      return 'None';
    }
    
    return furniture.join(', ');
  }
} 