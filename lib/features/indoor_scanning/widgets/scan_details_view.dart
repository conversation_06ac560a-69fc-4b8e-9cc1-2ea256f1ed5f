import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../models/indoor_scan_result.dart';

/// Widget that displays a 3D visualization of an indoor scan
/// This is a simulated/placeholder implementation for the proof of concept
class ScanDetailsView extends StatefulWidget {
  final IndoorScanResult scan;

  const ScanDetailsView({
    Key? key,
    required this.scan,
  }) : super(key: key);

  @override
  State<ScanDetailsView> createState() => _ScanDetailsViewState();
}

class _ScanDetailsViewState extends State<ScanDetailsView>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  
  // Variables for gesture based rotation
  double _dragStartX = 0.0;
  double _rotationX = 0.0;
  double _dragStartY = 0.0;
  double _rotationY = 0.0;
  
  // Scale factor for zoom
  double _scaleFactor = 1.0;

  @override
  void initState() {
    super.initState();
    
    // Setup animation controller for automatic rotation
    _controller = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.linear,
      ),
    );
    
    // Start the automatic rotation animation
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // Handle gesture-based rotation
      onPanStart: (details) {
        _controller.stop();
        _dragStartX = details.localPosition.dx;
        _dragStartY = details.localPosition.dy;
      },
      onPanUpdate: (details) {
        setState(() {
          _rotationY += (details.localPosition.dx - _dragStartX) / 100;
          _rotationX += (details.localPosition.dy - _dragStartY) / 100;
          _dragStartX = details.localPosition.dx;
          _dragStartY = details.localPosition.dy;
        });
      },
      onPanEnd: (_) {
        // Optionally restart automatic rotation
        // _controller.repeat();
      },
      // Handle pinch to zoom
      onScaleStart: (details) {
        _controller.stop();
      },
      onScaleUpdate: (details) {
        setState(() {
          _scaleFactor = math.max(0.5, math.min(2.5, details.scale));
        });
      },
      child: Stack(
        children: [
          // 3D visualization area
          Container(
            color: Colors.grey[900],
            width: double.infinity,
            height: double.infinity,
            child: CustomPaint(
              painter: _FloorPlanPainter(
                widget.scan,
                _controller.isAnimating ? _rotationAnimation.value : _rotationY,
                _rotationX,
                _scaleFactor,
              ),
            ),
          ),
          
          // Controls overlay
          Positioned(
            bottom: 16,
            right: 16,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildControlButton(
                  Icons.animation,
                  _controller.isAnimating ? 'Stop' : 'Animate',
                  () {
                    setState(() {
                      if (_controller.isAnimating) {
                        _controller.stop();
                      } else {
                        _controller.repeat();
                      }
                    });
                  },
                ),
                const SizedBox(height: 8),
                _buildControlButton(
                  Icons.home,
                  'Reset',
                  () {
                    setState(() {
                      _rotationX = 0.0;
                      _rotationY = 0.0;
                      _scaleFactor = 1.0;
                      _controller.reset();
                      _controller.repeat();
                    });
                  },
                ),
              ],
            ),
          ),
          
          // Instructions overlay
          Positioned(
            top: 16,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Drag to rotate • Pinch to zoom',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton(
    IconData icon,
    String tooltip,
    VoidCallback onPressed,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black54,
        borderRadius: BorderRadius.circular(20),
      ),
      child: IconButton(
        icon: Icon(icon, color: Colors.white),
        onPressed: onPressed,
        tooltip: tooltip,
      ),
    );
  }
}

/// Custom painter to simulate a 3D floor plan view
/// This is a placeholder visualization for the proof of concept
class _FloorPlanPainter extends CustomPainter {
  final IndoorScanResult scan;
  final double rotationY;
  final double rotationX;
  final double scaleFactor;
  
  final Paint _wallPaint = Paint()
    ..color = Colors.white
    ..style = PaintingStyle.stroke
    ..strokeWidth = 2.0;
  
  final Paint _floorPaint = Paint()
    ..color = Colors.grey.withOpacity(0.2)
    ..style = PaintingStyle.fill;
  
  final Paint _furniturePaint = Paint()
    ..color = Colors.blue.withOpacity(0.4)
    ..style = PaintingStyle.fill;
  
  final Paint _doorPaint = Paint()
    ..color = Colors.green
    ..style = PaintingStyle.stroke
    ..strokeWidth = 2.0;

  _FloorPlanPainter(
    this.scan,
    this.rotationY,
    this.rotationX,
    this.scaleFactor,
  );

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // Apply transformations
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.scale(scaleFactor);
    
    // Apply rotations
    final matrix = Matrix4.identity()
      ..rotateY(rotationY)
      ..rotateX(rotationX);
    
    // Draw floor (adjusted to match scan dimensions)
    final roomWidth = scan.dimensions.width * 20;  // Scale up for visibility
    final roomHeight = scan.dimensions.height * 20;
    
    // Create a rectangle representing the room
    final roomRect = Rect.fromCenter(
      center: Offset.zero,
      width: roomWidth,
      height: roomHeight,
    );
    
    // Transform room corners using the 3D rotation matrix
    final topLeft = _transform(
      Offset(roomRect.left, roomRect.top),
      matrix,
    );
    final topRight = _transform(
      Offset(roomRect.right, roomRect.top),
      matrix,
    );
    final bottomRight = _transform(
      Offset(roomRect.right, roomRect.bottom),
      matrix,
    );
    final bottomLeft = _transform(
      Offset(roomRect.left, roomRect.bottom),
      matrix,
    );
    
    // Draw the floor
    final floorPath = Path()
      ..moveTo(topLeft.dx, topLeft.dy)
      ..lineTo(topRight.dx, topRight.dy)
      ..lineTo(bottomRight.dx, bottomRight.dy)
      ..lineTo(bottomLeft.dx, bottomLeft.dy)
      ..close();
    
    canvas.drawPath(floorPath, _floorPaint);
    canvas.drawPath(floorPath, _wallPaint);
    
    // Draw walls (vertical lines from each corner)
    final wallHeight = 40.0; // Arbitrary wall height
    
    // Function to compute the 3D projection of a point with height
    Offset project3D(Offset base, double height) {
      final point3D = _transform(
        Offset(base.dx, base.dy - height), // Move up by height
        matrix,
      );
      return point3D;
    }
    
    // Draw the four walls
    _drawWall(canvas, topLeft, topRight, wallHeight, matrix);
    _drawWall(canvas, topRight, bottomRight, wallHeight, matrix);
    _drawWall(canvas, bottomRight, bottomLeft, wallHeight, matrix);
    _drawWall(canvas, bottomLeft, topLeft, wallHeight, matrix);
    
    // Draw ceiling
    final topLeftTop = project3D(topLeft, wallHeight);
    final topRightTop = project3D(topRight, wallHeight);
    final bottomRightTop = project3D(bottomRight, wallHeight);
    final bottomLeftTop = project3D(bottomLeft, wallHeight);
    
    final ceilingPath = Path()
      ..moveTo(topLeftTop.dx, topLeftTop.dy)
      ..lineTo(topRightTop.dx, topRightTop.dy)
      ..lineTo(bottomRightTop.dx, bottomRightTop.dy)
      ..lineTo(bottomLeftTop.dx, bottomLeftTop.dy)
      ..close();
    
    // Create a new paint with transparent color instead of using withOpacity on the paint
    final transparentFloorPaint = Paint()
      ..color = _floorPaint.color.withOpacity(0.1)
      ..style = _floorPaint.style;
    canvas.drawPath(ceilingPath, transparentFloorPaint);
    canvas.drawPath(ceilingPath, _wallPaint);
    
    // Draw doors
    final doorCount = scan.metadata['doors'] as int? ?? 1;
    if (doorCount > 0) {
      // Random door positions for the proof of concept
      // In a real implementation, door positions would come from the scan data
      for (int i = 0; i < doorCount; i++) {
        final wallIndex = i % 4; // Distribute doors on different walls
        final positionRatio = 0.5; // Door in the middle of the wall
        
        switch (wallIndex) {
          case 0: // Top wall
            _drawDoor(
              canvas,
              Offset.lerp(topLeft, topRight, positionRatio)!,
              30, // Door width
              wallHeight * 0.8, // Door height
              matrix,
              true,
            );
            break;
          case 1: // Right wall
            _drawDoor(
              canvas,
              Offset.lerp(topRight, bottomRight, positionRatio)!,
              30, // Door width
              wallHeight * 0.8, // Door height
              matrix,
              false,
            );
            break;
          case 2: // Bottom wall
            _drawDoor(
              canvas,
              Offset.lerp(bottomRight, bottomLeft, positionRatio)!,
              30, // Door width
              wallHeight * 0.8, // Door height
              matrix,
              true,
            );
            break;
          case 3: // Left wall
            _drawDoor(
              canvas,
              Offset.lerp(bottomLeft, topLeft, positionRatio)!,
              30, // Door width
              wallHeight * 0.8, // Door height
              matrix,
              false,
            );
            break;
        }
      }
    }
    
    // Draw furniture
    final furniture = scan.metadata['furniture'] as List<dynamic>? ?? [];
    if (furniture.isNotEmpty) {
      // Random furniture positions for the proof of concept
      // In a real implementation, positions would come from the scan data
      final random = math.Random(42); // Fixed seed for consistency
      
      for (int i = 0; i < furniture.length; i++) {
        final type = furniture[i].toString();
        
        // Random position within the room
        final x = (random.nextDouble() * 0.6 + 0.2) * roomWidth - roomWidth / 2;
        final y = (random.nextDouble() * 0.6 + 0.2) * roomHeight - roomHeight / 2;
        
        // Different furniture sizes based on type
        double width = 15;
        double height = 15;
        
        switch (type) {
          case 'table':
            width = 25;
            height = 20;
            break;
          case 'chair':
            width = 12;
            height = 12;
            break;
          case 'sofa':
            width = 30;
            height = 15;
            break;
          case 'tv':
            width = 20;
            height = 5;
            break;
          case 'bookshelf':
            width = 20;
            height = 10;
            break;
        }
        
        _drawFurniture(
          canvas,
          Offset(x, y),
          width,
          height,
          15, // Height above ground
          matrix,
          i, // Use index to vary furniture color
        );
      }
    }
    
    canvas.restore();
  }

  // Draw a single wall
  void _drawWall(
    Canvas canvas,
    Offset from,
    Offset to,
    double height,
    Matrix4 matrix,
  ) {
    // Calculate the top points (with height)
    final fromTop = _transform(
      Offset(from.dx, from.dy - height), // Move up by height
      matrix,
    );
    final toTop = _transform(
      Offset(to.dx, to.dy - height), // Move up by height
      matrix,
    );
    
    // Draw vertical lines
    canvas.drawLine(from, fromTop, _wallPaint);
    canvas.drawLine(to, toTop, _wallPaint);
    
    // Draw horizontal top line
    canvas.drawLine(fromTop, toTop, _wallPaint);
    
    // Create the wall path
    final wallPath = Path()
      ..moveTo(from.dx, from.dy)
      ..lineTo(to.dx, to.dy)
      ..lineTo(toTop.dx, toTop.dy)
      ..lineTo(fromTop.dx, fromTop.dy)
      ..close();
    
    // Fill the wall with a semi-transparent color
    canvas.drawPath(
      wallPath,
      Paint()
        ..color = Colors.grey.withOpacity(0.15)
        ..style = PaintingStyle.fill,
    );
  }
  
  // Draw a door
  void _drawDoor(
    Canvas canvas,
    Offset center,
    double width,
    double height,
    Matrix4 matrix,
    bool horizontal,
  ) {
    double halfWidth = width / 2;
    
    Offset topLeft, topRight, bottomLeft, bottomRight;
    
    if (horizontal) {
      topLeft = Offset(center.dx - halfWidth, center.dy);
      topRight = Offset(center.dx + halfWidth, center.dy);
      bottomLeft = Offset(center.dx - halfWidth, center.dy);
      bottomRight = Offset(center.dx + halfWidth, center.dy);
    } else {
      topLeft = Offset(center.dx, center.dy - halfWidth);
      topRight = Offset(center.dx, center.dy + halfWidth);
      bottomLeft = Offset(center.dx, center.dy - halfWidth);
      bottomRight = Offset(center.dx, center.dy + halfWidth);
    }
    
    // Transform to 3D
    topLeft = _transform(Offset(topLeft.dx, topLeft.dy - height), matrix);
    topRight = _transform(Offset(topRight.dx, topRight.dy - height), matrix);
    
    // Draw the door
    final doorPath = Path()
      ..moveTo(bottomLeft.dx, bottomLeft.dy)
      ..lineTo(bottomRight.dx, bottomRight.dy)
      ..lineTo(topRight.dx, topRight.dy)
      ..lineTo(topLeft.dx, topLeft.dy)
      ..close();
    
    canvas.drawPath(doorPath, _doorPaint);
  }
  
  // Draw a piece of furniture
  void _drawFurniture(
    Canvas canvas,
    Offset center,
    double width,
    double depth,
    double height,
    Matrix4 matrix,
    int colorIndex,
  ) {
    // Calculate corners of the base
    final halfWidth = width / 2;
    final halfDepth = depth / 2;
    
    final frontLeft = _transform(
      Offset(center.dx - halfWidth, center.dy + halfDepth),
      matrix,
    );
    final frontRight = _transform(
      Offset(center.dx + halfWidth, center.dy + halfDepth),
      matrix,
    );
    final backLeft = _transform(
      Offset(center.dx - halfWidth, center.dy - halfDepth),
      matrix,
    );
    final backRight = _transform(
      Offset(center.dx + halfWidth, center.dy - halfDepth),
      matrix,
    );
    
    // Calculate top corners
    final frontLeftTop = _transform(
      Offset(center.dx - halfWidth, center.dy + halfDepth - height),
      matrix,
    );
    final frontRightTop = _transform(
      Offset(center.dx + halfWidth, center.dy + halfDepth - height),
      matrix,
    );
    final backLeftTop = _transform(
      Offset(center.dx - halfWidth, center.dy - halfDepth - height),
      matrix,
    );
    final backRightTop = _transform(
      Offset(center.dx + halfWidth, center.dy - halfDepth - height),
      matrix,
    );
    
    // Choose a color based on the furniture index
    final colors = [
      Colors.blue.withOpacity(0.4),
      Colors.red.withOpacity(0.4),
      Colors.green.withOpacity(0.4),
      Colors.orange.withOpacity(0.4),
      Colors.purple.withOpacity(0.4),
    ];
    
    final color = colors[colorIndex % colors.length];
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    final outlinePaint = Paint()
      ..color = Colors.white70
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    // Draw the base (bottom face)
    final basePath = Path()
      ..moveTo(frontLeft.dx, frontLeft.dy)
      ..lineTo(frontRight.dx, frontRight.dy)
      ..lineTo(backRight.dx, backRight.dy)
      ..lineTo(backLeft.dx, backLeft.dy)
      ..close();
    
    canvas.drawPath(basePath, paint);
    canvas.drawPath(basePath, outlinePaint);
    
    // Draw the top face
    final topPath = Path()
      ..moveTo(frontLeftTop.dx, frontLeftTop.dy)
      ..lineTo(frontRightTop.dx, frontRightTop.dy)
      ..lineTo(backRightTop.dx, backRightTop.dy)
      ..lineTo(backLeftTop.dx, backLeftTop.dy)
      ..close();
    
    // Create new paints with adjusted opacity
    final topPaint = Paint()
      ..color = paint.color.withOpacity(0.7)
      ..style = paint.style;
    canvas.drawPath(topPath, topPaint);
    canvas.drawPath(topPath, outlinePaint);
    
    // Draw the front face
    final frontPath = Path()
      ..moveTo(frontLeft.dx, frontLeft.dy)
      ..lineTo(frontRight.dx, frontRight.dy)
      ..lineTo(frontRightTop.dx, frontRightTop.dy)
      ..lineTo(frontLeftTop.dx, frontLeftTop.dy)
      ..close();
    
    final frontPaint = Paint()
      ..color = paint.color.withOpacity(0.6)
      ..style = paint.style;
    canvas.drawPath(frontPath, frontPaint);
    canvas.drawPath(frontPath, outlinePaint);
    
    // Draw the right face
    final rightPath = Path()
      ..moveTo(frontRight.dx, frontRight.dy)
      ..lineTo(backRight.dx, backRight.dy)
      ..lineTo(backRightTop.dx, backRightTop.dy)
      ..lineTo(frontRightTop.dx, frontRightTop.dy)
      ..close();
    
    final rightPaint = Paint()
      ..color = paint.color.withOpacity(0.5)
      ..style = paint.style;
    canvas.drawPath(rightPath, rightPaint);
    canvas.drawPath(rightPath, outlinePaint);
  }
  
  // Helper method to transform a 2D point using a 3D matrix
  Offset _transform(Offset point, Matrix4 matrix) {
    // Create a 3D vector from the 2D point (Z=0)
    final vector = Vector(point.dx, point.dy, 0);
    
    // Apply the transformation
    final result = vector.transformed(matrix);
    
    // Project back to 2D
    return Offset(result.x, result.y);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// Simple vector class for 3D transformations
class Vector {
  final double x;
  final double y;
  final double z;
  
  Vector(this.x, this.y, this.z);
  
  Vector transformed(Matrix4 matrix) {
    // Apply the 3D transformation (simplified)
    final tx = matrix[0] * x + matrix[4] * y + matrix[8] * z + matrix[12];
    final ty = matrix[1] * x + matrix[5] * y + matrix[9] * z + matrix[13];
    final tz = matrix[2] * x + matrix[6] * y + matrix[10] * z + matrix[14];
    final tw = matrix[3] * x + matrix[7] * y + matrix[11] * z + matrix[15];
    
    // Perspective division
    if (tw != 0.0) {
      return Vector(tx / tw, ty / tw, tz / tw);
    }
    
    return Vector(tx, ty, tz);
  }
} 