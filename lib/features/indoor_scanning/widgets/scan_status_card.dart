import 'package:flutter/material.dart';

/// A card widget that displays the current status of an indoor scan
class ScanStatusCard extends StatelessWidget {
  final String status;
  final bool isScanning;
  final VoidCallback? onCancel;

  const ScanStatusCard({
    Key? key,
    required this.status,
    required this.isScanning,
    this.onCancel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(
                  isScanning ? Icons.sensors : Icons.check_circle,
                  color: isScanning ? Colors.blue : Colors.green,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    isScanning ? 'Scanning in Progress' : 'Scan Status',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                if (isScanning && onCancel != null)
                  IconButton(
                    icon: const Icon(Icons.cancel),
                    onPressed: onCancel,
                    tooltip: 'Cancel Scan',
                  ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),
            Text(status),
            if (isScanning) ...[
              const SizedBox(height: 16),
              const LinearProgressIndicator(),
            ],
          ],
        ),
      ),
    );
  }
} 