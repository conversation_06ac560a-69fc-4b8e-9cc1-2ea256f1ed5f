import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/indoor_scan_provider.dart';
import 'scan_status_card.dart';

/// Widget that displays the camera preview and scanning UI
class IndoorScanView extends StatefulWidget {
  const IndoorScanView({Key? key}) : super(key: key);

  @override
  State<IndoorScanView> createState() => _IndoorScanViewState();
}

class _IndoorScanViewState extends State<IndoorScanView> {
  final TextEditingController _nameController = TextEditingController();
  bool _nameEntered = false;

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<IndoorScanProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  // This would be a camera preview in a real implementation
                  // For the proof of concept, we use a placeholder
                  Container(
                    color: Colors.black87,
                    width: double.infinity,
                    height: double.infinity,
                    child: Center(
                      child: provider.isScanning
                          ? Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.view_in_ar,
                                  color: Colors.white,
                                  size: 80,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Scanning in progress...',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                  ),
                                ),
                                const SizedBox(height: 24),
                                // Animated indicator to simulate scanning
                                SizedBox(
                                  width: 100,
                                  child: LinearProgressIndicator(
                                    backgroundColor: Colors.white24,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Theme.of(context).primaryColor,
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.view_in_ar_outlined,
                                  color: Colors.white54,
                                  size: 80,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Ready to scan',
                                  style: TextStyle(
                                    color: Colors.white70,
                                    fontSize: 18,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Move around the room slowly',
                                  style: TextStyle(
                                    color: Colors.white54,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                  // Display scanning guidance
                  if (!provider.isScanning)
                    Positioned(
                      top: 20,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        margin: const EdgeInsets.symmetric(horizontal: 24),
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            Text(
                              'Scanning Tips:',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '• Hold your device steady and move slowly',
                              style: TextStyle(color: Colors.white70),
                            ),
                            Text(
                              '• Ensure good lighting in the room',
                              style: TextStyle(color: Colors.white70),
                            ),
                            Text(
                              '• Capture all walls and major objects',
                              style: TextStyle(color: Colors.white70),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Control panel
            Container(
              padding: const EdgeInsets.all(16),
              color: Theme.of(context).cardColor,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Status card if scanning is in progress
                  if (provider.isScanning)
                    ScanStatusCard(
                      status: provider.currentStatus,
                      isScanning: provider.isScanning,
                    ),
                  // Scan name input if not scanning
                  if (!provider.isScanning)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: TextField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          labelText: 'Scan Name',
                          hintText: 'e.g. Living Room, Office, etc.',
                          prefixIcon: Icon(Icons.edit),
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          setState(() {
                            _nameEntered = value.trim().isNotEmpty;
                          });
                        },
                      ),
                    ),
                  // Action buttons
                  Row(
                    children: [
                      if (!provider.isScanning) ...[
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 16),
                      ],
                      Expanded(
                        child: ElevatedButton(
                          onPressed: provider.isScanning || !_nameEntered
                              ? null
                              : () {
                                  provider.startScan(_nameController.text.trim());
                                },
                          child: Text(
                            provider.isScanning ? 'Scanning...' : 'Start Scan',
                          ),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                  // Device compatibility notice
                  if (!provider.hasLidarCapability)
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Text(
                        'Note: For best results, use a device with LiDAR scanner (iPhone 12 Pro or newer)',
                        style: TextStyle(
                          color: Theme.of(context).hintColor,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
} 