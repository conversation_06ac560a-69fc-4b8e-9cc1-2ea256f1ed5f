import 'package:flutter/material.dart';

import '../models/indoor_scan_result.dart';
import '../services/indoor_scan_service.dart';

/// Provider for managing indoor scanning state
class IndoorScanProvider with ChangeNotifier {
  final IndoorScanService _scanService = IndoorScanService();
  
  // State variables
  bool _isScanning = false;
  bool _hasLidarCapability = false;
  String _currentStatus = 'Ready';
  List<IndoorScanResult> _savedScans = [];
  IndoorScanResult? _currentScan;
  
  // Getters
  bool get isScanning => _isScanning;
  bool get hasLidarCapability => _hasLidarCapability;
  String get currentStatus => _currentStatus;
  List<IndoorScanResult> get savedScans => _savedScans;
  IndoorScanResult? get currentScan => _currentScan;
  
  // Stream subscription for scan status updates
  Stream<String> get scanStatusStream => _scanService.scanStatus;
  
  IndoorScanProvider() {
    _initialize();
  }
  
  // Initialize provider state
  Future<void> _initialize() async {
    // Check device capability
    _hasLidarCapability = await _scanService.hasLiDARCapability();
    
    // Load saved scans
    _loadSavedScans();
    
    // Listen for status updates
    _scanService.scanStatus.listen((status) {
      _currentStatus = status;
      notifyListeners();
    });
    
    notifyListeners();
  }
  
  // Load saved scans
  Future<void> _loadSavedScans() async {
    _savedScans = await _scanService.getSavedScans();
    notifyListeners();
  }
  
  // Start a new indoor scan
  Future<void> startScan(String scanName) async {
    if (_isScanning) return;
    
    _isScanning = true;
    _currentStatus = 'Preparing scan...';
    notifyListeners();
    
    try {
      final result = await _scanService.startScan(scanName);
      
      if (result != null) {
        _currentScan = result;
        _savedScans = [result, ..._savedScans];
      }
    } finally {
      _isScanning = false;
      notifyListeners();
    }
  }
  
  // Select a scan to view details
  void selectScan(String scanId) {
    _currentScan = _savedScans.firstWhere((scan) => scan.id == scanId);
    notifyListeners();
  }
  
  // Clear current scan selection
  void clearCurrentScan() {
    _currentScan = null;
    notifyListeners();
  }
  
  @override
  void dispose() {
    _scanService.dispose();
    super.dispose();
  }
} 