import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import 'providers/indoor_scan_provider.dart';
import 'screens/indoor_scan_screen.dart';
import 'screens/scan_list_screen.dart';
import 'screens/scan_details_screen.dart';

/// Module that handles registration of indoor scanning feature
/// This ensures the feature is completely isolated from the rest of the app
class IndoorScanningModule {
  /// Register routes for this feature
  static Map<String, WidgetBuilder> getRoutes() {
    return {
      IndoorScanScreen.routeName: (context) => const IndoorScanScreen(),
      ScanListScreen.routeName: (context) => const ScanListScreen(),
      ScanDetailsScreen.routeName: (context) => const ScanDetailsScreen(),
    };
  }
  
  /// Register providers for this feature
  static List<SingleChildWidget> getProviders() {
    return [
      ChangeNotifierProvider(create: (_) => IndoorScanProvider()),
    ];
  }
  
  /// Get the entry point widget for this feature
  static Widget getEntryPoint() {
    return const ScanListScreen();
  }
} 