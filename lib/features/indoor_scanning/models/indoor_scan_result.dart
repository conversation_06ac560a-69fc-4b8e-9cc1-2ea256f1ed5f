import 'package:flutter/material.dart';

/// Model representing the result of an indoor scan operation
class IndoorScanResult {
  final String id;
  final DateTime scanDate;
  final String scanName;
  final String filePath; // Path to the stored scan file
  final Size dimensions; // Approximate room dimensions
  final Map<String, dynamic> metadata; // Additional scan data
  final bool isProcessed; // Whether the scan has been processed

  IndoorScanResult({
    required this.id,
    required this.scanDate,
    required this.scanName,
    required this.filePath,
    required this.dimensions,
    this.metadata = const {},
    this.isProcessed = false,
  });

  /// Create a new scan result from JSON data
  factory IndoorScanResult.fromJson(Map<String, dynamic> json) {
    return IndoorScanResult(
      id: json['id'] as String,
      scanDate: DateTime.parse(json['scanDate'] as String),
      scanName: json['scanName'] as String,
      filePath: json['filePath'] as String,
      dimensions: Size(
        json['width'] as double,
        json['height'] as double,
      ),
      metadata: json['metadata'] as Map<String, dynamic>,
      isProcessed: json['isProcessed'] as bool,
    );
  }

  /// Convert this model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'scanDate': scanDate.toIso8601String(),
      'scanName': scanName,
      'filePath': filePath,
      'width': dimensions.width,
      'height': dimensions.height,
      'metadata': metadata,
      'isProcessed': isProcessed,
    };
  }
} 