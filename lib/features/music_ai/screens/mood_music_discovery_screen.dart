import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/music_ai_provider.dart';
import '../widgets/mood_selector.dart';

class MoodMusicDiscoveryScreen extends StatefulWidget {
  static const String routeName = '/mood_music_discovery';
  
  const MoodMusicDiscoveryScreen({Key? key}) : super(key: key);

  @override
  State<MoodMusicDiscoveryScreen> createState() => _MoodMusicDiscoveryScreenState();
}

class _MoodMusicDiscoveryScreenState extends State<MoodMusicDiscoveryScreen> {
  bool _isLoading = false;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mood-Based Music'),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withOpacity(0.9)
                : Colors.black87,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Consumer<MusicAIProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              // Mood selector
              MoodSelector(
                selectedMoods: provider.userMoods,
                availableMoods: provider.availableMoods,
                onMoodToggled: provider.toggleMood,
                onClearAll: provider.clearMoods,
              ),
              
              const SizedBox(height: 16),
              
              // Find music button
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : () => _findMusicByMood(provider),
                  icon: const Icon(Icons.music_note),
                  label: const Text('Find Music For My Mood'),
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 50),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Placeholder for future implementation
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.music_note,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Coming Soon!',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Select your mood above to discover music that matches how you feel.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
  
  Future<void> _findMusicByMood(MusicAIProvider provider) async {
    if (provider.userMoods.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one mood'),
        ),
      );
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final tracks = await provider.findTracksByMood(provider.userMoods);
      
      if (!mounted) return;
      
      if (tracks.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No tracks found for the selected moods.'),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Found ${tracks.length} tracks matching your mood! Feature coming soon.'),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
} 