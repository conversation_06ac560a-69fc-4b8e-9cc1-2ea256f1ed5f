import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/music_ai_provider.dart';

class LocationMusicPatternsScreen extends StatefulWidget {
  static const String routeName = '/location_music_patterns';
  
  const LocationMusicPatternsScreen({Key? key}) : super(key: key);

  @override
  State<LocationMusicPatternsScreen> createState() => _LocationMusicPatternsScreenState();
}

class _LocationMusicPatternsScreenState extends State<LocationMusicPatternsScreen> {
  bool _isLoading = false;
  final double _defaultLatitude = 37.7749;
  final double _defaultLongitude = -122.4194;
  double _radius = 1.0; // km
  int _timeframe = 30; // days
  
  @override
  void initState() {
    super.initState();
    _loadLocationAnalysis();
  }
  
  Future<void> _loadLocationAnalysis() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final provider = Provider.of<MusicAIProvider>(context, listen: false);
      await provider.analyzeLocationPatterns(
        latitude: _defaultLatitude,
        longitude: _defaultLongitude,
        radius: _radius,
        timeframe: _timeframe,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Location Music Patterns'),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withOpacity(0.9)
                : Colors.black87,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withOpacity(0.9)
                  : Colors.black87,
            ),
            onPressed: _isLoading ? null : _loadLocationAnalysis,
            tooltip: 'Refresh analysis',
          ),
        ],
      ),
      body: Consumer<MusicAIProvider>(
        builder: (context, provider, child) {
          if (_isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          if (provider.errorMessage != null) {
            return Center(
              child: Text('Error: ${provider.errorMessage}'),
            );
          }
          
          if (provider.locationAnalysis == null) {
            return Center(
              child: Text(
                'No location analysis available',
                style: TextStyle(color: Colors.grey[600]),
              ),
            );
          }
          
          // Display placeholder content
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.map,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'Coming Soon!',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    'This feature will show music patterns and trends by location. Data has been analyzed for demonstration purposes.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Analysis Summary',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text('Location: San Francisco'),
                          Text('Music pins in area: ${provider.locationAnalysis!['total_pins']}'),
                          Text('Unique users: ${provider.locationAnalysis!['unique_users']}'),
                          Text('Top genre: ${(provider.locationAnalysis!['genre_distribution'] as List).first['name']}'),
                          Text('Top mood: ${(provider.locationAnalysis!['mood_distribution'] as List).first['name']}'),
                          Text('Peak activity hour: ${provider.locationAnalysis!['peak_hours'].first}:00'),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
} 