import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/music_ai_provider.dart';
import '../models/recommendation_result.dart';
import '../models/track.dart';
import '../../../widgets/music/track_card.dart';
import '../../../models/music_track.dart';
import '../widgets/ai_explanation_card.dart';
import '../widgets/mood_selector.dart';
import '../../../providers/spotify_provider.dart';

class MusicAIRecommendationScreen extends StatefulWidget {
  static const String routeName = '/music_ai_recommendations';
  
  final bool showBottomNav;
  
  const MusicAIRecommendationScreen({
    Key? key,
    this.showBottomNav = false,
  }) : super(key: key);

  @override
  State<MusicAIRecommendationScreen> createState() => _MusicAIRecommendationScreenState();
}

class _MusicAIRecommendationScreenState extends State<MusicAIRecommendationScreen> {
  bool _isGenerating = false;
  MusicTrack? _selectedSeedTrack;
  
  @override
  void initState() {
    super.initState();
    // Use a post-frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }
  
  Future<void> _loadInitialData() async {
    if (!mounted) return;
    
    final provider = Provider.of<MusicAIProvider>(context, listen: false);
    
    // Check if we have current location from MapProvider
    // This would be implemented in a real app
    final location = {
      'latitude': 37.7749,
      'longitude': -122.4194,
      'name': 'San Francisco',
    };
    
    // Set location data in provider
    provider.setLocationData(location);
    
    // Get currently playing track from Spotify as seed
    try {
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
      if (spotifyProvider.currentTrack != null) {
        setState(() {
          _selectedSeedTrack = spotifyProvider.currentTrack;
        });
        provider.setMusicSeed(spotifyProvider.currentTrack!);
      }
    } catch (e) {
      print('Error loading Spotify data: $e');
      // Continue without Spotify data
    }
  }
  
  Future<void> _generateRecommendations() async {
    setState(() {
      _isGenerating = true;
    });
    
    try {
      final provider = Provider.of<MusicAIProvider>(context, listen: false);
      await provider.generateRecommendations();
    } catch (e) {
      print('Error generating recommendations: $e');
      // Show error if needed
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Music Recommendations'),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withOpacity(0.9)
                : Colors.black87,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white.withOpacity(0.9)
                  : Colors.black87,
            ),
            onPressed: _isGenerating ? null : _generateRecommendations,
            tooltip: 'Refresh recommendations',
          ),
        ],
      ),
      body: SafeArea(
        child: Consumer<MusicAIProvider>(
          builder: (context, provider, child) {
            if (provider.isLoading || _isGenerating) {
              return _buildLoadingState();
            }
            
            if (provider.errorMessage != null) {
              return _buildErrorState(provider.errorMessage!);
            }
            
            return Column(
              children: [
                // Top section with scrollable content
                Container(
                  padding: const EdgeInsets.only(top: 16, bottom: 8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Seed track section (if any)
                      if (_selectedSeedTrack != null)
                        _buildSeedTrackSection(_selectedSeedTrack!),
                      
                      // Mood selector with horizontal scrolling to avoid overflow
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: MoodSelector(
                          selectedMoods: provider.userMoods,
                          availableMoods: provider.availableMoods,
                          onMoodToggled: provider.toggleMood,
                          onClearAll: provider.clearMoods,
                        ),
                      ),
                      
                      // Location indicator
                      _buildLocationIndicator(),
                    ],
                  ),
                ),
                
                // Generate button or recommendations list
                if (provider.recommendations.isEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
                    child: ElevatedButton.icon(
                      onPressed: _isGenerating ? null : _generateRecommendations,
                      icon: const Icon(Icons.auto_awesome),
                      label: const Text('Generate Recommendations'),
                      style: ElevatedButton.styleFrom(
                        minimumSize: const Size(double.infinity, 50),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                    ),
                  )
                else
                  Expanded(
                    child: _buildRecommendationsList(provider.recommendations),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
  
  Widget _buildSeedTrackSection(MusicTrack track) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.music_note, size: 16),
                const SizedBox(width: 8),
                Text(
                  'Based on this track:',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    track.albumArtUrl ?? 'https://via.placeholder.com/48',
                    width: 48,
                    height: 48,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: 48,
                      height: 48,
                      color: Colors.grey[300],
                      child: const Icon(Icons.music_note, size: 24),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        track.title,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        track.artist,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  onPressed: () {
                    setState(() {
                      _selectedSeedTrack = null;
                    });
                    Provider.of<MusicAIProvider>(context, listen: false)
                        .setMusicSeed(null);
                  },
                  tooltip: 'Remove seed track',
                  constraints: const BoxConstraints(
                    minWidth: 40,
                    minHeight: 40,
                  ),
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildLocationIndicator() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.location_on,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'San Francisco',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildRecommendationsList(List<RecommendationResult> recommendations) {
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 24),
      itemCount: recommendations.length,
      itemBuilder: (context, index) {
        final recommendation = recommendations[index];
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // AI explanation card
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: AIExplanationCard(
                  explanation: recommendation.explanation ?? 'Based on your preferences.',
                  confidence: recommendation.confidence,
                ),
              ),
              
              // Track card
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                child: TrackCard(
                  track: _convertToMusicTrack(recommendation.track),
                  title: recommendation.track.title,
                  artist: recommendation.track.artist,
                  albumArt: recommendation.track.artworkUrl,
                  duration: recommendation.track.formattedDuration,
                  onTap: () {
                    // Handle track selection
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Selected: ${recommendation.track.title}'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                  isSelected: false,
                  showPlayButton: recommendation.track.externalUrls != null && 
                                 recommendation.track.externalUrls!.isNotEmpty,
                  onPlay: () {
                    try {
                      // Play the track
                      _playTrack(recommendation.track);
                    } catch (e) {
                      print('Error playing track: $e');
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Could not play track. Spotify provider not available.'),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    }
                  },
                ),
              ),
              
              // Rating
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 4, 16, 0),
                child: _buildRatingBar(recommendation),
              ),
              
              // Add a separator except for the last item
              if (index < recommendations.length - 1)
                Padding(
                  padding: const EdgeInsets.fromLTRB(32, 16, 32, 0),
                  child: Divider(color: Colors.grey.withOpacity(0.3)),
                ),
            ],
          ),
        );
      },
    );
  }
  
  Widget _buildRatingBar(RecommendationResult recommendation) {
    return Card(
      elevation: 0,
      color: Theme.of(context).colorScheme.surface,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: Theme.of(context).dividerColor.withOpacity(0.1),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Rate this:',
              style: TextStyle(
                fontSize: 13,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
            const SizedBox(width: 8),
            Wrap(
              spacing: 2,
              children: List.generate(5, (i) => i + 1).map((rating) {
                // Extract user rating from metadata if available
                final userRating = recommendation.metadata != null && 
                                  recommendation.metadata!.containsKey('userRating') 
                                  ? recommendation.metadata!['userRating'] as int?
                                  : null;
                
                return IconButton(
                  icon: Icon(
                    userRating == rating 
                        ? Icons.star 
                        : Icons.star_border,
                    size: 18,
                    color: userRating == rating 
                        ? Colors.amber 
                        : Theme.of(context).disabledColor,
                  ),
                  onPressed: () {
                    try {
                      // Rate the recommendation
                      Provider.of<MusicAIProvider>(context, listen: false)
                          .rateRecommendation(
                            recommendationId: recommendation.track.id,
                            rating: rating,
                          );
                    } catch (e) {
                      print('Error rating track: $e');
                    }
                  },
                  constraints: const BoxConstraints(maxWidth: 28, maxHeight: 28),
                  padding: EdgeInsets.zero,
                  splashRadius: 16,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 24),
          Text(
            _isGenerating 
                ? 'Creating personalized recommendations...' 
                : 'Loading...',
            style: TextStyle(
              color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
              fontSize: 16,
            ),
          ),
          if (_isGenerating) ...[
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 48.0),
              child: Text(
                "We're finding music based on your preferences, location, and listening history",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodySmall?.color,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Text(
              error,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _generateRecommendations,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(180, 44),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.music_note,
              size: 72,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 32),
          Text(
            'Ready to discover new music?',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 48.0),
            child: Text(
              'Select your mood preferences above and tap Generate to get personalized recommendations',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Helper to convert Track to MusicTrack for UI components that expect MusicTrack
  MusicTrack _convertToMusicTrack(Track track) {
    return MusicTrack(
      id: track.id,
      title: track.title,
      artist: track.artist,
      album: track.album ?? '',
      albumArt: track.artworkUrl ?? 'https://via.placeholder.com/300',
      url: track.externalUrls?['spotify'] ?? '',
      service: 'Spotify',
      serviceType: 'spotify',
      genres: track.genres ?? [],
      durationMs: (track.durationSeconds ?? 0) * 1000,
      popularity: track.popularity ?? 50,
      uri: track.externalUrls?['spotify'] ?? '',
      isPlayable: true,
    );
  }
  
  void _playTrack(Track track) {
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    try {
      final musicTrack = MusicTrack(
        id: track.id,
        title: track.title,
        artist: track.artist,
        album: track.album ?? '',
        albumArt: track.artworkUrl ?? '',
        uri: track.externalUrls?['spotify'] ?? '',
        durationMs: (track.durationSeconds ?? 0) * 1000,
        url: track.externalUrls?['spotify'] ?? '',
        service: 'spotify',
        serviceType: 'spotify',
        genres: track.genres ?? [],
        explicit: track.metadata?['explicit'] as bool? ?? false,
        popularity: track.popularity ?? 0,
      );
      spotifyProvider.playTrack(musicTrack);
    } catch (e) {
      print('Error playing track: $e');
    }
  }
} 