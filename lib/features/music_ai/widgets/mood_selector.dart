import 'package:flutter/material.dart';

/// A widget that allows users to select from a list of mood options
class MoodSelector extends StatelessWidget {
  final List<String> selectedMoods;
  final List<String> availableMoods;
  final Function(String) onMoodToggled;
  final VoidCallback onClearAll;
  
  const MoodSelector({
    Key? key,
    required this.selectedMoods,
    required this.availableMoods,
    required this.onMoodToggled,
    required this.onClearAll,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Icon(
              Icons.mood,
              size: 16,
              color: theme.colorScheme.primary.withOpacity(0.8),
            ),
            const SizedBox(width: 8),
            Text(
              'YOUR MOOD',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                letterSpacing: 0.5,
                color: theme.colorScheme.primary,
              ),
            ),
            const Spacer(),
            if (selectedMoods.isNotEmpty)
              TextButton(
                onPressed: onClearAll,
                child: Text(
                  'Clear All', 
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.secondary,
                  ),
                ),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  minimumSize: const Size(50, 30),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Horizontally scrollable mood chips
        SizedBox(
          height: 36,
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.zero,
            children: availableMoods.map((mood) {
              final isSelected = selectedMoods.contains(mood);
              return Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: _buildMoodChip(context, mood, isSelected),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
  
  Widget _buildMoodChip(BuildContext context, String mood, bool isSelected) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    
    return FilterChip(
      selected: isSelected,
      label: Text(
        mood,
        style: TextStyle(
          fontSize: 13,
          color: isSelected ? primaryColor : theme.textTheme.bodyMedium?.color,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      onSelected: (selected) {
        onMoodToggled(mood);
      },
      selectedColor: primaryColor.withOpacity(0.15),
      checkmarkColor: primaryColor,
      backgroundColor: theme.colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: isSelected ? primaryColor : theme.dividerColor.withOpacity(0.3),
          width: isSelected ? 1.5 : 0.5,
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
      visualDensity: VisualDensity.compact,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      elevation: 0,
      pressElevation: 0,
      showCheckmark: true,
    );
  }
} 