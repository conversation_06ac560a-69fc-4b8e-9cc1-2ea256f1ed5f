import 'package:flutter/material.dart';

/// A card that displays an AI-generated explanation for a music recommendation
class AIExplanationCard extends StatelessWidget {
  final String explanation;
  final double confidence;
  final bool showConfidence;
  
  const AIExplanationCard({
    Key? key,
    required this.explanation,
    required this.confidence,
    this.showConfidence = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Get color based on confidence
    final Color confidenceColor = _getConfidenceColor(confidence, theme);
    
    return Card(
      margin: EdgeInsets.zero,
      color: theme.colorScheme.surface,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: theme.colorScheme.primary.withOpacity(0.15),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // AI icon and label
            Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  size: 18,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'AI RECOMMENDATION',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                    letterSpacing: 0.5,
                  ),
                ),
                if (showConfidence) ...[
                  const Spacer(),
                  _buildConfidenceBadge(confidenceColor, theme),
                ],
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Explanation text
            Text(
              explanation,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.textTheme.bodyMedium?.color?.withOpacity(0.9),
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build confidence badge
  Widget _buildConfidenceBadge(Color confidenceColor, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: confidenceColor.withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${(confidence * 100).toInt()}%',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: confidenceColor,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            'match',
            style: TextStyle(
              fontSize: 11,
              color: confidenceColor.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Get color based on confidence level
  Color _getConfidenceColor(double confidence, ThemeData theme) {
    if (confidence >= 0.8) {
      return theme.brightness == Brightness.dark ? Colors.greenAccent : Colors.green;
    } else if (confidence >= 0.6) {
      return theme.brightness == Brightness.dark ? Colors.orangeAccent : Colors.orange;
    } else {
      return theme.brightness == Brightness.dark ? Colors.redAccent : Colors.red;
    }
  }
} 