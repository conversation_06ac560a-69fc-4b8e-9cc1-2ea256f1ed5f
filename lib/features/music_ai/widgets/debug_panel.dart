import 'package:flutter/material.dart';
import '../models/recommendation_result.dart';

/// A modern, expandable debug panel for displaying diagnostic information about AI recommendations
class AIDebugPanel extends StatefulWidget {
  final List<RecommendationResult> recommendations;
  final Map<String, dynamic>? locationData;
  final bool isLocationUsed;
  final List<String> selectedMoods;
  final String? systemInfo;
  final int apiCallCount;
  final Map<String, dynamic> timings;
  
  const AIDebugPanel({
    Key? key,
    required this.recommendations,
    this.locationData,
    this.isLocationUsed = false,
    this.selectedMoods = const [],
    this.systemInfo,
    this.apiCallCount = 0,
    this.timings = const {},
  }) : super(key: key);

  @override
  State<AIDebugPanel> createState() => _AIDebugPanelState();
}

class _AIDebugPanelState extends State<AIDebugPanel> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightFactor;
  bool _isExpanded = false;
  final _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _heightFactor = _controller.drive(CurveTween(curve: Curves.easeInOut));
  }
  
  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }
  
  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 4,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          InkWell(
            onTap: _toggleExpand,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              child: Row(
                children: [
                  const Icon(Icons.bug_report, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'AI Debug Info',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    'Recommendations: ${widget.recommendations.length}',
                    style: theme.textTheme.bodySmall,
                  ),
                  const SizedBox(width: 8),
                  RotationTransition(
                    turns: Tween(begin: 0.0, end: 0.5).animate(_controller),
                    child: const Icon(Icons.keyboard_arrow_down),
                  ),
                ],
              ),
            ),
          ),
          
          // Animated expandable content
          SizeTransition(
            sizeFactor: _heightFactor,
            child: Container(
              constraints: const BoxConstraints(maxHeight: 300),
              child: Scrollbar(
                controller: _scrollController,
                thumbVisibility: true,
                thickness: 6,
                radius: const Radius.circular(8),
                child: SingleChildScrollView(
                  controller: _scrollController,
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Divider(),
                      
                      // Input parameters section
                      _buildSectionHeader('Input Parameters'),
                      _buildKeyValueRow('Moods Used', widget.selectedMoods.isEmpty 
                          ? 'None' 
                          : widget.selectedMoods.join(', ')),
                      _buildKeyValueRow('Location Used', widget.isLocationUsed ? 'Yes' : 'No'),
                      if (widget.locationData != null) ...[
                        _buildKeyValueRow('Latitude', '${widget.locationData!['latitude']}'),
                        _buildKeyValueRow('Longitude', '${widget.locationData!['longitude']}'),
                      ],
                      
                      const Divider(),
                      
                      // Results overview
                      _buildSectionHeader('Results Overview'),
                      _buildKeyValueRow('Recommendations', '${widget.recommendations.length}'),
                      _buildKeyValueRow('Avg Confidence', _calculateAvgConfidence()),
                      _buildKeyValueRow('API Calls', '${widget.apiCallCount}'),
                      
                      const Divider(),
                      
                      // Performance metrics
                      _buildSectionHeader('Performance Metrics'),
                      ...widget.timings.entries.map((entry) {
                        return _buildKeyValueRow(entry.key, '${entry.value} ms');
                      }),
                      
                      const Divider(),
                      
                      // Technical details
                      _buildSectionHeader('Technical Details'),
                      _buildKeyValueRow('Engine', 'RecommendationEngine v1.0'),
                      _buildKeyValueRow('Algorithms', 'Content-based filtering, Collaborative filtering'),
                      if (widget.systemInfo != null)
                        _buildKeyValueRow('System', widget.systemInfo!),
                      
                      // Recommendation insights
                      if (widget.recommendations.isNotEmpty) ...[
                        const Divider(),
                        _buildSectionHeader('Recommendation Insights'),
                        ..._buildRecommendationInsights(),
                      ],
                      
                      const SizedBox(height: 8),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
          fontSize: 14,
        ),
      ),
    );
  }
  
  Widget _buildKeyValueRow(String key, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              key,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13,
                color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  String _calculateAvgConfidence() {
    if (widget.recommendations.isEmpty) return 'N/A';
    
    final totalConfidence = widget.recommendations.fold<double>(
      0, (sum, rec) => sum + rec.confidence);
    
    return '${((totalConfidence / widget.recommendations.length) * 100).toStringAsFixed(1)}%';
  }
  
  List<Widget> _buildRecommendationInsights() {
    final insights = <Widget>[];
    
    // Count by factors
    int locationBased = 0;
    int moodBased = 0;
    int seedBased = 0;
    
    for (final rec in widget.recommendations) {
      if (rec.isLocationBased) locationBased++;
      if (rec.isMoodBased) moodBased++;
      if (rec.isSeedBased) seedBased++;
    }
    
    insights.add(_buildKeyValueRow('Location-based', '$locationBased'));
    insights.add(_buildKeyValueRow('Mood-based', '$moodBased'));
    insights.add(_buildKeyValueRow('Seed-based', '$seedBased'));
    
    // Most confident recommendation
    if (widget.recommendations.isNotEmpty) {
      final mostConfident = widget.recommendations.reduce(
          (a, b) => a.confidence > b.confidence ? a : b);
      
      insights.add(_buildKeyValueRow(
        'Top Recommendation',
        '"${mostConfident.track.title}" (${(mostConfident.confidence * 100).toStringAsFixed(1)}%)',
      ));
    }
    
    return insights;
  }
} 