import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import 'providers/music_ai_provider.dart';
import 'screens/music_ai_recommendation_screen.dart';
import 'screens/mood_music_discovery_screen.dart';
import 'screens/location_music_patterns_screen.dart';
import '../../providers/spotify_provider.dart';

/// Module that handles registration of music AI recommendation features
/// This ensures the feature is completely isolated from the rest of the app
class MusicAIModule {
  /// Register routes for this feature
  static Map<String, WidgetBuilder> getRoutes() {
    return {
      MusicAIRecommendationScreen.routeName: (context) => const MusicAIRecommendationScreen(),
      MoodMusicDiscoveryScreen.routeName: (context) => const MoodMusicDiscoveryScreen(),
      LocationMusicPatternsScreen.routeName: (context) => const LocationMusicPatternsScreen(),
    };
  }
  
  /// Register providers for this feature
  static List<SingleChildWidget> getProviders() {
    return [
      ChangeNotifierProvider(create: (_) => MusicAIProvider()),
      // Adding SpotifyProvider as it's required by the MusicAIRecommendationScreen
      ChangeNotifierProvider(create: (_) => SpotifyProvider()),
    ];
  }
  
  /// Get the entry point widget for this feature
  static Widget getEntryPoint() {
    return const MusicAIRecommendationScreen();
  }
} 