import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../../models/music_track.dart';
import '../services/music_ai_service.dart';
import '../models/recommendation_parameters.dart';
import '../models/recommendation_result.dart';

/// Provider for managing AI-driven music recommendations
class MusicAIProvider with ChangeNotifier {
  final MusicAIService _aiService = MusicAIService();
  
  // State variables
  bool _isLoading = false;
  List<RecommendationResult> _recommendationResults = [];
  String? _errorMessage;
  MusicTrack? _currentMusicSeed;
  Map<String, dynamic>? _currentLocationData;
  List<String> _userMoods = [];
  List<String> _availableMoods = [
    'Happy', 'Sad', 'Energetic', 'Relaxed', 'Focused',
    'Romantic', 'Nostalgic', 'Confident', 'Peaceful', 'Excited'
  ];
  
  // Analysis results
  Map<String, dynamic>? _locationAnalysis;
  
  // Getters
  bool get isLoading => _isLoading;
  List<RecommendationResult> get recommendations => _recommendationResults;
  String? get errorMessage => _errorMessage;
  MusicTrack? get currentMusicSeed => _currentMusicSeed;
  List<String> get userMoods => _userMoods;
  List<String> get availableMoods => _availableMoods;
  Map<String, dynamic>? get locationAnalysis => _locationAnalysis;
  
  // Initialize provider
  MusicAIProvider() {
    _initialize();
  }
  
  Future<void> _initialize() async {
    // Load initial data if needed
    try {
      _isLoading = true;
      notifyListeners();
      
      // Get popular moods for initial display
      await _loadPopularMoods();
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to initialize: $e';
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Load popular moods from backend
  Future<void> _loadPopularMoods() async {
    // In a real implementation, this would load data from backend
    // For now, we use predefined moods
  }
  
  // Set current seed track for recommendations
  void setMusicSeed(MusicTrack? track) {
    _currentMusicSeed = track;
    notifyListeners();
  }
  
  // Set location data for location-based recommendations
  void setLocationData(Map<String, dynamic> locationData) {
    _currentLocationData = locationData;
    notifyListeners();
  }
  
  // Add or remove a mood from selected moods
  void toggleMood(String mood) {
    if (_userMoods.contains(mood)) {
      _userMoods.remove(mood);
    } else {
      _userMoods.add(mood);
    }
    notifyListeners();
  }
  
  // Clear all moods
  void clearMoods() {
    _userMoods = [];
    notifyListeners();
  }
  
  // Generate recommendations based on parameters
  Future<List<RecommendationResult>> generateRecommendations({
    MusicTrack? seedTrack,
    List<String>? moods,
    Map<String, dynamic>? location,
    int limit = 10,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();
      
      // Use provided parameters or current state
      final trackToUse = seedTrack ?? _currentMusicSeed;
      final moodsToUse = moods ?? _userMoods;
      final locationToUse = location ?? _currentLocationData;
      
      // Build recommendation parameters
      final parameters = RecommendationParameters(
        seedTrack: trackToUse,
        moods: moodsToUse,
        location: locationToUse,
        limit: limit,
      );
      
      // Call service to generate recommendations
      final results = await _aiService.generateRecommendations(parameters);
      
      _recommendationResults = results;
      _isLoading = false;
      notifyListeners();
      
      return results;
    } catch (e) {
      _errorMessage = 'Failed to generate recommendations: $e';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }
  
  // Analyze music patterns by location
  Future<Map<String, dynamic>> analyzeLocationPatterns({
    required double latitude,
    required double longitude,
    double radius = 1.0, // km
    int timeframe = 30, // days
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();
      
      // Call service to analyze patterns
      final analysis = await _aiService.analyzeLocationMusicPatterns(
        latitude: latitude,
        longitude: longitude,
        radius: radius,
        timeframe: timeframe,
      );
      
      _locationAnalysis = analysis;
      _isLoading = false;
      notifyListeners();
      
      return analysis;
    } catch (e) {
      _errorMessage = 'Failed to analyze location patterns: $e';
      _isLoading = false;
      notifyListeners();
      return {'error': _errorMessage};
    }
  }
  
  // Find tracks matching a specific mood
  Future<List<MusicTrack>> findTracksByMood(List<String> moods) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();
      
      // Call service to find tracks by mood
      final tracks = await _aiService.findTracksByMood(moods);
      
      _isLoading = false;
      notifyListeners();
      
      return tracks;
    } catch (e) {
      _errorMessage = 'Failed to find tracks by mood: $e';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }
  
  // Helper method to rate a recommendation to improve future results
  Future<void> rateRecommendation({
    required String recommendationId,
    required int rating, // 1-5
  }) async {
    try {
      await _aiService.submitRecommendationFeedback(
        recommendationId: recommendationId,
        rating: rating,
      );
      
      // Optionally update local state if needed
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to submit feedback: $e';
      notifyListeners();
    }
  }
} 