import 'dart:math';
import 'package:uuid/uuid.dart';

import '../../../models/music_track.dart';
import '../models/recommendation_parameters.dart';
import '../models/recommendation_result.dart';
import '../models/track.dart';

/// Local recommendation engine for generating sample recommendations
/// This is used for demo/offline purposes when the API is not available
class RecommendationEngine {
  final Random _random = Random();
  final Uuid _uuid = const Uuid();
  
  /// Generate a list of recommendations based on the provided parameters
  Future<List<RecommendationResult>> generateRecommendations(
    RecommendationParameters parameters
  ) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1500));
    
    final List<RecommendationResult> results = [];
    final int count = parameters.limit;
    
    // Generate recommendations
    for (int i = 0; i < count; i++) {
      final result = _generateRecommendation(
        parameters: parameters,
        index: i,
      );
      results.add(result);
    }
    
    // Sort by confidence
    results.sort((a, b) => b.confidence.compareTo(a.confidence));
    
    return results;
  }
  
  /// Generate a single recommendation
  RecommendationResult _generateRecommendation({
    required RecommendationParameters parameters,
    required int index,
  }) {
    // Determine if this should be location-based, mood-based, or seed-based
    final isLocationBased = parameters.location != null && _random.nextBool();
    final isMoodBased = parameters.moods != null && parameters.moods!.isNotEmpty && _random.nextBool();
    final isSeedBased = parameters.seedTrack != null && _random.nextBool();
    
    // Create the track with appropriate characteristics
    final musicTrack = _createMusicTrack(
      seedTrack: parameters.seedTrack,
      moods: parameters.moods,
      index: index,
    );
    
    // Convert MusicTrack to Track
    final track = _convertToTrack(musicTrack);
    
    // Calculate confidence based on number of matching criteria
    final confidenceBase = 0.5 + (_random.nextDouble() * 0.3);
    double confidence = confidenceBase;
    
    // Adjust confidence based on criteria
    if (isLocationBased) confidence += 0.05;
    if (isMoodBased) confidence += 0.1;
    if (isSeedBased) confidence += 0.15;
    
    // Cap at 0.98 to avoid being "too perfect"
    confidence = min(confidence, 0.98);
    
    // Generate explanation
    final explanation = _generateExplanation(
      track: musicTrack,
      isLocationBased: isLocationBased,
      isMoodBased: isMoodBased, 
      isSeedBased: isSeedBased,
      parameters: parameters,
    );
    
    // Create factors that influenced this recommendation
    final factors = _generateFactors(
      track: musicTrack,
      isLocationBased: isLocationBased,
      isMoodBased: isMoodBased,
      isSeedBased: isSeedBased,
      parameters: parameters,
    );
    
    return RecommendationResult(
      track: track,
      confidence: confidence,
      explanation: explanation,
      metadata: factors,
      isLocationBased: isLocationBased,
      isMoodBased: isMoodBased,
      isSeedBased: isSeedBased,
    );
  }
  
  /// Convert MusicTrack to Track model
  Track _convertToTrack(MusicTrack musicTrack) {
    return Track(
      id: musicTrack.id,
      title: musicTrack.title,
      artist: musicTrack.artist,
      album: musicTrack.album,
      artworkUrl: musicTrack.albumArt,
      durationSeconds: (musicTrack.durationMs / 1000).round(),
      genres: musicTrack.genres,
      popularity: musicTrack.popularity,
      externalUrls: {
        musicTrack.serviceType: musicTrack.url,
      },
    );
  }
  
  /// Create a track with appropriate characteristics based on parameters
  MusicTrack _createMusicTrack({
    MusicTrack? seedTrack,
    List<String>? moods,
    required int index,
  }) {
    // If we have a seed track, create something similar
    if (seedTrack != null) {
      return MusicTrack(
        id: _uuid.v4(),
        title: _getSimilarTitle(seedTrack.title),
        artist: _getRelatedArtist(seedTrack.artist),
        album: 'Album ${_random.nextInt(10) + 1}',
        albumArt: 'https://picsum.photos/200/200?random=${_random.nextInt(1000)}',
        url: 'https://open.spotify.com/track/${_uuid.v4()}',
        service: 'Spotify',
        previewUrl: _random.nextBool() ? 'https://example.com/preview/${_uuid.v4()}' : null,
        serviceType: 'spotify',
        genres: seedTrack.genres,
        durationMs: seedTrack.durationMs + (_random.nextInt(60) - 30) * 1000,
        popularity: _random.nextInt(100),
        uri: 'spotify:track:${_uuid.v4()}',
      );
    }
    
    // If we have moods, create a track for those moods
    if (moods != null && moods.isNotEmpty) {
      final mood = moods[_random.nextInt(moods.length)];
      
      String title;
      List<String> genres;
      
      switch (mood.toLowerCase()) {
        case 'happy':
          title = _getRandomTitle(['Happy', 'Joy', 'Smile', 'Sunny', 'Bright']);
          genres = ['pop', 'dance'];
          break;
        case 'sad':
          title = _getRandomTitle(['Sad', 'Blue', 'Tears', 'Rain', 'Missing']);
          genres = ['indie', 'acoustic'];
          break;
        case 'energetic':
          title = _getRandomTitle(['Energy', 'Power', 'Fire', 'Pump', 'Boost']);
          genres = ['electronic', 'dance'];
          break;
        case 'relaxed':
          title = _getRandomTitle(['Calm', 'Peace', 'Relax', 'Chill', 'Breeze']);
          genres = ['ambient', 'chill'];
          break;
        default:
          title = 'Track #${_random.nextInt(100)}';
          genres = ['pop'];
      }
      
      return MusicTrack(
        id: _uuid.v4(),
        title: title,
        artist: _getRandomArtist(),
        album: 'Mood Collection Vol. ${_random.nextInt(5) + 1}',
        albumArt: 'https://picsum.photos/200/200?random=${_random.nextInt(1000)}',
        url: 'https://open.spotify.com/track/${_uuid.v4()}',
        service: 'Spotify',
        previewUrl: _random.nextBool() ? 'https://example.com/preview/${_uuid.v4()}' : null,
        serviceType: 'spotify',
        genres: genres,
        durationMs: 180000 + _random.nextInt(120000),
        popularity: _random.nextInt(100),
        uri: 'spotify:track:${_uuid.v4()}',
      );
    }
    
    // Default to a generic track
    return MusicTrack(
      id: _uuid.v4(),
      title: 'Recommended Track #${index + 1}',
      artist: _getRandomArtist(),
      album: 'Discovery Playlist',
      albumArt: 'https://picsum.photos/200/200?random=${_random.nextInt(1000)}',
      url: 'https://open.spotify.com/track/${_uuid.v4()}',
      service: 'Spotify',
      previewUrl: _random.nextBool() ? 'https://example.com/preview/${_uuid.v4()}' : null,
      serviceType: 'spotify',
      genres: ['pop', 'rock'],
      durationMs: 180000 + _random.nextInt(120000),
      popularity: _random.nextInt(100),
      uri: 'spotify:track:${_uuid.v4()}',
    );
  }
  
  /// Generate an explanation for why this track was recommended
  String _generateExplanation({
    required MusicTrack track,
    required bool isLocationBased,
    required bool isMoodBased,
    required bool isSeedBased,
    required RecommendationParameters parameters,
  }) {
    final List<String> reasons = [];
    
    if (isSeedBased && parameters.seedTrack != null) {
      reasons.add("Based on your interest in \"${parameters.seedTrack!.title}\" by ${parameters.seedTrack!.artist}");
    }
    
    if (isMoodBased && parameters.moods != null && parameters.moods!.isNotEmpty) {
      final mood = parameters.moods![_random.nextInt(parameters.moods!.length)];
      reasons.add("Matches your $mood mood");
    }
    
    if (isLocationBased && parameters.location != null) {
      reasons.add("Popular in this area");
    }
    
    if (track.genres.isNotEmpty) {
      reasons.add("Features ${track.genres.join(' and ')} elements");
    }
    
    if (reasons.isEmpty) {
      reasons.add("Recommended based on your listening patterns");
    }
    
    return reasons.join('. ') + '.';
  }
  
  /// Generate factors that influenced this recommendation
  Map<String, dynamic> _generateFactors({
    required MusicTrack track,
    required bool isLocationBased,
    required bool isMoodBased,
    required bool isSeedBased,
    required RecommendationParameters parameters,
  }) {
    final Map<String, dynamic> factors = {};
    
    // Track features
    factors['audio_features'] = {
      'energy': _randomFeatureValue(),
      'danceability': _randomFeatureValue(),
      'tempo': 80 + _random.nextInt(100),
      'valence': _randomFeatureValue(),
      'acousticness': _randomFeatureValue(),
    };
    
    // Seed match if applicable
    if (isSeedBased && parameters.seedTrack != null) {
      factors['seed_similarity'] = {
        'artist_match': _random.nextDouble() * 0.5 + 0.3,
        'genre_match': _random.nextDouble() * 0.6 + 0.3,
        'features_match': _random.nextDouble() * 0.7 + 0.2,
      };
    }
    
    // Mood match if applicable
    if (isMoodBased && parameters.moods != null && parameters.moods!.isNotEmpty) {
      final Map<String, double> moodScores = {};
      for (final mood in parameters.moods!) {
        moodScores[mood] = _random.nextDouble() * 0.5 + 0.4;
      }
      factors['mood_match'] = moodScores;
    }
    
    // Location popularity if applicable
    if (isLocationBased) {
      factors['location_popularity'] = {
        'plays_in_area': 10 + _random.nextInt(90),
        'pins_in_area': 5 + _random.nextInt(20),
        'local_rank': 1 + _random.nextInt(10),
      };
    }
    
    return factors;
  }
  
  // Helper methods
  
  double _randomFeatureValue() {
    return double.parse((_random.nextDouble()).toStringAsFixed(2));
  }
  
  String _getSimilarTitle(String originalTitle) {
    // Add a prefix or suffix to make it sound similar but different
    final prefixes = ['The ', 'New ', 'My ', 'Our '];
    final suffixes = [' (Remix)', ' Pt. 2', ' Again', ' 2023'];
    
    if (_random.nextBool()) {
      return prefixes[_random.nextInt(prefixes.length)] + originalTitle;
    } else {
      return originalTitle + suffixes[_random.nextInt(suffixes.length)];
    }
  }
  
  String _getRelatedArtist(String originalArtist) {
    // Create a related artist name
    final relations = [
      'feat.',
      'with',
      '&',
      'presents',
    ];
    
    if (_random.nextBool()) {
      final secondArtist = _getRandomArtist();
      final relation = relations[_random.nextInt(relations.length)];
      return '$originalArtist $relation $secondArtist';
    } else {
      return _getRandomArtist();
    }
  }
  
  String _getRandomTitle(List<String> themes) {
    final theme = themes[_random.nextInt(themes.length)];
    final nouns = ['Day', 'Night', 'Dream', 'Memory', 'Moment', 'Feeling', 'Soul', 'Heart', 'Light'];
    final noun = nouns[_random.nextInt(nouns.length)];
    
    if (_random.nextBool()) {
      return '$theme $noun';
    } else {
      return '$noun of $theme';
    }
  }
  
  String _getRandomArtist() {
    final firstNames = ['James', 'Sarah', 'Alex', 'Luna', 'Zane', 'Nova', 'Max', 'Ella'];
    final lastNames = ['Smith', 'Wave', 'Beat', 'Ryder', 'Star', 'Moon', 'Jones', 'King'];
    final bandWords = ['The', 'Electric', 'Neon', 'Lunar', 'Crystal', 'Sonic', 'Midnight', 'Echo'];
    
    if (_random.nextBool()) {
      // Solo artist
      return '${firstNames[_random.nextInt(firstNames.length)]} ${lastNames[_random.nextInt(lastNames.length)]}';
    } else {
      // Band name
      final word1 = bandWords[_random.nextInt(bandWords.length)];
      final word2 = lastNames[_random.nextInt(lastNames.length)];
      return 'The $word1 $word2';
    }
  }
} 