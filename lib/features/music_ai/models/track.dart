/// Represents a music track with its metadata
class Track {
  /// Unique identifier for the track
  final String id;
  
  /// Title of the track
  final String title;
  
  /// Artist name
  final String artist;
  
  /// Album name
  final String? album;
  
  /// URL to album artwork/cover image
  final String? artworkUrl;
  
  /// Track duration in seconds
  final int? durationSeconds;
  
  /// Release year
  final int? releaseYear;
  
  /// Music genres associated with this track
  final List<String>? genres;
  
  /// Track popularity score (0-100)
  final int? popularity;
  
  /// BPM (beats per minute) of the track
  final double? bpm;
  
  /// Key of the track (e.g., "C Major")
  final String? key;
  
  /// Energy level of the track (0.0-1.0)
  final double? energy;
  
  /// Danceability score (0.0-1.0)
  final double? danceability;
  
  /// Acoustic properties score (0.0-1.0)
  final double? acousticness;
  
  /// External URLs for this track (e.g., {spotify: "...", apple_music: "..."})
  final Map<String, String>? externalUrls;
  
  /// Additional track metadata
  final Map<String, dynamic>? metadata;

  const Track({
    required this.id,
    required this.title,
    required this.artist,
    this.album,
    this.artworkUrl,
    this.durationSeconds,
    this.releaseYear,
    this.genres,
    this.popularity,
    this.bpm,
    this.key,
    this.energy,
    this.danceability,
    this.acousticness,
    this.externalUrls,
    this.metadata,
  });
  
  /// Creates a copy of this Track with the given fields replaced
  Track copyWith({
    String? id,
    String? title,
    String? artist,
    String? album,
    String? artworkUrl,
    int? durationSeconds,
    int? releaseYear,
    List<String>? genres,
    int? popularity,
    double? bpm,
    String? key,
    double? energy,
    double? danceability,
    double? acousticness,
    Map<String, String>? externalUrls,
    Map<String, dynamic>? metadata,
  }) {
    return Track(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      artworkUrl: artworkUrl ?? this.artworkUrl,
      durationSeconds: durationSeconds ?? this.durationSeconds,
      releaseYear: releaseYear ?? this.releaseYear,
      genres: genres ?? this.genres,
      popularity: popularity ?? this.popularity,
      bpm: bpm ?? this.bpm,
      key: key ?? this.key,
      energy: energy ?? this.energy,
      danceability: danceability ?? this.danceability,
      acousticness: acousticness ?? this.acousticness,
      externalUrls: externalUrls ?? this.externalUrls,
      metadata: metadata ?? this.metadata,
    );
  }
  
  /// Factory constructor to create a Track from JSON
  factory Track.fromJson(Map<String, dynamic> json) {
    return Track(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      artist: json['artist'] ?? '',
      album: json['album'],
      artworkUrl: json['artworkUrl'],
      durationSeconds: json['durationSeconds'],
      releaseYear: json['releaseYear'],
      genres: json['genres'] != null 
        ? List<String>.from(json['genres']) 
        : null,
      popularity: json['popularity'],
      bpm: json['bpm'],
      key: json['key'],
      energy: json['energy'],
      danceability: json['danceability'],
      acousticness: json['acousticness'],
      externalUrls: json['externalUrls'] != null 
        ? Map<String, String>.from(json['externalUrls']) 
        : null,
      metadata: json['metadata'],
    );
  }
  
  /// Convert this Track to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'artist': artist,
      'album': album,
      'artworkUrl': artworkUrl,
      'durationSeconds': durationSeconds,
      'releaseYear': releaseYear,
      'genres': genres,
      'popularity': popularity,
      'bpm': bpm,
      'key': key,
      'energy': energy,
      'danceability': danceability,
      'acousticness': acousticness,
      'externalUrls': externalUrls,
      'metadata': metadata,
    };
  }
  
  /// Format track as "Artist - Title"
  String get displayName => '$artist - $title';
  
  /// Gets formatted duration string (mm:ss)
  String get formattedDuration {
    if (durationSeconds == null) return '--:--';
    final minutes = (durationSeconds! ~/ 60).toString().padLeft(2, '0');
    final seconds = (durationSeconds! % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }
  
  @override
  String toString() => displayName;
} 