import '../../../models/music_track.dart';

/// Parameters for generating AI music recommendations
class RecommendationParameters {
  /// Optional seed track to base recommendations on
  final MusicTrack? seedTrack;
  
  /// List of moods to match (e.g., "happy", "energetic")
  final List<String>? moods;
  
  /// Optional location data for location-based recommendations
  final Map<String, dynamic>? location;
  
  /// Optional genre preferences
  final List<String>? genres;
  
  /// Max number of recommendations to return
  final int limit;
  
  /// Optional time of day to consider
  final DateTime? timeOfDay;
  
  /// Optional acoustic features to match (tempo, energy, etc.)
  final Map<String, double>? acousticFeatures;
  
  RecommendationParameters({
    this.seedTrack,
    this.moods,
    this.location,
    this.genres,
    this.limit = 10,
    this.timeOfDay,
    this.acousticFeatures,
  });
  
  /// Convert to JSON for API requests
  Map<String, dynamic> toJson() {
    return {
      'seed_track': seedTrack?.toJson(),
      'moods': moods,
      'location': location,
      'genres': genres,
      'limit': limit,
      'time_of_day': timeOfDay?.toIso8601String(),
      'acoustic_features': acousticFeatures,
    };
  }
  
  /// Create a copy with some fields changed
  RecommendationParameters copyWith({
    MusicTrack? seedTrack,
    List<String>? moods,
    Map<String, dynamic>? location,
    List<String>? genres,
    int? limit,
    DateTime? timeOfDay,
    Map<String, double>? acousticFeatures,
  }) {
    return RecommendationParameters(
      seedTrack: seedTrack ?? this.seedTrack,
      moods: moods ?? this.moods,
      location: location ?? this.location,
      genres: genres ?? this.genres,
      limit: limit ?? this.limit,
      timeOfDay: timeOfDay ?? this.timeOfDay,
      acousticFeatures: acousticFeatures ?? this.acousticFeatures,
    );
  }
} 