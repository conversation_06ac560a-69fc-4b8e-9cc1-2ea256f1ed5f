import 'track.dart';

/// Represents a music recommendation result with confidence score and recommendation factors
class RecommendationResult {
  /// The recommended track
  final Track track;
  
  /// Confidence score (0.0 - 1.0) indicating how well this recommendation matches the user's criteria
  final double confidence;
  
  /// Whether this recommendation was influenced by user's location
  final bool isLocationBased;
  
  /// Whether this recommendation was influenced by selected moods
  final bool isMoodBased;
  
  /// Whether this recommendation was influenced by seed tracks
  final bool isSeedBased;
  
  /// Optional explanation for why this track was recommended
  final String? explanation;
  
  /// Additional metadata about the recommendation (can include feature weights, similarity scores, etc.)
  final Map<String, dynamic>? metadata;

  const RecommendationResult({
    required this.track,
    required this.confidence,
    this.isLocationBased = false,
    this.isMoodBased = false,
    this.isSeedBased = false,
    this.explanation,
    this.metadata,
  });
  
  /// Creates a copy of this RecommendationResult with the given fields replaced
  RecommendationResult copyWith({
    Track? track,
    double? confidence,
    bool? isLocationBased,
    bool? isMoodBased,
    bool? isSeedBased,
    String? explanation,
    Map<String, dynamic>? metadata,
  }) {
    return RecommendationResult(
      track: track ?? this.track,
      confidence: confidence ?? this.confidence,
      isLocationBased: isLocationBased ?? this.isLocationBased,
      isMoodBased: isMoodBased ?? this.isMoodBased, 
      isSeedBased: isSeedBased ?? this.isSeedBased,
      explanation: explanation ?? this.explanation,
      metadata: metadata ?? this.metadata,
    );
  }
  
  /// Factory constructor to create a RecommendationResult from JSON
  factory RecommendationResult.fromJson(Map<String, dynamic> json) {
    return RecommendationResult(
      track: Track.fromJson(json['track']),
      confidence: json['confidence'] ?? 0.0,
      isLocationBased: json['isLocationBased'] ?? false,
      isMoodBased: json['isMoodBased'] ?? false,
      isSeedBased: json['isSeedBased'] ?? false,
      explanation: json['explanation'],
      metadata: json['metadata'],
    );
  }
  
  /// Convert this RecommendationResult to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'track': track.toJson(),
      'confidence': confidence,
      'isLocationBased': isLocationBased,
      'isMoodBased': isMoodBased,
      'isSeedBased': isSeedBased,
      'explanation': explanation,
      'metadata': metadata,
    };
  }
  
  @override
  String toString() {
    return 'RecommendationResult(track: ${track.title}, confidence: $confidence, '
        'factors: [${_getFactors().join(", ")}])';
  }
  
  /// Helper method to get the list of active recommendation factors
  List<String> _getFactors() {
    final factors = <String>[];
    if (isLocationBased) factors.add('location');
    if (isMoodBased) factors.add('mood');
    if (isSeedBased) factors.add('seed');
    return factors.isEmpty ? ['generic'] : factors;
  }
} 