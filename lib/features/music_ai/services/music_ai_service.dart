import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:bop_maps/config/constants.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';

import '../../../models/music_track.dart';
import '../models/recommendation_parameters.dart';
import '../models/recommendation_result.dart';
import '../utils/recommendation_engine.dart';

/// Service that handles AI-driven music recommendation features
class MusicAIService {
  // Singleton pattern
  static final MusicAIService _instance = MusicAIService._internal();
  factory MusicAIService() => _instance;
  MusicAIService._internal();

  // API endpoints - would point to real endpoints in production
  static final String _baseUrl =
      '${AppConstants.baseApiUrl}/v1';
  static final String _recommendationsEndpoint = '$_baseUrl/recommendations';
  static final String _locationAnalysisEndpoint = '$_baseUrl/location/analyze';
  static final String _moodBasedEndpoint = '$_baseUrl/tracks/mood';
  static final String _feedbackEndpoint = '$_baseUrl/feedback';

  // Local fallback recommendation engine for offline/demo use
  final RecommendationEngine _localEngine = RecommendationEngine();

  /// Generate music recommendations based on parameters
  Future<List<RecommendationResult>> generateRecommendations(
      RecommendationParameters parameters) async {
    try {
      // For production, this would call the actual API
      // final response = await http.post(
      //   Uri.parse(_recommendationsEndpoint),
      //   headers: {'Content-Type': 'application/json'},
      //   body: jsonEncode(parameters.toJson()),
      // );

      // if (response.statusCode == 200) {
      //   final List<dynamic> data = jsonDecode(response.body);
      //   return data.map((json) => RecommendationResult.fromJson(json)).toList();
      // }

      // For demo, use local engine to generate recommendations
      return await _generateLocalRecommendations(parameters);
    } catch (e) {
      debugPrint('Error generating recommendations: $e');
      // Still use local engine as fallback
      return await _generateLocalRecommendations(parameters);
    }
  }

  /// Analyze music patterns for a specific location
  Future<Map<String, dynamic>> analyzeLocationMusicPatterns({
    required double latitude,
    required double longitude,
    double radius = 1.0,
    int timeframe = 30,
  }) async {
    try {
      // For production, this would call the actual API
      // final response = await http.get(
      //   Uri.parse('$_locationAnalysisEndpoint?lat=$latitude&lng=$longitude&radius=$radius&days=$timeframe'),
      // );

      // if (response.statusCode == 200) {
      //   return jsonDecode(response.body);
      // }

      // For demo, generate simulated location analysis
      return _generateSimulatedLocationAnalysis(latitude, longitude);
    } catch (e) {
      debugPrint('Error analyzing location patterns: $e');
      // Return simulated data as fallback
      return _generateSimulatedLocationAnalysis(latitude, longitude);
    }
  }

  /// Find tracks matching a specific mood
  Future<List<MusicTrack>> findTracksByMood(List<String> moods) async {
    try {
      // For production, this would call the actual API
      // final response = await http.post(
      //   Uri.parse(_moodBasedEndpoint),
      //   headers: {'Content-Type': 'application/json'},
      //   body: jsonEncode({'moods': moods}),
      // );

      // if (response.statusCode == 200) {
      //   final List<dynamic> data = jsonDecode(response.body);
      //   return data.map((json) => MusicTrack.fromJson(json)).toList();
      // }

      // For demo, generate simulated mood-based tracks
      return _generateSimulatedMoodTracks(moods);
    } catch (e) {
      debugPrint('Error finding tracks by mood: $e');
      // Return simulated data as fallback
      return _generateSimulatedMoodTracks(moods);
    }
  }

  /// Submit feedback on a recommendation to improve future results
  Future<bool> submitRecommendationFeedback({
    required String recommendationId,
    required int rating,
  }) async {
    try {
      // For production, this would call the actual API
      // final response = await http.post(
      //   Uri.parse(_feedbackEndpoint),
      //   headers: {'Content-Type': 'application/json'},
      //   body: jsonEncode({
      //     'recommendation_id': recommendationId,
      //     'rating': rating,
      //   }),
      // );

      // return response.statusCode == 200;

      // For demo, always return success
      return true;
    } catch (e) {
      debugPrint('Error submitting feedback: $e');
      return false;
    }
  }

  /// Generate recommendations locally for demo/offline mode
  Future<List<RecommendationResult>> _generateLocalRecommendations(
      RecommendationParameters parameters) async {
    // Use the local recommendation engine
    return _localEngine.generateRecommendations(parameters);
  }

  /// Generate simulated location analysis for demo
  Map<String, dynamic> _generateSimulatedLocationAnalysis(
      double latitude, double longitude) {
    // Create a realistic-looking simulated location analysis
    final random = Random();
    final genres = [
      {'name': 'Pop', 'percentage': 35 + random.nextInt(15)},
      {'name': 'Hip-Hop', 'percentage': 20 + random.nextInt(15)},
      {'name': 'R&B', 'percentage': 15 + random.nextInt(10)},
      {'name': 'Electronic', 'percentage': 10 + random.nextInt(10)},
      {'name': 'Rock', 'percentage': 5 + random.nextInt(10)},
    ];

    final moods = [
      {'name': 'Energetic', 'percentage': 40 + random.nextInt(20)},
      {'name': 'Happy', 'percentage': 30 + random.nextInt(20)},
      {'name': 'Relaxed', 'percentage': 20 + random.nextInt(15)},
      {'name': 'Melancholic', 'percentage': 10 + random.nextInt(10)},
    ];

    final popularArtists = [
      'Drake',
      'The Weeknd',
      'Dua Lipa',
      'Billie Eilish',
      'Bad Bunny',
    ];

    final hourlyActivity = List.generate(24, (hour) {
      return {
        'hour': hour,
        'activity': hour >= 8 && hour <= 23
            ? 10 + random.nextInt(90)
            : random.nextInt(20),
      };
    });

    return {
      'location': {
        'latitude': latitude,
        'longitude': longitude,
        'address': 'Near ${_getRandomLocation()}',
      },
      'total_pins': 50 + random.nextInt(200),
      'unique_users': 30 + random.nextInt(100),
      'genre_distribution': genres,
      'mood_distribution': moods,
      'popular_artists': popularArtists,
      'hourly_activity': hourlyActivity,
      'peak_hours': [
        12 + random.nextInt(2),
        17 + random.nextInt(3),
      ],
      'avg_song_duration': 180 + random.nextInt(60),
    };
  }

  /// Generate simulated mood-based tracks for demo
  List<MusicTrack> _generateSimulatedMoodTracks(List<String> moods) {
    final List<MusicTrack> tracks = [];
    final random = Random();

    // Generate 10 tracks that match the requested moods
    for (int i = 0; i < 10; i++) {
      final mood = moods[random.nextInt(moods.length)];
      tracks.add(_createTrackForMood(mood, i));
    }

    return tracks;
  }

  /// Create a track that matches a specific mood for demo
  MusicTrack _createTrackForMood(String mood, int index) {
    final random = Random();
    final uuid = const Uuid();

    // Map moods to appropriate track characteristics
    String title;
    String artist;
    List<String> genres;
    int popularity = 50 + random.nextInt(50);
    int durationMs = 180000 + random.nextInt(120000);

    switch (mood.toLowerCase()) {
      case 'happy':
        title = _getRandomHappyTitle();
        artist = _getRandomArtist();
        genres = ['pop', 'dance'];
        break;
      case 'sad':
        title = _getRandomSadTitle();
        artist = _getRandomArtist();
        genres = ['indie', 'acoustic'];
        break;
      case 'energetic':
        title = _getRandomEnergeticTitle();
        artist = _getRandomArtist();
        genres = ['electronic', 'dance'];
        break;
      case 'relaxed':
        title = _getRandomRelaxedTitle();
        artist = _getRandomArtist();
        genres = ['ambient', 'chill'];
        break;
      case 'focused':
        title = _getRandomFocusedTitle();
        artist = _getRandomArtist();
        genres = ['instrumental', 'electronic'];
        break;
      default:
        title = 'Song #${random.nextInt(100)}';
        artist = _getRandomArtist();
        genres = ['pop'];
    }

    return MusicTrack(
      id: uuid.v4(),
      title: title,
      artist: artist,
      album: 'Album ${random.nextInt(10) + 1}',
      albumArt: 'https://picsum.photos/200/200?random=${random.nextInt(1000)}',
      url: 'https://open.spotify.com/track/${uuid.v4()}',
      service: 'Spotify',
      serviceType: 'spotify',
      genres: genres,
      durationMs: durationMs,
      popularity: popularity,
      uri: 'spotify:track:${uuid.v4()}',
    );
  }

  // Helper methods to generate random content
  String _getRandomHappyTitle() {
    final titles = [
      'Sunny Day',
      'Good Vibes',
      'Happy Together',
      'Celebration',
      'Brighter Days',
      'Feeling Good',
      'Summer Nights',
      'Dancing Queen',
      'Happy Hour',
      'On Top of the World',
    ];
    return titles[Random().nextInt(titles.length)];
  }

  String _getRandomSadTitle() {
    final titles = [
      'Rainy Day',
      'Lost Without You',
      'Broken Heart',
      'Goodbye',
      'Missing You',
      'Memories',
      'The One That Got Away',
      'Tears in Heaven',
      'Lonely Night',
      'Bittersweet Symphony',
    ];
    return titles[Random().nextInt(titles.length)];
  }

  String _getRandomEnergeticTitle() {
    final titles = [
      'Power Up',
      'Adrenaline',
      'Party Time',
      'Unstoppable',
      'Fireworks',
      'Electric Feel',
      'On Fire',
      'Higher Ground',
      'Elevate',
      'Dynamite',
    ];
    return titles[Random().nextInt(titles.length)];
  }

  String _getRandomRelaxedTitle() {
    final titles = [
      'Calm Waters',
      'Peaceful Mind',
      'Easy Like Sunday Morning',
      'Daydream',
      'Tranquility',
      'Waves',
      'Gentle Breeze',
      'Sunset',
      'Meditation',
      'Deep Blue',
    ];
    return titles[Random().nextInt(titles.length)];
  }

  String _getRandomFocusedTitle() {
    final titles = [
      'Concentration',
      'Deep Work',
      'Flow State',
      'Mental Clarity',
      'Focus Point',
      'Study Session',
      'Mindfulness',
      'Precision',
      'The Zone',
      'Clarity',
    ];
    return titles[Random().nextInt(titles.length)];
  }

  String _getRandomArtist() {
    final artists = [
      'The Melodics',
      'Electric Dreams',
      'Lunar Waves',
      'Sunset Collective',
      'Urban Echo',
      'Crystal Sound',
      'Midnight Pulse',
      'Rhythm Section',
      'Neon Lights',
      'Harmony Heights',
    ];
    return artists[Random().nextInt(artists.length)];
  }

  String _getRandomLocation() {
    final locations = [
      'Central Park',
      'Downtown Square',
      'Main Street',
      'University Campus',
      'Beach Boardwalk',
      'Shopping Mall',
      'Concert Hall',
      'Coffee District',
      'Arts Center',
      'Riverside Park',
    ];
    return locations[Random().nextInt(locations.length)];
  }
}
