import 'package:bop_maps/providers/friends_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:provider/provider.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:geolocator/geolocator.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

// Config imports
import 'config/themes.dart';
import 'config/routes.dart';
import 'config/route_constants.dart';

// Provider imports
import 'providers/auth_provider.dart';
import 'providers/pin_provider.dart';
import 'providers/map_provider.dart';
import 'providers/music_provider.dart';
import 'providers/search_provider.dart';
import 'providers/map_settings_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/now_playing_provider.dart';
import 'providers/public_profile_provider.dart';
import 'providers/spotify_provider.dart';
import 'providers/gamification_provider.dart';
import 'providers/music_chat_provider.dart';
import 'providers/settings_provider.dart';
import 'providers/spotify_connection_provider.dart';
import 'providers/apple_music_provider.dart';
import 'providers/onesignal_provider.dart';
import 'providers/weekly_challenges_provider.dart';
import 'providers/skin_provider.dart';
import 'providers/notification_provider.dart';
import 'providers/user_stats_provider.dart';
import 'providers/user_provider.dart';
import 'providers/youtube_provider.dart';
import 'providers/recommendation_context_provider.dart';
import 'screens/search/ai_search/ai_search_provider.dart';
import 'services/notifications/notification_api_service.dart';
import 'services/notifications/onesignal_service.dart';

// Navigation
import 'widgets/navigation/app_navigation_system.dart';

// Screen imports
import 'screens/map/snapchat_style_map_screen.dart';

// Service imports
import 'services/gamification_service.dart';
import 'services/api_service.dart';
import 'services/auth_service.dart';
import 'services/music/apple_music_service.dart';
import 'services/gamification_integration_service.dart';
import 'services/api/weekly_challenges_api_service.dart';
import 'services/api/rank_service.dart';
import 'services/api/api_client.dart';
import 'services/achievement_websocket_service.dart';

// Feature modules
import 'features/indoor_scanning/indoor_scanning_module.dart';
import 'features/music_ai/music_ai_module.dart';

// Screen imports
import 'screens/auth/login_screen.dart';
import 'screens/map/map_screen.dart';
import 'screens/main_screen.dart';

// Services for early initialization
import 'services/location/location_manager.dart';
import 'services/location/location_service.dart';
import 'services/notifications/onesignal_service.dart';
import 'config/constants.dart';

Future<void> main() async {
  // This needs to be called first
  WidgetsFlutterBinding.ensureInitialized();
  
  // Configure debug logging for location initialization
  FlutterError.onError = (details) {
    FlutterError.presentError(details);
    print('CRITICAL ERROR: ${details.exception}');
  };
  
  // Load environment variables - make it resilient to missing .env file
  try {
    await dotenv.load(fileName: ".env");
    print("Environment variables loaded successfully");
  } catch (e) {
    print("Warning: No .env file found or failed to load environment variables: $e");
    // Continue execution without the .env file
  }

   final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
   final token = await _secureStorage.read(key: AppConstants.tokenKey);
  
  // OneSignal initialization is now handled by NotificationProvider after user authentication
  
  // PRE-INITIALIZE LOCATION SERVICES (Uber/Google Maps best practice)
  print("📍 Starting location services pre-initialization...");
  
  try {
    // Check if location services are enabled at startup
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    print("📍 Location services enabled: $serviceEnabled");
    
    // Check initial permission status
    LocationPermission permission = await Geolocator.checkPermission();
    print("📍 Initial location permission status: $permission");
    
    // Early initialize the LocationManager singleton
    final locationManager = LocationManager();
    // This will trigger permission requests and initial location fetch
    print("📍 LocationManager initialized: ${locationManager.hashCode}");
    
    // Try to get a location fix before showing the map
    // Timeout after 2 seconds to ensure app doesn't hang if location is slow
    try {
      print("📍 Attempting initial location fix (will timeout after 2 seconds)...");
      await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 2),
      ).then((position) {
        print("📍 STARTUP LOCATION: Lat: ${position.latitude}, Lng: ${position.longitude}");
      }).timeout(const Duration(seconds: 2));
    } catch (e) {
      print("📍 Initial location request timed out or failed, continuing startup: $e");
      // App will continue and location will be fetched later
    }
  } catch (e) {
    print("📍 Error during location pre-initialization: $e");
    // Continue app startup even if location fails
  }
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  print("🚀 App initialization complete, launching UI...");

  await SentryFlutter.init(
    (options) {
      options.dsn = 'https://<EMAIL>/4509770127638528';
      // Adds request headers and IP for users, for more info visit:
      // https://docs.sentry.io/platforms/dart/guides/flutter/data-management/data-collected/
      options.sendDefaultPii = true;
      // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
      // We recommend adjusting this value in production.
      options.tracesSampleRate = 1.0;
      // The sampling rate for profiling is relative to tracesSampleRate
      // Setting to 1.0 will profile 100% of sampled transactions:
      options.profilesSampleRate = 1.0;
      // Configure Session Replay
      options.replay.sessionSampleRate = 0.1;
      options.replay.onErrorSampleRate = 1.0;
    },
    appRunner: () => runApp(
      MultiProvider(
        providers: [
          // Add ApiService provider first
          Provider<ApiService>(
            create: (_) => ApiService(),
          ),
          
          // Add ApiClient provider
          Provider<ApiClient>(
            create: (_) => ApiClient(),
          ),
          
          // Add AuthService provider that depends on ApiService
          Provider<AuthService>(
            create: (context) => AuthService(context.read<ApiService>()),
          ),
          
          ChangeNotifierProvider(create: (_) => AuthProvider()),
          ChangeNotifierProvider(create: (_) => UserProvider()),
          ChangeNotifierProvider(create: (_) => PinProvider()),
          ChangeNotifierProvider(create: (_) => MapProvider()),
          ChangeNotifierProvider(create: (_) => MusicProvider()),
          ChangeNotifierProvider(create: (_) => MapSettingsProvider()),
          ChangeNotifierProvider(create: (_) => ThemeProvider()),
          ChangeNotifierProvider(create: (_) => NowPlayingProvider()),
          ChangeNotifierProvider(create: (_) => FriendsProvider()),
          ChangeNotifierProvider(create: (_) => PublicProfileProvider()),
          ChangeNotifierProvider(create: (_) => SpotifyProvider()),
          ChangeNotifierProvider(
            create: (context) {
              final apiService = context.read<ApiService>();
              final authService = context.read<AuthService>();
              return AppleMusicProvider(
                AppleMusicService(apiService, authService),
              );
            },
          ),
          ChangeNotifierProxyProvider2<AuthProvider, SpotifyProvider, MusicChatProvider>(
            create: (context) => MusicChatProvider(
              context.read<AuthProvider>(),
              context.read<SpotifyProvider>(),
            ),
            update: (context, authProvider, spotifyProvider, previous) => 
              previous ?? MusicChatProvider(authProvider, spotifyProvider),
          ),
          ChangeNotifierProvider(
            create: (_) {
              final settings = SettingsProvider();
              settings.init(); // Initialize settings
              return settings;
            },
          ),
          ChangeNotifierProvider(create: (_) => SpotifyConnectionProvider()),
          ChangeNotifierProvider(create: (_) => OneSignalProvider()),
          
          // SkinProvider with ApiClient dependency
          ChangeNotifierProvider(
            create: (context) => SkinProvider(
              context.read<ApiClient>(),
            ),
          ),
          
          // GamificationProvider with dependencies
          ChangeNotifierProxyProvider<AuthProvider, GamificationProvider>(
            create: (context) {
              final apiService = context.read<ApiService>();
              final authService = context.read<AuthService>();
              return GamificationProvider(
                GamificationService(apiService, authService),
                RankService(apiService, authService),
                AchievementWebSocketService(),
              );
            },
            update: (context, authProvider, previous) {
              final apiService = context.read<ApiService>();
              final authService = context.read<AuthService>();
              return GamificationProvider(
                GamificationService(apiService, authService),
                RankService(apiService, authService),
                AchievementWebSocketService(),
              );
            },
          ),
          
          // SearchProvider with dependencies
          ChangeNotifierProxyProvider2<MapProvider, PinProvider, SearchProvider>(
            create: (context) => SearchProvider(
              mapProvider: Provider.of<MapProvider>(context, listen: false),
              pinProvider: Provider.of<PinProvider>(context, listen: false),
            ),
            update: (context, mapProvider, pinProvider, previous) =>
              SearchProvider(
                mapProvider: mapProvider,
                pinProvider: pinProvider,
              ),
          ),
          
          // AISearchProvider for shared AI recommendations across all screens
          ChangeNotifierProvider(
            create: (context) => AISearchProvider(context, initialCategory: 'all'),
          ),
          
          // Feature module providers
          ...MusicAIModule.getProviders(),
          
          // WeeklyChallengesProvider with dependencies
          ChangeNotifierProvider(
            create: (context) => WeeklyChallengesProvider(
              WeeklyChallengesApiService(
                context.read<ApiService>(),
                context.read<AuthService>(),
              ),
            ),
          ),

          // NotificationProvider with dependencies
          ChangeNotifierProvider(
            create: (context) {
              final apiClient = context.read<ApiClient>();
              final notificationApiService = NotificationApiService(apiClient);
              final oneSignalService = OneSignalService(notificationApiService);
              
              return NotificationProvider(
                apiService: notificationApiService,
                oneSignalService: oneSignalService,
              );
            },
          ),

          // UserStatsProvider with dependencies
          ChangeNotifierProvider(create: (_) => UserStatsProvider()),

          // YouTubeProvider
          ChangeNotifierProvider(create: (_) => YouTubeProvider()),

          // RecommendationContextProvider
          ChangeNotifierProvider(create: (_) => RecommendationContextProvider()),
        ],
        child: Consumer2<AuthProvider, ThemeProvider>(
          builder: (context, authProvider, themeProvider, _) {
            // Initialize gamification WebSocket when user is authenticated
            if (authProvider.isAuthenticated) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                GamificationIntegrationService.initializeGamification(context);
              });
            }
          
            // Get initial route
            final initialRoute = authProvider.isAuthenticated 
                ? RouteConstants.map 
                : RouteConstants.aiOnboarding;
            
            if (!themeProvider.isThemeLoaded) {
              // Show a blank screen or splash while theme loads
              return const SizedBox.shrink();
            }
            return PiPMaterialApp(
              title: 'BOPMaps',
              navigatorKey: AppNavigationSystem().navigatorKey,
              // Add route observer to detect screen navigation
              navigatorObservers: [
                SnapchatStyleMapScreenState.routeObserver,
              ],
              theme: AppTheme.lightTheme.copyWith(
                colorScheme: AppTheme.lightTheme.colorScheme.copyWith(
                  primary: themeProvider.primaryColor,
                ),
              ),
              darkTheme: AppTheme.darkTheme.copyWith(
                colorScheme: AppTheme.darkTheme.colorScheme.copyWith(
                  primary: themeProvider.primaryColor,
                ),
              ),
              themeMode: themeProvider.themeMode,
              debugShowCheckedModeBanner: false,
              initialRoute: initialRoute,
              onGenerateRoute: AppRouter.onGenerateRoute,
            );
          },
        ),
      ),
    ),
  );
} 