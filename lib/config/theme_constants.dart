import 'package:flutter/material.dart';

// Light Theme Colors
const primaryColorLight = Color(0xFFFF80AB);  // Pink color 
const secondaryColorLight = Color(0xFFFF9FBC); // Lighter pink (was blue)
const accentColorLight = Color(0xFFFF7D54);   // Orange accent color
const backgroundColorLight = Color(0xFFF8F9FA);
const surfaceColorLight = Colors.white;
const errorColorLight = Color(0xFFEB5757);

// Dark Theme Colors
const primaryColorDark = Color(0xFFFF80AB);   // Same pink for consistency (was blue)
const secondaryColorDark = Color(0xFFE05C89); // Deeper pink (was blue)
const accentColorDark = Color(0xFFFF7D54);    // Same orange for consistency
const backgroundColorDark = Color(0xFF121212);
const surfaceColorDark = Color(0xFF1E1E1E);
const errorColorDark = Color(0xFFEB5757);

// Theme Color Options
const themeColorPink = Color(0xFFFF80AB);     // Default pink
const themeColorCyan = Color(0xFF09C3DB);     // Deep cyan
const themeColorBlue = Color(0xFF3B7DD8);     // Rich blue

// Common Brand Colors
const musicPinColor = Color(0xFFFF7D54);     // Orange for music pins
const friendPinColor = Color(0xFFFF9FBC);    // Light pink for friend pins (was blue)
const collectedPinColor = Color(0xFF34D399); // Green for collected pins

// Gradient Colors
const gradientStart = Color(0xFFFF80AB);     // Pink (was blue)
const gradientEnd = Color(0xFFFF9FBC);       // Lighter pink (was blue)

// Opacity values for consistent UI elements
const double disabledOpacity = 0.5;
const double inactiveOpacity = 0.7; 