import 'package:flutter/material.dart';
import 'route_constants.dart';

// Auth screens
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';

// Onboarding screen
import '../screens/onboarding/onboarding_screen.dart';
import '../screens/onboarding/onboarding_screen_spotify.dart';
import '../screens/onboarding/ai_onboarding_screen.dart';

// Map screens
import '../screens/map/map_screen.dart';
import '../screens/map/pin_details_screen.dart';

// Music screens
import '../screens/music/track_select_screen.dart';
import '../screens/music/player_screen.dart';

// Profile screens
import '../screens/profile/profile_screen.dart';
import '../screens/profile/settings_screen.dart';
import '../screens/profile/apple_music_screen.dart';

// Social screens
import '../screens/friends/friends_screen.dart';
import '../screens/social/activity_feed_screen.dart';

// Gamification screens
import '../screens/gamification/challenges_screen.dart';
import '../screens/skins/skin_collection_screen.dart';

// Feature modules
import '../features/indoor_scanning/indoor_scanning_module.dart';
import '../features/music_ai/music_ai_module.dart';

// Providers
import '../providers/auth_provider.dart';
import 'package:provider/provider.dart';

class AppRouter {
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    // First check if the route is handled by a feature module
    
    // Check Indoor Scanning module routes
    final indoorScanRoutes = IndoorScanningModule.getRoutes();
    if (indoorScanRoutes.containsKey(settings.name)) {
      return MaterialPageRoute(
        builder: indoorScanRoutes[settings.name]!,
        settings: settings,
      );
    }
    
    // Check Music AI module routes
    final musicAIRoutes = MusicAIModule.getRoutes();
    if (musicAIRoutes.containsKey(settings.name)) {
      return MaterialPageRoute(
        builder: musicAIRoutes[settings.name]!,
        settings: settings,
      );
    }
    
    // Then check app-level routes
    switch (settings.name) {
      // Auth routes
      case RouteConstants.login:
        return MaterialPageRoute(builder: (_) => const LoginScreen());
      case RouteConstants.register:
        return MaterialPageRoute(builder: (_) => const RegisterScreen());
      case RouteConstants.onboarding:
        return MaterialPageRoute(builder: (_) => const OnboardingScreen());
      case RouteConstants.onboardingSpotify:
        return MaterialPageRoute(builder: (_) => const OnboardingScreenSpotify());
      case RouteConstants.aiOnboarding:
        return MaterialPageRoute(builder: (_) => const AIOnboardingScreen());
      
      // Map routes
      case RouteConstants.map:
        return MaterialPageRoute(
          builder: (context) {
            print('🗺️ Navigating to map screen');
            
            // Check authentication state
            final authProvider = Provider.of<AuthProvider>(context, listen: false);
            final isAuth = authProvider.isAuthenticated;
            
            print('🔐 Auth check for map route - isAuthenticated: $isAuth');
            
            // If not authenticated and not already on login screen
            if (!isAuth && ModalRoute.of(context)?.settings.name != RouteConstants.aiOnboarding) {
              print('⚠️ User not authenticated, redirecting to AI onboarding');
              return const AIOnboardingScreen();
            }
            
            // If authenticated or already on login screen, proceed to map
            return const MapScreen();
          },
          settings: settings,
          maintainState: true,
        );
      case RouteConstants.pinDetails:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => PinDetailsScreen(pin: args['pin']),
        );
      
      // Music routes
      case RouteConstants.trackSelect:
        return MaterialPageRoute(builder: (_) => const TrackSelectScreen());
      case RouteConstants.player:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => PlayerScreen(track: args['track']),
        );
      
      // Profile routes
      case RouteConstants.profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());
      case RouteConstants.settings:
        return MaterialPageRoute(builder: (_) => const SettingsScreen());
      case RouteConstants.appleMusic:
        return MaterialPageRoute(builder: (_) => const AppleMusicScreen());
      
      // Social routes
      case RouteConstants.friends:
        return MaterialPageRoute(builder: (_) => const FriendsScreen());
      case RouteConstants.activity:
        return MaterialPageRoute(builder: (_) => const ActivityFeedScreen());
      
      // Gamification routes
      case RouteConstants.challenges:
        return MaterialPageRoute(builder: (_) => const ChallengesScreen());
      
      // Skins route
      case RouteConstants.skins:
        return MaterialPageRoute(builder: (_) => const SkinCollectionScreen());
      
      // Default route (404)
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(
              child: Text(
                '404 - Page not found\nRoute: ${settings.name}',
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
    }
  }
} 