import 'package:flutter/foundation.dart';

/// Feature flags to control experimental features
class FeatureFlags {
  /// Controls whether vector tile support is enabled
  static const bool enableVectorTiles = true;
  
  /// Controls whether vector tiles should be used as primary source when available
  static const bool preferVectorTiles = false;
  
  /// Maximum area in km² before forcing vector tiles (if enabled)
  static const double forceVectorTilesAreaThreshold = 4.0;
  
  /// Debug mode for vector tiles
  static const bool vectorTileDebugMode = kDebugMode;
  
  /// Controls parallel loading of both OSM and vector tiles for comparison
  static const bool enableParallelLoading = true;
  
  /// Memory cache size for vector tiles in MB
  static const int vectorTileCacheSize = 50;
  
  /// Disk cache size for vector tiles in MB
  static const int vectorTileDiskCacheSize = 200;
  
  /// Maximum concurrent tile requests
  static const int maxConcurrentTileRequests = 6;
  
  /// Enable hardware acceleration for vector tile rendering
  static const bool enableHardwareAcceleration = true;
} 