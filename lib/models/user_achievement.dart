import 'achievement.dart';

class UserAchievement {
  final int id;
  final int userId;
  final Achievement achievement;
  final DateTime? completedAt;
  final Map<String, dynamic>? progress;
  final DateTime createdAt;

  const UserAchievement({
    required this.id,
    required this.userId,
    required this.achievement,
    this.completedAt,
    this.progress,
    required this.createdAt,
  });

  factory UserAchievement.fromJson(Map<String, dynamic> json) {
    // Safely parse dates
    DateTime? completedDate;
    try {
      if (json['completed_at'] != null) {
        completedDate = DateTime.parse(json['completed_at'].toString());
      }
    } catch (e) {
      completedDate = null;
    }
    
    // Ensure we have a valid createdAt date
    DateTime createdDate;
    try {
      if (json['created_at'] != null) {
        createdDate = DateTime.parse(json['created_at'].toString());
      } else {
        createdDate = DateTime.now();
      }
    } catch (e) {
      createdDate = DateTime.now();
    }
    
    // Safely parse progress map
    Map<String, dynamic>? progressMap;
    try {
      if (json['progress'] != null) {
        progressMap = Map<String, dynamic>.from(json['progress']);
      }
    } catch (e) {
      progressMap = null;
    }
    
    return UserAchievement(
      id: json['id'] as int? ?? 0,
      userId: json['user'] as int? ?? 0,
      achievement: Achievement.fromJson(json['achievement'] ?? {}),
      completedAt: completedDate,
      progress: progressMap,
      createdAt: createdDate,
    );
  }

  // Factory constructor for easier creation with defaults
  factory UserAchievement.create({
    required int id,
    required int userId,
    required Achievement achievement,
    DateTime? completedAt,
    Map<String, dynamic>? progress,
    DateTime? createdAt,
  }) {
    return UserAchievement(
      id: id,
      userId: userId,
      achievement: achievement,
      completedAt: completedAt,
      progress: progress,
      createdAt: createdAt ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': userId,
      'achievement': achievement.toJson(),
      'completed_at': completedAt?.toIso8601String(),
      'progress': progress,
      'created_at': createdAt.toIso8601String(),
    };
  }

  UserAchievement copyWith({
    int? id,
    int? userId,
    Achievement? achievement,
    DateTime? completedAt,
    Map<String, dynamic>? progress,
    DateTime? createdAt,
  }) {
    return UserAchievement(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      achievement: achievement ?? this.achievement,
      completedAt: completedAt ?? this.completedAt,
      progress: progress ?? this.progress,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  bool get isCompleted => completedAt != null;

  /// Calculate progress percentage based on achievement criteria and current progress
  double calculateProgressPercentage() {
    return achievement.copyWith(progress: progress).calculateProgressPercentage();
  }

  /// Get human readable progress text
  String getProgressText() {
    if (isCompleted) return 'Completed';
    
    final percentage = calculateProgressPercentage();
    if (percentage == 0) return 'Not started';
    
    return '${percentage.toStringAsFixed(0)}% complete';
  }
} 