import 'package:flutter/material.dart';

class UserActivity {
  final String userId;
  final String displayName;
  final String profilePicUrl;
  final ActivityStatus status;
  final String? currentSong;
  final String? currentArtist;
  final DateTime lastActive;

  const UserActivity({
    required this.userId,
    required this.displayName,
    required this.profilePicUrl,
    required this.status,
    this.currentSong,
    this.currentArtist,
    required this.lastActive,
  });

  bool get isListeningToMusic => currentSong != null && currentArtist != null;
  bool get isActive => status == ActivityStatus.online || isListeningToMusic;
  
  String get statusText {
    if (isListeningToMusic) {
      return 'Listening to $currentSong by $currentArtist';
    }
    switch (status) {
      case ActivityStatus.online:
        return 'Online';
      case ActivityStatus.offline:
        return 'Last seen ${_getTimeAgo()}';
      case ActivityStatus.away:
        return 'Away';
    }
  }

  String _getTimeAgo() {
    final now = DateTime.now();
    final difference = now.difference(lastActive);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${difference.inDays ~/ 7}w ago';
    }
  }
}

enum ActivityStatus {
  online,
  offline,
  away,
} 