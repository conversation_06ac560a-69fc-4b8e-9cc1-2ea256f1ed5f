import 'package:json_annotation/json_annotation.dart';

part 'notification_model.g.dart';

@JsonSerializable()
class NotificationModel {
  @Json<PERSON><PERSON>(name: 'id')
  final String id;
  
  @Json<PERSON>ey(name: 'notification_type')
  final NotificationType type;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'category')
  final NotificationCategory category;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'priority')
  final NotificationPriority priority;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'title')
  final String title;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message')
  final String message;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime timestamp;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_read')
  final bool isRead;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'image_url')
  final String? imageUrl;
  
  @J<PERSON><PERSON><PERSON>(name: 'action_data')
  final Map<String, dynamic>? actionData;
  
  final String? onesignalId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'read_at')
  final DateTime? readAt;

  const NotificationModel({
    required this.id,
    required this.type,
    required this.category,
    required this.priority,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.isRead,
    this.imageUrl,
    this.actionData,
    this.onesignalId,
    this.readAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    // Handle the id conversion from int to string if needed
    if (json['id'] is int) {
      json['id'] = json['id'].toString();
    }
    
    return _$NotificationModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$NotificationModelToJson(this);

  NotificationModel copyWith({
    String? id,
    NotificationType? type,
    NotificationCategory? category,
    NotificationPriority? priority,
    String? title,
    String? message,
    DateTime? timestamp,
    bool? isRead,
    String? imageUrl,
    Map<String, dynamic>? actionData,
    String? onesignalId,
    DateTime? readAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      type: type ?? this.type,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      title: title ?? this.title,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      imageUrl: imageUrl ?? this.imageUrl,
      actionData: actionData ?? this.actionData,
      onesignalId: onesignalId ?? this.onesignalId,
      readAt: readAt ?? this.readAt,
    );
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, type: $type, title: $title, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum NotificationType {
  // Map & Pin related
  @JsonValue('pin_like')
  pinLike,
  @JsonValue('pin_comment')
  pinComment,
  @JsonValue('pin_trending')
  pinTrending,
  @JsonValue('friend_nearby')
  friendNearby,
  @JsonValue('pin_milestone')
  pinMilestone,
  @JsonValue('fresh_pins_found')
  freshPinsFound,
  
  // Social & Friends
  @JsonValue('friend_request')
  friendRequest,
  @JsonValue('friend_accepted')
  friendAccepted,
  @JsonValue('music_chat')
  musicChat,
  @JsonValue('activity_digest')
  activityDigest,
  @JsonValue('live_listening')
  liveListening,
  @JsonValue('weekly_digest')
  weeklyDigest,
  
  // Music
  @JsonValue('new_release')
  newRelease,
  @JsonValue('favorite_artist_update')
  favoriteArtistUpdate,
  @JsonValue('daily_mix')
  dailyMix,
  @JsonValue('weekly_recommendation')
  weeklyRecommendation,
  @JsonValue('music_sync')
  musicSync,
  @JsonValue('ai_recommendation')
  aiRecommendation,
  
  // Challenges & Gamification
  @JsonValue('challenge_complete')
  challengeComplete,
  @JsonValue('challenge_progress')
  challengeProgress,
  @JsonValue('challenge_available')
  challengeAvailable,
  @JsonValue('level_up')
  levelUp,
  @JsonValue('achievement_unlocked')
  achievementUnlocked,
  @JsonValue('xp_earned')
  xpEarned,
  
  // Collections & Playlists
  @JsonValue('collection_update')
  collectionUpdate,
  @JsonValue('collection_milestone')
  collectionMilestone,
  @JsonValue('collaborative_update')
  collaborativeUpdate,
  @JsonValue('playlist_shared')
  playlistShared,
  
  // Explore & Discovery
  @JsonValue('new_ar_pins')
  newArPins,
  @JsonValue('seasonal_drop')
  seasonalDrop,
  @JsonValue('event_available')
  eventAvailable,
  @JsonValue('trending_in_city')
  trendingInCity,
  @JsonValue('new_pins_nearby')
  newPinsNearby,
  @JsonValue('seasonal_event')
  seasonalEvent,
  
  // Skins & Customization
  @JsonValue('skin_unlocked')
  skinUnlocked,
  @JsonValue('limited_skin_available')
  limitedSkinAvailable,
  @JsonValue('customization_reminder')
  customizationReminder,
  
  // System & General
  @JsonValue('general')
  general,
  @JsonValue('system_update')
  systemUpdate,
  @JsonValue('retention_reminder')
  retentionReminder,
  @JsonValue('welcome_message')
  welcomeMessage,
  @JsonValue('unread_reminder')
  unreadReminder,
}

enum NotificationCategory {
  @JsonValue('all')
  all,
  @JsonValue('map')
  map,
  @JsonValue('social')
  social,
  @JsonValue('music')
  music,
  @JsonValue('gamification')
  gamification,
  @JsonValue('collection')
  collection,
  @JsonValue('exploration')
  exploration,
  @JsonValue('customization')
  customization,
  @JsonValue('general')
  general,
}

enum NotificationPriority {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      // Map & Pin related
      case NotificationType.pinLike:
        return 'Pin Liked';
      case NotificationType.pinComment:
        return 'Pin Comment';
      case NotificationType.pinTrending:
        return 'Pin Trending';
      case NotificationType.friendNearby:
        return 'Friend Nearby';
      case NotificationType.pinMilestone:
        return 'Pin Milestone';
      case NotificationType.freshPinsFound:
        return 'Fresh Pins Found';
      
      // Social & Friends
      case NotificationType.friendRequest:
        return 'Friend Request';
      case NotificationType.friendAccepted:
        return 'Friend Accepted';
      case NotificationType.musicChat:
        return 'Music Chat';
      case NotificationType.activityDigest:
        return 'Activity Digest';
      case NotificationType.liveListening:
        return 'Live Listening';
      case NotificationType.weeklyDigest:
        return 'Weekly Digest';
      
      // Music
      case NotificationType.newRelease:
        return 'New Release';
      case NotificationType.favoriteArtistUpdate:
        return 'Artist Update';
      case NotificationType.dailyMix:
        return 'Daily Mix';
      case NotificationType.weeklyRecommendation:
        return 'Weekly Mix';
      case NotificationType.musicSync:
        return 'Music Sync';
      case NotificationType.aiRecommendation:
        return 'AI Recommendation';
      
      // Challenges & Gamification
      case NotificationType.challengeComplete:
        return 'Challenge Complete';
      case NotificationType.challengeProgress:
        return 'Challenge Progress';
      case NotificationType.challengeAvailable:
        return 'New Challenge';
      case NotificationType.levelUp:
        return 'Level Up';
      case NotificationType.achievementUnlocked:
        return 'Achievement';
      case NotificationType.xpEarned:
        return 'XP Earned';
      
      // Collections & Playlists
      case NotificationType.collectionUpdate:
        return 'Collection Update';
      case NotificationType.collectionMilestone:
        return 'Collection Milestone';
      case NotificationType.collaborativeUpdate:
        return 'Collaborative Update';
      case NotificationType.playlistShared:
        return 'Playlist Shared';
      
      // Explore & Discovery
      case NotificationType.newArPins:
        return 'New AR Pins';
      case NotificationType.seasonalDrop:
        return 'Seasonal Drop';
      case NotificationType.eventAvailable:
        return 'Event Available';
      case NotificationType.trendingInCity:
        return 'Trending in City';
      case NotificationType.newPinsNearby:
        return 'New Pins Nearby';
      case NotificationType.seasonalEvent:
        return 'Seasonal Event';
      
      // Skins & Customization
      case NotificationType.skinUnlocked:
        return 'Skin Unlocked';
      case NotificationType.limitedSkinAvailable:
        return 'Limited Skin';
      case NotificationType.customizationReminder:
        return 'Customization';
      
      // System & General
      case NotificationType.general:
        return 'General';
      case NotificationType.systemUpdate:
        return 'System Update';
      case NotificationType.retentionReminder:
        return 'Come Back';
      case NotificationType.welcomeMessage:
        return 'Welcome';
      case NotificationType.unreadReminder:
        return 'Unread Reminder';
    }
  }

  NotificationCategory get defaultCategory {
    switch (this) {
      // Map & Pin related
      case NotificationType.pinLike:
      case NotificationType.pinComment:
      case NotificationType.pinTrending:
      case NotificationType.friendNearby:
      case NotificationType.pinMilestone:
      case NotificationType.freshPinsFound:
        return NotificationCategory.map;
      
      // Social & Friends
      case NotificationType.friendRequest:
      case NotificationType.friendAccepted:
      case NotificationType.musicChat:
      case NotificationType.activityDigest:
      case NotificationType.liveListening:
      case NotificationType.weeklyDigest:
        return NotificationCategory.social;
      
      // Music
      case NotificationType.aiRecommendation:
      case NotificationType.newRelease:
      case NotificationType.favoriteArtistUpdate:
      case NotificationType.dailyMix:
      case NotificationType.weeklyRecommendation:
      case NotificationType.musicSync:
        return NotificationCategory.music;
      
      // Challenges & Gamification
      case NotificationType.challengeComplete:
      case NotificationType.challengeProgress:
      case NotificationType.challengeAvailable:
      case NotificationType.levelUp:
      case NotificationType.achievementUnlocked:
      case NotificationType.xpEarned:
        return NotificationCategory.gamification;
      
      // Collections & Playlists
      case NotificationType.collectionUpdate:
      case NotificationType.collectionMilestone:
      case NotificationType.collaborativeUpdate:
      case NotificationType.playlistShared:
        return NotificationCategory.collection;
      
      // Explore & Discovery
      case NotificationType.newArPins:
      case NotificationType.seasonalDrop:
      case NotificationType.eventAvailable:
      case NotificationType.trendingInCity:
      case NotificationType.newPinsNearby:
      case NotificationType.seasonalEvent:
        return NotificationCategory.exploration;
      
      // Skins & Customization
      case NotificationType.skinUnlocked:
      case NotificationType.limitedSkinAvailable:
      case NotificationType.customizationReminder:
        return NotificationCategory.customization;
      
      // System & General
      case NotificationType.general:
      case NotificationType.systemUpdate:
      case NotificationType.retentionReminder:
      case NotificationType.welcomeMessage:
      case NotificationType.unreadReminder:
        return NotificationCategory.general;
    }
  }
}

// API Response Models
@JsonSerializable()
class NotificationListResponse {
  @JsonKey(name: 'results')
  final List<NotificationModel> results;
  
  @JsonKey(name: 'count')
  final int totalCount;
  
  @JsonKey(name: 'unread_count')
  final int unreadCount;

  const NotificationListResponse({
    required this.results,
    required this.totalCount,
    required this.unreadCount,
  });

  factory NotificationListResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationListResponseToJson(this);
}

@JsonSerializable()
class SuccessResponse {
  final bool success;
  final String message;

  const SuccessResponse({
    required this.success,
    required this.message,
  });

  factory SuccessResponse.fromJson(Map<String, dynamic> json) =>
      _$SuccessResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SuccessResponseToJson(this);
}

@JsonSerializable()
class BulkActionResponse {
  final bool success;
  @JsonKey(name: 'updated_count')
  final int? updatedCount;
  final String message;

  const BulkActionResponse({
    required this.success,
    this.updatedCount,
    required this.message,
  });

  factory BulkActionResponse.fromJson(Map<String, dynamic> json) {
    // Extract count from message if updatedCount is not provided
    int? count = json['updated_count'] as int?;
    if (count == null && json['message'] != null) {
      final message = json['message'] as String;
      final regex = RegExp(r'Deleted (\d+)');
      final match = regex.firstMatch(message);
      if (match != null) {
        count = int.tryParse(match.group(1) ?? '0');
      }
    }
    
    return BulkActionResponse(
      success: json['success'] as bool,
      updatedCount: count,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() => _$BulkActionResponseToJson(this);
}

@JsonSerializable()
class NotificationStatsResponse {
  final int totalNotifications;
  final int unreadNotifications;
  final Map<String, int> categoryCounts;
  final Map<String, int> unreadCategoryCounts;

  const NotificationStatsResponse({
    required this.totalNotifications,
    required this.unreadNotifications,
    required this.categoryCounts,
    required this.unreadCategoryCounts,
  });

  factory NotificationStatsResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationStatsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationStatsResponseToJson(this);
}

 