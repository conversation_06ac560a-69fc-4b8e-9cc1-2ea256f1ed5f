import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'pin_skin.dart';

class Achievement {
  final int id;
  final String name;
  final String description;
  final String icon;
  final Map<String, dynamic> criteria;
  final PinSkin? rewardSkin;
  final DateTime? completedAt;
  final Map<String, dynamic>? progress;
  final bool isCompleted;
  final double progressPercentage;
  final int points;
  final String category;
  final DateTime createdAt;
  final int xpReward;

  // Using a default DateTime constant to avoid runtime DateTime.now() calls
  static final DateTime _defaultDate = DateTime(2023, 1, 1);

  const Achievement({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.criteria,
    this.rewardSkin,
    this.completedAt,
    this.progress,
    this.isCompleted = false,
    this.progressPercentage = 0.0,
    this.points = 100,
    this.category = 'General',
    required this.createdAt,
    this.xpReward = 100,
  });
  
  // Factory constructor for mocks and easier creation with default createdAt
  factory Achievement.create({
    required int id,
    required String name,
    required String description,
    required String icon,
    required Map<String, dynamic> criteria,
    PinSkin? rewardSkin,
    DateTime? completedAt,
    Map<String, dynamic>? progress,
    bool isCompleted = false,
    double progressPercentage = 0.0,
    int points = 100,
    String category = 'General',
    DateTime? createdAt,
    int xpReward = 100,
  }) {
    return Achievement(
      id: id,
      name: name,
      description: description,
      icon: icon,
      criteria: criteria,
      rewardSkin: rewardSkin,
      completedAt: completedAt,
      progress: progress,
      isCompleted: isCompleted,
      progressPercentage: progressPercentage,
      points: points,
      category: category,
      createdAt: createdAt ?? DateTime.now(),
      xpReward: xpReward,
    );
  }

  // Convert string icon to IconData
  IconData get iconData {
    switch (icon) {
      case 'mic':
        return Icons.mic;
      case 'music_note':
        return Icons.music_note;
      case 'location_on':
        return Icons.location_on;
      case 'favorite':
        return Icons.favorite;
      case 'emoji_events':
        return Icons.emoji_events;
      case 'star':
        return Icons.star;
      case 'local_activity':
        return Icons.local_activity;
      case 'people':
        return Icons.people;
      default:
        return Icons.emoji_events;
    }
  }

  factory Achievement.fromJson(Map<String, dynamic> json) {
    // Handle createdAt date parsing
    DateTime createdDate;
    
    try {
      if (json['created_at'] != null) {
        createdDate = DateTime.parse(json['created_at'].toString());
      } else {
        createdDate = DateTime.now();
      }
    } catch (e) {
      // Use current time if parsing fails
      createdDate = DateTime.now();
    }
    
    // Handle completedAt date parsing
    DateTime? completedDate;
    
    try {
      if (json['completed_at'] != null) {
        completedDate = DateTime.parse(json['completed_at'].toString());
      }
    } catch (e) {
      completedDate = null;
    }
    
    // Safely parse progress map
    Map<String, dynamic>? progressMap;
    try {
      if (json['progress'] != null) {
        progressMap = Map<String, dynamic>.from(json['progress']);
      }
    } catch (e) {
      progressMap = null;
    }
    
    // Safely parse criteria map
    Map<String, dynamic> criteriaMap;
    try {
      if (json['criteria'] != null) {
        criteriaMap = Map<String, dynamic>.from(json['criteria']);
      } else {
        criteriaMap = {};
      }
    } catch (e) {
      criteriaMap = {};
    }
    
    return Achievement(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? 'Unknown Achievement',
      description: json['description'] as String? ?? 'No description available',
      icon: json['icon'] as String? ?? 'emoji_events',
      criteria: criteriaMap,
      rewardSkin: _parseRewardSkin(json),
      completedAt: completedDate,
      progress: progressMap,
      isCompleted: _parseIsCompleted(json),
      progressPercentage: _parseProgressPercentage(json),
      points: json['points'] as int? ?? 100,
      category: json['category'] as String? ?? 'General',
      createdAt: createdDate,
      xpReward: json['xp_reward'] as int? ?? 100,
    );
  }

  static PinSkin? _parseRewardSkin(Map<String, dynamic> json) {
    try {
      if (json['reward_skin_details'] != null) {
        return PinSkin.fromJson(json['reward_skin_details']);
      }
      if (json['reward_skin'] != null && json['reward_skin'] is Map) {
        return PinSkin.fromJson(json['reward_skin']);
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return null;
  }

  static bool _parseIsCompleted(Map<String, dynamic> json) {
    try {
      final isCompleted = json['is_completed'];
      if (isCompleted is bool) {
        return isCompleted;
      }
      if (isCompleted is String) {
        return isCompleted.toLowerCase() == 'true' || isCompleted == '1';
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return false;
  }

  static double _parseProgressPercentage(Map<String, dynamic> json) {
    try {
      final progressPercentage = json['progress_percentage'];
      if (progressPercentage is num) {
        return progressPercentage.toDouble();
      }
      if (progressPercentage is String) {
        return double.tryParse(progressPercentage) ?? 0.0;
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'criteria': criteria,
      'reward_skin': rewardSkin?.toJson(),
      'completed_at': completedAt?.toIso8601String(),
      'progress': progress,
      'is_completed': isCompleted,
      'progress_percentage': progressPercentage,
      'points': points,
      'category': category,
      'created_at': createdAt.toIso8601String(),
      'xp_reward': xpReward,
    };
  }

  Achievement copyWith({
    int? id,
    String? name,
    String? description,
    String? icon,
    Map<String, dynamic>? criteria,
    PinSkin? rewardSkin,
    DateTime? completedAt,
    Map<String, dynamic>? progress,
    bool? isCompleted,
    double? progressPercentage,
    int? points,
    String? category,
    DateTime? createdAt,
    int? xpReward,
  }) {
    return Achievement(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      criteria: criteria ?? this.criteria,
      rewardSkin: rewardSkin ?? this.rewardSkin,
      completedAt: completedAt ?? this.completedAt,
      progress: progress ?? this.progress,
      isCompleted: isCompleted ?? this.isCompleted,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      points: points ?? this.points,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      xpReward: xpReward ?? this.xpReward,
    );
  }

  /// Calculate progress percentage based on criteria and current progress
  double calculateProgressPercentage() {
    if (criteria.isEmpty) return 0.0;
    
    double totalProgress = 0.0;
    int criteriaCount = 0;
    
    for (String key in criteria.keys) {
      final requiredValue = criteria[key];
      final currentValue = progress?[key] ?? 0;
      
      if (requiredValue is num && requiredValue > 0) {
        final progressRatio = (currentValue / requiredValue).clamp(0.0, 1.0);
        totalProgress += progressRatio;
        criteriaCount++;
      }
    }
    
    return criteriaCount > 0 ? (totalProgress / criteriaCount) * 100 : 0.0;
  }

  /// Get human readable progress text
  String getProgressText() {
    if (isCompleted) return 'Completed';
    
    final percentage = calculateProgressPercentage();
    if (percentage == 0) return 'Not started';
    
    return '${percentage.toStringAsFixed(0)}% complete';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Achievement &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          description == other.description &&
          icon == other.icon &&
          _mapEquals(criteria, other.criteria) &&
          rewardSkin == other.rewardSkin &&
          completedAt == other.completedAt &&
          _mapEquals(progress, other.progress) &&
          isCompleted == other.isCompleted &&
          progressPercentage == other.progressPercentage &&
          points == other.points &&
          category == other.category &&
          createdAt == other.createdAt &&
          xpReward == other.xpReward;

  // Helper method to compare maps
  bool _mapEquals(Map<String, dynamic>? map1, Map<String, dynamic>? map2) {
    if (map1 == null && map2 == null) return true;
    if (map1 == null || map2 == null) return false;
    if (map1.length != map2.length) return false;
    
    for (final key in map1.keys) {
      if (!map2.containsKey(key) || map1[key] != map2[key]) {
        return false;
      }
    }
    return true;
  }

  @override
  int get hashCode =>
      id.hashCode ^
      name.hashCode ^
      description.hashCode ^
      icon.hashCode ^
      criteria.hashCode ^
      rewardSkin.hashCode ^
      completedAt.hashCode ^
      progress.hashCode ^
      isCompleted.hashCode ^
      progressPercentage.hashCode ^
      points.hashCode ^
      category.hashCode ^
      createdAt.hashCode ^
      xpReward.hashCode;
} 