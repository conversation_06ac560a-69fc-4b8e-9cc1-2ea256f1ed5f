import 'package:flutter/material.dart';

/// Represents a user rank level in the game
class UserRank {
  final String id;
  final String name;
  final String emoji;
  final String badgeDescription;
  final String visualDescription;
  final int requiredLevel;
  final Color primaryColor;
  final Color backgroundColor;
  final bool hasTrailEffect;
  final bool hasProfileEffect;
  
  const UserRank({
    required this.id,
    required this.name,
    required this.emoji,
    required this.badgeDescription,
    required this.visualDescription,
    required this.requiredLevel,
    required this.primaryColor,
    required this.backgroundColor,
    this.hasTrailEffect = false,
    this.hasProfileEffect = false,
  });
  
  /// Get all available ranks in order
  static List<UserRank> get allRanks => [
    basementBopper,
    selector,
    tastemaker,
    trendsetter,
    icon,
    architect,
    legend,
  ];
  
  /// Get rank for a given level
  static UserRank getRankForLevel(int level) {
    debugPrint('🎯 Getting rank for level $level');
    final rank = allRanks.lastWhere(
      (rank) => level >= rank.requiredLevel,
      orElse: () => basementBopper,
    );
    debugPrint('📊 Selected rank: ${rank.name} (required level: ${rank.requiredLevel})');
    return rank;
  }
  
  /// Basement Bopper - Level 1 (0 XP)
  static const basementBopper = UserRank(
    id: 'basement_bopper',
    name: 'Basement Bopper',
    emoji: '🎧',
    badgeDescription: 'Gritty badge — vinyl & concrete texture',
    visualDescription: 'Matte dark background, flickering pin light. Looks raw and DIY.',
    requiredLevel: 1,
    primaryColor: Color(0xFF2D2D2D),
    backgroundColor: Color(0xFF1A1A1A),
  );
  
  /// Selector - Level 2 (500 XP)
  static const selector = UserRank(
    id: 'selector',
    name: 'Selector',
    emoji: '🎛️',
    badgeDescription: 'Animated waveform badge',
    visualDescription: 'Thin rotating audio dial. Hover pulses with static-like interference.',
    requiredLevel: 2,
    primaryColor: Color(0xFF4A90E2),
    backgroundColor: Color(0xFF2C3E50),
    hasTrailEffect: true,
  );
  
  /// Tastemaker - Level 3 (1,500 XP)
  static const tastemaker = UserRank(
    id: 'tastemaker',
    name: 'Tastemaker',
    emoji: '🌀',
    badgeDescription: 'Swirling motion badge',
    visualDescription: 'Fluid motion, soft glow. Gives a "taste vortex" feeling.',
    requiredLevel: 3,
    primaryColor: Color(0xFF9B59B6),
    backgroundColor: Color(0xFF34495E),
    hasTrailEffect: true,
  );
  
  /// Trendsetter - Level 4 (3,500 XP)
  static const trendsetter = UserRank(
    id: 'trendsetter',
    name: 'Trendsetter',
    emoji: '📡',
    badgeDescription: 'Radiating pulse icon',
    visualDescription: 'Animated radar rings. Icon subtly animates across the map when they drop.',
    requiredLevel: 4,
    primaryColor: Color(0xFFE74C3C),
    backgroundColor: Color(0xFF2C3E50),
    hasTrailEffect: true,
    hasProfileEffect: true,
  );
  
  /// Icon - Level 5 (7,000 XP)
  static const icon = UserRank(
    id: 'icon',
    name: 'Icon',
    emoji: '🔥',
    badgeDescription: 'Glowing flame icon',
    visualDescription: 'Flame flickers in 3D. Creates trail of light where drops have landed.',
    requiredLevel: 5,
    primaryColor: Color(0xFFE67E22),
    backgroundColor: Color(0xFF34495E),
    hasTrailEffect: true,
    hasProfileEffect: true,
  );
  
  /// Architect - Level 6 (12,000 XP)
  static const architect = UserRank(
    id: 'architect',
    name: 'Architect',
    emoji: '🧠',
    badgeDescription: 'Constellation badge',
    visualDescription: 'Floating pins connected with motion lines. Dynamic orbit effect on profile.',
    requiredLevel: 6,
    primaryColor: Color(0xFF1ABC9C),
    backgroundColor: Color(0xFF2C3E50),
    hasTrailEffect: true,
    hasProfileEffect: true,
  );
  
  /// Legend - Level 7 (20,000 XP)
  static const legend = UserRank(
    id: 'legend',
    name: 'Legend',
    emoji: '👑',
    badgeDescription: 'Crown + golden echo loop',
    visualDescription: 'Crown slowly rotates. Profile glows with gold aura. Map echoes softly from past pins.',
    requiredLevel: 7,
    primaryColor: Color(0xFFFFD700),
    backgroundColor: Color(0xFF2C3E50),
    hasTrailEffect: true,
    hasProfileEffect: true,
  );
} 