import 'pin_interaction.dart';

class PinEngagement {
  final int pinId;
  final EngagementCounts counts;
  final double engagementRate;
  final int totalInteractions;
  final List<PinInteraction> recentActivity;
  final bool isOwner;
  final DetailedEngagement? detailedEngagement; // Add detailed engagement

  PinEngagement({
    required this.pinId,
    required this.counts,
    required this.engagementRate,
    required this.totalInteractions,
    required this.recentActivity,
    required this.isOwner,
    this.detailedEngagement,
  });

  factory PinEngagement.fromJson(Map<String, dynamic> json) {
    final pinId = json['pin_id'];
    return PinEngagement(
      pinId: pinId,
      counts: EngagementCounts.fromJson(json['counts']),
      engagementRate: (json['engagement_rate'] ?? 0.0).toDouble(),
      totalInteractions: json['total_interactions'] ?? 0,
      recentActivity: (json['recent_activity'] as List<dynamic>? ?? [])
          .map((item) {
            // Add pin_id context to each activity item since API doesn't include it
            final activityJson = Map<String, dynamic>.from(item);
            activityJson['pin_id'] = pinId;
            // Generate a unique ID if missing (using index + timestamp)
            if (activityJson['id'] == null) {
              activityJson['id'] = DateTime.now().millisecondsSinceEpoch;
            }
            return PinInteraction.fromJson(activityJson);
          })
          .toList(),
      isOwner: json['is_owner'] ?? false,
      detailedEngagement: json['detailed_engagement'] != null 
          ? DetailedEngagement.fromJson(json['detailed_engagement'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pin_id': pinId,
      'counts': counts.toJson(),
      'engagement_rate': engagementRate,
      'total_interactions': totalInteractions,
      'recent_activity': recentActivity.map((activity) => activity.toJson()).toList(),
      'is_owner': isOwner,
      'detailed_engagement': detailedEngagement?.toJson(),
    };
  }
}

class DetailedEngagement {
  final List<UserEngagementDetails> upvotes;
  final List<UserEngagementDetails> downvotes;
  final List<UserEngagementDetails> views;
  final List<CommentDetails> comments;

  DetailedEngagement({
    required this.upvotes,
    required this.downvotes,
    required this.views,
    required this.comments,
  });

  factory DetailedEngagement.fromJson(Map<String, dynamic> json) {
    return DetailedEngagement(
      upvotes: (json['upvotes'] as List<dynamic>? ?? [])
          .map((item) => UserEngagementDetails.fromJson(item))
          .toList(),
      downvotes: (json['downvotes'] as List<dynamic>? ?? [])
          .map((item) => UserEngagementDetails.fromJson(item))
          .toList(),
      views: (json['views'] as List<dynamic>? ?? [])
          .map((item) => UserEngagementDetails.fromJson(item))
          .toList(),
      comments: (json['comments'] as List<dynamic>? ?? [])
          .map((item) => CommentDetails.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'upvotes': upvotes.map((item) => item.toJson()).toList(),
      'downvotes': downvotes.map((item) => item.toJson()).toList(),
      'views': views.map((item) => item.toJson()).toList(),
      'comments': comments.map((item) => item.toJson()).toList(),
    };
  }

  // Get all likes (treating upvotes as likes)
  List<UserEngagementDetails> get likes => upvotes;
}

class UserEngagementDetails {
  final int userId;
  final String username;
  final String? firstName;
  final String? lastName;
  final String? profilePic;
  final DateTime engagedAt;

  UserEngagementDetails({
    required this.userId,
    required this.username,
    this.firstName,
    this.lastName,
    this.profilePic,
    required this.engagedAt,
  });

  factory UserEngagementDetails.fromJson(Map<String, dynamic> json) {
    return UserEngagementDetails(
      userId: json['user_id'],
      username: json['username'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      profilePic: json['profile_pic'],
      engagedAt: DateTime.parse(json['voted_at'] ?? json['viewed_at'] ?? json['engaged_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'username': username,
      'first_name': firstName,
      'last_name': lastName,
      'profile_pic': profilePic,
      'engaged_at': engagedAt.toIso8601String(),
    };
  }

  String get displayName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    return username;
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(engagedAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'just now';
    }
  }
}

class CommentDetails {
  final int commentId;
  final String text;
  final int userId;
  final String username;
  final String? firstName;
  final String? lastName;
  final String? profilePic;
  final DateTime commentedAt;
  final bool isEdited;

  CommentDetails({
    required this.commentId,
    required this.text,
    required this.userId,
    required this.username,
    this.firstName,
    this.lastName,
    this.profilePic,
    required this.commentedAt,
    this.isEdited = false,
  });

  factory CommentDetails.fromJson(Map<String, dynamic> json) {
    return CommentDetails(
      commentId: json['comment_id'],
      text: json['text'],
      userId: json['user_id'],
      username: json['username'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      profilePic: json['profile_pic'],
      commentedAt: DateTime.parse(json['commented_at']),
      isEdited: json['is_edited'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'comment_id': commentId,
      'text': text,
      'user_id': userId,
      'username': username,
      'first_name': firstName,
      'last_name': lastName,
      'profile_pic': profilePic,
      'commented_at': commentedAt.toIso8601String(),
      'is_edited': isEdited,
    };
  }

  String get displayName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    return username;
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(commentedAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'just now';
    }
  }
}

class EngagementCounts {
  final int likes;
  final int views;
  final int collects;
  final int shares;
  final int comments;
  final int dislikes; // Add dislikes field to match API

  EngagementCounts({
    required this.likes,
    required this.views,
    required this.collects,
    required this.shares,
    required this.comments,
    this.dislikes = 0, // Default to 0
  });

  factory EngagementCounts.fromJson(Map<String, dynamic> json) {
    return EngagementCounts(
      // Handle both 'likes' and 'dislikes' from API - use dislikes as likes for now
      likes: json['likes'] ?? json['dislikes'] ?? 0,
      views: json['views'] ?? 0,
      collects: json['collects'] ?? 0,
      shares: json['shares'] ?? 0,
      comments: json['comments'] ?? 0,
      dislikes: json['dislikes'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'likes': likes,
      'views': views,
      'collects': collects,
      'shares': shares,
      'comments': comments,
      'dislikes': dislikes,
    };
  }

  int get total => likes + views + collects + shares + comments;

  /// Get the most popular interaction type
  String get topInteractionType {
    final interactions = {
      'likes': likes,
      'views': views,
      'collects': collects,
      'shares': shares,
      'comments': comments,
    };

    return interactions.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }
} 