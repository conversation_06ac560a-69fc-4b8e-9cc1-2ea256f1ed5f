class ExplorationSettings {
  final int fastExpansionRate;
  final int secondLevelExpansionRate;
  final int backgroundExpansionRate;
  final int continuousDiscoveryRate;
  final int primaryTargetsCount;
  final int secondLevelTargetsCount;
  final int backgroundTargetsCount;
  final int discoveryQueueTarget;
  final int artistsPerPage;
  final int tracksPerArtist;
  final Duration backgroundExpansionInterval;
  final String mode;

  const ExplorationSettings({
    required this.fastExpansionRate,
    required this.secondLevelExpansionRate,
    required this.backgroundExpansionRate,
    required this.continuousDiscoveryRate,
    required this.primaryTargetsCount,
    required this.secondLevelTargetsCount,
    required this.backgroundTargetsCount,
    required this.discoveryQueueTarget,
    required this.artistsPerPage,
    required this.tracksPerArtist,
    required this.backgroundExpansionInterval,
    required this.mode,
  });

  /// Factory constructor for Conservative mode
  factory ExplorationSettings.conservative() => const ExplorationSettings(
        fastExpansionRate: 8,
        secondLevelExpansionRate: 5,
        backgroundExpansionRate: 6,
        continuousDiscoveryRate: 4,
        primaryTargetsCount: 3,
        secondLevelTargetsCount: 2,
        backgroundTargetsCount: 1,
        discoveryQueueTarget: 30,
        artistsPerPage: 6,
        tracksPerArtist: 20,
        backgroundExpansionInterval: Duration(seconds: 60),
        mode: 'conservative',
      );

  /// Factory constructor for Normal mode (default)
  factory ExplorationSettings.normal() => const ExplorationSettings(
        fastExpansionRate: 15,
        secondLevelExpansionRate: 10,
        backgroundExpansionRate: 12,
        continuousDiscoveryRate: 8,
        primaryTargetsCount: 5,
        secondLevelTargetsCount: 3,
        backgroundTargetsCount: 2,
        discoveryQueueTarget: 50,
        artistsPerPage: 8,
        tracksPerArtist: 25,
        backgroundExpansionInterval: Duration(seconds: 30),
        mode: 'normal',
      );

  /// Factory constructor for Aggressive mode
  factory ExplorationSettings.aggressive() => const ExplorationSettings(
        fastExpansionRate: 25,
        secondLevelExpansionRate: 18,
        backgroundExpansionRate: 20,
        continuousDiscoveryRate: 15,
        primaryTargetsCount: 8,
        secondLevelTargetsCount: 5,
        backgroundTargetsCount: 3,
        discoveryQueueTarget: 80,
        artistsPerPage: 12,
        tracksPerArtist: 35,
        backgroundExpansionInterval: Duration(seconds: 15),
        mode: 'aggressive',
      );

  /// Factory constructor for Insane mode
  factory ExplorationSettings.insane() => const ExplorationSettings(
        fastExpansionRate: 40,
        secondLevelExpansionRate: 30,
        backgroundExpansionRate: 35,
        continuousDiscoveryRate: 25,
        primaryTargetsCount: 10,
        secondLevelTargetsCount: 8,
        backgroundTargetsCount: 5,
        discoveryQueueTarget: 150,
        artistsPerPage: 16,
        tracksPerArtist: 50,
        backgroundExpansionInterval: Duration(seconds: 10),
        mode: 'insane',
      );

  /// Factory constructor for default settings (Normal mode)
  factory ExplorationSettings.defaults() => ExplorationSettings.normal();

  /// Create ExplorationSettings from mode string
  factory ExplorationSettings.fromMode(String mode) {
    switch (mode.toLowerCase()) {
      case 'conservative':
        return ExplorationSettings.conservative();
      case 'normal':
        return ExplorationSettings.normal();
      case 'aggressive':
        return ExplorationSettings.aggressive();
      case 'insane':
        return ExplorationSettings.insane();
      default:
        return ExplorationSettings.normal();
    }
  }
 
 /// Get all available exploration modes
  static List<ExplorationModeInfo> get availableModes => [
        ExplorationModeInfo(
          mode: 'conservative',
          name: 'Conservative',
          emoji: '🐌',
          description: 'Slower, focused discovery that stays close to your preferences',
          bestFor: 'Users who prefer familiar music with minimal surprises',
          technicalDetails: 'Fewer artists explored, longer intervals, focused recommendations',
          settings: ExplorationSettings.conservative(),
        ),
        ExplorationModeInfo(
          mode: 'normal',
          name: 'Normal',
          emoji: '⚡',
          description: 'Balanced discovery that mixes familiar and new music',
          bestFor: 'Most users who want a good balance of discovery and familiarity',
          technicalDetails: 'Standard exploration rates with moderate discovery speed',
          settings: ExplorationSettings.normal(),
        ),
        ExplorationModeInfo(
          mode: 'aggressive',
          name: 'Aggressive',
          emoji: '🚀',
          description: 'Maximum discovery speed with lots of new artists and genres',
          bestFor: 'Music enthusiasts who love discovering new artists',
          technicalDetails: 'High exploration rates, shorter intervals, broad recommendations',
          settings: ExplorationSettings.aggressive(),
        ),
        ExplorationModeInfo(
          mode: 'insane',
          name: 'Insane',
          emoji: '🔥',
          description: 'Infinite discovery mode - constantly finding new music',
          bestFor: 'Power users who want the most diverse recommendations possible',
          technicalDetails: 'Maximum exploration rates, continuous discovery, vast music pool',
          settings: ExplorationSettings.insane(),
        ),
      ];

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() => {
        'fastExpansionRate': fastExpansionRate,
        'secondLevelExpansionRate': secondLevelExpansionRate,
        'backgroundExpansionRate': backgroundExpansionRate,
        'continuousDiscoveryRate': continuousDiscoveryRate,
        'primaryTargetsCount': primaryTargetsCount,
        'secondLevelTargetsCount': secondLevelTargetsCount,
        'backgroundTargetsCount': backgroundTargetsCount,
        'discoveryQueueTarget': discoveryQueueTarget,
        'artistsPerPage': artistsPerPage,
        'tracksPerArtist': tracksPerArtist,
        'backgroundExpansionIntervalSeconds': backgroundExpansionInterval.inSeconds,
        'mode': mode,
      };

  /// Create from JSON
  factory ExplorationSettings.fromJson(Map<String, dynamic> json) =>
      ExplorationSettings(
        fastExpansionRate: json['fastExpansionRate'] ?? 15,
        secondLevelExpansionRate: json['secondLevelExpansionRate'] ?? 10,
        backgroundExpansionRate: json['backgroundExpansionRate'] ?? 12,
        continuousDiscoveryRate: json['continuousDiscoveryRate'] ?? 8,
        primaryTargetsCount: json['primaryTargetsCount'] ?? 5,
        secondLevelTargetsCount: json['secondLevelTargetsCount'] ?? 3,
        backgroundTargetsCount: json['backgroundTargetsCount'] ?? 2,
        discoveryQueueTarget: json['discoveryQueueTarget'] ?? 50,
        artistsPerPage: json['artistsPerPage'] ?? 8,
        tracksPerArtist: json['tracksPerArtist'] ?? 25,
        backgroundExpansionInterval: Duration(
          seconds: json['backgroundExpansionIntervalSeconds'] ?? 30,
        ),
        mode: json['mode'] ?? 'normal',
      );

  /// Create a copy with updated values
  ExplorationSettings copyWith({
    int? fastExpansionRate,
    int? secondLevelExpansionRate,
    int? backgroundExpansionRate,
    int? continuousDiscoveryRate,
    int? primaryTargetsCount,
    int? secondLevelTargetsCount,
    int? backgroundTargetsCount,
    int? discoveryQueueTarget,
    int? artistsPerPage,
    int? tracksPerArtist,
    Duration? backgroundExpansionInterval,
    String? mode,
  }) =>
      ExplorationSettings(
        fastExpansionRate: fastExpansionRate ?? this.fastExpansionRate,
        secondLevelExpansionRate: secondLevelExpansionRate ?? this.secondLevelExpansionRate,
        backgroundExpansionRate: backgroundExpansionRate ?? this.backgroundExpansionRate,
        continuousDiscoveryRate: continuousDiscoveryRate ?? this.continuousDiscoveryRate,
        primaryTargetsCount: primaryTargetsCount ?? this.primaryTargetsCount,
        secondLevelTargetsCount: secondLevelTargetsCount ?? this.secondLevelTargetsCount,
        backgroundTargetsCount: backgroundTargetsCount ?? this.backgroundTargetsCount,
        discoveryQueueTarget: discoveryQueueTarget ?? this.discoveryQueueTarget,
        artistsPerPage: artistsPerPage ?? this.artistsPerPage,
        tracksPerArtist: tracksPerArtist ?? this.tracksPerArtist,
        backgroundExpansionInterval: backgroundExpansionInterval ?? this.backgroundExpansionInterval,
        mode: mode ?? this.mode,
      );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExplorationSettings &&
          runtimeType == other.runtimeType &&
          fastExpansionRate == other.fastExpansionRate &&
          secondLevelExpansionRate == other.secondLevelExpansionRate &&
          backgroundExpansionRate == other.backgroundExpansionRate &&
          continuousDiscoveryRate == other.continuousDiscoveryRate &&
          primaryTargetsCount == other.primaryTargetsCount &&
          secondLevelTargetsCount == other.secondLevelTargetsCount &&
          backgroundTargetsCount == other.backgroundTargetsCount &&
          discoveryQueueTarget == other.discoveryQueueTarget &&
          artistsPerPage == other.artistsPerPage &&
          tracksPerArtist == other.tracksPerArtist &&
          backgroundExpansionInterval == other.backgroundExpansionInterval &&
          mode == other.mode;

  @override
  int get hashCode =>
      fastExpansionRate.hashCode ^
      secondLevelExpansionRate.hashCode ^
      backgroundExpansionRate.hashCode ^
      continuousDiscoveryRate.hashCode ^
      primaryTargetsCount.hashCode ^
      secondLevelTargetsCount.hashCode ^
      backgroundTargetsCount.hashCode ^
      discoveryQueueTarget.hashCode ^
      artistsPerPage.hashCode ^
      tracksPerArtist.hashCode ^
      backgroundExpansionInterval.hashCode ^
      mode.hashCode;

  @override
  String toString() => 'ExplorationSettings(mode: $mode, fastExpansionRate: $fastExpansionRate, discoveryQueueTarget: $discoveryQueueTarget)';
}

/// Information about an exploration mode for UI display
class ExplorationModeInfo {
  final String mode;
  final String name;
  final String emoji;
  final String description;
  final String bestFor;
  final String technicalDetails;
  final ExplorationSettings settings;

  const ExplorationModeInfo({
    required this.mode,
    required this.name,
    required this.emoji,
    required this.description,
    required this.bestFor,
    required this.technicalDetails,
    required this.settings,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExplorationModeInfo &&
          runtimeType == other.runtimeType &&
          mode == other.mode;

  @override
  int get hashCode => mode.hashCode;

  @override
  String toString() => 'ExplorationModeInfo(mode: $mode, name: $name)';
}