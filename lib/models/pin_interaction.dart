import 'user.dart';

class PinInteraction {
  final int id;
  final User user;
  final int pinId;
  final String interactionType;
  final DateTime createdAt;

  PinInteraction({
    required this.id,
    required this.user,
    required this.pinId,
    required this.interactionType,
    required this.createdAt,
  });

  factory PinInteraction.fromJson(Map<String, dynamic> json) {
    return PinInteraction(
      // <PERSON>le missing id field - use a default or generate one
      id: json['id'] ?? 0,
      user: User.from<PERSON><PERSON>(json['user']),
      // Handle missing pin_id field - use a default or from parent context
      pinId: json['pin_id'] ?? json['pin'] ?? 0,
      // API returns 'type' instead of 'interaction_type'
      interactionType: json['interaction_type'] ?? json['type'] ?? 'unknown',
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': user.toJson(),
      'pin_id': pinId,
      'interaction_type': interactionType,
      'created_at': createdAt.toIso8601String(),
    };
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'just now';
    }
  }

  bool get isLike => interactionType == 'like' || interactionType == 'upvote';
  bool get isView => interactionType == 'view';
  bool get isCollect => interactionType == 'collect';
  bool get isShare => interactionType == 'share';
  bool get isUpvote => interactionType == 'upvote';
  bool get isDownvote => interactionType == 'downvote';
} 