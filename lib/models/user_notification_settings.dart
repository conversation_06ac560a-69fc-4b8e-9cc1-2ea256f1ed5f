import 'package:json_annotation/json_annotation.dart';

part 'user_notification_settings.g.dart';

@JsonSerializable()
class UserNotificationSettings {
  final bool mapEnabled;
  final bool socialEnabled;
  final bool musicEnabled;
  final bool gamificationEnabled;
  final bool collectionEnabled;
  final bool explorationEnabled;
  final bool customizationEnabled;
  final bool generalEnabled;
  final bool pushNotificationsEnabled;
  final bool emailNotificationsEnabled;
  final bool quietHoursEnabled;
  final String quietStartTime;
  final String quietEndTime;
  final int maxDailyNotifications;

  const UserNotificationSettings({
    this.mapEnabled = true,
    this.socialEnabled = true,
    this.musicEnabled = true,
    this.gamificationEnabled = true,
    this.collectionEnabled = true,
    this.explorationEnabled = true,
    this.customizationEnabled = true,
    this.generalEnabled = true,
    this.pushNotificationsEnabled = true,
    this.emailNotificationsEnabled = false,
    this.quietHoursEnabled = false,
    this.quietStartTime = '22:00',
    this.quietEndTime = '08:00',
    this.maxDailyNotifications = 50,
  });

  factory UserNotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$UserNotificationSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$UserNotificationSettingsToJson(this);

  UserNotificationSettings copyWith({
    bool? mapEnabled,
    bool? socialEnabled,
    bool? musicEnabled,
    bool? gamificationEnabled,
    bool? collectionEnabled,
    bool? explorationEnabled,
    bool? customizationEnabled,
    bool? generalEnabled,
    bool? pushNotificationsEnabled,
    bool? emailNotificationsEnabled,
    bool? quietHoursEnabled,
    String? quietStartTime,
    String? quietEndTime,
    int? maxDailyNotifications,
  }) {
    return UserNotificationSettings(
      mapEnabled: mapEnabled ?? this.mapEnabled,
      socialEnabled: socialEnabled ?? this.socialEnabled,
      musicEnabled: musicEnabled ?? this.musicEnabled,
      gamificationEnabled: gamificationEnabled ?? this.gamificationEnabled,
      collectionEnabled: collectionEnabled ?? this.collectionEnabled,
      explorationEnabled: explorationEnabled ?? this.explorationEnabled,
      customizationEnabled: customizationEnabled ?? this.customizationEnabled,
      generalEnabled: generalEnabled ?? this.generalEnabled,
      pushNotificationsEnabled: pushNotificationsEnabled ?? this.pushNotificationsEnabled,
      emailNotificationsEnabled: emailNotificationsEnabled ?? this.emailNotificationsEnabled,
      quietHoursEnabled: quietHoursEnabled ?? this.quietHoursEnabled,
      quietStartTime: quietStartTime ?? this.quietStartTime,
      quietEndTime: quietEndTime ?? this.quietEndTime,
      maxDailyNotifications: maxDailyNotifications ?? this.maxDailyNotifications,
    );
  }
} 