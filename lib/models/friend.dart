import 'user.dart';

enum FriendStatus {
  pending,
  accepted,
  rejected,
}

class Friend {
  final int id;
  final User requester;
  final User recipient;
  final FriendStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  Friend({
    required this.id,
    required this.requester,
    required this.recipient,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Friend.fromJson(Map<String, dynamic> json) {
    try {
      return Friend(
        id: json['id'] ?? 0,
        requester: User.from<PERSON><PERSON>(json['requester'] ?? {}),
        recipient: User.from<PERSON>son(json['recipient'] ?? {}),
        status: _parseStatus(json['status'] ?? 'pending'),
        createdAt: json['created_at'] != null 
            ? DateTime.parse(json['created_at'])
            : DateTime.now(),
        updatedAt: json['updated_at'] != null 
            ? DateTime.parse(json['updated_at'])
            : DateTime.now(),
      );
    } catch (e) {
      print('Error parsing Friend from JSON: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'requester': requester.toJson(),
      'recipient': recipient.toJson(),
      'status': _statusToString(status),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  static FriendStatus _parseStatus(String statusStr) {
    switch (statusStr.toLowerCase()) {
      case 'pending':
        return FriendStatus.pending;
      case 'accepted':
        return FriendStatus.accepted;
      case 'rejected':
        return FriendStatus.rejected;
      default:
        return FriendStatus.pending;
    }
  }

  static String _statusToString(FriendStatus status) {
    switch (status) {
      case FriendStatus.pending:
        return 'pending';
      case FriendStatus.accepted:
        return 'accepted';
      case FriendStatus.rejected:
        return 'rejected';
    }
  }

  User getOtherUser(int currentUserId) {
    if (requester.id == currentUserId) {
      return recipient;
    } else {
      return requester;
    }
  }

  bool isOutgoingRequest(int currentUserId) {
    return requester.id == currentUserId && status == FriendStatus.pending;
  }

  bool isIncomingRequest(int currentUserId) {
    return recipient.id == currentUserId && status == FriendStatus.pending;
  }

  bool get isAccepted => status == FriendStatus.accepted;

  String getTimeSinceCreated() {
    final difference = DateTime.now().difference(createdAt);
    
    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}y';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}

class FriendRequest extends Friend {
  FriendRequest({
    required int id,
    required User requester,
    required User recipient,
    required FriendStatus status,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) : super(
          id: id,
          requester: requester,
          recipient: recipient,
          status: status,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  factory FriendRequest.fromJson(Map<String, dynamic> json) {
    try {
      return FriendRequest(
        id: json['id'] ?? 0,
        requester: User.fromJson(json['requester'] ?? {}),
        recipient: User.fromJson(json['recipient'] ?? {}),
        status: Friend._parseStatus(json['status'] ?? 'pending'),
        createdAt: json['created_at'] != null 
            ? DateTime.parse(json['created_at'])
            : DateTime.now(),
        updatedAt: json['updated_at'] != null 
            ? DateTime.parse(json['updated_at'])
            : DateTime.now(),
      );
    } catch (e) {
      print('Error parsing FriendRequest from JSON: $e');
      print('JSON data: $json');
      rethrow;
    }
  }
}

/// Updated FriendshipData to match the new unified FriendSerializer structure
/// This now contains all friend information in a single object
class FriendshipData {
  final int id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String friendId;
  final String username;
  final String name;
  final String? avatarUrl;
  final bool isOnline;
  final DateTime? lastActive;
  final int pinsCount;
  final String? latestPin;
  final DateTime? latestPinTime;

  FriendshipData({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.friendId,
    required this.username,
    required this.name,
    this.avatarUrl,
    required this.isOnline,
    this.lastActive,
    required this.pinsCount,
    this.latestPin,
    this.latestPinTime,
  });

  factory FriendshipData.fromJson(Map<String, dynamic> json) {
    try {
      // Get the friend object which contains the name fields
      final friendData = json['friend'] as Map<String, dynamic>? ?? {};
      final firstName = friendData['first_name'] as String? ?? '';
      final lastName = friendData['last_name'] as String? ?? '';
      final fullName = [firstName, lastName].where((s) => s.isNotEmpty).join(' ');
      
      return FriendshipData(
        id: json['id'] is String ? int.parse(json['id']) : (json['id'] ?? 0),
        createdAt: json['created_at'] != null 
            ? DateTime.parse(json['created_at'])
            : DateTime.now(),
        updatedAt: json['updated_at'] != null 
            ? DateTime.parse(json['updated_at'])
            : DateTime.now(),
        friendId: friendData['id']?.toString() ?? '0',
        username: friendData['username'] ?? '',
        name: fullName.isNotEmpty ? fullName : friendData['username'] ?? '',
        avatarUrl: friendData['profile_pic'],
        isOnline: json['is_online'] ?? false,
        lastActive: json['last_active'] != null
            ? DateTime.parse(json['last_active'])
            : null,
        pinsCount: json['pins_count'] is String ? int.parse(json['pins_count']) : (json['pins_count'] ?? 0),
        latestPin: json['latest_pin'],
        latestPinTime: json['latest_pin_time'] != null
            ? DateTime.parse(json['latest_pin_time'])
            : null,
      );
    } catch (e) {
      print('Error parsing FriendshipData from JSON: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'friend_id': friendId,
      'username': username,
      'name': name,
      'avatar_url': avatarUrl,
      'is_online': isOnline,
      'last_active': lastActive?.toIso8601String(),
      'pins_count': pinsCount,
      'latest_pin': latestPin,
      'latest_pin_time': latestPinTime?.toIso8601String(),
    };
  }

  /// Create a User object from this friendship data for backward compatibility
  User get friend {
    return User(
      id: int.tryParse(friendId) ?? 0,
      username: username,
      email: '', // Not provided in the new structure
      profilePicUrl: avatarUrl,
      bio: null,
      location: null,
      isVerified: false, // Not provided in the new structure
      favoriteGenres: [],
      connectedServices: {},
      createdAt: createdAt,
      lastActive: lastActive,
    );
  }

  /// Get display name (same as name field from backend)
  String get displayName => name;

  /// Check if friend was active recently (within 5 minutes)
  bool get isRecentlyActive {
    if (lastActive == null) return false;
    return DateTime.now().difference(lastActive!).inMinutes <= 5;
  }

  /// Get formatted time since friendship was created
  String getTimeSinceCreated() {
    final difference = DateTime.now().difference(createdAt);
    
    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}y';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }

  /// Get formatted time since latest pin
  String? getTimeSinceLatestPin() {
    if (latestPinTime == null) return null;
    
    final difference = DateTime.now().difference(latestPinTime!);
    
    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}y ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'just now';
    }
  }
}