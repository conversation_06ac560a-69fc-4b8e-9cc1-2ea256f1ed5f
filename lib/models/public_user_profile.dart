class PublicUserProfile {
  final int id;
  final String username;
  final String? profilePicUrl;
  final String? bio;
  final DateTime? lastActive;
  final int pinCount;
  final int publicCollectionCount;
  final bool isVerified;
  final String? location;

  PublicUserProfile({
    required this.id,
    required this.username,
    this.profilePicUrl,
    this.bio,
    this.lastActive,
    required this.pinCount,
    required this.publicCollectionCount,
    this.isVerified = false,
    this.location,
  });

  factory PublicUserProfile.fromJson(Map<String, dynamic> json) {
    try {
      return PublicUserProfile(
        id: json['id'] ?? 0,
        username: json['username'] ?? '',
        profilePicUrl: json['profile_pic'],
        bio: json['bio'],
        lastActive: json['last_active'] != null
            ? DateTime.parse(json['last_active'])
            : null,
        pinCount: json['pin_count'] ?? 0,
        publicCollectionCount: json['public_collection_count'] ?? 0,
        isVerified: json['is_verified'] ?? false,
        location: json['location'],
      );
    } catch (e) {
      print('Error parsing PublicUserProfile from JSON: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'profile_pic': profilePicUrl,
      'bio': bio,
      'last_active': lastActive?.toIso8601String(),
      'pin_count': pinCount,
      'public_collection_count': publicCollectionCount,
      'is_verified': isVerified,
      'location': location,
    };
  }

  // Returns the user's display name
  String get displayName => username.isNotEmpty ? username : 'User $id';

  // Check if user is currently online (active within last 15 minutes)
  bool get isOnline {
    if (lastActive == null) return false;
    return DateTime.now().difference(lastActive!).inMinutes < 15;
  }

  // Get formatted last active time
  String get lastActiveFormatted {
    if (lastActive == null) return 'Never';
    
    final now = DateTime.now();
    final difference = now.difference(lastActive!);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${(difference.inDays / 7).floor()}w ago';
    }
  }
} 