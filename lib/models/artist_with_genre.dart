class ArtistWithGenre {
  final String id;
  final String name;
  final String? imageUrl;
  final List<String> genres;
  final int popularity;
  final String url;
  final String sourceGenre; // The genre this artist was loaded from
  final String uniqueKey;   // Generated artist|genre key
  final int followers;
  final bool isSimilar;
  final bool isSearchResult;

  ArtistWithGenre({
    required this.id,
    required this.name,
    this.imageUrl,
    required this.genres,
    required this.popularity,
    required this.url,
    required this.sourceGenre,
    this.followers = 0,
    this.isSimilar = false,
    this.isSearchResult = false,
  }) : uniqueKey = SelectionKeyManager.generateArtistKey(name, sourceGenre);

  // Create from Spotify API response
  factory ArtistWithGenre.fromSpotifyResponse(
    Map<String, dynamic> artist,
    String sourceGenre,
  ) {
    return ArtistWithGenre(
      id: artist['id'] ?? '',
      name: artist['name'] ?? 'Unknown Artist',
      imageUrl: artist['image_url'],
      genres: List<String>.from(artist['genres'] ?? []),
      popularity: artist['popularity'] ?? 50,
      url: artist['url'] ?? '',
      sourceGenre: sourceGenre,
      followers: artist['followers'] ?? 0,
      isSimilar: artist['is_similar'] ?? false,
      isSearchResult: artist['is_search_result'] ?? false,
    );
  }

  // Convert to map for storage/serialization
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'image_url': imageUrl,
      'genres': genres,
      'popularity': popularity,
      'url': url,
      'source_genre': sourceGenre,
      'unique_key': uniqueKey,
      'followers': followers,
      'is_similar': isSimilar,
      'is_search_result': isSearchResult,
    };
  }

  // Create from map
  factory ArtistWithGenre.fromMap(Map<String, dynamic> map) {
    return ArtistWithGenre(
      id: map['id'] ?? '',
      name: map['name'] ?? 'Unknown Artist',
      imageUrl: map['image_url'],
      genres: List<String>.from(map['genres'] ?? []),
      popularity: map['popularity'] ?? 50,
      url: map['url'] ?? '',
      sourceGenre: map['source_genre'] ?? '',
      followers: map['followers'] ?? 0,
      isSimilar: map['is_similar'] ?? false,
      isSearchResult: map['is_search_result'] ?? false,
    );
  }

  // Copy with modifications
  ArtistWithGenre copyWith({
    String? id,
    String? name,
    String? imageUrl,
    List<String>? genres,
    int? popularity,
    String? url,
    String? sourceGenre,
    int? followers,
    bool? isSimilar,
    bool? isSearchResult,
  }) {
    return ArtistWithGenre(
      id: id ?? this.id,
      name: name ?? this.name,
      imageUrl: imageUrl ?? this.imageUrl,
      genres: genres ?? this.genres,
      popularity: popularity ?? this.popularity,
      url: url ?? this.url,
      sourceGenre: sourceGenre ?? this.sourceGenre,
      followers: followers ?? this.followers,
      isSimilar: isSimilar ?? this.isSimilar,
      isSearchResult: isSearchResult ?? this.isSearchResult,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ArtistWithGenre && other.uniqueKey == uniqueKey;
  }

  @override
  int get hashCode => uniqueKey.hashCode;

  @override
  String toString() {
    return 'ArtistWithGenre(name: $name, sourceGenre: $sourceGenre, uniqueKey: $uniqueKey)';
  }
}

class SelectionKeyManager {
  /// Generate unique key for artist-genre combination
  static String generateArtistKey(String artistName, String genre) {
    return '${artistName.toLowerCase().trim()}|${genre.toLowerCase().trim()}';
  }

  /// Parse artist key back to components
  static ArtistGenreKey parseArtistKey(String key) {
    final parts = key.split('|');
    if (parts.length != 2) {
      throw ArgumentError('Invalid artist key format: $key');
    }
    return ArtistGenreKey(artist: parts[0], genre: parts[1]);
  }

  /// Check if specific artist-genre combination is selected
  static bool isArtistSelected(String artistName, String genre, Set<String> selectedKeys) {
    return selectedKeys.contains(generateArtistKey(artistName, genre));
  }

  /// Get all selected artist names (without genre context)
  static List<String> getSelectedArtistNames(Set<String> selectedKeys) {
    return selectedKeys
        .map((key) => parseArtistKey(key).artist)
        .toSet() // Remove duplicates
        .toList();
  }

  /// Get selected artists grouped by genre
  static Map<String, List<String>> getSelectedArtistsByGenre(Set<String> selectedKeys) {
    final Map<String, List<String>> result = {};
    
    for (final key in selectedKeys) {
      final parsed = parseArtistKey(key);
      result.putIfAbsent(parsed.genre, () => []).add(parsed.artist);
    }
    
    return result;
  }
}

class ArtistGenreKey {
  final String artist;
  final String genre;

  const ArtistGenreKey({
    required this.artist,
    required this.genre,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ArtistGenreKey &&
        other.artist == artist &&
        other.genre == genre;
  }

  @override
  int get hashCode => artist.hashCode ^ genre.hashCode;

  @override
  String toString() => 'ArtistGenreKey(artist: $artist, genre: $genre)';
}