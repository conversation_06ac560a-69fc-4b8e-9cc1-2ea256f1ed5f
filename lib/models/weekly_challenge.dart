import 'package:flutter/material.dart';

class WeeklyChallenge {
  final String id;
  final String title;
  final String description;
  final int entries;
  final Duration timeLeft;
  final List<Color> gradientColors;
  final IconData icon;
  final bool hasParticipated;

  const WeeklyChallenge({
    required this.id,
    required this.title,
    required this.description,
    required this.entries,
    required this.timeLeft,
    required this.gradientColors,
    required this.icon,
    required this.hasParticipated,
  });

  // Helper to convert hex color string to Color
  static Color _hexToColor(String hex) {
    hex = hex.replaceFirst('#', '');
    return Color(int.parse('FF$hex', radix: 16));
  }

  // Helper to convert icon string to IconData
  static IconData _getIconData(String iconName) {
    // Map backend icon names to Flutter Icons
    final iconMap = {
      'heart_broken_rounded': Icons.heart_broken_rounded,
      'wb_sunny_rounded': Icons.wb_sunny_rounded,
      'diamond_rounded': Icons.diamond_rounded,
      'replay_rounded': Icons.replay_rounded,
      'fitness_center': Icons.fitness_center,
      'nightlight_rounded': Icons.nightlight_rounded,
      'language': Icons.language,
      'stars_rounded': Icons.stars_rounded,
      'water_drop': Icons.water_drop,
      'festival': Icons.festival,
    };
    return iconMap[iconName] ?? Icons.error;
  }

  factory WeeklyChallenge.fromJson(Map<String, dynamic> json) {
    // Convert the ID to string, handling both int and string inputs
    final id = json['id'].toString();

    // Handle gradient colors from API or use defaults
    List<Color> gradientColors;
    if (json['gradient_colors'] != null) {
      final colors = json['gradient_colors'] as List;
      gradientColors = colors.map((c) => _hexToColor(c.toString())).toList();
    } else {
      // Fallback to default gradients
      final defaultGradients = [
        [Colors.purple, Colors.blue],
        [Colors.orange, Colors.pink],
        [Colors.teal, Colors.indigo],
      ];
      final index = json['index'] as int? ?? 0;
      gradientColors = defaultGradients[index % 3];
    }

    final timeLeftData = json['time_left'] as Map<String, dynamic>;
    final timeLeft = Duration(seconds: timeLeftData['inSeconds'] as int);

    return WeeklyChallenge(
      id: id,
      title: json['title'] as String,
      description: json['description'] as String,
      entries: json['entries'] as int,
      timeLeft: timeLeft,
      gradientColors: gradientColors,
      icon: _getIconData(json['icon'] as String),
      hasParticipated: json['has_participated'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'entries': entries,
    'time_left': {
      'inSeconds': timeLeft.inSeconds,
      'inMinutes': timeLeft.inMinutes,
      'inHours': timeLeft.inHours,
      'inDays': timeLeft.inDays,
    },
    'icon': icon.toString(),
    'has_participated': hasParticipated,
  };
} 