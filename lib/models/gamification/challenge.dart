import 'package:flutter/material.dart';
import 'category.dart';

class Challenge {
  final int id;
  final String name;
  final String description;
  final String categoryId;
  final Map<String, dynamic> criteria;
  final int xpReward;
  final DateTime createdAt;
  final DateTime? completedAt;
  final Map<String, dynamic>? progress;
  final bool isCompleted;
  final double progressPercentage;

  const Challenge({
    required this.id,
    required this.name,
    required this.description,
    required this.categoryId,
    required this.criteria,
    this.xpReward = 100,
    required this.createdAt,
    this.completedAt,
    this.progress,
    this.isCompleted = false,
    this.progressPercentage = 0.0,
  });

  // Get the category for this challenge
  GamificationCategory? get category => GamificationCategory.findById(categoryId);

  // Calculate progress percentage based on criteria and current progress
  double calculateProgressPercentage() {
    if (criteria.isEmpty) return 0.0;
    
    double totalProgress = 0.0;
    int criteriaCount = 0;
    
    for (String key in criteria.keys) {
      final requiredValue = criteria[key];
      final currentValue = progress?[key] ?? 0;
      
      if (requiredValue is num && requiredValue > 0) {
        final progressRatio = (currentValue / requiredValue).clamp(0.0, 1.0);
        totalProgress += progressRatio;
        criteriaCount++;
      }
    }
    
    return criteriaCount > 0 ? (totalProgress / criteriaCount) * 100 : 0.0;
  }

  // Get human readable progress text
  String getProgressText() {
    if (isCompleted) return 'Completed';
    
    final percentage = calculateProgressPercentage();
    if (percentage == 0) return 'Not started';
    
    return '${percentage.toStringAsFixed(0)}% complete';
  }

  // Factory constructor for easier creation
  factory Challenge.create({
    required int id,
    required String name,
    required String description,
    required String categoryId,
    required Map<String, dynamic> criteria,
    int xpReward = 100,
    DateTime? createdAt,
    DateTime? completedAt,
    Map<String, dynamic>? progress,
    bool isCompleted = false,
    double progressPercentage = 0.0,
  }) {
    return Challenge(
      id: id,
      name: name,
      description: description,
      categoryId: categoryId,
      criteria: criteria,
      xpReward: xpReward,
      createdAt: createdAt ?? DateTime.now(),
      completedAt: completedAt,
      progress: progress,
      isCompleted: isCompleted,
      progressPercentage: progressPercentage,
    );
  }

  // JSON serialization
  factory Challenge.fromJson(Map<String, dynamic> json) {
    return Challenge(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? 'Unknown Challenge',
      description: json['description'] as String? ?? 'No description available',
      categoryId: json['category_id'] as String? ?? 'general',
      criteria: Map<String, dynamic>.from(json['criteria'] ?? {}),
      xpReward: json['xp_reward'] as int? ?? 100,
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      completedAt: DateTime.tryParse(json['completed_at'] ?? ''),
      progress: json['progress'] != null ? Map<String, dynamic>.from(json['progress']) : null,
      isCompleted: json['is_completed'] as bool? ?? false,
      progressPercentage: (json['progress_percentage'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category_id': categoryId,
      'criteria': criteria,
      'xp_reward': xpReward,
      'created_at': createdAt.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'progress': progress,
      'is_completed': isCompleted,
      'progress_percentage': progressPercentage,
    };
  }

  // Copy with method for immutability
  Challenge copyWith({
    int? id,
    String? name,
    String? description,
    String? categoryId,
    Map<String, dynamic>? criteria,
    int? xpReward,
    DateTime? createdAt,
    DateTime? completedAt,
    Map<String, dynamic>? progress,
    bool? isCompleted,
    double? progressPercentage,
  }) {
    return Challenge(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      categoryId: categoryId ?? this.categoryId,
      criteria: criteria ?? this.criteria,
      xpReward: xpReward ?? this.xpReward,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      progress: progress ?? this.progress,
      isCompleted: isCompleted ?? this.isCompleted,
      progressPercentage: progressPercentage ?? this.progressPercentage,
    );
  }
} 