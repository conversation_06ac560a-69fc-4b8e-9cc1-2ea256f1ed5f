import 'package:flutter/material.dart';
import '../achievement.dart';

class GamificationCategory {
  final String id;
  final String title;
  final IconData icon;
  final Color color;
  final String criteriaKey;
  final List<Achievement> achievements;

  const GamificationCategory({
    required this.id,
    required this.title,
    required this.icon,
    required this.color,
    required this.criteriaKey,
    this.achievements = const [],
  });

  // Factory constructors for each category type
  factory GamificationCategory.artist() {
    return const GamificationCategory(
      id: 'artist',
      title: 'Artist Challenges',
      icon: Icons.mic,
      color: Colors.purple,
      criteriaKey: 'artist_pins',
    );
  }

  factory GamificationCategory.genre() {
    return const GamificationCategory(
      id: 'genre',
      title: 'Genre Challenges',
      icon: Icons.music_note,
      color: Colors.blue,
      criteriaKey: 'genre_pins',
    );
  }

  factory GamificationCategory.location() {
    return const GamificationCategory(
      id: 'location',
      title: 'Location Challenges',
      icon: Icons.location_on,
      color: Colors.green,
      criteriaKey: 'location_badges',
    );
  }

  factory GamificationCategory.social() {
    return const GamificationCategory(
      id: 'social',
      title: 'Social Challenges',
      icon: Icons.favorite,
      color: Colors.pink,
      criteriaKey: 'upvotes',
    );
  }

  // Helper method to get all categories
  static List<GamificationCategory> getAllCategories() {
    return [
      GamificationCategory.artist(),
      GamificationCategory.genre(),
      GamificationCategory.location(),
      GamificationCategory.social(),
    ];
  }

  // Helper method to find category by ID
  static GamificationCategory? findById(String id) {
    try {
      return getAllCategories().firstWhere((cat) => cat.id == id);
    } catch (e) {
      return null;
    }
  }

  // Copy with method for immutability
  GamificationCategory copyWith({
    String? id,
    String? title,
    IconData? icon,
    Color? color,
    String? criteriaKey,
    List<Achievement>? achievements,
  }) {
    return GamificationCategory(
      id: id ?? this.id,
      title: title ?? this.title,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      criteriaKey: criteriaKey ?? this.criteriaKey,
      achievements: achievements ?? this.achievements,
    );
  }
} 