// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_notification_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserNotificationSettings _$UserNotificationSettingsFromJson(
        Map<String, dynamic> json) =>
    UserNotificationSettings(
      mapEnabled: json['mapEnabled'] as bool? ?? true,
      socialEnabled: json['socialEnabled'] as bool? ?? true,
      musicEnabled: json['musicEnabled'] as bool? ?? true,
      gamificationEnabled: json['gamificationEnabled'] as bool? ?? true,
      collectionEnabled: json['collectionEnabled'] as bool? ?? true,
      explorationEnabled: json['explorationEnabled'] as bool? ?? true,
      customizationEnabled: json['customizationEnabled'] as bool? ?? true,
      generalEnabled: json['generalEnabled'] as bool? ?? true,
      pushNotificationsEnabled:
          json['pushNotificationsEnabled'] as bool? ?? true,
      emailNotificationsEnabled:
          json['emailNotificationsEnabled'] as bool? ?? false,
      quietHoursEnabled: json['quietHoursEnabled'] as bool? ?? false,
      quietStartTime: json['quietStartTime'] as String? ?? '22:00',
      quietEndTime: json['quietEndTime'] as String? ?? '08:00',
      maxDailyNotifications:
          (json['maxDailyNotifications'] as num?)?.toInt() ?? 50,
    );

Map<String, dynamic> _$UserNotificationSettingsToJson(
        UserNotificationSettings instance) =>
    <String, dynamic>{
      'mapEnabled': instance.mapEnabled,
      'socialEnabled': instance.socialEnabled,
      'musicEnabled': instance.musicEnabled,
      'gamificationEnabled': instance.gamificationEnabled,
      'collectionEnabled': instance.collectionEnabled,
      'explorationEnabled': instance.explorationEnabled,
      'customizationEnabled': instance.customizationEnabled,
      'generalEnabled': instance.generalEnabled,
      'pushNotificationsEnabled': instance.pushNotificationsEnabled,
      'emailNotificationsEnabled': instance.emailNotificationsEnabled,
      'quietHoursEnabled': instance.quietHoursEnabled,
      'quietStartTime': instance.quietStartTime,
      'quietEndTime': instance.quietEndTime,
      'maxDailyNotifications': instance.maxDailyNotifications,
    };
