import 'package:flutter/material.dart';
import 'user.dart';
import 'music_track.dart';

class MemoryGroup {
  final String id;
  final String title;
  final String? coverImage;
  final List<User> members;
  final List<MemoryPin> pins;
  final DateTime createdAt;
  final DateTime updatedAt;

  MemoryGroup({
    required this.id,
    required this.title,
    this.coverImage,
    required this.members,
    required this.pins,
    required this.createdAt,
    required this.updatedAt,
  });
}

class MemoryPin {
  final String id;
  final LatLng location;
  final String placeName;
  final User createdBy;
  final DateTime createdAt;
  final String note;
  final Map<String, MemoryPinSong> songsByUser; // userId -> song

  MemoryPin({
    required this.id,
    required this.location,
    required this.placeName,
    required this.createdBy,
    required this.createdAt,
    required this.note,
    required this.songsByUser,
  });
}

class MemoryPinSong {
  final MusicTrack track;
  final String? message;
  final DateTime addedAt;

  MemoryPinSong({
    required this.track,
    this.message,
    required this.addedAt,
  });
}

class LatLng {
  final double latitude;
  final double longitude;
  const LatLng(this.latitude, this.longitude);
} 