// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationModel _$NotificationModelFromJson(Map<String, dynamic> json) =>
    NotificationModel(
      id: json['id'] as String,
      type: $enumDecode(_$NotificationTypeEnumMap, json['notification_type']),
      category: $enumDecode(_$NotificationCategoryEnumMap, json['category']),
      priority: $enumDecode(_$NotificationPriorityEnumMap, json['priority']),
      title: json['title'] as String,
      message: json['message'] as String,
      timestamp: DateTime.parse(json['created_at'] as String),
      isRead: json['is_read'] as bool,
      imageUrl: json['image_url'] as String?,
      actionData: json['action_data'] as Map<String, dynamic>?,
      onesignalId: json['onesignalId'] as String?,
      readAt: json['read_at'] == null
          ? null
          : DateTime.parse(json['read_at'] as String),
    );

Map<String, dynamic> _$NotificationModelToJson(NotificationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'notification_type': _$NotificationTypeEnumMap[instance.type]!,
      'category': _$NotificationCategoryEnumMap[instance.category]!,
      'priority': _$NotificationPriorityEnumMap[instance.priority]!,
      'title': instance.title,
      'message': instance.message,
      'created_at': instance.timestamp.toIso8601String(),
      'is_read': instance.isRead,
      'image_url': instance.imageUrl,
      'action_data': instance.actionData,
      'onesignalId': instance.onesignalId,
      'read_at': instance.readAt?.toIso8601String(),
    };

const _$NotificationTypeEnumMap = {
  NotificationType.pinLike: 'pin_like',
  NotificationType.pinComment: 'pin_comment',
  NotificationType.pinTrending: 'pin_trending',
  NotificationType.friendNearby: 'friend_nearby',
  NotificationType.pinMilestone: 'pin_milestone',
  NotificationType.freshPinsFound: 'fresh_pins_found',
  NotificationType.friendRequest: 'friend_request',
  NotificationType.friendAccepted: 'friend_accepted',
  NotificationType.musicChat: 'music_chat',
  NotificationType.activityDigest: 'activity_digest',
  NotificationType.liveListening: 'live_listening',
  NotificationType.weeklyDigest: 'weekly_digest',
  NotificationType.newRelease: 'new_release',
  NotificationType.favoriteArtistUpdate: 'favorite_artist_update',
  NotificationType.dailyMix: 'daily_mix',
  NotificationType.weeklyRecommendation: 'weekly_recommendation',
  NotificationType.musicSync: 'music_sync',
  NotificationType.aiRecommendation: 'ai_recommendation',
  NotificationType.challengeComplete: 'challenge_complete',
  NotificationType.challengeProgress: 'challenge_progress',
  NotificationType.challengeAvailable: 'challenge_available',
  NotificationType.levelUp: 'level_up',
  NotificationType.achievementUnlocked: 'achievement_unlocked',
  NotificationType.xpEarned: 'xp_earned',
  NotificationType.collectionUpdate: 'collection_update',
  NotificationType.collectionMilestone: 'collection_milestone',
  NotificationType.collaborativeUpdate: 'collaborative_update',
  NotificationType.playlistShared: 'playlist_shared',
  NotificationType.newArPins: 'new_ar_pins',
  NotificationType.seasonalDrop: 'seasonal_drop',
  NotificationType.eventAvailable: 'event_available',
  NotificationType.trendingInCity: 'trending_in_city',
  NotificationType.newPinsNearby: 'new_pins_nearby',
  NotificationType.seasonalEvent: 'seasonal_event',
  NotificationType.skinUnlocked: 'skin_unlocked',
  NotificationType.limitedSkinAvailable: 'limited_skin_available',
  NotificationType.customizationReminder: 'customization_reminder',
  NotificationType.general: 'general',
  NotificationType.systemUpdate: 'system_update',
  NotificationType.retentionReminder: 'retention_reminder',
  NotificationType.welcomeMessage: 'welcome_message',
  NotificationType.unreadReminder: 'unread_reminder',
};

const _$NotificationCategoryEnumMap = {
  NotificationCategory.all: 'all',
  NotificationCategory.map: 'map',
  NotificationCategory.social: 'social',
  NotificationCategory.music: 'music',
  NotificationCategory.gamification: 'gamification',
  NotificationCategory.collection: 'collection',
  NotificationCategory.exploration: 'exploration',
  NotificationCategory.customization: 'customization',
  NotificationCategory.general: 'general',
};

const _$NotificationPriorityEnumMap = {
  NotificationPriority.low: 'low',
  NotificationPriority.medium: 'medium',
  NotificationPriority.high: 'high',
  NotificationPriority.urgent: 'urgent',
};

NotificationListResponse _$NotificationListResponseFromJson(
        Map<String, dynamic> json) =>
    NotificationListResponse(
      results: (json['results'] as List<dynamic>)
          .map((e) => NotificationModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: (json['count'] as num).toInt(),
      unreadCount: (json['unread_count'] as num).toInt(),
    );

Map<String, dynamic> _$NotificationListResponseToJson(
        NotificationListResponse instance) =>
    <String, dynamic>{
      'results': instance.results,
      'count': instance.totalCount,
      'unread_count': instance.unreadCount,
    };

SuccessResponse _$SuccessResponseFromJson(Map<String, dynamic> json) =>
    SuccessResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
    );

Map<String, dynamic> _$SuccessResponseToJson(SuccessResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
    };

BulkActionResponse _$BulkActionResponseFromJson(Map<String, dynamic> json) =>
    BulkActionResponse(
      success: json['success'] as bool,
      updatedCount: (json['updated_count'] as num?)?.toInt(),
      message: json['message'] as String,
    );

Map<String, dynamic> _$BulkActionResponseToJson(BulkActionResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'updated_count': instance.updatedCount,
      'message': instance.message,
    };

NotificationStatsResponse _$NotificationStatsResponseFromJson(
        Map<String, dynamic> json) =>
    NotificationStatsResponse(
      totalNotifications: (json['totalNotifications'] as num).toInt(),
      unreadNotifications: (json['unreadNotifications'] as num).toInt(),
      categoryCounts: Map<String, int>.from(json['categoryCounts'] as Map),
      unreadCategoryCounts:
          Map<String, int>.from(json['unreadCategoryCounts'] as Map),
    );

Map<String, dynamic> _$NotificationStatsResponseToJson(
        NotificationStatsResponse instance) =>
    <String, dynamic>{
      'totalNotifications': instance.totalNotifications,
      'unreadNotifications': instance.unreadNotifications,
      'categoryCounts': instance.categoryCounts,
      'unreadCategoryCounts': instance.unreadCategoryCounts,
    };
