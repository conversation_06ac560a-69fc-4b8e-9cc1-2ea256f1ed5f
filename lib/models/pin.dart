import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'user.dart';
import 'music_track.dart';

enum PinRarity {
  common,
  uncommon,
  rare,
  epic,
  legendary,
}

class Pin {
  final int id;
  final User owner;
  final Map<String, dynamic> location;  // GeoJSON Point
  final String title;
  final String? description;
  final String? caption;
  final String? locationName;  // Location name like "Atlanta, GA"
  final String trackTitle;
  final String trackArtist;
  final String? album;
  final String trackUrl;
  final String service;  // "spotify", "apple", "soundcloud"
  final String? artworkUrl;  // Album/track artwork URL
  final int? durationMs;     // Track duration in milliseconds
  final String? genre;       // Canonical genre (lowercase)
  final int skin;
  final Map<String, dynamic>? skinDetails;
  final String rarity;
  final double? auraRadius;
  final bool isPrivate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, int> interactionCount;
  final int upvoteCount;
  final int downvoteCount;
  
  // New field to preserve raw engagement counts from the API
  final Map<String, dynamic>? engagementCounts;
  
  // UI helper properties (not from API)
  double? screenX;
  double? screenY;
  bool isWithinRange = false;

  Pin({
    required this.id,
    required this.owner,
    required this.location,
    required this.title,
    this.description,
    this.caption,
    this.locationName,
    required this.trackTitle,
    required this.trackArtist,
    this.album,
    required this.trackUrl,
    required this.service,
    this.artworkUrl,
    this.durationMs,
    this.genre,
    required this.skin,
    this.skinDetails,
    required this.rarity,
    this.auraRadius,
    this.isPrivate = false,
    required this.createdAt,
    required this.updatedAt,
    required this.interactionCount,
    this.upvoteCount = 0,
    this.downvoteCount = 0,
    this.engagementCounts,
    this.screenX,
    this.screenY,
  });

  // Helper getters for coordinates
  double get latitude => location['coordinates'][1];
  double get longitude => location['coordinates'][0];

  factory Pin.fromJson(Map<String, dynamic> json) {
    try {
      // Parse location field - handle both GeoJSON and WKT formats
      Map<String, dynamic> locationData;
      if (json['location'] is String) {
        // Handle WKT format like "SRID=4326;POINT (-86.80797539423747 36.146717510111166)"
        final locationStr = json['location'] as String;
        if (locationStr.contains('POINT')) {
          final coordsStr = locationStr.split('POINT ')[1].trim();
          final coordsParts = coordsStr
              .replaceAll('(', '')
              .replaceAll(')', '')
              .split(' ')
              .map((s) => double.parse(s))
              .toList();
          // WKT format is (longitude latitude), same as GeoJSON
          locationData = {
            'type': 'Point',
            'coordinates': [coordsParts[0], coordsParts[1]] // [lng, lat]
          };
        } else {
          // Fallback for other string formats
          locationData = {
            'type': 'Point',
            'coordinates': [0.0, 0.0]
          };
        }
      } else if (json['location'] is Map<String, dynamic>) {
        // Handle GeoJSON format
        locationData = json['location'] as Map<String, dynamic>;
      } else {
        // Fallback for unknown formats
        locationData = {
          'type': 'Point',
          'coordinates': [0.0, 0.0]
        };
      }

      return Pin(
        id: json['id'] is String ? int.parse(json['id']) : json['id'],
        owner: json['owner'] != null ? User.fromJson(json['owner']) : User.anonymous(),
        location: locationData,
        title: json['title'] ?? '',
        description: json['description'],
        caption: json['caption'],
        locationName: json['location_name'],
        trackTitle: json['track_title'] ?? '',
        trackArtist: json['track_artist'] ?? '',
        album: json['album'],
        trackUrl: json['track_url'] ?? '',
        service: json['service'] ?? 'spotify',
        artworkUrl: json['artwork_url'],
        durationMs: json['duration_ms'] is String ? int.tryParse(json['duration_ms']) : json['duration_ms'],
        genre: json['genre'], // Canonical genre (already lowercase from backend)
        skin: json['skin'] is String ? int.parse(json['skin']) : (json['skin'] ?? 1),
        skinDetails: json['skin_details'],
        rarity: json['rarity'] ?? 'common',
        auraRadius: json['aura_radius']?.toDouble(),
        isPrivate: json['is_private'] ?? false,
        createdAt: json['created_at'] != null 
            ? DateTime.parse(json['created_at']) 
            : DateTime.now(),
        updatedAt: json['updated_at'] != null 
            ? DateTime.parse(json['updated_at']) 
            : DateTime.now(),
        interactionCount: Map<String, int>.from(json['interaction_count'] ?? {
          'view': 0,
          'like': 0,
          'collect': 0,
          'share': 0
        }),
        upvoteCount: json['upvote_count'] ?? 0,
        downvoteCount: json['downvote_count'] ?? 0,
        engagementCounts: json['engagement_counts'],
      );
    } catch (e) {
      print('Error parsing Pin from JSON: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'owner': owner.toJson(),
      'location': location,
      'title': title,
      'description': description,
      'caption': caption,
      'location_name': locationName,
      'track_title': trackTitle,
      'track_artist': trackArtist,
      'album': album,
      'track_url': trackUrl,
      'service': service,
      'artwork_url': artworkUrl,
      'duration_ms': durationMs,
      'skin': skin,
      'skin_details': skinDetails,
      'rarity': rarity,
      'aura_radius': auraRadius,
      'is_private': isPrivate,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'interaction_count': interactionCount,
      'upvote_count': upvoteCount,
      'downvote_count': downvoteCount,
      'engagement_counts': engagementCounts,
    };
  }
  
  // Helper method to create a new pin (for creating pins)
  static Pin createNew({
    required User owner,
    required double latitude,
    required double longitude,
    required String title,
    String? description,
    String? caption,
    required String trackTitle,
    required String trackArtist,
    String? album,
    required String trackUrl,
    required String service,
    String? artworkUrl,
    int? durationMs,
    required int skin,
    double? auraRadius,
    bool isPrivate = false,
  }) {
    final now = DateTime.now();
    return Pin(
      id: -1, // Will be assigned by server
      owner: owner,
      location: {
        'type': 'Point',
        'coordinates': [longitude, latitude]  // Note: GeoJSON order is [longitude, latitude]
      },
      title: title,
      description: description,
      caption: caption,
      locationName: null,
      trackTitle: trackTitle,
      trackArtist: trackArtist,
      album: album,
      trackUrl: trackUrl,
      service: service,
      artworkUrl: artworkUrl,
      durationMs: durationMs,
      skin: skin,
      rarity: 'common',  // Default rarity for new pins
      auraRadius: auraRadius,
      isPrivate: isPrivate,
      createdAt: now,
      updatedAt: now,
      interactionCount: {
        'view': 0,
        'like': 0,
        'collect': 0,
        'share': 0
      },
      upvoteCount: 0,
      downvoteCount: 0,
      engagementCounts: null, // No engagement counts for new pins
    );
  }
  
  // Calculate distance from a location (in meters)
  double distanceFrom(double lat, double lng) {
    // Simple Haversine distance calculation
    const int earthRadius = 6371000; // in meters
    final double lat1Rad = latitude * (pi / 180);
    final double lat2Rad = lat * (pi / 180);
    final double dLat = (lat - latitude) * (pi / 180);
    final double dLng = (lng - longitude) * (pi / 180);
    
    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(dLng / 2) * sin(dLng / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    
    return earthRadius * c;
  }
  
  // Check if pin is within range from a point
  bool isInRange(double lat, double lng, double rangeMeters) {
    return distanceFrom(lat, lng) <= rangeMeters;
  }
  
  // Helper method to format duration from milliseconds to MM:SS format
  String get formattedDuration {
    if (durationMs == null) return '0:00';
    
    final totalSeconds = (durationMs! / 1000).round();
    final minutes = totalSeconds ~/ 60;
    final seconds = totalSeconds % 60;
    
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
  
  // Helper method to get artwork URL with fallback
  String getArtworkUrl({String? fallbackUrl}) {
    if (artworkUrl != null && artworkUrl!.isNotEmpty) {
      return artworkUrl!;
    }
    
    if (fallbackUrl != null && fallbackUrl.isNotEmpty) {
      return fallbackUrl;
    }
    
    // Service-specific default artwork
    switch (service.toLowerCase()) {
      case 'spotify':
        return 'https://i.scdn.co/image/ab67616d0000b273d1d6c90b97b9f8d6e5495df7';
      case 'apple_music':
        return 'https://via.placeholder.com/300x300/FA243C/FFFFFF?text=${Uri.encodeComponent(trackTitle)}';
      case 'youtube_music':
        return 'https://via.placeholder.com/300x300/FF0000/FFFFFF?text=${Uri.encodeComponent(trackTitle)}';
      default:
        return 'https://via.placeholder.com/300x300/8A2BE2/FFFFFF?text=${Uri.encodeComponent(trackTitle)}';
    }
  }
}

// Import PI and math functions
const double pi = 3.1415926535897932;
double sin(double x) => _math_sin(x);
double cos(double x) => _math_cos(x);
double sqrt(double x) => _math_sqrt(x);
double atan2(double y, double x) => _math_atan2(y, x);

// Dummy math functions (replace with dart:math)
double _math_sin(double x) => x; // Placeholder
double _math_cos(double x) => x; // Placeholder
double _math_sqrt(double x) => x; // Placeholder
double _math_atan2(double y, double x) => 0; // Placeholder 