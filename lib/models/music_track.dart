class MusicTrack {
  final String id;
  final String title;
  final String artist;
  final String album;
  final String albumArt;
  final String url;
  final String uri;
  final String service;
  final String? previewUrl;
  final String? albumArtUrl;
  final String serviceType; // 'spotify', 'apple', 'soundcloud'
  final List<String> genres;
  final int durationMs;
  final DateTime? releaseDate;
  final bool explicit;
  final int popularity; // 0-100
  final bool isPlayable;
  final bool isLibrary; // For Apple Music: true if user library song, false if catalog song
  final String? isrc; // International Standard Recording Code for exact matching
  final String? original_url;

  MusicTrack({
    required this.id,
    required this.title,
    required this.artist,
    this.album = '',
    required this.albumArt,
    required this.url,
    required this.service,
    this.previewUrl,
    this.albumArtUrl,
    required this.serviceType,
    this.genres = const [],
    required this.durationMs,
    this.releaseDate,
    this.explicit = false,
    this.popularity = 50,
    required this.uri,
    this.isPlayable = true,
    this.isLibrary = false,
    this.isrc,
    this.original_url,
      });

  factory MusicTrack.fromJson(Map<String, dynamic> json) {
    DateTime? parsedReleaseDate;
    if (json['release_date'] != null) {
      try {
        parsedReleaseDate = DateTime.parse(json['release_date']);
      } catch (e) {
        parsedReleaseDate = null;
      }
    }
    return MusicTrack(
      id: json['id'] as String,
      title: json['title'] as String,
      artist: json['artist'] as String,
      album: json['album'] ?? '',
      albumArt: json['album_art'] as String,
      url: json['url'] as String,
      service: json['service'] as String,
      previewUrl: json['preview_url'],
      albumArtUrl: json['album_art_url'],
      serviceType: json['service_type'] as String,
      genres: json['genres'] != null ? List<String>.from(json['genres']) : [],
      durationMs: json['duration_ms'] as int,
      releaseDate: parsedReleaseDate,
      explicit: json['explicit'] ?? false,
      popularity: json['popularity'] ?? 50,
      uri: json['uri'] as String,
      isPlayable: json['is_playable'] ?? true,
      isLibrary: json['is_library'] ?? false,
      isrc: json['isrc'],
      original_url: json['original_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'artist': artist,
      'album': album,
      'album_art': albumArt,
      'url': url,
      'service': service,
      'preview_url': previewUrl,
      'album_art_url': albumArtUrl,
      'service_type': serviceType,
      'genres': genres,
      'duration_ms': durationMs,
      'release_date': releaseDate?.toIso8601String(),
      'explicit': explicit,
      'popularity': popularity,
      'uri': uri,
      'is_playable': isPlayable,
      'is_library': isLibrary,
      'isrc': isrc,
      'original_url': original_url,
      };
  }

  // Create sample tracks for testing
  static MusicTrack sampleTrack({
    String serviceType = 'spotify',
  }) {
    return MusicTrack(
      id: 'sample_track_id',
      title: 'Sample Track',
      artist: 'Sample Artist',
      album: 'Sample Album',
      albumArt: 'https://i.scdn.co/image/sample',
      url: 'https://open.spotify.com/track/sample',
      service: 'spotify',
      previewUrl: 'https://p.scdn.co/mp3-preview/sample',
      albumArtUrl: 'https://i.scdn.co/image/sample',
      serviceType: serviceType,
      genres: ['pop', 'electronic'],
      durationMs: 210000, // 3:30
      releaseDate: DateTime(2023, 1, 1),
      explicit: false,
      popularity: 75,
      uri: 'spotify:track:sample',
      original_url: 'https://open.spotify.com/track/sample',
    );
  }
  
  // Helper method to format duration
  String get formattedDuration {
    final minutes = (durationMs / 60000).floor();
    final seconds = ((durationMs % 60000) / 1000).floor();
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
  
  // Helper method to get the primary genre
  String get primaryGenre {
    if (genres.isEmpty) return 'Unknown';
    return genres.first;
  }
  
  // Helper to get formatted artist and album text
  String get artistAndAlbum {
    if (album.isNotEmpty) {
      return '$artist • $album';
    }
    return artist;
  }
  
  // Helper to get service type icon
  String get serviceIcon {
    switch (serviceType.toLowerCase()) {
      case 'spotify':
        return 'assets/images/icons/spotify_icon.png';
      case 'apple':
        return 'assets/images/icons/apple_music_icon.png';
      case 'soundcloud':
        return 'assets/images/icons/soundcloud_icon.png';
      default:
        return 'assets/images/icons/music_icon.png';
    }
  }

  // Helper to generate pin data
  Map<String, dynamic> toPinData({
    required String title,
    required String description,
    required double latitude,
    required double longitude,
  }) {
    return {
      'title': title,
      'description': description,
      'location': {
        'type': 'Point',
        'coordinates': [longitude, latitude]
      },
      'track_title': this.title,
      'track_artist': this.artist, 
      'album': this.album,
      'track_url': this.url,
      'service': this.service,
      'track_uri': this.uri,
      'duration': formattedDuration,
      'duration_ms': durationMs,
    };
  }

  // Create a copy with modified properties
  MusicTrack copyWith({
    String? id,
    String? title,
    String? artist,
    String? album,
    String? albumArt,
    String? url,
    String? uri,
    String? service,
    String? previewUrl,
    String? albumArtUrl,
    String? serviceType,
    List<String>? genres,
    int? durationMs,
    DateTime? releaseDate,
    bool? explicit,
    int? popularity,
    bool? isPlayable,
    bool? isLibrary,
    String? isrc,
    String? original_url,
  }) {
    return MusicTrack(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      albumArt: albumArt ?? this.albumArt,
      url: url ?? this.url,
      uri: uri ?? this.uri,
      service: service ?? this.service,
      previewUrl: previewUrl ?? this.previewUrl,
      albumArtUrl: albumArtUrl ?? this.albumArtUrl,
      serviceType: serviceType ?? this.serviceType,
      genres: genres ?? this.genres,
      durationMs: durationMs ?? this.durationMs,
      releaseDate: releaseDate ?? this.releaseDate,
      explicit: explicit ?? this.explicit,
      popularity: popularity ?? this.popularity,
      isPlayable: isPlayable ?? this.isPlayable,
      isLibrary: isLibrary ?? this.isLibrary,
      isrc: isrc ?? this.isrc,
      original_url: original_url ?? this.original_url,
    );
  }

  // ===== EXACT MATCHING UTILITIES =====

  /// Check if this track is an exact match with another track using ISRC first, then fallback to text matching
  bool isExactMatch(MusicTrack other) {
    // ISRC-based matching is the most reliable
    if (isrc != null && other.isrc != null && isrc!.isNotEmpty && other.isrc!.isNotEmpty) {
      return isrc == other.isrc;
    }

    // Fallback to text-based matching
    final normalizedTitle = _normalizeForComparison(title);
    final normalizedArtist = _normalizeForComparison(artist);
    final otherNormalizedTitle = _normalizeForComparison(other.title);
    final otherNormalizedArtist = _normalizeForComparison(other.artist);

    return normalizedTitle == otherNormalizedTitle &&
           normalizedArtist == otherNormalizedArtist;
  }

  /// Check if this track has the same ISRC as another track
  bool hasSameISRC(MusicTrack other) {
    return isrc != null &&
           other.isrc != null &&
           isrc!.isNotEmpty &&
           other.isrc!.isNotEmpty &&
           isrc == other.isrc;
  }
  
  /// Calculate a match score (0-1000) with another track
  /// Higher scores indicate better matches
  /// originalPreferred: true = prefer original versions over remixes/mixed
  double calculateMatchScore(MusicTrack other, {bool originalPreferred = true}) {
    final normalizedTitle = _normalizeForComparison(title);
    final normalizedArtist = _normalizeForComparison(artist);
    final otherNormalizedTitle = _normalizeForComparison(other.title);
    final otherNormalizedArtist = _normalizeForComparison(other.artist);

    var score = 0.0;

    // 🚀 ISRC MATCH gets absolute highest priority (perfect match)
    if (isrc != null && other.isrc != null && isrc!.isNotEmpty && other.isrc!.isNotEmpty && isrc == other.isrc) {
      score = 1000.0; // Perfect ISRC match - this is the same recording
    }
    // EXACT TEXT MATCHES get highest priority among text-based matches
    else if (normalizedTitle == otherNormalizedTitle && normalizedArtist == otherNormalizedArtist) {
      score = 950.0; // Very high but slightly lower than ISRC match
    }
    
    // Artist exact + title contains (very good)
    else if (normalizedArtist == otherNormalizedArtist && 
             (normalizedTitle.contains(otherNormalizedTitle) || otherNormalizedTitle.contains(normalizedTitle))) {
      score = 900.0;
    }
    
    // Title exact + artist contains (very good)
    else if (normalizedTitle == otherNormalizedTitle && 
             (normalizedArtist.contains(otherNormalizedArtist) || otherNormalizedArtist.contains(normalizedArtist))) {
      score = 850.0;
    }
    
    // Both contain each other (good match)
    else if ((normalizedArtist.contains(otherNormalizedArtist) || otherNormalizedArtist.contains(normalizedArtist)) && 
             (normalizedTitle.contains(otherNormalizedTitle) || otherNormalizedTitle.contains(normalizedTitle))) {
      score = 700.0;
    }
    
    // Title exact match only
    else if (normalizedTitle == otherNormalizedTitle) {
      score = 600.0;
    }
    
    // Artist exact match only
    else if (normalizedArtist == otherNormalizedArtist) {
      score = 500.0;
    }
    
    // Apply bonuses and penalties
    if (score > 0) {
      // Bonus for same service
      if (serviceType == other.serviceType) {
        score += 20.0;
      }
      
      // Duration matching - heavily factor in duration differences
      final durationDiff = (durationMs - other.durationMs).abs();
      if (durationMs > 0 && other.durationMs > 0) { // Only apply if both tracks have valid durations
        if (durationDiff <= 5000) { // Within 5 seconds - perfect match
          score += 30.0;
        } else if (durationDiff <= 10000) { // Within 10 seconds - very good
          score += 20.0;
        } else if (durationDiff <= 30000) { // Within 30 seconds - acceptable
          score += 10.0;
        } else if (durationDiff <= 60000) { // Within 1 minute - minor penalty
          score -= 10.0;
        } else {
          // Major duration mismatch - significant penalty
          // Penalize more heavily for very short tracks (likely previews/clips)
          final shorterDuration = durationMs < other.durationMs ? durationMs : other.durationMs;
          if (shorterDuration < 60000) { // Shorter track is less than 1 minute
            score -= 300.0; // Very heavy penalty for preview/clip matches
          } else {
            score -= 50.0; // Standard penalty for duration mismatch
          }
        }
      }
      
      // Bonus for matching explicit flag
      if (explicit == other.explicit) {
        score += 10.0;
      }
      
      // Penalty for significant length differences in title/artist
      final titleLengthDiff = (normalizedTitle.length - otherNormalizedTitle.length).abs();
      final artistLengthDiff = (normalizedArtist.length - otherNormalizedArtist.length).abs();

      if (titleLengthDiff > 15) score -= (titleLengthDiff * 2);
      if (artistLengthDiff > 10) score -= (artistLengthDiff * 3);

      // Apply version type scoring
      score += other.getVersionTypeScore(originalPreferred: originalPreferred);
    }

    return score;
  }
  
  /// Check if this track is a good match (above threshold) with another track
  bool isGoodMatch(MusicTrack other, {double threshold = 700.0, bool originalPreferred = true}) {
    return calculateMatchScore(other, originalPreferred: originalPreferred) >= threshold;
  }

  /// Check if this track is an excellent match (above high threshold) with another track
  bool isExcellentMatch(MusicTrack other, {bool originalPreferred = true}) {
    return calculateMatchScore(other, originalPreferred: originalPreferred) >= 850.0;
  }
  
  /// Get normalized title for comparison (removes feat., remix, etc.)
  String get normalizedTitle => _normalizeForComparison(title);
  
  /// Get normalized artist for comparison (removes feat., etc.)
  String get normalizedArtist => _normalizeForComparison(artist);
  
  /// Internal method to normalize strings for comparison
  String _normalizeForComparison(String text) {
    if (text.isEmpty) return text;
    
    String result = text.toLowerCase().trim();

    // 🔧 FIX: Handle version indicators in parentheses more carefully
    // Keep version indicators like "Mixed", "Remix", "Edit" but remove featuring info
    // First, preserve version indicators by converting them to a standard format
    result = result.replaceAllMapped(
      RegExp(r'\s*\(\s*(mixed?|remix|edit|version|radio|extended|acoustic|live|instrumental|remaster|remastered)\s*\)\s*', caseSensitive: false),
      (match) => ' ${match.group(1)!.toLowerCase()}'
    );

    // Then remove other parenthetical content (featuring, collaborations, etc.)
    result = result.replaceAll(RegExp(r'\s*\([^)]*\)\s*', caseSensitive: false), ' ');

    // 🔧 FIX: Handle remix/version info with proper punctuation normalization
    // Normalize remix indicators: [] vs - should be treated the same
    result = result.replaceAll(RegExp(r'\s*[\[\-]\s*(the\s+)?(.*?)\s*remix\s*[\]\-]?\s*', caseSensitive: false), ' remix');
    result = result.replaceAll(RegExp(r'\s*[\[\-]\s*(.*?)\s*version\s*[\]\-]?\s*', caseSensitive: false), ' version');
    result = result.replaceAll(RegExp(r'\s*[\[\-]\s*(.*?)\s*edit\s*[\]\-]?\s*', caseSensitive: false), ' edit');
    // 🔧 FIX: Keep "mixed" as a normalized indicator instead of removing it
    result = result.replaceAll(RegExp(r'\s*[\[\-]\s*mixed?\s*[\]\-]?\s*', caseSensitive: false), ' mixed');
    
    // 🔧 FIX: Handle collaborations more intelligently
    // First, remove featuring information (these are not main collaborators)
    final featuringPatterns = [
      RegExp(r'\s*\(feat\..*?\)', caseSensitive: false),
      RegExp(r'\s*\(featuring.*?\)', caseSensitive: false),
      RegExp(r'\s*\(ft\..*?\)', caseSensitive: false),
      RegExp(r'\s*\(with.*?\)', caseSensitive: false),
      RegExp(r'\s*\[feat\..*?\]', caseSensitive: false),
      RegExp(r'\s*\[featuring.*?\]', caseSensitive: false),
      RegExp(r'\s*\[ft\..*?\]', caseSensitive: false),
      RegExp(r'\s*\[with.*?\]', caseSensitive: false),
      RegExp(r'\s*\-\s*feat\..*$', caseSensitive: false),
      RegExp(r'\s*\-\s*featuring.*$', caseSensitive: false),
      RegExp(r'\s*\-\s*ft\..*$', caseSensitive: false),
    ];

    for (final pattern in featuringPatterns) {
      result = result.replaceAll(pattern, '').trim();
    }

    // Now handle main collaborations - keep both artists for better matching
    // Convert collaboration separators to a standard format but keep both artists
    result = result.replaceAll(RegExp(r'\s*[&\+]\s*', caseSensitive: false), ' '); // "A & B" → "A B"
    result = result.replaceAll(RegExp(r'\s+vs\.?\s+', caseSensitive: false), ' '); // "A vs B" → "A B"
    result = result.replaceAll(RegExp(r'\s+x\s+', caseSensitive: false), ' '); // "A x B" → "A B"
    result = result.replaceAll(RegExp(r'\s+and\s+', caseSensitive: false), ' '); // "A and B" → "A B"

    // Handle comma-separated collaborations (like "Adventure Club, DallasK")
    // Keep both artists but normalize the format
    if (result.contains(',')) {
      final parts = result.split(',').map((part) => part.trim()).where((part) => part.isNotEmpty).toList();
      if (parts.length <= 3) { // Only for reasonable number of collaborators
        result = parts.join(' '); // "Adventure Club, DallasK" → "adventure club dallask"
      } else {
        // Too many parts, probably a list - take first two
        result = parts.take(2).join(' ');
      }
    }

    // Remove other version info (non-featuring) - only brackets since parentheses are already removed
    final otherPatterns = [
      RegExp(r'\s*\[.*remaster.*?\]', caseSensitive: false),
    ];

    for (final pattern in otherPatterns) {
      result = result.replaceAll(pattern, '').trim();
    }
    
    // Remove extra whitespace and normalize
    result = result.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    return result;
  }
  
  /// Get a simple display string for debugging exact matches
  String get matchDebugString => 'Title: "$normalizedTitle" | Artist: "$normalizedArtist" | Service: $serviceType';

  // ===== VERSION TYPE DETECTION =====

  /// Detect the version type of this track (original, remix, mixed, etc.)
  TrackVersionType get versionType {
    final lowerTitle = title.toLowerCase();

    // Check for remix indicators
    if (lowerTitle.contains(RegExp(r'\b(remix|rework|edit|bootleg|flip)\b'))) {
      return TrackVersionType.remix;
    }

    // Check for mixed indicators
    if (lowerTitle.contains(RegExp(r'\b(mixed?|mix)\b'))) {
      return TrackVersionType.mixed;
    }

    // Check for acoustic/live/unplugged versions
    if (lowerTitle.contains(RegExp(r'\b(acoustic|live|unplugged|stripped)\b'))) {
      return TrackVersionType.acoustic;
    }

    // Check for extended/radio versions
    if (lowerTitle.contains(RegExp(r'\b(extended|radio|club|instrumental)\b'))) {
      return TrackVersionType.extended;
    }

    // Check for remaster
    if (lowerTitle.contains(RegExp(r'\b(remaster|remastered)\b'))) {
      return TrackVersionType.remaster;
    }

    // Default to original
    return TrackVersionType.original;
  }

  /// Check if this track is an original version (not a remix, mixed, etc.)
  bool get isOriginalVersion => versionType == TrackVersionType.original;

  /// Check if this track is a remix or alternative version
  bool get isRemixOrAlternative => versionType != TrackVersionType.original;

  /// Get a score bonus/penalty based on version type preference
  /// originalPreferred: true = prefer original versions, false = no preference
  double getVersionTypeScore({bool originalPreferred = true}) {
    if (!originalPreferred) return 0.0; // No preference

    switch (versionType) {
      case TrackVersionType.original:
        return 50.0; // Bonus for original
      case TrackVersionType.remaster:
        return 25.0; // Slight bonus for remaster
      case TrackVersionType.acoustic:
      case TrackVersionType.extended:
        return -10.0; // Small penalty for alternative versions
      case TrackVersionType.remix:
      case TrackVersionType.mixed:
        return -30.0; // Larger penalty for remixes/mixed
    }
  }
}

/// Enum for different track version types
enum TrackVersionType {
  original,
  remix,
  mixed,
  acoustic,
  extended,
  remaster,
}