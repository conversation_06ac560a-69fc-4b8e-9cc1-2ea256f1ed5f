import 'package:flutter/material.dart';
import '../utils/text_encoding_utils.dart';

class Collection {
  final int id;
  final String name;
  final String description;
  final int itemCount;
  final DateTime lastUpdated;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> coverImageUrls;
  final Color? primaryColor;
  final bool isPublic;
  final int owner;
  final String? ownerName;
  final List<CollectionPin>? pins; // For detailed view

  Collection({
    required this.id,
    required this.name,
    required this.description,
    required this.itemCount,
    required this.lastUpdated,
    required this.createdAt,
    required this.updatedAt,
    this.coverImageUrls = const [],
    this.primaryColor,
    this.isPublic = true,
    required this.owner,
    this.ownerName,
    this.pins,
  });

  // Factory constructor for creating a Collection instance from API response
  factory Collection.fromJson(Map<String, dynamic> json) {
    // Clean the name and description to fix emoji encoding issues
    final rawName = json['name'] as String? ?? '';
    final rawDescription = json['description'] as String? ?? '';
    
    return Collection(
      id: json['id'] as int,
      name: TextEncodingUtils.cleanCollectionText(rawName, isDescription: false),
      description: TextEncodingUtils.cleanCollectionText(rawDescription, isDescription: true),
      itemCount: json['item_count'] as int? ?? 0,
      lastUpdated: json['last_updated'] != null 
          ? DateTime.parse(json['last_updated'] as String) 
          : DateTime.now(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      coverImageUrls: json['cover_image_urls'] != null 
          ? List<String>.from(json['cover_image_urls'] as List<dynamic>)
          : [],
      isPublic: json['is_public'] as bool? ?? true,
      owner: json['owner'] as int,
      ownerName: json['owner_name'] as String?,
      primaryColor: json['primary_color'] != null 
          ? _parseColor(json['primary_color'] as String)
          : null,
      pins: json['pins'] != null
          ? (json['pins'] as List<dynamic>)
              .map((pinJson) => CollectionPin.fromJson(pinJson))
              .toList()
          : null,
    );
  }

  // Method to convert Collection instance to JSON for API requests
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'is_public': isPublic,
      if (primaryColor != null) 'primary_color': _colorToString(primaryColor!),
      'cover_image_urls': coverImageUrls,
    };
  }

  // Helper method to parse color from hex string
  static Color? _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        colorString = colorString.substring(1);
      }
      if (colorString.length == 6) {
        colorString = 'FF$colorString'; // Add alpha if not present
      }
      return Color(int.parse(colorString, radix: 16));
    } catch (e) {
      return null;
    }
  }

  // Helper method to convert color to hex string
  static String _colorToString(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }

  // Create a copy with updated fields
  Collection copyWith({
    int? id,
    String? name,
    String? description,
    int? itemCount,
    DateTime? lastUpdated,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? coverImageUrls,
    Color? primaryColor,
    bool? isPublic,
    int? owner,
    String? ownerName,
    List<CollectionPin>? pins,
  }) {
    return Collection(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      itemCount: itemCount ?? this.itemCount,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      coverImageUrls: coverImageUrls ?? this.coverImageUrls,
      primaryColor: primaryColor ?? this.primaryColor,
      isPublic: isPublic ?? this.isPublic,
      owner: owner ?? this.owner,
      ownerName: ownerName ?? this.ownerName,
      pins: pins ?? this.pins,
    );
  }
}

class CollectionPin {
  final int id;
  final int? pin; // Regular pin ID (nullable for virtual pins)
  final int? virtualPin; // Virtual pin ID (nullable for regular pins)
  final Map<String, dynamic>? pinDetails;
  final DateTime addedAt;
  final bool isVirtual; // Indicates if this is a virtual pin

  CollectionPin({
    required this.id,
    this.pin,
    this.virtualPin,
    this.pinDetails,
    required this.addedAt,
    required this.isVirtual,
  });

  factory CollectionPin.fromJson(Map<String, dynamic> json) {
    return CollectionPin(
      id: json['id'] as int,
      pin: json['pin'] as int?,
      virtualPin: json['virtual_pin'] as int?,
      pinDetails: json['pin_details'] as Map<String, dynamic>?,
      addedAt: DateTime.parse(json['added_at'] as String),
      isVirtual: json['is_virtual'] as bool? ?? false,
    );
  }
} 