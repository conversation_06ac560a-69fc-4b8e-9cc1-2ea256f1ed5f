class BopDropUser {
  final int id;
  final String username;
  final String firstName;
  final String lastName;
  final String? avatarUrl;

  BopDropUser({
    required this.id,
    required this.username,
    required this.firstName,
    required this.lastName,
    this.avatarUrl,
  });

  factory BopDropUser.fromJson(Map<String, dynamic> json) {
    return BopDropUser(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      avatarUrl: json['profile_pic'],
    );
  }

  String get displayName => '$firstName $lastName'.trim();
  String get initials => '${firstName.isNotEmpty ? firstName[0] : ''}${lastName.isNotEmpty ? lastName[0] : ''}'.toUpperCase();
}

class BopDrop {
  final int id;
  final BopDropUser user;
  final String trackId;
  final String trackTitle;
  final String trackArtist;
  final String? trackAlbum;
  final String? albumArtUrl;
  final String? previewUrl;
  final String musicService;
  final String? caption;
  final String? mood;
  final bool isCurrentlyPlaying;
  final bool isActive;
  final bool friendsOnly;
  final int likeCount;
  final int viewCount;
  final bool isLikedByUser;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final String timeSinceCreated;

  BopDrop({
    required this.id,
    required this.user,
    required this.trackId,
    required this.trackTitle,
    required this.trackArtist,
    this.trackAlbum,
    this.albumArtUrl,
    this.previewUrl,
    this.musicService = 'spotify',
    this.caption,
    this.mood,
    required this.isCurrentlyPlaying,
    this.isActive = true,
    this.friendsOnly = true,
    required this.likeCount,
    required this.viewCount,
    required this.isLikedByUser,
    required this.createdAt,
    this.expiresAt,
    required this.timeSinceCreated,
  });

  factory BopDrop.fromJson(Map<String, dynamic> json) {
    return BopDrop(
      id: json['id'] ?? 0,
      user: json['user'] != null ? BopDropUser.fromJson(json['user']) : BopDropUser(
        id: 0,
        username: 'unknown',
        firstName: 'Unknown',
        lastName: 'User',
      ),
      trackId: json['track_id'] ?? '',
      trackTitle: json['track_title'] ?? '',
      trackArtist: json['track_artist'] ?? '',
      trackAlbum: json['track_album'],
      albumArtUrl: json['album_art_url'],
      previewUrl: json['preview_url'],
      musicService: json['music_service'] ?? 'spotify',
      caption: json['caption'],
      mood: json['mood'],
      isCurrentlyPlaying: json['is_currently_playing'] ?? false,
      isActive: json['is_active'] ?? true,
      friendsOnly: json['friends_only'] ?? true,
      likeCount: json['like_count'] ?? 0,
      viewCount: json['view_count'] ?? 0,
      isLikedByUser: json['is_liked_by_user'] ?? false,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : DateTime.now(),
      expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at']) : null,
      timeSinceCreated: json['time_since_created'] ?? 'just now',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'track_id': trackId,
      'track_title': trackTitle,
      'track_artist': trackArtist,
      'track_album': trackAlbum,
      'album_art_url': albumArtUrl,
      'preview_url': previewUrl,
      'music_service': musicService,
      'caption': caption,
      'mood': mood,
      'is_currently_playing': isCurrentlyPlaying,
      'friends_only': friendsOnly,
    };
  }

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  bool get isNew {
    return DateTime.now().difference(createdAt).inHours < 24; // Extended to 24 hours for better visibility
  }

  /// Check if this bop drop belongs to the current user
  bool isFromCurrentUser(String? currentUserId) {
    if (currentUserId == null) return false;
    return user.id.toString() == currentUserId;
  }
  
  /// Get display name for the user, showing "You" if it's the current user
  String getDisplayName(String? currentUserId) {
    if (isFromCurrentUser(currentUserId)) {
      return "You";
    }
    return user.displayName;
  }
}

class BopDropStats {
  final int totalDrops;
  final int activeDrops;
  final int totalLikesReceived;
  final int totalViewsReceived;
  final String? mostLikedTrack;
  final String? favoriteMood;
  final int dropsThisWeek;
  final double averageLikesPerDrop;

  BopDropStats({
    required this.totalDrops,
    required this.activeDrops,
    required this.totalLikesReceived,
    required this.totalViewsReceived,
    this.mostLikedTrack,
    this.favoriteMood,
    required this.dropsThisWeek,
    required this.averageLikesPerDrop,
  });

  factory BopDropStats.fromJson(Map<String, dynamic> json) {
    return BopDropStats(
      totalDrops: json['total_drops'] ?? 0,
      activeDrops: json['active_drops'] ?? 0,
      totalLikesReceived: json['total_likes_received'] ?? 0,
      totalViewsReceived: json['total_views_received'] ?? 0,
      mostLikedTrack: json['most_liked_track'],
      favoriteMood: json['favorite_mood'],
      dropsThisWeek: json['drops_this_week'] ?? 0,
      averageLikesPerDrop: (json['average_likes_per_drop'] ?? 0.0).toDouble(),
    );
  }
}

// Available moods
enum BopDropMood {
  happy,
  sad,
  energetic,
  chill,
  nostalgic,
  party,
  focus,
  romantic,
  angry,
  other;

  String get displayName {
    switch (this) {
      case BopDropMood.happy:
        return 'Happy';
      case BopDropMood.sad:
        return 'Sad';
      case BopDropMood.energetic:
        return 'Energetic';
      case BopDropMood.chill:
        return 'Chill';
      case BopDropMood.nostalgic:
        return 'Nostalgic';
      case BopDropMood.party:
        return 'Party';
      case BopDropMood.focus:
        return 'Focus';
      case BopDropMood.romantic:
        return 'Romantic';
      case BopDropMood.angry:
        return 'Angry';
      case BopDropMood.other:
        return 'Other';
    }
  }

  String get emoji {
    switch (this) {
      case BopDropMood.happy:
        return '😊';
      case BopDropMood.sad:
        return '😢';
      case BopDropMood.energetic:
        return '⚡';
      case BopDropMood.chill:
        return '😎';
      case BopDropMood.nostalgic:
        return '🌅';
      case BopDropMood.party:
        return '🎉';
      case BopDropMood.focus:
        return '🎯';
      case BopDropMood.romantic:
        return '💕';
      case BopDropMood.angry:
        return '😠';
      case BopDropMood.other:
        return '🎵';
    }
  }
} 