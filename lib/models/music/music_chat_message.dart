import '../music_track.dart';

class MessageReaction {
  final int id;
  final int userId;
  final String username;
  final String emoji;
  final DateTime createdAt;

  MessageReaction({
    required this.id,
    required this.userId,
    required this.username,
    required this.emoji,
    required this.createdAt,
  });

  factory MessageReaction.fromJson(Map<String, dynamic> json) {
    return MessageReaction(
      id: json['id'] is String ? int.parse(json['id']) : json['id'] as int,
      userId: json['user']['id'] is String ? int.parse(json['user']['id']) : json['user']['id'] as int,
      username: json['user']['username'] as String,
      emoji: json['emoji'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }
}

class MusicChatMessage {
  final String id;
  final int senderId;
  final int receiverId;
  final MusicTrack track;
  final String? message;
  final DateTime timestamp;
  final bool isRead;
  final List<MessageReaction> reactions;
  final int? conversationId;
  final String messageType;

  MusicChatMessage({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.track,
    this.message,
    required this.timestamp,
    this.isRead = false,
    this.reactions = const [],
    this.conversationId,
    this.messageType = 'track_recommendation',
  });

  factory MusicChatMessage.fromJson(Map<String, dynamic> json) {
    return MusicChatMessage(
      id: json['id'].toString(),
      senderId: json['sender'] != null 
          ? (json['sender']['id'] is String ? int.parse(json['sender']['id']) : json['sender']['id'] as int)
          : (json['sender_id'] is String ? int.parse(json['sender_id']) : json['sender_id'] as int),
      receiverId: json['receiver_id'] != null 
          ? (json['receiver_id'] is String ? int.parse(json['receiver_id']) : json['receiver_id'] as int)
          : 0, // Handle missing receiver_id
      track: MusicTrack(
        id: json['track_id'] as String? ?? '',
        title: json['track_title'] as String? ?? _extractTitleFromPreview(json['preview'] as String?),
        artist: json['track_artist'] as String? ?? _extractArtistFromPreview(json['preview'] as String?),
        albumArt: json['album_art_url'] as String? ?? json['track_artwork_url'] as String? ?? '',
        url: json['track_url'] as String? ?? json['track_external_url'] as String? ?? '',
        service: json['music_service'] as String? ?? json['service'] as String? ?? 'spotify',
        serviceType: json['music_service'] as String? ?? json['service'] as String? ?? 'spotify',
        durationMs: json['duration_ms'] as int? ?? 200000, // Use actual duration or default
        uri: json['track_id'] as String? ?? '',
        album: json['track_album'] as String? ?? '',
        previewUrl: json['preview_url'] as String? ?? json['track_preview_url'] as String?,
      ),
      message: json['text_content'] as String? ?? json['content'] as String?,
      timestamp: DateTime.parse(json['created_at'] as String),
      isRead: json['is_read'] as bool? ?? false,
      reactions: (json['reactions'] as List<dynamic>?)
          ?.map((r) => MessageReaction.fromJson(r as Map<String, dynamic>))
          .toList() ?? [],
      conversationId: json['conversation'] != null
          ? (json['conversation'] is String ? int.parse(json['conversation']) : json['conversation'] as int)
          : null,
      messageType: json['message_type'] as String? ?? 'track_recommendation',
    );
  }

  // Helper method to extract title from preview string like "🎵 Ghost Town by Kanye West, PARTYNEXTDOOR"
  static String _extractTitleFromPreview(String? preview) {
    if (preview == null || preview.isEmpty) return '';
    
    // Remove emoji and "by" part
    final cleaned = preview.replaceAll('🎵', '').trim();
    if (cleaned.contains(' by ')) {
      return cleaned.split(' by ').first.trim();
    }
    return cleaned;
  }

  // Helper method to extract artist from preview string
  static String _extractArtistFromPreview(String? preview) {
    if (preview == null || preview.isEmpty) return '';
    
    // Remove emoji and extract artist part
    final cleaned = preview.replaceAll('🎵', '').trim();
    if (cleaned.contains(' by ')) {
      return cleaned.split(' by ').last.trim();
    }
    return '';
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sender_id': senderId,
      'receiver_id': receiverId,
      'track': track.toJson(),
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'is_read': isRead,
    };
  }
} 