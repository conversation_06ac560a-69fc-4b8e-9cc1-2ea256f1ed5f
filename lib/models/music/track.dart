class Track {
  final String id;
  final String uri;
  final String title;
  final String artist;
  final String? albumArt;

  const Track({
    required this.id,
    required this.uri,
    required this.title,
    required this.artist,
    this.albumArt,
  });

  factory Track.fromJson(Map<String, dynamic> json) {
    return Track(
      id: json['id'] as String,
      uri: json['uri'] as String,
      title: json['name'] as String,
      artist: (json['artists'] as List).first['name'] as String,
      albumArt: json['album']?['images']?.first['url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'uri': uri,
      'name': title,
      'artists': [{'name': artist}],
      'album': albumArt != null ? {'images': [{'url': albumArt}]} : null,
    };
  }
} 