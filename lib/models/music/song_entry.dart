import 'package:flutter/material.dart';

class SongEntry {
  final int id;
  final String songId;
  final String title;
  final String artist;
  final String albumArt;
  final String dropperName;
  final String? dropperAvatarUrl;
  final bool isFromFriend;
  final DateTime droppedAt;
  final int upvotes;
  final int downvotes;
  final int voteScore;
  final bool? userVote;

  const SongEntry({
    required this.id,
    required this.songId,
    required this.title,
    required this.artist,
    required this.albumArt,
    required this.dropperName,
    this.dropperAvatarUrl,
    required this.isFromFriend,
    required this.droppedAt,
    required this.upvotes,
    required this.downvotes,
    required this.voteScore,
    this.userVote,
  });

  // Helper to get the total score
  int get score => voteScore;

  // Helper to get properly formatted Spotify URI
  String get spotifyUri {
    if (songId.startsWith('spotify:track:')) {
      return songId;
    }
    return 'spotify:track:$songId';
  }

  // Create a copy with updated values
  SongEntry copyWith({
    int? id,
    String? songId,
    String? title,
    String? artist,
    String? albumArt,
    String? dropperName,
    String? dropperAvatarUrl,
    bool? isFromFriend,
    DateTime? droppedAt,
    int? upvotes,
    int? downvotes,
    int? voteScore,
    bool? userVote,
  }) {
    return SongEntry(
      id: id ?? this.id,
      songId: songId ?? this.songId,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      albumArt: albumArt ?? this.albumArt,
      dropperName: dropperName ?? this.dropperName,
      dropperAvatarUrl: dropperAvatarUrl ?? this.dropperAvatarUrl,
      isFromFriend: isFromFriend ?? this.isFromFriend,
      droppedAt: droppedAt ?? this.droppedAt,
      upvotes: upvotes ?? this.upvotes,
      downvotes: downvotes ?? this.downvotes,
      voteScore: voteScore ?? this.voteScore,
      userVote: userVote ?? this.userVote,
    );
  }
} 