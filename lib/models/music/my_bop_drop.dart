import 'package:bop_maps/models/music/bop_drop.dart';

class MyBopDropData {
  final String userId;
  final String username;
  final String? userAvatar;
  final int totalDrops;
  final EngagementStats totalEngagement;
  final List<BopDrop> bopDrops;
  final BopDrop? lastBopDrop;

  const MyBopDropData({
    required this.userId,
    required this.username,
    this.userAvatar,
    required this.totalDrops,
    required this.totalEngagement,
    required this.bopDrops,
    this.lastBopDrop,
  });

  factory MyBopDropData.fromJson(Map<String, dynamic> json) {
    final userInfo = json['user_info'] as Map<String, dynamic>;
    final bopDropsList = (json['bop_drops'] as List?)?.map((drop) => BopDrop.fromJson(drop)).toList() ?? [];
    
    return MyBopDropData(
      userId: userInfo['id'].toString(), // Convert int to String
      username: userInfo['username'] as String,
      userAvatar: userInfo['profile_pic'] as String?, // Use correct field name
      totalDrops: userInfo['total_drops'] as int,
      totalEngagement: EngagementStats.fromJson(userInfo['total_engagement']),
      bopDrops: bopDropsList,
      lastBopDrop: bopDropsList.isNotEmpty ? bopDropsList.first : null,
    );
  }
}

class EngagementStats {
  final int likes;
  final int views;
  final double engagementRate;

  const EngagementStats({
    required this.likes,
    required this.views,
    required this.engagementRate,
  });

  factory EngagementStats.fromJson(Map<String, dynamic> json) {
    return EngagementStats(
      likes: json['likes'] as int,
      views: json['views'] as int,
      engagementRate: (json['engagement_rate'] as num).toDouble(),
    );
  }
} 