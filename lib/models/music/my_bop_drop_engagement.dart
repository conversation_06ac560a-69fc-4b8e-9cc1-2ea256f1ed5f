import '../user.dart';
import 'bop_drop.dart';

class MyBopDropEngagement {
  final UserInfo userInfo;
  final List<BopDropWithEngagement> bopDrops;

  const MyBopDropEngagement({
    required this.userInfo,
    required this.bopDrops,
  });

  factory MyBopDropEngagement.fromJson(Map<String, dynamic> json) {
    return MyBopDropEngagement(
      userInfo: UserInfo.fromJson(json['user_info'] ?? {}),
      bopDrops: (json['bop_drops'] as List<dynamic>? ?? [])
          .map((item) => BopDropWithEngagement.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_info': userInfo.toJson(),
      'bop_drops': bopDrops.map((item) => item.toJson()).toList(),
    };
  }
}

class UserInfo {
  final String id;
  final String username;
  final String? profilePic;
  final int totalDrops;

  const UserInfo({
    required this.id,
    required this.username,
    this.profilePic,
    required this.totalDrops,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id']?.toString() ?? '',
      username: json['username']?.toString() ?? '',
      profilePic: json['profile_pic']?.toString(),
      totalDrops: json['total_drops'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'profile_pic': profilePic,
      'total_drops': totalDrops,
    };
  }
}

class BopDropWithEngagement {
  final String id;
  final User user;
  final String trackTitle;
  final String trackArtist;
  final String? trackAlbum;
  final String? artworkUrl;
  final String? locationName;
  final String? caption;
  final DateTime createdAt;
  final List<UserEngagement> likes;
  final List<UserEngagement> views;
  final EngagementStats engagementStats;

  const BopDropWithEngagement({
    required this.id,
    required this.user,
    required this.trackTitle,
    required this.trackArtist,
    this.trackAlbum,
    this.artworkUrl,
    this.locationName,
    this.caption,
    required this.createdAt,
    required this.likes,
    required this.views,
    required this.engagementStats,
  });

  factory BopDropWithEngagement.fromJson(Map<String, dynamic> json) {
    return BopDropWithEngagement(
      id: json['id']?.toString() ?? '',
      user: User.fromJson(json['user'] as Map<String, dynamic>? ?? {}),
      trackTitle: json['track_title']?.toString() ?? '',
      trackArtist: json['track_artist']?.toString() ?? '',
      trackAlbum: json['track_album']?.toString(),
      artworkUrl: json['artwork_url']?.toString(),
      locationName: json['location_name']?.toString(),
      caption: json['caption']?.toString(),
      createdAt: DateTime.tryParse(json['created_at']?.toString() ?? '') ?? DateTime.now(),
      likes: (json['likes'] as List<dynamic>? ?? [])
          .map((item) => UserEngagement.fromJson(item as Map<String, dynamic>))
          .toList(),
      views: (json['views'] as List<dynamic>? ?? [])
          .map((item) => UserEngagement.fromJson(item as Map<String, dynamic>))
          .toList(),
      engagementStats: EngagementStats.fromJson(json['engagement_stats'] as Map<String, dynamic>? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': user.toJson(),
      'track_title': trackTitle,
      'track_artist': trackArtist,
      'track_album': trackAlbum,
      'artwork_url': artworkUrl,
      'location_name': locationName,
      'caption': caption,
      'created_at': createdAt.toIso8601String(),
      'likes': likes.map((item) => item.toJson()).toList(),
      'views': views.map((item) => item.toJson()).toList(),
      'engagement_stats': engagementStats.toJson(),
    };
  }
}

class UserEngagement {
  final String userId;
  final String username;
  final String? firstName;
  final String? lastName;
  final String? profilePic;
  final DateTime? likedAt;
  final DateTime? viewedAt;

  const UserEngagement({
    required this.userId,
    required this.username,
    this.firstName,
    this.lastName,
    this.profilePic,
    this.likedAt,
    this.viewedAt,
  });

  String get displayName {
    final firstName = this.firstName ?? '';
    final lastName = this.lastName ?? '';
    final fullName = [firstName, lastName].where((s) => s.isNotEmpty).join(' ');
    return fullName.isNotEmpty ? fullName : username;
  }

  factory UserEngagement.fromJson(Map<String, dynamic> json) {
    return UserEngagement(
      userId: json['user_id']?.toString() ?? '',
      username: json['username']?.toString() ?? '',
      firstName: json['first_name']?.toString(),
      lastName: json['last_name']?.toString(),
      profilePic: json['profile_pic']?.toString(),
      likedAt: json['liked_at'] != null ? DateTime.tryParse(json['liked_at'].toString()) : null,
      viewedAt: json['viewed_at'] != null ? DateTime.tryParse(json['viewed_at'].toString()) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'username': username,
      'first_name': firstName,
      'last_name': lastName,
      'profile_pic': profilePic,
      'liked_at': likedAt?.toIso8601String(),
      'viewed_at': viewedAt?.toIso8601String(),
    };
  }
}

class EngagementStats {
  final int totalLikes;
  final int totalViews;
  final int totalShares;
  final double engagementRate;
  final bool isTrending;

  const EngagementStats({
    required this.totalLikes,
    required this.totalViews,
    required this.totalShares,
    required this.engagementRate,
    required this.isTrending,
  });

  factory EngagementStats.fromJson(Map<String, dynamic> json) {
    return EngagementStats(
      totalLikes: json['total_likes'] as int? ?? 0,
      totalViews: json['total_views'] as int? ?? 0,
      totalShares: json['total_shares'] as int? ?? 0,
      engagementRate: (json['engagement_rate'] as num?)?.toDouble() ?? 0.0,
      isTrending: json['is_trending'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_likes': totalLikes,
      'total_views': totalViews,
      'total_shares': totalShares,
      'engagement_rate': engagementRate,
      'is_trending': isTrending,
    };
  }
} 