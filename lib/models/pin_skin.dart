class PinSkin {
  final int id;
  final String name;
  final String slug;
  final String image;
  final String? description;
  final String skinType; // 'HOUSE' or 'ARTIST'
  final String? artist;
  final bool isPremium;
  final int? challengeId;
  final int? achievementId;
  final String unlockRule;
  final bool locked;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final bool isEquipped;
  final bool isUnlocked;
  final String? modelPath;
  final DateTime? unlockedAt;
  final DateTime? expiryDate;
  final Map<String, dynamic>? challengeDetails;
  final Map<String, dynamic>? achievementDetails;
  final Map<String, dynamic>? expiryDetails;
  final bool isExpired;
  final bool isLimitedTime;
  final double? timeRemaining; // in seconds

  PinSkin({
    required this.id,
    required this.name,
    this.slug = '',
    required this.image,
    this.description,
    this.skinType = 'HOUSE',
    this.artist,
    this.isPremium = false,
    this.challengeId,
    this.achievementId,
    this.unlockRule = 'ALWAYS_AVAILABLE',
    this.locked = false,
    this.metadata = const {},
    required this.createdAt,
    this.isEquipped = false,
    this.isUnlocked = false,
    this.modelPath,
    this.unlockedAt,
    this.expiryDate,
    this.challengeDetails,
    this.achievementDetails,
    this.expiryDetails,
    this.isExpired = false,
    this.isLimitedTime = false,
    this.timeRemaining,
  });

  // Factory for API response format
  factory PinSkin.fromApiJson(Map<String, dynamic> json) {
    return PinSkin(
      id: json['id'] ?? 0,
      name: json['name']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
      image: _sanitizeImageUrl(json['image']),
      description: json['description']?.toString(),
      skinType: json['skin_type']?.toString() ?? 'HOUSE',
      artist: json['artist']?.toString(),
      isPremium: json['is_premium'] ?? false,
      challengeId: json['challenge_id'] is int ? json['challenge_id'] : null,
      achievementId: json['achievement_id'] is int ? json['achievement_id'] : null,
      unlockRule: json['unlock_rule']?.toString() ?? 'UNKNOWN',
      locked: json['locked'] ?? false,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at']) 
          : DateTime.now(),
      isEquipped: json['is_equipped'] ?? false,
      isUnlocked: json['is_unlocked'] ?? false,
      modelPath: json['model_path']?.toString(),
      expiryDate: json['expiry_date'] != null 
          ? DateTime.parse(json['expiry_date']) 
          : null,
      challengeDetails: json['challenge_details'] != null
          ? Map<String, dynamic>.from(json['challenge_details'])
          : null,
      achievementDetails: json['achievement_details'] != null
          ? Map<String, dynamic>.from(json['achievement_details'])
          : null,
      expiryDetails: json['expiry_details'] != null
          ? Map<String, dynamic>.from(json['expiry_details'])
          : null,
      isExpired: json['is_expired'] ?? false,
      isLimitedTime: json['is_limited_time'] ?? false,
      timeRemaining: json['time_remaining']?.toDouble(),
    );
  }

  // Legacy factory for existing JSON format
  factory PinSkin.fromJson(Map<String, dynamic> json) {
    return PinSkin(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      slug: json['slug'] ?? '',
      image: _sanitizeImageUrl(json['image']),
      description: json['description'],
      isPremium: json['is_premium'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      isEquipped: json['is_equipped'] ?? false,
      isUnlocked: json['is_unlocked'] ?? false,
      modelPath: json['model_path'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'image': image,
      'description': description,
      'skin_type': skinType,
      'artist': artist,
      'is_premium': isPremium,
      'challenge_id': challengeId,
      'achievement_id': achievementId,
      'unlock_rule': unlockRule,
      'locked': locked,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'is_equipped': isEquipped,
      'is_unlocked': isUnlocked,
      'model_path': modelPath,
      'unlocked_at': unlockedAt?.toIso8601String(),
      'expiry_date': expiryDate?.toIso8601String(),
      'challenge_details': challengeDetails,
      'achievement_details': achievementDetails,
      'expiry_details': expiryDetails,
      'is_expired': isExpired,
      'is_limited_time': isLimitedTime,
      'time_remaining': timeRemaining,
    };
  }

  PinSkin copyWith({
    int? id,
    String? name,
    String? slug,
    String? image,
    String? description,
    String? skinType,
    String? artist,
    bool? isPremium,
    int? challengeId,
    int? achievementId,
    String? unlockRule,
    bool? locked,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    bool? isEquipped,
    bool? isUnlocked,
    String? modelPath,
    DateTime? unlockedAt,
    DateTime? expiryDate,
    Map<String, dynamic>? challengeDetails,
    Map<String, dynamic>? achievementDetails,
    Map<String, dynamic>? expiryDetails,
    bool? isExpired,
    bool? isLimitedTime,
    double? timeRemaining,
  }) {
    return PinSkin(
      id: id ?? this.id,
      name: name ?? this.name,
      slug: slug ?? this.slug,
      image: image ?? this.image,
      description: description ?? this.description,
      skinType: skinType ?? this.skinType,
      artist: artist ?? this.artist,
      isPremium: isPremium ?? this.isPremium,
      challengeId: challengeId ?? this.challengeId,
      achievementId: achievementId ?? this.achievementId,
      unlockRule: unlockRule ?? this.unlockRule,
      locked: locked ?? this.locked,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      isEquipped: isEquipped ?? this.isEquipped,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      modelPath: modelPath ?? this.modelPath,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      expiryDate: expiryDate ?? this.expiryDate,
      challengeDetails: challengeDetails ?? this.challengeDetails,
      achievementDetails: achievementDetails ?? this.achievementDetails,
      expiryDetails: expiryDetails ?? this.expiryDetails,
      isExpired: isExpired ?? this.isExpired,
      isLimitedTime: isLimitedTime ?? this.isLimitedTime,
      timeRemaining: timeRemaining ?? this.timeRemaining,
    );
  }

  // Helper methods
  bool get isArtistSkin => skinType == 'ARTIST';
  bool get isHouseSkin => skinType == 'HOUSE';
  bool get isChallengeSkin => challengeId != null;
  bool get isAchievementSkin => achievementId != null;
  bool get requiresChallenge => unlockRule.contains('CHALLENGE');
  bool get requiresAchievement => unlockRule.contains('ACHIEVEMENT');
  bool get isTopRankerReward => unlockRule.contains('TOP_');
  bool get requiresPremium => unlockRule == 'PREMIUM_SUBSCRIPTION';
  bool get isAlwaysAvailable => unlockRule == 'ALWAYS_AVAILABLE';
  
  String get unlockRequirementText {
    if (unlockRule.contains('TOP_')) {
      final match = RegExp(r'TOP_(\d+)').firstMatch(unlockRule);
      final n = match?.group(1) ?? '3';
      return 'Top $n challenge winners';
    }
    
    switch (unlockRule) {
      case 'ALWAYS_AVAILABLE':
        return 'Free to unlock';
      case 'PREMIUM_SUBSCRIPTION':
        return 'Premium subscription required';
      case 'COMPLETE_WEEKLY_CHALLENGE':
        return 'Complete weekly challenge';
      default:
        if (unlockRule.contains('COMPLETE_ACHIEVEMENT_')) {
          return 'Complete achievement';
        }
        return 'Special requirement';
    }
  }

  String get formattedTimeRemaining {
    if (timeRemaining == null || timeRemaining! <= 0) return 'Expired';
    
    final seconds = timeRemaining!.toInt();
    final days = seconds ~/ 86400;
    final hours = (seconds % 86400) ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    
    if (days > 0) {
      return '$days day${days > 1 ? 's' : ''} ${hours}h';
    } else if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PinSkin && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PinSkin(id: $id, name: $name, type: $skinType, isPremium: $isPremium, locked: $locked)';
  }

  static String _sanitizeImageUrl(dynamic raw) {
    if (raw == null) return '';
    final str = raw.toString().trim();
    // Remove wrapping quotation marks if present
    return str.startsWith('"') && str.endsWith('"')
        ? str.substring(1, str.length - 1)
        : str;
  }
} 