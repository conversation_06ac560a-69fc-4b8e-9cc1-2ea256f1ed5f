import 'exploration_settings.dart';

class CustomizationData {
  final List<String> preferredArtists;
  final List<String> preferredGenres;
  final ExplorationSettings explorationSettings;
  final Map<String, String>? artistImageUrls;
  final Map<String, String>? artistSpotifyIds;
  final Map<String, List<String>>? artistGenres;

  const CustomizationData({
    required this.preferredArtists,
    required this.preferredGenres,
    required this.explorationSettings,
    this.artistImageUrls,
    this.artistSpotifyIds,
    this.artistGenres,
  });

  /// Create empty customization data
  factory CustomizationData.empty() => CustomizationData(
        preferredArtists: const [],
        preferredGenres: const [],
        explorationSettings: ExplorationSettings.defaults(),
      );

  /// Create from JSON
  factory CustomizationData.fromJson(Map<String, dynamic> json) =>
      CustomizationData(
        preferredArtists: List<String>.from(json['preferredArtists'] ?? []),
        preferredGenres: List<String>.from(json['preferredGenres'] ?? []),
        explorationSettings: ExplorationSettings.fromJson(
          json['explorationSettings'] ?? {},
        ),
      );

  /// Convert to JSON
  Map<String, dynamic> toJson() => {
        'preferredArtists': preferredArtists,
        'preferredGenres': preferredGenres,
        'explorationSettings': explorationSettings.toJson(),
      };

  /// Create a copy with updated values
  CustomizationData copyWith({
    List<String>? preferredArtists,
    List<String>? preferredGenres,
    ExplorationSettings? explorationSettings,
  }) =>
      CustomizationData(
        preferredArtists: preferredArtists ?? this.preferredArtists,
        preferredGenres: preferredGenres ?? this.preferredGenres,
        explorationSettings: explorationSettings ?? this.explorationSettings,
      );

  /// Check if data has changes compared to another instance
  bool hasChanges(CustomizationData other) {
    return !_listEquals(preferredArtists, other.preferredArtists) ||
        !_listEquals(preferredGenres, other.preferredGenres) ||
        explorationSettings != other.explorationSettings;
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomizationData &&
          runtimeType == other.runtimeType &&
          _listEquals(preferredArtists, other.preferredArtists) &&
          _listEquals(preferredGenres, other.preferredGenres) &&
          explorationSettings == other.explorationSettings;

  @override
  int get hashCode =>
      preferredArtists.hashCode ^
      preferredGenres.hashCode ^
      explorationSettings.hashCode;

  @override
  String toString() =>
      'CustomizationData(artists: ${preferredArtists.length}, genres: ${preferredGenres.length}, mode: ${explorationSettings.mode})';
}