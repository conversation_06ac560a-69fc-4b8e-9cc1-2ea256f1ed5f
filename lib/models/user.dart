class User {
  final int id;
  final String username;
  final String email;
  final String? profilePicUrl;
  final String? profilePictureUrl; // Alias for compatibility
  final String? bio;
  final String? location;
  final bool isVerified;
  final List<String> favoriteGenres;
  final Map<String, bool> connectedServices;
  final DateTime createdAt;
  final DateTime? lastActive;
  final String? firstName;
  final String? lastName;
  final String? displayName;
  final int? mutualFriendsCount;

  // School verification fields
  final bool isSchoolVerified;
  final String? schoolName;
  final String? schoolDomain;
  final String? schoolId;
  final DateTime? schoolVerifiedAt;

  // Music preferences
  final List<String>? topGenres;
  final List<String>? topArtists;
  
  // Artist genre mapping for fast validation (e.g., {'artist_name': ['genre1', 'genre2']})
  final Map<String, List<String>>? artistGenres;
  
  // Artist image URLs mapping (e.g., {'artist_name': 'https://image.url'})
  final Map<String, String>? artistImageUrls;
  
  // Artist Spotify IDs mapping (e.g., {'artist_name': 'spotify_id'})
  final Map<String, String>? artistSpotifyIds;

  User({
    required this.id,
    required this.username,
    required this.email,
    this.profilePicUrl,
    this.profilePictureUrl,
    this.bio,
    this.location,
    required this.isVerified,
    required this.favoriteGenres,
    required this.connectedServices,
    required this.createdAt,
    this.lastActive,
    this.firstName,
    this.lastName,
    this.displayName,
    this.mutualFriendsCount,
    this.isSchoolVerified = false,
    this.schoolName,
    this.schoolDomain,
    this.schoolId,
    this.schoolVerifiedAt,
    this.topGenres,
    this.topArtists,
    this.artistGenres,
    this.artistImageUrls,
    this.artistSpotifyIds,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    try {
      return User(
        id: json['id'] ?? 0,
        username: json['username'] ?? '',
        email: json['email'] ?? '',
        profilePicUrl: json['profile_pic'],
        profilePictureUrl: json['profile_picture_url'] ?? json['profile_pic'],
        bio: json['bio'],
        location: json['location'],
        isVerified: json['is_verified'] ?? false,
        favoriteGenres: json['favorite_genres'] != null 
            ? List<String>.from(json['favorite_genres']) 
            : <String>[],
        connectedServices: {
          'spotify': json['spotify_connected'] ?? false,
          'apple_music': json['apple_music_connected'] ?? false,
          'soundcloud': json['soundcloud_connected'] ?? false,
        },
        createdAt: json['created_at'] != null 
            ? DateTime.parse(json['created_at'])
            : DateTime.now(),
        lastActive: json['last_active'] != null
            ? DateTime.parse(json['last_active'])
            : null,
        firstName: json['first_name'],
        lastName: json['last_name'],
        displayName: json['display_name'] ?? json['name'],
        mutualFriendsCount: json['mutual_friends_count'],
        isSchoolVerified: json['is_school_verified'] ?? false,
        schoolName: json['school_name'],
        schoolDomain: json['school_domain'],
        schoolId: json['school_id'],
        schoolVerifiedAt: json['school_verified_at'] != null
            ? DateTime.parse(json['school_verified_at'])
            : null,
        topGenres: json['top_genres'] != null 
            ? List<String>.from(json['top_genres']) 
            : null,
        topArtists: json['top_artists'] != null 
            ? List<String>.from(json['top_artists']) 
            : null,
        artistGenres: json['artist_genres'] != null 
            ? Map<String, List<String>>.from(
                json['artist_genres'].map((key, value) => 
                  MapEntry(key, List<String>.from(value))
                )
              )
            : null,
        artistImageUrls: json['artist_image_urls'] != null 
            ? Map<String, String>.from(json['artist_image_urls'])
            : null,
        artistSpotifyIds: json['artist_spotify_ids'] != null 
            ? Map<String, String>.from(json['artist_spotify_ids'])
            : null,
      );
    } catch (e) {
      print('Error parsing User from JSON: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'profile_pic': profilePicUrl,
      'profile_picture_url': profilePictureUrl,
      'bio': bio,
      'location': location,
      'is_verified': isVerified,
      'favorite_genres': favoriteGenres,
      'spotify_connected': connectedServices['spotify'],
      'apple_music_connected': connectedServices['apple_music'],
      'soundcloud_connected': connectedServices['soundcloud'],
      'created_at': createdAt.toIso8601String(),
      'last_active': lastActive?.toIso8601String(),
      'first_name': firstName,
      'last_name': lastName,
      'display_name': displayName,
      'mutual_friends_count': mutualFriendsCount,
      'is_school_verified': isSchoolVerified,
      'school_name': schoolName,
      'school_domain': schoolDomain,
      'school_id': schoolId,
      'school_verified_at': schoolVerifiedAt?.toIso8601String(),
      'top_genres': topGenres,
      'top_artists': topArtists,
      'artist_genres': artistGenres,
      'artist_image_urls': artistImageUrls,
      'artist_spotify_ids': artistSpotifyIds,
    };
  }

  // Create a copy of this user with updated fields
  User copyWith({
    int? id,
    String? username,
    String? email,
    String? profilePicUrl,
    String? profilePictureUrl,
    String? bio,
    String? location,
    bool? isVerified,
    List<String>? favoriteGenres,
    Map<String, bool>? connectedServices,
    DateTime? createdAt,
    DateTime? lastActive,
    String? firstName,
    String? lastName,
    String? displayName,
    int? mutualFriendsCount,
    bool? isSchoolVerified,
    String? schoolName,
    String? schoolDomain,
    String? schoolId,
    DateTime? schoolVerifiedAt,
    List<String>? topGenres,
    List<String>? topArtists,
    Map<String, List<String>>? artistGenres,
    Map<String, String>? artistImageUrls,
    Map<String, String>? artistSpotifyIds,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      profilePicUrl: profilePicUrl ?? this.profilePicUrl,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      bio: bio ?? this.bio,
      location: location ?? this.location,
      isVerified: isVerified ?? this.isVerified,
      favoriteGenres: favoriteGenres ?? this.favoriteGenres,
      connectedServices: connectedServices ?? this.connectedServices,
      createdAt: createdAt ?? this.createdAt,
      lastActive: lastActive ?? this.lastActive,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      displayName: displayName ?? this.displayName,
      mutualFriendsCount: mutualFriendsCount ?? this.mutualFriendsCount,
      isSchoolVerified: isSchoolVerified ?? this.isSchoolVerified,
      schoolName: schoolName ?? this.schoolName,
      schoolDomain: schoolDomain ?? this.schoolDomain,
      schoolId: schoolId ?? this.schoolId,
      schoolVerifiedAt: schoolVerifiedAt ?? this.schoolVerifiedAt,
      topGenres: topGenres ?? this.topGenres,
      topArtists: topArtists ?? this.topArtists,
      artistGenres: artistGenres ?? this.artistGenres,
      artistImageUrls: artistImageUrls ?? this.artistImageUrls,
      artistSpotifyIds: artistSpotifyIds ?? this.artistSpotifyIds,
    );
  }
  
  // Checks if user has connected a specific music service
  bool hasConnectedService(String service) {
    return connectedServices[service] ?? false;
  }
  
  // Returns the user's display name (username or first part of email)
  String get displayNameComputed {
    if (displayName != null && displayName!.isNotEmpty) return displayName!;
    if (firstName != null && lastName != null) return '$firstName $lastName';
    if (username.isNotEmpty) return username;
    // Extract name from email (e.g., "john" from "<EMAIL>")
    return email.split('@').first;
  }
  
  // Check if two users are the same (by ID)
  bool isSameUser(User other) {
    return id == other.id;
  }

  // Check if user has school verification
  bool get hasSchoolVerification {
    return isSchoolVerified && schoolName != null;
  }
  
  // Get school display name or fallback
  String get schoolDisplayName {
    return schoolName ?? 'Unknown School';
  }

  // Factory method for creating an anonymous user
  factory User.anonymous() {
    return User(
      id: 0,
      username: 'Anonymous',
      email: '',
      isVerified: false,
      favoriteGenres: [],
      connectedServices: {},
      createdAt: DateTime.now(),
      isSchoolVerified: false,
    );
  }
} 