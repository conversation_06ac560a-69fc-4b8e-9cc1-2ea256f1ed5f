abstract class Spacing {
  // Base spacing units
  static const double micro = 4.0;
  static const double small = 8.0;
  static const double medium = 12.0;
  static const double large = 16.0;
  static const double xlarge = 24.0;

  // Specific use-cases
  static const double cardPadding = medium;
  static const double cardMargin = small;
  static const double gridSpacing = medium;
  static const double iconSpacing = medium;
  static const double contentSpacing = small;
  static const double sectionSpacing = large;
  
  // Border radii
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 20.0;
} 