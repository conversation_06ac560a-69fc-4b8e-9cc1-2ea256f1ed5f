import '../models/api_response.dart';
import '../models/public_user_profile.dart';
import '../models/pin.dart';
import '../models/collection_model.dart';
import '../models/friendship_status.dart';
import 'api_service.dart';
import 'auth_service.dart';

/// A modern, robust, and sleek service for handling public user profiles and social actions.
class PublicProfileService {
  final ApiService _apiService;
  final AuthService _authService;

  PublicProfileService(this._apiService, this._authService);

  /// Search for users by username (minimum 2 characters).
  Future<List<PublicUserProfile>> searchUsers(String query) async {
    final trimmed = query.trim();
    if (trimmed.length < 2) return [];

    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/users/search/',
      queryParams: {'q': trimmed},
      token: token,
    );

    if (!response.success) {
      throw Exception('Could not search users: ${response.message ?? "Unknown error"}');
    }

    final results = response.data is List
        ? response.data as List
        : (response.data['results'] ?? []) as List;
    return results.map((json) => PublicUserProfile.fromJson(json)).toList();
  }

  /// Fetch a user's public profile by their user ID.
  Future<PublicUserProfile?> getPublicProfile(int userId) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/users/$userId/public_profile/',
      token: token,
    );

    if (!response.success) {
      if (response.statusCode == 404) return null;
      throw Exception('Could not load user profile: ${response.message ?? "Unknown error"}');
    }

    return PublicUserProfile.fromJson(response.data);
  }

  /// Get a user's public pins, paginated.
  Future<List<Pin>> getUserPublicPins(
    int userId, {
    int page = 1,
    int pageSize = 20,
  }) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/users/$userId/public_pins/',
      queryParams: {
        'page': page.toString(),
        'page_size': pageSize.toString(),
      },
      token: token,
    );

    if (!response.success) {
      throw Exception('Could not load user pins: ${response.message ?? "Unknown error"}');
    }

    final results = (response.data['results'] ?? []) as List;
    return results.map((json) => Pin.fromJson(json)).toList();
  }

  /// Get a user's public collections, paginated.
  Future<List<Collection>> getUserPublicCollections(
    int userId, {
    int page = 1,
    int pageSize = 20,
  }) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/users/$userId/public_collections/',
      queryParams: {
        'page': page.toString(),
        'page_size': pageSize.toString(),
      },
      token: token,
    );

    if (!response.success) {
      throw Exception('Could not load user collections: ${response.message ?? "Unknown error"}');
    }

    final results = (response.data['results'] ?? []) as List;
    return results.map((json) => Collection.fromJson(json)).toList();
  }

  /// Get the friendship status with a user and return both status and request ID if applicable.
  Future<Map<String, dynamic>> getFriendshipStatusWithRequestId(int userId) async {
    final token = await _authService.getToken();
    
    try {
      // First try to get the friendship status
      final statusResponse = await _apiService.get(
        '/friends/status/$userId/',
        token: token,
      );

      print('🤝 Friendship status response: ${statusResponse.data}');
      print('🤝 Response success: ${statusResponse.success}');
      print('🤝 Response error: ${statusResponse.error}');
      print('🤝 Response message: ${statusResponse.message}');
      print('🤝 Response status code: ${statusResponse.statusCode}');

      if (!statusResponse.success) {
        print('❌ Failed to get friendship status: ${statusResponse.message}');
        throw Exception('Failed to get friendship status: ${statusResponse.message}');
      }

      String status = statusResponse.data['status'] as String? ?? 'none';
      String? requestId = statusResponse.data['request_id'] as String?;
      
      // If we don't have request ID but status indicates pending, try to find it
      if (requestId == null && (status == 'pending_received' || status == 'pending_sent')) {
        try {
          if (status == 'pending_received') {
            // Get received requests to find the request ID
            final receivedResponse = await _apiService.get(
              '/friends/requests/received/',
              token: token,
            );
            
            if (receivedResponse.success) {
              final requests = receivedResponse.data is List 
                  ? receivedResponse.data as List
                  : (receivedResponse.data['results'] ?? []) as List;
              
              for (final request in requests) {
                if (request['requester']?['id'] == userId) {
                  requestId = request['id']?.toString();
                  break;
                }
              }
            }
          } else if (status == 'pending_sent') {
            // Get sent requests to find the request ID
            final sentResponse = await _apiService.get(
              '/friends/requests/sent/',
              token: token,
            );
            
            if (sentResponse.success) {
              final requests = sentResponse.data is List 
                  ? sentResponse.data as List
                  : (sentResponse.data['results'] ?? []) as List;
              
              for (final request in requests) {
                if (request['recipient']?['id'] == userId) {
                  requestId = request['id']?.toString();
                  break;
                }
              }
            }
          }
        } catch (e) {
          print('⚠️ Could not fetch request ID: $e');
        }
      }
      
      return {
        'status': status,
        'request_id': requestId,
      };
    } catch (e) {
      print('❌ Error getting friendship status: $e');
      throw Exception('Failed to get friendship status: $e');
    }
  }

  /// Get the friendship status with a user (legacy method for compatibility).
  Future<FriendshipStatus> getFriendshipStatus(int userId) async {
    final result = await getFriendshipStatusWithRequestId(userId);
    final status = result['status'] as String;
    
    switch (status) {
      case 'friends':
        return FriendshipStatus.friends;
      case 'pending_sent':
        return FriendshipStatus.pendingSent;
      case 'pending_received':
        return FriendshipStatus.pendingReceived;
      default:
        return FriendshipStatus.none;
    }
  }

  /// Send a friend request to a user.
  Future<bool> sendFriendRequest(int userId) async {
    final token = await _authService.getToken();
    final response = await _apiService.post(
      '/friends/requests/',
      data: {'recipient_id': userId},
      token: token,
    );
    return response.success;
  }

  /// Accept a friend request by request ID.
  Future<bool> acceptFriendRequest(String requestId) async {
    final token = await _authService.getToken();
    final response = await _apiService.post(
      '/friends/requests/$requestId/accept/',
      token: token,
    );
    return response.success;
  }

  /// Reject a friend request by request ID.
  Future<bool> rejectFriendRequest(String requestId) async {
    final token = await _authService.getToken();
    final response = await _apiService.post(
      '/friends/requests/$requestId/reject/',
      token: token,
    );
    return response.success;
  }

  /// Cancel a sent friend request by request ID.
  Future<bool> cancelFriendRequest(String requestId) async {
    final token = await _authService.getToken();
    final response = await _apiService.delete(
      '/friends/requests/$requestId/cancel/',
      token: token,
    );
    return response.success;
  }

  /// Unfriend a user by their user ID.
  Future<bool> unfriend(int userId) async {
    final token = await _authService.getToken();
    final response = await _apiService.delete(
      '/friends/unfriend/$userId/',
      token: token,
    );
    return response.success;
  }
}