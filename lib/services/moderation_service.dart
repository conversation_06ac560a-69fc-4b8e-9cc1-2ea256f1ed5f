import '../models/api_response.dart';
import 'api_service.dart';
import 'auth_service.dart';

/// Service for handling user moderation actions including blocking and reporting
class ModerationService {
  final ApiService _apiService;
  final AuthService _authService;

  ModerationService(this._apiService, this._authService);

  /// Block a user
  Future<ApiResponse> blockUser({
    required int userId,
    String? reason,
  }) async {
    final token = await _authService.getToken();
    
    return await _apiService.post(
      '/moderation/blocks/block_user/',
      data: {
        'user_id': userId,
        if (reason != null) 'reason': reason,
      },
      token: token,
    );
  }

  /// Unblock a user
  Future<ApiResponse> unblockUser({
    required int userId,
  }) async {
    final token = await _authService.getToken();
    
    return await _apiService.post(
      '/moderation/blocks/unblock_user/',
      data: {
        'user_id': userId,
      },
      token: token,
    );
  }

  /// Check if a user is blocked
  Future<bool> isUserBlocked(int userId) async {
    final token = await _authService.getToken();
    
    final response = await _apiService.get(
      '/moderation/blocks/is_blocked/',
      queryParams: {
        'user_id': userId.toString(),
      },
      token: token,
    );

    if (response.success && response.data != null) {
      return response.data['is_blocked'] == true;
    }
    
    return false;
  }

  /// Get all blocked users
  Future<List<Map<String, dynamic>>> getBlockedUsers() async {
    final token = await _authService.getToken();
    
    final response = await _apiService.get(
      '/moderation/blocks/',
      token: token,
    );

    if (response.success && response.data != null) {
      if (response.data is List) {
        return List<Map<String, dynamic>>.from(response.data);
      } else if (response.data is Map && response.data['results'] != null) {
        return List<Map<String, dynamic>>.from(response.data['results']);
      }
    }
    
    return [];
  }

  /// Get available report types
  Future<List<Map<String, dynamic>>> getReportTypes() async {
    final token = await _authService.getToken();
    
    final response = await _apiService.get(
      '/moderation/report-types/',
      token: token,
    );

    if (response.success && response.data != null) {
      if (response.data is List) {
        return List<Map<String, dynamic>>.from(response.data);
      } else if (response.data is Map && response.data['results'] != null) {
        return List<Map<String, dynamic>>.from(response.data['results']);
      }
    }
    
    return [];
  }

  /// Report a user
  Future<ApiResponse> reportUser({
    required int userId,
    required int reportTypeId,
    String? description,
  }) async {
    final token = await _authService.getToken();
    
    return await _apiService.post(
      '/moderation/reports/report_user/',
      data: {
        'user_id': userId,
        'report_type_id': reportTypeId,
        'description': description ?? 'Reported by user',
      },
      token: token,
    );
  }

  /// Get user's reports
  Future<List<Map<String, dynamic>>> getUserReports() async {
    final token = await _authService.getToken();
    
    final response = await _apiService.get(
      '/moderation/reports/',
      token: token,
    );

    if (response.success && response.data != null) {
      if (response.data is List) {
        return List<Map<String, dynamic>>.from(response.data);
      } else if (response.data is Map && response.data['results'] != null) {
        return List<Map<String, dynamic>>.from(response.data['results']);
      }
    }
    
    return [];
  }
} 