import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:background_location/background_location.dart';

import 'location_service.dart';

/// Status of the user's location
enum LocationStatus {
  /// Location services are not enabled
  disabled,
  
  /// Permission has not been requested yet
  notDetermined,
  
  /// Permission has been denied
  denied,
  
  /// Permission has been permanently denied
  permanentlyDenied,
  
  /// Location is available and being tracked
  available,
  
  /// Location is being determined
  determining,
  
  /// Error occurred while getting location
  error
}

/// Central manager for all location-related operations
class LocationManager {
  // Singleton instance
  static final LocationManager _instance = LocationManager._internal();
  
  factory LocationManager() => _instance;
  
  LocationManager._internal() {
    print("🗺️ LocationManager: Initializing singleton instance");
    // Initialize
    _checkInitialLocationStatus();
  }
  
  // Dependencies
  final LocationService _locationService = LocationService();
  
  // Location state
  Position? _currentPosition;
  Position? _lastValidPosition; // Track last position for movement validation
  LocationStatus _locationStatus = LocationStatus.notDetermined;
  String? _errorMessage;
  bool _isTracking = false;
  bool _isBackgroundTracking = false;
  
  // Movement validation settings
  static const double _minMovementDistance = 5.0; // Reduced from 15.0 to 5.0 meters for more responsive updates
  static const double _minMovementSpeed = 0.1; // Reduced from 0.5 to 0.1 m/s for slower movement detection
  static const int _backgroundUpdateInterval = 30000; // 30 seconds instead of 5
  
  // Track startup status
  bool _isInitializing = true;
  DateTime _startupTime = DateTime.now();
  int _locationRequests = 0;
  int _locationSuccesses = 0;
  int _locationFailures = 0;
  int _filteredUpdates = 0; // Track how many updates were filtered as GPS noise
  
  // Stream controllers
  final _locationStatusController = StreamController<LocationStatus>.broadcast();
  final _positionController = StreamController<Position?>.broadcast();
  
  // Stream subscriptions
  StreamSubscription<Position>? _positionSubscription;
  
  // Background location state
  bool _backgroundLocationInitialized = false;
  
  // Getters
  Position? get currentPosition => _currentPosition;
  LocationStatus get locationStatus => _locationStatus;
  String? get errorMessage => _errorMessage;
  bool get isTracking => _isTracking;
  bool get isBackgroundTracking => _isBackgroundTracking;
  
  // Streams
  Stream<LocationStatus> get locationStatusStream => _locationStatusController.stream;
  Stream<Position?> get positionStream => _positionController.stream;
  
  // Check initial location status on startup
  Future<void> _checkInitialLocationStatus() async {
    try {
      _isInitializing = true;
      print("🗺️ LocationManager: Checking initial location status");
      
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print("🗺️ LocationManager: Location services are disabled");
        _updateLocationStatus(LocationStatus.disabled);
        return;
      }
      
      final permission = await Geolocator.checkPermission();
      print("🗺️ LocationManager: Initial permission check result: $permission");
      _updateLocationStatusFromPermission(permission);
      
      // If we have permission, get current position
      if (permission == LocationPermission.always || 
          permission == LocationPermission.whileInUse) {
        print("🗺️ LocationManager: Have permission, requesting current location");
        await requestCurrentLocation();
      } else {
        print("🗺️ LocationManager: Don't have permission yet, will request later");
      }
    } catch (e) {
      _handleError('Error checking location status: $e');
    } finally {
      _isInitializing = false;
    }
  }
  
  // Initialize background location service
  Future<void> _initializeBackgroundLocation() async {
    if (_backgroundLocationInitialized) return;
    
    try {
      print("🗺️ LocationManager: Initializing background location service");
      
      // Set Android notification (required for background location)
      await BackgroundLocation.setAndroidNotification(
        title: "BOPMaps Location",
        message: "Tracking your location to discover nearby music pins",
        icon: "@mipmap/ic_launcher",
      );
      
      // Set Android configuration (update interval in milliseconds)
      await BackgroundLocation.setAndroidConfiguration(_backgroundUpdateInterval);
      
      // Listen to location updates from background service
      BackgroundLocation.getLocationUpdates((location) {
        print("🗺️ LocationManager: Background location update received: "
            "lat: ${location.latitude}, lng: ${location.longitude}, "
            "accuracy: ${location.accuracy}m, speed: ${location.speed}m/s");
        
        // Convert background location to Position object
        final position = Position(
          latitude: location.latitude!,
          longitude: location.longitude!,
          accuracy: location.accuracy!,
          altitude: location.altitude!,
          heading: location.bearing!,
          speed: location.speed!,
          speedAccuracy: 0,
          timestamp: DateTime.fromMillisecondsSinceEpoch((location.time! * 1000).toInt()),
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
        
        if (_isSignificantMovement(position)) {
          _updateCurrentPosition(position);
        } else {
          _filteredUpdates++;
          print("🗺️ LocationManager: Filtered GPS noise (total filtered: $_filteredUpdates)");
        }
      });
      
      _backgroundLocationInitialized = true;
      print("🗺️ LocationManager: Background location service initialized with ${_backgroundUpdateInterval}ms intervals");
    } catch (e) {
      print("🗺️ LocationManager: Error initializing background location: $e");
      _handleError('Error initializing background location: $e');
    }
  }
  
  // Request location permission
  Future<LocationStatus> requestLocationPermission() async {
    try {
      print("🗺️ LocationManager: Requesting location permission");
      _updateLocationStatus(LocationStatus.determining);
      
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print("🗺️ LocationManager: Location services are disabled");
        _updateLocationStatus(LocationStatus.disabled);
        return LocationStatus.disabled;
      }
      
      final permission = await _locationService.requestPermission();
      print("🗺️ LocationManager: Permission request result: $permission");
      _updateLocationStatusFromPermission(permission);
      
      return _locationStatus;
    } catch (e) {
      _handleError('Error requesting location permission: $e');
      return LocationStatus.error;
    }
  }
  
  // Convert permission to status
  void _updateLocationStatusFromPermission(LocationPermission permission) {
    switch (permission) {
      case LocationPermission.denied:
        _updateLocationStatus(LocationStatus.denied);
        break;
      case LocationPermission.deniedForever:
        _updateLocationStatus(LocationStatus.permanentlyDenied);
        break;
      case LocationPermission.whileInUse:
      case LocationPermission.always:
        _updateLocationStatus(LocationStatus.available);
        break;
      default:
        _updateLocationStatus(LocationStatus.notDetermined);
    }
  }
  
  // Request current location
  Future<Position?> requestCurrentLocation() async {
    try {
      _locationRequests++;
      print("🗺️ LocationManager: Requesting current location (request #$_locationRequests)");
      _updateLocationStatus(LocationStatus.determining);
      
      // Useful for debugging - show time since app startup
      final requestTime = DateTime.now();
      final startupDuration = requestTime.difference(_startupTime).inMilliseconds;
      print("🗺️ LocationManager: Location request started at T+${startupDuration}ms since app startup");
      
      final position = await _locationService.getCurrentPosition();
      
      // Log the acquired position details
      final duration = DateTime.now().difference(requestTime).inMilliseconds;
      print("🗺️ LocationManager: Location acquired in ${duration}ms: "
          "lat: ${position.latitude}, lng: ${position.longitude}, "
          "accuracy: ${position.accuracy}m, speed: ${position.speed}m/s, "
          "altitude: ${position.altitude}m");
      
      _updateCurrentPosition(position);
      _updateLocationStatus(LocationStatus.available);
      
      _locationSuccesses++;
      final successRate = (_locationSuccesses / _locationRequests * 100).toStringAsFixed(1);
      print("🗺️ LocationManager: Location success rate: $successRate% (${_locationSuccesses}/${_locationRequests})");
      
      return position;
    } catch (e) {
      _locationFailures++;
      _handleError('Error getting current location: $e');
      return null;
    }
  }
  
  // Start tracking location (foreground)
  Future<bool> startTracking() async {
    if (_isTracking) {
      print("🗺️ LocationManager: Already tracking location");
      return true;
    }
    
    try {
      // Ensure we have permission first
      print("🗺️ LocationManager: Starting foreground location tracking");
      
      final status = await requestLocationPermission();
      if (status != LocationStatus.available) {
        print("🗺️ LocationManager: Cannot start tracking, permission not available: $status");
        return false;
      }
      
      // Add a small delay to ensure iOS location services are fully initialized
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Start with a single location request to ensure services are working
      final initialPosition = await requestCurrentLocation();
      if (initialPosition == null) {
        print("🗺️ LocationManager: Failed to get initial position");
        return false;
      }
      
      // Start position updates with retry logic
      print("🗺️ LocationManager: Starting foreground position stream");
      _positionSubscription = _locationService.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 5, // Reduced from 15 to 5 meters for more responsive updates
        ),
      ).listen(
        (position) {
          print("🗺️ LocationManager: Received foreground position update: "
              "lat: ${position.latitude}, lng: ${position.longitude}, "
              "accuracy: ${position.accuracy}m, speed: ${position.speed}m/s");
          
          // SIMPLE ACCURACY FILTER: Skip poor accuracy GPS readings
          const maxAccuracy = 20.0; // meters - reject readings with accuracy worse than 20m
          if (position.accuracy > maxAccuracy) {
            _filteredUpdates++;
            print("🗺️ LocationManager: Rejected low accuracy GPS (${position.accuracy.toStringAsFixed(1)}m > ${maxAccuracy}m) (total filtered: $_filteredUpdates)");
            return;
          }
          
          // FIXED: Add movement validation before updating position
          if (_isSignificantMovement(position)) {
            _updateCurrentPosition(position);
          } else {
            _filteredUpdates++;
            print("🗺️ LocationManager: Filtered GPS noise (total filtered: $_filteredUpdates)");
          }
        },
        onError: (error) {
          _handleError('Location stream error: $error');
          // Try to recover from error
          _attemptErrorRecovery();
        },
        cancelOnError: false, // Don't cancel subscription on error
      );
      
      _isTracking = true;
      print("🗺️ LocationManager: Foreground location tracking started successfully");
      return true;
    } catch (e) {
      _handleError('Error starting location tracking: $e');
      return false;
    }
  }
  
  // Start background location tracking
  Future<bool> startBackgroundTracking() async {
    if (_isBackgroundTracking) {
      print("🗺️ LocationManager: Already tracking background location");
      return true;
    }
    
    try {
      print("🗺️ LocationManager: Starting background location tracking");
      
      // Ensure we have permission first
      final status = await requestLocationPermission();
      if (status != LocationStatus.available) {
        print("🗺️ LocationManager: Cannot start background tracking, permission not available: $status");
        return false;
      }
      
      // Initialize background location service
      await _initializeBackgroundLocation();
      
      // Stop any existing background location service
      await BackgroundLocation.stopLocationService();
      
      // Start background location service
      await BackgroundLocation.startLocationService();
      
      _isBackgroundTracking = true;
      print("🗺️ LocationManager: Background location tracking started successfully");
      return true;
    } catch (e) {
      _handleError('Error starting background location tracking: $e');
      return false;
    }
  }
  
  // Stop background location tracking
  Future<void> stopBackgroundTracking() async {
    if (!_isBackgroundTracking) return;
    
    try {
      print("🗺️ LocationManager: Stopping background location tracking");
      await BackgroundLocation.stopLocationService();
      _isBackgroundTracking = false;
      print("🗺️ LocationManager: Background location tracking stopped");
    } catch (e) {
      print("🗺️ LocationManager: Error stopping background location: $e");
    }
  }
  
  // Attempt to recover from location errors
  Future<void> _attemptErrorRecovery() async {
    print("🗺️ LocationManager: Attempting to recover from location error");
    
    try {
      // Wait a bit before trying to recover
      await Future.delayed(const Duration(seconds: 2));
      
      // Check if location services are still enabled
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print("🗺️ LocationManager: Location services disabled during recovery");
        _updateLocationStatus(LocationStatus.disabled);
        return;
      }
      
      // Check permission again
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied || 
          permission == LocationPermission.deniedForever) {
        print("🗺️ LocationManager: Location permission lost during recovery");
        _updateLocationStatusFromPermission(permission);
        return;
      }
      
      // Try to get a new position
      print("🗺️ LocationManager: Attempting to get new position after error");
      final position = await requestCurrentLocation();
      if (position != null) {
        print("🗺️ LocationManager: Successfully recovered location tracking");
        _updateLocationStatus(LocationStatus.available);
      } else {
        // If foreground location fails, try background location as fallback
        print("🗺️ LocationManager: Foreground recovery failed, trying background location");
        final backgroundStarted = await startBackgroundTracking();
        if (backgroundStarted) {
          print("🗺️ LocationManager: Successfully started background location as fallback");
        }
      }
    } catch (e) {
      print("🗺️ LocationManager: Error recovery failed: $e");
    }
  }
  
  // Stop tracking location
  void stopTracking() {
    _positionSubscription?.cancel();
    _positionSubscription = null;
    _isTracking = false;
    print("🗺️ LocationManager: Foreground location tracking stopped");
  }
  
  // Toggle tracking
  Future<bool> toggleTracking() async {
    if (_isTracking) {
      stopTracking();
      return false;
    } else {
      return await startTracking();
    }
  }
  
  // Toggle background tracking
  Future<bool> toggleBackgroundTracking() async {
    if (_isBackgroundTracking) {
      await stopBackgroundTracking();
      return false;
    } else {
      return await startBackgroundTracking();
    }
  }
  
  // Convert Position to LatLng
  LatLng? positionToLatLng(Position? position) {
    if (position == null) return null;
    return LatLng(position.latitude, position.longitude);
  }
  
  // Get current position as LatLng
  LatLng? getCurrentLatLng() {
    return positionToLatLng(_currentPosition);
  }
  
  // Calculate distance between two positions
  double calculateDistance(LatLng point1, LatLng point2) {
    return _locationService.calculateDistance(
      point1.latitude,
      point1.longitude,
      point2.latitude,
      point2.longitude,
    );
  }
  
  // Check if a point is within range of another point
  bool isPointInRange(LatLng userLocation, LatLng targetLocation, double rangeInMeters) {
    return _locationService.isInRange(
      userLocation.latitude,
      userLocation.longitude,
      targetLocation.latitude,
      targetLocation.longitude,
      rangeInMeters,
    );
  }
  
  // FIXED: Add movement validation to filter GPS noise
  bool _isSignificantMovement(Position newPosition) {
    // Always accept the first position
    if (_lastValidPosition == null) {
      print("🗺️ LocationManager: First position - accepting");
      return true;
    }
    
    // Calculate distance moved since last valid position
    final distance = calculateDistance(
      LatLng(_lastValidPosition!.latitude, _lastValidPosition!.longitude),
      LatLng(newPosition.latitude, newPosition.longitude),
    );
    
    // More lenient movement detection for better responsiveness
    final isSignificantDistance = distance > _minMovementDistance;
    final isSignificantSpeed = newPosition.speed > _minMovementSpeed;
    final isAccurateFix = newPosition.accuracy <= 25; // Slightly more lenient accuracy threshold
    
    // Accept position if it shows any movement OR has good accuracy
    // This makes location updates more responsive while still filtering obvious GPS noise
    final isSignificant = isSignificantDistance || 
                         isSignificantSpeed ||
                         (isAccurateFix && distance > 1.0) || // Accept accurate fixes with any movement > 1m
                         distance > (newPosition.accuracy * 0.5); // Moved more than half the GPS uncertainty
    
    if (isSignificant) {
      print("🗺️ LocationManager: Movement detected - "
            "distance: ${distance.toStringAsFixed(1)}m, "
            "speed: ${newPosition.speed.toStringAsFixed(1)}m/s, "
            "accuracy: ${newPosition.accuracy.toStringAsFixed(1)}m");
    } else {
      print("🗺️ LocationManager: GPS noise filtered - "
            "distance: ${distance.toStringAsFixed(1)}m, "
            "speed: ${newPosition.speed.toStringAsFixed(1)}m/s, "
            "accuracy: ${newPosition.accuracy.toStringAsFixed(1)}m");
    }
    
    return isSignificant;
  }
  
  // Update current position and broadcast to listeners
  void _updateCurrentPosition(Position position) {
    _currentPosition = position;
    _lastValidPosition = position; // Update last valid position for movement validation
    _positionController.add(position);
    _errorMessage = null;
    
    // Clear error status when we get a valid position
    if (_locationStatus == LocationStatus.error) {
      _updateLocationStatus(LocationStatus.available);
    }
    
    // Log position updates during initialization
    if (_isInitializing) {
      print("🗺️ LocationManager: Initial position set: "
          "lat: ${position.latitude}, lng: ${position.longitude}, "
          "accuracy: ${position.accuracy}m");
    }
  }
  
  // Update location status and broadcast to listeners
  void _updateLocationStatus(LocationStatus status) {
    final previousStatus = _locationStatus;
    _locationStatus = status;
    _locationStatusController.add(status);
    
    // Log status transitions
    if (previousStatus != status) {
      print("🗺️ LocationManager: Status changed from $previousStatus to $status");
    }
    
    if (status != LocationStatus.error && status != LocationStatus.determining) {
      _errorMessage = null;
    }
  }
  
  // Handle errors
  void _handleError(String message) {
    debugPrint("❌ LocationManager ERROR: $message");
    _errorMessage = message;
    _updateLocationStatus(LocationStatus.error);
  }
  
  // Dispose resources
  void dispose() {
    stopTracking();
    stopBackgroundTracking();
    _locationStatusController.close();
    _positionController.close();
  }
} 