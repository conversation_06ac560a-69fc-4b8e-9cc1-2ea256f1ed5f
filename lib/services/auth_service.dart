import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/api_response.dart';
import '../models/user.dart';
import 'api_service.dart';

class AuthService {
  final ApiService _apiService;
  final FlutterSecureStorage _storage;
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';

  // School verification storage keys
  static const String _schoolVerifiedKey = 'is_school_verified';
  static const String _schoolNameKey = 'school_name';
  static const String _schoolDomainKey = 'school_domain';
  static const String _schoolIdKey = 'school_id';
  static const String _schoolVerifiedAtKey = 'school_verified_at';
  static const String _userDataKey = 'user_data';

  AuthService(this._apiService) : _storage = const FlutterSecureStorage();

  Future<String?> getToken() async {
    return await _storage.read(key: _tokenKey);
  }

  Future<String?> getRefreshToken() async {
    return await _storage.read(key: _refreshTokenKey);
  }

  Future<void> saveTokens(String token, String refreshToken) async {
    await _storage.write(key: _tokenKey, value: token);
    await _storage.write(key: _refreshTokenKey, value: refreshToken);
  }

  Future<void> clearTokens() async {
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _refreshTokenKey);
  }

  Future<ApiResponse> login(String username, String password) async {
    final response = await _apiService.post(
      '/api/users/auth/token/',
      data: {
        'username': username,
        'password': password,
      },
      requiresAuth: false,
    );

    if (response.success) {
      await saveTokens(
        response.data['access'],
        response.data['refresh'],
      );
    }

    return response;
  }

  Future<ApiResponse> register({
    required String username,
    required String email,
    required String password,
    required String passwordConfirm,
    String? bio,
    String? profilePic,
  }) async {
    return await _apiService.post(
      '/api/users/auth/register/',
      data: {
        'username': username,
        'email': email,
        'password': password,
        'password_confirm': passwordConfirm,
        if (bio != null) 'bio': bio,
        if (profilePic != null) 'profile_pic': profilePic,
      },
      requiresAuth: false,
    );
  }

  Future<ApiResponse> refreshToken() async {
    final refreshToken = await getRefreshToken();
    if (refreshToken == null) {
      return ApiResponse(
        success: false,
        message: 'No refresh token found',
        error: 'no_refresh_token',
      );
    }

    final response = await _apiService.post(
      '/users/auth/token/refresh/',
      data: {
        'refresh': refreshToken,
      },
      requiresAuth: false,
    );

    if (response.success) {
      await saveTokens(
        response.data['access'],
        response.data['refresh'],
      );
    }

    return response;
  }

  Future<ApiResponse> requestPasswordReset(String email) async {
    return await _apiService.post(
      '/api/users/auth/password-reset/',
      data: {
        'email': email,
      },
      requiresAuth: false,
    );
  }

  Future<ApiResponse> confirmPasswordReset({
    required String token,
    required String newPassword,
  }) async {
    return await _apiService.post(
      '/api/users/auth/password-reset/confirm/',
      data: {
        'token': token,
        'password': newPassword,
      },
      requiresAuth: false,
    );
  }

  Future<ApiResponse> logout() async {
    final token = await getToken();
    final response = await _apiService.post(
      '/api/users/auth/logout/',
      token: token,
    );

    if (response.success) {
      await clearAllUserData();
    }

    return response;
  }

  Future<void> clearAllUserData() async {
    await clearTokens();
    await clearSchoolVerification();
    await _storage.delete(key: _userDataKey);
  }

  Future<bool> verifyToken(String token) async {
    final response = await _apiService.post(
      '/api/token/verify/',
      data: {
        'token': token,
      },
      requiresAuth: false,
    );

    return response.success;
  }

  // School verification data methods
  Future<void> saveSchoolVerification({
    required bool isVerified,
    String? schoolName,
    String? schoolDomain,
    String? schoolId,
    DateTime? verifiedAt,
  }) async {
    await _storage.write(key: _schoolVerifiedKey, value: isVerified.toString());
    if (schoolName != null) {
      await _storage.write(key: _schoolNameKey, value: schoolName);
    }
    if (schoolDomain != null) {
      await _storage.write(key: _schoolDomainKey, value: schoolDomain);
    }
    if (schoolId != null) {
      await _storage.write(key: _schoolIdKey, value: schoolId);
    }
    if (verifiedAt != null) {
      await _storage.write(key: _schoolVerifiedAtKey, value: verifiedAt.toIso8601String());
    }
  }

  Future<Map<String, dynamic>> getSchoolVerificationData() async {
    final isVerified = await _storage.read(key: _schoolVerifiedKey);
    final schoolName = await _storage.read(key: _schoolNameKey);
    final schoolDomain = await _storage.read(key: _schoolDomainKey);
    final schoolId = await _storage.read(key: _schoolIdKey);
    final verifiedAtStr = await _storage.read(key: _schoolVerifiedAtKey);
    
    return {
      'is_verified': isVerified == 'true',
      'school_name': schoolName,
      'school_domain': schoolDomain,
      'school_id': schoolId,
      'verified_at': verifiedAtStr != null ? DateTime.parse(verifiedAtStr) : null,
    };
  }

  Future<void> clearSchoolVerification() async {
    await _storage.delete(key: _schoolVerifiedKey);
    await _storage.delete(key: _schoolNameKey);
    await _storage.delete(key: _schoolDomainKey);
    await _storage.delete(key: _schoolIdKey);
    await _storage.delete(key: _schoolVerifiedAtKey);
  }

  // User data storage
  Future<void> saveUserData(User user) async {
    final userData = user.toJson();
    await _storage.write(key: _userDataKey, value: userData.toString());
    
    // Also save school verification data separately for quick access
    await saveSchoolVerification(
      isVerified: user.isSchoolVerified,
      schoolName: user.schoolName,
      schoolDomain: user.schoolDomain,
      schoolId: user.schoolId,
      verifiedAt: user.schoolVerifiedAt,
    );
  }

  Future<User?> getCachedUser() async {
    try {
      final userDataStr = await _storage.read(key: _userDataKey);
      if (userDataStr != null) {
        // Parse the stored user data (this would need proper JSON parsing)
        // For now, we'll fetch fresh user data from the API
        return await getCurrentUser();
      }
      return null;
    } catch (e) {
      print('Error getting cached user: $e');
      return null;
    }
  }

  Future<User?> getCurrentUser() async {
    try {
      final token = await getToken();
      if (token == null) return null;
      
      final response = await _apiService.get(
        '/api/users/me/',
        token: token,
      );
      
      if (response.success) {
        final user = User.fromJson(response.data);
        // Cache the user data
        await saveUserData(user);
        return user;
      }
      
      return null;
    } catch (e) {
      print('Error getting current user: $e');
      return null;
    }
  }

  // Convenience methods for school verification
  Future<bool> isSchoolVerified() async {
    final data = await getSchoolVerificationData();
    return data['is_verified'] == true;
  }

  Future<String?> getSchoolName() async {
    final data = await getSchoolVerificationData();
    return data['school_name'];
  }

  Future<Map<String, dynamic>> getQuickSchoolInfo() async {
    final data = await getSchoolVerificationData();
    return {
      'is_verified': data['is_verified'] == true,
      'school_name': data['school_name'],
      'school_domain': data['school_domain'],
    };
  }
} 