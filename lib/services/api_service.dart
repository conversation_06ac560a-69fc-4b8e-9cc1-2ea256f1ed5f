import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../config/constants.dart';
import '../models/api_response.dart';

class ApiService {
  final String baseUrl;
  final Map<String, String> defaultHeaders;
  final http.Client _client = http.Client();

  // Retry configuration
  final int maxRetries;
  final Duration initialRetryDelay;
  ApiService({
    String? baseUrl,
    this.maxRetries = 3,
    this.initialRetryDelay = const Duration(seconds: 1),
    Map<String, String>? headers,
  }) : baseUrl = baseUrl ?? AppConstants.baseApiUrl,
       defaultHeaders = headers ??
            {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            };

  // Add auth token to headers
  Map<String, String> _getHeaders(String? token) {
    final headers = Map<String, String>.from(defaultHeaders);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  // Enhanced logging for requests
  void _logRequest(String method, Uri uri, Map<String, String> headers,
      [String? body]) {
    if (kDebugMode) {
      print('');
      print('🚀 === API REQUEST ===');
      print('📤 $method ${uri.toString()}');
      print('📋 Headers: ${_sanitizeHeaders(headers)}');
      if (body != null && body.isNotEmpty) {
        print('📦 Request Body: $body');
      }
      print('⏰ Timestamp: ${DateTime.now().toIso8601String()}');
      print('');
    }
  }

  // Enhanced logging for responses
  void _logResponse(String method, Uri uri, http.Response response) {
    if (kDebugMode) {
      print('');
      print('📥 === API RESPONSE ===');
      print('🎯 $method ${uri.toString()}');
      print('📊 Status Code: ${response.statusCode}');
      print('📋 Response Headers: ${response.headers}');
      print('📦 Response Body: ${response.body}');
      print('⏰ Timestamp: ${DateTime.now().toIso8601String()}');

      // Additional status indicators
      if (response.statusCode >= 200 && response.statusCode < 300) {
        print('✅ Request successful');
      } else if (response.statusCode >= 400 && response.statusCode < 500) {
        print('⚠️ Client error - check request');
      } else if (response.statusCode >= 500) {
        print('❌ Server error');
      }
      print('========================');
      print('');
    }
  }

  // Sanitize headers for logging (hide sensitive info)
  Map<String, String> _sanitizeHeaders(Map<String, String> headers) {
    final sanitized = Map<String, String>.from(headers);
    if (sanitized.containsKey('Authorization')) {
      sanitized['Authorization'] = 'Bearer [HIDDEN]';
    }
    return sanitized;
  }

  // Generic GET request with retry logic
  Future<ApiResponse> get(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    String? token,
    bool requiresAuth = true,
  }) async {
    return _executeWithRetry(() => _performGet(endpoint,
        queryParams: queryParams, token: token, requiresAuth: requiresAuth));
  }

  // Generic POST request with retry logic
  Future<ApiResponse> post(
    String endpoint, {
    Map<String, dynamic>? data,
    String? token,
    bool requiresAuth = true,
  }) async {
    return _executeWithRetry(() => _performPost(endpoint,
        data: data, token: token, requiresAuth: requiresAuth));
  }

  // Generic PATCH request with retry logic
  Future<ApiResponse> patch(
    String endpoint, {
    Map<String, dynamic>? data,
    String? token,
    bool requiresAuth = true,
  }) async {
    return _executeWithRetry(() => _performPatch(endpoint,
        data: data, token: token, requiresAuth: requiresAuth));
  }

  // Generic DELETE request with retry logic
  Future<ApiResponse> delete(
    String endpoint, {
    String? token,
    bool requiresAuth = true,
  }) async {
    return _executeWithRetry(() =>
        _performDelete(endpoint, token: token, requiresAuth: requiresAuth));
  }

  // Actual GET implementation
  Future<ApiResponse> _performGet(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    String? token,
    bool requiresAuth = true,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint').replace(
        queryParameters: queryParams,
      );
      final headers = _getHeaders(token);

      // Log the request
      _logRequest('GET', uri, headers);

      final response = await _client.get(uri, headers: headers);

      // Log the response
      _logResponse('GET', uri, response);

      return _processResponse(response);
    } on SocketException {
      return ApiResponse(
        success: false,
        message: 'No internet connection',
        error: 'network_error',
      );
    } on TimeoutException {
      return ApiResponse(
        success: false,
        message: 'Request timed out',
        error: 'timeout',
      );
    } catch (e) {
      return ApiResponse(
        success: false,
        message: 'Unknown error occurred',
        error: e.toString(),
      );
    }
  }

  // Actual POST implementation
  Future<ApiResponse> _performPost(
    String endpoint, {
    Map<String, dynamic>? data,
    String? token,
    bool requiresAuth = true,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final encodedData = jsonEncode(data ?? {});
      final headers = _getHeaders(token);

      // Log the request
      _logRequest('POST', uri, headers, encodedData);

      final response = await _client.post(
        uri,
        headers: headers,
        body: encodedData,
      );

      // Log the response
      _logResponse('POST', uri, response);

      return _processResponse(response);
    } on SocketException {
      return ApiResponse(
        success: false,
        message: 'No internet connection',
        error: 'network_error',
      );
    } on TimeoutException {
      return ApiResponse(
        success: false,
        message: 'Request timed out',
        error: 'timeout',
      );
    } catch (e) {
      return ApiResponse(
        success: false,
        message: 'Unknown error occurred',
        error: e.toString(),
      );
    }
  }

  // Actual PATCH implementation
  Future<ApiResponse> _performPatch(
    String endpoint, {
    Map<String, dynamic>? data,
    String? token,
    bool requiresAuth = true,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final encodedData = jsonEncode(data ?? {});
      final headers = _getHeaders(token);

      // Log the request
      _logRequest('PATCH', uri, headers, encodedData);

      final response = await _client.patch(
        uri,
        headers: headers,
        body: encodedData,
      );

      // Log the response
      _logResponse('PATCH', uri, response);

      return _processResponse(response);
    } on SocketException {
      return ApiResponse(
        success: false,
        message: 'No internet connection',
        error: 'network_error',
      );
    } on TimeoutException {
      return ApiResponse(
        success: false,
        message: 'Request timed out',
        error: 'timeout',
      );
    } catch (e) {
      return ApiResponse(
        success: false,
        message: 'Unknown error occurred',
        error: e.toString(),
      );
    }
  }

  // Actual DELETE implementation
  Future<ApiResponse> _performDelete(
    String endpoint, {
    String? token,
    bool requiresAuth = true,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final headers = _getHeaders(token);

      // Log the request
      _logRequest('DELETE', uri, headers);

      final response = await _client.delete(uri, headers: headers);

      // Log the response
      _logResponse('DELETE', uri, response);

      return _processResponse(response);
    } on SocketException {
      return ApiResponse(
        success: false,
        message: 'No internet connection',
        error: 'network_error',
      );
    } on TimeoutException {
      return ApiResponse(
        success: false,
        message: 'Request timed out',
        error: 'timeout',
      );
    } catch (e) {
      return ApiResponse(
        success: false,
        message: 'Unknown error occurred',
        error: e.toString(),
      );
    }
  }

  // Process HTTP response with proper UTF-8 decoding
  ApiResponse _processResponse(http.Response response) {
    try {
      // Handle success cases
      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Handle 204 No Content (common for DELETE operations)
        if (response.statusCode == 204 || response.body.isEmpty) {
          return ApiResponse(
            success: true,
            data: null,
            statusCode: response.statusCode,
            message: 'Operation completed successfully',
          );
        }

        // Properly decode UTF-8 to avoid character encoding issues
        String responseBody;
        try {
          responseBody = utf8.decode(response.bodyBytes);
        } catch (e) {
          if (kDebugMode) {
            print(
                '⚠️ [ApiService] UTF-8 decode failed, falling back to response.body: $e');
          }
          responseBody = response.body;
        }

        // Handle responses with JSON body
        final dynamic decodedBody = jsonDecode(responseBody);
        return ApiResponse(
          success: true,
          data: decodedBody,
          statusCode: response.statusCode,
        );
      } else {
        // Handle error responses with proper UTF-8 decoding
        String responseBody;
        try {
          responseBody = utf8.decode(response.bodyBytes);
        } catch (e) {
          if (kDebugMode) {
            print(
                '⚠️ [ApiService] UTF-8 decode failed for error response, falling back to response.body: $e');
          }
          responseBody = response.body;
        }

        final dynamic decodedBody = responseBody.isNotEmpty
            ? jsonDecode(responseBody)
            : <String, dynamic>{};

        return ApiResponse(
          success: false,
          data: decodedBody,
          message: decodedBody['message'] ?? 'Request failed',
          error: decodedBody['error'] ?? 'api_error',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      // If we can't parse the response but it's a success status code,
      // still return success (useful for 204 No Content responses)
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse(
          success: true,
          data: null,
          statusCode: response.statusCode,
          message: 'Operation completed successfully',
        );
      }

      return ApiResponse(
        success: false,
        message: 'Failed to process response',
        error: 'parse_error',
        statusCode: response.statusCode,
      );
    }
  }

  // Retry mechanism
  Future<ApiResponse> _executeWithRetry(
      Future<ApiResponse> Function() operation) async {
    ApiResponse response;
    int attempts = 0;
    Duration delay = initialRetryDelay;

    while (true) {
      response = await operation();
      attempts++;

      // No need to retry on success or non-network errors
      if (response.success ||
          (response.error != 'network_error' && response.error != 'timeout') ||
          attempts >= maxRetries) {
        break;
      }

      // Wait before retry with exponential backoff
      await Future.delayed(delay);
      delay *= 2; // Exponential backoff
    }

    return response;
  }

  // Close the client when done
  void dispose() {
    _client.close();
  }
}
