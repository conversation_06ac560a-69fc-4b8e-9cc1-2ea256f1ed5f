import '../models/api_response.dart';
import '../models/friend.dart';
import '../models/user.dart';
import 'api_service.dart';
import 'auth_service.dart';
import 'friends_cache_service.dart';
import '../config/constants.dart';

class FriendsService {
  final ApiService _apiService;
  final AuthService _authService;
  final FriendsCacheService _cacheService;

  FriendsService(this._apiService, this._authService, this._cacheService);

  Future<List<Friend>> getFriends({int page = 1}) async {
    print('🔥 FriendsService: Getting friends page $page');
    // Try cache first
    final cachedFriends = await _cacheService.getCachedFriends();
    if (cachedFriends != null) {
      print('🔥 FriendsService: Using cached friends data');
      return cachedFriends.map((json) => Friend.fromJson(json)).toList();
    }

    print('🔥 FriendsService: Cache miss, fetching from API');
    // Fetch from API if cache miss
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/friends/',  // Updated endpoint
      queryParams: {
        'page': page.toString(),
      },
      token: token,
    );

    if (!response.success) {
      print('🔥 FriendsService: API request failed: ${response.error}');
      return [];
    }

    print('🔥 FriendsService: API request successful');
    final List<dynamic> friends = response.data['results'] ?? [];
    // Cache the results
    await _cacheService.cacheFriends(
      friends.map((f) => f as Map<String, dynamic>).toList(),
    );

    return friends.map((json) => Friend.fromJson(json)).toList();
  }

  Future<List<FriendshipData>> getAllFriends() async {
    print('🔥 FriendsService: Getting all friends');
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/friends/',  // Base endpoint
        token: token,
      );

      print('🔥 FriendsService: Raw API response data: ${response.data}');
      print('🔥 FriendsService: Response success: ${response.success}');
      print('🔥 FriendsService: Response error: ${response.error}');
      print('🔥 FriendsService: Response message: ${response.message}');
      print('🔥 FriendsService: Response status code: ${response.statusCode}');

      if (!response.success) {
        print('🔥 FriendsService: Failed to get all friends: ${response.error}');
        return [];
      }

      print('🔥 FriendsService: Successfully got all friends');
      
      // Handle both array and object with results field
      List<dynamic> results;
      if (response.data is List) {
        results = response.data;
      } else if (response.data is Map<String, dynamic>) {
        results = response.data['results'] as List<dynamic>? ?? [];
      } else {
        print('🔥 FriendsService: Invalid response format: ${response.data.runtimeType}');
        return [];
      }

      return results.map((json) => FriendshipData.fromJson(json)).toList();
    } catch (e, stackTrace) {
      print('🔥 FriendsService: Error getting all friends: $e');
      print('🔥 Stack trace: $stackTrace');
      return [];
    }
  }

  Future<List<FriendshipData>> searchFriends(String query) async {
    print('🔥 FriendsService: Searching friends with query: $query');
    try {
      if (query.trim().isEmpty) {
        return getAllFriends();
      }

      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/friends/search/',
        queryParams: {
          'q': query.trim(),
        },
        token: token,
      );

      if (!response.success) {
        print('🔥 FriendsService: Friend search failed: ${response.error}');
        return [];
      }

      print('🔥 FriendsService: Friend search successful');
      if (response.data is! Map<String, dynamic>) {
        print('🔥 FriendsService: Invalid response format: ${response.data}');
        return [];
      }

      final results = response.data['results'] as List<dynamic>? ?? [];
      return results.map((json) => FriendshipData.fromJson(json)).toList();
    } catch (e) {
      print('🔥 FriendsService: Error searching friends: $e');
      return [];
    }
  }

  Future<List<FriendRequest>> getReceivedRequests() async {
    print('🔥 FriendsService: Getting received friend requests');
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/friends/requests/received/',
        token: token,
      );

      print('🔥 FriendsService: Raw API response data: ${response.data}');
      print('🔥 FriendsService: Response success: ${response.success}');
      print('🔥 FriendsService: Response error: ${response.error}');
      print('🔥 FriendsService: Response message: ${response.message}');
      print('🔥 FriendsService: Response status code: ${response.statusCode}');

      if (!response.success) {
        print('🔥 FriendsService: Failed to get received requests: ${response.error}');
        return [];
      }

      print('🔥 FriendsService: Successfully got received requests');
      
      // Handle both array and object with results field
      List<dynamic> results;
      if (response.data is List) {
        results = response.data;
      } else if (response.data is Map<String, dynamic>) {
        results = response.data['results'] as List<dynamic>? ?? [];
      } else {
        print('🔥 FriendsService: Invalid response format: ${response.data.runtimeType}');
        return [];
      }

      return results.map((json) => FriendRequest.fromJson(json)).toList();
    } catch (e, stackTrace) {
      print('🔥 FriendsService: Error getting received requests: $e');
      print('🔥 Stack trace: $stackTrace');
      return [];
    }
  }

  Future<List<FriendRequest>> getSentRequests() async {
    print('🔥 FriendsService: Getting sent friend requests');
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/friends/requests/sent/',
        token: token,
      );

      print('🔥 FriendsService: Raw API response data: ${response.data}');
      print('🔥 FriendsService: Response success: ${response.success}');
      print('🔥 FriendsService: Response error: ${response.error}');
      print('🔥 FriendsService: Response message: ${response.message}');
      print('🔥 FriendsService: Response status code: ${response.statusCode}');

      if (!response.success) {
        print('🔥 FriendsService: Failed to get sent requests: ${response.error}');
        return [];
      }

      print('🔥 FriendsService: Successfully got sent requests');
      
      // Handle both array and object with results field
      List<dynamic> results;
      if (response.data is List) {
        results = response.data;
      } else if (response.data is Map<String, dynamic>) {
        results = response.data['results'] as List<dynamic>? ?? [];
      } else {
        print('🔥 FriendsService: Invalid response format: ${response.data.runtimeType}');
        return [];
      }

      return results.map((json) => FriendRequest.fromJson(json)).toList();
    } catch (e, stackTrace) {
      print('🔥 FriendsService: Error getting sent requests: $e');
      print('🔥 Stack trace: $stackTrace');
      return [];
    }
  }

  Future<FriendRequest?> sendFriendRequest(int recipientId) async {
    print('🔥 FriendsService: Sending friend request to user $recipientId');
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/friends/requests/',
        data: {
          'recipient_id': recipientId,
        },
        token: token,
      );

      if (!response.success) {
        print('🔥 FriendsService: Failed to send friend request: ${response.error}');
        return null;
      }

      print('🔥 FriendsService: Successfully sent friend request');
      return FriendRequest.fromJson(response.data);
    } catch (e) {
      print('🔥 FriendsService: Error sending friend request: $e');
      return null;
    }
  }

  Future<FriendRequest?> acceptFriendRequest(String requestId) async {
    print('🔥 FriendsService: Accepting friend request $requestId');
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/friends/requests/$requestId/accept/',
        token: token,
      );

      if (!response.success) {
        print('🔥 FriendsService: Failed to accept friend request: ${response.error}');
        return null;
      }

      print('🔥 FriendsService: Successfully accepted friend request');
      await _cacheService.clearCache(); // Clear cache as friend list has changed
      return FriendRequest.fromJson(response.data);
    } catch (e) {
      print('🔥 FriendsService: Error accepting friend request: $e');
      return null;
    }
  }

  Future<FriendRequest?> rejectFriendRequest(String requestId) async {
    print('🔥 FriendsService: Rejecting friend request $requestId');
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/friends/requests/$requestId/reject/',
        token: token,
      );

      if (!response.success) {
        print('🔥 FriendsService: Failed to reject friend request: ${response.error}');
        return null;
      }

      print('🔥 FriendsService: Successfully rejected friend request');
      return FriendRequest.fromJson(response.data);
    } catch (e) {
      print('🔥 FriendsService: Error rejecting friend request: $e');
      return null;
    }
  }

  Future<FriendRequest?> cancelFriendRequest(String requestId) async {
    print('🔥 FriendsService: Canceling friend request $requestId');
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/friends/requests/$requestId/cancel/',
        token: token,
      );

      if (!response.success) {
        print('🔥 FriendsService: Failed to cancel friend request: ${response.error}');
        return null;
      }

      print('🔥 FriendsService: Successfully canceled friend request');
      return FriendRequest.fromJson(response.data);
    } catch (e) {
      print('🔥 FriendsService: Error canceling friend request: $e');
      return null;
    }
  }

  Future<bool> unfriend(String friendId) async {
    print('🔥 FriendsService: Unfriending user $friendId');
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/friends/$friendId/unfriend/',
        token: token,
      );

      if (response.success) {
        print('🔥 FriendsService: Successfully unfriended user');
        await _cacheService.clearCache(); // Clear cache as friend list has changed
      } else {
        print('🔥 FriendsService: Failed to unfriend user: ${response.error}');
      }

      return response.success;
    } catch (e) {
      print('🔥 FriendsService: Error unfriending user: $e');
      return false;
    }
  }

  Future<List<User>> searchAllUsers(String query) async {
    print('🔍 FriendsService: Searching all users with query: $query');
    try {
      if (query.trim().isEmpty || query.trim().length < 2) {
        print('🔍 FriendsService: Query too short, returning empty results');
        return [];
      }

      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/users/search/',
        queryParams: {
          'q': query.trim(),
        },
        token: token,
      );

      print('🔍 FriendsService: User search response success: ${response.success}');
      print('🔍 FriendsService: User search response data: ${response.data}');

      if (!response.success) {
        print('🔍 FriendsService: User search failed: ${response.error}');
        return [];
      }

      // Handle response data
      List<dynamic> results;
      if (response.data is List) {
        results = response.data;
      } else if (response.data is Map<String, dynamic>) {
        results = response.data['results'] as List<dynamic>? ?? [];
      } else {
        print('🔍 FriendsService: Invalid response format for user search');
        return [];
      }

      print('🔍 FriendsService: Found ${results.length} users');
      return results.map((json) => User.fromJson(json)).toList();
    } catch (e, stackTrace) {
      print('🔍 FriendsService: Error searching users: $e');
      print('🔍 Stack trace: $stackTrace');
      return [];
    }
  }

  Future<List<User>> getSuggestedFriends() async {
    print('🤝 FriendsService: Getting suggested friends');
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/friends/suggested/',
        token: token,
      );

      print('🤝 FriendsService: Suggested friends response success: ${response.success}');
      print('🤝 FriendsService: Suggested friends response data: ${response.data}');

      if (!response.success) {
        print('🤝 FriendsService: Failed to get suggested friends: ${response.error}');
        return [];
      }

      // Handle response data
      List<dynamic> results;
      if (response.data is List) {
        results = response.data;
      } else if (response.data is Map<String, dynamic>) {
        results = response.data['results'] as List<dynamic>? ?? [];
      } else {
        print('🤝 FriendsService: Invalid response format for suggested friends');
        return [];
      }

      print('🤝 FriendsService: Found ${results.length} suggested friends');
      
      // Parse the nested structure where user data is under "user" key
      return results.map((item) {
        if (item is Map<String, dynamic> && item.containsKey('user')) {
          final userData = item['user'] as Map<String, dynamic>;
          final mutualFriendsCount = item['mutual_friends_count'] as int?;
          
          // Add mutual friends count to user data
          userData['mutual_friends_count'] = mutualFriendsCount;
          
          return User.fromJson(userData);
        } else {
          // Fallback for direct user data (shouldn't happen with current API)
          return User.fromJson(item);
        }
      }).toList();
    } catch (e, stackTrace) {
      print('🤝 FriendsService: Error getting suggested friends: $e');
      print('🤝 Stack trace: $stackTrace');
      return [];
    }
  }
} 