import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../config/constants.dart';
import '../../models/map/tile.dart';

/// Service for fetching and caching map tiles from the backend API
class TileCacheService {
  final http.Client _client;
  final String _token;
  final Map<String, String> _etagCache = {};
  
  TileCacheService(this._client, this._token);

  /// Builds the URL for a specific tile
  String _buildTileUrl(MapTile tile) {
    return '${AppConstants.baseApiUrl}${AppConstants.osmTilesEndpoint}/${tile.z}/${tile.x}/${tile.y}.png';
  }

  /// Gets the headers for API requests
  Map<String, String> get _headers => {
    'Authorization': 'Bearer $_token',
    'Accept': 'image/png',
    'X-Client-Version': AppConstants.cacheVersion,
  };

  /// Check if a tile should be cached based on zoom level
  bool shouldCacheTile(int zoomLevel) {
    return zoomLevel >= AppConstants.minCachedZoomLevel && 
           zoomLevel <= AppConstants.maxCachedZoomLevel;
  }

  /// Get a cached tile, fetching from server if not in cache
  /// 
  /// Returns the tile data as a List<int> if available, or null if not found
  /// or if the tile is already cached client-side (304 Not Modified)
  Future<List<int>?> getCachedTile(MapTile tile) async {
    if (!shouldCacheTile(tile.z)) {
      if (kDebugMode) {
        print('🗺️ [TileCacheService] Zoom level ${tile.z} outside caching range');
      }
      return null;
    }

    final url = _buildTileUrl(tile);
    if (kDebugMode) {
      print('🗺️ [TileCacheService] Requesting cached tile: $url');
    }

    try {
      // Add ETag if we have it cached
      final headers = Map<String, String>.from(_headers);
      if (_etagCache.containsKey(url)) {
        headers['If-None-Match'] = _etagCache[url]!;
      }

      final response = await _client.get(Uri.parse(url), headers: headers);

      switch (response.statusCode) {
        case 200:
          // Cache the new ETag
          final etag = response.headers['etag'];
          if (etag != null) {
            _etagCache[url] = etag;
          }
          if (kDebugMode) {
            print('🗺️ [TileCacheService] Cache MISS for tile $tile');
          }
          return response.bodyBytes;

        case 304:
          if (kDebugMode) {
            print('🗺️ [TileCacheService] Cache HIT for tile $tile');
          }
          return null; // Client should use cached version

        case 404:
          if (kDebugMode) {
            print('⚠️ [TileCacheService] Failed to get tile: 404');
          }
          return null;

        case 429:
          if (kDebugMode) {
            print('⚠️ [TileCacheService] Rate limit exceeded');
          }
          throw Exception(AppConstants.errorRateLimitExceeded);

        default:
          if (kDebugMode) {
            print('❌ [TileCacheService] Error fetching cached tile: HTTP ${response.statusCode}');
          }
          throw Exception(AppConstants.errorNetworkFailure);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [TileCacheService] Error fetching cached tile: $e');
      }
      if (e.toString().contains(AppConstants.errorRateLimitExceeded)) {
        rethrow;
      }
      throw Exception(AppConstants.errorNetworkFailure);
    }
  }

  /// Store a tile in the cache
  ///
  /// This method stores a tile in the backend cache. It returns true if the
  /// tile was successfully stored, false otherwise.
  Future<bool> storeTile(MapTile tile, List<int> tileData) async {
    if (!shouldCacheTile(tile.z)) {
      if (kDebugMode) {
        print('🗺️ [TileCacheService] Not storing tile: zoom level ${tile.z} outside caching range');
      }
      return false;
    }

    final url = _buildTileUrl(tile);
    if (kDebugMode) {
      print('🗺️ [TileCacheService] Storing tile to cache: $url');
    }

    try {
      final headers = Map<String, String>.from(_headers);
      headers['Content-Type'] = 'image/png';

      final response = await _client.put(
        Uri.parse(url),
        headers: headers,
        body: tileData,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Cache the new ETag if available
        final etag = response.headers['etag'];
        if (etag != null) {
          _etagCache[url] = etag;
        }
        if (kDebugMode) {
          print('✅ [TileCacheService] Successfully stored tile $tile');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ [TileCacheService] Failed to store tile: HTTP ${response.statusCode}');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [TileCacheService] Error storing tile: $e');
      }
      return false;
    }
  }

  /// Prefetch tiles for a geographic region
  /// 
  /// This method will prefetch tiles for the given region at the specified zoom levels.
  /// It respects rate limits and error handling.
  Future<void> prefetchTiles(
    double north,
    double south,
    double east,
    double west,
    List<int> zoomLevels,
  ) async {
    if (kDebugMode) {
      print('🗺️ [TileCacheService] Prefetching tiles for region: '
            'N=$north, S=$south, E=$east, W=$west, Z=${zoomLevels.join("-")}');
    }

    for (final zoom in zoomLevels) {
      if (!shouldCacheTile(zoom)) {
        if (kDebugMode) {
          print('🗺️ [TileCacheService] Skipping zoom level $zoom (outside cache range)');
        }
        continue;
      }

      final tiles = MapTile.tilesInBounds(north, south, east, west, zoom);
      if (kDebugMode) {
        print('🗺️ [TileCacheService] Prefetching ${tiles.length} tiles at zoom level $zoom');
      }

      // Process tiles in batches to respect rate limits
      for (var i = 0; i < tiles.length; i += AppConstants.maxBatchSize) {
        final batch = tiles.skip(i).take(AppConstants.maxBatchSize);
        try {
          await Future.wait(
            batch.map((tile) => getCachedTile(tile)),
            eagerError: false,
          );
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [TileCacheService] Error in batch: $e');
          }
          // Continue with next batch even if this one failed
          continue;
        }
      }
    }

    if (kDebugMode) {
      print('✅ [TileCacheService] Prefetching completed');
    }
  }

  /// Clear the ETag cache to force re-fetching tiles
  void clearETagCache() {
    _etagCache.clear();
  }

  /// Get cache statistics from the backend
  Future<Map<String, dynamic>> getCacheStats() async {
    final response = await _client.get(
      Uri.parse('${AppConstants.baseApiUrl}${AppConstants.cacheStatsEndpoint}'),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to fetch cache stats: ${response.statusCode}');
    }
  }

  /// Prefetch tiles for specific routes or points of interest
  Future<void> prefetchPOIs(List<Map<String, dynamic>> pois, List<int> zoomLevels) async {
    if (kDebugMode) {
      print('🗺️ [TileCacheService] Prefetching ${pois.length} POIs at zoom levels ${zoomLevels.join(', ')}');
    }

    for (final poi in pois) {
      final lat = poi['latitude'] as double;
      final lng = poi['longitude'] as double;
      
      // Create a small bounding box around each POI
      final delta = 0.002; // Roughly 200m at equator
      await prefetchTiles(
        lat + delta,  // north
        lat - delta,  // south
        lng + delta,  // east
        lng - delta,  // west
        zoomLevels,
      );
    }
  }
} 