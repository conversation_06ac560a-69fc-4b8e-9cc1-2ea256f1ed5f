import '../../models/gamification/challenge.dart';
import '../api_service.dart';
import '../auth_service.dart';

class ArtistChallengeService {
  final ApiService _apiService;
  final AuthService _authService;

  ArtistChallengeService(this._apiService, this._authService);

  // Get all artist challenges
  Future<List<Challenge>> getArtistChallenges() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/artist/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> challenges = response.data as List<dynamic>;
    return challenges.map((json) => Challenge.fromJson(json)).toList();
  }

  // Get completed artist challenges
  Future<List<Challenge>> getCompletedChallenges() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/artist/completed/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> challenges = response.data as List<dynamic>;
    return challenges.map((json) => Challenge.fromJson(json)).toList();
  }

  // Get in-progress artist challenges
  Future<List<Challenge>> getInProgressChallenges() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/artist/in_progress/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> challenges = response.data as List<dynamic>;
    return challenges.map((json) => Challenge.fromJson(json)).toList();
  }

  // Update challenge progress
  Future<bool> updateChallengeProgress(
    String challengeId,
    Map<String, dynamic> progress,
  ) async {
    final token = await _authService.getToken();
    final response = await _apiService.post(
      '/api/gamification/challenges/artist/$challengeId/progress/',
      data: {'progress': progress},
      token: token,
    );

    return response.success;
  }

  // Get challenge by ID
  Future<Challenge?> getChallengeById(int challengeId) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/artist/$challengeId/',
      token: token,
    );

    if (!response.success) return null;
    return Challenge.fromJson(response.data);
  }
} 