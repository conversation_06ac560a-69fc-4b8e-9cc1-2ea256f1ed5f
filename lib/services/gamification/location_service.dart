import 'package:flutter/material.dart';
import '../../models/achievement.dart';
import 'category_service.dart';
import '../../models/gamification/challenge.dart';
import '../api_service.dart';
import '../auth_service.dart';

class LocationService extends CategoryService {
  final ApiService _apiService;
  final AuthService _authService;

  LocationService(this._apiService, this._authService)
      : super(
          categoryId: 'location',
          categoryName: 'Location Challenges',
          categoryIcon: Icons.location_on,
          categoryColor: Colors.green,
        );

  @override
  Future<List<Achievement>> getAchievements() async {
    // TODO: Implement actual API call to fetch location achievements
    return [
      Achievement.create(
        id: 1,
        name: 'Local Explorer',
        description: 'Visit 5 different locations in your city',
        icon: 'location_on',
        criteria: {'locations_visited': 5},
        category: 'location',
      ),
      Achievement.create(
        id: 2,
        name: 'City Wanderer',
        description: 'Create pins in 10 different neighborhoods',
        icon: 'location_on',
        criteria: {'neighborhoods_visited': 10},
        category: 'location',
      ),
      Achievement.create(
        id: 3,
        name: 'Global Traveler',
        description: 'Create pins in 3 different countries',
        icon: 'location_on',
        criteria: {'countries_visited': 3},
        category: 'location',
      ),
    ];
  }

  @override
  Future<void> trackProgress(String achievementId, Map<String, dynamic> progress) async {
    if (!validateProgressData(progress)) {
      throw ArgumentError('Invalid progress data format');
    }
    
    // TODO: Implement actual API call to update progress
    print('Tracking progress for location achievement: $achievementId');
    print('Progress data: $progress');
  }

  @override
  Future<void> completeAchievement(String achievementId) async {
    // TODO: Implement actual API call to mark achievement as completed
    print('Marking location achievement as completed: $achievementId');
  }

  @override
  Future<double> calculateCategoryProgress() async {
    final achievements = await getAchievements();
    if (achievements.isEmpty) return 0.0;

    double totalProgress = 0.0;
    for (var achievement in achievements) {
      totalProgress += achievement.progressPercentage;
    }

    return totalProgress / achievements.length;
  }

  // Location-specific methods
  Future<void> trackLocationVisit(String locationId) async {
    // TODO: Implement location visit tracking
    print('Tracking visit to location: $locationId');
  }

  Future<void> validateLocation(String locationId) async {
    // TODO: Implement location validation
    print('Validating location: $locationId');
  }

  Future<Map<String, int>> getVisitedLocationsCount() async {
    // TODO: Implement actual API call to get visited locations count
    return {
      'locations_visited': 0,
      'neighborhoods_visited': 0,
      'countries_visited': 0,
    };
  }

  // Get all location challenges
  Future<List<Challenge>> getLocationChallenges() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/location/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> challenges = response.data as List<dynamic>;
    return challenges.map((json) => Challenge.fromJson(json)).toList();
  }

  // Get completed location challenges
  Future<List<Challenge>> getCompletedChallenges() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/location/completed/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> challenges = response.data as List<dynamic>;
    return challenges.map((json) => Challenge.fromJson(json)).toList();
  }

  // Get in-progress location challenges
  Future<List<Challenge>> getInProgressChallenges() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/location/in_progress/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> challenges = response.data as List<dynamic>;
    return challenges.map((json) => Challenge.fromJson(json)).toList();
  }

  // Update challenge progress
  Future<bool> updateChallengeProgress(
    String challengeId,
    Map<String, dynamic> progress,
  ) async {
    final token = await _authService.getToken();
    final response = await _apiService.post(
      '/api/gamification/challenges/location/$challengeId/progress/',
      data: {'progress': progress},
      token: token,
    );

    return response.success;
  }

  // Get challenge by ID
  Future<Challenge?> getChallengeById(int challengeId) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/location/$challengeId/',
      token: token,
    );

    if (!response.success) return null;
    return Challenge.fromJson(response.data);
  }
} 