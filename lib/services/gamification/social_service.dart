import 'package:flutter/material.dart';
import '../../models/achievement.dart';
import 'category_service.dart';
import '../../models/gamification/challenge.dart';
import '../api_service.dart';
import '../auth_service.dart';

class SocialService extends CategoryService {
  final ApiService _apiService;
  final AuthService _authService;

  SocialService(this._apiService, this._authService)
      : super(
          categoryId: 'social',
          categoryName: 'Social Challenges',
          categoryIcon: Icons.favorite,
          categoryColor: Colors.pink,
        );

  @override
  Future<List<Achievement>> getAchievements() async {
    // TODO: Implement actual API call to fetch social achievements
    return [
      Achievement.create(
        id: 1,
        name: 'Community Contributor',
        description: 'Get 10 upvotes on your pins',
        icon: 'favorite',
        criteria: {'upvotes_received': 10},
        category: 'social',
      ),
      Achievement.create(
        id: 2,
        name: 'Social Butterfly',
        description: 'Connect with 5 other music enthusiasts',
        icon: 'people',
        criteria: {'connections_made': 5},
        category: 'social',
      ),
      Achievement.create(
        id: 3,
        name: '<PERSON><PERSON>d Setter',
        description: 'Have 3 of your pins featured in trending',
        icon: 'local_activity',
        criteria: {'trending_pins': 3},
        category: 'social',
      ),
    ];
  }

  @override
  Future<void> trackProgress(String achievementId, Map<String, dynamic> progress) async {
    if (!validateProgressData(progress)) {
      throw ArgumentError('Invalid progress data format');
    }
    
    // TODO: Implement actual API call to update progress
    print('Tracking progress for social achievement: $achievementId');
    print('Progress data: $progress');
  }

  @override
  Future<void> completeAchievement(String achievementId) async {
    // TODO: Implement actual API call to mark achievement as completed
    print('Marking social achievement as completed: $achievementId');
  }

  @override
  Future<double> calculateCategoryProgress() async {
    final achievements = await getAchievements();
    if (achievements.isEmpty) return 0.0;

    double totalProgress = 0.0;
    for (var achievement in achievements) {
      totalProgress += achievement.progressPercentage;
    }

    return totalProgress / achievements.length;
  }

  // Social-specific methods
  Future<void> trackUpvote(String pinId) async {
    // TODO: Implement upvote tracking
    print('Tracking upvote for pin: $pinId');
  }

  Future<void> trackConnection(String userId) async {
    // TODO: Implement connection tracking
    print('Tracking connection with user: $userId');
  }

  Future<void> trackTrendingPin(String pinId) async {
    // TODO: Implement trending pin tracking
    print('Tracking trending status for pin: $pinId');
  }

  Future<Map<String, int>> getSocialStats() async {
    // TODO: Implement actual API call to get social statistics
    return {
      'upvotes_received': 0,
      'connections_made': 0,
      'trending_pins': 0,
    };
  }

  // Get all social challenges
  Future<List<Challenge>> getSocialChallenges() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/social/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> challenges = response.data as List<dynamic>;
    return challenges.map((json) => Challenge.fromJson(json)).toList();
  }

  // Get completed social challenges
  Future<List<Challenge>> getCompletedChallenges() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/social/completed/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> challenges = response.data as List<dynamic>;
    return challenges.map((json) => Challenge.fromJson(json)).toList();
  }

  // Get in-progress social challenges
  Future<List<Challenge>> getInProgressChallenges() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/social/in_progress/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> challenges = response.data as List<dynamic>;
    return challenges.map((json) => Challenge.fromJson(json)).toList();
  }

  // Update challenge progress
  Future<bool> updateChallengeProgress(
    String challengeId,
    Map<String, dynamic> progress,
  ) async {
    final token = await _authService.getToken();
    final response = await _apiService.post(
      '/api/gamification/challenges/social/$challengeId/progress/',
      data: {'progress': progress},
      token: token,
    );

    return response.success;
  }

  // Get challenge by ID
  Future<Challenge?> getChallengeById(int challengeId) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/gamification/challenges/social/$challengeId/',
      token: token,
    );

    if (!response.success) return null;
    return Challenge.fromJson(response.data);
  }
} 