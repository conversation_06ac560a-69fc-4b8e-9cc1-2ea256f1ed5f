import 'package:flutter/material.dart';
import '../../models/gamification/category.dart';
import '../../models/achievement.dart';

abstract class CategoryService {
  // Core properties that all category services should have
  final String categoryId;
  final String categoryName;
  final IconData categoryIcon;
  final Color categoryColor;

  CategoryService({
    required this.categoryId,
    required this.categoryName,
    required this.categoryIcon,
    required this.categoryColor,
  });

  // Abstract methods that must be implemented by specific category services
  Future<List<Achievement>> getAchievements();
  Future<void> trackProgress(String achievementId, Map<String, dynamic> progress);
  Future<void> completeAchievement(String achievementId);
  Future<double> calculateCategoryProgress();
  
  // Shared methods that can be used by all category services
  GamificationCategory getCategoryDetails() {
    return GamificationCategory(
      id: categoryId,
      title: categoryName,
      icon: categoryIcon,
      color: categoryColor,
      criteriaKey: '${categoryId}_criteria',
    );
  }

  // Helper method to check if an achievement is completed
  bool isAchievementCompleted(Achievement achievement) {
    return achievement.isCompleted || achievement.progressPercentage >= 100;
  }

  // Helper method to validate progress data
  bool validateProgressData(Map<String, dynamic> progress) {
    if (progress.isEmpty) return false;
    
    // Check if all values are numeric
    return progress.values.every((value) => value is num);
  }

  // Helper method to format progress percentage
  String getProgressText(double percentage) {
    if (percentage >= 100) return 'Completed';
    if (percentage <= 0) return 'Not Started';
    return '${percentage.toStringAsFixed(0)}% Complete';
  }

  // Helper method to get category-specific criteria key
  String getCriteriaKey(String achievementType) {
    return '${categoryId}_${achievementType}_criteria';
  }
} 