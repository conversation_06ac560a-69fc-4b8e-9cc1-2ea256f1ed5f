import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:latlong2/latlong.dart';

import '../../models/music_track.dart';
import '../../config/constants.dart';
import '../music/spotify_service.dart';
import 'recommendation_engine.dart';
import '../location/location_manager.dart';

/// An advanced service that provides AI-powered music recommendations
/// based on location, time, user preferences, and environmental context
class MusicRecommendationService {
  // Singleton instance
  static final MusicRecommendationService _instance = MusicRecommendationService._internal();
  factory MusicRecommendationService() => _instance;
  
  // Dependencies
  final SpotifyService _spotifyService = SpotifyService();
  final LocationManager _locationManager = LocationManager();
  final RecommendationEngine _recommendationEngine = RecommendationEngine();
  
  // Cache for recommendations
  final Map<String, CachedRecommendation> _recommendationCache = {};
  
  // Status tracking
  bool _isInitialized = false;
  bool _isProcessing = false;
  String? _lastError;
  DateTime? _lastUpdateTime;
  
  // Stream controller for recommendation updates
  final _recommendationsController = StreamController<List<MusicRecommendation>>.broadcast();
  
  // Stream for recommendation updates
  Stream<List<MusicRecommendation>> get recommendationsStream => _recommendationsController.stream;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isProcessing => _isProcessing;
  String? get lastError => _lastError;
  DateTime? get lastUpdateTime => _lastUpdateTime;
  
  // Private constructor
  MusicRecommendationService._internal();
  
  /// Initialize the recommendation service
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      debugPrint('📝 Initializing Music Recommendation Service...');
      
      // Load cached recommendations from shared preferences
      await _loadCachedRecommendations();
      
      // Initialize the recommendation engine
      await _recommendationEngine.initialize();
      
      _isInitialized = true;
      return true;
    } catch (e) {
      _lastError = 'Failed to initialize recommendation service: $e';
      debugPrint('❌ $_lastError');
      return false;
    }
  }
  
  /// Get music recommendations based on current context
  Future<List<MusicRecommendation>> getRecommendations({
    int limit = 10,
    bool forceRefresh = false,
    RecommendationContext? context,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      _isProcessing = true;
      
      // Determine the recommendation context
      final recommendationContext = context ?? await _buildRecommendationContext();
      final cacheKey = _generateCacheKey(recommendationContext);
      
      // Check if recommendations are cached and not expired
      final cachedRecommendation = _recommendationCache[cacheKey];
      if (!forceRefresh && 
          cachedRecommendation != null && 
          !_isCacheExpired(cachedRecommendation)) {
        _isProcessing = false;
        return cachedRecommendation.recommendations;
      }
      
      // Get user's music preferences
      final musicPreferences = await _getUserMusicPreferences();
      
      // Get recommendations from the engine
      List<MusicRecommendation> recommendations = await _recommendationEngine.generateRecommendations(
        context: recommendationContext,
        preferences: musicPreferences,
        limit: limit,
      );
      
      // Check if we have enough recommendations, if not, supplement with fallback recommendations
      if (recommendations.length < limit) {
        final fallbackCount = limit - recommendations.length;
        final fallbackRecommendations = await _getFallbackRecommendations(
          limit: fallbackCount, 
          existingRecommendations: recommendations,
        );
        recommendations.addAll(fallbackRecommendations);
      }
      
      // Cache the recommendations
      _cacheRecommendations(cacheKey, recommendations);
      
      // Update last update time
      _lastUpdateTime = DateTime.now();
      
      // Notify listeners
      _recommendationsController.add(recommendations);
      
      _isProcessing = false;
      return recommendations;
    } catch (e) {
      _isProcessing = false;
      _lastError = 'Failed to get recommendations: $e';
      debugPrint('❌ $_lastError');
      
      // Return fallback recommendations on error
      return await _getFallbackRecommendations(limit: limit);
    }
  }
  
  /// Get recommendations based on nearby music pins
  Future<List<MusicRecommendation>> getLocationBasedRecommendations({
    required LatLng location,
    double radius = 1000.0, // meters
    int limit = 10,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      _isProcessing = true;
      
      // Build a location-specific context
      final context = RecommendationContext(
        location: location,
        radius: radius,
        timeOfDay: _getTimeOfDay(),
        weather: await _getWeatherConditions(location),
        activityLevel: ActivityLevel.exploring,
      );
      
      // Get user's music preferences
      final musicPreferences = await _getUserMusicPreferences();
      
      // Generate recommendations based on location
      final recommendations = await _recommendationEngine.generateLocationBasedRecommendations(
        context: context,
        preferences: musicPreferences,
        limit: limit,
      );
      
      _isProcessing = false;
      return recommendations;
    } catch (e) {
      _isProcessing = false;
      _lastError = 'Failed to get location-based recommendations: $e';
      debugPrint('❌ $_lastError');
      
      // Return fallback recommendations on error
      return await _getFallbackRecommendations(limit: limit);
    }
  }
  
  /// Get recommendations for creating a new pin
  Future<List<MusicRecommendation>> getRecommendationsForNewPin({
    required LatLng location,
    int limit = 5,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }
      
      _isProcessing = true;
      
      // Special context for pin creation
      final context = RecommendationContext(
        location: location,
        radius: 500.0, // Smaller radius for pin creation
        timeOfDay: _getTimeOfDay(),
        weather: await _getWeatherConditions(location),
        activityLevel: ActivityLevel.creating,
        purpose: RecommendationPurpose.pinCreation,
      );
      
      // Get user's music preferences with higher weight on favorites
      final musicPreferences = await _getUserMusicPreferences(emphasizeFavorites: true);
      
      // Generate pin-optimized recommendations
      final recommendations = await _recommendationEngine.generateRecommendations(
        context: context,
        preferences: musicPreferences,
        limit: limit,
      );
      
      _isProcessing = false;
      return recommendations;
    } catch (e) {
      _isProcessing = false;
      _lastError = 'Failed to get recommendations for new pin: $e';
      debugPrint('❌ $_lastError');
      
      // Return some safe fallback recommendations for pin creation
      return await _getFallbackRecommendations(limit: limit, forPinCreation: true);
    }
  }
  
  /// Build the current recommendation context based on various factors
  Future<RecommendationContext> _buildRecommendationContext() async {
    // Get current location
    final position = _locationManager.currentPosition;
    final location = position != null 
      ? LatLng(position.latitude, position.longitude)
      : LatLng(AppConstants.defaultLatitude, AppConstants.defaultLongitude);
    
    // Determine the current time of day
    final timeOfDay = _getTimeOfDay();
    
    // Get weather conditions (if available)
    final weather = await _getWeatherConditions(location);
    
    // Determine activity level based on user motion and time
    final activityLevel = await _determineActivityLevel();
    
    return RecommendationContext(
      location: location,
      radius: 1000.0, // Default radius (1km)
      timeOfDay: timeOfDay,
      weather: weather,
      activityLevel: activityLevel,
    );
  }
  
  /// Get the current time of day
  TimeOfDay _getTimeOfDay() {
    final hour = DateTime.now().hour;
    
    if (hour >= 5 && hour < 12) {
      return TimeOfDay.morning;
    } else if (hour >= 12 && hour < 17) {
      return TimeOfDay.afternoon;
    } else if (hour >= 17 && hour < 21) {
      return TimeOfDay.evening;
    } else {
      return TimeOfDay.night;
    }
  }
  
  /// Get weather conditions for a location
  Future<WeatherCondition?> _getWeatherConditions(LatLng location) async {
    // In a real implementation, this would call a weather API
    // For demo purposes, we'll return a random weather condition
    try {
      final List<WeatherCondition> conditions = [
        WeatherCondition.clear,
        WeatherCondition.cloudy,
        WeatherCondition.rainy,
        WeatherCondition.snowy,
        WeatherCondition.foggy,
      ];
      
      return conditions[DateTime.now().millisecond % conditions.length];
    } catch (e) {
      debugPrint('❌ Failed to get weather conditions: $e');
      return null;
    }
  }
  
  /// Determine user's current activity level
  Future<ActivityLevel> _determineActivityLevel() async {
    // In a real implementation, this would use motion sensors and historical data
    // For demo purposes, we'll return a sensible default
    final hour = DateTime.now().hour;
    
    // Late night is likely resting
    if (hour >= 0 && hour < 6) {
      return ActivityLevel.resting;
    }
    
    // Early morning might be exercising
    if (hour >= 6 && hour < 9) {
      return ActivityLevel.exercising;
    }
    
    // Workday hours could be working
    if (hour >= 9 && hour < 17) {
      return ActivityLevel.working;
    }
    
    // Evening is often socializing
    if (hour >= 17 && hour < 22) {
      return ActivityLevel.socializing;
    }
    
    // Late evening might be relaxing
    return ActivityLevel.relaxing;
  }
  
  /// Get user's music preferences
  Future<MusicPreferences> _getUserMusicPreferences({bool emphasizeFavorites = false}) async {
    try {
      // Get top tracks from Spotify
      final topTracks = await _spotifyService.getTopTracks(limit: 20);
      
      // Get top genres from Spotify
      final topGenres = await _spotifyService.getTopGenres(limit: 10);
      
      // Get recently played tracks
      final recentlyPlayed = await _spotifyService.getRecentlyPlayed(limit: 10);
      
      // Extract track features
      final List<String> favoriteArtists = [];
      final List<String> favoriteGenres = topGenres;
      
      // Extract favorite artists
      for (final track in topTracks) {
        if (!favoriteArtists.contains(track.artist)) {
          favoriteArtists.add(track.artist);
        }
      }
      
      // Extract mood from recently played (this would use audio features in real app)
      final mood = _determineMoodFromTracks(recentlyPlayed);
      
      return MusicPreferences(
        favoriteArtists: favoriteArtists,
        favoriteGenres: favoriteGenres,
        recentTracks: recentlyPlayed,
        mood: mood,
        preferenceStrength: emphasizeFavorites ? 0.8 : 0.5, // Higher means stronger adherence to preferences
      );
    } catch (e) {
      debugPrint('❌ Failed to get user preferences: $e');
      
      // Return default preferences on error
      return MusicPreferences(
        favoriteArtists: [],
        favoriteGenres: ['pop', 'rock', 'hip-hop'],
        recentTracks: [],
        mood: Mood.energetic,
        preferenceStrength: 0.3, // Lower strength for default preferences
      );
    }
  }
  
  /// Determine mood from tracks
  Mood _determineMoodFromTracks(List<MusicTrack> tracks) {
    // In a real implementation, this would analyze audio features
    // For demo purposes, we'll return a sensible default based on time
    final hour = DateTime.now().hour;
    
    if (hour >= 0 && hour < 6) {
      return Mood.calm;
    } else if (hour >= 6 && hour < 12) {
      return Mood.energetic;
    } else if (hour >= 12 && hour < 17) {
      return Mood.focused;
    } else if (hour >= 17 && hour < 21) {
      return Mood.happy;
    } else {
      return Mood.relaxed;
    }
  }
  
  /// Generate a cache key for a recommendation context
  String _generateCacheKey(RecommendationContext context) {
    return 'rec_${context.location.latitude.toStringAsFixed(3)}_'
           '${context.location.longitude.toStringAsFixed(3)}_'
           '${context.timeOfDay.name}_'
           '${context.weather?.name ?? "unknown"}_'
           '${context.activityLevel.name}';
  }
  
  /// Check if cached recommendations have expired
  bool _isCacheExpired(CachedRecommendation cachedRecommendation) {
    final now = DateTime.now();
    final cacheTime = cachedRecommendation.timestamp;
    
    // Cache expiration time varies based on context
    Duration expirationDuration;
    
    // Location-based recommendations expire faster
    if (cachedRecommendation.cacheKey.contains('exploring')) {
      expirationDuration = const Duration(minutes: 30);
    } else {
      expirationDuration = const Duration(hours: 2);
    }
    
    return now.difference(cacheTime) > expirationDuration;
  }
  
  /// Cache recommendations
  void _cacheRecommendations(String cacheKey, List<MusicRecommendation> recommendations) {
    _recommendationCache[cacheKey] = CachedRecommendation(
      cacheKey: cacheKey,
      recommendations: recommendations,
      timestamp: DateTime.now(),
    );
    
    // Also save to shared preferences for persistence
    _saveCacheToPreferences();
  }
  
  /// Load cached recommendations from shared preferences
  Future<void> _loadCachedRecommendations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString('ai_music_recommendations');
      
      if (cachedJson != null) {
        final Map<String, dynamic> cachedData = jsonDecode(cachedJson);
        
        cachedData.forEach((key, value) {
          final jsonList = value['recommendations'] as List<dynamic>;
          final timestamp = DateTime.parse(value['timestamp']);
          
          final recommendations = jsonList
              .map((json) => MusicRecommendation.fromJson(json))
              .toList();
          
          _recommendationCache[key] = CachedRecommendation(
            cacheKey: key,
            recommendations: recommendations,
            timestamp: timestamp,
          );
        });
        
        debugPrint('📝 Loaded ${_recommendationCache.length} cached recommendations');
      }
    } catch (e) {
      debugPrint('❌ Failed to load cached recommendations: $e');
      // Continue without cached data
    }
  }
  
  /// Save cache to shared preferences
  Future<void> _saveCacheToPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Convert cache to JSON-friendly format
      final Map<String, dynamic> cacheJson = {};
      
      _recommendationCache.forEach((key, cached) {
        cacheJson[key] = {
          'recommendations': cached.recommendations.map((r) => r.toJson()).toList(),
          'timestamp': cached.timestamp.toIso8601String(),
        };
      });
      
      await prefs.setString('ai_music_recommendations', jsonEncode(cacheJson));
    } catch (e) {
      debugPrint('❌ Failed to save cache to preferences: $e');
      // Continue without saving cache
    }
  }
  
  /// Get fallback recommendations when the AI system fails
  Future<List<MusicRecommendation>> _getFallbackRecommendations({
    required int limit,
    List<MusicRecommendation> existingRecommendations = const [],
    bool forPinCreation = false,
  }) async {
    try {
      // Get some popular tracks that aren't already in the recommendations
      final excludeIds = existingRecommendations.map((r) => r.track.id).toList();
      
      // Try to get some tracks from Spotify
      List<MusicTrack> tracks = [];
      
      try {
        if (forPinCreation) {
          // For pin creation, try to get user's top tracks
          tracks = await _spotifyService.getTopTracks(limit: limit);
        } else {
          // Otherwise, try to get some recently played tracks
          tracks = await _spotifyService.getRecentlyPlayed(limit: limit);
        }
      } catch (e) {
        debugPrint('❌ Failed to get fallback tracks from Spotify: $e');
        // Use hardcoded tracks as a last resort
        tracks = _getHardcodedTracks(limit);
      }
      
      // Filter out tracks that are already in the recommendations
      tracks = tracks.where((track) => !excludeIds.contains(track.id)).toList();
      
      // Limit to the requested number
      if (tracks.length > limit) {
        tracks = tracks.sublist(0, limit);
      }
      
      // Convert to recommendations
      return tracks.map((track) => MusicRecommendation(
        track: track,
        confidence: 0.7,
        reason: 'Based on your listening history',
        category: RecommendationCategory.recentlyPlayed,
      )).toList();
    } catch (e) {
      debugPrint('❌ Failed to get fallback recommendations: $e');
      
      // Return hardcoded tracks as a last resort
      final tracks = _getHardcodedTracks(limit);
      
      return tracks.map((track) => MusicRecommendation(
        track: track,
        confidence: 0.5,
        reason: 'Popular track you might enjoy',
        category: RecommendationCategory.popular,
      )).toList();
    }
  }
  
  /// Get hardcoded tracks as a last resort
  List<MusicTrack> _getHardcodedTracks(int limit) {
    // These would be popular/timeless tracks that make sense as defaults
    return List.generate(limit, (index) => MusicTrack.sampleTrack());
  }
  
  /// Dispose resources
  void dispose() {
    _recommendationsController.close();
  }
}

/// Class representing a cached recommendation
class CachedRecommendation {
  final String cacheKey;
  final List<MusicRecommendation> recommendations;
  final DateTime timestamp;
  
  CachedRecommendation({
    required this.cacheKey,
    required this.recommendations,
    required this.timestamp,
  });
}

/// Class representing a music recommendation
class MusicRecommendation {
  final MusicTrack track;
  final double confidence; // 0.0 to 1.0
  final String reason;
  final RecommendationCategory category;
  
  MusicRecommendation({
    required this.track,
    required this.confidence,
    required this.reason,
    required this.category,
  });
  
  factory MusicRecommendation.fromJson(Map<String, dynamic> json) {
    return MusicRecommendation(
      track: MusicTrack.fromJson(json['track']),
      confidence: json['confidence'],
      reason: json['reason'],
      category: RecommendationCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => RecommendationCategory.general,
      ),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'track': track.toJson(),
      'confidence': confidence,
      'reason': reason,
      'category': category.name,
    };
  }
}

/// Enum representing recommendation categories
enum RecommendationCategory {
  locationBased,
  timeBased,
  moodBased,
  weatherBased,
  activityBased,
  genreBased,
  artistBased,
  similar,
  recentlyPlayed,
  popular,
  trending,
  discovery,
  general,
}

/// Enum representing time of day
enum TimeOfDay {
  morning,
  afternoon,
  evening,
  night,
}

/// Enum representing weather conditions
enum WeatherCondition {
  clear,
  cloudy,
  rainy,
  snowy,
  foggy,
}

/// Enum representing activity levels
enum ActivityLevel {
  resting,
  relaxing,
  working,
  exercising,
  socializing,
  exploring,
  creating,
}

/// Enum representing mood
enum Mood {
  happy,
  sad,
  energetic,
  calm,
  focused,
  relaxed,
}

/// Enum representing recommendation purpose
enum RecommendationPurpose {
  listening,
  pinCreation,
  exploration,
}

/// Class representing recommendation context
class RecommendationContext {
  final LatLng location;
  final double radius;
  final TimeOfDay timeOfDay;
  final WeatherCondition? weather;
  final ActivityLevel activityLevel;
  final RecommendationPurpose purpose;
  
  RecommendationContext({
    required this.location,
    required this.radius,
    required this.timeOfDay,
    this.weather,
    required this.activityLevel,
    this.purpose = RecommendationPurpose.listening,
  });
}

/// Class representing user music preferences
class MusicPreferences {
  final List<String> favoriteArtists;
  final List<String> favoriteGenres;
  final List<MusicTrack> recentTracks;
  final Mood mood;
  final double preferenceStrength; // 0.0 to 1.0
  
  MusicPreferences({
    required this.favoriteArtists,
    required this.favoriteGenres,
    required this.recentTracks,
    required this.mood,
    required this.preferenceStrength,
  });
} 