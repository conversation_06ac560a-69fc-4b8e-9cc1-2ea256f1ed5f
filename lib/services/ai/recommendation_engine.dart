import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../models/music_track.dart';
import '../../utils/vector_math_util.dart';
import 'music_recommendation_service.dart';

/// Core AI recommendation engine that powers the music recommendation feature
class RecommendationEngine {
  // Singleton instance
  static final RecommendationEngine _instance = RecommendationEngine._internal();
  factory RecommendationEngine() => _instance;
  
  // Status tracking
  bool _isInitialized = false;
  bool _isProcessing = false;
  String? _lastError;
  
  // Feature vectors for models (simulated for demo)
  late Map<String, List<double>> _genreVectors;
  late Map<String, List<double>> _moodVectors;
  late Map<String, List<double>> _timeVectors;
  late Map<String, List<double>> _weatherVectors;
  late Map<String, List<double>> _activityVectors;
  
  // Importance weights for different features
  final Map<String, double> _featureWeights = {
    'location': 0.25,   // How much location influences recommendations
    'time': 0.15,       // How much time of day influences recommendations
    'weather': 0.10,    // How much weather influences recommendations
    'activity': 0.20,   // How much activity level influences recommendations
    'preferences': 0.30, // How much user preferences influence recommendations
  };
  
  // Cache for track features (to avoid recomputing)
  final Map<String, Map<String, dynamic>> _trackFeaturesCache = {};
  
  // Private constructor for singleton pattern
  RecommendationEngine._internal();
  
  /// Initialize the recommendation engine
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      debugPrint('🤖 Initializing AI Recommendation Engine...');
      
      // Initialize feature vectors for the recommendation models
      _initializeFeatureVectors();
      
      // Load cached track features if available
      await _loadCachedTrackFeatures();
      
      _isInitialized = true;
      debugPrint('🤖 AI Recommendation Engine initialized successfully.');
      return true;
    } catch (e) {
      _lastError = 'Failed to initialize recommendation engine: $e';
      debugPrint('❌ $_lastError');
      return false;
    }
  }
  
  /// Generate recommendations based on context and preferences
  Future<List<MusicRecommendation>> generateRecommendations({
    required RecommendationContext context,
    required MusicPreferences preferences,
    int limit = 10,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      _isProcessing = true;
      
      // In a real implementation, this would call a trained model or API
      // For demo purposes, we'll simulate AI recommendations
      final recommendations = await _simulateAIRecommendations(
        context: context,
        preferences: preferences,
        limit: limit,
      );
      
      _isProcessing = false;
      return recommendations;
    } catch (e) {
      _isProcessing = false;
      _lastError = 'Failed to generate recommendations: $e';
      debugPrint('❌ $_lastError');
      return [];
    }
  }
  
  /// Generate location-based recommendations
  Future<List<MusicRecommendation>> generateLocationBasedRecommendations({
    required RecommendationContext context,
    required MusicPreferences preferences,
    int limit = 10,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      _isProcessing = true;
      
      // Override feature weights to emphasize location more
      final originalWeights = Map<String, double>.from(_featureWeights);
      _featureWeights['location'] = 0.5;  // Significantly increase location weight
      _featureWeights['preferences'] = 0.2; // Slightly reduce preference weight
      
      // Generate recommendations with location emphasis
      final recommendations = await _simulateAIRecommendations(
        context: context,
        preferences: preferences,
        limit: limit,
        emphasizeLocation: true,
      );
      
      // Restore original weights
      _featureWeights.clear();
      _featureWeights.addAll(originalWeights);
      
      _isProcessing = false;
      return recommendations;
    } catch (e) {
      _isProcessing = false;
      _lastError = 'Failed to generate location-based recommendations: $e';
      debugPrint('❌ $_lastError');
      return [];
    }
  }
  
  /// Initialize feature vectors for the recommendation models
  void _initializeFeatureVectors() {
    // In a real ML system, these would be learned vectors from a trained model
    // For this demo, we'll use simulated vectors that make intuitive sense
    
    // Genre vectors (simplified 5D vectors)
    _genreVectors = {
      'pop': [0.8, 0.6, 0.4, 0.3, 0.7],
      'rock': [0.6, 0.8, 0.7, 0.5, 0.3],
      'hip-hop': [0.7, 0.5, 0.8, 0.6, 0.4],
      'electronic': [0.9, 0.4, 0.5, 0.8, 0.6],
      'jazz': [0.4, 0.7, 0.6, 0.3, 0.8],
      'classical': [0.3, 0.8, 0.4, 0.2, 0.7],
      'r&b': [0.7, 0.6, 0.8, 0.5, 0.6],
      'indie': [0.5, 0.7, 0.6, 0.4, 0.5],
      'folk': [0.4, 0.6, 0.3, 0.5, 0.7],
      'metal': [0.6, 0.9, 0.7, 0.4, 0.2],
      'country': [0.5, 0.6, 0.4, 0.7, 0.5],
    };
    
    // Mood vectors (simplified 5D vectors)
    _moodVectors = {
      'happy': [0.9, 0.7, 0.6, 0.5, 0.8],
      'sad': [0.3, 0.4, 0.2, 0.5, 0.3],
      'energetic': [0.8, 0.9, 0.7, 0.6, 0.5],
      'calm': [0.3, 0.2, 0.4, 0.3, 0.5],
      'focused': [0.6, 0.5, 0.7, 0.8, 0.4],
      'relaxed': [0.5, 0.4, 0.3, 0.6, 0.7],
    };
    
    // Time of day vectors (simplified 5D vectors)
    _timeVectors = {
      'morning': [0.8, 0.7, 0.5, 0.6, 0.4],
      'afternoon': [0.6, 0.5, 0.7, 0.6, 0.5],
      'evening': [0.5, 0.6, 0.4, 0.7, 0.6],
      'night': [0.4, 0.3, 0.5, 0.6, 0.8],
    };
    
    // Weather vectors (simplified 5D vectors)
    _weatherVectors = {
      'clear': [0.8, 0.7, 0.6, 0.5, 0.6],
      'cloudy': [0.6, 0.5, 0.4, 0.7, 0.5],
      'rainy': [0.4, 0.5, 0.3, 0.6, 0.4],
      'snowy': [0.5, 0.6, 0.4, 0.5, 0.7],
      'foggy': [0.3, 0.4, 0.5, 0.6, 0.5],
    };
    
    // Activity vectors (simplified 5D vectors)
    _activityVectors = {
      'resting': [0.3, 0.2, 0.4, 0.5, 0.6],
      'relaxing': [0.4, 0.3, 0.5, 0.6, 0.7],
      'working': [0.6, 0.7, 0.5, 0.4, 0.3],
      'exercising': [0.9, 0.8, 0.7, 0.6, 0.5],
      'socializing': [0.8, 0.7, 0.6, 0.7, 0.8],
      'exploring': [0.7, 0.6, 0.8, 0.7, 0.6],
      'creating': [0.6, 0.8, 0.7, 0.5, 0.6],
    };
  }
  
  /// Load cached track features
  Future<void> _loadCachedTrackFeatures() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedFeaturesJson = prefs.getString('ai_track_features_cache');
      
      if (cachedFeaturesJson != null) {
        final Map<String, dynamic> cachedFeatures = jsonDecode(cachedFeaturesJson);
        
        cachedFeatures.forEach((id, features) {
          _trackFeaturesCache[id] = Map<String, dynamic>.from(features);
        });
        
        debugPrint('🤖 Loaded ${_trackFeaturesCache.length} cached track features');
      }
    } catch (e) {
      debugPrint('❌ Failed to load cached track features: $e');
      // Continue without cached data
    }
  }
  
  /// Save track features cache
  Future<void> _saveTrackFeaturesCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Limit cache size to avoid storage issues
      if (_trackFeaturesCache.length > 1000) {
        // Keep only the most recent 500 entries
        final keysToKeep = _trackFeaturesCache.keys.take(500).toList();
        final newCache = <String, Map<String, dynamic>>{};
        
        for (final key in keysToKeep) {
          newCache[key] = _trackFeaturesCache[key]!;
        }
        
        _trackFeaturesCache.clear();
        _trackFeaturesCache.addAll(newCache);
      }
      
      await prefs.setString('ai_track_features_cache', jsonEncode(_trackFeaturesCache));
    } catch (e) {
      debugPrint('❌ Failed to save track features cache: $e');
      // Continue without saving cache
    }
  }
  
  /// Simulate AI recommendations (for demo purposes)
  Future<List<MusicRecommendation>> _simulateAIRecommendations({
    required RecommendationContext context,
    required MusicPreferences preferences,
    int limit = 10,
    bool emphasizeLocation = false,
  }) async {
    // In a real implementation, this would use a machine learning model
    // For demo purposes, we'll create recommendations based on simple rules
    
    // Simulate some "processing" time for realism
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Create a pool of tracks to score
    final tracks = _generateTrackPool(preferences);
    
    // Score each track based on the context and preferences
    final scoredTracks = <ScoredTrack>[];
    
    for (var track in tracks) {
      final score = _scoreTrack(
        track: track,
        context: context,
        preferences: preferences,
        emphasizeLocation: emphasizeLocation,
      );
      
      // Determine the best reason for this recommendation
      final reason = _generateRecommendationReason(
        track: track,
        context: context,
        score: score,
        preferences: preferences,
      );
      
      // Determine the recommendation category
      final category = _determineRecommendationCategory(
        track: track,
        context: context,
        preferences: preferences,
        emphasizeLocation: emphasizeLocation,
      );
      
      scoredTracks.add(ScoredTrack(
        track: track,
        score: score,
        reason: reason,
        category: category,
      ));
    }
    
    // Sort tracks by score (highest first)
    scoredTracks.sort((a, b) => b.score.compareTo(a.score));
    
    // Take the top tracks up to the limit
    final topTracks = scoredTracks.take(limit).toList();
    
    // Convert to recommendations
    return topTracks.map((scored) => MusicRecommendation(
      track: scored.track,
      confidence: _normalizeScore(scored.score),
      reason: scored.reason,
      category: scored.category,
    )).toList();
  }
  
  /// Generate a pool of tracks to consider for recommendations
  List<MusicTrack> _generateTrackPool(MusicPreferences preferences) {
    // In a real implementation, this would query a database or API
    // For demo purposes, we'll create a simulated pool of tracks
    final List<MusicTrack> pool = [];
    
    // Add recent tracks
    pool.addAll(preferences.recentTracks);
    
    // Add sample tracks for various genres
    for (final genre in preferences.favoriteGenres) {
      if (_genreVectors.containsKey(genre)) {
        for (var i = 0; i < 3; i++) {
          pool.add(MusicTrack.sampleTrack());
        }
      }
    }
    
    // Ensure we have enough tracks (at least 30 for a good pool)
    if (pool.length < 30) {
      // Add more generic sample tracks
      for (var i = pool.length; i < 30; i++) {
        pool.add(MusicTrack.sampleTrack());
      }
    }
    
    return pool;
  }
  
  /// Score a track based on context and preferences
  double _scoreTrack({
    required MusicTrack track,
    required RecommendationContext context,
    required MusicPreferences preferences,
    bool emphasizeLocation = false,
  }) {
    // In a real implementation, this would use a sophisticated scoring algorithm
    // based on audio features, user history, and context
    
    double score = 0.0;
    
    // Get track features (cached if available)
    final features = _getTrackFeatures(track);
    
    // Score based on user genre preferences
    double genreScore = 0.0;
    if (preferences.favoriteGenres.isNotEmpty) {
      for (final genre in preferences.favoriteGenres) {
        if (features['genres'].contains(genre)) {
          genreScore += 1.0 / preferences.favoriteGenres.length;
        }
      }
    }
    
    // Score based on user artist preferences
    double artistScore = 0.0;
    if (preferences.favoriteArtists.isNotEmpty) {
      if (preferences.favoriteArtists.contains(track.artist)) {
        artistScore = 1.0;
      }
    }
    
    // Score based on mood
    double moodScore = 0.0;
    if (_moodVectors.containsKey(preferences.mood.name)) {
      final userMoodVector = _moodVectors[preferences.mood.name]!;
      final trackMoodVector = features['moodVector'] as List<double>;
      
      moodScore = _calculateCosineSimilarity(userMoodVector, trackMoodVector);
    }
    
    // Score based on time of day
    double timeScore = 0.0;
    if (_timeVectors.containsKey(context.timeOfDay.name)) {
      final timeVector = _timeVectors[context.timeOfDay.name]!;
      final trackTimeVector = features['timeVector'] as List<double>;
      
      timeScore = _calculateCosineSimilarity(timeVector, trackTimeVector);
    }
    
    // Score based on weather
    double weatherScore = 0.0;
    if (context.weather != null && _weatherVectors.containsKey(context.weather!.name)) {
      final weatherVector = _weatherVectors[context.weather!.name]!;
      final trackWeatherVector = features['weatherVector'] as List<double>;
      
      weatherScore = _calculateCosineSimilarity(weatherVector, trackWeatherVector);
    }
    
    // Score based on activity
    double activityScore = 0.0;
    if (_activityVectors.containsKey(context.activityLevel.name)) {
      final activityVector = _activityVectors[context.activityLevel.name]!;
      final trackActivityVector = features['activityVector'] as List<double>;
      
      activityScore = _calculateCosineSimilarity(activityVector, trackActivityVector);
    }
    
    // Score based on location (for demo, use a simple function of distance)
    double locationScore = 0.0;
    if (emphasizeLocation) {
      // For demo purposes, assign random location scores
      // In a real system, this would be based on location analysis
      locationScore = 0.5 + (math.Random().nextDouble() * 0.5);
    } else {
      locationScore = 0.5; // Neutral score for non-location-emphasized recommendations
    }
    
    // Calculate weighted score
    score = (
      (_featureWeights['preferences']! * ((genreScore * 0.4) + (artistScore * 0.6))) +
      (_featureWeights['time']! * timeScore) +
      (_featureWeights['weather']! * weatherScore) +
      (_featureWeights['activity']! * activityScore) +
      (_featureWeights['location']! * locationScore)
    );
    
    // Apply preference strength modifier
    if (preferences.preferenceStrength > 0.5) {
      // Stronger preference means we bias more toward genre and artist matches
      final preferenceBoost = preferences.preferenceStrength - 0.5;
      score = score * (1.0 + (preferenceBoost * ((genreScore + artistScore) / 2)));
    }
    
    return score;
  }
  
  /// Get features for a track (with caching)
  Map<String, dynamic> _getTrackFeatures(MusicTrack track) {
    // Check if features are cached
    if (_trackFeaturesCache.containsKey(track.id)) {
      return _trackFeaturesCache[track.id]!;
    }
    
    // In a real implementation, this would extract audio features from the track
    // For demo purposes, we'll generate simulated features
    
    // Extract genre vector (use first genre if available, otherwise random)
    List<double> genreVector;
    if (track.genres.isNotEmpty && _genreVectors.containsKey(track.genres[0])) {
      genreVector = List<double>.from(_genreVectors[track.genres[0]]!);
    } else {
      // Random key from genre vectors
      final randomGenre = _genreVectors.keys.elementAt(
        math.Random().nextInt(_genreVectors.length)
      );
      genreVector = List<double>.from(_genreVectors[randomGenre]!);
    }
    
    // Generate random vectors for other features
    // In a real implementation, these would be extracted from audio analysis
    final moodVector = _generateRandomVector(5, seed: track.id.hashCode);
    final timeVector = _generateRandomVector(5, seed: track.id.hashCode + 1);
    final weatherVector = _generateRandomVector(5, seed: track.id.hashCode + 2);
    final activityVector = _generateRandomVector(5, seed: track.id.hashCode + 3);
    
    // Create features map
    final features = {
      'genres': track.genres,
      'artist': track.artist,
      'popularity': track.popularity / 100.0, // Normalize to 0-1
      'genreVector': genreVector,
      'moodVector': moodVector,
      'timeVector': timeVector,
      'weatherVector': weatherVector,
      'activityVector': activityVector,
    };
    
    // Cache the features
    _trackFeaturesCache[track.id] = features;
    
    // Save the cache periodically (every 20 tracks)
    if (_trackFeaturesCache.length % 20 == 0) {
      _saveTrackFeaturesCache();
    }
    
    return features;
  }
  
  /// Generate a recommendation reason
  String _generateRecommendationReason({
    required MusicTrack track,
    required RecommendationContext context,
    required double score,
    required MusicPreferences preferences,
  }) {
    // In a real implementation, this would generate a natural language reason
    // based on the specific factors that contributed to the recommendation
    
    // For demo purposes, we'll use some templates
    
    final reasons = [
      if (preferences.favoriteArtists.contains(track.artist))
        'You listen to ${track.artist} often',
      if (track.genres.any((g) => preferences.favoriteGenres.contains(g)))
        'Based on your interest in ${track.genres.firstWhere(
          (g) => preferences.favoriteGenres.contains(g),
          orElse: () => track.genres.first,
        )}',
      'Perfect for ${context.timeOfDay.name} listening',
      if (context.weather != null)
        'Matches the ${context.weather!.name} weather right now',
      'Great for when you\'re ${context.activityLevel.name}',
      'Matches your current mood',
      'Popular track in this area',
      'Others near you enjoyed this track',
      'Trending in your area',
      'Recommended for your current location',
    ];
    
    // Pick a reason that makes the most sense based on score components
    if (reasons.isNotEmpty) {
      // For demo, pick a random valid reason
      return reasons[math.Random().nextInt(reasons.length)];
    }
    
    // Fallback generic reason
    return 'Recommended based on your preferences';
  }
  
  /// Determine the recommendation category
  RecommendationCategory _determineRecommendationCategory({
    required MusicTrack track,
    required RecommendationContext context,
    required MusicPreferences preferences,
    required bool emphasizeLocation,
  }) {
    // In a real implementation, this would determine the primary reason for recommendation
    
    // For demo purposes, prioritize categories based on context and feature weights
    if (emphasizeLocation) {
      return RecommendationCategory.locationBased;
    }
    
    // Check for category based on highest weight
    final highestWeightKey = _featureWeights.entries
      .reduce((a, b) => a.value > b.value ? a : b)
      .key;
    
    switch (highestWeightKey) {
      case 'location':
        return RecommendationCategory.locationBased;
      case 'time':
        return RecommendationCategory.timeBased;
      case 'weather':
        return RecommendationCategory.weatherBased;
      case 'activity':
        return RecommendationCategory.activityBased;
      default:
        // For preferences, determine if it's genre-based or artist-based
        if (track.genres.any((g) => preferences.favoriteGenres.contains(g))) {
          return RecommendationCategory.genreBased;
        } else if (preferences.favoriteArtists.contains(track.artist)) {
          return RecommendationCategory.artistBased;
        } else {
          // If none of the above, check for other categories
          if (preferences.recentTracks.any((t) => t.id == track.id)) {
            return RecommendationCategory.recentlyPlayed;
          } else if (track.popularity > 70) {
            return RecommendationCategory.popular;
          } else {
            return RecommendationCategory.discovery;
          }
        }
    }
  }
  
  /// Calculate cosine similarity between two vectors
  double _calculateCosineSimilarity(List<double> a, List<double> b) {
    // Ensure vectors are the same length
    if (a.length != b.length) {
      return 0.0;
    }
    
    double dotProduct = 0.0;
    double normA = 0.0;
    double normB = 0.0;
    
    for (int i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    normA = math.sqrt(normA);
    normB = math.sqrt(normB);
    
    if (normA == 0.0 || normB == 0.0) {
      return 0.0;
    }
    
    return dotProduct / (normA * normB);
  }
  
  /// Generate a random vector of given dimension
  List<double> _generateRandomVector(int dimension, {int? seed}) {
    final random = seed != null ? math.Random(seed) : math.Random();
    return List.generate(dimension, (_) => random.nextDouble());
  }
  
  /// Normalize a score to a confidence value between 0 and 1
  double _normalizeScore(double score) {
    // Clamp score between 0 and 1 with a sigmoid-like function
    // Using a custom tanh implementation since dart:math doesn't have tanh
    final x = (score - 0.5) * 3;
    final expX = math.exp(x);
    final expNegX = math.exp(-x);
    final tanhValue = (expX - expNegX) / (expX + expNegX);
    return 0.5 + (0.5 * tanhValue);
  }
}

/// Class to represent a scored track for internal processing
class ScoredTrack {
  final MusicTrack track;
  final double score;
  final String reason;
  final RecommendationCategory category;
  
  ScoredTrack({
    required this.track,
    required this.score,
    required this.reason,
    required this.category,
  });
} 