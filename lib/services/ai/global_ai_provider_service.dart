import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../screens/search/ai_search/ai_search_provider.dart';
import '../../providers/auth_provider.dart';

/// Global service for managing AI provider initialization across the app
/// Ensures single instance and proper lifecycle management
class GlobalAIProviderService {
  static GlobalAIProviderService? _instance;
  static GlobalAIProviderService get instance {
    _instance ??= GlobalAIProviderService._internal();
    return _instance!;
  }
  
  GlobalAIProviderService._internal();
  
  // Single global AI provider instance
  AISearchProvider? _aiProvider;
  bool _isInitialized = false;
  bool _isInitializing = false;
  
  // Getters
  AISearchProvider? get aiProvider => _aiProvider;
  bool get isInitialized => _isInitialized;
  bool get isInitializing => _isInitializing;
  
  /// Initialize the global AI provider if user is authenticated
  Future<void> initializeIfAuthenticated(BuildContext context) async {
    if (_isInitialized || _isInitializing) {
      if (kDebugMode) {
        print('🤖 Global AI Provider already initialized or initializing');
      }
      return;
    }
    

    
    try {
      // Check if user is authenticated
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (!authProvider.isAuthenticated) {
        if (kDebugMode) {
          print('🤖 User not authenticated, skipping AI provider initialization');
        }
        return;
      }
      
      _isInitializing = true;
      if (kDebugMode) {
        print('🤖 Initializing Global AI Provider...');
      }
      
      // Create AI provider instance with context and artist-based category
      _aiProvider = AISearchProvider(context, initialCategory: 'artistBased');
      
      _isInitialized = true;
      _isInitializing = false;
      
      if (kDebugMode) {
        print('✅ Global AI Provider initialized successfully');
      }
      
    } catch (e) {
      _isInitializing = false;
      if (kDebugMode) {
        print('❌ Failed to initialize Global AI Provider: $e');
      }
    }
  }
  
  /// Get or create AI provider instance
  Future<AISearchProvider?> getProvider(BuildContext context) async {
    if (_aiProvider != null && _isInitialized) {
      return _aiProvider;
    }
    
    if (!_isInitializing) {
      await initializeIfAuthenticated(context);
    }
    
    return _aiProvider;
  }
  
  /// Force artist-based recommendations for fast loading
  Future<void> ensureArtistBasedRecommendations(BuildContext context) async {
    final provider = await getProvider(context);
    if (provider != null && provider.currentCategory != 'artistBased') {
      if (kDebugMode) {
        print('🎯 Switching to artist-based recommendations for fast loading');
      }
      provider.onCategoryChanged('artistBased', context);
    }
  }
  
  /// Reset the provider (for logout or re-initialization)
  void reset() {
    if (kDebugMode) {
      print('🔄 Resetting Global AI Provider');
    }
    _aiProvider?.dispose();
    _aiProvider = null;
    _isInitialized = false;
    _isInitializing = false;
  }
  
  /// Clean up resources
  void dispose() {
    _aiProvider?.dispose();
    _aiProvider = null;
    _isInitialized = false;
    _isInitializing = false;
  }
}
