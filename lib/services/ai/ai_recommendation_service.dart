import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;

import '../../models/music_track.dart';
import '../music/spotify_service.dart';
import '../music/spotify_genre_service.dart';
import '../music/spotify_web_api_service.dart';
import '../music/lastfm_service.dart';

/// Represents a listening session for a track
class ListeningSession {
  final MusicTrack track;
  final bool wasAISuggested;
  final DateTime startTime;
  final String category; // which tab/category it was played from
  final String? genre; // genre context if applicable
  DateTime? endTime;
  bool wasSkipped = false;

  ListeningSession({
    required this.track,
    required this.wasAISuggested,
    required this.startTime,
    required this.category,
    this.genre,
  });

  /// Duration the track was played in milliseconds
  int get durationMs =>
      endTime != null ? endTime!.difference(startTime).inMilliseconds : 0;

  /// Whether the track was played for a significant duration (30+ seconds)
  bool get wasPlayedSignificantly => durationMs > 30000;

  /// Whether the track was completed (80%+ of duration)
  bool get wasCompleted => durationMs > (track.durationMs * 0.8);

  /// Score for how much the user liked this track (0.0 to 1.0)
  double get likeScore {
    if (wasCompleted) return 1.0;
    if (wasPlayedSignificantly) return 0.7;
    if (durationMs > 15000) return 0.4; // 15+ seconds
    if (durationMs > 5000) return 0.2; // 5+ seconds
    return 0.0; // Skipped quickly
  }
}

/// Manages personalization data based on listening sessions
class PersonalizationData {
  // Genre-specific data (resets when switching genres)
  final Map<String, List<ListeningSession>> _genreSessions = {};
  final Map<String, Map<String, double>> _genreArtistPreferences = {};
  final Map<String, Map<String, double>> _genreFeaturePreferences = {};

  // Track genre-relevant artists discovered through Last.fm
  final Map<String, Set<String>> _genreDiscoveredArtists = {};
  final Map<String, Set<String>> _genreVerifiedArtists = {};

  // Global data (accumulates across all sessions)
  final List<ListeningSession> _globalSessions = [];
  final Map<String, double> _globalArtistPreferences = {};
  final Map<String, double> _globalFeaturePreferences = {};
  final Set<String> _globalLikedTracks = {};
  final Set<String> _globalSkippedTracks = {};

  String? _currentGenre;

  void clearAllData() {
    _genreSessions.clear();
    _genreArtistPreferences.clear();
    _genreFeaturePreferences.clear();
    _genreDiscoveredArtists.clear();
    _genreVerifiedArtists.clear();
    _globalSessions.clear();
    _globalArtistPreferences.clear();
    _globalFeaturePreferences.clear();
    _globalLikedTracks.clear();
    _globalSkippedTracks.clear();
    _currentGenre = null;
  }

  /// Add a session to tracking
  void addSession(ListeningSession session) {
    // Add to global tracking
    _globalSessions.add(session);
    if (session.wasPlayedSignificantly) {
      _globalLikedTracks.add(session.track.id);
    } else {
      _globalSkippedTracks.add(session.track.id);
    }

    // Separate collaborations to track individual artists
    final individualArtists = _separateCollaborations(session.track.artist);

    // Update global artist preferences for each individual artist
    final artistScore = session.likeScore;
    for (final artist in individualArtists) {
      _globalArtistPreferences[artist] =
          (_globalArtistPreferences[artist] ?? 0.0) + artistScore;
    }

    // Add to genre-specific tracking if applicable
    if (session.genre != null) {
      _genreSessions.putIfAbsent(session.genre!, () => []).add(session);

      // Update genre-specific artist preferences for each individual artist
      _genreArtistPreferences.putIfAbsent(session.genre!, () => {});
      for (final artist in individualArtists) {
        _genreArtistPreferences[session.genre!]![artist] =
            (_genreArtistPreferences[session.genre!]![artist] ?? 0.0) +
                artistScore;
      }
    }

    print(
        '📊 [PersonalizationData] Added session: ${session.track.title} by ${session.track.artist} (individual artists: $individualArtists, score: ${session.likeScore}, genre: ${session.genre})');
  }

  /// Separate collaborations into individual artists
  List<String> _separateCollaborations(String artistString) {
    if (artistString.trim().isEmpty) return [];

    // Split by common collaboration indicators
    final separators = [
      ' feat. ',
      ' featuring ',
      ' ft. ',
      ' with ',
      ' vs. ',
      ' & ',
      ', ',
      ' x ',
      ' X ',
      ' and ',
      ' And ',
      ' AND ',
      ' + ',
      ' / ',
      '; ',
      ' | ',
      ' - '
    ];

    List<String> artists = [artistString];

    // Apply each separator
    for (final separator in separators) {
      final newArtists = <String>[];
      for (final artist in artists) {
        newArtists.addAll(artist.split(separator));
      }
      artists = newArtists;
    }

    // Clean up each artist name
    final cleanedArtists = artists
        .map((artist) => _cleanArtistName(artist))
        .where((artist) => artist.isNotEmpty)
        .toSet() // Remove duplicates
        .toList();

    return cleanedArtists;
  }

  /// Clean individual artist name
  String _cleanArtistName(String artist) {
    return artist
        .trim()
        // Remove common prefixes/suffixes that might remain
        .replaceAll(
            RegExp(r'^(feat\.?|featuring|ft\.?|with|vs\.?|and|&|\+)',
                caseSensitive: false),
            '')
        .replaceAll(
            RegExp(r'(feat\.?|featuring|ft\.?|with|vs\.?|and|&|\+)$',
                caseSensitive: false),
            '')
        // Remove extra whitespace
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  /// Reset genre-specific data when switching genres
  void resetGenreData(String newGenre) {
    if (_currentGenre != newGenre) {
      print(
          '🔄 [PersonalizationData] Switching from ${_currentGenre ?? 'none'} to $newGenre - resetting genre data');
      _currentGenre = newGenre;
      // Genre data remains but we start fresh tracking for this session
    }
  }

  /// Add artists discovered through Last.fm for a specific genre
  void addGenreDiscoveredArtists(String genre, List<String> artists) {
    _genreDiscoveredArtists.putIfAbsent(genre, () => {});
    _genreDiscoveredArtists[genre]!.addAll(artists);
    print(
        '🎯 [PersonalizationData] Added ${artists.length} discovered artists to $genre: $artists');
  }

  /// Add artists verified to be in a specific genre
  void addGenreVerifiedArtists(String genre, List<String> artists) {
    _genreVerifiedArtists.putIfAbsent(genre, () => {});
    _genreVerifiedArtists[genre]!.addAll(artists);
    print(
        '✅ [PersonalizationData] Added ${artists.length} verified artists to $genre: $artists');
  }

  /// Get preferred artists for a genre (or global if no genre) with heavy weighting for genre-relevant artists
  List<String> getPreferredArtists({String? genre, int limit = 10}) {
    if (genre != null && _genreArtistPreferences.containsKey(genre)) {
      // For genre context, heavily weight genre-relevant artists
      return _getGenreWeightedPreferredArtists(genre, limit);
    } else {
      // Global preferences
      final sorted = _globalArtistPreferences.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      return sorted.take(limit).map((e) => e.key).toList();
    }
  }

  /// Get genre-weighted preferred artists, heavily favoring genre-discovered and verified artists
  List<String> _getGenreWeightedPreferredArtists(String genre, int limit) {
    final preferences = _genreArtistPreferences[genre]!;
    final discoveredArtists = _genreDiscoveredArtists[genre] ?? {};
    final verifiedArtists = _genreVerifiedArtists[genre] ?? {};

    // Create weighted scores that heavily favor genre-relevant artists
    final weightedPreferences = <String, double>{};

    for (final entry in preferences.entries) {
      final artist = entry.key;
      final baseScore = entry.value;

      double weightedScore = baseScore;

      // Heavily boost verified artists (from genre classification or similar)
      if (verifiedArtists.contains(artist)) {
        weightedScore *= 5.0; // 5x weight for verified genre artists
        print(
            '🔥 [PersonalizationData] Verified artist boost: $artist (${baseScore} -> ${weightedScore})');
      }
      // Significantly boost discovered artists (from Last.fm similar searches)
      else if (discoveredArtists.contains(artist)) {
        weightedScore *= 3.0; // 3x weight for Last.fm discovered artists
        print(
            '🎵 [PersonalizationData] Discovered artist boost: $artist (${baseScore} -> ${weightedScore})');
      }
      // Keep other artists but with much lower weight
      else {
        weightedScore *= 0.3; // Reduce weight for non-genre-relevant artists
        print(
            '⬇️ [PersonalizationData] Non-genre artist reduced: $artist (${baseScore} -> ${weightedScore})');
      }

      weightedPreferences[artist] = weightedScore;
    }

    // Sort by weighted scores
    final sorted = weightedPreferences.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final result = sorted.take(limit).map((e) => e.key).toList();
    print(
        '🎯 [PersonalizationData] Genre-weighted artists for $genre: $result');

    return result;
  }

  /// Get tracks that should be avoided (frequently skipped)
  Set<String> getTracksToAvoid({String? genre}) {
    if (genre != null && _genreSessions.containsKey(genre)) {
      return _genreSessions[genre]!
          .where((session) => session.likeScore < 0.3)
          .map((session) => session.track.id)
          .toSet();
    }
    return _globalSkippedTracks;
  }

  /// Get success rate for AI suggestions vs user selections
  double getAISuggestionSuccessRate({String? genre}) {
    List<ListeningSession> sessions;
    if (genre != null && _genreSessions.containsKey(genre)) {
      sessions = _genreSessions[genre]!;
    } else {
      sessions = _globalSessions;
    }

    final aiSessions = sessions.where((s) => s.wasAISuggested).toList();
    if (aiSessions.isEmpty) return 0.5; // Default neutral

    final successfulSessions =
        aiSessions.where((s) => s.wasPlayedSignificantly).length;
    return successfulSessions / aiSessions.length;
  }

  /// Get recently liked artists (for boosting similar recommendations)
  List<String> getRecentlyLikedArtists({String? genre, int limit = 5}) {
    List<ListeningSession> sessions;
    if (genre != null && _genreSessions.containsKey(genre)) {
      sessions = _genreSessions[genre]!;
    } else {
      sessions = _globalSessions;
    }

    // Get recent high-scoring sessions (last hour)
    final recentTime = DateTime.now().subtract(const Duration(hours: 1));
    final recentLiked = sessions
        .where((s) => s.startTime.isAfter(recentTime) && s.likeScore > 0.6)
        .map((s) => s.track.artist)
        .toSet()
        .toList();

    return recentLiked.take(limit).toList();
  }
}

class AIRecommendationService {
  // Services
  final SpotifyService _spotifyService = SpotifyService();
  final SpotifyWebApiService _spotifyWebApiService = SpotifyWebApiService();
  final LastFmService _lastFmService = LastFmService();

  // Session tracking
  final PersonalizationData _personalizationData = PersonalizationData();
  ListeningSession? _currentSession;
  Timer? _playbackMonitor;
  String? _lastTrackedTrackId;
  DateTime? _lastPlaybackCheck;

  // Configuration - optimized for speed while maintaining accuracy
  int _explorationRate =
      4; // Reduced from 8 for more focused, personalized results
  int _maxDepth =
      1; // Keep at 1 for speed - depth 1 is sufficient for good results
  double _explorationDecay = 0.7; // More aggressive decay for focus
  final int _pageSize = 30; // Increased page size for better performance

  // State tracking
  final Set<String> _allLoadedTrackIds = {};
  final Set<String> _excludedTrackIds = {};
  final Set<String> _aiSuggestedTracks =
      {}; // Track which tracks were AI suggested
  List<String> _userTopArtists = []; // User's top artists from profile
  int _currentPage = 0;
  int _failedAttempts = 0;

  // International genre support using SpotifyGenreService
  final Map<String, List<String>> _internationalGenreArtists = {
    'j-pop': [
      'あいみょん',
      'aimyon',
      'YOASOBI',
      'King Gnu',
      'LiSA',
      '米津玄師',
      'Yonezu Kenshi',
      'Official髭男dism',
      'Higedan',
      'back number',
      'Perfume',
      'Kyary Pamyu Pamyu',
      'AKB48',
      'Nogizaka46',
      'Arashi',
      'Utada Hikaru',
      '宇多田ヒカル',
      'Ayumi Hamasaki',
      '浜崎あゆみ',
      'ONE OK ROCK',
      'BABYMETAL',
      'X JAPAN',
      'Dir En Grey',
      'Bump of Chicken',
      'Radwimps',
      'Mrs. GREEN APPLE',
      'Kenshi Yonezu',
      'Ado',
      'Eve',
      'Yorushika',
      'Zutomayo',
      'ずっと真夜中でいいのに',
      'Fujii Kaze',
      '藤井風',
      'Sheena Ringo',
      '椎名林檎'
    ],
    'k-pop': [
      'BTS',
      'BLACKPINK',
      'TWICE',
      'ITZY',
      'aespa',
      'NewJeans',
      'IVE',
      'LESSERAFIM',
      'Red Velvet',
      'Girls Generation',
      'SNSD',
      'EXO',
      'Stray Kids',
      'SEVENTEEN',
      'ENHYPEN',
      'TXT',
      'Tomorrow X Together',
      'ATEEZ',
      'NCT',
      'BIGBANG',
      'MAMAMOO',
      '(G)I-DLE',
      'NMIXX',
      'Kep1er',
      'IZ*ONE',
      'IZONE',
      'WANNA ONE',
      'X1',
      'IU',
      'Taeyeon',
      'Rosé',
      'LISA',
      'Jennie',
      'Jisoo',
      'PSY',
      'G-Dragon',
      'Taeyang'
    ],
    'c-pop': [
      'Jay Chou',
      '周杰倫',
      'Eason Chan',
      '陳奕迅',
      'Faye Wong',
      '王菲',
      'Teresa Teng',
      '鄧麗君',
      'Jacky Cheung',
      '張學友',
      'Andy Lau',
      '劉德華',
      'Coco Lee',
      '李玟',
      'Zhang Liangying',
      '張靚穎',
      'Wang Leehom',
      '王力宏',
      'David Tao',
      '陶喆',
      'Mayday',
      '五月天',
      'S.H.E',
      'F4',
      'Twins',
      'G.E.M.',
      '鄧紫棋',
      'JJ Lin',
      '林俊傑'
    ],
    't-pop': [
      'BNK48',
      'CGM48',
      'The Toys',
      'Carabao',
      'Bird Thongchai',
      'Tata Young',
      'Golf & Mike',
      'Palmy',
      'Bodyslam',
      'Potato',
      'Big Ass',
      'Scrubb',
      'Lomosonic',
      'BOWKYLION',
      'Three Man Down',
      'Slot Machine',
      'Polycat',
      'Cocktail',
      'Syndrome'
    ],
    'latin-pop': [
      'Bad Bunny',
      'J Balvin',
      'Karol G',
      'Ozuna',
      'Anuel AA',
      'Daddy Yankee',
      'Maluma',
      'Nicky Jam',
      'Farruko',
      'Becky G',
      'Anitta',
      'Rosalía',
      'Jesse & Joy',
      'Mau y Ricky',
      'Camilo',
      'Rauw Alejandro',
      'Manuel Turizo',
      'Sech',
      'Myke Towers',
      'Jhay Cortez',
      'Lunay',
      'Lali',
      'Tini',
      'CNCO'
    ],
    'afrobeats': [
      'Burna Boy',
      'Wizkid',
      'Davido',
      'Tiwa Savage',
      'Yemi Alade',
      'Mr Eazi',
      'Tekno',
      'Runtown',
      'Patoranking',
      'Kizz Daniel',
      'Fireboy DML',
      'Joeboy',
      'Rema',
      'Omah Lay',
      'Ayra Starr',
      'Tems',
      'CKay',
      'Ruger',
      'Asake',
      'Buju'
    ],
    'reggaeton': [
      'Bad Bunny',
      'J Balvin',
      'Ozuna',
      'Daddy Yankee',
      'Farruko',
      'Anuel AA',
      'Maluma',
      'Nicky Jam',
      'Don Omar',
      'Wisin & Yandel',
      'Arcángel',
      'De La Ghetto',
      'Zion & Lennox',
      'Plan B',
      'Tito El Bambino',
      'Ivy Queen',
      'Cardi B',
      'Sech'
    ]
  };

  AIRecommendationService() {
    _configureLastFmService();
    _startPlaybackMonitoring();
  }

  void _configureLastFmService() {
    _lastFmService.configureExploration(
      maxDepth: _maxDepth,
      maxArtistsPerLevel: _explorationRate,
      explorationDecay: _explorationDecay,
    );
  }

  /// Configure exploration parameters (for testing and optimization)
  void configureExploration({
    int? maxDepth,
    int? explorationRate,
    double? explorationDecay,
  }) {
    if (maxDepth != null) _maxDepth = maxDepth;
    if (explorationRate != null) _explorationRate = explorationRate;
    if (explorationDecay != null) _explorationDecay = explorationDecay;

    // Reconfigure Last.fm service with new parameters
    _configureLastFmService();

    print(
        '🔧 [AIRecommendationService] Updated exploration: depth=$_maxDepth, rate=$_explorationRate, decay=$_explorationDecay');
  }

  /// Start monitoring playback for session tracking
  void _startPlaybackMonitoring() {
    _playbackMonitor?.cancel();
    _playbackMonitor = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkPlaybackState();
    });
    print('🎧 [AIRecommendationService] Started playback monitoring');
  }

  /// Check current playback state and track sessions
  Future<void> _checkPlaybackState() async {
    try {
      if (!await _spotifyWebApiService.isConnected()) {
        return; // Not connected, skip monitoring
      }

      final playbackState = await _spotifyWebApiService.getCurrentPlayback();
      final now = DateTime.now();

      if (playbackState == null || playbackState['item'] == null) {
        // No playback or no track - end current session if any
        _endCurrentSession(now);
        return;
      }

      final currentTrack = playbackState['item'];
      final trackId = currentTrack['id'] as String?;
      final isPlaying = playbackState['is_playing'] as bool? ?? false;

      if (trackId == null) return;

      // If track changed or playback state changed significantly
      if (_lastTrackedTrackId != trackId) {
        // End previous session
        _endCurrentSession(now);

        // Start new session if playing
        if (isPlaying) {
          _startNewSession(trackId, currentTrack, now);
        }

        _lastTrackedTrackId = trackId;
      } else if (!isPlaying && _currentSession != null) {
        // Same track but stopped playing - end session
        _endCurrentSession(now);
      }

      _lastPlaybackCheck = now;
    } catch (e) {
      print('❌ [AIRecommendationService] Error checking playback state: $e');
    }
  }

  /// Start a new listening session
  void _startNewSession(
      String trackId, Map<String, dynamic> trackData, DateTime startTime) {
    try {
      // Create MusicTrack from Spotify data
      final track = _mapSpotifyTrack(trackData);

      // Determine if this was an AI suggestion
      final wasAISuggested = _aiSuggestedTracks.contains(trackId);

      // Get current category context (this would be set by the UI)
      final category = _getCurrentCategory();
      final genre = _getCurrentGenre();

      _currentSession = ListeningSession(
        track: track,
        wasAISuggested: wasAISuggested,
        startTime: startTime,
        category: category,
        genre: genre,
      );

      print(
          '🎵 [AIRecommendationService] Started session: ${track.title} by ${track.artist} (AI: $wasAISuggested, category: $category, genre: $genre)');
    } catch (e) {
      print('❌ [AIRecommendationService] Error starting session: $e');
    }
  }

  /// End the current listening session
  void _endCurrentSession(DateTime endTime) {
    if (_currentSession != null) {
      _currentSession!.endTime = endTime;

      // Add to personalization data
      _personalizationData.addSession(_currentSession!);

      print(
          '🏁 [AIRecommendationService] Ended session: ${_currentSession!.track.title} (duration: ${_currentSession!.durationMs}ms, score: ${_currentSession!.likeScore})');

      _currentSession = null;
    }
  }

  /// Map Spotify track data to MusicTrack model
  MusicTrack _mapSpotifyTrack(Map<String, dynamic> trackData) {
    String albumArt = '';
    if (trackData['album'] != null &&
        trackData['album']['images'] != null &&
        trackData['album']['images'].isNotEmpty) {
      albumArt = trackData['album']['images'][0]['url'];
    }

    String artistName = 'Unknown Artist';
    if (trackData['artists'] != null && trackData['artists'].isNotEmpty) {
      artistName =
          trackData['artists'].map((artist) => artist['name']).join(', ');
    }

    return MusicTrack(
      id: trackData['id'] ?? '',
      title: trackData['name'] ?? 'Unknown Track',
      artist: artistName,
      album: trackData['album']?['name'] ?? '',
      albumArt: albumArt,
      url: trackData['external_urls']?['spotify'] ?? '',
      service: 'spotify',
      serviceType: 'spotify',
      durationMs: trackData['duration_ms'] ?? 0,
      uri: trackData['uri'] ?? '',
    );
  }

  // These would be set by the UI when user navigates
  String _currentCategory = 'all';
  String? _currentGenre;

  String _getCurrentCategory() => _currentCategory;
  String? _getCurrentGenre() => _currentGenre;

  /// Set current context (called by UI)
  void setCurrentContext({required String category, String? genre}) {
    _currentCategory = category;

    // Handle genre changes for genre-specific tracking
    // Genre should persist across all categories when selected
    if (genre != null) {
      if (_currentGenre != genre) {
        _personalizationData.resetGenreData(genre);
        _currentGenre = genre;
        print(
            '🎯 [AIRecommendationService] Genre context set to: $genre (will apply to all categories)');
      }
    } else if (category == 'genreBased') {
      // Only clear genre if we're switching to genreBased category with no genre
      // Otherwise, maintain the previously selected genre across categories
      _currentGenre = null;
      print(
          '🔄 [AIRecommendationService] Genre context cleared (genreBased with no selection)');
    }
    // For other categories, keep the existing genre context

    print(
        '📍 [AIRecommendationService] Context set: category=$category, genre=${_currentGenre ?? 'None'}');
  }

  /// Mark tracks as AI suggested (called when providing recommendations)
  void markTracksAsAISuggested(List<MusicTrack> tracks) {
    for (final track in tracks) {
      _aiSuggestedTracks.add(track.id);
    }
    print(
        '🤖 [AIRecommendationService] Marked ${tracks.length} tracks as AI suggested');
  }

  /// Clear genre context (called when user wants to exit genre-specific mode)
  void clearGenreContext() {
    _currentGenre = null;
    print(
        '🧹 [AIRecommendationService] Genre context cleared - switching to global tracking');
  }

  /// Initialize exclusion data
  void setExclusionData(Set<String> excludedIds) {
    _excludedTrackIds.clear();
    _excludedTrackIds.addAll(excludedIds);
    print(
        '🚫 [AIRecommendationService] Set ${excludedIds.length} excluded tracks.');
  }

  /// Set user's top artists from an external provider
  void setUserTopArtists(List<String> artists) {
    _userTopArtists = artists;
    print(
        '👤 [AIRecommendationService] Received ${_userTopArtists.length} user top artists.');
  }

  /// Reset pagination state
  void resetPagination() {
    _currentPage = 0;
    _allLoadedTrackIds.clear();
    _failedAttempts = 0;
  }

  /// Add tracks to loaded tracking
  void addLoadedTracks(List<MusicTrack> tracks) {
    _allLoadedTrackIds.addAll(tracks.map((track) => track.id));
  }

  /// Get Last.fm-based recommendations for a category with improved endless pagination and personalization
  Future<List<MusicTrack>> getMoreRecommendationsForCategory(
    String category,
    List<String> seedArtists,
    Map<String, dynamic> context,
  ) async {
    // Enhanced seed artist logging with international genre detection
    print('🎵 [AI-Rec] === SEED ARTIST ANALYSIS ===');
    print('🎵 [AI-Rec] Category: $category');
    print(
        '🎵 [AI-Rec] Seed Artists (${seedArtists.length}): ${seedArtists.join(", ")}');

    // Print each seed artist individually for easier reading
    for (int i = 0; i < seedArtists.length; i++) {
      print('🎤 [AI-Rec] Seed Artist ${i + 1}: "${seedArtists[i]}"');
    }

    // Only do expensive international genre detection if we have international characters or known artists
    final hasInternationalChars = seedArtists.any((artist) =>
        RegExp(r'[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\uAC00-\uD7AF]')
            .hasMatch(artist));

    if (hasInternationalChars) {
      final detectedGenres = _detectInternationalGenres(seedArtists);
      if (detectedGenres.isNotEmpty) {
        print(
            '🌍 [AI-Rec] Detected International Genres: ${detectedGenres.join(", ")}');
      }
    }

    // Only log detailed genre context if genre is selected (avoid expensive operations for 'all' category)
    if (context['selectedGenre'] != null) {
      final selectedGenre = context['selectedGenre'] as String;
      final canonicalGenre =
          SpotifyGenreService.getCanonicalGenre(selectedGenre);

      print('🎯 [AI-Rec] Selected Genre: $selectedGenre -> $canonicalGenre');

      // Skip expensive related genres and aliases logging for faster performance
      // These are only needed for debugging, not for actual functionality
    }

    // Enhanced seed artist validation for international genres
    final validatedSeeds =
        _validateInternationalSeedArtists(seedArtists, context);
    print(
        '✅ [AI-Rec] Validated Seeds (${validatedSeeds.length}): ${validatedSeeds.join(", ")}');

    // Set context for session tracking
    setCurrentContext(
      category: category,
      genre: context['selectedGenre'] as String?,
    );

    // Use page from context if provided, otherwise use internal counter
    final currentPage = context['currentPage'] as int? ?? _currentPage;
    final pageSize = context['pageSize'] as int? ?? _pageSize;

    // Update internal page tracking only if no page was provided in context
    if (context['currentPage'] == null) {
      _currentPage++;
    }

    print(
        '📂 Getting Last.fm recommendations for category: $category (page: $currentPage)');
    print('📄 [AI-Rec] Page: $currentPage, Size: $pageSize');
    print('🎵 [AI-Rec] ===========================');

    List<MusicTrack> tracks = [];

    // Enhance seed artists with personalization data and international genre support
    final enhancedSeeds = _enhanceSeedArtists(
        validatedSeeds, category, context['selectedGenre'] as String?);

    try {
      // Use Last.fm for exploration, then focused Spotify searches
      tracks = await _getPersonalizedRecommendations(
        enhancedSeeds,
        category,
        currentPage,
        pageSize,
        context,
      );
    } catch (e) {
      print('❌ [AI] Error in category $category: $e');

      // Minimal fallback - just search for enhanced seeds directly
      tracks = await _getMinimalFallbackTracks(enhancedSeeds, pageSize);
    }

    // If we still have no tracks, try one more minimal fallback
    if (tracks.isEmpty && enhancedSeeds.isNotEmpty) {
      print(
          '⚠️ [AI] No tracks from primary methods, trying minimal fallback...');
      tracks = await _getMinimalFallbackTracks(
          enhancedSeeds.take(3).toList(), pageSize);
    }

    // Apply intelligent filtering with personalization
    final filteredTracks = filterAndDeduplicateTracksIntelligent(tracks);

    // Mark these tracks as AI suggested for session tracking
    markTracksAsAISuggested(filteredTracks);

    print(
        '📂 Last.fm category $category returned ${filteredTracks.length} tracks');
    return filteredTracks;
  }

  /// Detect international genres from seed artists using SpotifyGenreService
  List<String> _detectInternationalGenres(List<String> seedArtists) {
    final detectedGenres = <String>{};

    for (final artist in seedArtists) {
      final artistLower = artist.toLowerCase();

      // Check each international genre
      for (final genreEntry in _internationalGenreArtists.entries) {
        final genre = genreEntry.key;
        final artists = genreEntry.value;

        // Check if artist matches any in the genre list
        if (artists.any((genreArtist) =>
            artistLower.contains(genreArtist.toLowerCase()) ||
            genreArtist.toLowerCase().contains(artistLower))) {
          // Use SpotifyGenreService to get canonical form
          final canonicalGenre = SpotifyGenreService.getCanonicalGenre(genre);
          detectedGenres.add(canonicalGenre);

          print('🌍 [AI-Rec] Artist "$artist" detected as $canonicalGenre');
        }
      }

      bool isInternational = false;

      // Check for Korean characters (Hangul) - Most specific
      if (RegExp(r'[\uAC00-\uD7AF]').hasMatch(artist)) {
        detectedGenres.add('k-pop');
        print(
            '🇰🇷 [AI-Rec] Artist "$artist" contains Korean characters -> k-pop');
        isInternational = true;
      }

      // Check for Japanese characters (Hiragana/Katakana) - Also specific
      if (!isInternational &&
          RegExp(r'[\u3040-\u309F\u30A0-\u30FF]').hasMatch(artist)) {
        detectedGenres.add('j-pop');
        print(
            '🇯🇵 [AI-Rec] Artist "$artist" contains Japanese characters -> j-pop');
        isInternational = true;
      }

      // Check for Chinese characters (Hanzi) - Broader, check last
      if (!isInternational && RegExp(r'[\u4E00-\u9FFF]').hasMatch(artist)) {
        detectedGenres.add('c-pop');
        print(
            '🇨🇳 [AI-Rec] Artist "$artist" contains Chinese characters -> c-pop');
      }
    }

    return detectedGenres.toList();
  }

  /// Validate and enhance seed artists for international genres
  List<String> _validateInternationalSeedArtists(
      List<String> seedArtists, Map<String, dynamic> context) {
    final validatedSeeds = <String>[];
    final selectedGenre = context['selectedGenre'] as String?;

    if (selectedGenre != null) {
      final canonicalGenre =
          SpotifyGenreService.getCanonicalGenre(selectedGenre);

      // Add specific artists for international genres
      if (_internationalGenreArtists.containsKey(canonicalGenre)) {
        final genreArtists = _internationalGenreArtists[canonicalGenre]!;

        // Add top artists for the genre (filtered)
        final filteredGenreArtists =
            genreArtists.where((artist) => !_isProblematicSeed(artist)).take(5);
        validatedSeeds.addAll(filteredGenreArtists);
        print(
            '🌍 [AI-Rec] Added ${filteredGenreArtists.length} genre-specific artists for $canonicalGenre');
      }
    }

    // Add original seed artists (filtered)
    final filteredSeedArtists =
        seedArtists.where((artist) => !_isProblematicSeed(artist));
    validatedSeeds.addAll(filteredSeedArtists);

    final filteredCount = seedArtists.length - filteredSeedArtists.length;
    if (filteredCount > 0) {
      print('🚫 [AI-Rec] Filtered out $filteredCount problematic seed artists');
    }

    // Remove duplicates while preserving order
    final seen = <String>{};
    return validatedSeeds
        .where((artist) => seen.add(artist.toLowerCase()))
        .toList();
  }

  /// Check if an artist name is problematic and should be filtered out
  bool _isProblematicSeed(String artist) {
    final artistLower = artist.toLowerCase().trim();

    // Filter out problematic seeds
    final problematicSeeds = [
      'various artists',
      'various',
      'compilation',
      'soundtrack',
      'original soundtrack',
      'ost',
      'unknown artist',
      'unknown',
      'mixed artists',
      'multiple artists',
      'cast',
      'original cast',
      'ensemble',
      'choir',
      'orchestra',
      'symphony',
    ];

    return problematicSeeds.any((problematic) =>
        artistLower == problematic ||
        artistLower.contains(problematic) ||
        artistLower.startsWith(problematic));
  }

  /// Get personalized recommendations using Last.fm for exploration and focused Spotify searches
  Future<List<MusicTrack>> _getPersonalizedRecommendations(
    List<String> seedArtists,
    String category,
    int currentPage,
    int pageSize,
    Map<String, dynamic> context,
  ) async {
    if (seedArtists.isEmpty) return [];

    print(
        '🎯 [AI] Getting personalized recommendations (page: $currentPage, category: $category)');

    // Step 1: Use Last.fm to discover similar artists (minimal API calls)
    final similarArtists =
        await _discoverSimilarArtistsWithLastFm(seedArtists, currentPage);

    if (similarArtists.isEmpty) {
      // Fallback to enhanced seeds if Last.fm fails
      return await _getFocusedTracksFromArtists(
          seedArtists, currentPage, pageSize);
    }

    // Step 2: Add randomness to artist order to vary results
    final randomizedArtists =
        _randomizeArtistOrder(similarArtists, currentPage);

    // Step 3: Get focused tracks from discovered artists (limited, targeted searches)
    final tracks = await _getFocusedTracksFromArtists(
        randomizedArtists, currentPage, pageSize);

    print(
        '✅ [AI] Personalized recommendations: ${tracks.length} tracks from ${randomizedArtists.length} artists');
    return tracks;
  }

  /// Use Last.fm to discover similar artists (optimized for speed)
  Future<List<String>> _discoverSimilarArtistsWithLastFm(
      List<String> seedArtists, int currentPage) async {
    try {
      print(
          '🔍 [Last.fm] Discovering similar artists from ${seedArtists.length} seeds');
      print('🔍 [Last.fm] Seed Artists Used: ${seedArtists.join(", ")}');

      // Use more seed artists for better variety in infinite scrolling
      final seedLimit =
          math.min(5, seedArtists.length); // Increased from 3 to 5
      final limitedSeeds = seedArtists.take(seedLimit).toList();
      print('🔍 [Last.fm] Limited Seeds for API: ${limitedSeeds.join(", ")}');

      // For infinite scrolling, get more similar artists per seed
      final allSimilarArtists = <String>{};

      final similarArtistsFutures = limitedSeeds.map((seedArtist) async {
        try {
          // Get more similar artists per seed for better variety
          final limit = math.max(8,
              _explorationRate * 2); // At least 8, or double exploration rate
          final similar = await _lastFmService.getSimilarArtists(
            seedArtist,
            limit: limit,
          );
          print(
              '🎯 [Last.fm] Found ${similar.length} similar artists for: $seedArtist');
          return similar;
        } catch (e) {
          print('❌ [Last.fm] Error for seed $seedArtist: $e');
          return <String>[]; // Return empty list on error
        }
      }).toList();

      final results = await Future.wait(similarArtistsFutures);
      for (final similar in results) {
        allSimilarArtists.addAll(similar);
      }

      final similarArtists = allSimilarArtists.toList();

      // Record discovered artists for current genre if we have one
      if (_currentGenre != null && similarArtists.isNotEmpty) {
        _personalizationData.addGenreDiscoveredArtists(
            _currentGenre!, similarArtists);
        _personalizationData.addGenreVerifiedArtists(
            _currentGenre!, limitedSeeds);
        print(
            '🎯 [Last.fm] Recorded ${similarArtists.length} artists for genre: $_currentGenre');
      }

      print(
          '🎵 [Last.fm] Discovered ${similarArtists.length} similar artists: ${similarArtists.take(10).join(", ")}${similarArtists.length > 10 ? "..." : ""}');
      return similarArtists;
    } catch (e) {
      print('❌ [Last.fm] Error discovering similar artists: $e');
      return [];
    }
  }

  /// Add randomness to artist order based on page number with enhanced variety
  List<String> _randomizeArtistOrder(List<String> artists, int currentPage) {
    if (artists.isEmpty) return artists;

    // Use timestamp to add real randomization on refresh while keeping page consistency
    final refreshSeed = DateTime.now().millisecondsSinceEpoch ~/
        10000; // Changes every 10 seconds
    final random =
        math.Random(currentPage * 42 + refreshSeed); // Include refresh variety
    final shuffled = List<String>.from(artists);

    // Enhanced shuffle with multiple rounds for more variety
    for (int round = 0; round < 2; round++) {
      for (int i = shuffled.length - 1; i > 0; i--) {
        int j = random.nextInt(i + 1);
        String temp = shuffled[i];
        shuffled[i] = shuffled[j];
        shuffled[j] = temp;
      }
    }

    // Add additional randomness by rotating based on page + refresh
    final rotation = (currentPage + refreshSeed) % shuffled.length;
    final rotated = shuffled.sublist(rotation) + shuffled.sublist(0, rotation);

    print(
        '🎲 [AI] Randomized ${artists.length} artists for page $currentPage (refresh seed: $refreshSeed)');
    return rotated;
  }

  /// Get focused tracks from specific artists (limited, targeted searches)
  Future<List<MusicTrack>> _getFocusedTracksFromArtists(
      List<String> artists, int currentPage, int pageSize) async {
    if (artists.isEmpty) return [];

    print(
        '🎯 [AI] Getting focused tracks from ${artists.length} artists (page: $currentPage)');

    final allTracks = <MusicTrack>[];
    final maxArtistsPerPage = 8; // Increased back to 8 for more variety
    final tracksPerArtist = 3; // Keep at 3 for good variety per artist

    // Improved pagination: cycle through all artists across pages
    final totalArtists = artists.length;
    final artistsPerPage = math.min(maxArtistsPerPage, totalArtists);

    // Calculate starting index based on page to ensure variety
    // Use a larger step to cycle through artists more quickly
    final stepSize =
        math.max(1, artistsPerPage ~/ 2); // Step by half the artists per page
    final baseOffset = (currentPage * stepSize) % totalArtists;

    // Get different set of artists for each page with proper cycling
    final pageArtists = <String>[];
    for (int i = 0; i < artistsPerPage; i++) {
      final artistIndex = (baseOffset + i) % totalArtists;
      pageArtists.add(artists[artistIndex]);
    }

    print(
        '🎯 [AI] Processing ${pageArtists.length} artists for page $currentPage (base offset: $baseOffset, step: $stepSize)');
    print('🎯 [AI] Page Artists: ${pageArtists.join(", ")}');

    final searchFutures = <Future<List<MusicTrack>>>[];

    // Use hybrid approach for each artist to get better accuracy
    for (int i = 0; i < pageArtists.length; i++) {
      final artist = pageArtists[i];

      // Determine context based on current category and genre
      final context = _currentCategory != null
          ? 'AI_${_currentCategory!.toUpperCase()}'
          : 'AI_GENERAL';

      final fullContext = _currentGenre != null && _currentGenre!.isNotEmpty
          ? '${context}_${_currentGenre!.toUpperCase()}'
          : context;

      print(
          '🔄 [AI] Using hybrid search for artist "$artist" with context: $fullContext');

      searchFutures.add(_spotifyService
          .getArtistTracksHybrid(
        artist,
        limit: tracksPerArtist * 2, // Get more tracks for variety
        includeAppearsOn: true, // Include collaborations
        context: fullContext,
      )
          .then((tracks) {
        print(
            '📥 [AI] Artist "$artist": Got ${tracks.length} tracks via hybrid approach');
        if (tracks.isNotEmpty) {
          print(
              '🎵 [AI] Tracks: ${tracks.map((t) => '${t.title} by ${t.artist}').take(2).join(", ")}${tracks.length > 2 ? "..." : ""}');
        }
        // Take only the requested number for this artist
        return tracks.take(tracksPerArtist).toList();
      }).catchError((e) {
        print('❌ [AI] Error getting hybrid tracks for $artist: $e');
        return <MusicTrack>[];
      }));
    }

    final searchResults = await Future.wait(searchFutures);
    for (final tracks in searchResults) {
      allTracks.addAll(tracks);
    }

    print(
        '✅ [AI] Focused search: ${allTracks.length} tracks from ${pageArtists.length} artists');
    return allTracks;
  }

  /// Get focused search strategy for an artist with enhanced randomization
  String _getFocusedSearchStrategy(
      String artist, int currentPage, int artistIndex) {
    // Check if we have a specific genre selected
    if (_currentGenre != null && _currentGenre!.isNotEmpty) {
      // Genre-specific search strategies that ensure relevance
      final genreStrategies = [
        'genre:"$_currentGenre" artist:"$artist"', // Most specific and relevant
        '$artist $_currentGenre music', // Natural language with genre
        'artist:"$artist" genre:"$_currentGenre"', // Alternative order
        '$_currentGenre $artist songs', // Genre-focused approach
        'genre:"$_currentGenre" similar to "$artist"', // Discovery within genre
        '$artist $_currentGenre hits', // Popular tracks in genre
        'artist:"$artist" $_currentGenre tracks', // Tracks specifically
        'genre:"$_currentGenre" like "$artist"', // Similarity search
      ];

      // Use genre-specific strategy selection
      final strategyIndex =
          (currentPage + artistIndex) % genreStrategies.length;
      final strategy = genreStrategies[strategyIndex];

      print(
          '🎯 [AI] Genre-specific strategy for $artist in $_currentGenre: "$strategy"');
      return strategy;
    } else {
      // General search strategies for when no specific genre is selected
      final strategies = [
        'artist:"$artist"', // Most direct and reliable
        'artist:"$artist" track', // Focus on tracks specifically
        'artist:"$artist" album', // Album tracks
        'artist:"$artist" song', // Songs specifically
        '$artist track', // Less restrictive search
        '$artist music', // Even broader search
        'artist:"$artist" popular', // Popular tracks
        'artist:"$artist" new', // New tracks
      ];

      // Use a more predictable strategy selection to avoid too much randomization
      // that might hit cached empty results
      final strategyIndex = (currentPage + artistIndex) % strategies.length;
      return strategies[strategyIndex];
    }
  }

  /// Minimal fallback with very limited searches
  Future<List<MusicTrack>> _getMinimalFallbackTracks(
      List<String> artists, int limit) async {
    print('🔄 [AI] Minimal fallback with ${artists.length} artists');

    final allTracks = <MusicTrack>[];
    final maxArtists = math.min(3, artists.length); // Very limited fallback

    for (int i = 0; i < maxArtists; i++) {
      final artist = artists[i];

      try {
        // Use hybrid approach for minimal fallback too
        final context = _currentCategory != null
            ? 'AI_${_currentCategory!.toUpperCase()}_FALLBACK'
            : 'AI_FALLBACK';

        final tracks = await _spotifyService.getArtistTracksHybrid(
          artist,
          limit: math.max(2, limit ~/ maxArtists),
          includeAppearsOn: false, // Keep fallback focused on main releases
          context: context,
        );
        allTracks.addAll(tracks);

        // Add delay between searches
        if (i < maxArtists - 1) {
          await Future.delayed(const Duration(milliseconds: 200));
        }
      } catch (e) {
        print('❌ [AI] Minimal fallback failed for $artist: $e');
      }
    }

    return allTracks.take(limit).toList();
  }

  /// Enhance seed artists with personalization data and international genre support
  List<String> _enhanceSeedArtists(
      List<String> originalSeeds, String category, String? genre) {
    print('🔧 [AI] Enhancing seed artists...');
    print('📝 [AI] Original Seeds: ${originalSeeds.join(", ")}');

    // Separate collaborations in original seeds first
    final separatedSeeds = <String>[];
    for (final seed in originalSeeds) {
      separatedSeeds.addAll(_personalizationData._separateCollaborations(seed));
    }
    print('✂️ [AI] Separated Seeds: ${separatedSeeds.join(", ")}');

    // Filter out problematic seeds
    final filteredSeeds =
        separatedSeeds.where((seed) => !_isProblematicSeed(seed)).toList();
    final filteredCount = separatedSeeds.length - filteredSeeds.length;
    if (filteredCount > 0) {
      print(
          '🚫 [AI] Filtered out $filteredCount problematic seeds from separated list');
    }

    final enhanced = List<String>.from(filteredSeeds);

    // Add user's top artists from profile as a baseline, if available
    if (_userTopArtists.isNotEmpty) {
      enhanced.insertAll(0, _userTopArtists);
      print(
          '👤 [AI] Added ${_userTopArtists.length} artists from user profile to seeds.');
    }

    // PRIORITIZE USER PREFERENCES HEAVILY
    if (category == 'genreBased' && genre != null) {
      // Genre-specific personalization - get MORE preferred artists
      final genrePreferred = _personalizationData
          .getPreferredArtists(genre: genre, limit: 8) // Increased from 3
          .where((artist) => !_isProblematicSeed(artist))
          .toList();
      final genreRecent = _personalizationData
          .getRecentlyLikedArtists(genre: genre, limit: 4) // Increased from 2
          .where((artist) => !_isProblematicSeed(artist))
          .toList();

      // Add user preferences at the BEGINNING (higher priority)
      enhanced.insertAll(0, genrePreferred);
      enhanced.insertAll(genrePreferred.length, genreRecent);

      print(
          '🎯 [AI] Enhanced genre seeds with ${genrePreferred.length} preferred + ${genreRecent.length} recent artists for $genre');
      print('🎯 [AI] Genre Preferred: ${genrePreferred.join(", ")}');
      print('🎯 [AI] Genre Recent: ${genreRecent.join(", ")}');

      // Add fewer international genre artists (reduced focus)
      if (genre != null) {
        final canonicalGenre = SpotifyGenreService.getCanonicalGenre(genre);

        if (_internationalGenreArtists.containsKey(canonicalGenre)) {
          final genreArtists = _internationalGenreArtists[canonicalGenre]!;
          // Reduced from 3 to 2 international artists
          final additionalArtists = genreArtists.take(2).toList();
          enhanced.addAll(additionalArtists);
          print(
              '🌍 [AI] Added ${additionalArtists.length} international genre artists for $canonicalGenre: ${additionalArtists.join(", ")}');
        }
      }
    } else {
      // Global personalization for other categories - also get MORE preferred artists
      final globalPreferred = _personalizationData
          .getPreferredArtists(limit: 8) // Increased from 3
          .where((artist) => !_isProblematicSeed(artist))
          .toList();
      final globalRecent = _personalizationData
          .getRecentlyLikedArtists(limit: 4) // Increased from 2
          .where((artist) => !_isProblematicSeed(artist))
          .toList();

      // Add user preferences at the BEGINNING (higher priority)
      enhanced.insertAll(0, globalPreferred);
      enhanced.insertAll(globalPreferred.length, globalRecent);

      print(
          '🌍 [AI] Enhanced global seeds with ${globalPreferred.length} preferred + ${globalRecent.length} recent artists');
      print('🌍 [AI] Global Preferred: ${globalPreferred.join(", ")}');
      print('🌍 [AI] Global Recent: ${globalRecent.join(", ")}');
    }

    // Remove duplicates and filter one more time to ensure clean final list
    final finalSeeds = enhanced
        .toSet()
        .where((artist) => !_isProblematicSeed(artist))
        .toList();
    print(
        '✅ [AI] Final Enhanced Seeds (${finalSeeds.length}): ${finalSeeds.join(", ")}');
    return finalSeeds;
  }

  /// Intelligent filtering that becomes less strict when finding few results and considers personalization
  List<MusicTrack> filterAndDeduplicateTracksIntelligent(
      List<MusicTrack> tracks) {
    print(
        '🧹 Intelligent filtering ${tracks.length} tracks (attempt: $_failedAttempts)...');

    // Enhanced shuffling with timestamp-based randomization for refresh variety
    final refreshSeed = DateTime.now().millisecondsSinceEpoch ~/
        3000; // Changes every 3 seconds
    final random = math.Random(refreshSeed);

    // Multiple shuffle rounds for better randomization
    for (int i = 0; i < 3; i++) {
      tracks.shuffle(random);
    }

    // Get tracks to avoid based on current context
    final tracksToAvoid =
        _personalizationData.getTracksToAvoid(genre: _getCurrentGenre());

    final seenIds = <String>{};
    final seenTitleArtist = <String>{};
    int duplicateCount = 0;
    int titleArtistDuplicateCount = 0;
    int excludedCount = 0;
    int alreadyLoadedCount = 0;
    int personallyAvoidedCount = 0;

    // Adjust filtering strictness based on failed attempts
    final allowSomeExcluded = _failedAttempts >= 2;
    final allowSomeLoaded = _failedAttempts >= 3;
    final allowAvoidedTracks = _failedAttempts >=
        4; // Only allow personally avoided tracks after many failures

    final filteredTracks = tracks.where((track) {
      if (seenIds.contains(track.id)) {
        duplicateCount++;
        return false;
      }

      // Create title-artist key for deduplication
      final cleanTitle = _cleanTrackTitle(track.title);
      final cleanArtist = track.artist.toLowerCase().trim();
      final titleArtistKey = '$cleanTitle-$cleanArtist';

      if (seenTitleArtist.contains(titleArtistKey)) {
        titleArtistDuplicateCount++;
        return false;
      }

      // Check against personally avoided tracks (from listening behavior)
      if (tracksToAvoid.contains(track.id) && !allowAvoidedTracks) {
        personallyAvoidedCount++;
        return false;
      }

      // Be less strict about excluded tracks after failed attempts
      if (_excludedTrackIds.contains(track.id) && !allowSomeExcluded) {
        excludedCount++;
        return false;
      }

      // Be less strict about already loaded tracks after more failed attempts
      if (_allLoadedTrackIds.contains(track.id) && !allowSomeLoaded) {
        alreadyLoadedCount++;
        return false;
      }

      seenIds.add(track.id);
      seenTitleArtist.add(titleArtistKey);
      return true;
    }).toList();

    // If we still have very few tracks, clear some old tracking
    if (filteredTracks.length < 3 && _allLoadedTrackIds.length > 50) {
      print('🧹 Clearing old tracking due to low results...');
      // Keep only the most recent 25 track IDs
      final recentIds =
          _allLoadedTrackIds.skip(_allLoadedTrackIds.length - 25).toSet();
      _allLoadedTrackIds.clear();
      _allLoadedTrackIds.addAll(recentIds);
      _failedAttempts = 0; // Reset after clearing
    }

    // Track failed attempts
    if (filteredTracks.length < 5) {
      _failedAttempts++;
    } else {
      _failedAttempts = 0; // Reset on success
    }

    print('🧹 Intelligent filtering results (attempt: $_failedAttempts):');
    print('  - ID duplicates filtered: $duplicateCount');
    print('  - Title-artist duplicates filtered: $titleArtistDuplicateCount');
    print('  - Excluded tracks: $excludedCount (allowed: $allowSomeExcluded)');
    print(
        '  - Already loaded: $alreadyLoadedCount (allowed: $allowSomeLoaded)');
    print(
        '  - Personally avoided: $personallyAvoidedCount (allowed: $allowAvoidedTracks)');
    print('  - Final tracks: ${filteredTracks.length}');

    return filteredTracks;
  }

  /// Clean track title for better deduplication
  String _cleanTrackTitle(String title) {
    return title
        .toLowerCase()
        .trim()
        .replaceAll(
            RegExp(r'\s*\(.*?\)\s*'), '') // Remove content in parentheses
        .replaceAll(RegExp(r'\s*\[.*?\]\s*'), '') // Remove content in brackets
        .replaceAll(
            RegExp(r'\s*-\s*(remaster|remix|edit|version|explicit).*',
                caseSensitive: false),
            '') // Remove version info
        .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
        .trim();
  }

  /// Sync track IDs with main provider's rolling list
  void syncTrackIds(List<String> recentTrackIds) {
    _allLoadedTrackIds.clear();
    _allLoadedTrackIds.addAll(recentTrackIds);
    print(
        '🔄 [AI Service] Synced ${recentTrackIds.length} track IDs with main provider');
  }

  /// Get personalization insights for debugging/analytics
  Map<String, dynamic> getPersonalizationInsights() {
    return {
      'globalSessions': _personalizationData._globalSessions.length,
      'genreSessions': _personalizationData._genreSessions.length,
      'currentGenre': _currentGenre,
      'currentCategory': _currentCategory,
      'aiSuggestedTracks': _aiSuggestedTracks.length,
      'globalArtistPreferences':
          _personalizationData._globalArtistPreferences.length,
      'aiSuccessRate': _personalizationData.getAISuggestionSuccessRate(),
      'topGlobalArtists': _personalizationData.getPreferredArtists(limit: 5),
      'recentLikedArtists':
          _personalizationData.getRecentlyLikedArtists(limit: 5),
      'currentSession': _currentSession != null
          ? {
              'track': _currentSession!.track.title,
              'artist': _currentSession!.track.artist,
              'wasAISuggested': _currentSession!.wasAISuggested,
              'category': _currentSession!.category,
              'genre': _currentSession!.genre,
              'durationSoFar': _currentSession!.durationMs,
            }
          : null,
    };
  }

  /// Get current preferred artists for the active context (genre-specific or global)
  List<String> getCurrentPreferredArtists({int limit = 8}) {
    List<String> preferredArtists = [];

    if (_currentGenre != null) {
      // Get genre-specific preferred artists
      preferredArtists = _personalizationData.getPreferredArtists(
          genre: _currentGenre, limit: limit);
      print(
          '🎯 [AI] Genre preferred artists for $_currentGenre: $preferredArtists');
    } else {
      // Get global preferred artists
      preferredArtists = _personalizationData.getPreferredArtists(limit: limit);
      print('🌍 [AI] Global preferred artists: $preferredArtists');
    }

    // If no preferences yet, return some popular artists as fallback options
    if (preferredArtists.isEmpty) {
      preferredArtists = _getFallbackArtistOptions(limit);
      print(
          '🎵 [AI] No preferences yet, using fallback artists: $preferredArtists');
    }

    return preferredArtists;
  }

  /// Get fallback artist options when no preferences exist yet
  List<String> _getFallbackArtistOptions(int limit) {
    // Provide popular artists across different genres as initial options
    final fallbackArtists = [
      'Drake',
      'Taylor Swift',
      'The Weeknd',
      'Ariana Grande',
      'Post Malone',
      'Billie Eilish',
      'Dua Lipa',
      'Ed Sheeran',
      'Olivia Rodrigo',
      'Harry Styles',
      'Doja Cat',
      'Lil Baby',
      'Travis Scott',
      'Kanye West',
      'Kendrick Lamar',
      'J. Cole',
      'Bad Bunny',
      'Justin Bieber',
      'The Chainsmokers',
      'Coldplay',
    ];

    // If we have a current genre, try to provide genre-appropriate artists
    if (_currentGenre != null) {
      final genreSpecificArtists =
          _getGenreSpecificFallbackArtists(_currentGenre!);
      if (genreSpecificArtists.isNotEmpty) {
        return genreSpecificArtists.take(limit).toList();
      }
    }

    return fallbackArtists.take(limit).toList();
  }

  /// Get genre-specific fallback artists
  List<String> _getGenreSpecificFallbackArtists(String genre) {
    final genreLower = genre.toLowerCase();

    if (genreLower.contains('hip hop') || genreLower.contains('rap')) {
      return [
        'Drake',
        'Kendrick Lamar',
        'J. Cole',
        'Travis Scott',
        'Lil Baby',
        'Future',
        'Kanye West'
      ];
    } else if (genreLower.contains('pop')) {
      return [
        'Taylor Swift',
        'Ariana Grande',
        'Dua Lipa',
        'Ed Sheeran',
        'Olivia Rodrigo',
        'Harry Styles'
      ];
    } else if (genreLower.contains('rock')) {
      return [
        'Imagine Dragons',
        'Coldplay',
        'The Killers',
        'Arctic Monkeys',
        'Green Day',
        'Red Hot Chili Peppers'
      ];
    } else if (genreLower.contains('electronic') ||
        genreLower.contains('edm')) {
      return [
        'The Chainsmokers',
        'Calvin Harris',
        'David Guetta',
        'Skrillex',
        'Marshmello',
        'Diplo'
      ];
    } else if (genreLower.contains('r&b') || genreLower.contains('soul')) {
      return [
        'The Weeknd',
        'SZA',
        'Frank Ocean',
        'Daniel Caesar',
        'H.E.R.',
        'Summer Walker'
      ];
    } else if (genreLower.contains('country')) {
      return [
        'Morgan Wallen',
        'Luke Combs',
        'Chris Stapleton',
        'Kacey Musgraves',
        'Maren Morris',
        'Keith Urban'
      ];
    } else if (genreLower.contains('indie')) {
      return [
        'Tame Impala',
        'Arctic Monkeys',
        'The Strokes',
        'Vampire Weekend',
        'Foster the People',
        'Glass Animals'
      ];
    }

    return []; // Return empty to use general fallback
  }

  /// Update artist preference when user makes a selection
  void updateArtistPreference(String artist, {double boostScore = 2.0}) {
    // Separate collaborations to boost individual artists
    final individualArtists =
        _personalizationData._separateCollaborations(artist);

    if (_currentGenre != null) {
      // Update genre-specific preference for each individual artist
      _personalizationData._genreArtistPreferences
          .putIfAbsent(_currentGenre!, () => {});
      for (final individualArtist in individualArtists) {
        _personalizationData
                ._genreArtistPreferences[_currentGenre!]![individualArtist] =
            (_personalizationData._genreArtistPreferences[_currentGenre!]![
                        individualArtist] ??
                    0.0) +
                boostScore;
      }
      print(
          '🎯 [AI] Boosted ${individualArtists.join(', ')} preference in $_currentGenre by $boostScore');
    } else {
      // Update global preference for each individual artist
      for (final individualArtist in individualArtists) {
        _personalizationData._globalArtistPreferences[individualArtist] =
            (_personalizationData._globalArtistPreferences[individualArtist] ??
                    0.0) +
                boostScore;
      }
      print(
          '🌍 [AI] Boosted ${individualArtists.join(', ')} global preference by $boostScore');
    }
  }

  /// Check if it's time to show artist preference card (DISABLED)
  bool shouldShowArtistPreferenceCard(int currentTrackIndex) {
    // Artist preference cards are disabled
    return false;
  }

  /// Get AI message for artist preference card
  String getArtistPreferenceMessage() {
    if (_currentGenre != null) {
      return "Which $_currentGenre artists would you like to hear more of? 🎯";
    } else {
      return "Which artists would you like to see more? 🎵";
    }
  }

  // Search activity tracking
  static final List<Map<String, dynamic>> _searchLogs = [];

  /// Log all AI-related search activity for comprehensive tracking
  static void logSearchActivity({
    required String query,
    required String searchType,
    required String context,
    String? category,
    String? genre,
    int? resultsCount,
    int? limit,
    int? offset,
    bool isSuccess = true,
    String? errorMessage,
  }) {
    final timestamp = DateTime.now();

    final logEntry = {
      'timestamp': timestamp.toIso8601String(),
      'query': query,
      'searchType': searchType, // 'tracks', 'genres', 'artists', etc.
      'context':
          context, // AI context like 'AI_MOOD_BASED_HAPPY', 'USER_MANUAL_SEARCH', etc.
      'category': category, // AI category like 'moodBased', 'genreBased', etc.
      'genre': genre, // specific genre if applicable
      'resultsCount': resultsCount,
      'limit': limit,
      'offset': offset,
      'isSuccess': isSuccess,
      'errorMessage': errorMessage,
    };

    _searchLogs.add(logEntry);

    // Keep only the last 500 search logs to prevent memory issues
    if (_searchLogs.length > 500) {
      _searchLogs.removeAt(0);
    }

    // Enhanced logging
    final contextInfo = context.isNotEmpty ? ' [$context]' : '';
    final categoryInfo = category != null ? ' (category: $category)' : '';
    final genreInfo = genre != null ? ' (genre: $genre)' : '';
    final resultsInfo =
        resultsCount != null ? ' -> ${resultsCount} results' : '';
    final statusInfo = isSuccess ? '✅' : '❌';

    print(
        '$statusInfo [AI SEARCH LOG] ${timestamp.toLocal().toString().substring(11, 19)} | "$query"$contextInfo$categoryInfo$genreInfo$resultsInfo');

    if (!isSuccess && errorMessage != null) {
      print('   Error: $errorMessage');
    }
  }

  /// Get comprehensive search analytics for the current session
  static Map<String, dynamic> getSearchAnalytics() {
    if (_searchLogs.isEmpty) {
      return {
        'totalSearches': 0,
        'searchesByContext': {},
        'searchesByCategory': {},
        'searchesByGenre': {},
        'successRate': 0.0,
        'averageResults': 0.0,
        'recentSearches': [],
      };
    }

    final now = DateTime.now();
    final sessionStart = now.subtract(const Duration(hours: 1)); // Last hour

    // Filter to current session
    final sessionLogs = _searchLogs.where((log) {
      final timestamp = DateTime.parse(log['timestamp']);
      return timestamp.isAfter(sessionStart);
    }).toList();

    // Analytics
    final totalSearches = sessionLogs.length;
    final successfulSearches =
        sessionLogs.where((log) => log['isSuccess'] == true).length;
    final successRate =
        totalSearches > 0 ? successfulSearches / totalSearches : 0.0;

    final searchesWithResults =
        sessionLogs.where((log) => log['resultsCount'] != null).toList();
    final totalResults = searchesWithResults.fold<int>(
        0, (sum, log) => sum + (log['resultsCount'] as int? ?? 0));
    final averageResults = searchesWithResults.isNotEmpty
        ? totalResults / searchesWithResults.length
        : 0.0;

    // Group by context
    final searchesByContext = <String, int>{};
    final searchesByCategory = <String, int>{};
    final searchesByGenre = <String, int>{};

    for (final log in sessionLogs) {
      final context = log['context'] as String? ?? 'unknown';
      final category = log['category'] as String?;
      final genre = log['genre'] as String?;

      searchesByContext[context] = (searchesByContext[context] ?? 0) + 1;

      if (category != null) {
        searchesByCategory[category] = (searchesByCategory[category] ?? 0) + 1;
      }

      if (genre != null) {
        searchesByGenre[genre] = (searchesByGenre[genre] ?? 0) + 1;
      }
    }

    // Recent searches (last 10)
    final recentSearches = sessionLogs.reversed
        .take(10)
        .map((log) => {
              'timestamp': log['timestamp'],
              'query': log['query'],
              'context': log['context'],
              'results': log['resultsCount'],
              'success': log['isSuccess'],
            })
        .toList();

    return {
      'totalSearches': totalSearches,
      'successfulSearches': successfulSearches,
      'successRate': successRate,
      'averageResults': averageResults,
      'searchesByContext': searchesByContext,
      'searchesByCategory': searchesByCategory,
      'searchesByGenre': searchesByGenre,
      'recentSearches': recentSearches,
      'timeRange': 'Last 1 hour',
    };
  }

  /// Print detailed search analytics to console
  static void printSearchAnalytics() {
    final analytics = getSearchAnalytics();

    print('\n📊 ===== AI SEARCH ANALYTICS =====');
    print('📈 Total Searches: ${analytics['totalSearches']}');
    print(
        '✅ Success Rate: ${(analytics['successRate'] * 100).toStringAsFixed(1)}%');
    print(
        '📊 Average Results: ${analytics['averageResults'].toStringAsFixed(1)}');
    print('⏰ Time Range: ${analytics['timeRange']}');

    print('\n🏷️ Searches by Context:');
    final contextMap = analytics['searchesByContext'] as Map<String, int>;
    contextMap.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value))
      ..forEach((entry) => print('   ${entry.key}: ${entry.value}'));

    if ((analytics['searchesByCategory'] as Map).isNotEmpty) {
      print('\n📂 Searches by Category:');
      final categoryMap = analytics['searchesByCategory'] as Map<String, int>;
      categoryMap.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value))
        ..forEach((entry) => print('   ${entry.key}: ${entry.value}'));
    }

    if ((analytics['searchesByGenre'] as Map).isNotEmpty) {
      print('\n🎵 Searches by Genre:');
      final genreMap = analytics['searchesByGenre'] as Map<String, int>;
      genreMap.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value))
        ..forEach((entry) => print('   ${entry.key}: ${entry.value}'));
    }

    print('\n🕒 Recent Searches:');
    final recentSearches = analytics['recentSearches'] as List;
    for (final search in recentSearches.take(5)) {
      final time = DateTime.parse(search['timestamp'])
          .toLocal()
          .toString()
          .substring(11, 19);
      final status = search['success'] ? '✅' : '❌';
      final results =
          search['results'] != null ? ' (${search['results']} results)' : '';
      print(
          '   $time $status "${search['query']}" [${search['context']}]$results');
    }

    print('================================\n');
  }

  /// Cleanup resources when service is disposed
  void dispose() {
    _playbackMonitor?.cancel();
    _endCurrentSession(DateTime.now());
    // Print final analytics before disposing
    AIRecommendationService.printSearchAnalytics();
    print('🧹 [AIRecommendationService] Service disposed');
  }
}
