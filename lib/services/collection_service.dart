import 'package:flutter/foundation.dart';
import '../models/api_response.dart';
import '../models/collection_model.dart';
import 'api_service.dart';
import 'auth_service.dart';

class CollectionService {
  final ApiService _apiService;
  final AuthService _authService;

  CollectionService(this._apiService, this._authService);

  Future<List<Collection>> getCollections({int page = 1}) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/pins/collections/',
        queryParams: {
          'page': page.toString(),
        },
        token: token,
      );

      if (!response.success) return [];

      final List<dynamic> collections = response.data['results'] as List<dynamic>;
      return collections.map((json) => Collection.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching collections: $e');
      return [];
    }
  }

  Future<List<Collection>> getMyCollections() async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/pins/collections/my_collections/',
        token: token,
      );

      if (!response.success) return [];

      final List<dynamic> collections = response.data as List<dynamic>;
      return collections.map((json) => Collection.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching my collections: $e');
      return [];
    }
  }

  Future<Collection?> getCollectionById(int collectionId) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/pins/collections/$collectionId/',
        token: token,
      );

      if (!response.success) return null;
      return Collection.fromJson(response.data);
    } catch (e) {
      print('Error fetching collection by ID: $e');
      return null;
    }
  }

  Future<Collection?> createCollection({
    required String name,
    String? description,
    bool isPublic = true,
    String? primaryColor,
    List<String>? coverImageUrls,
  }) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/pins/collections/',
        data: {
          'name': name,
          if (description != null && description.isNotEmpty) 'description': description,
          'is_public': isPublic,
          if (primaryColor != null) 'primary_color': primaryColor,
          if (coverImageUrls != null && coverImageUrls.isNotEmpty) 'cover_image_urls': coverImageUrls,
        },
        token: token,
      );

      if (!response.success) {
        print('Failed to create collection: ${response.message}');
        return null;
      }
      return Collection.fromJson(response.data);
    } catch (e) {
      print('Error creating collection: $e');
      return null;
    }
  }

  Future<Collection?> updateCollection(
    int collectionId, {
    String? name,
    String? description,
    bool? isPublic,
    String? primaryColor,
    List<String>? coverImageUrls,
  }) async {
    try {
      final token = await _authService.getToken();
      final Map<String, dynamic> data = {};

      if (name != null) data['name'] = name;
      if (description != null) data['description'] = description;
      if (isPublic != null) data['is_public'] = isPublic;
      if (primaryColor != null) data['primary_color'] = primaryColor;
      if (coverImageUrls != null) data['cover_image_urls'] = coverImageUrls;

      final response = await _apiService.patch(
        '/pins/collections/$collectionId/',
        data: data,
        token: token,
      );

      if (!response.success) return null;
      return Collection.fromJson(response.data);
    } catch (e) {
      print('Error updating collection: $e');
      return null;
    }
  }

  Future<bool> deleteCollection(int collectionId) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.delete(
        '/pins/collections/$collectionId/',
        token: token,
      );

      if (kDebugMode) {
        print('🗑️ [CollectionService] Delete collection response:');
        print('   Status Code: ${response.statusCode}');
        print('   Success: ${response.success}');
        print('   Message: ${response.message}');
        print('   Error: ${response.error}');
      }

      return response.success;
    } catch (e) {
      print('Error deleting collection: $e');
      return false;
    }
  }

  Future<Collection?> addPinToCollection(
    int collectionId,
    int pinId,
  ) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/pins/collections/$collectionId/add_pin/',
        data: {
          'pin_id': pinId,
        },
        token: token,
      );

      if (!response.success) {
        print('Failed to add pin to collection: ${response.message}');
        return null;
      }
      
      // The response should contain the updated collection or success status
      // If it's just a success response, we need to fetch the updated collection
      if (response.data is Map && response.data.containsKey('success')) {
        return await getCollectionById(collectionId);
      }
      
      return Collection.fromJson(response.data);
    } catch (e) {
      print('Error adding pin to collection: $e');
      return null;
    }
  }

  Future<Collection?> removePinFromCollection(
    int collectionId,
    int pinId,
  ) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/pins/collections/$collectionId/remove_pin/',
        data: {
          'pin_id': pinId,
        },
        token: token,
      );

      if (!response.success) {
        print('Failed to remove pin from collection: ${response.message}');
        return null;
      }
      
      // Similar to addPinToCollection, handle the response appropriately
      if (response.data is Map && response.data.containsKey('success')) {
        return await getCollectionById(collectionId);
      }
      
      return Collection.fromJson(response.data);
    } catch (e) {
      print('Error removing pin from collection: $e');
      return null;
    }
  }

  /// Helper method to add track to collection by creating a virtual pin (no location required)
  Future<bool> addVirtualTrackToCollection({
    required int collectionId,
    required String trackTitle,
    required String trackArtist,
    required String trackUrl,
    required String service,
    String? album,
    String? artworkUrl,
    int? durationMs,
    String? description,
    String? locationName,
  }) async {
    try {
      final token = await _authService.getToken();
      
      // First create a virtual pin for the track (no location required)
      final virtualPinResponse = await _apiService.post(
        '/pins/virtual-pins/',
        data: {
          'title': trackTitle,
          'track_title': trackTitle,
          'track_artist': trackArtist,
          'track_url': trackUrl,
          'service': service,
          if (album != null) 'album': album,
          if (artworkUrl != null) 'artwork_url': artworkUrl,
          if (durationMs != null) 'duration_ms': durationMs,
          if (description != null) 'description': description,
          if (locationName != null) 'location_name': locationName,
          'is_private': true, // Virtual pins are private by default
        },
        token: token,
      );

      if (!virtualPinResponse.success) {
        print('Failed to create virtual pin: ${virtualPinResponse.message}');
        return false;
      }

      final virtualPinId = virtualPinResponse.data['id'] as int;

      // Then add the virtual pin to the collection
      final collectionResponse = await addVirtualPinToCollection(collectionId, virtualPinId);
      return collectionResponse['success'] == true;
    } catch (e) {
      print('Error adding virtual track to collection: $e');
      return false;
    }
  }

  /// Add a virtual pin to a collection
  /// Returns a map with 'success' boolean and optional 'error' and 'statusCode' for detailed error handling
  Future<Map<String, dynamic>> addVirtualPinToCollection(
    int collectionId,
    int virtualPinId,
  ) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/pins/collections/$collectionId/add_virtual_pin/',
        data: {
          'virtual_pin_id': virtualPinId,
        },
        token: token,
      );

      if (!response.success) {
        print('Failed to add virtual pin to collection: ${response.message} (Status: ${response.statusCode})');
        return {
          'success': false,
          'error': response.error ?? 'unknown_error',
          'message': response.message ?? 'Failed to add virtual pin to collection',
          'statusCode': response.statusCode,
        };
      }

      // The response should contain the updated collection or success status
      // If it's just a success response, we need to fetch the updated collection
      Collection? updatedCollection;
      if (response.data is Map && response.data.containsKey('success')) {
        updatedCollection = await getCollectionById(collectionId);
      } else {
        updatedCollection = Collection.fromJson(response.data);
      }

      return {
        'success': true,
        'collection': updatedCollection,
      };
    } catch (e) {
      print('Error adding virtual pin to collection: $e');
      return {
        'success': false,
        'error': 'exception',
        'message': 'Error adding virtual pin to collection: $e',
      };
    }
  }

  /// Add a virtual track to a collection with detailed error information
  /// Returns a map with 'success' boolean and optional error details for UI handling
  Future<Map<String, dynamic>> addVirtualTrackToCollectionWithDetails({
    required int collectionId,
    required String trackTitle,
    required String trackArtist,
    required String trackUrl,
    required String service,
    String? album,
    String? artworkUrl,
    int? durationMs,
    String? description,
    String? locationName,
  }) async {
    try {
      final token = await _authService.getToken();

      // First create a virtual pin for the track (no location required)
      final virtualPinResponse = await _apiService.post(
        '/pins/virtual-pins/',
        data: {
          'title': trackTitle,
          'track_title': trackTitle,
          'track_artist': trackArtist,
          'track_url': trackUrl,
          'service': service,
          if (album != null) 'album': album,
          if (artworkUrl != null) 'artwork_url': artworkUrl,
          if (durationMs != null) 'duration_ms': durationMs,
          if (description != null) 'description': description,
          if (locationName != null) 'location_name': locationName,
          'is_private': true, // Virtual pins are private by default
        },
        token: token,
      );

      if (!virtualPinResponse.success) {
        print('Failed to create virtual pin: ${virtualPinResponse.message}');
        return {
          'success': false,
          'error': 'virtual_pin_creation_failed',
          'message': virtualPinResponse.message ?? 'Failed to create virtual pin',
          'statusCode': virtualPinResponse.statusCode,
        };
      }

      final virtualPinId = virtualPinResponse.data['id'] as int;

      // Then add the virtual pin to the collection
      final collectionResponse = await addVirtualPinToCollection(collectionId, virtualPinId);
      return collectionResponse;
    } catch (e) {
      print('Error adding virtual track to collection: $e');
      return {
        'success': false,
        'error': 'exception',
        'message': 'Error adding virtual track to collection: $e',
      };
    }
  }

  /// Helper method to add track to collection by creating a pin first (DEPRECATED - use addVirtualTrackToCollection for private collections)
  Future<bool> addTrackToCollection({
    required int collectionId,
    required String trackTitle,
    required String trackArtist,
    required String trackUrl,
    required String service,
    required double latitude,
    required double longitude,
    String? album,
    String? artworkUrl,
    int? durationMs,
    String? description,
    String? locationName,
  }) async {
    try {
      final token = await _authService.getToken();
      
      // First create a pin for the track
      final pinResponse = await _apiService.post(
        '/pins/',
        data: {
          'title': trackTitle,
          'track_title': trackTitle,
          'track_artist': trackArtist,
          'track_url': trackUrl,
          'service': service,
          'location': {
            'type': 'Point',
            'coordinates': [longitude, latitude],
          },
          if (album != null) 'album': album,
          if (artworkUrl != null) 'artwork_url': artworkUrl,
          if (durationMs != null) 'duration_ms': durationMs,
          if (description != null) 'description': description,
          if (locationName != null) 'location_name': locationName,
          'is_private': false, // Make it public so it can be added to collections
        },
        token: token,
      );

      if (!pinResponse.success) {
        print('Failed to create pin: ${pinResponse.message}');
        return false;
      }

      final pinId = pinResponse.data['id'] as int;
      
      // Then add the pin to the collection
      final collectionResponse = await addPinToCollection(collectionId, pinId);
      return collectionResponse != null;
    } catch (e) {
      print('Error adding track to collection: $e');
      return false;
    }
  }

  /// Remove virtual pin from collection
  Future<Collection?> removeVirtualPinFromCollection(
    int collectionId,
    int virtualPinId,
  ) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/pins/collections/$collectionId/remove_pin/',
        data: {
          'virtual_pin_id': virtualPinId,
        },
        token: token,
      );

      if (!response.success) {
        print('Failed to remove virtual pin from collection: ${response.message}');
        return null;
      }
      
      // The response should contain success status, then fetch updated collection
      if (response.data is Map && response.data.containsKey('success')) {
        return await getCollectionById(collectionId);
      }
      
      return Collection.fromJson(response.data);
    } catch (e) {
      print('Error removing virtual pin from collection: $e');
      return null;
    }
  }

  /// Remove multiple pins from collection (individual operations)
  Future<int> removePinsFromCollection(
    int collectionId,
    List<Map<String, dynamic>> pinsToRemove,
  ) async {
    int successCount = 0;
    
    for (final pinData in pinsToRemove) {
      try {
        final token = await _authService.getToken();
        
        // Determine if it's a virtual pin or regular pin
        Map<String, dynamic> requestData = {};
        if (pinData['isVirtual'] == true && pinData['virtualPinId'] != null) {
          requestData['virtual_pin_id'] = pinData['virtualPinId'];
        } else if (pinData['pinId'] != null) {
          requestData['pin_id'] = pinData['pinId'];
        } else {
          continue; // Skip invalid pin data
        }
        
        final response = await _apiService.post(
          '/pins/collections/$collectionId/remove_pin/',
          data: requestData,
          token: token,
        );

        if (response.success) {
          successCount++;
        } else {
          print('Failed to remove pin from collection: ${response.message}');
        }
      } catch (e) {
        print('Error removing pin from collection: $e');
      }
    }
    
    return successCount;
  }
} 