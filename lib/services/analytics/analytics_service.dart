import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';

/// Service for tracking analytics and errors throughout the app
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  // Event tracking
  Future<void> trackEvent(String eventName, [Map<String, dynamic>? parameters]) async {
    if (kDebugMode) {
      print('📊 Analytics Event: $eventName');
      if (parameters != null) {
        print('📊 Parameters: $parameters');
      }
    }
    
    // Store event for offline sync
    await _storeEvent(eventName, parameters);
  }
  
  // Error tracking
  Future<void> trackError(String errorType, dynamic error, [StackTrace? stackTrace]) async {
    if (kDebugMode) {
      print('❌ Error: $errorType');
      print('Details: $error');
      if (stackTrace != null) {
        print('Stack trace: $stackTrace');
      }
    }
    
    // Store error for offline sync
    await _storeError(errorType, error, stackTrace);
  }
  
  // Store event for offline sync
  Future<void> _storeEvent(String eventName, Map<String, dynamic>? parameters) async {
    try {
      final events = await _getStoredEvents();
      events.add({
        'name': eventName,
        'parameters': parameters,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      // Keep only last 100 events
      if (events.length > 100) {
        events.removeRange(0, events.length - 100);
      }
      
      await _secureStorage.write(
        key: 'analytics_events',
        value: jsonEncode(events),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error storing analytics event: $e');
      }
    }
  }
  
  // Store error for offline sync
  Future<void> _storeError(String errorType, dynamic error, StackTrace? stackTrace) async {
    try {
      final errors = await _getStoredErrors();
      errors.add({
        'type': errorType,
        'error': error.toString(),
        'stackTrace': stackTrace?.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      // Keep only last 50 errors
      if (errors.length > 50) {
        errors.removeRange(0, errors.length - 50);
      }
      
      await _secureStorage.write(
        key: 'analytics_errors',
        value: jsonEncode(errors),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error storing analytics error: $e');
      }
    }
  }
  
  // Get stored events
  Future<List<Map<String, dynamic>>> _getStoredEvents() async {
    try {
      final eventsString = await _secureStorage.read(key: 'analytics_events');
      if (eventsString != null) {
        final List<dynamic> decodedList = jsonDecode(eventsString);
        return List<Map<String, dynamic>>.from(decodedList);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error reading stored events: $e');
      }
    }
    return [];
  }
  
  // Get stored errors
  Future<List<Map<String, dynamic>>> _getStoredErrors() async {
    try {
      final errorsString = await _secureStorage.read(key: 'analytics_errors');
      if (errorsString != null) {
        final List<dynamic> decodedList = jsonDecode(errorsString);
        return List<Map<String, dynamic>>.from(decodedList);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error reading stored errors: $e');
      }
    }
    return [];
  }
  
  // Sync stored events and errors when online
  Future<void> syncOfflineData() async {
    try {
      final events = await _getStoredEvents();
      final errors = await _getStoredErrors();
      
      if (events.isNotEmpty || errors.isNotEmpty) {
        // Here you would send the data to your analytics service
        if (kDebugMode) {
          print('Syncing ${events.length} events and ${errors.length} errors');
        }
        
        // Clear synced data
        await _secureStorage.delete(key: 'analytics_events');
        await _secureStorage.delete(key: 'analytics_errors');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing offline data: $e');
      }
    }
  }
} 