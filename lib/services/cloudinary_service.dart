import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../config/constants.dart';

/// Exception thrown when Cloudinary operations fail
class CloudinaryException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const CloudinaryException(
    this.message, {
    this.code,
    this.originalError,
  });

  @override
  String toString() => 'CloudinaryException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Result of a successful Cloudinary upload
class CloudinaryUploadResult {
  final String publicId;
  final String secureUrl;
  final String url;
  final int width;
  final int height;
  final String format;
  final int bytes;
  final String? signature;
  final Map<String, dynamic> rawResponse;

  const CloudinaryUploadResult({
    required this.publicId,
    required this.secureUrl,
    required this.url,
    required this.width,
    required this.height,
    required this.format,
    required this.bytes,
    this.signature,
    required this.rawResponse,
  });

  factory CloudinaryUploadResult.fromJson(Map<String, dynamic> json) {
    return CloudinaryUploadResult(
      publicId: json['public_id'] ?? '',
      secureUrl: json['secure_url'] ?? '',
      url: json['url'] ?? '',
      width: json['width'] ?? 0,
      height: json['height'] ?? 0,
      format: json['format'] ?? '',
      bytes: json['bytes'] ?? 0,
      signature: json['signature'],
      rawResponse: json,
    );
  }
}

/// Service for handling Cloudinary image uploads and transformations
class CloudinaryService {
  late final String _cloudName;
  late final String _apiKey;
  late final String _apiSecret;
  late final String? _uploadPreset;

  CloudinaryService() {
    // Load configuration from constants
    _cloudName = AppConstants.cloudinaryCloudName;
    _apiKey = AppConstants.cloudinaryApiKey;
    _apiSecret = AppConstants.cloudinaryApiSecret;
    _uploadPreset = AppConstants.cloudinaryUploadPreset != 'placeholder_cloudinary_upload_preset' 
        ? AppConstants.cloudinaryUploadPreset 
        : null;
    
    // Validate configuration
    if (!AppConstants.isCloudinaryConfigured) {
      throw const CloudinaryException(
        'Cloudinary is not properly configured. Please check your environment variables.',
        code: 'CONFIG_ERROR',
      );
    }
    
    if (kDebugMode) {
      print('✅ CloudinaryService initialized');
      print('   Cloud Name: $_cloudName');
      print('   API Key: ${_apiKey.substring(0, 8)}...');
      print('   Upload Preset: ${_uploadPreset ?? 'Not configured'}');
    }
  }
  
  /// Upload an image file to Cloudinary
  /// 
  /// [file] - The image file to upload
  /// [folder] - Optional folder to organize uploads (e.g., 'collections', 'profiles')
  /// [publicId] - Optional public ID for the image (if not provided, Cloudinary generates one)
  /// [tags] - Optional tags for the image
  /// Returns the secure URL of the uploaded image
  Future<CloudinaryUploadResult> uploadImage({
    required File file,
    String? folder,
    String? publicId,
    List<String>? tags,
    int? quality,
    String? transformation,
  }) async {
    try {
      final bytes = await file.readAsBytes();
      return await uploadImageFromBytes(
        bytes: bytes,
        filename: file.path.split('/').last,
        folder: folder,
        publicId: publicId,
        tags: tags,
        quality: quality,
        transformation: transformation,
      );
    } catch (e) {
      throw CloudinaryException('Failed to read file: $e');
    }
  }
  
  /// Upload image from bytes (useful for web or when you have image data)
  /// 
  /// [bytes] - The image bytes
  /// [filename] - Original filename for reference
  /// [folder] - Optional folder to organize uploads
  /// [publicId] - Optional public ID for the image
  /// [tags] - Optional tags for the image
  /// [quality] - Image quality (1-100)
  /// [transformation] - Optional transformation string
  /// Returns the secure URL of the uploaded image
  Future<CloudinaryUploadResult> uploadImageFromBytes({
    required Uint8List bytes,
    required String filename,
    String? folder,
    String? publicId,
    List<String>? tags,
    int? quality,
    String? transformation,
  }) async {
    try {
      final uploadUrl = 'https://api.cloudinary.com/v1_1/$_cloudName/image/upload';
      
      final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));
      
      // Generate timestamp for signature
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      // Build parameters for signature
      final params = <String, String>{
        'timestamp': timestamp.toString(),
      };
      
      if (folder != null) params['folder'] = folder;
      if (publicId != null) params['public_id'] = publicId;
      if (tags != null && tags.isNotEmpty) params['tags'] = tags.join(',');
      
      // Integrate quality into transformation string because Cloudinary expects it there
      String? finalTransformation = transformation;
      if (quality != null) {
        final qualityStr = 'q_$quality';
        if (finalTransformation == null || finalTransformation.isEmpty) {
          finalTransformation = qualityStr;
        } else {
          finalTransformation = '$qualityStr,$finalTransformation';
        }
      }

      if (finalTransformation != null && finalTransformation.isNotEmpty) {
        params['transformation'] = finalTransformation;
      }
      
      // Generate signature
      final signature = _generateSignature(params, _apiSecret);
      
      // Add all parameters to request
      request.fields.addAll(params);
      request.fields['api_key'] = _apiKey;
      request.fields['signature'] = signature;
      
      // Add the file
      request.files.add(
        http.MultipartFile.fromBytes(
          'file',
          bytes,
          filename: filename,
        ),
      );
      
      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return CloudinaryUploadResult.fromJson(responseData);
      } else {
        final errorData = json.decode(response.body);
        throw CloudinaryException(
          'Upload failed: ${errorData['error']?['message'] ?? 'Unknown error'}',
        );
      }
    } catch (e) {
      if (e is CloudinaryException) rethrow;
      throw CloudinaryException('Upload failed: $e');
    }
  }
  
  /// Upload using unsigned upload (requires upload preset)
  /// This is simpler but less secure - good for public uploads
  Future<CloudinaryUploadResult> uploadImageUnsigned({
    required File file,
    String? folder,
    List<String>? tags,
  }) async {
    try {
      final uploadUrl = 'https://api.cloudinary.com/v1_1/$_cloudName/image/upload';
      
      final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));
      
      // Add upload preset (required for unsigned uploads)
      request.fields['upload_preset'] = _uploadPreset ?? '';
      
      if (folder != null) request.fields['folder'] = folder;
      if (tags != null && tags.isNotEmpty) request.fields['tags'] = tags.join(',');
      
      // Add the file
      final bytes = await file.readAsBytes();
      request.files.add(
        http.MultipartFile.fromBytes(
          'file',
          bytes,
          filename: file.path.split('/').last,
        ),
      );
      
      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return CloudinaryUploadResult.fromJson(responseData);
      } else {
        final errorData = json.decode(response.body);
        throw CloudinaryException(
          'Upload failed: ${errorData['error']?['message'] ?? 'Unknown error'}',
        );
      }
    } catch (e) {
      if (e is CloudinaryException) rethrow;
      throw CloudinaryException('Upload failed: $e');
    }
  }
  
  /// Generate a Cloudinary URL for displaying images with transformations
  /// 
  /// [publicId] - The public ID of the image in Cloudinary
  /// [width] - Optional width for resizing
  /// [height] - Optional height for resizing
  /// [quality] - Image quality (1-100)
  /// [format] - Image format (jpg, png, webp, etc.)
  /// [crop] - Crop mode (fill, fit, scale, etc.)
  String generateImageUrl({
    required String publicId,
    int? width,
    int? height,
    int? quality,
    String? format,
    String? crop,
    List<String>? effects,
  }) {
    try {
      // Build transformation string manually
      List<String> transformations = [];
      
      if (width != null || height != null) {
        String resize = '';
        if (crop != null) resize += 'c_$crop,';
        if (width != null) resize += 'w_$width';
        if (height != null) {
          if (width != null) resize += ',';
          resize += 'h_$height';
        }
        transformations.add(resize);
      }
      
      if (quality != null) {
        transformations.add('q_$quality');
      }
      
      if (format != null) {
        transformations.add('f_$format');
      }
      
      // Apply effects if provided
      if (effects != null) {
        transformations.addAll(effects);
      }
      
      String transformationString = transformations.join(',');
      String baseUrl = 'https://res.cloudinary.com/$_cloudName/image/upload';
      
      if (transformationString.isNotEmpty) {
        return '$baseUrl/$transformationString/$publicId';
      } else {
        return '$baseUrl/$publicId';
      }
    } catch (e) {
      throw CloudinaryException('Failed to generate URL: $e');
    }
  }
  
  /// Delete an image from Cloudinary
  /// 
  /// [publicId] - The public ID of the image to delete
  Future<bool> deleteImage(String publicId) async {
    try {
      final deleteUrl = 'https://api.cloudinary.com/v1_1/$_cloudName/image/destroy';
      
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      final params = {
        'public_id': publicId,
        'timestamp': timestamp.toString(),
      };
      
      final signature = _generateSignature(params, _apiSecret);
      
      final response = await http.post(
        Uri.parse(deleteUrl),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          ...params,
          'api_key': _apiKey,
          'signature': signature,
        },
      );
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['result'] == 'ok';
      }
      
      return false;
    } catch (e) {
      debugPrint('Failed to delete image: $e');
      return false;
    }
  }
  
  /// Generate signature for authenticated requests
  String _generateSignature(Map<String, String> params, String apiSecret) {
    // Sort parameters by key (excluding signature and api_key)
    final filteredParams = Map<String, String>.from(params);
    filteredParams.remove('signature');
    filteredParams.remove('api_key');
    
    final sortedKeys = filteredParams.keys.toList()..sort();
    
    // Create query string with sorted keys
    final queryString = sortedKeys
        .map((key) => '$key=${filteredParams[key]}')
        .join('&');
    
    // Add API secret
    final stringToSign = queryString + apiSecret;
    
    if (kDebugMode) {
      print('🔐 Generating Cloudinary signature:');
      print('   Parameters: $filteredParams');
      print('   Sorted keys: $sortedKeys');
      print('   Query string: $queryString');
      print('   String to sign: $stringToSign');
    }
    
    // Generate SHA1 hash
    final bytes = utf8.encode(stringToSign);
    final digest = sha1.convert(bytes);
    
    return digest.toString();
  }
} 