import 'dart:io';
import '../models/api_response.dart';
import '../models/user.dart';
import 'api_service.dart';
import 'auth_service.dart';

class UserService {
  final ApiService _apiService;
  final AuthService _authService;

  UserService(this._apiService, this._authService);

  Future<User?> getCurrentUser() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/users/me/',
      token: token,
    );

    if (!response.success) return null;
    return User.from<PERSON>son(response.data);
  }

  Future<ApiResponse> updateProfile({
    String? username,
    String? bio,
    String? location,
    File? profilePic,
    String? currentPassword,
    String? newPassword,
  }) async {
    final token = await _authService.getToken();
    final Map<String, dynamic> data = {};

    if (username != null) data['username'] = username;
    if (bio != null) data['bio'] = bio;
    if (location != null) data['location'] = location;
    if (currentPassword != null) data['current_password'] = currentPassword;
    if (newPassword != null) data['new_password'] = newPassword;
    if (profilePic != null) {
      // Handle file upload
      // This will need to be implemented using multipart/form-data
      data['profile_pic'] = profilePic;
    }

    return await _apiService.patch(
      '/api/users/update_profile/',
      data: data,
      token: token,
    );
  }

  Future<ApiResponse> updateLocation(double latitude, double longitude) async {
    final token = await _authService.getToken();
    return await _apiService.post(
      '/api/users/update_location/',
      data: {
        'location': {
          'type': 'Point',
          'coordinates': [longitude, latitude],
        },
      },
      token: token,
    );
  }

  Future<ApiResponse> updateFCMToken(String fcmToken) async {
    final token = await _authService.getToken();
    return await _apiService.post(
      '/api/users/update_fcm_token/',
      data: {
        'fcm_token': fcmToken,
      },
      token: token,
    );
  }

  Future<List<User>> searchUsers(String query, {int page = 1}) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/users/',
      queryParams: {
        'search': query,
        'page': page.toString(),
      },
      token: token,
    );

    if (!response.success) return [];

    final results = response.data['results'] as List<dynamic>;
    return results.map((json) => User.fromJson(json)).toList();
  }

  Future<User?> getUserById(int userId) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/users/$userId/',
      token: token,
    );

    if (!response.success) return null;
    return User.fromJson(response.data);
  }

  Future<Map<String, String>> getUserStats() async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/users/profile_stats/',
        token: token,
      );

      if (!response.success) {
        print('Failed to get user stats: ${response.error}');
        // Return default values if API fails
        return {
          'pins': '0',
          'streak': '0',
          'following': '0',
          'followers': '0',
        };
      }

      // The API returns the data in the exact format we need
      final data = response.data as Map<String, dynamic>;
      return {
        'pins': data['pins']?.toString() ?? '0',
        'streak': data['streak']?.toString() ?? '0',
        'following': data['following']?.toString() ?? '0',
        'followers': data['followers']?.toString() ?? '0',
      };
    } catch (e) {
      print('Error getting user stats: $e');
      // Return default values on error
      return {
        'pins': '0',
        'streak': '0',
        'following': '0',
        'followers': '0',
      };
    }
  }
} 