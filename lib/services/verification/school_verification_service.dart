import 'package:flutter/foundation.dart';
import '../api_service.dart';
import '../auth_service.dart';

class SchoolVerificationService {
  final ApiService _apiService;
  final AuthService _authService;

  SchoolVerificationService({
    ApiService? apiService,
    AuthService? authService,
  }) : _apiService = apiService ?? ApiService(),
        _authService = authService ?? AuthService(ApiService());

  /// Check if user is already verified - returns verification status
  Future<Map<String, dynamic>> checkVerificationStatus() async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.get(
        '/verification/requests/status/',
        token: token,
        requiresAuth: true,
      );

      if (response.success && response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        final isVerified = data['is_verified'] == true || data['status'] == 'approved';
        
        // If user is verified, save the school data to AuthService
        if (isVerified) {
          await _authService.saveSchoolVerification(
            isVerified: true,
            schoolName: data['school_name'],
            schoolDomain: data['school_domain'],
            schoolId: data['school_id']?.toString(),
            verifiedAt: data['verified_at'] != null 
                ? DateTime.parse(data['verified_at'])
                : DateTime.now(),
          );
        }
        
        return {
          'success': true,
          'is_verified': isVerified,
          'status': data['status'],
          'email': data['email'],
          'school_name': data['school_name'],
          'school': data['school'],
          'has_request': data['has_request'] ?? false,
          'can_verify': data['can_verify'] ?? true,
          'verification_reason': data['verification_reason'],
        };
      } else {
        // Check if we have cached school verification data
        final cachedData = await _authService.getSchoolVerificationData();
        if (cachedData['is_verified'] == true) {
          return {
            'success': true,
            'is_verified': true,
            'status': 'approved',
            'school_name': cachedData['school_name'],
            'has_request': false,
            'can_verify': true,
          };
        }
        
        // No verification request exists
        return {
          'success': true,
          'is_verified': false,
          'has_request': false,
          'can_verify': true,
          'status': null,
          'email': null,
          'school_name': null,
          'school': null,
          'verification_reason': null,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Check verification status error: $e');
      }
      
      // Check cached data as fallback
      try {
        final cachedData = await _authService.getSchoolVerificationData();
        if (cachedData['is_verified'] == true) {
          return {
            'success': true,
            'is_verified': true,
            'status': 'approved',
            'school_name': cachedData['school_name'],
            'has_request': false,
            'can_verify': true,
          };
        }
      } catch (cacheError) {
        if (kDebugMode) {
          print('Error checking cached verification data: $cacheError');
        }
      }
      
      return {
        'success': false,
        'is_verified': false,
        'has_request': false,
        'can_verify': true,
        'message': 'Failed to check verification status',
      };
    }
  }

  /// Step 1: Submit verification request - sends code to school email
  Future<Map<String, dynamic>> submitVerificationRequest({
    required String email,
    String? schoolName,
  }) async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.post(
        '/verification/requests/submit/',
        data: {
          'email': email.toLowerCase().trim(),
          if (schoolName != null && schoolName.isNotEmpty) 'school_name': schoolName.trim(),
        },
        token: token,
        requiresAuth: true,
      );

      if (response.success && response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        return {
          'success': true,
          'message': data['message'] ?? 'Verification code sent to your school email',
          'school_name': data['school_name'],
          'email': data['email'],
          'status': data['status'], // 'email_sent'
        };
      } else {
        return {
          'success': false,
          'message': response.userFriendlyMessage,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Verification request error: $e');
      }
      
      // Parse API error messages from your backend
      String errorMessage = 'Failed to send verification code';
      if (e.toString().contains('already verified') || e.toString().contains('User is already verified')) {
        errorMessage = 'You are already verified! Check your school rankings.';
      } else if (e.toString().contains('already have a pending verification request')) {
        errorMessage = 'You already have a pending verification request';
      } else if (e.toString().contains('not recognized as an educational institution')) {
        errorMessage = 'Please use a valid .edu email address';
      } else if (e.toString().contains('Too many verification attempts')) {
        errorMessage = 'Too many attempts. Please wait before trying again';
      }
      
      return {
        'success': false,
        'message': errorMessage,
        'already_verified': e.toString().contains('already verified'),
      };
    }
  }

  /// Step 2: Verify the code sent to email
  Future<Map<String, dynamic>> verifyCode({
    required String code,
  }) async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.post(
        '/verification/requests/verify_code/',
        data: {
          'code': code.trim(),
        },
        token: token,
        requiresAuth: true,
      );

      if (response.success && response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        final isApproved = data['status'] == 'approved' || data['auto_approved'] == true;
        
        // If verification is approved, save school data to AuthService
        if (isApproved) {
          await _authService.saveSchoolVerification(
            isVerified: true,
            schoolName: data['school_name'],
            schoolDomain: data['school_domain'],
            schoolId: data['school_id']?.toString(),
            verifiedAt: DateTime.now(),
          );
        }
        
        return {
          'success': true,
          'message': data['message'] ?? 'Email verified successfully',
          'status': data['status'], // 'approved' or 'email_verified'
          'auto_approved': data['auto_approved'] ?? false,
          'school_name': data['school_name'],
        };
      } else {
        return {
          'success': false,
          'message': response.userFriendlyMessage,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Code verification error: $e');
      }
      
      // Parse API error messages from your backend
      String errorMessage = 'Invalid verification code';
      if (e.toString().contains('Invalid verification code')) {
        errorMessage = 'Invalid verification code';
      } else if (e.toString().contains('Verification code has expired')) {
        errorMessage = 'Verification code has expired. Please request a new one';
      } else if (e.toString().contains('No pending verification request found')) {
        errorMessage = 'No pending verification request found';
      }
      
      return {
        'success': false,
        'message': errorMessage,
      };
    }
  }

  /// Resend verification code
  Future<Map<String, dynamic>> resendCode() async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.post(
        '/verification/requests/resend_code/',
        data: {}, // Empty body as per documentation
        token: token,
        requiresAuth: true,
      );

      if (response.success && response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        return {
          'success': true,
          'message': data['message'] ?? 'Verification code sent successfully',
          'email': data['email'],
        };
      } else {
        return {
          'success': false,
          'message': response.userFriendlyMessage,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Resend code error: $e');
      }
      
      String errorMessage = 'Failed to resend verification code';
      if (e.toString().contains('No pending verification request found')) {
        errorMessage = 'No pending verification request found';
      } else if (e.toString().contains('Please wait before requesting another code')) {
        errorMessage = 'Please wait before requesting another code';
      }
      
      return {
        'success': false,
        'message': errorMessage,
      };
    }
  }

  /// Get current verification status
  Future<Map<String, dynamic>> getVerificationStatus() async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.get(
        '/verification/requests/status/',
        token: token,
        requiresAuth: true,
      );

      if (response.success && response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        return {
          'success': true,
          'status': data['status'],
          'email': data['email'],
          'school_name': data['school_name'],
          'email_verified': data['email_verified'] ?? false,
          'created_at': data['created_at'],
          'updated_at': data['updated_at'],
        };
      } else {
        return {
          'success': false,
          'message': response.userFriendlyMessage,
      };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get status error: $e');
      }
      
      String errorMessage = 'Failed to get verification status';
      if (e.toString().contains('No verification request found')) {
        errorMessage = 'No verification request found';
      }
      
      return {
        'success': false,
        'message': errorMessage,
      };
    }
  }

  // Legacy method for backward compatibility - now uses new two-step process
  @Deprecated('Use submitVerificationRequest and verifyCode instead')
  Future<Map<String, dynamic>> verifySchool({
    required String firstName,
    required String lastName,
    required String email,
    required String schoolName,
  }) async {
    // Just call the new submit method for backward compatibility
    return await submitVerificationRequest(
      email: email,
      schoolName: schoolName,
    );
  }
} 