import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:latlong2/latlong.dart';

import '../core/vector_tile_types.dart';
import '../core/tile_provider.dart';
import '../cache/tile_cache_manager.dart';

/// Provides vector tiles from a remote source with caching and error handling.
class NetworkTileProvider extends BaseTileProvider {
  final http.Client _client;
  final TileCacheManager _cacheManager;
  
  // Request throttling
  final Duration _minRequestInterval;
  DateTime _lastRequestTime = DateTime.now().subtract(const Duration(seconds: 30));
  
  // Error handling
  int _consecutiveErrors = 0;
  static const _maxConsecutiveErrors = 3;
  Duration _currentBackoff = const Duration(seconds: 1);
  static const _maxBackoff = const Duration(minutes: 5);
  
  // Parallel request limiting
  final int _maxParallelRequests;
  final Set<String> _pendingRequests = {};
  final _requestQueue = <_QueuedRequest>[];
  
  NetworkTileProvider({
    required VectorTileSource source,
    http.Client? client,
    TileCacheManager? cacheManager,
    Duration minRequestInterval = const Duration(milliseconds: 100),
    int maxParallelRequests = 6,
  }) : _client = client ?? http.Client(),
       _cacheManager = cacheManager ?? TileCacheManager(),
       _minRequestInterval = minRequestInterval,
       _maxParallelRequests = maxParallelRequests,
       super(source);

  @override
  Future<VectorTile?> getTile(TileCoordinate coordinate) async {
    checkDisposed();
    
    // Check cache first
    final cachedTile = await _cacheManager.getTile(coordinate, source.name);
    if (cachedTile != null) {
      return cachedTile;
    }

    // Generate tile URL
    final url = _getTileUrl(coordinate);
    
    // Check if this URL is already being fetched
    if (_pendingRequests.contains(url)) {
      // Wait for the existing request
      final completer = Completer<VectorTile?>();
      _requestQueue.add(_QueuedRequest(url, completer));
      return completer.future;
    }

    return _fetchTile(coordinate, url);
  }

  @override
  Future<List<VectorTile>> getTiles(List<TileCoordinate> coordinates) async {
    checkDisposed();
    
    final tiles = <VectorTile>[];
    final futures = <Future<void>>[];

    for (final coordinate in coordinates) {
      futures.add(getTile(coordinate).then((tile) {
        if (tile != null) {
          tiles.add(tile);
        }
      }));
    }

    await Future.wait(futures);
    return tiles;
  }

  @override
  Future<bool> hasTile(TileCoordinate coordinate) async {
    checkDisposed();
    final cachedTile = await _cacheManager.getTile(coordinate, source.name);
    return cachedTile != null;
  }

  @override
  Future<void> prefetchTiles(
    LatLngBounds bounds,
    int minZoom,
    int maxZoom, {
    bool force = false,
  }) async {
    checkDisposed();
    
    final futures = <Future<void>>[];
    
    for (var z = minZoom; z <= maxZoom; z++) {
      final coordinates = getTileCoordinates(bounds, z);
      
      for (final coordinate in coordinates) {
        if (force || !await hasTile(coordinate)) {
          futures.add(getTile(coordinate).then((_) {}));
        }
      }
    }

    await Future.wait(futures);
  }

  @override
  Future<void> clearCache() async {
    await _cacheManager.clearCache();
  }

  @override
  Map<String, dynamic> getCacheStats() {
    return {
      ..._cacheManager.getCacheStats(),
      'pending_requests': _pendingRequests.length,
      'queued_requests': _requestQueue.length,
      'consecutive_errors': _consecutiveErrors,
      'current_backoff': _currentBackoff.inSeconds,
    };
  }

  /// Fetch a tile from the network
  Future<VectorTile?> _fetchTile(TileCoordinate coordinate, String url) async {
    _pendingRequests.add(url);
    
    try {
      // Throttle requests
      final now = DateTime.now();
      final timeSinceLastRequest = now.difference(_lastRequestTime);
      if (timeSinceLastRequest < _minRequestInterval) {
        await Future.delayed(_minRequestInterval - timeSinceLastRequest);
      }
      _lastRequestTime = DateTime.now();

      // Apply exponential backoff if we've had errors
      if (_consecutiveErrors > 0) {
        await Future.delayed(_currentBackoff);
      }

      // Make the request
      final response = await _client.get(Uri.parse(url));

      if (response.statusCode == 200) {
        // Parse and validate the response
        final tile = await _parseTileResponse(response.body, coordinate);
        
        // Cache the tile
        if (tile != null) {
          await _cacheManager.storeTile(tile, source.name);
        }

        // Reset error counters on success
        _consecutiveErrors = 0;
        _currentBackoff = const Duration(seconds: 1);

        // Process queued requests for this URL
        _processQueuedRequests(url, tile);
        
        return tile;
      } else {
        throw TileLoadException(
          'HTTP ${response.statusCode}',
          coordinate,
        );
      }
    } catch (e) {
      // Handle errors
      _consecutiveErrors++;
      if (_consecutiveErrors >= _maxConsecutiveErrors) {
        _currentBackoff *= 2;
        if (_currentBackoff > _maxBackoff) {
          _currentBackoff = _maxBackoff;
        }
      }

      // Process queued requests with error
      _processQueuedRequests(url, null);
      
      debugPrint('Error fetching tile $coordinate: $e');
      return null;
    } finally {
      _pendingRequests.remove(url);
    }
  }

  /// Process any queued requests for a URL
  void _processQueuedRequests(String url, VectorTile? tile) {
    final requests = _requestQueue.where((r) => r.url == url).toList();
    _requestQueue.removeWhere((r) => r.url == url);
    
    for (final request in requests) {
      request.completer.complete(tile);
    }
  }

  /// Parse a tile response into a VectorTile object
  Future<VectorTile?> _parseTileResponse(
    String responseBody,
    TileCoordinate coordinate,
  ) async {
    try {
      final json = jsonDecode(responseBody);
      
      // Validate the response format
      if (json is! Map<String, dynamic> ||
          !json.containsKey('layers') ||
          json['layers'] is! List) {
        throw FormatException('Invalid tile format');
      }

      // Parse layers
      final layers = <VectorTileLayer>[];
      for (final layerJson in json['layers']) {
        if (layerJson is! Map<String, dynamic>) continue;
        
        try {
          layers.add(VectorTileLayer(
            name: layerJson['name'],
            extent: layerJson['extent'] ?? 4096,
            features: _parseFeatures(layerJson['features'] ?? []),
            metadata: layerJson['metadata'] ?? {},
          ));
        } catch (e) {
          debugPrint('Error parsing layer: $e');
          // Continue with other layers
        }
      }

      return VectorTile(
        coordinate: coordinate,
        layers: layers,
        timestamp: DateTime.now(),
        metadata: json['metadata'] ?? {},
      );
    } catch (e) {
      debugPrint('Error parsing tile response: $e');
      return null;
    }
  }

  /// Parse features from JSON
  List<VectorTileFeature> _parseFeatures(List features) {
    return features.map((featureJson) {
      if (featureJson is! Map<String, dynamic>) {
        throw FormatException('Invalid feature format');
      }

      return VectorTileFeature(
        id: featureJson['id'],
        layerName: featureJson['layer_name'],
        properties: featureJson['properties'] ?? {},
        geometry: _parseGeometry(featureJson['geometry'] ?? []),
        type: _parseFeatureType(featureJson['type']),
      );
    }).toList();
  }

  /// Parse geometry from JSON
  List<List<LatLng>> _parseGeometry(List geometry) {
    return geometry.map((ring) {
      if (ring is! List) {
        throw FormatException('Invalid geometry format');
      }

      return ring.map((point) {
        if (point is! List || point.length != 2) {
          throw FormatException('Invalid point format');
        }

        return LatLng(
          point[0].toDouble(),
          point[1].toDouble(),
        );
      }).toList();
    }).toList();
  }

  /// Parse feature type from JSON
  VectorTileFeatureType _parseFeatureType(dynamic type) {
    if (type is int && type >= 0 && type < VectorTileFeatureType.values.length) {
      return VectorTileFeatureType.values[type];
    }
    return VectorTileFeatureType.polygon; // Default to polygon
  }

  /// Generate a tile URL from coordinates
  String _getTileUrl(TileCoordinate coordinate) {
    return source.urlTemplate
        .replaceAll('{z}', coordinate.z.toString())
        .replaceAll('{x}', coordinate.x.toString())
        .replaceAll('{y}', coordinate.y.toString());
  }

  @override
  Future<void> dispose() async {
    await super.dispose();
    _client.close();
  }
}

/// Represents a queued tile request
class _QueuedRequest {
  final String url;
  final Completer<VectorTile?> completer;

  _QueuedRequest(this.url, this.completer);
} 
 