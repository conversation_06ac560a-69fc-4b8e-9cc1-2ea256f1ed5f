import 'dart:math' as math;
import 'package:latlong2/latlong.dart';

import 'vector_tile_types.dart';

/// Provides mathematical utilities for tile calculations and coordinate conversions
class TileMath {
  static const int EARTH_RADIUS = 6378137;
  static const double MAX_LATITUDE = 85.0511287798;
  static const double ORIGIN_SHIFT = math.pi * EARTH_RADIUS;

  /// Convert longitude to tile X coordinate
  static int longitudeToTileX(double lon, int zoom) {
    return ((lon + 180) / 360 * (1 << zoom)).floor();
  }

  /// Convert latitude to tile Y coordinate
  static int latitudeToTileY(double lat, int zoom) {
    final latRad = lat * math.pi / 180;
    return ((1 - math.log(math.tan(latRad) + 1 / math.cos(latRad)) / math.pi) /
            2 *
            (1 << zoom))
        .floor();
  }

  /// Convert tile X coordinate to longitude
  static double tileXToLongitude(int x, int zoom) {
    return x / (1 << zoom) * 360 - 180;
  }

  /// Convert tile Y coordinate to latitude
  static double tileYToLatitude(int y, int zoom) {
    final n = math.pi - 2 * math.pi * y / (1 << zoom);
    return 180 / math.pi * math.atan(0.5 * (math.exp(n) - math.exp(-n)));
  }

  /// Get tile bounds for a coordinate
  static LatLngBounds getTileBounds(TileCoordinate coordinate) {
    final north = tileYToLatitude(coordinate.y, coordinate.z);
    final south = tileYToLatitude(coordinate.y + 1, coordinate.z);
    final west = tileXToLongitude(coordinate.x, coordinate.z);
    final east = tileXToLongitude(coordinate.x + 1, coordinate.z);

    return LatLngBounds(
      LatLng(south, west),
      LatLng(north, east),
    );
  }

  /// Get tile coordinates that cover a bounding box
  static List<TileCoordinate> getTileCoordinates(
    LatLngBounds bounds,
    int zoom,
  ) {
    final minX = longitudeToTileX(bounds.southWest.longitude, zoom);
    final maxX = longitudeToTileX(bounds.northEast.longitude, zoom);
    final minY = latitudeToTileY(bounds.northEast.latitude, zoom);
    final maxY = latitudeToTileY(bounds.southWest.latitude, zoom);

    final tiles = <TileCoordinate>[];
    for (var x = minX; x <= maxX; x++) {
      for (var y = minY; y <= maxY; y++) {
        tiles.add(TileCoordinate(x: x, y: y, z: zoom));
      }
    }

    return tiles;
  }

  /// Calculate zoom level for a given scale
  static int getZoomForScale(double scale, double latitude) {
    final cosLatitude = math.cos(latitude * math.pi / 180);
    return (math.log(scale * cosLatitude / (256 / (2 * math.pi * EARTH_RADIUS))) /
            math.ln2)
        .round();
  }

  /// Calculate scale for a given zoom level
  static double getScaleForZoom(int zoom, double latitude) {
    final cosLatitude = math.cos(latitude * math.pi / 180);
    return (256 / (2 * math.pi * EARTH_RADIUS)) *
        math.pow(2, zoom) /
        cosLatitude;
  }

  /// Convert meters to pixels at a given zoom level and latitude
  static double metersToPixels(double meters, double latitude, int zoom) {
    final cosLatitude = math.cos(latitude * math.pi / 180);
    final pixelsPerMeter = 256 * math.pow(2, zoom) / (2 * ORIGIN_SHIFT * cosLatitude);
    return meters * pixelsPerMeter;
  }

  /// Convert pixels to meters at a given zoom level and latitude
  static double pixelsToMeters(double pixels, double latitude, int zoom) {
    final cosLatitude = math.cos(latitude * math.pi / 180);
    final metersPerPixel = (2 * ORIGIN_SHIFT * cosLatitude) / (256 * math.pow(2, zoom));
    return pixels * metersPerPixel;
  }

  /// Get resolution (meters/pixel) for a zoom level at a latitude
  static double getResolution(int zoom, double latitude) {
    final cosLatitude = math.cos(latitude * math.pi / 180);
    return (2 * math.pi * EARTH_RADIUS * cosLatitude) / (256 * math.pow(2, zoom));
  }

  /// Calculate the optimal zoom level for a given bounding box and screen size
  static int getOptimalZoom(
    LatLngBounds bounds,
    Size screenSize, {
    double minZoom = 0,
    double maxZoom = 22,
  }) {
    final latRatio = (bounds.northEast.latitude - bounds.southWest.latitude)
            .abs() /
        screenSize.height;
    final lonRatio = (bounds.northEast.longitude - bounds.southWest.longitude)
            .abs() /
        screenSize.width;
    final ratio = math.max(latRatio, lonRatio);

    var zoom = (math.log(360 / ratio) / math.ln2).floor();
    return zoom.clamp(minZoom.floor(), maxZoom.floor());
  }

  /// Check if a point is within a tile's bounds
  static bool isPointInTile(LatLng point, TileCoordinate tile) {
    final bounds = getTileBounds(tile);
    return bounds.contains(point);
  }

  /// Get the fraction of a tile's width/height at a point
  static Point<double> getTilePosition(LatLng point, TileCoordinate tile) {
    final bounds = getTileBounds(tile);
    
    final x = (point.longitude - bounds.southWest.longitude) /
        (bounds.northEast.longitude - bounds.southWest.longitude);
    final y = (point.latitude - bounds.southWest.latitude) /
        (bounds.northEast.latitude - bounds.southWest.latitude);

    return Point(x, y);
  }
}

/// Represents a point in pixels
class Point<T extends num> {
  final T x;
  final T y;

  const Point(this.x, this.y);

  @override
  String toString() => 'Point($x, $y)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Point &&
          runtimeType == other.runtimeType &&
          x == other.x &&
          y == other.y;

  @override
  int get hashCode => Object.hash(x, y);
} 
 