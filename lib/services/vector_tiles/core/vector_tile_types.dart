/// Core type definitions for the vector tile system.
/// These types form the foundation of how vector tile data is represented
/// throughout the application.

import 'package:latlong2/latlong.dart';
import 'package:flutter/foundation.dart';

/// Represents a single vector tile coordinate
@immutable
class TileCoordinate {
  final int x;
  final int y;
  final int z;

  const TileCoordinate({
    required this.x,
    required this.y,
    required this.z,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TileCoordinate &&
          runtimeType == other.runtimeType &&
          x == other.x &&
          y == other.y &&
          z == other.z;

  @override
  int get hashCode => Object.hash(x, y, z);

  @override
  String toString() => 'TileCoordinate(x: $x, y: $y, z: $z)';
}

/// Represents a geographic feature in a vector tile
@immutable
class VectorTileFeature {
  final String id;
  final String layerName;
  final Map<String, dynamic> properties;
  final List<List<LatLng>> geometry;
  final VectorTileFeatureType type;

  const VectorTileFeature({
    required this.id,
    required this.layerName,
    required this.properties,
    required this.geometry,
    required this.type,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VectorTileFeature &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          layerName == other.layerName;

  @override
  int get hashCode => Object.hash(id, layerName);
}

/// The type of geometry a feature represents
enum VectorTileFeatureType {
  point,
  lineString,
  polygon,
  multiPoint,
  multiLineString,
  multiPolygon,
}

/// Represents a complete vector tile layer
@immutable
class VectorTileLayer {
  final String name;
  final int extent;
  final List<VectorTileFeature> features;
  final Map<String, dynamic> metadata;

  const VectorTileLayer({
    required this.name,
    required this.extent,
    required this.features,
    this.metadata = const {},
  });
}

/// Represents a complete vector tile
@immutable
class VectorTile {
  final TileCoordinate coordinate;
  final List<VectorTileLayer> layers;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const VectorTile({
    required this.coordinate,
    required this.layers,
    required this.timestamp,
    this.metadata = const {},
  });

  /// Check if the tile is stale based on a maximum age
  bool isStale(Duration maxAge) {
    return DateTime.now().difference(timestamp) > maxAge;
  }
}

/// Represents the style information for rendering vector tiles
@immutable
class VectorTileStyle {
  final String layerName;
  final Map<String, dynamic> paint;
  final Map<String, dynamic> layout;
  final List<String> filter;
  final int minZoom;
  final int maxZoom;

  const VectorTileStyle({
    required this.layerName,
    required this.paint,
    required this.layout,
    this.filter = const [],
    this.minZoom = 0,
    this.maxZoom = 22,
  });
}

/// Configuration for vector tile source
@immutable
class VectorTileSource {
  final String name;
  final String urlTemplate;
  final int minZoom;
  final int maxZoom;
  final Map<String, String> headers;
  final Duration maxAge;
  final bool attribution;

  const VectorTileSource({
    required this.name,
    required this.urlTemplate,
    this.minZoom = 0,
    this.maxZoom = 22,
    this.headers = const {},
    this.maxAge = const Duration(days: 7),
    this.attribution = true,
  });
} 
 