import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';

import 'vector_tile_types.dart';

/// Abstract interface for vector tile providers.
/// This defines the core functionality that any tile provider must implement.
abstract class TileProvider {
  /// Get a vector tile for the specified coordinates
  Future<VectorTile?> getTile(TileCoordinate coordinate);

  /// Get multiple vector tiles for the specified coordinates
  Future<List<VectorTile>> getTiles(List<TileCoordinate> coordinates);

  /// Get the tile coordinates that cover a given bounding box
  List<TileCoordinate> getTileCoordinates(
    LatLngBounds bounds,
    int zoom,
  );

  /// Check if a tile exists in the provider
  Future<bool> hasTile(TileCoordinate coordinate);

  /// Clear any cached tiles
  Future<void> clearCache();

  /// Prefetch tiles for a given area
  Future<void> prefetchTiles(
    LatLngBounds bounds,
    int minZoom,
    int maxZoom, {
    bool force = false,
  });

  /// Get the source configuration
  VectorTileSource get source;

  /// Get the current cache statistics
  Map<String, dynamic> getCacheStats();

  /// Dispose of any resources
  Future<void> dispose();
}

/// Base implementation of TileProvider with common functionality
abstract class BaseTileProvider implements TileProvider {
  final VectorTileSource _source;
  bool _disposed = false;

  BaseTileProvider(this._source);

  @override
  VectorTileSource get source => _source;

  @override
  List<TileCoordinate> getTileCoordinates(LatLngBounds bounds, int zoom) {
    final tiles = <TileCoordinate>[];
    
    // Convert bounds to tile coordinates
    final minX = _lon2tile(bounds.southWest.longitude, zoom);
    final maxX = _lon2tile(bounds.northEast.longitude, zoom);
    final minY = _lat2tile(bounds.northEast.latitude, zoom);
    final maxY = _lat2tile(bounds.southWest.latitude, zoom);

    // Generate all tile coordinates in the bounds
    for (var x = minX; x <= maxX; x++) {
      for (var y = minY; y <= maxY; y++) {
        tiles.add(TileCoordinate(x: x, y: y, z: zoom));
      }
    }

    return tiles;
  }

  /// Convert longitude to tile X coordinate
  @protected
  int _lon2tile(double lon, int z) {
    return ((lon + 180) / 360 * (1 << z)).floor();
  }

  /// Convert latitude to tile Y coordinate
  @protected
  int _lat2tile(double lat, int z) {
    final latRad = lat * pi / 180;
    return ((1 - log(tan(latRad) + 1 / cos(latRad)) / pi) / 2 * (1 << z))
        .floor();
  }

  @override
  Future<void> dispose() async {
    if (_disposed) return;
    _disposed = true;
    await clearCache();
  }

  /// Check if the provider has been disposed
  @protected
  void checkDisposed() {
    if (_disposed) {
      throw StateError('TileProvider has been disposed');
    }
  }
}

/// Exception thrown when there is an error loading a tile
class TileLoadException implements Exception {
  final String message;
  final TileCoordinate coordinate;
  final Object? originalError;

  TileLoadException(
    this.message,
    this.coordinate, [
    this.originalError,
  ]);

  @override
  String toString() =>
      'TileLoadException: $message for tile $coordinate${originalError != null ? ' ($originalError)' : ''}';
} 
 