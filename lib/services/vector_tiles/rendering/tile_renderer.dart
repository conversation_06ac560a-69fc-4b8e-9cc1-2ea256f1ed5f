import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

import '../core/vector_tile_types.dart';
import './style_manager.dart';

/// Handles the rendering of vector tiles with proper styling and optimization.
class TileRenderer {
  final StyleManager styleManager;
  final bool enableHardwareAcceleration;
  
  // Reusable objects to reduce allocations
  final Paint _paint = Paint();
  final Path _path = Path();
  final TextPainter _textPainter = TextPainter(textDirection: TextDirection.ltr);
  
  // Label collision detection grid
  final Map<String, Rect> _labelBounds = {};
  
  TileRenderer({
    required this.styleManager,
    this.enableHardwareAcceleration = true,
  });

  /// Render a vector tile to a canvas
  Future<void> renderTile(
    Canvas canvas,
    VectorTile tile,
    Size size,
    double devicePixelRatio,
  ) async {
    if (enableHardwareAcceleration) {
      canvas.saveLayer(Offset.zero & size, Paint());
    }

    try {
      // Clear any previous label bounds
      _labelBounds.clear();

      // Sort layers by z-index
      final sortedLayers = List<VectorTileLayer>.from(tile.layers)
        ..sort((a, b) => a.zIndex.compareTo(b.zIndex));

      // Render each layer
      for (final layer in sortedLayers) {
        final style = styleManager.getStyle(layer.name);
        
        // Skip if layer is not visible at this zoom
        if (tile.zoom < style.minZoom || tile.zoom > style.maxZoom) {
          continue;
        }

        await _renderLayer(canvas, layer, style, size, devicePixelRatio);
      }
    } finally {
      if (enableHardwareAcceleration) {
        canvas.restore();
      }
    }
  }

  /// Render a single vector tile layer
  Future<void> _renderLayer(
    Canvas canvas,
    VectorTileLayer layer,
    VectorTileStyle style,
    Size size,
    double devicePixelRatio,
  ) async {
    // Apply layer opacity
    if (layer.opacity < 1.0) {
      canvas.saveLayer(
        Offset.zero & size,
        Paint()..color = Colors.white.withOpacity(layer.opacity),
      );
    }

    try {
      // Sort features by z-index if specified
      final features = List<VectorTileFeature>.from(layer.features)
        ..sort((a, b) => (a.properties['z-index'] ?? 0).compareTo(b.properties['z-index'] ?? 0));

      // Render features
      for (final feature in features) {
        await _renderFeature(canvas, feature, style, size, devicePixelRatio);
      }

    } finally {
      if (layer.opacity < 1.0) {
        canvas.restore();
      }
    }
  }

  /// Render a single vector tile feature
  Future<void> _renderFeature(
    Canvas canvas,
    VectorTileFeature feature,
    VectorTileStyle style,
    Size size,
    double devicePixelRatio,
  ) async {
    // Get data-driven style for this feature
    final dataStyle = styleManager.getDataDrivenStyle(
      style.layerName,
      feature.properties,
    );

    // Set up paint style
    _configurePaint(_paint, dataStyle);

    switch (feature.type) {
      case 'Point':
        await _renderPoint(canvas, feature, dataStyle, size);
        break;
      case 'LineString':
        _renderLineString(canvas, feature, dataStyle, size);
        break;
      case 'Polygon':
        _renderPolygon(canvas, feature, dataStyle, size);
        break;
      default:
        debugPrint('Unknown feature type: ${feature.type}');
    }
  }

  /// Render a point feature (usually icons or labels)
  Future<void> _renderPoint(
    Canvas canvas,
    VectorTileFeature feature,
    VectorTileStyle style,
    Size size,
  ) async {
    final point = feature.geometry.first;
    final screenPoint = _projectToScreen(point, size);

    // Check if we should render a symbol
    if (style.layout['icon-image'] != null) {
      final iconImage = style.layout['icon-image'];
      // TODO: Implement icon rendering
    }

    // Check if we should render text
    if (style.layout['text-field'] != null) {
      final textField = style.layout['text-field'];
      final text = _resolveTextField(textField, feature.properties);
      
      if (text != null && text.isNotEmpty) {
        await _renderText(canvas, text, screenPoint, style);
      }
    }
  }

  /// Render a line string feature
  void _renderLineString(
    Canvas canvas,
    VectorTileFeature feature,
    VectorTileStyle style,
    Size size,
  ) {
    _path.reset();
    
    var first = true;
    for (final point in feature.geometry) {
      final screenPoint = _projectToScreen(point, size);
      if (first) {
        _path.moveTo(screenPoint.dx, screenPoint.dy);
        first = false;
      } else {
        _path.lineTo(screenPoint.dx, screenPoint.dy);
      }
    }

    canvas.drawPath(_path, _paint);
  }

  /// Render a polygon feature
  void _renderPolygon(
    Canvas canvas,
    VectorTileFeature feature,
    VectorTileStyle style,
    Size size,
  ) {
    _path.reset();
    
    var first = true;
    for (final ring in feature.geometry) {
      for (final point in ring) {
        final screenPoint = _projectToScreen(point, size);
        if (first) {
          _path.moveTo(screenPoint.dx, screenPoint.dy);
          first = false;
        } else {
          _path.lineTo(screenPoint.dx, screenPoint.dy);
        }
      }
      _path.close();
    }

    canvas.drawPath(_path, _paint);
  }

  /// Render text with collision detection
  Future<void> _renderText(
    Canvas canvas,
    String text,
    Offset position,
    VectorTileStyle style,
  ) async {
    final textStyle = TextStyle(
      color: _parseColor(style.layout['text-color'] ?? '#000000'),
      fontSize: style.layout['text-size'] ?? 12.0,
      fontWeight: FontWeight.w400,
    );

    _textPainter.text = TextSpan(text: text, style: textStyle);
    _textPainter.layout();

    // Calculate text bounds
    final textBounds = Rect.fromCenter(
      center: position,
      width: _textPainter.width,
      height: _textPainter.height,
    );

    // Check for collisions
    if (!_hasTextCollision(text, textBounds)) {
      _labelBounds[text] = textBounds;
      
      // Apply offset if specified
      final offset = style.layout['text-offset'];
      final dx = offset != null ? offset[0] as double : 0.0;
      final dy = offset != null ? offset[1] as double : 0.0;
      
      _textPainter.paint(
        canvas,
        position.translate(
          -_textPainter.width / 2 + dx,
          -_textPainter.height / 2 + dy,
        ),
      );
    }
  }

  /// Configure paint based on style
  void _configurePaint(Paint paint, VectorTileStyle style) {
    paint
      ..color = _parseColor(style.paint['fill-color'] ?? '#000000')
      ..style = style.paint['fill-color'] != null 
          ? PaintingStyle.fill 
          : PaintingStyle.stroke
      ..strokeWidth = (style.paint['stroke-width'] ?? 1.0).toDouble()
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;

    if (style.paint['stroke-opacity'] != null) {
      paint.color = paint.color.withOpacity(
        (style.paint['stroke-opacity'] as num).toDouble(),
      );
    }
  }

  /// Project a point to screen coordinates
  Offset _projectToScreen(Point point, Size size) {
    return Offset(
      point.x * size.width,
      point.y * size.height,
    );
  }

  /// Parse color from style
  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final value = int.parse(colorString.substring(1), radix: 16);
      return Color(value | 0xFF000000);
    }
    return Colors.black;
  }

  /// Check for text collision
  bool _hasTextCollision(String text, Rect bounds) {
    for (final existing in _labelBounds.values) {
      if (existing.overlaps(bounds)) {
        return true;
      }
    }
    return false;
  }

  /// Resolve text field from feature properties
  String? _resolveTextField(dynamic textField, Map<String, dynamic> properties) {
    if (textField is String) {
      return textField;
    } else if (textField is Map && textField['property'] != null) {
      return properties[textField['property']]?.toString();
    }
    return null;
  }
} 