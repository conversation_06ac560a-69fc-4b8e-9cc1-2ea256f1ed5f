import 'package:flutter/material.dart';
import '../core/vector_tile_types.dart';

/// Manages styles for vector tile rendering with support for complex rules and defaults
class StyleManager {
  final Map<String, VectorTileStyle> _layerStyles = {};
  final VectorTileStyle _defaultStyle;

  StyleManager({
    VectorTileStyle? defaultStyle,
  }) : _defaultStyle = defaultStyle ?? _createDefaultStyle();

  /// Get style for a specific layer
  VectorTileStyle getStyle(String layerName) {
    return _layerStyles[layerName] ?? _defaultStyle;
  }

  /// Add or update a layer style
  void setStyle(String layerName, VectorTileStyle style) {
    _layerStyles[layerName] = style;
  }

  /// Remove a layer style
  void removeStyle(String layerName) {
    _layerStyles.remove(layerName);
  }

  /// Clear all layer styles
  void clearStyles() {
    _layerStyles.clear();
  }

  /// Create style from JSON configuration
  static VectorTileStyle styleFromJson(Map<String, dynamic> json) {
    return VectorTileStyle(
      layerName: json['layer'] ?? '',
      paint: json['paint'] ?? {},
      layout: json['layout'] ?? {},
      filter: (json['filter'] as List?)?.cast<String>() ?? [],
      minZoom: json['minzoom'] ?? 0,
      maxZoom: json['maxzoom'] ?? 22,
    );
  }

  /// Create default style
  static VectorTileStyle _createDefaultStyle() {
    return VectorTileStyle(
      layerName: 'default',
      paint: {
        'fill-color': '#000000',
        'stroke-color': '#000000',
        'stroke-width': 1.0,
        'stroke-opacity': 1.0,
      },
      layout: {
        'text-size': 12.0,
        'text-color': '#000000',
      },
    );
  }

  /// Get default styles for common layer types
  static Map<String, VectorTileStyle> getDefaultStyles() {
    return {
      'building': VectorTileStyle(
        layerName: 'building',
        paint: {
          'fill-color': '#D1D1D1',
          'stroke-color': '#A9A9A9',
          'stroke-width': 1.0,
        },
        layout: {
          'text-field': {'property': 'name'},
          'text-size': 12.0,
        },
      ),
      'road': VectorTileStyle(
        layerName: 'road',
        paint: {
          'stroke-color': '#FFFFFF',
          'stroke-width': 2.0,
        },
        layout: {
          'text-field': {'property': 'name'},
          'text-size': 10.0,
        },
      ),
      'water': VectorTileStyle(
        layerName: 'water',
        paint: {
          'fill-color': '#B8D6F7',
        },
      ),
      'park': VectorTileStyle(
        layerName: 'park',
        paint: {
          'fill-color': '#C8FACD',
          'stroke-color': '#93E5AB',
          'stroke-width': 1.0,
        },
        layout: {
          'text-field': {'property': 'name'},
          'text-size': 11.0,
        },
      ),
      'poi': VectorTileStyle(
        layerName: 'poi',
        paint: {
          'fill-color': '#FF4081',
          'stroke-color': '#FFFFFF',
          'stroke-width': 1.0,
        },
        layout: {
          'text-field': {'property': 'name'},
          'text-size': 11.0,
          'text-offset': [0, 2],
        },
      ),
    };
  }

  /// Apply data-driven styles based on feature properties
  VectorTileStyle getDataDrivenStyle(
    String layerName,
    Map<String, dynamic> properties,
  ) {
    final baseStyle = getStyle(layerName);
    final paint = Map<String, dynamic>.from(baseStyle.paint);
    final layout = Map<String, dynamic>.from(baseStyle.layout);

    // Apply property-based styles
    for (final entry in properties.entries) {
      switch (entry.key) {
        case 'height':
          paint['fill-color'] = _heightBasedColor(entry.value);
        case 'type':
          paint['stroke-width'] = _typeBasedWidth(entry.value);
        case 'importance':
          layout['text-size'] = _importanceBasedSize(entry.value);
      }
    }

    return VectorTileStyle(
      layerName: baseStyle.layerName,
      paint: paint,
      layout: layout,
      filter: baseStyle.filter,
      minZoom: baseStyle.minZoom,
      maxZoom: baseStyle.maxZoom,
    );
  }

  /// Calculate color based on building height
  String _heightBasedColor(dynamic height) {
    final h = (height as num).toDouble();
    if (h <= 10) return '#D1D1D1';
    if (h <= 25) return '#BDBDBD';
    if (h <= 50) return '#9E9E9E';
    return '#757575';
  }

  /// Calculate stroke width based on road type
  double _typeBasedWidth(String type) {
    switch (type) {
      case 'motorway':
        return 4.0;
      case 'trunk':
        return 3.0;
      case 'primary':
        return 2.5;
      case 'secondary':
        return 2.0;
      default:
        return 1.0;
    }
  }

  /// Calculate text size based on importance
  double _importanceBasedSize(int importance) {
    return 10.0 + importance * 2;
  }
} 
 