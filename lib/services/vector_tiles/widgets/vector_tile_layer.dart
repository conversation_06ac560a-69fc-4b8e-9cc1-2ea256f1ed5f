import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import '../../../config/feature_flags.dart';
import '../core/vector_tile_types.dart';
import '../core/tile_math.dart';
import '../rendering/style_manager.dart';
import '../rendering/tile_renderer.dart';
import '../providers/network_tile_provider.dart';
import '../cache/tile_cache_manager.dart';

/// A layer that renders vector tiles while maintaining compatibility with existing OSM layers
class VectorTileLayer extends StatefulWidget {
  final String urlTemplate;
  final String? styleUrl;
  final List<String> enabledLayers;
  final bool? visible;
  final double opacity;
  final StyleManager? styleManager;
  final bool enableHardwareAcceleration;

  const VectorTileLayer({
    Key? key,
    required this.urlTemplate,
    this.styleUrl,
    this.enabledLayers = const ['building', 'road', 'water', 'landuse'],
    this.visible = true,
    this.opacity = 1.0,
    this.styleManager,
    this.enableHardwareAcceleration = true,
  }) : super(key: key);

  @override
  State<VectorTileLayer> createState() => _VectorTileLayerState();
}

class _VectorTileLayerState extends State<VectorTileLayer> {
  late final StyleManager _styleManager;
  late final TileRenderer _renderer;
  late final NetworkTileProvider _tileProvider;
  late final TileCacheManager _cacheManager;
  
  bool _isInitialized = false;
  Map<String, VectorTile> _loadedTiles = {};
  
  @override
  void initState() {
    super.initState();
    _styleManager = widget.styleManager ?? StyleManager();
    _renderer = TileRenderer(
      styleManager: _styleManager,
      enableHardwareAcceleration: widget.enableHardwareAcceleration,
    );
    _cacheManager = TileCacheManager();
    _tileProvider = NetworkTileProvider(
      source: VectorTileSource(
        urlTemplate: widget.urlTemplate,
        name: 'main',
      ),
      cacheManager: _cacheManager,
    );
    _initialize();
  }

  Future<void> _initialize() async {
    if (!FeatureFlags.enableVectorTiles) return;
    
    try {
      // Initialize styles if URL provided
      if (widget.styleUrl != null) {
        // TODO: Load and parse style JSON
      }
      
      setState(() => _isInitialized = true);
    } catch (e) {
      debugPrint('Failed to initialize vector tile layer: $e');
      // Fallback to OSM layer will happen automatically
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!FeatureFlags.enableVectorTiles || !_isInitialized || widget.visible == false) {
      return const SizedBox.shrink();
    }

    return StreamBuilder<void>(
      stream: MapCamera.of(context).moveStream,
      builder: (context, snapshot) {
        final bounds = MapCamera.of(context).visibleBounds;
        final zoom = MapCamera.of(context).zoom.round();
        
        // Calculate optimal tiles to load
        final tiles = TileMath.getTileCoordinates(bounds, zoom);
        
        return Stack(
          children: [
            for (final coordinate in tiles)
              _buildTile(coordinate),
          ],
        );
      },
    );
  }

  Widget _buildTile(TileCoordinate coordinate) {
    final key = '${coordinate.z}_${coordinate.x}_${coordinate.y}';
    
    return FutureBuilder<VectorTile?>(
      future: _tileProvider.getTile(coordinate),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          debugPrint('Error loading tile: ${snapshot.error}');
          return const SizedBox.shrink();
        }

        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final tile = snapshot.data!;
        _loadedTiles[key] = tile;

        return CustomPaint(
          painter: _VectorTilePainter(
            tile: tile,
            renderer: _renderer,
            opacity: widget.opacity,
            enabledLayers: widget.enabledLayers,
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _tileProvider.dispose();
    super.dispose();
  }
}

class _VectorTilePainter extends CustomPainter {
  final VectorTile tile;
  final TileRenderer renderer;
  final double opacity;
  final List<String> enabledLayers;

  _VectorTilePainter({
    required this.tile,
    required this.renderer,
    required this.opacity,
    required this.enabledLayers,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    if (opacity < 1.0) {
      canvas.saveLayer(
        Offset.zero & size,
        Paint()..color = Colors.white.withOpacity(opacity),
      );
    }

    try {
      await renderer.renderTile(
        canvas,
        tile,
        size,
        WidgetsBinding.instance.window.devicePixelRatio,
      );
    } finally {
      if (opacity < 1.0) {
        canvas.restore();
      }
    }
  }

  @override
  bool shouldRepaint(_VectorTilePainter oldDelegate) {
    return tile != oldDelegate.tile ||
           opacity != oldDelegate.opacity ||
           !listEquals(enabledLayers, oldDelegate.enabledLayers);
  }
}