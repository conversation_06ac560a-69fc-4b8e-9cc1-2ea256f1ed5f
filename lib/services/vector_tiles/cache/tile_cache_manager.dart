import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';

import '../core/vector_tile_types.dart';

/// Manages caching of vector tiles both in memory and on disk.
/// Implements a two-level cache system with configurable size limits
/// and eviction policies.
class TileCacheManager {
  // Singleton instance
  static final TileCacheManager _instance = TileCacheManager._internal();
  factory TileCacheManager() => _instance;

  // Memory cache
  final Map<String, _MemoryCacheEntry> _memoryCache = {};
  
  // Cache configuration
  final int _maxMemoryCacheSize;
  final int _maxDiskCacheSize;
  final Duration _memoryCacheDuration;
  final Duration _diskCacheDuration;

  // Disk cache directory
  late final Directory _cacheDir;
  
  // Cache statistics
  int _memoryHits = 0;
  int _diskHits = 0;
  int _misses = 0;

  // Initialization flag and completer
  bool _initialized = false;
  final _initCompleter = Completer<void>();

  TileCacheManager._internal({
    int maxMemoryCacheSize = 100, // Maximum number of tiles in memory
    int maxDiskCacheSize = 1000, // Maximum number of tiles on disk
    Duration memoryCacheDuration = const Duration(minutes: 30),
    Duration diskCacheDuration = const Duration(days: 7),
  })  : _maxMemoryCacheSize = maxMemoryCacheSize,
        _maxDiskCacheSize = maxDiskCacheSize,
        _memoryCacheDuration = memoryCacheDuration,
        _diskCacheDuration = diskCacheDuration {
    _initialize();
  }

  /// Initialize the cache manager
  Future<void> _initialize() async {
    if (_initialized) return;

    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDir = Directory('${appDir.path}/vector_tile_cache');
      
      if (!await _cacheDir.exists()) {
        await _cacheDir.create(recursive: true);
      }

      // Clean up any stale cache files
      await _cleanStaleCache();
      
      _initialized = true;
      _initCompleter.complete();
    } catch (e) {
      _initCompleter.completeError(e);
      rethrow;
    }
  }

  /// Wait for initialization to complete
  Future<void> get initialized => _initCompleter.future;

  /// Get a tile from cache
  Future<VectorTile?> getTile(TileCoordinate coordinate, String source) async {
    await initialized;
    final key = _getCacheKey(coordinate, source);

    // Check memory cache first
    final memoryEntry = _memoryCache[key];
    if (memoryEntry != null && !memoryEntry.isExpired(_memoryCacheDuration)) {
      _memoryHits++;
      return memoryEntry.tile;
    }

    // Check disk cache
    try {
      final file = File('${_cacheDir.path}/$key');
      if (await file.exists()) {
        final data = await file.readAsString();
        final json = jsonDecode(data);
        final tile = _deserializeTile(json);
        
        if (!tile.isStale(_diskCacheDuration)) {
          // Add to memory cache
          _addToMemoryCache(key, tile);
          _diskHits++;
          return tile;
        }
      }
    } catch (e) {
      debugPrint('Error reading tile from disk cache: $e');
    }

    _misses++;
    return null;
  }

  /// Store a tile in cache
  Future<void> storeTile(VectorTile tile, String source) async {
    await initialized;
    final key = _getCacheKey(tile.coordinate, source);

    // Add to memory cache
    _addToMemoryCache(key, tile);

    // Add to disk cache
    try {
      final file = File('${_cacheDir.path}/$key');
      final json = _serializeTile(tile);
      await file.writeAsString(jsonEncode(json));

      // Clean up disk cache if needed
      await _cleanDiskCacheIfNeeded();
    } catch (e) {
      debugPrint('Error writing tile to disk cache: $e');
    }
  }

  /// Add a tile to memory cache
  void _addToMemoryCache(String key, VectorTile tile) {
    _memoryCache[key] = _MemoryCacheEntry(tile);

    // Clean up memory cache if needed
    if (_memoryCache.length > _maxMemoryCacheSize) {
      // Remove oldest entries
      final entriesToRemove = _memoryCache.entries
          .toList()
          .where((e) => e.value.isExpired(_memoryCacheDuration))
          .take((_maxMemoryCacheSize * 0.2).ceil()) // Remove 20% of entries
          .map((e) => e.key)
          .toList();

      for (final key in entriesToRemove) {
        _memoryCache.remove(key);
      }
    }
  }

  /// Clean up stale cache entries
  Future<void> _cleanStaleCache() async {
    try {
      final files = await _cacheDir.list().toList();
      for (final file in files) {
        if (file is File) {
          try {
            final data = await file.readAsString();
            final json = jsonDecode(data);
            final tile = _deserializeTile(json);
            
            if (tile.isStale(_diskCacheDuration)) {
              await file.delete();
            }
          } catch (e) {
            // If we can't read the file, delete it
            await file.delete();
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning stale cache: $e');
    }
  }

  /// Clean up disk cache if it exceeds size limit
  Future<void> _cleanDiskCacheIfNeeded() async {
    try {
      final files = await _cacheDir.list().toList();
      if (files.length > _maxDiskCacheSize) {
        // Sort files by last modified time
        files.sort((a, b) {
          return a.statSync().modified.compareTo(b.statSync().modified);
        });

        // Delete oldest files
        final filesToDelete = files.take(
          (files.length - _maxDiskCacheSize + (_maxDiskCacheSize * 0.2).ceil())
        );

        for (final file in filesToDelete) {
          await file.delete();
        }
      }
    } catch (e) {
      debugPrint('Error cleaning disk cache: $e');
    }
  }

  /// Generate a cache key for a tile
  String _getCacheKey(TileCoordinate coordinate, String source) {
    final key = '${source}_${coordinate.z}_${coordinate.x}_${coordinate.y}';
    return md5.convert(utf8.encode(key)).toString();
  }

  /// Clear all caches
  Future<void> clearCache() async {
    await initialized;
    _memoryCache.clear();
    
    try {
      await _cacheDir.delete(recursive: true);
      await _cacheDir.create(recursive: true);
    } catch (e) {
      debugPrint('Error clearing disk cache: $e');
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'memory_cache_size': _memoryCache.length,
      'memory_hits': _memoryHits,
      'disk_hits': _diskHits,
      'misses': _misses,
      'hit_rate': (_memoryHits + _diskHits) / (_memoryHits + _diskHits + _misses),
    };
  }

  /// Serialize a tile to JSON
  Map<String, dynamic> _serializeTile(VectorTile tile) {
    return {
      'coordinate': {
        'x': tile.coordinate.x,
        'y': tile.coordinate.y,
        'z': tile.coordinate.z,
      },
      'layers': tile.layers.map((layer) => {
        'name': layer.name,
        'extent': layer.extent,
        'features': layer.features.map((feature) => {
          'id': feature.id,
          'layer_name': feature.layerName,
          'properties': feature.properties,
          'geometry': feature.geometry.map(
            (ring) => ring.map((point) => [point.latitude, point.longitude]).toList()
          ).toList(),
          'type': feature.type.index,
        }).toList(),
        'metadata': layer.metadata,
      }).toList(),
      'timestamp': tile.timestamp.toIso8601String(),
      'metadata': tile.metadata,
    };
  }

  /// Deserialize a tile from JSON
  VectorTile _deserializeTile(Map<String, dynamic> json) {
    return VectorTile(
      coordinate: TileCoordinate(
        x: json['coordinate']['x'],
        y: json['coordinate']['y'],
        z: json['coordinate']['z'],
      ),
      layers: (json['layers'] as List).map((layerJson) => VectorTileLayer(
        name: layerJson['name'],
        extent: layerJson['extent'],
        features: (layerJson['features'] as List).map((featureJson) => VectorTileFeature(
          id: featureJson['id'],
          layerName: featureJson['layer_name'],
          properties: featureJson['properties'],
          geometry: (featureJson['geometry'] as List).map((ring) =>
            (ring as List).map((point) =>
              LatLng(point[0], point[1])
            ).toList()
          ).toList(),
          type: VectorTileFeatureType.values[featureJson['type']],
        )).toList(),
        metadata: layerJson['metadata'],
      )).toList(),
      timestamp: DateTime.parse(json['timestamp']),
      metadata: json['metadata'],
    );
  }
}

/// Represents an entry in the memory cache
class _MemoryCacheEntry {
  final VectorTile tile;
  final DateTime timestamp;

  _MemoryCacheEntry(this.tile) : timestamp = DateTime.now();

  bool isExpired(Duration maxAge) {
    return DateTime.now().difference(timestamp) > maxAge;
  }
} 
 