import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import '../../config/constants.dart';
import 'notification_api_service.dart';

/// OneSignal notification service
/// Handles all OneSignal push notification functionality
class OneSignalService {
  final NotificationApiService? _apiService;

  bool _isInitialized = false;
  String? _playerId;
  String? _pushToken;
  String? _externalUserId;

  OneSignalService([this._apiService]);

  // Getters
  bool get isInitialized => _isInitialized;
  String? get playerId => _playerId;
  String? get pushToken => _pushToken;
  String? get externalUserId => _externalUserId;

  /// Initialize OneSignal with user ID
  Future<bool> initialize({required String userId}) async {
    try {
      final oneSignalAppId = AppConstants.oneSignalAppId;
      
      if (!AppConstants.isOneSignalConfigured) {
        if (kDebugMode) {
          print('❌ OneSignal App ID not configured in .env file');
        }
        return false;
      }

      if (kDebugMode) {
        print('🔔 Initializing OneSignal with App ID: $oneSignalAppId for user: $userId');
        OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
      } else {
        OneSignal.Debug.setLogLevel(OSLogLevel.warn);
      }

      // Initialize OneSignal
      OneSignal.initialize(oneSignalAppId);

      // Set external user ID
      OneSignal.login(userId);
      _externalUserId = userId;

      // Set up event listeners
      _setupEventListeners();

      // Request permission for push notifications
      await requestNotificationPermission();

      // Update user info and register with backend
      _updateUserInfo();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ OneSignal initialized successfully for user: $userId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing OneSignal: $e');
      }
      return false;
    }
  }

  /// Set up OneSignal event listeners
  void _setupEventListeners() {
    // Handle notification clicks
    OneSignal.Notifications.addClickListener((event) {
      if (kDebugMode) {
        print('🔔 OneSignal notification clicked: ${event.notification.notificationId}');
      }
      _handleNotificationClick(event);
    });

    // Handle foreground notifications
    OneSignal.Notifications.addForegroundWillDisplayListener((event) {
      if (kDebugMode) {
        print('🔔 OneSignal notification received in foreground: ${event.notification.notificationId}');
      }
      
      // Display the notification (default behavior)
      event.notification.display();
    });

    // Handle permission changes
    OneSignal.Notifications.addPermissionObserver((state) {
      if (kDebugMode) {
        print('🔔 OneSignal permission changed: $state');
      }
    });

    // Handle user state changes
    OneSignal.User.addObserver((state) {
      if (kDebugMode) {
        print('🔔 OneSignal user state changed');
      }
      _updateUserInfo();
    });

    // Handle push subscription changes
    OneSignal.User.pushSubscription.addObserver((state) {
      if (kDebugMode) {
        print('🔔 OneSignal push subscription changed');
      }
      _updateUserInfo();
    });
  }

  /// Handle notification click
  void _handleNotificationClick(OSNotificationClickEvent event) {
    final notification = event.notification;
    final additionalData = notification.additionalData;
    
    if (kDebugMode) {
      print('🔔 Notification clicked with data: $additionalData');
    }
    
    // Handle deep linking or navigation based on notification data
    // You can add your custom logic here
  }

  /// Update user info (player ID and push token)
  void _updateUserInfo() {
    try {
      // Note: OneSignal v5 API access to user ID and tokens may be different
      // We'll update these when the subscription state changes
      final pushSubscriptionId = OneSignal.User.pushSubscription.id;
      final pushToken = OneSignal.User.pushSubscription.token;

      _playerId = pushSubscriptionId; // Use subscription ID as player ID
      _pushToken = pushToken;

      if (kDebugMode) {
        print('🔔 OneSignal user info updated:');
        print('   Push Subscription ID: $pushSubscriptionId');
        print('   Push Token: $pushToken');
      }

      // Register with backend if we have a player ID
      if (_playerId != null) {
        _registerPlayer(_playerId!);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating OneSignal user info: $e');
      }
    }
  }

  /// Register player with backend
  Future<void> _registerPlayer(String playerId) async {
    if (_apiService == null) {
      if (kDebugMode) {
        print('⚠️ OneSignal player registration skipped - no API service');
      }
      return;
    }
    
    try {
      final platform = _getPlatform();
      await _apiService!.registerOneSignalPlayer(
        playerId: playerId,
        platform: platform,
      );
      if (kDebugMode) {
        print('✅ OneSignal player registered with backend: $playerId ($platform)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to register OneSignal player with backend: $e');
      }
    }
  }

  /// Get current platform
  String _getPlatform() {
    if (Platform.isIOS) {
      return 'ios';
    } else if (Platform.isAndroid) {
      return 'android';
    } else {
      return 'flutter';
    }
  }

  /// Request notification permission
  Future<bool> requestNotificationPermission() async {
    try {
      final permission = await OneSignal.Notifications.requestPermission(true);
      if (kDebugMode) {
        print('🔔 OneSignal notification permission: $permission');
      }
      return permission;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting OneSignal permission: $e');
      }
      return false;
    }
  }

  /// Set external user ID (for targeting specific users)
  Future<void> setExternalUserId(String externalUserId) async {
    try {
      OneSignal.login(externalUserId);
      if (kDebugMode) {
        print('🔔 OneSignal external user ID set: $externalUserId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error setting OneSignal external user ID: $e');
      }
    }
  }

  /// Remove external user ID (logout)
  Future<void> removeExternalUserId() async {
    try {
      // Unregister from backend first if API service is available
      if (_playerId != null && _apiService != null) {
        await _apiService!.unregisterOneSignalPlayer(_playerId!);
      }

      OneSignal.logout();
      _externalUserId = null;
      _playerId = null;
      _pushToken = null;
      
      if (kDebugMode) {
        print('🔔 OneSignal external user ID removed and unregistered from backend');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error removing OneSignal external user ID: $e');
      }
    }
  }

  /// Add tags to the user (for segmentation)
  Future<void> addTags(Map<String, String> tags) async {
    try {
      OneSignal.User.addTags(tags);
      if (kDebugMode) {
        print('🔔 OneSignal tags added: $tags');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error adding OneSignal tags: $e');
      }
    }
  }

  /// Remove tags from the user
  Future<void> removeTags(List<String> tagKeys) async {
    try {
      OneSignal.User.removeTags(tagKeys);
      if (kDebugMode) {
        print('🔔 OneSignal tags removed: $tagKeys');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error removing OneSignal tags: $e');
      }
    }
  }

  /// Check if notifications are enabled
  bool get notificationsEnabled {
    return OneSignal.Notifications.permission;
  }

  /// Get current subscription state
  bool get isSubscribed {
    return OneSignal.User.pushSubscription.optedIn ?? false;
  }

  /// Opt user into push notifications
  Future<void> optIn() async {
    try {
      OneSignal.User.pushSubscription.optIn();
      if (kDebugMode) {
        print('🔔 OneSignal user opted in to push notifications');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error opting in to OneSignal notifications: $e');
      }
    }
  }

  /// Opt user out of push notifications
  Future<void> optOut() async {
    try {
      OneSignal.User.pushSubscription.optOut();
      if (kDebugMode) {
        print('🔔 OneSignal user opted out of push notifications');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error opting out of OneSignal notifications: $e');
      }
    }
  }
} 