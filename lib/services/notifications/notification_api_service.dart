import '../../models/notification_model.dart';
import '../../models/user_notification_settings.dart';
import '../api/api_client.dart';

class NotificationApiService {
  final ApiClient _apiClient;

  NotificationApiService(this._apiClient);

  /// GET /api/notifications/notifications/
  /// Returns: NotificationListResponse
  Future<NotificationListResponse> getNotifications({
    NotificationCategory? category,
    bool? isRead,
    int limit = 50,
    int offset = 0,
  }) async {
    final queryParams = <String, String>{
      'limit': limit.toString(),
      'offset': offset.toString(),
    };

    if (category != null && category != NotificationCategory.all) {
      queryParams['category'] = category.name;
    }
    if (isRead != null) {
      queryParams['is_read'] = isRead.toString();
    }

    try {
      final response = await _apiClient.get(
        '/notifications/notifications/',
        queryParams: queryParams,
      );

      print('response: $response');

      return NotificationListResponse.fromJson(response);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(500, 'Failed to load notifications: $e');
    }
  }

  /// POST /api/notifications/notifications/{id}/mark_read/
  /// Returns: SuccessResponse
  Future<SuccessResponse> markAsRead(String notificationId) async {
    try {
      final response = await _apiClient.post(
        '/notifications/notifications/$notificationId/mark_read/',
      );

      return SuccessResponse.fromJson(response);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(500, 'Failed to mark notification as read: $e');
    }
  }

  /// POST /api/notifications/notifications/mark_all_read/
  /// Body: {"category": "map"} (optional)
  /// Returns: BulkActionResponse
  Future<BulkActionResponse> markAllAsRead({NotificationCategory? category}) async {
    final body = <String, dynamic>{};
    if (category != null && category != NotificationCategory.all) {
      body['category'] = category.name;
    }

    try {
      final response = await _apiClient.post(
        '/notifications/notifications/mark_all_read/',
        body: body,
      );

      return BulkActionResponse.fromJson(response);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(500, 'Failed to mark all notifications as read: $e');
    }
  }

  /// DELETE /api/notifications/notifications/bulk_delete/
  /// Body: {"notification_ids": ["1", "2", "3"]}
  /// Returns: BulkActionResponse
  Future<BulkActionResponse> deleteNotifications(List<String> notificationIds) async {
    final body = {'notification_ids': notificationIds};

    try {
      final response = await _apiClient.delete(
        '/notifications/notifications/bulk_delete/',
        body: body,
      );

      print('response: $response');

      return BulkActionResponse.fromJson(response);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(500, 'Failed to delete notifications: $e');
    }
  }

  /// GET /api/notifications/notifications/stats/
  /// Returns: NotificationStatsResponse
  Future<NotificationStatsResponse> getStats() async {
    try {
      final response = await _apiClient.get('/notifications/notifications/stats/');
      return NotificationStatsResponse.fromJson(response);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(500, 'Failed to load notification stats: $e');
    }
  }

  /// GET /api/notifications/settings/
  /// Returns: UserNotificationSettings
  Future<UserNotificationSettings> getSettings() async {
    try {
      final response = await _apiClient.get('/notifications/settings/');
      return UserNotificationSettings.fromJson(response);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(500, 'Failed to load notification settings: $e');
    }
  }

  /// PUT /api/notifications/settings/
  /// Body: UserNotificationSettings
  /// Returns: UserNotificationSettings
  Future<UserNotificationSettings> updateSettings(UserNotificationSettings settings) async {
    try {
      final response = await _apiClient.put(
        '/notifications/settings/',
        body: settings.toJson(),
      );

      return UserNotificationSettings.fromJson(response);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(500, 'Failed to update notification settings: $e');
    }
  }

  /// POST /api/notifications/onesignal/register_player/
  /// Body: {"player_id": "...", "platform": "ios|android"}
  /// Returns: SuccessResponse
  Future<SuccessResponse> registerOneSignalPlayer({
    required String playerId,
    required String platform,
  }) async {
    final body = {
      'player_id': playerId,
      'platform': platform,
    };

    try {
      final response = await _apiClient.post(
        '/notifications/onesignal/register_player/',
        body: body,
      );

      return SuccessResponse.fromJson(response);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(500, 'Failed to register OneSignal player: $e');
    }
  }

  /// POST /api/notifications/onesignal/unregister_player/
  /// Body: {"player_id": "..."}
  /// Returns: SuccessResponse
  Future<SuccessResponse> unregisterOneSignalPlayer(String playerId) async {
    final body = {'player_id': playerId};

    try {
      final response = await _apiClient.post(
        '/notifications/onesignal/unregister_player/',
        body: body,
      );

      return SuccessResponse.fromJson(response);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(500, 'Failed to unregister OneSignal player: $e');
    }
  }
} 