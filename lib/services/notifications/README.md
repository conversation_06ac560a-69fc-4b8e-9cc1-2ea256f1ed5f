# OneSignal Authentication Integration

## Overview

OneSignal is automatically integrated with the BOPMaps authentication system. When users log in or out, their OneSignal external user ID and tags are automatically managed.

## How It Works

### Login Flow

1. **User Authentication**: When a user logs in via any method (Spotify, Apple Music, SoundCloud, or Demo), the `AuthProvider` handles the authentication.

2. **OneSignal External User ID**: After successful authentication, the system automatically:
   - Sets the OneSignal external user ID to the user's backend ID (`user.id.toString()`)
   - Adds user tags for targeting and segmentation

3. **User Tags Set**:
   ```dart
   {
     'user_id': user.id.toString(),
     'username': user.username,
     'email': user.email,
     'is_verified': user.isVerified.toString(),
     'has_spotify': user.hasConnectedService('spotify').toString(),
     'has_apple_music': user.hasConnectedService('apple_music').toString(),
     'has_soundcloud': user.hasConnectedService('soundcloud').toString(),
   }
   ```

### Logout Flow

1. **OneSignal Cleanup**: When a user logs out, the system automatically:
   - Removes the OneSignal external user ID
   - Clears user-specific tags

### Profile Updates

When a user updates their profile or connects/disconnects music services, OneSignal tags are automatically updated to reflect the changes.

## Code Integration Points

### AuthProvider Integration

The `AuthProvider` class has been updated with OneSignal integration:

- `_setOneSignalUserId()`: Sets external user ID and tags
- `_removeOneSignalUserId()`: Removes external user ID on logout
- Automatic calls in: `login()`, `logout()`, `updateProfile()`, `connectMusicService()`

### Files Modified

1. **`lib/providers/auth_provider.dart`**:
   - Added OneSignal service dependency
   - Added automatic external user ID management
   - Added user tagging for segmentation

2. **`lib/services/notifications/onesignal_service.dart`**:
   - Core OneSignal functionality
   - External user ID management
   - Tag management

3. **`lib/providers/onesignal_provider.dart`**:
   - Provider-based OneSignal state management
   - Integration with app's provider architecture

## Usage for Push Notifications

### Targeting Specific Users

You can now target notifications to specific users using their backend user ID:

```javascript
// In OneSignal dashboard or API
{
  "external_user_ids": ["123", "456", "789"]
}
```

### Segmentation Examples

Create segments in OneSignal dashboard based on user tags:

1. **Spotify Users**: `has_spotify = "true"`
2. **Verified Users**: `is_verified = "true"`
3. **Multi-Platform Users**: `has_spotify = "true" AND has_apple_music = "true"`
4. **New Users**: `created_at > "2024-01-01"`

### Sending Targeted Notifications

```dart
// Example: Send to users with Spotify connected
// This would be done from your backend or OneSignal dashboard
{
  "filters": [
    {"field": "tag", "key": "has_spotify", "relation": "=", "value": "true"}
  ],
  "contents": {"en": "Check out this new Spotify feature!"}
}
```

## Testing

### Verify Integration

1. **Login to the app**
2. **Check OneSignal dashboard**: User should appear with correct external user ID
3. **Check user tags**: Should show username, email, and connected services
4. **Send test notification**: Should receive on the device
5. **Logout**: External user ID should be removed

### Debug Logs

In debug mode, you'll see logs like:
```
✅ OneSignal external user ID set: 123
✅ OneSignal external user ID removed
```

## Benefits

1. **User Targeting**: Send notifications to specific users by their backend ID
2. **Segmentation**: Create user segments based on music preferences, verification status, etc.
3. **Personalization**: Customize notifications based on user data
4. **Analytics**: Track notification performance by user segments
5. **Lifecycle Management**: Automatic cleanup when users log out

## Future Enhancements

1. **Location-based tags**: Add user location for geo-targeted notifications
2. **Activity tags**: Add tags based on user activity (pins created, music played, etc.)
3. **Preference tags**: Add user notification preferences
4. **Advanced segmentation**: Add more sophisticated user categorization 