import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/gamification_provider.dart';
import '../providers/auth_provider.dart';
import '../services/achievement_websocket_service.dart';
import '../widgets/gamification/components/challenge_progress_widget.dart';
import '../models/achievement.dart';

/// Service that demonstrates complete gamification integration
/// following the user's comprehensive integration guide
class GamificationIntegrationService {
  
  /// Initialize gamification system with WebSocket and notifications
  static Future<void> initializeGamification(BuildContext context) async {
    try {
      final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      // Set context for notifications
      gamificationProvider.setContext(context);
      
      // Initialize core gamification data
      await gamificationProvider.initialize();
      
      // Connect WebSocket if authenticated
      if (authProvider.isAuthenticated && authProvider.token != null) {
        await gamificationProvider.connectWebSocket(authProvider.token!);
      }
      
      debugPrint('🎮 Gamification system initialized successfully');
      
    } catch (e) {
      debugPrint('🎮 Gamification initialization failed: $e');
    }
  }
  
  /// Track pin creation with full integration
  static Future<void> trackPinCreation(
    BuildContext context, 
    String pinId, 
    Map<String, dynamic> pinData
  ) async {
    try {
      final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
      
      // Prepare comprehensive tracking data
      final trackingData = {
        'pin_id': pinId,
        'latitude': pinData['latitude'],
        'longitude': pinData['longitude'],
        'city': pinData['city'] ?? 'Unknown',
        'state': pinData['state'] ?? 'Unknown',
        'country': pinData['country'] ?? 'Unknown',
        'continent': pinData['continent'] ?? 'Unknown',
        // 🔧 CRITICAL FIX: Handle both field name formats
        'artist_name': pinData['artist_name'] ?? pinData['track_artist'] ?? '',
        'genre': pinData['genre'] ?? '',
        'album_name': pinData['album_name'] ?? pinData['album'] ?? '',
        'created_at': DateTime.now().toIso8601String(),
        'is_public': !(pinData['is_private'] ?? false),
      };
      
      // Track the pin creation
      final success = await gamificationProvider.handlePinCreated(pinId, additionalData: trackingData);
      
      if (success) {
        debugPrint('📍 Pin creation tracked successfully');
      } else {
        debugPrint('📍 Pin creation tracking failed');
      }
      
    } catch (e) {
      debugPrint('📍 Pin creation tracking error: $e');
    }
  }
  
  /// Track social actions (votes, comments, etc.)
  static Future<void> trackVote(
    BuildContext context,
    int pinId,
    int voteValue,
  ) async {
    try {
      final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
      
      final success = await gamificationProvider.handleVoteGiven(pinId, voteValue);
      
      if (success) {
        debugPrint('👍 Vote tracking successful');
      }
      
    } catch (e) {
      debugPrint('👍 Vote tracking error: $e');
    }
  }
  
  /// Track comment actions
  static Future<void> trackComment(
    BuildContext context,
    int pinId,
    String comment,
  ) async {
    try {
      final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
      
      final success = await gamificationProvider.handleCommentGiven(pinId, comment);
      
      if (success) {
        debugPrint('💬 Comment tracking successful');
      }
      
    } catch (e) {
      debugPrint('💬 Comment tracking error: $e');
    }
  }
  
  /// Track generic user actions
  static Future<void> trackUserAction(
    BuildContext context,
    String actionType,
    Map<String, dynamic> actionData,
  ) async {
    try {
      final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
      
      final success = await gamificationProvider.handleUserAction(actionType, actionData);
      
      if (success) {
        debugPrint('🎯 Action "$actionType" tracked successfully');
      }
      
    } catch (e) {
      debugPrint('🎯 Action tracking error: $e');
    }
  }
  
  /// Build live progress widget
  static Widget buildLiveProgressWidget({
    bool showOnlyNearComplete = true,
    int maxItems = 5,
  }) {
    return ChallengeProgressWidget(
      showOnlyNearComplete: showOnlyNearComplete,
      maxItems: maxItems,
    );
  }
  
  /// Get real-time challenge progress for a category
  static Future<List<Achievement>> getLiveChallengeProgress(
    BuildContext context,
    String categoryId,
  ) async {
    try {
      final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
      
      // Load fresh category data
      await gamificationProvider.loadCategoryAchievements(categoryId);
      
      // Return achievements sorted by progress (closest to completion first)
      List<Achievement> achievements;
      switch (categoryId) {
        case 'artist':
          achievements = gamificationProvider.artistAchievements;
          break;
        case 'genre':
          achievements = gamificationProvider.genreAchievements;
          break;
        case 'location':
          achievements = gamificationProvider.locationAchievements;
          break;
        case 'social':
          achievements = gamificationProvider.socialAchievements;
          break;
        default:
          achievements = [];
      }
      
      // Sort by progress percentage (descending)
      achievements.sort((a, b) => b.progressPercentage.compareTo(a.progressPercentage));
      
      // Filter to only show those with progress > 0
      return achievements.where((a) => a.progressPercentage > 0 && !a.isCompleted).toList();
      
    } catch (e) {
      debugPrint('📊 Live progress loading error: $e');
      return [];
    }
  }
  
  /// Refresh all gamification data
  static Future<void> refreshGamificationData(BuildContext context) async {
    try {
      final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
      
      await Future.wait([
        gamificationProvider.forceReload(),
        gamificationProvider.updateQuickProgress(),
      ]);
      
      debugPrint('🔄 Gamification data refreshed');
      
    } catch (e) {
      debugPrint('🔄 Gamification refresh error: $e');
    }
  }
  
  /// Handle app lifecycle changes for WebSocket management
  static Future<void> handleAppLifecycleChange(
    BuildContext context,
    AppLifecycleState state,
  ) async {
    final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    switch (state) {
      case AppLifecycleState.resumed:
        // App came to foreground - reconnect WebSocket if needed
        if (authProvider.isAuthenticated && 
            authProvider.token != null && 
            !gamificationProvider.isWebSocketConnected) {
          await gamificationProvider.connectWebSocket(authProvider.token!);
        }
        // Refresh data to get latest updates
        await refreshGamificationData(context);
        break;
        
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
        // App going to background - disconnect WebSocket to save resources
        await gamificationProvider.disconnectWebSocket();
        break;
        
      default:
        // No action needed for other states
        break;
    }
  }
  
  /// Get WebSocket connection status
  static bool getWebSocketStatus(BuildContext context) {
    final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
    return gamificationProvider.isWebSocketConnected;
  }
  
  /// Manual WebSocket reconnection
  static Future<void> reconnectWebSocket(BuildContext context) async {
    final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    if (authProvider.isAuthenticated && authProvider.token != null) {
      await gamificationProvider.connectWebSocket(authProvider.token!);
    }
  }
} 