import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// A service for securely storing sensitive data
class SecureStorage {
  final _storage = const FlutterSecureStorage();
  
  /// Write a value to secure storage
  Future<void> write({
    required String key,
    required String value,
  }) async {
    await _storage.write(key: key, value: value);
  }
  
  /// Read a value from secure storage
  Future<String?> read({
    required String key,
  }) async {
    return await _storage.read(key: key);
  }
  
  /// Delete a value from secure storage
  Future<void> delete({
    required String key,
  }) async {
    await _storage.delete(key: key);
  }
  
  /// Clear all values from secure storage
  Future<void> deleteAll() async {
    await _storage.deleteAll();
  }
} 