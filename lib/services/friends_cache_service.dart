import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class FriendsCacheService {
  static const String _friendsKey = 'cached_friends';
  static const String _requestsKey = 'cached_friend_requests';
  static const String _suggestionsKey = 'cached_friend_suggestions';
  static const String _lastUpdateKey = 'friends_last_update';
  static const Duration _cacheExpiry = Duration(minutes: 5);

  final SharedPreferences _prefs;

  FriendsCacheService(this._prefs);

  Future<void> cacheFriends(List<Map<String, dynamic>> friends) async {
    await _prefs.setString(_friendsKey, jsonEncode(friends));
    await _prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
  }

  Future<void> cacheFriendRequests({
    required List<Map<String, dynamic>> incoming,
    required List<Map<String, dynamic>> outgoing,
    required List<Map<String, dynamic>> suggestions,
  }) async {
    await _prefs.setString(_requestsKey, jsonEncode({
      'incoming': incoming,
      'outgoing': outgoing,
    }));
    await _prefs.setString(_suggestions<PERSON><PERSON>, j<PERSON><PERSON>nco<PERSON>(suggestions));
    await _prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
  }

  Future<List<Map<String, dynamic>>?> getCachedFriends() async {
    final lastUpdate = _prefs.getInt(_lastUpdateKey);
    if (lastUpdate == null) return null;

    final lastUpdateTime = DateTime.fromMillisecondsSinceEpoch(lastUpdate);
    if (DateTime.now().difference(lastUpdateTime) > _cacheExpiry) return null;

    final cachedData = _prefs.getString(_friendsKey);
    if (cachedData == null) return null;

    try {
      final List<dynamic> decoded = jsonDecode(cachedData);
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      return null;
    }
  }

  Future<Map<String, dynamic>?> getCachedFriendRequests() async {
    final lastUpdate = _prefs.getInt(_lastUpdateKey);
    if (lastUpdate == null) return null;

    final lastUpdateTime = DateTime.fromMillisecondsSinceEpoch(lastUpdate);
    if (DateTime.now().difference(lastUpdateTime) > _cacheExpiry) return null;

    final cachedRequests = _prefs.getString(_requestsKey);
    final cachedSuggestions = _prefs.getString(_suggestionsKey);
    if (cachedRequests == null || cachedSuggestions == null) return null;

    try {
      final Map<String, dynamic> requests = jsonDecode(cachedRequests);
      final List<dynamic> suggestions = jsonDecode(cachedSuggestions);
      
      return {
        'incoming': requests['incoming'] as List<dynamic>,
        'outgoing': requests['outgoing'] as List<dynamic>,
        'suggestions': suggestions,
      };
    } catch (e) {
      return null;
    }
  }

  Future<void> clearCache() async {
    await _prefs.remove(_friendsKey);
    await _prefs.remove(_requestsKey);
    await _prefs.remove(_suggestionsKey);
    await _prefs.remove(_lastUpdateKey);
  }
} 