import 'package:dio/dio.dart';
import '../../models/user_rank.dart';
import '../api_service.dart';
import '../../models/api_response.dart';
import '../auth_service.dart';

class RankService {
  final ApiService _apiService;
  final AuthService _authService;

  RankService(this._apiService, this._authService);

  /// NEW: Get all rank badge data in one optimized call
  Future<Map<String, dynamic>> fetchRankBadgeData() async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.get(
        '/gamification/achievements/rank_badge_data/',
        token: token,
        requiresAuth: true,
      );
      
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to fetch rank badge data');
      }
      
      return response.data as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to fetch rank badge data: ${e.toString()}');
    }
  }

  /// NEW: Get quick progress updates for real-time progress bar
  Future<Map<String, dynamic>> fetchQuickProgress() async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.get(
        '/gamification/achievements/quick_progress/',
        token: token,
        requiresAuth: true,
      );
      
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to fetch quick progress');
      }
      
      return response.data as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to fetch quick progress: ${e.toString()}');
    }
  }

  /// NEW: Send action response for XP updates after user actions
  Future<Map<String, dynamic>> sendActionResponse(String action, Map<String, dynamic> data) async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.post(
        '/gamification/achievements/action_response/',
        data: {
          'action': action,
          'data': data,
        },
        token: token,
        requiresAuth: true,
      );
      
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to send action response');
      }
      
      return response.data as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to send action response: ${e.toString()}');
    }
  }

  /// LEGACY: Keep old method for backwards compatibility (deprecated)
  @Deprecated('Use fetchRankBadgeData() instead for better performance')
  Future<Map<String, dynamic>> fetchUserRankData() async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.get(
        '/rankings/badge/',
        token: token,
        requiresAuth: true,
      );
      
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to fetch rank data');
      }
      
      return response.data as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to fetch rank data: ${e.toString()}');
    }
  }
} 