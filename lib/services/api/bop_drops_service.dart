import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';
import '../../models/user.dart';
import '../../models/music/bop_drop.dart';
import '../../config/constants.dart';
import '../../models/music/my_bop_drop.dart';
import '../../models/music/my_bop_drop_engagement.dart';

class BopDropsService {
  static final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  // Use the same base URL as other services
  static String get baseUrl => AppConstants.baseApiUrl;
  
  // Helper method to get auth token (same pattern as ApiClient)
  static Future<String?> _getToken() async {
    return await _secureStorage.read(key: AppConstants.tokenKey);
  }
  
  // Helper method to get current user ID from stored user data
  static Future<String?> getCurrentUserId() async {
    // 1) Try explicit user_data blob
    try {
      final userDataString = await _secureStorage.read(key: 'user_data');
      if (userDataString != null) {
        final userData = json.decode(userDataString);
        final id = userData['id'] ?? userData['user_id'];
        if (id != null) {
          if (kDebugMode) {
            print('✅ [BopDropsService] user_data found → id=$id');
          }
          return id.toString();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [BopDropsService] Failed to parse user_data blob: $e');
      }
    }

    // 2) Fallback: decode JWT access token
    try {
      final token = await _secureStorage.read(key: AppConstants.tokenKey);
      if (token != null && token.split('.').length == 3) {
        final payloadMap = json.decode(utf8.decode(base64Url.decode(base64Url.normalize(token.split('.')[1]))));
        final id = payloadMap['user_id'] ?? payloadMap['sub'];
        if (id != null) {
          if (kDebugMode) {
            print('✅ [BopDropsService] Extracted user_id=$id from JWT token');
          }
          return id.toString();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [BopDropsService] Failed to decode JWT: $e');
      }
    }

    if (kDebugMode) {
      print('⚠️ [BopDropsService] getCurrentUserId(): could not determine user id');
    }
    return null;
  }
  
  static Map<String, String> get _headers => {
    'Content-Type': 'application/json',
  };

  // Helper to get headers with auth token
  static Future<Map<String, String>> _getAuthHeaders() async {
    final headers = Map<String, String>.from(_headers);
    final token = await _getToken();
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  // Enhanced logging for requests
  static void _logRequest(String method, Uri uri, Map<String, String> headers, [String? body]) {
    if (kDebugMode) {
      print('');
      print('🎵 === BOP DROPS API REQUEST ===');
      print('📤 $method ${uri.toString()}');
      print('📋 Headers: ${_sanitizeHeaders(headers)}');
      if (body != null && body.isNotEmpty) {
        print('📦 Request Body: $body');
      }
      print('⏰ Timestamp: ${DateTime.now().toIso8601String()}');
      print('');
    }
  }

  // Enhanced logging for responses
  static void _logResponse(String method, Uri uri, http.Response response) {
    if (kDebugMode) {
      print('');
      print('🎵 === BOP DROPS API RESPONSE ===');
      print('🎯 $method ${uri.toString()}');
      print('📊 Status Code: ${response.statusCode}');
      print('📋 Response Headers: ${response.headers}');
      print('📦 Response Body: ${response.body}');
      print('⏰ Timestamp: ${DateTime.now().toIso8601String()}');
      
      // Additional status indicators
      if (response.statusCode >= 200 && response.statusCode < 300) {
        print('✅ BOP DROPS Request successful');
      } else if (response.statusCode >= 400 && response.statusCode < 500) {
        print('⚠️ BOP DROPS Client error - check request');
      } else if (response.statusCode >= 500) {
        print('❌ BOP DROPS Server error');
      }
      print('============================');
      print('');
    }
  }

  // Sanitize headers for logging (hide sensitive info)
  static Map<String, String> _sanitizeHeaders(Map<String, String> headers) {
    final sanitized = Map<String, String>.from(headers);
    if (sanitized.containsKey('Authorization')) {
      sanitized['Authorization'] = 'Bearer [HIDDEN]';
    }
    return sanitized;
  }

  // Helper to handle API errors (same pattern as ApiClient)
  static dynamic _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    final responseBody = utf8.decode(response.bodyBytes);
    
    if (statusCode >= 200 && statusCode < 300) {
      if (responseBody.isNotEmpty) {
        return json.decode(responseBody);
      }
      return {'success': true};
    } else {
      // Try to parse error message from response
      try {
        final errorData = json.decode(responseBody);
        final errorMessage = errorData['message'] ?? errorData['detail'] ?? 'Unknown error';
        throw Exception('API Error $statusCode: $errorMessage');
      } catch (e) {
        if (e.toString().contains('API Error')) {
          throw e;
        } else {
          throw Exception('API Error $statusCode: Failed to parse error response');
        }
      }
    }
  }

  // Core endpoints
  
  /// Get friends' bop drops feed
  static Future<List<BopDrop>> getFeed({
    String? mood,
    bool? currentlyPlaying,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      String endpoint = '/bop-drops/feed/?page=$page&page_size=$pageSize';
      
      if (mood != null) {
        endpoint += '&mood=$mood';
      }
      if (currentlyPlaying != null) {
        endpoint += '&currently_playing=$currentlyPlaying';
      }
      
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('$baseUrl$endpoint');
      
      // Log the request
      _logRequest('GET', uri, headers);
      
      final response = await http.get(uri, headers: headers);
      
      // Log the response
      _logResponse('GET', uri, response);
      
      final data = _handleResponse(response);
      return (data['results'] as List)
          .map((item) => BopDrop.fromJson(item))
          .toList();
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
  
  /// Get the current user's own bop drops
  static Future<List<BopDrop>> getMyBopDrops({int limit = 10}) async {
    try {
      final currentUserId = await getCurrentUserId();
      if (currentUserId == null) {
        if (kDebugMode) {
          print('⚠️ [BopDropsService] Cannot get own bop drops: current user ID is null.');
        }
        return [];
      }
      
      // Use the existing method to get drops for a specific user
      if (kDebugMode) {
        print('✅ [BopDropsService] Fetching own bop drops for user ID: $currentUserId');
      }
      return await getUserBopDrops(currentUserId, limit: limit);

    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      if (kDebugMode) {
          print('❌ [BopDropsService] Error fetching own bop drops: $e');
      }
      throw Exception('Network error while fetching own bop drops: $e');
    }
  }
  
  /// Get current user's bop drops with engagement stats
  static Future<MyBopDropData?> getMyBopDrop({String? timeFilter, String? moodFilter}) async {
    try {
      String endpoint = '/bop-drops/mybopdrop/';
      
      // Add query parameters if provided
      final queryParams = <String, String>{};
      if (timeFilter != null) queryParams['time'] = timeFilter;
      if (moodFilter != null) queryParams['mood'] = moodFilter;
      
      final headers = await _getAuthHeaders();
      
      // Build URI with query parameters if any exist
      final uri = queryParams.isEmpty 
          ? Uri.parse('$baseUrl$endpoint')
          : Uri.parse('$baseUrl$endpoint').replace(queryParameters: queryParams);
      
      // Log the request
      _logRequest('GET', uri, headers);
      
      final response = await http.get(uri, headers: headers);
      
      // Log the response
      _logResponse('GET', uri, response);
      
      final data = _handleResponse(response);
      
      if (data is Map<String, dynamic>) {
        return MyBopDropData.fromJson(data);
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [BopDropsService] getMyBopDrop error: $e');
        print('❌ [BopDropsService] Error type: ${e.runtimeType}');
      }
      print('Error fetching my bop drops: $e');
      return null;
    }
  }
  
  /// Get combined feed including both friends' bop drops and user's own bop drops
  static Future<List<BopDrop>> getCombinedFeed({
    String? mood,
    bool? currentlyPlaying,
    int page = 1,
    int pageSize = 20,
    bool includeOwnDrops = true,
  }) async {
    try {
      // Get friends' bop drops
      final friendsDrops = await getFeed(
        mood: mood,
        currentlyPlaying: currentlyPlaying,
        page: page,
        pageSize: pageSize,
      );
      
      List<BopDrop> allDrops = List.from(friendsDrops);
      
      // Add user's own bop drops if requested
      if (includeOwnDrops) {
        try {
          final myDrops = await getMyBopDrops(limit: 10);
          allDrops.addAll(myDrops);
          
          if (kDebugMode) {
            print('✅ [BopDropsService] Combined feed: ${friendsDrops.length} friends\' + ${myDrops.length} own = ${allDrops.length} total');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [BopDropsService] Failed to load own bop drops: $e');
          }
          // Continue with just friends' drops if own drops fail
        }
      }
      
      // Remove duplicates based on ID (in case there's overlap)
      final seenIds = <int>{};
      allDrops = allDrops.where((drop) {
        if (seenIds.contains(drop.id)) {
          return false;
        }
        seenIds.add(drop.id);
        return true;
      }).toList();
      
      // Sort by creation date (newest first)
      allDrops.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      if (kDebugMode) {
        print('✅ [BopDropsService] Final combined feed: ${allDrops.length} total drops (sorted by date)');
      }
      
      return allDrops;
    } catch (e) {
      throw Exception('Failed to load combined feed: $e');
    }
  }
  
  /// Create a new bop drop
  static Future<BopDrop> createBopDrop({
    required String trackId,
    required String trackTitle,
    required String trackArtist,
    String? trackAlbum,
    String? albumArtUrl,
    String? previewUrl,
    String musicService = 'spotify',
    String? caption,
    String? mood,
    bool isCurrentlyPlaying = false,
    bool friendsOnly = true,
  }) async {
    try {
      final bopDropData = {
        'track_id': trackId,
        'track_title': trackTitle,
        'track_artist': trackArtist,
        'track_album': trackAlbum,
        'album_art_url': albumArtUrl,
        'preview_url': previewUrl,
        'music_service': musicService,
        'caption': caption,
        'mood': mood,
        'is_currently_playing': isCurrentlyPlaying,
        'friends_only': friendsOnly,
      };
      
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('$baseUrl/bop-drops/');
      final body = json.encode(bopDropData);
      
      // Log the request
      _logRequest('POST', uri, headers, body);
      
      final response = await http.post(uri, headers: headers, body: body);
      
      // Log the response
      _logResponse('POST', uri, response);
      
      final data = _handleResponse(response);
      
      if (kDebugMode) {
        print('🔍 [BopDropsService] Analyzing createBopDrop response data:');
        print('🔍 [BopDropsService] Response type: ${data.runtimeType}');
        print('🔍 [BopDropsService] Response keys: ${data is Map ? data.keys.toList() : 'N/A'}');
        print('🔍 [BopDropsService] Has user field: ${data is Map ? data.containsKey('user') : false}');
        print('🔍 [BopDropsService] Has id field: ${data is Map ? data.containsKey('id') : false}');
        print('🔍 [BopDropsService] Has created_at field: ${data is Map ? data.containsKey('created_at') : false}');
        print('🔍 [BopDropsService] Raw data: $data');
      }
      
      // Check if the response is a complete bop drop object
      if (data is Map<String, dynamic>) {
        if (!data.containsKey('user') || !data.containsKey('id') || !data.containsKey('created_at')) {
          if (kDebugMode) {
            print('⚠️ [BopDropsService] Incomplete bop drop response - missing required fields');
            print('⚠️ [BopDropsService] This appears to be a track confirmation rather than a complete bop drop');
            print('⚠️ [BopDropsService] Available fields: ${data.keys.toList()}');
          }
          
          // The API is returning just track confirmation, not the full bop drop
          // We'll create a minimal bop drop object for now
          final enrichedData = Map<String, dynamic>.from(data);
          enrichedData['id'] = 0; // Temporary ID
          enrichedData['created_at'] = DateTime.now().toIso8601String();
          enrichedData['like_count'] = 0;
          enrichedData['view_count'] = 0;
          enrichedData['is_liked_by_user'] = false;
          enrichedData['time_since_created'] = 'just now';
          enrichedData['is_active'] = true;
          
          if (kDebugMode) {
            print('✅ [BopDropsService] Created enriched bop drop data with defaults');
          }
          
          return BopDrop.fromJson(enrichedData);
        }
      }
      
      return BopDrop.fromJson(data);
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      if (kDebugMode) {
        print('❌ [BopDropsService] createBopDrop error: $e');
        print('❌ [BopDropsService] Error type: ${e.runtimeType}');
      }
      throw Exception('Network error: $e');
    }
  }
  
  // Social features
  
  /// Like a bop drop
  static Future<void> likeBopDrop(int bopDropId) async {
    try {
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('$baseUrl/bop-drops/$bopDropId/like/');
      
      // Log the request
      _logRequest('POST', uri, headers);
      
      final response = await http.post(uri, headers: headers);
      
      // Log the response
      _logResponse('POST', uri, response);
      
      _handleResponse(response);
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
  
  /// Unlike a bop drop
  static Future<void> unlikeBopDrop(int bopDropId) async {
    try {
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('$baseUrl/bop-drops/$bopDropId/unlike/');
      
      // Log the request
      _logRequest('POST', uri, headers);
      
      final response = await http.post(uri, headers: headers);
      
      // Log the response
      _logResponse('POST', uri, response);
      
      _handleResponse(response);
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
  
  /// Record a view for a bop drop
  static Future<void> recordView(int bopDropId) async {
    try {
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('$baseUrl/bop-drops/$bopDropId/view/');
      
      // Log the request
      _logRequest('POST', uri, headers);
      
      final response = await http.post(uri, headers: headers);
      
      // Log the response
      _logResponse('POST', uri, response);
      
      _handleResponse(response);
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
  
  // Discovery features
  
  /// Get shuffled bop drops from friends
  static Future<List<BopDrop>> getShuffledBopDrops({int limit = 10}) async {
    try {
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('$baseUrl/bop-drops/shuffle/?limit=$limit');
      
      // Log the request
      _logRequest('GET', uri, headers);
      
      final response = await http.get(uri, headers: headers);
      
      // Log the response
      _logResponse('GET', uri, response);
      
      final data = _handleResponse(response);
      return (data as List)
          .map((item) => BopDrop.fromJson(item))
          .toList();
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
  
  /// Get bop drops from a specific user
  static Future<List<BopDrop>> getUserBopDrops(String userId, {int limit = 10}) async {
    try {
      final headers = await _getAuthHeaders();
      // Use the bulletproof /feed/ endpoint with user_id filtering
      final uri = Uri.parse('$baseUrl/bop-drops/feed/?user_id=$userId&limit=$limit&ordering=-created_at');
      
      // Log the request
      _logRequest('GET', uri, headers);
      
      final response = await http.get(uri, headers: headers);
      
      // Log the response
      _logResponse('GET', uri, response);
      
      // Handle specific error cases from bulletproof backend
      if (response.statusCode == 404) {
        final errorData = json.decode(response.body);
        throw Exception('User not found: ${errorData['error'] ?? 'User ID $userId does not exist'}');
      } else if (response.statusCode == 403) {
        final errorData = json.decode(response.body);
        throw Exception('Access denied: ${errorData['error'] ?? 'Cannot access bop drops from this user'}');
      } else if (response.statusCode == 400) {
        final errorData = json.decode(response.body);
        throw Exception('Invalid request: ${errorData['error'] ?? 'Invalid user ID format'}');
      }
      
      final data = _handleResponse(response);
      
      // Handle bulletproof backend response format with user_info validation
      if (data is Map && data.containsKey('results')) {
        final results = data['results'] as List;
        final userInfo = data['user_info'] as Map<String, dynamic>?;
        
        if (kDebugMode && userInfo != null) {
          print('🔍 [BopDropsService] User validation from API:');
          print('🔍 [BopDropsService] Requested user_id: $userId');
          print('🔍 [BopDropsService] Returned user_id: ${userInfo['id']}');
          print('🔍 [BopDropsService] Returned user_name: ${userInfo['display_name']}');
          
          // Validate that we got the correct user's data
          if (userInfo['id'].toString() != userId) {
            print('🚨 [BopDropsService] CRITICAL: API returned wrong user data!');
            print('🚨 [BopDropsService] Expected: $userId, Got: ${userInfo['id']}');
            throw Exception('API validation failed: Expected user $userId, got user ${userInfo['id']}');
          } else {
            print('✅ [BopDropsService] User validation passed');
          }
        }
        
        final bopDrops = results.map((item) => BopDrop.fromJson(item)).toList();
        
        // Additional validation: ensure all returned drops belong to the requested user
        for (final drop in bopDrops) {
          if (drop.user.id.toString() != userId) {
            if (kDebugMode) {
              print('🚨 [BopDropsService] CRITICAL: Drop ${drop.id} belongs to user ${drop.user.id}, not $userId');
            }
            throw Exception('Data integrity error: Received track from wrong user (${drop.user.displayName})');
          }
        }
        
        if (kDebugMode) {
          print('✅ [BopDropsService] All ${bopDrops.length} drops validated for user $userId');
        }
        
        return bopDrops;
      } else if (data is List) {
        // Fallback for older response format
        return data.map((item) => BopDrop.fromJson(item)).toList();
      } else {
        throw Exception('Unexpected response format from bulletproof API');
      }
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      // Re-throw our custom exceptions
      if (e.toString().contains('User not found') ||
          e.toString().contains('Access denied') ||
          e.toString().contains('Invalid request') ||
          e.toString().contains('API validation failed') ||
          e.toString().contains('Data integrity error')) {
        rethrow;
      }
      throw Exception('Network error: $e');
    }
  }
  
  /// Get trending bop drops
  static Future<List<BopDrop>> getTrendingBopDrops() async {
    try {
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('$baseUrl/bop-drops/trending/');
      
      // Log the request
      _logRequest('GET', uri, headers);
      
      final response = await http.get(uri, headers: headers);
      
      // Log the response
      _logResponse('GET', uri, response);
      
      final data = _handleResponse(response);
      return (data['results'] as List)
          .map((item) => BopDrop.fromJson(item))
          .toList();
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
  
  /// Get bop drops by mood
  static Future<List<BopDrop>> getBopDropsByMood(String mood) async {
    try {
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('$baseUrl/bop-drops/by_mood/?mood=$mood');
      
      // Log the request
      _logRequest('GET', uri, headers);
      
      final response = await http.get(uri, headers: headers);
      
      // Log the response
      _logResponse('GET', uri, response);
      
      final data = _handleResponse(response);
      return (data['results'] as List)
          .map((item) => BopDrop.fromJson(item))
          .toList();
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
  
  // Analytics
  
  /// Get personal bop drop statistics
  static Future<BopDropStats> getPersonalStats() async {
    try {
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('$baseUrl/bop-drops/mystats/');
      
      // Log the request
      _logRequest('GET', uri, headers);
      
      final response = await http.get(uri, headers: headers);
      
      // Log the response
      _logResponse('GET', uri, response);
      
      final data = _handleResponse(response);
      return BopDropStats.fromJson(data);
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Helper methods for friends header integration
  
  /// Get aggregated friend recommendations for the header
  static Future<Map<String, FriendRecommendationData>> getFriendRecommendations() async {
    try {
      // Get the current user ID first
      final currentUserId = await getCurrentUserId();
      
      // Get both friends' bop drops and user's own bop drops
      final bopDrops = await getCombinedFeed(includeOwnDrops: true);
      final friendsMap = <String, FriendRecommendationData>{};
      
      if (kDebugMode) {
        print('🎵 [BopDropsService] === FRIEND RECOMMENDATIONS DEBUG ===');
        print('🎵 [BopDropsService] Current user ID: $currentUserId');
        print('🎵 [BopDropsService] Total bop drops fetched: ${bopDrops.length}');
        for (int i = 0; i < bopDrops.length; i++) {
          final drop = bopDrops[i];
          final hoursSinceCreated = DateTime.now().difference(drop.createdAt).inHours;
          final isCurrentUser = drop.user.id.toString() == currentUserId;
          print('🎵 [BopDropsService] [$i] "${drop.trackTitle}" by ${drop.user.displayName} (${drop.user.id}) - ${hoursSinceCreated}h ago (isNew: ${drop.isNew}) (isCurrentUser: $isCurrentUser)');
        }
        print('🎵 [BopDropsService] ===============================');
      }
      
      // Get friends data to ensure we have accurate profile pictures
      Map<String, String> friendAvatars = {};
      Map<String, String> friendNames = {};
      try {
        final headers = await _getAuthHeaders();
        final uri = Uri.parse('$baseUrl/friends/');
        
        // Log the request
        _logRequest('GET', uri, headers);
        
        final friendsResponse = await http.get(uri, headers: headers);
        
        // Log the response
        _logResponse('GET', uri, friendsResponse);
        
        if (friendsResponse.statusCode == 200) {
          final friendsData = json.decode(friendsResponse.body);
          final friendsList = friendsData is List ? friendsData : (friendsData['results'] ?? []);
          
          for (final friendship in friendsList) {
            final friend = friendship['friend'] as Map<String, dynamic>? ?? {};
            final userId = friend['id']?.toString();
            if (userId != null) {
              friendAvatars[userId] = friend['profile_pic'] ?? '';
              final firstName = friend['first_name'] ?? '';
              final lastName = friend['last_name'] ?? '';
              final fullName = [firstName, lastName].where((s) => s.isNotEmpty).join(' ');
              friendNames[userId] = fullName.isNotEmpty ? fullName : friend['username'] ?? '';
            }
          }
        }
      } catch (e) {
        print('Warning: Could not fetch friends data for avatars: $e');
      }
      
      // Process bop drops and merge with friends data (now includes user's own drops)
      for (final drop in bopDrops) {
        final userId = drop.user.id.toString();
        final existing = friendsMap[userId];
        
        // Check if this is the current user's bop drop
        final isCurrentUser = userId == currentUserId;
        
        // Use profile picture from friends API if available, otherwise from bop drop
        final avatarUrl = friendAvatars[userId]?.isNotEmpty == true 
            ? friendAvatars[userId]! 
            : (drop.user.avatarUrl ?? '');
            
        // Use name from friends API if available, otherwise from bop drop
        final name = friendNames[userId]?.isNotEmpty == true
            ? friendNames[userId]!
            : drop.user.displayName;
        
        if (existing != null) {
          friendsMap[userId] = FriendRecommendationData(
            id: userId,
            name: name,
            avatar: avatarUrl,
            count: existing.count + 1,
            hasNew: existing.hasNew || drop.isNew,
            lastBopDrop: drop,
            isCurrentUser: isCurrentUser, // Set the flag properly
          );
        } else {
          friendsMap[userId] = FriendRecommendationData(
            id: userId,
            name: name,
            avatar: avatarUrl,
            count: 1,
            hasNew: drop.isNew,
            lastBopDrop: drop,
            isCurrentUser: isCurrentUser, // Set the flag properly
          );
        }
      }
      
      if (kDebugMode) {
        print('🎵 [BopDropsService] === FINAL FRIEND RECOMMENDATIONS ===');
        print('🎵 [BopDropsService] Total friends with bops: ${friendsMap.length}');
        friendsMap.forEach((userId, data) {
          print('🎵 [BopDropsService] ${data.name} (${userId}): ${data.count} bops, hasNew: ${data.hasNew}, isCurrentUser: ${data.isCurrentUser}');
        });
        print('🎵 [BopDropsService] ================================');
      }
      
      return friendsMap;
    } catch (e) {
      throw Exception('Failed to get friend recommendations: $e');
    }
  }

  /// Get my bop drops with detailed engagement data
  static Future<MyBopDropEngagement> getMyBopDropEngagement({String? bopDropId}) async {
    try {
      final headers = await _getAuthHeaders();
      final queryParams = bopDropId != null ? '?bop_drop_id=$bopDropId' : '';
      final uri = Uri.parse('$baseUrl/bop-drops/mybopdrop/engagement/$queryParams');
      
      // Log the request
      _logRequest('GET', uri, headers);
      
      final response = await http.get(uri, headers: headers);
      
      // Log the response
      _logResponse('GET', uri, response);
      
      final data = _handleResponse(response);
      return MyBopDropEngagement.fromJson(data);
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// Delete (soft delete) a bop drop
  static Future<void> deleteBopDrop(int bopDropId) async {
    try {
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('$baseUrl/bop-drops/$bopDropId/');
      
      // Log the request
      _logRequest('DELETE', uri, headers);
      
      final response = await http.delete(uri, headers: headers);
      
      // Log the response
      _logResponse('DELETE', uri, response);
      
      _handleResponse(response);
    } on SocketException catch (_) {
      throw Exception('No internet connection');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
}

/// Helper class for friend recommendation data
class FriendRecommendationData {
  final String id;
  final String name;
  final String avatar;
  final int count;
  final bool hasNew;
  final BopDrop? lastBopDrop;
  final bool isCurrentUser;

  const FriendRecommendationData({
    required this.id,
    required this.name,
    required this.avatar,
    required this.count,
    required this.hasNew,
    this.lastBopDrop,
    this.isCurrentUser = false,
  });

  factory FriendRecommendationData.fromJson(Map<String, dynamic> json) {
    return FriendRecommendationData(
      id: json['id'] as String,
      name: json['name'] as String,
      avatar: json['avatar'] as String? ?? '',
      count: json['count'] as int? ?? 0,
      hasNew: json['has_new'] as bool? ?? false,
      lastBopDrop: json['last_bop_drop'] != null 
          ? BopDrop.fromJson(json['last_bop_drop'] as Map<String, dynamic>)
          : null,
      isCurrentUser: json['is_current_user'] as bool? ?? false,
    );
  }
} 