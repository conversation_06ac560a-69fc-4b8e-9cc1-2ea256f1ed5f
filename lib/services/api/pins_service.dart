import 'api_client.dart';
import '../../config/constants.dart';
import '../../models/pin.dart';
import '../../models/user.dart';
import '../../models/music_track.dart';

class PinsService {
  final ApiClient _apiClient = ApiClient();
  
  // Get nearby pins
  Future<List<Pin>> getNearbyPins(
    double latitude,
    double longitude,
    double radius,
  ) async {
    try {
      print('🔍 Fetching nearby pins at lat: $latitude, lng: $longitude, radius: $radius');
      final response = await _apiClient.get(
        '${AppConstants.pinsEndpoint}/nearby/',
        queryParams: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius':  radius.toString(),
        },
        requiresAuth: true,
      );
      
      // Use the shared parsing logic
      return _parseNearbyPinsResponse(response, latitude, longitude, 'all');
    } catch (e) {
      print('Error getting nearby pins: $e');
      print('Stack trace: ${StackTrace.current}');
      return [];
    }
  }

  
  // Get pin details
  Future<Pin?> getPinDetails(String pinId) async {
    try {
      final response = await _apiClient.get(
        '${AppConstants.pinsEndpoint}/$pinId',
        requiresAuth: true,
      );
      
      return Pin.fromJson(response);
    } catch (e) {
      print('Error getting pin details: $e');
      return null;
    }
  }
  
  // Create a new pin
  Future<Pin?> createPin({
    required double latitude,
    required double longitude,
    required String title,
    String? description,
    String? caption,
    required String trackTitle,
    required String trackArtist,
    String? album,
    required String trackUrl,
    required String service,
    required int skin,
    double? auraRadius,
    bool isPrivate = false,
  }) async {
    try {
      final response = await _apiClient.post(
        AppConstants.pinsEndpoint,
        body: {
          'location': {
            'type': 'Point',
            'coordinates': [longitude, latitude]  // GeoJSON order
          },
          'title': title,
          'description': description,
          'caption': caption,
          'track_title': trackTitle,
          'track_artist': trackArtist,
          'album': album,
          'track_url': trackUrl,
          'service': service,
          'skin': skin,
          'aura_radius': auraRadius,
          'is_private': isPrivate,
        },
        requiresAuth: true,
      );
      
      return Pin.fromJson(response);
    } catch (e) {
      print('Error creating pin: $e');
      return null;
    }
  }
  
  // Update a pin
  Future<Pin?> updatePin({
    required String pinId,
    String? title,
    String? description,
    String? caption,
    int? skin,
    double? auraRadius,
    bool? isPrivate,
  }) async {
    try {
      // Create request body with non-null fields
      final Map<String, dynamic> body = {};
      if (title != null) body['title'] = title;
      if (description != null) body['description'] = description;
      if (caption != null) body['caption'] = caption;
      if (skin != null) body['skin'] = skin;
      if (auraRadius != null) body['aura_radius'] = auraRadius;
      if (isPrivate != null) body['is_private'] = isPrivate;
      
      final response = await _apiClient.patch(
        '${AppConstants.pinsEndpoint}/$pinId',
        body: body,
        requiresAuth: true,
      );
      
      return Pin.fromJson(response);
    } catch (e) {
      print('Error updating pin: $e');
      return null;
    }
  }
  
  // Delete a pin
  Future<bool> deletePin(String pinId) async {
    try {
      final response = await _apiClient.delete(
        '${AppConstants.pinsEndpoint}/$pinId',
        requiresAuth: true,
      );
      
      return response['success'] == true;
    } catch (e) {
      print('Error deleting pin: $e');
      return false;
    }
  }
  
  // Record pin interaction
  Future<bool> recordPinInteraction(String pinId, String interactionType) async {
    try {
      final response = await _apiClient.post(
        '${AppConstants.pinsEndpoint}/$pinId/$interactionType/',
        requiresAuth: true,
      );
      
      return response['success'] == true;
    } catch (e) {
      print('Error recording pin $interactionType: $e');
      return false;
    }
  }
  
  // View pin
  Future<bool> viewPin(String pinId) => recordPinInteraction(pinId, 'view');
  
  // Like pin
  Future<bool> likePin(String pinId) => recordPinInteraction(pinId, 'like');
  
  // Collect pin
  Future<bool> collectPin(String pinId) => recordPinInteraction(pinId, 'collect');
  
  // Share pin
  Future<bool> sharePin(String pinId) => recordPinInteraction(pinId, 'share');
  
  // Get pins by user
  Future<List<Pin>> getPinsByUser(String userId) async {
    try {
      final response = await _apiClient.get(
        '${AppConstants.usersEndpoint}/$userId/pins',
        requiresAuth: true,
      );
      
      if (response.containsKey('results')) {
        final results = response['results'] as List;
        return results.map((pinJson) => Pin.fromJson(pinJson)).toList();
      }
      
      return [];
    } catch (e) {
      print('Error getting pins by user: $e');
      return [];
    }
  }
  
  // Get collected pins with ordering options
  Future<List<Pin>> getCollectedPins({
    String ordering = 'collected_recent',
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'ordering': ordering,
        'page': page.toString(),
        'page_size': pageSize.toString(),
      };
      
      final response = await _apiClient.get(
        '${AppConstants.pinsEndpoint}/collected/',
        queryParams: queryParams,
        requiresAuth: true,
      );
      
      if (response.containsKey('results')) {
        final results = response['results'] as List;
        return results.map((pinJson) => Pin.fromJson(pinJson)).toList();
      }
      
      return [];
    } catch (e) {
      print('Error getting collected pins: $e');
      return [];
    }
  }
  
  // Get recently collected pins
  Future<List<Pin>> getRecentlyCollectedPins({int page = 1, int pageSize = 20}) async {
    return getCollectedPins(ordering: 'collected_recent', page: page, pageSize: pageSize);
  }
  
  // Get most liked collected pins
  Future<List<Pin>> getMostLikedCollectedPins({int page = 1, int pageSize = 20}) async {
    return getCollectedPins(ordering: 'most_liked', page: page, pageSize: pageSize);
  }
  
  // Get oldest collected pins
  Future<List<Pin>> getOldestCollectedPins({int page = 1, int pageSize = 20}) async {
    return getCollectedPins(ordering: 'collected_oldest', page: page, pageSize: pageSize);
  }
  
  // Get upvoted pins with ordering options
  Future<List<Pin>> getUpvotedPins({
    String ordering = 'recent',
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'ordering': ordering,
        'page': page.toString(),
        'page_size': pageSize.toString(),
      };
      
      final response = await _apiClient.get(
        '${AppConstants.pinsEndpoint}/upvoted/',
        queryParams: queryParams,
        requiresAuth: true,
      );
      
      if (response.containsKey('results')) {
        final results = response['results'] as List;
        return results.map((pinJson) => Pin.fromJson(pinJson)).toList();
      }
      
      return [];
    } catch (e) {
      print('Error getting upvoted pins: $e');
      return [];
    }
  }
  
  // Get recent upvoted pins
  Future<List<Pin>> getRecentUpvotedPins({int page = 1, int pageSize = 20}) async {
    return getUpvotedPins(ordering: 'recent', page: page, pageSize: pageSize);
  }
  
  // Get oldest upvoted pins
  Future<List<Pin>> getOldestUpvotedPins({int page = 1, int pageSize = 20}) async {
    return getUpvotedPins(ordering: 'oldest', page: page, pageSize: pageSize);
  }
  
  // Get trending pins
  Future<List<Pin>> getTrendingPins({int days = 7, int limit = 20}) async {
    try {
      final response = await _apiClient.get(
        '${AppConstants.pinsEndpoint}/trending',
        queryParams: {
          'days': days.toString(),
          'limit': limit.toString(),
        },
        requiresAuth: true,
      );
      
      if (response.containsKey('results')) {
        final results = response['results'] as List;
        return results.map((pinJson) => Pin.fromJson(pinJson)).toList();
      }
      
      return [];
    } catch (e) {
      print('Error getting trending pins: $e');
      return [];
    }
  }
  
  // Get map pins
  Future<List<Pin>> getMapPins({
    required double latitude,
    required double longitude,
    required double radius,
  }) async {
    try {
      final response = await _apiClient.get(
        '${AppConstants.pinsEndpoint}/list_map',
        queryParams: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
        },
        requiresAuth: true,
      );
      
      if (response.containsKey('results')) {
        final results = response['results'] as List;
        return results.map((pinJson) => Pin.fromJson(pinJson)).toList();
      }
      
      return [];
    } catch (e) {
      print('Error getting map pins: $e');
      return [];
    }
  }
  
  // Generate sample pin for testing
  Pin generateSamplePin({
    required String id,
    required double latitude,
    required double longitude,
    required String title,
  }) {
    // Create a sample user
    final user = User(
      id: 1,
      username: 'sampleuser',
      email: '<EMAIL>',
      isVerified: true,
      favoriteGenres: ['pop', 'rock'],
      connectedServices: {
        'spotify': true,
        'apple_music': false,
        'soundcloud': false,
      },
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    );
    
    // Create and return a sample pin
    return Pin(
      id: int.parse(id),
      owner: user,
      location: {
        'type': 'Point',
        'coordinates': [longitude, latitude]
      },
      title: title,
      description: 'This is a sample pin for testing purposes.',
      caption: 'Sample pin caption 🎵',
      trackTitle: 'Sample Track',
      trackArtist: 'Sample Artist',
      album: 'Sample Album',
      trackUrl: 'https://example.com/track/123',
      service: 'spotify',
      skin: 1,
      rarity: 'common',
      auraRadius: 50.0,
      isPrivate: false,
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      interactionCount: {
        'view': 0,
        'like': 0,
        'collect': 0,
        'share': 0
      },
    );
  }
  
  // Get user's own pins (both public and private)
  Future<List<Pin>> getMyPins({
    bool? isPrivate,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'page_size': pageSize.toString(),
      };
      
      if (isPrivate != null) {
        queryParams['is_private'] = isPrivate.toString();
      }
      
      final response = await _apiClient.get(
        '${AppConstants.pinsEndpoint}/my_pins/',
        queryParams: queryParams,
        requiresAuth: true,
      );
      
      // Handle paginated response
      if (response is Map<String, dynamic> && response.containsKey('results')) {
        final results = response['results'] as List;
        if (results.isNotEmpty) {
          print('📍 Found ${results.length} user pins from API');
          return results.map((pinJson) {
            try {
              // Convert API response to Pin format
              final pinData = Map<String, dynamic>.from(pinJson);
              
              // Parse location coordinates
              List<double> coords = [0, 0];
              
              // Handle different location formats
              if (pinData['location'] != null) {
                final locationData = pinData['location'];
                
                // Case 1: GeoJSON Point format
                if (locationData is Map && locationData['coordinates'] != null) {
                  final coordsList = locationData['coordinates'] as List;
                  coords = [coordsList[0].toDouble(), coordsList[1].toDouble()]; // [lng, lat]
                }
                // Case 2: WKT format like "POINT(lng lat)"
                else if (locationData is String && locationData.contains('POINT')) {
                  final coordsStr = locationData.split('POINT ')[1].trim();
                  final coordsParts = coordsStr
                      .replaceAll('(', '')
                      .replaceAll(')', '')
                      .split(' ')
                      .map((s) => double.parse(s))
                      .toList();
                  coords = [coordsParts[0], coordsParts[1]]; // [lng, lat]
                }
                // Case 3: Direct coordinate array
                else if (locationData is List && locationData.length >= 2) {
                  coords = [locationData[0].toDouble(), locationData[1].toDouble()]; // [lng, lat]
                }
                else {
                  print('❌ Unknown location format: $locationData');
                }
              }
              
              // Ensure all required fields are present
              final processedPin = {
                'id': pinData['id'],
                'location': {
                  'type': 'Point',
                  'coordinates': coords,
                },
                'title': pinData['title'] ?? 'Untitled',
                'track_title': pinData['track_title'] ?? 'Unknown Track',
                'track_artist': pinData['track_artist'] ?? 'Unknown Artist',
                'description': pinData['description'],
                'caption': pinData['caption'],
                'service': pinData['service'] ?? 'unknown',
                'owner': pinData['owner'] ?? {'username': 'unknown', 'profile_pic': null},
                'aura_radius': pinData['aura_radius'] ?? 50,
                'created_at': pinData['created_at'],
                'updated_at': pinData['updated_at'],
                'interaction_count': pinData['interaction_count'] ?? {
                  'like': pinData['like_count'] ?? 0,
                  'collect': pinData['collect_count'] ?? 0,
                  'view': pinData['view_count'] ?? 0,
                  'share': pinData['share_count'] ?? 0,
                },
                // Add the new engagement_counts structure from the backend
                'engagement_counts': pinData['engagement_counts'],
                'rarity': pinData['rarity'] ?? 'common',
                'skin': pinData['skin'] ?? 1,
                'skin_details': pinData['skin_details'],
                'track_url': pinData['track_url'] ?? '',
                'artwork_url': pinData['artwork_url'],
                'is_private': pinData['is_private'] ?? false,
                'album': pinData['album'],
                'duration_ms': pinData['duration_ms'],
                'mood': pinData['mood'],
                'is_currently_playing': pinData['is_currently_playing'] ?? false,
                'expiration_date': pinData['expiration_date'],
                'location_name': pinData['location_name'],
                'has_expired': pinData['has_expired'] ?? false,
                'distance': pinData['distance'],
                'upvote_count': pinData['upvote_count'] ?? 0,
                'downvote_count': pinData['downvote_count'] ?? 0,
              };
              
              print('🎨 Processing my pin: ${processedPin['title']} at ${coords}');
              
              return Pin.fromJson(processedPin);
            } catch (e) {
              print('❌ Error processing my pin: $e');
              rethrow;
            }
          }).toList();
        }
      }
      
      print('No user pins found');
      return [];
    } catch (e) {
      print('Error getting my pins: $e');
      return [];
    }
  }

  // Get a friend's public pins (same data structure as getMyPins)
  Future<List<Pin>> getFriendPins(
    String userId, {
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'page_size': pageSize.toString(),
      };
      
      final response = await _apiClient.get(
        '/users/$userId/public_pins/',
        queryParams: queryParams,
        requiresAuth: true,
      );
      
      // Handle paginated response (same structure as getMyPins)
      if (response is Map<String, dynamic> && response.containsKey('results')) {
        final results = response['results'] as List;
        if (results.isNotEmpty) {
          print('📍 Found ${results.length} friend pins from API');
          return results.map((pinJson) {
            try {
              // Convert API response to Pin format (same processing as getMyPins)
              final pinData = Map<String, dynamic>.from(pinJson);
              
              print('🔍 Raw friend pin data: $pinData');
              print('🌍 Location field: ${pinData['location']}');
              
              // Parse location coordinates (same logic as getMyPins)
              List<double> coords = [0, 0];
              
              if (pinData['location'] != null) {
                final locationData = pinData['location'];
                
                // Case 1: GeoJSON Point format
                if (locationData is Map && locationData['coordinates'] != null) {
                  final coordsList = locationData['coordinates'] as List;
                  coords = [coordsList[0].toDouble(), coordsList[1].toDouble()]; // [lng, lat]
                  print('📌 Parsed friend pin GeoJSON coordinates: lng: ${coords[0]}, lat: ${coords[1]}');
                }
                // Case 2: WKT format like "POINT(lng lat)"
                else if (locationData is String && locationData.contains('POINT')) {
                  final coordsStr = locationData.split('POINT ')[1].trim();
                  final coordsParts = coordsStr
                      .replaceAll('(', '')
                      .replaceAll(')', '')
                      .split(' ')
                      .map((s) => double.parse(s))
                      .toList();
                  coords = [coordsParts[0], coordsParts[1]]; // [lng, lat]
                  print('📌 Parsed friend pin WKT coordinates: lng: ${coords[0]}, lat: ${coords[1]}');
                }
                // Case 3: Direct coordinate array
                else if (locationData is List && locationData.length >= 2) {
                  coords = [locationData[0].toDouble(), locationData[1].toDouble()]; // [lng, lat]
                  print('📌 Parsed friend pin direct coordinates: lng: ${coords[0]}, lat: ${coords[1]}');
                }
                else {
                  print('❌ Unknown friend pin location format: $locationData');
                }
              }
              
              // Ensure all required fields are present (same structure as getMyPins)
              final processedPin = {
                'id': pinData['id'],
                'location': {
                  'type': 'Point',
                  'coordinates': coords,
                },
                'title': pinData['title'] ?? 'Untitled',
                'track_title': pinData['track_title'] ?? 'Unknown Track',
                'track_artist': pinData['track_artist'] ?? 'Unknown Artist',
                'description': pinData['description'],
                'caption': pinData['caption'],
                'service': pinData['service'] ?? 'unknown',
                'owner': pinData['owner'] ?? {'username': 'unknown', 'profile_pic': null},
                'aura_radius': pinData['aura_radius'] ?? 50,
                'created_at': pinData['created_at'],
                'updated_at': pinData['updated_at'],
                'interaction_count': pinData['interaction_count'] ?? {
                  'like': pinData['like_count'] ?? 0,
                  'collect': pinData['collect_count'] ?? 0,
                  'view': pinData['view_count'] ?? 0,
                  'share': pinData['share_count'] ?? 0,
                },
                // Add the new engagement_counts structure from the backend
                'engagement_counts': pinData['engagement_counts'],
                'rarity': pinData['rarity'] ?? 'common',
                'skin': pinData['skin'] ?? 1,
                'skin_details': pinData['skin_details'],
                'track_url': pinData['track_url'] ?? '',
                'artwork_url': pinData['artwork_url'],
                'is_private': pinData['is_private'] ?? false,
                'album': pinData['album'],
                'duration_ms': pinData['duration_ms'],
                'mood': pinData['mood'],
                'is_currently_playing': pinData['is_currently_playing'] ?? false,
                'expiration_date': pinData['expiration_date'],
                'location_name': pinData['location_name'],
                'has_expired': pinData['has_expired'] ?? false,
                'distance': pinData['distance'],
                'upvote_count': pinData['upvote_count'] ?? 0,
                'downvote_count': pinData['downvote_count'] ?? 0,
              };
              
              print('🎨 Processing friend pin: ${processedPin['title']} at ${coords}');
              
              return Pin.fromJson(processedPin);
            } catch (e) {
              print('❌ Error processing friend pin: $e');
              rethrow;
            }
          }).toList();
        }
      }
      
      print('No friend pins found for user $userId');
      return [];
    } catch (e) {
      print('Error getting friend pins for user $userId: $e');
      return [];
    }
  }

  // Get nearby fresh pins (pins user hasn't interacted with)
  Future<List<Pin>> getNearbyFreshPins(
    double latitude,
    double longitude,
    double radius,
  ) async {
    try {
      print('🔍 Fetching nearby fresh pins at lat: $latitude, lng: $longitude, radius: $radius');
      final response = await _apiClient.get(
        '${AppConstants.pinsEndpoint}/nearby_fresh/',
        queryParams: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
        },
        requiresAuth: true,
      );
      
      // Handle the same response formats as getNearbyPins
      return _parseNearbyPinsResponse(response, latitude, longitude, 'fresh');
    } catch (e) {
      print('Stack trace: ${StackTrace.current}');
      return [];
    }
  }

  // Get nearby friends pins (pins from friends only)
  Future<List<Pin>> getNearbyFriendsPins(
    double latitude,
    double longitude,
    double radius,
  ) async {
    try {
      print('🔍 Fetching nearby friends pins at lat: $latitude, lng: $longitude, radius: $radius');
      final response = await _apiClient.get(
        '${AppConstants.pinsEndpoint}/nearby_friends/',
        queryParams: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radius.toString(),
        },
        requiresAuth: true,
      );
      
      // Handle the same response formats as getNearbyPins
      return _parseNearbyPinsResponse(response, latitude, longitude, 'friends');
    } catch (e) {
      print('Stack trace: ${StackTrace.current}');
      return [];
    }
  }

  // Extract the common parsing logic to avoid code duplication
  List<Pin> _parseNearbyPinsResponse(dynamic response, double latitude, double longitude, String type) {
    // ------------------------------------------------------------------
    // 1) Handle GeoJSON FeatureCollection format (server v2)
    // ------------------------------------------------------------------
    if (response is Map<String, dynamic> && response['type'] == 'FeatureCollection') {
      final features = response['features'] as List;
      if (features.isNotEmpty) {
        print('📍 Found ${features.length} $type pins from API');
        return features.map((feature) {
          print('🎯 Processing $type feature: $feature');
          
          // Convert GeoJSON feature to Pin format
          final properties = feature['properties'] as Map<String, dynamic>;
          final geometry = feature['geometry'] as String;
          
          // Parse SRID=4326;POINT (lng lat) format
          final coordsStr = geometry.split('POINT ')[1].trim();
          final coords = coordsStr
              .replaceAll('(', '')
              .replaceAll(')', '')
              .split(' ')
              .map((s) => double.parse(s))
              .toList();
          
          // Create a pin with display properties matching test pins
          final pinJson = {
            'id': feature['id'],
            'location': {
              'type': 'Point',
              'coordinates': coords // [longitude, latitude]
            },
            'latitude': coords[1],
            'longitude': coords[0],
            'title': properties['title'],
            'track_title': properties['track_title'],
            'track_artist': properties['track_artist'],
            'caption': properties['caption'],
            'service': properties['service'],
            'owner': {
              'username': properties['owner_name'],
              'profile_pic': properties['owner_profile_pic'],
            },
            'aura_radius': properties['aura_radius'],
            'created_at': properties['created_at'],
            'interaction_count': {
              'like': properties['like_count'] ?? 0,
              'collect': properties['collect_count'] ?? 0,
              'view': 0,
              'share': 0
            },
            // Add display properties to match test pins
            'rarity': properties['rarity'] ?? 'common',
            'skin': properties['skin'] ?? 1,
            'track_url': properties['track_url'] ?? '',
            'is_private': properties['has_expired'] ?? false,
            'artwork_url': properties['artwork_url'],
            'album': properties['album'],
            'duration_ms': properties['duration_ms'],
            // Add skin details if available
            'skin_details': properties['skin_details'],
            // Add custom display properties based on type
            'glow_intensity': type == 'fresh' ? 1.5 : 1.2, // Fresh pins glow more
            'scale': type == 'friends' ? 1.3 : 1.2, // Friends pins are slightly larger
            'upvote_count': properties['upvote_count'] ?? 0,
            'downvote_count': properties['downvote_count'] ?? 0,
          };
          
          print('🎨 Created $type pin JSON: $pinJson');
          
          try {
            final pin = Pin.fromJson(pinJson);
            print('✅ Successfully created $type Pin object:');
            print('  - ID: ${pin.id}');
            print('  - Title: ${pin.title}');
            print('  - Location: lat: ${pin.latitude}, lng: ${pin.longitude}');
            print('  - Track: ${pin.trackTitle} by ${pin.trackArtist}');
            return pin;
          } catch (e) {
            print('❌ Error creating $type Pin object: $e');
            rethrow;
          }
        }).toList();
      }
    }
    
    // ------------------------------------------------------------------
    // 2) Handle simple List<Map> format (current server v1)
    // ------------------------------------------------------------------
    if (response is List) {
      final pinsRaw = response as List<dynamic>;
      if (pinsRaw.isNotEmpty) {
        print('📍 Found ${pinsRaw.length} $type pins (list)');
        return pinsRaw.map<Pin>((item) {
          try {
            final map = Map<String, dynamic>.from(item as Map);

            // Parse WKT location string -> coordinates list
            List<double> coords = [0, 0];
            if (map['location'] != null && map['location'] is String) {
              final locStr = (map['location'] as String);
              // Use regex to robustly extract coordinates from WKT string
              final regExp = RegExp(r'POINT\s*\(([^)]+)\)');
              final match = regExp.firstMatch(locStr);
              if (match != null) {
                final coordsStr = match.group(1);
                if (coordsStr != null) {
                  final parts = coordsStr.split(' ').map((s) => double.tryParse(s)).toList();
                  if (parts.length == 2 && parts[0] != null && parts[1] != null) {
                    coords = [parts[0]!, parts[1]!]; // lng, lat
                  }
                }
              }
            }

            // Ensure skin_details field available as map
            Map<String, dynamic>? skinDetails;
            if (map['skin_details'] != null && map['skin_details'] is Map) {
              skinDetails = Map<String, dynamic>.from(map['skin_details']);
            }

            final pinJson = {
              'id': map['id'],
              'location': {
                'type': 'Point',
                'coordinates': coords,
              },
              'latitude': coords[1],
              'longitude': coords[0],
              'title': map['title'],
              'track_title': map['track_title'],
              'track_artist': map['track_artist'],
              'caption': map['caption'],
              'service': map['service'],
              'owner': {
                'username': map['owner']?['username'] ?? 'unknown',
                'profile_pic': map['owner']?['profile_pic'],
              },
              'aura_radius': map['aura_radius'],
              'created_at': map['created_at'],
              'interaction_count': map['interaction_count'] ?? {},
              'rarity': map['rarity'] ?? 'common',
              'skin': map['skin'],
              'skin_details': skinDetails,
              'track_url': map['track_url'] ?? '',
              'is_private': map['is_private'] ?? false,
              'artwork_url': map['artwork_url'],
              'album': map['album'],
              'duration_ms': map['duration_ms'],
              'upvote_count': map['upvote_count'] ?? 0,
              'downvote_count': map['downvote_count'] ?? 0,
            };

            final pin = Pin.fromJson(pinJson);
            return pin;
          } catch (e) {
            print('❌ Error converting $type pin item: $e');
            throw e;
          }
        }).toList();
      }
    }
    
    print('No $type pins found in range');
    return [];
  }
} 