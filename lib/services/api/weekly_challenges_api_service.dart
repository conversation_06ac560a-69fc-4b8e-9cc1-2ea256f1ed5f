import 'dart:convert';
import '../../models/weekly_challenge.dart';
import '../../models/api_response.dart';
import '../api_service.dart';
import '../auth_service.dart';
import 'package:flutter/foundation.dart';

class WeeklyChallengesApiService {
  final ApiService _apiService;
  final AuthService _authService;

  WeeklyChallengesApiService(this._apiService, this._authService);

  Future<List<WeeklyChallenge>> getWeeklyChallenges() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/challenges/',
      token: token,
      requiresAuth: true,
      queryParams: {
        'include_participation': 'true', // Request participation info
      },
    );

    if (response.success) {
      final results = response.data['results'] as List;
      debugPrint('Weekly Challenges API Response: ${response.data}');
      return results.asMap().entries.map((entry) {
        final json = entry.value as Map<String, dynamic>;
        // Add index to json for gradient color selection
        json['index'] = entry.key;
        debugPrint('Challenge ${json['id']} has_participated: ${json['has_participated']}');
        return WeeklyChallenge.fromJson(json);
      }).toList();
    } else {
      throw Exception(response.message ?? 'Failed to load weekly challenges');
    }
  }

  Future<ApiResponse> participateInChallenge({
    required String challengeId,
    required Map<String, dynamic> songData,
    double? latitude,
    double? longitude,
  }) async {
    final token = await _authService.getToken();
    final data = {
      'song': songData,
      if (latitude != null && longitude != null) ...{
        'latitude': latitude,
        'longitude': longitude,
      },
    };

    debugPrint('🎵 Participating in challenge $challengeId');
    debugPrint('🎵 Request data: ${jsonEncode(data)}');

    final response = await _apiService.post(
      '/challenges/$challengeId/participate/',
      data: data,
      token: token,
      requiresAuth: true,
    );

    debugPrint('🎵 Response status: ${response.statusCode}');
    debugPrint('🎵 Response data: ${response.data}');
    debugPrint('🎵 Response error: ${response.error}');
    debugPrint('🎵 Response message: ${response.message}');

    return response;
  }

  Future<Map<String, dynamic>> getChallengeEntries({
    required String challengeId,
    String scope = 'all',
    bool includeLocation = false,
    double? latitude,
    double? longitude,
    double radiusKm = 5,
  }) async {
    final token = await _authService.getToken();
    
    final queryParams = {
      'scope': scope,
      if (includeLocation && latitude != null && longitude != null) ...{
        'lat': latitude.toString(),
        'lng': longitude.toString(),
        'radius_km': radiusKm.toString(),
      },
    };

    final response = await _apiService.get(
      '/challenges/$challengeId/entries/',
      queryParams: queryParams,
      token: token,
      requiresAuth: true,
    );

    if (response.success) {
      return response.data;
    } else {
      throw Exception(response.message ?? 'Failed to load challenge entries');
    }
  }

  Future<ApiResponse> voteChallengeEntry({
    required int entryId,
    required bool isUpvote,  // true for upvote, false for downvote
  }) async {
    final token = await _authService.getToken();
    
    debugPrint('🗳️ Voting on entry $entryId with ${isUpvote ? "upvote" : "downvote"}');
    
    final response = await _apiService.post(
      '/challenges/votes/${isUpvote ? "upvote" : "downvote"}/',
      data: {
        'entry': entryId,
      },
      token: token,
      requiresAuth: true,
    );

    debugPrint('🗳️ Vote response:');
    debugPrint('  - Success: ${response.success}');
    debugPrint('  - Status: ${response.statusCode}');
    debugPrint('  - Data: ${response.data}');
    debugPrint('  - Error: ${response.error}');
    debugPrint('  - Message: ${response.message}');

    return response;
  }
} 