import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../api_service.dart';
import '../auth_service.dart';
import '../../models/user.dart';
import '../../models/pin_interaction.dart';
import '../../models/pin_engagement.dart';

class PinEngagementService {
  final ApiService _apiService;
  final AuthService _authService;

  PinEngagementService(this._apiService, this._authService);

  /// Get users who liked a specific pin
  Future<PaginatedResponse<User>> getUsersWhoLiked(
    int pinId, {
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/pins/$pinId/likes/',
        queryParams: {
          'page': page.toString(),
          'page_size': pageSize.toString(),
        },
        token: token,
        requiresAuth: true,
      );

      if (!response.success) {
        throw Exception(response.message ?? 'Failed to load likes');
      }

      return PaginatedResponse<User>.fromJson(
        response.data as Map<String, dynamic>,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting users who liked pin $pinId: $e');
      }
      throw Exception('Failed to load likes: $e');
    }
  }

  /// Get users who viewed a specific pin (only for pin owner)
  Future<PaginatedResponse<User>> getUsersWhoViewed(
    int pinId, {
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/pins/$pinId/views/',
        queryParams: {
          'page': page.toString(),
          'page_size': pageSize.toString(),
        },
        token: token,
        requiresAuth: true,
      );

      if (!response.success) {
        throw Exception(response.message ?? 'Failed to load views');
      }

      return PaginatedResponse<User>.fromJson(
        response.data as Map<String, dynamic>,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting users who viewed pin $pinId: $e');
      }
      throw Exception('Failed to load views: $e');
    }
  }

  /// Get all interactions for a pin
  Future<PaginatedResponse<PinInteraction>> getPinInteractions(
    int pinId, {
    String type = 'all',
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/pins/$pinId/interactions/',
        queryParams: {
          'type': type,
          'page': page.toString(),
          'page_size': pageSize.toString(),
        },
        token: token,
        requiresAuth: true,
      );

      if (!response.success) {
        throw Exception(response.message ?? 'Failed to load interactions');
      }

      return PaginatedResponse<PinInteraction>.fromJson(
        response.data as Map<String, dynamic>,
        (json) => PinInteraction.fromJson(json),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting pin interactions for pin $pinId: $e');
      }
      throw Exception('Failed to load interactions: $e');
    }
  }

  /// Get comprehensive engagement data for a pin
  Future<PinEngagement> getPinEngagement(int pinId) async {
    try {
      final token = await _authService.getToken();
      
      if (kDebugMode) {
        print('🌐 [PinEngagementService] ======== API CALL ========');
        print('🌐 [PinEngagementService] Endpoint: /pins/$pinId/engagement/');
        print('🌐 [PinEngagementService] Token: ${token != null ? 'Present' : 'Missing'}');
        print('🌐 [PinEngagementService] =============================');
      }
      
      final response = await _apiService.get(
        '/pins/$pinId/engagement/',
        token: token,
        requiresAuth: true,
      );

      if (kDebugMode) {
        print('🌐 [PinEngagementService] ======== RAW API RESPONSE ========');
        print('🌐 [PinEngagementService] Success: ${response.success}');
        print('🌐 [PinEngagementService] Status Code: ${response.statusCode}');
        print('🌐 [PinEngagementService] Message: ${response.message}');
        print('🌐 [PinEngagementService] Raw Data Type: ${response.data?.runtimeType}');
        print('🌐 [PinEngagementService] Raw Data: ${response.data}');
        print('🌐 [PinEngagementService] ---- Detailed Engagement Check ----');
        if (response.data is Map<String, dynamic>) {
          final data = response.data as Map<String, dynamic>;
          print('🌐 [PinEngagementService] Has detailed_engagement key: ${data.containsKey('detailed_engagement')}');
          if (data.containsKey('detailed_engagement')) {
            print('🌐 [PinEngagementService] detailed_engagement value: ${data['detailed_engagement']}');
            print('🌐 [PinEngagementService] detailed_engagement type: ${data['detailed_engagement']?.runtimeType}');
            if (data['detailed_engagement'] is Map) {
              final detailed = data['detailed_engagement'] as Map;
              print('🌐 [PinEngagementService] detailed_engagement keys: ${detailed.keys.toList()}');
              print('🌐 [PinEngagementService] upvotes: ${detailed['upvotes']}');
              print('🌐 [PinEngagementService] comments: ${detailed['comments']}');
            }
          }
        }
        print('🌐 [PinEngagementService] ===================================');
      }

      if (!response.success) {
        throw Exception(response.message ?? 'Failed to load engagement data');
      }

      final engagementData = PinEngagement.fromJson(response.data as Map<String, dynamic>);
      
      if (kDebugMode) {
        print('🌐 [PinEngagementService] ======== PARSED MODEL ========');
        print('🌐 [PinEngagementService] Model created successfully');
        print('🌐 [PinEngagementService] Pin ID: ${engagementData.pinId}');
        print('🌐 [PinEngagementService] Total interactions: ${engagementData.totalInteractions}');
        print('🌐 [PinEngagementService] ===============================');
      }
      
      return engagementData;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [PinEngagementService] ======== SERVICE ERROR ========');
        print('❌ [PinEngagementService] Error getting pin engagement for pin $pinId: $e');
        print('❌ [PinEngagementService] Error type: ${e.runtimeType}');
        print('❌ [PinEngagementService] Stack trace: ${StackTrace.current}');
        print('❌ [PinEngagementService] ================================');
      }
      throw Exception('Failed to load engagement data: $e');
    }
  }

  /// Get comments for a pin
  Future<PaginatedResponse<Comment>> getPinComments(
    int pinId, {
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/comments/',
        queryParams: {
          'pin_id': pinId.toString(),
          'page': page.toString(),
          'page_size': pageSize.toString(),
        },
        token: token,
        requiresAuth: true,
      );

      if (!response.success) {
        throw Exception(response.message ?? 'Failed to load comments');
      }

      return PaginatedResponse<Comment>.fromJson(
        response.data as Map<String, dynamic>,
        (json) => Comment.fromJson(json),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting comments for pin $pinId: $e');
      }
      throw Exception('Failed to load comments: $e');
    }
  }
}

/// Generic paginated response model
class PaginatedResponse<T> {
  final int count;
  final String? next;
  final String? previous;
  final List<T> results;

  PaginatedResponse({
    required this.count,
    this.next,
    this.previous,
    required this.results,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    return PaginatedResponse<T>(
      count: json['count'] ?? 0,
      next: json['next'],
      previous: json['previous'],
      results: (json['results'] as List<dynamic>? ?? [])
          .map((item) => fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  bool get hasMore => next != null;
  bool get hasPrevious => previous != null;
}

/// Vote stats model for pins with enhanced data from vote_post endpoint
class VoteStats {
  final int upvotes;
  final int downvotes;
  final int score;
  final int totalVotes;
  final double upvoteRatio;
  final int? userVote;  // 1 = upvote, -1 = downvote, null = no vote
  final bool canVote;   // Whether user can vote on this pin
  final String? action; // Action performed (e.g., 'vote_updated')
  final Map<String, dynamic>? pinInfo; // Additional pin information

  VoteStats({
    required this.upvotes,
    required this.downvotes,
    required this.score,
    required this.totalVotes,
    required this.upvoteRatio,
    this.userVote,
    this.canVote = true,  // Default to true for backward compatibility
    this.action,
    this.pinInfo,
  });

  factory VoteStats.fromJson(Map<String, dynamic> json) {
    return VoteStats(
      upvotes: json['upvotes'] ?? 0,
      downvotes: json['downvotes'] ?? 0,
      score: json['score'] ?? 0,
      totalVotes: json['total_votes'] ?? 0,
      upvoteRatio: (json['upvote_ratio'] ?? 0.0).toDouble(),
      userVote: json['user_vote'],
      canVote: json['can_vote'] ?? true,
      action: json['action'],
      pinInfo: json['pin_info'] as Map<String, dynamic>?,
    );
  }

  bool get isUpvoted => userVote == 1;
  bool get isDownvoted => userVote == -1;
  bool get hasVoted => userVote != null;
  
  /// Get the username from pin_info if available
  String? get ownerUsername => pinInfo?['owner']?['username'];
  
  /// Get the track title from pin_info if available  
  String? get trackTitle => pinInfo?['track_title'] ?? pinInfo?['title'];
  
  /// Get the track artist from pin_info if available
  String? get trackArtist => pinInfo?['track_artist'];
}

/// Comment model for pin comments
class Comment {
  final int id;
  final int userId;
  final int pinId;
  final String text;
  final DateTime createdAt;
  final bool isEdited;
  final User? user;

  Comment({
    required this.id,
    required this.userId,
    required this.pinId,
    required this.text,
    required this.createdAt,
    required this.isEdited,
    this.user,
  });

  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      id: json['id'],
      userId: json['user'] is int ? json['user'] : json['user']['id'],
      pinId: json['pin'],
      text: json['text'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      isEdited: json['is_edited'] ?? false,
      user: json['user'] is Map<String, dynamic> 
          ? User.fromJson(json['user'])
          : null,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}

/// Comment info model with pagination and pin context
class CommentInfo {
  final int pinId;
  final int commentCount;
  final List<Comment> comments;
  final bool hasMore;
  final int limit;
  final int offset;
  final int total;
  final bool canComment;
  final Map<String, dynamic>? pinInfo;

  CommentInfo({
    required this.pinId,
    required this.commentCount,
    required this.comments,
    required this.hasMore,
    required this.limit,
    required this.offset,
    required this.total,
    this.canComment = true,
    this.pinInfo,
  });

  factory CommentInfo.fromJson(Map<String, dynamic> json) {
    final commentsData = json['comments'] as List<dynamic>? ?? [];
    final pagination = json['pagination'] as Map<String, dynamic>? ?? {};
    final pinId = json['pin_id'] ?? 0;
    
    return CommentInfo(
      pinId: pinId,
      commentCount: json['comment_count'] ?? 0,
      comments: commentsData
          .map((item) {
            final commentJson = item as Map<String, dynamic>;
            // Add pinId to comment JSON since it's not included in individual comments
            commentJson['pin'] = pinId;
            return Comment.fromJson(commentJson);
          })
          .toList(),
      hasMore: pagination['has_more'] ?? false,
      limit: pagination['limit'] ?? 20,
      offset: pagination['offset'] ?? 0,
      total: pagination['total'] ?? 0,
      canComment: json['can_comment'] ?? true,
      pinInfo: json['pin_info'] as Map<String, dynamic>?,
    );
  }

  /// Get the track title from pin_info if available
  String? get trackTitle => pinInfo?['track_title'] ?? pinInfo?['title'];
  
  /// Get the track artist from pin_info if available
  String? get trackArtist => pinInfo?['track_artist'];
  
  /// Get the pin owner username from pin_info if available
  String? get ownerUsername => pinInfo?['owner']?['username'];
}

/// Comment post response model
class CommentPost {
  final int pinId;
  final int commentId;
  final int commentCount;
  final Comment newComment;
  final String action;
  final bool canComment;
  final Map<String, dynamic>? pinInfo;

  CommentPost({
    required this.pinId,
    required this.commentId,
    required this.commentCount,
    required this.newComment,
    required this.action,
    this.canComment = true,
    this.pinInfo,
  });

  factory CommentPost.fromJson(Map<String, dynamic> json) {
    final pinId = json['pin_id'] ?? 0;
    final newCommentJson = json['new_comment'] as Map<String, dynamic>;
    // Add pinId to comment JSON since it's not included in individual comments
    newCommentJson['pin'] = pinId;
    
    return CommentPost(
      pinId: pinId,
      commentId: json['comment_id'] ?? 0,
      commentCount: json['comment_count'] ?? 0,
      newComment: Comment.fromJson(newCommentJson),
      action: json['action'] ?? 'comment_added',
      canComment: json['can_comment'] ?? true,
      pinInfo: json['pin_info'] as Map<String, dynamic>?,
    );
  }

  /// Get the track title from pin_info if available
  String? get trackTitle => pinInfo?['track_title'] ?? pinInfo?['title'];
  
  /// Get the track artist from pin_info if available
  String? get trackArtist => pinInfo?['track_artist'];
}

// NEW: Vote functionality for pins
extension PinVoting on PinEngagementService {
  /// ⚡ ULTRA-FAST: Get vote info for a pin in single optimized call
  Future<VoteStats> getQuickVoteInfo(int pinId) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/pins/$pinId/vote_info/',  // ⚡ Optimized endpoint
        token: token,
        requiresAuth: true,
      );

      if (!response.success) {
        throw Exception(response.message ?? 'Failed to get vote info');
      }

      return VoteStats.fromJson(response.data as Map<String, dynamic>);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting quick vote info for pin $pinId: $e');
      }
      throw Exception('Failed to get vote info: $e');
    }
  }

  /// ⚡ ULTRA-FAST: Vote on a pin using optimized vote_post endpoint
  /// Returns complete vote info in single atomic call - no need for separate stats request!
  Future<VoteStats> voteOnPin(int pinId, int voteValue) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/pins/$pinId/vote_post/',  // ⚡ NEW: Ultra-fast dedicated voting endpoint
        data: {
          'value': voteValue,  // 1 for upvote, -1 for downvote, 0 to remove
        },
        token: token,
        requiresAuth: true,
      );

      if (!response.success) {
        throw Exception(response.message ?? 'Failed to vote on pin');
      }

      // ⚡ The vote_post endpoint returns complete vote info immediately!
      // No need for separate API call - everything is in the response
      return VoteStats.fromJson(response.data as Map<String, dynamic>);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error voting on pin $pinId: $e');
      }
      throw Exception('Failed to vote: $e');
    }
  }

  /// ⚡ Remove vote from a pin (convenience method)
  Future<VoteStats> removeVote(int pinId) async {
    return await voteOnPin(pinId, 0);  // Use 0 to remove vote
  }

  /// Get vote statistics for a pin (LEGACY - slower method)
  @deprecated
  Future<VoteStats> getVoteStats(int pinId) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/votes/stats/',
        queryParams: {
          'pin_id': pinId.toString(),
        },
        token: token,
        requiresAuth: true,
      );

      if (!response.success) {
        throw Exception(response.message ?? 'Failed to get vote stats');
      }

      return VoteStats.fromJson(response.data as Map<String, dynamic>);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting vote stats for pin $pinId: $e');
      }
      throw Exception('Failed to get vote stats: $e');
    }
  }

  /// Upvote a pin (convenience method)
  Future<VoteStats> upvotePin(int pinId) async {
    return await voteOnPin(pinId, 1);
  }

  /// Downvote a pin (convenience method)
  Future<VoteStats> downvotePin(int pinId) async {
    return await voteOnPin(pinId, -1);
  }
}

// NEW: Comment functionality for pins
extension PinComments on PinEngagementService {
  /// ⚡ ULTRA-FAST: Get comment info for a pin in single optimized call
  Future<CommentInfo> getQuickCommentInfo(int pinId, {int limit = 20, int offset = 0}) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.get(
        '/pins/$pinId/comment_info/',  // ⚡ Optimized endpoint
        queryParams: {
          'limit': limit.toString(),
          'offset': offset.toString(),
        },
        token: token,
        requiresAuth: true,
      );

      if (!response.success) {
        throw Exception(response.message ?? 'Failed to get comment info');
      }

      return CommentInfo.fromJson(response.data as Map<String, dynamic>);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting quick comment info for pin $pinId: $e');
      }
      throw Exception('Failed to get comment info: $e');
    }
  }

  /// ⚡ ULTRA-FAST: Post a comment on a pin using optimized comment_post endpoint
  /// Returns complete comment info in single atomic call - no need for separate request!
  Future<CommentPost> postComment(int pinId, String text) async {
    try {
      final token = await _authService.getToken();
      final response = await _apiService.post(
        '/pins/$pinId/comment_post/',  // ⚡ NEW: Ultra-fast dedicated comment endpoint
        data: {
          'text': text.trim(),  // Comment text
        },
        token: token,
        requiresAuth: true,
      );

      if (!response.success) {
        throw Exception(response.message ?? 'Failed to post comment');
      }

      // ⚡ The comment_post endpoint returns complete comment info immediately!
      // No need for separate API call - everything is in the response
      return CommentPost.fromJson(response.data as Map<String, dynamic>);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error posting comment on pin $pinId: $e');
      }
      throw Exception('Failed to post comment: $e');
    }
  }
} 