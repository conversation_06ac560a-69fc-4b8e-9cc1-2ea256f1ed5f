import '../../models/pin_skin.dart';
import 'api_client.dart';

class SkinService {
  final ApiClient _apiClient;
  
  SkinService(this._apiClient);

  /// Get all available skins (locked and unlocked)
  Future<List<PinSkin>> getAvailableSkins() async {
    try {
      final response = await _apiClient.get('/pins/skins/available/');
      
      if (response is List) {
        return response.map((json) => PinSkin.fromApiJson(json)).toList();
      } else {
        throw Exception('Invalid response format for available skins');
      }
    } catch (e) {
      throw Exception('Error fetching available skins: $e');
    }
  }

  /// Get featured skins (latest, premium, top)
  Future<Map<String, List<PinSkin>>> getFeaturedSkins() async {
    try {
      final response = await _apiClient.get('/pins/skins/featured/');
      
      if (response is Map<String, dynamic>) {
        return {
          'latest': (response['latest'] as List)
              .map((json) => PinSkin.fromApiJson(json))
              .toList(),
          'premium': (response['premium'] as List)
              .map((json) => PinSkin.fromApiJson(json))
              .toList(),
          'top': (response['top'] as List)
              .map((json) => PinSkin.fromApiJson(json))
              .toList(),
        };
      } else {
        throw Exception('Invalid response format for featured skins');
      }
    } catch (e) {
      throw Exception('Error fetching featured skins: $e');
    }
  }

  /// Get limited-time skins
  Future<Map<String, List<PinSkin>>> getLimitedSkins() async {
    try {
      final response = await _apiClient.get('/pins/skins/limited/');
      
      if (response is Map<String, dynamic>) {
        return {
          'active': (response['active'] as List)
              .map((json) => PinSkin.fromApiJson(json))
              .toList(),
          'expired': (response['expired'] as List)
              .map((json) => PinSkin.fromApiJson(json))
              .toList(),
        };
      } else {
        throw Exception('Invalid response format for limited skins');
      }
    } catch (e) {
      throw Exception('Error fetching limited skins: $e');
    }
  }

  /// Get user's unlocked skins only
  Future<List<PinSkin>> getUnlockedSkins() async {
    try {
      final response = await _apiClient.get('/pins/skins/unlocked/');
      
      if (response is List) {
        return response.map((json) => PinSkin.fromApiJson(json)).toList();
      } else {
        throw Exception('Invalid response format for unlocked skins');
      }
    } catch (e) {
      throw Exception('Error fetching unlocked skins: $e');
    }
  }

  /// Claim a skin if eligible
  Future<Map<String, dynamic>> claimSkin(int skinId) async {
    try {
      final response = await _apiClient.post('/pins/skins/$skinId/claim/');
      return Map<String, dynamic>.from(response);
    } catch (e) {
      if (e is ApiException && e.statusCode == 400) {
        // User not eligible
        throw Exception(e.message);
      }
      throw Exception('Error claiming skin: $e');
    }
  }

  /// Equip a skin for future pins
  Future<Map<String, dynamic>> equipSkin(int skinId) async {
    try {
      final response = await _apiClient.post('/pins/skins/$skinId/equip/');
      return Map<String, dynamic>.from(response);
    } catch (e) {
      if (e is ApiException && e.statusCode == 400) {
        // User doesn't have access to this skin
        throw Exception(e.message);
      }
      throw Exception('Error equipping skin: $e');
    }
  }

  /// Get user's skin status details
  Future<List<Map<String, dynamic>>> getUserSkins() async {
    try {
      final response = await _apiClient.get('/pins/user-skins/');
      
      if (response is List) {
        return List<Map<String, dynamic>>.from(response);
      } else {
        throw Exception('Invalid response format for user skins');
      }
    } catch (e) {
      throw Exception('Error fetching user skins: $e');
    }
  }
} 