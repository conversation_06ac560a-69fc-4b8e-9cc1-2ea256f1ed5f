import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import '../config/constants.dart';

class AchievementWebSocketService {
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  bool _isConnected = false;
  String? _authToken;
  
  // 🚫 TEMPORARY: Disable WebSocket until backend endpoint is implemented
  static const bool _webSocketEnabled = true;
  
  // Stream controllers for different types of updates
  final StreamController<Map<String, dynamic>> _achievementController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _levelUpController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _progressController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<int> _xpGainedController = 
      StreamController<int>.broadcast();

  // Public streams
  Stream<Map<String, dynamic>> get onAchievementCompleted => _achievementController.stream;
  Stream<Map<String, dynamic>> get onLevelUp => _levelUpController.stream;
  Stream<Map<String, dynamic>> get onProgressUpdate => _progressController.stream;
  Stream<int> get onXpGained => _xpGainedController.stream;

  bool get isConnected => _isConnected;

  /// Connect to the WebSocket server
  Future<void> connect(String authToken) async {
    if (_isConnected && _authToken == authToken) {
      debugPrint('🔌 WebSocket: Already connected with same token');
      return;
    }

    await disconnect(); // Close any existing connection
    
    _authToken = authToken;
    
    try {
      // Construct WebSocket URL properly
      String baseUrl = AppConstants.baseApiUrl;
      
      // Remove /api suffix if present
      if (baseUrl.endsWith('/api')) {
        baseUrl = baseUrl.substring(0, baseUrl.length - 4);
      }
      
      // Convert HTTP/HTTPS to WS/WSS
      if (baseUrl.startsWith('https://')) {
        baseUrl = baseUrl.replaceFirst('https://', 'wss://');
      } else if (baseUrl.startsWith('http://')) {
        baseUrl = baseUrl.replaceFirst('http://', 'ws://');
      }
      
      // Backend expects token as query parameter, not header
      final wsUrl = '$baseUrl/ws/achievements/?token=$authToken';
      
      debugPrint('🔌 WebSocket: Original API URL: ${AppConstants.baseApiUrl}');
      debugPrint('🔌 WebSocket: Converted base URL: $baseUrl');
      debugPrint('🔌 WebSocket: Final WebSocket URL: $wsUrl');
      debugPrint('🔌 WebSocket: Token length: ${authToken.length}');
      
      _channel = IOWebSocketChannel.connect(
        Uri.parse(wsUrl),
        headers: {
          'Authorization': 'Bearer $authToken',
        },
      );

      _subscription = _channel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketDisconnected,
      );

      _isConnected = true;
      debugPrint('🔌 WebSocket: Connected successfully');
      
      // Send initial connection message
      _sendMessage({
        'type': 'connection',
        'message': 'Achievement WebSocket connected',
      });
      
    } catch (e) {
      debugPrint('🔌 WebSocket: Connection failed: $e');
      _isConnected = false;
    }
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    if (_subscription != null) {
      await _subscription!.cancel();
      _subscription = null;
    }
    
    if (_channel != null) {
      await _channel!.sink.close();
      _channel = null;
    }
    
    _isConnected = false;
    _authToken = null;
    debugPrint('🔌 WebSocket: Disconnected');
  }

  /// Send a message through the WebSocket
  void _sendMessage(Map<String, dynamic> message) {
    if (_isConnected && _channel != null) {
      _channel!.sink.add(jsonEncode(message));
    }
  }

  /// Handle incoming WebSocket messages
  void _handleWebSocketMessage(dynamic message) {
    try {
      final data = jsonDecode(message as String) as Map<String, dynamic>;
      final messageType = data['type'] as String?;
      
      debugPrint('🔌 WebSocket: Received message type: $messageType');
      
      switch (messageType) {
        case 'achievement_completed':
          _handleAchievementCompleted(data);
          break;
        case 'level_up':
          _handleLevelUp(data);
          break;
        case 'progress_update':
          _handleProgressUpdate(data);
          break;
        case 'xp_gained':
          _handleXpGained(data);
          break;
        case 'connection_confirmed':
          debugPrint('🔌 WebSocket: Connection confirmed by server');
          break;
        default:
          debugPrint('🔌 WebSocket: Unknown message type: $messageType');
      }
    } catch (e) {
      debugPrint('🔌 WebSocket: Error parsing message: $e');
    }
  }

  /// Handle achievement completed messages
  void _handleAchievementCompleted(Map<String, dynamic> data) {
    final achievement = data['achievement'] as Map<String, dynamic>?;
    if (achievement != null) {
      debugPrint('🏆 WebSocket: Achievement completed: ${achievement['name']}');
      _achievementController.add(achievement);
    }
  }

  /// Handle level up messages
  void _handleLevelUp(Map<String, dynamic> data) {
    final newLevel = data['new_level'] as Map<String, dynamic>?;
    if (newLevel != null) {
      debugPrint('⭐ WebSocket: Level up to level ${newLevel['level']}');
      _levelUpController.add(newLevel);
    }
  }

  /// Handle progress update messages
  void _handleProgressUpdate(Map<String, dynamic> data) {
    final progressData = data['data'] as Map<String, dynamic>?;
    if (progressData != null) {
      debugPrint('📊 WebSocket: Progress update received');
      _progressController.add(progressData);
    }
  }

  /// Handle XP gained messages
  void _handleXpGained(Map<String, dynamic> data) {
    final xpGained = data['xp_gained'] as int?;
    if (xpGained != null && xpGained > 0) {
      debugPrint('💎 WebSocket: XP gained: +$xpGained');
      _xpGainedController.add(xpGained);
    }
  }

  /// Handle WebSocket errors
  void _handleWebSocketError(dynamic error) {
    debugPrint('🔌 WebSocket: Error occurred: $error');
    _isConnected = false;
    
    // Attempt to reconnect after a delay
    Future.delayed(const Duration(seconds: 30), () {
      if (_authToken != null) {
        debugPrint('🔌 WebSocket: Attempting to reconnect...');
        connect(_authToken!);
      }
    });
  }

  /// Handle WebSocket disconnection
  void _handleWebSocketDisconnected() {
    debugPrint('🔌 WebSocket: Connection closed');
    _isConnected = false;
    
    // Attempt to reconnect after a delay if we have a token
    
    Future.delayed(const Duration(seconds: 30), () {
      debugPrint('🔌 WebSocket: Attempting to reconnect...');
      if (_authToken != null) {
        connect(_authToken!);
      }
    });

  }

  /// Send ping to keep connection alive
  void sendPing() {
    _sendMessage({
      'type': 'ping',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// Start periodic ping to keep connection alive
  Timer? _pingTimer;
  
  void startPeriodicPing() {
    _pingTimer?.cancel();
    _pingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isConnected) {
        sendPing();
      }
    });
  }

  void stopPeriodicPing() {
    _pingTimer?.cancel();
    _pingTimer = null;
  }

  /// Clean up resources
  void dispose() {
    stopPeriodicPing();
    disconnect();
    _achievementController.close();
    _levelUpController.close();
    _progressController.close();
    _xpGainedController.close();
  }
} 