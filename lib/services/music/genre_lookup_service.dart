import 'package:flutter/foundation.dart';
import 'spotify_service.dart';
import 'musicbrainz_service.dart';
import 'spotify_genre_service.dart';

/// Service for looking up and canonicalizing genres for music pins
class GenreLookupService {
  final SpotifyService _spotifyService;
  
  GenreLookupService(this._spotifyService);
  
  /// Get the canonical genre for an artist, trying Spotify first, then MusicBrainz as fallback
  /// Returns lowercase canonical genre string or null if no genre found
  Future<String?> getArtistGenre(String artistName) async {
    if (kDebugMode) {
      print('🎵 [GenreLookup] Looking up genre for artist: $artistName');
    }
    
    try {
      // Step 1: Try Spotify first (primary source)
      final spotifyGenre = await _getGenreFromSpotify(artistName);
      if (spotifyGenre != null) {
        final canonical = SpotifyGenreService.getCanonicalGenre(spotifyGenre);
        final result = canonical.toLowerCase();
        
        if (kDebugMode) {
          print('✅ [GenreLookup] Found Spotify genre for $artistName: $spotifyGenre -> $result');
        }
        return result;
      }
      
      // Step 2: Fallback to MusicBrainz
      if (kDebugMode) {
        print('🔄 [GenreLookup] Spotify failed, trying MusicBrainz for: $artistName');
      }
      
      final musicbrainzGenre = await _getGenreFromMusicBrainz(artistName);
      if (musicbrainzGenre != null) {
        final canonical = SpotifyGenreService.getCanonicalGenre(musicbrainzGenre);
        final result = canonical.toLowerCase();
        
        if (kDebugMode) {
          print('✅ [GenreLookup] Found MusicBrainz genre for $artistName: $musicbrainzGenre -> $result');
        }
        return result;
      }
      
      // Step 3: No genre found
      if (kDebugMode) {
        print('⚠️ [GenreLookup] No genre found for artist: $artistName');
      }
      return null;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [GenreLookup] Error looking up genre for $artistName: $e');
      }
      return null;
    }
  }
  
  /// Get genre from Spotify by searching for the artist
  Future<String?> _getGenreFromSpotify(String artistName) async {
    try {
      // Use the existing getMultipleArtistsByNames method which returns genres  
      final artistsData = await _spotifyService.getMultipleArtistsByNames([artistName]);
      
      if (artistsData.isNotEmpty) {
        final artistData = artistsData.first;
        final genres = artistData['genres'] as List<String>?;
        
        if (genres != null && genres.isNotEmpty) {
          // Return the first/primary genre
          return genres.first;
        }
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [GenreLookup] Spotify lookup failed for $artistName: $e');
      }
      return null;
    }
  }
  
  /// Get genre from MusicBrainz by getting artist genres
  Future<String?> _getGenreFromMusicBrainz(String artistName) async {
    try {
      final genres = await MusicBrainzService().getArtistGenres(artistName);
      
      if (genres.isNotEmpty) {
        // Find the most relevant genre
        // MusicBrainz genres are returned in relevance order
        for (final genre in genres) {
          final genreName = genre.toLowerCase();
          
          // Check if this is a recognized genre
          if (SpotifyGenreService.isCanonicalGenre(genreName) || 
              SpotifyGenreService.getCanonicalGenre(genreName) != genreName) {
            return genreName;
          }
        }
        
        // If no perfect match, return the first genre as a fallback
        return genres.first;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [GenreLookup] MusicBrainz lookup failed for $artistName: $e');
      }
      return null;
    }
  }
  
  /// Extract the main artist name from a potentially collaborative artist string
  /// e.g., "Drake feat. Rihanna" -> "Drake"
  String extractMainArtist(String artistName) {
    // Common collaboration indicators
    final collaborationPatterns = [
      ' feat. ', ' featuring ', ' ft. ', ' with ', ' vs. ', ' & ', ' and ',
      ' feat ', ' featuring ', ' ft ', ' with ', ' vs ', ' x '
    ];
    
    String cleanArtist = artistName.trim();
    
    // Find the first collaboration pattern and split there
    for (final pattern in collaborationPatterns) {
      final index = cleanArtist.toLowerCase().indexOf(pattern.toLowerCase());
      if (index != -1) {
        cleanArtist = cleanArtist.substring(0, index).trim();
        break;
      }
    }
    
    // Also handle parenthetical collaborations like "Song (feat. Artist)"
    final parenIndex = cleanArtist.indexOf('(');
    if (parenIndex != -1) {
      final beforeParen = cleanArtist.substring(0, parenIndex).trim();
      if (beforeParen.isNotEmpty) {
        cleanArtist = beforeParen;
      }
    }
    
    if (kDebugMode && cleanArtist != artistName.trim()) {
      print('🎤 [GenreLookup] Extracted main artist: "$artistName" -> "$cleanArtist"');
    }
    
    return cleanArtist;
  }
}
