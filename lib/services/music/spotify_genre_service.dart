import 'dart:convert';

/// Service for handling Spotify genre mappings and canonical genre resolution
class SpotifyGenreService {
  /// Get the canonical Spotify genres list (comprehensive)
  static final List<String> canonicalGenres = [
    // Core genres
    'pop', 'rap', 'hip hop', 'rock', 'electronic', 'dance', 'r&b', 'indie',
    'jazz', 'blues', 'country', 'folk', 'reggae', 'metal', 'punk', 'classical',
    'ambient', 'soul', 'funk', 'gospel', 'latin', 'world music',
    
    // Popular subgenres from your list
    'urbano latino', 'trap latino', 'dance pop', 'reggaeton', 'trap',
    'show tunes', 'atl hip hop', 'melodic rap', 'pop rap', 'canadian pop',
    'reggaeton colombiano', 'modern bollywood', 'broadway', 'colombian pop',
    'southern hip hop', 'edm', 'art pop', 'puerto rican pop',
    'uk pop',
    
    // Major traditional genres
    'acoustic', 'afrobeat', 'alt rock', 'alternative', 'ancient', 'angry',
    'anti folk', 'antiviral pop', 'arabic', 'argentina', 'afrikaans',
    'alt country', 'alternative rock', 'anime', 'bossanova', 'brazil',
    'breakbeat', 'british', 'chill', 'club', 'comedy', 'dancehall',
    'death metal', 'deep house', 'detroit techno', 'disco', 'drum and bass',
    'dub', 'dubstep', 'emo', 'forro', 'french', 'garage', 'german',
    'goth', 'grindcore', 'groove', 'grunge', 'guitar', 'happy',
    'hard rock', 'hardcore', 'hardstyle', 'heavy metal', 'house',
    'idm', 'indian', 'indie pop', 'industrial', 'iranian', 'j dance',
    'j idol', 'j pop', 'j rock', 'k pop', 'kids', 'latino', 'malay',
    'mandopop', 'metal misc', 'metalcore', 'minimal techno', 'movies',
    'mpb', 'new age', 'new release', 'opera', 'pagode', 'party',
    'philippines opm', 'piano', 'pop film', 'post dubstep', 'power pop',
    'progressive house', 'psych rock', 'punk rock', 'r n b', 'rainy day',
    'road trip', 'rock n roll', 'rockabilly', 'romance', 'sad', 'salsa',
    'samba', 'sertanejo', 'singer songwriter', 'ska', 'sleep', 'songwriter',
    'soundtracks', 'spanish', 'study', 'summer', 'surf', 'swing',
    'synth pop', 'tango', 'techno', 'trance', 'trip hop', 'turkish',
    'work out',

    // Extended popular genres
    'uk drill', 'chicago drill', 'phonk', 'hyperpop', 'bedroom pop', 'lo fi', 'lo fi hip hop',
    'chillhop', 'vaporwave', 'synthwave', 'future bass', 'melodic dubstep',
    'melodic bass', 'liquid bass', 'neurobass', 'bass music', 'jungle',
    'atmospheric dnb', 'liquid dnb', 'neurofunk', 'breakcore',
    'hardcore techno', 'gabber', 'happy hardcore', 'uk garage', 'grime',
    'brostep', 'riddim', 'future garage', 'ambient techno', 'dark ambient',
    'drone', 'post rock', 'math rock', 'shoegaze', 'dream pop',
    'indie rock', 'indie folk', 'folk punk', 'post punk', 'new wave',
    'darkwave', 'retrowave', 'chillwave', 'witch house', 'glitch',
    'glitch hop', 'experimental', 'noise', 'ebm', 'dark electro',
    'aggrotech', 'futurepop', 'cyberpunk', 'psytrance', 'full on',
    'progressive psytrance', 'dark psytrance', 'goa trance', 'acid trance',
    'uplifting trance', 'vocal trance', 'hard trance', 'tech trance',
    'progressive trance',
    
    // Modern hip hop/rap subgenres
    'emo rap', 'sad rap', 'cloud rap', 'mumble rap', 'conscious rap',
    'boom bap', 'old school hip hop', 'underground hip hop',
    'alternative hip hop', 'experimental hip hop', 'jazz rap', 'lo fi rap',
    'chill rap', 'abstract hip hop', 'hardcore hip hop', 'gangsta rap',
    'east coast hip hop', 'west coast hip hop', 'midwest hip hop',
    'uk hip hop', 'french hip hop', 'german hip hop',
    
    // Modern electronic subgenres
    'future funk', 'synthpop', 'electropop', 'indietronica', 'electroclash',
    'microhouse', 'minimal house', 'tech house', 'tribal house',
    'funky house', 'garage house', 'acid house', 'hard house',
    'uk hard house', 'speed garage', 'bassline', '2 step', 'liquid funk',
    'jump up', 'hardstep', 'darkstep', 'jazzstep', 'intelligent dnb',
    'minimal dnb', 'dancefloor dnb',
    
    // Modern pop subgenres
    'alt pop', 'electro pop', 'lo fi pop', 'chill pop', 'sad pop',
    'dark pop', 'experimental pop', 'noise pop', 'shoegaze pop',
    'chamber pop', 'baroque pop', 'psychedelic pop', 'garage pop',
    
    // Modern rock subgenres
    'post hardcore', 'deathcore', 'djent', 'progressive metalcore',
    'melodic hardcore', 'screamo', 'emocore', 'post emo', 'midwest emo',
    'emo revival', 'twinkly emo', 'skramz', 'blackgaze', 'doomgaze',
    'sludge metal', 'stoner metal', 'desert rock', 'space rock',
    'krautrock', 'noise rock', 'no wave', 'art rock',

    // Asian genres
    'j hip hop', 'j electronic', 'j indie', 'j metal', 'k rock',
    'k hip hop', 'k indie', 'k r&b', 'c pop', 'cantopop', 'chinese rock',
    'chinese hip hop', 't pop', 'thai pop', 'thai rock',

    // World music
    'african', 'afrobeats', 'amapiano', 'highlife', 'soukous', 'makossa',
    'merengue', 'bachata', 'cumbia', 'flamenco', 'fado', 'bossa nova',
    'forró', 'axé', 'rocksteady', 'middle eastern', 'greek', 'balkan',
    'celtic', 'irish', 'scottish', 'bluegrass', 'americana', 'outlaw country',
    'indian classical', 'bollywood', 'bhangra', 'qawwali',
  ];

  /// Get the main canonical genres suitable for user selection in onboarding
  static List<String> getMainGenresForSelection() {
    return [
      // Core genres
      'pop',
      'rock',
      'rap',
      'hip-hop',
      'electronic',
      'r&b',
      'indie',
      'jazz',
      'blues',
      'country',
      'folk',
      'reggae',
      'metal',
      'punk',
      'classical',
      'ambient',
      
      // Popular subgenres from your list
      'urbano latino',
      'trap latino',
      'dance pop',
      'reggaeton',
      'trap',
      'show tunes',
      'atl hip-hop',
      'melodic rap',
      'pop rap',
      'canadian pop',
      'reggaeton colombiano',
      'modern bollywood',
      'broadway',
      'colombian pop',
      'southern hip-hop',
      'edm',
      'art pop',
      'puerto rican pop',
      'uk pop',
      
      // Electronic subgenres
      'house',
      'techno',
      'dubstep',
      'trance',
      'drum-and-bass',
      'breakbeat',
      'progressive house',
      'deep house',
      'future bass',
      'melodic bass',
      'uk garage',
      'minimal techno',
      'acid house',
      'hardcore techno',
      'hardstyle',
      
      // Hip hop subgenres
      'uk drill',
      'chicago drill',
      'emo rap',
      'atl hip-hop',
      'conscious rap',
      'boom bap',
      'underground hip-hop',
      'cloud rap',
      'rage rap',
      'lo-fi hip-hop',
      'jazz rap',
      
      // Pop subgenres
      'indie pop',
      'electro pop',
      'synth pop',
      'dream pop',
      'bedroom pop',
      'alt pop',
      'dark pop',
      
      // Rock subgenres
      'alt rock',
      'indie rock',
      'post rock',
      'math rock',
      'shoegaze',
      'post punk',
      'new wave',
      'grunge',
      'hard rock',
      'heavy metal',
      'metalcore',
      'post hardcore',
      'emo',
      'screamo',
      'punk rock',
      'pop punk',
      
      // Chill/Lo fi genres
      'lo-fi',
      'chillout',
      'chillstep',
      'chillhop',
      'dark ambient',
      'drone',
      
      // Internet/Modern genres
      'synthwave',
      'vaporwave',
      'phonk',
      'hyperpop',
      'witch house',
      'seapunk',
      
      // World/Regional
      'anime',
      'k-pop',
      'j-pop',
      'c-pop',
      'mandopop',
      'latin',
      'afrobeat',
      'amapiano',
      'bossa nova',
      'cumbia',
      'salsa',
      'dancehall',
      'dub',
      'ska',
      
      // Other popular
      'dance',
      'alternative',
      'funk',
      'soul',
      'neo soul',
      'gospel',
      'swing',
      'fusion',
      'smooth jazz',
      'experimental',
      'industrial',
      'psytrance',
      'grime',
      'garage',
      'glitch',
      'idm',
      'noise',
      'indie folk',
      'folk rock',
      'gothic rock',
      'darkwave',
      'disco',
      'mariachi',
      'flamenco',
      'world music'
    ];
  }

  /// Non-musical tags to filter out from MusicBrainz results
  static final Set<String> nonMusicalTags = {
    // Languages
    'english', 'spanish', 'french', 'german', 'italian', 'portuguese',
    'japanese',
    'korean', 'chinese', 'russian', 'arabic', 'hindi', 'mandarin', 'cantonese',
    'englisch', 'deutsch', 'français', 'italiano', 'português', 'español',

    // Decades/Years/Time periods
    '1950s', '1960s', '1970s', '1980s', '1990s', '2000s', '2010s', '2020s',
    '50s', '60s', '70s', '80s', '90s', '00s', '10s', '20s',
    'fifties', 'sixties', 'seventies', 'eighties', 'nineties',
    'vintage', 'retro', 'classic', 'oldies', 'golden era',

    // Record labels
    'universal', 'sony', 'warner', 'emi', 'rca', 'columbia', 'atlantic',
    'capitol', 'def jam', 'island', 'interscope', 'geffen', 'epic',
    'hollywood records', 'republic records', 'virgin records',

    // Demographics/Identity
    'male', 'female', 'male vocalists', 'female vocalists', 'boy band',
    'girl group',
    'american', 'british', 'canadian', 'australian', 'uk', 'usa', 'canada',
    'england', 'scotland', 'ireland', 'wales', 'london', 'new york',
    'los angeles', 'nashville', 'detroit', 'chicago', 'atlanta',

    // Non-genre descriptors
    'popular', 'mainstream', 'underground', 'commercial', 'independent',
    'major label', 'indie label', 'unsigned', 'debut', 'comeback',
    'chart-topper', 'hit', 'single', 'album', 'ep', 'compilation',
    'live', 'acoustic', 'unplugged', 'remix', 'cover', 'tribute',
    'soundtrack', 'score', 'theme', 'instrumental version',

    // Personal/biographical
    'death by murder', 'deceased', 'alive', 'married', 'divorced',
    'father', 'mother', 'son', 'daughter', 'family', 'relatives',
    'childhood', 'teen', 'adult', 'senior', 'young', 'old',

    // Misc non-genre
    'radio', 'television', 'tv', 'film', 'movie', 'cinema', 'documentary',
    'interview', 'biography', 'autobiography', 'memoir', 'story',
    'news', 'politics', 'religion', 'spiritual', 'christian', 'catholic',
    'protest', 'social', 'environmental', 'political activism',
  };

  /// Comprehensive MusicBrainz to Spotify genre mapping
  static final Map<String, String> musicBrainzToSpotifyMapping = {
    // Electronic/Dance mappings
    'electronica': 'electronic',
    'electro house': 'house',
    'electro disco': 'disco',
    'electropop': 'electro pop',
    'progressive house': 'progressive house',
    'deep house': 'deep house',
    'tech house': 'house',
    'acid house': 'house',
    'chicago house': 'house',
    'detroit techno': 'techno',
    'minimal techno': 'minimal techno',
    'acid techno': 'techno',
    'hardcore techno': 'hardcore techno',
    'drum and bass': 'drum and bass',
    'liquid drum and bass': 'drum and bass',
    'neurofunk': 'drum and bass',
    'jungle': 'drum and bass',
    'uk garage': 'uk garage',
    '2 step': 'uk garage',
    'future garage': 'future garage',
    'bass music': 'bass music',
    'future bass': 'future bass',
    'melodic dubstep': 'melodic dubstep',
    'brostep': 'dubstep',
    'riddim': 'dubstep',
    'ambient techno': 'ambient techno',
    'dark ambient': 'dark ambient',
    'drone ambient': 'ambient',
    'downtempo': 'trip hop',
    'chillout': 'chill',
    'trip hop': 'trip hop',
    'breakbeat': 'breakbeat',
    'big beat': 'breakbeat',
    'trance': 'trance',
    'progressive trance': 'trance',
    'uplifting trance': 'trance',
    'vocal trance': 'trance',
    'psytrance': 'trance',
    'goa trance': 'trance',
    'acid trance': 'trance',
    'hard trance': 'trance',
    'tech trance': 'trance',
    'balearic trance': 'trance',

    // Pop/Dance Pop mappings
    'dance-pop': 'dance',
    'teen pop': 'pop',
    'bubblegum pop': 'pop',
    'power pop': 'power-pop',
    'art pop': 'art-pop',
    'dream pop': 'dream-pop',
    'bedroom pop': 'bedroom-pop',
    'indie pop': 'indie-pop',
    'synthpop': 'synth-pop',
    'synth pop': 'synth-pop',
    'new wave': 'new-wave',
    'post-punk': 'post-punk',
    'darkwave': 'darkwave',
    'chillwave': 'chillwave',
    'vaporwave': 'vaporwave',
    'synthwave': 'synthwave',
    'retrowave': 'synthwave',
    'outrun': 'synthwave',

    // Rock mappings
    'alternative rock': 'alt-rock',
    'indie rock': 'indie-rock',
    'post-rock': 'post-rock',
    'math rock': 'math-rock',
    'garage rock': 'garage-rock',
    'psychedelic rock': 'psych-rock',
    'prog rock': 'prog-rock',
    'progressive rock': 'prog-rock',
    'art rock': 'art-rock',
    'glam rock': 'glam-rock',
    'hard rock': 'hard-rock',
    'classic rock': 'rock',
    'arena rock': 'rock',
    'southern rock': 'southern-rock',
    'blues rock': 'blues-rock',
    'folk rock': 'folk-rock',
    'country rock': 'country',
    'surf rock': 'surf',
    'stoner rock': 'stoner-rock',
    'shoegaze': 'shoegaze',
    'neo-psychedelia': 'psych-rock',
    'post-britpop': 'britpop',
    'brit rock': 'britpop',
    'britpop': 'britpop',

    // Metal mappings
    'heavy metal': 'heavy-metal',
    'death metal': 'death-metal',
    'black metal': 'black-metal',
    'thrash metal': 'thrash-metal',
    'power metal': 'power-metal',
    'progressive metal': 'progressive-metal',
    'symphonic metal': 'symphonic-metal',
    'gothic metal': 'gothic-metal',
    'doom metal': 'doom-metal',
    'sludge metal': 'sludge-metal',
    'nu metal': 'nu-metal',
    'metalcore': 'metalcore',
    'deathcore': 'deathcore',
    'grindcore': 'grindcore',
    'post-metal': 'post-metal',
    'stoner metal': 'stoner-metal',
    'drone metal': 'drone-metal',

    // Hip-hop/Rap mappings
    'hip hop': 'hip-hop',
    'rap': 'hip-hop',
    'trap music': 'trap',
    'drill': 'uk drill', // Default to UK drill for generic drill
    'uk drill': 'uk drill',
    'chicago drill': 'chicago drill',
    'ny drill': 'uk drill', // NY drill is closer to UK drill style
    'bronx drill': 'uk drill', // Bronx drill is closer to UK drill style
    'boom bap': 'boom-bap',
    'conscious hip hop': 'conscious-rap',
    'gangsta rap': 'gangsta-rap',
    'mumble rap': 'trap',
    'cloud rap': 'cloud-rap',
    'abstract hip hop': 'abstract-hip-hop',
    'alternative hip hop': 'alternative-hip-hop',
    'underground hip hop': 'underground-hip-hop',
    'east coast hip hop': 'east-coast-hip-hop',
    'west coast hip hop': 'west-coast-hip-hop',
    'southern hip hop': 'southern-hip-hop',
    'crunk': 'crunk',
    'phonk': 'phonk',
    'memphis rap': 'phonk',

    // R&B/Soul mappings
    'contemporary r&b': 'r-n-b',
    'alternative r&b': 'alternative-r&b',
    'neo soul': 'neo-soul',
    'neo-soul': 'neo-soul',
    'smooth soul': 'soul',
    'northern soul': 'soul',
    'motown': 'soul',
    'funk': 'funk',
    'disco': 'disco',
    'boogie': 'boogie',
    'new jack swing': 'new-jack-swing',

    // Jazz mappings
    'smooth jazz': 'smooth-jazz',
    'bebop': 'bebop',
    'hard bop': 'bebop',
    'cool jazz': 'cool-jazz',
    'free jazz': 'free-jazz',
    'fusion': 'fusion',
    'jazz fusion': 'fusion',
    'contemporary jazz': 'contemporary-jazz',
    'latin jazz': 'latin-jazz',
    'acid jazz': 'acid-jazz',
    'nu jazz': 'nu-jazz',
    'swing': 'swing',
    'big band': 'big-band',

    // Punk mappings
    'punk rock': 'punk-rock',
    'hardcore punk': 'hardcore-punk',
    'pop punk': 'pop-punk',
    'ska punk': 'ska-punk',
    'folk punk': 'folk-punk',
    'crust punk': 'crust-punk',
    'anarcho-punk': 'anarcho-punk',
    'street punk': 'street-punk',
    'oi!': 'oi',
    'skate punk': 'skate-punk',

    // Folk/Country mappings
    'indie folk': 'indie-folk',
    'folk rock': 'folk-rock',
    'singer-songwriter': 'singer-songwriter',
    'americana': 'americana',
    'alt-country': 'alt-country',
    'alternative country': 'alt-country',
    'country rock': 'country-rock',
    'bluegrass': 'bluegrass',
    'honky tonk': 'honky-tonk',
    'outlaw country': 'outlaw-country',
    'country pop': 'country-pop',
    'nashville sound': 'country',
    'bakersfield sound': 'country',

    // World/Regional mappings
    'afrobeat': 'afrobeat',
    'afrobeats': 'afrobeats',
    'highlife': 'highlife',
    'soukous': 'soukous',
    'makossa': 'makossa',
    'reggae': 'reggae',
    'dancehall': 'dancehall',
    'dub': 'dub',
    'ska': 'ska',
    'rocksteady': 'rocksteady',
    'latin pop': 'latin-pop',
    'reggaeton': 'reggaeton',
    'salsa': 'salsa',
    'merengue': 'merengue',
    'bachata': 'bachata',
    'cumbia': 'cumbia',
    'tango': 'tango',
    'bossa nova': 'bossa-nova',
    'samba': 'samba',
    'forró': 'forro',
    'mpb': 'mpb',
    'pagode': 'pagode',
    'axé': 'axe',
    'flamenco': 'flamenco',
    'fado': 'fado',
    'celtic': 'celtic',
    'irish folk': 'irish',
    'scottish folk': 'scottish',
    'balkan': 'balkan',
    'klezmer': 'klezmer',
    'middle eastern': 'middle-eastern',
    'arabic music': 'arabic',
    'indian classical': 'indian-classical',
    'bollywood': 'bollywood',
    'bhangra': 'bhangra',
    'qawwali': 'qawwali',
    'gamelan': 'gamelan',

    // Asian pop mappings
    'j-pop': 'j-pop',
    'j-rock': 'j-rock',
    'j-dance': 'j-dance',
    'j-idol': 'j-idol',
    'k-pop': 'k-pop',
    'k-rock': 'k-rock',
    'k-hip hop': 'k-hip-hop',
    'c-pop': 'c-pop',
    'mandopop': 'mandopop',
    'cantopop': 'cantopop',
    't-pop': 't-pop',
    'thai pop': 't-pop',

    // Classical mappings
    'classical': 'classical',
    'baroque': 'classical',
    'romantic': 'classical',
    'modern classical': 'contemporary-classical',
    'contemporary classical': 'contemporary-classical',
    'minimalism': 'minimalist',
    'opera': 'opera',
    'orchestral': 'orchestral',
    'chamber music': 'chamber-music',
    'string quartet': 'chamber-music',
    'piano': 'piano',
    'solo piano': 'piano',

    // Experimental/Avant-garde mappings
    'experimental': 'experimental',
    'avant-garde': 'avant-garde',
    'noise': 'noise',
    'drone': 'drone',
    'ambient': 'ambient',
    'field recording': 'field-recording',
    'sound art': 'sound-art',
    'musique concrète': 'musique-concrete',
    'acousmatic': 'acousmatic',
    'electroacoustic': 'electroacoustic',

    // Blues mappings
    'chicago blues': 'chicago-blues',
    'delta blues': 'delta-blues',
    'electric blues': 'electric-blues',
    'acoustic blues': 'acoustic-blues',
    'texas blues': 'texas-blues',
    'blues rock': 'blues-rock',
    'rhythm and blues': 'r-n-b',

    // Gospel/Religious mappings
    'gospel': 'gospel',
    'contemporary gospel': 'contemporary-gospel',
    'southern gospel': 'southern-gospel',
    'traditional gospel': 'traditional-gospel',
    'christian rock': 'christian-rock',
    'christian hip hop': 'christian-hip-hop',
    'christian pop': 'christian-pop',
    'worship': 'worship',
    'hymns': 'hymns',

    // Reggae family mappings
    'roots reggae': 'reggae',
    'dub reggae': 'dub',
    'lovers rock': 'lovers-rock',
    'digital dancehall': 'dancehall',
    'ragga': 'ragga',
    'deejay': 'deejay',

    // Modern internet genres
    'witch house': 'witch-house',
    'seapunk': 'seapunk',
    'future funk': 'future-funk',
    'lo-fi house': 'lo-fi-house',
    'slushwave': 'slushwave',
    'mallsoft': 'mallsoft',
    'simpsonwave': 'simpsonwave',
    'hardvapour': 'hardvapour',
    'vaportrap': 'vaportrap',
    'hyperpop': 'hyperpop',
    'pc music': 'pc-music',
    'digicore': 'digicore',
    'breakcore': 'breakcore',
    'speedcore': 'speedcore',
    'extratone': 'extratone',
    'splittercore': 'splittercore',
  };

  /// Comprehensive genre alias mapping for better search results
  static final Map<String, List<String>> genreAliases = {
    // International genres with FULL expansion - PRIORITIZE expanded forms
    'j-pop': [
      'japanese pop',
      'japanese music',
      'japan pop',
      'jpop music',
      'j-pop',
      'jpop'
    ],
    'j-rock': [
      'japanese rock',
      'japanese rock music',
      'japan rock',
      'jrock music',
      'j-rock',
      'jrock'
    ],
    'j-hip-hop': [
      'japanese hip hop',
      'japanese rap',
      'japan hip hop',
      'j-hip-hop',
      'j-rap'
    ],

    // C-pop with FULL chinese expansion - PRIORITIZE "chinese pop" over "c-pop"
    'c-pop': [
      'chinese pop',
      'chinese music',
      'mandarin pop',
      'china pop',
      'china music',
      'mandopop',
      'c-pop',
      'cpop'
    ],
    'mandopop': [
      'mandarin pop',
      'chinese pop',
      'chinese music',
      'mandopop music',
      'mandopop',
      'mando pop'
    ],
    'cantopop': [
      'cantonese pop',
      'hong kong pop',
      'hong kong music',
      'cantopop',
      'canto pop'
    ],

    // K-pop with korean expansion - PRIORITIZE "korean pop" over "k-pop"
    'k-pop': [
      'korean pop',
      'korean music',
      'korea pop',
      'korea music',
      'kpop music',
      'k-pop',
      'kpop'
    ],
    'k-rock': [
      'korean rock',
      'korean rock music',
      'korea rock',
      'krock music',
      'k-rock',
      'krock'
    ],
    'k-hip-hop': [
      'korean hip hop',
      'korean rap',
      'korea hip hop',
      'k-hip-hop',
      'k-rap'
    ],

    // T-pop with thai expansion
    't-pop': [
      'thai pop',
      'thai music',
      'thailand pop',
      'thailand music',
      't-pop',
      'tpop'
    ],

    // Latin pop with spanish expansion
    'latin-pop': ['latin pop', 'latin music', 'spanish pop', 'latino pop'],

    // Hip-hop variants - STRICT separation
    'hip-hop': ['hip-hop', 'hip hop', 'hiphop', 'rap music'],
    'trap': ['trap', 'trap music', 'atlanta trap'], // NO electronic references
    'uk drill': ['uk drill', 'british drill', 'london drill', 'drill', 'drill rap'],
    'chicago drill': ['chicago drill', 'chicago drill music', 'drill', 'drill rap'],
    'boom-bap': ['boom-bap', 'boom bap', 'old school hip hop'],
    'conscious-rap': ['conscious rap', 'conscious hip hop', 'lyrical rap'],

    // Electronic genres - STRICT separation
    'edm': ['edm', 'electronic dance music', 'dance music'],
    'house': ['house', 'house music', 'chicago house'],
    'techno': ['techno', 'detroit techno', 'minimal techno'],
    'trance': ['trance', 'progressive trance', 'uplifting trance'],
    'drum-and-bass': ['drum and bass', 'dnb', 'd&b', 'jungle'],
    'dubstep': ['dubstep', 'dub step', 'uk dubstep'],
    'future-bass': [
      'future bass',
      'melodic dubstep',
      'emotional bass'
    ], // NO trap reference

    // Rock genres
    'alt-rock': ['alternative rock', 'alt rock', 'alternative'],
    'indie-rock': ['indie rock', 'independent rock'],
    'post-rock': ['post rock', 'instrumental rock'],
    'prog-rock': ['progressive rock', 'prog rock'],
    'psych-rock': ['psychedelic rock', 'psych rock'],
    'garage-rock': ['garage rock', 'garage punk'],

    // Pop variants - PURE pop only
    'pop': ['pop', 'pop music'], // NO international variants
    'indie-pop': ['indie pop', 'independent pop'],
    'synth-pop': ['synth pop', 'synthpop', 'new wave'],
    'dream-pop': ['dream pop', 'dreampop', 'ethereal pop'],
    'bedroom-pop': ['bedroom pop', 'lo-fi pop'],
    'electro-pop': ['electro pop', 'electropop', 'electronic pop'],

    // Chill genres
    'lo-fi': ['lo-fi', 'lofi', 'lo fi', 'low fidelity'],
    'lo-fi-hip-hop': [
      'lo-fi hip hop',
      'lofi hip hop',
      'study beats',
      'chill hop'
    ],
    'chillout': ['chillout', 'chill out', 'chill', 'ambient chill'],
    'downtempo': ['downtempo', 'trip hop', 'chillstep'],

    // R&B variants
    'r-n-b': ['r&b', 'rnb', 'rhythm and blues', 'contemporary r&b'],
    'neo-soul': ['neo soul', 'alternative r&b', 'future r&b'],

    // Metal genres
    'death-metal': ['death metal', 'extreme metal'],
    'black-metal': ['black metal', 'atmospheric black metal'],
    'heavy-metal': ['heavy metal', 'traditional metal'],
    'metalcore': ['metalcore', 'melodic metalcore'],
    'progressive-metal': ['progressive metal', 'prog metal'],

    // Punk variants
    'punk-rock': ['punk rock', 'punk'],
    'pop-punk': ['pop punk', 'skate punk'],
    'hardcore-punk': ['hardcore punk', 'hardcore'],
    'post-punk': ['post punk', 'new wave'],

    // World music
    'reggaeton': ['reggaeton', 'reggaetón', 'raggaeton', 'urban latino'],
    'afrobeats': ['afrobeats', 'afro beats', 'afropop'],
    'bossa-nova': ['bossa nova', 'brazilian jazz'],

    // Ambient and experimental
    'ambient': ['ambient', 'atmospheric', 'soundscape'],
    'dark-ambient': ['dark ambient', 'drone ambient'],
    'experimental': ['experimental', 'avant-garde', 'abstract'],
    'noise': ['noise', 'harsh noise', 'power electronics'],

    // Classical variants
    'classical': ['classical', 'classical music', 'orchestral'],
    'contemporary-classical': ['contemporary classical', 'modern classical'],
    'minimalist': ['minimalist', 'minimal music', 'reich'],

    // Jazz variants
    'smooth-jazz': ['smooth jazz', 'contemporary jazz'],
    'bebop': ['bebop', 'hard bop'],
    'fusion': ['jazz fusion', 'fusion', 'jazz rock'],
    'free-jazz': ['free jazz', 'avant-garde jazz'],

    // Country variants
    'alt-country': ['alt country', 'alternative country', 'americana'],
    'bluegrass': ['bluegrass', 'traditional bluegrass'],
    'outlaw-country': ['outlaw country', 'red dirt'],

    // Folk variants
    'indie-folk': ['indie folk', 'independent folk'],
    'folk-rock': ['folk rock', 'electric folk'],
    'singer-songwriter': ['singer songwriter', 'acoustic folk'],

    // Dance music
    'uk-garage': ['uk garage', '2-step', 'garage'],
    'breakbeat': ['breakbeat', 'big beat', 'nu skool breaks'],
    'hardstyle': ['hardstyle', 'euphoric hardstyle'],
    'gabber': ['gabber', 'hardcore techno', 'rotterdam'],

    // Modern internet genres
    'vaporwave': ['vaporwave', 'vapor wave', 'mallsoft'],
    'synthwave': ['synthwave', 'retrowave', 'outrun'],
    'phonk': ['phonk', 'memphis rap', 'drift phonk'],
    'hyperpop': ['hyperpop', 'pc music', 'digital hardcore'],
    'seapunk': ['seapunk', 'aquacore'],
    'witch-house': ['witch house', 'drag'],
  };

  /// Filter and translate MusicBrainz genres to Spotify-compatible genres
  static List<String> filterAndTranslateMusicBrainzGenres(
      List<String> musicBrainzGenres) {
    if (musicBrainzGenres.isEmpty) return [];

    final translatedGenres = <String>{};

    for (final genre in musicBrainzGenres) {
      final normalizedGenre = normalizeGenre(genre);

      // Skip non-musical tags
      if (nonMusicalTags.contains(normalizedGenre)) {
        continue;
      }

      // Try direct mapping first
      if (musicBrainzToSpotifyMapping.containsKey(normalizedGenre)) {
        translatedGenres.add(musicBrainzToSpotifyMapping[normalizedGenre]!);
        continue;
      }

      // Check if it's already a canonical Spotify genre
      if (canonicalGenres.contains(normalizedGenre)) {
        translatedGenres.add(normalizedGenre);
        continue;
      }

      // Try to find canonical equivalent through aliases
      final canonical = getCanonicalGenre(normalizedGenre);
      if (canonical != normalizedGenre && canonicalGenres.contains(canonical)) {
        translatedGenres.add(canonical);
        continue;
      }

      // For unmapped genres, only add if they look like music genres
      if (isLikelyMusicGenre(normalizedGenre)) {
        translatedGenres.add(normalizedGenre);
      }
    }

    // Sort by priority (more common genres first)
    final result = translatedGenres.toList();
    result.sort((a, b) => _getGenrePriority(a).compareTo(_getGenrePriority(b)));

    return result;
  }

  /// Check if a tag is likely a music genre
  static bool isLikelyMusicGenre(String tag) {
    final tagLower = tag.toLowerCase();

    // If it's in our non-musical set, definitely not a genre
    if (nonMusicalTags.contains(tagLower)) return false;

    // Common music genre endings/patterns
    final musicGenrePatterns = [
      'rock',
      'pop',
      'hop',
      'core',
      'wave',
      'step',
      'bass',
      'house',
      'techno',
      'trance',
      'metal',
      'punk',
      'folk',
      'jazz',
      'blues',
      'soul',
      'funk',
      'reggae',
      'country',
      'classical',
      'ambient',
      'electronic',
      'dance',
      'garage',
      'uk drill',
      'chicago drill',
      'trap',
      'phonk',
      'grime',
      'dubstep',
      'breakbeat'
    ];

    // Check if the tag contains common music genre patterns
    for (final pattern in musicGenrePatterns) {
      if (tagLower.contains(pattern)) return true;
    }

    // Check length - very short tags are often not genres
    if (tagLower.length < 3) return false;

    // Check for numbers (often non-genre)
    if (RegExp(r'\d').hasMatch(tagLower)) return false;

    // Check for common non-genre words
    final nonGenreWords = [
      'the',
      'and',
      'of',
      'in',
      'at',
      'on',
      'by',
      'for',
      'with'
    ];
    for (final word in nonGenreWords) {
      if (tagLower == word ||
          tagLower.startsWith('$word ') ||
          tagLower.endsWith(' $word')) {
        return false;
      }
    }

    // If it passes all filters, likely a genre
    return true;
  }

  /// Get priority score for genre sorting (lower = higher priority)
  static int _getGenrePriority(String genre) {
    // High priority genres (most common/important)
    const highPriority = {
      'pop': 1,
      'rock': 2,
      'hip-hop': 3,
      'electronic': 4,
      'dance': 5,
      'house': 6,
      'techno': 7,
      'dubstep': 8,
      'trance': 9,
      'edm': 10,
      'indie': 11,
      'alternative': 12,
      'r-n-b': 13,
      'soul': 14,
      'jazz': 15,
      'funk': 16,
      'blues': 17,
      'country': 18,
      'folk': 19,
      'reggae': 20,
      'metal': 21,
      'punk': 22,
      'classical': 23,
      'ambient': 24,
      'trap': 25,
    };

    if (highPriority.containsKey(genre)) {
      return highPriority[genre]!;
    }

    // Medium priority for subgenres
    if (genre.contains('-') || genre.contains(' ')) {
      return 50;
    }

    // Low priority for everything else
    return 100;
  }

  /// Get all possible aliases for a genre
  static List<String> getGenreAliases(String genre) {
    final normalizedGenre = normalizeGenre(genre);

    // Check direct mapping first
    if (genreAliases.containsKey(normalizedGenre)) {
      return genreAliases[normalizedGenre]!;
    }

    // Check if the genre appears in any alias list
    for (final entry in genreAliases.entries) {
      if (entry.value.contains(normalizedGenre)) {
        return entry.value;
      }
    }

    // If no aliases found, return the genre itself and some basic variations
    return [
      genre,
      normalizedGenre,
      genre.replaceAll('-', ' '),
      genre.replaceAll(' ', '-')
    ];
  }

  /// Normalize genre string for consistent matching
  static String normalizeGenre(String genre) {
    return genre
        .toLowerCase()
        .trim()
        .replaceAll(RegExp(r'[^a-z0-9\s\-&]'), '')
        .replaceAll(RegExp(r'\s+'), ' ');
  }

  /// Check if a genre is canonical (exists in Spotify's official list)
  static bool isCanonicalGenre(String genre) {
    final normalized = normalizeGenre(genre);
    return canonicalGenres.contains(normalized) ||
        genreAliases.containsKey(normalized);
  }

  /// Get the canonical form of a genre
  static String getCanonicalGenre(String genre) {
    final normalized = normalizeGenre(genre);

    // Check if it's already canonical
    if (canonicalGenres.contains(normalized)) {
      return normalized;
    }

    // Check aliases
    for (final entry in genreAliases.entries) {
      if (entry.value.contains(normalized)) {
        return entry.key;
      }
    }

    // Return normalized version if not found
    return normalized;
  }

  /// Get related genres for better recommendations
  static List<String> getRelatedGenres(String genre) {
    final canonical = getCanonicalGenre(genre);

    final relatedMap = <String, List<String>>{
      // Hip-hop family - STRICTLY separated from electronic
      'hip-hop': [
        'rap',
        'trap',
        'uk drill',
        'chicago drill',
        'boom-bap',
        'conscious-rap',
        'gangsta-rap',
        'mumble-rap'
      ],
      'rap': [
        'hip-hop',
        'trap',
        'uk drill',
        'chicago drill',
        'boom-bap',
        'conscious-rap',
        'gangsta-rap'
      ],
      'trap': [
        'hip-hop',
        'rap',
        'uk drill',
        'chicago drill',
        'phonk',
        'mumble-rap'
      ], // REMOVED future-bass
      'uk drill': ['hip-hop', 'rap', 'grime', 'uk hip-hop'],
      'chicago drill': ['hip-hop', 'rap', 'trap', 'gangsta-rap'],

      // Electronic family - STRICTLY separated from hip-hop
      'edm': [
        'house',
        'techno',
        'trance',
        'dubstep',
        'electro-pop',
        'electronic'
      ],
      'electronic': ['edm', 'house', 'techno', 'trance', 'dubstep', 'ambient'],
      'house': [
        'edm',
        'techno',
        'uk-garage',
        'deep-house',
        'progressive-house'
      ],
      'techno': [
        'house',
        'edm',
        'industrial',
        'minimal-techno',
        'hardcore-techno'
      ],
      'dubstep': [
        'edm',
        'future-bass',
        'drum-and-bass',
        'garage',
        'melodic-dubstep'
      ],
      'future-bass': [
        'dubstep',
        'melodic-dubstep',
        'bass-music',
        'edm'
      ], // REMOVED trap
      'trance': [
        'edm',
        'progressive-house',
        'ambient',
        'psytrance',
        'uplifting-trance'
      ],
      'drum-and-bass': ['jungle', 'breakbeat', 'uk-garage', 'neurofunk'],

      // Rock family
      'rock': [
        'alt-rock',
        'indie-rock',
        'garage-rock',
        'post-rock',
        'classic-rock'
      ],
      'alt-rock': ['indie-rock', 'grunge', 'post-punk', 'garage-rock'],
      'indie-rock': ['alt-rock', 'indie-pop', 'post-rock', 'garage-rock'],
      'metal': ['heavy-metal', 'death-metal', 'metalcore', 'black-metal'],

      // Pop family - STRICT boundaries
      'pop': ['indie-pop', 'synth-pop', 'electro-pop', 'dance-pop'],
      'indie-pop': ['pop', 'indie-rock', 'dream-pop', 'bedroom-pop'],
      'synth-pop': ['pop', 'new-wave', 'electro-pop', 'synthwave'],

      // International pop - each strictly separate
      'k-pop': ['korean-pop'], // Only k-pop related
      'j-pop': ['japanese-pop'], // Only j-pop related
      'c-pop': ['chinese-pop', 'mandopop', 'cantopop'], // Only c-pop related
      't-pop': ['thai-pop'], // Only t-pop related
      'latin-pop': ['reggaeton', 'latin'], // Latin family only

      // Chill family
      'lo-fi': ['lo-fi-hip-hop', 'chillout', 'ambient', 'downtempo'],
      'ambient': ['chillout', 'drone', 'dark-ambient', 'new-age'],
      'chillout': ['ambient', 'downtempo', 'lo-fi', 'trip-hop'],

      // R&B family
      'r-n-b': ['neo-soul', 'soul', 'funk'],
      'soul': ['r-n-b', 'funk', 'gospel', 'neo-soul'],
      'funk': ['soul', 'r-n-b', 'disco', 'dance'],

      // Jazz family
      'jazz': ['smooth-jazz', 'fusion', 'bebop', 'neo-soul'],
      'fusion': ['jazz', 'funk', 'prog-rock', 'experimental'],

      // Folk/Country family
      'folk': ['indie-folk', 'folk-rock', 'singer-songwriter', 'americana'],
      'country': ['alt-country', 'americana', 'bluegrass', 'country-rock'],
      'americana': ['country', 'folk', 'alt-country', 'indie-folk'],

      // Punk family
      'punk': ['punk-rock', 'post-punk', 'hardcore-punk', 'pop-punk'],
      'post-punk': ['punk', 'new-wave', 'alt-rock', 'indie-rock'],

      // World music
      'reggae': ['dancehall', 'dub', 'ska'],
      'latin': ['reggaeton', 'salsa', 'bossa-nova', 'latin-pop'],
      'afrobeats': ['afrobeat', 'dancehall', 'world-music'],
    };

    return relatedMap[canonical] ?? [];
  }

  /// Check if two genres are related
  static bool areGenresRelated(String genre1, String genre2) {
    final canonical1 = getCanonicalGenre(genre1);
    final canonical2 = getCanonicalGenre(genre2);

    if (canonical1 == canonical2) return true;

    final related1 = getRelatedGenres(canonical1);
    final related2 = getRelatedGenres(canonical2);

    return related1.contains(canonical2) || related2.contains(canonical1);
  }

  /// Filter track titles and descriptions to avoid genre confusion
  static bool isGenreRelevantText(String text, String genre) {
    final textLower = text.toLowerCase();
    final aliases = getGenreAliases(genre);

    // Check for explicit genre mentions
    for (final alias in aliases) {
      if (textLower.contains(alias.toLowerCase())) {
        return true;
      }
    }

    // Check for related genre keywords
    final relatedGenres = getRelatedGenres(genre);
    for (final related in relatedGenres) {
      final relatedAliases = getGenreAliases(related);
      for (final alias in relatedAliases) {
        if (textLower.contains(alias.toLowerCase())) {
          return true;
        }
      }
    }

    return false;
  }

  /// Validate if an artist actually matches the target genre based on their Spotify genres
  static bool validateArtistForGenre(
      List<String> artistGenres, String targetGenre) {
    if (artistGenres.isEmpty) return false;

    final targetCanonical = getCanonicalGenre(targetGenre);
    final targetRelated = getRelatedGenres(targetCanonical);
    final allValidGenres = {targetCanonical, ...targetRelated};

    // Check if any of the artist's genres match the target or related genres
    for (final artistGenre in artistGenres) {
      final canonicalArtistGenre = getCanonicalGenre(artistGenre);

      // Direct match
      if (allValidGenres.contains(canonicalArtistGenre)) {
        return true;
      }

      // Check if genres are related
      if (areGenresRelated(canonicalArtistGenre, targetCanonical)) {
        return true;
      }
    }

    return false;
  }

  /// Get genre incompatibility rules (genres that should never be mixed)
  static bool areGenresIncompatible(String genre1, String genre2) {
    final g1 = getCanonicalGenre(genre1);
    final g2 = getCanonicalGenre(genre2);

    // Define incompatible genre groups
    final incompatibleGroups = <Set<String>, Set<String>>{
      // Classical vs Modern Electronic
      {'classical', 'opera', 'orchestral', 'chamber-music'}: {
        'edm',
        'house',
        'techno',
        'dubstep',
        'trap',
        'electronic'
      },

      // Traditional Folk/Country vs Electronic Dance
      {'folk', 'bluegrass', 'country', 'americana'}: {
        'edm',
        'house',
        'techno',
        'dubstep',
        'electronic'
      },

      // Jazz vs Hard Electronic
      {'jazz', 'smooth-jazz', 'bebop', 'swing'}: {
        'dubstep',
        'hardstyle',
        'gabber',
        'hardcore-techno'
      },

      // Classic Rock vs Electronic Dance
      {'classic-rock', 'blues-rock', 'southern-rock'}: {
        'edm',
        'house',
        'techno',
        'trance'
      },

      // Traditional Pop vs Extreme Genres
      {'traditional-pop', 'easy-listening', 'crooner'}: {
        'death-metal',
        'black-metal',
        'grindcore',
        'noise'
      },

      // Gospel/Religious vs Explicit Genres
      {'gospel', 'christian', 'hymns', 'worship'}: {
        'gangsta-rap',
        'explicit-rap',
        'hardcore-punk'
      },
    };

    // Check each incompatible group
    for (final group in incompatibleGroups.entries) {
      final group1 = group.key;
      final group2 = group.value;

      if ((group1.contains(g1) && group2.contains(g2)) ||
          (group1.contains(g2) && group2.contains(g1))) {
        return true;
      }
    }

    return false;
  }

  /// Genre families for high-level categorization
  static final Map<String, Set<String>> _genreFamilies = {
    'electronic': {
      'electronic',
      'edm',
      'house',
      'techno',
      'trance',
      'dubstep',
      'drum-and-bass',
      'ambient',
      'electronica',
      'electro',
      'synthpop',
      'synthwave',
      'chillwave',
      'future-bass',
      'bass-music',
      'uk-garage',
      'breakbeat',
      'downtempo',
      'trip-hop',
      'idm',
      'glitch',
      'minimal-techno',
      'progressive-house',
      'deep-house',
      'tech-house',
      'acid-house',
      'hardcore-techno',
      'gabber',
      'hardstyle',
      'psytrance',
      'goa-trance',
      'dark-ambient',
      'drone',
      'industrial',
      'ebm',
      'aggrotech',
      'futurepop',
      'darkwave',
      'new-wave',
      'synth-pop',
      'electro-pop',
      'dance',
      'dance-pop',
      'euro-dance',
      'hi-nrg',
      'disco',
      'funk',
      'boogie',
      'freestyle'
    },
    'rock': {
      'rock',
      'alt-rock',
      'indie-rock',
      'hard-rock',
      'classic-rock',
      'progressive-rock',
      'psychedelic-rock',
      'garage-rock',
      'post-rock',
      'math-rock',
      'art-rock',
      'glam-rock',
      'southern-rock',
      'blues-rock',
      'folk-rock',
      'country-rock',
      'surf-rock',
      'stoner-rock',
      'grunge',
      'punk',
      'punk-rock',
      'pop-punk',
      'hardcore-punk',
      'post-punk',
      'metal',
      'heavy-metal',
      'death-metal',
      'black-metal',
      'metalcore'
    },
    'hip_hop': {
      'hip-hop',
      'hip hop',  // Added: support both space and hyphen versions
      'rap',
      'trap',
      'uk drill',
      'chicago drill',
      'boom-bap',
      'boom bap',  // Added: space version
      'conscious-rap',
      'conscious rap',  // Added: space version  
      'gangsta-rap',
      'underground-hip-hop',
      'underground hip hop',  // Added: space version
      'alternative-hip-hop', 
      'alternative hip hop',  // Added: space version
      'east-coast-hip-hop',
      'east coast hip hop',  // Added: space version
      'west-coast-hip-hop',
      'west coast hip hop',  // Added: space version
      'southern-hip-hop',
      'southern hip hop',  // Added: space version
      'crunk',
      'phonk',
      'cloud-rap',
      'cloud rap',  // Added: support both space and hyphen versions
      'mumble-rap',
      'mumble rap',  // Added: space version
      'abstract-hip-hop',
      'abstract hip hop'  // Added: space version
    },
    'pop': {
      'pop',
      'indie-pop',
      'electro-pop',
      'synth-pop',
      'dance-pop',
      'teen-pop',
      'power-pop',
      'art-pop',
      'dream-pop',
      'bedroom-pop',
      'k-pop',
      'j-pop',
      'c-pop',
      'latin-pop',
      'country-pop'
    },
    'rnb_soul': {
      'r-n-b',
      'r&b',  // Added: ampersand version from canonical list
      'soul',
      'funk',
      'neo-soul',
      'contemporary-r&b',
      'contemporary r&b',  // Added: space version
      'motown',
      'gospel',
      'blues',
      'rhythm-and-blues',
      'rhythm and blues',  // Added: space version
      'quiet-storm',
      'lovers-rock'
    },
    'jazz': {
      'jazz',
      'smooth-jazz',
      'bebop',
      'hard-bop',
      'cool-jazz',
      'free-jazz',
      'fusion',
      'jazz-fusion',
      'contemporary-jazz',
      'latin-jazz',
      'acid-jazz',
      'nu-jazz',
      'swing',
      'big-band'
    },
    'folk_country': {
      'folk',
      'country',
      'americana',
      'bluegrass',
      'indie-folk',
      'singer-songwriter',
      'acoustic',
      'alt-country',
      'outlaw-country',
      'honky-tonk',
      'nashville-sound',
      'bakersfield-sound',
      'celtic',
      'irish-folk',
      'scottish-folk'
    },
    'classical': {
      'classical',
      'opera',
      'baroque',
      'romantic',
      'contemporary-classical',
      'minimalist',
      'orchestral',
      'chamber-music',
      'string-quartet',
      'piano',
      'solo-piano',
      'choral',
      'avant-garde',
      'experimental'
    },
    'world': {
      'reggae',
      'dancehall',
      'dub',
      'ska',
      'rocksteady',
      'latin',
      'salsa',
      'merengue',
      'bachata',
      'cumbia',
      'tango',
      'flamenco',
      'fado',
      'bossa-nova',
      'samba',
      'forro',
      'mpb',
      'pagode',
      'axe',
      'balkan',
      'klezmer',
      'middle-eastern',
      'arabic',
      'turkish',
      'greek',
      'indian-classical',
      'bollywood',
      'bhangra',
      'qawwali',
      'gamelan',
      'j-idol'
    },
    'asian': {
      // Japanese music
      'j-pop',
      'j-rock', 
      'j-hip-hop',
      'j-electronic',
      'japanese',
      
      // Korean music  
      'k-pop',
      'k-rock',
      'k-hip-hop', 
      'k-indie',
      'k-r&b',
      'korean',
      
      // Chinese music
      'c-pop',
      'chinese pop',
      'mandarin',
      'mandopop',
      'cantopop',
      'chinese',
      'chinese rock',
      'chinese hip hop',
      
      // Other Asian
      't-pop',
      'thai pop',
      'thai rock',
      'bollywood',  // Also in world, but Asian context too
      'indian',
    },
    'afrobeats': {
      // Nigerian pop music (upbeat, pop-influenced)
      'afrobeats',
      'afro beats',
      'nigerian pop',
      'nigerian music',
      'afropop',
      'highlife',    // Ghanaian/Nigerian
      'soukous',     // Central/East African
      'makossa',     // Cameroonian
      'african pop',
    },
    'amapiano': {
      // South African house/electronic music (piano-driven, deep house)
      'amapiano',
      'south african house',
      'gqom',        // South African electronic
      'kwaito',      // South African house
      'afro house',  // South African house variant
      'south african music',
    }
  };

  /// Get the family of a given genre
  static String? getGenreFamily(String genre) {
    final canonical = getCanonicalGenre(genre);
    for (final family in _genreFamilies.entries) {
      if (family.value.contains(canonical)) {
        return family.key;
      }
    }
    return null;
  }

  /// Get a more nuanced validation score for an artist against a target genre.
  /// Positive score = match, zero or negative = mismatch.
  static int getGenreValidationScore(
      List<String> artistGenres, String targetGenre) {
    if (artistGenres.isEmpty) return -1;

    final targetFamily = getGenreFamily(targetGenre);
    if (targetFamily == null) {
      // If target genre has no family, we can't reliably score it.
      // Fallback to a simple relatedness check.
      return areGenresRelated(artistGenres.first, targetGenre) ? 1 : -1;
    }

    int score = 0;
    final artistFamilies = artistGenres
        .map((g) => getGenreFamily(g))
        .where((f) => f != null)
        .toSet();

    if (artistFamilies.isEmpty) return -1;

    // Define family relationships for scoring
    const compatibleFamilies = {
      'electronic': {'pop', 'hip_hop', 'rnb_soul', 'funk'},
      'rock': {'folk_country', 'blues', 'punk', 'pop'},
      'hip_hop': {'rnb_soul', 'pop', 'electronic', 'jazz'},
      'pop': {'electronic', 'rnb_soul', 'hip_hop', 'rock'},
      'rnb_soul': {'hip_hop', 'pop', 'jazz', 'blues'},
      'jazz': {'rnb_soul', 'blues', 'classical'},
      'folk_country': {'rock', 'blues', 'pop'},
    };

    const incompatibleFamilies = {
      'electronic': {'classical', 'folk_country'},
      'rock': {'electronic', 'hip_hop'},
      'hip_hop': {'rock', 'classical', 'folk_country'},
      'classical': {'hip_hop', 'rock', 'pop'},
    };

    // Calculate score
    for (final family in artistFamilies) {
      if (family == targetFamily) {
        score += 5; // Strong positive score for a direct family match
      } else if (compatibleFamilies[targetFamily]?.contains(family) ?? false) {
        score += 1; // Small positive score for a compatible family
      } else if (incompatibleFamilies[targetFamily]?.contains(family) ??
          false) {
        score -= 10; // Strong negative penalty for an incompatible family
      }
    }

    // If the artist has genres from the target family, ensure the score is positive.
    if (artistFamilies.contains(targetFamily) && score <= 0) {
      score = 1; // Ensure it's at least a weak match
    }

    // If there's no direct family match, the score must be positive from compatible families.
    if (!artistFamilies.contains(targetFamily) && score <= 0) {
      return -1;
    }

    return score;
  }

  /// NEW: Get the broad category for a genre to allow for fuzzy matching
  static String getBroadCategory(String genre) {
    final canonical = getCanonicalGenre(genre);
    for (final family in _genreFamilies.entries) {
      if (family.value.contains(canonical)) {
        return family.key;
      }
    }
    return 'Unknown';
  }

  /// Enhanced genre validation for specific target genres
  static bool isValidArtistForTargetGenre(
      List<String> artistGenres, String targetGenre) {
    final target = getCanonicalGenre(targetGenre);

    switch (target) {
      case 'edm':
      case 'electronic':
      case 'house':
      case 'techno':
      case 'trance':
      case 'dubstep':
      case 'future-bass':
        return isValidElectronicArtist(artistGenres);

      case 'hip-hop':
      case 'rap':
      case 'trap':
      case 'uk drill':
      case 'chicago drill':
        return _isValidHipHopArtist(artistGenres);

      case 'rock':
      case 'alt-rock':
      case 'indie-rock':
        return _isValidRockArtist(artistGenres);

      case 'pop':
        return _isValidPurePopArtist(artistGenres); // Strict pop only

      case 'k-pop':
        return _isValidKPopArtist(artistGenres);

      case 'j-pop':
        return _isValidJPopArtist(artistGenres);

      case 'c-pop':
      case 'mandopop':
      case 'cantopop':
        return _isValidCPopArtist(artistGenres);

      case 'latin-pop':
        return _isValidLatinPopArtist(artistGenres);

      default:
        // For other genres, use the general validation with strict family rules
        return validateArtistForGenreStrict(artistGenres, targetGenre);
    }
  }

  /// NEW: Strict validation that prevents cross-family contamination
  static bool validateArtistForGenreStrict(
      List<String> artistGenres, String targetGenre) {
    if (artistGenres.isEmpty) return false;

    final targetCanonical = getCanonicalGenre(targetGenre);
    final targetFamily = getGenreFamily(targetCanonical);

    if (targetFamily == null) {
      // Fallback to basic validation for unknown genres
      return validateArtistForGenre(artistGenres, targetGenre);
    }

    // Get artist families
    final artistFamilies = artistGenres
        .map((g) => getGenreFamily(getCanonicalGenre(g)))
        .where((f) => f != null)
        .toSet();

    if (artistFamilies.isEmpty) return false;

    // STRICT rule: Artist must have at least one genre from the target family
    if (!artistFamilies.contains(targetFamily)) {
      print(
          '❌ [GenreValidation] Artist families $artistFamilies do not contain target family "$targetFamily" for genre "$targetGenre"');
      return false;
    }

    // Check for incompatible families (stronger rules)
    const strictIncompatibilities = {
      'hip_hop': {'electronic', 'classical', 'folk_country'},
      'electronic': {'hip_hop', 'classical', 'folk_country'},
      'pop': {
        'hip_hop',
        'electronic'
      }, // Pure pop separate from hip-hop and electronic
      'classical': {'hip_hop', 'electronic', 'rock'},
      'folk_country': {'hip_hop', 'electronic'},
    };

    final incompatibleFamilies = strictIncompatibilities[targetFamily] ?? {};
    final hasIncompatible = artistFamilies.any(incompatibleFamilies.contains);

    if (hasIncompatible) {
      print(
          '❌ [GenreValidation] Artist has incompatible family for target "$targetGenre": artist families $artistFamilies, incompatible: $incompatibleFamilies');
      return false;
    }

    print(
        '✅ [GenreValidation] Artist validated for "$targetGenre": families $artistFamilies, target family "$targetFamily"');
    return true;
  }

  /// Validate hip-hop artists - STRICT separation from electronic
  static bool _isValidHipHopArtist(List<String> artistGenres) {
    final hipHopGenres = {
      'hip-hop',
      'rap',
      'trap',
      'uk drill',
      'chicago drill',
      'boom-bap',
      'conscious-rap',
      'gangsta-rap',
      'underground-hip-hop',
      'alternative-hip-hop',
      'east-coast-hip-hop',
      'west-coast-hip-hop',
      'southern-hip-hop',
      'crunk',
      'phonk',
      'cloud-rap',
      'mumble-rap',
      'abstract-hip-hop'
    };

    // STRICT incompatibility list - NO electronic genres allowed
    final incompatibleGenres = {
      'electronic',
      'edm',
      'house',
      'techno',
      'trance',
      'dubstep',
      'future-bass',
      'drum-and-bass',
      'uk-garage',
      'bass-music',
      'ambient',
      'electronica',
      'country',
      'folk',
      'classical',
      'opera',
      'traditional-jazz',
      'smooth-jazz',
      'easy-listening',
      'crooner',
      'gospel-traditional'
    };

    final canonicalGenres = artistGenres.map(getCanonicalGenre).toSet();

    final hasHipHop = canonicalGenres.any(hipHopGenres.contains);
    final hasIncompatible = canonicalGenres.any(incompatibleGenres.contains);

    if (hasIncompatible) {
      print(
          '❌ [HipHopValidation] Artist has incompatible genres: $canonicalGenres, incompatible found: ${canonicalGenres.intersection(incompatibleGenres)}');
    }

    return hasHipHop && !hasIncompatible;
  }

  /// Validate pure pop artists - separate from international variants
  static bool _isValidPurePopArtist(List<String> artistGenres) {
    final purePopGenres = {
      'pop',
      'indie-pop',
      'electro-pop',
      'synth-pop',
      'dance-pop',
      'teen-pop',
      'power-pop',
      'art-pop',
      'dream-pop',
      'bedroom-pop'
    };

    // Exclude international pop variants to keep pure pop separate
    final incompatibleGenres = {
      'k-pop',
      'j-pop',
      'c-pop',
      'latin-pop',
      'country-pop',
      'mandopop',
      'cantopop',
      'hip-hop',
      'rap',
      'trap',
      'uk drill',
      'chicago drill',
      'electronic',
      'edm',
      'house',
      'techno'
    };

    final canonicalGenres = artistGenres.map(getCanonicalGenre).toSet();

    final hasPurePop = canonicalGenres.any(purePopGenres.contains);
    final hasIncompatible = canonicalGenres.any(incompatibleGenres.contains);

    return hasPurePop && !hasIncompatible;
  }

  /// Validate K-pop artists - ONLY K-pop
  static bool _isValidKPopArtist(List<String> artistGenres) {
    final kpopGenres = {'k-pop', 'korean-pop'};
    final canonicalGenres = artistGenres.map(getCanonicalGenre).toSet();

    // Must have K-pop genre and NO other pop variants
    final hasKPop = canonicalGenres.any(kpopGenres.contains);
    final hasOtherPop =
        canonicalGenres.any({'j-pop', 'c-pop', 'latin-pop', 'pop'}.contains);

    return hasKPop && !hasOtherPop;
  }

  /// Validate J-pop artists - ONLY J-pop
  static bool _isValidJPopArtist(List<String> artistGenres) {
    final jpopGenres = {'j-pop', 'japanese-pop'};
    final canonicalGenres = artistGenres.map(getCanonicalGenre).toSet();

    // Must have J-pop genre and NO other pop variants
    final hasJPop = canonicalGenres.any(jpopGenres.contains);
    final hasOtherPop =
        canonicalGenres.any({'k-pop', 'c-pop', 'latin-pop', 'pop'}.contains);

    return hasJPop && !hasOtherPop;
  }

  /// Validate C-pop artists - ONLY C-pop variants
  static bool _isValidCPopArtist(List<String> artistGenres) {
    final cpopGenres = {'c-pop', 'chinese-pop', 'mandopop', 'cantopop'};
    final canonicalGenres = artistGenres.map(getCanonicalGenre).toSet();

    // Must have C-pop genre and NO other pop variants
    final hasCPop = canonicalGenres.any(cpopGenres.contains);
    final hasOtherPop =
        canonicalGenres.any({'k-pop', 'j-pop', 'latin-pop', 'pop'}.contains);

    return hasCPop && !hasOtherPop;
  }

  /// Validate Latin pop artists - ONLY Latin variants
  static bool _isValidLatinPopArtist(List<String> artistGenres) {
    final latinPopGenres = {'latin-pop', 'reggaeton', 'latin'};
    final canonicalGenres = artistGenres.map(getCanonicalGenre).toSet();

    // Must have Latin genre and NO other pop variants
    final hasLatinPop = canonicalGenres.any(latinPopGenres.contains);
    final hasOtherPop =
        canonicalGenres.any({'k-pop', 'j-pop', 'c-pop', 'pop'}.contains);

    return hasLatinPop && !hasOtherPop;
  }

  /// Enhanced electronic validation - STRICT separation from hip-hop
  static bool isValidElectronicArtist(List<String> artistGenres) {
    final electronicGenres = {
      'electronic',
      'edm',
      'house',
      'techno',
      'trance',
      'dubstep',
      'drum-and-bass',
      'ambient',
      'electronica',
      'electro',
      'synthpop',
      'synthwave',
      'chillwave',
      'future-bass',
      'bass-music',
      'uk-garage',
      'breakbeat',
      'downtempo',
      'trip-hop',
      'idm',
      'glitch',
      'minimal-techno',
      'progressive-house',
      'deep-house',
      'tech-house',
      'acid-house',
      'hardcore-techno',
      'gabber',
      'hardstyle',
      'psytrance',
      'goa-trance',
      'dark-ambient',
      'drone',
      'industrial',
      'ebm',
      'aggrotech',
      'futurepop',
      'darkwave',
      'new-wave',
      'synth-pop',
      'electro-pop',
      'dance',
      'dance-pop',
      'euro-dance',
      'hi-nrg',
      'disco',
      'funk',
      'boogie',
      'freestyle'
    };

    // STRICT incompatibility - NO hip-hop allowed
    final incompatibleGenres = {
      'hip-hop',
      'rap',
      'trap',
      'uk drill',
      'chicago drill',
      'boom-bap',
      'conscious-rap',
      'gangsta-rap',
      'country',
      'bluegrass',
      'folk',
      'americana',
      'singer-songwriter',
      'classical',
      'opera',
      'orchestral',
      'chamber-music',
      'baroque',
      'jazz',
      'swing',
      'bebop',
      'traditional-jazz',
      'smooth-jazz',
      'gospel',
      'hymns',
      'traditional',
      'world-music',
      'ethnic',
      'celtic',
      'irish',
      'scottish',
      'middle-eastern',
      'indian-classical',
      'flamenco',
      'tango',
      'mariachi',
      'polka',
      'traditional-pop',
      'crooner',
      'easy-listening',
      'lounge',
      'cabaret',
      'musical-theatre'
    };

    final canonicalGenres = artistGenres.map(getCanonicalGenre).toSet();

    final hasElectronic = canonicalGenres.any(electronicGenres.contains);
    final hasIncompatible = canonicalGenres.any(incompatibleGenres.contains);

    if (hasIncompatible) {
      print(
          '❌ [ElectronicValidation] Artist has incompatible genres: $canonicalGenres, incompatible found: ${canonicalGenres.intersection(incompatibleGenres)}');
    }

    return hasElectronic && !hasIncompatible;
  }

  /// Validate rock artists
  static bool _isValidRockArtist(List<String> artistGenres) {
    final rockGenres = {
      'rock',
      'alt-rock',
      'indie-rock',
      'hard-rock',
      'classic-rock',
      'progressive-rock',
      'psychedelic-rock',
      'garage-rock',
      'post-rock',
      'math-rock',
      'art-rock',
      'glam-rock',
      'southern-rock',
      'blues-rock',
      'folk-rock',
      'country-rock',
      'surf-rock',
      'stoner-rock',
      'grunge',
      'punk',
      'punk-rock',
      'pop-punk',
      'hardcore-punk',
      'post-punk',
      'metal',
      'heavy-metal',
      'death-metal',
      'black-metal',
      'metalcore'
    };

    final canonicalGenres = artistGenres.map(getCanonicalGenre).toSet();
    return canonicalGenres.any(rockGenres.contains);
  }

  /// Filter artist names that are clearly not individual artists
  static bool isValidArtistName(String artistName) {
    final nameLower = artistName.toLowerCase();

    // Filter out non-artist entities
    final nonArtistPatterns = [
      'various artists',
      'various',
      'soundtrack',
      'cast recording',
      'original cast',
      'motion picture',
      'film score',
      'movie soundtrack',
      'tv soundtrack',
      'game soundtrack',
      'compilation',
      'unknown artist',
      'anonymous',
      'traditional',
      'public domain',
      'library music',
      'production music',
      'stock music',
      'royalty free',
      'creative commons'
    ];

    for (final pattern in nonArtistPatterns) {
      if (nameLower.contains(pattern)) {
        return false;
      }
    }

    // Filter out very generic names
    if (nameLower.length < 2) return false;

    // Filter out names that are just numbers or symbols
    if (RegExp(r'^[\d\s\-_!@#$%^&*()+=\[\]{}|;:,.<>?/~`]+$')
        .hasMatch(nameLower)) {
      return false;
    }

    return true;
  }
}
