import 'package:flutter/foundation.dart';
import '../../models/music_track.dart';
import 'spotify_web_api_service.dart';

/// Hybrid Queue Manager that combines local queue state with Spotify's native queue
/// This avoids rate limits by using add-to-queue API instead of rebuilding entire queue
class HybridQueueManager {
  final SpotifyWebApiService _spotifyService;
  
  // Local queue state (tracks we're managing on top of Spotify's queue)
  final List<MusicTrack> _localQueue = [];
  
  // Tracks that we've added to Spotify but haven't been consumed yet
  final Set<String> _pendingSpotifyTracks = {};
  
  // Currently playing track (to detect when tracks finish)
  MusicTrack? _currentTrack;
  
  HybridQueueManager(this._spotifyService);
  
  /// Get the combined queue view (local + Spotify's queue)
  Future<Map<String, dynamic>> getCombinedQueue() async {
    try {
      // Get Spotify's actual queue
      final spotifyQueue = await _spotifyService.getUserQueue();
      if (spotifyQueue == null) {
        return {
          'currently_playing': null,
          'local_queue': List<MusicTrack>.from(_localQueue),
          'spotify_queue': <Map<String, dynamic>>[],
          'combined_queue': List<MusicTrack>.from(_localQueue),
        };
      }
      
      final currentlyPlaying = spotifyQueue['currently_playing'];
      final spotifyQueueItems = spotifyQueue['queue'] as List<Map<String, dynamic>>;
      
      // Convert Spotify queue items to MusicTrack objects
      final spotifyTracks = <MusicTrack>[];
      for (final item in spotifyQueueItems) {
        try {
          if (item['type'] == 'track') {
            final track = _mapSpotifyTrackToMusicTrack(item);
            spotifyTracks.add(track);
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [HybridQueue] Error mapping Spotify queue item: $e');
          }
        }
      }
      
      // Combine local queue (priority) + Spotify queue
      final combinedQueue = <MusicTrack>[];
      combinedQueue.addAll(_localQueue);
      combinedQueue.addAll(spotifyTracks);
      
      if (kDebugMode) {
        print('🎵 [HybridQueue] Combined queue: ${_localQueue.length} local + ${spotifyTracks.length} Spotify = ${combinedQueue.length} total');
      }
      
      return {
        'currently_playing': currentlyPlaying,
        'local_queue': List<MusicTrack>.from(_localQueue),
        'spotify_queue': spotifyTracks,
        'combined_queue': combinedQueue,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [HybridQueue] Error getting combined queue: $e');
      }
      return {
        'currently_playing': null,
        'local_queue': List<MusicTrack>.from(_localQueue),
        'spotify_queue': <MusicTrack>[],
        'combined_queue': List<MusicTrack>.from(_localQueue),
      };
    }
  }
  
  /// Add track to local queue and sync with Spotify
  Future<bool> addTrackToQueue(MusicTrack track) async {
    try {
      if (kDebugMode) {
        print('🎵 [HybridQueue] Adding track to local queue: ${track.title}');
      }
      
      // Add to local queue immediately (optimistic update)
      _localQueue.add(track);
      
      // Validate track URI
      final trackUri = track.uri ?? 'spotify:track:${track.id}';
      if (trackUri.isEmpty || track.id.isEmpty) {
        if (kDebugMode) {
          print('❌ [HybridQueue] Invalid track URI or ID - uri: $trackUri, id: ${track.id}');
          print('⚠️ [HybridQueue] Keeping track in local queue despite invalid URI');
        }
        // Keep in local queue even with invalid URI - user manually added it
        return true; // Return true since it was added to local queue
      }
      
      if (kDebugMode) {
        print('🎯 [HybridQueue] Syncing to Spotify with URI: $trackUri');
      }
      
      // Try to add to Spotify's queue in background
      final spotifySuccess = await _spotifyService.addToQueue(trackUri);
      
      if (spotifySuccess) {
        // Track the fact that we added this to Spotify
        _pendingSpotifyTracks.add(track.id);
        
        if (kDebugMode) {
          print('✅ [HybridQueue] Successfully added track to both local and Spotify queue');
        }
      } else {
        // Spotify failed, but keep in local queue since user manually added it
        if (kDebugMode) {
          print('⚠️ [HybridQueue] Spotify add failed, but keeping in local queue (user manually added)');
        }
      }
      
      // Always return true since track was added to local queue
      return true;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [HybridQueue] Error adding track to queue: $e');
        print('❌ [HybridQueue] Error type: ${e.runtimeType}');
      }
      
      // Keep track in local queue even if there was an error - user manually added it
      if (_localQueue.isEmpty || _localQueue.last.id != track.id) {
        // If for some reason the track wasn't added to local queue, add it now
        _localQueue.add(track);
        if (kDebugMode) {
          print('🎵 [HybridQueue] Added track to local queue despite error (user manually added)');
        }
      }
      
      // Return true since track is in local queue
      return true;
    }
  }
  
  /// Reorder tracks in local queue
  Future<void> reorderLocalQueue(int oldIndex, int newIndex) async {
    if (oldIndex < 0 || oldIndex >= _localQueue.length || 
        newIndex < 0 || newIndex >= _localQueue.length) {
      return;
    }
    
    if (kDebugMode) {
      print('🔄 [HybridQueue] Reordering local queue: $oldIndex -> $newIndex');
    }
    
    // Update local queue immediately
    final track = _localQueue.removeAt(oldIndex);
    _localQueue.insert(newIndex, track);
    
    // Re-sync with Spotify (add the reordered track to ensure it plays in the right order)
    try {
      final success = await _spotifyService.addToQueue(track.uri ?? 'spotify:track:${track.id}');
      if (success) {
        _pendingSpotifyTracks.add(track.id);
      }
      
      if (kDebugMode) {
        print('✅ [HybridQueue] Local queue reordered and synced with Spotify');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [HybridQueue] Reorder sync with Spotify failed: $e');
      }
    }
  }
  
  /// Remove track from local queue
  Future<void> removeFromLocalQueue(int index) async {
    if (index < 0 || index >= _localQueue.length) {
      return;
    }
    
    final track = _localQueue[index];
    if (kDebugMode) {
      print('🗑️ [HybridQueue] Removing track from local queue: ${track.title}');
    }
    
    _localQueue.removeAt(index);
    _pendingSpotifyTracks.remove(track.id);
    
    // Note: We can't remove from Spotify's queue, but that's okay
    // The track will just play as part of Spotify's queue if user doesn't skip
  }
  
  /// Update current playing state and clean up consumed tracks
  Future<void> updateCurrentPlayingState(Map<String, dynamic>? playbackState) async {
    if (playbackState == null) return;
    
    try {
      final currentTrack = playbackState['item'];
      if (currentTrack == null) return;
      
      final trackId = currentTrack['id'] as String?;
      if (trackId == null) return;
      
      // If this track was in our pending set, it means it was consumed from our local queue
      if (_pendingSpotifyTracks.contains(trackId)) {
        // Find and remove this track from local queue
        _localQueue.removeWhere((track) => track.id == trackId);
        _pendingSpotifyTracks.remove(trackId);
        
        if (kDebugMode) {
          print('✅ [HybridQueue] Consumed track from local queue: ${currentTrack['name']}');
          print('🎵 [HybridQueue] Remaining in local queue: ${_localQueue.length}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [HybridQueue] Error updating current playing state: $e');
      }
    }
  }
  
  /// Get current local queue (for UI display)
  List<MusicTrack> get localQueue => List<MusicTrack>.from(_localQueue);
  
  /// Get local queue length
  int get localQueueLength => _localQueue.length;
  
  /// Check if local queue is empty
  bool get isLocalQueueEmpty => _localQueue.isEmpty;
  
  /// Clear local queue
  void clearLocalQueue() {
    if (kDebugMode) {
      print('🧹 [HybridQueue] Clearing local queue (${_localQueue.length} tracks)');
    }
    _localQueue.clear();
    _pendingSpotifyTracks.clear();
  }
  
  /// Retry failed Spotify syncs
  Future<void> retryFailedSyncs() async {
    final tracksToRetry = _localQueue.where((track) => !_pendingSpotifyTracks.contains(track.id)).toList();
    
    if (tracksToRetry.isEmpty) return;
    
    if (kDebugMode) {
      print('🔄 [HybridQueue] Retrying ${tracksToRetry.length} failed Spotify syncs');
    }
    
    for (final track in tracksToRetry) {
      try {
        final success = await _spotifyService.addToQueue(track.uri ?? 'spotify:track:${track.id}');
        if (success) {
          _pendingSpotifyTracks.add(track.id);
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ [HybridQueue] Retry failed for track: ${track.title}');
        }
      }
    }
  }
  
  /// Shuffle and queue multiple tracks
  Future<bool> shuffleAndQueueTracks(List<MusicTrack> tracks, {bool clearExisting = false}) async {
    if (tracks.isEmpty) {
      if (kDebugMode) {
        print('❌ [HybridQueue] Cannot shuffle empty track list');
      }
      return false;
    }
    
    try {
      // Clear existing queue if requested
      if (clearExisting) {
        clearLocalQueue();
      }
      
      // Create a shuffled copy of the tracks
      final shuffledTracks = List<MusicTrack>.from(tracks);
      shuffledTracks.shuffle();
      
      if (kDebugMode) {
        print('🎲 [HybridQueue] Shuffling ${tracks.length} tracks and adding to queue');
      }
      
      // Add shuffled tracks to local queue
      int successCount = 0;
      for (final track in shuffledTracks) {
        final success = await addTrackToQueue(track);
        if (success) {
          successCount++;
        }
        
        // Add small delay to avoid overwhelming the API
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      if (kDebugMode) {
        print('✅ [HybridQueue] Successfully shuffled and queued $successCount/${tracks.length} tracks');
      }
      
      return successCount > 0;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [HybridQueue] Error shuffling and queuing tracks: $e');
      }
      return false;
    }
  }
  
  /// Queue multiple tracks in order (without shuffling)
  Future<bool> queueMultipleTracks(List<MusicTrack> tracks, {bool clearExisting = false}) async {
    if (tracks.isEmpty) {
      if (kDebugMode) {
        print('❌ [HybridQueue] Cannot queue empty track list');
      }
      return false;
    }
    
    try {
      // Clear existing queue if requested
      if (clearExisting) {
        clearLocalQueue();
      }
      
      if (kDebugMode) {
        print('🎵 [HybridQueue] Queuing ${tracks.length} tracks in order');
      }
      
      // Add tracks to local queue
      int successCount = 0;
      for (final track in tracks) {
        final success = await addTrackToQueue(track);
        if (success) {
          successCount++;
        }
        
        // Add small delay to avoid overwhelming the API
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      if (kDebugMode) {
        print('✅ [HybridQueue] Successfully queued $successCount/${tracks.length} tracks');
      }
      
      return successCount > 0;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [HybridQueue] Error queuing multiple tracks: $e');
      }
      return false;
    }
  }
  
  /// Shuffle current local queue
  void shuffleLocalQueue() {
    if (_localQueue.isEmpty) {
      if (kDebugMode) {
        print('🎲 [HybridQueue] Cannot shuffle empty local queue');
      }
      return;
    }
    
    if (kDebugMode) {
      print('🎲 [HybridQueue] Shuffling current local queue (${_localQueue.length} tracks)');
    }
    
    _localQueue.shuffle();
    
    // Note: We don't re-sync with Spotify here since it would add duplicates
    // The shuffled order will be respected for future plays from the local queue
  }
  
  /// Helper method to map Spotify API response to MusicTrack
  MusicTrack _mapSpotifyTrackToMusicTrack(Map<String, dynamic> spotifyTrack) {
    // Extract album art
    String albumArt = '';
    if (spotifyTrack['album'] != null && 
        spotifyTrack['album']['images'] != null && 
        spotifyTrack['album']['images'].isNotEmpty) {
      albumArt = spotifyTrack['album']['images'][0]['url'];
    }
    
    // Extract artist name
    String artistName = 'Unknown Artist';
    if (spotifyTrack['artists'] != null && spotifyTrack['artists'].isNotEmpty) {
      artistName = spotifyTrack['artists'].map((artist) => artist['name']).join(', ');
    }
    
    return MusicTrack(
      id: spotifyTrack['id'] ?? '',
      title: spotifyTrack['name'] ?? 'Unknown Track',
      artist: artistName,
      album: spotifyTrack['album'] != null ? spotifyTrack['album']['name'] ?? '' : '',
      albumArt: albumArt,
      url: spotifyTrack['external_urls'] != null ? spotifyTrack['external_urls']['spotify'] ?? '' : '',
      service: 'spotify',
      previewUrl: spotifyTrack['preview_url'],
      albumArtUrl: albumArt,
      serviceType: 'spotify',
      durationMs: spotifyTrack['duration_ms'] ?? 0,
      uri: spotifyTrack['uri'] ?? 'spotify:track:${spotifyTrack['id']}',
    );
  }
} 