import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../models/music_track.dart';
import '../analytics/analytics_service.dart';

/// Manages offline functionality for Spotify integration
class SpotifyOfflineManager {
  static final SpotifyOfflineManager _instance = SpotifyOfflineManager._internal();
  factory SpotifyOfflineManager() => _instance;
  SpotifyOfflineManager._internal();

  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final AnalyticsService _analytics = AnalyticsService();
  
  // Keys for stored data
  static const String _offlineTracksKey = 'spotify_offline_tracks';
  static const String _offlineQueueKey = 'spotify_offline_queue';
  static const String _lastSyncKey = 'spotify_last_sync';
  
  // Stream controllers
  final _offlineStatusController = StreamController<bool>.broadcast();
  Stream<bool> get offlineStatusStream => _offlineStatusController.stream;
  
  // Cache recently played tracks
  List<MusicTrack> _cachedTracks = [];
  DateTime? _lastSyncTime;
  bool _isOffline = false;
  
  // Getters
  bool get isOffline => _isOffline;
  List<MusicTrack> get cachedTracks => _cachedTracks;
  DateTime? get lastSyncTime => _lastSyncTime;
  
  /// Initialize offline manager
  Future<void> initialize() async {
    try {
      // Load cached tracks
      await _loadCachedTracks();
      
      // Load last sync time
      final lastSyncString = await _secureStorage.read(key: _lastSyncKey);
      if (lastSyncString != null) {
        _lastSyncTime = DateTime.parse(lastSyncString);
      }
      
      _analytics.trackEvent('spotify_offline_manager_initialized');
    } catch (e, stackTrace) {
      _analytics.trackError('offline_manager_init_error', e, stackTrace);
      if (kDebugMode) {
        print('Error initializing offline manager: $e');
      }
    }
  }
  
  /// Cache tracks for offline use
  Future<void> cacheTracks(List<MusicTrack> tracks) async {
    try {
      // Add new tracks to cache
      final tracksToStore = [..._cachedTracks, ...tracks];
      
      // Keep only unique tracks and limit to 1000
      final uniqueTracks = tracksToStore.toSet().toList();
      if (uniqueTracks.length > 1000) {
        uniqueTracks.removeRange(0, uniqueTracks.length - 1000);
      }
      
      // Store tracks
      await _secureStorage.write(
        key: _offlineTracksKey,
        value: uniqueTracks.map((t) => t.toJson()).toList().toString(),
      );
      
      _cachedTracks = uniqueTracks;
      _lastSyncTime = DateTime.now();
      await _secureStorage.write(key: _lastSyncKey, value: _lastSyncTime!.toIso8601String());
      
      _analytics.trackEvent('tracks_cached', {
        'count': tracks.length,
        'total_cached': _cachedTracks.length,
      });
    } catch (e, stackTrace) {
      _analytics.trackError('cache_tracks_error', e, stackTrace);
      if (kDebugMode) {
        print('Error caching tracks: $e');
      }
    }
  }
  
  /// Load cached tracks from storage
  Future<void> _loadCachedTracks() async {
    try {
      final tracksString = await _secureStorage.read(key: _offlineTracksKey);
      if (tracksString != null) {
        final tracksList = List<Map<String, dynamic>>.from(
          tracksString as List<dynamic>
        );
        _cachedTracks = tracksList.map((t) => MusicTrack.fromJson(t)).toList();
      }
    } catch (e, stackTrace) {
      _analytics.trackError('load_cached_tracks_error', e, stackTrace);
      if (kDebugMode) {
        print('Error loading cached tracks: $e');
      }
    }
  }
  
  /// Queue actions for when online
  Future<void> queueAction(String action, Map<String, dynamic> parameters) async {
    try {
      final queue = await _getActionQueue();
      queue.add({
        'action': action,
        'parameters': parameters,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      await _secureStorage.write(
        key: _offlineQueueKey,
        value: queue.toString(),
      );
      
      _analytics.trackEvent('action_queued', {
        'action': action,
        'queue_size': queue.length,
      });
    } catch (e, stackTrace) {
      _analytics.trackError('queue_action_error', e, stackTrace);
      if (kDebugMode) {
        print('Error queuing action: $e');
      }
    }
  }
  
  /// Get queued actions
  Future<List<Map<String, dynamic>>> _getActionQueue() async {
    try {
      final queueString = await _secureStorage.read(key: _offlineQueueKey);
      if (queueString != null) {
        return List<Map<String, dynamic>>.from(
          queueString as List<dynamic>
        );
      }
    } catch (e, stackTrace) {
      _analytics.trackError('get_action_queue_error', e, stackTrace);
      if (kDebugMode) {
        print('Error getting action queue: $e');
      }
    }
    return [];
  }
  
  /// Process queued actions when back online
  Future<void> processQueue() async {
    try {
      final queue = await _getActionQueue();
      if (queue.isEmpty) return;
      
      _analytics.trackEvent('processing_queue', {
        'queue_size': queue.length,
      });
      
      // Process each action
      for (final action in queue) {
        try {
          // Here you would implement the actual processing logic
          // based on the action type
          if (kDebugMode) {
            print('Processing queued action: ${action['action']}');
          }
        } catch (e, stackTrace) {
          _analytics.trackError('process_action_error', e, stackTrace);
        }
      }
      
      // Clear processed queue
      await _secureStorage.delete(key: _offlineQueueKey);
    } catch (e, stackTrace) {
      _analytics.trackError('process_queue_error', e, stackTrace);
      if (kDebugMode) {
        print('Error processing queue: $e');
      }
    }
  }
  
  /// Set offline status
  void setOfflineStatus(bool offline) {
    _isOffline = offline;
    _offlineStatusController.add(offline);
    
    _analytics.trackEvent('offline_status_changed', {
      'is_offline': offline,
    });
  }
  
  /// Clear offline data
  Future<void> clearOfflineData() async {
    try {
      await _secureStorage.delete(key: _offlineTracksKey);
      await _secureStorage.delete(key: _offlineQueueKey);
      await _secureStorage.delete(key: _lastSyncKey);
      
      _cachedTracks = [];
      _lastSyncTime = null;
      
      _analytics.trackEvent('offline_data_cleared');
    } catch (e, stackTrace) {
      _analytics.trackError('clear_offline_data_error', e, stackTrace);
      if (kDebugMode) {
        print('Error clearing offline data: $e');
      }
    }
  }
  
  /// Dispose resources
  void dispose() {
    _offlineStatusController.close();
  }
} 