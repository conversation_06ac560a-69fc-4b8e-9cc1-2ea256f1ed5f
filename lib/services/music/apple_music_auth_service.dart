import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:music_kit/music_kit.dart';
import '../../config/constants.dart';
import '../api/api_client.dart';
import '../../utils/shared_preferences_storage.dart';

/// Service dedicated to handling Apple Music authentication
class AppleMusicAuthService {
  final ApiClient _apiClient = ApiClient();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final SharedPreferencesStorage _sharedPreferencesStorage = SharedPreferencesStorage.instance;
  final MusicKit _musicKit = MusicKit();
  
  // Storage keys for Apple Music tokens
  static const String _developerTokenKey = 'apple_music_developer_token';
  static const String _userTokenKey = 'apple_music_user_token';
  static const String _authStatusKey = 'apple_music_auth_status';
  static const String _userIdKey = 'apple_music_user_id';
  
  // Authentication status tracking
  bool _isAuthenticated = false;
  String? _currentUserToken;
  String? _currentUserId;
  
  // Getters
  bool get isAuthenticated => _isAuthenticated;
  String? get currentUserToken => _currentUserToken;
  String? get currentUserId => _currentUserId;
  
  /// Initialize Apple Music service
  Future<bool> initialize() async {
    try {
      if (kDebugMode) {
        print('🍎 Initializing Apple Music service...');
      }
      
      // First get the developer token from MusicKit
      final developerToken = await _musicKit.requestDeveloperToken();
      
      // Initialize MusicKit with the developer token
      await _musicKit.initialize(
        developerToken,
        musicUserToken: await _getStoredUserToken(),
      );
      
      // Check if we have stored authentication
      await _loadStoredAuth();
      
      if (kDebugMode) {
        print('✅ Apple Music service initialized. Authenticated: $_isAuthenticated');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Apple Music service: $e');
      }
      return false;
    }
  }
  
  /// Request Apple Music authorization
  Future<bool> requestAuthorization() async {
    try {
      if (kDebugMode) {
        print('🍎 Requesting Apple Music authorization...');
      }
      // Ask for authorization status (this will prompt the user if not determined)
      final authStatus = await _musicKit.requestAuthorizationStatus();
      if (kDebugMode) {
        print('🍎 Apple Music authorization status: $authStatus');
      }
      // Only proceed if authorized
      if (authStatus is MusicAuthorizationStatusAuthorized) {
        // Get developer token
        final developerToken = await _musicKit.requestDeveloperToken();
        // Request user token for personalized content
        final userToken = await _musicKit.requestUserToken(developerToken);
        if (userToken.isNotEmpty) {
          // Store and update authentication state
          await _storeUserToken(userToken);
          _currentUserToken = userToken;
          _isAuthenticated = true;
          // Fetch and store a basic user profile if needed
          await _fetchUserProfile();
          await _sharedPreferencesStorage.write(key: _authStatusKey, value: 'authorized');
          
          // Clean up old secure storage entry
          try {
            await _secureStorage.delete(key: _authStatusKey);
          } catch (e) {
            // Ignore errors when cleaning up
          }
          
          if (kDebugMode) {
            print('✅ Apple Music authorization successful');
          }
          return true;
        } else {
          if (kDebugMode) {
            print('❌ Apple Music user token is empty');
          }
          return false;
        }
      } else {
        if (kDebugMode) {
          print('❌ Apple Music authorization not granted: $authStatus');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during Apple Music authorization: $e');
      }
      return false;
    }
  }
  
  /// Check if user has Apple Music subscription
  Future<bool> hasActiveSubscription() async {
    try {
      // Listen to subscription updates to get current capability
      MusicSubscription? currentSubscription;
      final subscription = _musicKit.onSubscriptionUpdated.listen((subscription) {
        currentSubscription = subscription;
      });
      
      // Wait a moment for the subscription to be available
      await Future.delayed(const Duration(milliseconds: 500));
      subscription.cancel();
      
      return currentSubscription?.canPlayCatalogContent ?? false;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error checking Apple Music subscription: $e');
      }
      return false;
    }
  }
  
  /// Sign out from Apple Music
  Future<void> signOut() async {
    try {
      if (kDebugMode) {
        print('🍎 Signing out from Apple Music...');
      }
      
      // Clear stored tokens from SharedPreferences
      await _sharedPreferencesStorage.delete(key: _userTokenKey);
      await _sharedPreferencesStorage.delete(key: _authStatusKey);
      await _sharedPreferencesStorage.delete(key: _userIdKey);
      
      // Also try to clear from secure storage for backward compatibility
      try {
        await _secureStorage.delete(key: _userTokenKey);
        await _secureStorage.delete(key: _authStatusKey);
        await _secureStorage.delete(key: _userIdKey);
      } catch (e) {
        // Ignore errors when cleaning up old storage
      }
      
      // Reset state
      _isAuthenticated = false;
      _currentUserToken = null;
      _currentUserId = null;
      
      if (kDebugMode) {
        print('✅ Apple Music sign out completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during Apple Music sign out: $e');
      }
    }
  }
  
  /// Get stored user token
  Future<String?> _getStoredUserToken() async {
    // Try SharedPreferences first
    String? token = await _sharedPreferencesStorage.read(key: _userTokenKey);
    
    // If not found, try secure storage for backward compatibility
    if (token == null) {
      token = await _secureStorage.read(key: _userTokenKey);
      
      // Migrate to SharedPreferences if found
      if (token != null) {
        await _sharedPreferencesStorage.write(key: _userTokenKey, value: token);
        await _secureStorage.delete(key: _userTokenKey);
        if (kDebugMode) {
          print('🔄 Migrated Apple Music user token to SharedPreferences');
        }
      }
    }
    
    return token;
  }
  
  /// Store user token securely
  Future<void> _storeUserToken(String token) async {
    await _sharedPreferencesStorage.write(key: _userTokenKey, value: token);
    
    // Clean up old secure storage entry
    try {
      await _secureStorage.delete(key: _userTokenKey);
    } catch (e) {
      // Ignore errors when cleaning up
    }
  }
  
  /// Load stored authentication state
  Future<void> _loadStoredAuth() async {
    try {
      // Try SharedPreferences first
      String? authStatus = await _sharedPreferencesStorage.read(key: _authStatusKey);
      String? userToken = await _sharedPreferencesStorage.read(key: _userTokenKey);
      String? userId = await _sharedPreferencesStorage.read(key: _userIdKey);
      
      // If not found, try secure storage for backward compatibility
      if (authStatus == null || userToken == null) {
        authStatus = authStatus ?? await _secureStorage.read(key: _authStatusKey);
        userToken = userToken ?? await _secureStorage.read(key: _userTokenKey);
        userId = userId ?? await _secureStorage.read(key: _userIdKey);
        
        // Migrate to SharedPreferences if found
        if (authStatus != null) {
          await _sharedPreferencesStorage.write(key: _authStatusKey, value: authStatus);
          await _secureStorage.delete(key: _authStatusKey);
        }
        if (userToken != null && !(await _sharedPreferencesStorage.containsKey(_userTokenKey))) {
          await _sharedPreferencesStorage.write(key: _userTokenKey, value: userToken);
          await _secureStorage.delete(key: _userTokenKey);
        }
        if (userId != null) {
          await _sharedPreferencesStorage.write(key: _userIdKey, value: userId);
          await _secureStorage.delete(key: _userIdKey);
        }
        
        if (kDebugMode && (authStatus != null || userToken != null || userId != null)) {
          print('🔄 Migrated Apple Music auth data to SharedPreferences');
        }
      }
      
      if (authStatus == 'authorized' && userToken != null) {
        _isAuthenticated = true;
        _currentUserToken = userToken;
        _currentUserId = userId;
        
        if (kDebugMode) {
          print('✅ Loaded stored Apple Music authentication');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error loading stored Apple Music auth: $e');
      }
    }
  }
  
  /// Fetch user profile information
  Future<void> _fetchUserProfile() async {
    try {
      // This would typically involve making API calls to get user info
      // For now, we'll just generate a basic user ID
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      _currentUserId = 'apple_music_user_$timestamp';
      await _sharedPreferencesStorage.write(key: _userIdKey, value: _currentUserId!);
      
      // Clean up old secure storage entry
      try {
        await _secureStorage.delete(key: _userIdKey);
      } catch (e) {
        // Ignore errors when cleaning up
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error fetching Apple Music user profile: $e');
      }
    }
  }
  
  /// Refresh authentication if needed
  Future<bool> refreshAuth() async {
    if (!_isAuthenticated) {
      return await requestAuthorization();
    }
    return true;
  }
} 