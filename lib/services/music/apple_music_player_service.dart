import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:music_kit/music_kit.dart';
import '../../models/music_track.dart';
import '../../utils/music_fallback_utils.dart';
import 'apple_music_auth_service.dart';
import 'apple_music_queue_manager.dart';
import 'apple_music_service.dart';
import '../api_service.dart';
import '../auth_service.dart';

/// Apple Music player service using music_kit package
/// Handles all Apple Music playback operations with queue management
class AppleMusicPlayerService {
  final MusicKit _musicKit = MusicKit();
  final AppleMusicAuthService _authService = AppleMusicAuthService();
  final AppleMusicQueueManager _queueManager = AppleMusicQueueManager();
  AppleMusicService? _appleMusicService;
  
  // Optional services for cross-platform compatibility
  ApiService? _apiService;
  AuthService? _authServiceInstance;

  /// Set the Apple Music service (usually called by AppleMusicProvider)
  void setAppleMusicService(AppleMusicService service) {
    _appleMusicService = service;
    if (kDebugMode) {
      print('🍎 [AppleMusicPlayerService] Apple Music service injected');
    }
  }
  
  // State controllers
  final StreamController<bool> _isPlayingController = StreamController<bool>.broadcast();
  final StreamController<MusicTrack?> _currentTrackController = StreamController<MusicTrack?>.broadcast();
  final StreamController<Duration> _positionController = StreamController<Duration>.broadcast();
  final StreamController<Duration> _durationController = StreamController<Duration>.broadcast();
  
  // Current state
  bool _isInitialized = false;
  bool _isPlaying = false;
  MusicTrack? _currentTrack;
  Duration _currentPosition = Duration.zero;
  Duration _currentDuration = Duration.zero;
  
  // Position tracking
  Timer? _positionTimer;
  StreamSubscription<MusicPlayerState>? _playerStateSubscription;
  StreamSubscription<MusicPlayerQueue>? _playerQueueSubscription;
  
  // Queue manager subscriptions
  StreamSubscription<MusicTrack?>? _queueManagerTrackSubscription;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isPlaying => _isPlaying;
  MusicTrack? get currentTrack => _queueManager.currentTrack ?? _currentTrack;
  Duration get position => _currentPosition;
  Duration get duration => _currentDuration;
  
  // Queue manager getters
  AppleMusicQueueManager get queueManager => _queueManager;
  List<MusicTrack> get queue => _queueManager.queue;
  int get currentIndex => _queueManager.currentIndex;
  bool get isShuffleEnabled => _queueManager.isShuffleEnabled;
  RepeatMode get repeatMode => _queueManager.repeatMode;
  bool get hasNext => _queueManager.hasNext;
  bool get hasPrevious => _queueManager.hasPrevious;
  
  // Streams
  Stream<bool> get isPlayingStream => _isPlayingController.stream;
  Stream<MusicTrack?> get currentTrackStream => _currentTrackController.stream;
  Stream<Duration> get positionStream => _positionController.stream;
  Stream<Duration> get durationStream => _durationController.stream;
  
  /// Initialize the Apple Music player service
  Future<bool> initialize() async {
    try {
      if (kDebugMode) {
        print('🍎 Initializing Apple Music player service...');
      }
      
      // Initialize auth service first
      await _authService.initialize();
      
      // Initialize queue manager
      await _queueManager.initialize();
      
      // Listen to player state changes
      _playerStateSubscription = _musicKit.onMusicPlayerStateChanged.listen(_handlePlayerStateChange);
      
      // Listen to queue changes to track current song
      _playerQueueSubscription = _musicKit.onPlayerQueueChanged.listen(_handleQueueChange);
      
      // Listen to queue manager track changes for automatic progression
      _queueManagerTrackSubscription = _queueManager.currentTrackStream.listen(_handleQueueManagerTrackChange);
      
      // Start position tracking
      _startPositionTracking();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Apple Music player service initialized');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Apple Music player service: $e');
      }
      return false;
    }
  }
  
  /// Handle player state changes from MusicKit
  void _handlePlayerStateChange(MusicPlayerState state) {
    try {
      // Update playing state
      final wasPlaying = _isPlaying;
      _isPlaying = state.playbackStatus == MusicPlayerPlaybackStatus.playing;
      
      if (wasPlaying != _isPlaying) {
        _isPlayingController.add(_isPlaying);
        if (kDebugMode) {
          print('🍎 Apple Music playback state changed: ${_isPlaying ? 'playing' : 'paused'}');
        }
      }
      
      // Note: MusicKit doesn't provide track info in the state
      // Track info is managed separately when playTrack is called
      
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error handling Apple Music player state change: $e');
      }
    }
  }
  
  /// Handle queue changes to track current song
  void _handleQueueChange(MusicPlayerQueue queue) {
    try {
      if (queue.currentEntry != null) {
        final entry = queue.currentEntry!;
        // We can get some basic info from the queue entry
        if (kDebugMode) {
          print('🍎 Current track: ${entry.title ?? 'Unknown'}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error handling queue change: $e');
      }
    }
  }
  
  /// Handle track changes from the queue manager (for automatic progression)
  void _handleQueueManagerTrackChange(MusicTrack? track) {
    try {
      if (kDebugMode) {
        print('🍎 [PlayerService] Queue manager track changed: ${track?.title ?? 'None'}');
      }
      
      // Update our local state to match the queue manager
      _currentTrack = track;
      
      // Update duration if we have a track
      if (track != null) {
        _currentDuration = Duration(milliseconds: track.durationMs);
        _durationController.add(_currentDuration);
        
        // Reset position for new track
        _currentPosition = Duration.zero;
        _positionController.add(_currentPosition);
      }
      
      // Notify listeners about the track change
      _currentTrackController.add(_currentTrack);
      
      if (kDebugMode) {
        print('🍎 [PlayerService] ✅ Updated local state and notified listeners');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [PlayerService] Error handling queue manager track change: $e');
      }
    }
  }
  
  /// Start tracking playback position
  void _startPositionTracking() {
    _positionTimer?.cancel();
    _positionTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_isPlaying && _currentTrack != null) {
        try {
          // Get current playback time from MusicKit
          final currentTime = await _musicKit.playbackTime;
          _currentPosition = Duration(milliseconds: (currentTime * 1000).round());
          _positionController.add(_currentPosition);
        } catch (e) {
          // Ignore errors during position tracking
        }
      }
    });
  }
  
  /// Play a track
  Future<bool> playTrack(MusicTrack track) async {
    try {
      if (!_isInitialized) {
        throw Exception('Player service not initialized');
      }

      if (kDebugMode) {
        print('🍎 Playing Apple Music track: ${track.title} by ${track.artist}');
        print('🍎 Track ID: ${track.id} (isLibrary: ${track.isLibrary})');
        print('🍎 Track service: ${track.service}');
        if (_isPlaying) {
          print('🍎 Current playback detected - will replace with new track');
        }
      }
      
             // Note: Cross-platform track search is handled at the provider level
       // This service assumes all tracks passed to it are Apple Music compatible
      
             // Use queue manager for single track playback (queue of 1)
       // This ensures consistent behavior between single track and collection playback
       final success = await _queueManager.setQueue(
         tracks: [track],
         collectionType: 'single_track',
         startIndex: 0,
       );

       if (success) {
         // Update our local state to match the queue manager
         _currentTrack = track;
         _currentDuration = Duration(milliseconds: track.durationMs);
         _currentTrackController.add(_currentTrack);
         _durationController.add(_currentDuration);
         
         if (kDebugMode) {
           print('✅ Successfully started playback of: ${track.title}');
         }
       }
      
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error playing track: $e');
        print('🔍 Track ID was: ${track.id}, type: ${track.id.runtimeType}');
        
        // Check if this is the known iOS 18 queue bug
        if (e.toString().contains('MPMusicPlayerControllerErrorDomain error 2')) {
          print('🐛 This appears to be iOS 18 MusicKit queue bug FB15179078');
          print('🐛 See: https://developer.apple.com/forums/thread/764701');
        }
      }
      return false;
    }
  }
  
  
  
  /// Play current queue
  Future<bool> play() async {
    try {
      if (!_isInitialized) {
        throw Exception('Player service not initialized');
      }
      
      if (kDebugMode) {
        print('🍎 [PlayerService] play() called');
      }
      
      await _musicKit.play();
      
      if (kDebugMode) {
        print('🍎 [PlayerService] play() completed successfully');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [PlayerService] Error playing: $e');
      }
      return false;
    }
  }
  
  /// Pause playback
  Future<bool> pause() async {
    try {
      if (!_isInitialized) {
        throw Exception('Player service not initialized');
      }
      
      if (kDebugMode) {
        print('🍎 [PlayerService] pause() called');
      }
      
      await _musicKit.pause();
      
      if (kDebugMode) {
        print('🍎 [PlayerService] pause() completed successfully');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [PlayerService] Error pausing: $e');
      }
      return false;
    }
  }
  
  /// Stop playback
  Future<bool> stop() async {
    try {
      if (!_isInitialized) {
        throw Exception('Player service not initialized');
      }
      
      await _musicKit.stop();
      
      // Clear current track
      _currentTrack = null;
      _currentPosition = Duration.zero;
      _currentTrackController.add(null);
      _positionController.add(_currentPosition);
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error stopping: $e');
      }
      return false;
    }
  }
  
  /// Skip to the next track in the queue
  Future<bool> skipToNext() async {
    try {
      if (!_isInitialized) throw Exception('Player service not initialized');
      
      final success = await _queueManager.skipToNext();
      
      if (success) {
        // Update our local state to match the queue manager
        final currentTrack = _queueManager.currentTrack;
        if (currentTrack != null) {
          _currentTrack = currentTrack;
          _currentDuration = Duration(milliseconds: currentTrack.durationMs);
          _currentTrackController.add(_currentTrack);
          _durationController.add(_currentDuration);
        }
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) print('❌ Error skipping to next track: $e');
      return false;
    }
  }

  /// Skip to the previous track in the queue
  Future<bool> skipToPrevious() async {
    try {
      if (!_isInitialized) throw Exception('Player service not initialized');
      
      final success = await _queueManager.skipToPrevious();
      
      if (success) {
        // Update our local state to match the queue manager
        final currentTrack = _queueManager.currentTrack;
        if (currentTrack != null) {
          _currentTrack = currentTrack;
          _currentDuration = Duration(milliseconds: currentTrack.durationMs);
          _currentTrackController.add(_currentTrack);
          _durationController.add(_currentDuration);
        }
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) print('❌ Error skipping to previous track: $e');
      return false;
    }
  }

  /// Skip to the next entry in the queue (legacy method)
  Future<bool> skipToNextEntry() async {
    return await skipToNext();
  }

  /// Skip to the previous entry in the queue (legacy method)
  Future<bool> skipToPreviousEntry() async {
    return await skipToPrevious();
  }
  
  /// Seek to position
  Future<bool> seek(Duration position) async {
    try {
      if (!_isInitialized) {
        throw Exception('Player service not initialized');
      }
      
      // Note: MusicKit might not have direct seek support
      // For now, we'll just update our internal position
      // In a real implementation, you might need to use other methods
      _currentPosition = position;
      _positionController.add(_currentPosition);
      
      if (kDebugMode) {
        print('🍎 Seek requested to ${position.inSeconds}s (simulated)');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error seeking: $e');
      }
      return false;
    }
  }
  
  /// Toggle shuffle mode
  Future<bool> toggleShuffle() async {
    try {
      if (!_isInitialized) {
        throw Exception('Player service not initialized');
      }
      
      return await _queueManager.toggleShuffle();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error toggling shuffle: $e');
      }
      return false;
    }
  }
  
  /// Set shuffle mode
  Future<bool> setShuffle(bool enabled) async {
    try {
      if (!_isInitialized) {
        throw Exception('Player service not initialized');
      }
      
      if (enabled != _queueManager.isShuffleEnabled) {
        return await _queueManager.toggleShuffle();
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error setting shuffle: $e');
      }
      return false;
    }
  }
  
  /// Toggle repeat mode
  Future<bool> toggleRepeatMode() async {
    try {
      if (!_isInitialized) {
        throw Exception('Player service not initialized');
      }
      
      return await _queueManager.toggleRepeatMode();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error toggling repeat mode: $e');
      }
      return false;
    }
  }
  
  /// Set repeat mode
  Future<bool> setRepeatMode(String mode) async {
    try {
      if (!_isInitialized) {
        throw Exception('Player service not initialized');
      }
      
      RepeatMode repeatMode;
      switch (mode.toLowerCase()) {
        case 'one':
          repeatMode = RepeatMode.one;
          break;
        case 'all':
          repeatMode = RepeatMode.all;
          break;
        default:
          repeatMode = RepeatMode.off;
          break;
      }
      
      return await _queueManager.setRepeatMode(repeatMode);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error setting repeat mode: $e');
      }
      return false;
    }
  }
  
  /// Play a collection (playlist, album, recommendations) starting from a specific track
  Future<bool> playCollection({
    required List<MusicTrack> tracks,
    required String collectionType, // 'playlist', 'album', 'recommendations', etc.
    int startIndex = 0,
    Map<String, dynamic>? collectionMetadata,
  }) async {
    try {
      if (!_isInitialized) {
        throw Exception('Player service not initialized');
      }

      if (tracks.isEmpty) {
        throw Exception('No tracks provided for collection');
      }

      if (kDebugMode) {
        print('🍎 Playing collection: $collectionType with ${tracks.length} tracks, starting from track ${startIndex + 1}');
      }

      // Use the queue manager to set up and play the collection
      final success = await _queueManager.setQueue(
        tracks: tracks,
        collectionType: collectionType,
        collectionId: collectionMetadata?['playlistId'] ?? collectionMetadata?['albumId'],
        collectionMetadata: collectionMetadata,
        startIndex: startIndex,
      );

      if (success) {
        // Update our local state to match the queue manager
        final currentTrack = _queueManager.currentTrack;
        if (currentTrack != null) {
          _currentTrack = currentTrack;
          _currentDuration = Duration(milliseconds: currentTrack.durationMs);
          _currentTrackController.add(_currentTrack);
          _durationController.add(_currentDuration);
        }
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error playing collection: $e');
      }
      return false;
    }
  }

  /// Create a song resource map for MusicKit, handling both catalog and user-uploaded tracks
  Map<String, dynamic> _createSongResource(MusicTrack track) {
    // Base song resource structure
    final Map<String, dynamic> songResource = {
      'id': track.id,
      'type': 'songs',
      'attributes': {
        'name': track.title,
        'artistName': track.artist,
        'albumName': track.album,
        'artwork': {
          'url': track.albumArt.isNotEmpty 
                 ? track.albumArt.replaceAll('{w}', '300').replaceAll('{h}', '300') 
                 : 'https://via.placeholder.com/300?text=No+Art',
        },
        'durationInMillis': track.durationMs,
      },
    };

    // Add playParams - this is crucial for playback
    final Map<String, dynamic> playParams = {
      'id': track.id,
      'kind': 'song',
      'isLibrary': track.isLibrary,
    };

    // For user-uploaded tracks (typically starting with 'i.' and having isLibrary: true)
    // we might need to handle them slightly differently
    if (track.isLibrary && track.id.startsWith('i.')) {
      // User-uploaded track - ensure we don't add catalog-specific parameters
      playParams['reporting'] = false;
      if (kDebugMode) {
        print('🔧 Creating resource for user-uploaded track: ${track.title}');
      }
    } else {
      // Catalog track - can include reporting
      playParams['reporting'] = true;
    }

    songResource['attributes']['playParams'] = playParams;

    // Add URL if available
    if (track.url.isNotEmpty) {
      songResource['attributes']['url'] = track.url;
    }

    return songResource;
  }

  /// Play a playlist starting from a specific track
  Future<bool> playPlaylist({
    required String playlistId,
    required List<MusicTrack> tracks,
    int startIndex = 0,
  }) async {
    return await playCollection(
      tracks: tracks,
      collectionType: 'playlist',
      startIndex: startIndex,
      collectionMetadata: {'playlistId': playlistId},
    );
  }

  /// Play an album starting from a specific track
  Future<bool> playAlbum({
    required String albumId,
    required List<MusicTrack> tracks,
    int startIndex = 0,
  }) async {
    return await playCollection(
      tracks: tracks,
      collectionType: 'album',
      startIndex: startIndex,
      collectionMetadata: {'albumId': albumId},
    );
  }

  /// Play recommendations starting from a specific track
  Future<bool> playRecommendations({
    required List<MusicTrack> tracks,
    int startIndex = 0,
  }) async {
    return await playCollection(
      tracks: tracks,
      collectionType: 'recommendations',
      startIndex: startIndex,
    );
  }
  
  /// Dispose the service
  void dispose() {
    _positionTimer?.cancel();
    _playerStateSubscription?.cancel();
    _playerQueueSubscription?.cancel();
    _queueManagerTrackSubscription?.cancel();
    
    _isPlayingController.close();
    _currentTrackController.close();
    _positionController.close();
    _durationController.close();
    
    // Dispose queue manager
    _queueManager.dispose();
    
    if (kDebugMode) {
      print('🍎 Apple Music player service disposed');
    }
  }

  /// Play a track by searching for it first (enhanced with exact matching)
  /// This method is used when the track is from another service or needs to be found
  /// Throws NoExactMatchException when no exact match is found to allow user-controlled fallback
  Future<void> playTrackBySearch(MusicTrack track) async {
    try {
      print('🍎 [AppleMusicPlayerService] Searching to play: \"${track.title}\" by \"${track.artist}\"');

             // Use the new enhanced search method for exact matching
       if (_appleMusicService == null) {
         throw Exception('Apple Music service not initialized');
       }

       final foundTrack = await _appleMusicService!.searchTrackByArtistAndTitle(
         track.artist,
         track.title,
         isrc: track.isrc, // Pass ISRC for exact matching
         spotifyUrl: track.url, // Pass Spotify URL to extract ISRC if needed
         targetDurationMs: track.durationMs, // Pass target duration for better matching
       );

      if (foundTrack != null) {
        // Verify this is actually a good match using the new matching utilities
        final matchScore = track.calculateMatchScore(foundTrack, originalPreferred: true);

        print('🔍 [AppleMusicPlayerService] Match score: ${matchScore.toStringAsFixed(1)}');
        print('🔍 [AppleMusicPlayerService] Original: ${track.matchDebugString}');
        print('🔍 [AppleMusicPlayerService] Found: ${foundTrack.matchDebugString}');

        if (track.isExcellentMatch(foundTrack, originalPreferred: true)) {
          print('✅ [AppleMusicPlayerService] EXCELLENT match found - playing exact song');
          await playTrack(foundTrack);
        } else if (track.isGoodMatch(foundTrack, originalPreferred: true)) {
          print('✅ [AppleMusicPlayerService] GOOD match found - playing similar song');
          await playTrack(foundTrack);
        } else {
          print('❌ [AppleMusicPlayerService] Match quality too low (${matchScore.toStringAsFixed(1)}) - rejecting to avoid wrong song');
          print('❌ [AppleMusicPlayerService] User requested: \"${track.title}\" by \"${track.artist}\"');
          print('❌ [AppleMusicPlayerService] Would have played: \"${foundTrack.title}\" by \"${foundTrack.artist}\"');
          throw NoExactMatchException('No exact match found for \"${track.title}\" by \"${track.artist}\"');
        }
      } else {
        print('❌ [AppleMusicPlayerService] No tracks found for \"${track.title}\" by \"${track.artist}\"');
        throw NoExactMatchException('Track not found in Apple Music catalog: \"${track.title}\" by \"${track.artist}\"');
      }

    } catch (e) {
      print('❌ [AppleMusicPlayerService] Error playing track by search: $e');
      rethrow;
    }
  }
} 