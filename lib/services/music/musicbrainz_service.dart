import 'dart:convert';
import 'dart:io';
import 'package:bop_maps/services/music/spotify_genre_service.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class MusicBrainzService {
  static const String _baseUrl = 'https://musicbrainz.org/ws/2';
  static const String _userAgent = 'BOPMaps/1.0.0 ( <EMAIL> )';
  
  // Rate limiting: MusicBrainz allows max 1 request per second per IP
  // Being conservative to avoid 503 errors which block AL<PERSON> requests
  static DateTime? _lastRequestTime;
  static const Duration _rateLimitDelay = Duration(milliseconds: 1200); // 1.2 seconds to be safe
  
  // Circuit breaker pattern to handle rate limiting
  static int _consecutiveRateLimitErrors = 0;
  static DateTime? _circuitBreakerOpenUntil;
  static const int _maxConsecutiveErrors = 3;
  static const Duration _circuitBreakerDuration = Duration(minutes: 2);

  // Cache for better performance
  static final Map<String, List<String>> _genreArtistsCache = {};
  static final Map<String, List<String>> _similarArtistsCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(hours: 1);

  /// Check if we should skip requests due to circuit breaker being open
  static bool _isCircuitBreakerOpen() {
    if (_circuitBreakerOpenUntil == null) return false;
    
    if (DateTime.now().isBefore(_circuitBreakerOpenUntil!)) {
      return true; // Circuit breaker is still open
    } else {
      // Circuit breaker duration has passed, reset
      _circuitBreakerOpenUntil = null;
      _consecutiveRateLimitErrors = 0;
      return false;
    }
  }
  
  /// Handle rate limit errors and potentially open circuit breaker
  static void _handleRateLimitError() {
    _consecutiveRateLimitErrors++;
    
    if (_consecutiveRateLimitErrors >= _maxConsecutiveErrors) {
      _circuitBreakerOpenUntil = DateTime.now().add(_circuitBreakerDuration);
      if (kDebugMode) {
        print('🚨 [MusicBrainz] Circuit breaker opened due to ${ _consecutiveRateLimitErrors} consecutive rate limit errors. Pausing requests for ${_circuitBreakerDuration.inMinutes} minutes.');
      }
    }
  }
  
  /// Reset rate limit error counter on successful request
  static void _resetRateLimitErrors() {
    _consecutiveRateLimitErrors = 0;
  }

  /// Check if cache entry is valid
  static bool _isCacheValid(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return false;
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  /// Ensure we don't exceed rate limits (1 request per second)
  static Future<void> _enforceRateLimit() async {
    if (_lastRequestTime != null) {
      final timeSinceLastRequest = DateTime.now().difference(_lastRequestTime!);
      if (timeSinceLastRequest < _rateLimitDelay) {
        final waitTime = _rateLimitDelay - timeSinceLastRequest;
        print('⏱️ [MusicBrainz] Rate limiting: waiting ${waitTime.inMilliseconds}ms');
        await Future.delayed(waitTime);
      }
    }
    _lastRequestTime = DateTime.now();
  }

  /// Make a request to MusicBrainz API with proper headers and rate limiting
  static Future<Map<String, dynamic>?> _makeRequest(String endpoint) async {
    try {
      // Check circuit breaker before making request
      if (_isCircuitBreakerOpen()) {
        if (kDebugMode) {
          print('🚨 [MusicBrainz] Circuit breaker is open, skipping request to: $endpoint');
        }
        return null;
      }
      
      await _enforceRateLimit();
      
      final url = '$_baseUrl$endpoint';
      print('🎵 [MusicBrainz] Making request to: $url');
      
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': _userAgent,
          'Accept': 'application/json',
        },
      );
      
      if (response.statusCode == 200) {
        print('✓ [MusicBrainz] Request successful: ${response.statusCode}');
        _resetRateLimitErrors(); // Reset error counter on success
        return json.decode(response.body) as Map<String, dynamic>;
      } else if (response.statusCode == 503) {
        print('⚠️ [MusicBrainz] Rate limited (503), handling gracefully...');
        _handleRateLimitError(); // Track rate limit error
        await Future.delayed(const Duration(seconds: 5));
        return null; // Don't retry automatically, let caller handle
      } else {
        print('❌ [MusicBrainz] Request failed: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('❌ [MusicBrainz] Request error: $e');
      return null;
    }
  }

  /// Enhanced tag-based search for artists by genre using MusicBrainz tags
  static Future<List<Map<String, dynamic>>> searchArtistsByTag(
    String tag, {
    int limit = 25,
    int offset = 0,
    int minScore = 70,
  }) async {
    try {
      final cacheKey = 'tag_$tag${limit}_$offset';
      if (_isCacheValid(cacheKey) && _genreArtistsCache.containsKey(cacheKey)) {
        print('💾 [MusicBrainz] Using cached results for tag: $tag');
        return _genreArtistsCache[cacheKey]!.map((name) => {'name': name}).toList();
      }

      print('🏷️ [MusicBrainz] Searching for artists with tag: $tag');
      
      // Multiple tag search strategies for better coverage
      final searchStrategies = [
        'tag:"$tag"',
        'tag:$tag',
        'tag:${tag.replaceAll(' ', '-')}',
        'tag:${tag.replaceAll(' ', '_')}',
      ];

      final allArtists = <Map<String, dynamic>>[];
      final seenNames = <String>{};

      for (final strategy in searchStrategies) {
        final cleanStrategy = strategy.replaceAll(' ', '%20');
        final endpoint = '/artist/?query=$cleanStrategy&limit=$limit&offset=$offset&fmt=json';
        final data = await _makeRequest(endpoint);
        
        if (data != null && data.containsKey('artists')) {
          final artists = data['artists'] as List;
          
          for (final artist in artists) {
            final name = artist['name'] as String? ?? 'Unknown Artist';
            final score = artist['score'] as int? ?? 0;
            
            // Filter by score and avoid duplicates
            if (score >= minScore && !seenNames.contains(name.toLowerCase()) && name != 'Unknown Artist') {
              seenNames.add(name.toLowerCase());
              allArtists.add({
                'id': artist['id'],
                'name': name,
                'sort_name': artist['sort-name'] ?? name,
                'score': score,
                'country': artist['country'],
                'tags': (artist['tags'] as List?)?.map((tag) => tag['name'] as String).toList() ?? <String>[],
                'type': artist['type'],
                'disambiguation': artist['disambiguation'],
              });
            }
          }
        }

        // Break early if we have enough high-quality results
        if (allArtists.length >= limit) break;
      }

      // Sort by score (relevance)
      allArtists.sort((a, b) => (b['score'] as int).compareTo(a['score'] as int));
      final result = allArtists.take(limit).toList();

      // Cache the results
      _genreArtistsCache[cacheKey] = result.map((a) => a['name'] as String).toList();
      _cacheTimestamps[cacheKey] = DateTime.now();
      
      print('✓ [MusicBrainz] Found ${result.length} artists for tag: $tag');
      return result;
      
    } catch (e) {
      print('❌ [MusicBrainz] Error searching artists by tag: $e');
      return [];
    }
  }

  /// Get similar genre tags for expansion
  static Future<List<String>> getSimilarTags(String tag, {int limit = 10}) async {
    try {
      print('🔗 [MusicBrainz] Finding similar tags to: $tag');
      
      final cacheKey = 'similar_tags_$tag';
      if (_isCacheValid(cacheKey) && _genreArtistsCache.containsKey(cacheKey)) {
        return _genreArtistsCache[cacheKey]!;
      }

      // Get artists with this tag first
      final artists = await searchArtistsByTag(tag, limit: 20);
      final relatedTags = <String, int>{};

      // Collect all tags from these artists
      for (final artist in artists) {
        final tags = artist['tags'] as List<String>? ?? [];
        for (final relatedTag in tags) {
          if (relatedTag.toLowerCase() != tag.toLowerCase()) {
            relatedTags[relatedTag] = (relatedTags[relatedTag] ?? 0) + 1;
          }
        }
      }

      // Sort by frequency and return most common related tags
      final sortedTags = relatedTags.entries
          .where((entry) => entry.value >= 2) // Must appear in at least 2 artists
          .map((entry) => entry.key)
          .toList();
      
      sortedTags.sort((a, b) => relatedTags[b]!.compareTo(relatedTags[a]!));
      final result = sortedTags.take(limit).toList();

      // Cache the results
      _genreArtistsCache[cacheKey] = result;
      _cacheTimestamps[cacheKey] = DateTime.now();

      print('✓ [MusicBrainz] Found ${result.length} similar tags to $tag');
      return result;
      
    } catch (e) {
      print('❌ [MusicBrainz] Error finding similar tags: $e');
      return [];
    }
  }

  /// Search for artists by genre
  static Future<List<Map<String, dynamic>>> searchArtistsByGenre(
    String genre, {
    int limit = 25,
    int offset = 0,
  }) async {
    try {
      print('🎤 [MusicBrainz] Searching for artists in genre: $genre');
      
      // Clean up genre name for search
      final cleanGenre = genre.toLowerCase().replaceAll(' ', '%20');
      
      final endpoint = '/artist/?query=tag:$cleanGenre&limit=$limit&offset=$offset&fmt=json';
      final data = await _makeRequest(endpoint);
      
      if (data == null || !data.containsKey('artists')) {
        return [];
      }
      
      final artists = data['artists'] as List;
      final result = artists.map((artist) => {
        'id': artist['id'],
        'name': artist['name'] ?? 'Unknown Artist',
        'sort_name': artist['sort-name'] ?? artist['name'],
        'score': artist['score'] ?? 0,
        'country': artist['country'],
        'life_span': artist['life-span'],
        'tags': (artist['tags'] as List?)?.map((tag) => tag['name'] as String).toList() ?? <String>[],
        'aliases': (artist['aliases'] as List?)?.map((alias) => alias['name'] as String).toList() ?? <String>[],
        'type': artist['type'],
        'disambiguation': artist['disambiguation'],
      }).toList();
      
      print('✓ [MusicBrainz] Found ${result.length} artists for genre: $genre');
      return result;
      
    } catch (e) {
      print('❌ [MusicBrainz] Error searching artists by genre: $e');
      return [];
    }
  }

  /// Enhanced similar artist discovery with multiple strategies
  static Future<List<String>> findSimilarArtistsByGenre(
    String genre, {
    int limit = 30,
    bool useRelatedTags = true,
  }) async {
    try {
      final cacheKey = 'similar_genre_$genre$limit';
      if (_isCacheValid(cacheKey) && _similarArtistsCache.containsKey(cacheKey)) {
        print('💾 [MusicBrainz] Using cached similar artists for genre: $genre');
        return _similarArtistsCache[cacheKey]!;
      }

      print('🎯 [MusicBrainz] Finding similar artists for genre: $genre');
      
      final similarArtists = <String>{};

      // Strategy 1: Direct genre/tag search
      final directArtists = await searchArtistsByTag(genre, limit: limit);
      for (final artist in directArtists) {
        similarArtists.add(artist['name'] as String);
      }
      
      // Strategy 2: Search related/similar tags if enabled
      if (useRelatedTags && similarArtists.length < limit) {
        final relatedTags = await getSimilarTags(genre, limit: 5);
        
        for (final relatedTag in relatedTags) {
          if (similarArtists.length >= limit) break;
          
          final tagArtists = await searchArtistsByTag(
            relatedTag, 
            limit: (limit - similarArtists.length).clamp(5, 10),
          );
          
          for (final artist in tagArtists) {
            if (similarArtists.length >= limit) break;
            similarArtists.add(artist['name'] as String);
          }
        }
      }

      final result = similarArtists.take(limit).toList();
      
      // Cache the results
      _similarArtistsCache[cacheKey] = result;
      _cacheTimestamps[cacheKey] = DateTime.now();
      
      print('✓ [MusicBrainz] Found ${result.length} similar artists for genre: $genre');
      return result;
      
    } catch (e) {
      print('❌ [MusicBrainz] Error finding similar artists by genre: $e');
      return [];
    }
  }

  /// Get artists for multiple genres efficiently
  static Future<Map<String, List<String>>> getArtistsForMultipleGenres(
    List<String> genres, {
    int artistsPerGenre = 20,
  }) async {
    try {
      print('🎵 [MusicBrainz] Getting artists for ${genres.length} genres');
      
      final result = <String, List<String>>{};
      
      for (final genre in genres) {
        final artists = await findSimilarArtistsByGenre(
          genre, 
          limit: artistsPerGenre,
          useRelatedTags: false, // Faster without related tags
        );
        result[genre] = artists;
        
        print('✓ [MusicBrainz] Genre $genre: ${artists.length} artists');
      }
      
      return result;
      
    } catch (e) {
      print('❌ [MusicBrainz] Error getting artists for multiple genres: $e');
      return {};
    }
  }

  /// Get related artists using MusicBrainz relationships
  static Future<List<Map<String, dynamic>>> getRelatedArtists(
    String artistId, {
    int limit = 10,
  }) async {
    try {
      print('🔗 [MusicBrainz] Getting related artists for ID: $artistId');
      
      final endpoint = '/artist/$artistId/?inc=artist-rels&fmt=json';
      final data = await _makeRequest(endpoint);
      
      if (data == null || !data.containsKey('relations')) {
        return [];
      }
      
      final relations = data['relations'] as List;
      final relatedArtists = <Map<String, dynamic>>[];
      
      for (final relation in relations) {
        if (relation['type'] == 'collaboration' || 
            relation['type'] == 'member' ||
            relation['type'] == 'similar' ||
            relation['target-type'] == 'artist') {
          
          final artist = relation['artist'];
          if (artist != null) {
            relatedArtists.add({
              'id': artist['id'],
              'name': artist['name'] ?? 'Unknown Artist',
              'sort_name': artist['sort-name'] ?? artist['name'],
              'relationship_type': relation['type'],
              'direction': relation['direction'] ?? 'forward',
            });
          }
        }
      }
      
      print('✓ [MusicBrainz] Found ${relatedArtists.length} related artists');
      return relatedArtists.take(limit).toList();
      
    } catch (e) {
      print('❌ [MusicBrainz] Error getting related artists: $e');
      return [];
    }
  }

  /// Check if an artist belongs to a target genre by inspecting MusicBrainz tags.
  static Future<bool> isArtistInGenre(String artistName, String targetGenre) async {
    try {
      final results = await searchArtists(artistName, limit: 3);
      if (results.isEmpty) return false;

      // Collect tags from top result
      final tags = (results.first['tags'] as List<String>? ?? <String>[]);
      return SpotifyGenreService.isValidArtistForTargetGenre(tags, targetGenre);
    } catch (e) {
      print('⚠️ [MusicBrainz] Genre check failed for $artistName: $e');
      return false;
    }
  }

  /// Search for artists with more flexible query
  static Future<List<Map<String, dynamic>>> searchArtists(
    String query, {
    int limit = 25,
    int offset = 0,
  }) async {
    try {
      print('🔍 [MusicBrainz] Searching artists with query: $query');
      
      final encodedQuery = Uri.encodeComponent(query);
      final endpoint = '/artist/?query=$encodedQuery&limit=$limit&offset=$offset&fmt=json';
      final data = await _makeRequest(endpoint);
      
      if (data == null || !data.containsKey('artists')) {
        return [];
      }
      
      final artists = data['artists'] as List;
      final result = artists.map((artist) => {
        'id': artist['id'],
        'name': artist['name'] ?? 'Unknown Artist',
        'sort_name': artist['sort-name'] ?? artist['name'],
        'score': artist['score'] ?? 0,
        'country': artist['country'],
        'tags': (artist['tags'] as List?)?.map((tag) => tag['name'] as String).toList() ?? <String>[],
        'type': artist['type'],
        'disambiguation': artist['disambiguation'],
      }).toList();
      
      print('✓ [MusicBrainz] Found ${result.length} artists for query: $query');
      return result;
      
    } catch (e) {
      print('❌ [MusicBrainz] Error searching artists: $e');
      return [];
    }
  }

  /// Get top artists by tag/genre with better scoring
  static Future<List<Map<String, dynamic>>> getTopArtistsByGenre(
    String genre, {
    int limit = 50,
  }) async {
    try {
      print('🏆 [MusicBrainz] Getting top artists in genre: $genre');
      
      // Get multiple pages to find the best artists
      final allArtists = <Map<String, dynamic>>[];
      
      for (int offset = 0; offset < 100; offset += 25) {
        final artists = await searchArtistsByGenre(
          genre, 
          limit: 25, 
          offset: offset,
        );
        
        if (artists.isEmpty) break;
        allArtists.addAll(artists);
        
        // Don't get too many pages to avoid hitting rate limits
        if (allArtists.length >= limit * 2) break;
      }
      
      // Sort by score (relevance) and filter out low-quality matches
      allArtists.sort((a, b) => (b['score'] as int).compareTo(a['score'] as int));
      
      // Filter out artists with very low scores or no proper names
      final filteredArtists = allArtists.where((artist) {
        final score = artist['score'] as int;
        final name = artist['name'] as String;
        return score > 60 && name.isNotEmpty && name != 'Unknown Artist';
      }).toList();
      
      final result = filteredArtists.take(limit).toList();
      print('✓ [MusicBrainz] Returning ${result.length} top artists for genre: $genre');
      
      return result;
      
    } catch (e) {
      print('❌ [MusicBrainz] Error getting top artists by genre: $e');
      return [];
    }
  }

  /// Get all available genres/tags
  static Future<List<String>> getAvailableGenres({int limit = 100}) async {
    try {
      print('🎯 [MusicBrainz] Getting available genres...');
      
      // MusicBrainz doesn't have a direct genres endpoint, but we can get popular tags
      final endpoint = '/tag/?limit=$limit&fmt=json';
      final data = await _makeRequest(endpoint);
      
      if (data == null || !data.containsKey('tags')) {
        return [];
      }
      
      final tags = data['tags'] as List;
      final genres = tags
          .map((tag) => tag['name'] as String?)
          .where((name) => name != null && name.isNotEmpty)
          .cast<String>()
          .toList();
      
      print('✓ [MusicBrainz] Found ${genres.length} available genres/tags');
      return genres;
      
    } catch (e) {
      print('❌ [MusicBrainz] Error getting available genres: $e');
      return [];
    }
  }

  /// Search for similar artists using multiple strategies
  static Future<List<String>> findSimilarArtists(
    String artistName,
    String genre, {
    int limit = 20,
  }) async {
    try {
      print('🔍 [MusicBrainz] Finding similar artists to: $artistName in genre: $genre');
      
      final similarArtists = <String>{};
      
      // Strategy 1: Find the artist and get their related artists
      final artistSearch = await searchArtists(artistName, limit: 5);
      if (artistSearch.isNotEmpty) {
        final artistId = artistSearch.first['id'] as String;
        final related = await getRelatedArtists(artistId);
        for (final artist in related) {
          similarArtists.add(artist['name'] as String);
        }
      }
      
      // Strategy 2: Get top artists in the same genre
      final genreArtists = await getTopArtistsByGenre(genre, limit: 30);
      for (final artist in genreArtists) {
        final name = artist['name'] as String;
        if (name.toLowerCase() != artistName.toLowerCase()) {
          similarArtists.add(name);
        }
      }
      
      // Strategy 3: Search for artists with similar tags
      if (artistSearch.isNotEmpty) {
        final tagsData = artistSearch.first['tags'];
        final tags = tagsData is List ? tagsData.cast<String>() : <String>[];
        for (final tag in tags.take(3)) {
          final tagArtists = await searchArtistsByGenre(tag, limit: 10);
          for (final artist in tagArtists) {
            final name = artist['name'] as String;
            if (name.toLowerCase() != artistName.toLowerCase()) {
              similarArtists.add(name);
            }
          }
        }
      }
      
      final result = similarArtists.take(limit).toList();
      print('✓ [MusicBrainz] Found ${result.length} similar artists to $artistName');
      
      return result;
      
    } catch (e) {
      print('❌ [MusicBrainz] Error finding similar artists: $e');
      return [];
    }
  }

  final Map<String, List<String>> _artistGenreCache = {};
  final Map<String, Map<String, dynamic>> _artistSearchCache = {}; // Cache search results for genre extraction

  Future<List<String>> getArtistGenres(String artistName) async {
    if (_artistGenreCache.containsKey(artistName)) {
      if (kDebugMode) {
        print('🧠 [MusicBrainz] Using cached genres for $artistName');
      }
      return _artistGenreCache[artistName]!;
    }
    
    // Check circuit breaker before making any requests
    if (_isCircuitBreakerOpen()) {
      if (kDebugMode) {
        print('🚨 [MusicBrainz] Circuit breaker is open, skipping genre lookup for "$artistName"');
      }
      return [];
    }

    if (kDebugMode) {
      print('🧠 [MusicBrainz] Fetching genres for "$artistName" from search results...');
    }

    try {
      final artistData = await _searchArtistWithData(artistName);
      if (artistData == null) {
        if (kDebugMode) {
          print('🤔 [MusicBrainz] Could not find artist data for "$artistName" - caching empty result');
        }
        _artistGenreCache[artistName] = [];
        return [];
      }

      if (kDebugMode) {
        print('🆔 [MusicBrainz] Found artist data for "$artistName": ${artistData['name']} (score: ${artistData['score']})');
      }

      final genres = <String>{};

      // Extract genres/tags from the search results (much more efficient!)
      if (artistData['tags'] != null && (artistData['tags'] as List).isNotEmpty) {
        final searchTags = artistData['tags'] as List;
        for (var tag in searchTags) {
          if (tag['name'] != null) {
            genres.add(tag['name'].toLowerCase());
          }
        }
        if (kDebugMode) {
          print('🏷️ [MusicBrainz] Extracted ${genres.length} genres from search tags for "$artistName": ${genres.toList()}');
        }
      } else {
        if (kDebugMode) {
          print('🚫 [MusicBrainz] No tags found in search results for "$artistName"');
        }
      }

      final genreList = genres.toList();
      if (kDebugMode) {
        print('📝 [MusicBrainz] Final genres for "$artistName": $genreList');
      }
      
      _artistGenreCache[artistName] = genreList;
      return genreList;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [MusicBrainz] Error fetching genres for $artistName: $e');
      }
      // Don't cache exceptions, allow retry
      return [];
    }
  }

  Future<Map<String, dynamic>?> _searchArtistWithData(String artistName) async {
    try {
      await _enforceRateLimit();
      
      // Try multiple search strategies
      final searchStrategies = [
        // Strategy 1: Exact name search with dismax (handles special chars automatically)
        {
          'query': Uri.encodeComponent(artistName),
          'dismax': 'true',
          'description': 'dismax exact search'
        },
        // Strategy 2: Artist field search (more precise)
        {
          'query': Uri.encodeComponent('artist:"$artistName"'),
          'dismax': 'false',
          'description': 'artist field search'
        },
        // Strategy 3: Name field search
        {
          'query': Uri.encodeComponent('name:"$artistName"'),
          'dismax': 'false', 
          'description': 'name field search'
        },
        // Strategy 4: Fuzzy search without quotes
        {
          'query': Uri.encodeComponent(artistName.replaceAll(RegExp(r'[^\w\s]'), ' ')),
          'dismax': 'true',
          'description': 'fuzzy search'
        }
      ];

      for (final strategy in searchStrategies) {
        if (kDebugMode) {
          print('🔍 [MusicBrainz] Trying ${strategy['description']} for "$artistName"');
        }
        
        final url = Uri.parse('${_baseUrl}/artist/?query=${strategy['query']}&limit=5&fmt=json&dismax=${strategy['dismax']}');
        
        if (kDebugMode) {
          print('🌐 [MusicBrainz] Request URL: $url');
        }
        
        final response = await http.get(url, headers: {
          'Accept': 'application/json',
          'User-Agent': 'BOPMaps/1.0.0 ( <EMAIL> )'
        });

        if (kDebugMode) {
          print('📡 [MusicBrainz] Response status: ${response.statusCode}');
          print('📄 [MusicBrainz] Raw response body: ${response.body}');
        }

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['artists'] != null && (data['artists'] as List).isNotEmpty) {
            final artists = data['artists'] as List;
            
            if (kDebugMode) {
              print('🎤 [MusicBrainz] Found ${artists.length} artists for "$artistName":');
              for (var i = 0; i < artists.length; i++) {
                final artist = artists[i];
                print('  [$i] ${artist['name']} (score: ${artist['score']}, id: ${artist['id']})');
              }
            }
            
            // Look for exact or high-score matches first
            for (final artist in artists) {
              final name = artist['name'] as String;
              final score = artist['score'] as int? ?? 0;
              
              // Exact match or very high score
              if (name.toLowerCase() == artistName.toLowerCase() || score >= 95) {
                if (kDebugMode) {
                  print('✅ [MusicBrainz] Selected artist: $name (score: $score) for "$artistName"');
                }
                return artist;
              }
            }
            
            // If no exact match, take the highest scored result if it's reasonable
            final bestMatch = artists.first;
            final bestScore = bestMatch['score'] as int? ?? 0;
            if (bestScore >= 80) {
              if (kDebugMode) {
                print('✅ [MusicBrainz] Selected best match: ${bestMatch['name']} (score: $bestScore) for "$artistName"');
              }
              return bestMatch;
            }
          }
        } else if (response.statusCode == 503) {
          if (kDebugMode) {
            print('⏳ [MusicBrainz] Rate limited (503), waiting longer before retry...');
          }
          // Wait longer when rate limited to let the rate drop
          await Future.delayed(const Duration(seconds: 5));
        } else {
          if (kDebugMode) {
            print('❌ [MusicBrainz] Request failed with status ${response.statusCode}: ${response.body}');
          }
        }
        
        // Small delay between strategies to avoid overwhelming the API
        await Future.delayed(const Duration(milliseconds: 200));
      }
      
      if (kDebugMode) {
        print('❌ [MusicBrainz] No suitable match found for "$artistName" after trying all strategies');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [MusicBrainz] Error searching for artist $artistName: $e');
      }
    }
    return null;
  }

  /// Clear caches
  static void clearCache() {
    _genreArtistsCache.clear();
    _similarArtistsCache.clear();
    _cacheTimestamps.clear();
    print('🧹 [MusicBrainz] Cache cleared');
  }
} 