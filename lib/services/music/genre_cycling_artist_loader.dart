import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';

import '../../models/artist_with_genre.dart';
import '../../utils/cycling_loader_state.dart';
import 'spotify_service.dart';

class GenreCyclingArtistLoader {
  final List<String> _selectedGenres;
  final SpotifyService _spotifyService;
  
  int _currentGenreIndex = 0;
  final Map<String, int> _genreBatchCounts = {};
  static const int ARTISTS_PER_GENRE_BATCH = 10; // Artists shown to user per batch
  static const int ARTISTS_TO_PREFETCH_PER_GENRE = 50; // Artists to fetch and store per genre
  static const int MAX_RETRIES_PER_GENRE = 2;
  
  // Enhanced cache to store more artists per genre
  final Map<String, List<ArtistWithGenre>> _genreArtistCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Map<String, int> _genreArtistIndexes = {}; // Track current index for each genre
  static const Duration CACHE_DURATION = Duration(minutes: 30);

  GenreCyclingArtistLoader({
    required List<String> selectedGenres,
    required SpotifyService spotifyService,
  }) : _selectedGenres = List.from(selectedGenres),
       _spotifyService = spotifyService {
    print('🎵 [GenreCyclingArtistLoader] Initialized with ${_selectedGenres.length} genres: ${_selectedGenres.join(", ")}');
  }

  /// Load next batch of artists by cycling through genres
  Future<ArtistSelectionState> loadNextBatch(ArtistSelectionState currentState) async {
    if (_selectedGenres.isEmpty) {
      print('❌ [GenreCyclingArtistLoader] No genres selected');
      return currentState.withError('No genres selected');
    }

    print('🔄 [GenreCyclingArtistLoader] Loading next batch (current index: $_currentGenreIndex)');

    try {
      final List<ArtistWithGenre> newArtists = [];
      final Set<String> seenArtistKeys = currentState.items.map((a) => a.uniqueKey).toSet();
      int attemptsWithoutResults = 0;
      var maxAttemptsWithoutResults = _selectedGenres.length * 2; // Allow cycling through all genres twice

      while (newArtists.length < ARTISTS_PER_GENRE_BATCH && 
             attemptsWithoutResults < maxAttemptsWithoutResults) {
        
        final currentGenre = _selectedGenres[_currentGenreIndex];
        
        print('🎯 [GenreCyclingArtistLoader] Loading from genre: "$currentGenre"');

        try {
          // Get artists from prefetched cache or fetch if needed
          final genreArtists = await _getArtistsFromGenre(currentGenre);
          
          // Filter out artists we already have
          final filteredArtists = genreArtists
              .where((artist) => !seenArtistKeys.contains(artist.uniqueKey))
              .toList();

          if (filteredArtists.isNotEmpty) {
            // Take only the number we need for this batch
            final artistsToAdd = filteredArtists.take(ARTISTS_PER_GENRE_BATCH - newArtists.length).toList();
            newArtists.addAll(artistsToAdd);
            seenArtistKeys.addAll(artistsToAdd.map((a) => a.uniqueKey));
            
            // Update the index for this genre so we get different artists next time
            final currentIndex = _genreArtistIndexes[currentGenre] ?? 0;
            _genreArtistIndexes[currentGenre] = currentIndex + artistsToAdd.length;
            
            print('✅ [GenreCyclingArtistLoader] Added ${artistsToAdd.length} artists from "$currentGenre" (index now: ${_genreArtistIndexes[currentGenre]})');
            attemptsWithoutResults = 0; // Reset counter on success
          } else {
            print('⚠️ [GenreCyclingArtistLoader] No new artists available from "$currentGenre"');
            attemptsWithoutResults++;
          }

        } catch (e) {
          print('❌ [GenreCyclingArtistLoader] Error loading from genre "$currentGenre": $e');
          attemptsWithoutResults++;
        }

        // Move to next genre
        _currentGenreIndex = (_currentGenreIndex + 1) % _selectedGenres.length;
      }

      if (newArtists.isEmpty) {
        print('⚠️ [GenreCyclingArtistLoader] No new artists loaded after cycling through genres');
        return currentState.success(
          newItems: [],
          newIndex: _currentGenreIndex,
          newBatchCounts: Map.from(_genreBatchCounts),
          hasMore: false,
        );
      }

      // Sort by popularity to show best artists first
      newArtists.sort((a, b) => b.popularity.compareTo(a.popularity));

      print('🎉 [GenreCyclingArtistLoader] Successfully loaded ${newArtists.length} new artists');

      return currentState.success(
        newItems: newArtists,
        newIndex: _currentGenreIndex,
        newBatchCounts: Map.from(_genreBatchCounts),
        hasMore: true,
      );

    } catch (e) {
      print('❌ [GenreCyclingArtistLoader] Error in loadNextBatch: $e');
      return currentState.withError('Failed to load artists: ${e.toString()}');
    }
  }

  /// Get artists from genre with prefetching strategy
  Future<List<ArtistWithGenre>> _getArtistsFromGenre(String genre) async {
    final cacheKey = '${genre}_prefetched';
    
    // Check if we have prefetched artists for this genre
    if (_isValidCache(cacheKey)) {
      final cachedArtists = _genreArtistCache[cacheKey]!;
      final currentIndex = _genreArtistIndexes[genre] ?? 0;
      
      // Return remaining artists from the current index
      if (currentIndex < cachedArtists.length) {
        final remainingArtists = cachedArtists.skip(currentIndex).toList();
        print('📦 [GenreCyclingArtistLoader] Using cached artists for "$genre" (${remainingArtists.length} remaining from index $currentIndex)');
        return remainingArtists;
      } else {
        // We've used all cached artists, try to fetch more
        print('🔄 [GenreCyclingArtistLoader] All cached artists used for "$genre", fetching more...');
        return await _prefetchMoreArtistsForGenre(genre, currentIndex);
      }
    }

    // No cache, prefetch artists for this genre
    print('🔍 [GenreCyclingArtistLoader] Prefetching artists for "$genre"...');
    return await _prefetchArtistsForGenre(genre);
  }

  /// Prefetch a large number of artists for a genre
  Future<List<ArtistWithGenre>> _prefetchArtistsForGenre(String genre) async {
    final cacheKey = '${genre}_prefetched';
    
    try {
      final allArtists = <ArtistWithGenre>[];
      final seenArtistIds = <String>{};
      
      // Fetch artists in batches to get a good variety
      for (int offset = 0; offset < ARTISTS_TO_PREFETCH_PER_GENRE; offset += 20) {
        final result = await _spotifyService.searchArtistsByGenrePaginated(
          genre,
          limit: 20,
          offset: offset,
        );

        final List<Map<String, dynamic>> artistsData = result['artists'] as List<Map<String, dynamic>>? ?? [];
        
        if (artistsData.isEmpty) {
          print('⚠️ [GenreCyclingArtistLoader] No more artists available for "$genre" at offset $offset');
          break;
        }

        // Convert to ArtistWithGenre objects and filter duplicates
        for (final artistData in artistsData) {
          final artistId = artistData['id'] as String?;
          if (artistId != null && !seenArtistIds.contains(artistId)) {
            seenArtistIds.add(artistId);
            final artist = ArtistWithGenre.fromSpotifyResponse(artistData, genre);
            allArtists.add(artist);
          }
        }

        // Stop if we have enough artists
        if (allArtists.length >= ARTISTS_TO_PREFETCH_PER_GENRE) {
          break;
        }
      }

      // Sort by popularity to ensure best artists are served first
      allArtists.sort((a, b) => b.popularity.compareTo(a.popularity));

      // Cache the prefetched artists
      _genreArtistCache[cacheKey] = allArtists;
      _cacheTimestamps[cacheKey] = DateTime.now();
      _genreArtistIndexes[genre] = 0; // Reset index for this genre

      print('✅ [GenreCyclingArtistLoader] Prefetched ${allArtists.length} artists for "$genre"');
      return allArtists;

    } catch (e) {
      print('❌ [GenreCyclingArtistLoader] Error prefetching artists for genre "$genre": $e');
      return [];
    }
  }

  /// Fetch more artists when we've exhausted the prefetched ones
  Future<List<ArtistWithGenre>> _prefetchMoreArtistsForGenre(String genre, int startOffset) async {
    try {
      final moreArtists = <ArtistWithGenre>[];
      final seenArtistIds = <String>{};
      
      // Get existing cached artists to avoid duplicates
      final cacheKey = '${genre}_prefetched';
      final existingArtists = _genreArtistCache[cacheKey] ?? [];
      for (final artist in existingArtists) {
        seenArtistIds.add(artist.id);
      }
      
      // Fetch more artists starting from where we left off
      for (int offset = startOffset; offset < startOffset + ARTISTS_TO_PREFETCH_PER_GENRE; offset += 20) {
        final result = await _spotifyService.searchArtistsByGenrePaginated(
          genre,
          limit: 20,
          offset: offset,
        );

        final List<Map<String, dynamic>> artistsData = result['artists'] as List<Map<String, dynamic>>? ?? [];
        
        if (artistsData.isEmpty) {
          print('⚠️ [GenreCyclingArtistLoader] No more artists available for "$genre" at offset $offset');
          break;
        }

        // Convert and filter duplicates
        for (final artistData in artistsData) {
          final artistId = artistData['id'] as String?;
          if (artistId != null && !seenArtistIds.contains(artistId)) {
            seenArtistIds.add(artistId);
            final artist = ArtistWithGenre.fromSpotifyResponse(artistData, genre);
            moreArtists.add(artist);
          }
        }

        if (moreArtists.length >= 25) { // Get at least 25 more artists
          break;
        }
      }

      // Sort new artists by popularity
      moreArtists.sort((a, b) => b.popularity.compareTo(a.popularity));

      // Append to existing cache
      final updatedArtists = [...existingArtists, ...moreArtists];
      _genreArtistCache[cacheKey] = updatedArtists;
      _cacheTimestamps[cacheKey] = DateTime.now();

      print('✅ [GenreCyclingArtistLoader] Fetched ${moreArtists.length} more artists for "$genre" (total: ${updatedArtists.length})');
      return moreArtists;

    } catch (e) {
      print('❌ [GenreCyclingArtistLoader] Error fetching more artists for genre "$genre": $e');
      return [];
    }
  }

  /// Load artists for a specific genre with caching (legacy method, kept for compatibility)
  Future<List<ArtistWithGenre>> _loadArtistsForGenre(
    String genre, {
    required int offset,
    required int limit,
  }) async {
    final cacheKey = '${genre}_${offset}_$limit';
    
    // Check cache first
    if (_isValidCache(cacheKey)) {
      print('📦 [GenreCyclingArtistLoader] Using cached artists for "$genre" (offset: $offset)');
      return _genreArtistCache[cacheKey]!;
    }

    print('🔍 [GenreCyclingArtistLoader] Fetching artists from Spotify for "$genre" (offset: $offset, limit: $limit)');

    try {
      // Use the existing SpotifyService method
      final result = await _spotifyService.searchArtistsByGenrePaginated(
        genre,
        limit: limit,
        offset: offset,
      );

      final List<Map<String, dynamic>> artistsData = result['artists'] as List<Map<String, dynamic>>? ?? [];
      
      if (artistsData.isEmpty) {
        print('⚠️ [GenreCyclingArtistLoader] No artists returned from Spotify for "$genre"');
        return [];
      }

      // Convert to ArtistWithGenre objects
      final artists = artistsData.map((artistData) {
        return ArtistWithGenre.fromSpotifyResponse(artistData, genre);
      }).toList();

      // Cache the results
      _genreArtistCache[cacheKey] = artists;
      _cacheTimestamps[cacheKey] = DateTime.now();

      print('✅ [GenreCyclingArtistLoader] Loaded ${artists.length} artists for "$genre"');
      return artists;

    } catch (e) {
      print('❌ [GenreCyclingArtistLoader] Error loading artists for genre "$genre": $e');
      
      // Return empty list instead of throwing to allow cycling to continue
      return [];
    }
  }

  /// Check if cache entry is valid
  bool _isValidCache(String cacheKey) {
    if (!_genreArtistCache.containsKey(cacheKey)) return false;
    
    final timestamp = _cacheTimestamps[cacheKey];
    if (timestamp == null) return false;
    
    return DateTime.now().difference(timestamp) < CACHE_DURATION;
  }

  /// Reset the loader state
  void reset() {
    print('🔄 [GenreCyclingArtistLoader] Resetting loader state');
    _currentGenreIndex = 0;
    _genreBatchCounts.clear();
    // Keep cache for performance
  }

  /// Clear all caches
  void clearCache() {
    print('🧹 [GenreCyclingArtistLoader] Clearing cache');
    _genreArtistCache.clear();
    _cacheTimestamps.clear();
  }

  /// Get current loader statistics
  Map<String, dynamic> getStats() {
    return {
      'currentGenreIndex': _currentGenreIndex,
      'currentGenre': _selectedGenres.isNotEmpty ? _selectedGenres[_currentGenreIndex] : null,
      'genreBatchCounts': Map.from(_genreBatchCounts),
      'cacheSize': _genreArtistCache.length,
      'selectedGenres': List.from(_selectedGenres),
    };
  }

  /// Update selected genres (useful if user changes genre selection)
  void updateGenres(List<String> newGenres) {
    print('🔄 [GenreCyclingArtistLoader] Updating genres from ${_selectedGenres.length} to ${newGenres.length}');
    _selectedGenres.clear();
    _selectedGenres.addAll(newGenres);
    reset();
  }
}