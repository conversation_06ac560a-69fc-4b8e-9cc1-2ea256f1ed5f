import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:spotify_sdk/spotify_sdk.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../config/constants.dart';
import '../../models/music_track.dart';
import 'spotify_service.dart';
import 'spotify_connection_service.dart';

/// SpotifyPlayerService handles playback of Spotify tracks
/// It uses the Spotify SDK to control playback on the user's Spotify app
class SpotifyPlayerService {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final _spotifyService = SpotifyService();
  final SpotifyConnectionService _connectionService = SpotifyConnectionService();
  
  // Status
  bool _isInitialized = false;
  bool _isConnected = false;
  bool _isPremiumUser = false;
  bool _isPlaying = false;
  MusicTrack? _currentTrack;
  
  // Connection management
  DateTime? _lastConnectionAttempt;
  int _connectionAttempts = 0;
  static const int _maxConnectionAttempts = 3;
  static const Duration _connectionCooldown = Duration(seconds: 10);
  
  // Stream controllers for player state
  final _playbackStateController = StreamController<PlaybackState>.broadcast();
  final _currentTrackController = StreamController<MusicTrack?>.broadcast();
  final _errorController = StreamController<String>.broadcast();
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isConnected => _isConnected;
  bool get isPremiumUser => _isPremiumUser;
  bool get isPlaying => _isPlaying;
  MusicTrack? get currentTrack => _currentTrack;
  
  // Streams
  Stream<PlaybackState> get playbackStateStream => _playbackStateController.stream;
  Stream<MusicTrack?> get currentTrackStream => _currentTrackController.stream;
  Stream<String> get errorStream => _errorController.stream;
  
  // Initialize the player service
  Future<bool> initialize() async {
    try {
      if (kDebugMode) {
        print('SpotifyPlayerService: Starting initialization');
      }
      
      // Check connection cooldown
      if (_lastConnectionAttempt != null && 
          DateTime.now().difference(_lastConnectionAttempt!) < _connectionCooldown) {
        if (kDebugMode) {
          print('SpotifyPlayerService: Connection attempt too recent, waiting...');
        }
        return false;
      }
      
      _lastConnectionAttempt = DateTime.now();
      _connectionAttempts++;
      
      if (_connectionAttempts > _maxConnectionAttempts) {
        if (kDebugMode) {
          print('SpotifyPlayerService: Max connection attempts reached');
        }
        return false;
      }
      
      // Get access token
      final accessToken = await _secureStorage.read(key: 'spotify_access_token');
      if (accessToken == null) {
        if (kDebugMode) {
          print('SpotifyPlayerService: No access token found');
        }
        return false;
      }
      
      // Check if we're already connected and working
      if (_isConnected) {
        try {
          final playerState = await SpotifySdk.getPlayerState();
          if (playerState != null) {
            if (kDebugMode) {
              print('SpotifyPlayerService: Already connected and working');
            }
            return true;
          }
        } catch (e) {
          if (kDebugMode) {
            print('SpotifyPlayerService: Existing connection is stale: $e');
          }
          _isConnected = false;
        }
      }
      
      // Always disconnect first to ensure clean state
      try {
        await SpotifySdk.disconnect();
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        // Ignore disconnect errors
        if (kDebugMode) {
          print('SpotifyPlayerService: Disconnect error (expected): $e');
        }
      }
      
      // Connect to Spotify SDK
      try {
        await SpotifySdk.connectToSpotifyRemote(
          clientId: AppConstants.spotifyClientId,
          redirectUrl: AppConstants.spotifyRedirectUri,
        );
        
        if (kDebugMode) {
          print('✅ Connected to Spotify Remote');
        }
        
        // Wait for connection to stabilize
        await Future.delayed(const Duration(milliseconds: 1000));
        
        // Verify connection by checking if we can get player state
        final playerState = await SpotifySdk.getPlayerState();
        
        // Check premium status
        _isPremiumUser = await _spotifyService.isPremiumUser();
        
        // Set up state subscription with error handling
        SpotifySdk.subscribePlayerState().listen(
          (playerState) {
            _handlePlayerStateUpdate(playerState);
          },
          onError: (error) {
            if (kDebugMode) {
              print('SpotifyPlayerService: Player state subscription error - $error');
            }
            _errorController.add('Lost connection to Spotify player: $error');
            _isConnected = false;
          },
          cancelOnError: false,  // Don't cancel subscription on error
        );
        
        _isInitialized = true;
        _isConnected = true;
        _connectionAttempts = 0; // Reset on successful connection
        
        if (kDebugMode) {
          print('SpotifyPlayerService: Initialization successful');
        }
        
        return true;
      } catch (e) {
        if (kDebugMode) {
          print('SpotifyPlayerService: Connection failed - $e');
        }
        _isConnected = false;
        _isInitialized = false;
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('SpotifyPlayerService: Initialization failed - $e');
      }
      return false;
    }
  }
  
  /// Handle player state updates
  void _handlePlayerStateUpdate(dynamic playerState) {
    if (playerState != null) {
      _isPlaying = !playerState.isPaused!;
      
      if (playerState.track != null) {
        _currentTrack = MusicTrack(
          id: playerState.track!.uri,
          title: playerState.track!.name ?? 'Unknown Track',
          artist: playerState.track!.artist.name ?? 'Unknown Artist',
          album: playerState.track!.album.name ?? '',
          albumArt: playerState.track!.imageUri.raw,
          uri: playerState.track!.uri,
          durationMs: playerState.track!.duration,
          url: 'https://open.spotify.com/track/${playerState.track!.uri.split(":").last}',
          service: 'spotify',
          serviceType: 'spotify',
          genres: [],
          explicit: false,
          popularity: 0,
        );
        
        _currentTrackController.add(_currentTrack);
      } else {
        // No track means we might have lost the active device
        _isPlaying = false;
        if (kDebugMode) {
          print('SpotifyPlayerService: No active track in player state');
        }
      }
      
      _playbackStateController.add(
        PlaybackState(
          isPlaying: _isPlaying,
          isPaused: playerState.isPaused ?? true,
          track: _currentTrack,
          position: playerState.playbackPosition,
          duration: playerState.track?.duration ?? 0,
          isPremium: _isPremiumUser,
        ),
      );
    } else {
      // Null player state means we lost connection
      _isPlaying = false;
      if (kDebugMode) {
        print('SpotifyPlayerService: Received null player state');
      }
      
      _playbackStateController.add(
        PlaybackState(
          isPlaying: false,
          isPaused: true,
          track: _currentTrack,
          position: 0,
          duration: 0,
          isPremium: _isPremiumUser,
        ),
      );
    }
  }
  
  /// Check if connection is healthy
  Future<bool> _isConnectionHealthy() async {
    if (!_isConnected) return false;
    
    try {
      final playerState = await SpotifySdk.getPlayerState();
      return playerState != null;
    } catch (e) {
      if (kDebugMode) {
        print('SpotifyPlayerService: Connection health check failed: $e');
      }
      return false;
    }
  }
  
  /// Ensure device is active and ready for playback
  Future<bool> _ensureDeviceActive() async {
    try {
      // First check if device is already active
      try {
        final currentState = await SpotifySdk.getPlayerState();
        if (currentState != null) {
          print('✅ [SpotifyPlayerService] Device already active');
          return true;
        }
      } catch (e) {
        print('⚠️ [SpotifyPlayerService] Device not active, will try to activate');
      }
      
      for (int attempt = 0; attempt < 3; attempt++) {
        try {
          switch (attempt) {
            case 0:
              // First try: Resume any existing playback
              await SpotifySdk.resume();
              break;
              
            case 1:
              // Second try: Try to transfer playback to this device
              await SpotifySdk.connectToSpotifyRemote(
                clientId: AppConstants.spotifyClientId,
                redirectUrl: AppConstants.spotifyRedirectUri,
              );
              break;
              
            case 2:
              // Third try: Play a silent track
              await SpotifySdk.play(spotifyUri: 'spotify:track:4iV5W9uYEdYUVa79Axb7Rh');
              await Future.delayed(const Duration(milliseconds: 500));
              await SpotifySdk.pause();
              break;
          }
          
          // Wait longer for device activation
          await Future.delayed(Duration(milliseconds: 2000));
          
          // Check if device is now active
          final newPlayerState = await SpotifySdk.getPlayerState();
          if (newPlayerState != null) {
            print('✅ [SpotifyPlayerService] Successfully activated device on attempt ${attempt + 1}');
            return true;
          }
        } catch (e) {
          print('⚠️ [SpotifyPlayerService] Activation attempt ${attempt + 1} failed: $e');
          
          // If we get a 400 error, it might mean we need to open Spotify app
          if (e.toString().contains('400')) {
            _errorController.add('Please open the Spotify app and try again');
            return false;
          }
          
          // Wait before next attempt
          if (attempt < 2) {
            await Future.delayed(Duration(seconds: attempt + 1));
          }
        }
      }
      
      print('❌ [SpotifyPlayerService] Could not activate device after all attempts');
      _errorController.add('Could not activate Spotify device. Please open Spotify and start playing something, then try again.');
      return false;
    } catch (e) {
      print('❌ [SpotifyPlayerService] Error ensuring device active: $e');
      _errorController.add('Error activating Spotify device: $e');
      return false;
    }
  }
  
  /// Play a specific Spotify track
  Future<bool> playTrack(MusicTrack track) async {
    final operationId = 'play_${track.id}';
    
    try {
      print('SpotifyPlayerService: Attempting to play track: ${track.uri}');
      
      // Track this operation attempt
      if (!await _connectionService.trackOperation(operationId, SpotifyOperationType.play)) {
        print('⚠️ Too many play attempts for track ${track.title}, skipping');
        return false;
      }

      // Ensure device is active
      if (!await _ensureDeviceActive()) {
        print('❌ [SpotifyPlayerService] Failed to ensure device is active');
        return false;
      }

      // Try to play the track multiple times
      for (int attempt = 0; attempt < 3; attempt++) {
        try {
          await SpotifySdk.play(spotifyUri: track.uri);
          
          // Verify playback started
          final success = await _verifyPlayback();
          if (success) {
            print('✅ [SpotifyPlayerService] Track playback initiated successfully');
            return true;
          }
          
          // If verification failed but no exception was thrown, wait and retry
          if (attempt < 2) {
            print('⚠️ [SpotifyPlayerService] Playback verification failed, retrying...');
            await Future.delayed(Duration(seconds: attempt + 1));
          }
        } catch (e) {
          print('⚠️ [SpotifyPlayerService] Play attempt ${attempt + 1} failed: $e');
          if (attempt < 2) {
            await Future.delayed(Duration(seconds: attempt + 1));
          }
        }
      }
      
      print('❌ [SpotifyPlayerService] Failed to play track after all attempts');
      return false;
    } catch (e) {
      print('❌ [SpotifyPlayerService] Error playing track: $e');
      return false;
    }
  }
  
  /// Resume playback
  Future<bool> resume() async {
    try {
      if (!await _connectionService.trackOperation('resume', SpotifyOperationType.play)) {
        return false;
      }
      await SpotifySdk.resume();
      _isPlaying = true;
      return true;
    } catch (e) {
      print('❌ [SpotifyPlayerService] Error resuming: $e');
      _errorController.add('Error resuming playback: $e');
      return false;
    }
  }
  
  /// Pause playback
  Future<bool> pause() async {
    try {
      if (!await _connectionService.trackOperation('pause', SpotifyOperationType.pause)) {
        return false;
      }
      await SpotifySdk.pause();
      _isPlaying = false;
      return true;
    } catch (e) {
      print('❌ [SpotifyPlayerService] Error pausing: $e');
      _errorController.add('Error pausing playback: $e');
      return false;
    }
  }
  
  /// Skip to next track
  Future<bool> skipNext() async {
    try {
      await SpotifySdk.skipNext();
      return true;
    } catch (e) {
      _errorController.add('Error skipping to next track: $e');
      return false;
    }
  }
  
  /// Skip to previous track
  Future<bool> skipPrevious() async {
    try {
      await SpotifySdk.skipPrevious();
      return true;
    } catch (e) {
      _errorController.add('Error skipping to previous track: $e');
      return false;
    }
  }
  
  /// Seek to position
  Future<bool> seekTo(int positionMs) async {
    try {
      if (!await _connectionService.trackOperation('seek_$positionMs', SpotifyOperationType.seek)) {
        return false;
      }
      await SpotifySdk.seekTo(positionedMilliseconds: positionMs);
      return true;
    } catch (e) {
      print('❌ [SpotifyPlayerService] Error seeking: $e');
      _errorController.add('Error seeking: $e');
      return false;
    }
  }
  
  /// Toggle shuffle mode
  Future<bool> toggleShuffle() async {
    try {
      await SpotifySdk.toggleShuffle();
      return true;
    } catch (e) {
      _errorController.add('Error toggling shuffle: $e');
      return false;
    }
  }
  
  /// Set repeat mode
  Future<bool> setRepeatMode(RepeatMode repeatMode) async {
    try {
      await SpotifySdk.setRepeatMode(repeatMode: repeatMode);
      return true;
    } catch (e) {
      _errorController.add('Error setting repeat mode: $e');
      return false;
    }
  }
  
  /// Get current playback position
  Future<int> getPosition() async {
    try {
      final playerState = await SpotifySdk.getPlayerState();
      return playerState?.playbackPosition ?? 0;
    } catch (e) {
      _errorController.add('Error getting position: $e');
      return 0;
    }
  }
  
  /// Disconnect from Spotify
  Future<void> disconnect() async {
    try {
      await SpotifySdk.disconnect();
      _isConnected = false;
      _isInitialized = false;
    } catch (e) {
      _errorController.add('Error disconnecting: $e');
    }
  }
  
  /// Dispose resources
  void dispose() {
    disconnect();
    _playbackStateController.close();
    _currentTrackController.close();
    _errorController.close();
  }

  Future<bool> _verifyPlayback() async {
    try {
      // Give Spotify more time to start playback and try multiple times
      for (int attempt = 0; attempt < 3; attempt++) {
        // Increasing delay between attempts
        await Future.delayed(Duration(milliseconds: 1000 * (attempt + 1)));
        
        final playerState = await SpotifySdk.getPlayerState();
        if (playerState != null) {
          final isPlaying = !(playerState as dynamic).isPaused;
          if (isPlaying) {
            print('✅ [SpotifyPlayerService] Playback verified on attempt ${attempt + 1}');
            return true;
          }
          print('⚠️ [SpotifyPlayerService] Track not playing yet on attempt ${attempt + 1}');
        }
      }
      
      print('❌ [SpotifyPlayerService] Failed to verify playback after all attempts');
      return false;
    } catch (e) {
      print('❌ [SpotifyPlayerService] Error verifying playback: $e');
      return false;
    }
  }
}

/// Playback state model to track current playback status
class PlaybackState {
  final bool isPlaying;
  final bool isPaused;
  final MusicTrack? track;
  final int position;
  final int duration;
  final bool isPremium;
  
  PlaybackState({
    required this.isPlaying,
    required this.isPaused,
    this.track,
    required this.position,
    this.duration = 0,
    this.isPremium = false,
  });
} 