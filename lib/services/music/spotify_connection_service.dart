import 'dart:async';
import 'package:flutter/material.dart';
import 'package:spotify_sdk/spotify_sdk.dart';
import '../../config/constants.dart';

enum SpotifyConnectionState {
  disconnected,
  connecting,
  connected,
  error,
  authenticating
}

enum SpotifyOperationType {
  play,
  pause,
  skip,
  seek,
  other
}

class SpotifyOperationAttempt {
  final String identifier;
  final DateTime timestamp;
  final SpotifyOperationType type;

  SpotifyOperationAttempt(this.identifier, this.type)
      : timestamp = DateTime.now();
}

class SpotifyConnectionService {
  static final SpotifyConnectionService _instance = SpotifyConnectionService._internal();
  factory SpotifyConnectionService() => _instance;
  
  // Operation tracking
  final Map<String, List<SpotifyOperationAttempt>> _operationAttempts = {};
  static const int _maxOperationAttempts = 2;
  static const Duration _operationAttemptWindow = Duration(minutes: 1);
  
  SpotifyConnectionService._internal() {
    _startConnectionMonitoring();
    _startOperationCleanupTimer();
  }

  // Connection state management
  SpotifyConnectionState _state = SpotifyConnectionState.disconnected;
  String? _lastError;
  DateTime? _lastReconnectAttempt;
  DateTime? _lastAuthAttempt;
  Timer? _reconnectDebounceTimer;
  Timer? _connectionMonitorTimer;
  Timer? _operationCleanupTimer;
  bool _isReconnecting = false;
  
  // Constants
  static const Duration _debounceDelay = Duration(milliseconds: 500);
  static const Duration _reconnectDelay = Duration(seconds: 2);
  static const Duration _connectionCheckInterval = Duration(seconds: 30);
  static const Duration _operationCleanupInterval = Duration(minutes: 5);
  static const Duration _minAuthRetryInterval = Duration(minutes: 5);
  static const int _maxReconnectAttempts = 3;
  static const int _maxConnectionCheckRetries = 2;

  // Stream controllers
  final _connectionStateController = StreamController<SpotifyConnectionState>.broadcast();
  final _errorController = StreamController<String>.broadcast();
  final _authRequiredController = StreamController<void>.broadcast();

  // Public streams
  Stream<SpotifyConnectionState> get connectionState => _connectionStateController.stream;
  Stream<String> get errorStream => _errorController.stream;
  Stream<void> get authRequiredStream => _authRequiredController.stream;
  
  // Public getters
  bool get isConnected => _state == SpotifyConnectionState.connected;
  String? get lastError => _lastError;
  SpotifyConnectionState get state => _state;

  void _startOperationCleanupTimer() {
    _operationCleanupTimer?.cancel();
    _operationCleanupTimer = Timer.periodic(_operationCleanupInterval, (_) {
      _cleanupOldOperations();
    });
  }

  void _cleanupOldOperations() {
    final now = DateTime.now();
    _operationAttempts.removeWhere((_, attempts) {
      return attempts.every((attempt) =>
          now.difference(attempt.timestamp) > _operationAttemptWindow);
    });
  }

  Future<bool> trackOperation(String identifier, SpotifyOperationType type) async {
    final attempts = _operationAttempts[identifier] ?? [];
    final now = DateTime.now();
    
    // Clean up old attempts outside the window
    attempts.removeWhere(
        (attempt) => now.difference(attempt.timestamp) > _operationAttemptWindow);
    
    // Add new attempt
    attempts.add(SpotifyOperationAttempt(identifier, type));
    _operationAttempts[identifier] = attempts;

    // Check if we need to trigger reauth
    if (attempts.length >= _maxOperationAttempts) {
      print('⚠️ Operation $identifier failed $_maxOperationAttempts times, attempting reauth');
      await _handleRepeatedFailures();
      return false;
    }

    return true;
  }

  Future<void> _handleRepeatedFailures() async {
    _lastAuthAttempt = DateTime.now();
    _updateConnectionState(SpotifyConnectionState.authenticating);
    
    try {
      // Trigger reauth
      await SpotifySdk.disconnect();
      await Future.delayed(Duration(seconds: 1));
      
      final authResult = await SpotifySdk.connectToSpotifyRemote(
        clientId: AppConstants.spotifyClientId,
        redirectUrl: AppConstants.spotifyRedirectUri,
        scope: 'app-remote-control,user-modify-playback-state,user-read-playback-state',
      );

      if (authResult) {
        print('✅ Reauthorization successful');
        _updateConnectionState(SpotifyConnectionState.connected);
        // Clear operation attempts after successful reauth
        _operationAttempts.clear();
      } else {
        throw Exception('Reauthorization failed');
      }
    } catch (e) {
      print('❌ Reauthorization failed: $e');
      _updateConnectionState(SpotifyConnectionState.error);
      _lastError = 'Reauthorization failed: $e';
      _errorController.add(_lastError!);
      _authRequiredController.add(null);
    }
  }

  void _startConnectionMonitoring() {
    _connectionMonitorTimer?.cancel();
    _connectionMonitorTimer = Timer.periodic(_connectionCheckInterval, (timer) {
      if (_state != SpotifyConnectionState.connecting) {
        _verifyConnection();
      }
    });
  }

  Future<void> _verifyConnection({int retryCount = 0}) async {
    try {
      final playerState = await SpotifySdk.getPlayerState();
      if (playerState != null) {
        _updateConnectionState(SpotifyConnectionState.connected);
      } else {
        throw Exception('Player state is null');
      }
    } catch (e) {
      print('⚠️ Connection verification failed: $e');
      if (retryCount < _maxConnectionCheckRetries) {
        await Future.delayed(Duration(seconds: 1));
        return _verifyConnection(retryCount: retryCount + 1);
      }
      if (_state == SpotifyConnectionState.connected) {
        _handleDisconnection();
      }
    }
  }

  void _handleDisconnection() {
    _updateConnectionState(SpotifyConnectionState.disconnected);
    _errorController.add('Lost connection to Spotify');
    // Attempt to reconnect automatically
    reconnect();
  }

  void _updateConnectionState(SpotifyConnectionState newState) {
    if (_state != newState) {
      _state = newState;
      _connectionStateController.add(newState);
      print('🎵 Spotify connection state changed to: $_state');
    }
  }

  Future<bool> reconnect({int attempt = 1}) async {
    if (_isReconnecting) {
      print('🎵 Already reconnecting to Spotify, skipping...');
      return false;
    }

    final now = DateTime.now();
    if (_lastReconnectAttempt != null && 
        now.difference(_lastReconnectAttempt!) < _debounceDelay) {
      print('⏳ Debounced Spotify reconnect, skipping...');
      return false;
    }

    try {
      _isReconnecting = true;
      _lastReconnectAttempt = now;
      _updateConnectionState(SpotifyConnectionState.connecting);

      // First verify if we're already connected
      try {
        final playerState = await SpotifySdk.getPlayerState();
        if (playerState != null) {
          _updateConnectionState(SpotifyConnectionState.connected);
          print('✅ Already connected to Spotify');
          return true;
        }
      } catch (e) {
        // Continue with reconnection if verification fails
        print('⚠️ Connection verification failed, attempting reconnection');
      }

      print('🎵 Attempting to reconnect to Spotify (attempt $attempt/$_maxReconnectAttempts)');
      
      await SpotifySdk.connectToSpotifyRemote(
        clientId: AppConstants.spotifyClientId,
        redirectUrl: AppConstants.spotifyRedirectUri,
      );

      // Verify connection after connecting
      await _verifyConnection();
      
      _lastError = null;
      print('✅ Successfully reconnected to Spotify');
      return true;
    } catch (e) {
      _lastError = e.toString();
      _errorController.add(_lastError!);
      print('❌ Failed to reconnect to Spotify: $e');
      _updateConnectionState(SpotifyConnectionState.error);
      
      if (attempt < _maxReconnectAttempts) {
        await Future.delayed(_reconnectDelay);
        return reconnect(attempt: attempt + 1);
      }
      _updateConnectionState(SpotifyConnectionState.disconnected);
      return false;
    } finally {
      _isReconnecting = false;
    }
  }

  void handleAppLifecycleState(AppLifecycleState state) {
    print('🎵 Spotify lifecycle state changed: $state');
    
    switch (state) {
      case AppLifecycleState.resumed:
        print('🎵 App resumed, handling Spotify connection');
        _reconnectDebounceTimer?.cancel();
        _reconnectDebounceTimer = Timer(_debounceDelay, () {
          _verifyConnection().then((_) {
            if (_state != SpotifyConnectionState.connected) {
              reconnect();
            }
          });
        });
        _startConnectionMonitoring();
        break;
        
      case AppLifecycleState.paused:
        _updateConnectionState(SpotifyConnectionState.disconnected);
        _connectionMonitorTimer?.cancel();
        break;
        
      case AppLifecycleState.inactive:
        // Handle when app becomes inactive (e.g., incoming call)
        _connectionMonitorTimer?.cancel();
        break;
        
      case AppLifecycleState.detached:
        // Clean up resources
        dispose();
        break;
        
      default:
        break;
    }
  }

  void dispose() {
    _reconnectDebounceTimer?.cancel();
    _connectionMonitorTimer?.cancel();
    _operationCleanupTimer?.cancel();
    _connectionStateController.close();
    _errorController.close();
    _authRequiredController.close();
  }
} 