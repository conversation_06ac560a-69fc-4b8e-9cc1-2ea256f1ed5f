import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';

import '../../models/music_track.dart';
import '../../utils/cycling_loader_state.dart';
import 'spotify_service.dart';

class ArtistCyclingSongLoader {
  final List<String> _selectedArtists;
  final SpotifyService _spotifyService;

  int _currentArtistIndex = 0;
  final Map<String, int> _artistBatchCounts = {};
  static const int SONGS_PER_ARTIST_BATCH = 3;
  static const int MAX_RETRIES_PER_ARTIST = 2;

  // Cache to avoid duplicate API calls
  final Map<String, List<MusicTrack>> _artistSongCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration CACHE_DURATION = Duration(minutes: 20);

  ArtistCyclingSongLoader({
    required List<String> selectedArtists,
    required SpotifyService spotifyService,
  })  : _selectedArtists = List.from(selectedArtists),
        _spotifyService = spotifyService {
    print(
        '🎵 [ArtistCyclingSongLoader] Initialized with ${_selectedArtists.length} artists: ${_selectedArtists.join(", ")}');
  }

  /// Load next batch of songs by cycling through artists
  Future<SongSelectionState> loadNextBatch(
      SongSelectionState currentState) async {
    if (_selectedArtists.isEmpty) {
      print('❌ [ArtistCyclingSongLoader] No artists selected');
      return currentState.withError('No artists selected');
    }

    print(
        '🔄 [ArtistCyclingSongLoader] Loading next batch (current index: $_currentArtistIndex)');

    try {
      final List<MusicTrack> newSongs = [];
      final Set<String> seenSongIds =
          currentState.items.map((s) => s.id).toSet();
      int attemptsWithoutResults = 0;
      var maxAttemptsWithoutResults = _selectedArtists.length *
          2; // Allow cycling through all artists twice

      while (newSongs.length < SONGS_PER_ARTIST_BATCH &&
          attemptsWithoutResults < maxAttemptsWithoutResults) {
        final currentArtist = _selectedArtists[_currentArtistIndex];
        final currentBatch = _artistBatchCounts[currentArtist] ?? 0;

        print(
            '🎯 [ArtistCyclingSongLoader] Loading from artist: "$currentArtist" (batch: $currentBatch)');

        try {
          final artistSongs = await _loadSongsForArtist(
            currentArtist,
            offset: currentBatch * SONGS_PER_ARTIST_BATCH,
            limit: SONGS_PER_ARTIST_BATCH,
          );

          // Filter out songs we already have
          final filteredSongs = artistSongs
              .where((song) => !seenSongIds.contains(song.id))
              .toList();

          if (filteredSongs.isNotEmpty) {
            newSongs.addAll(filteredSongs);
            seenSongIds.addAll(filteredSongs.map((s) => s.id));

            // Update batch count for this artist
            _artistBatchCounts[currentArtist] = currentBatch + 1;

            print(
                '✅ [ArtistCyclingSongLoader] Added ${filteredSongs.length} songs from "$currentArtist"');
            attemptsWithoutResults = 0; // Reset counter on success
          } else {
            print(
                '⚠️ [ArtistCyclingSongLoader] No new songs from "$currentArtist" (batch: $currentBatch)');
            attemptsWithoutResults++;
          }
        } catch (e) {
          print(
              '❌ [ArtistCyclingSongLoader] Error loading from artist "$currentArtist": $e');
          attemptsWithoutResults++;
        }

        // Move to next artist
        _currentArtistIndex =
            (_currentArtistIndex + 1) % _selectedArtists.length;
      }

      if (newSongs.isEmpty) {
        print(
            '⚠️ [ArtistCyclingSongLoader] No new songs loaded after cycling through artists');
        return currentState.success(
          newItems: [],
          newIndex: _currentArtistIndex,
          newBatchCounts: Map.from(_artistBatchCounts),
          hasMore: false,
        );
      }

      // Sort by popularity to show best songs first
      newSongs.sort((a, b) => (b.popularity ?? 0).compareTo(a.popularity ?? 0));

      print(
          '🎉 [ArtistCyclingSongLoader] Successfully loaded ${newSongs.length} new songs');

      return currentState.success(
        newItems: newSongs,
        newIndex: _currentArtistIndex,
        newBatchCounts: Map.from(_artistBatchCounts),
        hasMore: true,
      );
    } catch (e) {
      print('❌ [ArtistCyclingSongLoader] Error in loadNextBatch: $e');
      return currentState.withError('Failed to load songs: ${e.toString()}');
    }
  }

  /// Load songs for a specific artist with caching
  Future<List<MusicTrack>> _loadSongsForArtist(
    String artist, {
    required int offset,
    required int limit,
  }) async {
    final cacheKey = '${artist}_${offset}_$limit';

    // Check cache first
    if (_isValidCache(cacheKey)) {
      print(
          '📦 [ArtistCyclingSongLoader] Using cached songs for "$artist" (offset: $offset)');
      return _artistSongCache[cacheKey]!;
    }

    print(
        '🔍 [ArtistCyclingSongLoader] Fetching songs from Spotify for "$artist" (offset: $offset, limit: $limit)');

    try {
      // Use the existing SpotifyService method
      final tracks = await _spotifyService.getTopTracksByArtistPaginated(
        artist,
        limit: limit,
        offset: offset,
      );

      if (tracks.isEmpty) {
        print(
            '⚠️ [ArtistCyclingSongLoader] No songs returned from Spotify for "$artist"');
        return [];
      }

      // Cache the results
      _artistSongCache[cacheKey] = tracks;
      _cacheTimestamps[cacheKey] = DateTime.now();

      print(
          '✅ [ArtistCyclingSongLoader] Loaded ${tracks.length} songs for "$artist"');
      return tracks;
    } catch (e) {
      print(
          '❌ [ArtistCyclingSongLoader] Error loading songs for artist "$artist": $e');

      // Return empty list instead of throwing to allow cycling to continue
      return [];
    }
  }

  /// Check if cache entry is valid
  bool _isValidCache(String cacheKey) {
    if (!_artistSongCache.containsKey(cacheKey)) return false;

    final timestamp = _cacheTimestamps[cacheKey];
    if (timestamp == null) return false;

    return DateTime.now().difference(timestamp) < CACHE_DURATION;
  }

  /// Reset the loader state
  void reset() {
    print('🔄 [ArtistCyclingSongLoader] Resetting loader state');
    _currentArtistIndex = 0;
    _artistBatchCounts.clear();
    // Keep cache for performance
  }

  /// Clear all caches
  void clearCache() {
    print('🧹 [ArtistCyclingSongLoader] Clearing cache');
    _artistSongCache.clear();
    _cacheTimestamps.clear();
  }

  /// Get current loader statistics
  Map<String, dynamic> getStats() {
    return {
      'currentArtistIndex': _currentArtistIndex,
      'currentArtist': _selectedArtists.isNotEmpty
          ? _selectedArtists[_currentArtistIndex]
          : null,
      'artistBatchCounts': Map.from(_artistBatchCounts),
      'cacheSize': _artistSongCache.length,
      'selectedArtists': List.from(_selectedArtists),
    };
  }

  /// Update selected artists (useful if user changes artist selection)
  void updateArtists(List<String> newArtists) {
    print(
        '🔄 [ArtistCyclingSongLoader] Updating artists from ${_selectedArtists.length} to ${newArtists.length}');
    _selectedArtists.clear();
    _selectedArtists.addAll(newArtists);
    reset();
  }
}
