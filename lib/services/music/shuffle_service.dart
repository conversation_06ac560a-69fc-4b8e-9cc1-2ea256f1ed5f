import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../../models/music_track.dart';
import '../../models/music/bop_drop.dart';
import '../../providers/spotify_provider.dart';
import '../../providers/apple_music_provider.dart';

/// Unified shuffle service that works with both Spotify and Apple Music
/// Provides smart shuffling algorithms and queue management
class ShuffleService {
  static const int _maxQueueSize = 50; // Prevent overwhelming the queue
  static const Duration _apiDelay = Duration(milliseconds: 150); // Prevent rate limiting
  
  /// Shuffle and play a collection with enhanced algorithms
  static Future<ShuffleResult> shuffleCollection({
    required List<Map<String, dynamic>> pins,
    required SpotifyProvider spotifyProvider,
    required AppleMusicProvider appleMusicProvider,
    ShuffleMode mode = ShuffleMode.random,
    bool clearExistingQueue = true,
    int? maxTracks,
  }) async {
    try {
      if (pins.isEmpty) {
        return ShuffleResult(
          success: false,
          message: 'No songs to shuffle',
          tracksQueued: 0,
        );
      }
      
      // Convert pins to MusicTrack objects
      final tracks = pins.map(_convertPinToMusicTrack).where((track) => track != null).cast<MusicTrack>().toList();
      
      if (tracks.isEmpty) {
        return ShuffleResult(
          success: false,
          message: 'No valid tracks found in collection',
          tracksQueued: 0,
        );
      }
      
      // Apply shuffle algorithm
      final shuffledTracks = _applyShuffleAlgorithm(tracks, mode);
      
      // Limit tracks if specified
      final finalTracks = maxTracks != null && maxTracks < shuffledTracks.length
          ? shuffledTracks.take(maxTracks).toList()
          : shuffledTracks.take(_maxQueueSize).toList();
      
      if (kDebugMode) {
        print('🎲 [ShuffleService] Shuffling ${finalTracks.length} tracks with mode: $mode');
      }
      
      // Determine which service to use
      final platform = _determinePrimaryPlatform(pins);
      
      if (platform == 'spotify' && spotifyProvider.isConnected) {
        return await _shuffleWithSpotify(finalTracks, spotifyProvider, clearExistingQueue);
      } else if (platform == 'apple' && appleMusicProvider.isConnected) {
        return await _shuffleWithAppleMusic(finalTracks, appleMusicProvider, clearExistingQueue);
      } else {
        // Fallback to any available service
        if (spotifyProvider.isConnected) {
          return await _shuffleWithSpotify(finalTracks, spotifyProvider, clearExistingQueue);
        } else if (appleMusicProvider.isConnected) {
          return await _shuffleWithAppleMusic(finalTracks, appleMusicProvider, clearExistingQueue);
        } else {
          return ShuffleResult(
            success: false,
            message: 'No music service is connected',
            tracksQueued: 0,
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [ShuffleService] Error shuffling collection: $e');
      }
      return ShuffleResult(
        success: false,
        message: 'Error shuffling collection: $e',
        tracksQueued: 0,
      );
    }
  }
  
  /// NEW: Shuffle and play recommendations with social context
  static Future<ShuffleResult> shuffleRecommendations({
    required List<BopDrop> bopDrops,
    required SpotifyProvider spotifyProvider,
    required AppleMusicProvider appleMusicProvider,
    ShuffleMode mode = ShuffleMode.random,
    bool clearExistingQueue = true,
    int? maxTracks,
  }) async {
    try {
      if (bopDrops.isEmpty) {
        return ShuffleResult(
          success: false,
          message: 'No recommendations to shuffle',
          tracksQueued: 0,
          source: 'recommendations',
        );
      }
      
      if (kDebugMode) {
        print('🎲 [ShuffleService] Shuffling ${bopDrops.length} recommendations');
        print('🎲 [ShuffleService] Spotify connected: ${spotifyProvider.isConnected}');
        print('🎲 [ShuffleService] Apple Music connected: ${appleMusicProvider.isConnected}');
      }
      
      // Convert BopDrops to MusicTrack objects with cross-platform search
      final tracks = await _convertBopDropsToMusicTracks(
        bopDrops, 
        spotifyProvider, 
        appleMusicProvider
      );
      
      if (tracks.isEmpty) {
        return ShuffleResult(
          success: false,
          message: 'No valid tracks found in recommendations',
          tracksQueued: 0,
          source: 'recommendations',
        );
      }
      
      if (kDebugMode) {
        print('🎲 [ShuffleService] Successfully converted ${tracks.length} recommendations to tracks');
      }
      
      // Apply shuffle algorithm
      final shuffledTracks = _applyShuffleAlgorithm(tracks, mode);
      
      // Limit tracks if specified
      final finalTracks = maxTracks != null && maxTracks < shuffledTracks.length
          ? shuffledTracks.take(maxTracks).toList()
          : shuffledTracks.take(_maxQueueSize).toList();
      
      // Use the appropriate music service
      if (appleMusicProvider.isConnected) {
        final result = await _shuffleWithAppleMusic(finalTracks, appleMusicProvider, clearExistingQueue);
        return ShuffleResult(
          success: result.success,
          message: result.message,
          tracksQueued: result.tracksQueued,
          platform: result.platform,
          source: 'recommendations',
          recommendationData: bopDrops,
        );
      } else if (spotifyProvider.isConnected) {
        final result = await _shuffleWithSpotify(finalTracks, spotifyProvider, clearExistingQueue);
        return ShuffleResult(
          success: result.success,
          message: result.message,
          tracksQueued: result.tracksQueued,
          platform: result.platform,
          source: 'recommendations',
          recommendationData: bopDrops,
        );
      } else {
        return ShuffleResult(
          success: false,
          message: 'No music service is connected',
          tracksQueued: 0,
          source: 'recommendations',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [ShuffleService] Error shuffling recommendations: $e');
      }
      return ShuffleResult(
        success: false,
        message: 'Error shuffling recommendations: $e',
        tracksQueued: 0,
        source: 'recommendations',
      );
    }
  }
  
  /// NEW: Shuffle and play challenge entries with proper music playback
  static Future<ShuffleResult> shuffleChallengeEntries({
    required List<dynamic> entries, // List<SongEntry>
    required SpotifyProvider spotifyProvider,
    required AppleMusicProvider appleMusicProvider,
    ShuffleMode mode = ShuffleMode.random,
    bool clearExistingQueue = true,
    int? maxTracks,
  }) async {
    try {
      if (entries.isEmpty) {
        return ShuffleResult(
          success: false,
          message: 'No songs to shuffle',
          tracksQueued: 0,
          source: 'challenge',
        );
      }
      
      if (kDebugMode) {
        print('🎲 [ShuffleService] Shuffling ${entries.length} challenge entries');
        print('🎲 [ShuffleService] Spotify connected: ${spotifyProvider.isConnected}');
        print('🎲 [ShuffleService] Apple Music connected: ${appleMusicProvider.isConnected}');
      }
      
      // Convert SongEntry objects to MusicTrack objects
      final tracks = entries.map(_convertSongEntryToMusicTrack).where((track) => track != null).cast<MusicTrack>().toList();
      
      if (tracks.isEmpty) {
        return ShuffleResult(
          success: false,
          message: 'No valid tracks found in challenge entries',
          tracksQueued: 0,
          source: 'challenge',
        );
      }
      
      if (kDebugMode) {
        print('🎲 [ShuffleService] Successfully converted ${tracks.length} entries to tracks');
      }
      
      // Apply shuffle algorithm
      final shuffledTracks = _applyShuffleAlgorithm(tracks, mode);
      
      // Limit tracks if specified
      final finalTracks = maxTracks != null && maxTracks < shuffledTracks.length
          ? shuffledTracks.take(maxTracks).toList()
          : shuffledTracks.take(_maxQueueSize).toList();
      
      // Use the appropriate music service
      if (spotifyProvider.isConnected) {
        final result = await _shuffleWithSpotify(finalTracks, spotifyProvider, clearExistingQueue);
        return ShuffleResult(
          success: result.success,
          message: result.message,
          tracksQueued: result.tracksQueued,
          platform: result.platform,
          source: 'challenge',
        );
      } else if (appleMusicProvider.isConnected) {
        final result = await _shuffleWithAppleMusic(finalTracks, appleMusicProvider, clearExistingQueue);
        return ShuffleResult(
          success: result.success,
          message: result.message,
          tracksQueued: result.tracksQueued,
          platform: result.platform,
          source: 'challenge',
        );
      } else {
        return ShuffleResult(
          success: false,
          message: 'No music service is connected',
          tracksQueued: 0,
          source: 'challenge',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [ShuffleService] Error shuffling challenge entries: $e');
      }
      return ShuffleResult(
        success: false,
        message: 'Error shuffling challenge entries: $e',
        tracksQueued: 0,
        source: 'challenge',
      );
    }
  }
  
  /// Convert BopDrops to MusicTrack objects with proper cross-platform search
  static Future<List<MusicTrack>> _convertBopDropsToMusicTracks(
    List<BopDrop> bopDrops,
    SpotifyProvider spotifyProvider,
    AppleMusicProvider appleMusicProvider,
  ) async {
    final tracks = <MusicTrack>[];
    
    // Determine which service the user prefers
    final preferAppleMusic = appleMusicProvider.isConnected;
    final preferSpotify = spotifyProvider.isConnected && !preferAppleMusic;
    
    if (kDebugMode) {
      print('🎲 [ShuffleService] Converting ${bopDrops.length} BopDrops to tracks...');
      print('🎲 [ShuffleService] Prefer Apple Music: $preferAppleMusic');
    }
    
    for (final bopDrop in bopDrops) {
      try {
        MusicTrack? track;
        final bopDropService = bopDrop.musicService.toLowerCase();
        
        if ((preferAppleMusic && bopDropService.contains('apple')) ||
            (preferSpotify && bopDropService.contains('spotify'))) {
          track = _createMusicTrackFromBopDrop(bopDrop);
        } else {
          if (preferAppleMusic) {
            track = await appleMusicProvider.searchForTrack(
              bopDrop.trackArtist,
              bopDrop.trackTitle,
            );
            if (track != null) {
              print('✅ [ShuffleService] Found Apple Music equivalent: ${track.title}');
            }
          } else if (preferSpotify) {
            // You can add Spotify search logic here if needed
            track = _createMusicTrackFromBopDrop(bopDrop);
          }
        }
        
        if (track != null) {
          tracks.add(track);
        }
        
        await Future.delayed(_apiDelay);
        
      } catch (e) {
        if (kDebugMode) {
          print('❌ [ShuffleService] Error converting BopDrop: ${bopDrop.trackTitle} - $e');
        }
      }
    }
    
    return tracks;
  }
  
  /// Create MusicTrack directly from BopDrop (when services match)
  static MusicTrack _createMusicTrackFromBopDrop(BopDrop bopDrop) {
    final service = bopDrop.musicService.toLowerCase();
    String uri = '';
    
    if (service.contains('spotify')) {
      final trackId = bopDrop.trackId;
      if (trackId.startsWith('spotify:track:')) {
        uri = trackId;
      } else if (trackId.contains('spotify.com/track/')) {
        final id = trackId.split('/track/').last.split('?').first;
        uri = 'spotify:track:$id';
      } else {
        uri = 'spotify:track:$trackId';
      }
    } else {
      uri = bopDrop.trackId;
    }
    
    return MusicTrack(
      id: bopDrop.trackId,
      title: bopDrop.trackTitle,
      artist: bopDrop.trackArtist,
      album: bopDrop.trackAlbum ?? '',
      albumArt: bopDrop.albumArtUrl ?? '',
      url: bopDrop.previewUrl ?? '',
      service: service,
      serviceType: service,
      genres: [],
      durationMs: 210000, // Default 3:30
      popularity: 75,
      uri: uri,
      isLibrary: false,
    );
  }
  
  /// Convert BopDrop to pin format for shuffle service (legacy method, kept for compatibility)
  static Map<String, dynamic> _convertBopDropToPin(BopDrop bopDrop) {
    return {
      'id': bopDrop.trackId,
      'title': bopDrop.trackTitle,
      'artist': bopDrop.trackArtist,
      'album': bopDrop.trackAlbum ?? '',
      'imageUrl': bopDrop.albumArtUrl ?? '',
      'track_url': _constructTrackUrlFromBopDrop(bopDrop),
      'url': _constructTrackUrlFromBopDrop(bopDrop),
      'platform': bopDrop.musicService.toLowerCase(),
      'service': bopDrop.musicService.toLowerCase(),
      'duration': '3:30', // Default duration
      'popularity': 75, // Default popularity
    };
  }
  
  /// Helper method to construct track URLs from BopDrop data
  static String _constructTrackUrlFromBopDrop(BopDrop bopDrop) {
    final service = bopDrop.musicService.toLowerCase();
    final trackId = bopDrop.trackId;
    
    if (service.contains('spotify')) {
      // For Spotify, construct the proper URL or URI
      if (trackId.startsWith('spotify:track:')) {
        return trackId;
      } else if (trackId.contains('spotify.com/track/')) {
        return trackId;
      } else {
        // Assume it's just the track ID
        return 'spotify:track:$trackId';
      }
    } else if (service.contains('apple')) {
      // For Apple Music, return the track ID or URL as-is
      return trackId;
    } else {
      // Generic fallback
      return trackId;
    }
  }
  
  /// Shuffle with Spotify using fresh playback context
  static Future<ShuffleResult> _shuffleWithSpotify(
    List<MusicTrack> tracks,
    SpotifyProvider spotifyProvider,
    bool clearExisting,
  ) async {
    try {
      if (kDebugMode) {
        print('🎲 [ShuffleService] Starting fresh shuffled playback with ${tracks.length} tracks');
      }
      
      // Start fresh playback with ALL tracks - this replaces the entire context
      final playSuccess = await spotifyProvider.playMultipleTracks(tracks);
      
      if (!playSuccess) {
        return ShuffleResult(
          success: false,
          message: 'Failed to start shuffled playback',
          tracksQueued: 0,
        );
      }
      
      // Clear the local queue since we're using a fresh context
      final hybridQueueManager = spotifyProvider.hybridQueueManager;
      hybridQueueManager.clearLocalQueue();
      
      if (kDebugMode) {
        print('✅ [ShuffleService] Successfully started fresh playback context with ${tracks.length} tracks');
      }
      
      return ShuffleResult(
        success: true,
        message: 'Successfully shuffled and playing ${tracks.length} tracks',
        tracksQueued: tracks.length,
        platform: 'spotify',
      );
    } catch (e) {
      return ShuffleResult(
        success: false,
        message: 'Spotify shuffle error: $e',
        tracksQueued: 0,
      );
    }
  }
  
  /// Shuffle with Apple Music using the queue manager
  static Future<ShuffleResult> _shuffleWithAppleMusic(
    List<MusicTrack> tracks,
    AppleMusicProvider appleMusicProvider,
    bool clearExisting,
  ) async {
    try {
      final queueManager = appleMusicProvider.queueManager;

      if (kDebugMode) {
        print('🎲 [ShuffleService] Setting up Apple Music shuffle with ${tracks.length} tracks');
        print('🎲 [ShuffleService] Tracks are already pre-shuffled by algorithm');
      }

      // CRITICAL: Only disable Apple Music's NATIVE shuffle (MusicKit shuffle)
      // We want to keep our pre-shuffled track order intact
      // The ensureShuffleDisabled method was interfering with our pre-shuffled order
      await queueManager.disableNativeShuffleOnly();
      if (kDebugMode) {
        print('🎲 [ShuffleService] Disabled Apple Music native shuffle only (preserving pre-shuffled order)');
      }

      // Set the queue with our pre-shuffled tracks
      // The tracks are already in the correct shuffled order from _applyShuffleAlgorithm
      final success = await queueManager.setQueue(
        tracks: tracks,
        collectionType: 'shuffled_collection',
        collectionId: 'shuffle_${DateTime.now().millisecondsSinceEpoch}',
        collectionMetadata: {
          'name': 'Shuffled Collection',
          'type': 'shuffled_collection',
          'shuffled': true,
        },
        startIndex: 0,
      );

      if (success) {
        if (kDebugMode) {
          print('✅ [ShuffleService] Successfully set up Apple Music shuffle queue with pre-shuffled tracks');
          print('✅ [ShuffleService] Track order preserved from shuffle algorithm');
        }

        return ShuffleResult(
          success: true,
          message: 'Successfully shuffled and playing ${tracks.length} tracks',
          tracksQueued: tracks.length,
          platform: 'apple',
        );
      } else {
        return const ShuffleResult(
          success: false,
          message: 'Failed to set up Apple Music shuffle queue',
          tracksQueued: 0,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [ShuffleService] Apple Music shuffle error: $e');
      }
      return ShuffleResult(
        success: false,
        message: 'Apple Music shuffle error: $e',
        tracksQueued: 0,
      );
    }
  }
  
  /// Apply different shuffle algorithms
  static List<MusicTrack> _applyShuffleAlgorithm(List<MusicTrack> tracks, ShuffleMode mode) {
    if (tracks.isEmpty) return [];
    
    final random = math.Random();
    final shuffled = List<MusicTrack>.from(tracks);
    
    switch (mode) {
      case ShuffleMode.random:
        // Fisher-Yates shuffle algorithm for better randomization
        for (var i = shuffled.length - 1; i > 0; i--) {
          var j = random.nextInt(math.max(1, i + 1)); // Ensure we never pass 0 to nextInt
          var temp = shuffled[i];
          shuffled[i] = shuffled[j];
          shuffled[j] = temp;
        }
        break;
        
      case ShuffleMode.weighted:
        // Improved weighted shuffle with better distribution
        final weights = shuffled.map((track) => track.popularity ?? 50).toList();
        final totalWeight = weights.reduce((a, b) => a + b);
        
        final weightedShuffled = <MusicTrack>[];
        final remainingTracks = List<MusicTrack>.from(shuffled);
        final remainingWeights = List<int>.from(weights);
        
        while (remainingTracks.isNotEmpty) {
          final currentTotal = remainingWeights.reduce((a, b) => a + b);
          if (currentTotal <= 0) break; // Break if total weight becomes 0 or negative
          var r = random.nextInt(math.max(1, currentTotal)); // Ensure we never pass 0 to nextInt
          
          for (var i = 0; i < remainingTracks.length; i++) {
            r -= remainingWeights[i];
            if (r <= 0) {
              weightedShuffled.add(remainingTracks[i]);
              remainingTracks.removeAt(i);
              remainingWeights.removeAt(i);
              break;
            }
          }
        }
        
        // Add any remaining tracks if the weight calculation failed
        weightedShuffled.addAll(remainingTracks);
        return weightedShuffled;
        

        
      case ShuffleMode.artistSpaced:
        // Improved artist spacing with better distribution
        final artistGroups = <String, List<MusicTrack>>{};
        for (final track in tracks) {
          final artist = track.artist.toLowerCase();
          artistGroups.putIfAbsent(artist, () => []).add(track);
        }
        
        // Shuffle each artist's tracks using Fisher-Yates
        for (final tracks in artistGroups.values) {
          for (var i = tracks.length - 1; i > 0; i--) {
            var j = random.nextInt(i + 1);
            var temp = tracks[i];
            tracks[i] = tracks[j];
            tracks[j] = temp;
          }
        }
        
        // Improved artist spacing algorithm
        shuffled.clear();
        final artists = artistGroups.keys.toList();
        var currentArtistIndex = 0;
        var consecutiveSkips = 0;
        final maxConsecutiveSkips = artists.length;
        
        while (artistGroups.values.any((tracks) => tracks.isNotEmpty)) {
          var added = false;
          var startIndex = currentArtistIndex;
          
          do {
            final artist = artists[currentArtistIndex];
            final tracks = artistGroups[artist]!;
            
            if (tracks.isNotEmpty && (shuffled.isEmpty || 
                (shuffled.length >= 2 && 
                 shuffled.last.artist.toLowerCase() != artist && 
                 shuffled[shuffled.length - 2].artist.toLowerCase() != artist))) {
              shuffled.add(tracks.removeAt(0));
              added = true;
              consecutiveSkips = 0;
              break;
            }
            
            currentArtistIndex = (currentArtistIndex + 1) % artists.length;
            consecutiveSkips++;
            
            // If we've skipped too many times, just add the next available track
            if (consecutiveSkips >= maxConsecutiveSkips) {
              for (final tracks in artistGroups.values) {
                if (tracks.isNotEmpty) {
                  shuffled.add(tracks.removeAt(0));
                  added = true;
                  consecutiveSkips = 0;
                  break;
                }
              }
            }
          } while (currentArtistIndex != startIndex && !added);
          
          // If we couldn't add any track, break to avoid infinite loop
          if (!added) break;
        }
        break;
    }
    
    return shuffled;
  }
  
  /// Determine the primary platform based on collection content
  static String _determinePrimaryPlatform(List<Map<String, dynamic>> pins) {
    final platformCounts = <String, int>{};
    
    for (final pin in pins) {
      final platform = pin['platform']?.toString().toLowerCase() ?? 'unknown';
      platformCounts[platform] = (platformCounts[platform] ?? 0) + 1;
    }
    
    if (platformCounts.isEmpty) return 'spotify'; // Default fallback
    
    // Return the platform with the most tracks
    return platformCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }
  
  /// Convert pin data to MusicTrack
  static MusicTrack? _convertPinToMusicTrack(Map<String, dynamic> pin) {
    try {
      final platform = pin['platform']?.toString().toLowerCase() ?? 'spotify';
      String uri = '';
      
      // Construct proper URI based on platform
      if (platform.contains('spotify')) {
        final trackUrl = pin['track_url'] ?? pin['url'] ?? '';
        if (trackUrl.contains('spotify.com/track/')) {
          final id = trackUrl.split('/track/').last.split('?').first;
          uri = 'spotify:track:$id';
        } else if (trackUrl.startsWith('spotify:track:')) {
          uri = trackUrl;
        } else {
          uri = 'spotify:track:${pin['id']}';
        }
      } else {
        uri = pin['track_url'] ?? pin['url'] ?? '';
      }
      
      return MusicTrack(
        id: pin['id']?.toString() ?? '',
        title: pin['title']?.toString() ?? 'Unknown Track',
        artist: pin['artist']?.toString() ?? 'Unknown Artist',
        album: pin['album']?.toString() ?? '',
        albumArt: pin['imageUrl']?.toString() ?? '',
        url: pin['track_url'] ?? pin['url'] ?? '',
        service: platform,
        serviceType: platform,
        genres: [], // Could be enhanced to extract from pin data
        durationMs: _parseDurationMs(pin['duration']?.toString() ?? '0:00'),
        popularity: pin['popularity']?.toInt() ?? 50,
        uri: uri,
      );
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [ShuffleService] Error converting pin to MusicTrack: $e');
      }
      return null;
    }
  }
  
  /// Parse duration string to milliseconds
  static int _parseDurationMs(String duration) {
    try {
      final parts = duration.split(':');
      if (parts.length == 2) {
        final minutes = int.parse(parts[0]);
        final seconds = int.parse(parts[1]);
        return (minutes * 60 + seconds) * 1000;
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return 0;
  }

  /// Convert SongEntry to MusicTrack for challenge shuffling
  static MusicTrack? _convertSongEntryToMusicTrack(dynamic entry) {
    try {
      // Handle the SongEntry object (accessing properties dynamically)
      final songId = entry.songId?.toString() ?? '';
      final title = entry.title?.toString() ?? 'Unknown Track';
      final artist = entry.artist?.toString() ?? 'Unknown Artist';
      final albumArt = entry.albumArt?.toString() ?? '';
      
      // Determine platform from songId format
      String platform = 'spotify'; // Default to Spotify for challenge entries
      String uri = '';
      
      if (songId.startsWith('spotify:track:')) {
        uri = songId;
        platform = 'spotify';
      } else if (songId.contains('spotify.com/track/')) {
        final id = songId.split('/track/').last.split('?').first;
        uri = 'spotify:track:$id';
        platform = 'spotify';
      } else if (songId.contains('apple.com') || songId.contains('music.apple')) {
        uri = songId;
        platform = 'apple';
      } else {
        // Assume it's a Spotify track ID
        uri = 'spotify:track:$songId';
        platform = 'spotify';
      }
      
      return MusicTrack(
        id: songId,
        title: title,
        artist: artist,
        album: '', // Not available in SongEntry
        albumArt: albumArt,
        url: uri,
        service: platform,
        serviceType: platform,
        genres: [], // Not available in SongEntry
        durationMs: 210000, // Default 3:30 for challenge entries
        popularity: entry.voteScore?.toInt() ?? 50, // Use vote score as popularity
        uri: uri,
        isLibrary: false,
      );
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [ShuffleService] Error converting SongEntry to MusicTrack: $e');
      }
      return null;
    }
  }
}

/// Different shuffle algorithms available
enum ShuffleMode {
  random,         // Pure random shuffle
  weighted,       // Weighted by popularity
  artistSpaced,   // Spaces out songs by same artist
}

/// Result of a shuffle operation
class ShuffleResult {
  final bool success;
  final String message;
  final int tracksQueued;
  final String? platform;
  final String? source;
  final dynamic recommendationData;
  
  const ShuffleResult({
    required this.success,
    required this.message,
    required this.tracksQueued,
    this.platform,
    this.source,
    this.recommendationData,
  });
  
  @override
  String toString() {
    return 'ShuffleResult(success: $success, message: $message, tracksQueued: $tracksQueued, platform: $platform, source: $source, recommendationData: $recommendationData)';
  }
} 