import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:math' as math;
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_web_auth_2/flutter_web_auth_2.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';
import 'package:spotify_sdk/spotify_sdk.dart';
import '../../config/constants.dart';
import '../../models/music_track.dart';

// Add this extension for MusicTrack compatibility (from working implementation)
extension MusicTrackCopyWith on MusicTrack {
  MusicTrack copyWith({
    String? id,
    String? title,
    String? artist,
    String? album,
    String? albumArt,
    String? url,
    String? uri,
    String? service,
    String? previewUrl,
    String? albumArtUrl,
    String? serviceType,
    List<String>? genres,
    int? durationMs,
    DateTime? releaseDate,
    bool? explicit,
    int? popularity,
    bool? isPlayable,
  }) {
    return MusicTrack(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      albumArt: albumArt ?? this.albumArt,
      url: url ?? this.url,
      uri: uri ?? this.uri,
      service: service ?? this.service,
      previewUrl: previewUrl ?? this.previewUrl,
      albumArtUrl: albumArtUrl ?? this.albumArtUrl,
      serviceType: serviceType ?? this.serviceType,
      genres: genres ?? this.genres,
      durationMs: durationMs ?? this.durationMs,
      releaseDate: releaseDate ?? this.releaseDate,
      explicit: explicit ?? this.explicit,
      popularity: popularity ?? this.popularity,
      isPlayable: isPlayable ?? this.isPlayable,
    );
  }
}

class SpotifyWebApiService {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  
  // Storage keys
  static const String _accessTokenKey = 'spotify_web_access_token';
  static const String _refreshTokenKey = 'spotify_web_refresh_token';
  static const String _expiryTimeKey = 'spotify_web_token_expiry';
  static const String _codeVerifierKey = 'spotify_pkce_code_verifier';
  
  // Rate limiting (enhanced to match working implementation)
  DateTime _lastRequestTime = DateTime.now().subtract(const Duration(seconds: 1));
  static const Duration _minRequestInterval = Duration(milliseconds: 250); // 4 requests/sec like working impl
  
  // Caching (enhanced like working implementation)
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheExpiry = {};
  final Map<String, List<MusicTrack>> _searchCache = {}; // Add search cache like working impl
  static const Duration _defaultCacheExpiry = Duration(minutes: 5);
  
  // SDK connection tracking (for fallback playback only)
  bool _sdkConnected = false;
  bool _sdkConnecting = false;
  DateTime? _lastSdkConnectionAttempt;
  
  // Refresh token management
  bool _isRefreshing = false;
  Completer<bool>? _refreshCompleter;
  DateTime? _lastRefreshAttempt;
  static const Duration _refreshCooldown = Duration(seconds: 30);
  static const Duration _refreshThreshold = Duration(minutes: 2); // Refresh when less than 2 minutes remain

  /// Generate PKCE code verifier and challenge
  Map<String, String> _generatePKCE() {
    final random = Random.secure();
    
    // Generate code verifier (43-128 characters as per PKCE spec)
    // Using base64url encoding of 64 random bytes gives us ~86 characters
    final codeVerifier = base64UrlEncode(List<int>.generate(64, (i) => random.nextInt(256)))
        .replaceAll('=', ''); // Remove padding as per spec
    
    // Generate code challenge from verifier
    final bytes = utf8.encode(codeVerifier);
    final digest = sha256.convert(bytes);
    final codeChallenge = base64UrlEncode(digest.bytes)
        .replaceAll('=', ''); // Remove padding as per spec
    
    if (kDebugMode) {
      print('🔐 [SpotifyWebApi] Generated code_verifier length: ${codeVerifier.length}');
      print('🔐 [SpotifyWebApi] Generated code_challenge length: ${codeChallenge.length}');
    }
    
    return {
      'code_verifier': codeVerifier,
      'code_challenge': codeChallenge,
    };
  }

  /// Start the Spotify authentication flow using PKCE
  Future<bool> authenticate() async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Starting PKCE authentication flow');
      }

      // Generate PKCE parameters
      final pkce = _generatePKCE();
      await _secureStorage.write(key: _codeVerifierKey, value: pkce['code_verifier']);

      // Build authorization URL
      final authUrl = Uri.https('accounts.spotify.com', '/authorize', {
        'response_type': 'code',
        'client_id': AppConstants.spotifyClientId,
        'scope': AppConstants.spotifyScopes.join(' '),
        'redirect_uri': AppConstants.spotifyRedirectUri,
        'code_challenge_method': 'S256',
        'code_challenge': pkce['code_challenge'],
        'state': _generateRandomString(16), // For security
      });

      if (kDebugMode) {
        print('🔐 [SpotifyWebApi] Launching auth URL: $authUrl');
      }

      // Launch the authentication flow
      final result = await FlutterWebAuth2.authenticate(
        url: authUrl.toString(),
        callbackUrlScheme: 'bopmaps',
      );

      // Extract authorization code from callback
      final uri = Uri.parse(result);
      final code = uri.queryParameters['code'];
      final error = uri.queryParameters['error'];

      if (error != null) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Authentication error: $error');
        }
        return false;
      }

      if (code == null) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] No authorization code received');
        }
        return false;
      }

      // Exchange code for tokens using backend
      return await _exchangeCodeForTokens(code);
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Authentication error: $e');
      }
      return false;
    }
  }

  /// Exchange authorization code for access tokens via backend
  Future<bool> _exchangeCodeForTokens(String code) async {
    try {
      final codeVerifier = await _secureStorage.read(key: _codeVerifierKey);
      if (codeVerifier == null) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] No code verifier found in secure storage');
        }
        return false;
      }

      // Prepare request body
      final requestBody = {
        'code': code,
        'code_verifier': codeVerifier,
        'redirect_uri': AppConstants.spotifyRedirectUri,
      };

      // Call backend PKCE token exchange endpoint
      final response = await http.post(
        Uri.parse('${AppConstants.baseApiUrl}/music/spotify/token/exchange/'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (kDebugMode) {
        print('🔄 [SpotifyWebApi] Backend response status: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final tokenData = jsonDecode(response.body);
        
        // Validate that we have the required tokens
        // Store tokens
        await _storeTokens(
          tokenData['access_token'],
          tokenData['refresh_token'],
          tokenData['expires_in'] ?? 3600, // Default to 1 hour if not provided
        );

        // Clean up the code verifier after successful exchange
        await _secureStorage.delete(key: _codeVerifierKey);

        return true;
      } else {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Backend token exchange failed: ${response.statusCode}');
          print('❌ [SpotifyWebApi] Backend response: ${response.body}');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error exchanging tokens: $e');
      }
      return false;
    }
  }

  /// Store tokens securely
  Future<void> _storeTokens(String accessToken, String refreshToken, int expiresIn) async {
    final expiryTime = DateTime.now().add(Duration(seconds: expiresIn));
    
    
    await Future.wait([
      _secureStorage.write(key: _accessTokenKey, value: accessToken),
      _secureStorage.write(key: _refreshTokenKey, value: refreshToken),
      _secureStorage.write(key: _expiryTimeKey, value: expiryTime.toIso8601String()),
    ]);
    
    // Verify tokens were stored
    if (kDebugMode) {
      final storedAccessToken = await _secureStorage.read(key: _accessTokenKey);
      final storedRefreshToken = await _secureStorage.read(key: _refreshTokenKey);
      final storedExpiryTime = await _secureStorage.read(key: _expiryTimeKey);
      
    }
  }

  /// Get current access token, refreshing if necessary
  Future<String?> getAccessToken() async {
    try {
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      final expiryTimeStr = await _secureStorage.read(key: _expiryTimeKey);
      
      
      // If no access token, we can't proceed
      if (accessToken == null) {
        return null;
      }

      // If no expiry time, assume token is valid (legacy support)
      if (expiryTimeStr == null) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] No expiry time found, returning existing token');
        }
        return accessToken;
      }

      final expiryTime = DateTime.parse(expiryTimeStr);
      final now = DateTime.now();
      final timeUntilExpiry = expiryTime.difference(now);


      // If token is not expired and has more than threshold time remaining, return it
      if (timeUntilExpiry > _refreshThreshold) {
        return accessToken;
      }

      // If no refresh token, we can't refresh
      if (refreshToken == null) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Token expiring soon but no refresh token available');
        }
        return accessToken; // Return expired token, let the API call handle the 401
      }

      // Check if we're already refreshing
      if (_isRefreshing) {
        if (kDebugMode) {
          print('⏳ [SpotifyWebApi] Token refresh already in progress, waiting...');
        }
        final refreshed = await _refreshCompleter?.future ?? false;
        if (refreshed) {
          return await _secureStorage.read(key: _accessTokenKey);
        } else {
          return null;
        }
      }

      // Check cooldown to prevent too frequent refresh attempts
      if (_lastRefreshAttempt != null && 
          now.difference(_lastRefreshAttempt!) < _refreshCooldown) {
        if (kDebugMode) {
          print('⏳ [SpotifyWebApi] Refresh cooldown active, returning existing token');
        }
        return accessToken;
      }

      // Token needs refresh and we can refresh it
      if (kDebugMode) {
        print('🔄 [SpotifyWebApi] Token expiring soon (${timeUntilExpiry.inMinutes} min remaining), initiating refresh...');
      }
      
      final refreshed = await _refreshTokenWithLock();
      if (refreshed) {
        final newToken = await _secureStorage.read(key: _accessTokenKey);
        if (kDebugMode) {
          print('✅ [SpotifyWebApi] Token refresh successful, returning new token');
        }
        return newToken;
      } else {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Token refresh failed, returning existing token');
        }
        return accessToken; // Return the old token, let the API call handle potential 401
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error getting access token: $e');
      }
      return null;
    }
  }

  /// Refresh access token using backend with concurrency control
  Future<bool> _refreshTokenWithLock() async {
    // If already refreshing, wait for the existing refresh to complete
    if (_isRefreshing) {
      return await _refreshCompleter?.future ?? false;
    }

    _isRefreshing = true;
    final completer = Completer<bool>();
    _refreshCompleter = completer;
    _lastRefreshAttempt = DateTime.now();

    try {
      if (kDebugMode) {
        print('🔒 [SpotifyWebApi] Acquired refresh lock, starting token refresh');
      }

      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      if (refreshToken == null) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] No refresh token available in secure storage');
        }
        completer.complete(false);
        return false;
      }

      if (kDebugMode) {
        print('🔄 [SpotifyWebApi] Refreshing token via backend');
        print('🔐 [SpotifyWebApi] Refresh token length: ${refreshToken.length}');
      }

      // Call backend token refresh endpoint
      final response = await http.post(
        Uri.parse('${AppConstants.baseApiUrl}/music/spotify/token/refresh/'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'refresh_token': refreshToken,
        }),
      ).timeout(const Duration(seconds: 30)); // Add timeout for refresh requests

      if (response.statusCode == 200) {
        final tokenData = jsonDecode(response.body);
        
        if (kDebugMode) {
          print('🔄 [SpotifyWebApi] Token refresh response keys: ${tokenData.keys.join(', ')}');
          print('🔄 [SpotifyWebApi] New access token present: ${tokenData.containsKey('access_token')}');
          print('🔄 [SpotifyWebApi] New refresh token present: ${tokenData.containsKey('refresh_token')}');
        }

        // Validate that we got a new access token
        final newAccessToken = tokenData['access_token'];
        if (newAccessToken == null || newAccessToken.isEmpty) {
          if (kDebugMode) {
            print('❌ [SpotifyWebApi] Refresh response missing access token');
          }
          completer.complete(false);
          return false;
        }
        
        // Store new tokens
        await _storeTokens(
          newAccessToken,
          tokenData['refresh_token'] ?? refreshToken, // Use existing refresh token if not returned
          tokenData['expires_in'] ?? 3600, // Default to 1 hour if not provided
        );

        // Verify the new token was stored correctly
        final storedToken = await _secureStorage.read(key: _accessTokenKey);
        if (storedToken != newAccessToken) {
          if (kDebugMode) {
            print('❌ [SpotifyWebApi] Token storage verification failed');
          }
          completer.complete(false);
          return false;
        }

        if (kDebugMode) {
          print('✅ [SpotifyWebApi] Token refreshed and verified successfully');
        }

        completer.complete(true);
        return true;
      } else {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Backend token refresh failed: ${response.statusCode}');
          print('❌ [SpotifyWebApi] Backend refresh response: ${response.body}');
        }
        
        // If refresh token is invalid (401/403), clear all tokens
        if (response.statusCode == 401 || response.statusCode == 403) {
          if (kDebugMode) {
            print('🧹 [SpotifyWebApi] Refresh token invalid, clearing all tokens');
          }
          await _clearAllTokens();
        }

        completer.complete(false);
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error refreshing token: $e');
      }
      completer.complete(false);
      return false;
    } finally {
      _isRefreshing = false;
      _refreshCompleter = null;
      if (kDebugMode) {
        print('🔓 [SpotifyWebApi] Released refresh lock');
      }
    }
  }

  /// Clear all stored tokens
  Future<void> _clearAllTokens() async {
    try {
      await Future.wait([
        _secureStorage.delete(key: _accessTokenKey),
        _secureStorage.delete(key: _refreshTokenKey),
        _secureStorage.delete(key: _expiryTimeKey),
      ]);
      if (kDebugMode) {
        print('🧹 [SpotifyWebApi] All tokens cleared from storage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error clearing tokens: $e');
      }
    }
  }

  /// Make authenticated request to Spotify Web API
  Future<Map<String, dynamic>?> _makeRequest(
    String endpoint, {
    String method = 'GET',
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
  }) async {
    try {
      // Rate limiting
      await _waitForRateLimit();

      final accessToken = await getAccessToken();
      if (accessToken == null) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] No access token available for request');
        }
        return null;
      }

      // Build URL
      String url = endpoint.startsWith('http') 
          ? endpoint 
          : '${AppConstants.spotifyApiBaseUrl}$endpoint';
      
      if (queryParams != null && queryParams.isNotEmpty) {
        final uri = Uri.parse(url);
        url = uri.replace(queryParameters: {...uri.queryParameters, ...queryParams}).toString();
      }

      // Make request
      
      
      final headers = {
        'Authorization': 'Bearer $accessToken',
        'Content-Type': 'application/json',
      };

      late http.Response response;
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(Uri.parse(url), headers: headers)
              .timeout(const Duration(seconds: 15)); // Add timeout like working implementation
          break;
        case 'POST':
          response = await http.post(
            Uri.parse(url),
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(const Duration(seconds: 15));
          break;
        case 'PUT':
          response = await http.put(
            Uri.parse(url),
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(const Duration(seconds: 15));
          break;
        case 'DELETE':
          response = await http.delete(Uri.parse(url), headers: headers)
              .timeout(const Duration(seconds: 15));
          break;
        default:
          throw Exception('Unsupported method: $method');
      }

      if (kDebugMode) {
        if (response.body.isNotEmpty && response.body.length < 200) {
          print('📝 [SpotifyWebApi] Response body preview: ${response.body.substring(0, response.body.length.clamp(0, 100))}');
        }
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Handle different success response types
        if (response.statusCode == 204) {
          // 204 No Content - successful but no response body (common for PUT/POST operations like add to queue)
          if (kDebugMode) {
            print('✅ [SpotifyWebApi] 204 No Content - operation successful');
          }
          return {}; // Return empty map to indicate success
        } else if (response.body.isNotEmpty) {
          try {
            return jsonDecode(response.body);
          } catch (e) {
            if (kDebugMode) {
              print('❌ [SpotifyWebApi] Failed to parse JSON response: $e');
              print('📝 [SpotifyWebApi] Raw response body: ${response.body}');
            }
            // If JSON parsing fails but status is success, treat as successful operation
            return {};
          }
        } else {
          // Empty response body but successful status
          return {};
        }
      } else if (response.statusCode == 401) {
        // Token might be expired, try to refresh and retry once
        if (kDebugMode) {
          print('🔄 [SpotifyWebApi] Got 401, attempting token refresh');
        }
        
        final refreshed = await _refreshTokenWithLock();
        if (refreshed) {
          // Retry the request once with new token
          final newToken = await _secureStorage.read(key: _accessTokenKey);
          if (newToken != null) {
            headers['Authorization'] = 'Bearer $newToken';
            
            switch (method.toUpperCase()) {
              case 'GET':
                response = await http.get(Uri.parse(url), headers: headers)
                    .timeout(const Duration(seconds: 15));
                break;
              case 'POST':
                response = await http.post(
                  Uri.parse(url),
                  headers: headers,
                  body: body != null ? jsonEncode(body) : null,
                ).timeout(const Duration(seconds: 15));
                break;
              case 'PUT':
                response = await http.put(
                  Uri.parse(url),
                  headers: headers,
                  body: body != null ? jsonEncode(body) : null,
                ).timeout(const Duration(seconds: 15));
                break;
              case 'DELETE':
                response = await http.delete(Uri.parse(url), headers: headers)
                    .timeout(const Duration(seconds: 15));
                break;
            }
            
            if (response.statusCode >= 200 && response.statusCode < 300) {
              // Handle retry response same way as above
              if (response.statusCode == 204) {
                if (kDebugMode) {
                  print('✅ [SpotifyWebApi] 204 No Content - retry operation successful');
                }
                return {};
              } else if (response.body.isNotEmpty) {
                try {
                  return jsonDecode(response.body);
                } catch (e) {
                  if (kDebugMode) {
                    print('❌ [SpotifyWebApi] Failed to parse JSON retry response: $e');
                  }
                  return {};
                }
              } else {
                return {};
              }
            }
          }
        }
        
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Authentication failed after refresh attempt');
        }
        return null;
      } else {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] API request failed: ${response.statusCode}');
          print('❌ [SpotifyWebApi] Error response: ${response.body}');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Request error: $e');
      }
      return null;
    }
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final accessToken = await getAccessToken();
    return accessToken != null;
  }

  /// Check if user is connected (alias for compatibility with working implementation)
  Future<bool> isConnected() async {
    return await isAuthenticated();
  }

  /// Debug method to check token storage status
  Future<Map<String, dynamic>> getTokenStorageStatus() async {
    try {
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      final expiryTimeStr = await _secureStorage.read(key: _expiryTimeKey);
      final codeVerifier = await _secureStorage.read(key: _codeVerifierKey);
      
      DateTime? expiryTime;
      if (expiryTimeStr != null) {
        try {
          expiryTime = DateTime.parse(expiryTimeStr);
        } catch (e) {
          // Invalid expiry time format
        }
      }
      
      final now = DateTime.now();
      final timeUntilExpiry = expiryTime?.difference(now);
      final needsRefresh = timeUntilExpiry != null && timeUntilExpiry <= _refreshThreshold;
      final isExpired = expiryTime != null && now.isAfter(expiryTime);
      
      return {
        'hasAccessToken': accessToken != null,
        'hasRefreshToken': refreshToken != null,
        'hasExpiryTime': expiryTime != null,
        'hasCodeVerifier': codeVerifier != null,
        'accessTokenLength': accessToken?.length ?? 0,
        'refreshTokenLength': refreshToken?.length ?? 0,
        'expiryTime': expiryTime?.toIso8601String(),
        'isExpired': isExpired,
        'needsRefresh': needsRefresh,
        'minutesUntilExpiry': timeUntilExpiry?.inMinutes,
        'secondsUntilExpiry': timeUntilExpiry?.inSeconds,
        'refreshThresholdMinutes': _refreshThreshold.inMinutes,
        'isCurrentlyRefreshing': _isRefreshing,
        'lastRefreshAttempt': _lastRefreshAttempt?.toIso8601String(),
        'refreshCooldownSeconds': _refreshCooldown.inSeconds,
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'hasAccessToken': false,
        'hasRefreshToken': false,
        'hasExpiryTime': false,
        'hasCodeVerifier': false,
        'isCurrentlyRefreshing': _isRefreshing,
      };
    }
  }

  /// Get current user's profile
  Future<Map<String, dynamic>?> getCurrentUserProfile() async {
    const cacheKey = 'user_profile';
    
    // Check cache first
    if (_cache.containsKey(cacheKey) && 
        _cacheExpiry[cacheKey]?.isAfter(DateTime.now()) == true) {
      return _cache[cacheKey];
    }

    final data = await _makeRequest('/me');
    if (data != null) {
      _cache[cacheKey] = data;
      _cacheExpiry[cacheKey] = DateTime.now().add(_defaultCacheExpiry);
    }
    
    return data;
  }

  /// Get user's saved tracks (liked songs) - enhanced to match working implementation
  Future<Map<String, dynamic>> getSavedTracks({int limit = 50, int offset = 0}) async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Getting saved tracks (limit: $limit, offset: $offset)');
      }

      // Check if connected like working implementation
      if (!await isConnected()) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Not connected to Spotify for saved tracks');
        }
        throw Exception('Not connected to Spotify');
      }
      
      final data = await _makeRequest('/me/tracks', queryParams: {
        'limit': limit.toString(),
        'offset': offset.toString(),
        'market': 'from_token', // Add market parameter to ensure we get all available tracks
      });

      if (data == null) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] No data returned from Spotify API for saved tracks');
        }
        return {
          'tracks': <MusicTrack>[],
          'total': 0,
          'hasMore': false,
        };
      }

      final List items = data['items'] ?? [];
      final total = data['total'] as int? ?? 0;
      final hasMore = (offset + items.length) < total;

      if (kDebugMode) {
        print('✓ [SpotifyWebApi] Got ${items.length} saved tracks (offset: $offset, total available: $total)');
      }

      final tracks = items.map((item) {
        try {
          final track = item['track'];
          if (track == null) {
            if (kDebugMode) {
              print('⚠️ [SpotifyWebApi] Null track found in saved tracks item');
            }
            return null;
          }
          return _mapTrackResponse(track);
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [SpotifyWebApi] Error mapping saved track: $e');
            print('⚠️ [SpotifyWebApi] Track item: $item');
          }
          return null;
        }
      }).whereType<MusicTrack>().toList(); // Filter out any null tracks from mapping errors

      return {
        'tracks': tracks,
        'total': total,
        'hasMore': hasMore,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error getting saved tracks: $e');
      }
      rethrow; // Rethrow to let the provider handle the error
    }
  }

  /// Get user's available devices
  Future<List<Map<String, dynamic>>> getAvailableDevices() async {
    final data = await _makeRequest('/me/player/devices');
    if (data == null) return [];

    print('🔍 [SpotifyWebApi] Available devices: ${data}');
    
    final List devices = data['devices'] ?? [];
    final deviceList = devices.cast<Map<String, dynamic>>();
    
    // Add detailed logging for all available devices
    if (kDebugMode) {
      print('📱 [SpotifyWebApi] Available devices (${deviceList.length} total):');
      for (int i = 0; i < deviceList.length; i++) {
        final device = deviceList[i];
        print('   ${i + 1}. ${device['name']} (${device['type']}) - Active: ${device['is_active']}, Restricted: ${device['is_restricted']}, Volume: ${device['volume_percent']}%');
      }
    }
    
    return deviceList;
  }

  /// Get available smartphone devices only
  Future<List<Map<String, dynamic>>> getSmartphoneDevices() async {
    final allDevices = await getAvailableDevices();
    final smartphones = allDevices.where((device) => 
      device['type']?.toString().toLowerCase() == 'smartphone'
    ).toList();
    
    if (kDebugMode) {
      print('📱 [SpotifyWebApi] Smartphone devices found: ${smartphones.length}');
      for (int i = 0; i < smartphones.length; i++) {
        final device = smartphones[i];
        print('   ${i + 1}. ${device['name']} - Active: ${device['is_active']}, Restricted: ${device['is_restricted']}');
      }
    }
    
    return smartphones;
  }

  /// Get the currently active device
  Future<Map<String, dynamic>?> getActiveDevice() async {
    final devices = await getAvailableDevices();
    try {
      return devices.firstWhere((device) => device['is_active'] == true);
    } catch (e) {
      return null; // No active device found
    }
  }

  /// Get the currently active smartphone device
  Future<Map<String, dynamic>?> getActiveSmartphoneDevice() async {
    final smartphones = await getSmartphoneDevices();
    try {
      return smartphones.firstWhere((device) => device['is_active'] == true);
    } catch (e) {
      if (kDebugMode) {
        print('📱 [SpotifyWebApi] No active smartphone device found');
      }
      return null; // No active smartphone device found
    }
  }

  /// Transfer playback to a specific device
  Future<bool> transferPlaybackToDevice(String deviceId, {bool play = false}) async {
    try {
      if (kDebugMode) {
        print('🔄 [SpotifyWebApi] Transferring playback to device: $deviceId (play: $play)');
      }
      
      final success = await _makeRequest(
        '/me/player',
        method: 'PUT',
        body: {
          'device_ids': [deviceId],
          'play': play,
        },
      );
      
      final result = success != null;
      if (kDebugMode) {
        print('${result ? '✅' : '❌'} [SpotifyWebApi] Transfer playback ${result ? 'successful' : 'failed'}');
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error transferring playback: $e');
      }
      return false;
    }
  }

  /// Ensure there's an active smartphone device for playback
  Future<Map<String, dynamic>?> ensureActiveSmartphoneDevice() async {
    try {
      if (kDebugMode) {
        print('📱 [SpotifyWebApi] Ensuring active smartphone device...');
      }
      
      // First check if there's already an active smartphone device
      final activeSmartphone = await getActiveSmartphoneDevice();
      if (activeSmartphone != null) {
        if (kDebugMode) {
          print('✅ [SpotifyWebApi] Active smartphone device found: ${activeSmartphone['name']}');
        }
        return activeSmartphone;
      }

      // No active smartphone device, get all available smartphones
      final smartphones = await getSmartphoneDevices();
      if (smartphones.isEmpty) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] No smartphone devices available');
        }
        return null;
      }

      // Try to find a suitable smartphone device (prefer non-restricted devices)
      Map<String, dynamic> suitableDevice;
      try {
        suitableDevice = smartphones.firstWhere((device) => device['is_restricted'] != true);
        if (kDebugMode) {
          print('📱 [SpotifyWebApi] Found non-restricted smartphone: ${suitableDevice['name']}');
        }
      } catch (e) {
        suitableDevice = smartphones.first; // Fallback to first smartphone
        if (kDebugMode) {
          print('📱 [SpotifyWebApi] Using first available smartphone: ${suitableDevice['name']}');
        }
      }

      if (kDebugMode) {
        print('🔄 [SpotifyWebApi] Attempting to transfer playback to smartphone: ${suitableDevice['name']}');
      }

      // Transfer playback to the smartphone device
      final success = await transferPlaybackToDevice(suitableDevice['id'], play: false);
      if (success) {
        if (kDebugMode) {
          print('✅ [SpotifyWebApi] Successfully transferred to smartphone device');
        }
        return suitableDevice;
      } else {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Failed to transfer playback to smartphone device');
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error ensuring active smartphone device: $e');
      }
      return null;
    }
  }

  /// Ensure there's an active device for playback (kept for backward compatibility)
  Future<Map<String, dynamic>?> ensureActiveDevice() async {
    // Delegate to smartphone-specific method
    return await ensureActiveSmartphoneDevice();
  }

  /// Get user's recently played tracks with pagination support
  Future<Map<String, dynamic>> getRecentlyPlayed({
    int limit = 20,
    int? beforeTimestamp,
  }) async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Getting recently played tracks (limit: $limit, before: $beforeTimestamp)');
      }

      // Check if connected
      if (!await isConnected()) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Not connected to Spotify for recently played');
        }
        throw Exception('Not connected to Spotify');
      }

      final queryParams = {
        'limit': limit.toString(),
      };
      
      // Add before timestamp for pagination if provided
      if (beforeTimestamp != null) {
        queryParams['before'] = beforeTimestamp.toString();
      }

      final data = await _makeRequest('/me/player/recently-played', queryParams: queryParams);

      if (data == null) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] No data returned from recently played API');
        }
        return {
          'tracks': <MusicTrack>[],
          'hasMore': false,
          'nextBeforeTimestamp': null,
        };
      }

      final List items = data['items'] ?? [];
      final cursors = data['cursors'] as Map<String, dynamic>?;
      
      if (kDebugMode) {
        print('✓ [SpotifyWebApi] Got ${items.length} recently played tracks');
      }

      // Process tracks and remove duplicates by track ID
      final Set<String> seenTrackIds = <String>{};
      final List<MusicTrack> tracks = [];
      
      for (final item in items) {
        try {
          final track = item['track'];
          if (track == null) continue;
          
          final trackId = track['id'] as String?;
          if (trackId == null || seenTrackIds.contains(trackId)) {
            continue; // Skip duplicate tracks
          }
          
          seenTrackIds.add(trackId);
          final mapped = _mapTrackResponse(track);
          
          // Add played_at timestamp if available
          if (item['played_at'] != null) {
            try {
              final playedAt = DateTime.parse(item['played_at']);
              tracks.add(mapped.copyWith(releaseDate: playedAt));
            } catch (e) {
              tracks.add(mapped);
            }
          } else {
            tracks.add(mapped);
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [SpotifyWebApi] Error mapping recently played track: $e');
          }
          continue;
        }
      }

      // Determine if there are more tracks available
      final hasMore = items.length >= limit && cursors != null && cursors['before'] != null;
      
      // Extract the "before" timestamp for next page
      int? nextBeforeTimestamp;
      if (hasMore && cursors != null && cursors['before'] != null) {
        try {
          // Convert the before cursor to timestamp
          final beforeCursor = cursors['before'] as String;
          // The cursor is already a Unix timestamp in milliseconds
          nextBeforeTimestamp = int.parse(beforeCursor);
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [SpotifyWebApi] Error parsing before cursor: $e');
          }
        }
      }

      if (kDebugMode) {
        print('✓ [SpotifyWebApi] Processed ${tracks.length} unique recently played tracks (hasMore: $hasMore)');
      }

      return {
        'tracks': tracks,
        'hasMore': hasMore,
        'nextBeforeTimestamp': nextBeforeTimestamp,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error getting recently played tracks: $e');
      }
      rethrow;
    }
  }

  /// Search for tracks (enhanced to match working implementation)
  Future<List<MusicTrack>> searchTracks(String query, {int limit = 20, int offset = 0}) async {
    try {
      if (query.isEmpty) return [];

      if (kDebugMode) {
        print('🔍 [SpotifyWebApi] Searching for tracks: "$query" (limit: $limit, offset: $offset)');
      }

      // Check if connected like working implementation
      if (!await isConnected()) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Not connected to Spotify for search');
        }
        throw Exception('Not connected to Spotify');
      }

      // Build cache key like working implementation
      final cacheKey = '$query|$limit|$offset';
      if (_searchCache.containsKey(cacheKey)) {
        if (kDebugMode) {
          print('✓ [SpotifyWebApi] Returning cached search results for "$query"');
        }
        return _searchCache[cacheKey]!;
      }

      final data = await _makeRequest('/search', queryParams: {
        'q': query,
        'type': 'track',
        'limit': limit.toString(),
        'offset': offset.toString(),
      });

      if (data == null) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] No data returned from search for "$query"');
        }
        return [];
      }

      final tracks = data['tracks'];
      if (tracks == null) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] No tracks section in search response for "$query"');
        }
        return [];
      }

      final List items = tracks['items'] ?? [];
      if (kDebugMode) {
        print('✓ [SpotifyWebApi] Got ${items.length} search results for "$query"');
      }

      final results = items.map((track) => _mapTrackResponse(track)).toList();
      
      // Cache the results like working implementation
      _searchCache[cacheKey] = results;
      
      return results;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error searching tracks: $e');
      }
      return [];
    }
  }

  /// Get user's playlists (enhanced to match working implementation)
  Future<List<Map<String, dynamic>>> getUserPlaylists({int limit = 50, int offset = 0}) async {
    try {
      if (kDebugMode) {
        print('📝 [SpotifyWebApi] Getting user playlists (limit: $limit, offset: $offset)');
      }

      // Check if connected like working implementation
      if (!await isConnected()) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Not connected to Spotify for playlists');
        }
        throw Exception('Not connected to Spotify');
      }
      
      final data = await _makeRequest('/me/playlists', queryParams: {
        'limit': limit.toString(),
        'offset': offset.toString(),
      });

      if (data == null || !data.containsKey('items')) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] No data returned from Spotify API for user playlists');
        }
        return [];
      }

      final List items = data['items'] ?? [];
      if (kDebugMode) {
        print('✓ [SpotifyWebApi] Got ${items.length} user playlists');
      }
      
      return items.map((item) {
        try {
          // Safely extract image URL from images array
          String? imageUrl;
          final images = item['images'];
          if (images is List && images.isNotEmpty) {
            final firstImage = images[0];
            if (firstImage is Map<String, dynamic> && firstImage['url'] is String) {
              imageUrl = firstImage['url'];
            }
          }

          // Safely extract tracks information
          int tracksCount = 0;
          final tracks = item['tracks'];
          if (tracks is Map<String, dynamic> && tracks['total'] is int) {
            tracksCount = tracks['total'];
          }

          // Safely extract owner information
          String ownerName = 'Unknown';
          String? ownerId;
          final owner = item['owner'];
          if (owner is Map<String, dynamic>) {
            if (owner['display_name'] is String) {
            ownerName = owner['display_name'];
            }
            if (owner['id'] is String) {
              ownerId = owner['id'];
            }
          }

          // Extract collaborative status
          final isCollaborative = item['collaborative'] is bool ? item['collaborative'] : false;
          
          // Extract public status
          final isPublic = item['public'] is bool ? item['public'] : false;
          
          // Extract snapshot_id for activity tracking
          final snapshotId = item['snapshot_id']?.toString() ?? '';

          if (kDebugMode) {
            print('🎵 [SpotifyWebApi] Playlist "${item['name']}": collaborative=$isCollaborative, public=$isPublic, tracks=$tracksCount, snapshot=$snapshotId');
          }

          return {
            'id': item['id']?.toString() ?? '',
            'name': item['name']?.toString() ?? 'Unknown Playlist',
            'description': item['description']?.toString() ?? '',
            'image_url': imageUrl, // Use image_url format expected by UI
            'tracks_count': tracksCount,
            'tracks': {
              'total': tracksCount, // Also provide in tracks.total format for compatibility
            },
            'public': isPublic,
            'collaborative': isCollaborative,
            'snapshot_id': snapshotId,
            'owner': {
              'display_name': ownerName,
              'id': ownerId,
            }, // Use proper owner structure expected by UI
          };
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [SpotifyWebApi] Error mapping playlist: $e');
            print('⚠️ [SpotifyWebApi] Playlist item: $item');
          }
          // Return a safe fallback playlist object
          return {
            'id': item['id']?.toString() ?? '',
            'name': item['name']?.toString() ?? 'Unknown Playlist',
            'description': '',
            'image_url': null, // Use image_url format expected by UI
            'tracks_count': 0,
            'tracks': {'total': 0},
            'public': false,
            'collaborative': false,
            'snapshot_id': '',
            'owner': {
              'display_name': 'Unknown',
              'id': null,
            }, // Use proper owner structure expected by UI
          };
        }
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error getting user playlists: $e');
      }
      return [];
    }
  }

  /// Get playlist tracks (enhanced to match working implementation)
  Future<Map<String, dynamic>> getPlaylistTracks(String playlistId, {int limit = 50, int offset = 0}) async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Getting playlist tracks (playlist: $playlistId, limit: $limit, offset: $offset)');
      }

      // Check if connected like working implementation
      if (!await isConnected()) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Not connected to Spotify for playlist tracks');
        }
        throw Exception('Not connected to Spotify');
      }
      
      final data = await _makeRequest('/playlists/$playlistId/tracks', queryParams: {
        'limit': limit.toString(),
        'offset': offset.toString(),
        'market': 'from_token', // Add market parameter to ensure we get all available tracks
      });

      if (data == null || !data.containsKey('items')) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] No data returned from Spotify API for playlist tracks');
        }
        return {
          'tracks': <MusicTrack>[],
          'total': 0,
          'hasMore': false,
        };
      }

      final List items = data['items'] ?? [];
      final total = data['total'] as int? ?? 0;
      final hasMore = (offset + items.length) < total;

      if (kDebugMode) {
        print('✓ [SpotifyWebApi] Got ${items.length} playlist tracks (offset: $offset, total available: $total)');
      }

      final tracks = items.map((item) {
        try {
          final track = item['track'];
          if (track == null) {
            if (kDebugMode) {
              print('⚠️ [SpotifyWebApi] Null track found in playlist item');
            }
            return null;
          }
          return _mapTrackResponse(track);
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [SpotifyWebApi] Error mapping playlist track: $e');
            print('⚠️ [SpotifyWebApi] Track item: $item');
          }
          return null;
        }
      }).whereType<MusicTrack>().toList(); // Filter out any null tracks from mapping errors

      return {
        'tracks': tracks,
        'total': total,
        'hasMore': hasMore,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error getting playlist tracks: $e');
      }
      rethrow; // Rethrow to let the provider handle the error
    }
  }

  /// Get current playback state
  Future<Map<String, dynamic>?> getCurrentPlayback() async {
    return await _makeRequest('/me/player');
  }

  /// Play a track with automatic recommendations (enhanced version)
  Future<bool> playTrackWithRecommendations(String trackUri) async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Playing track with recommendations: $trackUri');
      }
      
      return await startPlayback(trackUri: trackUri);
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error playing track with recommendations: $e');
      }
      rethrow;
    }
  }

  /// Start/Resume playback
  Future<bool> startPlayback({String? trackUri, List<String>? trackUris, String? contextUri}) async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Starting playback...');
      }
      
      // Get available smartphone devices
      final smartphones = await getSmartphoneDevices();
      
      if (smartphones.isNotEmpty) {
        // Try Web API playback on smartphone devices first
        final webApiSuccess = await _attemptWebApiPlayback(smartphones, trackUri, trackUris, contextUri);
        if (webApiSuccess) {
          return true;
        }
      }

      // Fallback to SDK if no smartphone devices or Web API failed
      if (kDebugMode) {
        print('📱 [SpotifyWebApi] Web API playback failed, attempting SDK fallback...');
      }
      
      return await _attemptSdkPlayback(trackUri, trackUris, contextUri);
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error in startPlayback: $e');
      }
      
      // If the error message is already our custom message, re-throw it
      if (e.toString().contains('you need to open spotify app on your phone')) {
        rethrow;
      }
      
      // For other errors, wrap with our custom message
      throw Exception('you need to open spotify app on your phone');
    }
  }

  /// Build context-aware playback body for better recommendations
  Future<Map<String, dynamic>?> _buildContextPlaybackBody(String trackUri) async {
    try {
      // Extract track ID from URI (spotify:track:ID or https://open.spotify.com/track/ID)
      String? trackId;
      if (trackUri.startsWith('spotify:track:')) {
        trackId = trackUri.split(':').last;
      } else if (trackUri.contains('open.spotify.com/track/')) {
        final uri = Uri.parse(trackUri);
        trackId = uri.pathSegments.last.split('?').first; // Remove query params if present
      }
      
      if (trackId == null || trackId.isEmpty) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] Could not extract track ID from URI: $trackUri');
        }
        return null;
      }
      
      if (kDebugMode) {
        print('🔍 [SpotifyWebApi] Getting track details for context: $trackId');
      }
      
      // Get track details to find its album
      final trackDetails = await _makeRequest('/tracks/$trackId');
      if (trackDetails == null) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] Could not get track details for: $trackId');
        }
        return null;
      }
      
      final album = trackDetails['album'];
      if (album == null || album['uri'] == null) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] Track has no album context, trying radio-style playback...');
        }
        // Try radio-style playback as fallback
        return await _buildRadioPlaybackBody(trackUri, trackDetails);
      }
      
      final albumUri = album['uri'] as String;
      final trackNumber = trackDetails['track_number'] as int? ?? 1;
      
      if (kDebugMode) {
        print('✅ [SpotifyWebApi] Found album context: $albumUri (track #$trackNumber)');
      }
      
      // Play from album context starting at the specific track
      return {
        'context_uri': albumUri,
        'offset': {
          'uri': trackUri, // Start at this specific track
        },
      };
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error building context playback: $e');
      }
      return null;
    }
  }

  /// Build radio-style playback body using recommendations
  Future<Map<String, dynamic>?> _buildRadioPlaybackBody(String trackUri, Map<String, dynamic> trackDetails) async {
    try {
      if (kDebugMode) {
        print('🔍 [SpotifyWebApi] Building radio-style queue for: ${trackDetails['name']}');
      }
      
      // Extract track ID from the URI for recommendations
      String trackId;
      if (trackUri.startsWith('spotify:track:')) {
        trackId = trackUri.split(':').last;
      } else {
        final uri = Uri.parse(trackUri);
        trackId = uri.pathSegments.last.split('?').first;
      }
      
      // Get recommendations based on this track
      final recommendations = await _makeRequest('/recommendations', queryParams: {
        'seed_tracks': trackId,
        'limit': '20', // Get enough tracks for a good radio experience
      });
      
      if (recommendations == null || recommendations['tracks'] == null) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] Could not get recommendations for radio playback');
        }
        return null;
      }
      
      final recommendedTracks = recommendations['tracks'] as List;
      if (recommendedTracks.isEmpty) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] No recommendations returned for radio playback');
        }
        return null;
      }
      
      // Build a queue starting with the original track, followed by recommendations
      final uris = <String>[trackUri]; // Start with the requested track
      uris.addAll(recommendedTracks.map((track) => track['uri'] as String));
      
      if (kDebugMode) {
        print('✅ [SpotifyWebApi] Built radio queue with ${uris.length} tracks (original + ${recommendedTracks.length} recommendations)');
      }
      
      return {
        'uris': uris,
      };
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error building radio playback: $e');
      }
      return null;
    }
  }

  /// Attempt Web API playback on smartphone devices
  Future<bool> _attemptWebApiPlayback(List<Map<String, dynamic>> smartphones, String? trackUri, List<String>? trackUris, String? contextUri) async {
    // Build the playback request body with context-aware playback for better recommendations
    final body = <String, dynamic>{};
    
    if (trackUri != null) {
      // For single track playback, try to get album context for better recommendations
      final contextPlayback = await _buildContextPlaybackBody(trackUri);
      if (contextPlayback != null) {
        body.addAll(contextPlayback);
        if (kDebugMode) {
          print('🎵 [SpotifyWebApi] Using context-based playback for better recommendations');
        }
      } else {
        // Fallback to single track playback
        body['uris'] = [trackUri];
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] Using single track playback (no recommendations will follow)');
        }
      }
    } else if (trackUris != null) {
      body['uris'] = trackUris;
    } else if (contextUri != null) {
      body['context_uri'] = contextUri;
    }

    // Try to play on each smartphone device (active first, then inactive)
    final sortedSmartphones = smartphones.toList();
    sortedSmartphones.sort((a, b) {
      // Active devices first
      if (a['is_active'] == true && b['is_active'] != true) return -1;
      if (b['is_active'] == true && a['is_active'] != true) return 1;
      // Non-restricted devices first
      if (a['is_restricted'] != true && b['is_restricted'] == true) return -1;
      if (b['is_restricted'] != true && a['is_restricted'] == true) return 1;
      return 0;
    });

    for (final smartphone in sortedSmartphones) {
      if (kDebugMode) {
        print('📱 [SpotifyWebApi] Attempting Web API playback on ${smartphone['name']} (Active: ${smartphone['is_active']}, Restricted: ${smartphone['is_restricted']})');
      }

      try {
        // If device is not active, try to transfer playback to it first
        if (smartphone['is_active'] != true) {
          if (kDebugMode) {
            print('🔄 [SpotifyWebApi] Device not active, attempting to transfer playback...');
          }
          
          final transferSuccess = await transferPlaybackToDevice(smartphone['id'], play: false);
          if (!transferSuccess) {
            if (kDebugMode) {
              print('⚠️ [SpotifyWebApi] Failed to transfer to ${smartphone['name']}, trying direct playback...');
            }
          } else {
            // Wait a moment for the transfer to complete
            await Future.delayed(const Duration(milliseconds: 500));
          }
        }

        // Attempt playback on this device
        final result = await _makeRequest('/me/player/play', method: 'PUT', body: {
          ...body,
          'device_id': smartphone['id'], // Specify the device ID for direct playback
        });

        if (result != null) {
          if (kDebugMode) {
            print('✅ [SpotifyWebApi] Web API playback started successfully on ${smartphone['name']}');
          }
          return true;
        } else {
          if (kDebugMode) {
            print('⚠️ [SpotifyWebApi] Web API playback failed on ${smartphone['name']}, trying next device...');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] Error attempting Web API playback on ${smartphone['name']}: $e');
        }
        continue; // Try next device
      }
    }

    return false; // All smartphone devices failed
  }

  /// Attempt SDK playback as fallback
  Future<bool> _attemptSdkPlayback(String? trackUri, List<String>? trackUris, String? contextUri) async {
    try {
      if (kDebugMode) {
        print('🎧 [SpotifyWebApi] Attempting SDK playback fallback...');
      }

      // Ensure SDK connection
      final sdkConnected = await _ensureSdkConnection();
      if (!sdkConnected) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Failed to connect to Spotify SDK');
        }
        throw Exception('you need to open spotify app on your phone');
      }

      // Double-check that we have available devices after SDK connection
      final devices = await getAvailableDevices();
      final smartphones = devices.where((device) => 
        device['type']?.toString().toLowerCase() == 'smartphone'
      ).toList();
      
      if (smartphones.isEmpty) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] SDK connected but no smartphone devices available - Spotify app likely closed');
        }
        // Reset SDK connection state since app appears closed
        _sdkConnected = false;
        throw Exception('you need to open spotify app on your phone');
      }

      if (kDebugMode) {
        print('✅ [SpotifyWebApi] SDK connected with ${smartphones.length} smartphone device(s) available');
      }

      // Determine what to play
      String? playUri;
      if (trackUri != null) {
        playUri = trackUri;
      } else if (trackUris != null && trackUris.isNotEmpty) {
        playUri = trackUris.first; // Play first track
      } else if (contextUri != null) {
        playUri = contextUri;
      }

      if (playUri == null) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] No URI to play via SDK');
        }
        throw Exception('you need to open spotify app on your phone');
      }

      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Attempting to play URI via SDK: $playUri');
      }

      // Attempt SDK playback
      await SpotifySdk.play(spotifyUri: playUri);
      
      // Wait a moment and verify playback started
      await Future.delayed(const Duration(milliseconds: 1500));
      final playerState = await SpotifySdk.getPlayerState();
      
      if (playerState != null && playerState.track != null) {
        if (kDebugMode) {
          print('✅ [SpotifyWebApi] SDK playback started successfully - now playing: ${playerState.track?.name}');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] SDK playback command sent but verification failed - may need user interaction');
        }
        
        // Check if devices disappeared after playback attempt
        final devicesAfterPlay = await getAvailableDevices();
        final smartphonesAfterPlay = devicesAfterPlay.where((device) => 
          device['type']?.toString().toLowerCase() == 'smartphone'
        ).toList();
        
        if (smartphonesAfterPlay.isEmpty) {
          if (kDebugMode) {
            print('❌ [SpotifyWebApi] No devices available after playback attempt - Spotify app likely closed');
          }
          _sdkConnected = false;
        }
        
        throw Exception('you need to open spotify app on your phone');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] SDK playback failed: $e');
      }
      
      // If it's not our custom error message, check if it's a connection issue
      if (!e.toString().contains('you need to open spotify app on your phone')) {
        // Reset SDK connection for next attempt
        _sdkConnected = false;
      }
      
      throw Exception('you need to open spotify app on your phone');
    }
  }

  /// Ensure SDK connection (for playback fallback only)
  Future<bool> _ensureSdkConnection() async {
    if (_sdkConnected) {
      // Verify connection is still active by checking both player state and available devices
      try {
        if (kDebugMode) {
          print('🔍 [SpotifyWebApi] Verifying existing SDK connection...');
        }
        
        // Check if there are any available devices first (faster check)
        final devices = await getAvailableDevices();
        final smartphones = devices.where((device) => 
          device['type']?.toString().toLowerCase() == 'smartphone'
        ).toList();
        
        if (smartphones.isEmpty) {
          if (kDebugMode) {
            print('⚠️ [SpotifyWebApi] No smartphone devices found - forcing SDK reconnection');
            print('🔄 [SpotifyWebApi] Forcefully disconnecting SDK...');
          }
          
          // Force disconnect - don't wait for verification
          _sdkConnected = false;
          
          try {
            await SpotifySdk.disconnect();
            if (kDebugMode) {
              print('✅ [SpotifyWebApi] SDK disconnect completed');
            }
          } catch (e) {
            if (kDebugMode) {
              print('⚠️ [SpotifyWebApi] Error during forced disconnect: $e (continuing anyway)');
            }
          }
          
          // Force a small delay to ensure disconnect is processed
          await Future.delayed(const Duration(milliseconds: 500));
          
          // Fall through to reconnection logic
        } else {
          // We have smartphone devices, verify SDK player state works
          try {
            await SpotifySdk.getPlayerState();
            if (kDebugMode) {
              print('✅ [SpotifyWebApi] SDK connection verified - ${smartphones.length} smartphone device(s) available');
            }
            return true;
          } catch (playerStateError) {
            if (kDebugMode) {
              print('⚠️ [SpotifyWebApi] SDK player state check failed: $playerStateError');
            }
            _sdkConnected = false;
            // Fall through to reconnection
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Error during SDK verification: $e');
        }
        _sdkConnected = false;
      }
    }

    // At this point, we need to establish a new SDK connection
    if (_sdkConnecting) {
      if (kDebugMode) {
        print('🔄 [SpotifyWebApi] SDK connection already in progress...');
      }
      return false;
    }

    final now = DateTime.now();
    if (_lastSdkConnectionAttempt != null &&
        now.difference(_lastSdkConnectionAttempt!) < const Duration(seconds: 5)) { // Reduced cooldown
      if (kDebugMode) {
        print('⏳ [SpotifyWebApi] SDK connection attempted recently, skipping...');
      }
      return false;
    }

    return await _performSdkConnection();
  }

  /// Perform the actual SDK connection with retry logic
  Future<bool> _performSdkConnection() async {
    try {
      _sdkConnecting = true;
      _lastSdkConnectionAttempt = DateTime.now();

      if (kDebugMode) {
        print('🚀 [SpotifyWebApi] Starting fresh SDK connection (this should open Spotify app)...');
      }

      // Try to connect multiple times if needed
      bool connectionSuccessful = false;
      int attempts = 0;
      const maxAttempts = 3; // Increased attempts

      while (!connectionSuccessful && attempts < maxAttempts) {
        attempts++;
        
        try {
          if (kDebugMode) {
            print('🔄 [SpotifyWebApi] SDK connection attempt ${attempts}/${maxAttempts}...');
          }

          // This should open the Spotify app if it's not running
          await SpotifySdk.connectToSpotifyRemote(
            clientId: AppConstants.spotifyClientId,
            redirectUrl: AppConstants.spotifyRedirectUri,
          );

          if (kDebugMode) {
            print('✅ [SpotifyWebApi] SDK connection established, verifying player state...');
          }

          // Verify connection by checking player state
          await SpotifySdk.getPlayerState();
          
          connectionSuccessful = true;
          
          if (kDebugMode) {
            print('✅ [SpotifyWebApi] SDK player state verified successfully');
          }
          
        } catch (connectError) {
          if (kDebugMode) {
            print('❌ [SpotifyWebApi] SDK connection attempt ${attempts} failed: $connectError');
          }
          
          if (attempts < maxAttempts) {
            if (kDebugMode) {
              print('⏳ [SpotifyWebApi] Waiting ${attempts * 1000}ms before retry...');
            }
            await Future.delayed(Duration(milliseconds: attempts * 1000)); // Progressive delay
          } else {
            // Final attempt failed, rethrow the error
            throw connectError;
          }
        }
      }
      
      // Give Spotify time to register as a device after connection/app opening
      if (kDebugMode) {
        print('⏳ [SpotifyWebApi] Waiting for Spotify app to register devices...');
      }
      
      // Check devices multiple times with increasing delays
      List<Map<String, dynamic>> smartphones = [];
      final waitTimes = [1000, 2000, 3000]; // Progressive waiting
      
      for (int i = 0; i < waitTimes.length; i++) {
        await Future.delayed(Duration(milliseconds: waitTimes[i]));
        
        final devices = await getAvailableDevices();
        smartphones = devices.where((device) => 
          device['type']?.toString().toLowerCase() == 'smartphone'
        ).toList();
        
        if (kDebugMode) {
          print('🔍 [SpotifyWebApi] Device check ${i + 1}/${waitTimes.length}: ${smartphones.length} smartphone devices found');
        }
        
        if (smartphones.isNotEmpty) {
          break; // Found devices, stop waiting
        }
      }
      
      _sdkConnected = true;
      
      if (smartphones.isEmpty) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] SDK connected but no smartphone devices found after all checks');
          print('💡 [SpotifyWebApi] User may need to manually open and play something in Spotify app');
        }
      } else {
        if (kDebugMode) {
          print('🎉 [SpotifyWebApi] Success! Found ${smartphones.length} smartphone device(s) after SDK connection');
        }
      }

      return true;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] All SDK connection attempts failed: $e');
        if (e.toString().contains('AUTHENTICATION_SERVICE_UNAVAILABLE') || 
            e.toString().contains('APP_NOT_INSTALLED')) {
          print('💡 [SpotifyWebApi] Spotify app may not be installed or available');
        } else if (e.toString().contains('REMOTE_CONNECTION_FAILED')) {
          print('💡 [SpotifyWebApi] Could not connect to Spotify app - it may be closed');
        }
      }
      _sdkConnected = false;
      return false;
    } finally {
      _sdkConnecting = false;
    }
  }

  /// Check if SDK is connected
  bool get isSdkConnected => _sdkConnected;

  /// Disconnect SDK (called during cleanup)
  Future<void> _disconnectSdk() async {
    if (_sdkConnected) {
      try {
        await SpotifySdk.disconnect();
        if (kDebugMode) {
          print('🎧 [SpotifyWebApi] SDK disconnected');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] Error disconnecting SDK: $e');
        }
      }
      _sdkConnected = false;
    }
  }

  /// Pause playback
  Future<bool> pausePlayback() async {
    final result = await _makeRequest('/me/player/pause', method: 'PUT');
    return result != null;
  }

  /// Skip to next track
  Future<bool> skipToNext() async {
    final result = await _makeRequest('/me/player/next', method: 'POST');
    return result != null;
  }

  /// Skip to previous track
  Future<bool> skipToPrevious() async {
    final result = await _makeRequest('/me/player/previous', method: 'POST');
    return result != null;
  }

  /// Seek to position
  Future<bool> seekToPosition(int positionMs) async {
    final result = await _makeRequest('/me/player/seek', method: 'PUT', queryParams: {
      'position_ms': positionMs.toString(),
    });
    return result != null;
  }

  /// Set volume
  Future<bool> setVolume(int volumePercent) async {
    final result = await _makeRequest('/me/player/volume', method: 'PUT', queryParams: {
      'volume_percent': volumePercent.toString(),
    });
    return result != null;
  }

  /// Toggle shuffle
  Future<bool> setShuffle(bool shuffle) async {
    final result = await _makeRequest('/me/player/shuffle', method: 'PUT', queryParams: {
      'state': shuffle.toString(),
    });
    return result != null;
  }

  /// Set repeat mode
  Future<bool> setRepeatMode(String mode) async {
    // mode: 'track', 'context', 'off'
    final result = await _makeRequest('/me/player/repeat', method: 'PUT', queryParams: {
      'state': mode,
    });
    return result != null;
  }

  /// Get user's top tracks
  Future<List<MusicTrack>> getTopTracks({String timeRange = 'medium_term', int limit = 20}) async {
    final data = await _makeRequest('/me/top/tracks', queryParams: {
      'time_range': timeRange,
      'limit': limit.toString(),
    });

    if (data == null) return [];

    final List items = data['items'] ?? [];
    return items.map((track) => _mapTrackResponse(track)).toList();
  }

  /// Get user's top artists
  Future<List<Map<String, dynamic>>> getTopArtists({
    String timeRange = 'medium_term', 
    int limit = 20
  }) async {
    final data = await _makeRequest('/me/top/artists', queryParams: {
      'time_range': timeRange,
      'limit': limit.toString(),
    });

    if (data == null) return [];

    final List items = data['items'] ?? [];
    return items.map((artist) {
      try {
        // Safely extract image URL from images array
        String? imageUrl;
        final images = artist['images'];
        if (images is List && images.isNotEmpty) {
          final firstImage = images[0];
          if (firstImage is Map<String, dynamic> && firstImage['url'] is String) {
            imageUrl = firstImage['url'];
          }
        }

        // Safely extract genres list
        List<String> genres = [];
        final artistGenres = artist['genres'];
        if (artistGenres is List) {
          genres = artistGenres.map((genre) => genre.toString()).toList();
        }

        return {
          'id': artist['id']?.toString() ?? '',
          'name': artist['name']?.toString() ?? 'Unknown Artist',
          'image_url': imageUrl, // Use same field name as working implementation
          'popularity': (artist['popularity'] is int) ? artist['popularity'] : 0,
          'genres': genres,
          'url': artist['external_urls']?['spotify']?.toString() ?? '',
        };
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] Error mapping artist: $e');
          print('⚠️ [SpotifyWebApi] Artist item: $artist');
        }
        // Return a safe fallback artist object
        return {
          'id': artist['id']?.toString() ?? '',
          'name': artist['name']?.toString() ?? 'Unknown Artist',
          'image_url': null, // Use same field name as working implementation
          'popularity': 0,
          'genres': <String>[],
          'url': '',
        };
      }
    }).toList();
  }

  /// Search for tracks by genre using proper Spotify query syntax (matching working implementation)
  Future<List<MusicTrack>> searchTracksByGenre(String genre, {int limit = 20, int offset = 0, int? year, String? artist}) async {
    try {
      if (genre.isEmpty) {
        return [];
      }
      
      // Build proper Spotify query with genre filter
      String query = 'genre:"$genre"';
      
      // Add optional filters
      if (year != null) {
        query += ' year:$year';
      }
      if (artist != null && artist.isNotEmpty) {
        query += ' artist:"$artist"';
      }
      
      if (kDebugMode) {
        print('🔍 [SpotifyWebApi] Searching by genre: "$query" (limit: $limit, offset: $offset)');
      }
      
      // Check if connected
      if (!await isConnected()) {
        throw Exception('Not connected to Spotify');
      }
      
      // Build cache key
      final cacheKey = 'genre:$genre|$limit|$offset|${year ?? ''}|${artist ?? ''}';
      if (_searchCache.containsKey(cacheKey)) {
        if (kDebugMode) {
          print('✓ [SpotifyWebApi] Returning cached genre search results for "$query"');
        }
        return _searchCache[cacheKey]!;
      }
      
      // Use the actual Spotify API
      final data = await _makeRequest('/search', queryParams: {
        'q': query,
        'type': 'track',
        'limit': limit.toString(),
        if (offset > 0) 'offset': offset.toString(),
      });
      
      if (data == null) {
        return [];
      }
      
      final tracks = data['tracks'];
      if (tracks == null) {
        return [];
      }
      
      final List items = tracks['items'] ?? [];
      if (kDebugMode) {
        print('✓ [SpotifyWebApi] Got ${items.length} genre-filtered results for "$query"');
      }
      
      final results = items.map((track) => _mapTrackResponse(track)).toList();
      _searchCache[cacheKey] = results;
      return results;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error searching tracks by genre: $e');
      }
      return [];
    }
  }

  /// Get user-friendly device status
  Future<Map<String, dynamic>> getDeviceStatus() async {
    try {
      final devices = await getAvailableDevices();
      final smartphones = await getSmartphoneDevices();
      final activeDevice = await getActiveDevice();
      final activeSmartphone = await getActiveSmartphoneDevice();
      
      // Check if SDK connection is meaningful (has devices available)
      bool sdkUsable = _sdkConnected && smartphones.isNotEmpty;
      if (_sdkConnected && smartphones.isEmpty) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] SDK connected but no devices - may indicate closed Spotify app');
        }
      }
      
      return {
        'hasDevices': devices.isNotEmpty,
        'hasActiveDevice': activeDevice != null,
        'deviceCount': devices.length,
        'activeDeviceName': activeDevice?['name'],
        'hasSmartphones': smartphones.isNotEmpty,
        'hasActiveSmartphone': activeSmartphone != null,
        'smartphoneCount': smartphones.length,
        'activeSmartphoneName': activeSmartphone?['name'],
        'sdkConnected': _sdkConnected,
        'sdkUsable': sdkUsable, // SDK connected AND has devices
        'sdkConnectionIssue': _sdkConnected && smartphones.isEmpty, // Connected but no devices
        'canUseSdkFallback': true, // SDK is always available as fallback
        'devices': devices.map((device) => {
          'name': device['name'],
          'type': device['type'],
          'is_active': device['is_active'],
          'is_restricted': device['is_restricted'],
        }).toList(),
        'smartphones': smartphones.map((device) => {
          'name': device['name'],
          'type': device['type'],
          'is_active': device['is_active'],
          'is_restricted': device['is_restricted'],
        }).toList(),
        'canPlayback': smartphones.isNotEmpty || sdkUsable, // Can play via Web API or usable SDK
        'canPlaybackOnSmartphone': activeSmartphone != null && activeSmartphone['is_restricted'] != true,
        'recommendedAction': _getRecommendedAction(smartphones, _sdkConnected, sdkUsable),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error getting device status: $e');
      }
      return {
        'hasDevices': false,
        'hasActiveDevice': false,
        'deviceCount': 0,
        'activeDeviceName': null,
        'hasSmartphones': false,
        'hasActiveSmartphone': false,
        'smartphoneCount': 0,
        'activeSmartphoneName': null,
        'sdkConnected': _sdkConnected,
        'sdkUsable': false,
        'sdkConnectionIssue': _sdkConnected, // Connected but error getting devices
        'canUseSdkFallback': true,
        'devices': <Map<String, dynamic>>[],
        'smartphones': <Map<String, dynamic>>[],
        'canPlayback': false,
        'canPlaybackOnSmartphone': false,
        'recommendedAction': 'Open Spotify app on your phone',
      };
    }
  }

  /// Get recommended action for user based on device and SDK status
  String _getRecommendedAction(List<Map<String, dynamic>> smartphones, bool sdkConnected, bool sdkUsable) {
    if (smartphones.isNotEmpty) {
      return 'Ready for playback via Web API';
    } else if (sdkUsable) {
      return 'Ready for playback via SDK';
    } else if (sdkConnected && smartphones.isEmpty) {
      return 'Open Spotify app on your phone - SDK connected but no devices found';
    } else {
      return 'Open Spotify app on your phone';
    }
  }

  /// Disconnect (clear tokens)
  Future<void> disconnect() async {
    // Disconnect SDK first
    await _disconnectSdk();
    
    await Future.wait([
      _secureStorage.delete(key: _accessTokenKey),
      _secureStorage.delete(key: _refreshTokenKey),
      _secureStorage.delete(key: _expiryTimeKey),
      _secureStorage.delete(key: _codeVerifierKey),
    ]);
    
    // Clear cache
    _cache.clear();
    _cacheExpiry.clear();
  }

  /// Helper methods (enhanced to match working implementation)
  Future<void> _waitForRateLimit() async {
    final diff = DateTime.now().difference(_lastRequestTime);
    if (diff < _minRequestInterval) {
      await Future.delayed(_minRequestInterval - diff);
    }
    _lastRequestTime = DateTime.now();
  }

  String _generateRandomString(int length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random.secure();
    return String.fromCharCodes(Iterable.generate(
      length, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  /// Map Spotify track response to MusicTrack model
  MusicTrack _mapTrackResponse(Map<String, dynamic> track) {
    // Extract album art using same approach as working implementation
    String albumArt = '';
    if (track['album'] != null && 
        track['album']['images'] != null && 
        track['album']['images'].isNotEmpty) {
      albumArt = track['album']['images'][0]['url'];
    }
    
    // Extract artist name - handle multiple artists like working implementation
    String artistName = 'Unknown Artist';
    if (track['artists'] != null && track['artists'].isNotEmpty) {
      artistName = track['artists'].map((artist) => artist['name']).join(', ');
    }
    
    // Extract genres from artists if available
    List<String> genres = [];
    if (track['artists'] != null && 
        track['artists'].isNotEmpty && 
        track['artists'][0]['genres'] != null) {
      genres = List<String>.from(track['artists'][0]['genres'] ?? []);
    }
    
    // Build URI consistently
    String uri = '';
    if (track['uri'] != null) {
      uri = track['uri'];
    } else if (track['external_urls'] != null && track['external_urls']['spotify'] != null) {
      uri = 'spotify:track:${track['id']}';
    }
    
    // Parse release date with same logic as working implementation
    DateTime? releaseDate;
    if (track['album'] != null && track['album']['release_date'] != null) {
      releaseDate = _parseReleaseDate(track['album']['release_date']);
    }
    
    return MusicTrack(
      id: track['id'] ?? '',
      title: track['name'] ?? 'Unknown Track',
      artist: artistName,
      album: track['album'] != null ? track['album']['name'] ?? '' : '',
      albumArt: albumArt,
      url: track['external_urls'] != null ? track['external_urls']['spotify'] ?? '' : '',
      service: 'spotify',
      previewUrl: track['preview_url'],
      albumArtUrl: albumArt, // Add this field for compatibility
      serviceType: 'spotify',
      genres: genres,
      durationMs: track['duration_ms'] ?? 0,
      releaseDate: releaseDate,
      explicit: track['explicit'] ?? false,
      popularity: track['popularity'] ?? 50,
      uri: uri,
    );
  }

  /// Parse release date with better error handling for different formats (from working implementation)
  DateTime? _parseReleaseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    
    try {
      // Handle different date formats from Spotify
      if (dateString.length == 4) {
        // Year only (e.g., "1991")
        return DateTime(int.parse(dateString));
      } else if (dateString.length == 7 && dateString.contains('-')) {
        // Year-month (e.g., "1991-05")
        final parts = dateString.split('-');
        return DateTime(int.parse(parts[0]), int.parse(parts[1]));
      } else {
        // Full date (e.g., "1991-05-15")
        return DateTime.parse(dateString);
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [SpotifyWebApi] Failed to parse release date "$dateString": $e');
      }
      return null;
    }
  }

  /// Get user's current queue
  Future<Map<String, dynamic>?> getUserQueue() async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Getting user queue');
      }

      // Check if connected
      if (!await isConnected()) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Not connected to Spotify for queue');
        }
        throw Exception('Not connected to Spotify');
      }

      final data = await _makeRequest('/me/player/queue');

      if (data == null) {
        if (kDebugMode) {
          print('⚠️ [SpotifyWebApi] No data returned from queue API');
        }
        return {
          'currently_playing': null,
          'queue': <Map<String, dynamic>>[],
        };
      }

      // Process the currently playing track
      Map<String, dynamic>? currentlyPlaying;
      if (data['currently_playing'] != null) {
        try {
          currentlyPlaying = data['currently_playing'];
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [SpotifyWebApi] Error processing currently playing track: $e');
          }
        }
      }

      // Process the queue tracks
      final List queueItems = data['queue'] ?? [];
      final processedQueue = <Map<String, dynamic>>[];
      
      for (final item in queueItems) {
        try {
          // Handle both tracks and episodes
          if (item != null && item['type'] != null) {
            processedQueue.add(item);
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [SpotifyWebApi] Error processing queue item: $e');
          }
          continue;
        }
      }

      if (kDebugMode) {
        print('✓ [SpotifyWebApi] Got queue: currently playing = ${currentlyPlaying != null ? currentlyPlaying['name'] : 'none'}, ${processedQueue.length} items in queue');
      }

      return {
        'currently_playing': currentlyPlaying,
        'queue': processedQueue,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error getting user queue: $e');
      }
      rethrow;
    }
  }

  /// Add track to user's queue
  Future<bool> addToQueue(String trackUri) async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Adding track to queue: $trackUri');
      }

      // Check if connected
      if (!await isConnected()) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Not connected to Spotify for add to queue');
        }
        throw Exception('Not connected to Spotify');
      }

      // Validate track URI format
      if (!trackUri.startsWith('spotify:track:') && !trackUri.contains('open.spotify.com')) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Invalid track URI format: $trackUri');
        }
        throw Exception('Invalid track URI format');
      }

      // Ensure we have a valid access token before making the request
      final accessToken = await getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] No valid access token for add to queue');
        }
        throw Exception('No valid access token');
      }

      if (kDebugMode) {
        print('🔑 [SpotifyWebApi] Using access token (length: ${accessToken.length})');
        print('🎯 [SpotifyWebApi] Making add to queue request...');
      }

      // Make the request - _makeRequest now handles 204 responses properly
      final result = await _makeRequest('/me/player/queue', method: 'POST', queryParams: {
        'uri': trackUri,
      });

      final success = result != null;
      if (kDebugMode) {
        print('${success ? '✅' : '❌'} [SpotifyWebApi] Add to queue ${success ? 'successful' : 'failed'} for: $trackUri');
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error adding to queue: $e');
        print('❌ [SpotifyWebApi] Error type: ${e.runtimeType}');
      }
      return false;
    }
  }

  /// Get user's top genres based on their top artists
  Future<List<String>> getTopGenres({int limit = 10, String timeRange = 'medium_term'}) async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Getting top genres (limit: $limit, timeRange: $timeRange)');
      }

      // Check if connected
      if (!await isConnected()) {
        throw Exception('Not connected to Spotify');
      }

      // Get top artists first
      final response = await _makeRequest('/me/top/artists', queryParams: {
        'limit': '50', // Get more artists to capture more genres
        'time_range': timeRange, // short_term, medium_term, or long_term
      });

      if (response == null || !response.containsKey('items')) {
        return [];
      }

      // Extract all genres from artists
      final List<dynamic> artists = response['items'];
      final Map<String, int> genreCounts = {};

      // Count genre occurrences
      for (var artist in artists) {
        final List<dynamic> genres = artist['genres'] ?? [];
        for (var genre in genres) {
          genreCounts[genre] = (genreCounts[genre] ?? 0) + 1;
        }
      }

      // Sort genres by count
      final sortedGenres = genreCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      // Return top genres up to the limit
      final topGenres = sortedGenres
          .take(limit)
          .map((entry) => entry.key)
          .toList();

      if (kDebugMode) {
        print('✅ [SpotifyWebApi] Retrieved ${topGenres.length} top genres');
      }

      return topGenres;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error getting top genres: $e');
      }
      return [];
    }
  }

  /// Get multiple artists' information by their names (search for them first to get IDs)
  Future<List<Map<String, dynamic>>> getMultipleArtistsByNames(List<String> artistNames) async {
    try {
      if (kDebugMode) {
        print('🔍 [SpotifyWebApi] Getting artist info for ${artistNames.length} artists');
      }

      if (!await isConnected()) {
        throw Exception('Not connected to Spotify');
      }

      final artistIds = <String>[];
      final batchSize = 5; // Search multiple artists per request

      // Search for artists in batches to reduce API calls
      for (int i = 0; i < artistNames.length; i += batchSize) {
        final batch = artistNames.skip(i).take(batchSize).toList();
        final searchQuery = batch.join(' OR ');

        try {
          final searchResponse = await _makeRequest('/search', queryParams: {
            'q': searchQuery,
            'type': 'artist',
            'limit': (batch.length * 2).toString(), // Get more results to find best matches
          });

          if (searchResponse != null && 
              searchResponse['artists'] != null && 
              searchResponse['artists']['items'] != null) {

            final artists = searchResponse['artists']['items'] as List;

            // Match returned artists to our search terms
            for (final artistName in batch) {
              final matchedArtist = artists.firstWhere(
                (artist) => artist['name'].toString().toLowerCase().contains(artistName.toLowerCase()) ||
                           artistName.toLowerCase().contains(artist['name'].toString().toLowerCase()),
                orElse: () => null,
              );

              if (matchedArtist != null) {
                final artistId = matchedArtist['id'] as String?;
                if (artistId != null && !artistIds.contains(artistId)) {
                  artistIds.add(artistId);
                  if (kDebugMode) {
                    print('✓ [SpotifyWebApi] Found artist: ${matchedArtist['name']} -> $artistId');
                  }
                }
              }
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [SpotifyWebApi] Failed to search for artist batch "$searchQuery": $e');
          }
        }
      }

      if (artistIds.isEmpty) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] No artist IDs found');
        }
        return [];
      }

      // Now get detailed info for all artists using the Get Several Artists endpoint
      final artistIdsParam = artistIds.take(50).join(','); // Spotify limit is 50 artists per request
      if (kDebugMode) {
        print('🎤 [SpotifyWebApi] Getting detailed info for artists: $artistIdsParam');
      }

      final response = await _makeRequest('/artists', queryParams: {
        'ids': artistIdsParam,
      });

      if (response == null || !response.containsKey('artists')) {
        return [];
      }

      final artists = response['artists'] as List;
      return artists.map<Map<String, dynamic>>((artist) => {
        'id': artist['id'],
        'name': artist['name'],
        'image_url': artist['images']?.isNotEmpty == true ? artist['images'][0]['url'] : null,
        'genres': List<String>.from(artist['genres'] ?? []),
        'popularity': artist['popularity'],
        'url': artist['external_urls']['spotify'],
        'followers': artist['followers']['total'],
      }).toList();

    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error getting multiple artists: $e');
      }
      return [];
    }
  }

  /// Search for tracks inspired by multiple artists (enhanced recommendation approach)
  Future<List<MusicTrack>> searchTracksInspiredByArtists(
    List<String> artistNames, {
    String? genre,
    int limit = 20,
    int? year,
  }) async {
    try {
      if (artistNames.isEmpty) {
        return [];
      }

      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Searching tracks inspired by ${artistNames.length} artists (genre: $genre, limit: $limit)');
      }

      final allTracks = <MusicTrack>[];

      // Strategy 1: Use "similar to" queries for each artist
      for (final artist in artistNames.take(3)) { // Limit to avoid too many API calls
        try {
          String query = 'similar to "$artist"';
          if (genre != null) {
            query = 'genre:"$genre" similar to "$artist"';
          }
          if (year != null) {
            query += ' year:$year';
          }

          final tracks = await searchTracks(query, limit: limit ~/ artistNames.length);
          allTracks.addAll(tracks);
          if (kDebugMode) {
            print('📥 [SpotifyWebApi] Got ${tracks.length} tracks similar to "$artist"');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ [SpotifyWebApi] Error getting tracks similar to "$artist": $e');
          }
        }
      }

      // Strategy 2: Search for tracks that mention multiple artists
      if (artistNames.length >= 2) {
        try {
          final artistPairs = <String>[];
          for (int i = 0; i < artistNames.length - 1; i++) {
            artistPairs.add('"${artistNames[i]}" "${artistNames[i + 1]}"');
          }

          for (final pair in artistPairs.take(2)) {
            String query = 'fans of $pair';
            if (genre != null) {
              query = 'genre:"$genre" fans of $pair';
            }

            final tracks = await searchTracks(query, limit: 5);
            allTracks.addAll(tracks);
            if (kDebugMode) {
              print('📥 [SpotifyWebApi] Got ${tracks.length} tracks for fans of $pair');
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ [SpotifyWebApi] Error getting tracks for artist combinations: $e');
          }
        }
      }

      // Strategy 3: Genre-based search if specified
      if (genre != null) {
        try {
          final genreTracks = await searchTracksByGenre(genre, limit: limit ~/ 3, year: year);
          allTracks.addAll(genreTracks);
          if (kDebugMode) {
            print('📥 [SpotifyWebApi] Got ${genreTracks.length} additional genre tracks');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ [SpotifyWebApi] Error getting additional genre tracks: $e');
          }
        }
      }

      // Remove duplicates and return
      final seenIds = <String>{};
      final uniqueTracks = allTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      if (kDebugMode) {
        print('✓ [SpotifyWebApi] Got ${uniqueTracks.length} tracks inspired by ${artistNames.length} artists');
      }
      return uniqueTracks.take(limit).toList();

    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error searching tracks inspired by artists: $e');
      }
      return [];
    }
  }

  /// Search using artist combinations and genre constraints
  Future<List<MusicTrack>> searchWithArtistSeeds(
    List<String> seedArtists, {
    String? targetGenre,
    int limit = 20,
    List<String>? excludeArtists,
  }) async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyWebApi] Searching with artist seeds: ${seedArtists.length} artists (genre: $targetGenre, limit: $limit)');
      }

      final allTracks = <MusicTrack>[];

      // Build queries that combine artists with genre
      final queries = <String>[];

      // Single artist inspiration queries
      for (final artist in seedArtists.take(3)) {
        if (targetGenre != null) {
          queries.addAll([
            'genre:"$targetGenre" inspired by "$artist"',
            'genre:"$targetGenre" similar to "$artist"',
            'genre:"$targetGenre" fans of "$artist"',
            'genre:"$targetGenre" like "$artist"',
          ]);
        } else {
          queries.addAll([
            'inspired by "$artist"',
            'similar to "$artist"',
            'fans of "$artist"',
            'like "$artist"',
          ]);
        }
      }

      // Multi-artist combination queries
      if (seedArtists.length >= 2) {
        final artistCombos = [
          '${seedArtists[0]} ${seedArtists[1]}',
          if (seedArtists.length >= 3) '${seedArtists[0]} ${seedArtists[2]}',
        ];

        for (final combo in artistCombos) {
          if (targetGenre != null) {
            queries.addAll([
              'genre:"$targetGenre" fans of $combo',
              'genre:"$targetGenre" similar to $combo',
            ]);
          } else {
            queries.addAll([
              'fans of $combo',
              'similar to $combo',
            ]);
          }
        }
      }

      // Execute queries
      final maxQueries = 6; // Limit to avoid rate limits
      for (int i = 0; i < math.min(queries.length, maxQueries); i++) {
        try {
          final tracks = await searchTracks(queries[i], limit: limit ~/ maxQueries + 2);

          // Filter out excluded artists if specified
          final filteredTracks = excludeArtists != null 
            ? tracks.where((track) => !excludeArtists.any((excluded) => 
                track.artist.toLowerCase().contains(excluded.toLowerCase()))).toList()
            : tracks;

          allTracks.addAll(filteredTracks);
          if (kDebugMode) {
            print('📥 [SpotifyWebApi] Got ${filteredTracks.length} tracks for "${queries[i]}"');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ [SpotifyWebApi] Error with query "${queries[i]}": $e');
          }
        }
      }

      // Deduplicate and return
      final seenIds = <String>{};
      final uniqueTracks = allTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      return uniqueTracks.take(limit).toList();

    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error in artist seed search: $e');
      }
      return [];
    }
  }

  /// Enhanced search with multiple types and filters
  Future<Map<String, dynamic>> searchMultipleTypes(
    String query, {
    List<String> types = const ['track'],
    int limit = 20,
    String? market,
  }) async {
    try {
      if (query.isEmpty) {
        return {};
      }

      final typeString = types.join(',');
      if (kDebugMode) {
        print('🔍 [SpotifyWebApi] Multi-type search: "$query" (types: $typeString, limit: $limit)');
      }

      // Check if connected
      if (!await isConnected()) {
        throw Exception('Not connected to Spotify');
      }

      final queryParams = {
        'q': query,
        'type': typeString,
        'limit': limit.toString(),
      };

      if (market != null) {
        queryParams['market'] = market;
      }

      // Use the actual Spotify API
      final data = await _makeRequest('/search', queryParams: queryParams);

      if (data == null) {
        return {};
      }

      if (kDebugMode) {
        print('✓ [SpotifyWebApi] Got multi-type search results for "$query"');
      }
      return data;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error in multi-type search: $e');
      }
      return {};
    }
  }

  /// Search for a specific track by artist and title (for cross-platform pin playback)
  Future<MusicTrack?> searchTrackByArtistAndTitle(String artist, String title) async {
    try {
      if (artist.isEmpty || title.isEmpty) {
        if (kDebugMode) {
          print('❌ [SpotifyWebApi] Cannot search with empty artist or title');
        }
        return null;
      }

      if (kDebugMode) {
        print('🔍 [SpotifyWebApi] Searching for EXACT track: "$title" by "$artist"');
      }

      // Clean and normalize the search terms  
      final cleanTitle = _removeFeaturingFromTitle(title);
      final cleanArtist = _normalizeArtistName(artist);
      
      if (kDebugMode) {
        print('🔍 [SpotifyWebApi] Normalized: "$cleanTitle" by "$cleanArtist"');
      }

      // Try multiple search approaches with higher precision
      final searchQueries = <Map<String, String>>[];
      
      // Strategy 1: Most precise with clean terms
      if (cleanTitle != title && cleanTitle.isNotEmpty) {
        searchQueries.add({
          'query': 'artist:"$cleanArtist" track:"$cleanTitle"',
          'title': cleanTitle,
          'artist': cleanArtist,
          'type': 'clean_precise'
        });
        searchQueries.add({
          'query': '"$cleanTitle" "$cleanArtist"',
          'title': cleanTitle,
          'artist': cleanArtist,
          'type': 'clean_quoted'
        });
      }
      
      // Strategy 2: Precise with original terms
      searchQueries.add({
        'query': 'artist:"$artist" track:"$title"',
        'title': title,
        'artist': artist,
        'type': 'original_precise'
      });
      searchQueries.add({
        'query': '"$title" "$artist"',
        'title': title,
        'artist': artist,
        'type': 'original_quoted'
      });
      
      // Strategy 3: Combined searches (less precise but broader)
      searchQueries.add({
        'query': '$cleanTitle $cleanArtist',
        'title': cleanTitle,
        'artist': cleanArtist,
        'type': 'clean_combined'
      });
      searchQueries.add({
        'query': '$title $artist',
        'title': title,
        'artist': artist,
        'type': 'original_combined'
      });
      
      // Try each search strategy
      for (int i = 0; i < searchQueries.length; i++) {
        final searchInfo = searchQueries[i];
        final query = searchInfo['query']!;
        final titleToMatch = searchInfo['title']!;
        final artistToMatch = searchInfo['artist']!;
        final searchType = searchInfo['type']!;
        
        if (kDebugMode) {
          print('🔍 [SpotifyWebApi] Search attempt ${i + 1} ($searchType): "$query"');
        }
        
        final tracks = await searchTracks(query, limit: 30);
        
        if (tracks.isEmpty) {
          if (kDebugMode) {
            print('❌ [SpotifyWebApi] No tracks found for search ${i + 1}');
          }
          continue;
        }
        
        if (kDebugMode) {
          print('✓ [SpotifyWebApi] Search ${i + 1} got ${tracks.length} results');
          // Log first few results for debugging
          for (int j = 0; j < math.min(3, tracks.length); j++) {
            print('   ${j + 1}. "${tracks[j].title}" by "${tracks[j].artist}"');
          }
        }
        
        final bestMatch = _findBestMatchWithScoring(tracks, artistToMatch, titleToMatch);
        
        if (bestMatch != null) {
          if (kDebugMode) {
            print('✅ [SpotifyWebApi] Found EXACT match with search ${i + 1}: "${bestMatch.title}" by "${bestMatch.artist}"');
          }
          return bestMatch;
        } else if (kDebugMode) {
          print('❌ [SpotifyWebApi] No suitable exact match in search ${i + 1} results');
        }
      }
      
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] No EXACT match found for "$title" by "$artist"');
      }
      return null;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] Error searching track by artist and title: $e');
      }
      return null;
    }
  }

  /// Normalize artist name by removing common variations
  String _normalizeArtistName(String artist) {
    if (artist.isEmpty) return artist;
    
    String result = artist.trim();
    
    // Remove featuring artists from main artist field
    final patterns = [
      RegExp(r'\s*feat\..*', caseSensitive: false),
      RegExp(r'\s*featuring.*', caseSensitive: false),
      RegExp(r'\s*ft\..*', caseSensitive: false),
      RegExp(r'\s*with.*', caseSensitive: false),
      RegExp(r'\s*&.*', caseSensitive: false),
      RegExp(r'\s*\+.*', caseSensitive: false),
      RegExp(r'\s*vs\..*', caseSensitive: false),
      RegExp(r'\s*vs\s.*', caseSensitive: false),
    ];
    
    for (final pattern in patterns) {
      result = result.replaceAll(pattern, '').trim();
    }
    
    // Remove extra whitespace
    result = result.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    return result;
  }

  /// Remove featuring information from track title (enhanced)
  String _removeFeaturingFromTitle(String title) {
    if (title.isEmpty) return title;
    
    String result = title.trim();
    
    // Remove featuring patterns in parentheses, brackets, and with dashes
    final patterns = [
      RegExp(r'\s*\(feat\..*?\)', caseSensitive: false),
      RegExp(r'\s*\(featuring.*?\)', caseSensitive: false),
      RegExp(r'\s*\(ft\..*?\)', caseSensitive: false),
      RegExp(r'\s*\(with.*?\)', caseSensitive: false),
      RegExp(r'\s*\[feat\..*?\]', caseSensitive: false),
      RegExp(r'\s*\[featuring.*?\]', caseSensitive: false),
      RegExp(r'\s*\[ft\..*?\]', caseSensitive: false),
      RegExp(r'\s*\- feat\..*', caseSensitive: false),
      RegExp(r'\s*\- featuring.*', caseSensitive: false),
      RegExp(r'\s*\- ft\..*', caseSensitive: false),
      // Remove remix/version info
      RegExp(r'\s*\(.*remix.*?\)', caseSensitive: false),
      RegExp(r'\s*\(.*version.*?\)', caseSensitive: false),
      RegExp(r'\s*\(.*edit.*?\)', caseSensitive: false),
      RegExp(r'\s*\[.*remix.*?\]', caseSensitive: false),
      RegExp(r'\s*\[.*version.*?\]', caseSensitive: false),
      // Remove year information
      RegExp(r'\s*\(19\d{2}\)', caseSensitive: false),
      RegExp(r'\s*\(20\d{2}\)', caseSensitive: false),
      // Remove remaster info
      RegExp(r'\s*\(.*remaster.*?\)', caseSensitive: false),
      RegExp(r'\s*\[.*remaster.*?\]', caseSensitive: false),
    ];
    
    for (final pattern in patterns) {
      result = result.replaceAll(pattern, '').trim();
    }
    
    // Remove extra whitespace
    result = result.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    return result;
  }

  /// Enhanced matching with scoring system for precise matches
  MusicTrack? _findBestMatchWithScoring(List<MusicTrack> tracks, String targetArtist, String targetTitle) {
    if (tracks.isEmpty) return null;
    
    // Normalize targets for comparison
    final normalizedTargetArtist = targetArtist.toLowerCase().trim();
    final normalizedTargetTitle = targetTitle.toLowerCase().trim();
    
    if (kDebugMode) {
      print('🔍 [SpotifyWebApi] Looking for EXACT: artist="$normalizedTargetArtist", title="$normalizedTargetTitle"');
    }
    
    var bestMatch = tracks.first;
    var bestScore = 0.0;
    
    for (final track in tracks) {
      final normalizedTrackArtist = track.artist.toLowerCase().trim();
      final normalizedTrackTitle = track.title.toLowerCase().trim();
      
      // Calculate match score
      var score = 0.0;
      
      // EXACT MATCHES get highest priority
      if (normalizedTrackArtist == normalizedTargetArtist && 
          normalizedTrackTitle == normalizedTargetTitle) {
        score = 1000.0; // Perfect match
      }
      
      // Artist exact + title contains (very good)
      else if (normalizedTrackArtist == normalizedTargetArtist && 
               normalizedTrackTitle.contains(normalizedTargetTitle)) {
        score = 900.0;
      }
      
      // Title exact + artist contains (very good)
      else if (normalizedTrackTitle == normalizedTargetTitle && 
               normalizedTrackArtist.contains(normalizedTargetArtist)) {
        score = 850.0;
      }
      
      // Reverse matching - target contains track (handles abbreviated titles)
      else if (normalizedTargetArtist.contains(normalizedTrackArtist) && 
               normalizedTargetTitle.contains(normalizedTrackTitle)) {
        score = 800.0;
      }
      
      // Both contain (but not exact)
      else if (normalizedTrackArtist.contains(normalizedTargetArtist) && 
               normalizedTrackTitle.contains(normalizedTargetTitle)) {
        score = 700.0;
      }
      
      // Title exact match only (risky but sometimes necessary)
      else if (normalizedTrackTitle == normalizedTargetTitle) {
        score = 600.0;
      }
      
      // Artist exact match only (very risky)
      else if (normalizedTrackArtist == normalizedTargetArtist) {
        score = 500.0;
      }
      
      // Strong title match (contains both ways)
      else if (normalizedTrackTitle.contains(normalizedTargetTitle) || 
               normalizedTargetTitle.contains(normalizedTrackTitle)) {
        if (normalizedTrackArtist.contains(normalizedTargetArtist) ||
            normalizedTargetArtist.contains(normalizedTrackArtist)) {
          score = 400.0;
        }
      }
      
      // Apply bonuses and penalties
      if (score > 0) {
        // Bonus for popularity (if available)
        if (track.popularity > 80) {
          score += 10.0;
        } else if (track.popularity > 60) {
          score += 5.0;
        }
        
        // Penalty for significant length differences
        final titleLengthDiff = (normalizedTrackTitle.length - normalizedTargetTitle.length).abs();
        final artistLengthDiff = (normalizedTrackArtist.length - normalizedTargetArtist.length).abs();
        
        if (titleLengthDiff > 15) score -= (titleLengthDiff * 2);
        if (artistLengthDiff > 10) score -= (artistLengthDiff * 3);
        
        // Penalty for very short matches (avoid generic matches)
        if (normalizedTargetTitle.length < 3 || normalizedTargetArtist.length < 3) {
          score -= 50.0;
        }
      }
      
      if (score > bestScore) {
        bestScore = score;
        bestMatch = track;
      }
      
      if (kDebugMode && score > 0) {
        print('   📊 Score ${score.toStringAsFixed(1)}: "${track.title}" by "${track.artist}"');
      }
    }
    
    // Only return matches with high confidence (exact or very close matches)
    if (bestScore >= 850.0) {
      if (kDebugMode) {
        print('✅ [SpotifyWebApi] EXCELLENT match (score: ${bestScore.toStringAsFixed(1)}): "${bestMatch.title}" by "${bestMatch.artist}"');
      }
      return bestMatch;
    } else if (bestScore >= 700.0) {
      if (kDebugMode) {
        print('⚠️ [SpotifyWebApi] GOOD match (score: ${bestScore.toStringAsFixed(1)}): "${bestMatch.title}" by "${bestMatch.artist}"');
      }
      return bestMatch;
    } else if (bestScore >= 600.0) {
      if (kDebugMode) {
        print('⚠️ [SpotifyWebApi] ACCEPTABLE match (score: ${bestScore.toStringAsFixed(1)}): "${bestMatch.title}" by "${bestMatch.artist}"');
      }
      return bestMatch;
    } else {
      if (kDebugMode) {
        print('❌ [SpotifyWebApi] NO reliable match found (best score: ${bestScore.toStringAsFixed(1)}) - rejecting to avoid wrong song');
      }
      return null;
    }
  }

  /// Find the best matching track from search results (legacy method - now using enhanced scoring)
  MusicTrack? _findBestMatch(List<MusicTrack> tracks, String targetArtist, String targetTitle) {
    return _findBestMatchWithScoring(tracks, targetArtist, targetTitle);
  }

} 