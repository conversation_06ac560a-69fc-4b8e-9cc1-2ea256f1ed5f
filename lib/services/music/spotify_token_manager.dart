import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../config/constants.dart';
import '../analytics/analytics_service.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

/// Manages Spotify authentication tokens with advanced refresh logic
class SpotifyTokenManager {
  static final SpotifyTokenManager _instance = SpotifyTokenManager._internal();
  factory SpotifyTokenManager() => _instance;
  SpotifyTokenManager._internal();

  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final AnalyticsService _analytics = AnalyticsService();
  
  // Token-related keys
  static const String _accessTokenKey = 'spotify_access_token';
  static const String _refreshTokenKey = 'spotify_refresh_token';
  static const String _expiryTimeKey = 'spotify_token_expiry';
  static const String _tokenTypeKey = 'spotify_token_type';
  
  // Refresh token lock to prevent multiple simultaneous refreshes
  bool _isRefreshing = false;
  Completer<bool>? _refreshCompleter;
  
  // Token refresh threshold (refresh if less than 5 minutes remaining)
  static const int _refreshThresholdSeconds = 300;
  
  /// Get access token with automatic refresh if needed
  Future<String?> getAccessToken() async {
    try {
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      final expiryTimeString = await _secureStorage.read(key: _expiryTimeKey);
      
      if (accessToken == null || expiryTimeString == null) {
        return null;
      }
      
      final expiryTime = DateTime.parse(expiryTimeString);
      final now = DateTime.now();
      
      // Check if token needs refresh
      if (now.isAfter(expiryTime.subtract(Duration(seconds: _refreshThresholdSeconds)))) {
        // Token needs refresh
        final refreshed = await refreshToken();
        if (!refreshed) {
          _analytics.trackError('token_refresh_failed', 'Token refresh failed', null);
          return null;
        }
        return await _secureStorage.read(key: _accessTokenKey);
      }
      
      return accessToken;
    } catch (e, stackTrace) {
      _analytics.trackError('get_access_token_error', e, stackTrace);
      return null;
    }
  }
  
  /// Store new tokens
  Future<void> storeTokens({
    required String accessToken,
    required String refreshToken,
    required DateTime expiryTime,
    String tokenType = 'Bearer',
  }) async {
    try {
      await Future.wait([
        _secureStorage.write(key: _accessTokenKey, value: accessToken),
        _secureStorage.write(key: _refreshTokenKey, value: refreshToken),
        _secureStorage.write(key: _expiryTimeKey, value: expiryTime.toIso8601String()),
        _secureStorage.write(key: _tokenTypeKey, value: tokenType),
      ]);
      
      _analytics.trackEvent('tokens_stored', {
        'expiry_time': expiryTime.toIso8601String(),
        'token_type': tokenType,
      });
    } catch (e, stackTrace) {
      _analytics.trackError('store_tokens_error', e, stackTrace);
      rethrow;
    }
  }
  
  /// Refresh token with locking mechanism
  Future<bool> refreshToken() async {
    if (_isRefreshing) {
      // Wait for existing refresh to complete
      return await _refreshCompleter?.future ?? false;
    }
    
    _isRefreshing = true;
    final completer = Completer<bool>();
    _refreshCompleter = completer;
    
    try {
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      if (refreshToken == null) {
        _analytics.trackError('refresh_token_missing', 'No refresh token found', null);
        return false;
      }
      
      final response = await http.post(
        Uri.parse(AppConstants.spotifyTokenUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'grant_type': 'refresh_token',
          'refresh_token': refreshToken,
          'client_id': AppConstants.spotifyClientId,
          'client_secret': AppConstants.spotifyClientSecret,
        },
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final newAccessToken = data['access_token'];
        final newRefreshToken = data['refresh_token'] ?? refreshToken;
        final expiresIn = data['expires_in'] as int;
        final tokenType = data['token_type'] ?? 'Bearer';
        
        final expiryTime = DateTime.now().add(Duration(seconds: expiresIn));
        
        await storeTokens(
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          expiryTime: expiryTime,
          tokenType: tokenType,
        );
        
        _analytics.trackEvent('token_refreshed', {
          'expiry_time': expiryTime.toIso8601String(),
        });
        
        completer.complete(true);
        return true;
      } else {
        _analytics.trackError(
          'token_refresh_failed',
          'Failed to refresh token: ${response.statusCode}',
          null,
        );
        completer.complete(false);
        return false;
      }
    } catch (e, stackTrace) {
      _analytics.trackError('refresh_token_error', e, stackTrace);
      completer.complete(false);
      return false;
    } finally {
      _isRefreshing = false;
    }
  }
  
  /// Clear all tokens
  Future<void> clearTokens() async {
    try {
      await Future.wait([
        _secureStorage.delete(key: _accessTokenKey),
        _secureStorage.delete(key: _refreshTokenKey),
        _secureStorage.delete(key: _expiryTimeKey),
        _secureStorage.delete(key: _tokenTypeKey),
      ]);
      
      _analytics.trackEvent('tokens_cleared');
    } catch (e, stackTrace) {
      _analytics.trackError('clear_tokens_error', e, stackTrace);
      rethrow;
    }
  }
  
  /// Check if we have valid tokens
  Future<bool> hasValidTokens() async {
    try {
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      final expiryTimeString = await _secureStorage.read(key: _expiryTimeKey);
      
      if (accessToken == null || refreshToken == null || expiryTimeString == null) {
        return false;
      }
      
      final expiryTime = DateTime.parse(expiryTimeString);
      return DateTime.now().isBefore(expiryTime);
    } catch (e, stackTrace) {
      _analytics.trackError('check_valid_tokens_error', e, stackTrace);
      return false;
    }
  }
  
  /// Get token expiry time
  Future<DateTime?> getTokenExpiryTime() async {
    try {
      final expiryTimeString = await _secureStorage.read(key: _expiryTimeKey);
      if (expiryTimeString != null) {
        return DateTime.parse(expiryTimeString);
      }
      return null;
    } catch (e, stackTrace) {
      _analytics.trackError('get_token_expiry_error', e, stackTrace);
      return null;
    }
  }
  
  /// Get remaining token validity duration
  Future<Duration?> getTokenValidityDuration() async {
    try {
      final expiryTime = await getTokenExpiryTime();
      if (expiryTime != null) {
        return expiryTime.difference(DateTime.now());
      }
      return null;
    } catch (e, stackTrace) {
      _analytics.trackError('get_token_validity_error', e, stackTrace);
      return null;
    }
  }
} 