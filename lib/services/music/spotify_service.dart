import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'dart:io'; // Add this import for HttpClient
import '../../config/constants.dart';
import '../../models/music_track.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'spotify_token_manager.dart';
import 'spotify_genre_service.dart';
import '../auth_service.dart' as CoreAuthService;
import '../../screens/auth/services/auth_service.dart' as UiAuthService;
import 'dart:math' as math;
import '../ai/ai_recommendation_service.dart';
import '../api_service.dart';
import 'package:string_similarity/string_similarity.dart';

class SpotifyService {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final String _clientId = AppConstants.spotifyClientId;
  final String _redirectUri = AppConstants.spotifyRedirectUri;

  // Spotify API endpoints from AppConstants
  // static final String _apiBaseUrl = AppConstants.spotifyApiBaseUrl;
  static final String _apiBaseUrl =
      '${AppConstants.baseApiUrl}/spotify';

  // Storage keys (matching spotify_web_api_service.dart)
  static const String _accessTokenKey = 'spotify_web_access_token';
  static const String _refreshTokenKey = 'spotify_web_refresh_token';
  static const String _expiryTimeKey = 'spotify_web_token_expiry';
  static const String _codeVerifierKey = 'spotify_pkce_code_verifier';

  // --- Added for caching/rate-limit improvements ---
  final Map<String, List<MusicTrack>> _searchCache = {};
  static const int _maxCacheSize = 200; // Limit cache size

  void _manageCacheSize() {
    if (_searchCache.length > _maxCacheSize) {
      // Remove oldest entries (first 50 entries)
      final keysToRemove = _searchCache.keys.take(50).toList();
      for (final key in keysToRemove) {
        _searchCache.remove(key);
      }
      print(
          '🧹 [SPOTIFY CACHE] Cleaned cache: removed ${keysToRemove.length} old entries');
    }
  }

  DateTime _lastRequestTime =
      DateTime.now().subtract(const Duration(seconds: 1));
  static const Duration _minRequestInterval =
      Duration(milliseconds: 250); // 4 requests/sec

  // HTTP Client management for SSL issues
  static http.Client? _httpClient;
  static DateTime? _clientCreatedAt;
  static const Duration _clientMaxAge = Duration(minutes: 5);

  // Get or create HTTP client with proper SSL handling
  http.Client _getHttpClient() {
    final now = DateTime.now();

    // Create new client if none exists or if it's too old
    if (_httpClient == null ||
        _clientCreatedAt == null ||
        now.difference(_clientCreatedAt!) > _clientMaxAge) {
      // Close old client if exists
      _httpClient?.close();

      // Create new client with custom SSL handling

      _httpClient = http.Client();
      _clientCreatedAt = now;

      print('🔄 [SpotifyService] Created new HTTP client');
    }

    return _httpClient!;
  }

  // Clean up HTTP client
  static void disposeHttpClient() {
    _httpClient?.close();
    _httpClient = null;
    _clientCreatedAt = null;
    print('🧹 [SpotifyService] Disposed HTTP client');
  }

  // Add a new method for rate limit handling with exponential backoff
  Future<void> _waitForRateLimit({int maxRetries = 3}) async {
    int retryCount = 0;
    while (retryCount < maxRetries) {
      final now = DateTime.now();
      final timeSinceLastRequest = now.difference(_lastRequestTime);

      if (timeSinceLastRequest >= _minRequestInterval) {
        _lastRequestTime = now;
        return;
      }

      // Exponential backoff
      final waitTime = Duration(milliseconds: 250 * (retryCount + 1));
      print(
          '🕒 [SpotifyService] Rate limit: Waiting ${waitTime.inMilliseconds}ms before next request');

      await Future.delayed(waitTime);
      retryCount++;
    }

    throw Exception('Max retry attempts reached for rate limiting');
  }

  // Check if user has connected Spotify
  Future<bool> isConnected() async {
    try {
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      if (accessToken == null) return false;

      // If this is a mock token from dev mode, just assume it's connected
      if (kDebugMode && accessToken.startsWith('dev_token_')) {
        print(
            'Debug mode: Using cached development token for Spotify connection');
        return true;
      }

      // For SDK-based tokens, we don't need to check expiry as the SDK handles it
      return true;
    } catch (e) {
      print('Error checking Spotify connection: $e');
      return false;
    }
  }

  // Store access token from Spotify SDK
  Future<void> storeAccessToken(String accessToken) async {
    try {
      // Set a reasonable expiry time (Spotify tokens typically last 1 hour)
      final expiryTime = DateTime.now().add(const Duration(hours: 1));

      // Store tokens in both locations
      await Future.wait([
        _secureStorage.write(key: _accessTokenKey, value: accessToken),
        _secureStorage.write(
            key: _expiryTimeKey, value: expiryTime.toIso8601String()),
        // Also store in backend format (for backward compatibility)
        _secureStorage.write(
            key: 'spotify_backend_access_token', value: accessToken),
        _secureStorage.write(
            key: 'spotify_backend_token_expiry',
            value: expiryTime.toIso8601String()),
      ]);

      if (kDebugMode) {
        print(
            '✅ [SpotifyService] Stored access token from SDK in both storage locations');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyService] Error storing access token: $e');
      }
    }
  }

  // Get current access token (refreshing if needed)
  Future<String?> getAccessToken() async {
    try {
      // Check both token storage locations (new keys first, then legacy)
      String? accessToken = await _secureStorage.read(key: _accessTokenKey);
      String? backendToken =
          await _secureStorage.read(key: 'spotify_backend_access_token');
      String? legacyToken =
          await _secureStorage.read(key: 'spotify_access_token'); // Legacy key

      // Use tokens in priority order: web API key, backend key, legacy key
      accessToken = accessToken ?? backendToken ?? legacyToken;

      if (accessToken == null) {
        if (kDebugMode) {
          print(
              '❌ [SpotifyService] No access token found in either storage location');
        }
        return null;
      }

      // If this is a dev token, return it as-is
      if (kDebugMode &&
          (accessToken.startsWith('dev_token_') ||
              accessToken.startsWith('spotify_dev_token_'))) {
        return accessToken;
      }

      // For SDK tokens, just return the stored token
      // The SDK will handle token refresh automatically when needed
      return accessToken;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyService] Error getting access token: $e');
      }
      return null;
    }
  }

  // Get backend auth token for backend API requests
  Future<String?> getBackendAuthToken() async {
    try {
      // Try multiple possible keys where the backend auth token might be stored
      final possibleKeys = [
        'auth_token', // Main backend auth token
        'backend_auth_token', // Alternative storage
        'user_auth_token', // User-specific auth token
        'api_auth_token', // API auth token
      ];

      for (final key in possibleKeys) {
        final token = await _secureStorage.read(key: key);
        if (token != null && token.isNotEmpty) {
          return token;
        }
      }

      if (kDebugMode) {
        print(
            '❌ [SpotifyService] No backend auth token found in any storage location');
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyService] Error getting backend auth token: $e');
      }
      return null;
    }
  }

  // Modify _makeAuthenticatedRequest to handle rate limit errors
  Future<Map<String, dynamic>?> _makeAuthenticatedRequest(
    String endpoint, {
    String method = 'GET',
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
    BuildContext? context,
    int maxRetries = 3,
  }) async {
    int retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        // await _waitForRateLimit();

        String url = endpoint;
        if (!url.startsWith('http')) {
          // Ensure trailing slash for Django backend compatibility
          final cleanEndpoint =
              endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
          url = '$_apiBaseUrl/$cleanEndpoint/';
        }

        // Determine if this is a backend request or direct Spotify API request
        final isBackendRequest =
            url.contains(_apiBaseUrl.replaceAll('/api/spotify', '')) ||
                url.contains('ngrok-free.app') ||
                url.contains('localhost:8000') ||
                url.contains('127.0.0.1:8000');

        // For /me endpoints on backend, always use POST with access token in body
        final isUserEndpoint =
            endpoint.contains('/me/') || endpoint.endsWith('/me');
        if (isBackendRequest && isUserEndpoint && method == 'GET') {
          method = 'POST';
          print(
              '🔄 [Spotify API] Converting GET to POST for user endpoint: $endpoint');
        }

        // Add query params to URL for GET requests with proper URL encoding
        if (method == 'GET' && queryParams != null && queryParams.isNotEmpty) {
          final uri = Uri.parse(url);
          final updatedUri = uri.replace(queryParameters: {
            ...uri.queryParameters,
            ...queryParams,
          });
          url = updatedUri.toString();
        }

        String? authToken;
        String? spotifyAccessToken;

        if (isBackendRequest) {
          // For backend requests, get both backend auth token and Spotify access token
          authToken = await getBackendAuthToken();
          spotifyAccessToken = await getAccessToken();

          // For user endpoints, we need the Spotify access token
          if (isUserEndpoint && spotifyAccessToken == null) {
            return null;
          }
        } else {
          // For direct Spotify API requests, use Spotify access token
          authToken = await getAccessToken();
          if (authToken == null) {
            print(
                '❌ [Spotify API] No Spotify access token for Spotify API request to: $url');
            return null;
          }
          spotifyAccessToken = authToken;
          print(
              '🔑 [Spotify API] Using Spotify access token for request to: $url');
        }

        // Build headers based on whether we have an auth token
        final headers = <String, String>{
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        };

        // Add authorization header if we have a token (backend auth token for backend requests)
        if (authToken != null && authToken.isNotEmpty) {
          headers['Authorization'] = 'Bearer $authToken';
        }

        // Prepare request body
        Map<String, dynamic>? requestBody = body;

        // For backend POST requests to user endpoints, add Spotify access token to body
        if (isBackendRequest && isUserEndpoint && method == 'POST') {
          requestBody = {
            ...?body,
            ...?queryParams,
            'access_token': spotifyAccessToken,
          };
          print(
              '🔑 [Spotify API] Added Spotify access token to request body for user endpoint');
        }

        http.Response response;

        // Get the managed HTTP client
        final client = _getHttpClient();

        try {
          switch (method) {
            case 'GET':
              response = await client
                  .get(
                    Uri.parse(url),
                    headers: headers,
                  )
                  .timeout(const Duration(seconds: 15));
              break;
            case 'POST':
              response = await client
                  .post(
                    Uri.parse(url),
                    headers: headers,
                    body: requestBody != null ? jsonEncode(requestBody) : null,
                  )
                  .timeout(const Duration(seconds: 15));
              break;
            case 'PUT':
              response = await client
                  .put(
                    Uri.parse(url),
                    headers: headers,
                    body: requestBody != null ? jsonEncode(requestBody) : null,
                  )
                  .timeout(const Duration(seconds: 15));
              break;
            default:
              throw Exception('Unsupported method: $method');
          }
        } on HandshakeException catch (e) {
          print('🔒 [Spotify API] SSL/TLS Handshake error: $e');
          print('🔄 [Spotify API] Recreating HTTP client and retrying...');

          // Dispose current client and force recreation
          disposeHttpClient();
          final newClient = _getHttpClient();

          // Retry once with new client
          switch (method) {
            case 'GET':
              response = await newClient
                  .get(
                    Uri.parse(url),
                    headers: headers,
                  )
                  .timeout(const Duration(seconds: 15));
              break;
            case 'POST':
              response = await newClient
                  .post(
                    Uri.parse(url),
                    headers: headers,
                    body: requestBody != null ? jsonEncode(requestBody) : null,
                  )
                  .timeout(const Duration(seconds: 15));
              break;
            case 'PUT':
              response = await newClient
                  .put(
                    Uri.parse(url),
                    headers: headers,
                    body: requestBody != null ? jsonEncode(requestBody) : null,
                  )
                  .timeout(const Duration(seconds: 15));
              break;
            default:
              throw Exception('Unsupported method: $method');
          }
          print(
              '✅ [Spotify API] Retry successful after recreating HTTP client');
        }

        // Handle rate limit errors with exponential backoff
        if (response.statusCode == 429) {
          final retryAfterHeader = response.headers['retry-after'];
          int retrySeconds = 5; // Default to 1 minute

          if (retryAfterHeader != null) {
            try {
              retrySeconds = int.parse(retryAfterHeader);
            } catch (e) {
              print(
                  '⚠️ [Spotify API] Invalid retry-after header: $retryAfterHeader');
            }
          }

          print(
              '🚦 [Spotify API] Rate limit hit. Retry after $retrySeconds seconds');

          // Exponential backoff with jitter
          final jitterFactor =
              1.0 + (math.Random().nextDouble() * 0.5); // 1.0 to 1.5
          final waitTime = Duration(
              seconds: (retrySeconds * jitterFactor * math.pow(2, retryCount))
                  .toInt());

          print(
              '⏳ [Spotify API] Waiting ${waitTime.inSeconds} seconds before retry (Attempt ${retryCount + 1})');
          await Future.delayed(waitTime);

          retryCount++;
          continue;
        }

        if (response.statusCode == 200 || response.statusCode == 201) {
          // Properly decode UTF-8 to avoid character encoding issues
          String responseBody;
          try {
            responseBody = utf8.decode(response.bodyBytes);
          } catch (e) {
            print(
                '⚠️ [Spotify API] UTF-8 decode failed, falling back to response.body: $e');
            responseBody = response.body;
          }

          final data = json.decode(responseBody);
          return data;
        } else if (response.statusCode == 401) {
          if (isBackendRequest) {
            print(
                '❌ [Spotify API] Backend authentication failed (401) for: $endpoint');
            print('💡 [Spotify API] Attempting to refresh backend auth token');
            final refreshed = await _refreshBackendAuthToken();
            if (refreshed) {
              // Retry once with new token
              return await _makeAuthenticatedRequest(endpoint,
                  method: method,
                  body: body,
                  queryParams: queryParams,
                  context: context,
                  maxRetries: maxRetries);
            }
            return null;
          } else {
            // Token expired - auto-refresh for all Spotify user endpoints
            final isUserEndpoint = _endpointRequiresUserAuth(endpoint);
            if (isUserEndpoint) {
              print(
                  '🔄 [Spotify API] Attempting automatic token refresh for user endpoint: $endpoint');

              // Try to refresh token using SpotifyTokenManager
              final tokenManager = SpotifyTokenManager();
              final refreshed = await tokenManager.refreshToken();

              if (refreshed) {
                final newAccessToken = await getAccessToken();
                if (newAccessToken != null) {
                  print(
                      '✅ [Spotify API] Token refreshed successfully, retrying request');

                  // Retry the request with new token
                  final retryHeaders = {
                    'Authorization': 'Bearer $newAccessToken',
                    'Content-Type': 'application/json',
                  };

                  http.Response retryResponse;

                  try {
                    switch (method) {
                      case 'GET':
                        retryResponse = await client
                            .get(
                              Uri.parse(url),
                              headers: retryHeaders,
                            )
                            .timeout(const Duration(seconds: 15));
                        break;
                      case 'POST':
                        retryResponse = await client
                            .post(
                              Uri.parse(url),
                              headers: retryHeaders,
                              body: requestBody != null
                                  ? jsonEncode(requestBody)
                                  : null,
                            )
                            .timeout(const Duration(seconds: 15));
                        break;
                      case 'PUT':
                        retryResponse = await client
                            .put(
                              Uri.parse(url),
                              headers: retryHeaders,
                              body: requestBody != null
                                  ? jsonEncode(requestBody)
                                  : null,
                            )
                            .timeout(const Duration(seconds: 15));
                        break;
                      default:
                        throw Exception(
                            'Unsupported method for retry: $method');
                    }

                    if (retryResponse.statusCode == 200 ||
                        retryResponse.statusCode == 201) {
                      // Properly decode UTF-8 to avoid character encoding issues
                      String retryResponseBody;
                      try {
                        retryResponseBody =
                            utf8.decode(retryResponse.bodyBytes);
                      } catch (e) {
                        print(
                            '⚠️ [Spotify API] UTF-8 decode failed for retry, falling back to response.body: $e');
                        retryResponseBody = retryResponse.body;
                      }

                      final data = json.decode(retryResponseBody);
                      print(
                          '✅ [Spotify API] Retry after token refresh successful');
                      return data;
                    } else {
                      print(
                          '❌ [Spotify API] Retry after token refresh failed: ${retryResponse.statusCode}');
                    }
                  } catch (e) {
                    print(
                        '❌ [Spotify API] Exception during retry after token refresh: $e');
                  }
                }
              } else {
                print(
                    '❌ [Spotify API] Token refresh failed for user endpoint: $endpoint');

                // If refresh token is missing and context is available, trigger full re-auth
                if (context != null) {
                  print(
                      '🔄 [Spotify API] Triggering full Spotify re-authentication via AuthService');
                  final reauthed =
                      await UiAuthService.AuthService.handleSpotifySignIn(
                          context);
                  if (reauthed) {
                    final newAccessToken = await getAccessToken();
                    if (newAccessToken != null) {
                      print(
                          '✅ [Spotify API] Full re-auth successful, retrying request');

                      // Retry the request with new token after full re-auth
                      final retryHeaders = {
                        'Authorization': 'Bearer $newAccessToken',
                        'Content-Type': 'application/json',
                      };

                      try {
                        http.Response retryResponse;

                        switch (method) {
                          case 'GET':
                            retryResponse = await client
                                .get(
                                  Uri.parse(url),
                                  headers: retryHeaders,
                                )
                                .timeout(const Duration(seconds: 15));
                            break;
                          case 'POST':
                            retryResponse = await client
                                .post(
                                  Uri.parse(url),
                                  headers: retryHeaders,
                                  body: requestBody != null
                                      ? jsonEncode(requestBody)
                                      : null,
                                )
                                .timeout(const Duration(seconds: 15));
                            break;
                          case 'PUT':
                            retryResponse = await client
                                .put(
                                  Uri.parse(url),
                                  headers: retryHeaders,
                                  body: requestBody != null
                                      ? jsonEncode(requestBody)
                                      : null,
                                )
                                .timeout(const Duration(seconds: 15));
                            break;
                          default:
                            throw Exception(
                                'Unsupported method for retry: $method');
                        }

                        if (retryResponse.statusCode == 200 ||
                            retryResponse.statusCode == 201) {
                          // Properly decode UTF-8 to avoid character encoding issues
                          String retryResponseBody;
                          try {
                            retryResponseBody =
                                utf8.decode(retryResponse.bodyBytes);
                          } catch (e) {
                            print(
                                '⚠️ [Spotify API] UTF-8 decode failed for retry, falling back to response.body: $e');
                            retryResponseBody = retryResponse.body;
                          }

                          final data = json.decode(retryResponseBody);
                          print(
                              '✅ [Spotify API] Retry after full re-auth successful');
                          return data;
                        } else {
                          print(
                              '❌ [Spotify API] Retry after full re-auth failed: ${retryResponse.statusCode}');
                        }
                      } catch (e) {
                        print(
                            '❌ [Spotify API] Exception during retry after full re-auth: $e');
                      }
                    }
                  }
                }
              }
            }

            // For all cases, log the failure
            print(
                '❌ [Spotify API] Spotify token expired for request to: $endpoint');
            print(
                '💡 [Spotify API] User may need to re-authenticate through the app');
            return null;
          }
        } else {
          print(
              '❌ [Spotify API] Error response (${response.statusCode}): ${response.body}');
          print('🔗 [Spotify API] Failed request details:');
          print('   - URL: $url');
          print('   - Method: $method');
          print('   - Headers: $headers');
          if (requestBody != null) {
            print('   - Body: ${jsonEncode(requestBody)}');
          }
          print('   - Response Headers: ${response.headers}');

          if (isBackendRequest) {
            print(
                '💡 [Spotify API] Backend request failed - check backend server status and auth');

            // For ngrok issues, suggest direct testing
            if (url.contains('ngrok')) {
              print('🔧 [NGROK DEBUG] Try testing this URL directly:');
              print(
                  '   curl -X $method "$url" -H "Content-Type: application/json"');
            }
          }
          return null;
        }
      } catch (e) {
        print(
            '❌ [Spotify API] Exception in _makeAuthenticatedRequest to $endpoint: $e');

        // Exponential backoff for other transient errors
        if (retryCount < maxRetries) {
          final waitTime = Duration(milliseconds: 250 * (retryCount + 1));
          print(
              '🔄 [Spotify API] Retrying request after ${waitTime.inMilliseconds}ms (Attempt ${retryCount + 1})');
          await Future.delayed(waitTime);
          retryCount++;
        } else {
          return null;
        }
      }
    }

    return null;
  }

  // === NEW: Attempt to refresh backend auth token ===
  Future<bool> _refreshBackendAuthToken() async {
    try {
      final apiService = ApiService();
      final authService = CoreAuthService.AuthService(apiService);
      final refreshResponse = await authService.refreshToken();
      if (refreshResponse.success) {
        print('🔑 [SpotifyService] Backend auth token refreshed successfully');
        return true;
      } else {
        print('❌ [SpotifyService] Failed to refresh backend auth token: '
            '${refreshResponse.message}');
        return false;
      }
    } catch (e) {
      print('❌ [SpotifyService] Exception refreshing backend auth token: $e');
      return false;
    }
  }

  // === NEW: Force clear Spotify tokens to trigger re-authentication ===
  Future<void> _clearExpiredTokens() async {
    try {
      print(
          '🧹 [SpotifyService] Clearing expired Spotify tokens to force re-authentication');
      final tokenManager = SpotifyTokenManager();
      await tokenManager.clearTokens();

      // Also clear tokens from all possible storage locations
      await Future.wait([
        _secureStorage.delete(key: _accessTokenKey),
        _secureStorage.delete(key: _refreshTokenKey),
        _secureStorage.delete(key: _expiryTimeKey),
        _secureStorage.delete(key: 'spotify_backend_access_token'),
        _secureStorage.delete(key: 'spotify_backend_token_expiry'),
        _secureStorage.delete(key: 'spotify_access_token'),
        _secureStorage.delete(key: 'spotify_refresh_token'),
        _secureStorage.delete(key: 'spotify_token_expiry'),
      ]);

      print('✅ [SpotifyService] All Spotify tokens cleared successfully');
    } catch (e) {
      print('❌ [SpotifyService] Error clearing expired tokens: $e');
    }
  }

  // Helper to determine whether an endpoint needs a user access token
  bool _endpointRequiresUserAuth(String endpoint) {
    // Normalise to path only
    final path =
        endpoint.startsWith('http') ? Uri.parse(endpoint).path : endpoint;

    // User-specific endpoints that require authentication
    final userEndpoints = [
      '/me/tracks', // Liked songs
      '/me/top/tracks', // Top tracks
      '/me/top/artists', // Top artists
      '/me/player/', // Player endpoints (recently-played, etc)
      '/me/playlists', // User playlists
      '/me/albums', // Saved albums
      '/me/shows', // Saved shows
      '/me/episodes', // Saved episodes
      '/me/following', // Following
      '/me/', // Any /me/ endpoint
      '/users/', // User profiles
    ];

    // Search and other public endpoints typically don't require user auth
    final publicEndpoints = [
      '/search',
      '/tracks/',
      '/artists/',
      '/albums/',
      '/shows/'
    ];

    // Check if it's a public endpoint first
    if (publicEndpoints
        .any((publicEndpoint) => path.contains(publicEndpoint))) {
      return false;
    }

    // Check if it's a user endpoint
    return userEndpoints.any((userEndpoint) =>
        path.startsWith(userEndpoint) || path.contains(userEndpoint));
  }

  // Get saved tracks (liked songs)
  Future<Map<String, dynamic>> getSavedTracks(
      {int limit = 50, int offset = 0}) async {
    try {
      print(
          '🎵 [SpotifyService] Getting saved tracks (limit: $limit, offset: $offset)');

      // Check if connected
      if (!await isConnected()) {
        throw Exception('Not connected to Spotify');
      }

      // For development, create mock saved tracks
      if (kDebugMode &&
          (await getAccessToken())?.startsWith('dev_token_') == true) {
        print('🔬 [SpotifyService] Using mock saved tracks for development');

        // Return mock saved tracks data for development
        final mockTracks = List.generate(
            math.min(limit, 20),
            (index) => MusicTrack(
                  id: 'dev_saved_track_$index',
                  title: 'Saved Track ${index + 1}',
                  artist: 'Saved Artist ${(index % 3) + 1}',
                  album: 'Saved Album',
                  albumArt: 'https://via.placeholder.com/300x300',
                  url: 'https://spotify.com/track/dev_saved_track_$index',
                  service: 'spotify',
                  serviceType: 'spotify',
                  uri: 'spotify:track:dev_saved_track_$index',
                  durationMs: 180000 + (index * 5000),
                  genres: [
                    ['pop', 'indie', 'electronic'][index % 3]
                  ],
                  explicit: false,
                  popularity: 50 + (index * 2),
                ));

        return {
          'tracks': mockTracks,
          'total': 50,
          'hasMore': offset + mockTracks.length < 50
        };
      }

      // Use the actual Spotify API for production
      // Build query parameters without market for backend API to avoid user endpoint errors
      final queryParams = {
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      // Only add market parameter if we're NOT using the backend API (direct Spotify API)
      final isBackendRequest =
          _apiBaseUrl.contains('ngrok') || _apiBaseUrl.contains('localhost');

      if (!isBackendRequest) {
        queryParams['market'] = 'from_token';
        print(
            '🌍 [SpotifyService] Adding market parameter for direct Spotify API');
      } else {
        print(
            '🚫 [SpotifyService] Skipping market parameter for backend API to avoid user endpoint errors');
      }

      final data = await _makeAuthenticatedRequest(
        '/me/tracks',
        queryParams: queryParams,
      );

      if (data == null) {
        print(
            '⚠️ [SpotifyService] No data returned from Spotify API for saved tracks');
        return {'tracks': <MusicTrack>[], 'total': 0, 'hasMore': false};
      }

      final List items = data['items'] ?? [];
      final total = data['total'] as int? ?? 0;
      final hasMore = (offset + items.length) < total;

      print(
          '✓ [SpotifyService] Got ${items.length} saved tracks (offset: $offset, total available: $total)');

      final tracks = items
          .map((item) {
            try {
              return _mapTrackResponse(item['track']);
            } catch (e) {
              print('⚠️ [SpotifyService] Error mapping track: $e');
              return null;
            }
          })
          .whereType<MusicTrack>()
          .toList(); // Filter out any null tracks from mapping errors

      return {'tracks': tracks, 'total': total, 'hasMore': hasMore};
    } catch (e) {
      print('❌ [SpotifyService] Error getting saved tracks: $e');
      rethrow; // Rethrow to let the provider handle the error
    }
  }

  // Get recently played tracks
  Future<List<MusicTrack>> getRecentlyPlayed({int limit = 20}) async {
    try {
      print(
          '🎵 [SpotifyService] Getting recently played tracks (limit: $limit)');

      // Check if connected
      if (!await isConnected()) {
        throw Exception('Not connected to Spotify');
      }

      // For development, create mock recently played tracks
      if (kDebugMode &&
          (await getAccessToken())?.startsWith('dev_token_') == true) {
        print('🔬 [SpotifyService] Using mock recently played for development');

        // Return mock recently played tracks data for development
        final mockTracks = List.generate(
            math.min(limit, 10),
            (index) => MusicTrack(
                  id: 'dev_recent_track_$index',
                  title: 'Recent Track ${index + 1}',
                  artist: 'Recent Artist ${(index % 4) + 1}',
                  album: 'Recent Album',
                  albumArt: 'https://via.placeholder.com/300x300',
                  url: 'https://spotify.com/track/dev_recent_track_$index',
                  service: 'spotify',
                  serviceType: 'spotify',
                  uri: 'spotify:track:dev_recent_track_$index',
                  durationMs: 200000 + (index * 3000),
                  genres: [
                    ['hip hop', 'rock', 'jazz', 'classical'][index % 4]
                  ],
                  explicit: index % 3 == 0,
                  popularity: 60 + (index * 3),
                  releaseDate:
                      DateTime.now().subtract(Duration(days: index + 1)),
                ));

        return mockTracks;
      }

      // Use the actual Spotify API for production
      final data = await _makeAuthenticatedRequest(
        '/me/player/recently-played',
        queryParams: {
          'limit': limit.toString(),
        },
      );

      if (data == null) {
        return [];
      }

      final List items = data['items'] ?? [];
      print('✓ [SpotifyService] Got ${items.length} recently played tracks');

      return items.map((item) {
        final track = item['track'];
        DateTime? playedAt;
        if (item['played_at'] != null) {
          try {
            playedAt = DateTime.parse(item['played_at']);
          } catch (e) {
            playedAt = null;
          }
        }
        final mapped = _mapTrackResponse(track);
        // If you want to store playedAt as releaseDate for recently played, do so:
        return mapped.copyWith(releaseDate: playedAt);
      }).toList();
    } catch (e) {
      print('❌ [SpotifyService] Error getting recently played tracks: $e');
      return [];
    }
  }

  // Search for tracks
  Future<List<MusicTrack>> searchTracks(String query,
      {int limit = 20, int offset = 0, String? context, String? market}) async {
    // Enhanced logging for AI tracking
    final timestamp = DateTime.now().toIso8601String();
    final contextInfo = context != null ? ' [CONTEXT: $context]' : '';

    // Auto-detect market for international genre searches if not already specified
    if (market == null) {
      final queryLower = query.toLowerCase();
      // Check if query contains genre-related terms that need specific markets
      if (queryLower.contains('chinese') ||
          queryLower.contains('mandarin') ||
          queryLower.contains('cantopop') ||
          queryLower.contains('mandopop') ||
          queryLower.contains('c-pop') ||
          queryLower.contains('china music')) {
        market = 'HK';
        print(
            '🌍 [SPOTIFY AI SEARCH] Auto-detected Chinese content, using market: HK');
      } else if (queryLower.contains('korean') ||
          queryLower.contains('kpop') ||
          queryLower.contains('k-pop') ||
          queryLower.contains('korea music')) {
        market = 'KR';
        print(
            '🌍 [SPOTIFY AI SEARCH] Auto-detected Korean content, using market: KR');
      } else if (queryLower.contains('japanese') ||
          queryLower.contains('jpop') ||
          queryLower.contains('j-pop') ||
          queryLower.contains('japan music')) {
        market = 'JP';
        print(
            '🌍 [SPOTIFY AI SEARCH] Auto-detected Japanese content, using market: JP');
      }
    }

    final marketInfo = market != null ? ' [MARKET: $market]' : '';

    try {
      if (query.isEmpty) {
        return [];
      }

      print(
          '🔍 [SPOTIFY AI SEARCH] [$timestamp] QUERY: "$query" | LIMIT: $limit | OFFSET: $offset$contextInfo$marketInfo');

      // Proceed even if the user is not connected – we'll automatically fall back
      // to the backend client-credentials flow for public catalogue searches.

      // Build cache key with page-specific variations to avoid repeated empty results
      final cacheKey = '$query|$limit|$offset|${market ?? ''}';
      if (_searchCache.containsKey(cacheKey)) {
        final cachedResult = _searchCache[cacheKey]!;
        // If cached result is empty and we have a different offset, don't use cache
        if (cachedResult.isNotEmpty || offset == 0) {
          print('📦 [SPOTIFY AI SEARCH] Cache hit for: "$query"');
          return cachedResult;
        } else {
          // Remove empty cached results for non-zero offsets to allow retries
          print(
              '🧹 [SPOTIFY AI SEARCH] Removing empty cache for: "$query" (offset: $offset)');
          _searchCache.remove(cacheKey);
        }
      }

      // If using development tokens, use the backend API endpoint
      if (kDebugMode &&
          (await getAccessToken())?.startsWith('dev_token_') == true) {
        print(
            '🔬 [SpotifyService] Using backend API for search in development');

        // Use the same Spotify cache API endpoint for consistency
        final sanitizedQuery = _sanitizeSearchQuery(query);

        // Use POST method for backend search API
        final spotifyAccessToken = await getAccessToken();
        final requestBody = {
          'q': sanitizedQuery,
          'type': 'track',
          'limit': limit.toString(),
          if (offset > 0) 'offset': offset.toString(),
          if (market != null) 'market': market,
          if (spotifyAccessToken != null) 'access_token': spotifyAccessToken,
        };

        print(
            '🔗 [SpotifyService] Development search using POST to: $_apiBaseUrl/search/');

        final client = _getHttpClient();

        try {
          final response = await client
              .post(
                Uri.parse('$_apiBaseUrl/search/'),
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                },
                body: jsonEncode(requestBody),
              )
              .timeout(const Duration(seconds: 15));

          if (response.statusCode == 200) {
            // Properly decode UTF-8 to avoid character encoding issues
            String responseBody;
            try {
              responseBody = utf8.decode(response.bodyBytes);
            } catch (e) {
              print(
                  '⚠️ [SpotifyService] UTF-8 decode failed, falling back to response.body: $e');
              responseBody = response.body;
            }

            final data = json.decode(responseBody);

            // Handle Spotify cache API response format (same as production)
            final tracks = data['tracks'];
            if (tracks == null) {
              print(
                  '⚠️ [SpotifyService] No tracks key in development response');
              return [];
            }

            final List items = tracks['items'] ?? [];
            final results =
                items.map((track) => _mapTrackResponse(track)).toList();
            _searchCache[cacheKey] = results;
            print(
                '✅ [SPOTIFY AI SEARCH] SUCCESS (Backend): ${results.length} tracks returned for "$query"$contextInfo');

            // Log to AI analytics system
            AIRecommendationService.logSearchActivity(
              query: query,
              searchType: 'tracks',
              context: context ?? 'UNKNOWN',
              resultsCount: results.length,
              limit: limit,
              offset: offset,
              isSuccess: true,
            );

            return results;
          } else {
            print(
                '⚠️ [SpotifyService] Failed to search tracks from backend API: ${response.statusCode}');
            print('⚠️ [SpotifyService] Response body: ${response.body}');
            print('⚠️ [SpotifyService] Request URL: ${response.request?.url}');

            // For development, return mock results if backend fails
            if (response.statusCode == 401 || response.statusCode == 500) {
              print(
                  '🔬 [SpotifyService] Backend failed, using mock search results for development');
              final mockResults = List.generate(
                  math.min(limit, 5),
                  (index) => MusicTrack(
                        id: 'dev_search_track_${query.hashCode}_$index',
                        title: 'Search Result ${index + 1} for "$query"',
                        artist: 'Mock Artist ${(index % 3) + 1}',
                        album: 'Mock Album',
                        albumArt: 'https://via.placeholder.com/300x300',
                        url:
                            'https://spotify.com/track/dev_search_track_${query.hashCode}_$index',
                        service: 'spotify',
                        serviceType: 'spotify',
                        uri:
                            'spotify:track:dev_search_track_${query.hashCode}_$index',
                        durationMs: 180000 + (index * 5000),
                        genres: [
                          ['pop', 'rock', 'indie'][index % 3]
                        ],
                        explicit: false,
                        popularity: 50 + (index * 5),
                      ));

              _searchCache[cacheKey] = mockResults;
              return mockResults;
            }

            throw Exception(
                'Failed to search tracks from backend API: ${response.statusCode}');
          }
        } on HandshakeException catch (e) {
          print('🔒 [SpotifyService] SSL/TLS Handshake error in search: $e');
          print(
              '🔄 [SpotifyService] Recreating HTTP client and retrying search...');

          // Dispose current client and force recreation
          disposeHttpClient();
          final newClient = _getHttpClient();

          // Retry once with new client
          final retryResponse = await newClient
              .post(
                Uri.parse('$_apiBaseUrl/search/'),
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                },
                body: jsonEncode(requestBody),
              )
              .timeout(const Duration(seconds: 15));

          if (retryResponse.statusCode == 200) {
            // Properly decode UTF-8 to avoid character encoding issues
            String retryResponseBody;
            try {
              retryResponseBody = utf8.decode(retryResponse.bodyBytes);
            } catch (e) {
              print(
                  '⚠️ [SpotifyService] UTF-8 decode failed for retry, falling back to response.body: $e');
              retryResponseBody = retryResponse.body;
            }

            final data = json.decode(retryResponseBody);
            final tracks = data['tracks'];
            if (tracks != null) {
              final List items = tracks['items'] ?? [];
              final results =
                  items.map((track) => _mapTrackResponse(track)).toList();
              _searchCache[cacheKey] = results;
              print(
                  '✅ [SpotifyService] Search retry successful after recreating HTTP client');
              return results;
            }
          }

          print(
              '❌ [SpotifyService] Search retry failed: ${retryResponse.statusCode}');
          return [];
        }
      }

      // Use the actual Spotify API for production
      final sanitizedQuery = _sanitizeSearchQuery(query);
      final data = await _makeAuthenticatedRequest(
        '/search',
        queryParams: {
          'q': sanitizedQuery,
          'type': 'track',
          'limit': limit.toString(),
          if (offset > 0) 'offset': offset.toString(),
          if (market != null) 'market': market,
        },
      );

      if (data == null) {
        return [];
      }

      final tracks = data['tracks'];
      if (tracks == null) {
        return [];
      }

      final List items = tracks['items'] ?? [];
      final results = items
          .map((track) => _mapTrackResponse(track))
          // Filter out generic misleading tracks (e.g., songs literally titled "Popular")
          .where((track) => _shouldIncludeSearchResult(track, query))
          .toList();
      _searchCache[cacheKey] = results;
      _manageCacheSize(); // Clean cache if needed

      print(
          '✅ [SPOTIFY AI SEARCH] SUCCESS: ${results.length} tracks returned for "$query"$contextInfo');

      // Log to AI analytics system
      AIRecommendationService.logSearchActivity(
        query: query,
        searchType: 'tracks',
        context: context ?? 'UNKNOWN',
        resultsCount: results.length,
        limit: limit,
        offset: offset,
        isSuccess: true,
      );

      return results;
    } catch (e) {
      print(
          '❌ [SPOTIFY AI SEARCH] ERROR: Failed to search "$query"$contextInfo - $e');

      // Log error to AI analytics system
      AIRecommendationService.logSearchActivity(
        query: query,
        searchType: 'tracks',
        context: context ?? 'UNKNOWN',
        limit: limit,
        offset: offset,
        isSuccess: false,
        errorMessage: e.toString(),
      );

      return [];
    }
  }

  // ---------- NEW: helper to filter misleading generic results ----------
  /// Determine whether a search result should be kept. Filters out tracks
  /// whose title is a generic word (e.g. "Popular", "Hits", "Trending") that
  /// often appears due to queries containing those words but are unrelated to
  /// the intended artist/genre context.
  bool _shouldIncludeSearchResult(MusicTrack track, String originalQuery) {
    final genericTitles = {
      'popular',
      'hits',
      'top',
      'trending',
      'best',
      'greatest',
    };

    final titleLower = track.title.toLowerCase().trim();
    // If the entire title is a generic word (or that word followed by "song")
    if (genericTitles.contains(titleLower) ||
        genericTitles.contains(titleLower.replaceAll(' song', ''))) {
      // Extra safeguard: if the original query explicitly quoted the title we
      // should not filter, but for our autogenerated queries we never quote
      // these generic words, so it is safe to exclude.
      return false;
    }

    // Avoid false-positives: keep track if the artist name actually appears in
    // the original query (common when user manually searches)
    final artistInQuery =
        originalQuery.toLowerCase().contains(track.artist.toLowerCase());
    if (!artistInQuery &&
        genericTitles.any((w) => originalQuery.toLowerCase().contains(w))) {
      // The query contains a generic word but not the artist; be stricter.
      return !genericTitles.contains(titleLower);
    }

    return true;
  }

  // Get track details
  Future<MusicTrack?> getTrackDetails(String trackId) async {
    try {
      print('🎵 [SpotifyService] Getting track details for: $trackId');

      // Check if connected
      if (!await isConnected()) {
        throw Exception('Not connected to Spotify');
      }

      // If using development tokens, use the backend API endpoint
      if (kDebugMode &&
          (await getAccessToken())?.startsWith('dev_token_') == true) {
        print(
            '🔬 [SpotifyService] Using backend API for track details in development');

        // Use the backend API endpoint instead of direct Spotify API
        final response = await http.get(
          Uri.parse(
              '${AppConstants.baseApiUrl}/music/api/tracks/track/spotify/$trackId/'),
          headers: {
            'Content-Type': 'application/json',
          },
        );

        if (response.statusCode == 200) {
          // Properly decode UTF-8 to avoid character encoding issues
          String responseBody;
          try {
            responseBody = utf8.decode(response.bodyBytes);
          } catch (e) {
            print(
                '⚠️ [SpotifyService] UTF-8 decode failed, falling back to response.body: $e');
            responseBody = response.body;
          }

          final data = json.decode(responseBody);
          return MusicTrack.fromJson(data);
        } else {
          print(
              '⚠️ [SpotifyService] Failed to get track details from backend API: ${response.statusCode}');
          throw Exception('Failed to get track details from backend API');
        }
      }

      // Use the actual Spotify API for production
      final data = await _makeAuthenticatedRequest('/tracks/$trackId');

      if (data == null) {
        return null;
      }

      return _mapTrackResponse(data);
    } catch (e) {
      print('❌ [SpotifyService] Error getting track details: $e');
      return null;
    }
  }

  // Get top tracks
  Future<List<MusicTrack>> getTopTracks(
      {int limit = 20, BuildContext? context}) async {
    try {
      print('🏆 [SpotifyService] Getting top tracks (limit: $limit)');

      // Check if connected
      if (!await isConnected()) {
        throw Exception('Not connected to Spotify');
      }

      // If using development tokens, use the backend API endpoint
      if (kDebugMode &&
          (await getAccessToken())?.startsWith('dev_token_') == true) {
        print(
            '🔬 [SpotifyService] Using backend API for top tracks in development');

        // Use the backend API endpoint
        // Note: Your backend API may need a specific endpoint for top tracks
        // Using a default endpoint here, but you should update it based on your API
        final response = await http.get(
          Uri.parse(
              '${AppConstants.baseApiUrl}/music/api/spotify/top-tracks/?limit=$limit'),
          headers: {
            'Content-Type': 'application/json',
          },
        );

        if (response.statusCode == 200) {
          // Properly decode UTF-8 to avoid character encoding issues
          String responseBody;
          try {
            responseBody = utf8.decode(response.bodyBytes);
          } catch (e) {
            print(
                '⚠️ [SpotifyService] UTF-8 decode failed, falling back to response.body: $e');
            responseBody = response.body;
          }

          final data = json.decode(responseBody);
          // Backend API returns data in different format
          // Adjust this parsing based on your actual API response structure
          final List tracksData = data['items'] ?? [];
          return tracksData.map((track) => MusicTrack.fromJson(track)).toList();
        } else {
          print(
              '⚠️ [SpotifyService] Failed to get top tracks from backend API: ${response.statusCode}');
          throw Exception('Failed to get top tracks from backend API');
        }
      }

      // Use the actual Spotify API for production
      final data = await _makeAuthenticatedRequest(
        '/me/top/tracks',
        queryParams: {
          'limit': limit.toString(),
          'time_range':
              'medium_term', // Options: short_term, medium_term, long_term
        },
        context: context,
      );

      if (data == null) {
        return [];
      }

      final List items = data['items'] ?? [];
      print('✓ [SpotifyService] Got ${items.length} top tracks');

      return items.map((track) => _mapTrackResponse(track)).toList();
    } catch (e) {
      print('❌ [SpotifyService] Error getting top tracks: $e');
      return [];
    }
  }

  // Get user's playlists
  Future<List<Map<String, dynamic>>> getUserPlaylists(
      {int limit = 20, int offset = 0}) async {
    try {
      print(
          '📝 [SpotifyService] Getting user playlists (limit: $limit, offset: $offset)');

      // Check if connected
      if (!await isConnected()) {
        throw Exception('Not connected to Spotify');
      }

      // If using development tokens, use the backend API endpoint
      if (kDebugMode &&
          (await getAccessToken())?.startsWith('dev_token_') == true) {
        print(
            '🔬 [SpotifyService] Using backend API for playlists in development');

        // Use the backend API endpoint
        final response = await http.get(
          Uri.parse(
              '${AppConstants.baseApiUrl}/music/api/spotify/playlists/?limit=$limit&offset=$offset'),
          headers: {
            'Content-Type': 'application/json',
          },
        );

        if (response.statusCode == 200) {
          // Properly decode UTF-8 to avoid character encoding issues
          String responseBody;
          try {
            responseBody = utf8.decode(response.bodyBytes);
          } catch (e) {
            print(
                '⚠️ [SpotifyService] UTF-8 decode failed, falling back to response.body: $e');
            responseBody = response.body;
          }

          final data = json.decode(responseBody);
          return List<Map<String, dynamic>>.from(data['items'] ?? []);
        } else {
          print(
              '⚠️ [SpotifyService] Failed to get playlists from backend API: ${response.statusCode}');
          throw Exception('Failed to get playlists from backend API');
        }
      }

      // Use the actual Spotify API for production
      final response = await _makeAuthenticatedRequest(
        '/me/playlists',
        queryParams: {
          'limit': limit.toString(),
          'offset': offset.toString(),
        },
      );

      if (response == null || !response.containsKey('items')) return [];

      return List<Map<String, dynamic>>.from(response['items']);
    } catch (e) {
      print('❌ [SpotifyService] Error getting user playlists: $e');
      return [];
    }
  }

  // Get user's top artists
  Future<List<Map<String, dynamic>>> getTopArtists(
      {int limit = 20, String timeRange = 'medium_term'}) async {
    try {
      print(
          '👨‍🎤 [SpotifyService] Getting top artists (limit: $limit, time_range: $timeRange)');

      // Check if connected
      if (!await isConnected()) {
        throw Exception('Not connected to Spotify');
      }

      // If using development tokens, use the backend API endpoint
      if (kDebugMode &&
          (await getAccessToken())?.startsWith('dev_token_') == true) {
        print(
            '🔬 [SpotifyService] Using backend API for top artists in development');

        // Use the backend API endpoint
        final response = await http.get(
          Uri.parse(
              '${AppConstants.baseApiUrl}/music/api/spotify/top-artists/?limit=$limit&time_range=$timeRange'),
          headers: {
            'Content-Type': 'application/json',
          },
        );

        if (response.statusCode == 200) {
          // Properly decode UTF-8 to avoid character encoding issues
          String responseBody;
          try {
            responseBody = utf8.decode(response.bodyBytes);
          } catch (e) {
            print(
                '⚠️ [SpotifyService] UTF-8 decode failed, falling back to response.body: $e');
            responseBody = response.body;
          }

          final data = json.decode(responseBody);
          return List<Map<String, dynamic>>.from(data['items'] ?? []);
        } else {
          print(
              '⚠️ [SpotityService] Failed to get top artists from backend API: ${response.statusCode}');
          throw Exception('Failed to get top artists from backend API');
        }
      }

      // Use the actual Spotify API for production
      final response = await _makeAuthenticatedRequest(
        '/me/top/artists',
        queryParams: {
          'limit': limit.toString(),
          'time_range': timeRange, // short_term, medium_term, or long_term
        },
      );

      if (response == null || !response.containsKey('items')) return [];

      final items = response['items'] as List;
      return items
          .map((artist) => {
                'id': artist['id'],
                'name': artist['name'],
                'image_url': artist['images']?.isNotEmpty == true
                    ? artist['images'][0]['url']
                    : null,
                'genres': List<String>.from(artist['genres'] ?? []),
                'popularity': artist['popularity'],
                'url': artist['external_urls']['spotify'],
              })
          .toList();
    } catch (e) {
      print('❌ [SpotifyService] Error getting top artists: $e');
      return [];
    }
  }

  // Get multiple artists' information by their names (search for them first to get IDs)
  Future<List<Map<String, dynamic>>> getMultipleArtistsByNames(
      List<String> artistNames) async {
    try {
      print(
          '⚡️ [SpotifyService] Optimizing artist search for ${artistNames.length} artists with parallel requests...');

      // Proceed even if the user is not connected – backend client-credentials fallback will supply public data.

      // First, separate collaborations into individual artists
      final allIndividualArtists = <String>{};
      for (final artistName in artistNames) {
        final individualArtists = _separateCollaborations(artistName);
        allIndividualArtists.addAll(individualArtists);
      }

      print(
          '🎵 Separated ${artistNames.length} artist entries into ${allIndividualArtists.length} individual artists');

      // Create a list of futures to execute in parallel
      final searchFutures = allIndividualArtists.map((artistName) {
        return _makeAuthenticatedRequest(
          '/search',
          queryParams: {
            'q': 'artist:"$artistName"', // Use quoted exact artist search
            'type': 'artist',
            'limit': '10',
          },
        )
            .then((response) => {'name': artistName, 'response': response})
            .catchError((e) {
          print('⚠️ Failed to search for artist "$artistName" in parallel: $e');
          return {'name': artistName, 'response': null};
        });
      }).toList();

      // Await all search requests to complete
      final searchResults = await Future.wait(searchFutures);

      final artistIds = <String>{}; // Use a Set to handle duplicate IDs

      // Process the results
      for (final result in searchResults) {
        final artistName = result['name'] as String;
        final searchResponse = result['response'] as Map<String, dynamic>?;

        if (searchResponse != null &&
            searchResponse['artists'] != null &&
            searchResponse['artists']['items'] != null) {
          final artists = searchResponse['artists']['items'] as List;
          Map<String, dynamic>? matchedArtist;

          // 1. Try exact match (case insensitive)
          matchedArtist = artists.firstWhere(
            (artist) =>
                artist['name'].toString().toLowerCase() ==
                artistName.toLowerCase(),
            orElse: () => null,
          );

          // 2. If no exact match, try close similarity
          if (matchedArtist == null) {
            matchedArtist = artists.firstWhere(
              (artist) =>
                  _isCloseArtistMatch(artist['name'].toString(), artistName),
              orElse: () => null,
            );
          }

          if (matchedArtist != null) {
            final artistId = matchedArtist['id'] as String?;
            if (artistId != null) {
              artistIds.add(artistId);
              print('✓ Found artist: ${matchedArtist['name']} -> $artistId');
            }
          } else {
            print('❌ No match found for artist: $artistName');
          }
        }
      }

      if (artistIds.isEmpty) {
        print('❌ No artist IDs found after parallel search.');
        return [];
      }

      // Now get detailed info for all artists using the Get Several Artists endpoint in batches of 50
      final artistInfo = <Map<String, dynamic>>[];
      final allArtistIds = artistIds.toList();

      for (int i = 0; i < allArtistIds.length; i += 50) {
        final batch = allArtistIds.skip(i).take(50).toList();
        final artistIdsParam = batch.join(',');
        print(
            '🎤 Getting detailed info for batch of ${batch.length} artists...');

        final response = await _makeAuthenticatedRequest(
          '/artists',
          queryParams: {'ids': artistIdsParam},
        );

        if (response != null && response.containsKey('artists')) {
          final artists = response['artists'] as List;
          artistInfo.addAll(artists.whereType<Map<String, dynamic>>());
        }
      }

      return artistInfo
          .map<Map<String, dynamic>>((artist) => {
                'id': artist['id'],
                'name': artist['name'],
                'image_url': artist['images']?.isNotEmpty == true
                    ? artist['images'][0]['url']
                    : null,
                'genres': List<String>.from(artist['genres'] ?? []),
                'popularity': artist['popularity'],
                'url': artist['external_urls']['spotify'],
                'followers': artist['followers']['total'],
              })
          .toList();
    } catch (e) {
      print('❌ [SpotifyService] Error getting multiple artists: $e');
      return [];
    }
  }

  /// Separate collaborations into individual artists
  List<String> _separateCollaborations(String artistName) {
    if (artistName.trim().isEmpty) return [];

    // List of collaboration separators (ordered by specificity)
    final separators = [
      ' featuring ',
      ' feat. ',
      ' feat ',
      ' ft. ',
      ' ft ',
      ' with ',
      ' vs. ',
      ' vs ',
      ' and ',
      ' & ',
      ', ',
      ' x ',
      ' + ',
      ' / ',
      ' ; ',
      ' | ',
      ' - ',
    ];

    String workingString = artistName.trim();

    // Apply separators in order of specificity
    for (final separator in separators) {
      if (workingString.toLowerCase().contains(separator.toLowerCase())) {
        final parts =
            workingString.split(RegExp(separator, caseSensitive: false));
        final cleanedParts = parts
            .map((part) => part.trim())
            .where((part) => part.isNotEmpty)
            .map((part) => _cleanArtistName(part))
            .where((part) => part.isNotEmpty)
            .toSet() // Remove duplicates
            .toList();

        if (cleanedParts.length > 1) {
          print('🎵 Separated "$artistName" into: ${cleanedParts.join(", ")}');
          return cleanedParts;
        }
        break; // Only apply the first matching separator
      }
    }

    // No collaboration found, return the cleaned original name
    final cleaned = _cleanArtistName(workingString);
    return cleaned.isNotEmpty ? [cleaned] : [];
  }

  /// Clean up artist names by removing collaboration remnants
  String _cleanArtistName(String name) {
    if (name.trim().isEmpty) return '';

    String cleaned = name.trim();

    // Remove common collaboration remnants
    final cleanupPatterns = [
      RegExp(r'\s*\(.*feat.*\).*$', caseSensitive: false),
      RegExp(r'\s*\(.*featuring.*\).*$', caseSensitive: false),
      RegExp(r'\s*\(.*with.*\).*$', caseSensitive: false),
      RegExp(r'\s*\[.*feat.*\].*$', caseSensitive: false),
      RegExp(r'\s*\[.*featuring.*\].*$', caseSensitive: false),
    ];

    for (final pattern in cleanupPatterns) {
      cleaned = cleaned.replaceAll(pattern, '');
    }

    return cleaned.trim();
  }

  /// Helper method to check if two artist names are close matches
  bool _isCloseArtistMatch(String spotifyName, String searchName) {
    // Remove common variations that don't affect matching
    final cleanSpotifyName = spotifyName.toLowerCase().trim();
    final cleanSearchName = searchName.toLowerCase().trim();

    // Exact match
    if (cleanSpotifyName == cleanSearchName) return true;

    // Remove spaces and try again
    if (cleanSpotifyName.replaceAll(' ', '') ==
        cleanSearchName.replaceAll(' ', '')) return true;

    // Check for artist name variations (like "The Band" vs "Band")
    final spotifyWords =
        cleanSpotifyName.split(' ').where((w) => w.isNotEmpty).toList();
    final searchWords =
        cleanSearchName.split(' ').where((w) => w.isNotEmpty).toList();

    // If one name is just "The" + the other name
    if (spotifyWords.length == searchWords.length + 1 &&
        spotifyWords.first == 'the') {
      final withoutThe = spotifyWords.skip(1).join(' ');
      if (withoutThe == cleanSearchName) return true;
    }
    if (searchWords.length == spotifyWords.length + 1 &&
        searchWords.first == 'the') {
      final withoutThe = searchWords.skip(1).join(' ');
      if (withoutThe == cleanSpotifyName) return true;
    }

    // Check for close contains match (but be more restrictive)
    if (cleanSpotifyName.contains(cleanSearchName) &&
        (cleanSpotifyName.length - cleanSearchName.length) <= 3) {
      return true;
    }

    return false;
  }

  /// Sanitize search query to handle encoding issues (especially for Japanese/Chinese/Korean artists)
  String _sanitizeSearchQuery(String query) {
    if (query.trim().isEmpty) return query;

    // For international artist names, be VERY conservative with sanitization
    // Only remove SPECIFIC known corruption patterns, preserve ALL valid unicode
    String sanitized = query
        .trim()
        // Fix specific encoding corruption patterns but preserve unicode
        .replaceAll(RegExp(r'Ã¢â¬â¢'), "'") // Fix corrupted apostrophe
        .replaceAll(RegExp(r'Ã¢â¬'), "") // Remove specific corruption
        .replaceAll(RegExp(r'ÃâÂ°'), '') // Remove specific corruption
        .replaceAll(RegExp(r'Ã¢â'), '') // Remove specific corruption
        .replaceAll(RegExp(r'Ãâ'), '') // Remove specific corruption
        .replaceAll(RegExp(r'Ã©'), 'é') // Fix corrupted é
        .replaceAll(RegExp(r'Ã¨'), 'è') // Fix corrupted è
        .replaceAll(RegExp(r'Ã¡'), 'á') // Fix corrupted á
        .replaceAll(RegExp(r'Ã '), 'à') // Fix corrupted à
        .replaceAll(RegExp(r'Ã¼'), 'ü') // Fix corrupted ü
        .replaceAll(RegExp(r'Ã¶'), 'ö') // Fix corrupted ö
        // Only remove control characters, keep ALL unicode including CJK characters
        .replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '')
        .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
        .trim();

    // If something went wrong with sanitization, return original
    if (sanitized.isEmpty && query.isNotEmpty) {
      print(
          '⚠️ [SpotifyService] Sanitization resulted in empty string, using original: "$query"');
      return query;
    }

    // Log if we actually changed something
    if (sanitized != query) {
      print('🧹 [SpotifyService] Sanitized query: "$query" -> "$sanitized"');
    }

    return sanitized;
  }

  // Get tracks from a specific playlist
  Future<Map<String, dynamic>> getPlaylistTracks(String playlistId,
      {int limit = 50, int offset = 0}) async {
    try {
      print(
          '🎵 [SpotifyService] Getting playlist tracks (playlist: $playlistId, limit: $limit, offset: $offset)');

      // Handle development mode playlist IDs
      if (kDebugMode && playlistId.startsWith('dev_playlist_')) {
        print(
            '🔬 [SpotifyService] Using mock tracks for development playlist: $playlistId');

        // Return mock track data for development
        final mockTracks = List.generate(
            math.min(limit, 20),
            (index) => MusicTrack(
                  id: 'dev_track_${playlistId}_$index',
                  title: 'Mock Track ${index + 1}',
                  artist: 'Mock Artist ${(index % 3) + 1}',
                  album: 'Mock Album',
                  albumArt: 'https://via.placeholder.com/300x300',
                  url:
                      'https://spotify.com/track/dev_track_${playlistId}_$index',
                  service: 'spotify',
                  serviceType: 'spotify',
                  uri: 'spotify:track:dev_track_${playlistId}_$index',
                  durationMs: 180000 + (index * 5000), // Vary duration
                  genres: [
                    ['pop', 'indie', 'electronic'][index % 3]
                  ],
                  explicit: false,
                  popularity: 50 + (index * 2),
                ));

        return {
          'tracks': mockTracks,
          'total': 50,
          'hasMore': offset + mockTracks.length < 50
        };
      }

      // Build query parameters without market for backend API to avoid playlist not found errors
      final queryParams = {
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      // Only add market parameter if we're NOT using the backend API (direct Spotify API)
      final endpoint = '/playlists/$playlistId/tracks';
      final isBackendRequest =
          _apiBaseUrl.contains('ngrok') || _apiBaseUrl.contains('localhost');

      if (!isBackendRequest) {
        queryParams['market'] = 'from_token';
        print(
            '🌍 [SpotifyService] Adding market parameter for direct Spotify API');
      } else {
        print(
            '🚫 [SpotifyService] Skipping market parameter for backend API to avoid playlist not found error');
      }

      final response = await _makeAuthenticatedRequest(
        endpoint,
        queryParams: queryParams,
      );

      if (response == null || !response.containsKey('items')) {
        return {'tracks': <MusicTrack>[], 'total': 0, 'hasMore': false};
      }

      final List items = response['items'] as List;
      final total = response['total'] as int? ?? 0;
      final hasMore = (offset + items.length) < total;

      print(
          '✓ [SpotifyService] Got ${items.length} playlist tracks (offset: $offset, total available: $total)');

      final tracks = items
          .map((item) {
            try {
              return _mapTrackResponse(item['track']);
            } catch (e) {
              print('⚠️ [SpotifyService] Error mapping track: $e');
              return null;
            }
          })
          .whereType<MusicTrack>()
          .toList(); // Filter out any null tracks from mapping errors

      return {'tracks': tracks, 'total': total, 'hasMore': hasMore};
    } catch (e) {
      print('❌ [SpotifyService] Error getting playlist tracks: $e');
      rethrow;
    }
  }

  // Parse release date with better error handling for different formats
  DateTime? _parseReleaseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;

    try {
      // Handle different date formats from Spotify
      if (dateString.length == 4) {
        // Year only (e.g., "1991")
        return DateTime(int.parse(dateString));
      } else if (dateString.length == 7 && dateString.contains('-')) {
        // Year-month (e.g., "1991-05")
        final parts = dateString.split('-');
        return DateTime(int.parse(parts[0]), int.parse(parts[1]));
      } else {
        // Full date (e.g., "1991-05-15")
        return DateTime.parse(dateString);
      }
    } catch (e) {
      print(
          '⚠️ [SpotifyService] Failed to parse release date "$dateString": $e');
      return null;
    }
  }

  // Map Spotify track response to our MusicTrack model
  MusicTrack _mapTrackResponse(Map<String, dynamic> track) {
    String albumArt = '';
    if (track['album'] != null &&
        track['album']['images'] != null &&
        track['album']['images'].isNotEmpty) {
      albumArt = track['album']['images'][0]['url'];
    }

    String artistName = 'Unknown Artist';
    if (track['artists'] != null && track['artists'].isNotEmpty) {
      artistName = track['artists'].map((artist) => artist['name']).join(', ');
    }

    List<String> genres = [];
    if (track['artists'] != null &&
        track['artists'].isNotEmpty &&
        track['artists'][0]['genres'] != null) {
      genres = List<String>.from(track['artists'][0]['genres'] ?? []);
    }

    String uri = '';
    if (track['uri'] != null) {
      uri = track['uri'];
    } else if (track['external_urls'] != null &&
        track['external_urls']['spotify'] != null) {
      uri = 'spotify:track:${track['id']}';
    }

    // Extract ISRC from external_ids
    String? isrc;
    if (track['external_ids'] != null &&
        track['external_ids']['isrc'] != null) {
      isrc = track['external_ids']['isrc'];
    }

    return MusicTrack(
      id: track['id'] ?? '',
      title: track['name'] ?? 'Unknown Track',
      artist: artistName,
      album: track['album'] != null ? track['album']['name'] ?? '' : '',
      albumArt: albumArt,
      url: track['external_urls'] != null
          ? track['external_urls']['spotify'] ?? ''
          : '',
      service: 'spotify',
      previewUrl: track['preview_url'],
      albumArtUrl: albumArt,
      serviceType: 'spotify',
      genres: genres,
      durationMs: track['duration_ms'] ?? 0,
      releaseDate:
          track['album'] != null && track['album']['release_date'] != null
              ? _parseReleaseDate(track['album']['release_date'])
              : null,
      explicit: track['explicit'] ?? false,
      popularity: track['popularity'] ?? 50,
      uri: uri,
      isrc: isrc,
    );
  }

  // Get user's profile and subscription status
  Future<Map<String, dynamic>?> getUser() async {
    try {
      final response = await _makeAuthenticatedRequest('/me');

      if (response == null) return null;

      // Add a premium status check
      final isPremium = response['product'] == 'premium';
      response['is_premium'] = isPremium;

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user profile: $e');
      }
      return null;
    }
  }

  // Check if user has a premium account
  Future<bool> isPremiumUser() async {
    try {
      final user = await getUser();
      return user != null && user['is_premium'] == true;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking premium status: $e');
      }
      return false;
    }
  }

  // Search for artists by genre with pagination support
  Future<Map<String, dynamic>> searchArtistsByGenrePaginated(
    String genre, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      print(
          '🎯 [SpotifyService] Searching artists for genre: "$genre" (limit: $limit, offset: $offset)');

      // Use multiple search strategies to get the most popular artists for the genre
      final allArtists = <Map<String, dynamic>>[];
      final seenArtistIds = <String>{};

      // Strategy 1: Search for popular artists in the genre
      final searchQueries = [
        genre,
      ];

      // Try each search query to get diverse results
      for (final searchQuery in searchQueries.take(2)) {
        // Use first 2 queries for better results
        try {
          final data = await _makeAuthenticatedRequest(
            '/search',
            queryParams: {
              'q': searchQuery,
              'type': 'artist',
              'limit': (50).toString(), // Get more results to filter from
              'offset': offset.toString(),
            },
          );

          if (data != null && data['artists'] != null) {
            final artists = data['artists'];
            final List items = artists['items'] ?? [];

            for (final artist in items) {
              final artistId = artist['id'] as String?;
              if (artistId != null && !seenArtistIds.contains(artistId)) {
                seenArtistIds.add(artistId);

                // Verify the artist actually has the target genre
                final artistGenres = List<String>.from(artist['genres'] ?? []);
                final hasTargetGenre = artistGenres.any((g) =>
                    g.toLowerCase().contains(genre.toLowerCase()) ||
                    genre.toLowerCase().contains(g.toLowerCase()));

                if (hasTargetGenre || searchQuery.contains('genre:"$genre"')) {
                  allArtists.add({
                    'id': artistId,
                    'name': artist['name'] ?? 'Unknown Artist',
                    'image_url': artist['images']?.isNotEmpty == true
                        ? artist['images'][0]['url']
                        : null,
                    'genres': artistGenres,
                    'popularity': artist['popularity'] ?? 50,
                    'url': artist['external_urls']?['spotify'] ?? '',
                    'followers': artist['followers']?['total'] ?? 0,
                  });
                }
              }
            }
          }
        } catch (e) {
          print(
              '❌ [SpotifyService] Error with search query "$searchQuery": $e');
        }
      }

      // Sort by popularity (highest first) to ensure top artists come first
      allArtists.sort(
          (a, b) => (b['popularity'] as int).compareTo(a['popularity'] as int));

      // Apply pagination to the sorted results
      final startIndex = math.min(offset, allArtists.length);
      final endIndex = math.min(offset + limit, allArtists.length);
      final paginatedArtists = allArtists.sublist(startIndex, endIndex);

      final hasMore = endIndex < allArtists.length;

      print(
          '✅ [SpotifyService] Found ${paginatedArtists.length} top artists for genre "$genre" (sorted by popularity)');

      return {
        'artists': paginatedArtists,
        'hasMore': hasMore,
        'total': allArtists.length,
      };
    } catch (e) {
      print('❌ [SpotifyService] Error searching artists by genre "$genre": $e');
      return {'artists': <Map<String, dynamic>>[], 'hasMore': false};
    }
  }

  // Get top tracks by artist with pagination support
  Future<List<MusicTrack>> getTopTracksByArtistPaginated(
    String artistName, {
    int limit = 10,
    int offset = 0,
  }) async {
    try {
      print(
          '🎯 [SpotifyService] Getting top tracks for artist: "$artistName" (limit: $limit, offset: $offset)');

      // Build search query for artist's tracks
      final searchQuery = 'artist:"$artistName"';

      final data = await _makeAuthenticatedRequest(
        '/search',
        queryParams: {
          'q': searchQuery,
          'type': 'track',
          'limit': limit.toString(),
          'offset': offset.toString(),
        },
      );

      if (data == null || data['tracks'] == null) {
        return [];
      }

      final tracks = data['tracks'];
      final List items = tracks['items'] ?? [];

      // Convert to MusicTrack objects
      final musicTracks =
          items.map((track) => _mapTrackResponse(track)).toList();

      // Sort by popularity (highest first)
      musicTracks
          .sort((a, b) => (b.popularity ?? 0).compareTo(a.popularity ?? 0));

      print(
          '✅ [SpotifyService] Found ${musicTracks.length} tracks for artist "$artistName"');

      return musicTracks;
    } catch (e) {
      print(
          '❌ [SpotifyService] Error getting top tracks for artist "$artistName": $e');
      return [];
    }
  }

  // Get user's top genres based on their top artists
  Future<List<String>> getTopGenres(
      {int limit = 10, String timeRange = 'medium_term'}) async {
    try {
      // Get top artists first
      final response = await _makeAuthenticatedRequest(
        "${_apiBaseUrl}/me/top/artists/",
        queryParams: {
          'limit': '50', // Get more artists to capture more genres
          'time_range': timeRange, // short_term, medium_term, or long_term
        },
      );

      if (response == null || !response.containsKey('items')) {
        return [];
      }

      // Extract all genres from artists
      final List<dynamic> artists = response['items'];
      final Map<String, int> genreCounts = {};

      // Count genre occurrences
      for (var artist in artists) {
        final List<dynamic> genres = artist['genres'] ?? [];
        for (var genre in genres) {
          genreCounts[genre] = (genreCounts[genre] ?? 0) + 1;
        }
      }

      // Sort genres by count
      final sortedGenres = genreCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      // Return top genres up to the limit
      final topGenres =
          sortedGenres.take(limit).map((entry) => entry.key).toList();

      if (kDebugMode) {
        print('✅ Retrieved ${topGenres.length} top genres');
      }

      return topGenres;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting top genres: $e');
      }
      return [];
    }
  }

  // Get comprehensive user statistics from Spotify
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      final Map<String, dynamic> stats = {
        'profile': null,
        'top_artists': <Map<String, dynamic>>[],
        'top_tracks': <Map<String, dynamic>>[],
        'top_genres': <String>[],
        'playlists_count': 0,
        'saved_tracks_count': 0,
        'recently_played': <Map<String, dynamic>>[],
      };

      // Get user profile
      stats['profile'] = await getUserProfile();

      // Get top artists
      final artistsResponse = await _makeAuthenticatedRequest(
        "${_apiBaseUrl}/me/top/artists",
        queryParams: {'limit': '10', 'time_range': 'medium_term'},
      );

      if (artistsResponse != null && artistsResponse.containsKey('items')) {
        stats['top_artists'] = List<Map<String, dynamic>>.from(
          artistsResponse['items'].map((artist) => {
                'id': artist['id'],
                'name': artist['name'],
                'image_url': artist['images']?.isNotEmpty == true
                    ? artist['images'][0]['url']
                    : null,
                'genres': List<String>.from(artist['genres'] ?? []),
                'popularity': artist['popularity'],
                'url': artist['external_urls']['spotify'],
              }),
        );
      }

      // Get top tracks
      final tracksResponse = await _makeAuthenticatedRequest(
        "${_apiBaseUrl}/me/top/tracks",
        queryParams: {'limit': '10', 'time_range': 'medium_term'},
      );

      if (tracksResponse != null && tracksResponse.containsKey('items')) {
        stats['top_tracks'] =
            _convertSpotifyTracksToMusicTracks(tracksResponse['items']);
      }

      // Get top genres
      stats['top_genres'] = await getTopGenres(limit: 10);

      // Get playlists count
      final playlistsResponse = await _makeAuthenticatedRequest(
        "${_apiBaseUrl}/me/playlists",
        queryParams: {'limit': '1'},
      );

      if (playlistsResponse != null && playlistsResponse.containsKey('total')) {
        stats['playlists_count'] = playlistsResponse['total'];
      }

      // Get saved tracks count
      final savedTracksResponse = await _makeAuthenticatedRequest(
        "${_apiBaseUrl}/me/tracks",
        queryParams: {'limit': '1'},
      );

      if (savedTracksResponse != null &&
          savedTracksResponse.containsKey('total')) {
        stats['saved_tracks_count'] = savedTracksResponse['total'];
      }

      // Get recently played
      final recentlyPlayedResponse = await _makeAuthenticatedRequest(
        "${_apiBaseUrl}/me/player/recently-played",
        queryParams: {'limit': '10'},
      );

      if (recentlyPlayedResponse != null &&
          recentlyPlayedResponse.containsKey('items')) {
        final items = recentlyPlayedResponse['items'];
        stats['recently_played'] =
            items.map((item) => _mapTrackResponse(item['track'])).toList();
      }

      return stats;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting user stats: $e');
      }
      return {
        'error': e.toString(),
        'profile': null,
        'top_artists': [],
        'top_tracks': [],
        'top_genres': [],
        'playlists_count': 0,
        'saved_tracks_count': 0,
        'recently_played': [],
      };
    }
  }

  /// Get user profile information from Spotify
  Future<Map<String, dynamic>?> getUserProfile() async {
    try {
      final response = await _makeAuthenticatedRequest(
        "${_apiBaseUrl}/me",
      );

      if (response != null) {
        // Add some helpful calculated fields
        final Map<String, dynamic> enhancedProfile = Map.from(response);

        // Add profile image URL if available
        if (response['images'] != null &&
            (response['images'] as List).isNotEmpty) {
          enhancedProfile['profile_image_url'] = response['images'][0]['url'];
        } else {
          enhancedProfile['profile_image_url'] = null;
        }

        // Add account type and status
        enhancedProfile['is_premium'] = response['product'] == 'premium';
        enhancedProfile['account_type'] = response['product'] ?? 'free';

        if (kDebugMode) {
          print(
              '✅ Retrieved Spotify user profile: ${enhancedProfile['display_name']}');
        }

        return enhancedProfile;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting user profile: $e');
      }
      return null;
    }
  }

  /// Convert a list of Spotify track objects to our MusicTrack model
  List<MusicTrack> _convertSpotifyTracksToMusicTracks(List<dynamic> tracks) {
    return tracks.map((track) => _mapTrackResponse(track)).toList();
  }

  // Clear search cache to prevent encoding issues and cross-contamination
  void clearSearchCache() {
    _searchCache.clear();
    print('🧹 [SpotifyService] Search cache cleared');
  }

  // Test specific playlist access for debugging
  Future<void> debugPlaylistAccess(String playlistId) async {
    print('🧪 [SPOTIFY DEBUG] Testing playlist access for: $playlistId');

    try {
      // Test the exact URL construction
      final endpoint = '/playlists/$playlistId/tracks';
      final cleanEndpoint =
          endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
      final testUrl = '$_apiBaseUrl/$cleanEndpoint/';

      print('🧪 [SPOTIFY DEBUG] Constructed URL: $testUrl');

      // Test if we can reach the endpoint
      final client = _getHttpClient();

      // Test 1: Without market parameter (backend-friendly)
      print('🧪 [SPOTIFY DEBUG] Test 1: Without market parameter');
      final response1 = await client.get(
        Uri.parse(testUrl).replace(queryParameters: {'limit': '1'}),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      print('🧪 [SPOTIFY DEBUG] Test 1 result: ${response1.statusCode}');
      print('🧪 [SPOTIFY DEBUG] Test 1 body: ${response1.body}');

      // Test 2: With market parameter (potential issue)
      print('🧪 [SPOTIFY DEBUG] Test 2: With market=from_token parameter');
      final response2 = await client.get(
        Uri.parse(testUrl)
            .replace(queryParameters: {'limit': '1', 'market': 'from_token'}),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      print('🧪 [SPOTIFY DEBUG] Test 2 result: ${response2.statusCode}');
      print('🧪 [SPOTIFY DEBUG] Test 2 body: ${response2.body}');

      // Test 3: Using the actual method (should now work)
      print('🧪 [SPOTIFY DEBUG] Test 3: Using actual getPlaylistTracks method');
      try {
        final methodResult = await getPlaylistTracks(playlistId, limit: 1);
        print(
            '🧪 [SPOTIFY DEBUG] Method result: ${methodResult['tracks']?.length ?? 0} tracks');
        print(
            '🧪 [SPOTIFY DEBUG] Method success: ${methodResult['tracks']?.isNotEmpty ?? false}');
      } catch (methodError) {
        print('🧪 [SPOTIFY DEBUG] Method failed: $methodError');
      }
    } catch (e) {
      print('🧪 [SPOTIFY DEBUG] Direct test failed: $e');
    }
  }

  // Debug method to check service health
  Future<Map<String, dynamic>> debugServiceHealth() async {
    final results = <String, dynamic>{};

    try {
      // Check connection status
      results['isConnected'] = await isConnected();

      // Check Spotify token status
      final spotifyToken = await getAccessToken();
      results['hasSpotifyToken'] = spotifyToken != null;
      results['spotifyTokenType'] =
          spotifyToken?.startsWith('dev_token_') == true
              ? 'development'
              : 'production';

      // Check backend token status
      final backendToken = await getBackendAuthToken();
      results['hasBackendToken'] = backendToken != null;
      results['backendTokenLength'] = backendToken?.length ?? 0;

      // Test basic search endpoint
      try {
        final searchTest = await searchTracks('test', limit: 1);
        results['searchWorking'] = true;
        results['searchResults'] = searchTest.length;
      } catch (e) {
        results['searchWorking'] = false;
        results['searchError'] = e.toString();
      }

      // Test backend connectivity
      try {
        final testUrl = '$_apiBaseUrl/search/';
        final response = await http.get(
          Uri.parse(testUrl).replace(
              queryParameters: {'q': 'test', 'type': 'track', 'limit': '1'}),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ).timeout(const Duration(seconds: 5));

        results['backendReachable'] = response.statusCode != 404;
        results['backendStatus'] = response.statusCode;
        results['backendUrl'] = testUrl;
      } catch (e) {
        results['backendReachable'] = false;
        results['backendError'] = e.toString();
      }
    } catch (e) {
      results['error'] = e.toString();
    }

    return results;
  }

  // Disconnect from Spotify (clear tokens)
  Future<bool> disconnect() async {
    try {
      await Future.wait([
        // Clear current keys
        _secureStorage.delete(key: _accessTokenKey),
        _secureStorage.delete(key: _refreshTokenKey),
        _secureStorage.delete(key: _expiryTimeKey),
        _secureStorage.delete(key: _codeVerifierKey),
        // Clear backend keys
        _secureStorage.delete(key: 'spotify_backend_access_token'),
        _secureStorage.delete(key: 'spotify_backend_token_expiry'),
        // Clear legacy keys
        _secureStorage.delete(key: 'spotify_access_token'),
        _secureStorage.delete(key: 'spotify_refresh_token'),
        _secureStorage.delete(key: 'spotify_token_expiry'),
      ]);
      return true;
    } catch (e) {
      print('Error disconnecting from Spotify: $e');
      return false;
    }
  }

  // Search for tracks by genre using SpotifyGenreService aliases for better results
  Future<List<MusicTrack>> searchTracksByGenre(String genre,
      {int limit = 20, int offset = 0, int? year, String? artist}) async {
    // Enhanced logging for AI tracking
    final timestamp = DateTime.now().toIso8601String();
    final filters = [
      if (year != null) 'YEAR: $year',
      if (artist != null) 'ARTIST: $artist',
    ].join(' | ');
    final filterInfo = filters.isNotEmpty ? ' [FILTERS: $filters]' : '';

    try {
      if (genre.isEmpty) {
        return [];
      }

      print(
          '🎯 [SPOTIFY AI SEARCH GENRE] [$timestamp] GENRE: "$genre" | LIMIT: $limit | OFFSET: $offset$filterInfo');

      // Proceed even if the user is not connected – backend client-credentials fallback will handle public catalogue access.

      // Determine appropriate market for the genre
      final market = _getMarketForGenre(genre);
      if (market != null) {
        print(
            '🌍 [SPOTIFY AI SEARCH GENRE] Using market: $market for genre: $genre');
      }

      // Get all possible search terms for this genre using SpotifyGenreService
      final List<String> genreAliases =
          SpotifyGenreService.getGenreAliases(genre);
      print(
          '🎯 [SPOTIFY AI SEARCH GENRE] Using ${genreAliases.length} aliases for "$genre": ${genreAliases.join(", ")}');

      // Build cache key
      final cacheKey =
          'genre:$genre|$limit|$offset|${year ?? ''}|${artist ?? ''}|${market ?? ''}';
      if (_searchCache.containsKey(cacheKey)) {
        return _searchCache[cacheKey]!;
      }

      // ===== NEW APPROACH: Search for genre playlists instead of tracks =====
      final allTracks = <MusicTrack>[];
      final processedPlaylistIds = <String>{};

      // Define genre-specific keywords to EXCLUDE (wrong genres)
      final excludeKeywords = _getExcludeKeywordsForGenre(genre);

      // Search for playlists for each genre alias
      for (final alias in genreAliases.take(4)) {
        // Increased from 2 to 4 aliases for more comprehensive search
        try {
          // Build MORE SPECIFIC playlist search queries
          final playlistQueries =
              _getSpecificPlaylistQueries(genre, alias, market);

          for (final playlistQuery in playlistQueries.take(5)) {
            // Increased from 3 to 5 queries for more variety
            print(
                '🎵 [SPOTIFY AI SEARCH GENRE] Searching for playlists: "$playlistQuery"');

            // Search for playlists
            final playlists = await searchPlaylists(
              playlistQuery,
              limit:
                  20, // Increased from 10 to 20 playlists per query for more tracks
              offset: offset ~/ 10, // Adjust offset for playlists
            );

            if (playlists.isEmpty) {
              print(
                  '⚠️ [SPOTIFY AI SEARCH GENRE] No playlists found for "$playlistQuery"');
              continue;
            }

            print(
                '📚 [SPOTIFY AI SEARCH GENRE] Found ${playlists.length} playlists for "$playlistQuery"');

            // Get tracks from each playlist
            for (final playlist in playlists) {
              final playlistId = playlist['id'] as String?;
              final playlistName = playlist['name'] as String? ?? '';
              final playlistOwner =
                  playlist['owner']?['display_name'] as String? ?? '';

              if (playlistId == null ||
                  processedPlaylistIds.contains(playlistId)) {
                continue;
              }

              processedPlaylistIds.add(playlistId);

              // Enhanced playlist filtering
              final playlistDesc =
                  (playlist['description'] as String? ?? '').toLowerCase();
              final playlistNameLower = playlistName.toLowerCase();

              // STRICTER relevance check - require more specific matches
              bool isRelevant = false;
              bool isExcluded = false;

              // Check for exclusion keywords first
              for (final excludeKeyword in excludeKeywords) {
                if (playlistNameLower.contains(excludeKeyword) ||
                    playlistDesc.contains(excludeKeyword)) {
                  isExcluded = true;
                  print(
                      '🚫 [SPOTIFY AI SEARCH GENRE] Excluding playlist due to keyword "$excludeKeyword": "$playlistName"');
                  break;
                }
              }

              if (isExcluded) continue;

              // For C-pop/K-pop/J-pop, require more specific matches
              if (genre.toLowerCase().contains('c-pop') ||
                  genre.toLowerCase().contains('chinese') ||
                  genre.toLowerCase().contains('cantopop') ||
                  genre.toLowerCase().contains('mandopop') ||
                  genre.toLowerCase().contains('taiwanese') ||
                  genre.toLowerCase().contains('taiwan')) {
                isRelevant = playlistNameLower.contains('chinese') ||
                    playlistNameLower.contains('mandarin') ||
                    playlistNameLower.contains('cantonese') ||
                    playlistNameLower.contains('taiwanese') ||
                    playlistNameLower.contains('taiwan') ||
                    playlistNameLower.contains('c-pop') ||
                    playlistNameLower.contains('cpop') ||
                    playlistNameLower.contains('pop') &&
                        (playlistNameLower.contains('chinese') ||
                            playlistNameLower.contains('mandarin') ||
                            playlistNameLower.contains('taiwan') ||
                            playlistNameLower.contains('中文') ||
                            playlistNameLower.contains('華語') ||
                            playlistNameLower.contains('粵語')) ||
                    playlistNameLower.contains('中文') || // Chinese characters
                    playlistNameLower.contains('華語') || // Chinese music
                    playlistNameLower.contains('粵語') || // Cantonese
                    playlistNameLower.contains('台') || // Taiwan character
                    playlistDesc.contains('chinese') ||
                    playlistDesc.contains('mandarin') ||
                    playlistDesc.contains('cantonese') ||
                    playlistDesc.contains('taiwanese') ||
                    playlistDesc.contains('taiwan');

                // Also check owner - prefer Spotify official or Asian curators
                if (playlistOwner.toLowerCase().contains('spotify') &&
                    (playlistNameLower.contains('china') ||
                        playlistNameLower.contains('taiwan') ||
                        playlistNameLower.contains('hong kong'))) {
                  isRelevant = true;
                }
              } else if (genre.toLowerCase().contains('k-pop') ||
                  genre.toLowerCase().contains('korean')) {
                // Must have Korean specific terms
                isRelevant = playlistNameLower.contains('korean') ||
                    playlistNameLower.contains('k-pop') ||
                    playlistNameLower.contains('kpop') ||
                    playlistNameLower.contains('한국') || // Korean characters
                    playlistDesc.contains('korean') ||
                    playlistDesc.contains('k-pop');
              } else if (genre.toLowerCase().contains('j-pop') ||
                  genre.toLowerCase().contains('japanese') ||
                  genre.toLowerCase().contains('j-rock') ||
                  genre.toLowerCase().contains('jrock')) {
                // For any Japanese genre, be more inclusive with playlists.
                // The main filtering will happen at the track level.
                isRelevant = playlistNameLower.contains('japan') ||
                    playlistNameLower.contains('j-pop') ||
                    playlistNameLower.contains('j-rock') ||
                    playlistNameLower.contains('jpop') ||
                    playlistNameLower.contains('jrock') ||
                    playlistNameLower.contains('日本') ||
                    playlistDesc.contains('japan') ||
                    playlistDesc.contains('j-pop') ||
                    playlistDesc.contains('j-rock');
              } else {
                // For other genres, use standard matching but be more specific
                for (final genreAlias in genreAliases) {
                  final aliasLower = genreAlias.toLowerCase();
                  // Require word boundaries for better matching
                  final regex =
                      RegExp(r'\b' + RegExp.escape(aliasLower) + r'\b');
                  if (regex.hasMatch(playlistNameLower) ||
                      regex.hasMatch(playlistDesc)) {
                    isRelevant = true;
                    break;
                  }
                }
              }

              if (!isRelevant) {
                print(
                    '⚠️ [SPOTIFY AI SEARCH GENRE] Skipping unrelated playlist: "$playlistName"');
                continue;
              }

              print(
                  '📝 [SPOTIFY AI SEARCH GENRE] Getting tracks from playlist: "$playlistName" (${playlist['tracks']?['total'] ?? 0} tracks)');

              try {
                // Get tracks from this playlist
                final playlistTracksData = await getPlaylistTracks(
                  playlistId,
                  limit:
                      200, // Increased from 100 to 200 tracks per playlist for more variety
                  offset: 0,
                );

                final playlistTracks =
                    playlistTracksData['tracks'] as List<MusicTrack>? ?? [];

                if (playlistTracks.isNotEmpty) {
                  // Apply filters if specified
                  List<MusicTrack> filteredTracks = playlistTracks;

                  // Additional genre validation for tracks (from trusted playlist source)
                  filteredTracks = _validateTracksForGenre(
                      filteredTracks, genre,
                      isFromPlaylist: true, playlistName: playlistName);

                  if (artist != null && artist.isNotEmpty) {
                    filteredTracks = filteredTracks
                        .where((track) => track.artist
                            .toLowerCase()
                            .contains(artist.toLowerCase()))
                        .toList();
                  }

                  if (year != null) {
                    filteredTracks = filteredTracks
                        .where((track) => track.releaseDate?.year == year)
                        .toList();
                  }

                  // Mix tracks from this playlist to avoid artist clustering
                  filteredTracks = _mixTracksByArtist(filteredTracks);

                  allTracks.addAll(filteredTracks);
                  print(
                      '✅ [SPOTIFY AI SEARCH GENRE] Added ${filteredTracks.length} validated and mixed tracks from "$playlistName"');
                }
              } catch (e) {
                print(
                    '❌ [SPOTIFY AI SEARCH GENRE] Error getting tracks from playlist $playlistId: $e');
              }

              // Stop if we have enough tracks (increased limit for more variety)
              if (allTracks.length >= limit * 10) {
                // Increased from limit * 2 to limit * 10 for hundreds of tracks
                break;
              }
            }

            // Stop if we have enough tracks (increased limit for more variety)
            if (allTracks.length >= limit * 10) {
              // Increased from limit * 2 to limit * 10 for hundreds of tracks
              break;
            }
          }
        } catch (e) {
          print(
              '❌ [SPOTIFY AI SEARCH GENRE] Error searching playlists for alias "$alias": $e');
        }
      }

      // If we didn't get enough tracks from playlists, fall back to the old method for the remaining
      if (allTracks.length < limit) {
        print(
            '⚠️ [SPOTIFY AI SEARCH GENRE] Only found ${allTracks.length} tracks from playlists, falling back to direct search');

        try {
          // Check if we have genre aliases to avoid null error
          if (genreAliases.isEmpty) {
            print(
                '❌ [SPOTIFY AI SEARCH GENRE] No genre aliases available for fallback search');
            return [];
          }

          // For artist-based fallback, don't include genre since artists come from Last.fm (same genre)
          String query;
          if (artist != null && artist.isNotEmpty) {
            // Artist from Last.fm is already genre-specific, just search for them directly
            query = 'artist:"$artist"';
            print(
                '🎤 [SPOTIFY AI SEARCH GENRE] Artist-based fallback (no genre): $query');
          } else {
            // Only use genre if no specific artist
            final fallbackQuery = genreAliases.first;
            query = 'genre:"$fallbackQuery"';
            print('🎯 [SPOTIFY AI SEARCH GENRE] Genre-based fallback: $query');
          }

          if (year != null) {
            query += ' year:$year';
          }

          // Spotify search offset has a limit of 1000. Don't use a high offset for fallback.
          final fallbackOffset = offset < 950 ? offset : (offset % 950);
          print(
              '🔄 [SPOTIFY AI SEARCH GENRE] Using fallback offset: $fallbackOffset (original: $offset)');

          final fallbackTracks = await searchTracks(
            query,
            limit: math.max(limit - allTracks.length,
                50), // Increased from 20 to 50 tracks in fallback for more variety
            offset: fallbackOffset,
            context: 'AI_GENRE_FALLBACK',
            market: market, // Use market for better regional results
          );

          // Validate fallback tracks too (not from playlists)
          final validatedFallback =
              _validateTracksForGenre(fallbackTracks, genre);

          allTracks.addAll(validatedFallback);
          print(
              '📥 [SPOTIFY AI SEARCH GENRE] Added ${validatedFallback.length} validated fallback tracks');
        } catch (e) {
          print('❌ [SPOTIFY AI SEARCH GENRE] Fallback search failed: $e');
        }
      }

      // Remove duplicates and mix for variety
      final seenIds = <String>{};
      final uniqueTracks = allTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      // Mix tracks by artist and playlists to avoid clustering
      final mixedTracks = _mixTracksByArtist(uniqueTracks);

      print(
          '✅ [SPOTIFY AI SEARCH GENRE] FINAL SUCCESS: ${mixedTracks.length} mixed tracks for genre "$genre"$filterInfo');

      // Log to AI analytics system
      AIRecommendationService.logSearchActivity(
        query: genre,
        searchType: 'genre_playlist',
        context: 'AI_GENRE_PLAYLIST_SEARCH',
        genre: genre,
        resultsCount: mixedTracks.length,
        limit: limit,
        offset: offset,
        isSuccess: true,
      );

      // Return more tracks to support streaming - cache and return more than requested limit
      final tracksToReturn = mixedTracks
          .take(math.max(limit * 5, 100))
          .toList(); // Return at least 100 tracks or 5x limit
      _searchCache[cacheKey] = tracksToReturn;
      return tracksToReturn;
    } catch (e) {
      print(
          '❌ [SPOTIFY AI SEARCH GENRE] FINAL ERROR: Failed for genre "$genre"$filterInfo - $e');

      // Log error to AI analytics system
      AIRecommendationService.logSearchActivity(
        query: genre,
        searchType: 'genre_playlist',
        context: 'AI_GENRE_PLAYLIST_SEARCH',
        genre: genre,
        limit: limit,
        offset: offset,
        isSuccess: false,
        errorMessage: e.toString(),
      );

      return [];
    }
  }

  /// Get specific playlist queries for a genre
  List<String> _getSpecificPlaylistQueries(
      String genre, String alias, String? market) {
    final genreLower = genre.toLowerCase();

    // For Asian genres, use very specific queries
    if (genreLower.contains('c-pop') ||
        genreLower.contains('chinese') ||
        genreLower.contains('cantopop') ||
        genreLower.contains('mandopop')) {
      return [
        'chinese pop official',
        'mandarin pop hits',
        'cantonese pop music',
        '華語流行音樂', // Chinese pop music in Chinese
        'c-pop 2024',
        'chinese music playlist',
        'taiwan pop music',
        'hong kong cantopop',
      ];
    } else if (genreLower.contains('k-pop') || genreLower.contains('korean')) {
      return [
        'k-pop official',
        'korean pop hits',
        'kpop 2024',
        '케이팝', // K-pop in Korean
        'korean music playlist',
        'korea pop music',
        'kpop new releases',
      ];
    } else if (genreLower.contains('j-pop') ||
        genreLower.contains('japanese')) {
      return [
        'j-pop official',
        'japanese pop hits',
        'jpop 2024',
        'ジェイポップ', // J-pop in Japanese
        'japanese music playlist',
        'japan pop music',
        'jpop new releases',
      ];
    }

    // For other genres, use standard queries
    return [
      '$alias playlist',
      '$alias official',
      '$alias hits ${DateTime.now().year}',
      'best $alias music',
      '$alias essentials',
    ];
  }

  /// Get keywords to exclude for a specific genre to avoid wrong results
  List<String> _getExcludeKeywordsForGenre(String genre) {
    final genreLower = genre.toLowerCase();

    if (genreLower.contains('c-pop') ||
        genreLower.contains('chinese') ||
        genreLower.contains('cantopop') ||
        genreLower.contains('mandopop')) {
      // Exclude other pop genres and country music
      return [
        'country',
        'nashville',
        'morgan wallen',
        'luke combs',
        'blake shelton',
        'k-pop',
        'kpop',
        'korean',
        'j-pop',
        'jpop',
        'japanese',
        'latin pop',
        'reggaeton',
        'spanish pop'
      ];
    } else if (genreLower.contains('k-pop') || genreLower.contains('korean')) {
      return [
        'country',
        'nashville',
        'morgan wallen',
        'c-pop',
        'cpop',
        'chinese',
        'mandarin',
        'cantonese',
        'j-pop',
        'jpop',
        'japanese',
        'latin pop',
        'reggaeton',
        'spanish pop'
      ];
    } else if (genreLower.contains('j-pop') ||
        genreLower.contains('japanese')) {
      return [
        'country',
        'nashville',
        'morgan wallen',
        'c-pop',
        'cpop',
        'chinese',
        'mandarin',
        'cantonese',
        'k-pop',
        'kpop',
        'korean',
        'latin pop',
        'reggaeton',
        'spanish pop'
      ];
    }

    // For other genres, exclude obvious wrong genres
    return [
      'morgan wallen',
      'luke combs',
      'blake shelton'
    ]; // Common country artists to exclude
  }

  /// Validate tracks belong to the expected genre
  List<MusicTrack> _validateTracksForGenre(
      List<MusicTrack> tracks, String genre,
      {bool isFromPlaylist = false, String? playlistName}) {
    final genreLower = genre.toLowerCase();

    // For Asian genres, do artist name validation
    if (genreLower.contains('c-pop') ||
        genreLower.contains('chinese') ||
        genreLower.contains('cantopop') ||
        genreLower.contains('mandopop')) {
      return tracks.where((track) {
        final artistLower = track.artist.toLowerCase();
        final titleLower = track.title.toLowerCase();

        // Exclude obvious non-Chinese artists
        if (artistLower.contains('morgan wallen') ||
            artistLower.contains('luke combs') ||
            artistLower.contains('blake shelton') ||
            artistLower.contains('keith urban')) {
          print('🚫 Filtering out non-Chinese artist: ${track.artist}');
          return false;
        }

        // Prefer tracks with Chinese characters or romanized Chinese names
        if (_containsChineseCharacters(track.artist) ||
            _containsChineseCharacters(track.title)) {
          return true;
        }

        // Check for common Chinese/Taiwanese artist name patterns
        if (_isLikelyChineseArtist(track.artist)) {
          return true;
        }

        // If uncertain, include it (to avoid over-filtering)
        return true;
      }).toList();
    } else if (genreLower.contains('k-pop') || genreLower.contains('korean')) {
      return tracks.where((track) {
        final artistLower = track.artist.toLowerCase();

        // Exclude obvious non-Korean artists
        if (artistLower.contains('morgan wallen') ||
            artistLower.contains('luke combs') ||
            artistLower.contains('blake shelton')) {
          print('🚫 Filtering out non-Korean artist: ${track.artist}');
          return false;
        }

        return true;
      }).toList();
    } else if (genreLower.contains('j-pop') ||
        genreLower.contains('japanese') ||
        genreLower.contains('j-rock') ||
        genreLower.contains('jrock')) {
      final isRockRequest = genreLower.contains('rock') ||
          genreLower.contains('j-rock') ||
          genreLower.contains('jrock');
      final isPopRequest = genreLower.contains('pop') ||
          genreLower.contains('j-pop') ||
          genreLower.contains('jpop');

      return tracks.where((track) {
        final artistLower = track.artist.toLowerCase();
        final titleLower = track.title.toLowerCase();

        // Strong indicator for relevance
        final hasJapaneseChars = _containsJapaneseCharacters(artistLower) ||
            _containsJapaneseCharacters(titleLower);

        // --- NEW STRICT FILTERING ---

        // 1. Definite non-Japanese artists
        final nonJapaneseArtists = [
          'ghostface killah',
          'wu-tang clan',
          'pete rock',
          'c.l. smooth',
          'morgan wallen',
          'luke combs',
          'blake shelton',
          'keith urban',
          'drake',
          'kanye west',
          'taylor swift',
          'ariana grande',
          'ed sheeran'
        ];
        if (nonJapaneseArtists.any((name) => artistLower.contains(name))) {
          print(
              '🚫 Filtering out non-Japanese artist by name: ${track.artist}');
          return false;
        }

        // 2. Definite non-Japanese genres (if track.genres is available)
        if (track.genres.isNotEmpty) {
          final trackGenres = track.genres.map((g) => g.toLowerCase());
          final nonJapaneseGenres = [
            'hip hop',
            'rap',
            'r&b',
            'country',
            'latin',
            'reggaeton',
            'afrobeat',
            'funk',
            'soul',
            'blues',
            'gospel'
          ];

          if (trackGenres.any((tg) => nonJapaneseGenres.contains(tg))) {
            print(
                '🚫 Filtering out track with non-Japanese genre: ${track.title} by ${track.artist} (Genres: ${track.genres})');
            return false;
          }
        }

        // 3. Heuristics for tracks without Japanese characters
        if (!hasJapaneseChars) {
          final nonJapaneseKeywords = [
            'feat',
            ' ft',
            ' remix',
            ' radio edit',
            ' live',
            ' & ',
            ' vs '
          ];
          if (nonJapaneseKeywords.any((keyword) =>
              titleLower.contains(keyword) || artistLower.contains(keyword))) {
            print(
                '🚫 Filtering out non-Japanese-character track with common English keywords: ${track.title}');
            return false;
          }

          if (isRockRequest && artistLower.startsWith('dj ')) {
            print('🚫 Filtering out DJ from rock request: ${track.artist}');
            return false;
          }
        }

        // 4. Specific filtering for J-Rock vs J-Pop
        if (track.genres.isNotEmpty) {
          final trackGenres = track.genres.map((g) => g.toLowerCase());
          if (isRockRequest &&
              (trackGenres.contains('j-pop') ||
                  trackGenres.contains('k-pop') ||
                  trackGenres.contains('pop'))) {
            // Allow if it ALSO has a rock genre
            if (!trackGenres.any((tg) => tg.contains('rock'))) {
              print(
                  '🚫 Filtering out pop track from J-Rock request: ${track.title}');
              return false;
            }
          }
          if (isPopRequest &&
              trackGenres.any(
                  (tg) => tg.contains('rock') && !tg.contains('pop rock'))) {
            // If it's a rock genre but not pop-rock, exclude from pop request.
            print(
                '🚫 Filtering out rock track from J-Pop request: ${track.title}');
            return false;
          }
        }

        return true;
      }).toList();
    }

    // For Western genres, add strict validation to prevent cross-contamination
    return _validateWesternGenreTracks(tracks, genre,
        isFromPlaylist: isFromPlaylist, playlistName: playlistName);
  }

  /// Validate tracks for Western genres to prevent cross-contamination
  List<MusicTrack> _validateWesternGenreTracks(
      List<MusicTrack> tracks, String genre,
      {bool isFromPlaylist = false, String? playlistName}) {
    final genreLower = genre.toLowerCase();

    return tracks.where((track) {
      final artistLower = track.artist.toLowerCase();
      final titleLower = track.title.toLowerCase();
      final trackGenres = track.genres.map((g) => g.toLowerCase()).toList();

      // Hip hop / rap validation
      if (genreLower.contains('hip hop') ||
          genreLower.contains('rap') ||
          genreLower.contains('trap') ||
          genreLower.contains('uk drill') ||
          genreLower.contains('chicago drill') ||
          genreLower.contains('drill')) {
        // Exclude Asian artists/tracks from hip hop searches
        if (_containsChineseCharacters(track.artist) ||
            _containsChineseCharacters(track.title) ||
            _containsJapaneseCharacters(track.artist) ||
            _containsJapaneseCharacters(track.title)) {
          print(
              '🚫 Filtering out Asian track from hip hop search: "${track.title}" by ${track.artist}');
          return false;
        }

        // Exclude obviously non-hip hop genres if available
        if (trackGenres.isNotEmpty) {
          final nonHipHopGenres = [
            'c-pop',
            'k-pop',
            'j-pop',
            'chinese',
            'korean',
            'japanese',
            'country',
            'classical',
            'opera',
            'folk',
            'bluegrass'
          ];
          if (trackGenres
              .any((tg) => nonHipHopGenres.any((nhg) => tg.contains(nhg)))) {
            print(
                '🚫 Filtering out non-hip hop genre track: "${track.title}" by ${track.artist} (Genres: ${track.genres})');
            return false;
          }
        }

        // Check for obvious Chinese artist patterns in hip hop
        if (_isLikelyChineseArtist(track.artist)) {
          print(
              '🚫 Filtering out likely Chinese artist from hip hop: ${track.artist}');
          return false;
        }
      }

      // Rock validation
      else if (genreLower.contains('rock') ||
          genreLower.contains('metal') ||
          genreLower.contains('punk')) {
        // Check if this is a search for international rock genres or if tracks come from international rock playlists
        final isInternationalRockSearch = genreLower.contains('j-rock') ||
            genreLower.contains('jrock') ||
            genreLower.contains('k-rock') ||
            genreLower.contains('krock') ||
            genreLower.contains('japanese rock') ||
            genreLower.contains('korean rock') ||
            genreLower.contains('asian rock') ||
            genreLower.contains('international rock');

        // Check if tracks come from a playlist that indicates international rock
        final isFromInternationalRockPlaylist = isFromPlaylist &&
            playlistName != null &&
            (playlistName.toLowerCase().contains('j-rock') ||
                playlistName.toLowerCase().contains('jrock') ||
                playlistName.toLowerCase().contains('japanese rock') ||
                playlistName.toLowerCase().contains('japan rock') ||
                playlistName.toLowerCase().contains('k-rock') ||
                playlistName.toLowerCase().contains('krock') ||
                playlistName.toLowerCase().contains('korean rock') ||
                playlistName.toLowerCase().contains('korea rock') ||
                playlistName.toLowerCase().contains('asian rock') ||
                playlistName.toLowerCase().contains('international rock'));

        // Be more permissive if this is international rock or from international rock playlists
        final shouldUseInternationalLogic =
            isInternationalRockSearch || isFromInternationalRockPlaylist;

        // Log context for debugging
        if (isFromInternationalRockPlaylist) {
          print(
              '🎸 [Rock Validation] Track "${track.title}" by ${track.artist} from international rock playlist "$playlistName" - using permissive validation');
        }

        // For international rock searches or international rock playlists, be more permissive with Asian characters
        if (!shouldUseInternationalLogic) {
          // Standard Western rock validation - exclude Asian pop from rock searches
          if (_containsChineseCharacters(track.artist) ||
              _containsChineseCharacters(track.title)) {
            // Only allow if it's explicitly rock-related
            if (!titleLower.contains('rock') &&
                !artistLower.contains('rock') &&
                !trackGenres.any((tg) => tg.contains('rock'))) {
              print(
                  '🚫 Filtering out Asian track from Western rock search: "${track.title}" by ${track.artist}');
              return false;
            }
          }

          // For Japanese characters, be more permissive if the track has rock genres
          if (_containsJapaneseCharacters(track.artist) ||
              _containsJapaneseCharacters(track.title)) {
            // Allow if it has rock-related genres (j-rock, rock, etc.) or explicit rock indicators
            if (!titleLower.contains('rock') &&
                !artistLower.contains('rock') &&
                !trackGenres.any((tg) => tg.contains('rock')) &&
                !trackGenres.contains('j-rock')) {
              print(
                  '🚫 Filtering out Japanese track from Western rock search: "${track.title}" by ${track.artist}');
              return false;
            }
          }
        }

        if (trackGenres.isNotEmpty) {
          final nonRockGenres = [
            'c-pop',
            'k-pop',
            'j-pop',
            'chinese pop',
            'korean pop',
            'japanese pop',
            'hip hop',
            'rap',
            'r&b',
            'country',
            'classical'
          ];

          // For international rock searches or international rock playlists, allow j-rock tracks even if they also have j-pop genre
          if (shouldUseInternationalLogic) {
            // More lenient filtering - only exclude if it's clearly non-rock
            final strictNonRockGenres = [
              'c-pop',
              'k-pop',
              'hip hop',
              'rap',
              'r&b',
              'country',
              'classical'
            ];
            if (trackGenres.any((tg) =>
                    strictNonRockGenres.any((nrg) => tg.contains(nrg))) &&
                !trackGenres.any((tg) => tg.contains('rock'))) {
              print(
                  '🚫 Filtering out non-rock genre track from international rock search: "${track.title}" by ${track.artist} (Genres: ${track.genres})');
              return false;
            }
          } else {
            // Standard Western rock filtering
            if (trackGenres
                .any((tg) => nonRockGenres.any((nrg) => tg.contains(nrg)))) {
              print(
                  '🚫 Filtering out non-rock genre track from Western rock search: "${track.title}" by ${track.artist} (Genres: ${track.genres})');
              return false;
            }
          }
        }
      }

      // Pop validation (but be less strict since pop is broad)
      else if (genreLower.contains('pop') &&
          !genreLower.contains('c-pop') &&
          !genreLower.contains('k-pop') &&
          !genreLower.contains('j-pop')) {
        // For Western pop, exclude Asian pop unless specifically requested
        if (trackGenres.isNotEmpty) {
          final asianPopGenres = [
            'c-pop',
            'k-pop',
            'j-pop',
            'chinese',
            'korean',
            'japanese'
          ];
          if (trackGenres
              .any((tg) => asianPopGenres.any((apg) => tg.contains(apg)))) {
            print(
                '🚫 Filtering out Asian pop from Western pop search: "${track.title}" by ${track.artist}');
            return false;
          }
        }
      }

      // Country validation
      else if (genreLower.contains('country') ||
          genreLower.contains('bluegrass') ||
          genreLower.contains('americana')) {
        // Exclude Asian tracks from country searches
        if (_containsChineseCharacters(track.artist) ||
            _containsChineseCharacters(track.title) ||
            _containsJapaneseCharacters(track.artist) ||
            _containsJapaneseCharacters(track.title)) {
          print(
              '🚫 Filtering out Asian track from country search: "${track.title}" by ${track.artist}');
          return false;
        }
      }

      // Electronic/EDM validation
      else if (genreLower.contains('electronic') ||
          genreLower.contains('edm') ||
          genreLower.contains('house') ||
          genreLower.contains('techno')) {
        // Allow most electronic music but exclude obvious Asian pop
        if (trackGenres.isNotEmpty) {
          final asianPopGenres = ['c-pop', 'k-pop', 'j-pop'];
          if (trackGenres.any((tg) => asianPopGenres.contains(tg))) {
            print(
                '🚫 Filtering out Asian pop from electronic search: "${track.title}" by ${track.artist}');
            return false;
          }
        }
      }

      return true;
    }).toList();
  }

  /// Check if text contains Chinese characters
  bool _containsChineseCharacters(String text) {
    // Check for common Chinese character ranges
    return RegExp(r'[\u4e00-\u9fa5\u3400-\u4dbf]').hasMatch(text);
  }

  /// Check if text contains Japanese characters (Kanji, Hiragana, Katakana)
  bool _containsJapaneseCharacters(String text) {
    // Check for common Japanese character ranges: CJK Unified Ideographs, Hiragana, Katakana
    return RegExp(r'[\u4e00-\u9faf\u3040-\u309f\u30a0-\u30ff]').hasMatch(text);
  }

  /// Check if an artist name is likely Chinese/Taiwanese
  bool _isLikelyChineseArtist(String artist) {
    if (artist.trim().isEmpty) return false;

    final artistLower = artist.toLowerCase();

    // Fast-path: if the string actually contains Chinese characters we can immediately accept.
    if (_containsChineseCharacters(artistLower)) return true;

    // Whitelist of known Western hip hop/rap artists to prevent false positives
    const knownWesternArtists = {
      'iann dior',
      'polo g',
      'k-trap',
      'lil nas',
      'lil baby',
      'lil wayne',
      'lil uzi',
      'lil durk',
      'lil tjay',
      'lil pump',
      'lil skies',
      'lil yachty',
      'lil peep',
      'big sean',
      'young thug',
      'future',
      'travis scott',
      'post malone',
      'juice wrld',
      'xxxtentacion',
      'ski mask',
      'denzel curry',
      'jid',
      'earl sweatshirt',
      'tyler the creator',
      'frank ocean',
      'brockhampton',
      'vince staples',
      'mac miller',
      'kid cudi',
      'chance the rapper',
      'childish gambino',
      'kendrick lamar',
      'j cole',
      'joey badass',
      'capital steez',
      'pro era',
      'flatbush zombies',
      'beast coast',
      'cordae',
      'dababy',
      'lil loaded',
      'pop smoke',
      'fivio foreign',
      'sleepy hallow',
      'sheff g',
      'toosii',
      'rod wave',
      'nf',
      'logic',
      'joyner lucas',
      'hopsin',
      'token',
      'dax',
      'tech n9ne',
      'eminem',
      'dr dre',
      'snoop dogg',
      'ice cube',
      'kendrick',
      'drake',
      'kanye west',
      'jay z',
      '2pac',
      'biggie',
      'nas',
      '50 cent',
      'ludacris',
      'outkast',
      'andre 3000',
      'big boi',
      'lil jon',
      'ying yang twins',
      'three 6 mafia',
      'bone thugs',
      'nelly',
      'chingy',
      'st lunatics',
      'murphy lee',
      'ali',
      'kno',
      'cunninlynguists',
      'atmosphere',
      'brother ali',
      'slug',
      'aesop rock',
      'el-p',
      'killer mike',
      'run the jewels',
      'danny brown',
      'big krit',
      'freddie gibbs',
      'pusha t',
      'clipse',
      'pharrell',
      'n e r d',
      'the neptunes',
      'timbaland',
      'missy elliott',
      'busta rhymes',
      'q tip',
      'tribe called quest',
      'de la soul',
      'jungle brothers',
      'black sheep',
      'leaders of the new school',
      'public enemy',
      'nwa',
      'eazy e',
      'mc ren',
      'dj yella',
      'the d o c',
      'above the law',
      'cypress hill',
      'house of pain',
      'naughty by nature',
      'redman',
      'method man',
      'wu tang',
      'ol dirty bastard',
      'ghostface killah',
      'raekwon',
      'gza',
      'rza',
      'inspectah deck',
      'u god',
      'masta killa',
      'cappadonna',
      'street life',
      'shyheim',
      'sunz of man',
      'killarmy',
      'gravediggaz',
      'kool g rap',
      'big daddy kane',
      'rakim',
      'eric b',
      'gang starr',
      'dj premier',
      'guru',
      'pete rock',
      'cl smooth',
      'epmd',
      'erick sermon',
      'pmd',
      'das efx',
      'brand nubian',
      'lord jamar',
      'sadat x',
      'grand puba',
      'digable planets',
      'us3',
      'jazz liberatorz',
      'nujabes',
      'j dilla',
      'madlib',
      'mf doom',
      'kmd',
      'king geedorah',
      'viktor vaughn',
      'metal fingers',
      'quasimoto',
      'yesterday new quintet',
      'beat konducta',
      'jaylib',
      'slum village',
      'frank n dank',
      'phat kat',
    };

    // Check if this is a known Western artist (exact match)
    if (knownWesternArtists.contains(artistLower)) {
      return false;
    }

    // Explicit Chinese artist patterns (exact matches only)
    final knownChinesePatterns = [
      'jay chou',
      '周杰伦',
      'eason chan',
      '陈奕迅',
      'jj lin',
      '林俊杰',
      'g.e.m.',
      '邓紫棋',
      'faye wong',
      '王菲',
      'teresa teng',
      '邓丽君',
      'jacky cheung',
      '张学友',
      'andy lau',
      '刘德华',
      'mayday',
      '五月天',
      'leehom wang',
      '王力宏',
      'david tao',
      '陶喆',
      's.h.e',
      'twins',
      'joey yung',
      'karen mok',
      'sandy lam',
      'coco lee',
      'fish leong',
      'jolin tsai',
      '蔡依林',
      'hebe tien',
      'wanting',
      'phil lam',
      'ronghao li',
      '李荣浩',
      'joker xue',
      '薛之谦',
      'a-lin',
      'f.i.r.',
      'mc hotdog',
      '热狗',
      'sodagreen',
      '苏打绿',
      'ashin',
      '阿信',
      'beyond',
      'alan tam',
      '谭咏麟',
      'leslie cheung',
      '张国荣',
      'anita mui',
      '梅艳芳',
      'wakin chau',
      '周华健',
      'jeff chang',
      '张信哲',
      'richie jen',
      '任贤齐',
      'nicholas tse',
      '谢霆锋',
      'daniel chan',
      '陈晓东',
      'ekin cheng',
      '郑伊健',
      'hins cheung',
      '张敬轩',
      'kay tse',
      '谢安琪',
      'ivana wong',
      '王菀之',
      'at17',
      'my little airport',
      'da mouth',
      '大嘴巴',
      'show lo',
      '罗志祥',
      'rainie yang',
      '杨丞琳',
      'a-mei',
      '张惠妹',
      'fish leong',
      '梁静茹',
      'stefanie sun',
      '孙燕姿',
      'karen mok',
      '莫文蔚',
      'coco lee',
      '李玟',
      'sandy lam',
      '林忆莲',
      'priscilla chan',
      '陈慧娴',
    ];

    // Check for exact Chinese artist patterns
    if (knownChinesePatterns.contains(artistLower)) {
      return true;
    }

    // VERY conservative surname matching - only for very specific patterns
    final words = artistLower
        .split(RegExp(r'[^a-z]+'))
        .where((w) => w.isNotEmpty)
        .toList();

    // Only flag if it's exactly two words where BOTH are common Chinese surnames/given names
    if (words.length == 2) {
      final chineseSurnames = {
        'wang',
        'zhang',
        'chen',
        'liu',
        'yang',
        'huang',
        'zhao',
        'wu',
        'zhou',
        'xu'
      };
      final chineseGivenNames = {
        'wei',
        'ming',
        'jun',
        'jie',
        'lei',
        'tao',
        'bin',
        'qiang',
        'peng'
      };

      // Only flag if first word is a surname AND second word is a given name
      if (chineseSurnames.contains(words.first) &&
          chineseGivenNames.contains(words.last)) {
        return true;
      }
    }

    return false;
  }

  // Search for playlists
  Future<List<Map<String, dynamic>>> searchPlaylists(String query,
      {int limit = 20, int offset = 0}) async {
    try {
      if (query.isEmpty) {
        return [];
      }

      print(
          '🔍 [SpotifyService] Searching for playlists: "$query" (limit: $limit, offset: $offset)');

      // Proceed even if the user is not connected – backend client-credentials fallback will handle public catalogue access.

      // Build cache key
      final cacheKey = 'playlists:$query|$limit|$offset';

      // If using development tokens, use the backend API endpoint
      if (kDebugMode &&
          (await getAccessToken())?.startsWith('dev_token_') == true) {
        print(
            '🔬 [SpotifyService] Using backend API for playlist search in development');

        // For development, we'll simulate playlist search results
        return [
          {
            'id': 'dev_playlist_1',
            'name': '$query Hits',
            'description': 'Top hits for $query',
            'tracks': {'total': 50},
            'images': [
              {'url': 'https://via.placeholder.com/300x300'}
            ],
            'owner': {'display_name': 'Spotify'},
            'external_urls': {
              'spotify': 'https://spotify.com/playlist/dev_playlist_1'
            },
            'public': true,
            'snapshot_id': 'dev_snapshot_1',
          },
          {
            'id': 'dev_playlist_2',
            'name': 'Best of $query',
            'description': 'Curated $query playlist',
            'tracks': {'total': 30},
            'images': [
              {'url': 'https://via.placeholder.com/300x300'}
            ],
            'owner': {'display_name': 'Spotify'},
            'external_urls': {
              'spotify': 'https://spotify.com/playlist/dev_playlist_2'
            },
            'public': true,
            'snapshot_id': 'dev_snapshot_2',
          },
        ];
      }

      // Use the actual Spotify API for production
      final data = await _makeAuthenticatedRequest(
        '/search',
        queryParams: {
          'q': query,
          'type': 'playlist',
          'limit': limit.toString(),
          if (offset > 0) 'offset': offset.toString(),
        },
      );

      if (data == null) {
        print('⚠️ [SpotifyService] No data returned from playlist search');
        return [];
      }

      // Debug: Print the response structure to understand what we're getting
      print(
          '🔍 [SpotifyService] Playlist search response keys: ${data.keys.toList()}');

      // Try different possible response structures
      List<dynamic> items = [];
      if (data.containsKey('playlists')) {
        // Standard Spotify API format
        final playlists = data['playlists'];
        if (playlists != null &&
            playlists is Map &&
            playlists.containsKey('items')) {
          items = (playlists['items'] as List?) ?? [];
        }
      } else if (data.containsKey('items')) {
        // Backend might return items directly
        items = (data['items'] as List?) ?? [];
      }

      if (items.isEmpty) {
        print(
            '⚠️ [SpotifyService] No playlist items found in response structure');
        return [];
      }

      print(
          '✓ [SpotifyService] Got ${items.length} playlist results for "$query"');

      return items
          .map<Map<String, dynamic>>((playlist) {
            // Add null safety for all playlist fields
            if (playlist == null) return <String, dynamic>{};

            return {
              'id': playlist['id'] ?? '',
              'name': playlist['name'] ?? 'Unknown Playlist',
              'description': playlist['description'] ?? '',
              'tracks': playlist['tracks'] ?? {'total': 0},
              'images': playlist['images'] ?? [],
              'owner': playlist['owner'] ?? {'display_name': 'Unknown'},
              'external_urls': playlist['external_urls'] ?? {'spotify': ''},
              'public': playlist['public'] ?? false,
              'snapshot_id': playlist['snapshot_id'] ?? '',
            };
          })
          .where((playlist) => playlist['id']?.isNotEmpty == true)
          .toList();
    } catch (e) {
      print('❌ [SpotifyService] Error searching playlists: $e');
      return [];
    }
  }

  // Enhanced search with multiple types and filters
  Future<Map<String, dynamic>> searchMultipleTypes(
    String query, {
    List<String> types = const ['track'],
    int limit = 20,
    String? market,
  }) async {
    try {
      if (query.isEmpty) {
        return {};
      }

      final typeString = types.join(',');
      print(
          '🔍 [SpotifyService] Multi-type search: "$query" (types: $typeString, limit: $limit)');

      // Proceed even if the user is not connected – backend client-credentials fallback will handle public catalogue access.

      final queryParams = {
        'q': query,
        'type': typeString,
        'limit': limit.toString(),
      };

      if (market != null) {
        queryParams['market'] = market;
      }

      // Use the actual Spotify API
      final data = await _makeAuthenticatedRequest(
        '/search',
        queryParams: queryParams,
      );

      if (data == null) {
        return {};
      }

      print('✓ [SpotifyService] Got multi-type search results for "$query"');
      return data;
    } catch (e) {
      print('❌ [SpotifyService] Error in multi-type search: $e');
      return {};
    }
  }

  // Search for tracks inspired by multiple artists (since recommendations API is deprecated)
  Future<List<MusicTrack>> searchTracksInspiredByArtists(
    List<String> artistNames, {
    String? genre,
    int limit = 20,
    int? year,
  }) async {
    // Enhanced logging for AI tracking
    final timestamp = DateTime.now().toIso8601String();
    final artistList =
        artistNames.take(5).join(', ') + (artistNames.length > 5 ? '...' : '');
    final filters = [
      if (genre != null) 'GENRE: $genre',
      if (year != null) 'YEAR: $year',
    ].join(' | ');
    final filterInfo = filters.isNotEmpty ? ' [FILTERS: $filters]' : '';

    try {
      if (artistNames.isEmpty) {
        return [];
      }

      print(
          '🎤 [SPOTIFY AI SEARCH ARTISTS] [$timestamp] ARTISTS: [$artistList] | LIMIT: $limit$filterInfo');

      final allTracks = <MusicTrack>[];

      // Strategy 1: Use "similar to" queries for each artist
      for (final artist in artistNames.take(5)) {
        // Increased from 3 to 5 artists
        try {
          String query = 'similar to "$artist"';
          if (genre != null) {
            query = 'genre:"$genre" similar to "$artist"';
          }
          if (year != null) {
            query += ' year:$year';
          }

          final tracks = await searchTracks(query,
              limit: math.max(limit ~/ artistNames.length, 15),
              context: 'AI_ARTIST_INSPIRED'); // Ensure at least 15 per artist
          allTracks.addAll(tracks);
          print(
              '📥 [SPOTIFY AI SEARCH ARTISTS] Got ${tracks.length} tracks similar to "$artist"');
        } catch (e) {
          print(
              '❌ [SPOTIFY AI SEARCH ARTISTS] Error getting tracks similar to "$artist": $e');
        }
      }

      // Strategy 2: Search for tracks that mention multiple artists
      if (artistNames.length >= 2) {
        try {
          final artistPairs = <String>[];
          for (int i = 0; i < artistNames.length - 1; i++) {
            artistPairs.add('"${artistNames[i]}" "${artistNames[i + 1]}"');
          }

          for (final pair in artistPairs.take(3)) {
            // Increased from 2 to 3
            String query = 'fans of $pair';
            if (genre != null) {
              query = 'genre:"$genre" fans of $pair';
            }

            final tracks = await searchTracks(query,
                limit: 10,
                context: 'AI_ARTIST_COMBO'); // Increased from 5 to 10
            allTracks.addAll(tracks);
            print(
                '📥 [SPOTIFY AI SEARCH ARTISTS] Got ${tracks.length} tracks for fans of $pair');
          }
        } catch (e) {
          print(
              '❌ [SPOTIFY AI SEARCH ARTISTS] Error getting tracks for artist combinations: $e');
        }
      }

      // Strategy 3: Genre-based search if specified
      if (genre != null) {
        try {
          final genreTracks = await searchTracksByGenre(genre,
              limit: math.max(limit ~/ 2, 25),
              year: year); // Increased from limit ~/ 3 to limit ~/ 2, min 25
          allTracks.addAll(genreTracks);
          print(
              '📥 [SPOTIFY AI SEARCH ARTISTS] Got ${genreTracks.length} additional genre tracks');
        } catch (e) {
          print(
              '❌ [SPOTIFY AI SEARCH ARTISTS] Error getting additional genre tracks: $e');
        }
      }

      // Remove duplicates and mix by artist
      final seenIds = <String>{};
      final uniqueTracks = allTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      // Mix tracks by artist to avoid clustering
      final mixedTracks = _mixTracksByArtist(uniqueTracks);

      print(
          '✅ [SPOTIFY AI SEARCH ARTISTS] FINAL SUCCESS: ${mixedTracks.length} mixed tracks inspired by ${artistNames.length} artists$filterInfo');

      // Log to AI analytics system
      AIRecommendationService.logSearchActivity(
        query: artistList,
        searchType: 'artists',
        context: 'AI_ARTIST_INSPIRED',
        genre: genre,
        resultsCount: mixedTracks.length,
        limit: limit,
        isSuccess: true,
      );

      return mixedTracks.take(limit).toList();
    } catch (e) {
      print(
          '❌ [SPOTIFY AI SEARCH ARTISTS] FINAL ERROR: Failed for artists [$artistList]$filterInfo - $e');

      // Log error to AI analytics system
      AIRecommendationService.logSearchActivity(
        query: artistList,
        searchType: 'artists',
        context: 'AI_ARTIST_INSPIRED',
        genre: genre,
        limit: limit,
        isSuccess: false,
        errorMessage: e.toString(),
      );

      return [];
    }
  }

  // Search using artist combinations and genre constraints
  Future<List<MusicTrack>> searchWithArtistSeeds(
    List<String> seedArtists, {
    String? targetGenre,
    int limit = 20,
    List<String>? excludeArtists,
  }) async {
    try {
      final allTracks = <MusicTrack>[];

      // Build queries that combine artists with genre
      final queries = <String>[];

      // Single artist inspiration queries
      for (final artist in seedArtists.take(3)) {
        if (targetGenre != null) {
          queries.addAll([
            'genre:"$targetGenre" inspired by "$artist"',
            'genre:"$targetGenre" similar to "$artist"',
            'genre:"$targetGenre" fans of "$artist"',
            'genre:"$targetGenre" like "$artist"',
          ]);
        } else {
          queries.addAll([
            'inspired by "$artist"',
            'similar to "$artist"',
            'fans of "$artist"',
            'like "$artist"',
          ]);
        }
      }

      // Multi-artist combination queries
      if (seedArtists.length >= 2) {
        final artistCombos = [
          '${seedArtists[0]} ${seedArtists[1]}',
          if (seedArtists.length >= 3) '${seedArtists[0]} ${seedArtists[2]}',
        ];

        for (final combo in artistCombos) {
          if (targetGenre != null) {
            queries.addAll([
              'genre:"$targetGenre" fans of $combo',
              'genre:"$targetGenre" similar to $combo',
            ]);
          } else {
            queries.addAll([
              'fans of $combo',
              'similar to $combo',
            ]);
          }
        }
      }

      // Execute queries
      final maxQueries = 6; // Limit to avoid rate limits
      for (int i = 0; i < math.min(queries.length, maxQueries); i++) {
        try {
          final tracks =
              await searchTracks(queries[i], limit: limit ~/ maxQueries + 2);

          // Filter out excluded artists if specified
          final filteredTracks = excludeArtists != null
              ? tracks
                  .where((track) => !excludeArtists.any((excluded) => track
                      .artist
                      .toLowerCase()
                      .contains(excluded.toLowerCase())))
                  .toList()
              : tracks;

          allTracks.addAll(filteredTracks);
          print('📥 Got ${filteredTracks.length} tracks for "${queries[i]}"');
        } catch (e) {
          print('❌ Error with query "${queries[i]}": $e');
        }
      }

      // Deduplicate and return
      final seenIds = <String>{};
      final uniqueTracks = allTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      return uniqueTracks.take(limit).toList();
    } catch (e) {
      print('❌ [SpotifyService] Error in artist seed search: $e');
      return [];
    }
  }

  // Get top tracks for a specific artist by name
  Future<List<MusicTrack>> getTopTracksByArtist(String artistName,
      {int limit = 10}) async {
    try {
      if (artistName.isEmpty) return [];

      print('🎵 [SpotifyService] Getting top tracks for artist: $artistName');

      // Proceed even if the user is not connected – backend client-credentials fallback will handle public catalogue access.

      // Search for the artist's top tracks using multiple query strategies
      final queries = [
        'artist:"$artistName" popular',
        'artist:"$artistName" top',
        'artist:"$artistName" hits',
        'artist:"$artistName"',
      ];

      final allTracks = <MusicTrack>[];

      for (final query in queries.take(3)) {
        // Increased from 2 to 3 queries
        try {
          final tracks = await searchTracks(query,
              limit:
                  math.max(limit ~/ 2 + 5, 20)); // Increased limit and minimum

          // Filter to ensure tracks are actually by the specified artist
          final artistTracks = tracks.where((track) {
            return track.artist
                    .toLowerCase()
                    .contains(artistName.toLowerCase()) ||
                artistName.toLowerCase().contains(track.artist.toLowerCase());
          }).toList();

          allTracks.addAll(artistTracks);
        } catch (e) {
          print('❌ [SpotifyService] Error with query "$query": $e');
        }
      }

      // Remove duplicates and sort by popularity
      final seenIds = <String>{};
      final uniqueTracks = allTracks.where((track) {
        if (seenIds.contains(track.id)) return false;
        seenIds.add(track.id);
        return true;
      }).toList();

      // Sort by popularity (highest first)
      uniqueTracks
          .sort((a, b) => (b.popularity ?? 50).compareTo(a.popularity ?? 50));

      print(
          '✅ [SpotifyService] Found ${uniqueTracks.length} top tracks for $artistName, sorted by popularity');
      return uniqueTracks.take(limit).toList();
    } catch (e) {
      print(
          '❌ [SpotifyService] Error getting top tracks for artist $artistName: $e');
      return [];
    }
  }

  // Add getUserPlaylistTracks method
  Future<List<MusicTrack>> getUserPlaylistTracks() async {
    try {
      // First get user's playlists
      final playlistsData = await _makeAuthenticatedRequest('/me/playlists');
      if (playlistsData == null) return [];

      final List<MusicTrack> allTracks = [];
      final playlists = playlistsData['items'] as List;

      // For each playlist, get its tracks
      for (final playlist in playlists) {
        final playlistId = playlist['id'];
        final tracksData =
            await _makeAuthenticatedRequest('/playlists/$playlistId/tracks');

        if (tracksData != null) {
          final tracks = (tracksData['items'] as List)
              .map((item) {
                final track = item['track'];
                if (track == null) return null;

                String albumArt = '';
                if (track['album'] != null &&
                    track['album']['images'] != null &&
                    track['album']['images'].isNotEmpty) {
                  albumArt = track['album']['images'][0]['url'];
                }

                // Extract ISRC from external_ids
                String? isrc;
                if (track['external_ids'] != null &&
                    track['external_ids']['isrc'] != null) {
                  isrc = track['external_ids']['isrc'];
                }

                return MusicTrack(
                  id: track['id'] ?? '',
                  title: track['name'] ?? 'Unknown Track',
                  artist: (track['artists'] as List)
                      .map((a) => a['name'])
                      .join(', '),
                  albumArt: albumArt,
                  url: track['external_urls']?['spotify'] ?? '',
                  service: 'spotify',
                  serviceType: 'spotify',
                  uri: track['uri'] ?? 'spotify:track:${track['id']}',
                  durationMs: track['duration_ms'] ?? 0,
                  genres: [],
                  explicit: track['explicit'] ?? false,
                  popularity: track['popularity'] ?? 50,
                  isrc: isrc,
                );
              })
              .whereType<MusicTrack>()
              .toList(); // Filter out any null tracks

          allTracks.addAll(tracks);
        }
      }

      return allTracks;
    } catch (e) {
      print('Error getting playlist tracks: $e');
      return [];
    }
  }

  /// Mix tracks by artist to avoid having the same artist in consecutive positions
  List<MusicTrack> _mixTracksByArtist(List<MusicTrack> tracks) {
    if (tracks.length <= 2) return tracks;

    // Group tracks by artist
    final Map<String, List<MusicTrack>> tracksByArtist = {};
    for (final track in tracks) {
      final artistKey = track.artist.toLowerCase();
      tracksByArtist[artistKey] = (tracksByArtist[artistKey] ?? [])..add(track);
    }

    // If all tracks are by different artists, just shuffle and return
    if (tracksByArtist.length == tracks.length) {
      final shuffled = List<MusicTrack>.from(tracks);
      shuffled.shuffle();
      return shuffled;
    }

    // Create a mixed list by distributing tracks from different artists
    final List<MusicTrack> mixedTracks = [];
    final List<List<MusicTrack>> artistTrackLists =
        tracksByArtist.values.toList();

    // Shuffle each artist's tracks internally
    for (final artistTracks in artistTrackLists) {
      artistTracks.shuffle();
    }

    // Sort artist lists by size (largest first) to better distribute
    artistTrackLists.sort((a, b) => b.length.compareTo(a.length));

    // Round-robin through artists to mix tracks
    int maxLength =
        artistTrackLists.isNotEmpty ? artistTrackLists.first.length : 0;
    for (int i = 0; i < maxLength; i++) {
      for (final artistTracks in artistTrackLists) {
        if (i < artistTracks.length) {
          mixedTracks.add(artistTracks[i]);
        }
      }
    }

    return mixedTracks;
  }

  /// Get the appropriate market code for a given genre
  String? _getMarketForGenre(String genre) {
    // Map genres to appropriate markets for better regional content
    final genreLower = genre.toLowerCase();

    // Chinese genres
    if (genreLower.contains('c-pop') ||
        genreLower.contains('cantopop') ||
        genreLower.contains('mandopop') ||
        genreLower.contains('chinese') ||
        genreLower.contains('cantonese')) {
      return 'HK'; // Hong Kong (best for C-pop/Cantopop availability)
    }

    // Korean genres
    if (genreLower.contains('k-pop') ||
        genreLower.contains('korean') ||
        genreLower == 'k-indie' ||
        genreLower == 'k-rock') {
      return 'KR'; // South Korea
    }

    // Japanese genres
    if (genreLower.contains('j-pop') ||
        genreLower.contains('japanese') ||
        genreLower == 'j-rock' ||
        genreLower == 'anime' ||
        genreLower.contains('visual kei')) {
      return 'JP'; // Japan
    }

    // Latin genres
    if (genreLower.contains('reggaeton') ||
        genreLower.contains('latin') ||
        genreLower.contains('salsa') ||
        genreLower.contains('bachata') ||
        genreLower.contains('merengue')) {
      return 'MX'; // Mexico (good Latin music availability)
    }

    // Brazilian genres
    if (genreLower.contains('mpb') ||
        genreLower.contains('samba') ||
        genreLower.contains('bossa nova') ||
        genreLower.contains('brazilian') ||
        genreLower.contains('funk carioca')) {
      return 'BR'; // Brazil
    }

    // Indian genres
    if (genreLower.contains('bollywood') ||
        genreLower.contains('indian') ||
        genreLower.contains('bhangra') ||
        genreLower.contains('carnatic')) {
      return 'IN'; // India
    }

    // Turkish genres
    if (genreLower.contains('turkish') || genreLower.contains('arabesque')) {
      return 'TR'; // Turkey
    }

    // French genres
    if (genreLower.contains('french') || genreLower.contains('chanson')) {
      return 'FR'; // France
    }

    // Spanish genres
    if (genreLower.contains('flamenco') || genreLower.contains('spanish')) {
      return 'ES'; // Spain
    }

    // Default to null (use user's market)
    return null;
  }

  // Search for artists using Spotify's search API
  Future<List<Map<String, dynamic>>> searchArtists(String query,
      {int limit = 20, int offset = 0}) async {
    try {
      print(
          '🎤 [SpotifyService] Searching for artists: "$query" (limit: $limit, offset: $offset)');

      // Proceed even if the user is not connected – backend client-credentials fallback will handle public catalogue access.

      final sanitizedQuery = _sanitizeSearchQuery(query);

      final data = await _makeAuthenticatedRequest(
        '/search',
        queryParams: {
          'q': sanitizedQuery,
          'type': 'artist',
          'limit': limit.toString(),
          if (offset > 0) 'offset': offset.toString(),
        },
      );

      if (data == null || data['artists'] == null) {
        print(
            '⚠️ [SpotifyService] No artist search results for query: "$query"');
        return [];
      }

      final List artists = data['artists']['items'] ?? [];

      final results = artists
          .map<Map<String, dynamic>>((artist) => {
                'id': artist['id'] ?? '',
                'name': artist['name'] ?? 'Unknown Artist',
                'image_url': artist['images']?.isNotEmpty == true
                    ? artist['images'][0]['url']
                    : null,
                'genres': List<String>.from(artist['genres'] ?? []),
                'popularity': artist['popularity'] ?? 50,
                'url': artist['external_urls']?['spotify'] ?? '',
                'followers': artist['followers']?['total'] ?? 0,
              })
          .toList();

      print(
          '✅ [SpotifyService] Found ${results.length} artists for query: "$query"');

      return results;
    } catch (e) {
      print(
          '❌ [SpotifyService] Error searching artists for query "$query": $e');
      return [];
    }
  }

  // Search for artists by genre using Spotify's search API
  Future<List<Map<String, dynamic>>> searchArtistsByGenre(String genre,
      {int limit = 20}) async {
    try {
      print(
          '🎤 [SpotifyService] Searching for artists by genre: $genre (limit: $limit)');

      // Proceed even if the user is not connected – backend client-credentials fallback will handle public catalogue access.

      // Get genre aliases for better search results
      final genreAliases = SpotifyGenreService.getGenreAliases(genre);
      print(
          '🎯 [SpotifyService] Using ${genreAliases.length} aliases for "$genre": ${genreAliases.join(", ")}');

      final allArtists = <Map<String, dynamic>>[];
      final seenArtistIds = <String>{};

      // Search for artists using each genre alias
      for (final alias in genreAliases.take(3)) {
        // Use up to 3 aliases to avoid too many requests
        try {
          // Build search query for artists in this genre
          final query = 'genre:"$alias"';

          print('🔍 [SpotifyService] Searching artists with query: "$query"');

          final data = await _makeAuthenticatedRequest(
            '/search',
            queryParams: {
              'q': query,
              'type': 'artist',
              'limit': '50', // Get more artists per search to have variety
            },
          );

          if (data != null && data['artists'] != null) {
            final List artists = data['artists']['items'] ?? [];

            for (final artist in artists) {
              final artistId = artist['id'] as String?;
              if (artistId != null && !seenArtistIds.contains(artistId)) {
                seenArtistIds.add(artistId);

                // Check if this artist actually has the genre we're looking for
                final artistGenres = List<String>.from(artist['genres'] ?? []);
                final hasTargetGenre = artistGenres.any((g) =>
                    g.toLowerCase().contains(genre.toLowerCase()) ||
                    genreAliases.any((alias) =>
                        g.toLowerCase().contains(alias.toLowerCase())));

                if (hasTargetGenre) {
                  allArtists.add({
                    'id': artistId,
                    'name': artist['name'] ?? 'Unknown Artist',
                    'image_url': artist['images']?.isNotEmpty == true
                        ? artist['images'][0]['url']
                        : null,
                    'genres': artistGenres,
                    'popularity': artist['popularity'] ?? 50,
                    'url': artist['external_urls']?['spotify'] ?? '',
                    'followers': artist['followers']?['total'] ?? 0,
                  });
                }
              }
            }

            print(
                '✅ [SpotifyService] Found ${artists.length} artists for genre alias "$alias"');
          }
        } catch (e) {
          print(
              '❌ [SpotifyService] Error searching artists for genre alias "$alias": $e');
        }
      }

      // Sort by popularity and limit results
      allArtists.sort(
          (a, b) => (b['popularity'] as int).compareTo(a['popularity'] as int));

      final limitedArtists = allArtists.take(limit).toList();
      print(
          '🎵 [SpotifyService] Returning ${limitedArtists.length} artists for genre "$genre"');

      return limitedArtists;
    } catch (e) {
      print('❌ [SpotifyService] Error searching artists by genre "$genre": $e');
      return [];
    }
  }

  /// Get details for a single artist by their name.
  Future<Map<String, dynamic>?> getArtist(String artistName) async {
    try {
      // Use a more specific search query to improve accuracy
      final response = await _makeAuthenticatedRequest(
        'search',
        queryParams: {
          'q': artistName,
          'type': 'artist',
          'limit': '5',
        },
      );
      print('🔍 [SpotifyService] Artist search response: $response');
      if (response != null &&
          response['artists'] != null &&
          response['artists']['items'].isNotEmpty) {
        return response['artists']['items'][0] as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      print('Error getting artist details for $artistName: $e');
      return null;
    }
  }

  /// Filter a list of artist names by a target genre.
  Future<List<String>> filterArtistsByGenre(
      List<String> artistNames, String targetGenre) async {
    final validArtists = <String>[];
    for (final artistName in artistNames) {
      final artistDetails = await getArtist(artistName);
      if (artistDetails != null) {
        final artistGenres = List<String>.from(artistDetails['genres'] ?? []);
        if (SpotifyGenreService.isValidArtistForTargetGenre(
            artistGenres, targetGenre)) {
          validArtists.add(artistName);
        }
      }
      // Add a small delay to avoid hitting rate limits too quickly
      await Future.delayed(const Duration(milliseconds: 50));
    }
    return validArtists;
  }

  // // Paginated search for artists by genre with progressive loading
  // Future<Map<String, dynamic>> searchArtistsByGenrePaginated(String genre,
  //     {int limit = 20, int offset = 0}) async {
  //   try {
  //     print(
  //         '🎤 [SpotifyService] Paginated search for artists by genre: "$genre" (limit: $limit, offset: $offset)');

  //     final queries = [
  //       'genre:"$genre"',
  //       '$genre music',
  //       '$genre artists',
  //     ];

  //     final allArtists = <Map<String, dynamic>>[];
  //     final seenIds = <String>{};

  //     for (final query in queries) {
  //       try {
  //         final results = await searchArtists(query,
  //             limit: limit ~/ queries.length + 5, offset: offset);

  //         // Filter and add unique artists
  //         for (final artist in results) {
  //           if (!seenIds.contains(artist['id']) && allArtists.length < limit) {
  //             seenIds.add(artist['id']);
  //             allArtists.add(artist);
  //           }
  //         }

  //         if (allArtists.length >= limit) break;
  //       } catch (e) {
  //         print('❌ [SpotifyService] Error with genre query "$query": $e');
  //       }
  //     }

  //     // Sort by popularity (highest first)
  //     allArtists.sort(
  //         (a, b) => (b['popularity'] as int).compareTo(a['popularity'] as int));

  //     // Check if there are more results available
  //     final hasMore = allArtists.length == limit;

  //     return {
  //       'artists': allArtists,
  //       'hasMore': hasMore,
  //       'nextOffset': offset + limit,
  //       'total': allArtists.length,
  //     };
  //   } catch (e) {
  //     print(
  //         '❌ [SpotifyService] Error in paginated genre search for "$genre": $e');
  //     return {
  //       'artists': <Map<String, dynamic>>[],
  //       'hasMore': false,
  //       'nextOffset': offset,
  //       'total': 0,
  //     };
  //   }
  // }

  // // Paginated search for tracks by artist with progressive loading
  // Future<Map<String, dynamic>> getTopTracksByArtistPaginated(String artistName,
  //     {int limit = 20, int offset = 0}) async {
  //   try {
  //     print(
  //         '🎵 [SpotifyService] Paginated search for tracks by artist: "$artistName" (limit: $limit, offset: $offset)');

  //     final queries = [
  //       'artist:"$artistName" popular',
  //       'artist:"$artistName" top',
  //       'artist:"$artistName" hits',
  //       'artist:"$artistName"',
  //     ];

  //     final allTracks = <MusicTrack>[];
  //     final seenIds = <String>{};

  //     for (final query in queries.take(2)) {
  //       try {
  //         final tracks =
  //             await searchTracks(query, limit: limit ~/ 2 + 10, offset: offset);

  //         // Filter to ensure tracks are actually by the specified artist
  //         final artistTracks = tracks.where((track) {
  //           final trackArtistLower = track.artist.toLowerCase();
  //           final artistNameLower = artistName.toLowerCase();
  //           return trackArtistLower.contains(artistNameLower) ||
  //               artistNameLower.contains(trackArtistLower);
  //         });

  //         for (final track in artistTracks) {
  //           if (!seenIds.contains(track.id) && allTracks.length < limit) {
  //             seenIds.add(track.id);
  //             allTracks.add(track);
  //           }
  //         }

  //         if (allTracks.length >= limit) break;
  //       } catch (e) {
  //         print('❌ [SpotifyService] Error with artist query "$query": $e');
  //       }
  //     }

  //     // Sort by popularity (highest first)
  //     allTracks
  //         .sort((a, b) => (b.popularity ?? 50).compareTo(a.popularity ?? 50));

  //     // Check if there are more results available
  //     final hasMore = allTracks.length == limit;

  //     return {
  //       'tracks': allTracks,
  //       'hasMore': hasMore,
  //       'nextOffset': offset + limit,
  //       'total': allTracks.length,
  //     };
  //   } catch (e) {
  //     print(
  //         '❌ [SpotifyService] Error in paginated artist tracks search for "$artistName": $e');
  //     return {
  //       'tracks': <MusicTrack>[],
  //       'hasMore': false,
  //       'nextOffset': offset,
  //       'total': 0,
  //     };
  //   }
  // }

  // Paginated general artist search
  Future<Map<String, dynamic>> searchArtistsPaginated(String query,
      {int limit = 20, int offset = 0}) async {
    try {
      final results = await searchArtists(query, limit: limit, offset: offset);

      // Sort by popularity (highest first)
      results.sort(
          (a, b) => (b['popularity'] as int).compareTo(a['popularity'] as int));

      // For search results, we assume there might be more if we got the full limit
      final hasMore = results.length == limit;

      return {
        'artists': results,
        'hasMore': hasMore,
        'nextOffset': offset + limit,
        'total': results.length,
      };
    } catch (e) {
      print(
          '❌ [SpotifyService] Error in paginated artist search for "$query": $e');
      return {
        'artists': <Map<String, dynamic>>[],
        'hasMore': false,
        'nextOffset': offset,
        'total': 0,
      };
    }
  }

  // Paginated general track search
  Future<Map<String, dynamic>> searchTracksPaginated(String query,
      {int limit = 20, int offset = 0}) async {
    try {
      final results = await searchTracks(query, limit: limit, offset: offset);

      // Sort by popularity (highest first)
      results
          .sort((a, b) => (b.popularity ?? 50).compareTo(a.popularity ?? 50));

      // For search results, we assume there might be more if we got the full limit
      final hasMore = results.length == limit;

      return {
        'tracks': results,
        'hasMore': hasMore,
        'nextOffset': offset + limit,
        'total': results.length,
      };
    } catch (e) {
      print(
          '❌ [SpotifyService] Error in paginated track search for "$query": $e');
      return {
        'tracks': <MusicTrack>[],
        'hasMore': false,
        'nextOffset': offset,
        'total': 0,
      };
    }
  }

  // Load tracks for multiple artists with pagination
  Future<Map<String, dynamic>> loadTracksForArtistsPaginated(
      List<String> artistNames,
      {int limit = 20,
      int offset = 0,
      int tracksPerArtist = 8}) async {
    try {
      print(
          '🎵 [SpotifyService] Loading tracks for ${artistNames.length} artists (limit: $limit, offset: $offset)');

      final allTracks = <MusicTrack>[];
      final seenIds = <String>{};

      // Calculate how many artists to process based on offset
      final artistsToSkip = (offset / tracksPerArtist).floor();
      final remainingOffset = offset % tracksPerArtist;

      for (int i = artistsToSkip;
          i < artistNames.length && allTracks.length < limit;
          i++) {
        final artist = artistNames[i];
        try {
          final artistLimit = i == artistsToSkip
              ? tracksPerArtist - remainingOffset
              : tracksPerArtist;
          final artistOffset = i == artistsToSkip ? remainingOffset : 0;

          final artistTracks = await getTopTracksByArtist(
            artist,
            limit: artistLimit,
          );

          // Skip tracks based on offset for first artist
          final tracksToAdd = i == artistsToSkip
              ? artistTracks.skip(artistOffset).toList()
              : artistTracks;

          for (final track in tracksToAdd) {
            if (!seenIds.contains(track.id) && allTracks.length < limit) {
              seenIds.add(track.id);
              allTracks.add(track);
            }
          }
        } catch (e) {
          print('❌ [SpotifyService] Error fetching tracks for $artist: $e');
        }
      }

      // Sort by popularity instead of shuffling
      allTracks
          .sort((a, b) => (b.popularity ?? 50).compareTo(a.popularity ?? 50));

      // Check if there are more results available
      final hasMore = allTracks.length == limit &&
          (artistsToSkip + 1 < artistNames.length ||
              remainingOffset + allTracks.length < tracksPerArtist);

      return {
        'tracks': allTracks,
        'hasMore': hasMore,
        'nextOffset': offset + allTracks.length,
        'total': allTracks.length,
      };
    } catch (e) {
      print('❌ [SpotifyService] Error loading tracks for artists: $e');
      return {
        'tracks': <MusicTrack>[],
        'hasMore': false,
        'nextOffset': offset,
        'total': 0,
      };
    }
  }

  /// Get artist's albums using the Albums API
  Future<List<Map<String, dynamic>>> getArtistAlbums(
    String artistId, {
    List<String> includeGroups = const ['album', 'single', 'appears_on'],
    int limit = 20,
    int offset = 0,
    String? market,
  }) async {
    try {
      print(
          '📀 [SpotifyService] Getting albums for artist: $artistId (limit: $limit, offset: $offset)');

      final response = await _makeAuthenticatedRequest(
        '/artists/$artistId/albums',
        queryParams: {
          'include_groups': includeGroups.join(','),
          'limit': limit.toString(),
          'offset': offset.toString(),
          if (market != null) 'market': market,
        },
      );

      if (response == null || response['items'] == null) {
        print('⚠️ [SpotifyService] No albums found for artist: $artistId');
        return [];
      }

      final albums = List<Map<String, dynamic>>.from(response['items']);
      print(
          '📀 [SpotifyService] Found ${albums.length} albums for artist: $artistId');
      return albums;
    } catch (e) {
      print('❌ [SpotifyService] Error getting albums for artist $artistId: $e');
      return [];
    }
  }

  /// Get multiple albums and their tracks in batch
  Future<List<Map<String, dynamic>>> getMultipleAlbums(
    List<String> albumIds, {
    String? market,
  }) async {
    try {
      if (albumIds.isEmpty) return [];

      print('📀 [SpotifyService] Getting ${albumIds.length} albums in batch');

      // Spotify API allows up to 20 album IDs per request
      final batches = <List<String>>[];
      for (int i = 0; i < albumIds.length; i += 20) {
        batches.add(albumIds.skip(i).take(20).toList());
      }

      final allAlbums = <Map<String, dynamic>>[];

      for (final batch in batches) {
        final response = await _makeAuthenticatedRequest(
          '/albums',
          queryParams: {
            'ids': batch.join(','),
            if (market != null) 'market': market,
          },
        );

        if (response != null && response['albums'] != null) {
          final albums = List<Map<String, dynamic>>.from(response['albums']);
          allAlbums.addAll(albums.where((album) => album != null));
        }
      }

      print(
          '📀 [SpotifyService] Retrieved ${allAlbums.length} albums with tracks');
      return allAlbums;
    } catch (e) {
      print('❌ [SpotifyService] Error getting multiple albums: $e');
      return [];
    }
  }

  /// Get tracks from artist's albums (precise method for smaller artists)
  Future<List<MusicTrack>> getTracksFromArtistAlbums(
    String artistName, {
    bool includeAppearsOn = true,
    int maxTracks = 50,
    String? context,
  }) async {
    try {
      print('🔍 [DEBUG] Starting getTracksFromArtistAlbums for: "$artistName"');

      // Step 1: Search for multiple artist candidates and find best match
      print(
          '🔍 [DEBUG] Step 1: Searching for artist candidates for "$artistName"...');
      final artistCandidates = await searchArtists(artistName, limit: 5);
      print(
          '🔍 [DEBUG] Artist search returned ${artistCandidates.length} candidates');

      if (artistCandidates.isEmpty) {
        print(
            '❌ [DEBUG] No artist candidates found for: "$artistName" - falling back to traditional search');
        return [];
      }

      // Step 1.5: Find best matching artist using string similarity
      Map<String, dynamic>? bestArtist;
      double bestSimilarity = 0.0;

      print('🔍 [DEBUG] Evaluating artist candidates:');
      for (final candidate in artistCandidates) {
        final candidateName = candidate['name'] as String? ?? '';
        final similarity =
            artistName.toLowerCase().similarityTo(candidateName.toLowerCase());
        print(
            '🔍 [DEBUG] - "$candidateName": similarity = ${similarity.toStringAsFixed(3)}');

        if (similarity > bestSimilarity) {
          bestSimilarity = similarity;
          bestArtist = candidate;
        }
      }

      // Require minimum similarity threshold to avoid completely wrong matches
      if (bestArtist == null || bestSimilarity < 0.6) {
        print(
            '❌ [DEBUG] No good artist match found (best similarity: ${bestSimilarity.toStringAsFixed(3)}) - falling back to traditional search');
        return [];
      }

      final artistId = bestArtist['id'] as String?;
      if (artistId == null || artistId.isEmpty) {
        print(
            '❌ [DEBUG] No artist ID found for best match - falling back to traditional search');
        return [];
      }

      final exactArtistName = bestArtist['name'] as String? ?? artistName;
      print(
          '✅ [DEBUG] Best match: "$exactArtistName" (ID: $artistId, similarity: ${bestSimilarity.toStringAsFixed(3)})');

      // Step 2: Get artist's albums
      print('🔍 [DEBUG] Step 2: Getting albums for artist ID: $artistId');
      final includeGroups = includeAppearsOn
          ? ['album', 'single', 'appears_on']
          : ['album', 'single'];

      print('🔍 [DEBUG] Include groups: $includeGroups');
      final albums = await getArtistAlbums(
        artistId,
        includeGroups: includeGroups,
        limit: 50,
      );

      print('🔍 [DEBUG] Albums API returned ${albums.length} albums');
      if (albums.isEmpty) {
        print(
            '⚠️ [DEBUG] No albums found for artist: "$exactArtistName" - falling back');
        return [];
      }

      // Step 3: Get album IDs and fetch tracks in batch
      print('🔍 [DEBUG] Step 3: Extracting album IDs...');
      final albumIds = albums
          .map((album) => album['id'] as String?)
          .where((id) => id != null && id.isNotEmpty)
          .cast<String>()
          .toList();

      print('🔍 [DEBUG] Found ${albumIds.length} valid album IDs: $albumIds');
      if (albumIds.isEmpty) {
        print('❌ [DEBUG] No valid album IDs found - falling back');
        return [];
      }

      print('🔍 [DEBUG] Step 4: Fetching album details and tracks...');
      final albumsWithTracks = await getMultipleAlbums(albumIds);
      print(
          '🔍 [DEBUG] Multiple albums API returned ${albumsWithTracks.length} albums with tracks');

      // Step 4: Extract tracks from albums
      final allTracks = <MusicTrack>[];
      final seenTrackIds = <String>{};

      for (final album in albumsWithTracks) {
        if (album['tracks'] != null && album['tracks']['items'] != null) {
          final albumTracks =
              List<Map<String, dynamic>>.from(album['tracks']['items']);

          print(
              '🔍 [DEBUG] Album "${album['name']}" has ${albumTracks.length} tracks');

          for (final track in albumTracks) {
            if (allTracks.length >= maxTracks) break;

            final trackId = track['id'] as String?;
            if (trackId == null || seenTrackIds.contains(trackId)) continue;

            try {
              // Convert to MusicTrack with album context
              final albumArt = album['images']?.isNotEmpty == true
                  ? album['images'][0]['url']
                  : '';

              // Extract actual track artist(s) instead of using searched artist
              String trackArtist = 'Unknown Artist';
              if (track['artists'] != null && track['artists'].isNotEmpty) {
                trackArtist =
                    track['artists'].map((artist) => artist['name']).join(', ');
              }

              // Extract ISRC from external_ids
              String? isrc;
              if (track['external_ids'] != null &&
                  track['external_ids']['isrc'] != null) {
                isrc = track['external_ids']['isrc'];
              }

              final musicTrack = MusicTrack(
                id: trackId,
                title: track['name'] ?? 'Unknown Title',
                artist: trackArtist, // Use actual track artist(s)
                album: album['name'] ?? 'Unknown Album',
                albumArt: albumArt,
                url: track['external_urls']?['spotify'] ?? '',
                service: 'spotify',
                previewUrl: track['preview_url'],
                albumArtUrl: albumArt,
                serviceType: 'spotify',
                genres: const [], // Albums don't typically have genre info per track
                durationMs: track['duration_ms'] ?? 0,
                releaseDate: album['release_date'] != null
                    ? _parseReleaseDate(album['release_date'])
                    : null,
                explicit: track['explicit'] ?? false,
                popularity: album['popularity'] ?? 50,
                uri: track['uri'] ?? '',
                isrc: isrc,
              );

              allTracks.add(musicTrack);
              seenTrackIds.add(trackId);
            } catch (e) {
              print('⚠️ [DEBUG] Error converting track: $e');
            }
          }
        }
      }

      // Sort by popularity and album release date
      allTracks.sort((a, b) {
        final aPopularity = a.popularity ?? 0;
        final bPopularity = b.popularity ?? 0;
        return bPopularity.compareTo(aPopularity);
      });

      print(
          '✅ [DEBUG] Successfully retrieved ${allTracks.length} tracks from albums');

      // Log to AI analytics system
      AIRecommendationService.logSearchActivity(
        query: artistName,
        searchType: 'artist_albums',
        context: context ?? 'ALBUM_BASED_SEARCH',
        resultsCount: allTracks.length,
        isSuccess: true,
      );

      return allTracks;
    } catch (e) {
      print('❌ [DEBUG] Exception in getTracksFromArtistAlbums: $e');
      print('❌ [DEBUG] Stack trace: ${StackTrace.current}');

      // Log error to AI analytics system
      AIRecommendationService.logSearchActivity(
        query: artistName,
        searchType: 'artist_albums',
        context: context ?? 'ALBUM_BASED_SEARCH',
        resultsCount: 0,
        isSuccess: false,
        errorMessage: e.toString(),
      );

      return [];
    }
  }

  /// Hybrid artist search: tries album-based first, falls back to text search
  Future<List<MusicTrack>> getArtistTracksHybrid(
    String artistName, {
    int limit = 20,
    bool includeAppearsOn = true,
    String? context,
  }) async {
    try {
      print('🔄 [DEBUG] Starting hybrid search for: "$artistName"');

      // Try album-based approach first (more precise)
      // Get more tracks from albums to allow for better variety and shuffling
      final albumTracks = await getTracksFromArtistAlbums(
        artistName,
        includeAppearsOn: includeAppearsOn,
        maxTracks: limit * 3, // Get 3x more tracks for better variety
        context: context,
      );

      if (albumTracks.isNotEmpty) {
        print(
            '✅ [DEBUG] Album-based search successful: ${albumTracks.length} tracks');
        return albumTracks.take(limit).toList();
      }

      // Fallback to traditional search
      print(
          '🔄 [DEBUG] Album-based failed, falling back to traditional search for: "$artistName"');
      final fallbackTracks = await searchTracks(
        'artist:"$artistName"',
        limit: limit,
        context: context,
      );

      print(
          '✅ [DEBUG] Traditional search returned: ${fallbackTracks.length} tracks');
      return fallbackTracks;
    } catch (e) {
      print('❌ [DEBUG] Error in hybrid artist search: $e');
      return [];
    }
  }
}

// Add this extension to MusicTrack for easy copyWith
extension MusicTrackCopyWith on MusicTrack {
  MusicTrack copyWith({
    String? id,
    String? title,
    String? artist,
    String? album,
    String? albumArt,
    String? url,
    String? uri,
    String? service,
    String? previewUrl,
    String? albumArtUrl,
    String? serviceType,
    List<String>? genres,
    int? durationMs,
    DateTime? releaseDate,
    bool? explicit,
    int? popularity,
    bool? isPlayable,
  }) {
    return MusicTrack(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      albumArt: albumArt ?? this.albumArt,
      url: url ?? this.url,
      uri: uri ?? this.uri,
      service: service ?? this.service,
      previewUrl: previewUrl ?? this.previewUrl,
      albumArtUrl: albumArtUrl ?? this.albumArtUrl,
      serviceType: serviceType ?? this.serviceType,
      genres: genres ?? this.genres,
      durationMs: durationMs ?? this.durationMs,
      releaseDate: releaseDate ?? this.releaseDate,
      explicit: explicit ?? this.explicit,
      popularity: popularity ?? this.popularity,
      isPlayable: isPlayable ?? this.isPlayable,
    );
  }
}
