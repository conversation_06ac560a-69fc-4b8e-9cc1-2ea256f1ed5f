import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:music_kit/music_kit.dart';
import '../../models/music_track.dart';
import 'apple_music_service.dart';

enum RepeatMode { off, all, one }

class AppleMusicQueueManager {
  final MusicKit _musicKit = MusicKit();
  AppleMusicService? _appleMusicService;
  
  // Queue state
  List<MusicTrack> _queue = [];
  int _currentIndex = 0;
  bool _isShuffleEnabled = false;
  RepeatMode _repeatMode = RepeatMode.off;
  
  // Shuffle state
  List<int> _shuffleIndices = [];
  int _shufflePosition = 0;
  
  // Collection context
  String? _collectionType;
  String? _collectionId;
  Map<String, dynamic>? _collectionMetadata;
  
  // Stream controllers for notifications
  final StreamController<MusicTrack?> _currentTrackController = StreamController<MusicTrack?>.broadcast();
  final StreamController<List<MusicTrack>> _queueController = StreamController<List<MusicTrack>>.broadcast();
  final StreamController<bool> _shuffleController = StreamController<bool>.broadcast();
  final StreamController<RepeatMode> _repeatModeController = StreamController<RepeatMode>.broadcast();
  
  // MusicKit subscriptions
  StreamSubscription<MusicPlayerState>? _playerStateSubscription;
  StreamSubscription<MusicPlayerQueue>? _playerQueueSubscription;
  
  // Track if we're currently handling a manual skip to avoid double-processing
  bool _isHandlingManualSkip = false;
  bool _isAutoProgressing = false;

  // Track when we just completed auto-progression to avoid false triggers
  DateTime? _lastAutoProgressionTime;
  
  // Track if the queue manager has been disposed
  bool _isDisposed = false;

  // Track completion attempts to prevent duplicates
  DateTime? _lastCompletionAttempt;
  static const Duration _completionCooldown = Duration(milliseconds: 500);

  // Enhanced pre-loading for instant skipping and seamless transitions
  Map<int, MusicTrack> _preloadedTracks = {}; // Map of index -> preloaded track
  Set<int> _preloadingIndices = {}; // Tracks currently being preloaded
  Timer? _preloadTimer;
  static const int _maxPreloadTracks = 3; // Preload next 3 tracks
  static const Duration _preloadTriggerTime = Duration(seconds: 10); // Also keep late preloading for seamless transitions

  // Simplified auto progression - near end detection
  bool _isNearTrackEnd = false;
  static const Duration _nearEndThreshold = Duration(seconds: 5); // Increased to 5s for more reliable detection

  // Track position jump detection for completion
  double _previousPosition = 0.0;
  double _lastValidPosition = 0.0; // Backup for when periodic update hits at jump moment

  // Store actual Apple Music duration for current track
  double _currentAppleMusicDuration = 0.0;

  // Pause event debouncing to prevent multiple rapid triggers
  DateTime? _lastPauseEventTime;

  // Getters
  List<MusicTrack> get queue => List.unmodifiable(_queue);
  int get currentIndex => _currentIndex;
  MusicTrack? get currentTrack => _queue.isNotEmpty && _currentIndex < _queue.length ? _queue[_currentIndex] : null;
  bool get isShuffleEnabled => _isShuffleEnabled;
  RepeatMode get repeatMode => _repeatMode;
  bool get hasNext => _getNextIndex() != null;
  bool get hasPrevious => _getPreviousIndex() != null;
  String? get collectionType => _collectionType;
  String? get collectionId => _collectionId;
  int get queueLength => _queue.length;
  
  // Streams
  Stream<MusicTrack?> get currentTrackStream => _currentTrackController.stream;
  Stream<List<MusicTrack>> get queueStream => _queueController.stream;
  Stream<bool> get shuffleStream => _shuffleController.stream;
  Stream<RepeatMode> get repeatModeStream => _repeatModeController.stream;
  
  /// Initialize the queue manager
  Future<void> initialize({AppleMusicService? appleMusicService}) async {
    if (kDebugMode) {
      print('🎵 [QueueManager] Initializing...');
    }

    // Set the Apple Music service if provided
    if (appleMusicService != null) {
      _appleMusicService = appleMusicService;
    }

    // Listen to player state changes to handle track completion
    _playerStateSubscription = _musicKit.onMusicPlayerStateChanged.listen(_handlePlayerStateChange);

    // Listen to player queue changes for additional context
    _playerQueueSubscription = _musicKit.onPlayerQueueChanged.listen(_handlePlayerQueueChange);

    if (kDebugMode) {
      print('✅ [QueueManager] Initialized successfully');
    }
  }
  
  // Track last known position for near end detection
  double _lastKnownPosition = 0.0;
  Timer? _positionTracker;

  // Skip operation protection
  bool _isSkipOperationInProgress = false;
  DateTime? _lastSkipTime;
  static const Duration _skipDebounceDelay = Duration(milliseconds: 150);
  
  /// Handle player state changes
  void _handlePlayerStateChange(MusicPlayerState state) {
    if (kDebugMode) {
      print('🎵 [QueueManager] ===== PLAYER STATE CHANGE =====');
      print('🎵 [QueueManager] Playback Status: ${state.playbackStatus}');
      print('🎵 [QueueManager] Playback Rate: ${state.playbackRate}');
      print('🎵 [QueueManager] Shuffle Mode: ${state.shuffleMode}');
      print('🎵 [QueueManager] Repeat Mode: ${state.repeatMode}');
      print('🎵 [QueueManager] Manual Skip Flag: $_isHandlingManualSkip');
      print('🎵 [QueueManager] Auto-Progressing Flag: $_isAutoProgressing');
      print('🎵 [QueueManager] Collection Type: $_collectionType');
      print('🎵 [QueueManager] Queue Length: ${_queue.length}');
      print('🎵 [QueueManager] Current Index: $_currentIndex');
      print('🎵 [QueueManager] Has Next: $hasNext');
      print('🎵 [QueueManager] Is Disposed: $_isDisposed');
      print('🎵 [QueueManager] ================================');
    }
    
    // Handle different playback statuses
    switch (state.playbackStatus) {
      case MusicPlayerPlaybackStatus.stopped:
        _handleStoppedState(state);
        break;
      case MusicPlayerPlaybackStatus.playing:
        if (kDebugMode) {
          print('🎵 [QueueManager] Track is playing - starting position tracking');
        }
        _startPositionTracking();

        // Clear auto-progression timestamp when we start playing to allow future completions
        if (_lastAutoProgressionTime != null) {
          final timeSinceProgression = DateTime.now().difference(_lastAutoProgressionTime!).inSeconds;
          if (timeSinceProgression > 5) {
            _lastAutoProgressionTime = null;
            if (kDebugMode) {
              print('🎵 [QueueManager] Cleared auto-progression timestamp after stable playback');
            }
          }
        }
        break;
      case MusicPlayerPlaybackStatus.paused:
        if (kDebugMode) {
          print('🎵 [QueueManager] Track is paused - checking if at end...');
        }
        _handlePausedState(state);
        break;
      case MusicPlayerPlaybackStatus.seekingForward:
        if (kDebugMode) {
          print('🎵 [QueueManager] Seeking forward');
        }
        break;
      case MusicPlayerPlaybackStatus.seekingBackward:
        if (kDebugMode) {
          print('🎵 [QueueManager] Seeking backward');
        }
        break;
      default:
        if (kDebugMode) {
          print('🎵 [QueueManager] Unknown playback status: ${state.playbackStatus}');
        }
    }
  }
  
  /// Handle stopped state specifically
  void _handleStoppedState(MusicPlayerState state) {

    if (kDebugMode) {
      print('🎵 [QueueManager] ----- STOPPED STATE ANALYSIS -----');
      print('🎵 [QueueManager] Can auto-progress? ${!_isHandlingManualSkip && !_isAutoProgressing && _collectionType != 'single_track' && !_isDisposed && _queue.isNotEmpty}');
      print('🎵 [QueueManager] Not manual skip: ${!_isHandlingManualSkip}');
      print('🎵 [QueueManager] Not auto-progressing: ${!_isAutoProgressing}');
      print('🎵 [QueueManager] Not single track: ${_collectionType != 'single_track'}');
      print('🎵 [QueueManager] Not disposed: ${!_isDisposed}');
      print('🎵 [QueueManager] Has queue: ${_queue.isNotEmpty}');
      print('🎵 [QueueManager] Current index: $_currentIndex/${_queue.length}');
      print('🎵 [QueueManager] Has next: $hasNext');
      print('🎵 [QueueManager] Skip operation in progress: $_isSkipOperationInProgress');
      print('🎵 [QueueManager] ----------------------------------');
    }

    // Handle track completion - but not if we're in the middle of operations or single track
    if (_queue.isNotEmpty &&
        !_isHandlingManualSkip &&
        !_isAutoProgressing &&
        !_isSkipOperationInProgress &&
        _collectionType != 'single_track' &&
        !_isDisposed &&
        _canAttemptCompletion()) {

      if (kDebugMode) {
        print('🎵 [QueueManager] ✅ Track stopped - scheduling auto-progression check');
      }

      // Mark completion attempt to prevent duplicates
      _lastCompletionAttempt = DateTime.now();

      // Use a shorter delay but with better state validation
      Future.delayed(const Duration(milliseconds: 100), () {
        // Revalidate all conditions with additional safety checks
        if (_isValidForAutoProgression()) {
          if (kDebugMode) {
            print('🎵 [QueueManager] ✅ Conditions still valid after delay - proceeding with auto-progression');
          }
          _handleTrackCompletion();
        } else {
          if (kDebugMode) {
            print('🎵 [QueueManager] ❌ Auto-progression cancelled - conditions changed during delay');
            _logValidationFailureReasons();
          }
        }
      });
    } else {
      if (kDebugMode) {
        print('🎵 [QueueManager] ❌ Auto-progression blocked - conditions not met');
        if (_queue.isEmpty) print('   - Queue is empty');
        if (_isHandlingManualSkip) print('   - Manual skip in progress');
        if (_isAutoProgressing) print('   - Auto-progression in progress');
        if (_isSkipOperationInProgress) print('   - Skip operation in progress');
        if (_collectionType == 'single_track') print('   - Single track mode');
        if (_isDisposed) print('   - Queue manager disposed');
      }
    }

    if (kDebugMode) {
      print('🎵 [QueueManager] ----------------------------------');
    }
  }
  
  /// Start tracking playback position
  void _startPositionTracking() {
    _positionTracker?.cancel();
    _positionTracker = Timer.periodic(const Duration(milliseconds: 500), (timer) async {
      try {
        final currentTime = await _musicKit.playbackTime;

        // Store last valid position (>1s) as backup for edge cases
        if (_lastKnownPosition > 1.0) {
          _lastValidPosition = _lastKnownPosition;
        }

        // Always update previous position before updating current
        // This ensures we capture position changes more reliably
        if (_lastKnownPosition > 0) {
          _previousPosition = _lastKnownPosition;
        }
        _lastKnownPosition = currentTime;

        // Check for near end detection (simplified auto progression)
        _checkForNearEnd(currentTime);

        // Check if we should trigger pre-loading
        await _checkForPreloadTrigger(currentTime);

        // Debug position updates (less verbose)
        if (timer.tick % 4 == 0) { // Log every 2 seconds
          if (kDebugMode) {
            print('🎵 [QueueManager] Position: ${currentTime.toStringAsFixed(1)}s${_isNearTrackEnd ? ' (NEAR END)' : ''}');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ [QueueManager] Position tracking error: $e');
        }
        // Continue tracking despite errors, but log them for debugging
      }
    });
  }
  
  /// Stop position tracking
  void _stopPositionTracking() {
    _positionTracker?.cancel();
    _positionTracker = null;
    // Don't reset near end flag here - we need it for pause-based progression
    // Don't stop preloading here - we want to keep preloaded tracks for instant skipping
  }

  /// Check if track is near end (≤5 seconds remaining) - simplified auto progression
  void _checkForNearEnd(double currentTime) {
    // Use stored Apple Music duration if available, otherwise fall back to track duration
    final actualDuration = _currentAppleMusicDuration > 0
        ? _currentAppleMusicDuration
        : (currentTrack?.durationMs ?? 0) / 1000.0;

    if (actualDuration <= 0) {
      _isNearTrackEnd = false;
      return;
    }

    final timeRemaining = actualDuration - currentTime;

    // Set flag when ≤3 seconds remaining
    final wasNearEnd = _isNearTrackEnd;
    _isNearTrackEnd = timeRemaining <= _nearEndThreshold.inSeconds && timeRemaining > 0;

    // Log when flag changes
    if (_isNearTrackEnd && !wasNearEnd && kDebugMode) {
      print('🎵 [QueueManager] ✅ Track near end detected: ${timeRemaining.toStringAsFixed(1)}s remaining (duration: ${actualDuration.toStringAsFixed(1)}s)');
    } else if (!_isNearTrackEnd && wasNearEnd && kDebugMode) {
      print('🎵 [QueueManager] ❌ Track no longer near end: ${timeRemaining.toStringAsFixed(1)}s remaining');
    }
  }

  /// Check if we should trigger late pre-loading for seamless transitions
  Future<void> _checkForPreloadTrigger(double currentTime) async {
    // Skip if single track mode
    if (_collectionType == 'single_track' || !hasNext) {
      return;
    }

    final currentTrack = this.currentTrack;
    if (currentTrack == null) return;

    final trackDurationSeconds = currentTrack.durationMs / 1000.0;
    final timeRemaining = trackDurationSeconds - currentTime;

    // Trigger late pre-loading for seamless transitions (10 seconds before end)
    if (timeRemaining <= _preloadTriggerTime.inSeconds && timeRemaining > 0) {
      final nextIndex = _getNextIndex();
      if (nextIndex != null && !_preloadedTracks.containsKey(nextIndex) && !_preloadingIndices.contains(nextIndex)) {
        if (kDebugMode) {
          print('🎵 [QueueManager] Late pre-loading trigger: ${timeRemaining.toStringAsFixed(1)}s remaining');
        }
        await _preloadTrackAtIndex(nextIndex);
      }
    }
  }

  /// Start enhanced pre-loading when track begins
  Future<void> _startEnhancedPreloading() async {
    if (_collectionType == 'single_track') return;

    // Clean up old preloaded tracks first
    _cleanupOldPreloadedTracks();

    if (kDebugMode) {
      print('🎵 [QueueManager] Starting enhanced pre-loading for next ${_maxPreloadTracks} tracks');
    }

    // Preload next few tracks for instant skipping
    for (int i = 1; i <= _maxPreloadTracks; i++) {
      final targetIndex = _getIndexOffset(i);
      if (targetIndex != null && targetIndex < _queue.length) {
        // Don't await - preload in parallel for speed
        _preloadTrackAtIndex(targetIndex);
      }
    }
  }

  /// Preload a specific track at the given index
  Future<void> _preloadTrackAtIndex(int index) async {
    if (index < 0 || index >= _queue.length ||
        _preloadedTracks.containsKey(index) ||
        _preloadingIndices.contains(index)) {
      return;
    }

    final track = _queue[index];
    _preloadingIndices.add(index);

    if (kDebugMode) {
      print('🎵 [QueueManager] Pre-loading track at index $index: ${track.title}');
    }

    try {
      // Pre-search for the track and get Apple Music equivalent
      final appleMusicTrack = await _prepareNextTrack(track);
      _preloadedTracks[index] = appleMusicTrack;

      if (kDebugMode) {
        print('✅ [QueueManager] Track pre-loaded and ready at index $index: ${appleMusicTrack.title} (${appleMusicTrack.service})');
      }

      // If this is the current track, store its duration
      if (index == _currentIndex) {
        _storeAppleMusicDuration(appleMusicTrack);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Failed to pre-load track at index $index: $e');
      }
    } finally {
      _preloadingIndices.remove(index);
    }
  }

  /// Get index offset from current position (handles shuffle and repeat modes)
  int? _getIndexOffset(int offset) {
    if (_isShuffleEnabled) {
      final newShufflePosition = _shufflePosition + offset;
      if (newShufflePosition < _shuffleIndices.length) {
        return _shuffleIndices[newShufflePosition];
      } else if (_repeatMode == RepeatMode.all) {
        // Wrap around for repeat all
        final wrappedPosition = newShufflePosition % _shuffleIndices.length;
        return _shuffleIndices[wrappedPosition];
      }
    } else {
      final newIndex = _currentIndex + offset;
      if (newIndex < _queue.length) {
        return newIndex;
      } else if (_repeatMode == RepeatMode.all) {
        // Wrap around for repeat all
        return newIndex % _queue.length;
      }
    }
    return null;
  }

  /// Prepare the next track for playback (search if needed) and return Apple Music equivalent
  Future<MusicTrack> _prepareNextTrack(MusicTrack track) async {
    // If it's an Apple Music track, it's already ready
    if (track.service.toLowerCase().contains('apple') && track.id.isNotEmpty) {
      return track; // Apple Music tracks are ready to play immediately
    }

    // For Spotify tracks, search for Apple Music equivalent
    if (_appleMusicService != null) {
      try {
        final appleMusicTrack = await _appleMusicService!.searchTrackByArtistAndTitle(
          track.artist,
          track.title,
          isrc: track.isrc,
          spotifyUrl: track.url,
        );

        if (appleMusicTrack != null) {
          if (kDebugMode) {
            print('🎵 [QueueManager] Pre-searched Apple Music equivalent: ${appleMusicTrack.title} (${appleMusicTrack.id})');
          }
          return appleMusicTrack;
        } else {
          if (kDebugMode) {
            print('⚠️ [QueueManager] No Apple Music equivalent found for: ${track.title}');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ [QueueManager] Pre-search failed for ${track.title}: $e');
        }
      }
    }

    // Return original track if no Apple Music equivalent found
    return track;
  }

  /// Stop pre-loading and clear pre-loaded data
  void _stopPreloading() {
    _preloadTimer?.cancel();
    _preloadTimer = null;

    if (kDebugMode && _preloadedTracks.isNotEmpty) {
      print('🎵 [QueueManager] Clearing ${_preloadedTracks.length} preloaded tracks: ${_preloadedTracks.keys.toList()}');
    }

    _preloadedTracks.clear();
    _preloadingIndices.clear();
  }

  /// Check if a track at index is preloaded
  bool _isTrackPreloaded(int index) {
    return _preloadedTracks.containsKey(index);
  }

  /// Get preloaded track at index
  MusicTrack? _getPreloadedTrack(int index) {
    return _preloadedTracks[index];
  }

  /// Store Apple Music duration for current track
  void _storeAppleMusicDuration(MusicTrack track) {
    if (track.service.toLowerCase().contains('apple') && track.durationMs > 0) {
      _currentAppleMusicDuration = track.durationMs / 1000.0;
      if (kDebugMode) {
        print('🎵 [QueueManager] Stored Apple Music duration: ${_currentAppleMusicDuration.toStringAsFixed(1)}s for "${track.title}"');
      }
    }
  }

  /// Clean up old preloaded tracks that are no longer needed
  void _cleanupOldPreloadedTracks() {
    final currentIndex = _currentIndex;
    final keysToRemove = <int>[];

    for (final index in _preloadedTracks.keys) {
      // Remove tracks that are behind current position (we've already passed them)
      if (index < currentIndex) {
        keysToRemove.add(index);
      }
    }

    if (kDebugMode && keysToRemove.isNotEmpty) {
      print('🎵 [QueueManager] Cleaning up ${keysToRemove.length} old preloaded tracks: $keysToRemove');
    }

    for (final key in keysToRemove) {
      _preloadedTracks.remove(key);
    }

    if (kDebugMode && _preloadedTracks.isNotEmpty) {
      print('🎵 [QueueManager] Remaining preloaded tracks: ${_preloadedTracks.keys.toList()}');
    }
  }

  /// Play current track with seamless transition support
  Future<bool> _playCurrentTrackSeamlessly() async {
    final track = currentTrack;
    if (track == null) {
      if (kDebugMode) {
        print('❌ [QueueManager] No current track to play');
      }
      return false;
    }

    try {
      final isPreloaded = _isTrackPreloaded(_currentIndex);
      if (kDebugMode) {
        print('🎵 [QueueManager] Playing track seamlessly: ${track.title} (${_currentIndex + 1}/${_queue.length})');
        if (isPreloaded) {
          print('✨ [QueueManager] Using pre-loaded track for seamless transition!');
        }
      }

      // Reset near end flag and position tracking for new track
      _isNearTrackEnd = false;
      _previousPosition = 0.0;
      _lastValidPosition = 0.0; // Reset backup position

      // Store Apple Music duration for accurate timing
      _storeAppleMusicDuration(track);

      // CRITICAL: Always ensure Apple Music's native shuffle is OFF
      await _musicKit.setShuffleMode(MusicPlayerShuffleMode.off);

      // Always stop current playback, but use shorter delay for preloaded tracks
      try {
        await _musicKit.stop();
        if (isPreloaded) {
          // Shorter delay for preloaded tracks for faster transitions
          await Future.delayed(const Duration(milliseconds: 25));
          if (kDebugMode) {
            print('🎵 [QueueManager] Quick stop for preloaded track transition');
          }
        } else {
          // Normal delay for non-preloaded tracks
          await Future.delayed(const Duration(milliseconds: 50));
          if (kDebugMode) {
            print('🎵 [QueueManager] Stopped previous playback');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ [QueueManager] Could not stop previous playback: $e');
        }
      }

      // Play the track directly without using MusicKit's queue system
      bool success = false;

      // Check if we have a preloaded version of this track
      final preloadedTrack = _getPreloadedTrack(_currentIndex);
      if (kDebugMode) {
        print('🔍 [QueueManager] Checking for preloaded track at index $_currentIndex');
        print('🔍 [QueueManager] Preloaded tracks available: ${_preloadedTracks.keys.toList()}');
        print('🔍 [QueueManager] Currently preloading indices: ${_preloadingIndices.toList()}');
        print('🔍 [QueueManager] Found preloaded track: ${preloadedTrack?.title ?? 'None'}');
      }

      if (preloadedTrack != null) {
        if (kDebugMode) {
          print('✨ [QueueManager] Using preloaded track: ${preloadedTrack.title}');
        }
        success = await _playAppleMusicTrack(preloadedTrack);
      } else {
        // No preloaded version, use normal search/play logic
        if (track.service.toLowerCase().contains('apple') && track.id.isNotEmpty) {
          // For Apple Music tracks, play directly by track ID
          success = await _playAppleMusicTrack(track);
        } else {
          // For Spotify tracks or any non-Apple Music tracks, search for Apple Music equivalent
          success = await _searchAndPlayTrack(track);
        }
      }

      if (success) {
        // Notify listeners
        _currentTrackController.add(track);

        // Start enhanced preloading for next tracks
        _startEnhancedPreloading();

        if (kDebugMode) {
          print('✅ [QueueManager] Successfully started seamless playback of: ${track.title}');
        }
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error in seamless track playback: $e');
      }
      // Fallback to regular playback method
      return await _playCurrentTrack();
    }
  }



  /// Check if we can attempt completion (not too soon after last attempt)
  bool _canAttemptCompletion() {
    if (_lastCompletionAttempt == null) {
      return true;
    }
    
    final timeSinceLastAttempt = DateTime.now().difference(_lastCompletionAttempt!);
    final canAttempt = timeSinceLastAttempt >= _completionCooldown;
    
    if (kDebugMode && !canAttempt) {
      print('🎵 [QueueManager] Completion cooldown active: ${timeSinceLastAttempt.inMilliseconds}ms < ${_completionCooldown.inMilliseconds}ms');
    }
    
    return canAttempt;
  }

  /// Validate all conditions for auto progression
  bool _isValidForAutoProgression() {
    return !_isDisposed &&
           !_isHandlingManualSkip &&
           !_isAutoProgressing &&
           !_isSkipOperationInProgress &&
           _collectionType != 'single_track' &&
           _queue.isNotEmpty;
  }

  /// Log detailed reasons why auto progression validation failed
  void _logValidationFailureReasons() {
    if (kDebugMode) {
      print('🎵 [QueueManager] Auto-progression validation failed:');
      if (_isDisposed) print('   - Queue manager disposed');
      if (_isHandlingManualSkip) print('   - Manual skip in progress');
      if (_isAutoProgressing) print('   - Auto-progression already in progress');
      if (_isSkipOperationInProgress) print('   - Skip operation in progress');
      if (_collectionType == 'single_track') print('   - Single track mode');
      if (_queue.isEmpty) print('   - Queue is empty');
      if (!_canAttemptCompletion()) print('   - Completion cooldown active');
    }
  }
  
  /// Handle paused state - simplified auto progression logic
  void _handlePausedState(MusicPlayerState state) async {
    // Capture current position before stopping tracking
    try {
      final currentTime = await _musicKit.playbackTime;

      // Smart position handling for edge cases
      if (_lastKnownPosition > 0) {
        _previousPosition = _lastKnownPosition;
      } else if (_lastValidPosition > 0) {
        // Fallback to last valid position if current tracking failed
        _previousPosition = _lastValidPosition;
        if (kDebugMode) {
          print('🎵 [QueueManager] Using last valid position as fallback: ${_lastValidPosition.toStringAsFixed(1)}s');
        }
      }

      // Update with the most current position
      _lastKnownPosition = currentTime;

      if (kDebugMode) {
        print('🎵 [QueueManager] Captured position on pause: previous=${_previousPosition.toStringAsFixed(1)}s, current=${_lastKnownPosition.toStringAsFixed(1)}s');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ [QueueManager] Could not capture position on pause: $e');
      }
    }

    // Stop position tracking when paused
    _stopPositionTracking();

    // Debounce rapid pause events, but allow them if there's a significant position jump
    // This prevents blocking natural track completion events
    final now = DateTime.now();
    final positionDiff = (_previousPosition - _lastKnownPosition).abs();
    final isSignificantPositionJump = positionDiff > 3.0;

    if (_lastPauseEventTime != null &&
        now.difference(_lastPauseEventTime!).inMilliseconds < 500 &&
        !isSignificantPositionJump) {
      if (kDebugMode) {
        print('🎵 [QueueManager] Ignoring rapid pause event (debounced) - no significant position jump');
      }
      return;
    }
    _lastPauseEventTime = now;

    if (kDebugMode) {
      print('🎵 [QueueManager] ----- PAUSED STATE (SIMPLIFIED) -----');
      print('🎵 [QueueManager] Position Analysis: previous=${_previousPosition.toStringAsFixed(1)}s, current=${_lastKnownPosition.toStringAsFixed(1)}s, diff=${(_previousPosition - _lastKnownPosition).abs().toStringAsFixed(1)}s');
      print('🎵 [QueueManager] Near Track End: $_isNearTrackEnd');
      print('🎵 [QueueManager] Last Known Position: ${_lastKnownPosition.toStringAsFixed(1)}s');
      final currentTrack = this.currentTrack;
      if (currentTrack != null) {
        final trackDurationSeconds = currentTrack.durationMs / 1000.0;
        final timeRemaining = trackDurationSeconds - _lastKnownPosition;
        print('🎵 [QueueManager] Track Duration: ${trackDurationSeconds.toStringAsFixed(1)}s');
        print('🎵 [QueueManager] Time Remaining: ${timeRemaining.toStringAsFixed(1)}s');
      }
      print('🎵 [QueueManager] Manual Skip Flag: $_isHandlingManualSkip');
      print('🎵 [QueueManager] Auto-Progressing Flag: $_isAutoProgressing');
      print('🎵 [QueueManager] Collection Type: $_collectionType');
      print('🎵 [QueueManager] Queue Length: ${_queue.length}');
      print('🎵 [QueueManager] Has Next: $hasNext');
    }

    // Simplified auto progression: Check if paused AND near track end
    // Also check current position as fallback in case position tracking missed it
    bool shouldAutoProgress = _isNearTrackEnd;

    // Check for position jump detection FIRST (background skip detection)
    // This should run regardless of near-end status
    final jumpDiff = (_previousPosition - _lastKnownPosition).abs();
    bool isPositionJump = _previousPosition > 1.0 &&
                          _lastKnownPosition < 1.0 &&
                          jumpDiff > 3.0;

    if (isPositionJump) {
      shouldAutoProgress = true;
      if (kDebugMode) {
        print('🎵 [QueueManager] 🔄 Position jump detection (background skip): ${_previousPosition.toStringAsFixed(1)}s → ${_lastKnownPosition.toStringAsFixed(1)}s');
      }
    }

    // Fallback: Check current position if we haven't detected near end yet
    if (!shouldAutoProgress) {
      final currentTrack = this.currentTrack;
      if (currentTrack != null) {
        // Use stored Apple Music duration if available, otherwise fall back to track duration
        final trackDurationSeconds = _currentAppleMusicDuration > 0
            ? _currentAppleMusicDuration
            : currentTrack.durationMs / 1000.0;
        final timeRemaining = trackDurationSeconds - _lastKnownPosition;

        // Method 1: Check if we're near the end (≤5 seconds remaining) AND position is reasonable
        bool isNearEnd = trackDurationSeconds > 0 && timeRemaining <= _nearEndThreshold.inSeconds && timeRemaining > 0;

        // Method 2: Handle case where track completed and position reset to 0
        // But avoid false positives when pausing early in long tracks
        bool isCompletedAndReset = _lastKnownPosition < 5.0 &&
                                   trackDurationSeconds > 30.0 &&
                                   _previousPosition > 10.0; // Must have been playing for a while

        // Method 3: Handle bad duration data (duration = 0 but we have a position)
        bool isBadDurationData = trackDurationSeconds <= 0 && _lastKnownPosition > 0;

        // Method 4: Track clearly completed (position >= duration)
        bool isTrackCompleted = trackDurationSeconds > 0 && _lastKnownPosition >= trackDurationSeconds;

        shouldAutoProgress = isNearEnd || isCompletedAndReset || isBadDurationData || isTrackCompleted;

        if (shouldAutoProgress && kDebugMode) {
          if (isNearEnd) {
            print('🎵 [QueueManager] 🔄 Fallback near-end detection: ${timeRemaining.toStringAsFixed(1)}s remaining');
          } else if (isCompletedAndReset) {
            print('🎵 [QueueManager] 🔄 Fallback completion detection: position reset to ${_lastKnownPosition.toStringAsFixed(1)}s');
          } else if (isBadDurationData) {
            print('🎵 [QueueManager] 🔄 Bad duration data detection: duration=${trackDurationSeconds}s, position=${_lastKnownPosition.toStringAsFixed(1)}s');
          } else if (isTrackCompleted) {
            print('🎵 [QueueManager] 🔄 Track completion detection: position=${_lastKnownPosition.toStringAsFixed(1)}s >= duration=${trackDurationSeconds.toStringAsFixed(1)}s');
          }
        }
      }
    }

    // Safety check: Don't auto-progress if user paused very early in the track
    // But allow it if there's a significant position jump (background skip)
    if (shouldAutoProgress && _lastKnownPosition < 10.0 && _previousPosition < 10.0) {
      final positionDiff = (_previousPosition - _lastKnownPosition).abs();
      if (positionDiff < 3.0) { // No significant jump, likely just early pause
        shouldAutoProgress = false;
        if (kDebugMode) {
          print('🎵 [QueueManager] ⛔ Blocking auto-progression: paused too early in track (${_lastKnownPosition.toStringAsFixed(1)}s, diff=${positionDiff.toStringAsFixed(1)}s)');
        }
      } else {
        if (kDebugMode) {
          print('🎵 [QueueManager] ✅ Allowing auto-progression: significant position jump detected (${positionDiff.toStringAsFixed(1)}s)');
        }
      }
    }

    // Check if this is natural track completion (should override manual skip flag)
    bool isNaturalCompletion = false;
    if (shouldAutoProgress) {
      final currentTrack = this.currentTrack;
      if (currentTrack != null) {
        final trackDurationSeconds = currentTrack.durationMs / 1000.0;
        isNaturalCompletion = trackDurationSeconds > 0 && _lastKnownPosition >= trackDurationSeconds;
      }
    }

    // Allow auto-progression if conditions are met OR if it's natural completion
    bool canAutoProgress = shouldAutoProgress && (
      (isNaturalCompletion && !_isAutoProgressing && !_isDisposed && _queue.isNotEmpty && _collectionType != 'single_track') ||
      (_isValidForAutoProgression() && _canAttemptCompletion())
    );

    if (canAutoProgress) {

      if (kDebugMode) {
        if (isNaturalCompletion) {
          print('🎵 [QueueManager] ✅ Natural track completion detected - triggering auto progression (overriding manual skip flag)');
        } else {
          print('🎵 [QueueManager] ✅ Track paused near end - triggering auto progression');
        }
      }

      // Simple auto progression: trigger immediately when paused near end
      _handleTrackCompletion();
    } else {
      if (kDebugMode) {
        if (!shouldAutoProgress) {
          print('🎵 [QueueManager] Track paused but not near end - user pause');
        } else {
          print('🎵 [QueueManager] ❌ Paused near end but conditions not met for auto-progression');
          if (_isHandlingManualSkip) print('   - Manual skip in progress');
          if (_isAutoProgressing) print('   - Auto-progression in progress');
          if (_isSkipOperationInProgress) print('   - Skip operation in progress');
          if (_collectionType == 'single_track') print('   - Single track mode');
          if (_isDisposed) print('   - Queue manager disposed');
          if (_queue.isEmpty) print('   - Queue is empty');
        }
      }
    }
    
    if (kDebugMode) {
      print('🎵 [QueueManager] ----------------------------------');
    }
  }
  
  /// Handle player queue changes
  void _handlePlayerQueueChange(MusicPlayerQueue queue) {
    if (kDebugMode) {
      print('🎵 [QueueManager] ===== QUEUE CHANGE =====');
      print('🎵 [QueueManager] Current Entry: ${queue.currentEntry?.title ?? 'None'}');
      print('🎵 [QueueManager] Current Entry ID: ${queue.currentEntry?.id ?? 'None'}');
      print('🎵 [QueueManager] MusicKit Queue Length: ${queue.entries.length}');
      
      // Compare with our internal queue state
      print('🎵 [QueueManager] Internal Queue Length: ${_queue.length}');
      print('🎵 [QueueManager] Internal Current Index: $_currentIndex');
      if (_queue.isNotEmpty && _currentIndex < _queue.length) {
        print('🎵 [QueueManager] Internal Current Track: ${_queue[_currentIndex].title}');
        print('🎵 [QueueManager] Internal Current Track ID: ${_queue[_currentIndex].id}');
      }
      print('🎵 [QueueManager] =========================');
    }
  }
  
  /// Handle when a track completes
  void _handleTrackCompletion() async {
    // Skip auto-progression for single track playback
    if (_collectionType == 'single_track') {
      if (kDebugMode) {
        print('🎵 [QueueManager] Single track completed - no auto-progression');
      }
      return;
    }

    // Double-check we're not disposed before proceeding
    if (_isDisposed) {
      if (kDebugMode) {
        print('🎵 [QueueManager] Queue manager disposed - cancelling auto-progression');
      }
      return;
    }

    if (kDebugMode) {
      print('🎵 [QueueManager] ===== AUTO-PROGRESSION START =====');
      print('🎵 [QueueManager] Track completed, checking for next track...');
      print('🎵 [QueueManager] Collection type: $_collectionType, queue size: ${_queue.length}');
      print('🎵 [QueueManager] Current index: $_currentIndex, has next: $hasNext');
      print('🎵 [QueueManager] Repeat mode: $_repeatMode');
      print('🎵 [QueueManager] Shuffle enabled: $_isShuffleEnabled');
    }

    // Set auto-progressing flag to prevent interference
    _isAutoProgressing = true;

    try {
      switch (_repeatMode) {
        case RepeatMode.one:
          // Repeat the current track
          if (kDebugMode) {
            print('🎵 [QueueManager] Repeat one: replaying current track');
          }
          // Reset position for replay
          _lastKnownPosition = 0.0;
          final success = await _playCurrentTrack();
          if (kDebugMode) {
            print('🎵 [QueueManager] Repeat one result: $success');
          }
          break;
        case RepeatMode.all:
        case RepeatMode.off:
          // Try to play next track
          if (hasNext) {
            if (kDebugMode) {
              print('🎵 [QueueManager] Auto-progressing to next track');
            }
            final nextIndex = _getNextIndex();
            if (nextIndex != null && nextIndex < _queue.length) {
              _currentIndex = nextIndex;
              // Update shuffle position if shuffle is enabled
              if (_isShuffleEnabled) {
                _shufflePosition = _shuffleIndices.indexOf(_currentIndex);
                if (kDebugMode) {
                  print('🎵 [QueueManager] Updated shuffle position to: $_shufflePosition');
                }
              }

              // Reset position tracking for new track
              _lastKnownPosition = 0.0;

              // Clean up old preloaded tracks but keep future ones
              _cleanupOldPreloadedTracks();

              if (kDebugMode) {
                print('🎵 [QueueManager] Auto-progressing to index $_currentIndex: ${currentTrack?.title}');
              }

              // Notify listeners of track change
              _currentTrackController.add(currentTrack);

              // Play the next track with seamless transition if pre-loaded
              final success = await _playCurrentTrackSeamlessly();
              if (kDebugMode) {
                print('🎵 [QueueManager] Auto-progression to track "${currentTrack?.title}" result: $success');
              }

              if (!success) {
                if (kDebugMode) {
                  print('❌ [QueueManager] Auto-progression failed, trying fallback play method');
                }
                // Fallback to regular play method if seamless fails
                await _playCurrentTrack();
              }
            } else {
              if (kDebugMode) {
                print('❌ [QueueManager] Invalid next index: $nextIndex');
              }
            }
          } else if (_repeatMode == RepeatMode.all && _queue.isNotEmpty) {
            // Restart from beginning if repeat all is enabled
            if (kDebugMode) {
              print('🎵 [QueueManager] Repeat all: restarting from beginning');
            }
            _currentIndex = 0;
            if (_isShuffleEnabled) {
              _shufflePosition = 0;
              // Regenerate shuffle for a fresh cycle
              _generateShuffleIndices();
            }
            _lastKnownPosition = 0.0;

            // Clear pre-loading state since we're restarting
            _stopPreloading();

            // Notify listeners of track change
            _currentTrackController.add(currentTrack);

            final success = await _playCurrentTrackSeamlessly();
            if (kDebugMode) {
              print('🎵 [QueueManager] Repeat all restart result: $success');
            }
          } else {
            if (kDebugMode) {
              print('🎵 [QueueManager] End of queue reached - playback complete');
            }
          }
          break;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error in track completion handling: $e');
      }
    } finally {
      // Always reset the auto-progressing flag, even on error
      _isAutoProgressing = false;
      _lastAutoProgressionTime = DateTime.now();
      if (kDebugMode) {
        print('🎵 [QueueManager] Auto-progression flag reset');
        print('🎵 [QueueManager] ===== AUTO-PROGRESSION END =====');
      }
    }
  }
  
  /// Set a new queue from a collection
  Future<bool> setQueue({
    required List<MusicTrack> tracks,
    required String collectionType,
    String? collectionId,
    Map<String, dynamic>? collectionMetadata,
    int startIndex = 0,
  }) async {
    try {
      if (tracks.isEmpty) {
        if (kDebugMode) {
          print('❌ [QueueManager] Cannot set empty queue');
        }
        return false;
      }
      
      // Ensure start index is valid
      if (startIndex < 0 || startIndex >= tracks.length) {
        startIndex = 0;
      }
      
      if (kDebugMode) {
        print('🎵 [QueueManager] Setting queue: ${tracks.length} tracks, starting at $startIndex');
        print('🎵 [QueueManager] Collection: $collectionType${collectionId != null ? ' ($collectionId)' : ''}');
        if (collectionType == 'single_track') {
          print('🎵 [QueueManager] Single track playback requested');
        }
      }
      
      // Flag to prevent automatic progression detection from interfering during setup
      _isHandlingManualSkip = true;

      // Update queue state BEFORE playing to ensure consistency
      _queue = List.from(tracks);
      _currentIndex = startIndex;
      _collectionType = collectionType;
      _collectionId = collectionId;
      _collectionMetadata = collectionMetadata;

      // Clear preloaded tracks when setting a new queue
      // This prevents old preloaded tracks from interfering with the new queue
      _stopPreloading();
      if (kDebugMode) {
        print('🎵 [QueueManager] Cleared preloaded tracks for new queue');
      }

      // Handle shuffle state based on collection type
      if (collectionType == 'single_track') {
        // Disable shuffle for single track playback
        _isShuffleEnabled = false;
        _shuffleIndices.clear();
        _shufflePosition = 0;
      } else if (_isShuffleEnabled) {
        // Regenerate shuffle indices for the new queue
        _generateShuffleIndices();
        _shufflePosition = _shuffleIndices.indexOf(_currentIndex);
        if (_shufflePosition == -1) {
          _shufflePosition = 0;
        }
        if (kDebugMode) {
          print('🎵 [QueueManager] Shuffle enabled for new queue, position: $_shufflePosition');
        }
      }

      // Notify listeners
      _queueController.add(_queue);
      _currentTrackController.add(currentTrack);

      // Reset position tracking for new queue
      _lastKnownPosition = 0.0;

      // Start playing the track at startIndex
      final success = await _playCurrentTrack();

      // Reset the manual skip flag after playback has started
      // Use a shorter delay to avoid interfering with auto-progression
      Future.delayed(const Duration(milliseconds: 50), () {
        if (!_isDisposed) {
          _isHandlingManualSkip = false;
          if (kDebugMode) {
            print('🎵 [QueueManager] Manual skip flag reset after queue setup');
          }
        }
      });

      return success;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error setting queue: $e');
      }
      _isHandlingManualSkip = false;
      return false;
    }
  }
  
  /// Play the current track using our own queue management (no MusicKit setQueue)
  Future<bool> _playCurrentTrack() async {
    final track = currentTrack;
    if (track == null) {
      if (kDebugMode) {
        print('❌ [QueueManager] No current track to play');
      }
      return false;
    }

    try {
      if (kDebugMode) {
        print('🎵 [QueueManager] Playing track: ${track.title} (${_currentIndex + 1}/${_queue.length})');
      }

      // Reset near end flag and position tracking for new track
      _isNearTrackEnd = false;
      _previousPosition = 0.0;
      _lastValidPosition = 0.0; // Reset backup position

      // Store Apple Music duration for accurate timing
      _storeAppleMusicDuration(track);

      // CRITICAL: Always ensure Apple Music's native shuffle is OFF
      await _musicKit.setShuffleMode(MusicPlayerShuffleMode.off);

      // Stop any current playback first
      try {
        await _musicKit.stop();
        await Future.delayed(const Duration(milliseconds: 100));
        if (kDebugMode) {
          print('🎵 [QueueManager] Stopped previous playback');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ [QueueManager] Could not stop previous playback: $e');
        }
      }

      // Play the track directly without using MusicKit's queue system
      bool success = false;

      // Check if we have a preloaded version of this track
      final preloadedTrack = _getPreloadedTrack(_currentIndex);
      if (kDebugMode) {
        print('🔍 [QueueManager] Checking for preloaded track at index $_currentIndex');
        print('🔍 [QueueManager] Preloaded tracks available: ${_preloadedTracks.keys.toList()}');
        print('🔍 [QueueManager] Currently preloading indices: ${_preloadingIndices.toList()}');
        print('🔍 [QueueManager] Found preloaded track: ${preloadedTrack?.title ?? 'None'}');
      }

      if (preloadedTrack != null) {
        if (kDebugMode) {
          print('✨ [QueueManager] Using preloaded track: ${preloadedTrack.title}');
        }
        success = await _playAppleMusicTrack(preloadedTrack);
      } else {
        // No preloaded version, use normal search/play logic
        if (track.service.toLowerCase().contains('apple') && track.id.isNotEmpty) {
          // For Apple Music tracks, play directly by track ID
          success = await _playAppleMusicTrack(track);
        } else {
          // For Spotify tracks or any non-Apple Music tracks, search for Apple Music equivalent
          success = await _searchAndPlayTrack(track);
        }
      }

      if (success) {
        // Notify listeners
        _currentTrackController.add(track);

        // Start enhanced preloading for next tracks
        _startEnhancedPreloading();

        if (kDebugMode) {
          print('✅ [QueueManager] Successfully started playback of: ${track.title}');
        }
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error playing track: $e');
      }
      return false;
    }
  }

  /// Play an Apple Music track directly by creating a single-item queue
  Future<bool> _playAppleMusicTrack(MusicTrack track) async {
    try {
      if (kDebugMode) {
        print('🍎 [QueueManager] Playing Apple Music track: ${track.title} (ID: ${track.id})');
      }

      // Create song resource for the track
      final Map<String, dynamic> songResource = _createSongResource(track);

      if (kDebugMode) {
        print('🎵 [QueueManager] Created song resource for: ${songResource['attributes']['name']}');
      }

      // Set a single-item queue and play
      await _musicKit.setQueue('songs', item: songResource);
      await Future.delayed(const Duration(milliseconds: 50));
      await _musicKit.play();

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Failed to play Apple Music track: $e');
      }
      return false;
    }
  }



  /// Search for and play a track in Apple Music
  /// When no exact match is found, moves to next track in queue automatically
  Future<bool> _searchAndPlayTrack(MusicTrack track) async {
    try {
      if (kDebugMode) {
        print('🔍 [QueueManager] Searching for Apple Music equivalent: ${track.title} by ${track.artist}');
      }

      // Search for the track in Apple Music catalog using ISRC if available
      MusicTrack? appleMusicTrack = _appleMusicService != null
          ? await _appleMusicService!.searchTrackByArtistAndTitle(
              track.artist,
              track.title,
              isrc: track.isrc, // Pass ISRC for exact matching
              spotifyUrl: track.url, // Pass Spotify URL to extract ISRC if needed
              targetDurationMs: track.durationMs, // Pass target duration for better matching
            )
          : null;

      appleMusicTrack = appleMusicTrack?.copyWith(original_url: track.url);

      if (appleMusicTrack != null) {
        if (kDebugMode) {
          print('✅ [QueueManager] Found Apple Music equivalent: ${appleMusicTrack.title} by ${appleMusicTrack.artist}');
        }

        // Create song resource from the found Apple Music track
        final Map<String, dynamic> songResource = _createSongResource(appleMusicTrack);

        // Play the Apple Music track
        await _musicKit.setQueue('songs', item: songResource);
        await Future.delayed(const Duration(milliseconds: 50));
        await _musicKit.play();

        return true;
      } else {
        if (kDebugMode) {
          print('❌ [QueueManager] No Apple Music equivalent found for: ${track.title} by ${track.artist}');
          print('🔄 [QueueManager] Moving to next track in queue due to no exact match');
        }

        // No exact match found - move to next track instead of fallback
        // This prevents playing wrong songs in queue context
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Search and play failed: $e');
      }
      return false;
    }
  }


  /// Create song resource for MusicKit
  Map<String, dynamic> _createSongResource(MusicTrack track) {
    if (kDebugMode) {
      print('🎵 [QueueManager] Creating song resource for: ${track.title}');
      print('🎵 [QueueManager] Track ID: ${track.id}, isLibrary: ${track.isLibrary}');
      print('🎵 [QueueManager] Track URL: ${track.url.isNotEmpty ? track.url : 'No URL'}');
      print('🎵 [QueueManager] Track URI: ${track.uri}');
      print('🎵 [QueueManager] Album art: ${track.albumArt.isNotEmpty ? track.albumArt.substring(0, math.min(50, track.albumArt.length)) : 'No art'}...');
    }
    
    // Base song resource structure
    final Map<String, dynamic> songResource = {
      'id': track.id,
      'type': 'songs',
      'attributes': {
        'name': track.title,
        'artistName': track.artist,
        'albumName': track.album,
        'artwork': {
          'url': track.albumArt.isNotEmpty 
                 ? track.albumArt.replaceAll('{w}', '300').replaceAll('{h}', '300') 
                 : 'https://via.placeholder.com/300?text=No+Art',
        },
        'durationInMillis': track.durationMs,
      },
    };

    // Add playParams - this is crucial for playback
    final Map<String, dynamic> playParams = {
      'id': track.id,
      'kind': 'song',
      'isLibrary': track.isLibrary,
    };

    // For user-uploaded tracks (typically starting with 'i.' and having isLibrary: true)
    // we might need to handle them slightly differently
    if (track.isLibrary && track.id.startsWith('i.')) {
      // User-uploaded track - ensure we don't add catalog-specific parameters
      playParams['reporting'] = false;
      if (kDebugMode) {
        print('🎵 [QueueManager] User-uploaded track detected, setting reporting: false');
      }
    } else {
      // Catalog track - can include reporting
      playParams['reporting'] = true;
      if (kDebugMode) {
        print('🎵 [QueueManager] Catalog track detected, setting reporting: true');
      }
    }

    songResource['attributes']['playParams'] = playParams;

    // Add URL if available
    if (track.url.isNotEmpty) {
      songResource['attributes']['url'] = track.url;
      if (kDebugMode) {
        print('🎵 [QueueManager] Added track URL to song resource');
      }
    }

    if (kDebugMode) {
      print('🎵 [QueueManager] Song resource created successfully');
    }

    return songResource;
  }
  
  /// Skip to next track
  Future<bool> skipToNext() async {
    if (kDebugMode) {
      print('🎵 [QueueManager] Manual skip to next called. Current: ${_currentIndex + 1}/${_queue.length}');
    }
    
    // Check for rapid button presses
    final now = DateTime.now();
    if (_lastSkipTime != null && now.difference(_lastSkipTime!) < _skipDebounceDelay) {
      if (kDebugMode) {
        print('🎵 [QueueManager] ⏸️ Skip to next ignored - too rapid (debounced)');
      }
      return false;
    }
    
    // Check if another skip operation is in progress
    if (_isSkipOperationInProgress) {
      if (kDebugMode) {
        print('🎵 [QueueManager] ⏸️ Skip to next ignored - operation already in progress');
      }
      return false;
    }
    
    // Set operation flags
    _isSkipOperationInProgress = true;
    _isHandlingManualSkip = true;
    _lastSkipTime = now;
    
    try {
      final nextIndex = _getNextIndex();
      if (nextIndex == null) {
        if (kDebugMode) {
          print('🎵 [QueueManager] No next track available - shuffle: $_isShuffleEnabled, repeat: $_repeatMode');
        }
        return false;
      }
      
      if (kDebugMode) {
        print('🎵 [QueueManager] Next index: $nextIndex, track: ${_queue[nextIndex].title}');
      }
      
      // Update index and shuffle position immediately
      _currentIndex = nextIndex;
      if (_isShuffleEnabled) {
        _shufflePosition = _shuffleIndices.indexOf(_currentIndex);
      }
      
      // Reset position for new track
      _lastKnownPosition = 0.0;
      
      // Play the track
      final success = await _playCurrentTrack();
      
      if (kDebugMode) {
        print('🎵 [QueueManager] Skip to next result: $success');
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error in skip to next: $e');
      }
      return false;
    } finally {
      // Always reset flags, even on error
      _isSkipOperationInProgress = false;
      _isHandlingManualSkip = false;
    }
  }
  
  /// Skip to previous track
  Future<bool> skipToPrevious() async {
    if (kDebugMode) {
      print('🎵 [QueueManager] Manual skip to previous called. Current: ${_currentIndex + 1}/${_queue.length}');
    }
    
    // Check for rapid button presses
    final now = DateTime.now();
    if (_lastSkipTime != null && now.difference(_lastSkipTime!) < _skipDebounceDelay) {
      if (kDebugMode) {
        print('🎵 [QueueManager] ⏸️ Skip to previous ignored - too rapid (debounced)');
      }
      return false;
    }
    
    // Check if another skip operation is in progress
    if (_isSkipOperationInProgress) {
      if (kDebugMode) {
        print('🎵 [QueueManager] ⏸️ Skip to previous ignored - operation already in progress');
      }
      return false;
    }
    
    // Set operation flags
    _isSkipOperationInProgress = true;
    _isHandlingManualSkip = true;
    _lastSkipTime = now;
    
    try {
      final previousIndex = _getPreviousIndex();
      if (previousIndex == null) {
        if (kDebugMode) {
          print('🎵 [QueueManager] No previous track available - shuffle: $_isShuffleEnabled');
        }
        _isHandlingManualSkip = false;
        _isSkipOperationInProgress = false;
        return false;
      }
      
      if (kDebugMode) {
        print('🎵 [QueueManager] Previous index: $previousIndex, track: ${_queue[previousIndex].title}');
      }
      
      // Update index and shuffle position immediately
      _currentIndex = previousIndex;
      if (_isShuffleEnabled) {
        _shufflePosition = _shuffleIndices.indexOf(_currentIndex);
      }
      
      // Reset position for new track
      _lastKnownPosition = 0.0;
      
      // Play the track
      final success = await _playCurrentTrack();
      
      if (kDebugMode) {
        print('🎵 [QueueManager] Skip to previous result: $success');
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error in skip to previous: $e');
      }
      _isHandlingManualSkip = false;
      return false;
    } finally {
      // Always reset flags, even on error
      _isSkipOperationInProgress = false;
    }
  }
  
  /// Skip to specific index
  Future<bool> skipToIndex(int index) async {
    if (index < 0 || index >= _queue.length) {
      if (kDebugMode) {
        print('❌ [QueueManager] Invalid index: $index (queue length: ${_queue.length})');
      }
      return false;
    }
    
    // Check if another skip operation is in progress
    if (_isSkipOperationInProgress) {
      if (kDebugMode) {
        print('🎵 [QueueManager] ⏸️ Skip to index ignored - operation already in progress');
      }
      return false;
    }
    
    if (kDebugMode) {
      print('🎵 [QueueManager] Skipping to index $index: ${_queue[index].title}');
    }
    
    // Set operation flags
    _isSkipOperationInProgress = true;
    _isHandlingManualSkip = true;
    _lastSkipTime = DateTime.now();
    
    try {
      _currentIndex = index;
      
      // Update shuffle position if shuffle is enabled
      if (_isShuffleEnabled) {
        _shufflePosition = _shuffleIndices.indexOf(_currentIndex);
        if (kDebugMode) {
          print('🎵 [QueueManager] Updated shuffle position to: $_shufflePosition');
        }
      }
      
      // Reset position for new track
      _lastKnownPosition = 0.0;
      
      final success = await _playCurrentTrack();
      if (kDebugMode) {
        print('🎵 [QueueManager] Skip to index $index result: $success');
      }
      
      _isHandlingManualSkip = false;
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error in skip to index: $e');
      }
      _isHandlingManualSkip = false;
      return false;
    } finally {
      // Always reset flags, even on error
      _isSkipOperationInProgress = false;
    }
  }
  
  /// Get next index considering shuffle and repeat modes
  int? _getNextIndex() {
    if (_queue.isEmpty) return null;
    
    if (_isShuffleEnabled) {
      // Shuffle mode
      if (_shufflePosition + 1 < _shuffleIndices.length) {
        return _shuffleIndices[_shufflePosition + 1];
      } else if (_repeatMode == RepeatMode.all) {
        // Restart shuffle from beginning
        return _shuffleIndices[0];
      }
      return null;
    } else {
      // Normal mode
      if (_currentIndex + 1 < _queue.length) {
        return _currentIndex + 1;
      } else if (_repeatMode == RepeatMode.all) {
        return 0;
      }
      return null;
    }
  }
  
  /// Get previous index considering shuffle mode
  int? _getPreviousIndex() {
    if (_queue.isEmpty) return null;
    
    if (_isShuffleEnabled) {
      // Shuffle mode
      if (_shufflePosition > 0) {
        return _shuffleIndices[_shufflePosition - 1];
      }
      return null;
    } else {
      // Normal mode
      if (_currentIndex > 0) {
        return _currentIndex - 1;
      }
      return null;
    }
  }
  
  /// Ensure Apple Music's native shuffle is disabled
  /// This is critical for proper queue control when we handle shuffling ourselves
  Future<bool> ensureShuffleDisabled() async {
    try {
      if (_isShuffleEnabled) {
        _isShuffleEnabled = false;
        _shuffleIndices.clear();
        _shufflePosition = 0;

        if (kDebugMode) {
          print('🎵 [QueueManager] Disabling Apple Music native shuffle for queue control');
        }
      }

      // ALWAYS set Apple Music shuffle to OFF to prevent library shuffling
      await _musicKit.setShuffleMode(MusicPlayerShuffleMode.off);

      _shuffleController.add(false);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error ensuring shuffle disabled: $e');
      }
      return false;
    }
  }

  /// Disable only Apple Music's native shuffle without affecting our internal shuffle state
  /// This is used when we want to preserve pre-shuffled track order from shuffle algorithms
  Future<bool> disableNativeShuffleOnly() async {
    try {
      if (kDebugMode) {
        print('🎵 [QueueManager] Disabling Apple Music native shuffle only (preserving internal state)');
      }

      // ONLY disable Apple Music's native shuffle, don't touch our internal shuffle state
      await _musicKit.setShuffleMode(MusicPlayerShuffleMode.off);

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error disabling native shuffle: $e');
      }
      return false;
    }
  }

  /// Toggle shuffle mode
  Future<bool> toggleShuffle() async {
    try {
      _isShuffleEnabled = !_isShuffleEnabled;

      if (_isShuffleEnabled && _queue.isNotEmpty) {
        _generateShuffleIndices();
        // Find current track in shuffle order
        _shufflePosition = _shuffleIndices.indexOf(_currentIndex);
        if (_shufflePosition == -1) {
          // If current index not found, start from beginning
          _shufflePosition = 0;
        }

        if (kDebugMode) {
          print('🎵 [QueueManager] Generated shuffle order: $_shuffleIndices');
          print('🎵 [QueueManager] Current shuffle position: $_shufflePosition');
        }
      } else if (!_isShuffleEnabled) {
        // Clear shuffle state when disabled
        _shuffleIndices.clear();
        _shufflePosition = 0;
      }

      if (kDebugMode) {
        print('🎵 [QueueManager] Shuffle ${_isShuffleEnabled ? 'enabled' : 'disabled'}');
      }

      // CRITICAL: Never enable Apple Music's native shuffle when we control the queue
      // Always keep it OFF to prevent shuffling from the entire Apple Music library
      await _musicKit.setShuffleMode(MusicPlayerShuffleMode.off);

      _shuffleController.add(_isShuffleEnabled);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error toggling shuffle: $e');
      }
      return false;
    }
  }
  
  /// Set repeat mode
  Future<bool> setRepeatMode(RepeatMode mode) async {
    try {
      _repeatMode = mode;
      
      if (kDebugMode) {
        print('🎵 [QueueManager] Repeat mode set to: $mode');
      }
      
      // Set repeat mode in MusicKit
      MusicPlayerRepeatMode musicKitMode;
      switch (mode) {
        case RepeatMode.one:
          musicKitMode = MusicPlayerRepeatMode.one;
          break;
        case RepeatMode.all:
          musicKitMode = MusicPlayerRepeatMode.all;
          break;
        case RepeatMode.off:
          musicKitMode = MusicPlayerRepeatMode.none;
          break;
      }
      
      await _musicKit.setRepeatMode(musicKitMode);
      
      _repeatModeController.add(_repeatMode);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [QueueManager] Error setting repeat mode: $e');
      }
      return false;
    }
  }
  
  /// Toggle repeat mode (off -> all -> one -> off)
  Future<bool> toggleRepeatMode() async {
    RepeatMode nextMode;
    switch (_repeatMode) {
      case RepeatMode.off:
        nextMode = RepeatMode.all;
        break;
      case RepeatMode.all:
        nextMode = RepeatMode.one;
        break;
      case RepeatMode.one:
        nextMode = RepeatMode.off;
        break;
    }
    
    return await setRepeatMode(nextMode);
  }
  
  /// Generate shuffle indices
  void _generateShuffleIndices() {
    if (_queue.isEmpty) {
      _shuffleIndices.clear();
      _shufflePosition = 0;
      return;
    }

    _shuffleIndices = List.generate(_queue.length, (index) => index);

    // Use Fisher-Yates shuffle for better randomization
    final random = math.Random();
    for (var i = _shuffleIndices.length - 1; i > 0; i--) {
      var j = random.nextInt(i + 1);
      var temp = _shuffleIndices[i];
      _shuffleIndices[i] = _shuffleIndices[j];
      _shuffleIndices[j] = temp;
    }

    _shufflePosition = 0;

    if (kDebugMode) {
      print('🎵 [QueueManager] Generated shuffle order: $_shuffleIndices');
      print('🎵 [QueueManager] Queue length: ${_queue.length}, Shuffle indices length: ${_shuffleIndices.length}');
    }
  }
  
  /// Clear the queue
  void clearQueue() {
    if (kDebugMode) {
      print('🎵 [QueueManager] Clearing queue');
    }
    
    _queue.clear();
    _currentIndex = 0;
    _shuffleIndices.clear();
    _shufflePosition = 0;
    _collectionType = null;
    _collectionId = null;
    _collectionMetadata = null;
    _currentAppleMusicDuration = 0.0; // Clear stored duration

    // Clear preloaded tracks when clearing the queue
    _stopPreloading();

    _queueController.add(_queue);
    _currentTrackController.add(null);
  }
  
  /// Add track to queue
  void addToQueue(MusicTrack track) {
    _queue.add(track);
    _queueController.add(_queue);
    
    // Regenerate shuffle if needed
    if (_isShuffleEnabled) {
      _generateShuffleIndices();
    }
    
    if (kDebugMode) {
      print('🎵 [QueueManager] Added track to queue: ${track.title}');
    }
  }
  
  /// Remove track from queue
  void removeFromQueue(int index) {
    if (index < 0 || index >= _queue.length) return;
    
    final removedTrack = _queue.removeAt(index);
    
    // Adjust current index if necessary
    if (index < _currentIndex) {
      _currentIndex--;
    } else if (index == _currentIndex && _currentIndex >= _queue.length) {
      _currentIndex = _queue.length - 1;
    }
    
    // Regenerate shuffle if needed
    if (_isShuffleEnabled) {
      _generateShuffleIndices();
    }
    
    _queueController.add(_queue);
    _currentTrackController.add(currentTrack);
    
    if (kDebugMode) {
      print('🎵 [QueueManager] Removed track from queue: ${removedTrack.title}');
    }
  }
  
  /// Get queue position info
  Map<String, dynamic> getQueueInfo() {
    return {
      'currentIndex': _currentIndex,
      'totalTracks': _queue.length,
      'currentTrack': currentTrack?.toJson(),
      'isShuffleEnabled': _isShuffleEnabled,
      'repeatMode': _repeatMode.toString(),
      'collectionType': _collectionType,
      'collectionId': _collectionId,
      'hasNext': hasNext,
      'hasPrevious': hasPrevious,
    };
  }
  
  /// Dispose the queue manager
  void dispose() {
    if (kDebugMode) {
      print('🎵 [QueueManager] Disposing...');
    }

    _isDisposed = true;
    _playerStateSubscription?.cancel();
    _playerQueueSubscription?.cancel();
    _stopPositionTracking();
    _currentTrackController.close();
    _queueController.close();
    _shuffleController.close();
    _repeatModeController.close();

    if (kDebugMode) {
      print('✅ [QueueManager] Disposed successfully');
    }
  }
} 