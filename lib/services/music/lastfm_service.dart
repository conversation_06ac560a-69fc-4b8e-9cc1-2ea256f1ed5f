import 'dart:convert';
import 'package:http/http.dart' as http;
import 'dart:io'; // Add this import for HttpClient
import 'package:flutter/foundation.dart';
import 'dart:math' as math;
import '../../config/constants.dart';

class LastFmService {
  // TODO: Replace with your actual Last.fm API key from https://www.last.fm/api/account/create
  static const String _apiKey = 'fd7597d7a76640941f4db64e38649529';
  static final String _baseUrl =
      '${AppConstants.baseApiUrl}/lastfm/';
  // static const String _baseUrl = 'https://ws.audioscrobbler.com/2.0/';

  // Check if API key is configured
  bool get isConfigured =>
      _apiKey != 'YOUR_LASTFM_API_KEY' && _apiKey.isNotEmpty;

  // Rate limiting
  DateTime _lastRequestTime =
      DateTime.now().subtract(const Duration(seconds: 1));
  static const Duration _minRequestInterval =
      Duration(milliseconds: 200); // 5 requests/sec

  // Caching
  final Map<String, List<String>> _similarArtistsCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(hours: 6);

  // Exploration control
  int maxDepth = 3;
  int maxArtistsPerLevel = 10;
  double explorationDecay = 0.7; // Reduce exploration at each level

  // HTTP Client management for SSL issues
  static http.Client? _httpClient;
  static DateTime? _clientCreatedAt;
  static const Duration _clientMaxAge = Duration(minutes: 5);

  // Get or create HTTP client with proper SSL handling
  http.Client _getHttpClient() {
    final now = DateTime.now();

    // Create new client if none exists or if it's too old
    if (_httpClient == null ||
        _clientCreatedAt == null ||
        now.difference(_clientCreatedAt!) > _clientMaxAge) {
      // Close old client if exists
      _httpClient?.close();

      // Create new client with custom SSL handling

      _httpClient = http.Client();
      _clientCreatedAt = now;

      print('🔄 [LastFM] Created new HTTP client');
    }

    return _httpClient!;
  }

  // Clean up HTTP client
  static void disposeHttpClient() {
    _httpClient?.close();
    _httpClient = null;
    _clientCreatedAt = null;
    print('🧹 [LastFM] Disposed HTTP client');
  }

  Future<void> _waitForRateLimit() async {
    final diff = DateTime.now().difference(_lastRequestTime);
    if (diff < _minRequestInterval) {
      await Future.delayed(_minRequestInterval - diff);
    }
    _lastRequestTime = DateTime.now();
  }

  bool _isCacheValid(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return false;
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  /// Clean artist names from API to fix encoding issues while preserving original formatting
  /// This method ONLY removes encoding corruption and control characters
  /// It does NOT normalize case or change the artist name formatting
  String _cleanArtistNameFromAPI(String name) {
    if (name.isEmpty) return name;

    // IMPORTANT: Only fix encoding corruption, preserve original case and formatting
    // This ensures artist names are saved to backend exactly as they appear in music services
    final cleaned = name
        .replaceAll(RegExp(r'ÃâÂ°'), '') // Remove specific corruption
        .replaceAll(RegExp(r'Ã¢â¬â'), '') // Remove specific corruption
        .replaceAll(RegExp(r'Ãâ'), '') // Remove specific corruption
        .replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'),
            '') // Remove control chars
        .trim();

    print('🎵 [LastFM] Cleaned artist name: "$name" → "$cleaned"');
    return cleaned;
  }

  void _setCacheEntry(String key, dynamic value) {
    _cacheTimestamps[key] = DateTime.now();
    if (value is List<String>) {
      if (key.startsWith('similar_')) {
        _similarArtistsCache[key] = value;
      }
    }
  }

  /// Get similar artists from Last.fm API
  Future<List<String>> getSimilarArtists(String artistName,
      {int limit = 20}) async {
    print(
        '🚀 [LastFM DEBUG] getSimilarArtists called with: "$artistName", limit: $limit');

    try {
      if (artistName.isEmpty) {
        print('❌ [LastFM DEBUG] Empty artist name provided');
        return [];
      }

      print('🔧 [LastFM DEBUG] API key configured: $isConfigured');
      print('🔧 [LastFM DEBUG] API key value: ${_apiKey.substring(0, 8)}...');

      // Check if API key is configured
      if (!isConfigured) {
        print('⚠️ [LastFM] API key not configured, returning empty results');
        return [];
      }

      final cacheKey = 'similar_${artistName.toLowerCase()}';
      if (_isCacheValid(cacheKey) &&
          _similarArtistsCache.containsKey(cacheKey)) {
        print('📦 [LastFM] Using cached similar artists for: $artistName');
        return _similarArtistsCache[cacheKey]!;
      }

      await _waitForRateLimit();

      final url = Uri.parse(_baseUrl).replace(queryParameters: {
        'method': 'artist.getSimilar',
        'artist': artistName,
        'api_key': _apiKey,
        'format': 'json',
        'limit': limit.toString(),
      });

      print('🎵 [LastFM] Getting similar artists for: $artistName');

      print('🌐 [LastFM DEBUG] Making HTTP request to: $url');

      final client = _getHttpClient();
      http.Response response;

      try {
        response = await client.get(url).timeout(const Duration(seconds: 10));
        print(
            '📡 [LastFM DEBUG] HTTP response received: ${response.statusCode}');
      } catch (httpError) {
        print('❌ [LastFM DEBUG] HTTP error: $httpError');
        print('❌ [LastFM DEBUG] Error type: ${httpError.runtimeType}');

        if (httpError is HandshakeException ||
            httpError.toString().contains('HandshakeException') ||
            httpError.toString().contains('WRONG_VERSION_NUMBER')) {
          print(
              '🔒 [LastFM] SSL/TLS Handshake error, recreating client and retrying...');

          // Dispose current client and force recreation
          disposeHttpClient();
          final newClient = _getHttpClient();

          try {
            // Retry once with new client
            response =
                await newClient.get(url).timeout(const Duration(seconds: 10));
            print('✅ [LastFM] Retry successful after recreating HTTP client');
          } catch (retryError) {
            print('❌ [LastFM] Retry failed: $retryError');
            // For SSL errors, return empty list and rely on Spotify fallback
            return [];
          }
        } else {
          rethrow;
        }
      }

      if (response.statusCode == 200) {
        // Ensure proper UTF-8 decoding
        String responseBody;
        try {
          responseBody = utf8.decode(response.bodyBytes);
        } catch (e) {
          print('⚠️ [LastFM] UTF-8 decode failed, using response.body: $e');
          responseBody = response.body;
        }

        final data = json.decode(responseBody);

        if (data['error'] != null) {
          print('❌ [LastFM] API Error: ${data['message']}');
          return [];
        }

        final similarArtists = data['similarartists'];
        if (similarArtists == null || similarArtists['artist'] == null) {
          print('⚠️ [LastFM] No similar artists found for: $artistName');
          return [];
        }

        final artists = similarArtists['artist'] as List;
        final artistNames = artists
            .map((artist) {
              String name = artist['name'] as String;

              // Clean up any encoding issues in artist names from Last.fm
              name = _cleanArtistNameFromAPI(name);

              final match = double.tryParse(artist['match'].toString()) ?? 0.0;
              return {'name': name, 'match': match};
            })
            .where((artist) =>
                    (artist['match'] as double >
                        0.1) && // Filter low similarity
                    (artist['name'] as String)
                        .isNotEmpty && // Filter empty names
                    !(artist['name'] as String)
                        .contains('ÃâÂ') // Filter corrupted names
                )
            .toList();

        // Sort by similarity and extract names
        artistNames.sort(
            (a, b) => (b['match'] as double).compareTo(a['match'] as double));
        final result =
            artistNames.map((artist) => artist['name'] as String).toList();

        _setCacheEntry(cacheKey, result);
        print(
            '✅ [LastFM] Found ${result.length} clean similar artists for: $artistName');
        return result;
      } else {
        print('❌ [LastFM] HTTP Error ${response.statusCode}: ${response.body}');
        return [];
      }
    } catch (e) {
      print('❌ [LastFM] Exception getting similar artists for $artistName: $e');
      return [];
    }
  }

  /// Recursively discover similar artists with exploration control
  Future<Map<String, dynamic>> discoverSimilarArtists({
    required List<String> seedArtists,
    int depth = 0,
    int explorationRate = 5,
  }) async {
    final Map<String, double> allArtists = {}; // Artist -> similarity score
    final Set<String> processedArtists = <String>{};

    print(
        '🔍 [LastFM] Starting discovery at depth $depth with ${seedArtists.length} seed artists');
    print('🎯 [LastFM] Exploration rate: $explorationRate');

    // Queue for BFS-style discovery
    final queue = <Map<String, dynamic>>[];

    // Initialize queue with seed artists
    for (final artist in seedArtists) {
      queue.add({
        'artist': artist,
        'depth': 0,
        'similarity': 1.0, // Seed artists have max similarity
      });
      allArtists[artist] = 1.0;
    }

    while (queue.isNotEmpty && depth <= maxDepth) {
      final current = queue.removeAt(0);
      final currentArtist = current['artist'] as String;
      final currentDepth = current['depth'] as int;
      final currentSimilarity = current['similarity'] as double;

      if (processedArtists.contains(currentArtist.toLowerCase()) ||
          currentDepth > maxDepth) {
        continue;
      }

      processedArtists.add(currentArtist.toLowerCase());

      try {
        // Get similar artists for current artist
        final adjustedExplorationRate =
            (explorationRate * math.pow(explorationDecay, currentDepth))
                .round();
        final similarArtists = await getSimilarArtists(
          currentArtist,
          limit: math.max(adjustedExplorationRate, 3),
        );

        // Add similar artists to discovery queue
        for (int i = 0; i < similarArtists.length; i++) {
          final similarArtist = similarArtists[i];
          final similarityDecay =
              1.0 - (i * 0.1); // Reduce similarity for lower-ranked artists
          final combinedSimilarity =
              currentSimilarity * similarityDecay * explorationDecay;

          if (!processedArtists.contains(similarArtist.toLowerCase()) &&
              combinedSimilarity > 0.05) {
            // Minimum similarity threshold

            // Update or add artist with best similarity score
            if (!allArtists.containsKey(similarArtist) ||
                allArtists[similarArtist]! < combinedSimilarity) {
              allArtists[similarArtist] = combinedSimilarity;
            }

            // Add to queue for further exploration
            if (currentDepth < maxDepth) {
              queue.add({
                'artist': similarArtist,
                'depth': currentDepth + 1,
                'similarity': combinedSimilarity,
              });
            }
          }
        }

        print(
            '✅ [LastFM] Processed ${currentArtist} (depth: $currentDepth, similar: ${similarArtists.length})');
      } catch (e) {
        print(
            '❌ [LastFM] Error processing ${currentArtist} at depth $currentDepth: $e');
      }
    }

    // Sort artists by similarity score
    final sortedArtists = allArtists.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final result = {
      'artists': sortedArtists.map((entry) => entry.key).toList(),
      'similarities': Map.fromEntries(sortedArtists),
      'totalProcessed': processedArtists.length,
      'maxDepthReached': depth,
    };

    print(
        '🎉 [LastFM] Discovery complete! Found ${sortedArtists.length} total artists');
    print(
        '📊 [LastFM] Processed ${processedArtists.length} artists across ${depth + 1} levels');

    return result;
  }

  /// Get similar artists for multiple seed artists with weighted results
  Future<List<String>> getWeightedSimilarArtists({
    required List<String> seedArtists,
    int limit = 50,
    double minSimilarity = 0.1,
  }) async {
    try {
      final Map<String, double> artistScores = {};
      final Set<String> processedSeeds = <String>{};

      print(
          '🔗 [LastFM] Getting weighted similar artists for ${seedArtists.length} seeds');

      for (int i = 0; i < seedArtists.length; i++) {
        final seedArtist = seedArtists[i];
        if (processedSeeds.contains(seedArtist.toLowerCase())) continue;

        processedSeeds.add(seedArtist.toLowerCase());

        // Weight decreases for later seed artists
        final seedWeight = 1.0 - (i * 0.1);

        final similarArtists = await getSimilarArtists(seedArtist, limit: 30);

        for (int j = 0; j < similarArtists.length; j++) {
          final artist = similarArtists[j];
          final positionWeight =
              1.0 - (j * 0.03); // Decrease weight for lower positions
          final finalScore = seedWeight * positionWeight;

          // Accumulate scores for artists found multiple times
          artistScores[artist] = (artistScores[artist] ?? 0.0) + finalScore;
        }

        print(
            '✅ [LastFM] Processed seed: $seedArtist (${similarArtists.length} similar)');
      }

      // Filter and sort by accumulated score
      final filteredArtists = artistScores.entries
          .where((entry) => entry.value >= minSimilarity)
          .toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      final result =
          filteredArtists.take(limit).map((entry) => entry.key).toList();

      print('🎯 [LastFM] Weighted discovery found ${result.length} artists');
      return result;
    } catch (e) {
      print('❌ [LastFM] Error in weighted similar artists: $e');
      return [];
    }
  }

  /// Configure exploration parameters
  void configureExploration({
    int? maxDepth,
    int? maxArtistsPerLevel,
    double? explorationDecay,
  }) {
    if (maxDepth != null) this.maxDepth = maxDepth;
    if (maxArtistsPerLevel != null)
      this.maxArtistsPerLevel = maxArtistsPerLevel;
    if (explorationDecay != null) this.explorationDecay = explorationDecay;

    print(
        '⚙️ [LastFM] Exploration configured: depth=$maxDepth, artists/level=$maxArtistsPerLevel, decay=$explorationDecay');
  }

  /// Clear cache
  void clearCache() {
    _similarArtistsCache.clear();
    _cacheTimestamps.clear();
    print('🧹 [LastFM] Cache cleared');
  }

  /// Get cache statistics
  Map<String, int> getCacheStats() {
    return {
      'similarArtists': _similarArtistsCache.length,
      'timestamps': _cacheTimestamps.length,
    };
  }

  /// Get top artists for a specific genre from Last.fm
  Future<List<Map<String, dynamic>>> getTopArtistsByGenre(String genre,
      {int limit = 20}) async {
    print('🚀 [LastFM] Getting top artists for genre: "$genre", limit: $limit');

    try {
      if (genre.isEmpty) {
        print('❌ [LastFM] Empty genre provided');
        return [];
      }

      // Check if API key is configured
      if (!isConfigured) {
        print('⚠️ [LastFM] API key not configured, returning empty results');
        return [];
      }

      final cacheKey = 'top_artists_${genre.toLowerCase()}';
      if (_isCacheValid(cacheKey)) {
        print('📦 [LastFM] Using cached top artists for genre: $genre');
        return _similarArtistsCache[cacheKey] as List<Map<String, dynamic>>;
      }

      await _waitForRateLimit();

      // Use the base Last.fm API URL for this request
      final url = Uri.parse('https://ws.audioscrobbler.com/2.0/')
          .replace(queryParameters: {
        'method': 'tag.gettopartists',
        'tag': genre,
        'api_key': _apiKey,
        'format': 'json',
        'limit': limit.toString(),
      });

      print('🌐 [LastFM DEBUG] Making HTTP request to: $url');

      final client = _getHttpClient();
      http.Response response;

      try {
        response = await client.get(url).timeout(const Duration(seconds: 10));
        print(
            '📡 [LastFM DEBUG] HTTP response received: ${response.statusCode}');
      } catch (httpError) {
        print('❌ [LastFM DEBUG] HTTP error: $httpError');
        return [];
      }

      if (response.statusCode == 200) {
        // Ensure proper UTF-8 decoding
        String responseBody;
        try {
          responseBody = utf8.decode(response.bodyBytes);
        } catch (e) {
          print('⚠️ [LastFM] UTF-8 decode failed, using response.body: $e');
          responseBody = response.body;
        }

        final data = json.decode(responseBody);

        if (data['error'] != null) {
          print('❌ [LastFM] API Error: ${data['message']}');
          return [];
        }

        final topArtists = data['topartists']?['artist'] as List?;
        if (topArtists == null || topArtists.isEmpty) {
          print('⚠️ [LastFM] No top artists found for genre: $genre');
          return [];
        }

        final processedArtists = topArtists.map((artist) {
          // Extract the largest image URL
          final images = artist['image'] as List?;
          String? imageUrl;
          if (images != null && images.isNotEmpty) {
            final largeImage = images.lastWhere(
                (img) => img['size'] == 'extralarge' || img['size'] == 'mega',
                orElse: () => images.last);
            imageUrl = largeImage['#text'];
          }

          return {
            'name': _cleanArtistNameFromAPI(artist['name'] as String),
            'url': artist['url'] as String,
            'image_url': imageUrl,
            'rank':
                int.tryParse(artist['@attr']?['rank']?.toString() ?? '0') ?? 0,
          };
        }).toList();

        // Cache the results
        _setCacheEntry(cacheKey, processedArtists);

        print(
            '✅ [LastFM] Found ${processedArtists.length} top artists for genre: $genre');
        return processedArtists;
      } else {
        print('❌ [LastFM] HTTP Error ${response.statusCode}: ${response.body}');
        return [];
      }
    } catch (e) {
      print('❌ [LastFM] Exception getting top artists for genre $genre: $e');
      return [];
    }
  }
}
