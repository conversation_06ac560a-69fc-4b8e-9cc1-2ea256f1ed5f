import '../../models/api_response.dart';
import '../../models/music/music_track.dart';
import '../api_service.dart';
import '../auth_service.dart';

enum MusicServiceType {
  spotify,
  apple,
  soundcloud,
}

class MusicService {
  final ApiService _apiService;
  final AuthService _authService;

  MusicService(this._apiService, this._authService);

  Future<List<MusicService>> getConnectedServices() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/music/services/connected_services/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> services = response.data as List<dynamic>;
    return services.map((json) => MusicService.fromJson(json)).toList();
  }

  Future<ApiResponse> disconnectService(MusicServiceType serviceType) async {
    final token = await _authService.getToken();
    return await _apiService.delete(
      '/api/music/services/disconnect/${serviceType.name}/',
      token: token,
    );
  }

  Future<List<MusicTrack>> searchTracks(String query) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/music/tracks/search/',
      queryParams: {
        'query': query,
      },
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> tracks = response.data as List<dynamic>;
    return tracks.map((json) => MusicTrack.fromJson(json)).toList();
  }

  Future<List<MusicTrack>> getRecentlyPlayed() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/music/tracks/recently_played/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> tracks = response.data as List<dynamic>;
    return tracks.map((json) => MusicTrack.fromJson(json)).toList();
  }

  Future<List<MusicTrack>> getSavedTracks() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/music/tracks/saved_tracks/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> tracks = response.data as List<dynamic>;
    return tracks.map((json) => MusicTrack.fromJson(json)).toList();
  }

  Future<List<dynamic>> getPlaylists() async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/music/tracks/playlists/',
      token: token,
    );

    if (!response.success) return [];

    return response.data as List<dynamic>;
  }

  Future<List<MusicTrack>> getPlaylistTracks(
    String service,
    String playlistId,
  ) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/music/tracks/playlist/$service/$playlistId/',
      token: token,
    );

    if (!response.success) return [];

    final List<dynamic> tracks = response.data as List<dynamic>;
    return tracks.map((json) => MusicTrack.fromJson(json)).toList();
  }

  Future<MusicTrack?> getTrackDetails(
    String service,
    String trackId,
  ) async {
    final token = await _authService.getToken();
    final response = await _apiService.get(
      '/api/music/tracks/track/$service/$trackId/',
      token: token,
    );

    if (!response.success) return null;

    return MusicTrack.fromJson(response.data);
  }
} 