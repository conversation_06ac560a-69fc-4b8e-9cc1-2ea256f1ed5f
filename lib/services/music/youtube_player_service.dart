import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';
import '../../models/music_track.dart';
import '../../config/constants.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:string_similarity/string_similarity.dart';

class _VideoCandidate {
  final String videoId;
  final double score;

  _VideoCandidate({required this.videoId, required this.score});

  @override
  String toString() => 'Candidate(videoId: $videoId, score: $score)';
}

typedef StartPiPCallback = void Function();

class YouTubePlayerService {
  // YouTube API key from environment variables
  static String get _apiKey => AppConstants.youTubeApiKey;
  static final String _apiBaseUrl =
      AppConstants.baseApiUrl;

  // State controllers
  final StreamController<bool> _isPlayingController =
      StreamController<bool>.broadcast();
  final StreamController<MusicTrack?> _currentTrackController =
      StreamController<MusicTrack?>.broadcast();
  final StreamController<Duration> _positionController =
      StreamController<Duration>.broadcast();
  final StreamController<Duration> _durationController =
      StreamController<Duration>.broadcast();
  final StreamController<List<MusicTrack>> _queueController =
      StreamController<List<MusicTrack>>.broadcast();

  // Fallback event controllers
  final StreamController<void> _onWillTryNextCandidateController =
      StreamController<void>.broadcast();
  final StreamController<void> _onCandidatePlaybackConfirmedController =
      StreamController<void>.broadcast();
  final StreamController<void> _onAllCandidatesFailedController =
      StreamController<void>.broadcast();

  // Player controller
  YoutubePlayerController? _controller;
  StartPiPCallback? _startPiPCallback;

  // Current state
  bool _isInitialized = false;
  bool _isPlaying = false;
  MusicTrack? _currentTrack;
  String? _currentVideoId;
  List<MusicTrack> _queue = [];
  int _currentQueueIndex = -1;

  // Fallback mechanism state
  List<_VideoCandidate> _videoCandidates = [];
  int _currentCandidateIndex = -1;
  Timer? _fallbackTimer;
  StreamSubscription? _playerStateSubscription;
  Set<String> _triedVideoIds = {}; // Track already tried video IDs

  // Position tracking
  Timer? _positionTimer;

  // Debouncing mechanism to prevent rapid clicks
  Timer? _debounceTimer;
  bool isProcessingRequest = false;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isPlaying => _isPlaying;
  MusicTrack? get currentTrack => _currentTrack;
  YoutubePlayerController? get controller => _controller;
  List<MusicTrack> get queue => List.unmodifiable(_queue);
  int get currentQueueIndex => _currentQueueIndex;
  bool get hasNext => _currentQueueIndex < _queue.length - 1;
  bool get hasPrevious => _currentQueueIndex > 0;

  // Streams
  Stream<bool> get isPlayingStream => _isPlayingController.stream;
  Stream<MusicTrack?> get currentTrackStream => _currentTrackController.stream;
  Stream<Duration> get positionStream => _positionController.stream;
  Stream<Duration> get durationStream => _durationController.stream;
  Stream<List<MusicTrack>> get queueStream => _queueController.stream;

  // Fallback event streams
  Stream<void> get onWillTryNextCandidate =>
      _onWillTryNextCandidateController.stream;
  Stream<void> get onCandidatePlaybackConfirmed =>
      _onCandidatePlaybackConfirmedController.stream;
  Stream<void> get onAllCandidatesFailed =>
      _onAllCandidatesFailedController.stream;

  /// Initialize the service
  Future<void> initialize({StartPiPCallback? startPiPCallback}) async {
    _startPiPCallback = startPiPCallback;
    if (kDebugMode) {
      print('🎬 [YouTubePlayerService] Initializing...');
    }
    _isInitialized = true;
  }

  /// Clean and normalize search query for better foreign language support
  String _normalizeSearchQuery(String title, String artist) {
    // Remove common punctuation but preserve parentheses and important characters
    String cleanTitle = title
        .replaceAll(
            RegExp(
                r'[^\w\s\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af\(\)]'),
            ' ')
        .trim();
    String cleanArtist = artist
        .replaceAll(
            RegExp(
                r'[^\w\s\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af\(\)]'),
            ' ')
        .trim();

    // Normalize multiple spaces to single space
    cleanTitle = cleanTitle.replaceAll(RegExp(r'\s+'), ' ');
    cleanArtist = cleanArtist.replaceAll(RegExp(r'\s+'), ' ');

    // For foreign languages, try both original and romanized versions
    final query = '$cleanTitle $cleanArtist';

    if (kDebugMode) {
      print('🎬 [Search Query] Original: "$title $artist"');
      print('🎬 [Search Query] Normalized: "$query"');
    }

    return query;
  }

  /// Helper to strip (feat. ...) or (featuring ...) from titles
  String _stripFeat(String title) {
    return title
        .replaceAll(
            RegExp(r'\s*\(feat(uring)?\.[^)]*\)', caseSensitive: false), '')
        .trim();
  }

  /// Normalize artist name for comparison
  String _normalizeArtistName(String artist) {
    return artist
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'),
            '') // Remove punctuation but keep letters and spaces
        .trim()
        .split(' ')
        .where((word) => word.length > 1) // Ignore very short words
        .toSet()
        .join(' ');
  }

  /// Check if query artists match result artists
  bool _artistsMatch(
      String queryArtistString, List resultArtists, String title) {
    // Split query artists, handling various separators and formats
    final queryArtists = queryArtistString
        .split(RegExp(r'[,&]')) // Split by comma or ampersand
        .expand((artist) => artist.split(' feat. ')) // Handle featured artists
        .map((artist) => _normalizeArtistName(artist.trim()))
        .where((artist) => artist.isNotEmpty)
        .toSet();

    // Normalize result artists
    final normalizedResultArtists = resultArtists
        .map((a) => _normalizeArtistName((a['name'] ?? '').toString()))
        .toSet();

    // Extract featured artists from title
    final featuredArtists =
        _extractFeaturedArtists(title).map(_normalizeArtistName).toSet();

    // Combine result artists with featured artists
    final allResultArtists = {...normalizedResultArtists, ...featuredArtists};

    if (kDebugMode) {
      print('🎵 Query Artists: $queryArtists');
      print('🎵 Result Artists: $normalizedResultArtists');
      print('🎵 Featured Artists: $featuredArtists');
    }

    // Require at least one query artist to be present in result artists
    return queryArtists.any((queryArtist) => allResultArtists.any(
        (resultArtist) =>
            resultArtist.contains(queryArtist) ||
            queryArtist.contains(resultArtist)));
  }

  /// Extract featured artists from a title
  Set<String> _extractFeaturedArtists(String title) {
    final featMatch =
        RegExp(r'\(feat(?:uring)?\.?\s*([^)]+)\)', caseSensitive: false)
            .firstMatch(title);
    if (featMatch != null) {
      return featMatch
          .group(1)!
          .split(RegExp(r'[,&]'))
          .map((artist) => artist.trim())
          .toSet();
    }
    return {};
  }

  /// Normalize title for strict matching, handling remixes and featured artists
  String _normalizeTitleForStrictMatch(String title) {
    String t = title.toLowerCase();

    // Remove (feat. ...) or (featuring ...) or (with ...)
    t = t.replaceAll(
        RegExp(r'\s*\(feat(uring)?(\.| )[^)]*\)', caseSensitive: false), '');
    t = t.replaceAll(RegExp(r'\s*\(with\s+[^)]*\)', caseSensitive: false), '');

    // Remove remix and alternative version indicators
    const remixTerms = [
      'remix',
      'mix',
      'rework',
      'edit',
      'version',
      'radio edit',
      'extended',
      'club mix',
      'acoustic',
      'instrumental',
      'live',
      'cover',
      'alternative',
      'unplugged',
      'stripped',
      'demo'
    ];
    for (final term in remixTerms) {
      t = t.replaceAll('($term)', '').replaceAll(term, '');
    }

    // Remove punctuation but preserve spaces
    t = t.replaceAll(RegExp(r'[^\w\s]'), '');

    // Normalize whitespace
    t = t.replaceAll(RegExp(r'\s+'), ' ').trim();

    return t;
  }

  /// Strict validation for YouTube Music results
  bool _isStrictYouTubeMusicMatch(
      MusicTrack track, Map<String, dynamic> result) {
    final String resultTitle =
        _normalizeTitleForStrictMatch((result['title'] ?? '').toString());
    final String trackTitle = _normalizeTitleForStrictMatch(track.title);

    if (kDebugMode) {
      print('🎬 [Strict Match] Comparing titles:');
      print('   Track title: "${track.title}" -> "$trackTitle"');
      print('   Result title: "${result['title']}" -> "$resultTitle"');
    }

    // Exact title match is critical
    if (resultTitle != trackTitle) {
      if (kDebugMode) {
        print('❌ [Strict Match] Title mismatch');
      }
      return false;
    }

    // Artist matching with more strict logic
    final List artists = result['artists'] ?? [];
    if (!_artistsMatch(track.artist, artists, result['title'] ?? '')) {
      if (kDebugMode) {
        print('❌ [Strict Match] Artist mismatch');
      }
      return false;
    }

    // Strict duration matching
    try {
      // YouTube Music API returns duration_seconds as integer
      final durationSeconds = result['duration_seconds'] as int?;
      if (durationSeconds != null && track.durationMs > 1000) {
        final videoDuration = Duration(seconds: durationSeconds);
        final trackDuration = Duration(milliseconds: track.durationMs);

        final durationDiff =
            (videoDuration.inSeconds - trackDuration.inSeconds).abs();

        // Very strict duration matching
        if (durationDiff > 10) {
          if (kDebugMode) {
            print(
                '❌ [Duration Mismatch] Track duration: ${trackDuration.inSeconds}s, '
                'Video duration: ${videoDuration.inSeconds}s, '
                'Difference: $durationDiff');
          }
          return false;
        }
      }
    } catch (e) {
      // Ignore duration parsing errors
      if (kDebugMode) {
        print('⚠️ [Duration Parsing Error]: $e');
      }
    }

    if (kDebugMode) {
      print('✅ [Strict Match] All criteria passed');
    }

    return true;
  }

  /// Search YouTube Music for a track and return a list of strictly validated candidates.
  Future<List<_VideoCandidate>> _searchYouTubeMusic(MusicTrack track) async {
    try {
      // Prepare the request body for YouTube Music search
      final requestBody = {
        'query': _normalizeSearchQuery(track.title, track.artist),
        'filter': 'songs',
        'limit': 20,
        'ignore_spelling': false,
      };

      if (kDebugMode) {
        print('🎬 [YouTube Music Search] Query: "${requestBody['query']}"');
      }

      // Use the YouTube Music search endpoint
      final searchUrl = '$_apiBaseUrl/youtube_music/search/';

      final response = await http
          .post(
        Uri.parse(searchUrl),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(requestBody),
      )
          .timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('YouTube Music search request timed out');
        },
      );

      print(response.body);

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to search YouTube Music: ${response.statusCode}');
      }

      final data = json.decode(response.body);
      final results = data['results'] as List;

      if (kDebugMode) {
        print('🎬 [YouTube Music Search] Found ${results.length} items');
      }

      final List<_VideoCandidate> candidates = [];
      for (final result in results) {
        // Strict validation for YouTube Music
        if (!_isStrictYouTubeMusicMatch(track, result)) continue;

        // Create snippet and details objects for scoring
        final snippet = {
          'title': result['title'],
          'channelTitle': result['artists'][0]['name'],
          'description': result['album']?['name'] ?? '',
        };

        final details = {
          'contentDetails': {
            'duration':
                'PT${result['duration_seconds'] ~/ 60}M${result['duration_seconds'] % 60}S'
          },
          'statistics': {
            'viewCount': '0', // We don't have view count from this API
            'likeCount': '0',
          }
        };

        final score = _scoreVideo(
          track,
          snippet,
          details,
        );

        // Lower threshold for foreign language content
        final isForeignLanguage =
            _isForeignLanguage(track.title) || _isForeignLanguage(track.artist);
        final minScore = isForeignLanguage ? 5.0 : 10.0;

        if (score >= minScore) {
          candidates.add(_VideoCandidate(
            videoId: result['videoId'],
            score: score,
          ));
        }
      }

      // Sort candidates by score descending
      candidates.sort((a, b) => b.score.compareTo(a.score));

      if (kDebugMode) {
        print(
            '🎬 [YouTube Music Search] Found ${candidates.length} strict candidates.');
        for (final candidate in candidates.take(5)) {
          // Show top 5 for debugging
          print('  - ${candidate.toString()}');
        }
      }

      return candidates;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTube Music Search] Error: $e');
      }
      return [];
    }
  }

  /// Search YouTube for a track using default YouTube Data API and return a list of scored candidates.
  Future<List<_VideoCandidate>> _searchYouTubeDefault(MusicTrack track) async {
    try {
      final query = '${track.title} ${track.artist}';
      final encodedQuery = Uri.encodeComponent(query);

      final searchUrl = '$_apiBaseUrl/youtube/search'
          '?part=snippet'
          '&maxResults=15' // Increased from 10 to 15 for better fallback options
          '&q=$encodedQuery'
          '&regionCode=US'
          '&videoEmbeddable=true'
          '&safeSearch=none'
          '&videoSyndicated=true'
          '&type=video'
          '&videoCategoryId=10'
          '&key=$_apiKey';

      final response = await http.get(Uri.parse(searchUrl)).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('YouTube search request timed out');
        },
      );
      if (response.statusCode != 200) {
        throw Exception('Failed to search YouTube: ${response.statusCode}');
      }

      final data = json.decode(response.body);
      final items = data['items'] as List;
      if (items.isEmpty) return [];

      final videoIds = items
          .where((item) => item['id']?['videoId'] != null)
          .map((item) => item['id']['videoId'] as String)
          .join(',');

      if (videoIds.isEmpty) return [];

      final detailsUrl = '$_apiBaseUrl/youtube/videos'
          '?part=contentDetails,statistics'
          '&id=$videoIds'
          '&key=$_apiKey';

      final detailsResponse = await http.get(Uri.parse(detailsUrl)).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('YouTube video details request timed out');
        },
      );
      if (detailsResponse.statusCode != 200) {
        throw Exception(
            'Failed to get video details: ${detailsResponse.statusCode}');
      }

      final detailsData = json.decode(detailsResponse.body);
      final detailsItems = detailsData['items'] as List;

      final List<_VideoCandidate> candidates = [];
      for (var i = 0; i < items.length; i++) {
        final searchItem = items[i];
        final videoId = searchItem['id']?['videoId'] as String?;
        if (videoId == null) continue;

        final detailsItem = detailsItems.firstWhere(
          (d) => d['id'] == videoId,
          orElse: () => null,
        );
        if (detailsItem == null) continue;

        final snippet = searchItem['snippet'] as Map<String, dynamic>?;
        if (snippet == null) continue;

        final score = _scoreVideo(
          track,
          snippet,
          detailsItem,
        );

        // Lower threshold for foreign language content
        final isForeignLanguage =
            _isForeignLanguage(track.title) || _isForeignLanguage(track.artist);
        final minScore = isForeignLanguage ? 5.0 : 10.0;

        if (score >= minScore) {
          candidates.add(_VideoCandidate(
            videoId: videoId,
            score: score,
          ));
        }
      }

      // Sort candidates by score descending
      candidates.sort((a, b) => b.score.compareTo(a.score));

      if (kDebugMode) {
        print(
            '🎬 [YouTube Default Search] Found ${candidates.length} candidates.');
        for (final candidate in candidates.take(5)) {
          // Show top 5 for debugging
          print('  - ${candidate.toString()}');
        }
      }

      return candidates;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTube Default Search] Error: $e');
      }
      return [];
    }
  }

  /// Search for a track using multiple strategies
  Future<List<_VideoCandidate>> _searchForTrack(MusicTrack track) async {
    // First, try YouTube Music search
    List<_VideoCandidate> candidates = await _searchYouTubeMusic(track);

    // If no candidates found, fallback to default YouTube search
    if (candidates.isEmpty) {
      if (kDebugMode) {
        print(
            '🎬 [Search] No YouTube Music candidates found. Falling back to default YouTube search.');
      }
      candidates = await _searchYouTubeDefault(track);
    }

    return candidates;
  }

  /// Check if text contains foreign language characters
  bool _isForeignLanguage(String text) {
    // Check for Chinese, Japanese, Korean, and other non-Latin scripts
    return RegExp(
            r'[\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af\u0600-\u06ff\u0750-\u077f\u08a0-\u08ff\u1e00-\u1eff]')
        .hasMatch(text);
  }

  /// Score a video using advanced matching heuristics
  double _scoreVideo(
    MusicTrack track,
    Map<String, dynamic> snippet,
    Map<String, dynamic> details,
  ) {
    double score = 0.0;

    if (kDebugMode) {
      print(
          '🎬 [Scoring] "${snippet['title']}" by "${snippet['channelTitle']}"');
    }

    final snippetTitle = (snippet['title'] as String? ?? '').toLowerCase();
    final channelTitle =
        (snippet['channelTitle'] as String? ?? '').toLowerCase();
    final trackTitle = track.title.toLowerCase();
    final trackArtist = track.artist.toLowerCase();

    // 1. Exact title match is CRITICAL
    final normalizedSnippetTitle = _normalizeTitleForStrictMatch(snippetTitle);
    final normalizedTrackTitle = _normalizeTitleForStrictMatch(trackTitle);

    if (normalizedSnippetTitle == normalizedTrackTitle) {
      score += 500.0; // Massive bonus for exact title match
    } else {
      // Partial title match scoring
      final similarity = StringSimilarity.compareTwoStrings(
          normalizedSnippetTitle, normalizedTrackTitle);
      score += similarity * 100.0;
    }

    // 2. Artist matching
    final queryArtists =
        track.artist.split(RegExp(r'[,&]')).map(_normalizeArtistName).toSet();
    final resultArtists =
        _extractFeaturedArtists(snippetTitle).map(_normalizeArtistName).toSet();

    // Bonus for artist match
    final artistMatchScore = queryArtists.every((artist) => resultArtists.any(
            (resultArtist) =>
                resultArtist.contains(artist) || artist.contains(resultArtist)))
        ? 200.0
        : 0.0;

    score += artistMatchScore;

    // 3. Official version and channel bonus
    final isOfficialChannel = channelTitle.contains('topic') ||
        channelTitle.contains('vevo') ||
        channelTitle == trackArtist;

    final isOfficialVersion = snippetTitle.contains('official') ||
        snippetTitle.contains('music video') ||
        isOfficialChannel;

    if (isOfficialVersion) {
      score += 150.0; // Significant bonus for official versions
    }

    // 4. Duration matching (CRITICAL)
    try {
      final durationString = details['contentDetails']?['duration'] as String?;
      if (durationString != null && track.durationMs > 1000) {
        final videoDuration = _parseDuration(durationString);
        final trackDuration = Duration(milliseconds: track.durationMs);

        final durationDiff =
            (videoDuration.inSeconds - trackDuration.inSeconds).abs();

        // Extremely strict duration scoring
        if (durationDiff <= 5) {
          score += 200.0; // Perfect duration match
        } else if (durationDiff <= 10) {
          score += 100.0; // Close match
        } else {
          score -= 200.0; // Significant duration mismatch
        }
      }
    } catch (e) {
      // Ignore duration parsing errors
    }

    // 5. Penalize unwanted versions
    const unwantedTerms = [
      'remix',
      'cover',
      'live',
      'acoustic',
      'instrumental',
      'edit',
      'demo',
      'snippet'
    ];

    final isUnwantedVersion = unwantedTerms.any(snippetTitle.contains);
    if (isUnwantedVersion) {
      score -= 100.0; // Penalty for unwanted versions
    }

    if (kDebugMode) {
      print('  - Final score: $score');
    }

    return score;
  }

  /// Parse ISO 8601 duration to Duration
  Duration _parseDuration(String iso8601Duration) {
    final pattern = RegExp(r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?');
    final matches = pattern.firstMatch(iso8601Duration);
    if (matches == null) return Duration.zero;

    final hours = int.tryParse(matches[1] ?? '0') ?? 0;
    final minutes = int.tryParse(matches[2] ?? '0') ?? 0;
    final seconds = int.tryParse(matches[3] ?? '0') ?? 0;

    return Duration(
      hours: hours,
      minutes: minutes,
      seconds: seconds,
    );
  }

  /// Play a track via YouTube, with fallback logic and debouncing.
  Future<bool> playTrack(MusicTrack track) async {
    if (!_isInitialized) throw Exception('Service not initialized');

    // Debouncing mechanism to prevent rapid clicks from crashing the app
    if (isProcessingRequest) {
      if (kDebugMode) {
        print(
            '🎬 [YouTubePlayerService] Request already in progress, ignoring new request');
      }
      return false;
    }

    // Cancel any existing debounce timer
    _debounceTimer?.cancel();

    // Set processing flag and start debounce timer
    isProcessingRequest = true;
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      isProcessingRequest = false;
    });

    if (kDebugMode) {
      print(
          '🎬 [YouTubePlayerService] Searching for: ${track.title} by ${track.artist}');
    }

    try {
      await stop(); // Stop current playback

      // Reset the tried video IDs for the new track
      _triedVideoIds.clear();

      _videoCandidates = await _searchForTrack(track);
      if (_videoCandidates.isEmpty) {
        if (kDebugMode) {
          print(
              '❌ [YouTubePlayerService] No suitable videos found for fallback.');
        }
        return false;
      }

      _currentCandidateIndex = -1;
      _currentTrack = track; // Store the original track
      return _tryNextCandidate();
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubePlayerService] Error in playTrack: $e');
      }
      return false;
    } finally {
      // Reset processing flag after a delay to allow for UI updates
      Future.delayed(const Duration(milliseconds: 100), () {
        isProcessingRequest = false;
      });
    }
  }

  Future<bool> _tryNextCandidate() async {
    _fallbackTimer?.cancel();
    _playerStateSubscription?.cancel();
    _currentCandidateIndex++;

    if (_currentCandidateIndex >= _videoCandidates.length) {
      // If we've exhausted all YouTube Music candidates, try YouTube default search
      if (_currentTrack != null) {
        if (kDebugMode) {
          print(
              '🎬 [YouTubePlayerService] All YouTube Music candidates failed. Trying YouTube default search...');
          print(
              '🎬 [YouTubePlayerService] Already tried ${_triedVideoIds.length} video IDs');
        }

        // Get candidates from default YouTube search
        final defaultCandidates = await _searchYouTubeDefault(_currentTrack!);

        // Filter out already tried video IDs
        final newCandidates = defaultCandidates
            .where((candidate) => !_triedVideoIds.contains(candidate.videoId))
            .toList();

        if (kDebugMode) {
          print(
              '🎬 [YouTubePlayerService] Found ${newCandidates.length} new candidates from YouTube default search');
        }

        if (newCandidates.isNotEmpty) {
          // Add new candidates to the list
          _videoCandidates = newCandidates;
          _currentCandidateIndex =
              -1; // Reset index to start from the beginning
          return _tryNextCandidate(); // Try the first new candidate
        }
      }

      if (kDebugMode) {
        print('❌ [YouTubePlayerService] All candidates failed to play.');
      }
      _isPlaying = false;
      _isPlayingController.add(false);
      _onAllCandidatesFailedController.add(null);
      return false;
    }

    // Announce that we are about to try a new candidate
    _onWillTryNextCandidateController.add(null);

    // Reset playing state before trying the next candidate
    _isPlaying = false;
    _isPlayingController.add(false);

    final candidate = _videoCandidates[_currentCandidateIndex];
    final videoId = candidate.videoId;

    // Add this video ID to the set of tried IDs
    _triedVideoIds.add(videoId);

    if (kDebugMode) {
      print(
          '▶️ [YouTubePlayerService] Attempting to play candidate #${_currentCandidateIndex + 1}: $videoId');
    }

    // Stop previous controller if it exists. Don't await, as it can hang on error.
    if (_controller != null) {
      _controller!.close(); // Fire-and-forget
      _controller = null; // Ensure we create a new one
      // Add a small delay to allow components to settle before loading the next video
      await Future.delayed(const Duration(milliseconds: 500));
    }

    _controller = YoutubePlayerController.fromVideoId(
      videoId: videoId,
      autoPlay: true,
      params: const YoutubePlayerParams(
        showControls: true,
        showFullscreenButton: true,
        playsInline: true, // Better mobile experience
      ),
    );

    _currentVideoId = videoId;

    // Create a new track with YouTube-specific URL and ID
    final youtubeTrack = _currentTrack!.copyWith(
      id: videoId,
      uri: 'https://www.youtube.com/watch?v=$videoId',
      url: 'https://www.youtube.com/watch?v=$videoId',
      service: 'youtube',
      serviceType: 'youtube',
      // Preserve YouTube Music metadata if available
      durationMs: _currentTrack!.durationMs ?? 0,
      explicit: _currentTrack!.explicit ?? false,
      album: _currentTrack!.album ?? '',
    );

    _currentTrack = youtubeTrack;
    _currentTrackController.add(_currentTrack);
    _startPiPCallback?.call();

    _startPositionTracking();

    // Track buffering state for the first 2 seconds
    bool hasBuffered = false;
    Timer? bufferingTimer;

    // Start a fallback timer
    _fallbackTimer = Timer(const Duration(seconds: 8), () {
      if (kDebugMode) {
        print(
            '⌛ [YouTubePlayerService] Fallback timer triggered for $videoId. Checking status...');
      }
      final currentState = _controller?.value.playerState;
      if (currentState != PlayerState.playing &&
          currentState != PlayerState.paused) {
        if (kDebugMode) {
          print(
              '⚠️ [YouTubePlayerService] Video $videoId failed to start, trying next...');
        }
        // Schedule the next attempt in a microtask to avoid race conditions
        Future.microtask(() => _tryNextCandidate());
      }
    });

    // Buffering check timer
    bufferingTimer = Timer(const Duration(seconds: 5), () {
      if (!hasBuffered) {
        if (kDebugMode) {
          print(
              '⚠️ [YouTubePlayerService] Video $videoId did not buffer in 5 seconds, trying next...');
        }
        // Schedule the next attempt in a microtask to avoid race conditions
        Future.microtask(() => _tryNextCandidate());
      }
    });

    // Listen for state changes to handle success or failure.
    _playerStateSubscription = _controller!.stream.listen(
      (state) {
        if (kDebugMode) {
          print(
              '🔍 [YouTubePlayerService] Player state changed: ${state.playerState}');
        }

        // Check for buffering state
        if (state.playerState == PlayerState.buffering) {
          hasBuffered = true;
          if (kDebugMode) {
            print('🔄 [YouTubePlayerService] Video $videoId is buffering');
          }
        }

        // Success case: Video is playing
        if (state.playerState == PlayerState.playing) {
          if (kDebugMode) {
            print(
                '✅ [YouTubePlayerService] Playback confirmed for $videoId. Fallback cancelled.');
          }
          _isPlaying = true;
          _isPlayingController.add(true);
          _fallbackTimer?.cancel();
          bufferingTimer?.cancel();
          _playerStateSubscription?.cancel(); // Clean up the listener
          _onCandidatePlaybackConfirmedController.add(null);
          return;
        }

        // Error case: Player threw an error
        if (state.hasError) {
          if (kDebugMode) {
            print(
                '🔥 [YouTubePlayerService] Player error for $videoId: ${state.error}');
          }
          _fallbackTimer?.cancel();
          bufferingTimer?.cancel();
          _playerStateSubscription?.cancel();
          // Schedule the next attempt in a microtask to avoid race conditions
          Future.microtask(() => _tryNextCandidate());
          return;
        }
      },
      onError: (error, stackTrace) {
        if (kDebugMode) {
          print(
              '🚨 [YouTubePlayerService] Error in player stream: $error\n$stackTrace');
        }
        _fallbackTimer?.cancel();
        bufferingTimer?.cancel();
        _playerStateSubscription?.cancel();
        // Schedule the next attempt in a microtask to avoid race conditions
        Future.microtask(() => _tryNextCandidate());
      },
      cancelOnError: false,
    );

    return true;
  }

  /// Start tracking playback position
  void _startPositionTracking() {
    _positionTimer?.cancel();
    _positionTimer = Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (_isPlaying && _controller != null) {
        try {
          // Use millisecond precision for position and duration
          _controller!.currentTime.then((position) {
            if (_controller != null) {
              // Check again in case controller was set to null
              _controller!.duration.then((duration) {
                if (_controller != null) {
                  // Check again before adding to controllers
                  _positionController
                      .add(Duration(milliseconds: position.toInt()));
                  _durationController
                      .add(Duration(milliseconds: duration.toInt()));
                }
              });
            }
          });
        } catch (e) {
          // More robust error handling
          if (kDebugMode) {
            print('⚠️ [YouTubePlayerService] Position tracking error: $e');
            print('⚠️ Controller state: $_controller');
          }

          // If tracking fails multiple times, reset the timer
          if (timer.tick % 10 == 0) {
            _positionTimer?.cancel();
            _startPositionTracking();
          }
        }
      }
    });
  }

  /// Play/resume playback
  Future<bool> play() async {
    try {
      if (_controller == null) return false;
      _controller!.playVideo();
      _isPlaying = true;
      _isPlayingController.add(_isPlaying);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubePlayerService] Error playing: $e');
      }
      return false;
    }
  }

  /// Pause playback
  Future<bool> pause() async {
    try {
      if (_controller == null) return false;
      _controller!.pauseVideo();
      _isPlaying = false;
      _isPlayingController.add(_isPlaying);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubePlayerService] Error pausing: $e');
      }
      return false;
    }
  }

  /// Stop playback
  Future<bool> stop() async {
    try {
      // Cancel timers first to prevent race conditions
      _positionTimer?.cancel();
      _fallbackTimer?.cancel();
      _playerStateSubscription?.cancel();

      if (_controller != null) {
        _controller!.pauseVideo();
        _controller!.close();
        _controller = null;
      }

      _isPlaying = false;
      _currentTrack = null;
      _currentVideoId = null;

      // Notify listeners
      _isPlayingController.add(false);
      _currentTrackController.add(null);
      _positionController.add(Duration.zero);
      _durationController.add(Duration.zero);

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubePlayerService] Error stopping: $e');
      }
      return false;
    }
  }

  /// Seek to position
  Future<bool> seek(Duration position) async {
    try {
      if (_controller == null) return false;
      await _controller!.seekTo(seconds: position.inSeconds.toDouble());
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubePlayerService] Error seeking: $e');
      }
      return false;
    }
  }

  /// Add a track to the queue
  void addToQueue(MusicTrack track) {
    _queue.add(track);
    _queueController.add(_queue);

    // If this is the first track and nothing is playing, play it
    if (_queue.length == 1 && !_isPlaying) {
      playTrack(track);
      _currentQueueIndex = 0;
    }
  }

  /// Add multiple tracks to the queue
  void addTracksToQueue(List<MusicTrack> tracks) {
    _queue.addAll(tracks);
    _queueController.add(_queue);

    // If queue was empty and nothing is playing, start playing the first track
    if (_queue.length == tracks.length && !_isPlaying) {
      playTrack(_queue.first);
      _currentQueueIndex = 0;
    }
  }

  /// Remove a track from the queue
  void removeFromQueue(int index) {
    if (index < 0 || index >= _queue.length) return;

    // If removing current track, stop playback
    if (index == _currentQueueIndex) {
      stop();
      _currentQueueIndex = -1;
    }
    // If removing track before current, adjust index
    else if (index < _currentQueueIndex) {
      _currentQueueIndex--;
    }

    _queue.removeAt(index);
    _queueController.add(_queue);
  }

  /// Clear the queue
  void clearQueue() {
    stop();
    _queue.clear();
    _currentQueueIndex = -1;
    _queueController.add(_queue);
  }

  /// Skip to next track in queue
  Future<bool> playNext() async {
    if (!hasNext) return false;

    _currentQueueIndex++;
    return await playTrack(_queue[_currentQueueIndex]);
  }

  /// Skip to previous track in queue
  Future<bool> playPrevious() async {
    if (!hasPrevious) return false;

    _currentQueueIndex--;
    return await playTrack(_queue[_currentQueueIndex]);
  }

  /// Skip to specific track in queue
  Future<bool> skipToTrack(int index) async {
    if (index < 0 || index >= _queue.length) return false;

    _currentQueueIndex = index;
    return await playTrack(_queue[_currentQueueIndex]);
  }

  /// Shuffle the remaining tracks in the queue
  void shuffleQueue() {
    if (_queue.isEmpty || _currentQueueIndex >= _queue.length - 1) return;

    // Keep current track in place, shuffle only remaining tracks
    final currentTracks = _queue.sublist(0, _currentQueueIndex + 1);
    final remainingTracks = _queue.sublist(_currentQueueIndex + 1);
    remainingTracks.shuffle();

    _queue = [...currentTracks, ...remainingTracks];
    _queueController.add(_queue);
  }

  /// Dispose the service
  void dispose() {
    _isPlayingController.close();
    _currentTrackController.close();
    _positionController.close();
    _durationController.close();
    _queueController.close();
    _onWillTryNextCandidateController.close();
    _onCandidatePlaybackConfirmedController.close();
    _onAllCandidatesFailedController.close();
    _positionTimer?.cancel();
    _fallbackTimer?.cancel();
    _debounceTimer?.cancel();
    _playerStateSubscription?.cancel();
    _controller?.close();
  }
}
