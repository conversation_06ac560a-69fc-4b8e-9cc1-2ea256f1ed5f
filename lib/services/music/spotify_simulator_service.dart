import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../models/music_track.dart';
import '../analytics/analytics_service.dart';

/// Provides simulated Spotify functionality for testing and development
class SpotifySimulatorService {
  static final SpotifySimulatorService _instance = SpotifySimulatorService._internal();
  factory SpotifySimulatorService() => _instance;
  SpotifySimulatorService._internal();

  final AnalyticsService _analytics = AnalyticsService();
  
  // Simulated state
  bool _isPlaying = false;
  bool _isConnected = false;
  bool _isPremium = true;
  MusicTrack? _currentTrack;
  int _currentPosition = 0;
  Timer? _positionTimer;
  
  // Stream controllers
  final _playbackStateController = StreamController<Map<String, dynamic>>.broadcast();
  final _connectionStateController = StreamController<bool>.broadcast();
  
  // Getters
  Stream<Map<String, dynamic>> get playbackStateStream => _playbackStateController.stream;
  Stream<bool> get connectionStateStream => _connectionStateController.stream;
  bool get isPlaying => _isPlaying;
  bool get isConnected => _isConnected;
  bool get isPremium => _isPremium;
  MusicTrack? get currentTrack => _currentTrack;
  
  // Mock track library
  final List<MusicTrack> _mockLibrary = [
    MusicTrack(
      id: 'mock_1',
      title: 'Simulated Track 1',
      artist: 'Test Artist',
      album: 'Test Album',
      albumArt: 'https://via.placeholder.com/300',
      uri: 'spotify:track:mock1',
      durationMs: 180000,
      url: 'https://open.spotify.com/track/mock1',
      service: 'spotify',
      serviceType: 'spotify',
      genres: ['pop', 'test'],
      explicit: false,
      popularity: 80,
    ),
    MusicTrack(
      id: 'mock_2',
      title: 'Simulated Track 2',
      artist: 'Another Artist',
      album: 'Another Album',
      albumArt: 'https://via.placeholder.com/300',
      uri: 'spotify:track:mock2',
      durationMs: 240000,
      url: 'https://open.spotify.com/track/mock2',
      service: 'spotify',
      serviceType: 'spotify',
      genres: ['rock', 'test'],
      explicit: false,
      popularity: 70,
    ),
  ];
  
  /// Initialize simulator
  Future<bool> initialize() async {
    try {
      _isConnected = true;
      _connectionStateController.add(true);
      
      _analytics.trackEvent('simulator_initialized');
      return true;
    } catch (e, stackTrace) {
      _analytics.trackError('simulator_init_error', e, stackTrace);
      return false;
    }
  }
  
  /// Simulate playing a track
  Future<bool> playTrack(String uri) async {
    try {
      // Find track in mock library
      final track = _mockLibrary.firstWhere(
        (t) => t.uri == uri,
        orElse: () => MusicTrack(
          id: uri,
          title: 'Unknown Track',
          artist: 'Unknown Artist',
          album: 'Unknown Album',
          albumArt: 'https://via.placeholder.com/300',
          uri: uri,
          durationMs: 180000,
          url: 'https://open.spotify.com/track/${uri.split(":").last}',
          service: 'spotify',
          serviceType: 'spotify',
          genres: ['unknown'],
          explicit: false,
          popularity: 50,
        ),
      );
      
      _currentTrack = track;
      _isPlaying = true;
      _currentPosition = 0;
      
      // Start position timer
      _startPositionTimer();
      
      // Emit state
      _emitPlaybackState();
      
      _analytics.trackEvent('simulator_play_track', {
        'track_uri': uri,
      });
      
      return true;
    } catch (e, stackTrace) {
      _analytics.trackError('simulator_play_error', e, stackTrace);
      return false;
    }
  }
  
  /// Simulate pausing playback
  Future<bool> pause() async {
    try {
      _isPlaying = false;
      _positionTimer?.cancel();
      _emitPlaybackState();
      
      _analytics.trackEvent('simulator_pause');
      return true;
    } catch (e, stackTrace) {
      _analytics.trackError('simulator_pause_error', e, stackTrace);
      return false;
    }
  }
  
  /// Simulate resuming playback
  Future<bool> resume() async {
    try {
      _isPlaying = true;
      _startPositionTimer();
      _emitPlaybackState();
      
      _analytics.trackEvent('simulator_resume');
      return true;
    } catch (e, stackTrace) {
      _analytics.trackError('simulator_resume_error', e, stackTrace);
      return false;
    }
  }
  
  /// Simulate seeking to position
  Future<bool> seekTo(int positionMs) async {
    try {
      _currentPosition = positionMs;
      _emitPlaybackState();
      
      _analytics.trackEvent('simulator_seek', {
        'position': positionMs,
      });
      return true;
    } catch (e, stackTrace) {
      _analytics.trackError('simulator_seek_error', e, stackTrace);
      return false;
    }
  }
  
  /// Get mock library tracks
  Future<List<MusicTrack>> getMockLibrary() async {
    return _mockLibrary;
  }
  
  /// Start position timer
  void _startPositionTimer() {
    _positionTimer?.cancel();
    _positionTimer = Timer.periodic(const Duration(milliseconds: 1000), (timer) {
      if (_isPlaying && _currentTrack != null) {
        _currentPosition += 1000;
        if (_currentPosition >= _currentTrack!.durationMs) {
          _currentPosition = 0;
          _isPlaying = false;
          timer.cancel();
        }
        _emitPlaybackState();
      }
    });
  }
  
  /// Emit current playback state
  void _emitPlaybackState() {
    _playbackStateController.add({
      'is_playing': _isPlaying,
      'track': _currentTrack?.toJson(),
      'position': _currentPosition,
      'is_premium': _isPremium,
    });
  }
  
  /// Simulate connection state change
  void setConnectionState(bool connected) {
    _isConnected = connected;
    _connectionStateController.add(connected);
    
    _analytics.trackEvent('simulator_connection_changed', {
      'connected': connected,
    });
  }
  
  /// Simulate premium state change
  void setPremiumState(bool premium) {
    _isPremium = premium;
    _emitPlaybackState();
    
    _analytics.trackEvent('simulator_premium_changed', {
      'premium': premium,
    });
  }
  
  /// Clean up resources
  void dispose() {
    _positionTimer?.cancel();
    _playbackStateController.close();
    _connectionStateController.close();
  }
} 