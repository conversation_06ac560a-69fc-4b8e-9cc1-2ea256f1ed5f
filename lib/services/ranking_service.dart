import 'package:flutter/foundation.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'auth_service.dart';

class RankingService {
  final ApiService _apiService;
  final AuthService _authService;

  RankingService(this._apiService, this._authService);

  // Public getters for accessing services
  ApiService get apiService => _apiService;
  AuthService get authService => _authService;

  /// Get leaderboard data based on scope and location
  Future<Map<String, dynamic>> getLeaderboard({
    required String scope, // 'local', 'friends', 'all', 'school'
    double? latitude,
    double? longitude,
    double radiusKm = 5.0,
    int limit = 20,
    int page = 1, // Add pagination support
  }) async {
    try {
      final token = await _authService.getToken();
      
      // Build query parameters
      final queryParams = <String, dynamic>{
        'scope': scope,
        'limit': limit.toString(),
        'page': page.toString(), // Add page parameter
      };
      
      // Add location parameters for local scope
      if (scope == 'local' && latitude != null && longitude != null) {
        queryParams['lat'] = latitude.toString();
        queryParams['lng'] = longitude.toString();
        queryParams['radius_km'] = radiusKm.toString();
      }
      
      final response = await _apiService.get(
        '/rankings/leaderboard/',
        queryParams: queryParams,
        token: token,
      );

      if (!response.success) {
        if (kDebugMode) {
          print('❌ [RankingService] Failed to get leaderboard: ${response.message}');
        }
        return {
          'leaderboard': <Map<String, dynamic>>[],
          'location': null,
          'scope': scope,
          'school_info': null,
          'has_more': false,
          'current_page': page,
          'total_pages': 0,
        };
      }

      // Process the response data to match UI expectations
      final responseData = response.data as Map<String, dynamic>;
      final leaderboardList = responseData['leaderboard'] as List<dynamic>;
      
      // Transform backend data to match UI structure
      final processedLeaderboard = leaderboardList.map((item) {
        final userData = item as Map<String, dynamic>;
        
        return {
          'id': userData['id']?.toString() ?? '0',
          'name': userData['name'] ?? userData['username'] ?? 'Unknown User',
          'username': userData['username'] ?? 'unknown',
          'avatar': userData['avatar'] ?? 'https://i.pravatar.cc/150?img=1',
          'score': userData['score']?.toString() ?? '0',
          'change': _mapChangeDirection(userData['change']),
          'changeAmount': userData['changeAmount']?.toString() ?? '0',
          'isCurrentUser': userData['isCurrentUser'] ?? false,
          'rank': userData['rank'] ?? 0,
          // Include school info for each user if available
          'school': userData['school'],
        };
      }).toList();

      return {
        'leaderboard': processedLeaderboard,
        'location': responseData['location'],
        'scope': responseData['scope'] ?? scope,
        // Include school-specific metadata
        'school_info': responseData['school_info'],
        'total_students': responseData['school_info']?['total_students'],
        // Add pagination metadata
        'has_more': responseData['has_more'] ?? false,
        'current_page': responseData['current_page'] ?? page,
        'total_pages': responseData['total_pages'] ?? 1,
        'total_count': responseData['total_count'] ?? processedLeaderboard.length,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [RankingService] Error getting leaderboard: $e');
      }
      return {
        'leaderboard': <Map<String, dynamic>>[],
        'location': null,
        'scope': scope,
        'school_info': null,
        'has_more': false,
        'current_page': page,
        'total_pages': 0,
      };
    }
  }

  /// Get user's ranking statistics
  Future<Map<String, dynamic>?> getUserStats() async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.get(
        '/rankings/stats/',
        token: token,
      );

      if (!response.success) {
        if (kDebugMode) {
          print('❌ [RankingService] Failed to get user stats: ${response.message}');
        }
        return null;
      }

      return response.data as Map<String, dynamic>;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [RankingService] Error getting user stats: $e');
      }
      return null;
    }
  }

  /// Update user location for local rankings
  Future<bool> updateUserLocation(double latitude, double longitude) async {
    try {
      final token = await _authService.getToken();
      
      final response = await _apiService.post(
        '/rankings/update-location/',
        data: {
          'latitude': latitude,
          'longitude': longitude,
        },
        token: token,
      );

      if (!response.success) {
        if (kDebugMode) {
          print('❌ [RankingService] Failed to update location: ${response.message}');
        }
      }

      return response.success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [RankingService] Error updating location: $e');
      }
      return false;
    }
  }

  /// Map backend change values to UI expected values
  String _mapChangeDirection(dynamic change) {
    if (change == null) return 'same';
    
    final changeStr = change.toString().toLowerCase();
    switch (changeStr) {
      case 'up':
      case 'increased':
      case '1':
        return 'up';
      case 'down':
      case 'decreased':
      case '-1':
        return 'down';
      case 'same':
      case 'unchanged':
      case '0':
      default:
        return 'same';
    }
  }
} 