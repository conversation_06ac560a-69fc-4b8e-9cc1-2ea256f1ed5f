import 'dart:async';
import 'package:flutter/foundation.dart';

/// Global event bus for pin-related events
/// This allows communication between screens without depending on widget tree
class PinEventBus {
  static final PinEventBus _instance = PinEventBus._internal();
  factory PinEventBus() => _instance;
  PinEventBus._internal();

  // Stream controllers for different pin events
  final StreamController<Map<String, dynamic>> _optimisticPinController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<void> _forceRefreshController = 
      StreamController<void>.broadcast();

  /// Stream for optimistic pin display events
  Stream<Map<String, dynamic>> get optimisticPinStream => _optimisticPinController.stream;

  /// Stream for force refresh events
  Stream<void> get forceRefreshStream => _forceRefreshController.stream;

  /// Emit an optimistic pin display event
  void emitOptimisticPin(Map<String, dynamic> pinData) {
    debugPrint('🌐 [PinEventBus] Emitting optimistic pin event');
    debugPrint('🌐 [PinEventBus] Pin data: ${pinData['title']} at ${pinData['location']}');
    _optimisticPinController.add(pinData);
  }

  /// Emit a force refresh event
  void emitForceRefresh() {
    debugPrint('🌐 [PinEventBus] Emitting force refresh event');
    _forceRefreshController.add(null);
  }

  /// Dispose resources (call this when app is closing)
  void dispose() {
    _optimisticPinController.close();
    _forceRefreshController.close();
  }
}
