import 'package:flutter/foundation.dart';
import '../services/user_service.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';

class UserStatsProvider extends ChangeNotifier {
  late final UserService _userService;
  
  // State
  Map<String, String> _stats = {
    'pins': '0',
    'streak': '0',
    'following': '0',
    'followers': '0',
  };
  bool _isLoading = false;
  String? _error;
  DateTime? _lastFetch;
  
  // Cache timeout - refresh stats every 5 minutes
  static const Duration _cacheTimeout = Duration(minutes: 5);
  
  // Getters
  Map<String, String> get stats => _stats;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  UserStatsProvider() {
    _initializeService();
  }
  
  void _initializeService() {
    final apiService = ApiService();
    final authService = AuthService(apiService);
    _userService = UserService(apiService, authService);
    
    // Auto-load stats when provider is initialized
    loadStats();
  }
  
  // Load user stats from API
  Future<void> loadStats({bool forceRefresh = false}) async {
    // Check if we need to refresh based on cache timeout
    if (!forceRefresh && 
        _lastFetch != null && 
        DateTime.now().difference(_lastFetch!) < _cacheTimeout &&
        _stats['pins'] != '0') {
      print('📊 UserStatsProvider: Using cached stats');
      return;
    }
    
    print('📊 UserStatsProvider: Loading user stats...');
    _setLoading(true);
    _clearError();
    
    try {
      final newStats = await _userService.getUserStats();
      _stats = newStats;
      _lastFetch = DateTime.now();
      
      print('📊 UserStatsProvider: Loaded stats - Pins: ${_stats['pins']}, Friends: ${_stats['following']}, Streak: ${_stats['streak']}, Followers: ${_stats['followers']}');
      
      // Check if stats are all zeros and warn
      if (_stats['pins'] == '0' && _stats['following'] == '0' && _stats['streak'] == '0' && _stats['followers'] == '0') {
        print('⚠️ UserStatsProvider: All stats are zero - check if API endpoint exists and returns correct data');
      }
      
      notifyListeners();
    } catch (e) {
      print('📊 UserStatsProvider: Error loading stats: $e');
      _setError('Failed to load user stats: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }
  
  // Refresh stats (force reload)
  Future<void> refresh() async {
    await loadStats(forceRefresh: true);
  }
  
  // Update a specific stat (for optimistic updates)
  void updateStat(String key, String value) {
    _stats[key] = value;
    notifyListeners();
  }
  
  // Increment a stat by 1 (useful for when user creates a pin, etc.)
  void incrementStat(String key) {
    final currentValue = int.tryParse(_stats[key] ?? '0') ?? 0;
    _stats[key] = (currentValue + 1).toString();
    notifyListeners();
  }
  
  // Decrement a stat by 1
  void decrementStat(String key) {
    final currentValue = int.tryParse(_stats[key] ?? '0') ?? 0;
    if (currentValue > 0) {
      _stats[key] = (currentValue - 1).toString();
      notifyListeners();
    }
  }
  
  // Private helper methods
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }
  
  void _setError(String message) {
    _error = message;
    notifyListeners();
  }
  
  void _clearError() {
    _error = null;
  }
} 