import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'dart:async';
import 'map_provider.dart';
import 'pin_provider.dart';

class SearchProvider extends ChangeNotifier {
  final MapProvider _mapProvider;
  final PinProvider _pinProvider;

  SearchProvider({
    required MapProvider mapProvider,
    required PinProvider pinProvider,
  })  : _mapProvider = mapProvider,
        _pinProvider = pinProvider;

  // Search state
  String _query = '';
  bool _isLoading = false;
  List<dynamic> _searchResults = [];
  bool _hasSearched = false;
  String? _errorMessage;

  // Debouncing search
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  // Getters
  String get query => _query;
  bool get isLoading => _isLoading;
  List<dynamic> get searchResults => _searchResults;
  bool get hasSearched => _hasSearched;
  bool get hasResults => _searchResults.isNotEmpty;
  String? get errorMessage => _errorMessage;

  // Handle search query with debouncing
  Future<void> search(String query) async {
    // Cancel any existing debounce timer
    _debounceTimer?.cancel();
    
    _query = query;
    
    // Don't search if query is too short
    if (query.length < 2) {
      if (query.isEmpty) {
        _searchResults = [];
        _hasSearched = false;
        _errorMessage = null;
        notifyListeners();
      }
      return;
    }
    
    // Set loading state immediately
    _isLoading = true;
    _hasSearched = true;
    _errorMessage = null;
    notifyListeners();
    
    // Debounce the actual search
    _debounceTimer = Timer(_debounceDuration, () async {
      try {
        // Simulating search latency
        await Future.delayed(const Duration(milliseconds: 300));
        
        // TODO: Replace with actual search implementation
        // For now, just returning mock results
        final results = _mockSearch(query);
        
        // Calculate and add distance to each result
        _searchResults = _addDistanceToResults(results);
        
        // Sort by distance
        _searchResults.sort((a, b) => 
          (a['distance'] as double).compareTo(b['distance'] as double));
        
      } catch (e) {
        debugPrint('Search error: $e');
        _searchResults = [];
        _errorMessage = 'Error searching: ${e.toString()}';
      } finally {
        _isLoading = false;
        notifyListeners();
      }
    });
  }

  // Add distance information to each result
  List<dynamic> _addDistanceToResults(List<dynamic> results) {
    final currentLocation = _mapProvider.currentCenter;
    
    return results.map((pin) {
      if (pin['latitude'] != null && pin['longitude'] != null) {
        // Calculate distance in kilometers
        final double distance = _calculateDistance(
          currentLocation.latitude, 
          currentLocation.longitude,
          pin['latitude'] as double, 
          pin['longitude'] as double
        );
        
        // Create a new map with all existing properties plus distance
        return {
          ...pin,
          'distance': distance,
        };
      }
      return pin;
    }).toList();
  }
  
  // Calculate distance between two points using Haversine formula
  double _calculateDistance(
    double lat1, double lon1, 
    double lat2, double lon2
  ) {
    const double earthRadius = 6371; // Radius of the Earth in kilometers
    
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
        math.sin(dLon / 2) * math.sin(dLon / 2);
        
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }
  
  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  // Clear search
  void clearSearch() {
    _debounceTimer?.cancel();
    _query = '';
    _searchResults = [];
    _hasSearched = false;
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }

  // Mock search for development
  List<dynamic> _mockSearch(String query) {
    final lowercaseQuery = query.toLowerCase();
    
    // This would normally come from your backend or local database
    final mockPins = [
      {
        'id': '1',
        'title': 'Starboy',
        'artist': 'The Weeknd',
        'latitude': 37.7749,
        'longitude': -122.4194,
        'albumArtUrl': 'https://example.com/starboy.jpg',
      },
      {
        'id': '2',
        'title': 'Blinding Lights',
        'artist': 'The Weeknd',
        'latitude': 37.7739,
        'longitude': -122.4312,
        'albumArtUrl': 'https://example.com/blindinglights.jpg',
      },
      {
        'id': '3',
        'title': 'Uptown Funk',
        'artist': 'Mark Ronson ft. Bruno Mars',
        'latitude': 37.7831,
        'longitude': -122.4039,
        'albumArtUrl': 'https://example.com/uptownfunk.jpg',
      },
      {
        'id': '4',
        'title': 'Shape of You',
        'artist': 'Ed Sheeran',
        'latitude': 37.7914,
        'longitude': -122.4089,
        'albumArtUrl': 'https://example.com/shapeofyou.jpg',
      },
      {
        'id': '5',
        'title': 'God\'s Plan',
        'artist': 'Drake',
        'latitude': 37.7866,
        'longitude': -122.4235,
        'albumArtUrl': 'https://example.com/godsplan.jpg',
      },
    ];
    
    // Filter mock pins based on query
    return mockPins.where((pin) {
      final title = pin['title'].toString().toLowerCase();
      final artist = pin['artist'].toString().toLowerCase();
      return title.contains(lowercaseQuery) || artist.contains(lowercaseQuery);
    }).toList();
  }

  // Navigate to pin on map
  void navigateToPin(dynamic pin) {
    if (pin != null && pin['latitude'] != null && pin['longitude'] != null) {
      _mapProvider.animateToLocation(
        latitude: pin['latitude'],
        longitude: pin['longitude'],
        zoom: 16,
      );
    }
  }
  
  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
} 