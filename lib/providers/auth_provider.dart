import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/constants.dart';
import '../models/user.dart';
import '../services/api/auth_service.dart';
import '../services/notifications/onesignal_service.dart';
import '../services/ai/global_ai_provider_service.dart';
import 'gamification_provider.dart';
import 'onesignal_provider.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  // Remove direct OneSignalService usage - we'll use OneSignalProvider instead
  // final OneSignalService _oneSignalService = OneSignalService();

  User? _currentUser;
  String? _token;
  String? _refreshToken;
  bool _isLoading = false;
  String? _error;

  // Getters
  User? get currentUser => _currentUser;
  bool get isAuthenticated {
    final hasToken = _token != null;
    final hasUser = _currentUser != null;
    final isAuth = hasToken && hasUser;

    // Add debug logging to help diagnose issues
    if (kDebugMode) {
      print(
          '🔐 Auth state: token=${hasToken}, user=${hasUser}, isAuthenticated=${isAuth}');
      if (hasUser) {
        print(
            '👤 Current user: ${_currentUser?.username} (${_currentUser?.email})');
        print('🎯 Display name computed: ${_currentUser?.displayNameComputed}');
      }
      if (hasToken) {
        print('🔑 Token starts with: $token');
      }
    }

    return isAuth;
  }

  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get token => _token;

  // Constructor
  AuthProvider() {
    _loadStoredCredentials();
  }

  Future<void> loadStoredCredentials() async {
    await _loadStoredCredentials();
  }

  // Load stored token and user data on app start
  Future<void> _loadStoredCredentials() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Load auth token
      _token = await _secureStorage.read(key: AppConstants.tokenKey);
      _refreshToken =
          await _secureStorage.read(key: AppConstants.refreshTokenKey);

      // If token exists, try to load user data
      if (_token != null) {
        final prefs = await SharedPreferences.getInstance();
        final userData = prefs.getString('user_data');

        if (userData != null) {
          _currentUser = User.fromJson(json.decode(userData));
          // Set OneSignal external user ID on app startup if user is already logged in
          await _setOneSignalUserId();
        } else {
          // If we have a token but no stored user data, fetch it from API
          await _fetchUserProfile();
        }
      }
    } catch (e) {
      _error = 'Error loading credentials: $e';
      print(_error);
      await logout(); // Clear invalid credentials
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Register a new user
  Future<bool> register({
    required String username,
    required String email,
    required String password,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _authService.register(
        username: username,
        email: email,
        password: password,
      );

      if (response.containsKey('token')) {
        // Store token
        _token = response['token'];
        _refreshToken = response['refresh_token'];

        await _secureStorage.write(key: AppConstants.tokenKey, value: _token);
        await _secureStorage.write(
            key: AppConstants.refreshTokenKey, value: _refreshToken);

        // Get user profile
        await _fetchUserProfile();

        // Store user data in preferences
        await _storeUserData();

        // Initialize OneSignal with user ID after successful registration
        await _setOneSignalUserId();

        return true;
      } else {
        _error = response['message'] ?? 'Registration failed';
        return false;
      }
    } catch (e) {
      _error = 'Registration error: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Login with backend-provided tokens and user data
  Future<bool> login({
    required String email,
    required String token,
    required String refreshToken,
    required Map<String, dynamic> user,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Store tokens
      _token = token;
      _refreshToken = refreshToken;

      await _secureStorage.write(key: AppConstants.tokenKey, value: token);
      await _secureStorage.write(
          key: AppConstants.refreshTokenKey, value: refreshToken);

      // Create user from provided data
      _currentUser = User.fromJson(user);

      // Store user data in preferences
      await _storeUserData();

      // Initialize OneSignal with user ID after successful login
      await _setOneSignalUserId();

      if (kDebugMode) {
        print('✅ User logged in: ${_currentUser?.displayName}');
        print('✅ Using backend auth token: ${token.substring(0, 10)}...');
      }

      return true;
    } catch (e) {
      _error = 'Login error: $e';
      if (kDebugMode) {
        print('❌ Login error: $e');
      }
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Logout user
  BuildContext? _currentContext;

  /// Set the current BuildContext to be used during logout
  void setCurrentContext(BuildContext context) {
    _currentContext = context;
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();
    
    // Get SharedPreferences instance
    final prefs = await SharedPreferences.getInstance();
    
    // Ensure we have a valid context
    if (_currentContext == null) {
      debugPrint('⚠️ Warning: No BuildContext set for logout. WebSocket may not disconnect properly.');
    }
    
    try {
      // Remove OneSignal external user ID before logout
      await _removeOneSignalUserId();

      // Call logout API (to invalidate token on server)
      if (_token != null) {
        await _authService.logout(_token!);
      }
    } catch (e) {
      print('Logout API error: $e');
      // Continue with local logout regardless of API error
    }

    // Clear stored data
    await _secureStorage.delete(key: AppConstants.tokenKey);
    await _secureStorage.delete(key: AppConstants.refreshTokenKey);

    await prefs.remove('user_data');
    
    // Clear Apple Music intro dialog flag so it shows again on next login
    await prefs.remove('has_shown_apple_music_intro');
    
    // Clear Apple Music authentication data
    // await prefs.remove('apple_music_developer_token');
    // await prefs.remove('apple_music_user_token');
    // await prefs.remove('apple_music_auth_status');
    // await prefs.remove('apple_music_user_id');
    
    // Clear gamification cached data
    await prefs.remove('achievements_cache');
    await prefs.remove('achievements_last_load_time');
    await prefs.remove('user_level_cache');
    await prefs.remove('user_xp_cache');
    await prefs.remove('user_progress_cache');
    await prefs.remove('last_xp_update_time');
    
    // Clear map style preference
    await prefs.remove('selected_map_style');
    
    // Clear notification permission flag
    await prefs.remove('has_requested_notification_permission');
    
    // Clear challenges cache data
    await prefs.remove('challenges_cache_data');
    await prefs.remove('challenges_cache_timestamp');
    await prefs.remove('challenges_ui_state');
    
    // Clear user profile cache data
    await prefs.remove('user_header_cache');
    await prefs.remove('user_profile_cache');
    
    // Clear friends cache data
    await prefs.remove('friends_cache');
    await prefs.remove('friends_last_update');
    await prefs.remove('friend_requests_cache');
    await prefs.remove('friend_suggestions_cache');
    
    // Clear AI music recommendations cache
    await prefs.remove('ai_music_recommendations');
    await prefs.remove('ai_track_features_cache');
    
    // Clear map cache metadata
    await prefs.remove('map_cache_metadata');
    await prefs.remove('map_cache_file_refs');
    await prefs.remove('downloaded_regions');
    
    // Clear offline regions data
    await prefs.remove('offline_regions');
    
    // Clear theme preferences
    await prefs.remove('theme_preference');
    await prefs.remove('theme_color');
    
    // Clear settings preferences
    await prefs.remove('animated_background_enabled');
    
    // Clear map settings
    await prefs.remove('show_feature_info');
    await prefs.remove('use_3d_buildings');
    await prefs.remove('show_debug_info');

    // Clear in-memory data
    _token = null;
    _refreshToken = null;
    _currentUser = null;
    
    // Reset Global AI Provider Service
    try {
      final globalAIProviderService = GlobalAIProviderService.instance;
      globalAIProviderService.reset();
    } catch (e) {
      print('Error resetting Global AI Provider Service: $e');
    }
    
      // Dispose gamification provider resources
    try {
      if (_currentContext != null) {
        // Get the GamificationProvider instance and disconnect WebSocket
        final gamificationProvider = Provider.of<GamificationProvider>(_currentContext!, listen: false);
        await gamificationProvider.disconnectWebSocket();
        debugPrint('🔌 Disconnected gamification WebSocket on logout');
      } else {
        debugPrint('⚠️ Could not disconnect WebSocket: No context available');
      }
    } catch (e) {
      debugPrint('Error disconnecting gamification WebSocket: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  // Mock login for testing and development
  Future<bool> simulateLogin({
    required String userId,
    required String name,
    required String email,
    String? profilePic,
    String? bio,
    String? authToken,
  }) async {
    try {
      // Start loading
      _isLoading = true;
      notifyListeners();

      // Add a small delay to simulate network request
      await Future.delayed(const Duration(milliseconds: 500));

      // Create a mock user
      _currentUser = User(
        id: int.tryParse(userId) ?? 1,
        username: name,
        email: email,
        isVerified: true,
        profilePicUrl: profilePic,
        bio: bio ?? 'BOPMaps user',
        createdAt: DateTime.now(),
        favoriteGenres: ['Pop', 'Rock', 'Hip-Hop'],
        connectedServices: {'spotify': true},
      );

      // Use provided authToken or generate a mock token
      _token =
          authToken ?? 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
      _refreshToken =
          'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}';

      // Store the data
      await _secureStorage.write(key: AppConstants.tokenKey, value: _token);
      await _secureStorage.write(
          key: AppConstants.refreshTokenKey, value: _refreshToken);
      await _storeUserData();

      // Initialize OneSignal with user ID after successful simulated login
      await _setOneSignalUserId();

      // Allow a small delay for state to propagate
      await Future.delayed(const Duration(milliseconds: 200));

      notifyListeners();

      if (kDebugMode) {
        print('✅ User logged in (simulated): $name');
        if (authToken != null) {
          print('✅ Using backend auth token: ${authToken.substring(0, 10)}...');
        }
      }
      return true;
    } catch (e) {
      _error = 'Simulated login error: $e';
      _isLoading = false;
      notifyListeners();

      if (kDebugMode) {
        print('❌ Simulated login error: $e');
      }
      return false;
    }
  }

  // Sign in with Google
  Future<bool> signInWithGoogle() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // This is a mock implementation since we're not actually connecting to Google
      // In a real app, we would use the GoogleSignIn package to authenticate

      // Simulate a successful login
      await Future.delayed(const Duration(seconds: 1));

      // Create a mock user for testing
      _currentUser = User(
        id: 1,
        username: 'googleuser',
        email: '<EMAIL>',
        isVerified: true,
        profilePicUrl: null,
        bio: 'Logged in with Google',
        createdAt: DateTime.now(),
        favoriteGenres: ['Pop', 'Rock'],
        connectedServices: {'spotify': true},
      );

      // Set a mock token
      _token = 'mock_google_token';
      _refreshToken = 'mock_google_refresh_token';

      // Store the mock data
      await _secureStorage.write(key: AppConstants.tokenKey, value: _token);
      await _secureStorage.write(
          key: AppConstants.refreshTokenKey, value: _refreshToken);
      await _storeUserData();

      // Initialize OneSignal with user ID after successful Google sign-in
      await _setOneSignalUserId();

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Google sign-in error: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Fetch user profile from API
  Future<void> _fetchUserProfile() async {
    if (_token == null) return;

    try {
      if (kDebugMode) {
        print('🔍 Fetching user profile from backend...');
        print('🔑 Using token: ${_token!.substring(0, 10)}...');
      }

      final response = await _authService.getUserProfile(_token!);

      if (response.containsKey('id')) {
        if (kDebugMode) {
          print('✅ User profile fetched successfully');
          print('📋 Profile data: ${response.keys.join(', ')}');
          print('👤 Backend username: ${response['username']}');
          print('📧 Backend email: ${response['email']}');
          print('🏷️ Backend display_name: ${response['display_name']}');
          print('🆔 Backend user ID: ${response['id']}');
        }

        _currentUser = User.fromJson(response);
        await _storeUserData();

        // Set OneSignal external user ID after profile is loaded
        await _setOneSignalUserId();

        if (kDebugMode) {
          print('✅ User profile loaded in app:');
          print('👤 App username: ${_currentUser?.username}');
          print('🏷️ App displayName: ${_currentUser?.displayName}');
          print(
              '🎯 App displayNameComputed: ${_currentUser?.displayNameComputed}');
          print('📧 App email: ${_currentUser?.email}');
          print(
              '🖼️ Profile pic: ${_currentUser?.profilePicUrl != null ? 'Yes' : 'No'}');
        }
      } else {
        throw Exception(
            'Failed to fetch user profile - invalid response format');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching user profile: $e');
      }
      // If we get a 401 error, token might be expired
      if (e.toString().contains('401')) {
        await _refreshAuthToken();
      }
    }
  }

  // Store user data in SharedPreferences
  Future<void> _storeUserData() async {
    if (_currentUser == null) return;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_data', json.encode(_currentUser!.toJson()));

    // Set OneSignal external user ID when storing user data
    await _setOneSignalUserId();
  }

  // Set OneSignal external user ID
  Future<void> _setOneSignalUserId() async {
    if (_currentUser == null) return;

    try {
      // Use the user ID as the external user ID for OneSignal
      final externalUserId = _currentUser!.id.toString();
      
      // Try to get OneSignalProvider from context if available
      if (_currentContext != null) {
        try {
          final oneSignalProvider = Provider.of<OneSignalProvider>(_currentContext!, listen: false);
          
          // Initialize OneSignal with user ID
          await oneSignalProvider.initialize(userId: externalUserId);
          
          // Add user tags for better segmentation
          await oneSignalProvider.addTags({
            'user_id': externalUserId,
            'username': _currentUser!.username,
            'email': _currentUser!.email,
            'is_verified': _currentUser!.isVerified.toString(),
            'has_spotify': _currentUser!.hasConnectedService('spotify').toString(),
            'has_apple_music':
                _currentUser!.hasConnectedService('apple_music').toString(),
            'has_soundcloud':
                _currentUser!.hasConnectedService('soundcloud').toString(),
          });
          
          if (kDebugMode) {
            print('✅ OneSignal external user ID set: $externalUserId');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error accessing OneSignalProvider from context: $e');
          }
          // Fallback to direct OneSignalService usage
          await _setOneSignalUserIdDirect();
        }
      } else {
        // No context available, use direct service
        await _setOneSignalUserIdDirect();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error setting OneSignal external user ID: $e');
      }
    }
  }

  // Fallback method using OneSignalService directly
  Future<void> _setOneSignalUserIdDirect() async {
    if (_currentUser == null) return;

    try {
      final externalUserId = _currentUser!.id.toString();
      final oneSignalService = OneSignalService();
      
      // Initialize OneSignal with user ID
      await oneSignalService.initialize(userId: externalUserId);
      
      // Add user tags for better segmentation
      await oneSignalService.addTags({
        'user_id': externalUserId,
        'username': _currentUser!.username,
        'email': _currentUser!.email,
        'is_verified': _currentUser!.isVerified.toString(),
        'has_spotify': _currentUser!.hasConnectedService('spotify').toString(),
        'has_apple_music':
            _currentUser!.hasConnectedService('apple_music').toString(),
        'has_soundcloud':
            _currentUser!.hasConnectedService('soundcloud').toString(),
      });
      
      if (kDebugMode) {
        print('✅ OneSignal external user ID set (direct): $externalUserId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error setting OneSignal external user ID (direct): $e');
      }
    }
  }

  // Remove OneSignal external user ID
  Future<void> _removeOneSignalUserId() async {
    try {
      if (_currentContext != null) {
        try {
          final oneSignalProvider = Provider.of<OneSignalProvider>(_currentContext!, listen: false);
          await oneSignalProvider.removeExternalUserId();
          
          if (kDebugMode) {
            print('✅ OneSignal external user ID removed');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error accessing OneSignalProvider from context: $e');
          }
          // Fallback to direct OneSignalService usage
          await _removeOneSignalUserIdDirect();
        }
      } else {
        // No context available, use direct service
        await _removeOneSignalUserIdDirect();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error removing OneSignal external user ID: $e');
      }
    }
  }

  // Fallback method for removing OneSignal external user ID
  Future<void> _removeOneSignalUserIdDirect() async {
    try {
      final oneSignalService = OneSignalService();
      await oneSignalService.removeExternalUserId();
      
      if (kDebugMode) {
        print('✅ OneSignal external user ID removed (direct)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error removing OneSignal external user ID (direct): $e');
      }
    }
  }

  // Refresh authentication token
  Future<bool> _refreshAuthToken() async {
    if (_refreshToken == null) return false;

    try {
      final response = await _authService.refreshToken(_refreshToken!);

      if (response.containsKey('token')) {
        _token = response['token'];
        _refreshToken = response['refresh_token'];

        await _secureStorage.write(key: AppConstants.tokenKey, value: _token);
        await _secureStorage.write(
            key: AppConstants.refreshTokenKey, value: _refreshToken);

        return true;
      }
      return false;
    } catch (e) {
      print('Error refreshing token: $e');
      // If refresh fails, force logout
      await logout();
      return false;
    }
  }

  // Update user profile - now calls the correct backend endpoint
  Future<bool> updateProfile({
    String? username,
    String? bio,
    List<String>? favoriteGenres,
  }) async {
    if (_token == null || _currentUser == null) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      if (kDebugMode) {
        print('🔄 Updating user profile...');
        print('📝 Bio: $bio');
        print('👤 Username: $username');
      }

      final response = await _authService.updateProfile(
        token: _token!,
        username: username,
        bio: bio,
        favoriteGenres: favoriteGenres,
      );

      if (response.containsKey('id')) {
        if (kDebugMode) {
          print('✅ Profile updated successfully');
        }

        _currentUser = User.fromJson(response);
        await _storeUserData();

        // Update OneSignal tags with new profile data
        await _setOneSignalUserId();

        return true;
      } else {
        _error = response['message'] ?? 'Profile update failed';
        if (kDebugMode) {
          print('❌ Profile update failed: $_error');
        }
        return false;
      }
    } catch (e) {
      _error = 'Profile update error: $e';
      if (kDebugMode) {
        print('❌ Profile update error: $e');
      }
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (_token == null) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _authService.changePassword(
        token: _token!,
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (response['success'] == true) {
        return true;
      } else {
        _error = response['message'] ?? 'Password change failed';
        return false;
      }
    } catch (e) {
      _error = 'Password change error: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Request password reset
  Future<bool> requestPasswordReset(String email) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _authService.requestPasswordReset(email);

      if (response['success'] == true) {
        return true;
      } else {
        _error = response['message'] ?? 'Password reset request failed';
        return false;
      }
    } catch (e) {
      _error = 'Password reset request error: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Verify email
  Future<bool> verifyEmail(String code) async {
    if (_token == null || _currentUser == null) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _authService.verifyEmail(_token!, code);

      if (response['success'] == true) {
        // Update user verified status
        _currentUser = _currentUser!.copyWith(isVerified: true);
        await _storeUserData();
        return true;
      } else {
        _error = response['message'] ?? 'Email verification failed';
        return false;
      }
    } catch (e) {
      _error = 'Email verification error: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Connect music service
  Future<bool> connectMusicService(String service) async {
    if (_token == null || _currentUser == null) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _authService.connectMusicService(_token!, service);

      if (response['success'] == true) {
        // Update connected services
        Map<String, bool> updatedServices =
            Map.from(_currentUser!.connectedServices);
        updatedServices[service] = true;

        _currentUser =
            _currentUser!.copyWith(connectedServices: updatedServices);
        await _storeUserData();

        // Update OneSignal tags with new connected service
        await _setOneSignalUserId();

        return true;
      } else {
        _error = response['message'] ?? 'Music service connection failed';
        return false;
      }
    } catch (e) {
      _error = 'Music service connection error: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Helper to get authorization header for API requests
  Map<String, String> get authHeaders {
    return {
      'Authorization': 'Bearer $_token',
      'Content-Type': 'application/json',
    };
  }
}
