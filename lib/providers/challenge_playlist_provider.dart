import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/music/song_entry.dart';
import '../services/api/weekly_challenges_api_service.dart';
import '../services/location/location_service.dart';
import '../services/music/shuffle_service.dart';
import '../providers/spotify_provider.dart';
import '../providers/apple_music_provider.dart';
import 'dart:convert';

enum SortType {
  main, // By score (upvotes - downvotes)
  recent, // By drop time
}

class ChallengePlaylistProvider extends ChangeNotifier {
  final WeeklyChallengesApiService _apiService;
  final LocationService _locationService = LocationService();
  
  List<SongEntry> _entries = [];
  bool _showFriendsOnly = false;
  bool _showSchoolOnly = false;
  SortType _sortType = SortType.main;
  bool _isLoading = false;
  String? _error;
  String? _currentChallengeId; // Store current challenge ID for reloading

  ChallengePlaylistProvider(this._apiService);

  // Getters
  List<SongEntry> get entries => _getFilteredAndSortedEntries();
  bool get showFriendsOnly => _showFriendsOnly;
  bool get showSchoolOnly => _showSchoolOnly;
  SortType get sortType => _sortType;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Methods
  void toggleFriendsOnly() {
    _showFriendsOnly = !_showFriendsOnly;
    // If enabling friends filter, disable school filter
    if (_showFriendsOnly) {
      _showSchoolOnly = false;
    }
    notifyListeners();
    
    // Reload entries with new filter in background without clearing UI
    if (_currentChallengeId != null) {
      _loadEntriesInBackground(_currentChallengeId!);
    }
  }

  void toggleSchoolOnly() {
    _showSchoolOnly = !_showSchoolOnly;
    // If enabling school filter, disable friends filter
    if (_showSchoolOnly) {
      _showFriendsOnly = false;
    }
    notifyListeners();
    
    // Reload entries with new filter in background without clearing UI
    if (_currentChallengeId != null) {
      _loadEntriesInBackground(_currentChallengeId!);
    }
  }

  void setSortType(SortType type) {
    _sortType = type;
    notifyListeners();
  }

  void shuffleEntries() {
    // Just shuffle the UI list for visual feedback, but don't play music yet
    // The actual music shuffling will be handled by the header button
    _entries.shuffle();
    notifyListeners();
  }

  /// New method: Actually shuffle and play the challenge entries using ShuffleService
  Future<void> shuffleAndPlay({
    required SpotifyProvider spotifyProvider,
    required AppleMusicProvider appleMusicProvider,
    ShuffleMode mode = ShuffleMode.random,
  }) async {
    if (_entries.isEmpty) {
      throw 'No songs to shuffle';
    }

    try {
      debugPrint('🎲 [ChallengePlaylist] Starting shuffle with ${_entries.length} entries');
      
      // Use ShuffleService to actually shuffle and play the music
      final result = await ShuffleService.shuffleChallengeEntries(
        entries: _entries,
        spotifyProvider: spotifyProvider,
        appleMusicProvider: appleMusicProvider,
        mode: mode,
        clearExistingQueue: true,
        maxTracks: 50, // Reasonable limit
      );

      if (result.success) {
        // Also shuffle the UI list for visual feedback
        _entries.shuffle();
        notifyListeners();
        
        debugPrint('✅ [ChallengePlaylist] Successfully shuffled and playing ${result.tracksQueued} tracks');
      } else {
        debugPrint('❌ [ChallengePlaylist] Shuffle failed: ${result.message}');
        throw result.message;
      }
    } catch (e) {
      debugPrint('❌ [ChallengePlaylist] Error in shuffleAndPlay: $e');
      rethrow;
    }
  }

  Future<void> loadEntries(String challengeId) async {
    if (_isLoading) return;
    
    _currentChallengeId = challengeId; // Store challenge ID
    _isLoading = true;
    _error = null;
    _entries = []; // Clear existing entries immediately
    notifyListeners(); // Notify UI of loading state

    try {
      // Determine scope based on active filters
      String scope = 'all';
      if (_showFriendsOnly) {
        scope = 'friends';
      } else if (_showSchoolOnly) {
        scope = 'school';
      }

      // Fetch entries from API with appropriate scope
      final response = await _apiService.getChallengeEntries(
        challengeId: challengeId,
        scope: scope,
      );

      // Parse GeoJSON response into SongEntry objects
      _entries = response['features'].map((feature) {
        final props = feature['properties'];
        final songData = props['song'] as Map<String, dynamic>? ?? {};
        
        // Add debug logging
        debugPrint('🎵 Parsing entry:');
        debugPrint('  - Feature ID: ${feature['id']}');
        debugPrint('  - Properties: $props');
        
        try {
          // Get the entry ID from either the feature ID or properties
          final entryId = feature['id'] as int? ?? props['entry_id'] as int?;
          if (entryId == null) {
            debugPrint('⚠️ Entry ID is missing in both feature.id and properties.entry_id');
            return null;
          }
          
          debugPrint('🎵 Creating entry with entry ID: $entryId');

          return SongEntry(
            id: entryId,
            songId: songData['spotify_id'] ?? '',
            title: songData['title'] ?? 'Unknown Title',
            artist: songData['artist'] ?? 'Unknown Artist',
            albumArt: songData['album_art'] ?? '',
            dropperName: (props['user'] as Map<String, dynamic>)['username'] ?? 'Unknown User',
            dropperAvatarUrl: (props['user'] as Map<String, dynamic>)['profile_pic'],
            isFromFriend: props['is_friend'] ?? false,
            droppedAt: DateTime.parse(props['created_at'] ?? DateTime.now().toIso8601String()),
            upvotes: props['upvotes'] ?? 0,
            downvotes: props['downvotes'] ?? 0,
            voteScore: props['vote_score'] ?? 0,
            userVote: props['user_vote'] == 1 ? true : 
                     props['user_vote'] == -1 ? false : null,
          );
        } catch (e) {
          debugPrint('⚠️ Failed to create SongEntry: $e');
          debugPrint('  - Props: $props');
          debugPrint('  - Song Data: $songData');
          return null;
        }
      }).whereType<SongEntry>().toList();

      debugPrint('🎵 Loaded ${_entries.length} entries');
      _error = null;
    } catch (e) {
      _error = 'Failed to load entries: $e';
      debugPrint('Error loading entries: $e');
      _entries = []; // Clear entries on error
    } finally {
      _isLoading = false;
      notifyListeners(); // Notify UI of new entries or error state
    }
  }

  Future<void> vote(int entryId, bool isUpvote) async {
    // Find the entry
    final index = _entries.indexWhere((e) => e.id == entryId);
    if (index == -1) {
      debugPrint('⚠️ Vote failed: Entry not found: $entryId');
      return;
    }

    // Get the current entry
    final entry = _entries[index];
    final currentVote = entry.userVote;
    
    debugPrint('🗳️ Vote Action:');
    debugPrint('  - Entry ID: $entryId');
    debugPrint('  - Current vote state: ${currentVote == null ? "no vote" : currentVote ? "upvoted" : "downvoted"}');
    debugPrint('  - Attempting to: ${isUpvote ? "upvote" : "downvote"}');
    debugPrint('  - Current counts: up=${entry.upvotes}, down=${entry.downvotes}, score=${entry.voteScore}');
    
    try {
      // Submit vote to API
      debugPrint('🗳️ Sending to API: ${isUpvote ? "upvote" : "downvote"}');
      
      final response = await _apiService.voteChallengeEntry(
        entryId: entryId,
        isUpvote: isUpvote,
      );

      debugPrint('🗳️ API Response:');
      debugPrint('  - Success: ${response.success}');
      debugPrint('  - Status: ${response.statusCode}');
      debugPrint('  - Raw Data: ${response.data}');
      debugPrint('  - Error: ${response.error}');
      debugPrint('  - Message: ${response.message}');

      if (response.success) {
        // Update entry with response data
        final voteData = response.data;
        
        // Handle user_vote properly - if it's null in the response, it means no vote
        final apiUserVote = voteData['user_vote'];
        final newUserVote = apiUserVote == null ? null :  // If null from API, vote was removed
                           apiUserVote == 1 ? true :      // If 1, it's an upvote
                           apiUserVote == -1 ? false :    // If -1, it's a downvote
                           null;                          // Fallback to no vote
        
        final updatedEntry = entry.copyWith(
          upvotes: voteData['upvotes'] as int? ?? entry.upvotes,
          downvotes: voteData['downvotes'] as int? ?? entry.downvotes,
          voteScore: voteData['score'] as int? ?? entry.voteScore,
          userVote: newUserVote,  // Use the properly parsed user vote
        );
        
        debugPrint('🗳️ Vote Result:');
        debugPrint('  - API user_vote value: $apiUserVote');
        debugPrint('  - Previous state: up=${entry.upvotes}, down=${entry.downvotes}, score=${entry.voteScore}, vote=${entry.userVote}');
        debugPrint('  - New state: up=${updatedEntry.upvotes}, down=${updatedEntry.downvotes}, score=${updatedEntry.voteScore}, vote=${updatedEntry.userVote}');
        debugPrint('  - Vote changed from ${entry.userVote} to ${updatedEntry.userVote}');

        _entries[index] = updatedEntry;
        notifyListeners();
      } else {
        _error = 'Failed to record vote: ${response.message}';
        notifyListeners();
        debugPrint('⚠️ Vote failed: ${response.message}');
      }
    } catch (e) {
      _error = 'Failed to record vote: $e';
      notifyListeners();
      debugPrint('⚠️ Vote failed with error: $e');
    }
  }

  List<SongEntry> _getFilteredAndSortedEntries() {
    // Since API filtering by scope is handled in loadEntries(),
    // we just need to sort the already-filtered entries
    var filtered = List<SongEntry>.from(_entries);

    switch (_sortType) {
      case SortType.main:
        filtered.sort((a, b) => b.score.compareTo(a.score));
        break;
      case SortType.recent:
        filtered.sort((a, b) => b.droppedAt.compareTo(a.droppedAt));
        break;
    }

    return filtered;
  }

  // Background loading without disrupting UI
  Future<void> _loadEntriesInBackground(String challengeId) async {
    try {
      // Determine scope based on active filters
      String scope = 'all';
      if (_showFriendsOnly) {
        scope = 'friends';
      } else if (_showSchoolOnly) {
        scope = 'school';
      }

      // Fetch entries from API with appropriate scope (no loading state change)
      final response = await _apiService.getChallengeEntries(
        challengeId: challengeId,
        scope: scope,
      );

      // Parse GeoJSON response into SongEntry objects
      final newEntries = response['features'].map((feature) {
        final props = feature['properties'];
        final songData = props['song'] as Map<String, dynamic>? ?? {};
        
        try {
          // Get the entry ID from either the feature ID or properties
          final entryId = feature['id'] as int? ?? props['entry_id'] as int?;
          if (entryId == null) {
            debugPrint('⚠️ Entry ID is missing in both feature.id and properties.entry_id');
            return null;
          }

          return SongEntry(
            id: entryId,
            songId: songData['spotify_id'] ?? '',
            title: songData['title'] ?? 'Unknown Title',
            artist: songData['artist'] ?? 'Unknown Artist',
            albumArt: songData['album_art'] ?? '',
            dropperName: (props['user'] as Map<String, dynamic>)['username'] ?? 'Unknown User',
            dropperAvatarUrl: (props['user'] as Map<String, dynamic>)['profile_pic'],
            isFromFriend: props['is_friend'] ?? false,
            droppedAt: DateTime.parse(props['created_at'] ?? DateTime.now().toIso8601String()),
            upvotes: props['upvotes'] ?? 0,
            downvotes: props['downvotes'] ?? 0,
            voteScore: props['vote_score'] ?? 0,
            userVote: props['user_vote'] == 1 ? true : 
                     props['user_vote'] == -1 ? false : null,
          );
        } catch (e) {
          debugPrint('⚠️ Failed to create SongEntry: $e');
          return null;
        }
      }).whereType<SongEntry>().toList();

      // Update entries without showing loading state
      _entries = newEntries;
      _error = null;
      notifyListeners();
      
      debugPrint('🎵 Background loaded ${_entries.length} entries for scope: $scope');
    } catch (e) {
      _error = 'Failed to load filtered entries: $e';
      debugPrint('Error loading filtered entries: $e');
      notifyListeners();
    }
  }
} 