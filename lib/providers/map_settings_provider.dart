import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider to manage map settings that persist between app sessions
class MapSettingsProvider with ChangeNotifier {
  // Default values
  bool _showFeatureInfo = false; // Disabled by default
  bool _use3DBuildings = true;
  bool _showDebugInfo = false;
  
  // Getters
  bool get showFeatureInfo => _showFeatureInfo;
  bool get use3DBuildings => _use3DBuildings;
  bool get showDebugInfo => _showDebugInfo;
  
  // Preference keys
  static const String _keyShowFeatureInfo = 'map_show_feature_info';
  static const String _keyUse3DBuildings = 'map_use_3d_buildings';
  static const String _keyShowDebugInfo = 'map_show_debug_info';
  
  // Constructor
  MapSettingsProvider() {
    _loadSettings();
  }
  
  // Load settings from shared preferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _showFeatureInfo = prefs.getBool(_keyShowFeatureInfo) ?? false;
      _use3DBuildings = prefs.getBool(_keyUse3DBuildings) ?? true;
      _showDebugInfo = prefs.getBool(_keyShowDebugInfo) ?? false;
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading map settings: $e');
    }
  }
  
  // Toggle feature info
  Future<void> toggleFeatureInfo() async {
    _showFeatureInfo = !_showFeatureInfo;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyShowFeatureInfo, _showFeatureInfo);
    } catch (e) {
      debugPrint('Error saving show feature info setting: $e');
    }
  }
  
  // Toggle 3D buildings
  Future<void> toggle3DBuildings() async {
    _use3DBuildings = !_use3DBuildings;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyUse3DBuildings, _use3DBuildings);
    } catch (e) {
      debugPrint('Error saving 3D buildings setting: $e');
    }
  }
  
  // Centralized method to toggle map view mode (2D/3D)
  // Returns the new 3D state for convenience
  Future<bool> toggleMapViewMode() async {
    debugPrint('🗺️ SETTINGS: Toggling map view from ${_use3DBuildings ? "3D" : "2D"} to ${!_use3DBuildings ? "3D" : "2D"}');
    
    // Toggle the 3D buildings setting
    await toggle3DBuildings();
    
    // Return the new state after toggling
    return _use3DBuildings;
  }
  
  // Toggle debug info
  Future<void> toggleDebugInfo() async {
    _showDebugInfo = !_showDebugInfo;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyShowDebugInfo, _showDebugInfo);
    } catch (e) {
      debugPrint('Error saving debug info setting: $e');
    }
  }
} 