import 'package:flutter/material.dart';
import '../models/pin.dart';
import '../services/api/pins_service.dart';

class PinProvider with ChangeNotifier {
  final PinsService _pinsService = PinsService();
  
  List<Pin> _pins = [];
  List<Pin> _userPins = [];
  List<Pin> _collectedPins = [];
  List<Pin> _nearbyPins = [];
  bool _isLoading = false;
  String? _errorMessage;
  
  // Currently playing pin tracking
  String? _currentlyPlayingPinId;
  Map<String, dynamic>? _currentlyPlayingPinData;
  
  // Getters
  List<Pin> get pins => _pins;
  List<Pin> get userPins => _userPins;
  List<Pin> get collectedPins => _collectedPins;
  List<Pin> get nearbyPins => _nearbyPins;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  
  // Currently playing pin getters
  String? get currentlyPlayingPinId => _currentlyPlayingPinId;
  Map<String, dynamic>? get currentlyPlayingPinData => _currentlyPlayingPinData;
  bool get hasCurrentlyPlayingPin => _currentlyPlayingPinId != null && _currentlyPlayingPinData != null;
  
  // Set currently playing pin
  void setCurrentlyPlayingPin(String pinId, Map<String, dynamic> pinData) {
    debugPrint('🎵 [PinProvider] Setting currently playing pin: $pinId');
    debugPrint('🎵 [PinProvider] Pin data: ${pinData['title'] ?? pinData['track_title']} by ${pinData['artist'] ?? pinData['track_artist']}');
    debugPrint('🎵 [PinProvider] Full pin data: $pinData');

    _currentlyPlayingPinId = pinId;
    _currentlyPlayingPinData = _normalizePinData(pinData);
    notifyListeners();

    debugPrint('🎵 [PinProvider] Successfully set currently playing pin, notified listeners');

    // Record view and collect interactions when pin is played
    _recordPinPlayInteractions(pinId);
  }

  // Normalize pin data to ensure consistent structure across different sources
  Map<String, dynamic> _normalizePinData(Map<String, dynamic> rawPinData) {
    // Create a mutable copy
    final pinData = Map<String, dynamic>.from(rawPinData);

    // Ensure url field exists for music playback
    if (!pinData.containsKey('url') || pinData['url'] == null || pinData['url'].toString().isEmpty) {
      // Try to get URL from track_url or uri
      final trackUrl = pinData['track_url'] ?? pinData['uri'] ?? '';
      pinData['url'] = trackUrl;
    }

    // Ensure track_url field exists
    if (!pinData.containsKey('track_url') || pinData['track_url'] == null || pinData['track_url'].toString().isEmpty) {
      final url = pinData['url'] ?? pinData['uri'] ?? '';
      pinData['track_url'] = url;
    }

    // Ensure consistent title fields
    if (!pinData.containsKey('title') || pinData['title'] == null || pinData['title'].toString().isEmpty) {
      pinData['title'] = pinData['track_title'] ?? pinData['songTitle'] ?? '';
    }
    if (!pinData.containsKey('track_title') || pinData['track_title'] == null || pinData['track_title'].toString().isEmpty) {
      pinData['track_title'] = pinData['title'] ?? pinData['songTitle'] ?? '';
    }

    // Ensure consistent artist fields
    if (!pinData.containsKey('artist') || pinData['artist'] == null || pinData['artist'].toString().isEmpty) {
      pinData['artist'] = pinData['track_artist'] ?? '';
    }
    if (!pinData.containsKey('track_artist') || pinData['track_artist'] == null || pinData['track_artist'].toString().isEmpty) {
      pinData['track_artist'] = pinData['artist'] ?? '';
    }

    debugPrint('🎵 [PinProvider] Normalized pin data - url: ${pinData['url']}, track_url: ${pinData['track_url']}');

    return pinData;
  }
  
  // Record both view and collect interactions when a pin is played
  Future<void> _recordPinPlayInteractions(String pinId) async {
    try {
      debugPrint('🎵 [PinProvider] Recording play interactions for pin: $pinId');
      
      // Record view interaction
      final viewSuccess = await _pinsService.viewPin(pinId);
      if (viewSuccess) {
        debugPrint('✅ [PinProvider] Successfully recorded view interaction for pin: $pinId');
      } else {
        debugPrint('❌ [PinProvider] Failed to record view interaction for pin: $pinId');
      }
      
      // Record collect interaction
      final collectSuccess = await _pinsService.collectPin(pinId);
      if (collectSuccess) {
        debugPrint('✅ [PinProvider] Successfully recorded collect interaction for pin: $pinId');
      } else {
        debugPrint('❌ [PinProvider] Failed to record collect interaction for pin: $pinId');
      }
      
    } catch (e) {
      debugPrint('❌ [PinProvider] Error recording play interactions for pin $pinId: $e');
    }
  }
  
  // Manual interaction recording methods
  Future<bool> recordPinView(String pinId) async {
    try {
      debugPrint('🎵 [PinProvider] Manually recording view interaction for pin: $pinId');
      final success = await _pinsService.viewPin(pinId);
      if (success) {
        debugPrint('✅ [PinProvider] Successfully recorded view interaction for pin: $pinId');
      } else {
        debugPrint('❌ [PinProvider] Failed to record view interaction for pin: $pinId');
      }
      return success;
    } catch (e) {
      debugPrint('❌ [PinProvider] Error recording view interaction for pin $pinId: $e');
      return false;
    }
  }
  
  Future<bool> recordPinCollect(String pinId) async {
    try {
      debugPrint('🎵 [PinProvider] Manually recording collect interaction for pin: $pinId');
      final success = await _pinsService.collectPin(pinId);
      if (success) {
        debugPrint('✅ [PinProvider] Successfully recorded collect interaction for pin: $pinId');
      } else {
        debugPrint('❌ [PinProvider] Failed to record collect interaction for pin: $pinId');
      }
      return success;
    } catch (e) {
      debugPrint('❌ [PinProvider] Error recording collect interaction for pin $pinId: $e');
      return false;
    }
  }
  
  Future<bool> recordPinLike(String pinId) async {
    try {
      debugPrint('🎵 [PinProvider] Manually recording like interaction for pin: $pinId');
      final success = await _pinsService.likePin(pinId);
      if (success) {
        debugPrint('✅ [PinProvider] Successfully recorded like interaction for pin: $pinId');
      } else {
        debugPrint('❌ [PinProvider] Failed to record like interaction for pin: $pinId');
      }
      return success;
    } catch (e) {
      debugPrint('❌ [PinProvider] Error recording like interaction for pin $pinId: $e');
      return false;
    }
  }
  
  Future<bool> recordPinShare(String pinId) async {
    try {
      debugPrint('🎵 [PinProvider] Manually recording share interaction for pin: $pinId');
      final success = await _pinsService.sharePin(pinId);
      if (success) {
        debugPrint('✅ [PinProvider] Successfully recorded share interaction for pin: $pinId');
      } else {
        debugPrint('❌ [PinProvider] Failed to record share interaction for pin: $pinId');
      }
      return success;
    } catch (e) {
      debugPrint('❌ [PinProvider] Error recording share interaction for pin $pinId: $e');
      return false;
    }
  }
  
  // Clear currently playing pin
  void clearCurrentlyPlayingPin() {
    debugPrint('🎵 [PinProvider] Clearing currently playing pin: $_currentlyPlayingPinId');
    
    _currentlyPlayingPinId = null;
    _currentlyPlayingPinData = null;
    notifyListeners();
    
    debugPrint('🎵 [PinProvider] Successfully cleared currently playing pin, notified listeners');
  }
  
  // Get caption for currently playing pin
  String? getCurrentlyPlayingPinCaption() {
    if (_currentlyPlayingPinData == null) return null;
    
    return _currentlyPlayingPinData!['caption'] as String? ?? 
           _currentlyPlayingPinData!['description'] as String?;
  }
  
  // Load all pins
  Future<void> loadAllPins() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      // This is a placeholder until we implement the actual API call
      // In a real app, you would call _pinsService.getAllPins()
      await Future.delayed(const Duration(seconds: 1));
      _pins = []; // Would be populated with real data
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to load pins: ${e.toString()}';
      notifyListeners();
    }
  }
  
  // Load user's pins
  Future<void> loadUserPins() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      _userPins = await _pinsService.getMyPins();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to load user pins: ${e.toString()}';
      notifyListeners();
    }
  }
  
  // Load collected pins
  Future<void> loadCollectedPins() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      // This is a placeholder until we implement the actual API call
      // In a real app, you would call _pinsService.getCollectedPins()
      await Future.delayed(const Duration(seconds: 1));
      _collectedPins = []; // Would be populated with real data
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to load collected pins: ${e.toString()}';
      notifyListeners();
    }
  }
  
  // Load pins near a given location and radius
  Future<void> loadNearbyPins(double latitude, double longitude, double radius) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      _nearbyPins = await _pinsService.getNearbyPins(latitude, longitude, radius);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to load nearby pins: ${e.toString()}';
      notifyListeners();
    }
  }
  
  // Create a new pin
  Future<bool> createPin({
    required String title,
    String? description,
    String? caption,
    required String trackTitle,
    required String trackArtist,
    String? album,
    required String trackUrl,
    required String service,
    required int skin,
    required double latitude,
    required double longitude,
    double? auraRadius,
    bool isPrivate = false,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      final pin = await _pinsService.createPin(
        title: title,
        description: description,
        caption: caption,
        trackTitle: trackTitle,
        trackArtist: trackArtist,
        album: album,
        trackUrl: trackUrl,
        service: service,
        skin: skin,
        latitude: latitude,
        longitude: longitude,
        auraRadius: auraRadius,
        isPrivate: isPrivate,
      );
      
      if (pin != null) {
        _pins.add(pin);
        _userPins.add(pin);
        _isLoading = false;
        notifyListeners();
        return true;
      }
      
      _isLoading = false;
      _errorMessage = 'Failed to create pin.';
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Error creating pin: ${e.toString()}';
      notifyListeners();
      return false;
    }
  }
  
  // Collect a pin
  Future<bool> collectPin(String pinId) async {
    try {
      final success = await _pinsService.collectPin(pinId);
      
      if (success) {
        // Find the pin in our lists
        final pin = _pins.firstWhere((p) => p.id.toString() == pinId);
        
        // Add to collected pins if not already there
        if (!_collectedPins.any((p) => p.id.toString() == pinId)) {
          _collectedPins.add(pin);
        }
        
        notifyListeners();
        return true;
      }
      
      return false;
    } catch (e) {
      _errorMessage = 'Error collecting pin: ${e.toString()}';
      notifyListeners();
      return false;
    }
  }
  
  // Delete a pin
  Future<bool> deletePin(String pinId) async {
    try {
      final success = await _pinsService.deletePin(pinId);
      
      if (success) {
        // Remove from all lists
        _pins.removeWhere((p) => p.id.toString() == pinId);
        _userPins.removeWhere((p) => p.id.toString() == pinId);
        _collectedPins.removeWhere((p) => p.id.toString() == pinId);
        
        notifyListeners();
        return true;
      }
      
      return false;
    } catch (e) {
      _errorMessage = 'Error deleting pin: ${e.toString()}';
      notifyListeners();
      return false;
    }
  }
} 