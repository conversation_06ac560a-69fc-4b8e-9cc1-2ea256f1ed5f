import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/constants.dart';
import '../models/user.dart';
import '../services/api/api_client.dart';
import '../services/cloudinary_service.dart';
import 'package:cached_network_image/cached_network_image.dart';

class UserProvider with ChangeNotifier {
  final ApiClient _apiClient = ApiClient();
  
  // User data
  User? _currentUser;
  User? _headerUser; // Lightweight user data for headers
  
  // Loading states
  bool _isLoadingProfile = false;
  bool _isLoadingHeader = false;
  bool _isUpdatingProfile = false;
  
  // Error handling
  String? _error;
  
  // Getters
  User? get currentUser => _currentUser;
  User? get headerUser => _headerUser;
  bool get isLoadingProfile => _isLoadingProfile;
  bool get isLoadingHeader => _isLoadingHeader;
  bool get isUpdatingProfile => _isUpdatingProfile;
  String? get error => _error;
  
  // Get user for header display (username + profile pic)
  String get displayUsername => _headerUser?.username ?? _currentUser?.username ?? 'Username';
  String? get profilePictureUrl => _headerUser?.profilePicUrl ?? _currentUser?.profilePicUrl;


  
  
  /// Fetch lightweight user data for headers/navigation
  /// Uses /api/users/profile_header/ endpoint
  Future<bool> fetchHeaderData() async {
    if (_isLoadingHeader) return false;
    
    _isLoadingHeader = true;
    _error = null;
    notifyListeners();
    
    try {
      if (kDebugMode) {
        print('🔍 Fetching user header data from /api/users/profile_header/');
      }
      
      final response = await _apiClient.get(
        '/users/profile_header/',
        requiresAuth: true,
      );
      
      if (response.containsKey('id')) {
        if (kDebugMode) {
          print('✅ Header data fetched successfully');
          print('👤 Username: ${response['username']}');
          print('🖼️ Profile pic: ${response['profile_pic'] ?? 'None'}');
        }
        
        // Create lightweight user object
        _headerUser = User(
          id: response['id'] ?? 0,
          username: response['username'] ?? '',
          email: '', // Not needed for header
          profilePicUrl: response['profile_pic'],
          isVerified: false, // Not needed for header
          favoriteGenres: [],
          connectedServices: {},
          createdAt: DateTime.now(),
        );
        
        // Cache header data
        await _cacheHeaderData();
        
        return true;
      } else {
        throw Exception('Invalid header response format');
      }
    } catch (e) {
      _error = 'Failed to fetch header data: $e';
      if (kDebugMode) {
        print('❌ Error fetching header data: $e');
      }
      
      // Try to load cached data on error
      await _loadCachedHeaderData();
      return false;
    } finally {
      _isLoadingHeader = false;
      notifyListeners();
    }
  }
  
  /// Fetch complete user profile data
  /// Uses /api/users/me/ endpoint
  Future<bool> fetchFullProfile() async {
    if (_isLoadingProfile) return false;
    
    print('🚀 [UserProvider] ===== STARTING fetchFullProfile =====');
    _isLoadingProfile = true;
    _error = null;
    notifyListeners();
    
    try {
      if (kDebugMode) {
        print('🔍 Fetching full user profile from /api/users/me/');
      }
      
      final response = await _apiClient.get(
        AppConstants.userProfileEndpoint,
        requiresAuth: true,
      );
      
      if (response.containsKey('id')) {
        if (kDebugMode) {
          print('✅ Full profile fetched successfully');
          print('👤 Username: ${response['username']}');
          print('📧 Email: ${response['email']}');
          print('📝 Bio: ${response['bio'] ?? 'None'}');
          print('🎵 Spotify: ${response['spotify_connected'] ?? false}');
          print('🍎 Apple Music: ${response['apple_music_connected'] ?? false}');
          
          // Debug the music preference fields we're expecting
          print('🎤 Raw top_artists: ${response['top_artists']}');
          print('🎼 Raw top_genres: ${response['top_genres']}');
          print('🎯 Raw artist_genres: ${response['artist_genres']}');
          print('🖼️ Raw artist_image_urls: ${response['artist_image_urls']}');
          print('🎵 Raw artist_spotify_ids: ${response['artist_spotify_ids']}');
        }
        
        _currentUser = User.fromJson(response);
        
        // Also update header data from full profile
        _headerUser = User(
          id: _currentUser!.id,
          username: _currentUser!.username,
          email: '',
          profilePicUrl: _currentUser!.profilePicUrl,
          isVerified: false,
          favoriteGenres: [],
          connectedServices: {},
          createdAt: DateTime.now(),
        );
        
        // Cache both full and header data
        await _cacheFullProfile();
        await _cacheHeaderData();
        
        return true;
      } else {
        throw Exception('Invalid profile response format');
      }
    } catch (e) {
      _error = 'Failed to fetch profile: $e';
      if (kDebugMode) {
        print('❌ Error fetching full profile: $e');
      }
      
      // Try to load cached data on error
      await _loadCachedProfile();
      return false;
    } finally {
      _isLoadingProfile = false;
      notifyListeners();
    }
  }
  
  /// Update user profile
  Future<bool> updateProfile({
    String? username,
    String? bio,
    List<String>? favoriteGenres,
  }) async {
    if (_isUpdatingProfile) return false;
    
    _isUpdatingProfile = true;
    _error = null;
    notifyListeners();
    
    try {
      if (kDebugMode) {
        print('🔄 Updating user profile...');
        print('👤 Username: $username');
        print('📝 Bio: $bio');
        print('🎵 Genres: $favoriteGenres');
      }
      
      // Create request body with non-null fields
      final Map<String, dynamic> body = {};
      if (username != null) body['username'] = username;
      if (bio != null) body['bio'] = bio;
      if (favoriteGenres != null) body['favorite_genres'] = favoriteGenres;
      
      final response = await _apiClient.patch(
        AppConstants.userProfileEndpoint,
        body: body,
        requiresAuth: true,
      );
      
      if (response.containsKey('id')) {
        if (kDebugMode) {
          print('✅ Profile updated successfully');
        }
        
        _currentUser = User.fromJson(response);
        
        // Update header data too
        _headerUser = User(
          id: _currentUser!.id,
          username: _currentUser!.username,
          email: '',
          profilePicUrl: _currentUser!.profilePicUrl,
          isVerified: false,
          favoriteGenres: [],
          connectedServices: {},
          createdAt: DateTime.now(),
        );
        
        // Cache updated data
        await _cacheFullProfile();
        await _cacheHeaderData();
        
        return true;
      } else {
        _error = response['message'] ?? 'Profile update failed';
        return false;
      }
    } catch (e) {
      _error = 'Profile update error: $e';
      if (kDebugMode) {
        print('❌ Profile update error: $e');
      }
      return false;
    } finally {
      _isUpdatingProfile = false;
      notifyListeners();
    }
  }
  
  /// Update username using dedicated endpoint
  Future<bool> updateUsername(String username) async {
    if (_isUpdatingProfile) return false;
    
    _isUpdatingProfile = true;
    _error = null;
    notifyListeners();
    
    try {
      if (kDebugMode) {
        print('🔄 Updating username to: $username');
      }
      
      final response = await _apiClient.patch(
        '/users/update_username/',
        body: {'username': username},
        requiresAuth: true,
      );
      
      if (response.containsKey('success') && response['success'] == true) {
        if (kDebugMode) {
          print('✅ Username updated successfully: ${response['username']}');
        }
        
        // Update both user objects with new username
        if (_currentUser != null) {
          _currentUser = _currentUser!.copyWith(username: response['username']);
        }
        if (_headerUser != null) {
          _headerUser = _headerUser!.copyWith(username: response['username']);
        }
        
        // Cache updated data
        await _cacheFullProfile();
        await _cacheHeaderData();
        
        return true;
      } else {
        _error = response['message'] ?? 'Username update failed';
        if (kDebugMode) {
          print('❌ Username update failed: $_error');
        }
        return false;
      }
    } catch (e) {
      _error = 'Username update error: $e';
      if (kDebugMode) {
        print('❌ Username update error: $e');
      }
      return false;
    } finally {
      _isUpdatingProfile = false;
      notifyListeners();
    }
  }
  
  /// Update bio using dedicated endpoint
  Future<bool> updateBio(String bio) async {
    if (_isUpdatingProfile) return false;
    
    _isUpdatingProfile = true;
    _error = null;
    notifyListeners();
    
    try {
      if (kDebugMode) {
        print('🔄 Updating bio to: $bio');
      }
      
      final response = await _apiClient.patch(
        '/users/update_bio/',
        body: {'bio': bio},
        requiresAuth: true,
      );
      
      if (response.containsKey('success') && response['success'] == true) {
        if (kDebugMode) {
          print('✅ Bio updated successfully: ${response['bio']}');
        }
        
        // Update both user objects with new bio
        if (_currentUser != null) {
          _currentUser = _currentUser!.copyWith(bio: response['bio']);
        }
        // Note: Header user doesn't need bio since it's lightweight
        
        // Cache updated data
        await _cacheFullProfile();
        
        return true;
      } else {
        _error = response['message'] ?? 'Bio update failed';
        if (kDebugMode) {
          print('❌ Bio update failed: $_error');
        }
        return false;
      }
    } catch (e) {
      _error = 'Bio update error: $e';
      if (kDebugMode) {
        print('❌ Bio update error: $e');
      }
      return false;
    } finally {
      _isUpdatingProfile = false;
      notifyListeners();
    }
  }
  
  /// Cache header data locally
  Future<void> _cacheHeaderData() async {
    if (_headerUser == null) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final headerData = {
        'id': _headerUser!.id,
        'username': _headerUser!.username,
        'profile_pic': _headerUser!.profilePicUrl,
        'cached_at': DateTime.now().toIso8601String(),
      };
      
      await prefs.setString('user_header_cache', json.encode(headerData));
      
      if (kDebugMode) {
        print('💾 Header data cached successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error caching header data: $e');
      }
    }
  }
  
  /// Load cached header data
  Future<void> _loadCachedHeaderData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString('user_header_cache');
      
      if (cachedData != null) {
        final data = json.decode(cachedData);
        
        // Check if cache is still valid (24 hours)
        final cachedAt = DateTime.parse(data['cached_at']);
        final isExpired = DateTime.now().difference(cachedAt).inHours > 24;
        
        if (!isExpired) {
          _headerUser = User(
            id: data['id'] ?? 0,
            username: data['username'] ?? '',
            email: '',
            profilePicUrl: data['profile_pic'],
            isVerified: false,
            favoriteGenres: [],
            connectedServices: {},
            createdAt: DateTime.now(),
          );
          
          if (kDebugMode) {
            print('💾 Loaded cached header data: ${_headerUser!.username}');
          }
        } else {
          if (kDebugMode) {
            print('⏰ Cached header data expired, will refetch');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading cached header data: $e');
      }
    }
  }
  
  /// Cache full profile data
  Future<void> _cacheFullProfile() async {
    if (_currentUser == null) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileData = _currentUser!.toJson();
      profileData['cached_at'] = DateTime.now().toIso8601String();
      
      await prefs.setString('user_profile_cache', json.encode(profileData));
      
      if (kDebugMode) {
        print('💾 Full profile cached successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error caching full profile: $e');
      }
    }
  }
  
  /// Load cached full profile
  Future<void> _loadCachedProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString('user_profile_cache');
      
      if (cachedData != null) {
        final data = json.decode(cachedData);
        
        // Check if cache is still valid (1 hour for full profile)
        final cachedAt = DateTime.parse(data['cached_at']);
        final isExpired = DateTime.now().difference(cachedAt).inHours > 1;
        
        if (!isExpired) {
          _currentUser = User.fromJson(data);
          
          if (kDebugMode) {
            print('💾 Loaded cached full profile: ${_currentUser!.username}');
          }
        } else {
          if (kDebugMode) {
            print('⏰ Cached profile data expired, will refetch');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading cached profile: $e');
      }
    }
  }
  
  /// Initialize provider - load cached data first, then fetch fresh data
  Future<void> initialize() async {
    if (kDebugMode) {
      print('🚀 Initializing UserProvider...');
    }
    
    // Load cached data first for immediate display
    await _loadCachedHeaderData();
    await _loadCachedProfile();
    notifyListeners();
    
    // Then fetch fresh data in background
    await Future.wait([
      fetchHeaderData(),
      fetchFullProfile(),
    ]);
  }
  
  /// Clear all user data (for logout)
  Future<void> clearUserData() async {
    _currentUser = null;
    _headerUser = null;
    _error = null;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_header_cache');
      await prefs.remove('user_profile_cache');
      
      if (kDebugMode) {
        print('🧹 User data cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing user data: $e');
      }
    }
    
    notifyListeners();
  }
  
  /// Refresh all data (pull-to-refresh)
  Future<void> refreshAll() async {
    if (kDebugMode) {
      print('🔄 Refreshing all user data...');
    }
    
    // Fetch both header and full profile data
    await Future.wait([
      fetchHeaderData(),
      fetchFullProfile(),
    ]);
  }
  
  /// Check if username is available (for real-time validation)
  Future<bool> checkUsernameAvailability(String username) async {
    try {
      if (kDebugMode) {
        print('🔍 Checking username availability: $username');
      }
      
      // We can use the update endpoint to check availability
      // If it succeeds, the username is available
      // If it fails with "already taken", it's not available
      final response = await _apiClient.patch(
        '/users/update_username/',
        body: {'username': username},
        requiresAuth: true,
      );
      
      if (response.containsKey('success') && response['success'] == true) {
        if (kDebugMode) {
          print('✅ Username available: $username');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ Username not available: ${response['message']}');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking username availability: $e');
      }
      // If error contains "already taken", username is not available
      if (e.toString().contains('already taken') || 
          e.toString().contains('already exists') ||
          e.toString().contains('duplicate')) {
        return false;
      }
      // For other errors, assume available to not block user
      return true;
    }
  }
  
  /// Update profile picture using CloudinaryService
  Future<bool> updateProfilePicture(String imagePath) async {
    if (_isUpdatingProfile) return false;
    
    _isUpdatingProfile = true;
    _error = null;
    notifyListeners();
    
    try {
      if (kDebugMode) {
        print('🔄 Uploading profile picture to Cloudinary: $imagePath');
      }
      
      // Initialize CloudinaryService
      final cloudinaryService = CloudinaryService();
      
      // Upload image to Cloudinary
      final uploadResult = await cloudinaryService.uploadImage(
        file: File(imagePath),
        folder: 'profiles',
        tags: ['profile_pic', 'user_upload'],
        quality: 85,
        transformation: 'c_fill,g_face,w_400,h_400', // Optimize for profile pictures
      );
      
      if (kDebugMode) {
        print('✅ Image uploaded to Cloudinary: ${uploadResult.secureUrl}');
      }
      
      // Send the Cloudinary URL to backend
      final response = await _apiClient.patch(
        '/users/update_profile_picture/',
        body: {
          'profile_pic': uploadResult.secureUrl,
        },
        requiresAuth: true,
      );
      
      if (response.containsKey('success') && response['success'] == true) {
        final newProfilePicUrl = response['profile_pic_url'] ?? uploadResult.secureUrl;
        
        if (kDebugMode) {
          print('✅ Profile picture updated successfully: $newProfilePicUrl');
        }
        
        // Clear cached network image for the old URL to ensure new image loads
        try {
          if (_currentUser?.profilePicUrl != null) {
            final oldImageProvider = CachedNetworkImageProvider(_currentUser!.profilePicUrl!);
            await oldImageProvider.evict();
          }
          if (_headerUser?.profilePicUrl != null && _headerUser!.profilePicUrl != _currentUser?.profilePicUrl) {
            final oldHeaderImageProvider = CachedNetworkImageProvider(_headerUser!.profilePicUrl!);
            await oldHeaderImageProvider.evict();
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Could not clear old image cache: $e');
          }
        }
        
        // Update both user objects with new profile picture URL
        if (_currentUser != null) {
          _currentUser = _currentUser!.copyWith(profilePicUrl: newProfilePicUrl);
        }
        if (_headerUser != null) {
          _headerUser = _headerUser!.copyWith(profilePicUrl: newProfilePicUrl);
        }
        
        // Cache updated data
        await _cacheFullProfile();
        await _cacheHeaderData();
        
        return true;
      } else {
        _error = response['message'] ?? 'Profile picture update failed';
        if (kDebugMode) {
          print('❌ Profile picture update failed: $_error');
        }
        return false;
      }
    } catch (e) {
      _error = 'Profile picture update error: $e';
      if (kDebugMode) {
        print('❌ Profile picture update error: $e');
      }
      return false;
    } finally {
      _isUpdatingProfile = false;
      notifyListeners();
    }
  }
  
  /// Clear profile picture cache for immediate refresh
  Future<void> clearProfilePictureCache() async {
    try {
      // Clear cache for current user profile picture
      if (_currentUser?.profilePicUrl != null) {
        final imageProvider = CachedNetworkImageProvider(_currentUser!.profilePicUrl!);
        await imageProvider.evict();
        if (kDebugMode) {
          print('🧹 Cleared cache for current user profile picture');
        }
      }
      
      // Clear cache for header user profile picture
      if (_headerUser?.profilePicUrl != null && _headerUser!.profilePicUrl != _currentUser?.profilePicUrl) {
        final headerImageProvider = CachedNetworkImageProvider(_headerUser!.profilePicUrl!);
        await headerImageProvider.evict();
        if (kDebugMode) {
          print('🧹 Cleared cache for header user profile picture');
        }
      }
      
      // Notify listeners to rebuild UI with fresh cache
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing profile picture cache: $e');
      }
    }
  }

  /// Update user's music preferences (artists and genres)
  Future<bool> updateMusicPreferences({
    List<String>? topArtists,
    List<String>? topGenres,
    Map<String, List<String>>? artistGenres,
    Map<String, String>? artistImageUrls,
    Map<String, String>? artistSpotifyIds,
  }) async {
    if (_isUpdatingProfile) return false;
    
    _isUpdatingProfile = true;
    _error = null;
    notifyListeners();
    
    try {
      if (kDebugMode) {
        print('🎵 Updating user music preferences...');
        print('🎤 Top artists: $topArtists');
        print('🎼 Top genres: $topGenres');
        if (artistGenres != null && artistGenres.isNotEmpty) {
          print('🎯 Artist genres: $artistGenres');
        }
        if (artistImageUrls != null && artistImageUrls.isNotEmpty) {
          print('🖼️ Artist image URLs: $artistImageUrls');
        }
        if (artistSpotifyIds != null && artistSpotifyIds.isNotEmpty) {
          print('🎵 Artist Spotify IDs: $artistSpotifyIds');
        }
      }
      
      // Validate input
      if ((topArtists == null || topArtists.isEmpty) && 
          (topGenres == null || topGenres.isEmpty)) {
        _error = 'At least one artist or genre must be provided';
        return false;
      }
      
      // Create request body with non-null fields
      final Map<String, dynamic> body = {};
      if (topArtists != null && topArtists.isNotEmpty) {
        body['top_artists'] = topArtists;
      }
      if (topGenres != null && topGenres.isNotEmpty) {
        body['top_genres'] = topGenres;
      }
      if (artistGenres != null && artistGenres.isNotEmpty) {
        body['artist_genres'] = artistGenres;
      }
      if (artistImageUrls != null && artistImageUrls.isNotEmpty) {
        body['artist_image_urls'] = artistImageUrls;
      }
      if (artistSpotifyIds != null && artistSpotifyIds.isNotEmpty) {
        body['artist_spotify_ids'] = artistSpotifyIds;
      }
      
      final response = await _apiClient.patch(
        AppConstants.musicPreferencesEndpoint,
        body: body,
        requiresAuth: true,
      );
      
      if (response['error'] == true || response['success'] == false) {
        throw Exception(response['message'] ?? 'Failed to save music preferences');
      }
      
      if (kDebugMode) {
        print('✅ Music preferences updated successfully');
        print('🎵 Response: $response');
      }
      
      // Update local user data if the response contains updated user info
      if (response.containsKey('user') && response['user'] != null) {
        _currentUser = User.fromJson(response['user']);
        
        // Also update header data
        _headerUser = User(
          id: _currentUser!.id,
          username: _currentUser!.username,
          email: '',
          profilePicUrl: _currentUser!.profilePicUrl,
          isVerified: false,
          favoriteGenres: [],
          connectedServices: {},
          createdAt: DateTime.now(),
        );
        
        // Cache updated data
        await _cacheFullProfile();
        await _cacheHeaderData();
      } else {
        // If no user data in response, update current user with new preferences
        if (_currentUser != null) {
          // Create updated user with new preferences
          final updatedUser = User(
            id: _currentUser!.id,
            username: _currentUser!.username,
            email: _currentUser!.email,
            profilePicUrl: _currentUser!.profilePicUrl,
            bio: _currentUser!.bio,
            isVerified: _currentUser!.isVerified,
            favoriteGenres: topGenres ?? _currentUser!.favoriteGenres,
            connectedServices: _currentUser!.connectedServices,
            createdAt: _currentUser!.createdAt,
            topArtists: topArtists,
            topGenres: topGenres,
          );
          
          _currentUser = updatedUser;
          
          // Cache updated data
          await _cacheFullProfile();
          await _cacheHeaderData();
          
          if (kDebugMode) {
            print('✅ Updated local user data with new music preferences');
          }
        }
      }
      
      return true;
    } catch (e) {
      _error = 'Music preferences update error: $e';
      if (kDebugMode) {
        print('❌ Music preferences update error: $e');
      }
      return false;
    } finally {
      _isUpdatingProfile = false;
      notifyListeners();
    }
  }
} 