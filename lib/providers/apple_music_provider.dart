import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:music_kit/music_kit.dart';
import '../services/music/apple_music_player_service.dart';
import '../services/music/apple_music_service.dart';
import '../services/music/apple_music_auth_service.dart';
import '../services/music/apple_music_queue_manager.dart';
import '../models/music_track.dart';
import '../models/music/track.dart';
import '../utils/music_fallback_utils.dart';

/// AppleMusicProvider manages the state of Apple Music playback and authentication
/// It uses AppleMusicPlayerService to control playback and exposes streams and methods to the UI
class AppleMusicProvider with ChangeNotifier, WidgetsBindingObserver {
  final AppleMusicPlayerService _playerService = AppleMusicPlayerService();
  final AppleMusicService _appleMusicService;
  final AppleMusicAuthService _authService = AppleMusicAuthService();
  
  // State
  bool _isInitialized = false;
  bool _isConnected = false;
  bool _isConnecting = false;
  bool _isPlaying = false;
  MusicTrack? _currentTrack;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  bool _hasActivePlayback = false;
  
  // User data
  Map<String, dynamic>? _userProfile;
  bool _isLoadingProfile = false;
  
  // Error handling
  String? _errorMessage;
  bool _hasActiveSubscription = false;
  
  // Liked songs and playlists
  List<MusicTrack> _likedSongs = [];
  List<Map<String, dynamic>> _playlists = [];
  bool _isLoadingLikedSongs = false;
  bool _isLoadingPlaylists = false;
  
  // Recent tracks
  List<Track>? _recentTracks;
  
  // Additional data for Apple Music
  List<Map<String, dynamic>> _heavyRotation = [];
  List<MusicTrack> _topTracks = [];
  List<MusicTrack> _recommendations = [];
  List<Map<String, dynamic>> _recentlyAdded = [];
  
  // Additional loading states
  bool _isLoadingHeavyRotation = false;
  bool _isLoadingTopTracks = false;
  bool _isLoadingRecommendations = false;
  bool _isLoadingRecentlyAdded = false;
  
  // Stream subscriptions
  StreamSubscription<bool>? _isPlayingSubscription;
  StreamSubscription<MusicTrack?>? _currentTrackSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration>? _durationSubscription;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isConnected => _isConnected;
  bool get isConnecting => _isConnecting;
  bool get isPlaying => _isPlaying;
  bool get hasActivePlayback => _hasActivePlayback;
  MusicTrack? get currentTrack => _currentTrack;
  Duration get position => _position;
  Duration get duration => _duration;
  String? get errorMessage => _errorMessage;
  bool get hasActiveSubscription => _hasActiveSubscription;
  List<MusicTrack> get likedSongs => _likedSongs;
  List<Map<String, dynamic>> get playlists => _playlists;
  bool get isLoadingLikedSongs => _isLoadingLikedSongs;
  bool get isLoadingPlaylists => _isLoadingPlaylists;
  Map<String, dynamic>? get userProfile => _userProfile;
  bool get isLoadingProfile => _isLoadingProfile;
  List<Track>? get recentTracks => _recentTracks;
  
  // Additional getters
  List<Map<String, dynamic>> get heavyRotation => _heavyRotation;
  List<MusicTrack> get topTracks => _topTracks;
  List<MusicTrack> get recommendations => _recommendations;
  List<Map<String, dynamic>> get recentlyAdded => _recentlyAdded;
  bool get isLoadingHeavyRotation => _isLoadingHeavyRotation;
  bool get isLoadingTopTracks => _isLoadingTopTracks;
  bool get isLoadingRecommendations => _isLoadingRecommendations;
  bool get isLoadingRecentlyAdded => _isLoadingRecentlyAdded;
  
  // Queue manager getter
  AppleMusicQueueManager get queueManager => _playerService.queueManager;
  
  AppleMusicProvider(this._appleMusicService) {
    WidgetsBinding.instance.addObserver(this);
    _init();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (kDebugMode) {
      print('🍎 Apple Music lifecycle state changed: $state');
    }
    
    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResume();
        break;
      case AppLifecycleState.paused:
        // Save the current state when app is paused
        break;
      default:
        break;
    }
  }
  
  /// Handle app resume
  Future<void> _handleAppResume() async {
    if (kDebugMode) {
      print('🍎 Handling Apple Music app resume');
    }
    
    try {
      // Check authentication status
      if (_authService.isAuthenticated) {
        await refreshUserData();
      }
    } catch (e) {
      _handleError('Error handling app resume: $e');
    }
  }
  
  /// Initialize the Apple Music provider
  Future<void> _init() async {
    try {
      if (kDebugMode) {
        print('🍎 Initializing Apple Music provider...');
      }
      
      // Initialize services
      await _authService.initialize();
      
      // Inject Apple Music service into player service BEFORE initializing
      _playerService.setAppleMusicService(_appleMusicService);
      
      await _playerService.initialize();

      // Initialize queue manager with Apple Music service for search functionality
      await _playerService.queueManager.initialize(appleMusicService: _appleMusicService);
      
      // Set up stream subscriptions
      _setupStreamSubscriptions();
      
      // Check if already authenticated
      if (_authService.isAuthenticated) {
        _isConnected = true;
        await loadUserData();
      }
      
      _isInitialized = true;
      notifyListeners();
      
      if (kDebugMode) {
        print('✅ Apple Music provider initialized. Connected: $_isConnected');
      }
    } catch (e) {
      _handleError('Error initializing Apple Music provider: $e');
    }
  }
  
  /// Set up stream subscriptions to player service
  void _setupStreamSubscriptions() {
    _isPlayingSubscription = _playerService.isPlayingStream.listen((isPlaying) {
      _isPlaying = isPlaying;
      _hasActivePlayback = isPlaying || _currentTrack != null;
      notifyListeners();
    });
    
    _currentTrackSubscription = _playerService.currentTrackStream.listen((track) {
      _currentTrack = track;
      _hasActivePlayback = _isPlaying || track != null;
      notifyListeners();
    });
    
    _positionSubscription = _playerService.positionStream.listen((position) {
      _position = position;
      notifyListeners();
    });
    
    _durationSubscription = _playerService.durationStream.listen((duration) {
      _duration = duration;
      notifyListeners();
    });
  }
  
  /// Connect to Apple Music (authenticate)
  Future<bool> connect() async {
    if (_isConnecting || _isConnected) return _isConnected;
    
    _isConnecting = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      if (kDebugMode) {
        print('🍎 Connecting to Apple Music...');
      }
      
      final success = await _authService.requestAuthorization();
      
      if (success) {
        _isConnected = true;
        _hasActiveSubscription = await _authService.hasActiveSubscription();
        await loadUserData();
        
        if (kDebugMode) {
          print('✅ Successfully connected to Apple Music');
        }
      } else {
        _handleError('Failed to connect to Apple Music');
      }
      
      return success;
    } catch (e) {
      _handleError('Error connecting to Apple Music: $e');
      return false;
    } finally {
      _isConnecting = false;
      notifyListeners();
    }
  }
  
  /// Disconnect from Apple Music
  Future<void> disconnect() async {
    try {
      if (kDebugMode) {
        print('🍎 Disconnecting from Apple Music...');
      }
      
      await _playerService.stop();
      await _authService.signOut();
      
      _isConnected = false;
      _hasActiveSubscription = false;
      _currentTrack = null;
      _userProfile = null;
      _likedSongs.clear();
      _playlists.clear();
      _heavyRotation.clear();
      _topTracks.clear();
      _recommendations.clear();
      _recentlyAdded.clear();
      _recentTracks = null;
      _errorMessage = null;
      
      notifyListeners();
      
      if (kDebugMode) {
        print('✅ Disconnected from Apple Music');
      }
    } catch (e) {
      _handleError('Error disconnecting from Apple Music: $e');
    }
  }
  
  /// Play a track
  Future<bool> playTrack(MusicTrack track) async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }

      final success = await _playerService.playTrack(track);
      if (success) {
        // Even though streams handle state, an explicit notify can help with immediate UI refresh
        notifyListeners();
      }
      return success;
    } catch (e) {
      _handleError('Error playing track: $e');
      return false;
    }
  }

  /// Play a track by searching for it first, with user-controlled fallback for no exact match
  /// This method shows a snackbar when no exact match is found instead of automatic fallback
  Future<bool> playTrackBySearchWithFallback(MusicTrack track, BuildContext context) async {
    if (!context.mounted) return false;

    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }

      await _playerService.playTrackBySearch(track);
      notifyListeners();
      return true;
    } catch (e) {
      if (e is NoExactMatchException) {
        // Show snackbar for no exact match instead of automatic fallback
        if (context.mounted) {
          MusicFallbackUtils.showNoExactMatchSnackbar(context, track);
        }
        return false;
      } else {
        // Handle other errors normally
        _handleError('Error playing track: $e');
        return false;
      }
    }
  }
  
  /// Play a collection of tracks starting from a specific track
  Future<bool> playCollection({
    required List<MusicTrack> tracks,
    required String collectionType,
    int startIndex = 0,
    Map<String, dynamic>? collectionMetadata,
  }) async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }
      
      if (tracks.isEmpty) {
        _handleError('No tracks provided for collection');
        return false;
      }
      
      // Ensure startIndex is valid
      if (startIndex < 0 || startIndex >= tracks.length) {
        if (kDebugMode) {
          print('⚠️ [AppleMusicProvider] Invalid startIndex $startIndex for ${tracks.length} tracks, defaulting to 0');
        }
        startIndex = 0;
      }
      
      if (kDebugMode) {
        print('🍎 [AppleMusicProvider] Playing collection: $collectionType with ${tracks.length} tracks, starting at index $startIndex');
        print('🍎 [AppleMusicProvider] Starting track: ${tracks[startIndex].title} by ${tracks[startIndex].artist}');
      }
      
      final success = await _playerService.playCollection(
        tracks: tracks,
        collectionType: collectionType,
        startIndex: startIndex,
        collectionMetadata: collectionMetadata,
      );
      
      if (success) {
        notifyListeners();
      }
      return success;
    } catch (e) {
      _handleError('Error playing collection: $e');
      return false;
    }
  }
  
  /// Play a playlist starting from a specific track
  Future<bool> playPlaylist({
    required String playlistId,
    int startIndex = 0,
  }) async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }

      // Get playlist tracks
      final tracks = await getPlaylistTracks(playlistId);
      
      if (tracks.isEmpty) {
        _handleError('Playlist is empty or could not be loaded');
        return false;
      }

      return await _playerService.playPlaylist(
        playlistId: playlistId,
        tracks: tracks,
        startIndex: startIndex,
      );
    } catch (e) {
      _handleError('Error playing playlist: $e');
      return false;
    }
  }
  
  /// Play an album starting from a specific track
  Future<bool> playAlbum({
    required String albumId,
    int startIndex = 0,
  }) async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }

      // Get album tracks
      final tracks = await getAlbumTracks(albumId);
      
      if (tracks.isEmpty) {
        _handleError('Album is empty or could not be loaded');
        return false;
      }

      return await _playerService.playAlbum(
        albumId: albumId,
        tracks: tracks,
        startIndex: startIndex,
      );
    } catch (e) {
      _handleError('Error playing album: $e');
      return false;
    }
  }
  
  /// Play recommendations starting from a specific track
  Future<bool> playRecommendations({
    required List<MusicTrack> tracks,
    int startIndex = 0,
  }) async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }

      if (tracks.isEmpty) {
        _handleError('No recommendations available');
        return false;
      }

      return await _playerService.playRecommendations(
        tracks: tracks,
        startIndex: startIndex,
      );
    } catch (e) {
      _handleError('Error playing recommendations: $e');
      return false;
    }
  }
  
  /// Play tracks from liked songs starting from a specific track
  Future<bool> playLikedSongs({int startIndex = 0}) async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }

      if (_likedSongs.isEmpty) {
        await loadLikedSongs();
      }

      if (_likedSongs.isEmpty) {
        _handleError('No liked songs available');
        return false;
      }

      return await playCollection(
        tracks: _likedSongs,
        collectionType: 'liked_songs',
        startIndex: startIndex,
      );
    } catch (e) {
      _handleError('Error playing liked songs: $e');
      return false;
    }
  }
  
  /// Play/pause toggle
  Future<bool> togglePlayPause() async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }
      
      if (kDebugMode) {
        print('🍎 [AppleMusicProvider] togglePlayPause called. Current state: isPlaying=$_isPlaying, hasActivePlayback=$_hasActivePlayback');
      }
      
      bool success;
      if (_isPlaying) {
        if (kDebugMode) {
          print('🍎 [AppleMusicProvider] Attempting to pause...');
        }
        success = await _playerService.pause();
      } else {
        if (kDebugMode) {
          print('🍎 [AppleMusicProvider] Attempting to play...');
        }
        success = await _playerService.play();
      }
      
      if (kDebugMode) {
        print('🍎 [AppleMusicProvider] togglePlayPause completed. Success: $success, New state should be: ${!_isPlaying}');
      }
      
      return success;
    } catch (e) {
      _handleError('Error toggling play/pause: $e');
      return false;
    }
  }
  
    /// Skip to the next item in the queue
  Future<bool> skipToNextItem() async {
    try {
      if (kDebugMode) {
        print('🍎 [AppleMusicProvider] Skip to next called. Connected: $_isConnected');
      }
      
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }
      
      final success = await _playerService.skipToNext();
      if (kDebugMode) {
        print('🍎 [AppleMusicProvider] Skip to next result: $success');
      }
      
      if (success) {
        notifyListeners(); // Notify UI of potential state changes
      }
      
      return success;
    } catch (e) {
      _handleError('Error skipping to next item: $e');
      return false;
    }
  }
  
  /// Skip to the previous item in the queue
  Future<bool> skipToPreviousItem() async {
    try {
      if (kDebugMode) {
        print('🍎 [AppleMusicProvider] Skip to previous called. Connected: $_isConnected');
      }
      
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }
      
      final success = await _playerService.skipToPrevious();
      if (kDebugMode) {
        print('🍎 [AppleMusicProvider] Skip to previous result: $success');
      }
      
      if (success) {
        notifyListeners(); // Notify UI of potential state changes
      }
      
      return success;
    } catch (e) {
      _handleError('Error skipping to previous item: $e');
      return false;
    }
  }
  
  /// Toggle shuffle mode
  Future<bool> toggleShuffle() async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }
      return await _playerService.toggleShuffle();
    } catch (e) {
      _handleError('Error toggling shuffle: $e');
      return false;
    }
  }
  
  /// Toggle repeat mode
  Future<bool> toggleRepeatMode() async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return false;
      }
      return await _playerService.toggleRepeatMode();
    } catch (e) {
      _handleError('Error toggling repeat mode: $e');
      return false;
    }
  }
  
  /// Get shuffle state
  bool get isShuffleEnabled => _playerService.isShuffleEnabled;
  
  /// Get repeat state  
  String get repeatMode {
    switch (_playerService.repeatMode) {
      case RepeatMode.one:
        return 'one';
      case RepeatMode.all:
        return 'all';
      case RepeatMode.off:
        return 'off';
    }
  }
  
  /// Check if there's a next track
  bool get hasNext => _playerService.hasNext;
  
  /// Check if there's a previous track
  bool get hasPrevious => _playerService.hasPrevious;
  
  /// Get current queue
  List<MusicTrack> get currentQueue => _playerService.queue;
  
  /// Get current queue index
  int get currentQueueIndex => _playerService.currentIndex;
  
  /// Stop playback
  Future<bool> stop() async {
    try {
      return await _playerService.stop();
    } catch (e) {
      _handleError('Error stopping playback: $e');
      return false;
    }
  }
  
  /// Seek to position
  Future<bool> seek(Duration position) async {
    try {
      return await _playerService.seek(position);
    } catch (e) {
      _handleError('Error seeking: $e');
      return false;
    }
  }
  
  /// Search for tracks
  Future<List<MusicTrack>> searchTracks(String query) async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return [];
      }
      
      return await _appleMusicService.searchTracks(query);
    } catch (e) {
      _handleError('Error searching tracks: $e');
      return [];
    }
  }
  
  /// Search for a single track by artist and title (more reliable for specific matches)
  Future<MusicTrack?> searchForTrack(String artist, String title) async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return null;
      }
      return await _appleMusicService.searchTrackByArtistAndTitle(artist, title);
    } catch (e) {
      _handleError('Error searching for a track: $e');
      return null;
    }
  }
  
  /// Load user data (profile, liked songs, playlists)
  Future<void> loadUserData() async {
    if (!_isConnected) return;
    
    try {
      await Future.wait([
        loadUserProfile(),
        loadLikedSongs(),
        loadPlaylists(),
      ]);
    } catch (e) {
      _handleError('Error loading user data: $e');
    }
  }
  
  /// Load user profile
  Future<void> loadUserProfile() async {
    _isLoadingProfile = true;
    notifyListeners();
    
    try {
      // Create a basic profile since Apple Music doesn't provide detailed user info
      _userProfile = {
        'display_name': 'Apple Music User',
        'id': _authService.currentUserId ?? 'unknown',
        'has_subscription': _hasActiveSubscription,
        'service': 'apple_music',
      };
      
      if (kDebugMode) {
        print('✅ Loaded Apple Music user profile');
      }
    } catch (e) {
      _handleError('Error loading user profile: $e');
    } finally {
      _isLoadingProfile = false;
      notifyListeners();
    }
  }
  
  /// Load liked songs
  Future<void> loadLikedSongs() async {
    _isLoadingLikedSongs = true;
    notifyListeners();
    
    try {
      _likedSongs = await _appleMusicService.getSavedTracks();
      
      if (kDebugMode) {
        print('✅ Loaded ${_likedSongs.length} Apple Music liked songs');
      }
    } catch (e) {
      _handleError('Error loading liked songs: $e');
    } finally {
      _isLoadingLikedSongs = false;
      notifyListeners();
    }
  }
  
  /// Load playlists
  Future<void> loadPlaylists() async {
    _isLoadingPlaylists = true;
    notifyListeners();
    
    try {
      final playlistsData = await _appleMusicService.getPlaylists();
      _playlists = playlistsData.map((item) => Map<String, dynamic>.from(item)).toList();
      
      if (kDebugMode) {
        print('✅ Loaded ${_playlists.length} Apple Music playlists');
      }
    } catch (e) {
      _handleError('Error loading playlists: $e');
    } finally {
      _isLoadingPlaylists = false;
      notifyListeners();
    }
  }
  
  /// Get tracks for a specific playlist
  Future<List<MusicTrack>> getPlaylistTracks(String playlistId, {int limit = 50, int offset = 0}) async {
    if (!_isConnected) {
      _handleError('Not connected to Apple Music');
      return [];
    }
    try {
      final Map<String, dynamic> playlistData = await _appleMusicService.getPlaylistTracks(
        playlistId,
        limit: limit,
        offset: offset,
      );
      // Ensure that 'tracks' key exists and is of the correct type
      if (playlistData.containsKey('tracks') && playlistData['tracks'] is List) {
        // Cast the list to List<MusicTrack>
        return (playlistData['tracks'] as List).cast<MusicTrack>();
      }
      _handleError('Failed to parse playlist tracks from service');
      return [];
    } catch (e) {
      _handleError('Error fetching playlist tracks: $e');
      return [];
    }
  }
 
  /// Get playlist metadata including total track count
  Future<Map<String, dynamic>?> getPlaylistMetadata(String playlistId) async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return null;
      }
      
      return await _appleMusicService.getPlaylistMetadata(playlistId);
    } catch (e) {
      _handleError('Error loading playlist metadata: $e');
      return null;
    }
  }

  /// Get tracks for a specific album
  Future<List<MusicTrack>> getAlbumTracks(String albumId, {int limit = 50, int offset = 0}) async {
    if (!_isConnected) {
      _handleError('Not connected to Apple Music');
      return [];
    }
    try {
      final Map<String, dynamic> albumData = await _appleMusicService.getAlbumTracks(
        albumId,
        limit: limit,
        offset: offset,
      );
      // Ensure that 'tracks' key exists and is of the correct type
      if (albumData.containsKey('tracks') && albumData['tracks'] is List) {
        // Cast the list to List<MusicTrack>
        return (albumData['tracks'] as List).cast<MusicTrack>();
      }
      _handleError('Failed to parse album tracks from service');
      return [];
    } catch (e) {
      _handleError('Error fetching album tracks: $e');
      return [];
    }
  }
 
  /// Get recently added tracks
  Future<List<Map<String, dynamic>>> getRecentlyAdded() async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return [];
      }
      
      return await _appleMusicService.getRecentlyAdded();
    } catch (e) {
      _handleError('Error loading recently added items: $e');
      return [];
    }
  }
  
  /// Load heavy rotation tracks
  Future<void> loadHeavyRotation() async {
    _isLoadingHeavyRotation = true;
    notifyListeners();
    
    try {
      _heavyRotation = await _appleMusicService.getHeavyRotation();
      
      if (kDebugMode) {
        print('✅ Loaded ${_heavyRotation.length} Apple Music heavy rotation items');
      }
    } catch (e) {
      _handleError('Error loading heavy rotation: $e');
    } finally {
      _isLoadingHeavyRotation = false;
      notifyListeners();
    }
  }

  /// Load top tracks
  Future<void> loadTopTracks() async {
    _isLoadingTopTracks = true;
    notifyListeners();
    
    try {
      _topTracks = await _appleMusicService.getTopTracks();
      
      if (kDebugMode) {
        print('✅ Loaded ${_topTracks.length} Apple Music top tracks');
      }
    } catch (e) {
      _handleError('Error loading top tracks: $e');
    } finally {
      _isLoadingTopTracks = false;
      notifyListeners();
    }
  }

  /// Load recommendations
  Future<void> loadRecommendations() async {
    _isLoadingRecommendations = true;
    notifyListeners();
    
    try {
      _recommendations = await _appleMusicService.getRecommendations();
      
      if (kDebugMode) {
        print('✅ Loaded ${_recommendations.length} Apple Music recommendations');
      }
    } catch (e) {
      _handleError('Error loading recommendations: $e');
    } finally {
      _isLoadingRecommendations = false;
      notifyListeners();
    }
  }

  /// Load recently added items
  Future<void> loadRecentlyAdded() async {
    _isLoadingRecentlyAdded = true;
    notifyListeners();
    
    try {
      _recentlyAdded = await _appleMusicService.getRecentlyAdded();
      
      if (kDebugMode) {
        print('✅ Loaded ${_recentlyAdded.length} Apple Music recently added items');
      }
    } catch (e) {
      _handleError('Error loading recently added: $e');
    } finally {
      _isLoadingRecentlyAdded = false;
      notifyListeners();
    }
  }

  /// Get recently played tracks
  Future<List<MusicTrack>> getRecentlyPlayed() async {
    try {
      if (!_isConnected) {
        _handleError('Not connected to Apple Music');
        return [];
      }
      
      return await _appleMusicService.getRecentlyPlayed();
    } catch (e) {
      _handleError('Error loading recently played tracks: $e');
      return [];
    }
  }
  
  /// Refresh user data
  Future<void> refreshUserData() async {
    if (!_isConnected) return;
    
    try {
      await loadUserData();
    } catch (e) {
      _handleError('Error refreshing user data: $e');
    }
  }
  
  /// Handle errors
  void _handleError(String error) {
    _errorMessage = error;
    
    if (kDebugMode) {
      print('❌ Apple Music Provider Error: $error');
    }
    
    // Check if error is related to authentication
    if (error.toLowerCase().contains('token') || 
        error.toLowerCase().contains('auth') ||
        error.toLowerCase().contains('unauthorized')) {
      _isConnected = false;
    }
    
    notifyListeners();
  }
  
  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  /// Play a track from pin data (cross-platform compatibility)
  Future<bool> playTrackFromPin(Map<String, dynamic> pinData) async {
    try {
      if (kDebugMode) {
        print('🍎 [AppleMusicProvider] Attempting to play track from pin data');
      }

      // Check connection status first
      if (!_isConnected) {
        if (kDebugMode) {
          print('⚠️ [AppleMusicProvider] Not connected, attempting to reconnect...');
        }
        final connected = await connect();
        if (!connected) {
          if (kDebugMode) {
            print('❌ [AppleMusicProvider] Failed to connect to Apple Music, trying Spotify fallback...');
          }
          return await _trySpotifyFallback(pinData, 'Apple Music connection failed');
        }
      }

      // Check if user has active subscription
      if (!await MusicKit().isPreparedToPlay) {
        if (kDebugMode) {
          print('⚠️ [AppleMusicProvider] No active Apple Music subscription, trying Spotify fallback...');
        }
        return await _trySpotifyFallback(pinData, 'Apple Music subscription required');
      }

      // Extract track information from pin data
      final title = pinData['title'] ?? pinData['track_title'] ?? pinData['name'];
      final artist = pinData['artist'] ?? pinData['track_artist'];
      final appleMusicUri = pinData['uri'];
      final appleMusicUrl = pinData['url'];

      if (title == null || title.toString().isEmpty) {
        if (kDebugMode) {
          print('❌ [AppleMusicProvider] No track title found in pin data');
        }
        _handleError('Track information is incomplete');
        return false;
      }

      if (artist == null || artist.toString().isEmpty) {
        if (kDebugMode) {
          print('❌ [AppleMusicProvider] No artist found in pin data');
        }
        _handleError('Artist information is missing');
        return false;
      }

      // If we have an Apple Music URI and this is the original Apple Music track, try to play it directly
      if (appleMusicUri != null && appleMusicUri.toString().startsWith('apple:')) {
        if (kDebugMode) {
          print('🍎 [AppleMusicProvider] Found Apple Music URI, attempting direct playback: $appleMusicUri');
        }

        // Create a MusicTrack object for the URI
        final track = MusicTrack(
          id: appleMusicUri.toString().split(':').last,
          title: title.toString(),
          artist: artist.toString(),
          album: pinData['album'] ?? '',
          albumArt: pinData['artwork_url'] ?? pinData['albumArt'] ?? pinData['album_art'] ?? '',
          uri: appleMusicUri.toString(),
          url: appleMusicUrl?.toString() ?? '',
          service: 'apple_music',
          serviceType: 'apple',
          genres: [],
          durationMs: 0,
          popularity: 0,
        );

        final success = await playTrack(track);
        if (success) {
          if (kDebugMode) {
            print('✅ [AppleMusicProvider] Successfully played track via direct URI');
          }
          return true;
        } else {
          if (kDebugMode) {
            print('⚠️ [AppleMusicProvider] Direct URI playback failed, falling back to search');
          }
        }
      }

      // Search for the track by artist and title with retry logic for auth errors
      if (kDebugMode) {
        print('🔍 [AppleMusicProvider] Searching for track: "$title" by "$artist"');
      }

      MusicTrack? foundTrack;
      try {
        foundTrack = await _appleMusicService.searchTrackByArtistAndTitle(
          artist.toString(),
          title.toString(),
        );
      } catch (e) {
        // If we get an auth error, try to reconnect and search again
        if (e.toString().toLowerCase().contains('unauthorized') ||
            e.toString().toLowerCase().contains('401') ||
            e.toString().toLowerCase().contains('authentication')) {
          
          if (kDebugMode) {
            print('🔄 [AppleMusicProvider] Authentication error during search, attempting to reconnect...');
          }
          
          final reconnected = await connect();
          if (reconnected) {
            if (kDebugMode) {
              print('✅ [AppleMusicProvider] Reconnected successfully, retrying search...');
            }
            try {
              foundTrack = await _appleMusicService.searchTrackByArtistAndTitle(
                artist.toString(),
                title.toString(),
              );
            } catch (retryError) {
              if (kDebugMode) {
                print('❌ [AppleMusicProvider] Search failed even after reconnection, trying Spotify fallback...');
              }
              return await _trySpotifyFallback(pinData, 'Apple Music authentication failed');
            }
          } else {
            if (kDebugMode) {
              print('❌ [AppleMusicProvider] Failed to reconnect to Apple Music, trying Spotify fallback...');
            }
            return await _trySpotifyFallback(pinData, 'Unable to authenticate with Apple Music');
          }
        } else {
          // Non-auth error, try Spotify fallback
          if (kDebugMode) {
            print('❌ [AppleMusicProvider] Search error, trying Spotify fallback: $e');
          }
          return await _trySpotifyFallback(pinData, 'Apple Music search failed');
        }
      }

      if (foundTrack == null) {
        if (kDebugMode) {
          print('❌ [AppleMusicProvider] Could not find track on Apple Music, trying Spotify fallback...');
        }
        return await _trySpotifyFallback(pinData, 'Track not available on Apple Music');
      }

      // Play the found track
      final success = await playTrack(foundTrack);
      if (success) {
        if (kDebugMode) {
          print('✅ [AppleMusicProvider] Successfully played track via search: "${foundTrack.title}" by "${foundTrack.artist}"');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ [AppleMusicProvider] Failed to play found track, trying Spotify fallback...');
        }
        return await _trySpotifyFallback(pinData, 'Apple Music playback failed');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ [AppleMusicProvider] Error playing track from pin, trying Spotify fallback: $e');
      }
      
      // Try Spotify fallback for any unexpected errors
      return await _trySpotifyFallback(pinData, 'Apple Music error: ${e.toString()}');
    }
  }
  
  /// Try to play the track using Spotify as a fallback
  Future<bool> _trySpotifyFallback(Map<String, dynamic> pinData, String appleMusicReason) async {
    try {
      if (kDebugMode) {
        print('🎵 [AppleMusicProvider] Attempting Spotify fallback - Reason: $appleMusicReason');
      }

      // Get Spotify provider from context (we need to find a way to access it)
      // For now, we'll use a static approach or dependency injection
      // Note: This is a limitation of the provider pattern - we need context to get other providers
      
      // Since we can't easily access context from here, we'll return false with a specific error
      // The calling widget should handle this by trying Spotify directly
      _handleError('FALLBACK_TO_SPOTIFY:$appleMusicReason');
      return false;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [AppleMusicProvider] Spotify fallback also failed: $e');
      }
      _handleError('Both Apple Music and Spotify failed');
      return false;
    }
  }
  
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _isPlayingSubscription?.cancel();
    _currentTrackSubscription?.cancel();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _playerService.dispose();
    super.dispose();
  }
} 