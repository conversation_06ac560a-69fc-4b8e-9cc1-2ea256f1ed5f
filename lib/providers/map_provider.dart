import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map/flutter_map.dart';

import '../config/constants.dart';
import '../models/pin.dart';
import '../services/api/pins_service.dart';
import '../services/location/location_service.dart';
import '../services/location/location_manager.dart';
import '../config/constants.dart';

class MapProvider with ChangeNotifier {
  final PinsService _pinsService = PinsService();
  final LocationManager _locationManager = LocationManager();
  
  // Map state
  LatLng _currentCenter = LatLng(
    AppConstants.defaultLatitude,
    AppConstants.defaultLongitude
  );
  double _currentZoom = AppConstants.defaultZoom;
  bool _isMapLoading = true;
  
  // Pins
  List<dynamic> _pins = [];  // Can be either Pin objects or Map<String, dynamic>
  bool _isPinsLoading = false;
  dynamic _selectedPin;
  final double _searchRadius = AppConstants.pinDiscoveryRadius;
  
  // Filter state
  PinFilterType _currentFilter = PinFilterType.all;
  
  // Error handling
  bool _hasNetworkError = false;
  String _errorMessage = '';
  
  // Flag for pin intent
  bool _hasAddPinIntent = false;
  
  // Flag for map reload needed
  bool _needsMapReload = false;
  
  // Flag to control whether mock/test pins should be generated
  bool _enableMockPins = false;
  
  // Subscription to location updates
  StreamSubscription<Position?>? _positionSubscription;
  StreamSubscription<LocationStatus>? _locationStatusSubscription;
  
  // Flag to track if subscriptions are active
  bool _areSubscriptionsActive = true;
  
  // Periodic refresh timer for when user is stationary
  Timer? _periodicRefreshTimer;
  static const Duration _periodicRefreshInterval = Duration(minutes: 2); // Refresh every 2 minutes
  
  // Variables for intent flag safeguards
  DateTime? _lastToggleTime;
  static const _minToggleInterval = Duration(milliseconds: 300);
  
  // Callback to trigger SmartLocationFetchManager force refresh
  Function()? _onForceRefreshRequested;
  
  // Callback to display optimistic pins immediately
  Function(Map<String, dynamic>)? _onOptimisticPinRequested;
  
  // Getters
  Position? get currentPosition => _locationManager.currentPosition;
  Stream<Position?> get positionStream => _locationManager.positionStream; // Expose LocationManager's position stream
  LatLng get currentCenter => _currentCenter;
  double get zoom => _currentZoom;
  bool get isMapLoading => _isMapLoading;
  bool get isLocationTracking => _locationManager.isTracking;
  List<dynamic> get pins => _pins;
  bool get isPinsLoading => _isPinsLoading;
  dynamic get selectedPin => _selectedPin;
  double get searchRadius => _searchRadius;
  bool get hasNetworkError => _hasNetworkError;
  String get errorMessage => _errorMessage;
  bool get isLoading => _isMapLoading || _isPinsLoading;
  LocationStatus get locationStatus => _locationManager.locationStatus;
  bool get hasAddPinIntent => _hasAddPinIntent;
  bool get needsMapReload => _needsMapReload;
  bool get enableMockPins => _enableMockPins;
  bool get isBackgroundLocationTracking => _locationManager.isBackgroundTracking;
  PinFilterType get currentFilter => _currentFilter;
  
  // Constructor
  MapProvider() {
    _initializeListeners();
    _startPeriodicRefresh();
  }
  
  // Set current filter
  void setCurrentFilter(PinFilterType filter) {
    if (_currentFilter != filter) {
      _currentFilter = filter;
      debugPrint('MapProvider: Filter changed to ${filter.toString().split('.').last}');
      notifyListeners();

      // Filter changes are now handled by SmartLocationFetchManager
      debugPrint('MapProvider: Filter change refresh disabled - handled by SmartLocationFetchManager');
    }
  }
  
  // Get filter name for display
  String getFilterName() {
    switch (_currentFilter) {
      case PinFilterType.all:
        return 'All';
      case PinFilterType.fresh:
        return 'Fresh';
      case PinFilterType.friends:
        return 'Friends';
    }
  }
  
  // Get filter name in lowercase for API calls
  String getFilterNameLowercase() {
    switch (_currentFilter) {
      case PinFilterType.all:
        return 'all';
      case PinFilterType.fresh:
        return 'fresh';
      case PinFilterType.friends:
        return 'friends';
    }
  }
  
  // Set add pin intent flag immediately without safeguards
  void setAddPinIntent(bool value) {
    // Set the flag immediately
    _hasAddPinIntent = value;
    debugPrint('Setting add pin intent: $value at ${DateTime.now().toString()}');
    notifyListeners();
  }
  
  // Force reset pin intent for cases where it might be stuck
  // This is a safety method to ensure the pin intent is always cleared
  // when it should be (e.g., when leaving the map screen)
  void forceResetPinIntent() {
    if (_hasAddPinIntent) {
      debugPrint('🧹 FORCE RESETTING pin intent from TRUE to FALSE');
      _hasAddPinIntent = false;
      notifyListeners();
      
      // Add a delayed redundant check to ensure it stays cleared
      Future.delayed(const Duration(milliseconds: 200), () {
        if (_hasAddPinIntent) {
          debugPrint('🔄 Secondary reset of hasAddPinIntent required');
          _hasAddPinIntent = false;
          notifyListeners();
        }
      });
    }
  }
  
  // Set map reload flag
  void setNeedsMapReload(bool value) {
    _needsMapReload = value;
    debugPrint('Setting needs map reload: $value');
    notifyListeners();
  }
  
  // Clear map reload flag (called after reload is complete)
  void clearMapReloadFlag() {
    if (_needsMapReload) {
      _needsMapReload = false;
      debugPrint('Clearing map reload flag');
      notifyListeners();
    }
  }
  
  // Enhanced method to ensure actions always execute regardless of state
  // This is a more reliable way to ensure actions execute
  bool executeMapAction(Function() action) {
    try {
      // ALWAYS execute the action immediately without any checks
      // This ensures the action works every time, even during initialization
      action();
      return true;
    } catch (e) {
      debugPrint('Error executing map action: $e');
      return false;
    }
  }
  
  // Start periodic refresh timer for when user is stationary
  void _startPeriodicRefresh() {
    debugPrint('MapProvider: Periodic refresh timer disabled - using SmartLocationFetchManager instead');
    // Periodic refresh is now disabled to prevent duplicate API calls
    // All pin fetching is handled by SmartLocationFetchManager
  }
  
  // Stop periodic refresh timer
  void _stopPeriodicRefresh() {
    _periodicRefreshTimer?.cancel();
    _periodicRefreshTimer = null;
    debugPrint('MapProvider: Stopped periodic refresh timer');
  }
  
  // Pause all subscriptions (called when map screen is hidden)
  void pauseSubscriptions() {
    if (!_areSubscriptionsActive) return;
    
    _areSubscriptionsActive = false;
    _positionSubscription?.pause();
    _locationStatusSubscription?.pause();
    _stopPeriodicRefresh(); // Also stop periodic refresh when paused
    debugPrint('MapProvider: Subscriptions paused');
  }
  
  // Resume all subscriptions (called when map screen becomes visible again)
  void resumeSubscriptions() {
    if (_areSubscriptionsActive) return;
    
    _areSubscriptionsActive = true;
    _positionSubscription?.resume();
    _locationStatusSubscription?.resume();
    _startPeriodicRefresh(); // Restart periodic refresh when resumed
    debugPrint('MapProvider: Subscriptions resumed');
    
    // Refresh pins in case we missed updates while paused
    if (currentPosition != null) {
      refreshPins();
    }
  }
  
  // Initialize listeners for location updates
  void _initializeListeners() {
    // Listen for position updates
    _positionSubscription = _locationManager.positionStream.listen(
      (position) {
        if (position != null) {
          // If we have a position, update our state
          _currentCenter = LatLng(position.latitude, position.longitude);

          // Pin refreshing is now handled by SmartLocationFetchManager
          debugPrint('MapProvider: Position update received - pin refresh handled by SmartLocationFetchManager');

          // Notify UI of changes
          notifyListeners();
        }
      },
      onError: (error) {
        debugPrint('Position stream error: $error');
        _setError('Location error: $error');
      }
    );
    
    // Listen for location status changes
    _locationStatusSubscription = _locationManager.locationStatusStream.listen(
      (status) {
        // Handle status changes
        switch (status) {
          case LocationStatus.error:
            _setError(_locationManager.errorMessage ?? 'Unknown location error');
            break;
          case LocationStatus.disabled:
            _setError('Location services are disabled');
            break;
          case LocationStatus.denied:
          case LocationStatus.permanentlyDenied:
            _setError('Location permission denied');
            break;
          case LocationStatus.available:
            _clearError();
            _isMapLoading = false;
            break;
          default:
            // Other states don't require specific handling
            break;
        }
        
        notifyListeners();
      }
    );
    
    // Start location tracking
    _locationManager.startTracking();
  }
  
  // Request location permission
  Future<LocationPermission> requestLocationPermission() async {
    try {
      final status = await _locationManager.requestLocationPermission();
      
      if (status == LocationStatus.available) {
        // Clear error if permission was granted
        _clearError();
        
        // Get position if we have permission
        final position = await _locationManager.requestCurrentLocation();
        if (position != null) {
          _currentCenter = LatLng(position.latitude, position.longitude);
          notifyListeners();
        }
        
        return LocationPermission.whileInUse;
      } else if (status == LocationStatus.denied) {
        return LocationPermission.denied;
      } else if (status == LocationStatus.permanentlyDenied) {
        return LocationPermission.deniedForever;
      } else {
        return LocationPermission.denied;
      }
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
      _setError('Error requesting location: $e');
      return LocationPermission.denied;
    }
  }
  
  // Toggle location tracking
  void toggleLocationTracking() {
    _locationManager.toggleTracking().then((isTracking) {
      notifyListeners();
    });
  }
  
  // Update viewport when map is moved
  void updateViewport({
    required double latitude,
    required double longitude,
    required double zoom,
  }) {
    _currentCenter = LatLng(latitude, longitude);
    _currentZoom = zoom;

    // Viewport-based refresh is now disabled to prevent duplicate API calls
    // All pin fetching is handled by SmartLocationFetchManager
    debugPrint('MapProvider: Viewport update refresh disabled - handled by SmartLocationFetchManager');
  }
  
  // Create mock pins for testing
  Future<List<Map<String, dynamic>>> _createMockPins() async {
    // Create mock data for testing
    await Future.delayed(const Duration(milliseconds: 500));
    
    final lat = _locationManager.currentPosition?.latitude ?? AppConstants.defaultLatitude;
    final lng = _locationManager.currentPosition?.longitude ?? AppConstants.defaultLongitude;
    
    // Use a fixed list of pins rather than generating random ones
    final List<Map<String, dynamic>> mockPins = [
      {
        'id': 'africa-1',
        'title': 'African Beats',
        'artist': 'Rhythms of Africa',
        'track_url': 'https://example.com/african-beats',
        'latitude': 0.4231, // Kenya near equator
        'longitude': 36.7119, // East Africa
        'rarity': 'Epic',
        'custom_color': Colors.deepOrange[400]!.value,
        'is_collected': false,
      },
      {
        'id': 'africa-2',
        'title': 'Serengeti Dreams',
        'artist': 'Tanzania Collective',
        'track_url': 'https://example.com/serengeti',
        'latitude': -2.7644, // Tanzania
        'longitude': 32.8972, // Serengeti
        'rarity': 'Legendary',
        'custom_color': Colors.purple[300]!.value,
        'is_collected': false,
      },
      {
        'id': 'africa-3',
        'title': 'Sahara Sands',
        'artist': 'Desert Nomads',
        'track_url': 'https://example.com/sahara',
        'latitude': 23.4162, // Northern Africa
        'longitude': 25.6628, // Sahara
        'rarity': 'Rare',
        'custom_color': Colors.amber[600]!.value,
        'is_collected': false,
      }
    ];
    
    // Only add local pins around current position if not in Africa
    if (lat < -40 || lat > 40 || lng < -20 || lng > 60) {
      // We're outside Africa, add some local pins too
      for (int i = 0; i < 3; i++) {
        final latOffset = (0.01 * (i % 3 == 0 ? 1 : -1)) * (i / 10);
        final lngOffset = (0.01 * (i % 2 == 0 ? 1 : -1)) * (i / 10);
        
        mockPins.add({
          'id': 'local-$i',
          'title': 'Local Track $i',
          'artist': 'Local Artist ${i % 3}',
          'track_url': 'https://example.com/track$i',
          'latitude': lat + latOffset,
          'longitude': lng + lngOffset,
          'rarity': _getRarityForIndex(i),
          'is_collected': false,
        });
      }
    }
    
    return mockPins;
  }
  
  // Manual refresh method that can be called from UI
  Future<void> manualRefreshPins() async {
    debugPrint('MapProvider: Manual refresh disabled - handled by SmartLocationFetchManager');
    // Manual refresh is now disabled to prevent duplicate API calls
    // All pin fetching is handled by SmartLocationFetchManager
  }
  
  // Refresh pins from API based on current filter
  Future<void> refreshPins() async {
    debugPrint('MapProvider: refreshPins() called - now disabled to prevent duplicate API calls');
    debugPrint('MapProvider: Pin fetching is now handled by SmartLocationFetchManager in the map screen');

    // This method is now disabled to prevent duplicate API calls
    // All pin fetching is handled by SmartLocationFetchManager
    return;
  }
  
  /// Force refresh pins by triggering SmartLocationFetchManager
  /// This method can be called externally (e.g., after creating a new pin in AR screen)
  Future<void> forceRefreshPins() async {
    debugPrint('MapProvider: forceRefreshPins() called - triggering SmartLocationFetchManager');
    
    if (_onForceRefreshRequested != null) {
      _onForceRefreshRequested!();
      debugPrint('MapProvider: Successfully triggered SmartLocationFetchManager force refresh');
    } else {
      debugPrint('MapProvider: Warning - No force refresh callback registered');
    }
  }
  
  /// Register callback for force refresh (called by map screen)
  void setForceRefreshCallback(Function() callback) {
    _onForceRefreshRequested = callback;
    debugPrint('MapProvider: Force refresh callback registered');
  }
  
  /// Unregister force refresh callback
  void clearForceRefreshCallback() {
    _onForceRefreshRequested = null;
    debugPrint('MapProvider: Force refresh callback cleared');
  }
  
  /// Display pin optimistically with immediate visual feedback
  /// This shows the pin immediately while waiting for backend confirmation
  Future<void> displayPinOptimistically(Map<String, dynamic> pinData) async {
    debugPrint('MapProvider: displayPinOptimistically() called - showing pin immediately (hashCode: $hashCode)');
    debugPrint('MapProvider: Current callback: $_onOptimisticPinRequested');

    if (_onOptimisticPinRequested != null) {
      _onOptimisticPinRequested!(pinData);
      debugPrint('MapProvider: Successfully triggered optimistic pin display');
    } else {
      debugPrint('MapProvider: Warning - No optimistic pin callback registered');
      debugPrint('MapProvider: This usually means the map screen hasn\'t finished initializing yet');
    }
  }
  
  /// Register callback for optimistic pin display (called by map screen)
  void setOptimisticPinCallback(Function(Map<String, dynamic>) callback) {
    _onOptimisticPinRequested = callback;
    debugPrint('MapProvider: Optimistic pin callback registered (hashCode: $hashCode)');
    debugPrint('MapProvider: Callback function: $callback');
  }
  
  /// Unregister optimistic pin callback
  void clearOptimisticPinCallback() {
    _onOptimisticPinRequested = null;
    debugPrint('MapProvider: Optimistic pin callback cleared');
  }

  /// Check if optimistic pin callback is registered
  bool get hasOptimisticPinCallback => _onOptimisticPinRequested != null;
  
  // Helper to set error state
  void _setError(String message) {
    _hasNetworkError = true;
    _errorMessage = message;
    _isMapLoading = false;
    _isPinsLoading = false;
    notifyListeners();
  }
  
  // Helper to get rarity based on index
  String _getRarityForIndex(int index) {
    switch (index % 5) {
      case 0:
        return 'Common';
      case 1:
        return 'Uncommon';
      case 2:
        return 'Rare';
      case 3:
        return 'Epic';
      case 4:
        return 'Legendary';
      default:
        return 'Common';
    }
  }
  
  // Set selected pin
  void selectPin(dynamic pin) {
    _selectedPin = pin;
    notifyListeners();
  }
  
  // Clear selected pin
  void clearSelectedPin() {
    _selectedPin = null;
    notifyListeners();
  }
  
  // Add a new pin at the specified location
  Future<Map<String, dynamic>?> addPin({
    required double latitude,
    required double longitude,
    required String title,
    required String trackTitle,
    required String trackArtist,
    String? album,
    required String trackUrl,
    required String service,
    required int skin,
    String? description,
    String? caption,
    double? auraRadius = 50.0,
    bool isPrivate = false,
    bool useMockData = false,
  }) async {
    try {
      if (useMockData) {
        // Generate a unique ID for mock data
        final id = DateTime.now().millisecondsSinceEpoch.toString();
        
        // Create new mock pin data
        final newPin = {
          'id': id,
          'title': title,
          'description': description,
          'caption': caption,
          'track_title': trackTitle,
          'track_artist': trackArtist,
          'album': album,
          'track_url': trackUrl,
          'service': service,
          'skin': skin,
          'location': {
            'type': 'Point',
            'coordinates': [longitude, latitude]
          },
          'rarity': 'common',
          'aura_radius': auraRadius,
          'is_private': isPrivate,
          'interaction_count': {
            'view': 0,
            'like': 0,
            'collect': 0,
            'share': 0
          }
        };
        
        // Add to local pin collection
        _pins.add(newPin);
        _selectedPin = newPin;
        notifyListeners();
        return newPin;
      } else {
        final response = await _pinsService.createPin(
          latitude: latitude,
          longitude: longitude,
          title: title,
          description: description,
          caption: caption,
          trackTitle: trackTitle,
          trackArtist: trackArtist,
          album: album,
          trackUrl: trackUrl,
          service: service,
          skin: skin,
          auraRadius: auraRadius,
          isPrivate: isPrivate,
        );
        
        if (response != null) {
          final newPin = _convertPinToMap(response);
          _pins.add(newPin);
          _selectedPin = newPin;
          notifyListeners();
          return newPin;
        }
      }
      return null;
    } catch (e) {
      _setError('Error adding pin: $e');
      return null;
    }
  }
  
  // Determine the rarity of a pin based on various factors
  String determinePinRarity() {
    // This could use various factors like:
    // - User's current level or status
    // - Time of day
    // - Special events
    // - Location-based factors
    // - Random chance
    
    final random = DateTime.now().millisecond % 100; // 0-99
    
    if (random < 50) {
      return 'Common';
    } else if (random < 75) {
      return 'Uncommon';
    } else if (random < 90) {
      return 'Rare';
    } else if (random < 98) {
      return 'Epic';
    } else {
      return 'Legendary';
    }
  }
  
  // Helper method to convert Pin object to Map format
  Map<String, dynamic> _convertPinToMap(Pin pin) {
    return {
      'id': pin.id.toString(),
      'title': pin.title,
      'description': pin.description,
      'caption': pin.caption,
      'location_name': pin.locationName, // Human-readable location
      'track_title': pin.trackTitle,
      'track_artist': pin.trackArtist,
      'album': pin.album,
      'track_url': pin.trackUrl,
      'service': pin.service,
      'artwork_url': pin.artworkUrl, // Album/track artwork for display
      'duration_ms': pin.durationMs, // Track duration
      'genre': pin.genre, // Canonical genre (lowercase)
      'skin': pin.skin,
      'skin_details': pin.skinDetails, // Skin configuration
      'location': pin.location,
      'rarity': pin.rarity,
      'aura_radius': pin.auraRadius,
      'is_private': pin.isPrivate,
      'created_at': pin.createdAt.toIso8601String(),
      'updated_at': pin.updatedAt.toIso8601String(),
      'interaction_count': pin.interactionCount,
      'upvote_count': pin.upvoteCount,
      'downvote_count': pin.downvoteCount,
      'engagement_counts': pin.engagementCounts,
      // User data for proper attribution and display
      'owner': {
        'id': pin.owner.id,
        'username': pin.owner.username,
        'display_name': pin.owner.displayName,
        'profile_picture_url': pin.owner.profilePictureUrl, // Use correct field name
        'is_verified': pin.owner.isVerified,
        'created_at': pin.owner.createdAt.toIso8601String(), // Remove null-aware operator
      },
    };
  }
  
  // Helper to clear error state
  void _clearError() {
    _hasNetworkError = false;
    _errorMessage = '';
    _isMapLoading = false;
    _isPinsLoading = false;
    notifyListeners();
  }
  
  // Added for search functionality - allows direct animation to a location
  void animateToLocation({
    required double latitude,
    required double longitude,
    double? zoom,
  }) {
    // Update the current center and zoom
    _currentCenter = LatLng(latitude, longitude);
    if (zoom != null) {
      _currentZoom = zoom;
    }
    
    // Notify listeners to trigger map animation
    notifyListeners();
  }
  
  // Collect multiple pins at once
  Future<void> collectPinsInRadius(List<Map<String, dynamic>> pinsToCollect) async {
    try {
      // Mark pins as collected in local state
      for (final pin in pinsToCollect) {
        final pinId = pin['id'];
        final pinIndex = _pins.indexWhere((p) => p['id'] == pinId);
        if (pinIndex != -1) {
          _pins[pinIndex] = {
            ..._pins[pinIndex],
            'is_collected': true,
          };
        }
      }
      
      // Notify listeners to update UI
      notifyListeners();
      
      // In a real implementation, you would call the API to update the server
      // for (final pin in pinsToCollect) {
      //   await _pinsService.collectPin(int.parse(pin['id']));
      // }
    } catch (e) {
      _setError('Error collecting pins: $e');
    }
  }
  
  // Add a real pin from the API while preserving mock pins
  void addRealPin(Map<String, dynamic> pin) {
    debugPrint('🆕 MapProvider: Adding new pin ${pin['id']} to collection');
    debugPrint('🆕 Pin count before: ${_pins.length}');
    
    // Add the new pin to our list while preserving mock pins
    _pins.add(pin);
    
    // Set as selected pin
    _selectedPin = pin;
    
    debugPrint('🆕 Pin count after: ${_pins.length}');
    debugPrint('🆕 MapProvider: Notifying listeners about new pin');
    
    // Notify listeners to update UI
    notifyListeners();
  }
  
  // Toggle mock/test pins generation at runtime (default = false)
  void setEnableMockPins(bool value) {
    if (_enableMockPins != value) {
      _enableMockPins = value;
      debugPrint('MapProvider: enableMockPins = $value');
      // Refresh pins immediately to apply the change
      refreshPins();
    }
  }
  
  // Start background location tracking
  Future<bool> startBackgroundLocationTracking() async {
    try {
      final success = await _locationManager.startBackgroundTracking();
      if (success) {
        debugPrint('MapProvider: Background location tracking started');
        notifyListeners();
      }
      return success;
    } catch (e) {
      debugPrint('MapProvider: Error starting background location: $e');
      _setError('Failed to start background location tracking: $e');
      return false;
    }
  }
  
  // Stop background location tracking
  Future<void> stopBackgroundLocationTracking() async {
    try {
      await _locationManager.stopBackgroundTracking();
      debugPrint('MapProvider: Background location tracking stopped');
      notifyListeners();
    } catch (e) {
      debugPrint('MapProvider: Error stopping background location: $e');
    }
  }
  
  // Toggle background location tracking
  Future<bool> toggleBackgroundLocationTracking() async {
    if (_locationManager.isBackgroundTracking) {
      await stopBackgroundLocationTracking();
      return false;
    } else {
      return await startBackgroundLocationTracking();
    }
  }
  
  @override
  void dispose() {
    _positionSubscription?.cancel();
    _locationStatusSubscription?.cancel();
    _stopPeriodicRefresh();
    super.dispose();
  }
} 