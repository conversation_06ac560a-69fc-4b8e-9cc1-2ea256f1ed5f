import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/music_track.dart';
import '../services/music/youtube_player_service.dart';
import 'package:flutter_in_app_pip/flutter_in_app_pip.dart';
import '../widgets/music/youtube_player_widget.dart';
import '../widgets/navigation/app_navigation_system.dart';
import '../widgets/bottomsheets/collection_selector_bottomsheet.dart';

class YouTubeProvider extends ChangeNotifier {
  final YouTubePlayerService _youtubeService = YouTubePlayerService();

  // State
  bool _isInitialized = false;
  bool _isPlaying = false;
  MusicTrack? _currentTrack;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  List<MusicTrack> _queue = [];
  int _currentQueueIndex = -1;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isPlaying => _isPlaying;
  MusicTrack? get currentTrack => _currentTrack;
  Duration get position => _position;
  Duration get duration => _duration;
  List<MusicTrack> get queue => _youtubeService.queue;
  int get currentQueueIndex => _youtubeService.currentQueueIndex;
  bool get hasNext => _youtubeService.hasNext;
  bool get hasPrevious => _youtubeService.hasPrevious;
  Stream<Duration> get positionStream => _youtubeService.positionStream;
  Stream<Duration> get durationStream => _youtubeService.durationStream;
  Stream<List<MusicTrack>> get queueStream => _youtubeService.queueStream;
  // Modify getter to ensure hasActivePlayback reflects current state
  bool get hasActivePlayback {
    final isActive =
        _currentTrack != null && (_isPlaying || _position > Duration.zero);
    return isActive;
  }

  /// Check if the provider is currently processing a request
  bool get isProcessingRequest => _youtubeService.isProcessingRequest;

  YouTubePlayerService get youtubeService => _youtubeService;

  /// Initialize the provider
  Future<bool> initialize() async {
    try {
      // Initialize with PiP callback to start PiP when videos load
      await _youtubeService.initialize(startPiPCallback: startYouTubePiP);
      _isInitialized = true;

      // Listen to service streams for fallback events
      _youtubeService.onWillTryNextCandidate.listen((_) {
        if (kDebugMode) {
          print('🎬 [YouTubeProvider] Trying next candidate...');
        }
      });
      
      _youtubeService.onAllCandidatesFailed.listen((_) {
        if (kDebugMode) {
          print('🎬 [YouTubeProvider] All candidates failed');
        }
      });
      
      _youtubeService.isPlayingStream.listen(
        (playing) {
          if (kDebugMode) {
            print('🎬 [YouTubeProvider] Playing state changed: $playing');
          }
          _isPlaying = playing;
          notifyListeners();
        },
        onError: (error) {
          if (kDebugMode) {
            print('❌ [YouTubeProvider] Error in playing state stream: $error');
          }
        },
        cancelOnError: false,
      );

      _youtubeService.currentTrackStream.listen(
        (track) {
          if (kDebugMode) {
            print(
                '🎬 [YouTubeProvider] Current track changed: ${track?.title ?? 'None'}');
          }
          _currentTrack = track;
          notifyListeners();
        },
        onError: (error) {
          if (kDebugMode) {
            print('❌ [YouTubeProvider] Error in current track stream: $error');
          }
        },
        cancelOnError: false,
      );

      _youtubeService.positionStream.listen(
        (pos) {
          // Defensive handling of position updates
          if (pos.inMilliseconds >= 0 &&
              (_duration.inMilliseconds == 0 || pos <= _duration)) {
            _position = pos;
            notifyListeners();
          }
        },
        onError: (error) {
          if (kDebugMode) {
            print('❌ [YouTubeProvider] Error in position stream: $error');
          }
        },
        cancelOnError: false,
      );

      _youtubeService.durationStream.listen(
        (dur) {
          // Defensive handling of duration updates
          if (dur.inMilliseconds > 0) {
            _duration = dur;
            notifyListeners();
          }
        },
        onError: (error) {
          if (kDebugMode) {
            print('❌ [YouTubeProvider] Error in duration stream: $error');
          }
        },
        cancelOnError: false,
      );

      if (kDebugMode) {
        print('✅ [YouTubeProvider] Initialized successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubeProvider] Failed to initialize: $e');
      }
      return false;
    }
  }

  // Add more detailed logging for playback state
  void _logPlaybackState(String method) {
    if (kDebugMode) {
      print('🎬 [YouTubeProvider] $method:');
      print('   - hasActivePlayback: $hasActivePlayback');
      print('   - isPlaying: $isPlaying');
      print('   - currentTrack: ${currentTrack?.title ?? 'null'}');
      print('   - currentTrack URI: ${currentTrack?.uri ?? 'null'}');
    }
  }

  // Modify playTrack to explicitly set hasActivePlayback with better error handling
  Future<bool> playTrack(MusicTrack track) async {
    try {
      if (kDebugMode) {
        print('🎬 [YouTubeProvider] Attempting to play: ${track.title} by ${track.artist}');
      }

      final success = await _youtubeService.playTrack(track);

      if (success) {
        _currentTrack = track;
        _isPlaying = true;

        // Start PiP for the new track
        startYouTubePiP();

        // Explicitly notify listeners to update UI
        notifyListeners();

        if (kDebugMode) {
          print('✅ [YouTubeProvider] Started playback: ${track.title}');
          print(
              '🎬 [YouTubeProvider] hasActivePlayback after playTrack: $hasActivePlayback');
        }
      } else {
        if (kDebugMode) {
          print('❌ [YouTubeProvider] Failed to start playback for: ${track.title}');
        }
        // Reset state on failure
        _currentTrack = null;
        _isPlaying = false;
        notifyListeners();
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubeProvider] Error playing track: $e');
      }
      // Reset state on error
      _currentTrack = null;
      _isPlaying = false;
      notifyListeners();
      return false;
    }
  }

  // Modify other methods to ensure consistent state
  Future<void> play() async {
    try {
      await _youtubeService.play();
      _isPlaying = true;
      notifyListeners();

      if (kDebugMode) {
        print('▶️ [YouTubeProvider] Play called');
        print(
            '🎬 [YouTubeProvider] hasActivePlayback after play: $hasActivePlayback');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubeProvider] Error playing: $e');
      }
    }
  }

  Future<void> pause() async {
    try {
      await _youtubeService.pause();
      _isPlaying = false;
      notifyListeners();

      if (kDebugMode) {
        print('⏸️ [YouTubeProvider] Pause called');
        print(
            '🎬 [YouTubeProvider] hasActivePlayback after pause: $hasActivePlayback');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubeProvider] Error pausing: $e');
      }
    }
  }

  /// Add a track to the queue
  void addToQueue(MusicTrack track) {
    _youtubeService.addToQueue(track);
    notifyListeners();
  }

  /// Add multiple tracks to the queue
  void addTracksToQueue(List<MusicTrack> tracks) {
    _youtubeService.addTracksToQueue(tracks);
    notifyListeners();
  }

  /// Remove a track from the queue
  void removeFromQueue(int index) {
    _youtubeService.removeFromQueue(index);
    notifyListeners();
  }

  /// Clear the queue
  void clearQueue() {
    _youtubeService.clearQueue();
    notifyListeners();
  }

  /// Skip to next track in queue
  Future<bool> playNext() async {
    final success = await _youtubeService.playNext();
    if (success) {
      notifyListeners();
      // Start PiP for the new track
      startYouTubePiP();
    }
    return success;
  }

  /// Skip to previous track in queue
  Future<bool> playPrevious() async {
    final success = await _youtubeService.playPrevious();
    if (success) {
      notifyListeners();
      // Start PiP for the new track
      startYouTubePiP();
    }
    return success;
  }

  /// Skip to specific track in queue
  Future<bool> skipToTrack(int index) async {
    final success = await _youtubeService.skipToTrack(index);
    if (success) {
      notifyListeners();
      // Start PiP for the new track
      startYouTubePiP();
    }
    return success;
  }

  /// Shuffle the remaining tracks in the queue
  void shuffleQueue() {
    _youtubeService.shuffleQueue();
    notifyListeners();
  }

  // Modify stop to also handle queue state
  Future<void> stop() async {
    try {
      if (kDebugMode) {
        print('⏹️ [YouTubeProvider] Stop called - stopping playback and PiP');
      }

      await _youtubeService.stop();
      _isPlaying = false;
      _currentTrack = null;
      _position = Duration.zero;
      _duration = Duration.zero;

      // Stop PiP when playback stops
      _stopYouTubePiP();

      notifyListeners();

      if (kDebugMode) {
        print('⏹️ [YouTubeProvider] Stop completed');
        print(
            '🎬 [YouTubeProvider] hasActivePlayback after stop: $hasActivePlayback');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubeProvider] Error stopping: $e');
      }
    }
  }

  /// Seek to position
  Future<bool> seek(Duration position) async {
    try {
      final success = await _youtubeService.seek(position);
      if (success) {
        _position = position;
        notifyListeners();
      }
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubeProvider] Error seeking: $e');
      }
      return false;
    }
  }

  /// Clear current track (without stopping playback)
  void clearCurrentTrack() {
    _currentTrack = null;
    notifyListeners();
  }

  /// Extract duration from pin data in milliseconds
  int _extractDurationFromPin(Map<String, dynamic> pinData) {
    print("pinData: $pinData");
    try {
      // Prioritize duration_ms key with multiple fallback strategies
      final duration = pinData['duration_ms'] ?? 
                     pinData['durationMs'] ?? 
                     pinData['duration'] ?? 
                     pinData['track_duration'] ?? 
                     pinData['length'] ?? 
                     pinData['length_ms'] ??
                     // Try nested keys
                     pinData['track']?['duration_ms'] ??
                     pinData['track']?['duration'] ??
                     // Try converting from seconds to milliseconds
                     (pinData['duration_seconds'] != null 
                       ? (pinData['duration_seconds'] as num).toInt() * 1000 
                       : null) ??
                     // Try extracting from Spotify/Apple Music URLs
                     _extractDurationFromUrl(pinData['uri'] ?? pinData['url'] ?? '');
      
      if (duration == null) {
        if (kDebugMode) {
          print('🎬 [YouTubeProvider] No duration found in pin data, using default estimate');
        }
        // Return a reasonable default duration (3.5 minutes) to help with YouTube search scoring
        return 210000; // 3.5 minutes in milliseconds
      }
      
      // Handle different duration formats
      if (duration is int) {
        return duration;
      } else if (duration is double) {
        return duration.toInt();
      } else if (duration is String) {
        // Try parsing string duration
        final parsed = int.tryParse(duration);
        if (parsed != null) return parsed;
        
        // If it looks like a decimal string, parse as double
        final doubleParsed = double.tryParse(duration);
        if (doubleParsed != null) return doubleParsed.toInt();
        
        // Try parsing time format like "3:30" or "03:30"
        final timeParts = duration.split(':');
        if (timeParts.length == 2) {
          final minutes = int.tryParse(timeParts[0]) ?? 0;
          final seconds = int.tryParse(timeParts[1]) ?? 0;
          return (minutes * 60 + seconds) * 1000;
        } else if (timeParts.length == 3) {
          final hours = int.tryParse(timeParts[0]) ?? 0;
          final minutes = int.tryParse(timeParts[1]) ?? 0;
          final seconds = int.tryParse(timeParts[2]) ?? 0;
          return (hours * 3600 + minutes * 60 + seconds) * 1000;
        }
      }
      
      // Fallback to default if parsing fails
      return 210000;
    } catch (e) {
      print('Error extracting duration: $e');
      return 210000; // Default duration
    }
  }

  /// Clean and normalize track information for better search results
  String _cleanTrackInfo(String info) {
    // Remove common punctuation and normalize spaces
    String cleaned = info.replaceAll(RegExp(r'[^\w\s\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]'), ' ').trim();
    
    // Normalize multiple spaces to single space
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');
    
    // Remove common suffixes that might interfere with search
    final suffixesToRemove = [
      ' (explicit)',
      ' (clean)',
      ' (radio edit)',
      ' (album version)',
      ' (single version)',
      ' (official video)',
      ' (music video)',
    ];
    
    for (final suffix in suffixesToRemove) {
      if (cleaned.toLowerCase().endsWith(suffix.toLowerCase())) {
        cleaned = cleaned.substring(0, cleaned.length - suffix.length);
      }
    }
    
    return cleaned.trim();
  }

  /// Normalize artist names, preserving collaborations
  String _normalizeArtistNames(String artistString) {
    // Split artists by common separators
    final artists = artistString
        .split(RegExp(r'[,&]'))  // Split by comma or ampersand
        .expand((artist) => artist.split(' feat. ')) // Handle featured artists
        .map((artist) => _cleanTrackInfo(artist.trim()))
        .where((artist) => artist.isNotEmpty)
        .toSet()
        .toList();

    // Rejoin artists with comma and 'feat.' for better readability
    return artists.length > 1 
      ? '${artists.take(artists.length - 1).join(', ')} feat. ${artists.last}'
      : artists.first;
  }

  /// Extract duration from Spotify/Apple Music URLs
  int _extractDurationFromUrl(String url) {
    try {
      // Spotify track URL format: https://open.spotify.com/track/trackId?si=someHash
      if (url.contains('open.spotify.com/track/')) {
        // Spotify URLs don't typically include duration in the URL
        return 0;
      }
      
      // Apple Music URL format: https://music.apple.com/us/song/songName/someId
      if (url.contains('music.apple.com/')) {
        // Apple Music URLs also don't include duration
        return 0;
      }
      
      return 0;
    } catch (e) {
      print('Error extracting duration from URL: $e');
      return 0;
    }
  }

  /// Play a track from pin data with proper pin provider communication
  Future<bool> playTrackFromPin(Map<String, dynamic> pinData,
      {BuildContext? context}) async {
    try {
      if (kDebugMode) {
        print('🎬 [YouTubeProvider] Attempting to play track from pin data');
      }

      // Extract track information from pin data
      final title =
          pinData['title'] ?? pinData['track_title'] ?? pinData['name'];
      final artist = pinData['artist'] ?? pinData['track_artist'];
      final originalUrl =
          pinData['uri'] ?? pinData['url'] ?? pinData['track_url'] ?? '';

      if (title == null || title.toString().isEmpty) {
        if (kDebugMode) {
          print('❌ [YouTubeProvider] No track title found in pin data');
        }
        return false;
      }

      if (artist == null || artist.toString().isEmpty) {
        if (kDebugMode) {
          print('❌ [YouTubeProvider] No artist found in pin data');
        }
        return false;
      }

      // Clean and normalize the title and artist for better search results
      final cleanTitle = _cleanTrackInfo(title.toString());
      final cleanArtist = _normalizeArtistNames(artist.toString());

      if (kDebugMode) {
        print('🎬 [YouTubeProvider] Original: "$title" by "$artist"');
        print('🎬 [YouTubeProvider] Cleaned: "$cleanTitle" by "$cleanArtist"');
      }

      // Initialize if not already done
      if (!_isInitialized) {
        final initialized = await initialize();
        if (!initialized) {
          if (kDebugMode) {
            print('❌ [YouTubeProvider] Failed to initialize YouTube service');
          }
          return false;
        }
      }

      // Attempt to extract duration with multiple fallback strategies
      final durationMs = _extractDurationFromPin(pinData);

      // Create a MusicTrack object for YouTube playback using cleaned info
      final track = MusicTrack(
        id: originalUrl.isNotEmpty
            ? originalUrl
            : 'youtube_search_${cleanTitle}_${cleanArtist}',
        title: cleanTitle,
        artist: cleanArtist,
        album: pinData['album']?.toString() ?? '',
        albumArt: pinData['artwork_url']?.toString() ??
            pinData['albumArt']?.toString() ??
            '',
        uri: originalUrl.isNotEmpty
            ? originalUrl
            : 'youtube_search_${cleanTitle}_${cleanArtist}',
        url: originalUrl.isNotEmpty
            ? originalUrl
            : 'youtube_search_${cleanTitle}_${cleanArtist}',
        service: 'youtube',
        serviceType: 'youtube',
        genres: [],
        durationMs: durationMs,
        explicit: false,
        popularity: 0,
      );

      if (kDebugMode) {
        print('🎬 [YouTubeProvider] Created MusicTrack for YouTube playback:');
        print('   Title: ${track.title}');
        print('   Artist: ${track.artist}');
        print('   URL: ${track.url}');
        print('   Duration: ${track.durationMs}ms');
      }

      // Play the track
      final success = await playTrack(track);

      if (success) {
        if (kDebugMode) {
          print(
              '✅ [YouTubeProvider] Successfully started YouTube playback: ${track.title}');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ [YouTubeProvider] Failed to start YouTube playback');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubeProvider] Error playing track from pin: $e');
      }
      return false;
    }
  }

  /// Start YouTube Picture-in-Picture mode with custom size
  void startYouTubePiP() {
    if (_currentTrack != null) {
      // Stop any existing PiP first
      _stopYouTubePiP();

      // Add a small delay to ensure PiP is properly closed
      Future.delayed(const Duration(milliseconds: 100), () {
        // Get screen width to calculate percentage-based spacing
        final context = AppNavigationSystem().navigatorKey.currentContext;
        if (context != null) {
          final screenWidth = MediaQuery.of(context).size.width;
          
          // Calculate responsive spacing (based on current perfect positioning)
          // Current: leftSpace: 12, rightSpace: 260 on ~390px screens
          final leftSpacePercent = 12 / 390; // ~3.08%
          final rightSpacePercent = 260 / 390; // ~66.67%
          
          final responsiveLeftSpace = (screenWidth * leftSpacePercent).round().toDouble();
          final responsiveRightSpace = (screenWidth * rightSpacePercent).round().toDouble();
          
          // Update PiP parameters with responsive positioning
          PictureInPicture.updatePiPParams(
            pipParams: PiPParams(
              pipWindowHeight: 125,
              pipWindowWidth: 125,
              bottomSpace: 64,
              leftSpace: responsiveLeftSpace, // Responsive left spacing
              rightSpace: responsiveRightSpace, // Responsive right spacing
              topSpace: 64,
              maxSize: const Size(125, 125),
              minSize: const Size(125, 125),
              movable: true,
              resizable: false,
              initialCorner: PIPViewCorner.topLeft,
            ),
          );
        } else {
          // Fallback to fixed values if context unavailable
          PictureInPicture.updatePiPParams(
            pipParams: const PiPParams(
              pipWindowHeight: 125,
              pipWindowWidth: 125,
              bottomSpace: 64,
              leftSpace: 12,
              rightSpace: 260,
              topSpace: 64,
              maxSize: Size(125, 125),
              minSize: Size(125, 125),
              movable: true,
              resizable: false,
              initialCorner: PIPViewCorner.topLeft,
            ),
          );
        }

        // Start PiP mode immediately with the YouTube player widget
        PictureInPicture.startPiP(
          pipWidget: PiPWidget(
            onPiPClose: () {
              // Handle PiP close event
              if (kDebugMode) {
                print('🎬 [YouTubeProvider] YouTube PiP closed');
              }
            },
            elevation: 8,
            pipBorderRadius: 8,
            child: YouTubePlayerWidget(
              service: _youtubeService,
              enablePiP: false,
              size: 125,
              onAddToCollection: (track) {
                // Get the current context from the navigation system
                final context = AppNavigationSystem().navigatorKey.currentContext;
                if (context != null) {
                  // Show the collection selector bottom sheet in the proper context
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    builder: (context) => CollectionSelectorBottomSheet(track: track),
                  );
                } else if (kDebugMode) {
                  print('❌ [YouTubeProvider] No context available for showing collection bottom sheet');
                }
              },
            ),
          ),
        );

        if (kDebugMode) {
          print(
              '🎬 [YouTubeProvider] Started YouTube PiP mode for: ${_currentTrack!.title}');
        }
      });
    } else if (kDebugMode) {
      print('❌ [YouTubeProvider] Cannot start PiP - no current track');
    }
  }

  /// Stop YouTube Picture-in-Picture mode
  void _stopYouTubePiP() {
    try {
      PictureInPicture.stopPiP();
      if (kDebugMode) {
        print('🎬 [YouTubeProvider] Stopped YouTube PiP mode');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [YouTubeProvider] Error stopping PiP: $e');
      }
    }
  }

  @override
  void dispose() {
    _youtubeService.dispose();
    super.dispose();
  }
}
