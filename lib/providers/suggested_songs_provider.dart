import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../models/music_track.dart';
import '../screens/search/ai_search/ai_search_provider.dart';
import '../services/ai/global_ai_provider_service.dart';

/// Global provider for managing suggested songs state across all profile tabs
/// Ensures shared state and prevents reloading when switching between tabs
class SuggestedSongsProvider extends ChangeNotifier {
  static SuggestedSongsProvider? _instance;
  static SuggestedSongsProvider get instance {
    _instance ??= SuggestedSongsProvider._internal();
    return _instance!;
  }
  
  SuggestedSongsProvider._internal();
  
  // Shared state - now uses global AI provider service
  List<MusicTrack> _suggestedSongs = [];
  bool _isInitialized = false;
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String? _errorMessage;
  DateTime? _lastLoadTime;
  
  // Debouncing and rate limiting for loadMore
  DateTime? _lastLoadMoreTime;
  static const Duration _loadMoreCooldown = Duration(seconds: 2); // Minimum 2 seconds between requests
  int _pendingLoadMoreRequests = 0;
  
  // Cache expiry duration
  static const Duration _cacheExpiry = Duration(hours: 1);
  
  // Getters
  List<MusicTrack> get suggestedSongs => _suggestedSongs;
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  String? get errorMessage => _errorMessage;
  AISearchProvider? get aiSearchProvider => GlobalAIProviderService.instance.aiProvider;
  bool get hasValidCache => _isInitialized && 
      _suggestedSongs.isNotEmpty && 
      _lastLoadTime != null && 
      DateTime.now().difference(_lastLoadTime!) < _cacheExpiry;
  
  /// Initialize the suggested songs provider with context
  Future<void> initialize(BuildContext context) async {
    // Check if cache is still valid
    if (hasValidCache) {
      if (kDebugMode) {
        print('📦 Using cached suggested songs (${_suggestedSongs.length} tracks) - no reload needed');
      }
      return;
    }

    if (_isLoading) {
      // Already loading, wait for completion
      if (kDebugMode) {
        print('🔄 Suggested songs already loading, waiting for completion...');
      }
      return;
    }
    
    // Try to sync with global provider first (fast path)
    if (_tryGlobalProviderSync()) {
      if (kDebugMode) {
        print('✅ Synced suggested songs from global provider (${_suggestedSongs.length} tracks) - no reload needed');
      }
      return;
    }
    
    // Only load if no valid data available
    await _loadSuggestedSongs(context);
  }
  
  /// Load suggested songs from AI provider
  Future<void> _loadSuggestedSongs(BuildContext context) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      if (kDebugMode) {
        print('🎯 Getting Global AI Provider for suggested songs...');
      }

      // Get the global AI provider instance
      final aiProvider = await GlobalAIProviderService.instance.getProvider(context);

      if (aiProvider == null) {
        if (kDebugMode) {
          print('❌ Global AI Provider not available');
        }
        _errorMessage = 'AI recommendations not available';
        return;
      }

      // Ensure we're using artist-based recommendations for fast loading
      await GlobalAIProviderService.instance.ensureArtistBasedRecommendations(context);

      // Check if it already has artist-based data
      if (aiProvider.currentRecommendations.isNotEmpty &&
          aiProvider.currentCategory == 'artistBased') {
        // Use existing artist-based data
        _suggestedSongs = List<MusicTrack>.from(aiProvider.currentRecommendations);
        _isInitialized = true;
        _lastLoadTime = DateTime.now();

        if (kDebugMode) {
          print('✅ Using existing artist-based suggested songs (${_suggestedSongs.length} tracks)');
        }
      } else {
        if (kDebugMode) {
          print('⚠️ No artist-based suggestions available from global provider');
        }
        _errorMessage = 'No suggested songs available';
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing shared suggested songs: $e');
      }
      _errorMessage = 'Error loading suggested songs: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  /// Load more suggested songs (pagination) with debouncing and rate limiting
  Future<void> loadMore(BuildContext context) async {
    // Increment pending requests counter
    _pendingLoadMoreRequests++;
    
    try {
      // Check if already loading more
      if (_isLoadingMore) {
        if (kDebugMode) {
          print('🚫 Load more already in progress, ignoring request (#${_pendingLoadMoreRequests})');
        }
        return;
      }
      
      // Check cooldown period to prevent rapid-fire requests
      if (_lastLoadMoreTime != null) {
        final timeSinceLastRequest = DateTime.now().difference(_lastLoadMoreTime!);
        if (timeSinceLastRequest < _loadMoreCooldown) {
          if (kDebugMode) {
            final remainingTime = _loadMoreCooldown.inMilliseconds - timeSinceLastRequest.inMilliseconds;
            print('🚫 Load more cooldown active, ${remainingTime}ms remaining (request #${_pendingLoadMoreRequests})');
          }
          return;
        }
      }
      
      final globalAIProvider = GlobalAIProviderService.instance;
      final aiProvider = await globalAIProvider.getProvider(context);
      if (aiProvider == null) {
        if (kDebugMode) {
          print('❌ Global AI Provider not available for load more');
        }
        return;
      }
      
      // Set loading state and update timestamp
      _isLoadingMore = true;
      _lastLoadMoreTime = DateTime.now();
      notifyListeners();
      
      if (kDebugMode) {
        print('📄 Loading more artist-based suggested songs (request #${_pendingLoadMoreRequests})...');
      }
      
      await aiProvider.loadMoreRecommendations(context);
      
      if (aiProvider.currentRecommendations.length > _suggestedSongs.length) {
        final previousCount = _suggestedSongs.length;
        _suggestedSongs = List<MusicTrack>.from(aiProvider.currentRecommendations);
        _lastLoadTime = DateTime.now();
        
        if (kDebugMode) {
          final newCount = _suggestedSongs.length - previousCount;
          print('✅ Loaded ${newCount} more suggested songs (total: ${_suggestedSongs.length})');
        }
      } else {
        if (kDebugMode) {
          print('⚠️ No new suggested songs loaded');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading more suggested songs: $e');
      }
    } finally {
      _isLoadingMore = false;
      _pendingLoadMoreRequests = 0; // Reset counter after completion
      notifyListeners();
    }
  }
  
  /// Refresh suggested songs
  Future<void> refresh(BuildContext context) async {
    _isInitialized = false;
    _lastLoadTime = null;
    _suggestedSongs.clear();
    
    await _loadSuggestedSongs(context);
  }
  
  /// Try to sync with global AI provider without loading (fast path)
  bool _tryGlobalProviderSync() {
    final aiProvider = GlobalAIProviderService.instance.aiProvider;
    
    // Check if global provider has artist-based data available
    if (aiProvider != null && 
        aiProvider.currentRecommendations.isNotEmpty && 
        aiProvider.currentCategory == 'artistBased') {
      
      // Use the already loaded data from global provider
      _suggestedSongs = List<MusicTrack>.from(aiProvider.currentRecommendations);
      _lastLoadTime = DateTime.now();
      _isInitialized = true;
      _errorMessage = null;
      notifyListeners();
      
      return true; // Successfully synced
    }
    
    return false; // No data available, need to load
  }
  
  /// Sync with global AI provider (called when needed)
  void syncWithGlobalProvider() {
    if (_tryGlobalProviderSync()) {
      if (kDebugMode) {
        print('🔄 Synced with global AI provider (${_suggestedSongs.length} tracks)');
      }
    }
  }
  
  /// Get shuffled copy of suggested songs
  List<MusicTrack> getShuffledSongs() {
    if (_suggestedSongs.isEmpty) return [];
    
    final shuffled = List<MusicTrack>.from(_suggestedSongs);
    shuffled.shuffle();
    return shuffled;
  }
  
  /// Clear cache and reset state
  void clearCache() {
    _suggestedSongs.clear();
    _isInitialized = false;
    _lastLoadTime = null;
    _errorMessage = null;
    notifyListeners();
  }
  

}
