import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import '../services/notifications/onesignal_service.dart';
import '../services/auth_service.dart';

/// OneSignal provider to manage push notification state
class OneSignalProvider with ChangeNotifier {
  final OneSignalService _oneSignalService = OneSignalService();
  
  bool _isInitialized = false;
  bool _notificationsEnabled = false;
  bool _isSubscribed = false;
  String? _playerId;
  String? _pushToken;
  String? _externalUserId;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get isSubscribed => _isSubscribed;
  String? get playerId => _playerId;
  String? get pushToken => _pushToken;
  String? get externalUserId => _externalUserId;

  /// Initialize OneSignal
  Future<bool> initialize({String? userId}) async {
    try {
      bool success;
      if (userId != null) {
        success = await _oneSignalService.initialize(userId: userId);
      } else {
        // For backward compatibility, use basic initialization
        success = await _oneSignalService.requestNotificationPermission();
        _isInitialized = success;
      }
      
      if (success) {
        _isInitialized = true;
        _updateState();
        notifyListeners();
      }
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing OneSignal provider: $e');
      }
      return false;
    }
  }

  /// Update internal state from OneSignal service
  void _updateState() {
    _notificationsEnabled = _oneSignalService.notificationsEnabled;
    _isSubscribed = _oneSignalService.isSubscribed;
    _playerId = _oneSignalService.playerId;
    _pushToken = _oneSignalService.pushToken;
  }

  /// Request notification permission
  Future<bool> requestPermission() async {
    final granted = await _oneSignalService.requestNotificationPermission();
    _updateState();
    notifyListeners();
    return granted;
  }

  /// Set external user ID (typically your app's user ID)
  Future<void> setExternalUserId(String userId) async {
    await _oneSignalService.setExternalUserId(userId);
    _externalUserId = userId;
    notifyListeners();
  }

  /// Set external user ID with user tags for comprehensive user identification
  Future<void> setUserData({
    required String userId,
    String? username,
    String? email,
    bool? isVerified,
    Map<String, bool>? connectedServices,
  }) async {
    // Set external user ID
    await _oneSignalService.setExternalUserId(userId);
    _externalUserId = userId;
    
    // Set user tags for segmentation
    final tags = <String, String>{
      'user_id': userId,
    };
    
    if (username != null) tags['username'] = username;
    if (email != null) tags['email'] = email;
    if (isVerified != null) tags['is_verified'] = isVerified.toString();
    
    if (connectedServices != null) {
      tags['has_spotify'] = (connectedServices['spotify'] ?? false).toString();
      tags['has_apple_music'] = (connectedServices['apple_music'] ?? false).toString();
      tags['has_soundcloud'] = (connectedServices['soundcloud'] ?? false).toString();
    }
    
    await _oneSignalService.addTags(tags);
    notifyListeners();
  }

  /// Remove external user ID (on logout)
  Future<void> removeExternalUserId() async {
    await _oneSignalService.removeExternalUserId();
    _externalUserId = null;
    notifyListeners();
  }

  /// Add user tags for segmentation
  Future<void> addTags(Map<String, String> tags) async {
    await _oneSignalService.addTags(tags);
  }

  /// Remove user tags
  Future<void> removeTags(List<String> tagKeys) async {
    await _oneSignalService.removeTags(tagKeys);
  }

  /// Opt user into push notifications
  Future<void> optIn() async {
    await _oneSignalService.optIn();
    _updateState();
    notifyListeners();
  }

  /// Opt user out of push notifications
  Future<void> optOut() async {
    await _oneSignalService.optOut();
    _updateState();
    notifyListeners();
  }

  /// Set user tags based on app context
  Future<void> setUserTags({
    String? musicPreference,
    String? location,
    bool? isPremium,
    String? lastActivity,
  }) async {
    final tags = <String, String>{};
    
    if (musicPreference != null) tags['music_preference'] = musicPreference;
    if (location != null) tags['location'] = location;
    if (isPremium != null) tags['is_premium'] = isPremium.toString();
    if (lastActivity != null) tags['last_activity'] = lastActivity;
    
    await addTags(tags);
  }

  /// Send user data to your backend (if needed)
  Future<void> syncWithBackend(AuthService authService) async {
    try {
      if (_playerId == null || _externalUserId == null) return;
      
      // You can implement API call to sync OneSignal data with your backend
      // Example:
      // await authService.updateUserNotificationSettings({
      //   'onesignal_player_id': _playerId,
      //   'push_token': _pushToken,
      //   'notifications_enabled': _notificationsEnabled,
      // });
      
      if (kDebugMode) {
        print('🔔 OneSignal data synced with backend');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing OneSignal data with backend: $e');
      }
    }
  }

  /// Handle notification click
  void handleNotificationClick(OSNotificationClickEvent event) {
    final additionalData = event.notification.additionalData;
    
    if (kDebugMode) {
      print('🔔 Handling notification click with data: $additionalData');
    }
    
         // Handle deep linking based on notification data
     if (additionalData != null) {
       final type = additionalData['type'] as String?;
       
       switch (type) {
        case 'pin_nearby':
          // Navigate to map with specific pin
          break;
        case 'friend_activity':
          // Navigate to friends screen
          break;
        case 'music_recommendation':
          // Navigate to music screen
          break;
        default:
          // Default action
          break;
      }
    }
  }

  /// Refresh state
  Future<void> refresh() async {
    _updateState();
    notifyListeners();
  }
} 