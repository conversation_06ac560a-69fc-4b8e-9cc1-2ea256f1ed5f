import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/music_track.dart';
import 'spotify_provider.dart';

class NowPlayingProvider with ChangeNotifier {
  // State
  bool _isVisible = false;
  bool _isExpanded = false;
  bool _isDragging = false;
  double _dragStartY = 0;
  double _dragUpdateY = 0;
  double _expandProgress = 0;
  
  // Getters
  bool get isVisible => _isVisible;
  bool get isExpanded => _isExpanded;
  bool get isDragging => _isDragging;
  double get expandProgress => _expandProgress;
  
  // Methods
  void setVisibility(bool visible) {
    if (_isVisible != visible) {
      _isVisible = visible;
      notifyListeners();
    }
  }
  
  void setExpanded(bool expanded) {
    if (_isExpanded != expanded) {
      _isExpanded = expanded;
      notifyListeners();
    }
  }
  
  void updateExpandProgress(double progress) {
    _expandProgress = progress;
    notifyListeners();
  }
  
  void startDrag(double startY) {
    _isDragging = true;
    _dragStartY = startY;
    notifyListeners();
  }
  
  void updateDrag(double updateY) {
    _dragUpdateY = updateY;
    notifyListeners();
  }
  
  void endDrag(double velocity) {
    _isDragging = false;
    final dragDistance = _dragUpdateY - _dragStartY;
    
    if (!_isExpanded && (dragDistance < -50 || velocity < -500)) {
      setExpanded(true);
    } else if (_isExpanded && (dragDistance > 50 || velocity > 500)) {
      setExpanded(false);
    }
    
    notifyListeners();
  }
  
  void toggleExpanded() {
    setExpanded(!_isExpanded);
  }
  
  // Static provider access helper
  static NowPlayingProvider of(BuildContext context) {
    return Provider.of<NowPlayingProvider>(context, listen: false);
  }
} 