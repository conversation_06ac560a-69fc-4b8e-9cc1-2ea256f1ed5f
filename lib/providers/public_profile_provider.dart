import 'package:flutter/foundation.dart';
import '../models/public_user_profile.dart';
import '../models/pin.dart';
import '../models/collection_model.dart';
import '../models/friendship_status.dart';
import '../models/user.dart';
import '../services/public_profile_service.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../services/api/pins_service.dart';
import 'dart:async';

class PublicProfileProvider extends ChangeNotifier {
  late final PublicProfileService _profileService;
  late final PinsService _pinsService;
  
  // State
  PublicUserProfile? _currentProfile;
  List<Pin> _userPins = [];
  List<Collection> _userCollections = [];
  List<PublicUserProfile> _searchResults = [];
  FriendshipStatus _friendshipStatus = FriendshipStatus.none;
  String? _currentRequestId; // Store the request ID for pending requests
  bool _isLoading = false;
  bool _isSearching = false;
  bool _isLoadingPins = false;
  bool _isLoadingCollections = false;
  bool _isLoadingFriendshipStatus = false;
  String? _error;
  String _currentSearchQuery = '';
  Timer? _searchDebounceTimer;
  
  // Getters
  PublicUserProfile? get currentProfile => _currentProfile;
  List<Pin> get userPins => _userPins;
  List<Collection> get userCollections => _userCollections;
  List<PublicUserProfile> get searchResults => _searchResults;
  FriendshipStatus get friendshipStatus => _friendshipStatus;
  String? get currentRequestId => _currentRequestId;
  bool get isLoading => _isLoading;
  bool get isSearching => _isSearching;
  bool get isLoadingPins => _isLoadingPins;
  bool get isLoadingCollections => _isLoadingCollections;
  bool get isLoadingFriendshipStatus => _isLoadingFriendshipStatus;
  String? get error => _error;
  String get currentSearchQuery => _currentSearchQuery;
  
  PublicProfileProvider() {
    _initializeService();
  }
  
  void _initializeService() {
    final apiService = ApiService();
    final authService = AuthService(apiService);
    _profileService = PublicProfileService(apiService, authService);
    _pinsService = PinsService();
  }
  
  // Load user profile
  Future<void> loadUserProfile(int userId) async {
    _setLoading(true);
    _clearError();
    
    try {
      // Try to use real API service to load profile
      final apiProfile = await _profileService.getPublicProfile(userId);
      
      if (apiProfile != null) {
        _currentProfile = apiProfile;
        
        // Load friendship status with request ID
        try {
          print('🤝 Loading friendship status for user $userId');
          await _loadFriendshipStatusWithRequestId(userId);
          print('✅ Friendship status loaded successfully: $_friendshipStatus');
        } catch (e) {
          print('❌ Error loading friendship status: $e');
          // Don't set a default status on error - let the error propagate
          rethrow;
        }
        
        // Load user's pins and collections
        try {
          print('📱 Starting to load user pins...');
          await loadUserPins(userId);
          print('✅ Finished loading user pins. Current pins count: ${_userPins.length}');
        } catch (e) {
          print('⚠️ Could not load user pins: $e');
          print('🔄 Setting empty pins array as fallback');
          _userPins = [];
        }
        
        try {
          await loadUserCollections(userId);
        } catch (e) {
          print('⚠️ Could not load user collections: $e');
          _userCollections = [];
        }
        
        notifyListeners();
      } else {
        throw Exception('User profile not found in API response');
      }
    } catch (e) {
      print('❌ Error loading user data: $e');
      _setError('Failed to load user profile: $e');
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }
  
  // Mock data for development
  PublicUserProfile _getMockProfile(int userId) {
    return PublicUserProfile(
      id: userId,
      username: 'user_$userId',
      bio: 'This is a mock profile for user $userId',
      profilePicUrl: null,
      isVerified: false,
      location: null,
      lastActive: DateTime.now().subtract(const Duration(hours: 2)),
      pinCount: 5,
      publicCollectionCount: 2,
    );
  }
  
  List<Pin> _getMockPins() {
    // Provide some mock pins for testing
    return [
      Pin(
        id: 1,
        owner: User.anonymous(),
        location: {
          'type': 'Point',
          'coordinates': [-74.006, 40.7128] // NYC coordinates
        },
        title: 'Mock Pin 1',
        description: 'This is a mock pin for testing',
        caption: 'Test pin',
        locationName: 'New York, NY',
        trackTitle: 'Test Song',
        trackArtist: 'Test Artist',
        album: 'Test Album',
        trackUrl: 'https://open.spotify.com/track/test',
        service: 'spotify',
        artworkUrl: null,
        durationMs: 180000,
        skin: 1,
        skinDetails: null,
        rarity: 'common',
        auraRadius: 100.0,
        isPrivate: false,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        interactionCount: {
          'view': 5,
          'like': 2,
          'collect': 1,
          'share': 0
        },
      ),
      Pin(
        id: 2,
        owner: User.anonymous(),
        location: {
          'type': 'Point',
          'coordinates': [-118.2437, 34.0522] // LA coordinates
        },
        title: 'Mock Pin 2',
        description: 'Another mock pin',
        caption: 'Another test',
        locationName: 'Los Angeles, CA',
        trackTitle: 'Another Song',
        trackArtist: 'Another Artist',
        album: 'Another Album',
        trackUrl: 'https://open.spotify.com/track/test2',
        service: 'spotify',
        artworkUrl: null,
        durationMs: 200000,
        skin: 2,
        skinDetails: null,
        rarity: 'rare',
        auraRadius: 150.0,
        isPrivate: false,
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 6)),
        interactionCount: {
          'view': 12,
          'like': 8,
          'collect': 3,
          'share': 1
        },
      ),
    ];
  }
  
  List<Collection> _getMockCollections() {
    return [
      // Mock collections would go here
    ];
  }
  
  // Load user pins
  Future<void> loadUserPins(int userId) async {
    print('🔍 Loading user pins for userId: $userId');
    _setLoadingPins(true);
    _clearError();
    
    try {
      // Use the working PinsService.getFriendPins() method
      _userPins = await _pinsService.getFriendPins(userId.toString());
      print('📍 Successfully loaded ${_userPins.length} user pins from PinsService');
      notifyListeners();
    } catch (e) {
      print('❌ Error loading user pins: $e');
      print('🔄 Falling back to mock pins');
      _userPins = _getMockPins();
      _setError('Failed to load user pins: ${e.toString()}');
      notifyListeners();
    } finally {
      _setLoadingPins(false);
    }
  }
  
  // Load user collections
  Future<void> loadUserCollections(int userId) async {
    _setLoadingCollections(true);
    _clearError();
    
    try {
      _userCollections = await _profileService.getUserPublicCollections(userId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load user collections: ${e.toString()}');
    } finally {
      _setLoadingCollections(false);
    }
  }
  
  // Load friendship status with request ID
  Future<void> _loadFriendshipStatusWithRequestId(int userId) async {
    _setLoadingFriendshipStatus(true);
    
    try {
      final result = await _profileService.getFriendshipStatusWithRequestId(userId);
      final status = result['status'] as String;
      _currentRequestId = result['request_id'] as String?;
      
      switch (status) {
        case 'friends':
          _friendshipStatus = FriendshipStatus.friends;
          break;
        case 'pending_sent':
          _friendshipStatus = FriendshipStatus.pendingSent;
          break;
        case 'pending_received':
          _friendshipStatus = FriendshipStatus.pendingReceived;
          break;
        default:
          _friendshipStatus = FriendshipStatus.none;
      }
      
      notifyListeners();
    } catch (e) {
      print('Failed to load friendship status: $e');
      // Don't set error for friendship status as it's not critical
    } finally {
      _setLoadingFriendshipStatus(false);
    }
  }
  
  // Load friendship status (legacy method)
  Future<void> _loadFriendshipStatus(int userId) async {
    _setLoadingFriendshipStatus(true);
    
    try {
      _friendshipStatus = await _profileService.getFriendshipStatus(userId);
      notifyListeners();
    } catch (e) {
      print('Failed to load friendship status: $e');
      // Don't set error for friendship status as it's not critical
    } finally {
      _setLoadingFriendshipStatus(false);
    }
  }
  
  // Search users with debouncing
  void searchUsers(String query) {
    _currentSearchQuery = query;
    _searchDebounceTimer?.cancel();
    
    final trimmed = query.trim();
    if (trimmed.length < 2) {
      _searchResults = [];
      _isSearching = false;
      notifyListeners();
      return;
    }
    
    _setSearching(true);
    _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query);
    });
  }
  
  // Perform the actual search
  Future<void> _performSearch(String query) async {
    _setSearching(true);
    _clearError();
    
    try {
      _searchResults = await _profileService.searchUsers(query);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search users: ${e.toString()}');
    } finally {
      _setSearching(false);
    }
  }
  
  // Clear search results
  void clearSearch() {
    _currentSearchQuery = '';
    _searchResults = [];
    _isSearching = false;
    _searchDebounceTimer?.cancel();
    notifyListeners();
  }
  
  // Send friend request
  Future<bool> sendFriendRequest(int userId) async {
    _clearError();
    
    try {
      final success = await _profileService.sendFriendRequest(userId);
      if (success) {
        _friendshipStatus = FriendshipStatus.pendingSent;
        // Reload to get the request ID
        await _loadFriendshipStatusWithRequestId(userId);
      }
      return success;
    } catch (e) {
      _setError('Failed to send friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Accept friend request - now uses the stored request ID
  Future<bool> acceptFriendRequest() async {
    if (_currentRequestId == null) {
      _setError('No friend request found to accept');
      return false;
    }
    
    _clearError();
    
    try {
      final success = await _profileService.acceptFriendRequest(_currentRequestId!);
      if (success) {
        _friendshipStatus = FriendshipStatus.friends;
        _currentRequestId = null;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('Failed to accept friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Accept friend request by request ID (legacy method for compatibility)
  Future<bool> acceptFriendRequestById(String requestId) async {
    _clearError();
    
    try {
      final success = await _profileService.acceptFriendRequest(requestId);
      if (success) {
        _friendshipStatus = FriendshipStatus.friends;
        _currentRequestId = null;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('Failed to accept friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Reject friend request
  Future<bool> rejectFriendRequest() async {
    if (_currentRequestId == null) {
      _setError('No friend request found to reject');
      return false;
    }
    
    _clearError();
    
    try {
      final success = await _profileService.rejectFriendRequest(_currentRequestId!);
      if (success) {
        _friendshipStatus = FriendshipStatus.none;
        _currentRequestId = null;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('Failed to reject friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Reject friend request by request ID (legacy method for compatibility)
  Future<bool> rejectFriendRequestById(String requestId) async {
    _clearError();
    
    try {
      final success = await _profileService.rejectFriendRequest(requestId);
      if (success) {
        _friendshipStatus = FriendshipStatus.none;
        _currentRequestId = null;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('Failed to reject friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Cancel friend request
  Future<bool> cancelFriendRequest() async {
    if (_currentRequestId == null) {
      _setError('No friend request found to cancel');
      return false;
    }
    
    _clearError();
    
    try {
      final success = await _profileService.cancelFriendRequest(_currentRequestId!);
      if (success) {
        _friendshipStatus = FriendshipStatus.none;
        _currentRequestId = null;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('Failed to cancel friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Cancel friend request by request ID (legacy method for compatibility)
  Future<bool> cancelFriendRequestById(String requestId) async {
    _clearError();
    
    try {
      final success = await _profileService.cancelFriendRequest(requestId);
      if (success) {
        _friendshipStatus = FriendshipStatus.none;
        _currentRequestId = null;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('Failed to cancel friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Unfriend user
  Future<bool> unfriend(int userId) async {
    _clearError();
    
    try {
      final success = await _profileService.unfriend(userId);
      if (success) {
        _friendshipStatus = FriendshipStatus.none;
        _currentRequestId = null;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('Failed to unfriend user: ${e.toString()}');
      return false;
    }
  }
  
  // Clear current profile data
  void clearProfile() {
    _currentProfile = null;
    _userPins = [];
    _userCollections = [];
    _friendshipStatus = FriendshipStatus.none;
    _currentRequestId = null;
    _clearError();
    notifyListeners();
  }
  
  // Private helper methods
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }
  
  void _setSearching(bool value) {
    _isSearching = value;
    notifyListeners();
  }
  
  void _setLoadingPins(bool value) {
    _isLoadingPins = value;
    notifyListeners();
  }
  
  void _setLoadingCollections(bool value) {
    _isLoadingCollections = value;
    notifyListeners();
  }
  
  void _setLoadingFriendshipStatus(bool value) {
    _isLoadingFriendshipStatus = value;
    notifyListeners();
  }
  
  void _setError(String message) {
    _error = message;
    notifyListeners();
  }
  
  void _clearError() {
    _error = null;
  }
  
  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }
} 