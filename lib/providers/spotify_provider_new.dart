import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/music/spotify_web_api_service.dart';
import '../models/music_track.dart';
import '../models/music/track.dart';
import '../services/secure_storage.dart';
import '../config/constants.dart';

/// SpotifyProvider manages the state of Spotify playback using Web API
class SpotifyProvider with ChangeNotifier, WidgetsBindingObserver {
  final SpotifyWebApiService _webApiService = SpotifyWebApiService();
  final SecureStorage _secureStorage = SecureStorage();
  
  // State
  bool _isInitialized = false;
  bool _isConnected = false;
  bool _isConnecting = false;
  bool _isPlaying = false;
  bool _isPaused = true;
  MusicTrack? _currentTrack;
  int _position = 0;
  int _duration = 0;
  bool _hasActivePlayback = false;
  
  // User data
  Map<String, dynamic>? _userProfile;
  Map<String, dynamic>? _userStats;
  bool _isLoadingProfile = false;
  bool _isLoadingStats = false;
  
  // Error handling
  String? _errorMessage;
  bool _isPremiumUser = false;
  
  // Position update timer
  Timer? _positionTimer;
  final _positionController = StreamController<int>.broadcast();
  
  // Liked songs and playlists
  List<MusicTrack> _likedSongs = [];
  List<Map<String, dynamic>> _playlists = [];
  bool _isLoadingLikedSongs = false;
  bool _isLoadingPlaylists = false;
  int _likedSongsPage = 0;
  bool _hasMoreLikedSongs = true;
  int _totalLikedSongs = 0;
  
  // For caching
  final Map<String, List<MusicTrack>> _playlistTracksCache = {};
  DateTime _lastLikedSongsUpdate = DateTime(1970);
  final Duration _likedSongsCacheExpiry = const Duration(minutes: 30);
  final int _likedSongsPageSize = 50;
  
  // Top artists cache
  Map<String, List<Map<String, dynamic>>>? _topArtistsCache;
  DateTime? _topArtistsCacheTime;
  final Duration _topArtistsCacheExpiry = const Duration(minutes: 15);
  
  // Recent tracks
  List<Track>? _recentTracks;
  
  // App lifecycle tracking
  DateTime? _lastReconnectAttempt;
  
  // Playback state persistence
  DateTime? _lastPlaybackCheck;
  final Duration _playbackCheckInterval = const Duration(milliseconds: 1000);
  
  // Enhanced position tracking
  Timer? _fastPositionTimer;
  DateTime? _lastPositionUpdate;
  int _estimatedPosition = 0;
  
  // Playback state
  bool _shuffleEnabled = false;
  String _repeatMode = 'off'; // 'off', 'track', or 'context'
  
  // State checking
  Timer? _stateCheckTimer;
  bool _isCheckingState = false;
  
  // Playlist pagination state
  final Map<String, int> _playlistPages = {};
  final Map<String, bool> _hasMorePlaylistTracks = {};
  final Map<String, int> _totalPlaylistTracks = {};
  final Map<String, bool> _isLoadingPlaylistTracks = {};
  final int _playlistPageSize = 50;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isConnected => _isConnected;
  bool get isConnecting => _isConnecting;
  bool get isPlaying => _isPlaying;
  bool get isPaused => _isPaused;
  bool get hasActivePlayback => _hasActivePlayback;
  bool get isPremiumUser => _isPremiumUser;
  bool get isPremium => _isPremiumUser;
  MusicTrack? get currentTrack => _currentTrack;
  int get position => _position;
  int get duration => _duration;
  String? get errorMessage => _errorMessage;
  List<MusicTrack> get likedSongs => _likedSongs;
  List<Map<String, dynamic>> get playlists => _playlists;
  bool get isLoadingLikedSongs => _isLoadingLikedSongs;
  bool get isLoadingPlaylists => _isLoadingPlaylists;
  bool get hasMoreLikedSongs => _hasMoreLikedSongs;
  Map<String, dynamic>? get userProfile => _userProfile;
  Map<String, dynamic>? get userStats => _userStats;
  bool get isLoadingProfile => _isLoadingProfile;
  bool get isLoadingStats => _isLoadingStats;
  List<Track>? get recentTracks => _recentTracks;
  Map<String, List<Map<String, dynamic>>>? get topArtistsCache => _topArtistsCache;
  
  // Stream getters
  Stream<int> get positionStream => _positionController.stream;

  // Properties for auth mode control (no-op for Web API)
  bool get prioritizeProductionAuth => false;
  set prioritizeProductionAuth(bool value) {
    // No-op for Web API
  }

  void toggleProductionAuthPriority() {
    // No-op for Web API
  }
  
  SpotifyProvider() {
    WidgetsBinding.instance.addObserver(this);
    _init();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (kDebugMode) {
      print('🎵 Spotify lifecycle state changed: $state');
    }

    if (state == AppLifecycleState.resumed) {
      final now = DateTime.now();
      if (_isConnecting) {
        if (kDebugMode) print('🔄 Already connecting, skipping...');
        return;
      }
      if (_lastReconnectAttempt != null &&
          now.difference(_lastReconnectAttempt!) < Duration(seconds: 5)) {
        if (kDebugMode) print('⏳ Debounced reconnect, skipping...');
        return;
      }
      _lastReconnectAttempt = now;
      _handleAppResume();
    } else if (state == AppLifecycleState.paused) {
      _savePlaybackState();
    } else if (state == AppLifecycleState.detached) {
      _cleanupResources();
    }
  }
  
  /// Handle app resume
  Future<void> _handleAppResume() async {
    if (kDebugMode) {
      print('🎵 Handling app resume');
    }
    
    try {
      await Future.delayed(const Duration(milliseconds: 1000));
      
      if (_isConnected) {
        if (kDebugMode) {
          print('✅ Already connected to Spotify, skipping reconnection');
        }
        return;
      }
      
      if (await _webApiService.isAuthenticated()) {
        final success = await connect();
        
        if (success) {
          if (kDebugMode) {
            print('✅ Successfully reconnected to Spotify');
          }
          return;
        }
      }
      
      if (kDebugMode) {
        print('⚠️ Could not reconnect automatically, user may need to re-authenticate');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling app resume: $e');
      }
    }
  }
  
  /// Clean up resources when app is detached
  Future<void> _cleanupResources() async {
    _stopPositionTimer();
    _stopStateChecking();
    await _savePlaybackState();
  }
  
  /// Save current playback state
  Future<void> _savePlaybackState() async {
    try {
      if (!_isConnected || !_hasActivePlayback) return;
      
      final playbackState = await _webApiService.getCurrentPlayback();
      if (playbackState != null && playbackState['item'] != null) {
        final track = playbackState['item'];
        await _secureStorage.write(
          key: 'last_track_uri',
          value: track['uri'],
        );
        await _secureStorage.write(
          key: 'was_playing',
          value: (playbackState['is_playing'] ?? false).toString(),
        );
        await _secureStorage.write(
          key: 'last_position',
          value: (playbackState['progress_ms'] ?? 0).toString(),
        );
        
        if (kDebugMode) {
          print('✅ Saved playback state: ${track['name']}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error saving playback state: $e');
      }
    }
  }
  
  /// Initialize
  Future<void> _init() async {
    if (_isInitialized) return;
    
    try {
      _startStateChecking();
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error in _init: $e');
      }
    }
  }
  
  /// Handle errors
  void _handleError(String error) {
    _errorMessage = error;
    
    if (error.toLowerCase().contains('token') || 
        error.toLowerCase().contains('auth') ||
        error.toLowerCase().contains('unauthorized')) {
      _isConnected = false;
    }
    
    notifyListeners();
  }

  /// Load user data
  Future<void> _loadUserData() async {
    if (!_isConnected) return;

    try {
      if (_userProfile == null) {
        await loadUserProfile();
      }

      if (_lastLikedSongsUpdate.difference(DateTime.now()).inHours.abs() > 1) {
        await loadLikedSongs();
      }

      if (_playlists.isEmpty) {
        await loadPlaylists();
      }
    } catch (e) {
      _handleError('Error loading user data: $e');
    }
  }

  /// Connect to Spotify using Web API
  Future<bool> connect() async {
    try {
      if (_isConnecting) {
        if (kDebugMode) {
          print('🔄 [SpotifyProvider] Connection already in progress, skipping duplicate attempt');
        }
        return true;
      }

      if (kDebugMode) {
        print('🎵 [SpotifyProvider] Starting Spotify Web API connection');
      }
      
      _isConnecting = true;
      _errorMessage = null;
      notifyListeners();

      final isAuthenticated = await _webApiService.isAuthenticated();
      if (isAuthenticated) {
        if (kDebugMode) {
          print('✅ [SpotifyProvider] Already authenticated with Web API');
        }
        
        _isConnected = true;
        _isInitialized = true;
        
        await loadUserProfile();
        _startStateChecking();
        
        _isConnecting = false;
        notifyListeners();
        return true;
      }

      if (kDebugMode) {
        print('🔐 [SpotifyProvider] No valid token found, need new authentication');
      }
      
      _isConnecting = false;
      return false;

    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyProvider] Connection error: $e');
      }
      _isConnecting = false;
      _handleError('Error connecting to Spotify: $e');
      return false;
    }
  }

  /// Disconnect from Spotify
  Future<void> disconnect() async {
    try {
      await _webApiService.disconnect();
      _isConnected = false;
      _isConnecting = false;
      _isInitialized = false;
      _currentTrack = null;
      _isPlaying = false;
      _isPaused = true;
      _userProfile = null;
      _likedSongs = [];
      _playlists = [];
      _stopPositionTimer();
      _stopStateChecking();
      notifyListeners();
    } catch (e) {
      _handleError('Error disconnecting from Spotify: $e');
    }
  }

  /// Play a track using Web API
  Future<bool> playTrack(MusicTrack track) async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyProvider] Attempting to play track: ${track.uri}');
      }
      
      if (!_isConnected) {
        if (kDebugMode) {
          print('⚠️ [SpotifyProvider] Not connected to Spotify, attempting to connect');
        }
        
        final connected = await connect();
        if (!connected) {
          throw Exception('Failed to connect to Spotify Web API');
        }
      }

      // Check if user has premium (required for Web API playback)
      if (!_isPremiumUser) {
        throw Exception('Spotify Premium is required for playback control');
      }

      final success = await _webApiService.playTrackWithRecommendations(track.uri);
      if (success) {
        if (kDebugMode) {
          print('✅ [SpotifyProvider] Track playback initiated successfully');
        }
        
        _position = 0;
        _estimatedPosition = 0;
        _lastPositionUpdate = DateTime.now();
        _currentTrack = track;
        _isPlaying = true;
        _hasActivePlayback = true;
        _startIntensivePlaybackCheck();
        notifyListeners();
        return true;
      } else {
        throw Exception('Failed to start playback');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyProvider] Error playing track: $e');
      }
      
      final userFriendlyError = getSpotifyErrorMessage(e.toString());
      _handleError(userFriendlyError);
      return false;
    }
  }

  /// Start intensive playback state checking temporarily
  void _startIntensivePlaybackCheck() {
    int checkCount = 0;
    Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (checkCount >= 25) {
        timer.cancel();
        return;
      }
      _checkPlaybackState();
      checkCount++;
    });
  }

  /// Resume playback
  Future<bool> resume() async {
    try {
      if (!_isPremiumUser) {
        _errorMessage = 'Premium account required for playback';
        notifyListeners();
        return false;
      }
      
      if (!_isConnected || !_hasActivePlayback) {
        final connected = await _reestablishPlayback();
        if (!connected) return false;
      }
      
      final success = await _webApiService.startPlayback();
      if (success) {
        _isPlaying = true;
        _lastPositionUpdate = DateTime.now();
        _startPositionTimer();
        _startIntensivePlaybackCheck();
        notifyListeners();
        
        if (kDebugMode) {
          print('✅ Successfully resumed playback');
        }
      }
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error resuming playback: $e');
      }
      _handleError('Error resuming playback: $e');
      return false;
    }
  }

  /// Reestablish playback if connection was lost
  Future<bool> _reestablishPlayback() async {
    try {
      if (!_isConnected) {
        final connected = await connect();
        if (!connected) return false;
      }
      
      if (_currentTrack != null) {
        await _webApiService.playTrackWithRecommendations(_currentTrack!.uri);
        await _webApiService.seekToPosition(_position);
        return true;
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error reestablishing playback: $e');
      }
      return false;
    }
  }

  /// Pause playback
  Future<bool> pause() async {
    try {
      if (!_isConnected || !_hasActivePlayback) {
        _isPlaying = false;
        notifyListeners();
        return true;
      }
      
      final success = await _webApiService.pausePlayback();
      if (success) {
        _isPlaying = false;
        _lastPositionUpdate = null;
        _stopPositionTimer();
        
        if (kDebugMode) {
          print('✅ Successfully paused playback');
        }
        
        notifyListeners();
      }
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error pausing playback: $e');
      }
      _handleError('Error pausing playback: $e');
      return false;
    }
  }

  /// Skip to next track
  Future<bool> skipNext() async {
    try {
      if (!_isPremiumUser) {
        _errorMessage = 'Premium account required for playback control';
        notifyListeners();
        return false;
      }
      
      if (!_isConnected || !_hasActivePlayback) {
        return false;
      }
      
      final success = await _webApiService.skipToNext();
      if (success) {
        _startIntensivePlaybackCheck();
      }
      return success;
    } catch (e) {
      _handleError('Error skipping to next track: $e');
      return false;
    }
  }

  /// Skip to previous track
  Future<bool> skipPrevious() async {
    try {
      if (!_isPremiumUser) {
        _errorMessage = 'Premium account required for playback control';
        notifyListeners();
        return false;
      }
      
      if (!_isConnected || !_hasActivePlayback) {
        return false;
      }
      
      final success = await _webApiService.skipToPrevious();
      if (success) {
        _startIntensivePlaybackCheck();
      }
      return success;
    } catch (e) {
      _handleError('Error skipping to previous track: $e');
      return false;
    }
  }
  
  /// Load user's liked songs with pagination
  Future<void> loadLikedSongs({bool refresh = false}) async {
    if (_isLoadingLikedSongs) return;
    
    if (!refresh && _likedSongs.isNotEmpty) {
      final now = DateTime.now();
      if (now.difference(_lastLikedSongsUpdate) < _likedSongsCacheExpiry) {
        if (kDebugMode) {
          print('✅ Using cached liked songs (${_likedSongs.length} of $_totalLikedSongs tracks)');
        }
        return;
      }
    }
    
    _isLoadingLikedSongs = true;
    _likedSongsPage = 0;
    
    if (refresh) {
      _likedSongs = [];
      _hasMoreLikedSongs = true;
      _totalLikedSongs = 0;
    }
    
    notifyListeners();
    
    try {
      final result = await _webApiService.getSavedTracks(
        limit: _likedSongsPageSize,
        offset: _likedSongsPage * _likedSongsPageSize,
      );
      
      _likedSongs = result['tracks'];
      _totalLikedSongs = result['total'];
      _hasMoreLikedSongs = result['hasMore'];
      _likedSongsPage++;
      _lastLikedSongsUpdate = DateTime.now();
      
      if (kDebugMode) {
        print('✅ Loaded ${_likedSongs.length} liked songs');
      }
      
      _isLoadingLikedSongs = false;
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading liked songs: $e');
      }
      _isLoadingLikedSongs = false;
      _hasMoreLikedSongs = false;
      notifyListeners();
      rethrow;
    }
  }
  
  /// Load more liked songs for pagination
  Future<void> loadMoreLikedSongs() async {
    if (_isLoadingLikedSongs || !_hasMoreLikedSongs) return;
    
    _isLoadingLikedSongs = true;
    notifyListeners();
    
    try {
      final result = await _webApiService.getSavedTracks(
        limit: _likedSongsPageSize,
        offset: _likedSongsPage * _likedSongsPageSize,
      );
      
      final newTracks = result['tracks'] as List<MusicTrack>;
      
      if (newTracks.isEmpty) {
        _hasMoreLikedSongs = false;
      } else {
        _likedSongs.addAll(newTracks);
        _hasMoreLikedSongs = result['hasMore'];
        _likedSongsPage++;
        
        if (kDebugMode) {
          print('✅ Loaded ${newTracks.length} more liked songs (total: ${_likedSongs.length})');
        }
      }
      
      _isLoadingLikedSongs = false;
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading more liked songs: $e');
      }
      _isLoadingLikedSongs = false;
      _hasMoreLikedSongs = false;
      notifyListeners();
      rethrow;
    }
  }
  
  /// Load user's playlists
  Future<void> loadPlaylists({bool refresh = false}) async {
    if (!_isConnected) {
      final connected = await connect();
      if (!connected) return;
    }
    
    try {
      _isLoadingPlaylists = true;
      if (refresh) {
        _playlists = [];
      }
      
      notifyListeners();
      
      final playlists = await _webApiService.getUserPlaylists();
      _playlists = playlists;
      _isLoadingPlaylists = false;
      notifyListeners();
    } catch (e) {
      _handleError('Error loading playlists: $e');
      _isLoadingPlaylists = false;
      notifyListeners();
    }
  }
  
  /// Load tracks from a specific playlist
  Future<List<MusicTrack>> getPlaylistTracks(String playlistId) async {
    if (!_isConnected) {
      final connected = await connect();
      if (!connected) return [];
    }
    
    try {
      _playlistPages[playlistId] ??= 0;
      _hasMorePlaylistTracks[playlistId] ??= true;
      _isLoadingPlaylistTracks[playlistId] ??= false;
      
      if (_playlistTracksCache.containsKey(playlistId)) {
        return _playlistTracksCache[playlistId]!;
      }
      
      return loadMorePlaylistTracks(playlistId);
    } catch (e) {
      _handleError('Error loading playlist tracks: $e');
      notifyListeners();
      return [];
    }
  }
  
  /// Load more tracks for a specific playlist
  Future<List<MusicTrack>> loadMorePlaylistTracks(String playlistId) async {
    if (_isLoadingPlaylistTracks[playlistId] == true || 
        _hasMorePlaylistTracks[playlistId] == false) {
      return _playlistTracksCache[playlistId] ?? [];
    }
    
    _isLoadingPlaylistTracks[playlistId] = true;
    notifyListeners();
    
    try {
      final result = await _webApiService.getPlaylistTracks(
        playlistId,
        limit: _playlistPageSize,
        offset: (_playlistPages[playlistId] ?? 0) * _playlistPageSize,
      );
      
      final newTracks = result['tracks'] as List<MusicTrack>;
      
      if (!_playlistTracksCache.containsKey(playlistId)) {
        _playlistTracksCache[playlistId] = [];
      }
      
      if (newTracks.isEmpty) {
        _hasMorePlaylistTracks[playlistId] = false;
      } else {
        _playlistTracksCache[playlistId]!.addAll(newTracks);
        _hasMorePlaylistTracks[playlistId] = result['hasMore'];
        _playlistPages[playlistId] = (_playlistPages[playlistId] ?? 0) + 1;
        _totalPlaylistTracks[playlistId] = result['total'];
        
        if (kDebugMode) {
          print('✅ Loaded ${newTracks.length} more tracks for playlist $playlistId');
        }
      }
      
      _isLoadingPlaylistTracks[playlistId] = false;
      notifyListeners();
      return _playlistTracksCache[playlistId] ?? [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading more playlist tracks: $e');
      }
      _isLoadingPlaylistTracks[playlistId] = false;
      _hasMorePlaylistTracks[playlistId] = false;
      notifyListeners();
      return _playlistTracksCache[playlistId] ?? [];
    }
  }
  
  /// Seek to position
  Future<bool> seekTo(int positionMs) async {
    try {
      if (!_isPremiumUser) {
        _errorMessage = 'Premium account required for playback control';
        notifyListeners();
        return false;
      }
      
      if (!_isConnected || !_hasActivePlayback) {
        return false;
      }
      
      final success = await _webApiService.seekToPosition(positionMs);
      if (success) {
        _position = positionMs;
        _estimatedPosition = positionMs;
        _lastPositionUpdate = DateTime.now();
        notifyListeners();
      }
      return success;
    } catch (e) {
      _handleError('Error seeking: $e');
      return false;
    }
  }
  
  /// Toggle shuffle mode
  Future<bool> toggleShuffle() async {
    try {
      final newState = !_shuffleEnabled;
      final success = await _webApiService.setShuffle(newState);
      if (success) {
        _shuffleEnabled = newState;
        notifyListeners();
      }
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('Error toggling shuffle: $e');
      }
      return false;
    }
  }
  
  /// Set repeat mode
  Future<bool> setRepeatMode(String repeatMode) async {
    try {
      final success = await _webApiService.setRepeatMode(repeatMode);
      if (success) {
        _repeatMode = repeatMode;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _handleError('Error setting repeat mode: $e');
      return false;
    }
  }
  
  /// Search for tracks
  Future<List<MusicTrack>> searchTracks(String query) async {
    try {
      if (query.isEmpty) {
        return [];
      }
      
      final results = await _webApiService.searchTracks(query);
      return results;
    } catch (e) {
      _handleError('Error searching tracks: $e');
      notifyListeners();
      return [];
    }
  }
  
  /// Get user's recently played tracks
  Future<List<MusicTrack>> getRecentlyPlayed({int limit = 20}) async {
    try {
      final result = await _webApiService.getRecentlyPlayed(limit: limit);
      return result['tracks'] as List<MusicTrack>;
    } catch (e) {
      _handleError('Error getting recently played tracks: $e');
      notifyListeners();
      return [];
    }
  }
  
  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  /// Start timer to update position periodically
  void _startPositionTimer() {
    _stopPositionTimer();
    
    _fastPositionTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (_isPlaying && _lastPositionUpdate != null) {
        final elapsed = DateTime.now().difference(_lastPositionUpdate!).inMilliseconds;
        _estimatedPosition = _position + elapsed;
        
        if (_duration > 0 && _estimatedPosition > _duration) {
          _estimatedPosition = _duration;
        }
        
        _positionController.add(_estimatedPosition);
        notifyListeners();
      }
    });
    
    _positionTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_isPlaying && _isConnected && _hasActivePlayback) {
        try {
          final playbackState = await _webApiService.getCurrentPlayback();
          if (playbackState != null) {
            final serverPosition = playbackState['progress_ms'] ?? 0;
            
            if ((_estimatedPosition - serverPosition).abs() < 1000) {
              _position = (_position * 0.7 + serverPosition * 0.3).round();
            } else {
              _position = serverPosition;
            }
            
            _estimatedPosition = _position;
            _lastPositionUpdate = DateTime.now();
            
            _positionController.add(_estimatedPosition);
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Error getting server position: $e');
          }
        }
      }
    });
  }
  
  /// Stop position update timer
  void _stopPositionTimer() {
    _positionTimer?.cancel();
    _positionTimer = null;
    _fastPositionTimer?.cancel();
    _fastPositionTimer = null;
  }
  
  /// Load user profile
  Future<Map<String, dynamic>?> loadUserProfile() async {
    if (!_isConnected) {
      final connected = await connect();
      if (!connected) return null;
    }
    
    try {
      _isLoadingProfile = true;
      notifyListeners();
      
      final profile = await _webApiService.getCurrentUserProfile();
      _userProfile = profile;
      
      if (profile != null && profile.containsKey('product')) {
        _isPremiumUser = profile['product'] == 'premium';
      }
      
      _isLoadingProfile = false;
      notifyListeners();
      return profile;
    } catch (e) {
      _handleError('Error loading user profile: $e');
      _isLoadingProfile = false;
      notifyListeners();
      return null;
    }
  }
  
  /// Load user stats
  Future<Map<String, dynamic>?> loadUserStats() async {
    if (!_isConnected) {
      final connected = await connect();
      if (!connected) return null;
    }
    
    try {
      _isLoadingStats = true;
      notifyListeners();
      
      // For Web API, we can get top artists/tracks as "stats"
      final topTracks = await _webApiService.getTopTracks();
      final topArtists = await _webApiService.getTopArtists();
      
      _userStats = {
        'top_tracks': topTracks,
        'top_artists': topArtists,
      };
      
      _isLoadingStats = false;
      notifyListeners();
      return _userStats;
    } catch (e) {
      _handleError('Error loading user stats: $e');
      _isLoadingStats = false;
      notifyListeners();
      return null;
    }
  }
  
  /// Check if using debug tokens (not applicable for Web API)
  Future<bool> isUsingDebugTokens() async {
    return false;
  }
  
  /// Get connection type label
  Future<String> getConnectionTypeLabel() async {
    return 'Using Spotify Web API';
  }
  
  void setCurrentTrack(MusicTrack track) {
    _currentTrack = track;
    notifyListeners();
  }

  void setIsPlaying(bool playing) {
    _isPlaying = playing;
    notifyListeners();
  }

  void setRecentTracks(List<Track> tracks) {
    _recentTracks = tracks;
    notifyListeners();
  }
  
  /// Start periodic state checking
  void _startStateChecking() {
    _stopStateChecking();
    
    if (!_isConnected) {
      if (kDebugMode) {
        print('🎵 [SpotifyProvider] Skipping state checking - not connected');
      }
      return;
    }
    
    _stateCheckTimer = Timer.periodic(const Duration(milliseconds: 2000), (_) {
      if (!_isCheckingState && _isConnected) {
        _checkPlaybackState();
      }
    });
    
    Timer(const Duration(milliseconds: 500), () {
      if (_isConnected && !_isCheckingState) {
        _checkPlaybackState();
      }
    });
  }
  
  /// Stop state checking
  void _stopStateChecking() {
    _stateCheckTimer?.cancel();
    _stateCheckTimer = null;
  }

  /// Check and update playback state
  Future<void> _checkPlaybackState() async {
    if (_isCheckingState) return;
    
    try {
      _isCheckingState = true;
      
      final now = DateTime.now();
      if (_lastPlaybackCheck != null && 
          now.difference(_lastPlaybackCheck!) < _playbackCheckInterval) {
        return;
      }
      _lastPlaybackCheck = now;

      if (!_isConnected) {
        return;
      }

      final playbackState = await _webApiService.getCurrentPlayback();
      
      if (playbackState != null) {
        _isPlaying = playbackState['is_playing'] ?? false;
        _isPaused = !_isPlaying;
        _hasActivePlayback = playbackState['item'] != null;
        
        if (_hasActivePlayback) {
          _position = playbackState['progress_ms'] ?? 0;
          final item = playbackState['item'];
          _duration = item['duration_ms'] ?? 0;
          
          if (_isPlaying && _lastPositionUpdate == null) {
            _estimatedPosition = _position;
            _lastPositionUpdate = DateTime.now();
          } else if (_isPlaying) {
            _estimatedPosition = _position;
            _lastPositionUpdate = DateTime.now();
          }
          
          if (item != null) {
            final newTrack = MusicTrack(
              id: item['id'] ?? '',
              title: item['name'] ?? 'Unknown Track',
              artist: item['artists']?.isNotEmpty == true 
                  ? item['artists'][0]['name'] ?? 'Unknown Artist'
                  : 'Unknown Artist',
              album: item['album']?['name'] ?? '',
              albumArt: item['album']?['images']?.isNotEmpty == true 
                  ? item['album']['images'][0]['url'] 
                  : null,
              uri: item['uri'] ?? '',
              durationMs: item['duration_ms'] ?? 0,
              url: item['external_urls']?['spotify'] ?? '',
              service: 'spotify',
              serviceType: 'spotify',
              genres: [],
              explicit: item['explicit'] ?? false,
              popularity: item['popularity'] ?? 0,
            );
            
            if (_currentTrack?.uri != newTrack.uri) {
              _currentTrack = newTrack;
            }
          }
        } else {
          _isPlaying = false;
          _isPaused = true;
        }
        
        _handlePlaybackStateChange();
        notifyListeners();
      } else {
        if (_hasActivePlayback) {
          _hasActivePlayback = false;
          _isPlaying = false;
          _isPaused = true;
          notifyListeners();
        }
      }
    } catch (e) {
      if (e.toString().toLowerCase().contains('connection')) {
        final now = DateTime.now();
        if (_lastPlaybackCheck == null || 
            now.difference(_lastPlaybackCheck!).inSeconds > 30) {
          if (kDebugMode) {
            print('⚠️ Spotify connection lost, will attempt to reconnect when needed');
          }
        }
        
        _isConnected = false;
        _hasActivePlayback = false;
        notifyListeners();
      } else {
        if (kDebugMode) {
          print('❌ Error checking playback state: $e');
        }
      }
    } finally {
      _isCheckingState = false;
    }
  }
  
  /// Handle playback state changes for position tracking
  void _handlePlaybackStateChange() {
    if (_isPlaying && _hasActivePlayback) {
      if (_lastPositionUpdate == null) {
        _estimatedPosition = _position;
        _lastPositionUpdate = DateTime.now();
      }
      
      if (_positionTimer == null || _fastPositionTimer == null) {
        _startPositionTimer();
      }
    } else {
      _lastPositionUpdate = null;
      if (!_isPlaying) {
        _stopPositionTimer();
      }
    }
  }
  
  /// Check if Spotify app is available (not applicable for Web API)
  Future<bool> isSpotifyAppAvailable() async {
    return true;
  }
  
  /// Get user-friendly error message
  String getSpotifyErrorMessage(String error) {
    if (error.toLowerCase().contains('premium')) {
      return 'Spotify Premium is required for playback control. Please upgrade your Spotify account.';
    } else if (error.toLowerCase().contains('device')) {
      return 'No active Spotify device found. Please open Spotify and start playing something, then try again.';
    } else {
      return 'Error with Spotify playback: $error';
    }
  }
  
  /// Set mock track for demo purposes
  void setMockTrack(MusicTrack track) {
    _currentTrack = track;
    _isPlaying = true;
    _isPaused = false;
    _hasActivePlayback = true;
    _duration = track.durationMs;
    _position = 0;
    _estimatedPosition = 0;
    _lastPositionUpdate = DateTime.now();
    _startPositionTimer();
    notifyListeners();
  }
  
  /// Clear mock track
  void clearMockTrack() {
    _isPlaying = false;
    _isPaused = true;
    _hasActivePlayback = false;
    _currentTrack = null;
    _position = 0;
    _duration = 0;
    _estimatedPosition = 0;
    _lastPositionUpdate = null;
    _stopPositionTimer();
    notifyListeners();
  }
  
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    
    _stopPositionTimer();
    _stopStateChecking();
    _positionController.close();
    super.dispose();
  }

  // Additional getters
  double get playbackPosition {
    if (duration <= 0) return 0.0;
    return position / duration;
  }
  
  int get currentPositionMs => position;
  bool get shuffleEnabled => _shuffleEnabled;
  String get repeatMode => _repeatMode;

  /// Toggle repeat mode
  Future<bool> toggleRepeatMode() async {
    try {
      String newMode;
      switch (_repeatMode) {
        case 'off':
          newMode = 'track';
          break;
        case 'track':
          newMode = 'context';
          break;
        default:
          newMode = 'off';
      }
      
      final success = await _webApiService.setRepeatMode(newMode);
      if (success) {
        _repeatMode = newMode;
        notifyListeners();
      }
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('Error toggling repeat mode: $e');
      }
      return false;
    }
  }

  // Additional getters
  int get totalLikedSongs => _totalLikedSongs;
  double get likedSongsProgress => _totalLikedSongs > 0 ? _likedSongs.length / _totalLikedSongs : 0.0;

  // Playlist state getters
  bool isLoadingPlaylistTracks(String playlistId) => _isLoadingPlaylistTracks[playlistId] ?? false;
  bool hasMorePlaylistTracks(String playlistId) => _hasMorePlaylistTracks[playlistId] ?? false;
  int totalPlaylistTracks(String playlistId) => _totalPlaylistTracks[playlistId] ?? 0;

  /// Get user's top artists with caching
  Future<Map<String, List<Map<String, dynamic>>>> getCachedTopArtists() async {
    final now = DateTime.now();
    if (_topArtistsCache != null && 
        _topArtistsCacheTime != null &&
        now.difference(_topArtistsCacheTime!) < _topArtistsCacheExpiry) {
      print('✅ Using cached top artists from provider');
      return _topArtistsCache!;
    }
    
    print('👨‍🎤 Fetching fresh top artists and caching...');
    try {
      final futures = {
        'short_term': _webApiService.getTopArtists(limit: 50, timeRange: 'short_term'),
        'medium_term': _webApiService.getTopArtists(limit: 50, timeRange: 'medium_term'),
        'long_term': _webApiService.getTopArtists(limit: 50, timeRange: 'long_term'),
      };
      
      final results = await Future.wait(futures.values);
      
      _topArtistsCache = {
        'short_term': results[0],
        'medium_term': results[1],
        'long_term': results[2],
      };
      _topArtistsCacheTime = now;
      
      print('💾 Cached top artists in provider');
      return _topArtistsCache!;
      
    } catch (e) {
      print('❌ Error fetching/caching top artists: $e');
      return {};
    }
  }

  /// Invalidate the top artists cache
  void invalidateTopArtistsCache() {
    _topArtistsCache = null;
    _topArtistsCacheTime = null;
    print('🗑️ Invalidated top artists cache');
    notifyListeners();
  }

  /// Get user playlists
  Future<List<MusicTrack>> getUserPlaylists() async {
    if (_isLoadingPlaylists) return [];
    
    try {
      _isLoadingPlaylists = true;
      notifyListeners();
      
      // For Web API, we need to get playlist tracks differently
      // This is a simplified implementation
      final playlists = await _webApiService.getUserPlaylists();
      final List<MusicTrack> allTracks = [];
      
      for (final playlist in playlists.take(5)) { // Limit to first 5 playlists
        try {
          final result = await _webApiService.getPlaylistTracks(playlist['id'], limit: 10);
          final tracks = result['tracks'] as List<MusicTrack>;
          allTracks.addAll(tracks);
        } catch (e) {
          if (kDebugMode) {
            print('Error loading tracks for playlist ${playlist['name']}: $e');
          }
        }
      }
      
      _isLoadingPlaylists = false;
      notifyListeners();
      
      return allTracks;
    } catch (e) {
      print('Error loading playlists: $e');
      _isLoadingPlaylists = false;
      notifyListeners();
      return [];
    }
  }
} 