import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
import '../models/user_notification_settings.dart';
import '../services/notifications/notification_api_service.dart';
import '../services/notifications/onesignal_service.dart';

class NotificationProvider extends ChangeNotifier {
  final NotificationApiService _apiService;
  final OneSignalService _oneSignalService;

  List<NotificationModel> _notifications = [];
  UserNotificationSettings? _settings;
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String? _error;
  
  // Pagination state
  int _currentOffset = 0;
  int _pageSize = 20;
  bool _hasMoreData = true;
  int _totalCount = 0;

  NotificationProvider({
    required NotificationApiService apiService,
    required OneSignalService oneSignalService,
  }) : _apiService = apiService,
       _oneSignalService = oneSignalService;
  
  // Getters
  List<NotificationModel> get notifications => _notifications;
  List<NotificationModel> get unreadNotifications => 
      _notifications.where((n) => !n.isRead).toList();
  int get unreadCount => unreadNotifications.length;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  String? get error => _error;
  UserNotificationSettings? get settings => _settings;
  bool get hasMoreData => _hasMoreData;
  int get totalCount => _totalCount;
  int get currentCount => _notifications.length;

  /// Initialize provider and OneSignal
  Future<void> initialize(String userId) async {
    try {
      await _oneSignalService.initialize(userId: userId);
      await loadNotifications();
      await loadSettings();
    } catch (e) {
      _setError('Failed to initialize notifications: $e');
    }
  }

  /// Load notifications from backend (initial load)
  Future<void> loadNotifications({
    NotificationCategory? category,
    bool? isRead,
    bool refresh = false,
  }) async {
    if (refresh) {
      _resetPagination();
    }
    
    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.getNotifications(
        category: category,
        isRead: isRead,
        limit: _pageSize,
        offset: 0,
      );

      _notifications = response.results;
      _totalCount = response.totalCount;
      _currentOffset = response.results.length;
      _hasMoreData = response.results.length >= _pageSize && _currentOffset < _totalCount;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load notifications: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load more notifications (pagination)
  Future<void> loadMoreNotifications({
    NotificationCategory? category,
    bool? isRead,
  }) async {
    if (_isLoadingMore || !_hasMoreData) return;

    _setLoadingMore(true);
    _clearError();

    try {
      final response = await _apiService.getNotifications(
        category: category,
        isRead: isRead,
        limit: _pageSize,
        offset: _currentOffset,
      );

      // Append new notifications, avoiding duplicates
      final newNotifications = response.results.where(
        (newNotif) => !_notifications.any((existing) => existing.id == newNotif.id)
      ).toList();
      
      _notifications.addAll(newNotifications);
      _totalCount = response.totalCount;
      _currentOffset += newNotifications.length;
      _hasMoreData = newNotifications.length >= _pageSize && _currentOffset < _totalCount;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load more notifications: $e');
    } finally {
      _setLoadingMore(false);
    }
  }
  
  /// Refresh notifications (pull to refresh)
  Future<void> refreshNotifications({
    NotificationCategory? category,
    bool? isRead,
  }) async {
    await loadNotifications(category: category, isRead: isRead, refresh: true);
  }

  /// Reset pagination state
  void _resetPagination() {
    _currentOffset = 0;
    _hasMoreData = true;
    _totalCount = 0;
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _apiService.markAsRead(notificationId);
      
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index >= 0) {
        _notifications[index] = _notifications[index].copyWith(
          isRead: true,
          readAt: DateTime.now(),
        );
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to mark notification as read: $e');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead({NotificationCategory? category}) async {
    try {
      await _apiService.markAllAsRead(category: category);
      
      for (int i = 0; i < _notifications.length; i++) {
        if (category == null || 
            category == NotificationCategory.all ||
            _notifications[i].category == category) {
          _notifications[i] = _notifications[i].copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
        }
      }
      notifyListeners();
    } catch (e) {
      _setError('Failed to mark all notifications as read: $e');
    }
  }

  /// Delete notifications
  Future<void> deleteNotifications(List<String> notificationIds) async {
    try {
      await _apiService.deleteNotifications(notificationIds);
      
      _notifications.removeWhere((n) => notificationIds.contains(n.id));
      _totalCount = _totalCount - notificationIds.length;
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete notifications: $e');
    }
  }

  /// Load user settings
  Future<void> loadSettings() async {
    try {
      _settings = await _apiService.getSettings();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load settings: $e');
    }
  }

  /// Update user settings
  Future<void> updateSettings(UserNotificationSettings settings) async {
    try {
      _settings = await _apiService.updateSettings(settings);
      notifyListeners();
    } catch (e) {
      _setError('Failed to update settings: $e');
    }
  }

  /// Get statistics
  Future<NotificationStatsResponse> getStats() async {
    return await _apiService.getStats();
  }

  // Filter methods
  List<NotificationModel> getNotificationsByCategory(NotificationCategory category) {
    if (category == NotificationCategory.all) {
      return _notifications;
    }
    return _notifications.where((n) => n.category == category).toList();
  }

  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Real-time updates (for when notifications are received)
  void addNotification(NotificationModel notification) {
    _notifications.insert(0, notification);
    _totalCount++;
    notifyListeners();
  }

  void updateNotification(NotificationModel notification) {
    final index = _notifications.indexWhere((n) => n.id == notification.id);
    if (index >= 0) {
      _notifications[index] = notification;
      notifyListeners();
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setLoadingMore(bool loading) {
    _isLoadingMore = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    _notifications.clear();
    super.dispose();
  }
} 