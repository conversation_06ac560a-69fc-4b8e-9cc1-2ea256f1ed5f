import 'package:flutter/material.dart';
import '../models/pin_skin.dart';
import '../services/api/skin_service.dart';
import '../services/api/api_client.dart';

class SkinProvider extends ChangeNotifier {
  final SkinService _skinService;
  
  List<PinSkin> _availableSkins = [];
  List<PinSkin> _unlockedSkins = [];
  Map<String, List<PinSkin>> _featuredSkins = {
    'latest': [],
    'premium': [],
    'top': [],
  };
  Map<String, List<PinSkin>> _limitedSkins = {
    'active': [],
    'expired': [],
  };
  PinSkin? _equippedSkin;
  bool _isLoading = false;
  String? _error;

  SkinProvider(ApiClient apiClient) : _skinService = SkinService(apiClient);

  // Getters
  List<PinSkin> get availableSkins => _availableSkins;
  List<PinSkin> get unlockedSkins => _unlockedSkins;
  List<PinSkin> get lockedSkins => _availableSkins.where((skin) => skin.locked).toList();
  List<PinSkin> get houseSkins => _availableSkins.where((skin) => skin.isHouseSkin).toList();
  List<PinSkin> get artistSkins => _availableSkins.where((skin) => skin.isArtistSkin).toList();
  List<PinSkin> get premiumSkins => _featuredSkins['premium'] ?? [];
  List<PinSkin> get challengeSkins => _availableSkins.where((skin) => skin.isChallengeSkin).toList();
  List<PinSkin> get achievementSkins => _availableSkins.where((skin) => skin.isAchievementSkin).toList();
  
  // Featured skins getters
  List<PinSkin> get latestSkins => _featuredSkins['latest'] ?? [];
  List<PinSkin> get topSkins => _featuredSkins['top'] ?? [];
  Map<String, List<PinSkin>> get featuredSkins => _featuredSkins;
  
  // Limited skins getters
  List<PinSkin> get activeLimitedSkins => _limitedSkins['active'] ?? [];
  List<PinSkin> get expiredLimitedSkins => _limitedSkins['expired'] ?? [];
  Map<String, List<PinSkin>> get limitedSkins => _limitedSkins;
  
  PinSkin? get equippedSkin => _equippedSkin;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasError => _error != null;

  /// Load all available skins from API
  Future<void> loadAvailableSkins() async {
    _setLoading(true);
    _clearError();
    
    try {
      _availableSkins = await _skinService.getAvailableSkins();
      
      // Find the equipped skin
      _equippedSkin = _availableSkins.firstWhere(
        (skin) => skin.isEquipped,
        orElse: () => _availableSkins.isNotEmpty ? _availableSkins.first : _getDefaultSkin(),
      );
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load available skins: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Load featured skins
  Future<void> loadFeaturedSkins() async {
    try {
      _featuredSkins = await _skinService.getFeaturedSkins();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load featured skins: ${e.toString()}');
    }
  }

  /// Load limited-time skins
  Future<void> loadLimitedSkins() async {
    try {
      _limitedSkins = await _skinService.getLimitedSkins();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load limited skins: ${e.toString()}');
    }
  }

  /// Load user's unlocked skins
  Future<void> loadUnlockedSkins() async {
    try {
      _unlockedSkins = await _skinService.getUnlockedSkins();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load unlocked skins: ${e.toString()}');
    }
  }

  /// Claim a skin if eligible
  Future<bool> claimSkin(PinSkin skin) async {
    _setLoading(true);
    _clearError();
    
    try {
      final result = await _skinService.claimSkin(skin.id);
      
      if (result['success'] == true) {
        // Update the skin in our list
        _updateSkinInList(skin.copyWith(locked: false, isUnlocked: true));
        
        // Refresh unlocked skins
        await loadUnlockedSkins();
        
        return true;
      } else {
        _setError(result['message'] ?? 'Failed to claim skin');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Equip a skin
  Future<bool> equipSkin(PinSkin skin) async {
    if (skin.locked) {
      _setError('Cannot equip locked skin');
      return false;
    }

    _setLoading(true);
    _clearError();
    
    try {
      final result = await _skinService.equipSkin(skin.id);
      
      if (result['success'] == true) {
        // Update equipped status
        _updateEquippedSkin(skin);
        return true;
      } else {
        _setError(result['message'] ?? 'Failed to equip skin');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh all skin data
  Future<void> refreshSkins() async {
    await Future.wait([
      loadAvailableSkins(),
      loadUnlockedSkins(),
      loadFeaturedSkins(),
      loadLimitedSkins(),
    ]);
  }

  /// Get skins by category for UI filtering
  List<PinSkin> getSkinsByCategory(String category) {
    switch (category.toLowerCase()) {
      case 'all':
        return _availableSkins;
      case 'unlocked':
        return _unlockedSkins;
      case 'locked':
        return lockedSkins;
      case 'house':
        return houseSkins;
      case 'artist':
        return artistSkins;
      case 'premium':
        return premiumSkins;
      case 'challenge':
        return challengeSkins;
      case 'achievement':
        return achievementSkins;
      case 'limited':
        return activeLimitedSkins;
      default:
        return _availableSkins;
    }
  }

  /// Check if user can claim a specific skin
  bool canClaimSkin(PinSkin skin) {
    if (!skin.locked) return false; // Already unlocked
    
    switch (skin.unlockRule) {
      case 'ALWAYS_AVAILABLE':
        return true;
      case 'PREMIUM_SUBSCRIPTION':
        return false; // Would need to check premium status
      case 'COMPLETE_WEEKLY_CHALLENGE':
      case 'PARTICIPATE_IN_CHALLENGE':
        return skin.challengeId != null; // Would need challenge completion status
      default:
        if (skin.unlockRule.contains('TOP_')) {
          return false; // Would need to check ranking
        }
        if (skin.unlockRule.contains('COMPLETE_ACHIEVEMENT_')) {
          return false; // Would need to check achievement status
        }
        return false;
    }
  }

  /// Get default/fallback skin
  PinSkin _getDefaultSkin() {
    return PinSkin(
      id: 1,
      name: 'Default Pin',
      slug: 'default-pin',
      image: 'https://picsum.photos/seed/default-pin/64/64',
      description: 'The standard pin for marking your favorite music spots.',
      skinType: 'HOUSE',
      unlockRule: 'ALWAYS_AVAILABLE',
      locked: false,
      createdAt: DateTime.now(),
      isUnlocked: true,
    );
  }

  /// Update a specific skin in the available skins list
  void _updateSkinInList(PinSkin updatedSkin) {
    final index = _availableSkins.indexWhere((skin) => skin.id == updatedSkin.id);
    if (index != -1) {
      _availableSkins[index] = updatedSkin;
      notifyListeners();
    }
  }

  /// Update the equipped skin and unequip others
  void _updateEquippedSkin(PinSkin newEquippedSkin) {
    // Unequip all skins
    _availableSkins = _availableSkins.map((skin) => 
      skin.copyWith(isEquipped: false)
    ).toList();
    
    // Equip the new skin
    _updateSkinInList(newEquippedSkin.copyWith(isEquipped: true));
    _equippedSkin = newEquippedSkin.copyWith(isEquipped: true);
    
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    super.dispose();
  }
} 