import 'package:flutter/material.dart';
import '../services/music/spotify_connection_service.dart';

class SpotifyConnectionProvider extends ChangeNotifier with WidgetsBindingObserver {
  final SpotifyConnectionService _service = SpotifyConnectionService();
  SpotifyConnectionState _state = SpotifyConnectionState.disconnected;
  String? _lastError;

  SpotifyConnectionProvider() {
    WidgetsBinding.instance.addObserver(this);
    _initializeListeners();
  }

  // Public getters
  bool get isConnected => _state == SpotifyConnectionState.connected;
  bool get isConnecting => _state == SpotifyConnectionState.connecting;
  bool get hasError => _state == SpotifyConnectionState.error;
  String? get lastError => _lastError;
  SpotifyConnectionState get connectionState => _state;

  void _initializeListeners() {
    // Listen to connection state changes
    _service.connectionState.listen((state) {
      if (_state != state) {
        _state = state;
        notifyListeners();
      }
    });

    // Listen to errors
    _service.errorStream.listen((error) {
      _lastError = error;
      notifyListeners();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _service.handleAppLifecycleState(state);
  }

  Future<bool> reconnect() => _service.reconnect();

  void clearError() {
    _lastError = null;
    notifyListeners();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _service.dispose();
    super.dispose();
  }
} 