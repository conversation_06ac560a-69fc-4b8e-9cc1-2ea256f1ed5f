import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider extends ChangeNotifier {
  static const String _animatedBackgroundKey = 'animated_background_enabled';
  
  late SharedPreferences _prefs;
  bool _isAnimatedBackgroundEnabled = true;

  bool get isAnimatedBackgroundEnabled => _isAnimatedBackgroundEnabled;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _loadSettings();
  }

  void _loadSettings() {
    _isAnimatedBackgroundEnabled = _prefs.getBool(_animatedBackgroundKey) ?? true;
    notifyListeners();
  }

  Future<void> setAnimatedBackground(bool enabled) async {
    _isAnimatedBackgroundEnabled = enabled;
    await _prefs.setBool(_animatedBackgroundKey, enabled);
    notifyListeners();
  }
} 