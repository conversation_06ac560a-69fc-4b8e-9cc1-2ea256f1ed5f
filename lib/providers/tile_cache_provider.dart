import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../services/map/tile_cache_service.dart';
import '../models/map/tile.dart';
import '../services/api/auth_service.dart';

/// Provider for the TileCacheService that handles map tile caching
/// 
/// This provider makes the TileCacheService available throughout the app
/// and handles initialization, authentication, and other lifecycle events.
class TileCacheProvider extends ChangeNotifier {
  TileCacheService? _tileCacheService;
  bool _isInitialized = false;
  bool _isInitializing = false;
  String? _initializationError;
  
  /// The current initialization status
  bool get isInitialized => _isInitialized;
  
  /// Whether the service is currently initializing
  bool get isInitializing => _isInitializing;
  
  /// Any error that occurred during initialization
  String? get initializationError => _initializationError;
  
  /// Get the TileCacheService instance (may be null if not initialized)
  TileCacheService? get service => _tileCacheService;
  
  /// Initialize the TileCacheService with authentication
  Future<void> initialize() async {
    if (_isInitialized || _isInitializing) return;
    
    _isInitializing = true;
    _initializationError = null;
    notifyListeners();
    
    try {
      // Get the auth token from AuthService
      final authService = AuthService();
      final token = await authService.getToken();
      
      // Create an HTTP client with appropriate timeouts
      final client = http.Client();
      
      // Initialize the TileCacheService
      // During development, we can use a dummy token if needed
      _tileCacheService = TileCacheService(client, token ?? 'dev_token');
      _isInitialized = true;
      
    } catch (e) {
      _initializationError = 'Failed to initialize tile cache: $e';
      debugPrint(_initializationError);
    } finally {
      _isInitializing = false;
      notifyListeners();
    }
  }
  
  /// Prefetch tiles for the given region
  Future<void> prefetchRegion(
    double north,
    double south,
    double east,
    double west,
    List<int> zoomLevels,
  ) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (_tileCacheService != null) {
      await _tileCacheService!.prefetchTiles(
        north, south, east, west, zoomLevels
      );
    }
  }
  
  /// Prefetch tiles for a list of points of interest
  Future<void> prefetchPOIs(
    List<Map<String, dynamic>> pois,
    List<int> zoomLevels,
  ) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (_tileCacheService != null) {
      await _tileCacheService!.prefetchPOIs(pois, zoomLevels);
    }
  }
  
  /// Get a cached tile
  Future<List<int>?> getCachedTile(MapTile tile) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (_tileCacheService != null) {
      return _tileCacheService!.getCachedTile(tile);
    }
    
    return null;
  }
  
  /// Clear the cache
  void clearCache() {
    if (_tileCacheService != null) {
      _tileCacheService!.clearETagCache();
    }
  }
  
  /// Get cache statistics
  Future<Map<String, dynamic>?> getCacheStats() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (_tileCacheService != null) {
      return _tileCacheService!.getCacheStats();
    }
    
    return null;
  }
  
  @override
  void dispose() {
    // Nothing to dispose
    super.dispose();
  }
} 