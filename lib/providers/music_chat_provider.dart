import 'package:flutter/foundation.dart';
import '../models/music/music_chat_message.dart';
import '../models/music_track.dart';
import '../models/user.dart';
import '../services/api_service.dart';
import '../services/music/spotify_service.dart';
import '../providers/auth_provider.dart';
import '../providers/spotify_provider.dart';
import 'dart:math' as math;

class Conversation {
  final int id;
  final List<ConversationParticipant> participants;
  final MusicChatMessage? lastMessage;
  final int unreadCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  Conversation({
    required this.id,
    required this.participants,
    this.lastMessage,
    required this.unreadCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'] is String ? int.parse(json['id']) : json['id'] as int,
      participants: (json['participants'] as List<dynamic>)
          .map((p) => ConversationParticipant.fromJson(p as Map<String, dynamic>))
          .toList(),
      lastMessage: json['last_message'] != null
          ? MusicChatMessage.fromJson(json['last_message'] as Map<String, dynamic>)
          : null,
      unreadCount: json['unread_count'] is String ? int.parse(json['unread_count']) : (json['unread_count'] as int? ?? 0),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['last_message_at'] as String? ?? json['updated_at'] as String? ?? json['created_at'] as String),
    );
  }
}

class ConversationParticipant {
  final int id;
  final String username;
  final String? profilePic;

  ConversationParticipant({
    required this.id,
    required this.username,
    this.profilePic,
  });

  factory ConversationParticipant.fromJson(Map<String, dynamic> json) {
    return ConversationParticipant(
      id: json['id'] is String ? int.parse(json['id']) : json['id'] as int,
      username: json['username'] as String,
      profilePic: json['profile_pic_url'] as String? ?? json['profile_pic'] as String?,
    );
  }
}

class MusicChatProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  final SpotifyService _spotifyService = SpotifyService();
  final AuthProvider? _authProvider;
  final SpotifyProvider? _spotifyProvider;
  
  List<MusicChatMessage> _messages = [];
  List<MusicTrack> _searchResults = [];
  List<Conversation> _conversations = [];
  bool _isLoading = false;
  String? _error;
  bool _isSearching = false;
  int? _currentConversationId;
  
  // Constructor that takes AuthProvider and SpotifyProvider
  MusicChatProvider(this._authProvider, [this._spotifyProvider]);
  
  // Helper to get auth token
  String? get _authToken => _authProvider?.token;
  
  // Getters
  List<MusicChatMessage> get messages => List.unmodifiable(_messages);
  List<MusicTrack> get searchResults => List.unmodifiable(_searchResults);
  List<Conversation> get conversations => List.unmodifiable(_conversations);
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isSearching => _isSearching;
  
  // Get or create conversation with friend
  Future<int?> _getOrCreateConversation(int friendId) async {
    try {
      // First, get all conversations
      final response = await _apiService.get(
        '/music/chat/conversations/',
        token: _authToken,
      );
      
      if (kDebugMode) {
        print('🎵 ===== API RESPONSE DEBUG =====');
        print('🎵 Response success: ${response.success}');
        print('🎵 Response status code: ${response.statusCode}');
        print('🎵 Response message: ${response.message}');
        print('🎵 Response data type: ${response.data?.runtimeType}');
        print('🎵 Raw response data: ${response.data}');
        
        if (response.data != null && response.data is Map) {
          final data = response.data as Map<String, dynamic>;
          print('🎵 Data keys: ${data.keys.toList()}');
          
          if (data.containsKey('results')) {
            final results = data['results'];
            print('🎵 Results type: ${results.runtimeType}');
            print('🎵 Results length: ${results is List ? results.length : 'not a list'}');
            print('🎵 Raw results: $results');
            
            if (results is List && results.isNotEmpty) {
              print('🎵 First conversation raw: ${results[0]}');
            }
          }
        }
        print('🎵 ===== END API RESPONSE DEBUG =====');
      }
      
      if (response.success && response.data != null) {
        List<dynamic> conversationsData;
        
        // Handle both List<dynamic> and Map with 'results' key
        if (response.data is List<dynamic>) {
          conversationsData = response.data as List<dynamic>;
        } else if (response.data is Map<String, dynamic> && response.data['results'] != null) {
          conversationsData = response.data['results'] as List<dynamic>;
        } else {
          conversationsData = [];
        }
        
        if (kDebugMode) {
          print('🎵 Processing ${conversationsData.length} conversations');
        }
        
        try {
          _conversations = conversationsData
              .map((c) => Conversation.fromJson(c as Map<String, dynamic>))
              .toList();
          
          if (kDebugMode) {
            print('🎵 Successfully parsed ${_conversations.length} conversations');
            for (var conv in _conversations) {
              print('🎵 Conv ${conv.id}: ${conv.participants.length} participants');
              for (var p in conv.participants) {
                print('🎵   - Participant ${p.id}: ${p.username}');
              }
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('🎵 ERROR parsing conversations: $e');
          }
          _conversations = [];
          return null;
        }
        
        if (kDebugMode) {
          print('🎵 About to search for friend ID: $friendId');
        }
        
        // Find existing conversation with this friend
        for (final conversation in _conversations) {
          if (kDebugMode) {
            print('🎵 Checking conversation ${conversation.id}:');
            for (var p in conversation.participants) {
              print('🎵   - Participant ${p.id}: ${p.username}');
            }
            print('🎵   Looking for friend ID: $friendId');
          }
          
          final hasThisFriend = conversation.participants
              .any((p) => p.id == friendId);
          
          if (kDebugMode) {
            print('🎵   Has friend $friendId: $hasThisFriend');
          }
          
          if (hasThisFriend) {
            if (kDebugMode) {
              print('🎵 Found existing conversation ID: ${conversation.id} for friend $friendId');
            }
            return conversation.id;
          }
        }
        
        // If no existing conversation, the first message will create it
        if (kDebugMode) {
          print('🎵 No existing conversation found for friend $friendId');
        }
        return null;
      }
    } catch (e) {
      _error = 'Failed to get conversations: $e';
      notifyListeners();
    }
    return null;
  }
  
  // Load chat history
  Future<void> loadChatHistory(int friendId) async {
    if (_isLoading) return;
    
    if (kDebugMode) {
      print('🎵 ===== LOAD CHAT HISTORY START =====');
      print('🎵 Friend ID: $friendId');
    }
    
    _setLoading(true);
    
    try {
      // Get conversation ID first
      final conversationId = await _getOrCreateConversation(friendId);
      _currentConversationId = conversationId;
      
      if (kDebugMode) {
        print('🎵 Got conversation ID: $conversationId');
      }
      
      if (conversationId != null) {
        // Load messages from API
        final response = await _apiService.get(
          '/music/chat/$conversationId/messages/',
          token: _authToken,
        );
        
        if (kDebugMode) {
          print('🎵 ===== MESSAGES API RESPONSE DEBUG =====');
          print('🎵 Conversation ID: $conversationId');
          print('🎵 Response success: ${response.success}');
          print('🎵 Response status code: ${response.statusCode}');
          print('🎵 Response message: ${response.message}');
          print('🎵 Response data type: ${response.data?.runtimeType}');
          print('🎵 Raw response data: ${response.data}');
          
          if (response.data != null && response.data is Map) {
            final data = response.data as Map<String, dynamic>;
            print('🎵 Data keys: ${data.keys.toList()}');
            
            // Check for both 'messages' and 'results' fields
            if (data.containsKey('messages')) {
              final messages = data['messages'];
              print('🎵 Messages type: ${messages.runtimeType}');
              print('🎵 Messages count: ${messages is List ? messages.length : 'not a list'}');
              print('🎵 Raw messages: $messages');
              
              if (messages is List && messages.isNotEmpty) {
                print('🎵 First message raw: ${messages[0]}');
              }
            } else if (data.containsKey('results')) {
              final results = data['results'];
              print('🎵 Messages results type: ${results.runtimeType}');
              print('🎵 Messages count: ${results is List ? results.length : 'not a list'}');
              print('🎵 Raw messages: $results');
              
              if (results is List && results.isNotEmpty) {
                print('🎵 First message raw: ${results[0]}');
              }
            }
          }
          print('🎵 ===== END MESSAGES API RESPONSE DEBUG =====');
        }
        
        if (response.success && response.data != null) {
          // Handle both 'messages' and 'results' field names
          List<dynamic> messagesData;
          if (response.data['messages'] != null) {
            messagesData = response.data['messages'] as List<dynamic>;
          } else if (response.data['results'] != null) {
            messagesData = response.data['results'] as List<dynamic>;
          } else {
            messagesData = [];
          }
          
          if (kDebugMode) {
            print('🎵 About to parse ${messagesData.length} messages');
          }
          
          try {
            _messages = messagesData
                .map((m) => MusicChatMessage.fromJson(m as Map<String, dynamic>))
                .toList();
            
            // Sort messages by timestamp (oldest first) for proper chat order with reverse: true
            _messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
            
            if (kDebugMode) {
              print('🎵 ✅ Successfully parsed and sorted ${_messages.length} messages');
              for (var msg in _messages) {
                print('🎵 Message: ${msg.id} - "${msg.track.title}" by "${msg.track.artist}" - ${msg.timestamp}');
              }
            }
          } catch (e, stackTrace) {
            if (kDebugMode) {
              print('🎵 ❌ Error parsing messages: $e');
              print('🎵 Stack trace: $stackTrace');
            }
            _messages = [];
          }
        } else {
          _messages = [];
          if (kDebugMode) {
            print('🎵 No messages data or response failed');
          }
        }
      } else {
        // No existing conversation, start with empty messages
        _messages = [];
      }
      
      _setLoading(false);
    } catch (e) {
      _handleError('Failed to load chat history: $e');
    }
  }
  
  // Search songs
  Future<void> searchSongs(String query) async {
    if (query.isEmpty) {
      _searchResults = [];
      notifyListeners();
      return;
    }
    
    if (_isSearching) return;
    
    _isSearching = true;
    notifyListeners();
    
    try {
      // Use SpotifyService search endpoint instead of SpotifyProvider
      final tracks = await _spotifyService.searchTracks(
        query, 
        context: 'MUSIC_CHAT_SEARCH',
        limit: 20,
      );
      
      _searchResults = tracks;
    } catch (e) {
      _error = 'Failed to search songs: $e';
      _searchResults = [];
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }
  
  // Send a song recommendation
  Future<bool> sendRecommendation({
    required User friend,
    required MusicTrack track,
    String? message,
  }) async {
    if (_isLoading) return false;
    
    try {
      // Prepare track recommendation data
      final messageData = {
        'recipient_id': friend.id,
        'message_type': 'track_recommendation',
        'text_content': message ?? 'Check this out!',
        'track_id': track.uri.isNotEmpty ? track.uri : track.id,
        'track_title': track.title,
        'track_artist': track.artist,
        'track_album': track.album ?? '',
        'album_art_url': track.albumArt,
        'preview_url': track.previewUrl ?? '',
        'track_url': track.url,
        'music_service': track.service ?? 'spotify',
      };
      
      if (kDebugMode) {
        print('🎵 📤 Sending message data:');
        print('🎵 text_content: "${messageData['text_content']}"');
        print('🎵 album_art_url: "${messageData['album_art_url']}"');
        print('🎵 track_title: "${messageData['track_title']}"');
        print('🎵 track_artist: "${messageData['track_artist']}"');
      }
      
      // Send to API
      final response = await _apiService.post(
        '/music/chat/send_message/',
        data: messageData,
        token: _authToken,
      );
      
      if (response.success && response.data != null) {
        // Create local message from API response for immediate UI update
        final newMessage = MusicChatMessage.fromJson(response.data as Map<String, dynamic>);
        
        if (kDebugMode) {
          print('🎵 MusicChat: Sent message successfully: ${newMessage.id}');
          print('🎵 Track: ${newMessage.track.title} by ${newMessage.track.artist}');
          print('🎵 Message: ${newMessage.message}');
        }
        
        // Add to local messages for immediate UI feedback
        _messages.add(newMessage);
        // Always maintain chronological order (oldest first)
        _messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
        
        if (kDebugMode) {
          print('🎵 ✅ Added new message and re-sorted. Total messages: ${_messages.length}');
          print('🎵 Last 3 messages in order:');
          for (int i = math.max(0, _messages.length - 3); i < _messages.length; i++) {
            final msg = _messages[i];
            print('🎵   ${i + 1}. ${msg.track.title} - ${msg.timestamp} (sender: ${msg.senderId})');
          }
        }
        
        notifyListeners();
        return true;
      } else {
        _error = response.message ?? 'Failed to send recommendation';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'Failed to send recommendation: $e';
      notifyListeners();
      return false;
    }
  }
  
  // Clear search results
  void clearSearchResults() {
    _searchResults = [];
    notifyListeners();
  }
  
  // Mark message as read
  Future<void> markAsRead(String messageId) async {
    if (_currentConversationId == null) return;
    
    try {
      final response = await _apiService.post(
        '/music/chat/$_currentConversationId/mark_read/',
        token: _authToken,
      );
      
      if (response.success) {
        // Update local messages to reflect read status
        for (int i = 0; i < _messages.length; i++) {
          if (!_messages[i].isRead) {
            final message = _messages[i];
            _messages[i] = MusicChatMessage(
              id: message.id,
              senderId: message.senderId,
              receiverId: message.receiverId,
              track: message.track,
              timestamp: message.timestamp,
              isRead: true,
            );
          }
        }
        notifyListeners();
      }
    } catch (e) {
      _error = 'Failed to mark message as read: $e';
      notifyListeners();
    }
  }
  
  // Add reaction to message
  Future<bool> addReaction(String messageId, String emoji) async {
    if (_currentConversationId == null) return false;
    
    try {
      final response = await _apiService.post(
        '/music/chat/$_currentConversationId/messages/$messageId/react/',
        data: {'emoji': emoji},
        token: _authToken,
      );
      
      if (response.success) {
        // Reload messages to get updated reactions
        // This maintains UI consistency without complex local state management
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Failed to add reaction: $e';
      notifyListeners();
      return false;
    }
  }
  
  // Remove reaction from message
  Future<bool> removeReaction(String messageId) async {
    if (_currentConversationId == null) return false;
    
    try {
      final response = await _apiService.delete(
        '/music/chat/$_currentConversationId/messages/$messageId/react/',
        token: _authToken,
      );
      
      if (response.success) {
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Failed to remove reaction: $e';
      notifyListeners();
      return false;
    }
  }
  
  // Private helper methods
  void _setLoading(bool loading) {
    if (kDebugMode) {
      print('🎵 🔄 Setting loading: $loading (current messages: ${_messages.length})');
    }
    _isLoading = loading;
    _error = null;
    notifyListeners();
    if (kDebugMode) {
      print('🎵 🔄 After notifyListeners - messages: ${_messages.length}');
    }
  }
  
  void _handleError(String errorMessage) {
    _error = errorMessage;
    _isLoading = false;
    _isSearching = false;
    notifyListeners();
  }
} 