import 'package:flutter/foundation.dart';
import '../models/music_track.dart';
import '../models/music/bop_drop.dart';

/// Provider to track when we're playing shuffled recommendations
/// This enables the now playing bar to show social context (recommender avatar, like button)
class RecommendationContextProvider with ChangeNotifier {
  List<BopDrop>? _currentRecommendations;
  bool _isFromShuffleRecommendations = false;
  MusicTrack? _lastTrackedTrack;
  
  /// Get the current recommendation for a specific track
  BopDrop? getCurrentRecommendationForTrack(MusicTrack track) {
    if (!_isFromShuffleRecommendations || _currentRecommendations == null) {
      return null;
    }
    
    // Match by track ID, title, or Spotify URI FIRST
    BopDrop? matchedRecommendation;
    try {
      // Use where().firstOrNull instead of firstWhere with orElse
      final matchingRecs = _currentRecommendations!.where(
        (rec) => 
          rec.trackId == track.id ||
          rec.trackId == track.uri ||
          rec.trackId.contains(track.id) ||
          track.uri.contains(rec.trackId) ||
          (rec.trackTitle.toLowerCase() == track.title.toLowerCase() && 
           rec.trackArtist.toLowerCase() == track.artist.toLowerCase()),
      );
      
      matchedRecommendation = matchingRecs.isNotEmpty ? matchingRecs.first : null;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [RecommendationContext] Error matching track: $e');
      }
      return null;
    }
    
    // Auto-clear if track changed to something not in recommendations (but be more lenient)
    _checkAndClearIfNeeded(track, matchedRecommendation);
    
    return matchedRecommendation;
  }
  
  /// Check if current track is from recommendations, clear context if not
  void _checkAndClearIfNeeded(MusicTrack track, BopDrop? matchedRecommendation) {
    // If this is a different track than we last tracked
    if (_lastTrackedTrack?.uri != track.uri) {
      if (kDebugMode) {
        print('🔄 [RecommendationContext] Track changed from "${_lastTrackedTrack?.title ?? "none"}" to "${track.title}"');
      }
      _lastTrackedTrack = track;
      
      // Only clear if we're sure this track is NOT from recommendations
      // and we've been playing recommendations for a reasonable time
      if (matchedRecommendation == null && _isFromShuffleRecommendations) {
        if (kDebugMode) {
          print('⚠️ [RecommendationContext] Track changed to non-recommendation: "${track.title}" by "${track.artist}"');
          print('⚠️ [RecommendationContext] Will clear context after brief delay to allow for track switching...');
        }
        
        // Add a small delay to allow for track switching timing issues
        Future.delayed(const Duration(milliseconds: 500), () {
          // Double-check after delay - maybe the track info updated
          final recheckMatch = _currentRecommendations?.any((rec) => 
            rec.trackId == track.id ||
            rec.trackId == track.uri ||
            rec.trackId.contains(track.id) ||
            track.uri.contains(rec.trackId) ||
            (rec.trackTitle.toLowerCase() == track.title.toLowerCase() && 
             rec.trackArtist.toLowerCase() == track.artist.toLowerCase())
          ) ?? false;
          
          if (!recheckMatch && _isFromShuffleRecommendations) {
            if (kDebugMode) {
              print('🧹 [RecommendationContext] Confirmed non-recommendation after delay, clearing context');
            }
            clearRecommendations();
          } else if (recheckMatch && kDebugMode) {
            print('✅ [RecommendationContext] Track matched after delay, keeping context');
          }
        });
      } else if (matchedRecommendation != null && kDebugMode) {
        print('✅ [RecommendationContext] Track is from recommendations, keeping context');
      }
    }
  }
  
  /// Check if we're currently playing recommendations
  bool get isPlayingRecommendations {
    final result = _isFromShuffleRecommendations && _currentRecommendations != null;
    return result;
  }
  
  /// Get all current recommendations
  List<BopDrop>? get currentRecommendations => _currentRecommendations;
  
  /// Set recommendations when shuffle recommendations is triggered
  void setRecommendations(List<BopDrop> recommendations) {
    if (kDebugMode) {
      print('🎯 [RecommendationContext] === SETTING RECOMMENDATIONS ===');
      print('🎯 [RecommendationContext] Count: ${recommendations.length}');
      for (int i = 0; i < recommendations.length; i++) {
        final rec = recommendations[i];
        print('🎯 [RecommendationContext] [$i] "${rec.trackTitle}" by "${rec.trackArtist}" (ID: ${rec.trackId})');
      }
      print('🎯 [RecommendationContext] ================================');
    }
    
    _currentRecommendations = recommendations;
    _isFromShuffleRecommendations = true;
    _lastTrackedTrack = null; // Reset tracking
    notifyListeners();
    
    if (kDebugMode) {
      print('✅ [RecommendationContext] Set ${recommendations.length} recommendations and notified listeners');
    }
  }
  
  /// Clear recommendations when normal playback starts
  void clearRecommendations() {
    if (_isFromShuffleRecommendations) {
      if (kDebugMode) {
        print('🧹 [RecommendationContext] === CLEARING RECOMMENDATIONS ===');
        print('🧹 [RecommendationContext] Previous count: ${_currentRecommendations?.length ?? 0}');
      }
      
      _currentRecommendations = null;
      _isFromShuffleRecommendations = false;
      _lastTrackedTrack = null;
      notifyListeners();
      
      if (kDebugMode) {
        print('🧹 [RecommendationContext] Cleared recommendations and notified listeners');
      }
    } else if (kDebugMode) {
      print('🧹 [RecommendationContext] clearRecommendations called but no recommendations were active');
    }
  }
  
  /// Force clear recommendations (for manual clearing)
  void forceClearRecommendations() {
    if (kDebugMode) {
      print('🧹 [RecommendationContext] === FORCE CLEARING RECOMMENDATIONS ===');
    }
    
    _currentRecommendations = null;
    _isFromShuffleRecommendations = false;
    _lastTrackedTrack = null;
    notifyListeners();
    
    if (kDebugMode) {
      print('🧹 [RecommendationContext] Force cleared recommendations and notified listeners');
    }
  }
  
  /// Check if a specific track is from recommendations
  bool isTrackFromRecommendations(MusicTrack track) {
    final result = getCurrentRecommendationForTrack(track) != null;
    if (kDebugMode) {
      print('🔍 [RecommendationContext] isTrackFromRecommendations("${track.title}"): $result');
    }
    return result;
  }
  
  /// Check if we have any recommendations loaded
  bool get hasRecommendations {
    final result = _currentRecommendations != null && _currentRecommendations!.isNotEmpty;
    if (kDebugMode) {
      print('🔍 [RecommendationContext] hasRecommendations: $result');
    }
    return result;
  }
} 