import 'package:flutter/foundation.dart';
import '../models/friend.dart';
import '../models/user.dart';
import '../services/friends_service.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../services/friends_cache_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

class FriendsProvider extends ChangeNotifier {
  late final FriendsService _friendsService;
  
  // State
  List<FriendshipData> _friends = [];
  List<FriendshipData> _searchResults = [];
  List<FriendRequest> _receivedRequests = [];
  List<FriendRequest> _sentRequests = [];
  bool _isLoading = false;
  bool _isSearching = false;
  String? _error;
  bool _isInitialized = false;
  String _currentSearchQuery = '';
  Timer? _searchDebounceTimer;
  DateTime? _lastFriendsLoad;
  DateTime? _lastRequestsLoad;
  static const Duration _cacheTimeout = Duration(minutes: 5);
  
  // Search results for user search (not just friends)
  List<User> _userSearchResults = [];
  List<User> get userSearchResults => _userSearchResults;

  // Suggested friends
  List<User> _suggestedFriends = [];
  List<User> get suggestedFriends => _suggestedFriends;
  
  // Getters
  List<FriendshipData> get friends => _friends;
  List<FriendshipData> get searchResults => _searchResults;
  List<FriendRequest> get receivedRequests => _receivedRequests;
  List<FriendRequest> get sentRequests => _sentRequests;
  bool get isLoading => _isLoading;
  bool get isSearching => _isSearching;
  String? get error => _error;
  bool get isInitialized => _isInitialized;
  String get currentSearchQuery => _currentSearchQuery;
  
  // Computed getters
  int get friendsCount => _friends.length;
  int get pendingRequestsCount => _receivedRequests.length;
  List<FriendshipData> get onlineFriends => 
      _friends.where((f) => f.isOnline || (f.lastActive != null && DateTime.now().difference(f.lastActive!).inMinutes < 15)).toList();
  
  FriendsProvider() {
    print('🔥 FriendsProvider: Constructor called');
    _initializeServices();
  }
  
  // Initialize services synchronously
  void _initializeServices() {
    try {
      print('🔥 FriendsProvider: Initializing services...');
      final apiService = ApiService();
      final authService = AuthService(apiService);
      
      // Initialize cache service asynchronously but don't wait for it
      _initializeCacheService().then((cacheService) {
        print('🔥 FriendsProvider: Cache service initialized');
        _friendsService = FriendsService(apiService, authService, cacheService);
        _isInitialized = true;
        print('🔥 FriendsProvider: Services initialized successfully');
        notifyListeners();
        
        // Auto-load data when initialized
        _autoLoadData();
      }).catchError((error) {
        print('🔥 FriendsProvider: Error initializing cache service: $error');
        _setError('Failed to initialize friends service: ${error.toString()}');
      });
    } catch (e) {
      print('🔥 FriendsProvider: Error in _initializeServices: $e');
      _setError('Failed to initialize friends provider: ${e.toString()}');
    }
  }
  
  // Initialize cache service asynchronously
  Future<FriendsCacheService> _initializeCacheService() async {
    print('🔥 FriendsProvider: Initializing cache service...');
    final prefs = await SharedPreferences.getInstance();
    return FriendsCacheService(prefs);
  }
  
  // Auto-load data when provider is initialized
  void _autoLoadData() {
    print('🔥 FriendsProvider: Auto-loading data...');
    // Load friends and requests automatically
    loadFriendsIfNeeded();
    loadFriendRequestsIfNeeded();
  }
  
  // Load all friends only if needed
  Future<void> loadFriendsIfNeeded() async {
    if (!_isInitialized) {
      print('🔥 FriendsProvider: Service not initialized yet');
      _setError('Friends service not initialized yet');
      return;
    }

    print('🔥 FriendsProvider: Checking if friends need to be loaded...');
    // Check if we need to reload based on cache timeout
    if (_lastFriendsLoad != null && 
        DateTime.now().difference(_lastFriendsLoad!) < _cacheTimeout &&
        _friends.isNotEmpty) {
      print('🔥 FriendsProvider: Using cached friends data');
      return;
    }
    
    await loadFriends();
  }
  
  // Load friend requests only if needed
  Future<void> loadFriendRequestsIfNeeded() async {
    if (!_isInitialized) {
      print('🔥 FriendsProvider: Service not initialized yet');
      _setError('Friends service not initialized yet');
      return;
    }

    print('🔥 FriendsProvider: Checking if requests need to be loaded...');
    // Check if we need to reload based on cache timeout
    if (_lastRequestsLoad != null && 
        DateTime.now().difference(_lastRequestsLoad!) < _cacheTimeout &&
        (_receivedRequests.isNotEmpty || _sentRequests.isNotEmpty)) {
      print('🔥 FriendsProvider: Using cached requests data');
      return;
    }
    
    await loadFriendRequests();
  }
  
  // Load all friends
  Future<void> loadFriends() async {
    if (!_isInitialized) {
      print('🔥 FriendsProvider: Service not initialized yet');
      _setError('Friends service not initialized yet');
      return;
    }
    
    print('🔥 FriendsProvider: Starting to load friends...');
    _setLoading(true);
    _clearError();
    
    try {
      final rawFriends = await _friendsService.getAllFriends();
      
      // Deduplicate friends by friendId to prevent duplicate Hero tags
      final seenIds = <String>{};
      _friends = rawFriends.where((friend) {
        final friendId = friend.friendId;
        if (seenIds.contains(friendId)) {
          print('🚨 [FriendsProvider] Removing duplicate friend: ${friend.name} (ID: $friendId)');
          return false;
        }
        seenIds.add(friendId);
        return true;
      }).toList();
      
      _lastFriendsLoad = DateTime.now();
      print('🔥 FriendsProvider: Loaded ${_friends.length} unique friends (after deduplication)');
      notifyListeners();
    } catch (e) {
      print('🔥 FriendsProvider: Error loading friends: $e');
      _setError('Failed to load friends: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }
  
  // Load friend requests
  Future<void> loadFriendRequests() async {
    if (!_isInitialized) {
      print('🔥 FriendsProvider: Service not initialized yet for requests');
      _setError('Friends service not initialized yet');
      return;
    }
    
    print('🔥 FriendsProvider: Starting to load friend requests...');
    _setLoading(true);
    _clearError();
    
    try {
      final results = await Future.wait([
        _friendsService.getReceivedRequests(),
        _friendsService.getSentRequests(),
      ]);
      
      _receivedRequests = results[0];
      _sentRequests = results[1];
      _lastRequestsLoad = DateTime.now();
      print('🔥 FriendsProvider: Loaded ${_receivedRequests.length} received and ${_sentRequests.length} sent requests');
      notifyListeners();
    } catch (e) {
      print('🔥 FriendsProvider: Error loading friend requests: $e');
      _setError('Failed to load friend requests: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }
  
  // Send friend request
  Future<bool> sendFriendRequest(int recipientId) async {
    if (!_isInitialized) {
      _setError('Friends service not initialized yet');
      return false;
    }
    
    _clearError();
    
    try {
      final request = await _friendsService.sendFriendRequest(recipientId);
      if (request != null) {
        _sentRequests.add(request);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to send friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Accept friend request
  Future<bool> acceptFriendRequest(String requestId) async {
    if (!_isInitialized) {
      _setError('Friends service not initialized yet');
      return false;
    }
    
    _clearError();
    
    try {
      final updatedRequest = await _friendsService.acceptFriendRequest(requestId);
      if (updatedRequest != null) {
        // Remove from received requests
        _receivedRequests.removeWhere((r) => r.id.toString() == requestId);
        // Reload friends list to include new friend
        await loadFriends();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to accept friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Reject friend request
  Future<bool> rejectFriendRequest(String requestId) async {
    if (!_isInitialized) {
      _setError('Friends service not initialized yet');
      return false;
    }
    
    _clearError();
    
    try {
      final updatedRequest = await _friendsService.rejectFriendRequest(requestId);
      if (updatedRequest != null) {
        _receivedRequests.removeWhere((r) => r.id.toString() == requestId);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to reject friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Cancel sent friend request
  Future<bool> cancelFriendRequest(String requestId) async {
    if (!_isInitialized) {
      _setError('Friends service not initialized yet');
      return false;
    }
    
    print('🔥 FriendsProvider: Cancelling friend request $requestId');
    print('🔥 FriendsProvider: Sent requests before cancel: ${_sentRequests.length}');
    
    _clearError();
    
    try {
      final result = await _friendsService.cancelFriendRequest(requestId);
      if (result != null) {
        print('🔥 FriendsProvider: Successfully cancelled request, removing from local state');
        _sentRequests.removeWhere((r) => r.id.toString() == requestId);
        print('🔥 FriendsProvider: Sent requests after cancel: ${_sentRequests.length}');
        notifyListeners();
        return true;
      }
      print('🔥 FriendsProvider: Failed to cancel request - service returned null');
      return false;
    } catch (e) {
      print('🔥 FriendsProvider: Error cancelling friend request: $e');
      _setError('Failed to cancel friend request: ${e.toString()}');
      return false;
    }
  }
  
  // Unfriend
  Future<bool> unfriend(String friendId) async {
    if (!_isInitialized) {
      _setError('Friends service not initialized yet');
      return false;
    }
    
    _clearError();
    
    try {
      final success = await _friendsService.unfriend(friendId);
      if (success) {
        _friends.removeWhere((f) => f.id.toString() == friendId);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to unfriend: ${e.toString()}');
      return false;
    }
  }
  
  // Search friends with debouncing and minimum length
  void searchFriends(String query) {
    _currentSearchQuery = query;
    // Cancel previous debounce timer
    _searchDebounceTimer?.cancel();
    final trimmed = query.trim();
    // Only search when query has at least 3 characters
    if (trimmed.length < 3) {
      // Clear any existing search results and searching state
      _searchResults = [];
      _isSearching = false;
      notifyListeners();
      return;
    }
    // Immediately show loading indicator for search
    _setSearching(true);
    // Debounce actual search call
    _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query);
    });
  }
  
  // Perform the actual search
  Future<void> _performSearch(String query) async {
    if (!_isInitialized) {
      _setError('Friends service not initialized yet');
      return;
    }
    
    print('🔍 FriendsProvider: Searching for "$query"...');
    _setSearching(true);
    _clearError();
    
    try {
      _searchResults = await _friendsService.searchFriends(query);
      print('🔍 FriendsProvider: Found ${_searchResults.length} results for "$query"');
      notifyListeners();
    } catch (e) {
      print('🔍 FriendsProvider: Error searching friends: $e');
      _setError('Failed to search friends: ${e.toString()}');
    } finally {
      _setSearching(false);
    }
  }
  
  // Clear search results
  void clearSearch() {
    _currentSearchQuery = '';
    _searchResults = [];
    _isSearching = false;
    _searchDebounceTimer?.cancel();
    notifyListeners();
  }
  
  // Get friend by ID
  FriendshipData? getFriendById(String friendId) {
    try {
      return _friends.firstWhere((f) => f.friendId == friendId);
    } catch (e) {
      return null;
    }
  }
  
  // Refresh all data
  Future<void> refresh() async {
    if (!_isInitialized) {
      _setError('Friends service not initialized yet');
      return;
    }
    
    await Future.wait([
      loadFriends(),
      loadFriendRequests(),
    ]);
    
    // Re-perform search if there's an active query
    if (_currentSearchQuery.isNotEmpty) {
      _performSearch(_currentSearchQuery);
    }
  }
  
  // Search all users (not just friends) for finding new people
  void searchAllUsers(String query) {
    _currentSearchQuery = query;
    // Cancel previous debounce timer
    _searchDebounceTimer?.cancel();
    final trimmed = query.trim();
    // Only search when query has at least 2 characters
    if (trimmed.length < 2) {
      // Clear any existing search results and searching state
      _userSearchResults = [];
      _isSearching = false;
      notifyListeners();
      return;
    }
    // Immediately show loading indicator for search
    _setSearching(true);
    // Debounce actual search call
    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _performUserSearch(query);
    });
  }

  // Perform the actual user search
  Future<void> _performUserSearch(String query) async {
    if (!_isInitialized) {
      _setError('Friends service not initialized yet');
      return;
    }
    
    print('🔍 FriendsProvider: Searching users for "$query"...');
    _setSearching(true);
    _clearError();
    
    try {
      _userSearchResults = await _friendsService.searchAllUsers(query);
      print('🔍 FriendsProvider: Found ${_userSearchResults.length} users for "$query"');
      notifyListeners();
    } catch (e) {
      print('🔍 FriendsProvider: Error searching users: $e');
      _setError('Failed to search users: ${e.toString()}');
    } finally {
      _setSearching(false);
    }
  }

  // Load suggested friends
  Future<void> loadSuggestedFriends() async {
    if (!_isInitialized) {
      _setError('Friends service not initialized yet');
      return;
    }
    
    print('🤝 FriendsProvider: Loading suggested friends...');
    _setLoading(true);
    _clearError();
    
    try {
      _suggestedFriends = await _friendsService.getSuggestedFriends();
      print('🤝 FriendsProvider: Loaded ${_suggestedFriends.length} suggested friends');
      
      // Debug: Print first few suggested friends
      for (int i = 0; i < _suggestedFriends.length && i < 3; i++) {
        final user = _suggestedFriends[i];
        print('🤝 Suggested friend $i: id=${user.id}, username=${user.username}, displayName=${user.displayName}, firstName=${user.firstName}, lastName=${user.lastName}');
      }
      
      notifyListeners();
    } catch (e) {
      print('🤝 FriendsProvider: Error loading suggested friends: $e');
      _setError('Failed to load suggested friends: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Clear user search results
  void clearUserSearch() {
    _currentSearchQuery = '';
    _userSearchResults = [];
    _isSearching = false;
    _searchDebounceTimer?.cancel();
    notifyListeners();
  }

  // Check if user is already a friend
  bool isAlreadyFriend(String userId) {
    return _friends.any((f) => f.friendId == userId);
  }

  // Check if there's a pending friend request
  bool hasPendingRequest(String userId) {
    return _sentRequests.any((request) => request.recipient.id.toString() == userId) ||
           _receivedRequests.any((request) => request.requester.id.toString() == userId);
  }

  // Check if the current user sent a friend request to this user (so they can cancel it)
  bool hasSentRequest(String userId) {
    return _sentRequests.any((request) => request.recipient.id.toString() == userId);
  }

  // Get the request ID for a sent request (for cancellation)
  String? getSentRequestId(String userId) {
    try {
      final request = _sentRequests.firstWhere((request) => request.recipient.id.toString() == userId);
      return request.id.toString();
    } catch (e) {
      return null;
    }
  }
  
  // Private helper methods
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }
  
  void _setSearching(bool value) {
    _isSearching = value;
    notifyListeners();
  }
  
  void _setError(String message) {
    _error = message;
    notifyListeners();
  }
  
  void _clearError() {
    _error = null;
  }
  
  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }
} 