import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../services/music/spotify_web_api_service.dart';
import '../models/music_track.dart';
import '../models/music/track.dart';
import '../services/secure_storage.dart';
import '../../services/music/hybrid_queue_manager.dart';
import '../services/music/apple_music_service.dart';
import '../providers/apple_music_provider.dart';
import '../providers/youtube_provider.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';

/// SpotifyProvider manages the state of Spotify playback using Web API
class SpotifyProvider with ChangeNotifier, WidgetsBindingObserver {
  final SpotifyWebApiService _webApiService = SpotifyWebApiService();
  final SecureStorage _secureStorage = SecureStorage();
  
  // State
  bool _isInitialized = false;
  bool _isConnected = false;
  bool _isConnecting = false;
  bool _isPlaying = false;
  bool _isPaused = true;
  MusicTrack? _currentTrack;
  int _position = 0;
  int _duration = 0;
  bool _hasActivePlayback = false;
  
  // 🔧 ENHANCEMENT: Track recent authentication success
  bool _justAuthenticated = false;
  DateTime? _lastAuthenticationTime;
  
  // User data
  Map<String, dynamic>? _userProfile;
  Map<String, dynamic>? _userStats;
  bool _isLoadingProfile = false;
  bool _isLoadingStats = false;
  
  // Error handling
  String? _errorMessage;
  bool _isPremiumUser = false;
  
  // Position update timer
  Timer? _positionTimer;
  final _positionController = StreamController<int>.broadcast();
  
  // Liked songs and playlists
  List<MusicTrack> _likedSongs = [];
  List<Map<String, dynamic>> _playlists = [];
  bool _isLoadingLikedSongs = false;
  bool _isLoadingPlaylists = false;
  int _likedSongsPage = 0;
  bool _hasMoreLikedSongs = true;
  int _totalLikedSongs = 0;
  
  // Recently played tracks state
  List<MusicTrack> _recentlyPlayedTracks = [];
  bool _isLoadingRecentlyPlayed = false;
  bool _hasMoreRecentlyPlayed = true;
  int? _nextBeforeTimestamp;
  
  // Caching and state
  final Map<String, List<MusicTrack>> _playlistTracksCache = {};
  DateTime _lastLikedSongsUpdate = DateTime(1970);
  final Duration _likedSongsCacheExpiry = const Duration(minutes: 30);
  final int _likedSongsPageSize = 50;
  
  Map<String, List<Map<String, dynamic>>>? _topArtistsCache;
  DateTime? _topArtistsCacheTime;
  final Duration _topArtistsCacheExpiry = const Duration(minutes: 15);
  
  List<Track>? _recentTracks;
  DateTime? _lastReconnectAttempt;
  DateTime? _lastPlaybackCheck;
  final Duration _playbackCheckInterval = const Duration(milliseconds: 1000);
  
  Timer? _fastPositionTimer;
  DateTime? _lastPositionUpdate;
  int _estimatedPosition = 0;
  
  bool _shuffleEnabled = false;
  String _repeatMode = 'off';
  
  Timer? _stateCheckTimer;
  bool _isCheckingState = false;
  
  final Map<String, int> _playlistPages = {};
  final Map<String, bool> _hasMorePlaylistTracks = {};
  final Map<String, int> _totalPlaylistTracks = {};
  final Map<String, bool> _isLoadingPlaylistTracks = {};
  final int _playlistPageSize = 50;
  
  // Add hybrid queue manager as a shared instance
  late final HybridQueueManager _hybridQueueManager;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isConnected => _isConnected;
  bool get isConnecting => _isConnecting;
  bool get isPlaying => _isPlaying;
  bool get isPaused => _isPaused;
  bool get hasActivePlayback => _hasActivePlayback;
  bool get isPremiumUser => _isPremiumUser;
  bool get isPremium => _isPremiumUser;
  MusicTrack? get currentTrack => _currentTrack;
  int get position => _position;
  int get duration => _duration;
  String? get errorMessage => _errorMessage;
  List<MusicTrack> get likedSongs => _likedSongs;
  List<Map<String, dynamic>> get playlists => _playlists;
  bool get isLoadingLikedSongs => _isLoadingLikedSongs;
  bool get isLoadingPlaylists => _isLoadingPlaylists;
  bool get hasMoreLikedSongs => _hasMoreLikedSongs;
  
  // Recently played getters
  List<MusicTrack> get recentlyPlayedTracks => _recentlyPlayedTracks;
  bool get isLoadingRecentlyPlayed => _isLoadingRecentlyPlayed;
  bool get hasMoreRecentlyPlayed => _hasMoreRecentlyPlayed;
  Map<String, dynamic>? get userProfile => _userProfile;
  Map<String, dynamic>? get userStats => _userStats;
  bool get isLoadingProfile => _isLoadingProfile;
  bool get isLoadingStats => _isLoadingStats;
  List<Track>? get recentTracks => _recentTracks;
  Map<String, List<Map<String, dynamic>>>? get topArtistsCache => _topArtistsCache;
  Stream<int> get positionStream => _positionController.stream;

  // Debug method to check user profile state
  void debugUserProfile() {
    if (kDebugMode) {
      print('🔍 [SpotifyProvider] Debug user profile state:');
      print('📧 Email: ${_userProfile?['email']}');
      print('👤 Display name: ${_userProfile?['display_name']}');
      print('🆔 ID: ${_userProfile?['id']}');
      print('📸 Images: ${_userProfile?['images']}');
      print('💎 Premium: $_isPremiumUser');
      print('🔗 Connected: $_isConnected');
    }
  }

  // No-op properties for compatibility
  bool get prioritizeProductionAuth => false;
  set prioritizeProductionAuth(bool value) {}
  void toggleProductionAuthPriority() {}
  
  SpotifyProvider() {
    WidgetsBinding.instance.addObserver(this);
    _init();
    // Initialize hybrid queue manager with the web API service
    _hybridQueueManager = HybridQueueManager(_webApiService);
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (kDebugMode) {
      print('🎵 Spotify lifecycle state changed: $state');
    }

    if (state == AppLifecycleState.resumed) {
      final now = DateTime.now();
      if (_isConnecting) return;
      if (_lastReconnectAttempt != null &&
          now.difference(_lastReconnectAttempt!) < Duration(seconds: 5)) return;
      _lastReconnectAttempt = now;
      _handleAppResume();
    } else if (state == AppLifecycleState.paused) {
      _savePlaybackState();
    } else if (state == AppLifecycleState.detached) {
      _cleanupResources();
    }
  }
  
  Future<void> _handleAppResume() async {
    try {
      await Future.delayed(const Duration(milliseconds: 1000));
      if (_isConnected) return;
      if (await _webApiService.isAuthenticated()) {
        await connect();
      }
    } catch (e) {
      if (kDebugMode) print('❌ Error handling app resume: $e');
    }
  }
  
  Future<void> _cleanupResources() async {
    _stopPositionTimer();
    _stopStateChecking();
    await _savePlaybackState();
  }
  
  Future<void> _savePlaybackState() async {
    try {
      if (!_isConnected || !_hasActivePlayback) return;
      
      final playbackState = await _webApiService.getCurrentPlayback();
      if (playbackState != null && playbackState['item'] != null) {
        final track = playbackState['item'];
        await _secureStorage.write(key: 'last_track_uri', value: track['uri']);
        await _secureStorage.write(key: 'was_playing', value: (playbackState['is_playing'] ?? false).toString());
        await _secureStorage.write(key: 'last_position', value: (playbackState['progress_ms'] ?? 0).toString());
      }
    } catch (e) {
      if (kDebugMode) print('⚠️ Error saving playback state: $e');
    }
  }
  
  Future<void> _init() async {
    if (_isInitialized) return;
    try {
      if (kDebugMode) print('🎵 [SpotifyProvider] Initializing...');
      
      // Check if we have stored tokens and automatically connect
      final isAuthenticated = await _webApiService.isAuthenticated();
      if (kDebugMode) print('🎵 [SpotifyProvider] Authentication check result: $isAuthenticated');
      
      if (isAuthenticated) {
        if (kDebugMode) print('🎵 [SpotifyProvider] Found stored tokens, auto-connecting...');
        _isConnected = true;
        _errorMessage = null; // Clear any previous error
        
        // Load user profile in background to set premium status
        try {
          await loadUserProfile();
          if (kDebugMode) print('✅ [SpotifyProvider] User profile loaded, premium: $_isPremiumUser');
        } catch (e) {
          if (kDebugMode) print('⚠️ [SpotifyProvider] Failed to load user profile: $e');
          // Don't fail initialization if profile loading fails
        }
        
        _startStateChecking();
        if (kDebugMode) print('✅ [SpotifyProvider] Auto-connection completed successfully');
      } else {
        if (kDebugMode) print('❌ [SpotifyProvider] No valid tokens found, user needs to authenticate');
        _isConnected = false;
      }
      
      _isInitialized = true;
      if (kDebugMode) print('✅ [SpotifyProvider] Initialization completed, connected: $_isConnected');
      notifyListeners();
    } catch (e) {
      if (kDebugMode) print('❌ [SpotifyProvider] Error in _init: $e');
      _isInitialized = true;
      _isConnected = false;
      notifyListeners();
    }
  }
  
  void _handleError(String error) {
    _errorMessage = error;
    if (error.toLowerCase().contains('token') || 
        error.toLowerCase().contains('auth') ||
        error.toLowerCase().contains('unauthorized')) {
      _isConnected = false;
    }
    notifyListeners();
  }

  /// Connect to Spotify using Web API
  Future<bool> connect() async {
    try {
      if (_isConnecting) {
        if (kDebugMode) print('🔄 [SpotifyProvider] Already connecting, returning current state: $_isConnected');
        return _isConnected;
      }
      if (_isConnected) {
        if (kDebugMode) print('✅ [SpotifyProvider] Already connected, returning true');
        return true;
      }

      if (kDebugMode) print('🎵 [SpotifyProvider] Starting Spotify Web API connection');
      
      _isConnecting = true;
      _errorMessage = null;
      notifyListeners();

      // 🔧 DEBUG: Add detailed token storage debugging
      if (kDebugMode) {
        print('🔍 [SpotifyProvider] Checking token storage status...');
        try {
          final storageStatus = await _webApiService.getTokenStorageStatus();
          print('🔍 [SpotifyProvider] Token storage status: $storageStatus');
        } catch (e) {
          print('❌ [SpotifyProvider] Error checking token storage: $e');
        }
      }

      // 🔧 ENHANCEMENT: Check if we just authenticated (within last 10 seconds)
      bool recentlyAuthenticated = false;
      if (_justAuthenticated && _lastAuthenticationTime != null) {
        final timeSinceAuth = DateTime.now().difference(_lastAuthenticationTime!);
        recentlyAuthenticated = timeSinceAuth.inSeconds < 10;
        if (kDebugMode) {
          print('🔍 [SpotifyProvider] Recently authenticated: $recentlyAuthenticated (${timeSinceAuth.inSeconds}s ago)');
        }
      } else {
        if (kDebugMode) {
          print('🔍 [SpotifyProvider] No recent authentication flag set (_justAuthenticated: $_justAuthenticated, _lastAuthenticationTime: $_lastAuthenticationTime)');
        }
      }

      bool isAuthenticated;
      if (recentlyAuthenticated) {
        // Skip complex token validation if we just authenticated
        if (kDebugMode) print('✅ [SpotifyProvider] Skipping token validation - recently authenticated');
        isAuthenticated = true;
        _justAuthenticated = false; // Reset flag
      } else {
        if (kDebugMode) print('🔍 [SpotifyProvider] Checking authentication via webApiService.isAuthenticated()...');
        try {
          isAuthenticated = await _webApiService.isAuthenticated();
          if (kDebugMode) print('🔐 [SpotifyProvider] Authentication check result: $isAuthenticated');
        } catch (e) {
          if (kDebugMode) print('❌ [SpotifyProvider] Error during authentication check: $e');
          isAuthenticated = false;
        }
        
        // 🔧 DEBUG: If not authenticated, try to get access token directly
        if (!isAuthenticated && kDebugMode) {
          print('🔍 [SpotifyProvider] Authentication failed, checking access token directly...');
          try {
            final accessToken = await _webApiService.getAccessToken();
            print('🔍 [SpotifyProvider] Direct access token check: ${accessToken != null ? 'EXISTS (length: ${accessToken!.length})' : 'NULL'}');
            
            // If we have a token but isAuthenticated returned false, there might be an issue
            if (accessToken != null && accessToken.isNotEmpty) {
              if (kDebugMode) print('⚠️ [SpotifyProvider] Token exists but isAuthenticated returned false - forcing authentication to true');
              isAuthenticated = true; // Override the false result
            }
          } catch (e) {
            print('❌ [SpotifyProvider] Error getting access token directly: $e');
          }
        }
      }
      
      if (kDebugMode) print('🎯 [SpotifyProvider] Final authentication decision: $isAuthenticated');
      
      if (isAuthenticated) {
        _isConnected = true;
        _isInitialized = true;
        if (kDebugMode) print('✅ [SpotifyProvider] Successfully connected to Spotify');
        
        try {
          if (kDebugMode) print('📋 [SpotifyProvider] Loading user profile...');
          await loadUserProfile();
          if (kDebugMode) print('✅ [SpotifyProvider] User profile loaded successfully');
        } catch (e) {
          if (kDebugMode) print('⚠️ [SpotifyProvider] Error loading user profile (continuing anyway): $e');
          // Don't fail connection just because profile loading failed
        }
        
        try {
          if (kDebugMode) print('⏰ [SpotifyProvider] Starting state checking...');
          _startStateChecking();
          if (kDebugMode) print('✅ [SpotifyProvider] State checking started successfully');
        } catch (e) {
          if (kDebugMode) print('⚠️ [SpotifyProvider] Error starting state checking (continuing anyway): $e');
          // Don't fail connection just because state checking failed
        }
        
        _isConnecting = false;
        notifyListeners();
        
        if (kDebugMode) print('🎉 [SpotifyProvider] Connection process completed successfully');
        return true;
      } else {
        if (kDebugMode) print('❌ [SpotifyProvider] Not authenticated - connection failed');
        _isConnecting = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      if (kDebugMode) print('❌ [SpotifyProvider] Error in connect method: $e');
      _isConnecting = false;
      _handleError('Error connecting to Spotify: $e');
      return false;
    }
  }

  /// Authenticate with Web API
  Future<bool> authenticate() async {
    try {
      if (kDebugMode) print('🔐 [SpotifyProvider] Starting Web API authentication');
      
      // Don't set _isConnecting here - let connect() handle it
      _errorMessage = null;
      notifyListeners();
      
      if (kDebugMode) print('📞 [SpotifyProvider] Calling _webApiService.authenticate()...');
      final success = await _webApiService.authenticate();
      if (kDebugMode) print('🔐 [SpotifyProvider] Authentication result: $success');
      
      if (success) {
        // 🔧 ENHANCEMENT: Set flag for recent authentication success
        if (kDebugMode) print('🏁 [SpotifyProvider] Setting authentication success flags...');
        _justAuthenticated = true;
        _lastAuthenticationTime = DateTime.now();
        if (kDebugMode) print('✅ [SpotifyProvider] Flags set - _justAuthenticated: $_justAuthenticated, _lastAuthenticationTime: $_lastAuthenticationTime');
        
        if (kDebugMode) print('🔄 [SpotifyProvider] Calling connect() after successful authentication...');
        final connected = await connect();
        if (kDebugMode) print('🔐 [SpotifyProvider] Post-auth connection result: $connected');
        
        return connected;
      } else {
        if (kDebugMode) print('❌ [SpotifyProvider] Authentication failed at webApiService level');
        _handleError('Authentication failed - please try again');
        return false;
      }
    } catch (e) {
      if (kDebugMode) print('❌ [SpotifyProvider] Authentication error: $e');
      _handleError('Authentication failed: $e');
      return false;
    }
  }

  /// Disconnect from Spotify
  Future<void> disconnect() async {
    try {
      await _webApiService.disconnect();
      _isConnected = false;
      _isConnecting = false;
      _isInitialized = false;
      _currentTrack = null;
      _isPlaying = false;
      _isPaused = true;
      _userProfile = null;
      _likedSongs = [];
      _playlists = [];
      _recentlyPlayedTracks = [];
      _hasMoreRecentlyPlayed = true;
      _nextBeforeTimestamp = null;
      _stopPositionTimer();
      _stopStateChecking();
      notifyListeners();
    } catch (e) {
      _handleError('Error disconnecting from Spotify: $e');
    }
  }

  /// Play a track using Web API with Apple Music fallback
  Future<bool> playTrack(MusicTrack track, {BuildContext? context}) async {
    try {
      if (!_isConnected) {
        final connected = await connect();
        if (!connected) {
          // Clear any Spotify error messages before trying Apple Music fallback
          if (context != null) {
            _errorMessage = null; // Clear Spotify connection error
            notifyListeners();
            return await _tryAppleMusicFallback(context, track, 'Spotify not connected');
          } else {
            throw Exception('Failed to connect to Spotify Web API and no context for fallback');
          }
        }
      }

      if (!_isPremiumUser) {
        // No Premium, try Apple Music fallback
        if (context != null) {
          _errorMessage = null; // Clear any previous errors
          notifyListeners();
          return await _tryAppleMusicFallback(context, track, 'Spotify Premium required');
        } else {
          throw Exception('Spotify Premium is required for playback control');
        }
      }

      final success = await _webApiService.playTrackWithRecommendations(track.uri);
      if (success) {
        // Clear any previous errors on success
        _errorMessage = null;
        _position = 0;
        _estimatedPosition = 0;
        _lastPositionUpdate = DateTime.now();
        _currentTrack = track;
        _isPlaying = true;
        _hasActivePlayback = true;
        _startIntensivePlaybackCheck();
        notifyListeners();
        return true;
      } else {
        // Spotify playback failed, try Apple Music fallback
        if (context != null) {
          _errorMessage = null; // Clear any previous errors
          notifyListeners();
          return await _tryAppleMusicFallback(context, track, 'Spotify playback failed');
        } else {
          throw Exception('Failed to start playback');
        }
      }
    } catch (e) {
      // Last resort - try Apple Music fallback if context available
      if (context != null) {
        _errorMessage = null; // Clear any previous errors
        notifyListeners();
        return await _tryAppleMusicFallback(context, track, e.toString());
      } else {
        final userFriendlyError = getSpotifyErrorMessage(e.toString());
        _handleError(userFriendlyError);
        return false;
      }
    }
  }

  void _startIntensivePlaybackCheck() {
    int checkCount = 0;
    Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (checkCount >= 25) {
        timer.cancel();
        return;
      }
      _checkPlaybackState();
      checkCount++;
    });
  }

  /// Resume playback
  Future<bool> resume() async {
    try {
      if (!_isPremiumUser) {
        _errorMessage = 'Premium account required for playback';
        notifyListeners();
        return false;
      }
      
      final success = await _webApiService.startPlayback();
      if (success) {
        _isPlaying = true;
        _lastPositionUpdate = DateTime.now();
        _startPositionTimer();
        _startIntensivePlaybackCheck();
        notifyListeners();
      }
      return success;
    } catch (e) {
      _handleError('Error resuming playback: $e');
      return false;
    }
  }

  /// Pause playback
  Future<bool> pause() async {
    try {
      if (!_isConnected || !_hasActivePlayback) {
        _isPlaying = false;
        notifyListeners();
        return true;
      }
      
      final success = await _webApiService.pausePlayback();
      if (success) {
        _isPlaying = false;
        _lastPositionUpdate = null;
        _stopPositionTimer();
        notifyListeners();
      }
      return success;
    } catch (e) {
      _handleError('Error pausing playback: $e');
      return false;
    }
  }

  /// Skip to next track
  Future<bool> skipNext() async {
    try {
      if (!_isPremiumUser) {
        _errorMessage = 'Premium account required for playback control';
        notifyListeners();
        return false;
      }
      
      if (!_isConnected || !_hasActivePlayback) return false;
      
      final success = await _webApiService.skipToNext();
      if (success) _startIntensivePlaybackCheck();
      return success;
    } catch (e) {
      _handleError('Error skipping to next track: $e');
      return false;
    }
  }

  /// Skip to previous track
  Future<bool> skipPrevious() async {
    try {
      if (!_isPremiumUser) {
        _errorMessage = 'Premium account required for playback control';
        notifyListeners();
        return false;
      }
      
      if (!_isConnected || !_hasActivePlayback) return false;
      
      final success = await _webApiService.skipToPrevious();
      if (success) _startIntensivePlaybackCheck();
      return success;
    } catch (e) {
      _handleError('Error skipping to previous track: $e');
      return false;
    }
  }

  /// Load user's liked songs
  Future<void> loadLikedSongs({bool refresh = false}) async {
    if (_isLoadingLikedSongs) return;
    
    if (!refresh && _likedSongs.isNotEmpty) {
      final now = DateTime.now();
      if (now.difference(_lastLikedSongsUpdate) < _likedSongsCacheExpiry) {
        return;
      }
    }
    
    _isLoadingLikedSongs = true;
    _likedSongsPage = 0;
    
    if (refresh) {
      _likedSongs = [];
      _hasMoreLikedSongs = true;
      _totalLikedSongs = 0;
    }
    
    notifyListeners();
    
    try {
      final result = await _webApiService.getSavedTracks(
        limit: _likedSongsPageSize,
        offset: _likedSongsPage * _likedSongsPageSize,
      );
      
      _likedSongs = result['tracks'];
      _totalLikedSongs = result['total'];
      _hasMoreLikedSongs = result['hasMore'];
      _likedSongsPage++;
      _lastLikedSongsUpdate = DateTime.now();
      
      _isLoadingLikedSongs = false;
      notifyListeners();
    } catch (e) {
      _isLoadingLikedSongs = false;
      _hasMoreLikedSongs = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Load more liked songs
  Future<void> loadMoreLikedSongs() async {
    if (_isLoadingLikedSongs || !_hasMoreLikedSongs) return;
    
    _isLoadingLikedSongs = true;
    notifyListeners();
    
    try {
      final result = await _webApiService.getSavedTracks(
        limit: _likedSongsPageSize,
        offset: _likedSongsPage * _likedSongsPageSize,
      );
      
      final newTracks = result['tracks'] as List<MusicTrack>;
      
      if (newTracks.isEmpty) {
        _hasMoreLikedSongs = false;
      } else {
        _likedSongs.addAll(newTracks);
        _hasMoreLikedSongs = result['hasMore'];
        _likedSongsPage++;
      }
      
      _isLoadingLikedSongs = false;
      notifyListeners();
    } catch (e) {
      _isLoadingLikedSongs = false;
      _hasMoreLikedSongs = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Load user's playlists
  Future<void> loadPlaylists({bool refresh = false}) async {
    if (!_isConnected) {
      final connected = await connect();
      if (!connected) return;
    }
    
    try {
      _isLoadingPlaylists = true;
      if (refresh) _playlists = [];
      
      notifyListeners();
      
      final playlists = await _webApiService.getUserPlaylists();
      _playlists = playlists;
      _isLoadingPlaylists = false;
      notifyListeners();
    } catch (e) {
      _handleError('Error loading playlists: $e');
      _isLoadingPlaylists = false;
      notifyListeners();
    }
  }

  /// Seek to position
  Future<bool> seekTo(int positionMs) async {
    try {
      if (!_isPremiumUser) {
        _errorMessage = 'Premium account required for playback control';
        notifyListeners();
        return false;
      }
      
      if (!_isConnected || !_hasActivePlayback) return false;
      
      final success = await _webApiService.seekToPosition(positionMs);
      if (success) {
        _position = positionMs;
        _estimatedPosition = positionMs;
        _lastPositionUpdate = DateTime.now();
        notifyListeners();
      }
      return success;
    } catch (e) {
      _handleError('Error seeking: $e');
      return false;
    }
  }

  /// Toggle shuffle mode
  Future<bool> toggleShuffle() async {
    try {
      final newState = !_shuffleEnabled;
      final success = await _webApiService.setShuffle(newState);
      if (success) {
        _shuffleEnabled = newState;
        notifyListeners();
      }
      return success;
    } catch (e) {
      return false;
    }
  }

  /// Set repeat mode
  Future<bool> setRepeatMode(String repeatMode) async {
    try {
      final success = await _webApiService.setRepeatMode(repeatMode);
      if (success) {
        _repeatMode = repeatMode;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _handleError('Error setting repeat mode: $e');
      return false;
    }
  }

  /// Search for tracks
  Future<List<MusicTrack>> searchTracks(String query) async {
    try {
      if (query.isEmpty) return [];
      return await _webApiService.searchTracks(query);
    } catch (e) {
      _handleError('Error searching tracks: $e');
      notifyListeners();
      return [];
    }
  }

  /// Load recently played tracks with pagination and duplicate removal
  Future<void> loadRecentlyPlayed({bool refresh = false}) async {
    if (_isLoadingRecentlyPlayed && !refresh) return;
    
    if (!_isConnected) {
      final connected = await connect();
      if (!connected) return;
    }
    
    _isLoadingRecentlyPlayed = true;
    
    if (refresh) {
      _recentlyPlayedTracks = [];
      _hasMoreRecentlyPlayed = true;
      _nextBeforeTimestamp = null;
    }
    
    notifyListeners();
    
    try {
      final result = await _webApiService.getRecentlyPlayed(
        limit: 50, // Use larger limit for recently played
        beforeTimestamp: _nextBeforeTimestamp,
      );
      
      final newTracks = result['tracks'] as List<MusicTrack>;
      
      if (refresh) {
        _recentlyPlayedTracks = newTracks;
      } else {
        // Merge new tracks and remove duplicates by track ID
        final existingIds = _recentlyPlayedTracks.map((t) => t.id).toSet();
        final uniqueNewTracks = newTracks.where((track) => !existingIds.contains(track.id)).toList();
        _recentlyPlayedTracks.addAll(uniqueNewTracks);
      }
      
      _hasMoreRecentlyPlayed = result['hasMore'] as bool;
      _nextBeforeTimestamp = result['nextBeforeTimestamp'] as int?;
      
      _isLoadingRecentlyPlayed = false;
      notifyListeners();
    } catch (e) {
      _isLoadingRecentlyPlayed = false;
      _hasMoreRecentlyPlayed = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Load more recently played tracks
  Future<void> loadMoreRecentlyPlayed() async {
    if (_isLoadingRecentlyPlayed || !_hasMoreRecentlyPlayed) return;
    
    _isLoadingRecentlyPlayed = true;
    notifyListeners();
    
    try {
      final result = await _webApiService.getRecentlyPlayed(
        limit: 50,
        beforeTimestamp: _nextBeforeTimestamp,
      );
      
      final newTracks = result['tracks'] as List<MusicTrack>;
      
      if (newTracks.isEmpty) {
        _hasMoreRecentlyPlayed = false;
      } else {
        // Remove duplicates by track ID
        final existingIds = _recentlyPlayedTracks.map((t) => t.id).toSet();
        final uniqueNewTracks = newTracks.where((track) => !existingIds.contains(track.id)).toList();
        _recentlyPlayedTracks.addAll(uniqueNewTracks);
        
        _hasMoreRecentlyPlayed = result['hasMore'] as bool;
        _nextBeforeTimestamp = result['nextBeforeTimestamp'] as int?;
      }
      
      _isLoadingRecentlyPlayed = false;
      notifyListeners();
    } catch (e) {
      _isLoadingRecentlyPlayed = false;
      _hasMoreRecentlyPlayed = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Get recently played tracks (compatibility method for existing code)
  Future<List<MusicTrack>> getRecentlyPlayed({int limit = 20}) async {
    try {
      final result = await _webApiService.getRecentlyPlayed(limit: limit);
      return result['tracks'] as List<MusicTrack>;
    } catch (e) {
      _handleError('Error getting recently played tracks: $e');
      notifyListeners();
      return [];
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Start position timer
  void _startPositionTimer() {
    _stopPositionTimer();
    
    _fastPositionTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (_isPlaying && _lastPositionUpdate != null) {
        final elapsed = DateTime.now().difference(_lastPositionUpdate!).inMilliseconds;
        _estimatedPosition = _position + elapsed;
        
        if (_duration > 0 && _estimatedPosition > _duration) {
          _estimatedPosition = _duration;
        }
        
        _positionController.add(_estimatedPosition);
        notifyListeners();
      }
    });
    
    _positionTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_isPlaying && _isConnected && _hasActivePlayback) {
        try {
          final playbackState = await _webApiService.getCurrentPlayback();
          if (playbackState != null) {
            final serverPosition = playbackState['progress_ms'] ?? 0;
            
            if ((_estimatedPosition - serverPosition).abs() < 1000) {
              _position = (_position * 0.7 + serverPosition * 0.3).round();
            } else {
              _position = serverPosition;
            }
            
            _estimatedPosition = _position;
            _lastPositionUpdate = DateTime.now();
            _positionController.add(_estimatedPosition);
          }
        } catch (e) {
          // Ignore position errors
        }
      }
    });
  }

  /// Stop position timer
  void _stopPositionTimer() {
    _positionTimer?.cancel();
    _positionTimer = null;
    _fastPositionTimer?.cancel();
    _fastPositionTimer = null;
  }

  /// Load user profile
  Future<Map<String, dynamic>?> loadUserProfile() async {
    if (!_isConnected) {
      final connected = await connect();
      if (!connected) return null;
    }
    
    try {
      _isLoadingProfile = true;
      notifyListeners();
      
      if (kDebugMode) {
        print('📋 [SpotifyProvider] Loading user profile...');
      }
      
      final profile = await _webApiService.getCurrentUserProfile();
      
      if (kDebugMode) {
        print('📋 [SpotifyProvider] Received profile from Web API: $profile');
        print('📧 [SpotifyProvider] Profile email: ${profile?['email']}');
        print('👤 [SpotifyProvider] Profile display_name: ${profile?['display_name']}');
      }
      
      _userProfile = profile;
      
      if (profile != null && profile.containsKey('product')) {
        _isPremiumUser = profile['product'] == 'premium';
      }
      
      _isLoadingProfile = false;
      notifyListeners();
      
      if (kDebugMode) {
        print('✅ [SpotifyProvider] User profile loaded and stored successfully');
        print('📧 [SpotifyProvider] Stored email: ${_userProfile?['email']}');
        print('👤 [SpotifyProvider] Stored display_name: ${_userProfile?['display_name']}');
      }
      
      return profile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyProvider] Error loading user profile: $e');
      }
      _handleError('Error loading user profile: $e');
      _isLoadingProfile = false;
      notifyListeners();
      return null;
    }
  }

  /// Start state checking
  void _startStateChecking() {
    _stopStateChecking();
    
    if (!_isConnected) return;
    
    _stateCheckTimer = Timer.periodic(const Duration(milliseconds: 2000), (_) {
      if (!_isCheckingState && _isConnected) {
        _checkPlaybackState();
      }
    });
    
    Timer(const Duration(milliseconds: 500), () {
      if (_isConnected && !_isCheckingState) {
        _checkPlaybackState();
      }
    });
  }

  /// Stop state checking
  void _stopStateChecking() {
    _stateCheckTimer?.cancel();
    _stateCheckTimer = null;
  }

  /// Check playback state
  Future<void> _checkPlaybackState() async {
    if (_isCheckingState) return;
    
    try {
      _isCheckingState = true;
      
      final now = DateTime.now();
      if (_lastPlaybackCheck != null && 
          now.difference(_lastPlaybackCheck!) < _playbackCheckInterval) {
        return;
      }
      _lastPlaybackCheck = now;

      if (!_isConnected) return;

      final playbackState = await _webApiService.getCurrentPlayback();
      
      if (playbackState != null) {
        _isPlaying = playbackState['is_playing'] ?? false;
        _isPaused = !_isPlaying;
        _hasActivePlayback = playbackState['item'] != null;
        
        if (_hasActivePlayback) {
          _position = playbackState['progress_ms'] ?? 0;
          final item = playbackState['item'];
          _duration = item['duration_ms'] ?? 0;
          
          if (_isPlaying && _lastPositionUpdate == null) {
            _estimatedPosition = _position;
            _lastPositionUpdate = DateTime.now();
          } else if (_isPlaying) {
            _estimatedPosition = _position;
            _lastPositionUpdate = DateTime.now();
          }
          
          if (item != null) {
            // Extract ISRC from external_ids
            String? isrc;
            if (item['external_ids'] != null && item['external_ids']['isrc'] != null) {
              isrc = item['external_ids']['isrc'];
            }

            final newTrack = MusicTrack(
              id: item['id'] ?? '',
              title: item['name'] ?? 'Unknown Track',
              artist: item['artists']?.isNotEmpty == true
                  ? item['artists'][0]['name'] ?? 'Unknown Artist'
                  : 'Unknown Artist',
              album: item['album']?['name'] ?? '',
              albumArt: item['album']?['images']?.isNotEmpty == true
                  ? item['album']['images'][0]['url']
                  : null,
              uri: item['uri'] ?? '',
              durationMs: item['duration_ms'] ?? 0,
              url: item['external_urls']?['spotify'] ?? '',
              service: 'spotify',
              serviceType: 'spotify',
              genres: [],
              explicit: item['explicit'] ?? false,
              popularity: item['popularity'] ?? 0,
              isrc: isrc,
            );
            
            if (_currentTrack?.uri != newTrack.uri) {
              _currentTrack = newTrack;
              // Notify queue manager about track change to clean up consumed tracks
              _hybridQueueManager.updateCurrentPlayingState(playbackState);
            }
          }
        } else {
          _isPlaying = false;
          _isPaused = true;
        }
        
        _handlePlaybackStateChange();
        notifyListeners();
      } else {
        if (_hasActivePlayback) {
          _hasActivePlayback = false;
          _isPlaying = false;
          _isPaused = true;
          notifyListeners();
        }
      }
    } catch (e) {
      if (e.toString().toLowerCase().contains('connection')) {
        _isConnected = false;
        _hasActivePlayback = false;
        notifyListeners();
      }
    } finally {
      _isCheckingState = false;
    }
  }

  /// Handle playback state changes
  void _handlePlaybackStateChange() {
    if (_isPlaying && _hasActivePlayback) {
      if (_lastPositionUpdate == null) {
        _estimatedPosition = _position;
        _lastPositionUpdate = DateTime.now();
      }
      
      if (_positionTimer == null || _fastPositionTimer == null) {
        _startPositionTimer();
      }
    } else {
      _lastPositionUpdate = null;
      if (!_isPlaying) {
        _stopPositionTimer();
      }
    }
  }

  String getSpotifyErrorMessage(String error) {
    if (error.toLowerCase().contains('premium')) {
      return 'Spotify Premium is required for playback control. Please upgrade your Spotify account.';
    } else if (error.toLowerCase().contains('you need to open spotify app on your phone')) {
      return 'You need to open Spotify app on your phone';
    } else if (error.toLowerCase().contains('device') || error.toLowerCase().contains('no active device')) {
      return 'No active Spotify device found. Please:\n• Open Spotify on your phone/computer\n• Start playing any song and pause it\n• Then try again';
    } else if (error.toLowerCase().contains('no_active_device')) {
      return 'No active Spotify device found. Please:\n• Open Spotify on your phone/computer\n• Start playing any song and pause it\n• Then try again';
    } else {
      return 'Error with Spotify playback: $error';
    }
  }

  /// Mock track methods for demo
  void setMockTrack(MusicTrack track) {
    _currentTrack = track;
    _isPlaying = true;
    _isPaused = false;
    _hasActivePlayback = true;
    _duration = track.durationMs;
    _position = 0;
    _estimatedPosition = 0;
    _lastPositionUpdate = DateTime.now();
    _startPositionTimer();
    notifyListeners();
  }

  void clearMockTrack() {
    _isPlaying = false;
    _isPaused = true;
    _hasActivePlayback = false;
    _currentTrack = null;
    _position = 0;
    _duration = 0;
    _estimatedPosition = 0;
    _lastPositionUpdate = null;
    _stopPositionTimer();
    notifyListeners();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _stopPositionTimer();
    _stopStateChecking();
    _positionController.close();
    super.dispose();
  }

  // Additional getters for compatibility
  double get playbackPosition => duration <= 0 ? 0.0 : position / duration;
  int get currentPositionMs => position;
  bool get shuffleEnabled => _shuffleEnabled;
  String get repeatMode => _repeatMode;
  int get totalLikedSongs => _totalLikedSongs;
  double get likedSongsProgress => _totalLikedSongs > 0 ? _likedSongs.length / _totalLikedSongs : 0.0;

  bool isLoadingPlaylistTracks(String playlistId) => _isLoadingPlaylistTracks[playlistId] ?? false;
  bool hasMorePlaylistTracks(String playlistId) => _hasMorePlaylistTracks[playlistId] ?? false;
  int totalPlaylistTracks(String playlistId) => _totalPlaylistTracks[playlistId] ?? 0;

  /// Toggle repeat mode
  Future<bool> toggleRepeatMode() async {
    try {
      String newMode;
      switch (_repeatMode) {
        case 'off':
          newMode = 'track';
          break;
        case 'track':
          newMode = 'context';
          break;
        default:
          newMode = 'off';
      }
      
      final success = await _webApiService.setRepeatMode(newMode);
      if (success) {
        _repeatMode = newMode;
        notifyListeners();
      }
      return success;
    } catch (e) {
      return false;
    }
  }

  // Compatibility methods
  void setCurrentTrack(MusicTrack track) {
    _currentTrack = track;
    notifyListeners();
  }

  void setIsPlaying(bool playing) {
    _isPlaying = playing;
    notifyListeners();
  }

  void setRecentTracks(List<Track> tracks) {
    _recentTracks = tracks;
    notifyListeners();
  }

  // No-op methods for compatibility
  Future<bool> isUsingDebugTokens() async => false;
  Future<String> getConnectionTypeLabel() async => 'Web API';
  void invalidateTopArtistsCache() {}
  Future<Map<String, List<Map<String, dynamic>>>> getCachedTopArtists() async => {};
  Future<List<MusicTrack>> getUserPlaylists() async => [];
  Future<bool> isSpotifyAppAvailable() async => true;
  
  // Additional SpotifyWebApiService methods exposed through provider
  Future<List<MusicTrack>> getTopTracks({String timeRange = 'medium_term', int limit = 20}) async {
    try {
      if (!_isConnected) {
        final connected = await connect();
        if (!connected) return [];
      }
      return await _webApiService.getTopTracks(timeRange: timeRange, limit: limit);
    } catch (e) {
      _handleError('Error getting top tracks: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getTopArtists({String timeRange = 'medium_term', int limit = 20}) async {
    try {
      if (!_isConnected) {
        final connected = await connect();
        if (!connected) return [];
      }
      return await _webApiService.getTopArtists(timeRange: timeRange, limit: limit);
    } catch (e) {
      _handleError('Error getting top artists: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getUserPlaylistsFromApi() async {
    try {
      if (!_isConnected) {
        final connected = await connect();
        if (!connected) return [];
      }
      return await _webApiService.getUserPlaylists();
    } catch (e) {
      _handleError('Error getting user playlists: $e');
      return [];
    }
  }

  // Placeholder methods for features not yet implemented
  Future<Map<String, dynamic>?> loadUserStats() async => null;
  
  /// Get playlist tracks using the web API service
  Future<List<MusicTrack>> getPlaylistTracks(String playlistId) async {
    try {
      if (!_isConnected) {
        final connected = await connect();
        if (!connected) return [];
      }
      
      // Initialize pagination state for this playlist
      _playlistPages[playlistId] = 0;
      _hasMorePlaylistTracks[playlistId] = true;
      _isLoadingPlaylistTracks[playlistId] = false;
      _playlistTracksCache[playlistId] = [];
      
      final result = await _webApiService.getPlaylistTracks(
        playlistId,
        limit: _playlistPageSize,
        offset: 0,
      );
      
      // Store the tracks and metadata
      final tracks = result['tracks'] as List<MusicTrack>;
      _playlistTracksCache[playlistId] = tracks;
      _totalPlaylistTracks[playlistId] = result['total'] as int? ?? 0;
      _hasMorePlaylistTracks[playlistId] = result['hasMore'] as bool? ?? false;
      _playlistPages[playlistId] = 1; // First page loaded
      
      if (kDebugMode) {
        print('✅ [SpotifyProvider] Loaded playlist $playlistId: ${tracks.length} tracks, total: ${_totalPlaylistTracks[playlistId]}');
      }
      
      return tracks;
    } catch (e) {
      _handleError('Error getting playlist tracks: $e');
      return [];
    }
  }
  
  Future<List<MusicTrack>> loadMorePlaylistTracks(String playlistId) async {
    // Check if we're already loading or if there are no more tracks
    if (_isLoadingPlaylistTracks[playlistId] == true || 
        _hasMorePlaylistTracks[playlistId] == false) {
      return _playlistTracksCache[playlistId] ?? [];
    }
    
    try {
      if (!_isConnected) {
        final connected = await connect();
        if (!connected) return _playlistTracksCache[playlistId] ?? [];
      }
      
      _isLoadingPlaylistTracks[playlistId] = true;
      notifyListeners();
      
      final currentPage = _playlistPages[playlistId] ?? 0;
      final offset = currentPage * _playlistPageSize;
      
      if (kDebugMode) {
        print('🔄 [SpotifyProvider] Loading more tracks for playlist $playlistId (page: $currentPage, offset: $offset)');
      }
      
      final result = await _webApiService.getPlaylistTracks(
        playlistId,
        limit: _playlistPageSize,
        offset: offset,
      );
      
      final newTracks = result['tracks'] as List<MusicTrack>;
      
      if (newTracks.isEmpty) {
        _hasMorePlaylistTracks[playlistId] = false;
      } else {
        // Add new tracks to the cache
        if (_playlistTracksCache[playlistId] == null) {
          _playlistTracksCache[playlistId] = [];
        }
        _playlistTracksCache[playlistId]!.addAll(newTracks);
        _hasMorePlaylistTracks[playlistId] = result['hasMore'] as bool? ?? false;
        _playlistPages[playlistId] = currentPage + 1;
        
        if (kDebugMode) {
          print('✅ [SpotifyProvider] Loaded ${newTracks.length} more tracks for playlist $playlistId (total cached: ${_playlistTracksCache[playlistId]!.length})');
        }
      }
      
      _isLoadingPlaylistTracks[playlistId] = false;
      notifyListeners();
      
      return _playlistTracksCache[playlistId] ?? [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyProvider] Error loading more playlist tracks: $e');
      }
      _isLoadingPlaylistTracks[playlistId] = false;
      _hasMorePlaylistTracks[playlistId] = false;
      notifyListeners();
      return _playlistTracksCache[playlistId] ?? [];
    }
  }
  
  /// Get user's current queue
  Future<Map<String, dynamic>?> getUserQueue() async {
    try {
      if (!_isConnected) {
        final connected = await connect();
        if (!connected) return null;
      }
      
      final result = await _webApiService.getUserQueue();
      
      if (kDebugMode && result != null) {
        print('✅ [SpotifyProvider] Loaded queue: ${(result['queue'] as List).length} tracks');
      }
      
      return result;
    } catch (e) {
      _handleError('Error getting user queue: $e');
      return null;
    }
  }
  
  /// Expose the web API service for advanced functionality
  SpotifyWebApiService get webApiService => _webApiService;

  /// Get the shared hybrid queue manager instance
  HybridQueueManager get hybridQueueManager => _hybridQueueManager;
  
  /// Play multiple tracks with fresh context (replaces entire queue)
  Future<bool> playMultipleTracks(List<MusicTrack> tracks) async {
    try {
      if (!_isConnected) {
        final connected = await connect();
        if (!connected) return false;
      }
      
      if (tracks.isEmpty) return false;
      
      // Convert tracks to URIs
      final trackUris = tracks.map((track) => track.uri ?? 'spotify:track:${track.id}').toList();
      
      if (kDebugMode) {
        print('🎵 [SpotifyProvider] Playing ${tracks.length} tracks with fresh context');
      }
      
      // Use Web API to start fresh playback context
      final success = await _webApiService.startPlayback(trackUris: trackUris);
      
      return success;
    } catch (e) {
      _handleError('Error playing multiple tracks: $e');
      return false;
    }
  }

  /// Try Apple Music fallback when Spotify is unavailable, with YouTube as final fallback
  Future<bool> _tryAppleMusicFallback(BuildContext context, MusicTrack track, String spotifyReason) async {
    try {
      if (kDebugMode) {
        print('🍎 [SpotifyProvider] Attempting Apple Music fallback for: ${track.title}');
        print('🍎 [SpotifyProvider] Spotify reason: $spotifyReason');
      }
      
      // Get Apple Music provider
      final appleMusicProvider = Provider.of<AppleMusicProvider>(context, listen: false);
      
      // Check if Apple Music is available
      if (!appleMusicProvider.isConnected) {
        if (kDebugMode) {
          print('🍎 [SpotifyProvider] Apple Music not connected, attempting to connect...');
        }
        
        final connected = await appleMusicProvider.connect();
        if (!connected) {
          if (kDebugMode) {
            print('❌ [SpotifyProvider] Apple Music connection failed, trying YouTube...');
          }
          return await _tryYouTubeFallback(context, track, spotifyReason, 'Apple Music connection failed');
        }

        // Show connecting message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                ),
                SizedBox(width: 12),
                Text('Trying Apple Music...'),
              ],
            ),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 3),
          ),
        );
        
      }
      
      // Search for the track on Apple Music
      if (kDebugMode) {
        print('🔍 [SpotifyProvider] Searching Apple Music for: "${track.title}" by "${track.artist}"');
      }
      
      final apiService = ApiService();
      final appleMusicService = AppleMusicService(apiService, AuthService(apiService));
      final foundTrack = await appleMusicService.searchTrackByArtistAndTitle(
        track.artist,
        track.title,
      );
      
      if (foundTrack == null) {
        if (kDebugMode) {
          print('❌ [SpotifyProvider] Could not find track on Apple Music, trying YouTube...');
        }
        return await _tryYouTubeFallback(context, track, spotifyReason, 'Track not found on Apple Music');
      }
      
      // Verify this is a good match using the existing matching logic
      final matchScore = track.calculateMatchScore(foundTrack);
      
      if (kDebugMode) {
        print('🔍 [SpotifyProvider] Apple Music match analysis:');
        print('   Requested: ${track.matchDebugString}');
        print('   Found: ${foundTrack.matchDebugString}');
        print('   Match Score: ${matchScore.toStringAsFixed(1)}');
      }
      
      // Only play if we have a good match
      if (!track.isGoodMatch(foundTrack)) {
        if (kDebugMode) {
          print('❌ [SpotifyProvider] Apple Music match quality too low (${matchScore.toStringAsFixed(1)}) - trying YouTube...');
        }
        return await _tryYouTubeFallback(context, track, spotifyReason, 'No exact match on Apple Music');
      }
      
      // Try playing on Apple Music
      final success = await appleMusicProvider.playTrack(foundTrack);
      
      if (success) {
        if (kDebugMode) {
          print('✅ [SpotifyProvider] Successfully played track on Apple Music: ${foundTrack.title}');
        }
        
        // Show success with information about the fallback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Now playing: ${foundTrack.title} (Apple Music)'),
                const SizedBox(height: 2),
                Text(
                  'Fallback from Spotify: $spotifyReason',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.blue, // Different color to indicate fallback
            duration: const Duration(seconds: 4),
          ),
        );
        return true;
      } else {
        // Apple Music also failed
        String appleMusicError = 'Apple Music playback failed';
        if (appleMusicProvider.errorMessage != null) {
          appleMusicError = appleMusicProvider.errorMessage!;
        }
        
        if (kDebugMode) {
          print('❌ [SpotifyProvider] Apple Music fallback also failed: $appleMusicError');
        }
        
        return await _tryYouTubeFallback(context, track, spotifyReason, appleMusicError);
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyProvider] Apple Music fallback error: $e');
      }
      
      return await _tryYouTubeFallback(context, track, spotifyReason, e.toString());
    }
  }

  /// Try YouTube fallback when both Spotify and Apple Music fail
  Future<bool> _tryYouTubeFallback(BuildContext context, MusicTrack track, String spotifyReason, String appleMusicReason) async {
    try {
      if (kDebugMode) {
        print('🎬 [SpotifyProvider] Attempting YouTube fallback for: ${track.title}');
        print('🎬 [SpotifyProvider] Spotify reason: $spotifyReason');
        print('🎬 [SpotifyProvider] Apple Music reason: $appleMusicReason');
      }
      
      // Get YouTube provider
      final youtubeProvider = Provider.of<YouTubeProvider>(context, listen: false);
      
      // Check if YouTube is initialized
      if (!youtubeProvider.isInitialized) {
        if (kDebugMode) {
          print('🎬 [SpotifyProvider] YouTube not initialized, attempting to initialize...');
        }
        
        // Show connecting message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                ),
                SizedBox(width: 12),
                Text('Trying YouTube...'),
              ],
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
        
        final initialized = await youtubeProvider.initialize();
        if (!initialized) {
          if (kDebugMode) {
            print('❌ [SpotifyProvider] YouTube initialization failed');
          }
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('All services failed for "${track.title}"'),
                  const SizedBox(height: 4),
                  Text(
                    'Spotify: $spotifyReason',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                  Text(
                    'Apple Music: $appleMusicReason',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                  Text(
                    'YouTube: Initialization failed',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 6),
            ),
          );
          return false;
        }
      }
      
      // Try playing on YouTube
      final success = await youtubeProvider.playTrack(track);
      
      if (success) {
        if (kDebugMode) {
          print('✅ [SpotifyProvider] Successfully played track on YouTube: ${track.title}');
        }
        
        // Show success with information about the fallback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Now playing: ${track.title} (YouTube)'),
              ],
            ),
            backgroundColor: Colors.red, // Different color to indicate final fallback
            duration: const Duration(seconds: 4),
          ),
        );
        return true;
      } else {
        if (kDebugMode) {
          print('❌ [SpotifyProvider] YouTube fallback also failed');
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('All services failed for "${track.title}"'),
                const SizedBox(height: 4),
                Text(
                  'Spotify: $spotifyReason',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
                Text(
                  'Apple Music: $appleMusicReason',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
                Text(
                  'YouTube: Playback failed',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 6),
          ),
        );
        return false;
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyProvider] YouTube fallback error: $e');
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('All services failed for "${track.title}"'),
              const SizedBox(height: 4),
              Text(
                'Spotify: $spotifyReason',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
              Text(
                'Apple Music: $appleMusicReason',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
              Text(
                'YouTube: ${e.toString()}',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 6),
        ),
      );
      return false;
    }
  }

  /// Play a track from pin data (cross-platform compatibility)
  Future<bool> playTrackFromPin(Map<String, dynamic> pinData, {BuildContext? context}) async {
    try {
      if (kDebugMode) {
        print('🎵 [SpotifyProvider] Attempting to play track from pin data');
      }

      // Extract track information from pin data
      final title = pinData['title'] ?? pinData['track_title'] ?? pinData['name'];
      final artist = pinData['artist'] ?? pinData['track_artist'];
      final spotifyUri = pinData['uri'];
      final spotifyUrl = pinData['url'];

      if (title == null || title.toString().isEmpty) {
        if (kDebugMode) {
          print('❌ [SpotifyProvider] No track title found in pin data');
        }
        _handleError('Track information is incomplete');
        return false;
      }

      if (artist == null || artist.toString().isEmpty) {
        if (kDebugMode) {
          print('❌ [SpotifyProvider] No artist found in pin data');
        }
        _handleError('Artist information is missing');
        return false;
      }

      // If we have a Spotify URI and this is the original Spotify track, try to play it directly
      if (spotifyUri != null && spotifyUri.toString().startsWith('spotify:')) {
        if (kDebugMode) {
          print('🎵 [SpotifyProvider] Found Spotify URI, attempting direct playback: $spotifyUri');
        }

        // Create a MusicTrack object for the URI
        final track = MusicTrack(
          id: spotifyUri.toString().split(':').last,
          title: title.toString(),
          artist: artist.toString(),
          album: pinData['album'] ?? '',
          albumArt: pinData['artwork_url'] ?? pinData['albumArt'] ?? pinData['album_art'] ?? '',
          uri: spotifyUri.toString(),
          url: spotifyUrl?.toString() ?? '',
          service: 'spotify',
          serviceType: 'spotify',
          genres: [],
          durationMs: 0,
          popularity: 0,
        );

        final success = await playTrack(track, context: context);
        if (success) {
          if (kDebugMode) {
            print('✅ [SpotifyProvider] Successfully played track via direct URI');
          }
          return true;
        } else {
          if (kDebugMode) {
            print('⚠️ [SpotifyProvider] Direct URI playback failed, falling back to search');
          }
        }
      }

      // Search for the track by artist and title
      if (kDebugMode) {
        print('🔍 [SpotifyProvider] Searching for track: \"$title\" by \"$artist\"');
      }

      final foundTrack = await _webApiService.searchTrackByArtistAndTitle(
        artist.toString(),
        title.toString(),
      );

      if (foundTrack == null) {
        if (kDebugMode) {
          print('❌ [SpotifyProvider] Could not find track on Spotify');
        }
        
        // Try Apple Music fallback if context is available
        if (context != null) {
          _errorMessage = null; // Clear any previous errors
          notifyListeners();
          
          // Create a temporary MusicTrack for the fallback
          final tempTrack = MusicTrack(
            id: 'temp_${title}_${artist}',
            title: title.toString(),
            artist: artist.toString(),
            album: pinData['album']?.toString() ?? '',
            albumArt: pinData['artwork_url']?.toString() ?? '',
            url: '',
            service: 'search',
            serviceType: 'search',
            genres: [],
            durationMs: 0,
            popularity: 0,
            uri: 'temp:track:search',
          );
          
          return await _tryAppleMusicFallback(context, tempTrack, 'Track not available on Spotify');
        } else {
          _handleError('Track not available on Spotify');
          return false;
        }
      }

      // 🔍 ENHANCED: Verify this is actually a good match using the new matching utilities
      final requestedTrack = MusicTrack(
        id: 'search_query',
        title: title.toString(),
        artist: artist.toString(),
        album: pinData['album']?.toString() ?? '',
        albumArt: '',
        url: '',
        service: 'search',
        serviceType: 'search',
        genres: [],
        durationMs: 0,
        popularity: 0,
        uri: 'search:track:query',
      );

      final matchScore = requestedTrack.calculateMatchScore(foundTrack);
      
      if (kDebugMode) {
        print('🔍 [SpotifyProvider] Match analysis:');
        print('   Requested: ${requestedTrack.matchDebugString}');
        print('   Found: ${foundTrack.matchDebugString}');
        print('   Match Score: ${matchScore.toStringAsFixed(1)}');
      }

      // Only play if we have a good match to avoid playing wrong songs
      if (requestedTrack.isExcellentMatch(foundTrack)) {
        if (kDebugMode) {
          print('✅ [SpotifyProvider] EXCELLENT match found - playing exact song');
        }
      } else if (requestedTrack.isGoodMatch(foundTrack)) {
        if (kDebugMode) {
          print('✅ [SpotifyProvider] GOOD match found - playing similar song');
        }
      } else {
        if (kDebugMode) {
          print('❌ [SpotifyProvider] Match quality too low (${matchScore.toStringAsFixed(1)}) - rejecting to avoid wrong song');
          print('❌ [SpotifyProvider] User requested: \"$title\" by \"$artist\"');
          print('❌ [SpotifyProvider] Would have played: \"${foundTrack.title}\" by \"${foundTrack.artist}\"');
        }
        
        // Try Apple Music fallback if context is available
        if (context != null) {
          _errorMessage = null; // Clear any previous errors
          notifyListeners();
          
          // Create a temporary MusicTrack for the fallback
          final tempTrack = MusicTrack(
            id: 'temp_${title}_${artist}',
            title: title.toString(),
            artist: artist.toString(),
            album: pinData['album']?.toString() ?? '',
            albumArt: pinData['artwork_url']?.toString() ?? '',
            url: '',
            service: 'search',
            serviceType: 'search',
            genres: [],
            durationMs: 0,
            popularity: 0,
            uri: 'temp:track:search',
          );
          
          return await _tryAppleMusicFallback(context, tempTrack, 'No exact match found for \"$title\" by \"$artist\" on Spotify');
        } else {
          _handleError('No exact match found for \"$title\" by \"$artist\" on Spotify');
          return false;
        }
      }

      // Play the verified track with Apple Music fallback
      final success = await playTrack(foundTrack, context: context);
      if (success) {
        if (kDebugMode) {
          print('✅ [SpotifyProvider] Successfully played exact match: \"${foundTrack.title}\" by \"${foundTrack.artist}\"');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ [SpotifyProvider] Failed to play found track');
        }
        return false;
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ [SpotifyProvider] Error playing track from pin: $e');
      }
      _handleError('Error playing track: $e');
      return false;
    }
  }
} 