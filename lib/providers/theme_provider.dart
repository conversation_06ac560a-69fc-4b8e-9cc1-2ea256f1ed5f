import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/theme_constants.dart';
import '../config/themes.dart';
import 'package:provider/provider.dart';

class ThemeProvider with ChangeNotifier {
  static const String _themePreferenceKey = 'theme_mode';
  static const String _themeColorKey = 'theme_color';
  
  ThemeMode _themeMode = ThemeMode.system;
  Color _primaryColor = themeColorPink; // Default to pink
  bool _isThemeLoaded = false;
  
  ThemeMode get themeMode => _themeMode;
  
  Color get primaryColor => _primaryColor;
  
  bool get isThemeLoaded => _isThemeLoaded;
  
  bool get isDarkMode {
    if (_themeMode == ThemeMode.system) {
      // Get the system brightness
      final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
      return brightness == Brightness.dark;
    }
    return _themeMode == ThemeMode.dark;
  }
  
  ThemeProvider() {
    _initTheme();
  }
  
  Future<void> _initTheme() async {
    await _loadThemePreference();
    await _loadThemeColor();
    _isThemeLoaded = true;
    notifyListeners();
  }
  
  Future<void> _loadThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTheme = prefs.getString(_themePreferenceKey);
      
      if (savedTheme != null) {
        _themeMode = ThemeMode.values.firstWhere(
          (mode) => mode.toString() == savedTheme,
          orElse: () => ThemeMode.system,
        );
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading theme preference: $e');
    }
  }
  
  Future<void> _loadThemeColor() async {
    final prefs = await SharedPreferences.getInstance();
    final colorValue = prefs.getInt(_themeColorKey);
    if (colorValue != null) {
      _primaryColor = Color(colorValue);
      notifyListeners();
    }
  }
  
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_themePreferenceKey, mode.toString());
      } catch (e) {
        debugPrint('Error saving theme preference: $e');
      }
    }
  }
  
  Future<void> toggleTheme() async {
    if (_themeMode == ThemeMode.light) {
      await setThemeMode(ThemeMode.dark);
    } else if (_themeMode == ThemeMode.dark) {
      await setThemeMode(ThemeMode.light);
    } else {
      // If system, check current brightness and set opposite
      final isDark = WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
      await setThemeMode(isDark ? ThemeMode.light : ThemeMode.dark);
    }
  }
  
  Future<void> setPrimaryColor(Color color) async {
    _primaryColor = color;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_themeColorKey, color.value);
    notifyListeners();
  }
  
  // Helper method to get all available theme colors
  List<Color> get availableColors => [
    themeColorPink,            // Pink - main theme color
    themeColorCyan,            // Cyan - main theme color  
    themeColorBlue,            // Blue - main theme color
    AppTheme.rockColor,        // Red
    AppTheme.hiphopColor,      // Green
    AppTheme.electronicColor,  // Blue
    AppTheme.jazzColor,        // Orange
    const Color(0xFF9C27B0),   // Purple
    const Color(0xFF607D8B),   // Blue Grey
    const Color(0xFF795548),   // Brown
  ];
  
  // Helper method to get color name
  String getColorName(Color color) {
    if (color.value == themeColorPink.value) return 'Pink';
    if (color.value == themeColorCyan.value) return 'Cyan';
    if (color.value == themeColorBlue.value) return 'Blue';
    if (color.value == AppTheme.rockColor.value) return 'Red';
    if (color.value == AppTheme.hiphopColor.value) return 'Green';
    if (color.value == AppTheme.electronicColor.value) return 'Blue';
    if (color.value == AppTheme.jazzColor.value) return 'Orange';
    if (color.value == const Color(0xFF9C27B0).value) return 'Purple';
    if (color.value == const Color(0xFF607D8B).value) return 'Grey';
    if (color.value == const Color(0xFF795548).value) return 'Brown';
    return 'Unknown';
  }
  
  static Future<void> ensureThemeLoaded(BuildContext context) async {
    final provider = Provider.of<ThemeProvider>(context, listen: false);
    if (!provider.isThemeLoaded) {
      // Wait until the theme is loaded
      while (!provider.isThemeLoaded) {
        await Future.delayed(const Duration(milliseconds: 10));
      }
    }
  }
} 