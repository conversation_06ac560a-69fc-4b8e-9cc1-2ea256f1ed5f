import 'package:flutter/material.dart';
import '../models/weekly_challenge.dart';
import '../services/api/weekly_challenges_api_service.dart';

class WeeklyChallengesProvider extends ChangeNotifier {
  final WeeklyChallengesApiService _apiService;
  
  List<WeeklyChallenge> _challenges = [];
  bool _isLoading = false;
  String? _error;
  
  WeeklyChallengesProvider(this._apiService);
  
  // Getters
  List<WeeklyChallenge> get challenges => _challenges;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  Future<void> loadChallenges() async {
    if (_isLoading) return;
    
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();
      
      debugPrint('Loading weekly challenges...');
      final challenges = await _apiService.getWeeklyChallenges();
      
      _challenges = challenges;
      debugPrint('Loaded ${_challenges.length} challenges');
      for (var challenge in _challenges) {
        debugPrint('Challenge ${challenge.id}: hasParticipated = ${challenge.hasParticipated}');
      }
      _error = null;
    } catch (e) {
      debugPrint('Error loading challenges: $e');
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  Future<bool> participateInChallenge({
    required String challengeId,
    required Map<String, dynamic> songData,
    double? latitude,
    double? longitude,
  }) async {
    try {
      final response = await _apiService.participateInChallenge(
        challengeId: challengeId,
        songData: songData,
        latitude: latitude,
        longitude: longitude,
      );
      
      if (response.success) {
        // Refresh challenges to update entry count
        await loadChallenges();
        return true;
      } else {
        // Check for specific error messages
        if (response.statusCode == 400 && response.data != null) {
          final errorMessage = response.data['message'] ?? response.data['error'] ?? response.message;
          if (errorMessage?.toString().toLowerCase().contains('already participated') == true) {
            throw 'already participated';
          }
          _error = errorMessage?.toString() ?? response.userFriendlyMessage;
        } else {
          _error = response.userFriendlyMessage;
        }
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      rethrow;  // Rethrow to allow handling in the UI
    }
  }
} 