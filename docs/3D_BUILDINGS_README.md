# 3D Buildings Implementation in BOPMaps

This document provides a comprehensive overview of how 3D buildings are implemented in the BOPMaps application, with a focus on the toggling functionality provided by `MapSettingsProvider`.

## Overview of Components

The 3D buildings implementation involves multiple components:

1. **MapSettingsProvider**: The central provider class storing user preferences for map settings
2. **FlutterMapWidget**: The main map widget that renders the map and all its layers
3. **MultiLevelRenderer**: The component responsible for rendering different map layers at appropriate zoom levels
4. **OSMBuildingsLayer**: The specific layer that renders 3D buildings
5. **ZoomLevelManager**: Determines rendering parameters for different zoom levels
6. **SettingsScreen**: Provides toggles for map settings, including 3D buildings

## MapSettingsProvider

Located in `lib/providers/map_settings_provider.dart`, this provider class is responsible for:

- Storing and managing map setting preferences
- Persisting settings between app sessions using SharedPreferences
- Notifying listeners of settings changes

Key components:

```dart
class MapSettingsProvider with ChangeNotifier {
  // Default values
  bool _showFeatureInfo = false; // Disabled by default
  bool _use3DBuildings = true;   // Enabled by default
  bool _showDebugInfo = false;
  
  // Getters
  bool get showFeatureInfo => _showFeatureInfo;
  bool get use3DBuildings => _use3DBuildings;
  bool get showDebugInfo => _showDebugInfo;
  
  // Preference keys
  static const String _keyShowFeatureInfo = 'map_show_feature_info';
  static const String _keyUse3DBuildings = 'map_use_3d_buildings';
  static const String _keyShowDebugInfo = 'map_show_debug_info';
  
  // Toggle 3D buildings
  Future<void> toggle3DBuildings() async {
    _use3DBuildings = !_use3DBuildings;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyUse3DBuildings, _use3DBuildings);
    } catch (e) {
      debugPrint('Error saving 3D buildings setting: $e');
    }
  }
}
```

## SettingsScreen

Located in `lib/screens/profile/settings_screen.dart`, this screen provides UI toggles for various map settings.

The 3D buildings toggle is implemented as:

```dart
Consumer<MapSettingsProvider>(
  builder: (context, mapSettings, child) {
    return SwitchListTile(
      title: const Text('3D Buildings'),
      subtitle: const Text('Show 3D building models on map'),
      value: mapSettings.use3DBuildings,
      onChanged: (value) {
        mapSettings.toggle3DBuildings();
      },
    );
  },
),
```

## FlutterMapWidget and Connection to MapSettingsProvider

Located in `lib/widgets/map/flutter_map_widget.dart`, this widget is responsible for rendering the map. It accesses the `MapSettingsProvider` through `Provider.of` in several places:

1. In the `_handleMapTap` method to check if feature info is enabled
2. In the map widget's build method via a `Consumer` widget

The 3D buildings setting connection is found in the map's handling of taps and in the rendering of map layers. The `MapSettingsProvider` is accessed to determine if buildings should be rendered in 3D.

## MultiLevelRenderer

Located in `lib/widgets/map/map_caching/multi_level_renderer.dart`, this class is responsible for efficiently managing different visual representations across zoom levels.

The critical connection point is in the `_buildZoomLevelLayers` method:

```dart
Widget _buildZoomLevelLayers() {
  // ...
  
  // Calculate an effective tilt factor that increases with zoom
  final double zoomEnhancedTilt = _calculateZoomEnhancedTilt();
  
  // ...
  
  // Buildings Layer
  if (_renderParams['showBuildings'] == true)
    OSMBuildingsLayer(
      tiltFactor: zoomEnhancedTilt,
      zoomLevel: widget.zoomLevel,
      isMapMoving: widget.isMapMoving,
      visibleBounds: widget.visibleBounds,
      enhancedDetail: _renderParams['detailLevel'] == 'high',
      detailLevel: _getDetailLevel(_renderParams['detailLevel']),
      mapCamera: widget.mapCamera,
    ),
  
  // ...
}
```

And the tilt calculation that respects the 3D rendering toggle:

```dart
// Calculate a tilt factor that enhances with zoom level
double _calculateZoomEnhancedTilt() {
  // Only apply zoom enhancement if 3D rendering is enabled
  if (_renderParams['render3D'] != true) return 0.0;
  
  // Base tilt from widget property
  final double baseTilt = widget.tiltFactor;
  
  // Enhance tilt based on zoom level for more dramatic 3D effect at high zooms
  if (widget.zoomLevel <= 14) return baseTilt;
  if (widget.zoomLevel >= 20) return baseTilt * 1.5; // 50% increase at maximum zoom
  
  // Linear interpolation between zoom 14 and 20
  final double zoomFactor = (widget.zoomLevel - 14) / 6;
  return baseTilt * (1.0 + zoomFactor * 0.5);
}
```

## ZoomLevelManager

Located in `lib/widgets/map/map_caching/zoom_level_manager.dart`, this class manages zoom levels and provides rendering parameters.

The key method that controls 3D rendering parameters:

```dart
Map<String, dynamic> getOptimizedRenderingParameters() {
  // ...
  
  // Adjust parameters based on zoom level
  switch (_currentZoomLevel) {
    // ...
    
    case 3: // Regional view
      params['showBuildings'] = true;
      params['showRoads'] = true;
      params['showWater'] = true;
      params['showParks'] = true;
      params['render3D'] = !_is2DModeActive;  // This is where 2D/3D mode is determined
      params['detailLevel'] = 'low';
      params['showLabels'] = true;
      break;
    
    // ...
  }
  
  return params;
}
```

## Integration Flow

The flow of information for 3D buildings toggle works as follows:

1. User toggles the "3D Buildings" switch in SettingsScreen
2. MapSettingsProvider updates the `_use3DBuildings` value and notifies listeners
3. FlutterMapWidget, which listens to MapSettingsProvider, receives the notification
4. The `render3D` parameter is updated accordingly when FlutterMapWidget passes it to MultiLevelRenderer
5. MultiLevelRenderer sets appropriate tilt values based on the `render3D` parameter
6. OSMBuildingsLayer receives the tilt factor and renders buildings with appropriate 3D effect

## Current Implementation Status

From the code analysis, we can observe:

1. The UI for toggling 3D buildings is fully implemented in SettingsScreen
2. The MapSettingsProvider correctly stores and persists the setting
3. The rendering logic in MultiLevelRenderer is set up to support conditional 3D rendering

## Missing Connection

The current implementation is missing a direct connection between the MapSettingsProvider's `use3DBuildings` value and the `render3D` parameter in the ZoomLevelManager or MultiLevelRenderer. 

To fully implement the toggle functionality, the map widget needs to:

1. Access `MapSettingsProvider.use3DBuildings` value when rendering
2. Pass this value to the MultiLevelRenderer or ZoomLevelManager
3. Set the `render3D` parameter based on this value

## Implementation Notes

- The MultiLevelRenderer checks `_renderParams['render3D']` to determine if 3D effects should be applied
- When `render3D` is false, the tilt factor is set to 0, effectively rendering the buildings in 2D
- The ZoomLevelManager sets `render3D = !_is2DModeActive` but doesn't directly reference MapSettingsProvider

## Solution Direction

To connect the MapSettingsProvider to the rendering, the FlutterMapWidget should:

```dart
// When creating layers for the map
Widget _buildZoomLevelLayers(BuildContext context) {
  // Get map settings
  final mapSettings = Provider.of<MapSettingsProvider>(context);
  
  // Override render3D parameter based on user setting
  final params = _zoomManager.getOptimizedRenderingParameters();
  params['render3D'] = params['render3D'] && mapSettings.use3DBuildings;
  
  // Use the updated parameters for rendering
  // ...
}
```

Or directly in the ZoomLevelManager:

```dart
Map<String, dynamic> getOptimizedRenderingParameters(BuildContext context) {
  // Get map settings
  final mapSettings = Provider.of<MapSettingsProvider>(context);
  
  // ...
  
  // Apply user preference
  params['render3D'] = params['render3D'] && mapSettings.use3DBuildings;
  
  return params;
}
```

## Implementing the Complete Solution

To implement the complete solution connecting the MapSettingsProvider's `use3DBuildings` setting to the rendering pipeline, follow these steps:

### 1. Modify the MultiLevelRenderer to Accept the 3D Buildings Setting

Update the `MultiLevelRenderer` class to explicitly receive the 3D buildings setting:

```dart
// lib/widgets/map/map_caching/multi_level_renderer.dart

class MultiLevelRenderer extends StatefulWidget {
  final LatLngBounds visibleBounds;
  final double zoomLevel;
  final bool isMapMoving;
  final double tiltFactor;
  final String theme;
  final MapCamera? mapCamera;
  final bool use3DBuildings; // Add this parameter
  
  const MultiLevelRenderer({
    Key? key,
    required this.visibleBounds,
    required this.zoomLevel,
    required this.isMapMoving,
    required this.tiltFactor,
    this.theme = 'vibrant',
    this.mapCamera,
    this.use3DBuildings = true, // Default to true for backward compatibility
  }) : super(key: key);

  @override
  State<MultiLevelRenderer> createState() => _MultiLevelRendererState();
}
```

### 2. Update the _calculateZoomEnhancedTilt Method

Modify the tilt calculation method to take into account both the render3D parameter and the use3DBuildings setting:

```dart
// Inside _MultiLevelRendererState class
double _calculateZoomEnhancedTilt() {
  // Check both render3D from params and use3DBuildings setting
  if (_renderParams['render3D'] != true || !widget.use3DBuildings) return 0.0;
  
  // Base tilt from widget property
  final double baseTilt = widget.tiltFactor;
  
  // Enhance tilt based on zoom level for more dramatic 3D effect at high zooms
  if (widget.zoomLevel <= 14) return baseTilt;
  if (widget.zoomLevel >= 20) return baseTilt * 1.5; // 50% increase at maximum zoom
  
  // Linear interpolation between zoom 14 and 20
  final double zoomFactor = (widget.zoomLevel - 14) / 6;
  return baseTilt * (1.0 + zoomFactor * 0.5);
}
```

### 3. Connect MapSettingsProvider in FlutterMapWidget

Update the `FlutterMapWidget` to pass the correct 3D buildings setting to the MultiLevelRenderer:

```dart
// lib/widgets/map/flutter_map_widget.dart

@override
Widget build(BuildContext context) {
  // Create markers from pins using the factory
  final markers = MarkersFactory.createMarkers(widget.mapProvider.pins, widget.onPinTap);
  
  // Get map settings
  final mapSettings = Provider.of<MapSettingsProvider>(context);
  
  return AnimatedBuilder(
    animation: Listenable.merge([_tiltAnimation, _stateManager]),
    builder: (context, child) {
      return Stack(
        children: [
          // ... Other code ...
          
          // When building the map using OSM layers:
          Stack(
            children: MapLayersBuilder.buildOSMDataLayers(
              tiltValue: _tiltAnimation.value,
              zoomLevel: _stateManager.currentZoom,
              isMapMoving: _controllerWrapper.isMoving || _isZooming,
              visibleBounds: _stateManager.visibleBounds,
              detailLevel: _stateManager.buildingDetailLevel,
              mapCamera: mapCamera,
              emergencyPerformanceMode: _emergencyPerformanceMode,
              use3DBuildings: mapSettings.use3DBuildings, // Pass the setting here
            ),
          ),
          
          // ... Rest of your build method ...
        ],
      );
    },
  );
}
```

### 4. Update MapLayersBuilder to Forward the Setting

Update the `MapLayersBuilder` class to pass the setting to the MultiLevelRenderer:

```dart
// lib/widgets/map/components/map_layers_builder.dart

static List<Widget> buildOSMDataLayers({
  required double tiltValue,
  required double zoomLevel,
  required bool isMapMoving,
  required LatLngBounds visibleBounds,
  int detailLevel = 3,
  MapCamera? mapCamera,
  bool emergencyPerformanceMode = false,
  bool use3DBuildings = true, // Add this parameter
}) {
  // ... Existing code ...
  
  return [
    // ... Other layers ...
    
    // When adding the MultiLevelRenderer:
    MultiLevelRenderer(
      visibleBounds: visibleBounds,
      zoomLevel: zoomLevel,
      isMapMoving: isMapMoving,
      tiltFactor: tiltValue,
      mapCamera: mapCamera,
      use3DBuildings: use3DBuildings, // Pass the setting here
    ),
    
    // ... Other layers ...
  ];
}
```

### 5. Verify the ZoomLevelManager

Ensure that the ZoomLevelManager's `getOptimizedRenderingParameters` method correctly respects the 3D setting:

```dart
// lib/widgets/map/map_caching/zoom_level_manager.dart

// Update method to be aware of the external 3D setting
Map<String, dynamic> getOptimizedRenderingParameters(bool use3DBuildings) {
  Map<String, dynamic> params = {
    // Default parameters...
  };
  
  // ... Existing logic ...
  
  // For any zoom level case that sets render3D, add the condition:
  params['render3D'] = !_is2DModeActive && use3DBuildings;
  
  return params;
}
```

### 6. Testing the Implementation

After making these changes, test the implementation by:

1. Running the app and going to the Settings screen
2. Toggling the 3D Buildings setting on and off
3. Observing the map to ensure buildings are rendered in 3D only when the setting is enabled
4. Verifying that the setting persists between app sessions

### 7. Refresh and Responsiveness

To ensure the map updates immediately when the setting changes, you may need to add a listener to the MapSettingsProvider:

```dart
@override
void initState() {
  super.initState();
  
  // ... Other initState code ...
  
  // Add a listener to the MapSettingsProvider
  final mapSettings = Provider.of<MapSettingsProvider>(context, listen: false);
  mapSettings.addListener(_onMapSettingsChanged);
}

void _onMapSettingsChanged() {
  // Force a rebuild of the map when settings change
  if (mounted) {
    setState(() {
      // No need to change any state variables, just trigger a rebuild
    });
  }
}

@override
void dispose() {
  // Remove the listener
  final mapSettings = Provider.of<MapSettingsProvider>(context, listen: false);
  mapSettings.removeListener(_onMapSettingsChanged);
  
  // ... Other dispose code ...
  super.dispose();
}
```

This implementation ensures that changes to the 3D buildings setting are properly reflected in the rendering pipeline, allowing users to toggle between 2D and 3D building representations on the map. 

## Implementation Status

The 3D buildings toggle implementation is now complete. The following files have been modified:

1. **lib/widgets/map/flutter_map_widget.dart**:
   - Added access to `MapSettingsProvider` in the build method
   - Passed the `use3DBuildings` setting to map layers
   - Added a listener to update the map when settings change

2. **lib/widgets/map/components/map_layers_builder.dart**:
   - Updated `buildOSMDataLayers` to accept `use3DBuildings` parameter
   - Made OSMBuildingsLayer conditional based on the setting
   - Updated `buildDecorativeLayers` to also respect the setting

3. **lib/widgets/map/map_caching/multi_level_renderer.dart**:
   - Added `use3DBuildings` parameter to the widget
   - Modified `_calculateZoomEnhancedTilt` to check both rendering mode and settings
   - Added didUpdateWidget handler to respond to setting changes

4. **lib/widgets/map/core/map_state_manager.dart**:
   - Added `_use3DBuildings` state variable and associated getter
   - Added `setUse3DBuildings` method to update the state
   - Added state preservation in cache for navigation

5. **lib/widgets/map/core/map_controller_wrapper.dart**:
   - Fixed compatibility issues with flutter_map version

These changes ensure that the 3D buildings setting is properly connected throughout the rendering pipeline and that changes to the setting take effect immediately. 