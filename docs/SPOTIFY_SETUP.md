# Spotify Integration Setup Guide

## Requirements

### 1. Spotify Premium Account
- **Required**: Spotify Premium is necessary for playback control through the app
- Free Spotify accounts can only browse tracks but cannot control playback
- Upgrade at: https://www.spotify.com/premium/

### 2. Spotify App Installation
- **iOS**: Install from the App Store
- **Android**: Install from Google Play Store
- **macOS**: Install from the Mac App Store or Spotify website

### 3. Spotify App Setup
1. Open the Spotify app
2. Log in with your Premium account
3. Start playing any song (this activates your device)
4. Keep Spotify running in the background

## Troubleshooting Common Issues

### "AppRemote is null" Errors
This usually means the Spotify SDK cannot connect to the Spotify app:

**Solutions:**
1. Make sure Spotify is installed and running
2. Log out and log back into Spotify
3. Restart the Spotify app
4. Restart your device
5. Make sure you're logged into the same Spotify account in both apps

### "No Active Device" Errors
This means Spotify doesn't recognize your device as active:

**Solutions:**
1. Open Spotify and play any song
2. Go to Spotify's "Connect to a device" menu and select your device
3. Make sure Spotify has proper permissions on your device

### "Premium Required" Errors
**Solution:** Upgrade to Spotify Premium - this is required for playback control

### Connection Issues
If the app keeps opening Spotify instead of playing through BOPMaps:

**Solutions:**
1. Force close both apps
2. Open Spotify first and play a song
3. Then open BOPMaps and try playing a track
4. Make sure both apps are using the same Spotify account

## How Playback Works

1. **Authentication**: BOPMaps authenticates with Spotify using OAuth
2. **SDK Connection**: The app connects to Spotify's SDK for playback control
3. **Device Activation**: Your device must be an active Spotify device
4. **Playback Control**: BOPMaps sends playback commands to Spotify

## Best Practices

1. **Keep Spotify Open**: Always have Spotify running in the background
2. **Start with Spotify**: Play something in Spotify first to activate your device
3. **Same Account**: Use the same Spotify account in both apps
4. **Stable Connection**: Ensure you have a stable internet connection
5. **App Permissions**: Grant all necessary permissions to both apps

## Development Notes

### Debug Mode
In debug mode, the app may use mock tokens for testing. For full functionality:
1. Set `prioritizeProductionAuth = true` in the Spotify provider
2. Use real Spotify credentials in your `.env` file
3. Ensure your Spotify app is properly configured in the Spotify Developer Dashboard

### Environment Variables
Make sure your `.env` file contains:
```
SPOTIFY_CLIENT_ID=your_client_id
SPOTIFY_CLIENT_SECRET=your_client_secret
SPOTIFY_REDIRECT_URI=bopmaps://callback
```

## Support

If you continue to experience issues:
1. Check the console logs for specific error messages
2. Verify your Spotify Premium subscription is active
3. Try the troubleshooting steps above
4. Contact support with specific error messages 