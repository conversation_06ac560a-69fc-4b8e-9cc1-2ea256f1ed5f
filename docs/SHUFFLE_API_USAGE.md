# Shuffle API Implementation

## Overview

I've enhanced your music queue system with a comprehensive shuffle API that properly integrates with your existing `HybridQueueManager`. The new implementation provides multiple shuffle algorithms and seamlessly works with both Spotify and Apple Music.

## What's New

### 1. Enhanced HybridQueueManager (`lib/services/music/hybrid_queue_manager.dart`)

Added new methods:
- `shuffleAndQueueTracks()` - Shuffles and queues multiple tracks
- `queueMultipleTracks()` - Queues multiple tracks in order
- `shuffleLocalQueue()` - Shuffles the current local queue

### 2. New ShuffleService (`lib/services/music/shuffle_service.dart`)

A unified service that provides:
- **Multiple shuffle algorithms:**
  - `Random` - Pure random shuffle
  - `Weighted` - Popular songs appear earlier
  - `Artist Spaced` - Avoids consecutive songs by same artist

- **Cross-platform support** for both Spotify and Apple Music
- **Smart platform detection** based on collection content
- **Rate limiting protection** to avoid API limits
- **Comprehensive error handling**

### 3. Updated Collection Screen (`lib/screens/collections/collection_detail_screen.dart`)

Enhanced shuffle button with:
- **Shuffle options dialog** - User can choose shuffle algorithm
- **Loading states** - Visual feedback during shuffle operations
- **Better error handling** - Clear success/error messages
- **Queue integration** - Properly uses your hybrid queue manager

## How to Use

### Basic Usage

```dart
import '../../services/music/shuffle_service.dart';

// In your collection screen
final result = await ShuffleService.shuffleCollection(
  pins: _filteredPins,
  spotifyProvider: spotifyProvider,
  appleMusicProvider: appleMusicProvider,
  mode: ShuffleMode.random, // or any other mode
  clearExistingQueue: true,
  maxTracks: 50,
);

if (result.success) {
  // Handle success
  print('Shuffled ${result.tracksQueued} tracks');
} else {
  // Handle error
  print('Error: ${result.message}');
}
```

### Available Shuffle Modes

1. **ShuffleMode.random** - Classic random shuffle
2. **ShuffleMode.weighted** - Popular songs have higher chance to appear earlier
3. **ShuffleMode.artistSpaced** - Prevents consecutive songs by same artist

### Queue Manager Integration

```dart
// Add shuffled tracks to queue
final success = await hybridQueueManager.shuffleAndQueueTracks(
  tracks,
  clearExisting: true,
);

// Just shuffle existing queue
hybridQueueManager.shuffleLocalQueue();
```

## Key Features

### 1. Smart Platform Detection
The service automatically detects which music platform has the most tracks in your collection and uses that service for optimal playback.

### 2. Hybrid Queue Integration
- Plays the first track immediately to start playback
- Queues remaining tracks in the background
- Works with your existing queue management system

### 3. Rate Limiting Protection
- Adds delays between API calls to prevent rate limiting
- Limits maximum queue size to prevent overwhelming the system

### 4. User Experience
- Loading states during shuffle operations
- Clear success/error messages
- Options dialog for choosing shuffle algorithm

## Fix Required

In `lib/screens/collections/collection_detail_screen.dart`, remove this line:
```dart
import '../../models/shuffle_mode.dart'; // Remove this line
```

The `ShuffleMode` enum is already included in the shuffle service.

## Benefits Over Previous Implementation

1. **Actually uses your queue system** - The old implementation just shuffled and played one song
2. **Multiple algorithms** - Users can choose their preferred shuffle style
3. **Cross-platform** - Works with both Spotify and Apple Music
4. **Better UX** - Loading states, options, clear feedback
5. **Rate limit safe** - Won't overwhelm music service APIs
6. **Proper error handling** - Graceful failures with user feedback

## Future Enhancements

You could further enhance this by:
- Adding user preferences for default shuffle mode
- Implementing smart shuffle based on listening history
- Adding shuffle history to avoid repeating recent shuffles
- Creating playlist-specific shuffle algorithms

The shuffle API is now properly integrated with your hybrid queue manager and provides a much better user experience than the previous basic implementation! 