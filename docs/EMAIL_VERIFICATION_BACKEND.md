# Email Verification Code Backend Implementation

This document outlines the Django backend implementation for email verification codes in the BOPMaps authentication system.

## Models

Add these models to your `models.py`:

```python
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import random
import string

class EmailVerificationCode(models.Model):
    email = models.EmailField()
    code = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    attempts = models.IntegerField(default=0)
    
    class Meta:
        db_table = 'email_verification_codes'
        
    def save(self, *args, **kwargs):
        if not self.pk:  # Only set expiry on creation
            self.expires_at = timezone.now() + timezone.timedelta(minutes=10)  # 10 minute expiry
        super().save(*args, **kwargs)
    
    @property
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    @property
    def is_valid(self):
        return not self.is_used and not self.is_expired and self.attempts < 5
    
    @classmethod
    def generate_code(cls):
        return ''.join(random.choices(string.digits, k=6))
    
    @classmethod
    def create_for_email(cls, email):
        # Deactivate existing codes for this email
        cls.objects.filter(email=email, is_used=False).update(is_used=True)
        
        # Create new code
        code = cls.generate_code()
        return cls.objects.create(email=email, code=code)
```

## Serializers

Add these serializers to your `serializers.py`:

```python
from rest_framework import serializers
from django.core.mail import send_mail
from django.conf import settings
from .models import EmailVerificationCode

class SendVerificationCodeSerializer(serializers.Serializer):
    email = serializers.EmailField()
    
    def validate_email(self, value):
        # Optional: Add additional email validation logic
        return value.lower()
    
    def save(self):
        email = self.validated_data['email']
        
        # Create verification code
        verification_code = EmailVerificationCode.create_for_email(email)
        
        # Send email
        self.send_verification_email(email, verification_code.code)
        
        return verification_code
    
    def send_verification_email(self, email, code):
        subject = 'BOPMaps - Verify Your Email'
        message = f'''
        Welcome to BOPMaps!
        
        Your verification code is: {code}
        
        This code will expire in 10 minutes.
        
        If you didn't request this code, please ignore this email.
        
        Best regards,
        The BOPMaps Team
        '''
        
        html_message = f'''
        <html>
        <body style="font-family: Arial, sans-serif; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2196F3;">Welcome to BOPMaps!</h2>
                <p>Your verification code is:</p>
                <div style="background-color: #f5f5f5; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;">
                    <h1 style="color: #2196F3; font-size: 32px; margin: 0; letter-spacing: 8px;">{code}</h1>
                </div>
                <p style="color: #666;">This code will expire in 10 minutes.</p>
                <p style="color: #666;">If you didn't request this code, please ignore this email.</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                <p style="color: #999; font-size: 14px;">Best regards,<br>The BOPMaps Team</p>
            </div>
        </body>
        </html>
        '''
        
        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                html_message=html_message,
                fail_silently=False,
            )
        except Exception as e:
            print(f"Failed to send verification email: {e}")
            raise serializers.ValidationError("Failed to send verification email")

class VerifyEmailCodeSerializer(serializers.Serializer):
    email = serializers.EmailField()
    code = serializers.CharField(max_length=6, min_length=6)
    
    def validate_email(self, value):
        return value.lower()
    
    def validate_code(self, value):
        if not value.isdigit():
            raise serializers.ValidationError("Code must contain only numbers")
        return value
    
    def validate(self, attrs):
        email = attrs['email']
        code = attrs['code']
        
        # Find the verification code
        try:
            verification_code = EmailVerificationCode.objects.get(
                email=email,
                code=code,
                is_used=False
            )
        except EmailVerificationCode.DoesNotExist:
            raise serializers.ValidationError("Invalid verification code")
        
        # Check if expired
        if verification_code.is_expired:
            raise serializers.ValidationError("Verification code has expired")
        
        # Check attempts
        if verification_code.attempts >= 5:
            raise serializers.ValidationError("Too many attempts. Please request a new code.")
        
        # Increment attempts
        verification_code.attempts += 1
        verification_code.save()
        
        # Mark as used if valid
        verification_code.is_used = True
        verification_code.save()
        
        attrs['verification_code'] = verification_code
        return attrs

class EmailVerifiedRegisterSerializer(serializers.Serializer):
    username = serializers.CharField(max_length=150)
    email = serializers.EmailField()
    email_verified = serializers.BooleanField()
    
    def validate_email(self, value):
        return value.lower()
    
    def validate_username(self, value):
        from django.contrib.auth.models import User
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("Username already exists")
        return value
    
    def validate_email_verified(self, value):
        if not value:
            raise serializers.ValidationError("Email must be verified before registration")
        return value
    
    def validate(self, attrs):
        email = attrs['email']
        
        # Check if email is already registered
        from django.contrib.auth.models import User
        if User.objects.filter(email=email).exists():
            raise serializers.ValidationError("Email already registered")
        
        # Verify that email was actually verified recently (within last hour)
        recent_verification = EmailVerificationCode.objects.filter(
            email=email,
            is_used=True,
            created_at__gte=timezone.now() - timezone.timedelta(hours=1)
        ).exists()
        
        if not recent_verification:
            raise serializers.ValidationError("Email verification required")
        
        return attrs
    
    def create(self, validated_data):
        from django.contrib.auth.models import User
        from rest_framework.authtoken.models import Token
        
        # Create user without password (passwordless authentication)
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data['email'],
            password=None  # No password required
        )
        
        # Create auth token
        token, created = Token.objects.get_or_create(user=user)
        
        return {
            'user': user,
            'auth_token': token.key,
            'refresh_token': token.key  # For simplicity, using same token
        }
```

## Views

Add these views to your `views.py`:

```python
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from .serializers import (
    SendVerificationCodeSerializer, 
    VerifyEmailCodeSerializer,
    EmailVerifiedRegisterSerializer
)
from .models import EmailVerificationCode

@api_view(['POST'])
@permission_classes([AllowAny])
def send_verification_code(request):
    """
    Send email verification code to the provided email address.
    
    POST /auth/send-verification-code/
    {
        "email": "<EMAIL>"
    }
    """
    serializer = SendVerificationCodeSerializer(data=request.data)
    
    if serializer.is_valid():
        try:
            verification_code = serializer.save()
            return Response({
                'success': True,
                'message': f'Verification code sent to {serializer.validated_data["email"]}',
                'expires_in': 600  # 10 minutes in seconds
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'success': False,
                'message': 'Failed to send verification code',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return Response({
        'success': False,
        'message': 'Invalid email address',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([AllowAny])
def verify_email_code(request):
    """
    Verify the email verification code.
    
    POST /auth/verify-email-code/
    {
        "email": "<EMAIL>",
        "code": "123456"
    }
    """
    serializer = VerifyEmailCodeSerializer(data=request.data)
    
    if serializer.is_valid():
        return Response({
            'success': True,
            'message': 'Email verified successfully'
        }, status=status.HTTP_200_OK)
    
    return Response({
        'success': False,
        'message': 'Verification failed',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([AllowAny])
def login_with_email_verification(request):
    """
    Login existing user with email verification (passwordless).
    
    POST /auth/login-with-email-verification/
    {
        "email": "<EMAIL>",
        "email_verified": true
    }
    """
    email = request.data.get('email', '').lower()
    email_verified = request.data.get('email_verified', False)
    
    if not email:
        return Response({
            'success': False,
            'message': 'Email is required',
        }, status=status.HTTP_400_BAD_REQUEST)
    
    if not email_verified:
        return Response({
            'success': False,
            'message': 'Email must be verified to login',
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        # Check if user exists
        from django.contrib.auth.models import User
        user = User.objects.get(email=email)
        
        # Verify that email was actually verified recently (within last hour)
        recent_verification = EmailVerificationCode.objects.filter(
            email=email,
            is_used=True,
            created_at__gte=timezone.now() - timezone.timedelta(hours=1)
        ).exists()
        
        if not recent_verification:
            return Response({
                'success': False,
                'message': 'Recent email verification required',
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create or get auth token
        from rest_framework.authtoken.models import Token
        token, created = Token.objects.get_or_create(user=user)
        
        return Response({
            'success': True,
            'message': 'Login successful',
            'auth_token': token.key,
            'refresh_token': token.key,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'date_joined': user.date_joined,
            }
        }, status=status.HTTP_200_OK)
        
    except User.DoesNotExist:
        return Response({
            'success': False,
            'message': 'User not found',
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Login failed',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    """
    Register a new user with email verification (passwordless).
    
    POST /auth/register/
    {
        "username": "johndoe",
        "email": "<EMAIL>",
        "email_verified": true
    }
    """
    serializer = EmailVerifiedRegisterSerializer(data=request.data)
    
    if serializer.is_valid():
        try:
            result = serializer.create(serializer.validated_data)
            
            return Response({
                'success': True,
                'message': 'Account created successfully',
                'auth_token': result['auth_token'],
                'refresh_token': result['refresh_token'],
                'user': {
                    'id': result['user'].id,
                    'username': result['user'].username,
                    'email': result['user'].email,
                    'date_joined': result['user'].date_joined,
                }
            }, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({
                'success': False,
                'message': 'Registration failed',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return Response({
        'success': False,
        'message': 'Registration failed',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)
```

## URL Configuration

Add these URLs to your `urls.py`:

```python
from django.urls import path
from . import views

urlpatterns = [
    # ... existing URLs ...
    path('auth/send-verification-code/', views.send_verification_code, name='send-verification-code'),
    path('auth/verify-email-code/', views.verify_email_code, name='verify-email-code'),
    path('auth/login-with-email-verification/', views.login_with_email_verification, name='login-with-email-verification'),
    path('auth/register/', views.register, name='register'),
]
```

## Settings Configuration

Add these settings to your Django `settings.py`:

```python
# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'  # or your email provider
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'
DEFAULT_FROM_EMAIL = 'BOPMaps <<EMAIL>>'

# For development, you can use console backend
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

## Database Migration

Create and run the migration:

```bash
python manage.py makemigrations
python manage.py migrate
```

## Usage Flow

### For New Users (Registration):
1. **Send Verification Code**: Frontend calls `/auth/send-verification-code/` with email
2. **User Receives Email**: User gets 6-digit code via email (expires in 10 minutes)
3. **Verify Code**: Frontend calls `/auth/verify-email-code/` with email and code
4. **Register User**: Frontend calls `/auth/register/` with username, email, and email_verified=true

### For Existing Users (Login):
1. **Send Verification Code**: Frontend calls `/auth/send-verification-code/` with email
2. **User Receives Email**: User gets 6-digit code via email (expires in 10 minutes)
3. **Verify Code**: Frontend calls `/auth/verify-email-code/` with email and code
4. **Login User**: Frontend calls `/auth/login-with-email-verification/` with email and email_verified=true

## Security Features

- **Rate Limiting**: Codes expire in 10 minutes
- **Attempt Limiting**: Maximum 5 verification attempts per code
- **Single Use**: Codes can only be used once
- **Recent Verification**: Registration requires recent verification (within 1 hour)
- **Email Deduplication**: New codes invalidate previous unused codes

## Optional Enhancements

1. **Rate Limiting**: Add rate limiting to prevent spam
2. **IP Tracking**: Track IP addresses for additional security
3. **Phone Verification**: Extend to support SMS verification
4. **Admin Interface**: Create admin interface to manage verification codes
5. **Analytics**: Track verification success rates and common failure points

## Testing

```bash
# Test sending verification code
curl -X POST http://localhost:8000/auth/send-verification-code/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Test verifying code
curl -X POST http://localhost:8000/auth/verify-email-code/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "code": "123456"}'

# Test login for existing user (passwordless)
curl -X POST http://localhost:8000/auth/login-with-email-verification/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "email_verified": true}'

# Test registration for new user
curl -X POST http://localhost:8000/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "email": "<EMAIL>", "email_verified": true}'
``` 