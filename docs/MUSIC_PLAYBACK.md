# Music Playback System Documentation

## Overview
The BOP Maps music playback system consists of two main components:
1. **Now Playing Friends Bar**: A social music widget showing what friends are currently listening to
2. **Now Playing Bar**: A minimal playback control bar for the current user's music

## Components

### 1. Now Playing Friends Bar (`now_playing_friends.dart`)
A collapsible widget that displays:
- Friends who are currently listening to music
- Their current tracks with playback controls
- An expandable section showing recently played tracks together

#### Key Features:
- Dismissible container
- Animated expand/collapse
- Friend avatars with online indicators
- Track information display
- Direct playback controls
- Recently played tracks section (when expanded)

#### Structure:
```
NowPlayingFriends
├── DismissibleMusicContainer
├── Header
│   ├── Title with icon
│   └── Action buttons (refresh, expand/collapse)
├── FriendsList (horizontal scroll)
│   └── FriendListeningCard (multiple)
└── RecentTracksSection (when expanded)
    ├── Section header
    └── RecentTrackCard (multiple)
```

### 2. Now Playing Bar (`now_playing_bar.dart`)
A minimal playback control bar showing:
- Current track information
- Basic playback controls (play/pause, skip)
- Track progress
- Album artwork

## State Management

### SpotifyProvider
- Manages Spotify SDK integration
- Handles playback state
- Tracks current song information
- Manages user authentication
- Provides playback controls

Key Methods:
- `playTrack(String uri)`
- `pause()`
- `resume()`
- `skipNext()`
- `skipPrevious()`

### Data Flow
1. User interacts with playback controls
2. Action is passed to SpotifyProvider
3. Provider updates Spotify SDK
4. SDK returns new state
5. UI updates to reflect changes

## Responsive Design
Both bars implement responsive design:
- Adapts to screen size (`isSmallScreen` check)
- Adjusts font sizes and spacing
- Maintains touch targets
- Preserves functionality across devices

## Widget Sizes

### Now Playing Friends Bar
- Collapsed height: 146-170px (small-large screen)
- Expanded height: 232-266px
- Card width: 100-120px

### Recent Tracks Section
- Height: 85-95px
- Card width: 56-64px
- Image size: 48-56px

## Implementation Notes

### Key Classes
1. `NowPlayingFriends`: Main social music widget
2. `FriendListeningCard`: Individual friend's current track
3. `RecentTracksSection`: Recently played tracks display
4. `RecentTrackCard`: Individual track in history
5. `NowPlayingBar`: Current user's playback controls

### Best Practices
- Use `mainAxisSize: MainAxisSize.min` to prevent overflow
- Implement proper constraints for fixed heights
- Handle image loading errors gracefully
- Use theme colors for consistency
- Implement proper spacing and padding

### Error Prevention
- Proper height calculations
- Flexible text with ellipsis
- Fallback images
- Null safety checks
- Error boundaries

## Usage Example
```dart
// Add to main screen
Stack(
  children: [
    MainContent(),
    Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          NowPlayingFriends(),
          NowPlayingBar(),
        ],
      ),
    ),
  ],
)
``` 