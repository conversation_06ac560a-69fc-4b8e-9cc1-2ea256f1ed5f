# Gamification System - Backend Integration

This document explains how the gamification system has been updated to connect to the Django backend instead of using mock data.

## Overview

The gamification system now connects to the Django backend API endpoints for:
- Achievements management
- Pin skins management  
- User progress tracking
- Achievement completion detection

## Key Changes Made

### 1. Updated Models

#### Achievement Model (`lib/models/achievement.dart`)
- Enhanced JSON parsing to handle backend response format
- Added support for `reward_skin_details` field from Django API
- Improved handling of `is_completed` and `progress_percentage` fields
- Better error handling for different data types

#### UserAchievement Model (`lib/models/user_achievement.dart`)
- New model to handle user-specific achievement progress
- Tracks completion status and progress data
- Provides progress calculation methods

### 2. Enhanced Service Layer

#### GamificationService (`lib/services/gamification_service.dart`)
- Added UserAchievement support
- Improved API response handling for paginated and non-paginated data
- Added helper method `getOrCreateUserAchievement()` for progress tracking
- Better error handling and response parsing

### 3. Updated Provider

#### GamificationProvider (`lib/providers/gamification_provider.dart`)
- Disabled mock data by default (`_useMockData = false`)
- Enhanced progress tracking using UserAchievement data
- Improved achievement loading with user progress merging
- Better error handling and debugging

## API Endpoints Used

The system connects to these Django API endpoints:

### Achievements
- `GET /api/gamification/achievements/` - Get all achievements
- `GET /api/gamification/achievements/completed/` - Get completed achievements
- `GET /api/gamification/achievements/in_progress/` - Get in-progress achievements
- `GET /api/gamification/achievements/{id}/` - Get specific achievement

### Pin Skins
- `GET /api/gamification/skins/` - Get all pin skins
- `GET /api/gamification/skins/unlocked/` - Get unlocked skins for user
- `GET /api/gamification/skins/owned/` - Get owned skins (alias for unlocked)
- `POST /api/gamification/skins/{id}/equip/` - Equip a pin skin

### User Achievements (Progress Tracking)
- `GET /api/gamification/user-achievements/` - Get user's achievement progress
- `POST /api/gamification/user-achievements/` - Create new user achievement
- `POST /api/gamification/user-achievements/{id}/update_progress/` - Update progress

## Usage Examples

### Updating Achievement Progress

```dart
// Get the gamification provider
final gamificationProvider = Provider.of<GamificationProvider>(context, listen: false);

// Update progress for an achievement
await gamificationProvider.updateAchievementProgress(
  '1', // Achievement ID as string
  {
    'artist_pins': 5, // Current progress value
    'genre_pins': 3,
  }
);
```

### Loading Achievements

```dart
// Initialize the gamification system
await gamificationProvider.initialize();

// Access different types of achievements
final allAchievements = gamificationProvider.achievements;
final completed = gamificationProvider.completedAchievements;
final inProgress = gamificationProvider.inProgressAchievements;

// Access by category
final artistAchievements = gamificationProvider.artistAchievements;
final genreAchievements = gamificationProvider.genreAchievements;
```

### Managing Pin Skins

```dart
// Load available skins
await gamificationProvider.loadPinSkins();
await gamificationProvider.loadUnlockedSkins();

// Get skins
final allSkins = gamificationProvider.pinSkins;
final unlockedSkins = gamificationProvider.unlockedSkins;
final equippedSkin = gamificationProvider.equippedSkin;

// Equip a skin
final success = await gamificationProvider.equipSkin(selectedSkin);
```

## Development vs Production

### Mock Data Mode
For development and testing, you can still enable mock data:

```dart
// Enable mock data for testing
gamificationProvider.enableMockData();

// Disable mock data to use real API
gamificationProvider.disableMockData();
```

### Error Handling
The system includes comprehensive error handling:
- Network connectivity issues
- API response parsing errors
- Authentication failures
- Invalid data formats

All errors are logged to the console with descriptive messages.

## Backend Requirements

Ensure your Django backend has:
1. The gamification app properly configured
2. All required API endpoints implemented
3. Proper authentication middleware
4. CORS configured for your Flutter app domain

## Testing

To test the integration:

1. Ensure your Django backend is running
2. Update the API base URL in `ApiService` if needed
3. Run the Flutter app and navigate to gamification screens
4. Check console logs for any API errors
5. Verify achievements and skins load correctly

## Troubleshooting

### Common Issues

1. **Empty achievements list**: Check API endpoint and authentication
2. **Progress not updating**: Verify UserAchievement creation and update endpoints
3. **Skins not loading**: Check unlocked skins endpoint and user permissions
4. **Network errors**: Verify API base URL and network connectivity

### Debug Mode

Enable debug logging by checking console output for messages starting with:
- `Error loading achievements:`
- `Error loading pin skins:`
- `Error updating achievement progress:`

These will help identify specific API or parsing issues. 