# OneSignal Integration Setup Guide

## Overview

OneSignal has been integrated into your BOPMaps Flutter app to handle push notifications. This guide will help you complete the setup process.

## Step 1: Create OneSignal Account and App

1. Go to [https://onesignal.com](https://onesignal.com) and create an account
2. Create a new app in the OneSignal dashboard
3. Select "Flutter" as your platform
4. Note down your **App ID** - you'll need this in the next step

## Step 2: Configure Environment Variables

Create or update your `.env` file in the project root with your OneSignal App ID:

```env
# OneSignal Configuration
ONESIGNAL_APP_ID=your_actual_onesignal_app_id_here

# Add other environment variables here as needed
```

**Important:** Replace `your_actual_onesignal_app_id_here` with your actual OneSignal App ID from step 1.

**Note:** The OneSignal App ID is now managed through `AppConstants.oneSignalAppId` in `lib/config/constants.dart`, following the same pattern as other API keys in your app. You can check if OneSignal is properly configured using `AppConstants.isOneSignalConfigured`.

## Step 3: Platform-Specific Setup

### iOS Setup

1. **Add Push Notifications capability:**
   - Open `ios/Runner.xcworkspace` in Xcode
   - Select your project in the navigator
   - Select the "Runner" target
   - Go to "Signing & Capabilities" tab
   - Click "+ Capability" and add "Push Notifications"

2. **Add Background Modes capability:**
   - In the same "Signing & Capabilities" tab
   - Click "+ Capability" and add "Background Modes"
   - Check "Background processing" and "Remote notifications"

3. **Add App Group capability (Optional but recommended):**
   - Click "+ Capability" and add "App Groups"
   - Create a new app group with ID: `group.com.yourdomain.bopmaps`

4. **Add Notification Service Extension (For rich notifications):**
   - In Xcode, go to File → New → Target
   - Select "Notification Service Extension"
   - Name it "OneSignalNotificationServiceExtension"
   - Add it to the same App Group

### Android Setup

The Android configuration is already completed in your `AndroidManifest.xml`:

```xml
<!-- OneSignal permissions -->
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
```

## Step 4: Usage in Your App

### Basic Integration

OneSignal is automatically initialized when your app starts. You can access it through the `OneSignalProvider`:

```dart
// Access OneSignal provider
final oneSignalProvider = Provider.of<OneSignalProvider>(context);

// Check if initialized
if (oneSignalProvider.isInitialized) {
  // OneSignal is ready to use
}

// Request notification permission
await oneSignalProvider.requestPermission();

// Set user ID when user logs in
await oneSignalProvider.setExternalUserId('user_123');

// Add user tags for segmentation
await oneSignalProvider.setUserTags(
  musicPreference: 'rock',
  location: 'new_york',
  isPremium: true,
);
```

### Integration with Authentication

In your authentication flow, set the external user ID:

```dart
// When user logs in
final authProvider = Provider.of<AuthProvider>(context, listen: false);
final oneSignalProvider = Provider.of<OneSignalProvider>(context, listen: false);

// After successful login
await oneSignalProvider.setExternalUserId(authProvider.currentUser.id);

// When user logs out
await oneSignalProvider.removeExternalUserId();
```

### Handling Notification Clicks

Notification clicks are automatically handled by the `OneSignalService`. You can customize the behavior in the `handleNotificationClick` method in `OneSignalProvider`.

## Step 5: Testing

### Test Push Notifications

1. **Get a test device subscription:**
   - Run your app on a device (not simulator)
   - Accept notification permissions
   - Check the OneSignal dashboard for the new subscription

2. **Send a test notification:**
   - Go to your OneSignal dashboard
   - Navigate to "Messages" → "Push"
   - Click "New Push"
   - Set up your test message
   - Select "Send to Test Users" and choose your device
   - Send the notification

### Debug Mode

The integration includes debug logging. In debug mode, you'll see detailed logs:

```
🔔 Initializing OneSignal with App ID: your_app_id
✅ OneSignal initialized successfully
🔔 OneSignal notification permission: true
🔔 OneSignal user info updated:
   Push Subscription ID: xxx
   Push Token: xxx
```

## Step 6: Advanced Features

### User Segmentation

You can create user segments in the OneSignal dashboard based on:
- Location tags
- Music preferences
- User behavior
- App usage patterns

### Custom Notification Actions

Modify the `handleNotificationClick` method in `OneSignalProvider` to add custom actions:

```dart
void handleNotificationClick(OSNotificationClickEvent event) {
  final additionalData = event.notification.additionalData;
  
  if (additionalData != null) {
    final type = additionalData['type'] as String?;
    
    switch (type) {
      case 'pin_nearby':
        // Navigate to specific pin on map
        Navigator.pushNamed(context, '/map', arguments: additionalData['pinId']);
        break;
      case 'friend_activity':
        // Navigate to friends screen
        Navigator.pushNamed(context, '/friends');
        break;
      // Add more custom actions
    }
  }
}
```

### Rich Notifications

With the Notification Service Extension setup, you can send rich notifications with:
- Images
- Videos
- Action buttons
- Custom sounds

## Step 7: Production Considerations

### iOS Production Setup

1. **Generate APNs Certificate:**
   - Go to Apple Developer Portal
   - Create APNs certificate for production
   - Upload to OneSignal dashboard

2. **Update OneSignal configuration:**
   - In OneSignal dashboard, go to Settings → Platforms
   - Upload your production APNs certificate

### Android Production Setup

1. **Firebase Configuration:**
   - Ensure your `google-services.json` is configured for production
   - Update OneSignal with your Firebase Server Key

### Privacy and Compliance

1. **Update Privacy Policy:**
   - Mention push notification usage
   - Explain data collection for notification targeting

2. **GDPR Compliance:**
   - Implement consent mechanisms if required
   - Use OneSignal's privacy features

## Troubleshooting

### Common Issues

1. **Notifications not received:**
   - Check device notification settings
   - Verify OneSignal App ID is correct
   - Ensure app is in foreground/background appropriately

2. **iOS notifications not working:**
   - Verify Push Notifications capability is enabled
   - Check APNs certificate in OneSignal dashboard
   - Test on physical device (not simulator)

3. **Permission not requested:**
   - Check if `requestPermission()` is called
   - Verify the call happens after OneSignal initialization

### Debug Steps

1. Check console logs for OneSignal messages
2. Verify subscription appears in OneSignal dashboard
3. Test with OneSignal's debugging tools
4. Use OneSignal's API to send test notifications

## Support

- [OneSignal Documentation](https://documentation.onesignal.com/docs/flutter-sdk-setup)
- [OneSignal Flutter GitHub](https://github.com/OneSignal/OneSignal-Flutter-SDK)
- OneSignal Support Portal

## Next Steps

1. Set up your `.env` file with the OneSignal App ID
2. Complete iOS capabilities setup in Xcode
3. Test notifications on a physical device
4. Implement user segmentation strategy
5. Add custom notification handling logic 