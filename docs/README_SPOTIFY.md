# Spotify Integration in BOPMaps

This document provides a comprehensive overview of how Spotify is integrated into the BOPMaps application, including architecture details, implementation specifics, and key components.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Core Components](#core-components)
3. [Authentication Flow](#authentication-flow)
4. [Data Models](#data-models)
5. [UI Components](#ui-components)
6. [API Integration](#api-integration)
7. [Player Functionality](#player-functionality)
8. [Configuration](#configuration)
9. [Development vs Production](#development-vs-production)
10. [Troubleshooting](#troubleshooting)
11. [Token Handling](#token-handling)
12. [SDK Integration](#sdk-integration)
13. [Django Backend Integration](#django-backend-integration)
14. [Complete Data Flow](#complete-data-flow)
15. [Testing Authentication](#testing-authentication)

## Architecture Overview

The Spotify integration in BOPMaps follows a layered architecture:

1. **Service Layer**: Contains low-level services for communicating with the Spotify API
2. **Provider Layer**: Manages state and provides a simplified interface to UI components
3. **UI Layer**: Display components that interact with the Spotify functionality
4. **Model Layer**: Data structures that represent Spotify resources 
5. **Backend Integration**: Optional Django backend for secure token handling

The app can operate in two modes:
- **Direct Mode**: Communicating directly with Spotify API from the mobile app
- **Backend Mode**: Using a Django backend as a proxy for Spotify API calls (preferred for production)

## Core Components

### Services

1. **SpotifyService** (`lib/services/music/spotify_service.dart`)
   - Handles direct API calls to Spotify
   - Manages token storage and refreshing
   - Maps Spotify API responses to app data models

2. **SpotifyAuthService** (`lib/services/music/spotify_auth_service.dart`)
   - Manages authentication with Spotify
   - Handles both direct auth and backend-mediated auth
   - Supports platform-specific redirect URI handling

3. **SpotifyPlayerService** (`lib/services/music/spotify_player_service.dart`)
   - Integrates with the Spotify SDK for playback control
   - Manages player state and notifications
   - Handles premium vs. free account limitations

### Providers

1. **SpotifyProvider** (`lib/providers/spotify_provider.dart`)
   - Central state management for Spotify functionality
   - Exposes simplified methods for UI components
   - Manages caching of tracks, playlists, and user data
   - Handles loading states and error messages

### Utilities

1. **AuthManager** (`lib/utils/auth_manager.dart`)
   - Provides unified authentication interface
   - Coordinates between app auth and Spotify auth

## Authentication Flow

The Spotify authentication follows the OAuth 2.0 Authorization Code flow:

1. **Authentication Initiation**:
   - User taps "Connect to Spotify" button
   - App tries to contact Django backend for auth URL
   - Falls back to direct auth URL generation if necessary

2. **Authorization**:
   - User is directed to Spotify's authorization page
   - User grants permissions based on the requested scopes

3. **Callback Handling**:
   - Spotify redirects to the app's redirect URI (platform specific)
   - App extracts authorization code from redirect URI
   - App exchanges code for access and refresh tokens

4. **Token Management**:
   - Access token is stored securely in FlutterSecureStorage
   - Token expiry is tracked and automatically refreshed when needed

5. **Disconnection**:
   - User can disconnect Spotify integration
   - Tokens are cleared from secure storage

## Data Models

1. **MusicTrack** (`lib/models/music_track.dart`)
   - Represents a track from any music service
   - Contains Spotify-specific fields like URI and preview URL
   - Includes helper methods for formatting and display

2. **Track** (`lib/features/music_ai/models/track.dart`)
   - Alternative track model for AI features
   - Contains audio feature data (tempo, key, etc.)

## UI Components

1. **TrackSelectScreen** (`lib/screens/music/track_select_screen.dart`)
   - Main interface for selecting Spotify tracks
   - Shows connection status and connect/disconnect options
   - Displays user's Spotify library when connected

2. **MiniPlayer** (`lib/widgets/music/mini_player.dart`)
   - Compact player interface for controlling playback
   - Shows currently playing track info

3. **Music Settings Screen** (`lib/screens/settings/music_settings_screen.dart`)
   - Interface for managing Spotify connection
   - Shows account information and connection status

4. **Create Pin Track Select Screen** (`lib/screens/music/create_pin_track_select_screen.dart`)
   - Interface for selecting tracks when creating map pins
   - Handles Spotify connection status

## API Integration

The app integrates with several Spotify API endpoints:

1. **Authentication**:
   - Authorization (`https://accounts.spotify.com/authorize`)
   - Token exchange (`https://accounts.spotify.com/api/token`)

2. **User Data**:
   - User profile information
   - User's playlists
   - Saved tracks (liked songs)

3. **Music Data**:
   - Search for tracks
   - Get track details
   - Access user's top tracks
   - Get recently played tracks

4. **Playback**:
   - Control playback through Spotify SDK
   - Get current playback state

## Backend API Endpoints

When using the Django backend, the app utilizes the following endpoints for music functionality:

1. **Authentication and Connection Endpoints**:
   - Check Connected Services: `/api/music/connected_services/`
   - Connect to Spotify: `/api/music/auth/spotify/`
   - OAuth Callback Handler: `/api/music/auth/callback/`

2. **Track-Related Endpoints**:
   - Saved/Liked Tracks: `/api/music/api/tracks/saved_tracks/?limit=[limit]&offset=[offset]&service=spotify`
   - Recently Played: `/api/music/api/tracks/recently_played/?limit=[limit]&service=spotify`
   - Search Tracks: `/api/music/api/tracks/search/?q=[query]&limit=[limit]&service=spotify`
   - Track Details: `/api/music/api/tracks/track/spotify/[track_id]/`
   - Top Tracks: `/api/music/api/spotify/top-tracks/?limit=[limit]`

3. **Direct Spotify API Access** (Optional endpoints for more direct access):
   - Spotify Playlists: `/api/music/api/spotify/playlists/`
   - Specific Playlist: `/api/music/api/spotify/playlist/[playlist_id]/`
   - Playlist Tracks: `/api/music/api/spotify/playlist/[playlist_id]/tracks/`

The app automatically switches between these backend endpoints and direct Spotify API calls based on:
- Development vs. Production mode
- Token type (debug tokens vs. real Spotify tokens)
- Feature requirements (some features may need direct API access)

Implementation details can be found in the `SpotifyService` class (`lib/services/music/spotify_service.dart`).

## Player Functionality

The player functionality uses the Spotify SDK for:

1. **Playback Control**:
   - Play/pause
   - Skip to next/previous track
   - Seek within a track

2. **Player State**:
   - Current track information
   - Playback position and duration
   - Playback status (playing/paused)

3. **Limitations**:
   - Full playback requires Spotify Premium
   - Free accounts have limited functionality
   - SDK requires official Spotify app to be installed

## Configuration

Spotify configuration is managed in `lib/config/constants.dart`:

1. **API Endpoints**:
   - Authentication URLs
   - Base API URL
   - Redirect URIs

2. **Credentials**:
   - Client ID from environment variables
   - Redirect URI with platform-specific handling

3. **Scopes**:
   - Permission scopes requested during authorization
   - Includes user data and playback permissions

## Development vs Production

The app includes special handling for development environments:

1. **Development Mode**:
   - Mock tokens for testing without real auth
   - Debug logging of auth flow
   - Detailed error messages

2. **Production Mode**:
   - Secure token handling
   - Error resilience and graceful fallbacks
   - Limited debug information

## Troubleshooting

Common issues and solutions:

1. **Authentication Failures**:
   - Check Spotify Developer Dashboard settings
   - Verify redirect URI is correctly registered
   - Ensure client ID is correct

2. **Playback Issues**:
   - Verify user has Spotify Premium for full playback
   - Check if official Spotify app is installed
   - Ensure device is online

3. **Token Management**:
   - Tokens are automatically refreshed when expired
   - Manual disconnection and reconnection can resolve token issues

## Token Handling

BOPMaps implements a sophisticated token management system for Spotify integration:

1. **Token Storage**:
   - Access tokens are stored in Flutter's SecureStorage
   - Refresh tokens are securely persisted for long-term access
   - Token expiry timestamps are tracked to determine when refreshes are needed

2. **Token Keys**:
   ```dart
   static const String _accessTokenKey = 'spotify_access_token';
   static const String _refreshTokenKey = 'spotify_refresh_token';
   static const String _expiryTimeKey = 'spotify_token_expiry';
   ```

3. **Token Refresh Process**:
   - Automatic background refresh when tokens expire
   - Silent refresh implementation to avoid interrupting user experience
   - Fallback mechanisms if refresh fails

4. **Error Handling**:
   - Graceful handling of invalid or expired tokens
   - Automatic retry with exponential backoff for failed API calls
   - Clear feedback to user when authentication issues occur

5. **Development Tokens**:
   - In development mode, mock tokens are used for testing
   - Format: `spotify_dev_token_[timestamp]`
   - Allows developers to test Spotify integration without real authentication

## SDK Integration

The Spotify SDK integration is handled primarily through the `SpotifyPlayerService`:

1. **SDK Initialization**:
   ```dart
   await SpotifySdk.connectToSpotifyRemote(
     clientId: AppConstants.spotifyClientId,
     redirectUrl: AppConstants.spotifyRedirectUri,
   );
   ```

2. **Player State Subscription**:
   ```dart
   SpotifySdk.subscribePlayerState().listen((playerState) {
     // Handle player state updates
   });
   ```

3. **Playback Control Methods**:
   - Play: `SpotifySdk.play(spotifyUri: uri)`
   - Pause: `SpotifySdk.pause()`
   - Resume: `SpotifySdk.resume()`
   - Skip: `SpotifySdk.skipNext()` / `SpotifySdk.skipPrevious()`
   - Seek: `SpotifySdk.seekTo(positionMs)`

4. **Track Mapping**:
   - The SDK returns Spotify-specific track objects that are mapped to the app's `MusicTrack` model:
   ```dart
   MusicTrack(
     id: playerState.track?.uri?.split(':').last ?? '',
     title: playerState.track?.name ?? 'Unknown',
     artist: playerState.track?.artist.name ?? 'Unknown',
     album: playerState.track?.album.name ?? 'Unknown',
     // ... additional fields
   )
   ```

5. **Platform-Specific Considerations**:
   - The Spotify SDK has different implementations for iOS and Android
   - Special handling for device capability detection
   - Graceful degradation for unsupported features

6. **Premium User Detection**:
   ```dart
   _isPremiumUser = await _spotifyService.isPremiumUser();
   ```

7. **Stream-Based Architecture**:
   - Playback state updates are exposed via Dart streams
   - UI components observe these streams to reflect current playback state
   - Ensures UI is always synchronized with actual player state

## Django Backend Integration

BOPMaps optionally uses a Django backend to enhance security and functionality of the Spotify integration:

1. **Backend API Endpoints**:
   - Defined in `lib/config/constants.dart`:
   ```dart
   static const String spotifyAuthEndpoint = '$musicBase/auth/spotify/';
   static const String spotifyCallbackEndpoint = '$musicBase/auth/spotify/callback/';
   static const String spotifyCallbackHandlerEndpoint = '$musicBase/auth/callback/';
   static const String connectedServicesEndpoint = '$musicBase/connected_services/';
   ```

2. **Security Benefits**:
   - Client secret can be kept secure on the backend
   - Protects refresh tokens from client-side exposure
   - Centralizes authentication for multi-device use

3. **Backend Detection**:
   - The app checks if the Django backend is available:
   ```dart
   final pingResponse = await http.get(
     Uri.parse('${AppConstants.baseApiUrl}/schema/'),
   );
   if (pingResponse.statusCode == 200) {
     djangoServerRunning = true;
   }
   ```

4. **Fallback Strategy**:
   - If backend is unavailable, the app falls back to direct Spotify API communication:
   ```dart
   if (!djangoServerRunning) {
     authUrl = _generateSpotifyAuthUrl();
   }
   ```

5. **Token Proxy**:
   - Backend securely exchanges authorization code for tokens
   - Returns only necessary information to mobile client
   - Handles refresh token rotation for enhanced security

6. **User Association**:
   - Backend associates Spotify accounts with BOPMaps user accounts
   - Enables social features like sharing music pins with friends
   - Provides unified user experience across devices

7. **Development Setup**:
   - Local Django server on `http://127.0.0.1:8000/`
   - Environment-specific configuration via .env files
   - Special handling for development vs. production servers

## Complete Data Flow

This section describes the end-to-end flow of data in the Spotify integration:

1. **Initial Connection**:
   ```
   User → TrackSelectScreen → SpotifyProvider → SpotifyAuthService 
   → Django Backend → Spotify Auth API → User Grants Permission 
   → Callback to App → Token Storage → Connection Complete
   ```

2. **Track Selection**:
   ```
   User → Track Selection UI → SpotifyProvider → SpotifyService 
   → Spotify API → Response → MusicTrack Mapping → UI Update
   ```

3. **Playback**:
   ```
   User → Play Button → SpotifyProvider → SpotifyPlayerService 
   → Spotify SDK → Official Spotify App → Audio Playback 
   → State Updates → UI Refresh
   ```

4. **Pin Creation**:
   ```
   User → Select Track → Create Pin → Map Location → Save 
   → API → Database → Track URI Stored → Pin Available on Map
   ```

5. **Track Discovery**:
   ```
   User → Discover Pin on Map → Tap Pin → View Track Details 
   → Play Button → Spotify Playback Flow → Audio Output
   ```

6. **Data Refresh**:
   ```
   App Opened → Check Token Expiry → Refresh If Needed 
   → Load User Data → Update Cached Playlists and Tracks
   ```

7. **Error Handling Flow**:
   ```
   API Error → Retry with Exponential Backoff → Check Token Validity 
   → Refresh Token If Needed → Retry Request → Success or User Notification
   ```

## Testing Authentication

The app provides special functionality for testing the authentication flow, particularly useful during development:

### Production Authentication with Debug Fallback

A feature has been added to allow testing the production authentication flow while still having mock tokens as a fallback in debug mode:

1. **Authentication Mode Toggle**:
   - In the Music Settings screen, a toggle is available in debug mode to prioritize production authentication
   - When enabled, the app will attempt real Spotify authentication first
   - If real authentication fails for any reason, it will automatically fall back to debug tokens
   - This allows for testing the production flow while maintaining development convenience

2. **Authentication Testing Button**:
   - A dedicated "Test Spotify Authentication" button is available in debug mode
   - This clears existing tokens and initiates a full authentication cycle
   - Use this to test the behavior with different authentication mode settings

3. **How to Use This Feature**:
   1. Go to Music Settings screen
   2. Toggle "Prioritize Production Authentication" based on your testing needs:
      - ON: Try real Spotify authentication, fall back to debug if it fails
      - OFF: Classic behavior - use debug tokens in debug mode, real auth in production
   3. Use the "Test Spotify Authentication" button to test the flow

4. **Technical Implementation**:
   - Controlled by the `prioritizeProductionAuth` flag in `SpotifyAuthService`
   - The flag is exposed through the `SpotifyProvider` for UI control
   - Error handling at various points in the authentication flow check for failures and apply the fallback if needed

This approach allows developers to test the real authentication flow while still having a reliable fallback, ensuring development can continue even when Spotify authentication issues occur. 