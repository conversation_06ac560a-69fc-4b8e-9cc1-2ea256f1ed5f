# Backend Caching Layer for Last.fm Similar Artists

## 1. Objective
Build a lightweight micro-service that proxies and caches requests to the public [Last.fm REST API](https://www.last.fm/api) so that mobile clients can query `/lastfm` on *your* infrastructure instead of talking to Last.fm directly. This dramatically decreases duplicate network traffic, improves latency, and shields the official Last.fm API key from client devices.

```
Flutter App ──►  /lastfm?method=artist.getSimilar&artist=Coldplay&limit=20
                         │
                         ▼
Backend Cache  ──► (Redis / In-Memory) ──► Last.fm REST API
```

The service returns *verbatim* Last.fm JSON responses to the client while storing copies in an LRU cache (e.g. Redis) for a configurable TTL (≈ 6 hours).

---

## 2. API Contract
1. **Endpoint**: `GET /lastfm`
2. **Accepted Query Parameters** (pass-through to Last.fm):
   * `method` – e.g. `artist.getSimilar`
   * `artist` – required for `artist.getSimilar`
   * `limit` – optional, default `20`
   * Any other official Last.fm parameters (album, mbid, etc.)
3. **Authentication (optional)**: add a `Bearer` token or `X-API-KEY` header to restrict usage.
4. **Success Response**: `200 OK` with identical JSON structure returned by Last.fm.
5. **Error Handling**: forward Last.fm error payloads (`error`, `message`, `links`) in a 502 response, or return `429` for rate-limit rejections.

---

## 3. Caching Strategy
| Aspect                 | Recommendation |
|------------------------|----------------|
| **Store**              | Redis (single instance or managed service) |
| **Key Format**         | `lastfm:<method>:<queryHash>` where `queryHash = sha1(sortedQueryParams)` |
| **TTL**                | 6 hours (aligns with client-side cache) |
| **Eviction**           | Least-Recently-Used (default Redis policy) |
| **Compression**        | Enable `redis-compression` or store `gzip` string to cut memory ~70 % |

> NOTE : For very small deployments an in-process LRU cache (e.g. `node-cache`, `fun‐cache`, `Map<String,CacheEntry>` in Go) is acceptable, but Redis is strongly recommended for horizontal scaling.

---

## 4. Rate Limiting
* Apply per-IP or per-user quotas to the **backend endpoint** (e.g. 60 requests/minute).
* Apply global rate limiting towards **Last.fm** (max ≃ 5 req/s) to stay under their ToS. A simple token-bucket in memory or a Redis counter suffices.

---

## 5. Reference Implementation (Node.js + Express + Redis)
```js
import express from 'express';
import fetch from 'node-fetch';
import crypto from 'crypto';
import Redis from 'ioredis';

const app   = express();
const redis = new Redis(process.env.REDIS_URL);

const LASTFM_KEY = process.env.LASTFM_API_KEY; // keep secret!
const LASTFM_URL = 'https://ws.audioscrobbler.com/2.0/';
const CACHE_TTL  = 60 * 60 * 6; // 6 hours in seconds

app.get('/lastfm', async (req, res) => {
  try {
    // 1. Build canonical query string (includes official API key)
    const params = new URLSearchParams({ ...req.query, api_key: LASTFM_KEY, format: 'json' });
    const queryHash = crypto.createHash('sha1').update(params.toString()).digest('hex');
    const cacheKey  = `lastfm:${req.query.method}:${queryHash}`;

    // 2. Cache lookup
    const cached = await redis.get(cacheKey);
    if (cached) {
      return res.type('application/json').send(cached);
    }

    // 3. Proxy to Last.fm (respect rate limits)
    const upstreamRes = await fetch(`${LASTFM_URL}?${params.toString()}`, { timeout: 10_000 });
    const body = await upstreamRes.text();

    // 4. Store in cache (only if successful)
    if (upstreamRes.ok) {
      await redis.set(cacheKey, body, 'EX', CACHE_TTL);
    }

    // 5. Pipe upstream status & body to client
    res.status(upstreamRes.status).type('application/json').send(body);
  } catch (err) {
    console.error('Last.fm proxy error', err);
    res.status(502).json({ error: 502, message: 'Bad Gateway – Last.fm proxy failed' });
  }
});

export default app;
```

To run locally:
```bash
LASTFM_API_KEY=👉YOUR_KEY👈 REDIS_URL=redis://localhost:6379 node server.js
```

---

## 6. Deployment Checklist
1. **Dockerfile** with multi-stage build (install deps → `node dist/server.js`).
2. **Env Vars**: `LASTFM_API_KEY`, `REDIS_URL`, `PORT`, optional `API_SECRET`.
3. **Observability**: log upstream latency, cache hits/misses, and `Last-Modified` headers.
4. **CI/CD**: build, unit test (Jest), deploy to Fly.io / Render / AWS Fargate / Cloud Run.
5. **HTTPS** & **HSTS**: terminate TLS at load-balancer.

---

## 7. Security Best Practices
* Never expose the real Last.fm key to mobile clients.
* Add CORS policy allowing only `bopmaps.com` origins.
* Protect with authentication for internal usage (JWT or HMAC header).
* Enable **WAF** rules to block automated scraping.

---

## 8. Updating the Flutter Client
* The constant `_baseUrl` in `lib/services/music/lastfm_service.dart` has been updated to `https://api.bopmaps.com/lastfm` (see commit above).
* No further code changes are required—the same query parameters are still appended.

---

## 9. Future Enhancements
* **Advanced Cache Invalidation**: purge on artist release events or at midnight.
* **GraphQL Endpoint**: expose curated fields instead of full Last.fm payload.
* **Edge Caching**: push hot keys to Cloudflare Workers KV for ultra-low latency.
* **Metrics Dashboard**: Grafana panel for cache hit ratio, Last.fm quota usage, p95 latency. 