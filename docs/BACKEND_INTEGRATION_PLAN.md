# BOPMaps Backend Integration Plan

## Overview

This document outlines a comprehensive plan for integrating Django backend APIs with the BOPMaps Flutter application to enhance map data fetching, caching, and overall performance. The plan focuses on maintaining and optimizing the existing 2.5D rendering capabilities while adding robust server-side components.

## Current Architecture Analysis

### Map Data Flow
1. **Data Sources**: 
   - OpenStreetMap (OSM) for base tiles and geographic data
   - Custom pins managed through an API service
   - Local caching via `MapCacheManager` and various caching components

2. **Caching System**:
   - Multi-level caching (memory and disk)
   - Offline region management
   - Smart throttling to avoid OSM rate limiting
   - Tile fetching and optimization pipelines

3. **Rendering Pipeline**:
   - `FlutterMapWidget` as the core rendering component
   - `MultiLevelRenderer` for handling different zoom levels
   - 2.5D effect implementation via perspective transformations
   - Toggle for 3D building rendering

4. **Performance Optimizations**:
   - Debouncing for zoom events
   - Detail level adjustments based on zoom
   - Emergency performance mode for handling potential freezes

## Backend Integration Strategy

### 1. Proxy API for Map Tiles

Create a Django service to proxy and cache map tile requests:

```python
# tiles/views.py
from django.http import HttpResponse, StreamingHttpResponse
from django.core.cache import cache
import requests

class OSMTileView(APIView):
    def get(self, request, z, x, y, format=None):
        # Generate cache key
        cache_key = f"osm_tile_{z}_{x}_{y}"
        
        # Check if tile exists in cache
        cached_tile = cache.get(cache_key)
        if cached_tile:
            return HttpResponse(cached_tile, content_type="image/png")
        
        # If not in cache, fetch from OSM
        url = f"https://tile.openstreetmap.org/{z}/{x}/{y}.png"
        response = requests.get(url, stream=True)
        
        if response.status_code == 200:
            # Store in cache (1 week expiry)
            cache.set(cache_key, response.content, timeout=604800)
            return HttpResponse(response.content, content_type="image/png")
        
        return HttpResponse(status=response.status_code)
```

```python
# urls.py
urlpatterns = [
    # Map tile proxy
    path('api/tiles/osm/<int:z>/<int:x>/<int:y>.png', OSMTileView.as_view(), name='osm-tile'),
]
```

### 2. Vector Data API

Create APIs for vector data (buildings, roads, etc.) to reduce direct OSM dependencies:

```python
# geo/views.py
from django.contrib.gis.geos import Polygon
from django.contrib.gis.db.models.functions import Distance
from rest_framework import viewsets
from .models import Building, Road, Park
from .serializers import BuildingSerializer, RoadSerializer, ParkSerializer

class BuildingViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = BuildingSerializer
    
    def get_queryset(self):
        # Get bounds parameters
        north = float(self.request.query_params.get('north', 90))
        south = float(self.request.query_params.get('south', -90))
        east = float(self.request.query_params.get('east', 180))
        west = float(self.request.query_params.get('west', -180))
        zoom = int(self.request.query_params.get('zoom', 15))
        
        # Create a polygon from the bounds
        bounds = Polygon.from_bbox((west, south, east, north))
        
        # Adjust detail level based on zoom
        if zoom < 14:
            return Building.objects.filter(
                geometry__intersects=bounds
            ).simplify(
                tolerance=0.0001
            )[:500]  # Limit result count for performance
        elif zoom < 16:
            return Building.objects.filter(
                geometry__intersects=bounds
            ).simplify(
                tolerance=0.00005
            )[:1000]
        else:
            return Building.objects.filter(
                geometry__intersects=bounds
            )[:2000]
```

### 3. Region Bundle API

Create an API for downloading complete map regions as bundled data packages:

```python
# regions/views.py
from django.http import FileResponse
from rest_framework.views import APIView
from .tasks import create_region_bundle

class RegionBundleView(APIView):
    def post(self, request, format=None):
        # Get region parameters
        north = float(request.data.get('north'))
        south = float(request.data.get('south'))
        east = float(request.data.get('east'))
        west = float(request.data.get('west'))
        min_zoom = int(request.data.get('min_zoom', 10))
        max_zoom = int(request.data.get('max_zoom', 18))
        
        # Create a task to generate the region bundle
        task = create_region_bundle.delay(
            north=north,
            south=south,
            east=east,
            west=west,
            min_zoom=min_zoom,
            max_zoom=max_zoom
        )
        
        # Return task ID for status polling
        return Response({"task_id": task.id})
    
    def get(self, request, task_id, format=None):
        # Check task status
        task = AsyncResult(task_id)
        
        if task.state == 'SUCCESS':
            # Return the bundle file
            bundle_path = task.result
            return FileResponse(
                open(bundle_path, 'rb'),
                content_type='application/zip',
                as_attachment=True,
                filename='region_bundle.zip'
            )
        
        return Response({
            "status": task.state,
            "progress": task.info.get('progress', 0) if task.info else 0
        })
```

### 4. Map Settings Sync API

Create an API for synchronizing map settings between devices:

```python
# settings/views.py
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from .models import UserMapSettings
from .serializers import UserMapSettingsSerializer

class UserMapSettingsViewSet(viewsets.ModelViewSet):
    serializer_class = UserMapSettingsSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return UserMapSettings.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
```

## Django Models

### 1. Buildings and Map Features

```python
# geo/models.py
from django.contrib.gis.db import models

class Building(models.Model):
    osm_id = models.BigIntegerField(unique=True)
    name = models.CharField(max_length=255, null=True, blank=True)
    height = models.FloatField(null=True, blank=True)
    levels = models.IntegerField(null=True, blank=True)
    geometry = models.GeometryField(srid=4326)
    
    class Meta:
        indexes = [models.Index(fields=['osm_id']), models.Index(name='building_geom_idx', fields=['geometry'])]

class Road(models.Model):
    osm_id = models.BigIntegerField(unique=True)
    name = models.CharField(max_length=255, null=True, blank=True)
    road_type = models.CharField(max_length=50)
    geometry = models.LineStringField(srid=4326)
    
    class Meta:
        indexes = [models.Index(fields=['osm_id']), models.Index(name='road_geom_idx', fields=['geometry'])]

class Park(models.Model):
    osm_id = models.BigIntegerField(unique=True)
    name = models.CharField(max_length=255, null=True, blank=True)
    geometry = models.GeometryField(srid=4326)
    
    class Meta:
        indexes = [models.Index(fields=['osm_id']), models.Index(name='park_geom_idx', fields=['geometry'])]
```

### 2. Cached Regions

```python
# regions/models.py
from django.db import models
from django.contrib.gis.db import models as gis_models

class CachedRegion(models.Model):
    name = models.CharField(max_length=255)
    north = models.FloatField()
    south = models.FloatField()
    east = models.FloatField()
    west = models.FloatField()
    min_zoom = models.IntegerField()
    max_zoom = models.IntegerField()
    bounds = gis_models.PolygonField(srid=4326)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    bundle_file = models.FileField(upload_to='region_bundles/')
    size_kb = models.IntegerField()
    
    class Meta:
        indexes = [models.Index(name='region_bounds_idx', fields=['bounds'])]
```

### 3. User Map Settings

```python
# settings/models.py
from django.db import models
from django.conf import settings

class UserMapSettings(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    show_feature_info = models.BooleanField(default=False)
    use_3d_buildings = models.BooleanField(default=True)
    default_latitude = models.FloatField(default=37.7749)
    default_longitude = models.FloatField(default=-122.4194)
    default_zoom = models.FloatField(default=17.0)
    max_cache_size_mb = models.IntegerField(default=500)
    updated_at = models.DateTimeField(auto_now=True)
```

## Flutter Integration

### 1. Update MapCacheManager to Use the New Backend

```dart
// lib/services/map_cache_manager.dart

// Add new method to fetch tiles from Django backend
Future<Uint8List?> getTileFromBackend(TileCoordinates coords) async {
  try {
    final url = '${AppConstants.apiBaseUrl}/api/tiles/osm/${coords.z}/${coords.x}/${coords.y}.png';
    
    final response = await http.get(Uri.parse(url), headers: {
      'Accept': 'image/png',
    }).timeout(const Duration(seconds: 10));
    
    if (response.statusCode == 200) {
      return response.bodyBytes;
    }
    return null;
  } catch (e) {
    debugPrint('Error fetching tile from backend: $e');
    return null;
  }
}

// Modify the existing tile fetching method to try backend first
Future<Uint8List?> getCachedTile(TileCoordinates coords, TileLayer options) async {
  await _ensureInitialized();
  
  final tileUrl = _getTileUrl(coords, options);
  final tileHash = _urlToTileHash(tileUrl);
  
  // First check memory cache for faster access
  if (_memoryTileCache.containsKey(tileHash)) {
    return _memoryTileCache[tileHash];
  }
  
  // Then check disk cache
  final tileFile = File('${_cacheDir!.path}/tiles/$tileHash');
  if (await tileFile.exists()) {
    try {
      final data = await tileFile.readAsBytes();
      // Store in memory cache for next time
      _memoryTileCache[tileHash] = data;
      return data;
    } catch (e) {
      debugPrint('Error reading cached tile: $e');
    }
  }
  
  // Try offline regions
  // ...existing offline region code...
  
  // If not found in any cache, try the backend
  final backendTile = await getTileFromBackend(coords);
  if (backendTile != null) {
    // Cache the tile for future use
    try {
      await tileFile.create(recursive: true);
      await tileFile.writeAsBytes(backendTile);
      _memoryTileCache[tileHash] = backendTile;
    } catch (e) {
      debugPrint('Error caching tile: $e');
    }
    return backendTile;
  }
  
  // If backend fails, fall back to OSM direct (existing code)
  return null;
}
```

### 2. Building Data Fetching from Backend

```dart
// lib/services/vector_data_service.dart
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:latlong2/latlong.dart';
import '../models/building.dart';
import '../config/constants.dart';

class VectorDataService {
  final String baseUrl = AppConstants.apiBaseUrl;
  
  Future<List<Building>> getBuildings(LatLngBounds bounds, int zoomLevel) async {
    try {
      final url = Uri.parse('$baseUrl/api/geo/buildings/').replace(
        queryParameters: {
          'north': bounds.northEast.latitude.toString(),
          'south': bounds.southWest.latitude.toString(),
          'east': bounds.northEast.longitude.toString(),
          'west': bounds.southWest.longitude.toString(),
          'zoom': zoomLevel.toString(),
        },
      );
      
      final response = await http.get(url, headers: {
        'Accept': 'application/json',
      }).timeout(const Duration(seconds: 15));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return compute(_parseBuildings, data);
      } else {
        throw Exception('Failed to load buildings: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching buildings: $e');
      throw Exception('Network error while fetching buildings');
    }
  }
  
  // Parse buildings on a background isolate for better performance
  static List<Building> _parseBuildings(dynamic data) {
    final List<dynamic> buildingData = data['results'];
    return buildingData.map((json) => Building.fromJson(json)).toList();
  }
  
  // Similar methods for roads, parks, etc.
}
```

### 3. Region Bundle Downloader

```dart
// lib/services/region_bundle_service.dart
import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import '../models/offline_region.dart';
import '../config/constants.dart';

class RegionBundleService {
  final String baseUrl = AppConstants.apiBaseUrl;
  
  // Request bundle creation
  Future<String> requestRegionBundle(OfflineRegion region) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/regions/bundle/'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'north': region.north,
          'south': region.south,
          'east': region.east,
          'west': region.west,
          'min_zoom': region.minZoom,
          'max_zoom': region.maxZoom,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['task_id'];
      } else {
        throw Exception('Failed to request region bundle: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error requesting region bundle: $e');
      throw Exception('Network error while requesting region bundle');
    }
  }
  
  // Check bundle status
  Future<Map<String, dynamic>> checkBundleStatus(String taskId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/regions/bundle/$taskId/'),
        headers: {
          'Accept': 'application/json',
        },
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to check bundle status: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error checking bundle status: $e');
      throw Exception('Network error while checking bundle status');
    }
  }
  
  // Download bundle
  Future<File> downloadBundle(String taskId, Function(double) onProgress) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/regions/bundle/$taskId/download/'),
        headers: {
          'Accept': 'application/zip',
        },
      );
      
      if (response.statusCode == 200) {
        // Save the bundle file
        final appDir = await getApplicationDocumentsDirectory();
        final file = File('${appDir.path}/bundles/$taskId.zip');
        await file.create(recursive: true);
        await file.writeAsBytes(response.bodyBytes);
        return file;
      } else {
        throw Exception('Failed to download bundle: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error downloading bundle: $e');
      throw Exception('Network error while downloading bundle');
    }
  }
}
```

### 4. Settings Synchronization

```dart
// lib/services/settings_sync_service.dart
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/map_settings.dart';
import '../config/constants.dart';

class SettingsSyncService {
  final String baseUrl = AppConstants.apiBaseUrl;
  
  Future<MapSettings> fetchUserMapSettings(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/settings/map/'),
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return MapSettings.fromJson(data);
      } else if (response.statusCode == 404) {
        // User doesn't have settings yet, return defaults
        return MapSettings();
      } else {
        throw Exception('Failed to fetch settings: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching user map settings: $e');
      // Return default settings on error
      return MapSettings();
    }
  }
  
  Future<bool> saveUserMapSettings(MapSettings settings, String token) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/settings/map/'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(settings.toJson()),
      );
      
      return response.statusCode == 200 || response.statusCode == 201;
    } catch (e) {
      debugPrint('Error saving user map settings: $e');
      return false;
    }
  }
}
```

## Performance Optimizations

### 1. Backend-Assisted Caching Strategy

Implement a smart caching strategy where the backend determines what data needs to be refreshed:

```dart
// lib/services/cache_intelligence_service.dart
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../config/constants.dart';

class CacheIntelligenceService {
  final String baseUrl = AppConstants.apiBaseUrl;
  
  Future<Map<String, dynamic>> checkDataFreshness(
    List<String> cachedHashes,
    String dataType,
    String regionId,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/cache/check-freshness/'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'cached_hashes': cachedHashes,
          'data_type': dataType,
          'region_id': regionId,
        }),
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        // If server fails, assume all data needs refresh
        return {
          'outdated_hashes': cachedHashes,
          'new_hashes': [],
        };
      }
    } catch (e) {
      debugPrint('Error checking data freshness: $e');
      // Assume all data is fresh if network fails (offline mode)
      return {
        'outdated_hashes': [],
        'new_hashes': [],
      };
    }
  }
}
```

### 2. Data Compression

Implement data compression for vector data to reduce transfer sizes:

```python
# api/views.py
import gzip
from django.http import HttpResponse
from django.views.decorators.gzip import gzip_page

@gzip_page
def compressed_vector_data(request):
    # Process data as usual
    data = get_vector_data(request.GET)
    
    # JSON encode the data
    json_data = json.dumps(data)
    
    # Return with Content-Encoding: gzip
    # (The @gzip_page decorator handles the compression)
    return HttpResponse(
        json_data,
        content_type='application/json'
    )
```

### 3. Differential Updates

Implement a system for differential updates to minimize data transfer:

```python
# api/views.py
from django.views.decorators.http import etag

def generate_etag(request, *args, **kwargs):
    # Generate an ETag based on the latest data modification time
    from .models import Building
    latest = Building.objects.latest('updated_at')
    return f"{latest.updated_at.timestamp()}"

@etag(generate_etag)
def buildings_view(request):
    # If client has current version, Django will return 304 Not Modified
    # Otherwise, process and return data as usual
    data = get_buildings_data(request.GET)
    return JsonResponse(data)
```

## Background Data Management

### 1. Data Import from OSM

Set up Celery tasks to import and update data from OSM:

```python
# tasks.py
import os
import subprocess
from celery import shared_task
from django.conf import settings
from .models import Building, Road, Park

@shared_task
def import_osm_data(north, south, east, west):
    # Create temp directory
    temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp_osm')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Download OSM data
    bbox = f"{west},{south},{east},{north}"
    osm_file = os.path.join(temp_dir, 'data.osm')
    
    # Use osmium or similar tool
    cmd = f"osmium extract -b {bbox} -o {osm_file} {settings.OSM_PLANET_FILE}"
    subprocess.run(cmd, shell=True, check=True)
    
    # Process and import to database
    # Process buildings
    process_buildings(osm_file)
    
    # Process roads
    process_roads(osm_file)
    
    # Process parks
    process_parks(osm_file)
    
    return True
```

### 2. Data Indexing and Optimization

Implement efficient spatial indexing for vector data:

```python
# models.py
from django.contrib.gis.db import models

class Building(models.Model):
    # ... fields as defined earlier
    
    class Meta:
        indexes = [
            models.Index(fields=['osm_id']),
            # R-tree spatial index
            models.Index(name='building_geom_idx', fields=['geometry']),
            # Additional index for common queries
            models.Index(name='building_height_idx', fields=['height']),
        ]
```

## Deployment Considerations

### 1. Infrastructure Requirements

For optimal performance, deploy the Django backend with:

- PostgreSQL with PostGIS extension for spatial data
- Redis for caching and Celery task queue
- CDN for tile serving
- Load balancer for API distribution
- Auto-scaling for handling traffic spikes

### 2. Monitoring Setup

Implement monitoring for:

- API response times
- Cache hit/miss rates
- Database query performance
- Storage usage
- Error rates

## Implementation Timeline

1. **Phase 1: Setup (Week 1-2)**
   - Configure Django project with GeoDjango
   - Set up PostgreSQL with PostGIS
   - Implement basic authentication and settings models

2. **Phase 2: Tile Proxy (Week 3-4)**
   - Implement OSM tile proxy
   - Add Redis caching for tiles
   - Create cache invalidation mechanisms

3. **Phase 3: Vector Data APIs (Week 5-6)**
   - Implement building, road, park models
   - Create data import scripts from OSM
   - Build optimized vector data APIs

4. **Phase 4: Region Bundle System (Week 7-8)**
   - Implement region bundling system
   - Create bundle download APIs
   - Test with various region sizes

5. **Phase 5: Flutter Integration (Week 9-10)**
   - Update Flutter services to use new APIs
   - Implement progressive loading strategies
   - Add synchronization of settings

6. **Phase 6: Testing and Optimization (Week 11-12)**
   - Performance testing under various conditions
   - Optimize bottlenecks
   - Refine caching strategies

## Conclusion

This backend integration plan enhances the BOPMaps application with server-side capabilities that will:

1. **Reduce API Rate Limiting Issues**: By proxying requests through our server
2. **Improve Data Loading Performance**: Through optimized spatial queries and compression
3. **Enable Robust Offline Capabilities**: With comprehensive region bundles
4. **Support Cross-Device Synchronization**: Through server-backed settings
5. **Maintain Rendering Performance**: By offloading data processing to the server

The plan focuses on enhancing the existing frontend implementation rather than replacing it, ensuring that the app's distinctive 2.5D visual effects and current user experience are preserved while adding robust backend support. 