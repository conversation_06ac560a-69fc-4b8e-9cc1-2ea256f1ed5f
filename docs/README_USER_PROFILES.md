# User Profile Viewing System

This document explains how to use the new user profile viewing functionality that allows users to view each other's public profiles, pins, and collections, as well as send friend requests.

## Overview

The system includes:
1. **User Search** - Search for users by username
2. **Public Profile View** - View another user's public profile information
3. **Public Pins View** - View another user's public pins
4. **Public Collections View** - View another user's public collections
5. **Friend Request System** - Send, accept, reject, and cancel friend requests

## Files Created

### Models
- `lib/models/public_user_profile.dart` - Model for public user profile data

### Services
- `lib/services/public_profile_service.dart` - API service for user profiles and friend requests

### Providers
- `lib/providers/public_profile_provider.dart` - State management for public profiles

### Screens
- `lib/screens/profile/user_profile_screen.dart` - Main user profile viewing screen

### Widgets
- `lib/widgets/profile/public_profile_header.dart` - Profile header with user info
- `lib/widgets/profile/public_profile_stats.dart` - Stats display (pins, collections)
- `lib/widgets/profile/public_pins_tab.dart` - Tab showing user's public pins
- `lib/widgets/profile/public_collections_tab.dart` - Tab showing user's public collections
- `lib/widgets/profile/user_search_widget.dart` - Search widget for finding users

## Usage

### 1. Add Provider to Your App

First, add the `PublicProfileProvider` to your app's provider list:

```dart
MultiProvider(
  providers: [
    // Your existing providers...
    ChangeNotifierProvider(create: (context) => PublicProfileProvider()),
  ],
  child: YourApp(),
)
```

### 2. Navigate to User Profile

To navigate to a user's profile:

```dart
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => UserProfileScreen(
      userId: userId, // The user's ID
      showBottomNav: false, // Whether to show bottom navigation
    ),
  ),
);
```

### 3. User Search

To show the user search interface:

```dart
// As a bottom sheet
UserSearchWidget.showAsBottomSheet(context);

// Or as a regular widget
const UserSearchWidget()
```

### 4. Add Search Button to Existing Screens

Add a search button to your existing screens:

```dart
IconButton(
  onPressed: () {
    UserSearchWidget.showAsBottomSheet(context);
  },
  icon: const Icon(Icons.person_search),
  tooltip: 'Search Users',
)
```

## Features

### Profile Screen Features
- **Profile Header**: Shows user's profile picture, username, bio, location, and online status
- **Stats**: Displays pin count and public collection count
- **Tabs**: Switch between viewing pins and collections
- **Friend Actions**: Send friend requests, unfriend, accept/reject requests
- **Responsive Design**: Adapts to different screen sizes

### Search Features
- **Debounced Search**: Waits 500ms after user stops typing
- **Minimum Characters**: Requires at least 2 characters
- **Loading States**: Shows shimmer loading during search
- **Error Handling**: Displays error messages and retry options
- **User Cards**: Shows profile picture, username, bio, and stats

### Friend Request System
- **Send Requests**: Send friend requests to other users
- **Accept/Reject**: Handle incoming friend requests
- **Cancel**: Cancel sent friend requests
- **Unfriend**: Remove existing friends
- **Status Indicators**: Shows current friendship status

## API Endpoints Used

The system expects these backend endpoints:

- `GET /api/users/search/?q={query}` - Search users
- `GET /api/users/{id}/public_profile/` - Get user profile
- `GET /api/users/{id}/public_pins/` - Get user's public pins
- `GET /api/users/{id}/public_collections/` - Get user's public collections
- `GET /api/friends/status/{id}/` - Get friendship status
- `POST /api/friends/request/` - Send friend request
- `POST /api/friends/accept/{id}/` - Accept friend request
- `POST /api/friends/reject/{id}/` - Reject friend request
- `DELETE /api/friends/cancel/{id}/` - Cancel friend request
- `DELETE /api/friends/unfriend/{id}/` - Unfriend user

## Integration Examples

### Add to Friends Screen

Add a search button to your friends screen:

```dart
// In your friends screen app bar
actions: [
  IconButton(
    onPressed: () {
      UserSearchWidget.showAsBottomSheet(context);
    },
    icon: const Icon(Icons.person_search),
    tooltip: 'Find Users',
  ),
],
```

### Navigate from Pin Cards

Allow users to tap on usernames in pin cards to view profiles:

```dart
GestureDetector(
  onTap: () {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(
          userId: pin.ownerId,
          showBottomNav: false,
        ),
      ),
    );
  },
  child: Text('@${pin.ownerUsername}'),
)
```

### Add to Main Navigation

Add user search to your main navigation:

```dart
// In your main screen
FloatingActionButton(
  onPressed: () {
    UserSearchWidget.showAsBottomSheet(context);
  },
  child: const Icon(Icons.person_search),
)
```

## Customization

### Styling

The widgets use your app's theme colors and can be customized by modifying:
- Theme colors in your app
- Individual widget styling in the widget files
- Screen layouts in the screen files

### Functionality

You can extend the functionality by:
- Adding more profile information
- Implementing additional friend request features
- Adding profile editing capabilities
- Integrating with your existing pin and collection systems

## Error Handling

The system includes comprehensive error handling:
- Network errors are caught and displayed to users
- Loading states are shown during API calls
- Retry buttons are provided for failed operations
- Empty states are shown when no data is available

## Performance Considerations

- **Debounced Search**: Prevents excessive API calls during typing
- **Lazy Loading**: Loads profile data progressively
- **Caching**: Provider maintains state to avoid repeated API calls
- **Shimmer Loading**: Provides smooth loading experience
- **Keep Alive**: Tabs maintain state when switching

## Security

- All endpoints require authentication
- Only public data is accessible
- Friend request validation on backend
- No private information is exposed

This system provides a complete user profile viewing experience that integrates seamlessly with your existing Flutter app architecture. 