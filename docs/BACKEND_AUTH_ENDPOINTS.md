# Backend Authentication Endpoints

This document describes the backend endpoints required for the email-first authentication flow and music service integration.

## Authentication Flow Overview

The app supports three authentication paths:

1. **Email-first flow**: User enters email → checks if account exists → login or signup
2. **Spotify authentication**: User authenticates with Spotify → data pre-filled in onboarding
3. **Apple Music authentication**: User authenticates with Apple Music → data pre-filled in onboarding

All paths eventually lead to the onboarding flow where users complete their profile setup and account creation.

## 1. Check Email Endpoint

**Endpoint:** `POST /api/auth/check-email`

**Purpose:** Check if an email address already has an account

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "exists": true,
  "user": {
    "id": "user_id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profile_pic": "https://example.com/profile.jpg",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**Success Response for non-existing email (200):**
```json
{
  "exists": false,
  "user": null
}
```

## 2. Login Endpoint

**Endpoint:** `POST /api/auth/login`

**Purpose:** Authenticate existing user with email and password

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123"
}
```

**Success Response (200):**
```json
{
  "auth_token": "jwt_token_here",
  "refresh_token": "refresh_token_here",
  "user": {
    "id": "user_id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profile_pic": "https://example.com/profile.jpg",
    "bio": "Music lover",
    "is_verified": true,
    "favorite_genres": ["pop", "rock"],
    "spotify_connected": true,
    "apple_music_connected": false,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**Error Response (401):**
```json
{
  "error": "Invalid credentials",
  "message": "The password you entered is incorrect"
}
```

## 3. Register Endpoint

**Endpoint:** `POST /api/auth/register`

**Purpose:** Create a new user account

**Request Body:**
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePassword123"
}
```

**Success Response (201):**
```json
{
  "auth_token": "jwt_token_here",
  "refresh_token": "refresh_token_here",
  "user": {
    "id": "new_user_id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profile_pic": null,
    "bio": "",
    "is_verified": false,
    "favorite_genres": [],
    "spotify_connected": false,
    "apple_music_connected": false,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**Error Response (400):**
```json
{
  "error": "Validation error",
  "errors": {
    "username": ["This username is already taken"],
    "email": ["An account with this email already exists"]
  }
}
```

## Music Service Integration

### Data Pre-filling from Music Services

When users authenticate through Spotify or Apple Music, the app will navigate to the onboarding flow with pre-filled data:

**From Spotify:**
- Email (if available)
- Username (display_name)
- Profile picture URL (from images array)

**From Apple Music:**
- Username (default: "Apple Music User")
- Email (not available from Apple Music API)
- Profile picture (not available from Apple Music API)

### Backend Considerations

1. **Duplicate Email Handling**: If a user authenticates with Spotify but an account with that email already exists, consider implementing account linking functionality.

2. **Username Conflicts**: Since usernames from music services might conflict with existing usernames, the backend should:
   - Suggest alternative usernames if conflicts occur
   - Allow username modification during onboarding

3. **Music Service Tokens**: Consider storing music service refresh tokens for API access:
   ```json
   {
     "spotify_refresh_token": "token_here",
     "apple_music_user_token": "token_here"
   }
   ```

## Password Requirements

- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- Optional: Special characters for extra strength

## Username Requirements

- 2-30 characters
- Only letters, numbers, underscore (_), and hyphen (-)
- Must be unique in the system
- Can be pre-filled from music service display names

## Security Considerations

1. All endpoints should use HTTPS
2. Implement rate limiting on authentication endpoints
3. Hash passwords using bcrypt or similar secure algorithm
4. JWT tokens should have appropriate expiration times
5. Implement CSRF protection where applicable
6. Validate music service tokens server-side when provided
7. Store music service tokens securely for future API access 