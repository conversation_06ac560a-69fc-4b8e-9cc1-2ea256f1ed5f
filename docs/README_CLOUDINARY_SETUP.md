# Cloudinary Integration Setup Guide

This guide explains how to set up and use Cloudinary for image uploads in the BOPMaps Flutter application.

## Overview

Cloudinary is a cloud-based image and video management service that provides:
- Image upload and storage
- Automatic image optimization
- Real-time image transformations
- CDN delivery for fast loading
- Comprehensive media management

## Prerequisites

1. A Cloudinary account (free tier available)
2. Flutter development environment set up
3. The BOPMaps project properly configured

## Setup Instructions

### 1. Create a Cloudinary Account

1. Go to [https://cloudinary.com/](https://cloudinary.com/)
2. Sign up for a free account or log in to your existing account
3. Once logged in, go to your Dashboard to find your credentials

### 2. Get Your Cloudinary Credentials

From your Cloudinary Dashboard, you'll need:
- **Cloud Name**: Your unique cloud name
- **API Key**: Your API key for authentication
- **API Secret**: Your API secret for signed uploads
- **Upload Preset**: (Optional) For unsigned uploads

### 3. Configure Environment Variables

Create a `.env` file in your project root (if it doesn't exist) and add your Cloudinary credentials:

```env
# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name_here
CLOUDINARY_API_KEY=your_api_key_here
CLOUDINARY_API_SECRET=your_api_secret_here
CLOUDINARY_UPLOAD_PRESET=your_upload_preset_here
```

⚠️ **Important**: Never commit your `.env` file to version control. Add it to your `.gitignore` file.

### 4. Initialize Environment Variables

Make sure your `main.dart` loads the environment variables:

```dart
import 'package:flutter_dotenv/flutter_dotenv.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Load environment variables
  await dotenv.load();
  
  runApp(MyApp());
}
```

### 5. Set Up Upload Preset (Optional)

For unsigned uploads (simpler but less secure), create an upload preset:

1. Go to your Cloudinary Dashboard
2. Navigate to Settings > Upload
3. Scroll down to "Upload presets"
4. Click "Add upload preset"
5. Configure the preset:
   - **Preset name**: Choose a name (e.g., `bop_maps_uploads`)
   - **Signing Mode**: Set to "Unsigned"
   - **Folder**: Optionally set a default folder
   - **Allowed formats**: Specify allowed image formats (jpg, png, webp, etc.)
   - **Transformation**: Set default transformations if needed
6. Save the preset

### 6. Dependencies

The following dependencies have been added to `pubspec.yaml`:

```yaml
dependencies:
  cloudinary_flutter: ^1.0.4
  cloudinary_url_gen: ^1.5.5
```

Run `flutter pub get` to install the dependencies.

## Usage

### Basic Image Upload

```dart
import 'package:your_app/services/cloudinary_service.dart';

// Initialize the service
final cloudinaryService = CloudinaryService();

// Upload an image file
try {
  final result = await cloudinaryService.uploadImage(
    file: imageFile,
    folder: 'collections', // Optional: organize in folders
    tags: ['user_upload', 'collection_cover'], // Optional: add tags
    quality: 85, // Optional: set quality (1-100)
  );
  
  print('Upload successful: ${result.secureUrl}');
  print('Public ID: ${result.publicId}');
} catch (e) {
  print('Upload failed: $e');
}
```

### Upload with Transformations

```dart
final result = await cloudinaryService.uploadImage(
  file: imageFile,
  folder: 'profiles',
  quality: 90,
  transformation: 'c_fill,w_400,h_400,g_face', // Crop to 400x400 focusing on face
);
```

### Unsigned Upload (Simpler)

```dart
final result = await cloudinaryService.uploadImageUnsigned(
  file: imageFile,
  folder: 'user_content',
  tags: ['user_generated'],
);
```

### Generate Optimized URLs

```dart
// Generate a URL with transformations
final optimizedUrl = cloudinaryService.generateImageUrl(
  publicId: 'collections/my_image',
  width: 300,
  height: 300,
  quality: 80,
  format: 'webp',
  crop: 'fill',
);
```

### Delete Images

```dart
final success = await cloudinaryService.deleteImage('collections/my_image');
if (success) {
  print('Image deleted successfully');
}
```

## Service Features

### CloudinaryService Methods

- `uploadImage()`: Upload image with full control and authentication
- `uploadImageFromBytes()`: Upload from bytes (useful for web)
- `uploadImageUnsigned()`: Simple upload using upload preset
- `generateImageUrl()`: Generate URLs with transformations
- `deleteImage()`: Delete images from Cloudinary

### Error Handling

The service includes comprehensive error handling:
- Network errors
- Authentication errors
- Upload failures
- Invalid file formats

### Security

- Uses signed uploads for security
- Supports both signed and unsigned uploads
- API secrets are loaded from environment variables through `AppConstants`
- Proper signature generation for authenticated requests

## Folder Organization

The service supports organizing uploads into folders:
- `collections/`: Collection cover images
- `profiles/`: User profile images
- `pins/`: Music pin images
- `user_content/`: General user-uploaded content

## Image Transformations

Cloudinary supports powerful image transformations:
- **Resizing**: `w_300,h_200` (width 300, height 200)
- **Cropping**: `c_fill,c_fit,c_scale` (different crop modes)
- **Quality**: `q_80` (80% quality)
- **Format**: `f_webp,f_jpg` (convert to WebP or JPEG)
- **Effects**: `e_grayscale,e_blur:300` (grayscale, blur)
- **Face detection**: `g_face` (focus on faces when cropping)

## Best Practices

1. **Use appropriate quality settings**: 85% for photos, 90% for images with text
2. **Organize with folders**: Keep uploads organized by feature/type
3. **Use tags**: Add relevant tags for better organization
4. **Handle errors gracefully**: Always wrap uploads in try-catch blocks
5. **Optimize for delivery**: Use appropriate transformations for different contexts
6. **Monitor usage**: Keep track of your Cloudinary usage limits

## Troubleshooting

### Common Issues

1. **Upload fails with "Invalid Signature" error**:
   - **Check your credentials**: Ensure `CLOUDINARY_CLOUD_NAME`, `CLOUDINARY_API_KEY`, and `CLOUDINARY_API_SECRET` are correctly set in your `.env` file
   - **Verify environment loading**: Make sure `await dotenv.load()` is called in your `main.dart` before the app starts
   - **Check for typos**: Ensure there are no extra spaces or characters in your credentials
   - **Restart the app**: After changing `.env` variables, restart your Flutter app completely

2. **Environment variables not loading**:
   - Ensure `.env` file is in project root (same level as `pubspec.yaml`)
   - Check that `flutter_dotenv` is properly initialized in `main.dart`
   - Verify the `.env` file format (no quotes around values, no extra spaces)

3. **Upload fails with 401 error**:
   - Check your API credentials
   - Ensure environment variables are loaded correctly
   - Verify your Cloudinary account is active

4. **Upload fails with 400 error**:
   - Verify file format is supported
   - Check file size limits
   - Ensure the file exists and is readable

5. **Images not showing after upload**:
   - Check the console for any error messages
   - Verify the returned `secureUrl` is valid
   - Ensure your app has internet connectivity

### Debug Information

The service includes debug logging when running in debug mode. Check your console for:
```
🌤️ Cloudinary service initialized:
   Cloud name: your_cloud_name
   API key: ********...
   Has secret: true

🔐 Generating Cloudinary signature:
   Parameters: {folder: collections, tags: collection_cover,user_upload, timestamp: **********}
   ...
```

### Testing Credentials

You can test your Cloudinary setup with this simple test:

```dart
import 'package:your_app/config/constants.dart';

void testCloudinaryCredentials() async {
  try {
    final service = CloudinaryService();
    print('✅ Cloudinary service initialized successfully');
    print('Cloud name: ${AppConstants.cloudinaryCloudName}');
    print('Is configured: ${AppConstants.isCloudinaryConfigured}');
  } catch (e) {
    print('❌ Cloudinary setup error: $e');
  }
}
```

### Environment File Format

Your `.env` file should look exactly like this (with your actual values):

```env
CLOUDINARY_CLOUD_NAME=my-cloud-name
CLOUDINARY_API_KEY=**********12345
CLOUDINARY_API_SECRET=abcdefghijklmnopqrstuvwxyz123456
CLOUDINARY_UPLOAD_PRESET=my_upload_preset
```

**Important notes:**
- No quotes around values
- No spaces around the `=` sign
- One variable per line
- No trailing spaces or characters

### Getting Help

If you're still having issues:
1. Double-check your Cloudinary dashboard for the correct credentials
2. Ensure your `.env` file is properly formatted
3. Check the Flutter debug console for detailed error messages
4. Try creating a new upload preset if using unsigned uploads
5. Verify your Cloudinary account status and usage limits

## Integration in BOPMaps

The Cloudinary service is integrated into:
- **Collection Creation**: Upload collection cover images
- **Profile Management**: Upload user profile pictures
- **Pin Creation**: Upload images for music pins
- **Image Display**: Generate optimized URLs for image display

## Support

For issues with the Cloudinary service:
1. Check the [Cloudinary Documentation](https://cloudinary.com/documentation)
2. Review the error messages in the console
3. Ensure your credentials are correctly configured
4. Check your Cloudinary dashboard for usage limits

## Resources

- [Cloudinary Flutter SDK Documentation](https://cloudinary.com/documentation/flutter_integration)
- [Cloudinary Transformation Reference](https://cloudinary.com/documentation/transformation_reference)
- [Cloudinary Upload API](https://cloudinary.com/documentation/upload_images)
- [Flutter Image Picker Documentation](https://pub.dev/packages/image_picker) 