# Background Location Integration Guide

This guide explains how to use the enhanced background location functionality in BOPMaps.

## Overview

The updated `LocationManager` now supports both foreground and background location tracking using the `background_location` package. This allows the app to continue tracking user location even when minimized, helping to discover nearby music pins automatically.

## Features

### Foreground Location Tracking
- High-accuracy location updates when app is active
- Automatic error recovery
- Stream-based position updates

### Background Location Tracking  
- Continues tracking when app is in background
- Persistent notification on Android
- Battery-optimized with configurable update intervals
- Automatic fallback when foreground tracking fails

## Setup

### Permissions Added

**iOS (Info.plist)**
```xml
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>BOPMaps needs access to your location to discover music pins nearby, even when the app is running in the background.</string>

<key>NSLocationAlwaysUsageDescription</key>
<string>BOPMaps needs access to your location to discover music pins nearby, even when the app is running in the background.</string>

<key>UIBackgroundModes</key>
<array>
    <string>location</string>
    <string>fetch</string>
</array>
```

**Android (AndroidManifest.xml)**
```xml
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android:permission.FOREGROUND_SERVICE_LOCATION" />
```

## Usage

### Basic Usage

```dart
import '../services/location/location_manager.dart';

final LocationManager locationManager = LocationManager();

// Start foreground tracking
await locationManager.startTracking();

// Start background tracking  
await locationManager.startBackgroundTracking();

// Stop background tracking
await locationManager.stopBackgroundTracking();

// Check status
bool isBackgroundTracking = locationManager.isBackgroundTracking;
LocationStatus status = locationManager.locationStatus;
```

### Using with MapProvider

```dart
import 'package:provider/provider.dart';
import '../providers/map_provider.dart';

// In your widget
final mapProvider = Provider.of<MapProvider>(context, listen: false);

// Start background location tracking
await mapProvider.startBackgroundLocationTracking();

// Stop background location tracking
await mapProvider.stopBackgroundLocationTracking();

// Toggle background tracking
bool isNowTracking = await mapProvider.toggleBackgroundLocationTracking();

// Check status
bool isTracking = mapProvider.isBackgroundLocationTracking;
```

### Error Handling

The LocationManager includes automatic error recovery:

```dart
// Listen to location status changes
locationManager.locationStatusStream.listen((status) {
  switch (status) {
    case LocationStatus.error:
      print('Location error: ${locationManager.errorMessage}');
      break;
    case LocationStatus.disabled:
      print('Location services disabled');
      break;
    case LocationStatus.denied:
      print('Location permission denied');
      break;
    case LocationStatus.available:
      print('Location tracking active');
      break;
  }
});

// Listen to position updates
locationManager.positionStream.listen((position) {
  if (position != null) {
    print('New location: ${position.latitude}, ${position.longitude}');
  }
});
```

## Configuration

### Android Notification Customization

```dart
await BackgroundLocation.setAndroidNotification(
  title: "BOPMaps Location",
  message: "Tracking your location to discover nearby music pins",
  icon: "@mipmap/ic_launcher",
);
```

### Update Interval

```dart
// Set update interval to 5 seconds (5000ms)
await BackgroundLocation.setAndroidConfiguration(5000);
```

## Example Implementation

See `lib/utils/background_location_example.dart` for a complete example showing:

- Location status monitoring
- Starting/stopping foreground and background tracking
- Error handling
- UI integration with Provider

## Best Practices

### 1. Request Permissions Progressively
```dart
// First request basic location permission
final permission = await locationManager.requestLocationPermission();

if (permission == LocationStatus.available) {
  // Then optionally request background location
  await locationManager.startBackgroundTracking();
}
```

### 2. Handle Battery Optimization
- Inform users that background location may affect battery life
- Provide easy toggle to disable background tracking
- Consider using lower update frequencies for background tracking

### 3. User Experience
- Show clear indication when background tracking is active
- Explain why background location is beneficial
- Allow users to control when background tracking is enabled

### 4. Error Recovery
The system automatically:
- Falls back to background location if foreground fails
- Retries failed location requests
- Handles permission changes gracefully

## Troubleshooting

### Common Issues

**Location Error: kCLErrorDomain error 1**
- This typically indicates location service couldn't determine location
- Background location service provides automatic fallback
- Error recovery attempts to restart location services

**Background Location Not Working on iOS**
- Ensure `UIBackgroundModes` includes `location`
- Check that "Always" location permission is granted
- Verify app has proper entitlements

**Android Foreground Service Issues**
- Ensure `FOREGROUND_SERVICE` permission is granted
- Check that notification settings allow persistent notifications
- Verify `FOREGROUND_SERVICE_LOCATION` permission for Android 14+

### Debug Logging

The LocationManager provides detailed console logging:
```
🗺️ LocationManager: Starting background location tracking
🗺️ LocationManager: Background location update received: lat: 37.7749, lng: -122.4194
```

Monitor these logs to debug location issues.

## Integration Points

### With Pin Discovery
Background location automatically triggers pin refresh when significant location changes occur.

### With MapProvider  
The MapProvider integrates seamlessly with background location to update the map even when the app is backgrounded.

### With Music Recommendations
Location updates can trigger nearby music pin discovery for better recommendations.

## Privacy Considerations

- Always explain to users why background location is needed
- Provide clear controls to enable/disable background tracking  
- Follow platform guidelines for location privacy
- Consider implementing location history limits

## Performance Impact

- Background location uses more battery than foreground-only tracking
- Update frequency can be adjusted to balance accuracy vs battery life
- System automatically optimizes location requests when possible

---

For questions or issues with background location, refer to the example implementation or check the console logs for detailed error information. 