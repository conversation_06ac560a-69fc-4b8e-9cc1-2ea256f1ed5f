name: Flutter CI

on:
  push:
    branches: [ main, develop, before_nav ]
  pull_request:
    branches: [ main, develop, before_nav ]
  workflow_dispatch:
    branches: [ main, develop, before_nav ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.x'
        channel: 'stable'
    
    - name: Install dependencies
      run: flutter pub get
      
    - name: Analyze project source
      run: flutter analyze
      
    - name: Run tests
      run: flutter test
      
    - name: Build APK
      run: flutter build apk --release
      
    - name: Build iOS
      run: flutter build ios --release --no-codesign 