import Foundation
import ARKit
import Flutter

/// Handles camera frame capture from ARKit for ML processing
class ARKitCameraCapture: NSObject {
    private weak var channel: FlutterMethodChannel?
    private var captureSession: ARSession?
    private var isCapturing = false
    private var captureTimer: Timer?
    
    init(channel: FlutterMethodChannel) {
        self.channel = channel
        super.init()
    }
    
    /// Start capturing camera frames at specified interval
    func startCapture(session: ARSession, intervalMs: Int) {
        guard !isCapturing else { return }
        
        self.captureSession = session
        self.isCapturing = true
        
        // Set up timer to capture frames at specified interval
        let interval = TimeInterval(intervalMs) / 1000.0
        captureTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            self?.captureCurrentFrame()
        }
    }
    
    /// Stop capturing camera frames
    func stopCapture() {
        isCapturing = false
        captureTimer?.invalidate()
        captureTimer = nil
        captureSession = nil
    }
    
    /// Capture current ARKit frame and send to Flutter
    private func captureCurrentFrame() {
        guard let session = captureSession,
              let currentFrame = session.currentFrame else { return }
        
        // Get the captured image
        let image = currentFrame.capturedImage
        
        // Convert CVPixelBuffer to UIImage
        guard let uiImage = pixelBufferToUIImage(pixelBuffer: image) else { return }
        
        // Convert to JPEG data with compression
        guard let imageData = uiImage.jpegData(compressionQuality: 0.7) else { return }
        
        // Send to Flutter via method channel
        DispatchQueue.main.async { [weak self] in
            self?.channel?.invokeMethod("onCameraFrame", arguments: [
                "imageData": FlutterStandardTypedData(bytes: imageData),
                "timestamp": Date().timeIntervalSince1970,
                "width": Int(uiImage.size.width),
                "height": Int(uiImage.size.height)
            ])
        }
    }
    
    /// Convert CVPixelBuffer to UIImage
    private func pixelBufferToUIImage(pixelBuffer: CVPixelBuffer) -> UIImage? {
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        let context = CIContext()
        
        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            return nil
        }
        
        // Rotate image to correct orientation (ARKit captures in landscape)
        let uiImage = UIImage(cgImage: cgImage)
        return rotateImage(uiImage, orientation: .right)
    }
    
    /// Rotate UIImage to correct orientation
    private func rotateImage(_ image: UIImage, orientation: UIImage.Orientation) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }
        
        var transform = CGAffineTransform.identity
        var width = CGFloat(cgImage.width)
        var height = CGFloat(cgImage.height)
        
        switch orientation {
        case .right:
            transform = transform.translatedBy(x: 0, y: width)
            transform = transform.rotated(by: -CGFloat.pi / 2)
            swap(&width, &height)
        case .left:
            transform = transform.translatedBy(x: height, y: 0)
            transform = transform.rotated(by: CGFloat.pi / 2)
            swap(&width, &height)
        case .down:
            transform = transform.translatedBy(x: width, y: height)
            transform = transform.rotated(by: CGFloat.pi)
        default:
            break
        }
        
        guard let colorSpace = cgImage.colorSpace,
              let context = CGContext(data: nil,
                                      width: Int(width),
                                      height: Int(height),
                                      bitsPerComponent: cgImage.bitsPerComponent,
                                      bytesPerRow: 0,
                                      space: colorSpace,
                                      bitmapInfo: cgImage.bitmapInfo.rawValue) else {
            return nil
        }
        
        context.concatenate(transform)
        
        switch orientation {
        case .left, .right:
            context.draw(cgImage, in: CGRect(x: 0, y: 0, width: height, height: width))
        default:
            context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        }
        
        guard let newCGImage = context.makeImage() else { return nil }
        return UIImage(cgImage: newCGImage)
    }
} 