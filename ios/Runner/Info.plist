<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ARKit</key>
	<true/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>BOPMaps</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>bopmaps</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.bopmaps.app</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>bopmaps</string>
				<string>com.googleusercontent.apps.1029712452528-l07jsf59h3f5g7uvq2il560trmg87c68</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>spotify</string>
		<string>spotify-action</string>
		<string>spotify-sdk</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppleMusicUsageDescription</key>
	<string>BOPMaps needs access to Apple Music to allow you to share and discover music through pins on the map.</string>
	<key>NSCameraUsageDescription</key>
	<string>BOPMaps needs access to your camera for AR pin placement and to capture photos when dropping pins.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>BOPMaps needs access to your location to discover music pins nearby, even when the app is running in the background.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>BOPMaps needs access to your location to discover music pins nearby, even when the app is running in the background.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>BOPMaps needs access to your location to show music pins near you and to place new pins.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>BOPMaps needs access to your microphone for voice interactions and audio features when creating music pins.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>BOPMaps needs permission to save photos to your photo library.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>BOPMaps needs access to your photo library to let you choose photos for your profile and pins.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>location</string>
		<string>fetch</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
</dict>
</plist>
