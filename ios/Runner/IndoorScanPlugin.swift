import Flutter
import UIKit
import ARKit
import RoomPlan

/// Plugin that handles indoor scanning using Apple's RoomPlan API
@available(iOS 16.0, *)
class IndoorScanPlugin: NSObject, FlutterPlugin {
    
    static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "com.bopmaps/indoor_scan", binaryMessenger: registrar.messenger())
        let instance = IndoorScanPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "checkLiDARCapability":
            result(ARWorldTrackingConfiguration.supportsSceneReconstruction(.mesh))
            
        case "startScan":
            if let args = call.arguments as? [String: Any],
               let scanName = args["scanName"] as? String {
                startRoomScan(scanName: scanName, result: result)
            } else {
                result(FlutterError(code: "INVALID_ARGUMENTS", 
                                    message: "Missing required arguments",
                                    details: nil))
            }
            
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    // MARK: - RoomPlan Integration
    
    private func startRoomScan(scanName: String, result: @escaping FlutterResult) {
        // Get the root view controller
        guard let rootVC = UIApplication.shared.windows.first?.rootViewController else {
            result(FlutterError(code: "NO_ROOT_VC", 
                                message: "Cannot access root view controller",
                                details: nil))
            return
        }
        
        // Create a RoomCaptureViewController
        let roomCaptureVC = RoomCaptureViewController()
        roomCaptureVC.scanName = scanName
        roomCaptureVC.delegate = self
        
        // Present the view controller
        rootVC.present(roomCaptureVC, animated: true)
        
        // Return success to Flutter - actual scan result will come later
        result(["status": "started"])
    }
}

// MARK: - RoomCaptureViewController

@available(iOS 16.0, *)
class RoomCaptureViewController: UIViewController {
    var scanName: String = ""
    weak var delegate: IndoorScanPlugin?
    private var roomCaptureView: RoomCaptureView!
    private var roomCaptureSessionConfig: RoomCaptureSession.Configuration = RoomCaptureSession.Configuration()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // Set up the room capture view
        roomCaptureView = RoomCaptureView(frame: view.bounds)
        roomCaptureView.captureSession.delegate = self
        roomCaptureView.delegate = self
        
        view.addSubview(roomCaptureView)
        
        // Add done button
        let doneButton = UIButton(type: .system)
        doneButton.setTitle("Done", for: .normal)
        doneButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
        doneButton.addTarget(self, action: #selector(doneButtonTapped), for: .touchUpInside)
        doneButton.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(doneButton)
        
        NSLayoutConstraint.activate([
            doneButton.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 16),
            doneButton.trailingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.trailingAnchor, constant: -16)
        ])
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        // Start the capture session
        roomCaptureView.captureSession.run(configuration: roomCaptureSessionConfig)
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // Stop the capture session
        roomCaptureView.captureSession.stop()
    }
    
    @objc private func doneButtonTapped() {
        // Stop the session and finish scanning
        roomCaptureView.captureSession.stop()
    }
}

// MARK: - RoomCaptureSessionDelegate

@available(iOS 16.0, *)
extension RoomCaptureViewController: RoomCaptureSessionDelegate {
    func captureSession(_ session: RoomCaptureSession, didUpdate room: CapturedRoom) {
        // Called when room capture data is updated
    }
    
    func captureSession(_ session: RoomCaptureSession, didAdd room: CapturedRoom) {
        // Called when capture adds a new room
    }
    
    func captureSession(_ session: RoomCaptureSession, didRemove room: CapturedRoom) {
        // Called when capture removes a room
    }
    
    func captureSession(_ session: RoomCaptureSession, didChange room: CapturedRoom) {
        // Called when capture changes a room
    }
    
    func captureSession(_ session: RoomCaptureSession, didStartWith configuration: RoomCaptureSession.Configuration) {
        // Called when session starts
    }
    
    func captureSession(_ session: RoomCaptureSession, didEndWith data: CapturedRoomData?, error: Error?) {
        if let error = error {
            print("Room capture error: \(error.localizedDescription)")
            return
        }
        
        guard let capturedRoomData = data else {
            print("No room capture data")
            return
        }
        
        // Process captured room data
        // This would typically involve exporting the model and sending data back to Flutter
        let roomBuilder = RoomBuilder(options: [.beautifyObjects])
        Task {
            do {
                let room = try await roomBuilder.capturedRoom(from: capturedRoomData)
                
                // Get the document directory
                let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                let scanDirectory = documentsPath.appendingPathComponent("indoor_scans", isDirectory: true)
                
                // Create the directory if it doesn't exist
                try? FileManager.default.createDirectory(at: scanDirectory, withIntermediateDirectories: true)
                
                // Create a UUID for the file
                let uuid = UUID().uuidString
                let fileName = "\(scanName.isEmpty ? "Room Scan" : scanName)_\(uuid).usdz"
                let fileURL = scanDirectory.appendingPathComponent(fileName)
                
                // Export the model to USDZ format
                try await room.export(to: fileURL)
                
                // Here we would send the results back to Flutter
                print("Exported room model to: \(fileURL.path)")
                
                // Dismiss the view controller
                DispatchQueue.main.async {
                    self.dismiss(animated: true)
                }
            } catch {
                print("Error processing room data: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - RoomCaptureViewDelegate

@available(iOS 16.0, *)
extension RoomCaptureViewController: RoomCaptureViewDelegate {
    func captureView(didPresent: CapturedRoom, error: Error?) {
        // Called when the view presents a captured room
    }
    
    func captureViewDidStartShowingResults(_ captureView: RoomCaptureView) {
        // Called when the view starts showing scanning results
    }
} 