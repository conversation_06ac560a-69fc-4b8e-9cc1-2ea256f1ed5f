# Uncomment this line to define a global platform for your project
platform :ios, '15.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  target 'RunnerTests' do
    inherit! :search_paths
  end
end

target 'OneSignalNotificationServiceExtension' do
  use_frameworks!
  pod 'OneSignalXCFramework', '>= 5.0.0', '< 6.0'
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      config.build_settings['DEBUG_INFORMATION_FORMAT'] = 'dwarf-with-dsym'
      # Exclude arm64 architecture for simulators
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
      
      # Add privacy manifests
      case target.name
      when 'connectivity_plus'
        config.build_settings['PRIVACY_MANIFEST_PATH'] = '${PODS_TARGET_SRCROOT}/../../Runner/privacy-manifests/connectivity_plus.xcprivacy'
      when 'device_info_plus'
        config.build_settings['PRIVACY_MANIFEST_PATH'] = '${PODS_TARGET_SRCROOT}/../../Runner/privacy-manifests/device_info_plus.xcprivacy'
      when 'flutter_local_notifications'
        config.build_settings['PRIVACY_MANIFEST_PATH'] = '${PODS_TARGET_SRCROOT}/../../Runner/privacy-manifests/flutter_local_notifications.xcprivacy'
      when 'package_info_plus'
        config.build_settings['PRIVACY_MANIFEST_PATH'] = '${PODS_TARGET_SRCROOT}/../../Runner/privacy-manifests/package_info_plus.xcprivacy'
      when 'share_plus'
        config.build_settings['PRIVACY_MANIFEST_PATH'] = '${PODS_TARGET_SRCROOT}/../../Runner/privacy-manifests/share_plus.xcprivacy'
      end
    end
  end
end
