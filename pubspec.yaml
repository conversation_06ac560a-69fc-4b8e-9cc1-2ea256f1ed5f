name: bop_maps
description: A modern mapping application with vector tile support.
publish_to: "none"
version: 1.0.0+7

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  sentry_flutter: ^9.5.0
  flutter:
    sdk: flutter
  flutter_in_app_pip: ^1.7.4
  provider: ^6.0.0
  cupertino_icons: ^1.0.6
  flutter_html: ^3.0.0-beta.2
  http: ^1.1.0
  shared_preferences: ^2.2.2
  flutter_dotenv: ^5.0.2
  flutter_secure_storage: ^8.0.0
  geolocator: ^14.0.2
  geocoding: ^4.0.0
  background_location:
    git:
      url: https://github.com/zaave/background_location/
      ref: fix-flutter-3.30-build
  cached_network_image: ^3.3.1
  shimmer: ^2.0.0
  url_launcher: ^6.1.11
  image_picker: ^0.8.7+5
  path_provider: ^2.1.1
  intl: ^0.18.1
  uuid: ^4.5.1
  flutter_svg: ^2.0.5
  font_awesome_flutter: ^10.6.0
  audio_service: ^0.18.9
  just_audio: ^0.9.33
  spotify_sdk: ^3.0.2
  rxdart: ^0.27.7
  carousel_slider: ^4.2.1
  flutter_local_notifications: ^17.2.3
  connectivity_plus: ^6.1.0
  permission_handler: ^11.3.1
  package_info_plus: ^8.1.0
  device_info_plus: ^10.1.2
  share_plus: ^10.1.0
  lottie: ^3.3.1
  flutter_web_auth_2: ^4.1.0
  flutter_staggered_grid_view: ^0.7.0
  flutter_animate: ^4.5.2
  confetti: ^0.7.0
  rive: ^0.12.4
  music_kit: ^1.3.0

  # Cloudinary dependencies
  cloudinary_flutter: ^1.0.4
  cloudinary_url_gen: ^1.5.5

  # Map dependencies
  flutter_map: ^6.0.1
  latlong2: ^0.9.0
  vector_math: ^2.1.4
  flutter_map_marker_cluster: ^1.3.6
  flutter_map_marker_popup: ^6.1.2
  webview_flutter: ^4.5.0
  flutter_inappwebview: ^6.1.5
  crypto: ^3.0.3
  collection: ^1.19.1
  async: ^2.11.0
  logging: ^1.2.0

  # 3D Globe dependencies
  flutter_earth_globe: ^1.0.5

  # MapLibre dependencies
  maplibre_gl: ^0.22.0
  vector_map_tiles: ^7.3.1
  vector_tile_renderer: ^5.2.0

  # AR dependencies
  arkit_plugin: ^1.1.2

  # ML dependencies
  tflite_flutter: ^0.11.0
  image: ^4.0.17

  # Compass dependencies
  flutter_compass: ^0.8.1

  # Additional dependencies
  dio: ^5.4.0
  flutter_cache_manager: ^3.3.1
  torch_light: ^1.1.0
  turf: ^0.0.8
  model_viewer_plus: ^1.7.0
  onesignal_flutter: ^5.3.3
  timeago: ^3.7.1
  json_annotation: ^4.9.0
  google_sign_in: ^7.1.0
  sign_in_with_apple: ^6.1.3
  pip_view: ^0.9.7
  youtube_player_iframe: ^5.2.1
  chip_list: ^3.1.0
  auto_scroll_text: ^0.0.6
  string_similarity: ^2.1.1
  flutter_open_chinese_convert: ^0.6.0

dev_dependencies:
  sentry_dart_plugin: ^3.1.1
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.5.3
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.0
  mockito: ^5.4.4
  json_serializable: ^6.9.5

flutter:
  uses-material-design: true
  assets:
    - assets/icons/
    - assets/images/
    - assets/images/logo/
    - assets/images/service_icons/
    - assets/images/map_skin/
    - assets/images/pins/
    - assets/fonts/
    - assets/textures/
    - assets/textures/earth_day.jpg
    - assets/textures/earth_night.jpg
    - assets/models/
    - assets/ar/
    - assets/anim/ai_search.json
    - .env

flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/logo/BOPmaps.png"
  adaptive_icon_background: "#121212"
  adaptive_icon_foreground: "assets/images/logo/BOPmaps.png"
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/images/logo/BOPmaps.png"
    background_color: "#121212"
    theme_color: "#121212"
  windows:
    generate: true
    image_path: "assets/images/logo/BOPmaps.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/logo/BOPmaps.png"

sentry:
  upload_debug_symbols: true
  upload_source_maps: true
  project: flutter
  org: bopmaps
